package com.caidaocloud.attendance.core.wa.dto.shift;

import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("班次设置-上班时间范围内的多段休息时间段DTO")
public class RestPeriodDto {
    @ApiModelProperty("开始时间(单位分钟),eg:750")
    private Integer noonRestStart;
    @ApiModelProperty("结束时间(单位分钟),eg:780")
    private Integer noonRestEnd;
    @ApiModelProperty("开始时间归属标记: 1 当日、2 次日")
    private Integer noonRestStartBelong;
    @ApiModelProperty("结束时间归属标记: 1 当日、2 次日")
    private Integer noonRestEndBelong;

    public Integer doGetRealNoonRestStart() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.noonRestStartBelong)) {
            return noonRestStart + 1440;
        }
        return noonRestStart;
    }

    public Integer doGetRealNoonRestEnd() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.noonRestEndBelong)) {
            return noonRestEnd + 1440;
        }
        return noonRestEnd;
    }

    public Integer doGetRestTotalTime() {
        if (this.noonRestStart == null || this.noonRestEnd == null) {
            return 0;
        }
        Integer wStart = this.doGetRealNoonRestStart();
        Integer wEnd = this.doGetRealNoonRestEnd();
        return wEnd - wStart;
    }

    public void doInitTimeBelong(WaShiftDef shiftDef) {
        if (null == this.noonRestStart || null == this.noonRestEnd
                || (null != this.noonRestStartBelong && null != this.noonRestEndBelong)) {
            return;
        }
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType())) {
            return;
        }
        boolean crossNightForShiftTime = CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType());

        if (!crossNightForShiftTime || this.noonRestEnd > shiftDef.getStartTime()) {
            this.noonRestStartBelong = ShiftTimeBelongTypeEnum.TODAY.getIndex();
            this.noonRestEndBelong = ShiftTimeBelongTypeEnum.TODAY.getIndex();
        } else {
            this.noonRestStartBelong = this.noonRestStart > shiftDef.getStartTime()
                    ? ShiftTimeBelongTypeEnum.TODAY.getIndex()
                    : ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex();
            this.noonRestEndBelong = ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex();
        }
    }
}
