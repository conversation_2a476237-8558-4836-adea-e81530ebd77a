package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.wa.dto.EmpCalendarInfoDto;
import com.caidao1.wa.mybatis.model.WaWorktime;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaWorktimeDo;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpRelCalendarQueryDto;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftPo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

/**
 * 工作日历仓储接口
 *
 * <AUTHOR>
 * @Date 2021/3/1
 */
public interface IWorktimeRepository {
    List<WaWorktimeDo> getWorktimeListByWorkRoundId(String belongOrgId, Integer workRoundId);

    int save(WaWorktimeDo waWorktimeDo);

    int updateById(WaWorktimeDo waWorktimeDo);

    WaWorktimeDo selectById(String belongOrgid, Integer id);

    int deleteById(Integer id);

    WaWorktimeDo getDefaultCalendar(String belongOrgId, Integer excludeWorkCalendarId);

    List<Map> getWorkCalendarDetailList(Integer workCalendarId, String start, String end);

    List<Map> getEmpCalendarList(String belongOrgId, Long startDate, Long endDate, Long empId);

    List<WaWorktimeDo> getWorktimeListByCalendarGroupId(String belongOrgId, Integer calendarGroupId);

    int deleteDetailById(Integer id);

    int deleteWorkTimeGroupById(Integer id);

    int getCalendarGroupCountByName(String belongOrgId, Integer excludeCalendarGroupId, String groupName);

    int getWorkTimeCountByName(String belongOrgId, Integer id, String name);

    List<Map> getEmpShiftChangeListByEmpId(String belongOrgId, Long empId, Long startDate, Long endDate);

    PageList<Map> getEmpShiftChangePageList(MyPageBounds pageBounds, Map params);

    List<Map> getEmpCalendarShiftList(String belongOrgId, Long startDate, Long endDate, Long empId, String dataScope);

    List<Map> getChangeShiftDefListByEmpId(String belongOrgId, Long empId, Long startDate, Long endDate);

    List<WaWorktime> getWorkCalendar(String belongOrgId);

    /**
     * 查询员工使用的工作日历
     *
     * @param queryDto
     * @return
     */
    List<EmpCalendarInfoDto> selectEmpCalendarList(ListEmpRelCalendarQueryDto queryDto);

    List<WaShiftPo> selectEmpCalendarShiftList(String belongOrgId, Long startDate, Long endDate, List<Long> empIds);

    AttendancePageResult<WaShiftPo> selectWaShiftList(AttendanceBasePage basePage, String belongOrgId, Long startDate, Long endDate);
}
