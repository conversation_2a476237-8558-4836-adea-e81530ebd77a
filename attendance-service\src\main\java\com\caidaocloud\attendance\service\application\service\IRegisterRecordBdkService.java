package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordBdkDto;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;
import com.caidaocloud.dto.UserInfo;

public interface IRegisterRecordBdkService {
    AttendancePageResult<RegisterRecordBdkDto> getRegisterRecordPageList(RegisterRecordRequestDto requestDto);

    AttendancePageResult<RegisterRecordBdkDto> getRegisterRecordPageList(RegisterRecordRequestDto requestDto, UserInfo userInfo);
}
