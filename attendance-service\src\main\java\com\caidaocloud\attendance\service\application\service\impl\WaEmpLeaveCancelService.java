package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.enums.LeaveStatusEnum;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.JacksonJsonUtil;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.mobile.bean.SessionBean;
import com.caidao1.mobile.service.MobileV16Service;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.enums.LeaveCancelSettingTypeEnum;
import com.caidao1.wa.enums.LeaveCancelStatusEnum;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidao1.wa.service.WaLeaveService;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.dto.ShiftRestPeriods;
import com.caidaocloud.attendance.core.wa.dto.WaLeaveDaytimeExtDto;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiWorkTimeBaseDto;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.application.dto.LeaveCancelResultDto;
import com.caidaocloud.attendance.service.application.dto.LeaveInfoDto;
import com.caidaocloud.attendance.service.application.dto.leave.LeaveApplyResultDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.IConfigService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.application.service.user.EmployeeGroupService;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.domain.service.WaEmpLeaveCancelDaytimeDomainService;
import com.caidaocloud.attendance.service.domain.service.WaEmpLeaveCancelDomainService;
import com.caidaocloud.attendance.service.domain.service.WaEmpLeaveCancelInfoDomainService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PatternUtil;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.SysConfigsEnum;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WaEmpLeaveCancelService {
    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaEmpLeaveCancelDomainService waEmpLeaveCancelDomainService;
    @Autowired
    private WaEmpLeaveCancelInfoDomainService waEmpLeaveCancelInfoDomainService;
    @Autowired
    private WaEmpLeaveCancelDaytimeDomainService waEmpLeaveCancelDaytimeDomainService;
    @Autowired
    private SysEmpInfoDo sysEmpInfoDo;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;
    @Autowired
    private WaEmpLeaveDo waEmpLeaveDo;
    @Autowired
    private WaEmpLeaveTimeDo waEmpLeaveTimeDo;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WaSobService waSobService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private WaCompensatoryQuotaUseMapper waCompensatoryQuotaUseMapper;
    @Autowired
    private EmpCompensatoryQuotaMapper waEmpCompensatoryQuotaMapper;
    @Autowired
    private WaLeaveQuotaUseMapper waLeaveQuotaUseMapper;
    @Autowired
    private WaLeaveDaytimeDo waLeaveDaytimeDo;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private IConfigService configService;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    @Lazy
    private IWfService wfService;
    @Autowired
    @Lazy
    private WorkflowCallBackService workflowCallBackService;
    @Autowired
    private LeaveApplyService leaveApplyService;
    @Resource
    private WaLeaveService waLeaveService;
    @Resource
    private EmployeeGroupService employeeGroupService;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private WaSobDo sobDo;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Resource
    private WaEmpLeaveTimeMapper waEmpLeaveTimeMapper;
    @Resource
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private MobileV16Service mobileV16Service;
    @Autowired
    private WaMapper waMapper;

    private static final float MAX_TIME_DURATION = 480f; // 最大时间间隔
    private static final int MINUTES_PER_DAY = 1440; // 一天的分钟数
    private static final int SECONDS_PER_DAY = 86400; // 一天的秒数

    private UserInfo getUser() {
        return sessionService.getUserInfo();
    }

    /**
     * 获取销假时长
     *
     * @param dto
     * @return
     */
    public Result<LeaveCancelResultDto> getTime(LeaveCancelDto dto) throws Exception {
        Result result = preCheck(dto);
        if (!result.isSuccess()) {
            return result;
        }
        return doCheckAndCalTime(dto);
    }

    /**
     * 参数值检查
     *
     * @param dto
     */
    private Result preCheck(LeaveCancelDto dto) {
        //销假单id不能为空
        if (null == dto.getLeaveId()) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ID_IS_NULL, Boolean.FALSE);
        }
        if (CollectionUtils.isEmpty(dto.getTimeInfoList())) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_TIME_MUST, Boolean.FALSE);
        }
        for (LeaveCancelInfoDto timeDto : dto.getTimeInfoList()) {
            if (null == timeDto.getStartTime() || null == timeDto.getEndTime()) {
                return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_ILLEGAL, Boolean.FALSE);
            }
            if (timeDto.getStartTime().equals(timeDto.getEndTime()) &&
                    "P".equals(timeDto.getShalfDay()) && "A".equals(timeDto.getEhalfDay())) {
                return ResponseWrap.wrapResult(AttendanceCodes.START_TIME_GREATER_THAN_END, Boolean.FALSE);
            }
            if (timeDto.getStartTime() > timeDto.getEndTime()) {
                return ResponseWrap.wrapResult(AttendanceCodes.START_TIME_GREATER_THAN_END, Boolean.FALSE);
            }
        }
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    /**
     * 检查并计算销假时长
     *
     * @param dto
     * @return
     */
    private Result doCheckAndCalTime(LeaveCancelDto dto) throws Exception {
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getById(dto.getLeaveId());
        WaEmpLeaveTimeDo empLeaveTime = waEmpLeaveTimeDo.getById(dto.getLeaveId());
        if (null == empLeave || null == empLeaveTime) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TIME_NOT_EXIST, Boolean.FALSE);
        }
        if (empLeave.getStatus() != 2) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_NOT_ALLOWED, Boolean.FALSE);
        }
        if (empLeave.getCancelTimeDuration() != null &&
                empLeave.getCancelTimeDuration() >= empLeave.getTotalTimeDuration()) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ALREADY_CANCEL, Boolean.FALSE);
        }
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(empLeave.getLeaveTypeId());
        if (null == leaveType) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        Result<Boolean> checkForMultiShiftResult = LeaveCancelDto.checkForMultiShift(dto, leaveType.getAcctTimeType());
        if (!checkForMultiShiftResult.isSuccess()) {
            return Result.fail(checkForMultiShiftResult.getMsg());
        }
        UserInfo userInfo = getUser();
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(userInfo.getTenantId(), empLeave.getEmpid());
        // 数据类型转换
        List<WaEmpLeaveCancelInfoDo> timeInfoList = ObjectConverter.convertList(dto.getTimeInfoList(), WaEmpLeaveCancelInfoDo.class);
        // 设置periodType
        setPeriodType(leaveType, timeInfoList);
        // 查询员工班次
        Map<Integer, WaShiftDef> corpAllShiftDef = waCommonService.getCorpAllShiftDef(userInfo.getTenantId());
        if (MapUtils.isEmpty(corpAllShiftDef)) {
            return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NOT_EXIST, Boolean.FALSE);
        }
        Map<Long, WaWorktimeDetail> empShiftMap = getEmpShift(empInfo, dto, empLeaveTime, userInfo);
        if (MapUtils.isEmpty(empShiftMap)) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, Boolean.FALSE);
        }
        List<WaEmpLeaveCancelDaytimeDo> leaveCancelDaytimeList = new ArrayList<>();
        BigDecimal totalTime = BigDecimal.ZERO;
        // 休假单据，leaveDayTime
        List<WaLeaveDaytimeDo> leaveDayTimes = waLeaveDaytimeDo.getByDate(empLeave.getLeaveId(), null);
        for (WaEmpLeaveCancelInfoDo timeInfo : timeInfoList) {
            // 班次校验
            Long startDate = DateUtil.getOnlyDate(new Date(timeInfo.getStartTime() * 1000));
            Long endDate = DateUtil.getOnlyDate(new Date(timeInfo.getEndTime() * 1000));
            doCheckEmpShift(empShiftMap, startDate, endDate);
            // 考勤截止日判断
            checkSobEndDate(empInfo.getEmpid(), endDate);
            // 校验是否能请
            checkDateType(leaveType, empShiftMap, timeInfo);
            // 计算销假时长, 销假时间重叠校验
            BigDecimal time = doCalTime(empLeave.getLeaveId(), empInfo, timeInfo, corpAllShiftDef, empShiftMap, leaveDayTimes, leaveType, leaveCancelDaytimeList);
            totalTime = totalTime.add(time);
        }
        if (checkTime(leaveCancelDaytimeList, corpAllShiftDef)) {
            return ResponseWrap.wrapResult(AttendanceCodes.TIME_REPETITION, Boolean.FALSE);
        }
        SessionBean sessionBean = new SessionBean();
        sessionBean.setLanguage(SessionHolder.getLang());
        if (leaveType.getAcctTimeType() == 1) {
            if (leaveType.getRoundTimeUnit() != null && totalTime.floatValue() % leaveType.getRoundTimeUnit() > 0) {
                String message = messageResource.getMessage("L005569", new Object[]{leaveType.getLeaveName(), leaveType.getRoundTimeUnit()}, new Locale(sessionBean.getLanguage()));
                return Result.fail(message);
            }
        } else {//小时
            if (leaveType.getRoundTimeUnit() != null && totalTime.floatValue() % (60 * leaveType.getRoundTimeUnit()) > 0) {
                String message = messageResource.getMessage("L005570", new Object[]{leaveType.getLeaveName(), leaveType.getRoundTimeUnit()}, new Locale(sessionBean.getLanguage()));
                return Result.fail(message);
            }
        }
        if (LeaveTypeUnitEnum.HOUR.getIndex().equals(leaveType.getAcctTimeType())) {
            totalTime = totalTime.divide(new BigDecimal(60), 2, RoundingMode.DOWN);
        }
        return Result.ok(new LeaveCancelResultDto(leaveType.getAcctTimeType(), totalTime.floatValue()));
    }

    private Map<Long, List<Integer>> getEmpLeaveDayTimeShiftMap(List<WaLeaveDaytimeDo> leaveDayTimes) {
        return CollectionUtils.isEmpty(leaveDayTimes) ? Collections.emptyMap()
                : leaveDayTimes.stream().collect(Collectors.groupingBy(WaLeaveDaytimeDo::getLeaveDate,
                Collectors.mapping(WaLeaveDaytimeDo::getUseShiftDefId, Collectors.toList())));
    }

    private WaEmpLeaveCancelInfoDo getEmpLeaveTime(List<WaLeaveDaytimeDo> leaveDayTimes, Map<Integer, WaShiftDef> shiftMap) {
        WaEmpLeaveCancelInfoDo empLeaveCancelInfo = new WaEmpLeaveCancelInfoDo();
        // 计算已经申请的休假单的实际休假开始、结束时间
        List<WaLeaveDaytimeExtDto> list = leaveDayTimes.stream().map(ltDay -> {
            WaShiftDef userShiftDef = shiftMap.get(ltDay.getShiftDefId());
            Integer useShiftDefId = ltDay.getUseShiftDefId();
            if (Optional.ofNullable(useShiftDefId).isPresent() && null != shiftMap.get(useShiftDefId)) {
                userShiftDef = shiftMap.get(useShiftDefId);
            }
            WaLeaveDaytimeExtPo extPo = ObjectConverter.convert(ltDay, WaLeaveDaytimeExtPo.class);
            return mobileV16Service.calLeaveDayRealTimeSlot(extPo, userShiftDef);
        }).collect(Collectors.toList());
        if (list.isEmpty()) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.APPLY_NOT_EXIST).getMsg());
        }
        LongSummaryStatistics startStats = list.stream().collect(Collectors.summarizingLong(WaLeaveDaytimeExtDto::getLeaveStartTime));
        empLeaveCancelInfo.setShiftStartTime(startStats.getMin());
        LongSummaryStatistics endStats = list.stream().collect(Collectors.summarizingLong(WaLeaveDaytimeExtDto::getLeaveEndTime));
        empLeaveCancelInfo.setShiftEndTime(endStats.getMax());
        return empLeaveCancelInfo;
    }

    /**
     * 员工排班校验
     *
     * @param empShiftMap
     * @param startDate
     * @param endDate
     */
    private void doCheckEmpShift(Map<Long, WaWorktimeDetail> empShiftMap, Long startDate, Long endDate) {
        long tmpDate = startDate;
        while (tmpDate <= endDate) {
            if (!empShiftMap.containsKey(tmpDate)) {
                throw new CDException("员工" + DateUtil.getDateStrByTimesamp(tmpDate) + "未排班");
            }
            tmpDate = tmpDate + 24 * 60 * 60;
        }
    }

    /**
     * 考勤截止日判断
     *
     * @param empId
     * @param date
     */
    private void checkSobEndDate(Long empId, Long date) {
        WaSob waSob = waSobService.getWaSob(empId, date);
        if (waSob == null) {
            return;
        }
        String enDate = DateUtil.getDateStrByTimesamp(waSob.getSobEndDate());
        String[] dateList = enDate.split("-");
        if (DateUtil.getOnlyDate() > waSob.getSobEndDate()) {
            throw new CDException("申请时间已超过" + waSob.getSysPeriodMonth() + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员");
        }
    }

    /**
     * 查询员工班次
     *
     * @param empInfo
     * @param dto
     * @param userInfo
     * @return
     */
    private Map<Long, WaWorktimeDetail> getEmpShift(SysEmpInfo empInfo, LeaveCancelDto dto, WaEmpLeaveTimeDo empLeaveTime, UserInfo userInfo) {
        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
        tmType = tmType == 0 ? 1 : tmType;
        Long startTime = DateUtil.getOnlyDate(new Date(empLeaveTime.getStartTime() * 1000));
        Long endTime = DateUtil.getOnlyDate(new Date(empLeaveTime.getEndTime() * 1000));
        Long minTime = dto.getTimeInfoList().stream().min(Comparator.comparing(LeaveCancelInfoDto::getStartTime)).get().getStartTime();
        Long minDate = DateUtil.getOnlyDate(new Date(minTime * 1000));
        Long maxTime = dto.getTimeInfoList().stream().max(Comparator.comparing(LeaveCancelInfoDto::getEndTime)).get().getEndTime();
        Long maxDate = DateUtil.getOnlyDate(new Date(maxTime * 1000));
        minDate = startTime > minDate ? minDate : startTime;
        maxDate = endTime > maxDate ? endTime : maxDate;
        return waCommonService.getEmpWaWorktimeDetail(userInfo.getTenantId(), empInfo.getEmpid(), tmType, minDate - 86400, maxDate, empInfo.getWorktimeType(), true);
    }

    /**
     * 设置PeriodType
     *
     * @param leaveType
     * @param timeInfoList
     */
    private void setPeriodType(WaLeaveTypeDo leaveType, List<WaEmpLeaveCancelInfoDo> timeInfoList) {
        for (WaEmpLeaveCancelInfoDo timeDto : timeInfoList) {
            if (leaveType.getAcctTimeType().equals(LeaveTypeUnitEnum.DAY.getIndex())) {
                if (StringUtils.isNotBlank(timeDto.getShalfDay()) && StringUtils.isNotBlank(timeDto.getEhalfDay())) {
                    // 半天
                    timeDto.setPeriodType(PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().shortValue());
                } else {
                    // 整天
                    timeDto.setPeriodType(PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().shortValue());
                }
            } else {
                Long startDate = DateUtil.getOnlyDate(new Date(timeDto.getStartTime() * 1000));
                Long endDate = DateUtil.getOnlyDate(new Date(timeDto.getEndTime() * 1000));
                long stime = timeDto.getStartTime() - startDate;
                long etime = timeDto.getEndTime() - endDate;
                if (stime > 0 || etime > 0) {
                    // 小时
                    timeDto.setPeriodType(PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().shortValue());
                } else {
                    // 小时整天
                    timeDto.setPeriodType(PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().shortValue());
                }
            }
        }
    }

    /**
     * 设置实际销假时间
     *
     * @param timeInfo    销假数据
     * @param shiftDefMap 系统班次
     * @param empShiftMap 员工排班
     */
    private void setRealTime(WaEmpLeaveCancelInfoDo timeInfo, Map<Integer, WaShiftDef> shiftDefMap, Map<Long, WaWorktimeDetail> empShiftMap) {
        Long startDate = DateUtil.getOnlyDate(new Date(timeInfo.getStartTime() * 1000));
        Long endDate = DateUtil.getOnlyDate(new Date(timeInfo.getEndTime() * 1000));
        WaWorktimeDetail sDetail = empShiftMap.get(startDate);
        WaShiftDef startDateShift = shiftDefMap.get(sDetail.getShiftDefId());
        WaWorktimeDetail eDetail = empShiftMap.get(endDate);
        WaShiftDef endDateShift = shiftDefMap.get(eDetail.getShiftDefId());

        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().shortValue() == timeInfo.getPeriodType()) {
            // 整天
            timeInfo.setShiftStartTime(startDate + startDateShift.getStartTime() * 60);
            if (CdWaShiftUtil.checkCrossNightV3(eDetail, shiftDefMap)) {
                endDate += 1440 * 60;
            }
            timeInfo.setShiftEndTime(endDate + endDateShift.getEndTime() * 60);
            if (!CollectionUtils.isEmpty(timeInfo.getStartShifts())) {
                List<Integer> startUseShiftDefIdList = timeInfo.getStartShifts().stream().map(Long::intValue).collect(Collectors.toList());
                timeInfo.setStartShift(FastjsonUtil.toJson(startUseShiftDefIdList));
            }
            if (!CollectionUtils.isEmpty(timeInfo.getEndShifts())) {
                List<Integer> endUseShiftDefIdList = timeInfo.getEndShifts().stream().map(Long::intValue).collect(Collectors.toList());
                timeInfo.setEndShift(FastjsonUtil.toJson(endUseShiftDefIdList));
            }
        } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().shortValue() == timeInfo.getPeriodType()) {
            // 小时整天
            timeInfo.setShiftStartTime(startDate + startDateShift.getStartTime() * 60);
            if (CdWaShiftUtil.checkCrossNightV3(eDetail, shiftDefMap)) {
                endDate += 1440 * 60;
            }
            timeInfo.setShiftEndTime(endDate + endDateShift.getEndTime() * 60);
        } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().shortValue() == timeInfo.getPeriodType()) {
            // 小时
            timeInfo.setShiftStartTime(timeInfo.getStartTime());
            timeInfo.setShiftEndTime(timeInfo.getEndTime());
        } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().shortValue() == timeInfo.getPeriodType()) {
            // 销假班次
            if (!CollectionUtils.isEmpty(timeInfo.getStartShifts())) {
                List<Integer> startUseShiftDefIdList = timeInfo.getStartShifts().stream().map(Long::intValue).collect(Collectors.toList());
                timeInfo.setStartShift(FastjsonUtil.toJson(startUseShiftDefIdList));
            }
            if (!CollectionUtils.isEmpty(timeInfo.getEndShifts())) {
                List<Integer> endUseShiftDefIdList = timeInfo.getEndShifts().stream().map(Long::intValue).collect(Collectors.toList());
                timeInfo.setEndShift(FastjsonUtil.toJson(endUseShiftDefIdList));
            }
            // 计算销假时间
            // 开始日期
            List<Integer> startShiftDefIdList = !CollectionUtils.isEmpty(timeInfo.getStartShifts())
                    ? timeInfo.getStartShifts().stream().map(Long::intValue).collect(Collectors.toList()) : sDetail.doGetShiftDefIdList();
            Optional<WaEmpLeaveCancelInfoDo> startDateTime = startShiftDefIdList.stream().map(shiftDefId -> {
                WaShiftDef shiftDef = shiftDefMap.get(shiftDefId);
                return calLeaveShiftTimeForPeriodTypeNine(startDate, startDate, shiftDef, shiftDef, timeInfo.getShalfDay(), timeInfo.getEhalfDay());
            }).min(Comparator.comparing(WaEmpLeaveCancelInfoDo::getShiftStartTime));
            startDateTime.ifPresent(waEmpLeaveTime -> timeInfo.setShiftStartTime(waEmpLeaveTime.getShiftStartTime()));
            // 结束日期
            List<Integer> endShiftDefIdList = !CollectionUtils.isEmpty(timeInfo.getEndShifts())
                    ? timeInfo.getEndShifts().stream().map(Long::intValue).collect(Collectors.toList()) : eDetail.doGetShiftDefIdList();
            long finalEndDate = endDate;
            Optional<WaEmpLeaveCancelInfoDo> endDateTime = endShiftDefIdList.stream().map(shiftDefId -> {
                WaShiftDef shiftDef = shiftDefMap.get(shiftDefId);
                return calLeaveShiftTimeForPeriodTypeNine(finalEndDate, finalEndDate, shiftDef, shiftDef, timeInfo.getShalfDay(), timeInfo.getEhalfDay());
            }).max(Comparator.comparing(WaEmpLeaveCancelInfoDo::getShiftEndTime));
            endDateTime.ifPresent(waEmpLeaveTime -> timeInfo.setShiftEndTime(waEmpLeaveTime.getShiftEndTime()));
        }
    }

    public WaEmpLeaveCancelInfoDo calLeaveShiftTimeForPeriodTypeNine(long startDate, long endDate, WaShiftDef startDateShift, WaShiftDef endDateShift, String sHalfDay, String eHalfDay) {
        WaShiftDef startShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(startDateShift);
        WaShiftDef endShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(endDateShift);
        WaEmpLeaveCancelInfoDo leaveTime = new WaEmpLeaveCancelInfoDo();
        // 半天开始
        if ("P".equals(sHalfDay)) {
            if (startDateShift.getIsHalfdayTime() != null && startDateShift.getIsHalfdayTime()
                    && startDateShift.getHalfdayTime() != null && startDateShift.getHalfdayTime() > 0) {
                leaveTime.setShiftStartTime(startDate + startDateShift.doGetRealHalfdayTime() * 60);
            } else if (startDateShift.getIsNoonRest() != null && startDateShift.getIsNoonRest()) {
                leaveTime.setShiftStartTime(startDate + startDateShift.doGetRealNoonRestEnd() * 60);
            } else {
                leaveTime.setShiftStartTime(startDate + startShiftWorkTime.getStartTime() * 60);
            }
        } else {
            leaveTime.setShiftStartTime(startDate + startShiftWorkTime.getStartTime() * 60);
        }
        // 半天结束
        if ("A".equals(eHalfDay)) {
            if (endDateShift.getIsHalfdayTime() != null && endDateShift.getIsHalfdayTime()
                    && endDateShift.getHalfdayTime() != null && endDateShift.getHalfdayTime() > 0) {
                leaveTime.setShiftEndTime(endDate + endDateShift.doGetRealHalfdayTime() * 60);
            } else if (endDateShift.getIsNoonRest() != null && endDateShift.getIsNoonRest()) {
                leaveTime.setShiftEndTime(endDate + endDateShift.doGetRealNoonRestStart() * 60);
            } else {
                leaveTime.setShiftEndTime(endDate + endShiftWorkTime.getEndTime() * 60);
            }
        } else {
            long endTime = endDate;
            if (CdWaShiftUtil.checkCrossNightV2(endDateShift, endDateShift.getDateType())) {
                endTime = DateUtil.addDate(endTime * 1000, 1);
            }
            leaveTime.setShiftEndTime(endTime + endShiftWorkTime.getEndTime() * 60);
        }
        return leaveTime;
    }

    /**
     * 销假时间重叠校验
     *
     * @param empId
     * @param timeInfo
     */
    private void doCheckTime(Long empId, Integer leaveId, WaEmpLeaveCancelInfoDo timeInfo) {
        // 时间重叠校验
        Long start = timeInfo.getShiftStartTime();
        Long end = timeInfo.getShiftEndTime();
        if (waEmpLeaveCancelInfoDomainService.checkTimeRepeat(empId, leaveId, start, end) > 0) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.TIME_REPETITION, Boolean.FALSE).getMsg());
        }
    }

    /**
     * 校验是否能销假
     */
    private void checkDateType(WaLeaveTypeDo waLeaveTypeDo, Map<Long, WaWorktimeDetail> empShiftMap, WaEmpLeaveCancelInfoDo timeInfo) {
        Long startDate = DateUtil.getOnlyDate(new Date(timeInfo.getStartTime() * 1000));
        Long endDate = DateUtil.getOnlyDate(new Date(timeInfo.getEndTime() * 1000));
        WaWorktimeDetail detail = empShiftMap.get(startDate);
        String startTimeStr = DateUtil.getDateStrByTimesamp(startDate);
        if (startDate.equals(endDate)) {
            if (DateTypeEnum.DATE_TYP_2.getIndex().equals(detail.getDateType()) && (waLeaveTypeDo.getIsRestDay() == null || !waLeaveTypeDo.getIsRestDay())) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.REST_DAY_NOT_LEAVE_CANCEL, Boolean.FALSE).getMsg());
            } else if ((DateTypeEnum.DATE_TYP_3.getIndex().equals(detail.getDateType()) || DateTypeEnum.DATE_TYP_5.getIndex().equals(detail.getDateType()))
                    && (waLeaveTypeDo.getIsLegalHoliday() == null || !waLeaveTypeDo.getIsLegalHoliday())) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.LEGAL_DAY_NOT_LEAVE_CANCEL, Boolean.FALSE).getMsg());
            } else if (DateTypeEnum.DATE_TYP_4.getIndex().equals(detail.getDateType()) && (waLeaveTypeDo.getIsRestDay() == null || !waLeaveTypeDo.getIsRestDay())) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.SPECIAL_DAY_NOT_LEAVE_CANCEL, Boolean.FALSE).getMsg());
            }
        } else {
            if (DateTypeEnum.DATE_TYP_2.getIndex().equals(detail.getDateType()) && (waLeaveTypeDo.getIsRestDay() == null || !waLeaveTypeDo.getIsRestDay())) {
                //{0}为休息日，不能为销假开始日
                throw new CDException(messageResource.getMessage("L008001", new Object[]{startTimeStr}, new Locale(SessionHolder.getLang())));
            } else if ((DateTypeEnum.DATE_TYP_3.getIndex().equals(detail.getDateType()) || DateTypeEnum.DATE_TYP_5.getIndex().equals(detail.getDateType()))
                    && (waLeaveTypeDo.getIsLegalHoliday() == null || !waLeaveTypeDo.getIsLegalHoliday())) {
                //{0}为法定假日，不能为销假开始日
                throw new CDException(messageResource.getMessage("L008002", new Object[]{startTimeStr}, new Locale(SessionHolder.getLang())));
            } else if (DateTypeEnum.DATE_TYP_4.getIndex().equals(detail.getDateType()) && (waLeaveTypeDo.getIsRestDay() == null || !waLeaveTypeDo.getIsRestDay())) {
                //{0}为特殊休日，不能为销假开始日
                throw new CDException(messageResource.getMessage("L008003", new Object[]{startTimeStr}, new Locale(SessionHolder.getLang())));
            }
        }
    }

    /**
     * 计算时长
     *
     * @param timeInfo
     * @param shiftMap
     * @param empShiftMap
     * @param waLeaveType
     * @return
     */
    private BigDecimal doCalTime(Integer leaveId, SysEmpInfo empInfo,
                                 WaEmpLeaveCancelInfoDo timeInfo, Map<Integer, WaShiftDef> shiftMap,
                                 Map<Long, WaWorktimeDetail> empShiftMap, List<WaLeaveDaytimeDo> leaveDayTimes,
                                 WaLeaveTypeDo waLeaveType, List<WaEmpLeaveCancelDaytimeDo> list) throws Exception {

        long startDate = DateUtil.getOnlyDate(new Date(timeInfo.getStartTime() * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(timeInfo.getEndTime() * 1000));
        // 休假单据，时间范围
        WaEmpLeaveCancelInfoDo original = getEmpLeaveTime(leaveDayTimes, shiftMap);
        // 休假单据，leaveDayTime的useShiftId
        Map<Long, List<Integer>> leaveTimeShiftMap = getEmpLeaveDayTimeShiftMap(leaveDayTimes);
        // 销假时间循环
        BigDecimal totalTimeDuration = BigDecimal.ZERO;
        WaWorktimeDetail workTimeDetail;
        WaShiftDef shiftDef;
        WaEmpLeaveCancelDaytimeDo dayTime;
        long leaveDate = startDate;
        while (leaveDate <= endDate) {
            workTimeDetail = empShiftMap.get(startDate);
            shiftDef = shiftMap.get(workTimeDetail.getShiftDefId());
            if (workTimeDetail.getDateType() == 4) {
                //公司特殊假日
                workTimeDetail.setDateType(shiftDef.getDateType());
            }
            // 查询当天休假班次
            List<Integer> shiftDefIdList = workTimeDetail.doGetShiftDefIdList();
            if (leaveDate == startDate && !CollectionUtils.isEmpty(timeInfo.getStartShifts())) {
                shiftDefIdList = timeInfo.getStartShifts().stream().map(Long::intValue).collect(Collectors.toList());
            } else if (leaveDate == endDate && !CollectionUtils.isEmpty(timeInfo.getEndShifts())) {
                shiftDefIdList = timeInfo.getEndShifts().stream().map(Long::intValue).collect(Collectors.toList());
            }
            if (leaveDate == startDate || leaveDate == endDate) {
                List<Integer> leaveDayTimeShiftIds = Optional.ofNullable(leaveTimeShiftMap.get(leaveDate)).orElse(Lists.newArrayList());
                if (!leaveDayTimeShiftIds.containsAll(shiftDefIdList)) {
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.TIME_OUT_OF_RANGE, null).getMsg());
                }
            }
            // 计算每个休假班次的休假时长
            for (Integer shiftDefId : shiftDefIdList) {
                dayTime = doBuildLeaveDaytime(timeInfo, waLeaveType, shiftMap, empShiftMap, workTimeDetail, leaveDate, shiftDefId);
                shiftDef = shiftMap.get(shiftDefId);
                // 销假时间重叠校验
                boolean timeOverlap = checkLeaveDayTimeOverlap(empInfo.getEmpid(), leaveId, dayTime, shiftDef, shiftMap);
                if (timeOverlap) {
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.TIME_REPETITION, null).getMsg());
                }
                // 销假时间段是否超出休假时间段校验
                if (checkLeaveCancelTimeIfOutOfLeaveTimeRange(original, dayTime, shiftDef, shiftMap)) {
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.TIME_OUT_OF_RANGE, null).getMsg());
                }
                totalTimeDuration = totalTimeDuration.add(BigDecimal.valueOf(dayTime.getTimeDuration()));
                list.add(dayTime);
            }
            leaveDate = leaveDate + 86400;
        }
        return totalTimeDuration;
    }

    private boolean checkLeaveCancelTimeIfOutOfLeaveTimeRange(WaEmpLeaveCancelInfoDo original, WaEmpLeaveCancelDaytimeDo dayTime,
                                                              WaShiftDef shiftDef, Map<Integer, WaShiftDef> shiftMap) {
        WaLeaveDaytimeExtPo waLeaveCancelDayTime = ObjectConverter.convert(dayTime, WaLeaveDaytimeExtPo.class);
        waLeaveCancelDayTime.setLeaveDate(dayTime.getLeaveCancelDate());
        WaShiftDef userShiftDef = shiftDef;
        Integer useShiftDefId = dayTime.getShiftDefId();
        if (null != useShiftDefId && null != shiftMap.get(useShiftDefId)) {
            userShiftDef = shiftMap.get(useShiftDefId);
        }
        WaLeaveDaytimeExtDto extDto = mobileV16Service.calLeaveDayRealTimeSlot(waLeaveCancelDayTime, userShiftDef);
        return extDto.getLeaveStartTime() < original.getShiftStartTime() || extDto.getLeaveEndTime() > original.getShiftEndTime();
    }

    public boolean checkLeaveDayTimeOverlap(Long empId, Integer leaveId, WaEmpLeaveCancelDaytimeDo apply, WaShiftDef shiftDef, Map<Integer, WaShiftDef> shiftMap) {
        // 1、先查询申请日期当天的销假数据，没有：允许申请，有：继续校验
        long startTime = apply.getLeaveCancelDate();
        long endTime = startTime + 86399;

        // 2、查询当天的销假数据，如：没有：允许申请
        List<Integer> leaveIds = Lists.newArrayList(leaveId);
        List<WaLeaveCancelDayTime> leaveCancelDayTimes = waMapper.selectLeaveCancelDaytimeList(empId, startTime, endTime, leaveIds);
        if (CollectionUtils.isEmpty(leaveCancelDayTimes)) {
            return false;
        }

        WaLeaveDaytimeExtPo applyExtPo = ObjectConverter.convert(apply, WaLeaveDaytimeExtPo.class);
        applyExtPo.setLeaveDate(apply.getLeaveCancelDate());
        WaLeaveDaytimeExtDto applyExtDto = mobileV16Service.calLeaveDayRealTimeSlot(applyExtPo, shiftDef);

        // 3、当天有销假单，检查销假单的时间是否和申请单的时间重叠，如不重叠：允许申请，重叠：返回
        for (WaLeaveCancelDayTime leaveCancelDayTime : leaveCancelDayTimes) {
            WaLeaveDaytimeExtPo waLeaveCancelDayTime = ObjectConverter.convert(leaveCancelDayTime, WaLeaveDaytimeExtPo.class);
            waLeaveCancelDayTime.setLeaveDate(leaveCancelDayTime.getLeaveCancelDate());
            WaShiftDef userShiftDef = shiftDef;
            Integer useShiftDefId = leaveCancelDayTime.getShiftDefId();
            if (null != useShiftDefId && null != shiftMap.get(useShiftDefId)) {
                userShiftDef = shiftMap.get(useShiftDefId);
            }
            WaLeaveDaytimeExtDto extDto = mobileV16Service.calLeaveDayRealTimeSlot(waLeaveCancelDayTime, userShiftDef);
            boolean timeOverlap = mobileV16Service.checkTimeRangeOverlap(applyExtDto.getLeaveStartTime(), applyExtDto.getLeaveEndTime(), extDto.getLeaveStartTime(), extDto.getLeaveEndTime());
            if (timeOverlap) {
                return true;
            }
        }
        return false;
    }

    public boolean checkLeaveTimeIfBelongToday(Map<Integer, WaShiftDef> shiftMap, Map<Long, WaWorktimeDetail> empShiftMap, WaWorktimeDetail worktimeDetail, Integer period, long endDate) {
        boolean isCheckDateType = true;
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType()) && PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            // 非工作日并且休的是小时假
            Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
            WaWorktimeDetail preLeaveDateWorkTime = empShiftMap.get(preEndDate);
            // TODO 多班次时查找跨夜的那个班次
            WaShiftDef preLeaveDateShift = null != preLeaveDateWorkTime ? shiftMap.get(preLeaveDateWorkTime.getShiftDefId()) : null;
            if (preLeaveDateShift != null
                    && DateTypeEnum.DATE_TYP_1.getIndex().equals(preLeaveDateWorkTime.getDateType())
                    && CdWaShiftUtil.checkCrossNightV2(preLeaveDateShift, preLeaveDateWorkTime.getDateType())) {
                // 班次是跨夜班
                isCheckDateType = false;
            }
        }
        return isCheckDateType;
    }

    public WaEmpLeaveCancelDaytimeDo doBuildLeaveDaytime(WaEmpLeaveCancelInfoDo timeInfo, WaLeaveTypeDo waLeaveType, Map<Integer, WaShiftDef> shiftMap,
                                                         Map<Long, WaWorktimeDetail> empShiftMap, WaWorktimeDetail workTimeDetail,
                                                         Long leaveDate, Integer useShiftDefId) throws Exception {
        Integer period = Integer.valueOf(timeInfo.getPeriodType());
        long startDate = DateUtil.getOnlyDate(new Date(timeInfo.getStartTime() * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(timeInfo.getEndTime() * 1000));
        String startTimeStr;
        String endTimeStr;
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
            //整天
            if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
                startTimeStr = DateUtil.getTimeStrByTimesamp4(timeInfo.getStartTime());
                endTimeStr = DateUtil.getTimeStrByTimesamp4(timeInfo.getEndTime());
            } else {
                startTimeStr = DateUtil.getDateStrByTimesamp(timeInfo.getStartTime());
                endTimeStr = DateUtil.getDateStrByTimesamp(timeInfo.getEndTime());
            }
        } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            //小时
            startTimeStr = DateUtil.getTimeStrByTimesamp4(timeInfo.getStartTime());
            endTimeStr = DateUtil.getTimeStrByTimesamp4(timeInfo.getEndTime());
        } else {
            // 半天
            startTimeStr = DateUtil.getDateStrByTimesamp(timeInfo.getStartTime());
            endTimeStr = DateUtil.getDateStrByTimesamp(timeInfo.getEndTime());
        }
        WaShiftDef shiftDef = shiftMap.get(Optional.ofNullable(useShiftDefId).orElse(workTimeDetail.getShiftDefId()));
        WaEmpLeaveCancelDaytimeDo dayTime = new WaEmpLeaveCancelDaytimeDo();
        dayTime.setLeaveCancelDate(leaveDate);
        dayTime.setRealDate(leaveDate);
        dayTime.setPeriodType(period.shortValue());
        dayTime.setTimeUnit(waLeaveType.getAcctTimeType().shortValue());
        dayTime.setShiftDefId(shiftDef.getShiftDefId());
        dayTime.setDateType(workTimeDetail.getDateType());
        dayTime.setTimeDuration(0f);
        dayTime.setCreateTime(DateUtil.getCurrentTime(true));
        // 是否校验日期类型
        boolean isCheckDateType = checkLeaveTimeIfBelongToday(shiftMap, empShiftMap, workTimeDetail, period, endDate);
        // 休息日连续计算
        boolean isRestDay = DateTypeEnum.DATE_TYP_2.getIndex().equals(workTimeDetail.getDateType()) && (!waLeaveType.getIsRestDay());
        // 法定节假日连续计算
        boolean isLegalHoliday = DateTypeEnum.DATE_TYP_3.getIndex().equals(workTimeDetail.getDateType()) && (!waLeaveType.getIsLegalHoliday());
        // 特殊休日连续计算
        boolean isSpecialDate = DateTypeEnum.DATE_TYP_4.getIndex().equals(workTimeDetail.getDateType()) && (!waLeaveType.getIsRestDay());
        // 法定休日
        boolean isLegalRest = DateTypeEnum.DATE_TYP_5.getIndex().equals(workTimeDetail.getDateType()) && !waLeaveType.getIsLegalHoliday();
        if (isCheckDateType && isRestDay) {
            dayTime.setTimeDuration(0f);
        } else if (isCheckDateType && isLegalHoliday) {
            dayTime.setTimeDuration(0f);
        } else if (isCheckDateType && isSpecialDate) {
            dayTime.setTimeDuration(0f);
        } else if (isCheckDateType && isLegalRest) {
            dayTime.setTimeDuration(0f);
        } else {
            if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
                calLeaveTimeByPeriod3(waLeaveType, startTimeStr, endTimeStr, empShiftMap, dayTime, shiftMap);
            } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
                processLeaveByPeriod4(waLeaveType, workTimeDetail, dayTime, shiftDef);
            } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
                processLeaveByPeriod9(waLeaveType, workTimeDetail, dayTime, timeInfo.getShalfDay(), timeInfo.getEhalfDay(),
                        startDate, DateUtil.getTimesampByDateStr2(startTimeStr), DateUtil.getTimesampByDateStr2(endTimeStr));
            } else {
                dayTime.setTimeDuration(1f);
            }
        }
        return dayTime;
    }

    /**
     * 小时
     *
     * @param leaveType
     * @param startTimeStr
     * @param endTimeStr
     * @param pbMap
     * @param dayTime
     * @param shiftMap
     * @throws Exception
     */
    private void calLeaveTimeByPeriod3(WaLeaveTypeDo leaveType, String startTimeStr, String endTimeStr, Map<Long, WaWorktimeDetail> pbMap,
                                       WaEmpLeaveCancelDaytimeDo dayTime, Map<Integer, WaShiftDef> shiftMap) throws Exception {
        //小时非整天
        long firstDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long lastDate = DateUtil.getTimesampByDateStr2(endTimeStr);
        long firstTime = DateUtil.getTimesampByStr2(startTimeStr);
        long lastTime = DateUtil.getTimesampByStr2(endTimeStr);
        if (firstDate == lastDate) {
            //请一天假，第一天也是最后一天
            calLeaveTimeByPeriod3ForOneDay(dayTime, firstTime, lastTime, pbMap, shiftMap);
        } else {
            //请多天假
            calLeaveTimeByPeriod3ForMultiDay(dayTime, firstDate, lastDate, firstTime, lastTime, pbMap, shiftMap, leaveType);
        }
        dayTime.setTimeDuration(Optional.ofNullable(dayTime.getTimeDuration()).filter(d -> d >= 0).orElse(0f));
        //特殊班次工作时长
        long leaveDate = dayTime.getLeaveCancelDate();
        WaWorktimeDetail detail = pbMap.get(leaveDate);//取得当天排班
        WaShiftDef shiftDef = shiftMap.get(detail.getShiftDefId());
        if (shiftDef != null && shiftDef.getIsSpecial() != null && shiftDef.getIsSpecial() && shiftDef.getSpecialWorkTime() != null
                && dayTime.getTimeDuration().equals(Float.valueOf(detail.getWorkTotalTime()))) {
            dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
        }
    }

    /**
     * 请一天假，第一天也是最后一天
     *
     * @param dayTime   销假实体
     * @param firstTime 开始时间
     * @param lastTime  结束时间
     * @param pbMap     排班分组
     * @param shiftMap  班次分组
     */
    private void calLeaveTimeByPeriod3ForOneDay(WaEmpLeaveCancelDaytimeDo dayTime, long firstTime, long lastTime,
                                                Map<Long, WaWorktimeDetail> pbMap, Map<Integer, WaShiftDef> shiftMap) {
        long leaveDate = dayTime.getLeaveCancelDate();
        WaWorktimeDetail detail = pbMap.get(leaveDate); // 取得当天排班
        WaShiftDef shiftDef = shiftMap.get(Optional.ofNullable(dayTime.getShiftDefId()).orElse(detail.getShiftDefId()));
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

        int tmpS = getLeaveDateHour(firstTime);
        int tmpE = getLeaveDateHour(lastTime);

        if (tmpE < shiftDef.getStartTime() || !DateTypeEnum.DATE_TYP_1.getIndex().equals(detail.getDateType())) {
            handleNonWorkdayOrEarlyLeave(dayTime, firstTime, lastTime, leaveDate, pbMap, shiftMap);
        } else {
            handleWorkday(dayTime, firstTime, lastTime, detail, shiftDef, shiftWorkTime, leaveDate, tmpS, tmpE);
        }
    }

    /**
     * 处理非工作日或请假结束时间早于上班开始时间的情况
     */
    private void handleNonWorkdayOrEarlyLeave(WaEmpLeaveCancelDaytimeDo dayTime, long firstTime, long lastTime,
                                              long leaveDate, Map<Long, WaWorktimeDetail> pbMap, Map<Integer, WaShiftDef> shiftMap) {
        long preDate = DateUtil.addDate(leaveDate * 1000, -1);
        WaWorktimeDetail preWorkTime = pbMap.get(preDate);

        if (preWorkTime != null && DateTypeEnum.DATE_TYP_1.getIndex().equals(preWorkTime.getDateType())) {
            int timeDuration = calculateTimeDurationForPreDay(firstTime, lastTime, preDate, preWorkTime, shiftMap);
            setTimeDuration(dayTime, 0, 0, (float) timeDuration);
        } else {
            setTimeDuration(dayTime, 0, 0, 0f);
        }
    }

    /**
     * 计算前一天的时间间隔
     */
    private int calculateTimeDurationForPreDay(long firstTime, long lastTime, long preDate, WaWorktimeDetail preWorkTime, Map<Integer, WaShiftDef> shiftMap) {
        int timeDuration = 0;
        for (Integer preShiftDefId : preWorkTime.doGetShiftDefIdList()) {
            WaShiftDef preShiftDef = shiftMap.get(preShiftDefId);
            if (preShiftDef == null || !CdWaShiftUtil.checkCrossNightV2(preShiftDef, preWorkTime.getDateType())) {
                continue;
            }

            WaShiftDef preShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(preShiftDef);
            long preWorkStartTime = preDate + preShiftWorkTime.getStartTime() * 60;
            long preWorkEndTime = preDate + SECONDS_PER_DAY + preShiftWorkTime.getEndTime() * 60;

            if (firstTime >= preWorkEndTime || lastTime <= preWorkStartTime) {
                continue;
            }

            long sTime = Math.max(preWorkStartTime, firstTime);
            long eTime = Math.min(preWorkEndTime, lastTime);

            List<MultiWorkTimeBaseDto> preMultiWorkTimeList = CdWaShiftUtil.getMultiWorkTimeList(preShiftDef);
            if (preMultiWorkTimeList != null && preMultiWorkTimeList.size() > 1) {
                for (MultiWorkTimeBaseDto preItemWorkTime : preMultiWorkTimeList) {
                    long itemWorkStartTime = preDate + preItemWorkTime.doGetRealStartTime() * 60;
                    long itemWorkEndTime = preDate + preItemWorkTime.doGetRealEndTime() * 60;
                    if (sTime >= itemWorkEndTime || eTime <= itemWorkStartTime) {
                        continue;
                    }
                    long itemStartTime = Math.max(itemWorkStartTime, sTime);
                    long itemEndTime = Math.min(itemWorkEndTime, eTime);
                    timeDuration += mobileV16Service.deductLeaveDayNooRest(preShiftDef, preWorkTime, itemStartTime, itemEndTime);
                }
            } else {
                timeDuration += mobileV16Service.deductLeaveDayNooRest(preShiftDef, preWorkTime, sTime, eTime);
            }
        }
        return timeDuration;
    }

    /**
     * 处理工作日逻辑
     */
    private void handleWorkday(WaEmpLeaveCancelDaytimeDo dayTime, long firstTime, long lastTime,
                               WaWorktimeDetail detail, WaShiftDef shiftDef, WaShiftDef shiftWorkTime,
                               long leaveDate, int tmpS, int tmpE) {
        boolean isKy = CdWaShiftUtil.checkCrossNightV2(shiftDef, detail.getDateType());
        long workStartTime = leaveDate + shiftWorkTime.getStartTime() * 60;
        long workEndTime = isKy ? (leaveDate + SECONDS_PER_DAY + shiftWorkTime.getEndTime() * 60) : (leaveDate + shiftWorkTime.getEndTime() * 60);

        long sTime = Math.max(workStartTime, firstTime);
        long eTime = Math.min(workEndTime, lastTime);

        tmpS = getLeaveDateHour(sTime);
        tmpE = getLeaveDateHour(eTime);

        if (detail.getIsNoonRest() != null && detail.getIsNoonRest()) {
            int timeDuration = mobileV16Service.deductLeaveDayNooRest(shiftDef, detail, sTime, eTime);
            setTimeDuration(dayTime, tmpS, tmpE, (float) timeDuration);
        } else {
            setTimeDuration(dayTime, tmpS, tmpE, (float) (tmpE - tmpS));
        }
    }

    /**
     * 请多天假
     *
     * @param dayTime     销假实体
     * @param firstDate   开始时间
     * @param lastDate    结束时间
     * @param firstTime   开始时间
     * @param lastTime    结束时间
     * @param pbMap       排班分组
     * @param shiftMap    班次分组
     * @param waLeaveType 假期类型
     * @throws Exception
     */
    private void calLeaveTimeByPeriod3ForMultiDay(WaEmpLeaveCancelDaytimeDo dayTime, long firstDate, long lastDate,
                                                  long firstTime, long lastTime, Map<Long, WaWorktimeDetail> pbMap,
                                                  Map<Integer, WaShiftDef> shiftMap, WaLeaveTypeDo waLeaveType) throws Exception {
        long leaveDate = dayTime.getLeaveCancelDate();
        WaWorktimeDetail workTimeDetail = pbMap.get(leaveDate);
        WaShiftDef shiftDef = shiftMap.get(Optional.ofNullable(dayTime.getShiftDefId()).orElse(workTimeDetail.getShiftDefId()));
        boolean includeNonWorkday = (DateTypeEnum.DATE_TYP_2.getIndex().equals(workTimeDetail.getDateType()) && waLeaveType.getIsRestDay())
                || (DateTypeEnum.DATE_TYP_3.getIndex().equals(workTimeDetail.getDateType()) && waLeaveType.getIsLegalHoliday())
                || (DateTypeEnum.DATE_TYP_4.getIndex().equals(workTimeDetail.getDateType()) && waLeaveType.getIsRestDay())
                || (DateTypeEnum.DATE_TYP_5.getIndex().equals(workTimeDetail.getDateType()) && waLeaveType.getIsLegalHoliday());
        if (leaveDate == lastDate) {//最后一天
            calLeaveTimeByPeriod3ForLastDay(dayTime, lastDate, lastTime, pbMap, shiftMap, workTimeDetail, includeNonWorkday, shiftDef);
        } else if (leaveDate == firstDate) {//第一天
            calLeaveTimeByPeriod3ForFirstDay(dayTime, firstDate, lastDate, firstTime, lastTime, workTimeDetail, shiftDef, includeNonWorkday);
        } else {//是中间一天
            calLeaveTimeByPeriod3ForMiddleDay(dayTime, lastDate, lastTime, workTimeDetail, shiftDef, includeNonWorkday);
        }
    }

    /**
     * 计算最后一天销假时间
     *
     * @param dayTime           销假实体
     * @param lastDate          最后一天
     * @param lastTime          最后一天
     * @param pbMap             排班分组
     * @param shiftMap          班次分组
     * @param workTimeDetail    排班
     * @param includeNonWorkday 是否非工作日：true/false
     * @param shiftDef          班次
     */
    private void calLeaveTimeByPeriod3ForLastDay(WaEmpLeaveCancelDaytimeDo dayTime, long lastDate, long lastTime,
                                                 Map<Long, WaWorktimeDetail> pbMap, Map<Integer, WaShiftDef> shiftMap,
                                                 WaWorktimeDetail workTimeDetail, boolean includeNonWorkday,
                                                 WaShiftDef shiftDef) {
        // 初始化变量
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        boolean isKy = CdWaShiftUtil.checkCrossNightV2(shiftDef, workTimeDetail.getDateType());
        long leaveDate = dayTime.getLeaveCancelDate();
        long workStartTime = leaveDate + shiftWorkTime.getStartTime() * 60;
        long workEndTime = isKy ? (leaveDate + 86400 + shiftWorkTime.getEndTime() * 60) : (leaveDate + shiftWorkTime.getEndTime() * 60);
        int endMin = getLeaveDateHour(lastTime);

        // 判断是否为非工作日
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(workTimeDetail.getDateType())) {
            handleNonWorkday(dayTime, lastDate, pbMap, shiftMap, endMin, includeNonWorkday);
        } else {
            handleWorkday(dayTime, lastDate, lastTime, shiftDef, workTimeDetail, shiftWorkTime, isKy, workStartTime, workEndTime, endMin);
        }
    }

    /**
     * 处理非工作日逻辑
     */
    private void handleNonWorkday(WaEmpLeaveCancelDaytimeDo dayTime, long lastDate, Map<Long, WaWorktimeDetail> pbMap,
                                  Map<Integer, WaShiftDef> shiftMap, int endMin, boolean includeNonWorkday) {
        Long preDate = DateUtil.addDate(lastDate * 1000, -1);
        WaWorktimeDetail preWorkTime = pbMap.get(preDate);

        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(preWorkTime.getDateType())) {
            boolean ifBelongPreDay = false;
            for (Integer preShiftDefId : preWorkTime.doGetShiftDefIdList()) {
                WaShiftDef preShiftDef = shiftMap.get(preShiftDefId);
                if (preShiftDef == null || !CdWaShiftUtil.checkCrossNightV2(preShiftDef, preWorkTime.getDateType())) {
                    continue;
                }
                ifBelongPreDay = true;
                WaShiftDef preShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(preShiftDef);
                updateTimeDuration(dayTime, includeNonWorkday, preShiftWorkTime.getEndTime(), endMin);
                break;
            }
            if (!ifBelongPreDay) {
                updateTimeDuration(dayTime, includeNonWorkday, 0, endMin);
            }
        } else {
            updateTimeDuration(dayTime, includeNonWorkday, 0, endMin);
        }
    }

    /**
     * 处理工作日逻辑
     */
    private void handleWorkday(WaEmpLeaveCancelDaytimeDo dayTime, long lastDate, long lastTime, WaShiftDef shiftDef,
                               WaWorktimeDetail workTimeDetail, WaShiftDef shiftWorkTime, boolean isKy,
                               long workStartTime, long workEndTime, int endMin) {
        if (endMin < shiftWorkTime.getStartTime()) {
            setTimeDuration(dayTime, 0, 0, 0f);
        } else {
            int startMin = isKy ? getLeaveDateHour(workStartTime) : shiftWorkTime.getStartTime();
            endMin = isKy ? Math.min(endMin, MINUTES_PER_DAY) : getLeaveDateHour(Math.min(workEndTime, lastTime));
            long sTime = isKy ? workStartTime : lastDate + startMin * 60L;
            long eTime = lastDate + endMin * 60L;

            List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getMultiWorkTimeList(shiftDef);
            int timeDuration = calculateTimeDuration(shiftDef, workTimeDetail, sTime, eTime, multiWorkTimeList);
            setTimeDuration(dayTime, startMin, endMin, (float) timeDuration);
        }
    }

    /**
     * 计算时间间隔
     */
    private int calculateTimeDuration(WaShiftDef shiftDef, WaWorktimeDetail workTimeDetail, long sTime, long eTime,
                                      List<MultiWorkTimeBaseDto> multiWorkTimeList) {
        if (multiWorkTimeList != null && multiWorkTimeList.size() > 1) {
            int totalDuration = 0;
            for (MultiWorkTimeBaseDto itemWorkTime : multiWorkTimeList) {
                long itemWorkStartTime = sTime + itemWorkTime.doGetRealStartTime() * 60;
                long itemWorkEndTime = sTime + itemWorkTime.doGetRealEndTime() * 60;
                if (sTime >= itemWorkEndTime || eTime <= itemWorkStartTime) {
                    continue;
                }
                long itemStartTime = Math.max(itemWorkStartTime, sTime);
                long itemEndTime = Math.min(itemWorkEndTime, eTime);
                totalDuration += mobileV16Service.deductLeaveDayNooRest(shiftDef, workTimeDetail, itemStartTime, itemEndTime);
            }
            return totalDuration;
        } else {
            return mobileV16Service.deductLeaveDayNooRest(shiftDef, workTimeDetail, sTime, eTime);
        }
    }

    /**
     * 更新时间间隔
     */
    private void updateTimeDuration(WaEmpLeaveCancelDaytimeDo dayTime, boolean includeNonWorkday, int startMin, int endMin) {
        if (includeNonWorkday) {
            int timeDuration = endMin - startMin;
            setTimeDuration(dayTime, startMin, endMin, Math.min((float) timeDuration, MAX_TIME_DURATION));
        } else {
            setTimeDuration(dayTime, startMin, 0, 0f);
        }
    }

    /**
     * 设置时间间隔
     */
    private void setTimeDuration(WaEmpLeaveCancelDaytimeDo dayTime, int startMin, int endMin, float timeDuration) {
        dayTime.setStartTime(startMin);
        dayTime.setEndTime(endMin);
        dayTime.setTimeDuration(timeDuration);
    }

    /**
     * 计算最后一天销假时间
     *
     * @param dayTime           销假实体
     * @param firstDate         第一天
     * @param lastDate          最后一天
     * @param firstTime         第一天
     * @param lastTime          最后一天
     * @param workTimeDetail    排班
     * @param shiftDef          班次
     * @param includeNonWorkday 是否非工作日：true/false
     * @throws Exception
     */
    private void calLeaveTimeByPeriod3ForFirstDay(WaEmpLeaveCancelDaytimeDo dayTime, long firstDate, long lastDate,
                                                  long firstTime, long lastTime, WaWorktimeDetail workTimeDetail,
                                                  WaShiftDef shiftDef, boolean includeNonWorkday) throws Exception {
        // 初始化变量
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        boolean isKy = CdWaShiftUtil.checkCrossNightV2(shiftDef, workTimeDetail.getDateType());
        long leaveDate = dayTime.getLeaveCancelDate();
        long workStartTime = leaveDate + shiftWorkTime.getStartTime() * 60;

        int startMin = getLeaveDateHour(firstTime);
        int endMin = MINUTES_PER_DAY;

        // 判断是否为非工作日
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(workTimeDetail.getDateType())) {
            handleNonWorkday(dayTime, startMin, endMin, includeNonWorkday);
        } else {
            handleWorkday(dayTime, firstDate, lastDate, firstTime, lastTime, shiftDef, workTimeDetail, shiftWorkTime, isKy, leaveDate, workStartTime);
        }
    }

    /**
     * 处理非工作日逻辑
     */
    private void handleNonWorkday(WaEmpLeaveCancelDaytimeDo dayTime, int startMin, int endMin, boolean includeNonWorkday) {
        if (includeNonWorkday) {
            int timeDuration = endMin - startMin;
            setTimeDuration(dayTime, startMin, endMin, Math.min((float) timeDuration, MAX_TIME_DURATION));
        } else {
            setTimeDuration(dayTime, 0, 0, 0f);
        }
    }

    /**
     * 处理工作日逻辑
     */
    private void handleWorkday(WaEmpLeaveCancelDaytimeDo dayTime, long firstDate, long lastDate, long firstTime, long lastTime,
                               WaShiftDef shiftDef, WaWorktimeDetail workTimeDetail, WaShiftDef shiftWorkTime, boolean isKy,
                               long leaveDate, long workStartTime) throws Exception {
        int startMin = getLeaveDateHour(Math.max(workStartTime, firstTime));
        int endMin;
        long etime;

        // 计算结束时间和分钟数
        int dateDiff = DateUtilExt.getDifferenceDay(firstDate, lastDate);
        if (isKy) {
            if (dateDiff > 1) { // 申请日期天数 > 2天
                endMin = shiftWorkTime.getEndTime();
            } else { // 申请日期天数 <= 2天
                endMin = Math.min(getLeaveDateHour(lastTime), shiftWorkTime.getEndTime());
            }
            etime = leaveDate + 86400 + endMin * 60L;
            endMin = MINUTES_PER_DAY;
        } else {
            endMin = shiftWorkTime.getEndTime();
            etime = leaveDate + endMin * 60L;
        }

        // 计算时间间隔
        List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getMultiWorkTimeList(shiftDef);
        int timeDuration = calculateTimeDuration(shiftDef, workTimeDetail, Math.max(workStartTime, firstTime), etime, multiWorkTimeList);
        setTimeDuration(dayTime, startMin, endMin, (float) timeDuration);
    }

    /**
     * 销假中间日期
     *
     * @param dayTime           销假实体
     * @param lastDate          最后一天
     * @param lastTime          最后一天
     * @param workTimeDetail    排班
     * @param shiftDef          班次
     * @param includeNonWorkday 是否非工作日：true/false
     */
    private void calLeaveTimeByPeriod3ForMiddleDay(WaEmpLeaveCancelDaytimeDo dayTime, long lastDate, long lastTime,
                                                   WaWorktimeDetail workTimeDetail, WaShiftDef shiftDef,
                                                   boolean includeNonWorkday) {
        // 初始化变量
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        boolean isKy = CdWaShiftUtil.checkCrossNightV2(shiftDef, workTimeDetail.getDateType());
        long leaveDate = dayTime.getLeaveCancelDate();
        long workStartTime = leaveDate + shiftWorkTime.getStartTime() * 60;

        // 判断是否为工作日
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(workTimeDetail.getDateType())) {
            handleWorkday(dayTime, lastDate, lastTime, shiftDef, workTimeDetail, shiftWorkTime, isKy, workStartTime);
        } else if (includeNonWorkday) {
            handleNonWorkday(dayTime);
        }
    }

    /**
     * 处理工作日逻辑
     */
    private void handleWorkday(WaEmpLeaveCancelDaytimeDo dayTime, long lastDate, long lastTime, WaShiftDef shiftDef,
                               WaWorktimeDetail workTimeDetail, WaShiftDef shiftWorkTime, boolean isKy,
                               long workStartTime) {
        long leaveDate = dayTime.getLeaveCancelDate();
        if (leaveDate == (lastDate - 86400) && isKy) {
            // 倒数第二天并且跨夜
            int lastMin = getLeaveDateHour(lastTime);
            if (lastMin >= shiftWorkTime.getEndTime()) {
                setTimeDuration(dayTime, shiftWorkTime.getStartTime(), shiftWorkTime.getEndTime(), shiftDef.getWorkTotalTime().floatValue());
            } else {
                int timeDuration = mobileV16Service.deductLeaveDayNooRest(shiftDef, workTimeDetail, workStartTime, lastTime);
                setTimeDuration(dayTime, shiftWorkTime.getStartTime(), lastMin, (float) timeDuration);
            }
        } else {
            setTimeDuration(dayTime, shiftWorkTime.getStartTime(), shiftWorkTime.getEndTime(), shiftDef.getWorkTotalTime().floatValue());
        }
    }

    /**
     * 处理非工作日逻辑
     */
    private void handleNonWorkday(WaEmpLeaveCancelDaytimeDo dayTime) {
        setTimeDuration(dayTime, 0, MINUTES_PER_DAY, MAX_TIME_DURATION);
    }

    /**
     * 小时整天
     *
     * @param leaveType
     * @param detail
     * @param dayTime
     * @param shiftDef
     */
    private void processLeaveByPeriod4(WaLeaveTypeDo leaveType, WaWorktimeDetail detail, WaEmpLeaveCancelDaytimeDo dayTime, WaShiftDef shiftDef) {
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        // 特殊班次工时调整
        boolean isSpecial = Optional.ofNullable(shiftDef.getIsSpecial()).orElse(false) && shiftDef.getSpecialWorkTime() != null;
        //缺少特殊日期
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(detail.getDateType())) {
            if (isSpecial) {
                dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
            } else {
                dayTime.setTimeDuration(shiftDef.getWorkTotalTime().floatValue());
            }
            dayTime.setStartTime(shiftWorkTime.getStartTime());
            dayTime.setEndTime(shiftWorkTime.getEndTime());
        } else if (DateTypeEnum.DATE_TYP_2.getIndex().equals(detail.getDateType()) && leaveType.getIsRestDay()) {//是休息日连续计算
            if (isSpecial) {
                dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
            } else {
                dayTime.setTimeDuration(8 * 60f);
            }
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        } else if ((DateTypeEnum.DATE_TYP_3.getIndex().equals(detail.getDateType()) || DateTypeEnum.DATE_TYP_5.getIndex().equals(detail.getDateType())) && leaveType.getIsLegalHoliday()) {
            //是法定节假日连续计算
            if (isSpecial) {
                dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
            } else {
                dayTime.setTimeDuration(8 * 60f);
            }
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        } else if (DateTypeEnum.DATE_TYP_4.getIndex().equals(detail.getDateType()) && leaveType.getIsRestDay()) {
            //是休息日连续计算
            if (isSpecial) {
                dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
            } else {
                dayTime.setTimeDuration(8 * 60f);
            }
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        }
    }

    /**
     * 半天
     *
     * @param leaveType
     * @param detail
     * @param dayTime
     * @param sHalfDay
     * @param eHalfDay
     * @param nowDate
     * @param startDate
     * @param endDate
     */
    private void processLeaveByPeriod9(WaLeaveTypeDo leaveType, WaWorktimeDetail detail, WaEmpLeaveCancelDaytimeDo dayTime,
                                       String sHalfDay, String eHalfDay, long nowDate, long startDate, long endDate) {
        if (nowDate == startDate && nowDate == endDate) {//第一天也是最后一天
            if ("P".equals(sHalfDay)) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("P");
                dayTime.setEhalfDay("P");
            } else if ("A".equals(sHalfDay) && "A".equals(eHalfDay)) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("A");
            } else {
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(1f);
            }
        } else if (nowDate == startDate && nowDate < endDate) {//第一天不是最后一天
            if ("A".equals(sHalfDay)) {
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(1f);
            } else {
                dayTime.setShalfDay("P");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(0.5f);
            }
        } else if (nowDate > startDate && nowDate < endDate) {//中间一天
            if (detail.getDateType() == 1) {
                dayTime.setTimeDuration(1f);
            } else if (detail.getDateType() == 2 && leaveType.getIsRestDay()) {//是休息日连续计算
                dayTime.setTimeDuration(1f);
            } else if ((detail.getDateType() == 3 || detail.getDateType() == 5) && leaveType.getIsLegalHoliday()) {//是法定节假日连续计算
                dayTime.setTimeDuration(1f);
            } else if (detail.getDateType() == 4 && leaveType.getIsRestDay()) {
                dayTime.setTimeDuration(1f);
            }
            dayTime.setShalfDay("A");
            dayTime.setEhalfDay("P");
        } else if (nowDate > startDate && nowDate == endDate) {
            if ("P".equals(eHalfDay)) {
                dayTime.setTimeDuration(1f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
            } else if ("A".equals(eHalfDay)) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("A");
            }
        }
    }

    private Integer deductLeaveDayNooRest(WaWorktimeDetail workTimeDetail, Long startTime, Long endTime) throws Exception {
        //计算扣除中午休息的时间，多段
        long durationSec = endTime - startTime;
        if (workTimeDetail.getIsNoonRest() != null && workTimeDetail.getIsNoonRest()) {
            List<Map> restList = new ArrayList<>();
            //定义半天的休息时间段
            Integer restStart = workTimeDetail.getNoonRestStart();
            Integer restEnd = workTimeDetail.getNoonRestEnd();
            if (restStart != null && restEnd != null) {
                Map noonRestMap = new LinkedHashMap();
                noonRestMap.put("noonRestStart", restStart);
                noonRestMap.put("noonRestEnd", restEnd);
                restList.add(noonRestMap);
            }
            if (workTimeDetail.getRestPeriods() != null) {
                List<Map> restPeriods = (List) JacksonJsonUtil.jsonToBean(workTimeDetail.getRestPeriods().toString(), List.class);
                if (!CollectionUtils.isEmpty(restPeriods)) {
                    restList.addAll(restPeriods);
                }
            }
            if (!CollectionUtils.isEmpty(restList)) {
                for (Map periodMap : restList) {
                    Integer noonRestStartMi = (Integer) periodMap.get("noonRestStart");
                    Integer noonRestEndMi = (Integer) periodMap.get("noonRestEnd");

                    ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMi, noonRestEndMi, workTimeDetail.getStartTime(), workTimeDetail.getEndTime(), workTimeDetail.getDateType());
                    noonRestStartMi = restPeriod.getNoonRestStart();
                    noonRestEndMi = restPeriod.getNoonRestEnd();

                    long noonRestStartTime = workTimeDetail.getWorkDate() + noonRestStartMi * 60;
                    long noonRestEndTime = workTimeDetail.getWorkDate() + noonRestEndMi * 60;

                    if (startTime <= noonRestEndTime && endTime >= noonRestStartTime) {
                        durationSec -= Math.min(noonRestEndTime, endTime) - Math.max(noonRestStartTime, startTime);
                    }
                }
            }
        }

        return new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
    }

    private Integer getLeaveDateHour(Long time) {
        String s = DateUtil.convertDateTimeToStr(time, "HH:mm", true);
        if (StringUtils.isNotBlank(s)) {
            String[] shmArr = s.split(":");
            return Integer.parseInt(shmArr[0]) * 60 + Integer.parseInt(shmArr[1]);
        }
        return 0;
    }

    /**
     * 休假结束
     *
     * @param dto
     * @param cancelDo
     * @return
     */
    public Result doLeaveEndSave(LeaveCancelDto dto, WaEmpLeaveCancelDo cancelDo) {
        UserInfo userInfo = getUser();
        //休假单id不能为空
        if (null == dto.getLeaveId()) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ID_IS_NULL, Boolean.FALSE);
        }
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getById(dto.getLeaveId());
        WaEmpLeaveTimeDo empLeaveTime = waEmpLeaveTimeDo.getById(dto.getLeaveId());
        if (null == empLeave || null == empLeaveTime) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TIME_NOT_EXIST, Boolean.FALSE);
        }
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(empLeave.getLeaveTypeId());
        if (null == leaveType) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        //上传附件 附件必传
        //if (LeaveCancelTypeEnum.FILE_ADD.getIndex().equals(dto.getLeaveCancelType())) {
        String attachmentRequired = leaveType.getCancelAttachmentRequired();
        if (StringUtils.isNotBlank(attachmentRequired) && Arrays.stream(attachmentRequired.split(",")).anyMatch(r -> dto.getLeaveCancelType().equals(Integer.valueOf(r)))) {
            if (StringUtils.isEmpty(dto.getFileId()) || StringUtils.isEmpty(dto.getFileName())) {
                return ResponseWrap.wrapResult(AttendanceCodes.PLEASE_UPLOAD_FILE, Boolean.FALSE);
            }
        }
        //生成销假主数据
        long curTime = DateUtil.getCurrentTime(true);
        cancelDo.setTypeId(Long.valueOf(dto.getLeaveCancelType()));
        cancelDo.setLeaveCancelId(snowflakeUtil.createId());
        cancelDo.setLeaveId(empLeave.getLeaveId());
        cancelDo.setLeaveTypeId(empLeave.getLeaveTypeId());
        cancelDo.setEmpid(empLeave.getEmpid());
        cancelDo.setReason(dto.getReason());
        cancelDo.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex().shortValue());
        cancelDo.setTimeDuration(0f);
        cancelDo.setTimeUnit(leaveType.getAcctTimeType());
        cancelDo.setFileId(dto.getFileId());
        cancelDo.setFileName(dto.getFileName());
        cancelDo.setDeleted(0);
        cancelDo.setCreateTime(curTime);
        cancelDo.setCreateBy(userInfo.getUserId());
        cancelDo.setUpdateTime(curTime);
        cancelDo.setUpdateBy(userInfo.getUserId());
        cancelDo.setTenantId(userInfo.getTenantId());
        waEmpLeaveCancelDomainService.save(cancelDo);
        //将假期状态改为销假中
        WaEmpLeaveDo leaveDo = waEmpLeaveDo.getById(cancelDo.getLeaveId());
        leaveDo.setUpdtime(curTime);
        leaveDo.setLeaveStatus(LeaveCancelStatusEnum.LEAVE_CANCEL_IN.getIndex());
        waEmpLeaveDo.update(leaveDo);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 取消部分休假
     *
     * @param dto
     * @param cancelDo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result doCheckAndSave(LeaveCancelDto dto, WaEmpLeaveCancelDo cancelDo) throws Exception {
        //参数校验
        Result result = preCheck(dto);
        if (!result.isSuccess()) {
            return result;
        }
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getById(dto.getLeaveId());
        WaEmpLeaveTimeDo empLeaveTime = waEmpLeaveTimeDo.getById(dto.getLeaveId());
        if (null == empLeave || null == empLeaveTime) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TIME_NOT_EXIST, Boolean.FALSE);
        }
        if (empLeave.getStatus() != 2) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_NOT_ALLOWED, Boolean.FALSE);
        }
        if (empLeave.getCancelTimeDuration() != null &&
                empLeave.getCancelTimeDuration() >= empLeave.getTotalTimeDuration()) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ALREADY_CANCEL, Boolean.FALSE);
        }
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(empLeave.getLeaveTypeId());
        if (null == leaveType) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        Result<Boolean> checkForMultiShiftResult = LeaveCancelDto.checkForMultiShift(dto, leaveType.getAcctTimeType());
        if (!checkForMultiShiftResult.isSuccess()) {
            return Result.fail(checkForMultiShiftResult.getMsg());
        }
        //上传附件 附件必传
        String attachmentRequired = leaveType.getCancelAttachmentRequired();
        if (StringUtils.isNotBlank(attachmentRequired) && Arrays.stream(attachmentRequired.split(",")).anyMatch(r -> dto.getLeaveCancelType().equals(Integer.valueOf(r)))) {
            if (StringUtils.isEmpty(dto.getFileId()) || StringUtils.isEmpty(dto.getFileName())) {
                return ResponseWrap.wrapResult(AttendanceCodes.PLEASE_UPLOAD_FILE, Boolean.FALSE);
            }
        }
        UserInfo userInfo = getUser();
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(userInfo.getTenantId(), empLeave.getEmpid());
        // 数据类型转换
        List<WaEmpLeaveCancelInfoDo> timeInfoList = ObjectConverter.convertList(dto.getTimeInfoList(), WaEmpLeaveCancelInfoDo.class);
        // 设置periodType
        setPeriodType(leaveType, timeInfoList);
        // 查询员工班次
        Map<Integer, WaShiftDef> corpAllShiftDef = waCommonService.getCorpAllShiftDef(userInfo.getTenantId());
        if (MapUtils.isEmpty(corpAllShiftDef)) {
            return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NOT_EXIST, Boolean.FALSE);
        }
        Map<Long, WaWorktimeDetail> empShiftMap = getEmpShift(empInfo, dto, empLeaveTime, userInfo);
        if (MapUtils.isEmpty(empShiftMap)) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, Boolean.FALSE);
        }
        //生成销假主数据
        long curTime = System.currentTimeMillis() / 1000L;
        cancelDo.setTypeId(Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex()));
        cancelDo.setLeaveCancelId(snowflakeUtil.createId());
        cancelDo.setLeaveId(empLeave.getLeaveId());
        cancelDo.setLeaveTypeId(empLeave.getLeaveTypeId());
        cancelDo.setEmpid(empLeave.getEmpid());
        cancelDo.setReason(dto.getReason());
        cancelDo.setTimeDuration(0f);
        cancelDo.setTimeUnit(leaveType.getAcctTimeType());
        cancelDo.setPeriodType(timeInfoList.get(0).getPeriodType());
        cancelDo.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex().shortValue());
        cancelDo.setFileId(dto.getFileId());
        cancelDo.setFileName(dto.getFileName());
        cancelDo.setDeleted(0);
        cancelDo.setCreateTime(curTime);
        cancelDo.setCreateBy(userInfo.getUserId());
        cancelDo.setUpdateTime(curTime);
        cancelDo.setUpdateBy(userInfo.getUserId());
        cancelDo.setTenantId(userInfo.getTenantId());
        waEmpLeaveCancelDomainService.save(cancelDo);
        //将假期状态改为销假中
        WaEmpLeaveDo leaveDo = waEmpLeaveDo.getById(cancelDo.getLeaveId());
        leaveDo.setUpdtime(curTime);
        leaveDo.setLeaveStatus(LeaveCancelStatusEnum.LEAVE_CANCEL_IN.getIndex());
        waEmpLeaveDo.update(leaveDo);
        List<WaEmpLeaveCancelDaytimeDo> leaveCancelDaytimeList = new ArrayList<>();
        BigDecimal totalTime = BigDecimal.ZERO;
        List<WaLeaveDaytimeDo> leaveDayTimes = waLeaveDaytimeDo.getByDate(empLeave.getLeaveId(), null);
        for (WaEmpLeaveCancelInfoDo timeInfo : timeInfoList) {
            // 设置实际销假时间
            setRealTime(timeInfo, corpAllShiftDef, empShiftMap);
            // 班次校验
            Long startDate = DateUtil.getOnlyDate(new Date(timeInfo.getStartTime() * 1000));
            Long endDate = DateUtil.getOnlyDate(new Date(timeInfo.getEndTime() * 1000));
            doCheckEmpShift(empShiftMap, startDate, endDate);
            // 考勤截止日判断
            checkSobEndDate(empInfo.getEmpid(), endDate);
            // 校验是否能请
            checkDateType(leaveType, empShiftMap, timeInfo);
            // 计算销假时长
            List<WaEmpLeaveCancelDaytimeDo> list = new ArrayList<>();
            BigDecimal time = doCalTime(empLeave.getLeaveId(), empInfo, timeInfo, corpAllShiftDef, empShiftMap, leaveDayTimes, leaveType, list);
            totalTime = totalTime.add(time);
            leaveCancelDaytimeList.addAll(list);
            //生成销假明细
            timeInfo.setLeaveCancelInfoId(snowflakeUtil.createId());
            timeInfo.setLeaveCancelId(cancelDo.getLeaveCancelId());
            timeInfo.setTimeDuration(time.floatValue());
            timeInfo.setTimeUnit(leaveType.getAcctTimeType());
            timeInfo.setDeleted(0);
            timeInfo.setCreateBy(userInfo.getUserId());
            timeInfo.setCreateTime(curTime);
            timeInfo.setUpdateBy(userInfo.getUserId());
            timeInfo.setUpdateTime(curTime);
            waEmpLeaveCancelInfoDomainService.save(timeInfo);
            //生成每日明细数据
            for (WaEmpLeaveCancelDaytimeDo daytimeDo : list) {
                daytimeDo.setLeaveCancelDaytimeId(snowflakeUtil.createId());
                daytimeDo.setLeaveCancelId(cancelDo.getLeaveCancelId());
                daytimeDo.setLeaveCancelInfoId(timeInfo.getLeaveCancelInfoId());
                daytimeDo.setDeleted(0);
                daytimeDo.setCreateBy(userInfo.getUserId());
                daytimeDo.setCreateTime(curTime);
                daytimeDo.setUpdateBy(userInfo.getUserId());
                daytimeDo.setUpdateTime(curTime);
            }
            waEmpLeaveCancelDaytimeDomainService.saveBatch(list);
        }
        if (checkTime(leaveCancelDaytimeList, corpAllShiftDef)) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.TIME_REPETITION, Boolean.FALSE).getMsg());
        }
        SessionBean sessionBean = new SessionBean();
        sessionBean.setLanguage(SessionHolder.getLang());
        if (leaveType.getAcctTimeType() == 1) {
            if (leaveType.getRoundTimeUnit() != null && totalTime.floatValue() % leaveType.getRoundTimeUnit() > 0) {
                String message = messageResource.getMessage("L005569", new Object[]{leaveType.getLeaveName(), leaveType.getRoundTimeUnit()}, new Locale(sessionBean.getLanguage()));
                throw new CDException(message);
            }
        } else {//小时
            if (leaveType.getRoundTimeUnit() != null && totalTime.floatValue() % (60 * leaveType.getRoundTimeUnit()) > 0) {
                String message = messageResource.getMessage("L005570", new Object[]{leaveType.getLeaveName(), leaveType.getRoundTimeUnit()}, new Locale(sessionBean.getLanguage()));
                throw new CDException(message);
            }
        }
        //更新总时长
        cancelDo.setTimeDuration(totalTime.floatValue());
        waEmpLeaveCancelDomainService.update(cancelDo);
        return Result.ok(Boolean.TRUE);
    }

    @Transactional
    public Result<String> process(WaEmpLeaveCancelDo cancelDo) throws Exception {
        UserInfo user = getUser();
        String businessKey = String.valueOf(cancelDo.getLeaveCancelId());

        //流程增加事件时间
        Long startDate = 0L;
        Long endDate = 0L;
        WaEmpLeaveCancelInfoDo leaveCancelInfo = null;
        if (Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex()).equals(cancelDo.getTypeId())) {
            List<WaEmpLeaveCancelInfoDo> leaveCancelInfoList = waEmpLeaveCancelInfoDomainService.getLeaveCancelInfoList(cancelDo.getLeaveCancelId());
            WaEmpLeaveCancelInfoDo cancelInfo = leaveCancelInfoList.get(0);
            leaveCancelInfo = cancelInfo;
            startDate = cancelInfo.getStartTime();
            endDate = cancelInfo.getEndTime();
        }

        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(user.getTenantId(), cancelDo.getEmpid());
        LogRecordContext.putVariable("name", empInfo.getEmpName());

        String wfBusKey = String.format("%s_%s", businessKey, BusinessCodeEnum.VACATION.getCode());
        // 检查流程是否已启用
        Result<?> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.VACATION.getCode());
        if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
            return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, "");
        }
        Boolean workflowEnabledResultData = (Boolean) checkWorkflowEnableResult.getData();
        if (!workflowEnabledResultData) {
            if (configService.checkSwitchStatus(SysConfigsEnum.LEAVE_CANCEL_WORKFLOW_SWITCH.name())) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
            }
            WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
            wfCallbackResultDto.setBusinessKey(wfBusKey);
            wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
            wfCallbackResultDto.setTenantId(empInfo.getBelongOrgId());
            workflowCallBackService.saveLeaveCancelApproval(wfCallbackResultDto);
        } else {
            try {
                //新工作流
                WfBeginWorkflowDto dto = new WfBeginWorkflowDto();
                dto.setFuncCode(BusinessCodeEnum.VACATION.getCode());
                dto.setBusinessId(businessKey);
                dto.setApplicantId(empInfo.getEmpid().toString());
                dto.setApplicantName(empInfo.getEmpName());
                if (startDate != null && endDate != null) {
                    dto.setEventTime(startDate * 1000);
                    dto.setEventEndTime(endDate * 1000);
                }
                dto.setTimeSlot(getTimeSlot(cancelDo, leaveCancelInfo));
                Result<?> result = wfRegisterFeign.begin(dto);
                if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                    if (configService.checkSwitchStatus(SysConfigsEnum.LEAVE_CANCEL_WORKFLOW_SWITCH.name())) {
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                if (e instanceof CDException) {
                    if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                        throw new CDException("工作流配置错误，请联系管理员");
                    } else {
                        throw e;
                    }
                } else {
                    throw new CDException("工作流启动异常，请检查流程配置");
                }
            }
        }

        return Result.ok(wfBusKey);
    }

    public String getTimeSlot(WaEmpLeaveCancelDo cancelDo, WaEmpLeaveCancelInfoDo leaveCancelInfo) {
        if (Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex()).equals(cancelDo.getTypeId())) {
            Integer periodType = Integer.valueOf(leaveCancelInfo.getPeriodType());
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                return String.format("%s~%s", DateUtil.getDateStrByTimesamp(leaveCancelInfo.getStartTime()), DateUtil.getDateStrByTimesamp(leaveCancelInfo.getEndTime()));
            } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                return String.format("%s %s~%s %s", DateUtil.getDateStrByTimesamp(leaveCancelInfo.getStartTime()), DayHalfTypeEnum.getDesc(leaveCancelInfo.getShalfDay()), DateUtil.getDateStrByTimesamp(leaveCancelInfo.getEndTime()), DayHalfTypeEnum.getDesc(leaveCancelInfo.getEhalfDay()));
            } else {
                return String.format("%s~%s", DateUtil.getTimeStrByTimesamp4(leaveCancelInfo.getShiftStartTime()), DateUtil.getTimeStrByTimesamp4(leaveCancelInfo.getShiftEndTime()));
            }
        } else if (Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST.getIndex()).equals(cancelDo.getTypeId())) {
            if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(Integer.valueOf(cancelDo.getStatus()))
                    && !ApprovalStatusEnum.PASSED.getIndex().equals(Integer.valueOf(cancelDo.getStatus()))) {
                String adjustTimeSlot = cancelDo.getAdjustTimeSlot();
                if (StringUtil.isNotBlank(adjustTimeSlot)) {
                    return adjustTimeSlot;
                }
            } else {
                WaEmpLeaveDo empLeave = waEmpLeaveDo.getLeaveDetailById(cancelDo.getTenantId(), Long.valueOf(cancelDo.getLeaveId()));
                if (null == empLeave) {
                    return "";
                }
                Long startTime = empLeave.getStartTime();
                Long endTime = empLeave.getEndTime();
                Integer periodType = empLeave.getPeriodType() != null ? Integer.valueOf(empLeave.getPeriodType()) : null;
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    return String.format("%s~%s", DateUtil.getDateStrByTimesamp(startTime), DateUtil.getDateStrByTimesamp(endTime));
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    return String.format("%s %s~%s %s", DateUtil.getDateStrByTimesamp(startTime), DayHalfTypeEnum.getDesc(empLeave.getShalfDay()), DateUtil.getDateStrByTimesamp(endTime), DayHalfTypeEnum.getDesc(empLeave.getEhalfDay()));
                } else {
                    if (empLeave.getShiftStartTime() != null && empLeave.getShiftEndTime() != null) {
                        startTime = empLeave.getShiftStartTime();
                        endTime = empLeave.getShiftEndTime();
                    }
                    return String.format("%s~%s", DateUtil.getTimeStrByTimesamp4(startTime), DateUtil.getTimeStrByTimesamp4(endTime));
                }
            }
            return "";
        } else {
            Integer leaveId = cancelDo.getLeaveId();
            WaEmpLeaveTimeExample example = new WaEmpLeaveTimeExample();
            WaEmpLeaveTimeExample.Criteria criteria = example.createCriteria();
            criteria.andLeaveIdEqualTo(leaveId);
            List<WaEmpLeaveTime> list = waEmpLeaveTimeMapper.selectByExample(example);
            WaEmpLeaveTime empLeaveTime = list.get(0);
            Long shiftStartTime = empLeaveTime.getShiftStartTime();
            Long shiftEndTime = empLeaveTime.getShiftEndTime();
            Integer periodType = empLeaveTime.getPeriodType().intValue();
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                return String.format("%s~%s", DateUtil.getDateStrByTimesamp(shiftStartTime), DateUtil.getDateStrByTimesamp(shiftEndTime));
            } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                Long start = empLeaveTime.getStartTime();
                Long end = empLeaveTime.getEndTime();
                return String.format("%s %s~%s %s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeaveTime.getShalfDay()), DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeaveTime.getEhalfDay()));
            } else {
                return String.format("%s~%s", DateUtil.getTimeStrByTimesamp(shiftStartTime), DateUtil.getTimeStrByTimesamp(shiftEndTime));
            }
        }
    }

    /**
     * 调整时间
     */
    @Transactional
    public Result<Boolean> handleAdjustTime(LeaveCancelDto dto, WaEmpLeaveCancelDo cancel) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();
        //休假单id不能为空
        if (null == dto.getLeaveId()) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ID_IS_NULL, Boolean.FALSE);
        }
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getLeaveDetailById(userInfo.getTenantId(), Long.valueOf(dto.getLeaveId()));
        WaEmpLeaveTimeDo empLeaveTime = waEmpLeaveTimeDo.getById(dto.getLeaveId());
        if (null == empLeave || null == empLeaveTime) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TIME_NOT_EXIST, Boolean.FALSE);
        }
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(empLeave.getLeaveTypeId());
        if (null == leaveType) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        Result<Boolean> checkForMultiShiftResult = LeaveCancelDto.checkForMultiShift(dto, leaveType.getAcctTimeType());
        if (!checkForMultiShiftResult.isSuccess()) {
            return Result.fail(checkForMultiShiftResult.getMsg());
        }
        //上传附件 附件必传
        String attachmentRequired = leaveType.getCancelAttachmentRequired();
        if (StringUtils.isNotBlank(attachmentRequired) && Arrays.stream(attachmentRequired.split(",")).anyMatch(r -> dto.getLeaveCancelType().equals(Integer.valueOf(r)))) {
            if (StringUtils.isEmpty(dto.getFileId()) || StringUtils.isEmpty(dto.getFileName())) {
                return ResponseWrap.wrapResult(AttendanceCodes.PLEASE_UPLOAD_FILE, Boolean.FALSE);
            }
        }
        //撤销原休假单，撤销休假单地销假单，返还额度
        Result<Boolean> revokeResult = leaveApplyService.revokeEmpLeave(new RevokeEmpLeaveDto(dto.getLeaveId(), "", null), userInfo, false, false);
        if (revokeResult.getCode() != 0) {
            return revokeResult;
        }
        LeaveApplySaveDto leaveApplySaveDto = ObjectConverter.convert(dto, LeaveApplySaveDto.class);
        leaveApplySaveDto.setLeaveTypeId(leaveType.getLeaveTypeId());
        leaveApplySaveDto.setEmpid(empLeave.getEmpid());
        leaveApplySaveDto.setOpenPreCheck(false);
        //获取新休假单时长
        Map map = waLeaveService.getLeaveTotalTime(leaveApplySaveDto);
        if ("-1".equals(map.get("status").toString())) {
            String wfBusKey = String.format("%s_%s", empLeave.getLeaveId(), BusinessCodeEnum.LEAVE.getCode());
            workflowCallBackService.saveWfLeaveApproval(workflowCallBackService.getWfCallbackResultDto(wfBusKey, userInfo.getTenantId(), WfCallbackTriggerOperationEnum.APPROVED));
            throw new Exception(map.get("message").toString());
        }
        leaveApplySaveDto.setLeaveId(null);
        leaveApplySaveDto.setReason("");

        //生成新地休假单，不走工作流
        Result leaveApplyResult = leaveApplyService.saveLeaveApply(leaveApplySaveDto, false);
        if (leaveApplyResult.getCode() != 0) {
            String wfBusKey = String.format("%s_%s", empLeave.getLeaveId(), BusinessCodeEnum.LEAVE.getCode());
            workflowCallBackService.saveWfLeaveApproval(workflowCallBackService.getWfCallbackResultDto(wfBusKey, userInfo.getTenantId(), WfCallbackTriggerOperationEnum.APPROVED));
            throw new Exception(leaveApplyResult.getMsg());
        }
        LeaveApplyResultDto applyResultDto = (LeaveApplyResultDto) leaveApplyResult.getData();
        String leaveData = applyResultDto.getBusinessKey();
        Integer newLeaveId = Integer.valueOf(leaveData.split("_")[0]);

        //生成销假主数据
        long curTime = DateUtil.getCurrentTime(true);
        cancel.setTypeId(Long.valueOf(dto.getLeaveCancelType()));
        cancel.setLeaveCancelId(snowflakeUtil.createId());
        cancel.setLeaveId(newLeaveId);
        cancel.setLeaveTypeId(empLeave.getLeaveTypeId());
        cancel.setEmpid(empLeave.getEmpid());
        cancel.setReason(dto.getReason());
        cancel.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex().shortValue());
        cancel.setTimeDuration(0f);
        cancel.setTimeUnit(leaveType.getAcctTimeType());
        cancel.setFileId(dto.getFileId());
        cancel.setFileName(dto.getFileName());
        cancel.setDeleted(0);
        cancel.setCreateTime(curTime);
        cancel.setCreateBy(Long.valueOf(userInfo.getUserid()));
        cancel.setUpdateTime(curTime);
        cancel.setUpdateBy(Long.valueOf(userInfo.getUserid()));
        cancel.setTenantId(userInfo.getTenantId());
        cancel.setOriginalLeaveId(dto.getLeaveId());
        WaEmpLeaveDo newEmpLeave = waEmpLeaveDo.getLeaveDetailById(userInfo.getTenantId(), Long.valueOf(newLeaveId));
        cancel.setAdjustTimeSlot(getTimeSlot(newEmpLeave));
        cancel.setAdjustDuration(getTimeDurationStr(newEmpLeave));
        waEmpLeaveCancelDomainService.save(cancel);
        return Result.ok(Boolean.TRUE);
    }

    private String getTimeDurationStr(WaEmpLeaveDo empLeave) {
        Float totalTimeDuration = empLeave.getTotalTimeDuration();
        if (empLeave.getTimeUnit() == 1) {
            return totalTimeDuration + PreTimeUnitEnum.getName(empLeave.getTimeUnit());
        } else {
            BigDecimal applyTime = BigDecimal.valueOf(totalTimeDuration);
            return applyTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit());
        }
    }

    private String getTimeSlot(WaEmpLeaveDo empLeave) {
        if (null == empLeave) {
            return "";
        }
        Integer periodType = empLeave.getPeriodType() != null ? Integer.valueOf(empLeave.getPeriodType()) : null;
        String cancelTimeSlot;
        Long startTime = empLeave.getStartTime();
        Long endTime = empLeave.getEndTime();
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
            cancelTimeSlot = String.format("%s~%s", DateUtil.getDateStrByTimesamp(startTime), DateUtil.getDateStrByTimesamp(endTime));
        } else {
            if (empLeave.getShiftStartTime() != null && empLeave.getShiftEndTime() != null) {
                startTime = empLeave.getShiftStartTime();
                endTime = empLeave.getShiftEndTime();
            }
            if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                Long start = empLeave.getStartTime();
                Long end = empLeave.getEndTime();
                cancelTimeSlot = String.format("%s~%s", String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeave.getShalfDay())),
                        String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeave.getEhalfDay())));
            } else {
                cancelTimeSlot = String.format("%s~%s", DateUtil.getTimeStrByTimesamp4(startTime), DateUtil.getTimeStrByTimesamp4(endTime));
            }
        }
        return cancelTimeSlot;
    }

    /**
     * 假期明细
     *
     * @param id
     * @return
     */
    public LeaveInfoDto getLeaveInfo(Long id) {
        UserInfo user = getUser();
        LeaveInfoDto dto = new LeaveInfoDto();
        WaEmpLeaveDo waLeaveDo = waEmpLeaveDo.getLeaveDetailById(user.getTenantId(), id);
        if (id != null) {
            Short periodType = waLeaveDo.getPeriodType();
            dto.setEmpName(waLeaveDo.getEmpName());
            dto.setWorkNo(waLeaveDo.getWorkno());
            //整天
            if (periodType == 1 || periodType == 4) {
                dto.setStartTime(DateUtil.getDateStrByTimesamp(waLeaveDo.getShiftStartTime()));
                dto.setEndTime(DateUtil.getDateStrByTimesamp(waLeaveDo.getShiftEndTime()));
            } else {
                if (periodType == 9) {
                    Long start = waLeaveDo.getStartTime();
                    Long end = waLeaveDo.getEndTime();
                    dto.setStartTime(String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(waLeaveDo.getShalfDay())));
                    dto.setEndTime(String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(waLeaveDo.getEhalfDay())));
                } else {
                    dto.setStartTime(DateUtil.getTimeStrByTimesamp(waLeaveDo.getShiftStartTime()));
                    dto.setEndTime(DateUtil.getTimeStrByTimesamp(waLeaveDo.getShiftEndTime()));
                }
            }
            Float totalTimeDuration = waLeaveDo.getTotalTimeDuration();
            if (LeaveTypeUnitEnum.HOUR.getIndex().equals(waLeaveDo.getTimeUnit())) {
                totalTimeDuration = new BigDecimal(totalTimeDuration).divide(new BigDecimal(60), 2, RoundingMode.DOWN).floatValue();
            }
            dto.setTotalTimeDuration(totalTimeDuration);
            dto.setLeaveName(waLeaveDo.getLeaveName());
            Float actualDuration = waLeaveDo.getTotalTimeDuration() - waLeaveDo.getCancelTimeDuration();
            if (LeaveTypeUnitEnum.HOUR.getIndex().equals(waLeaveDo.getTimeUnit())) {
                actualDuration = new BigDecimal(actualDuration).divide(new BigDecimal(60), 2, RoundingMode.DOWN).floatValue();
            }
            dto.setActualDuration(actualDuration);
            dto.setUnit(waLeaveDo.getTimeUnit());
            dto.setRemark(waLeaveDo.getLeaveCancelRemark());
        }
        return dto;
    }

    /**
     * 销假申请
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result saveCancelLeave(LeaveCancelDto dto) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();
        String lockKey = MessageFormat.format("SAVE_USER_LEAVE_CANCEL_APPLY_LOCK_{0}_{1}", userInfo.getTenantId(), userInfo.getStaffId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 10);
        WaEmpLeaveCancelDo cancel = new WaEmpLeaveCancelDo();
        Result saveResult;
        //1.校验及保存入库
        try {
            switch (dto.getLeaveCancelType()) {
                //取消部分休假（原时间调整）
                case 2:
                    saveResult = doCheckAndSave(dto, cancel);
                    break;
                //休假确认
                case 1:
                    //补充附件
                case 3:
                    //取消休假（原休假取消）
                case 4:
                    saveResult = doLeaveEndSave(dto, cancel);
                    break;
                //调整时间（新增）
                case 5:
                    saveResult = handleAdjustTime(dto, cancel);
                    break;
                default:
                    return ResponseWrap.wrapResult(AttendanceCodes.PLEASE_SELECT_LEAVE_CANCEL_TYPE, Boolean.FALSE);
            }
            if (!saveResult.isSuccess()) {
                return saveResult;
            }
            WaEmpLeave empLeave = waEmpLeaveMapper.selectByPrimaryKey(dto.getLeaveId());
            LogRecordContext.putVariable("empId", empLeave.getEmpid());
            //2.发起工作流
            WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(cancel.getLeaveTypeId());
            LogRecordContext.putVariable("leaveName", leaveType.getLeaveName());
            return process(cancel);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    /**
     * 校验时间重叠
     *
     * @param list
     */
    private boolean checkTime(List<WaEmpLeaveCancelInfoDo> list) {
        boolean flag = false;
        for (int i = 0; i < list.size() - 1; i++) {
            Long start = list.get(i).getShiftStartTime();
            Long end = list.get(i).getShiftEndTime();
            for (int j = i + 1; j < list.size(); j++) {
                Long startTime = list.get(j).getShiftStartTime();
                Long endTime = list.get(j).getShiftEndTime();
                if (!(start >= endTime || end <= startTime)) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }

    private boolean checkTime(List<WaEmpLeaveCancelDaytimeDo> leaveCancelDayTimes, Map<Integer, WaShiftDef> shiftMap) {
        if (CollectionUtils.isEmpty(leaveCancelDayTimes)) {
            return false;
        }
        // 转换并计算每个时间段的实际时间槽
        List<WaLeaveDaytimeExtDto> list = leaveCancelDayTimes.stream().map(leaveCancelDayTime -> {
            WaLeaveDaytimeExtPo waLeaveCancelDayTime = ObjectConverter.convert(leaveCancelDayTime, WaLeaveDaytimeExtPo.class);
            waLeaveCancelDayTime.setLeaveDate(leaveCancelDayTime.getLeaveCancelDate());
            WaShiftDef userShiftDef = shiftMap.get(leaveCancelDayTime.getShiftDefId());
            return mobileV16Service.calLeaveDayRealTimeSlot(waLeaveCancelDayTime, userShiftDef);
        }).sorted(Comparator.comparing(WaLeaveDaytimeExtDto::getLeaveStartTime)).collect(Collectors.toList());
        // 检查相邻时间段是否有重叠
        for (int i = 0; i < list.size() - 1; i++) {
            Long currentEnd = list.get(i).getLeaveEndTime();
            Long nextStart = list.get(i + 1).getLeaveStartTime();
            if (currentEnd > nextStart) {
                return true; // 存在重叠
            }
        }
        return false;
    }

    /**
     * 销假记录分页列表
     *
     * @param dto
     * @return
     */
    public PageResult<LeaveCancelListDto> getPageList(LeaveCancelReqDto dto, UserInfo userInfo) {
        if (userInfo == null) {
            userInfo = getUser();
        }
        Map params = new HashMap();
        params.put("belongOrgId", userInfo.getTenantId());
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            params.put("keywords", dto.getKeywords());
        }
        if (dto.getEmpId() != null) {
            params.put("empId", dto.getEmpId());
        }
        PageBean pageBean = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getNewPageBean(dto)), PageBean.class);
        changeParam(pageBean, params);
        params.put("filter", pageBean.getFilter());
        params.put("datafilter", dto.getDataScope());
        AttendanceBasePage basePage = ObjectConverter.convert(dto, AttendanceBasePage.class);
        PageResult<LeaveCancelListDto> dtoPageResult = new PageResult<>();
        PageResult<WaEmpLeaveCancelDo> pageResult = waEmpLeaveCancelDomainService.pageList(basePage, params);
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaEmpLeaveCancelDo> doList = pageResult.getItems();
            List<LeaveCancelListDto> dtoList = ObjectConverter.convertList(doList, LeaveCancelListDto.class);
            //查询休假单
            List<Integer> leaveIds = dtoList.stream().map(LeaveCancelListDto::getLeaveId).distinct().collect(Collectors.toList());
            List<WaEmpLeaveDo> leaveList = waEmpLeaveDo.getWaEmpLeaveListByIds(leaveIds);
            Map<Integer, WaEmpLeaveDo> leaveMap = leaveList.stream().collect(Collectors.toMap(WaEmpLeaveDo::getLeaveId, Function.identity(), (k1, k2) -> k2));
            for (LeaveCancelListDto cancelListDto : dtoList) {
                cancelListDto.setLeaveTypeName(LangParseUtil.getI18nLanguage(cancelListDto.getI18nLeaveTypeName(), cancelListDto.getLeaveTypeName()));
                //审批状态
                short status = cancelListDto.getStatus();
                cancelListDto.setStatusName(ApprovalStatusEnum.getName(status));
                cancelListDto.setFuncType(FuncTypeEnum.LEAVE_CANCEL.getIndex());
                cancelListDto.setBusinessKey(cancelListDto.getLeaveCancelId() + "_" + BusinessCodeEnum.VACATION.getCode());
                cancelListDto.setLeaveCancelTypeName(LeaveCancelTypeEnum.getName(cancelListDto.getTypeId()));
                if (Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex()).equals(cancelListDto.getTypeId())) {
                    Float timeDuration = cancelListDto.getTimeDuration();
                    if (LeaveTypeUnitEnum.HOUR.getIndex().equals(cancelListDto.getTimeUnit())) {
                        timeDuration = new BigDecimal(timeDuration).divide(new BigDecimal(60), 2, RoundingMode.DOWN).floatValue();
                    }
                    cancelListDto.setTimeDuration(timeDuration);
                    List<WaEmpLeaveCancelInfoDo> cancelInfoList = waEmpLeaveCancelInfoDomainService.getLeaveCancelInfoList(cancelListDto.getLeaveCancelId());
                    List<LeaveCancelPeriod> cancelPeriods = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(cancelInfoList)) {
                        for (WaEmpLeaveCancelInfoDo row : cancelInfoList) {
                            Integer periodType = Integer.valueOf(row.getPeriodType());
                            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                                cancelPeriods.add(new LeaveCancelPeriod(DateUtil.getDateStrByTimesamp(row.getStartTime()), DateUtil.getDateStrByTimesamp(row.getEndTime())));
                            } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                                cancelPeriods.add(new LeaveCancelPeriod(String.format("%s%s", DateUtil.getDateStrByTimesamp(row.getStartTime()), DayHalfTypeEnum.getDesc(row.getShalfDay())),
                                        String.format("%s%s", DateUtil.getDateStrByTimesamp(row.getEndTime()), DayHalfTypeEnum.getDesc(row.getEhalfDay()))));
                            } else {
                                cancelPeriods.add(new LeaveCancelPeriod(DateUtil.getTimeStrByTimesamp(row.getShiftStartTime()), DateUtil.getTimeStrByTimesamp(row.getShiftEndTime())));
                            }
                        }
                    }
                    cancelListDto.setCancelPeriods(cancelPeriods);
                } else {
                    cancelListDto.setTimeDuration(null);
                    cancelListDto.setTimeUnit(null);
                }
                if (leaveMap.containsKey(cancelListDto.getLeaveId())) {
                    WaEmpLeaveDo empLeave = leaveMap.get(cancelListDto.getLeaveId());
                    //休假
                    Long leaveShiftStartTime = empLeave.getShiftStartTime();
                    Long leaveShiftEndTime = empLeave.getShiftEndTime();
                    String leaveStartDate;
                    String leaveEndDate;
                    Integer leavePeriodType = Integer.valueOf(empLeave.getPeriodType());
                    if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(leavePeriodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(leavePeriodType)) {
                        leaveStartDate = DateUtil.getDateStrByTimesamp(leaveShiftStartTime);
                        leaveEndDate = DateUtil.getDateStrByTimesamp(leaveShiftEndTime);
                    } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(leavePeriodType)) {
                        Long start = empLeave.getStartTime();
                        Long end = empLeave.getEndTime();
                        leaveStartDate = String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeave.getShalfDay()));
                        leaveEndDate = String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeave.getEhalfDay()));
                    } else {
                        leaveStartDate = DateUtil.getTimeStrByTimesamp(leaveShiftStartTime);
                        leaveEndDate = DateUtil.getTimeStrByTimesamp(leaveShiftEndTime);
                    }
                    cancelListDto.setOriginalLeaveDate(String.format("%s-%s", leaveStartDate, leaveEndDate));
                }
            }
            dtoPageResult.setItems(dtoList);
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setTotal(pageResult.getTotal());
        }
        return dtoPageResult;
    }

    /**
     * 销假时间参数处理
     *
     * @param pageBean
     * @param map
     */
    private void changeParam(PageBean pageBean, Map map) {
        List<FilterBean> list = pageBean.getFilterList();
        if (!CollectionUtils.isEmpty(list)) {
            Iterator<FilterBean> it = list.iterator();
            while (it.hasNext()) {
                FilterBean filterBean = it.next();
                if ("leaveCancelTime".equals(filterBean.getField())) {
                    if (StringUtils.isNotBlank(filterBean.getMin())) {
                        map.put("startDateTime", Long.valueOf(filterBean.getMin()));
                    }
                    if (StringUtils.isNotBlank(filterBean.getMax())) {
                        map.put("endDateTime", Long.valueOf(filterBean.getMax()));
                    }
                    it.remove();
                }
            }
        }
    }

    /**
     * 销假撤销
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> revokeCancelLeave(RevokeLeaveCancelDto dto) throws Exception {
        if (StringUtils.isEmpty(dto.getRevokeReason())) {
            return Result.fail("请输入撤销原因");
        }
        if (dto.getRevokeReason().length() >= 100) {
            return Result.fail("请输入撤销原因100字以内");
        }
        UserInfo user = getUser();
        WaEmpLeaveCancelDo leaveCancelDo = waEmpLeaveCancelDomainService.getById(user.getTenantId(), dto.getLeaveCancelId());
        PreCheck.preCheckArgument(leaveCancelDo == null, "销假单据不存在");
        PreCheck.preCheckArgument(ApprovalStatusEnum.REVOKED.getIndex().equals(leaveCancelDo.getStatus().intValue()), "该数据已被撤销");
        // 只允许撤销审批中和审批通过的单据
        boolean flag = !Objects.equals(leaveCancelDo.getStatus(), LeaveStatusEnum.LEAVE_STATUS_1.value) && !Objects.equals(leaveCancelDo.getStatus(), LeaveStatusEnum.LEAVE_STATUS_2.value);
        PreCheck.preCheckArgument(flag, "不允许撤销");
        //考勤截止日判断
        if (!dto.isCloseCheck() && Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex()).equals(leaveCancelDo.getTypeId())) {
            List<WaEmpLeaveCancelInfoDo> list = waEmpLeaveCancelInfoDomainService.getLeaveCancelInfoList(leaveCancelDo.getLeaveCancelId());
            // 检查是否存在有效的休假单，有的话不允许撤销
            boolean allowRevoke = isAllowRevoke(list, leaveCancelDo.getEmpid());
            if (allowRevoke && leaveCancelDo.getStatus().intValue() == LeaveStatusEnum.LEAVE_STATUS_2.value) {
                return ResponseWrap.wrapResult(AttendanceCodes.NOT_ALLOW_REVOKE_LEAVE_CANCEL, Boolean.FALSE);
            }
            // 考勤截止日判断
            WaEmpLeaveCancelInfoDo cancelInfo = list.get(0);
            Long endDate = DateUtil.getOnlyDate(new Date(cancelInfo.getEndTime() * 1000));
            checkSobEndDate(leaveCancelDo.getEmpid(), endDate);
        }
        //新工作流
        WfRevokeDto wfRevokeDto = new WfRevokeDto();
        String businessKey = dto.getLeaveCancelId() + "_" + BusinessCodeEnum.VACATION.getCode();
        wfRevokeDto.setBusinessKey(businessKey);
        Result<String> result = wfRegisterFeign.revokeProcessOfTask(wfRevokeDto);
        if (null == result || !result.isSuccess()) {
            throw new CDException("工作流发起异常，请联系管理员！");
        }
        //1.更新单据状态
        WaEmpLeaveCancelDo cancelDo = new WaEmpLeaveCancelDo();
        cancelDo.setLeaveCancelId(leaveCancelDo.getLeaveCancelId());
        cancelDo.setStatus(ApprovalStatusEnum.REVOKED.getIndex().shortValue());
        cancelDo.setRevokeReason(dto.getRevokeReason());
        cancelDo.setUpdateBy(Long.valueOf(user.getUserid()));
        cancelDo.setUpdateTime(DateUtil.getCurrentTime(true));
        waEmpLeaveCancelDomainService.update(cancelDo);
        //2.更新休假单的销假时长
        if (Objects.equals(leaveCancelDo.getStatus(), LeaveStatusEnum.LEAVE_STATUS_2.value)) {
            WaEmpLeaveDo empLeave = waEmpLeaveDo.getById(leaveCancelDo.getLeaveId());
            empLeave.setUpduser(user.getUserId());
            empLeave.setUpdtime(DateUtil.getCurrentTime(true));
            empLeave.setCancelTimeDuration(empLeave.getCancelTimeDuration() - leaveCancelDo.getTimeDuration());
            waEmpLeaveDo.update(empLeave);
            //更新wa_leave_time
            WaEmpLeaveTimeDo leaveTime = waEmpLeaveTimeDo.getById(empLeave.getLeaveId());
            leaveTime.setCancelTimeDuration(leaveTime.getCancelTimeDuration() - leaveCancelDo.getTimeDuration());
            waEmpLeaveTimeDo.update(leaveTime);
            //额度重新扣减
            WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(empLeave.getLeaveTypeId());
            List<WaEmpLeaveCancelDaytimeDo> dayTimeList = waEmpLeaveCancelDaytimeDomainService.getByLeaveCancelId(leaveCancelDo.getLeaveCancelId());
            if (!CollectionUtils.isEmpty(dayTimeList)) {
                for (WaEmpLeaveCancelDaytimeDo daytimeDo : dayTimeList) {
                    Long date = daytimeDo.getLeaveCancelDate();
                    List<WaLeaveDaytimeDo> waLeaveDayTimeList = waLeaveDaytimeDo.getByDate(empLeave.getLeaveId(), date);
                    if (Optional.ofNullable(daytimeDo.getShiftDefId()).isPresent()) {
                        waLeaveDayTimeList = waLeaveDayTimeList.stream().filter(dayTime -> daytimeDo.getShiftDefId().equals(dayTime.getUseShiftDefId())).collect(Collectors.toList());
                    }
                    WaLeaveDaytimeDo leaveDaytimeDo = waLeaveDayTimeList.get(0);
                    leaveDaytimeDo.setCancelTimeDuration(leaveDaytimeDo.getCancelTimeDuration() - daytimeDo.getTimeDuration());
                    waLeaveDaytimeDo.update(leaveDaytimeDo);
                    Float cancelTimeDuration = daytimeDo.getTimeDuration();
                    if (leaveType.getLeaveType() != 4) {
                        if (leaveType.getQuotaRestrictionType() != null && QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(leaveType.getQuotaRestrictionType())) {
                            //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
                            Integer quotaType = leaveType.getQuotaType();
                            if (quotaType == 2) {
                                List<WaCompensatoryQuotaUse> quotaUseList = waCompensatoryQuotaUseMapper.selectOrderBy(empLeave.getEmpid(), ConvertHelper.longConvert(leaveDaytimeDo.getLeaveDaytimeId()));
                                for (WaCompensatoryQuotaUse quotaUse : quotaUseList) {
                                    Float oldCancelTimeDuration = quotaUse.getCancelTimeDuration();
                                    if (cancelTimeDuration > oldCancelTimeDuration) {
                                        cancelTimeDuration = cancelTimeDuration - oldCancelTimeDuration;
                                        WaCompensatoryQuotaUse updUse = new WaCompensatoryQuotaUse();
                                        updUse.setUseId(quotaUse.getUseId());
                                        updUse.setCancelTimeDuration(0f);
                                        updUse.setUpdateBy(SessionHolder.getUserId());
                                        updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                        waCompensatoryQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                    } else {
                                        WaCompensatoryQuotaUse updUse = new WaCompensatoryQuotaUse();
                                        updUse.setUseId(quotaUse.getUseId());
                                        updUse.setCancelTimeDuration(oldCancelTimeDuration - cancelTimeDuration);
                                        updUse.setUpdateBy(SessionHolder.getUserId());
                                        updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                        waCompensatoryQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                        break;
                                    }
                                }
                                UserInfo userInfo = sessionService.getUserInfo();
                                long updateTime = System.currentTimeMillis() / 1000;
                                List<Long> idList = new ArrayList<>();
                                idList.add(Long.valueOf(leaveDaytimeDo.getLeaveDaytimeId()));
                                waEmpCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(userInfo.getUserId(), updateTime, idList, ApprovalStatusEnum.PASSED.getIndex());
                            } else {
                                List<WaLeaveQuotaUse> quotaUseList = waLeaveQuotaUseMapper.selectOrderBy(empLeave.getEmpid(), leaveDaytimeDo.getLeaveDaytimeId());
                                for (WaLeaveQuotaUse quotaUse : quotaUseList) {
                                    Float oldCancelTimeDuration = quotaUse.getCancelTimeDuration();
                                    if (cancelTimeDuration > oldCancelTimeDuration) {
                                        cancelTimeDuration = cancelTimeDuration - oldCancelTimeDuration;
                                        WaLeaveQuotaUse updUse = new WaLeaveQuotaUse();
                                        updUse.setCancelTimeDuration(0f);
                                        updUse.setUpdateBy(SessionHolder.getUserId());
                                        updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                        waLeaveQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                    } else {
                                        WaLeaveQuotaUse updUse = new WaLeaveQuotaUse();
                                        updUse.setUseId(quotaUse.getUseId());
                                        updUse.setCancelTimeDuration(oldCancelTimeDuration - cancelTimeDuration);
                                        updUse.setUpdateBy(SessionHolder.getUserId());
                                        updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                        waLeaveQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                        break;
                                    }
                                }
                                UserInfo userInfo = sessionService.getUserInfo();
                                long updateTime = System.currentTimeMillis() / 1000;
                                List<Integer> idList = new ArrayList<>();
                                idList.add(leaveDaytimeDo.getLeaveDaytimeId());
                                waLeaveQuotaUseMapper.updateWaEmpQuota(userInfo.getUserId(), updateTime, idList, ApprovalStatusEnum.PASSED.getIndex());
                            }
                        }
                    }
                }
            }
        }
        //3.更新休假单的休假状态
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getById(leaveCancelDo.getLeaveId());
        List<WaEmpLeaveCancelDo> leaveCancelList = waEmpLeaveCancelDomainService.getListByLeaveId(leaveCancelDo.getTenantId(), leaveCancelDo.getLeaveId());
        //是否存在审批中的销假单
        boolean inApp = leaveCancelList.stream().anyMatch(e -> Objects.equals(LeaveStatusEnum.LEAVE_STATUS_1.value, e.getStatus()));
        if (!inApp && empLeave != null) {
            //是否存在审批通过的销假单
            boolean pass = leaveCancelList.stream().anyMatch(e -> Objects.equals(LeaveStatusEnum.LEAVE_STATUS_2.value, e.getStatus()));
            if (!pass) {
                empLeave.setLeaveStatus(LeaveCancelStatusEnum.NO_LEAVE_CANCEL.getIndex());
            } else {
                empLeave.setLeaveStatus(LeaveCancelStatusEnum.LEAVE_CANCEL_PASSED.getIndex());
            }
            waEmpLeaveDo.update(empLeave);
        }
        //休假调整，恢复原休假单，删除新地休假单
        if (Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST.getIndex()).equals(leaveCancelDo.getTypeId())) {
            workflowCallBackService.handleLeave(leaveCancelDo.getLeaveCancelId(), leaveCancelDo.getLeaveId(), leaveCancelDo.getOriginalLeaveId(), cancelDo.getStatus(), leaveCancelDo.getEmpid());
        }
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 销假类型
     *
     * @return
     */
    public Result<List<KeyValue>> getLeaveCancelType(Integer leaveId) {
        List<KeyValue> list = new ArrayList<>();
        if (leaveId != null) {
            WaEmpLeaveDo waEmpLeave = waEmpLeaveDo.getById(leaveId);
            WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(waEmpLeave.getLeaveTypeId());
            if (leaveType != null) {
                String allowedCancelType = leaveType.getAllowedCancelType();
                if (StringUtils.isNotBlank(allowedCancelType)) {
                    String[] leaveCancelTypeList = allowedCancelType.split(",");
                    for (String str : leaveCancelTypeList) {
                        KeyValue keyValue = new KeyValue();
                        String name = LeaveCancelTypeEnum.getName(Integer.parseInt(str));
                        if (null != name) {
                            keyValue.setText(name);
                            keyValue.setValue(str);
                            list.add(keyValue);
                        }
                    }
                    /*if (ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(Integer.valueOf(waEmpLeave.getStatus()))) {
                        list = list.stream().filter(e -> LeaveCancelTypeEnum.FILE_ADD.getIndex().toString().equals(e.getValue().toString())).collect(Collectors.toList());
                    }*/
                    boolean allowedMultipleCancel = leaveType.getAllowedMultipleCancel() != null && leaveType.getAllowedMultipleCancel();
                    Integer leaveApprovalStatus = Integer.valueOf(waEmpLeave.getStatus());
                    Integer leaveCancelApprovalStatus = waEmpLeave.getLeaveStatus() == null ? LeaveCancelStatusEnum.NO_LEAVE_CANCEL.getIndex() : waEmpLeave.getLeaveStatus();
                    if (ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(leaveApprovalStatus) || ApprovalStatusEnum.REJECTED.getIndex().equals(leaveApprovalStatus)) {
                        list = new ArrayList<>();
                    } else if (ApprovalStatusEnum.PASSED.getIndex().equals(leaveApprovalStatus)) {
                        if (LeaveCancelStatusEnum.LEAVE_CANCEL_IN.getIndex().equals(leaveCancelApprovalStatus)) {
                            list = new ArrayList<>();
                        } else if (LeaveCancelStatusEnum.LEAVE_CANCEL_PASSED.getIndex().equals(leaveCancelApprovalStatus)) {
                            if (!allowedMultipleCancel) {
                                list = new ArrayList<>();
                            }
                        }
                    } else {
                        list = new ArrayList<>();
                    }
                }
            }
        } else {
            for (LeaveCancelTypeEnum c : LeaveCancelTypeEnum.values()) {
                KeyValue keyValue = new KeyValue();
                keyValue.setText(LeaveCancelTypeEnum.getName(c.getIndex()));
                keyValue.setValue(c.getIndex());
                list.add(keyValue);
            }
        }
        return Result.ok(list);
    }

    /**
     * 根据销假单查找最小最大销假日期
     *
     * @param empLeaveCancelInfoList
     * @return
     */
    private Map<String, Long> findLeaveCancelDateRange(List<WaEmpLeaveCancelInfoDo> empLeaveCancelInfoList) {
        if (CollectionUtils.isEmpty(empLeaveCancelInfoList)) {
            return null;
        }
        long minStartTime = 0L;
        long maxEndTime = 0L;
        for (WaEmpLeaveCancelInfoDo cancelInfo : empLeaveCancelInfoList) {
            if (null != cancelInfo.getShiftStartTime() && null != cancelInfo.getShiftEndTime()) {
                if (minStartTime == 0 || minStartTime > cancelInfo.getShiftStartTime()) {
                    minStartTime = cancelInfo.getShiftStartTime();
                }
                if (maxEndTime == 0 || maxEndTime < cancelInfo.getShiftEndTime()) {
                    maxEndTime = cancelInfo.getShiftEndTime();
                }
            } else {
                if (minStartTime == 0 || minStartTime > cancelInfo.getStartTime()) {
                    minStartTime = cancelInfo.getStartTime();
                }
                if (maxEndTime == 0 || maxEndTime < cancelInfo.getEndTime()) {
                    maxEndTime = cancelInfo.getEndTime();
                }
            }
        }
        Map<String, Long> result = new HashMap<>();
        result.put("startDate", DateUtil.getOnlyDate(new Date(minStartTime * 1000)));
        result.put("endDate", DateUtil.getOnlyDate(new Date(maxEndTime * 1000)));
        return result;
    }

    /**
     * 是否允许撤销销假单
     *
     * @param list
     * @return
     */
    public boolean isAllowRevoke(List<WaEmpLeaveCancelInfoDo> list, Long empId) {
        UserInfo userInfo = getUser();
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(userInfo.getTenantId(), empId);
        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
        tmType = tmType == 0 ? 1 : tmType;
        // 查询员工班次
        Map<Integer, WaShiftDef> corpAllShiftDef = waCommonService.getCorpAllShiftDef(userInfo.getTenantId());
        // 查找最小、最大的日期范围
        Long selectStatDate = null;
        Long selectEndDate = null;
        Map<String, Long> leaveCancelDateRange = findLeaveCancelDateRange(list);
        if (!MapUtils.isEmpty(leaveCancelDateRange)) {
            selectStatDate = leaveCancelDateRange.get("startDate");
            selectEndDate = leaveCancelDateRange.get("endDate") + 86399;
        }
        List<Map> checkList = waEmpLeaveDo.getEmpNonBrjLtList(empId, selectStatDate, selectEndDate);
        int num = 0;
        for (WaEmpLeaveCancelInfoDo leaveCancelInfoDo : list) {
            Long shiftStartTime = leaveCancelInfoDo.getShiftStartTime();
            Long shiftEndTime = leaveCancelInfoDo.getShiftEndTime();
            for (Map map : checkList) {
                Long start = 0L;
                Long end = 0L;
                Long leaveDate = (Long) map.get("leave_date");
                Integer periodType = (Integer) map.get("period_type");
                Map<Long, WaWorktimeDetail> empWaWorktimeDetail = waCommonService.getEmpWaWorktimeDetail(userInfo.getTenantId(), empInfo.getEmpid(),
                        tmType, leaveDate - 86400, leaveDate, empInfo.getWorktimeType(), true);
                WaWorktimeDetail worktimeDetail = empWaWorktimeDetail.get(leaveDate);
                if (null == worktimeDetail) {
                    continue;
                }
                WaShiftDef waShiftDef = corpAllShiftDef.get(worktimeDetail.getShiftDefId());
                if (periodType == 1 || periodType == 4) {//整天
                    start = leaveDate + waShiftDef.getStartTime() * 60;
                    end = leaveDate + waShiftDef.getEndTime() * 60;
                    if (CdWaShiftUtil.checkCrossNightV2(waShiftDef, waShiftDef.getDateType())) {
                        end += 1440 * 60;
                    }
                } else if (periodType == 3) { //小时
                    Integer start_time = (Integer) map.get("start_time");
                    Integer end_time = (Integer) map.get("end_time");
                    start = leaveDate + start_time * 60;
                    end = leaveDate + end_time * 60;
                    if (CdWaShiftUtil.checkCrossNightV2(waShiftDef, waShiftDef.getDateType())) {
                        end += 1440 * 60;
                    }
                } else if (periodType == 9) { //半天
                    String shalf_day = (String) map.get("shalf_day");
                    String ehalf_day = (String) map.get("ehalf_day");
                    if ("P".equals(shalf_day)) {
                        if (waShiftDef.getIsHalfdayTime() != null && waShiftDef.getIsHalfdayTime() && waShiftDef.getHalfdayTime() > 0) {
                            start = leaveDate + waShiftDef.getHalfdayTime() * 60;
                            if (waShiftDef.getHalfdayTime() < waShiftDef.getStartTime()) {
                                start += 1440 * 60;
                            }
                        } else if (waShiftDef.getIsNoonRest() != null && waShiftDef.getIsNoonRest()) {
                            start = leaveDate + waShiftDef.getNoonRestEnd() * 60;
                            if (CdWaShiftUtil.checkCrossNightV2(waShiftDef, waShiftDef.getDateType())) {
                                start += 1440 * 60;
                            }
                        } else {
                            start = leaveDate + waShiftDef.getStartTime() * 60;
                        }
                    } else {
                        start = leaveDate + waShiftDef.getStartTime() * 60;
                    }
                    if ("A".equals(ehalf_day)) {
                        if (waShiftDef.getIsHalfdayTime() != null && waShiftDef.getIsHalfdayTime() && waShiftDef.getHalfdayTime() > 0) {
                            end = leaveDate + waShiftDef.getHalfdayTime() * 60;
                            if (waShiftDef.getHalfdayTime() < waShiftDef.getStartTime()) {
                                end += 1440 * 60;
                            }
                        } else if (waShiftDef.getIsNoonRest() != null && waShiftDef.getIsNoonRest()) {
                            end = leaveDate + waShiftDef.getNoonRestStart() * 60;
                            if (waShiftDef.getNoonRestStart() < waShiftDef.getStartTime()) {
                                end += 1440 * 60;
                            }
                        } else {
                            end = leaveDate + waShiftDef.getEndTime() * 60;
                        }
                    } else {
                        end = leaveDate + waShiftDef.getEndTime() * 60;
                        if (CdWaShiftUtil.checkCrossNightV2(waShiftDef, waShiftDef.getDateType())) {
                            end += 1440 * 60;
                        }
                    }
                }
                if ((shiftStartTime >= start && shiftStartTime < end) || (shiftEndTime > start && shiftEndTime <= end) || (shiftStartTime <= start && shiftEndTime >= end)) {
                    num++;
                }
            }
        }
        return num > 0;
    }

    public List<WaEmpLeaveCancelDaytimeDo> getLeaveCancelDaytimeList(String tenantId, Long startTime, Long endTime) {
        return waEmpLeaveCancelDaytimeDomainService.getLeaveCancelDaytimeList(tenantId, startTime, endTime);
    }

    public Boolean checkAttachmentRequired(Integer leaveId, Integer cancelType) {
        if (null == leaveId || null == cancelType) {
            return Boolean.FALSE;
        }
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getById(leaveId);
        if (null == empLeave) {
            return Boolean.FALSE;
        }
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(empLeave.getLeaveTypeId());
        if (null == leaveType) {
            return Boolean.FALSE;
        }
        String attachmentRequired = leaveType.getCancelAttachmentRequired();
        return StringUtils.isNotBlank(attachmentRequired) && Arrays.stream(attachmentRequired.split(",")).anyMatch(r -> cancelType.equals(Integer.valueOf(r)));
    }

    /**
     * 自动销假
     *
     * @param tenantId 租户
     */
    public void autoLeaveCancel(String tenantId, Long corpId) {
        //查询租户下审批通过，状态为未销假的单据
        List<WaEmpLeaveDo> empLeaveList = waEmpLeaveDo.getEmpLeaveList(tenantId,
                Collections.singletonList(ApprovalStatusEnum.PASSED.getIndex()),
                Collections.singletonList(LeaveCancelStatusEnum.NO_LEAVE_CANCEL.getIndex()));
        if (!CollectionUtils.isEmpty(empLeaveList)) {
            List<Integer> leaveTypeIds = empLeaveList.stream().map(WaEmpLeaveDo::getLeaveTypeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leaveTypeIds)) {
                log.error("leaveTypeIds is empty");
                return;
            }
            // 查休假规则
            List<WaLeaveTypeDo> leaveTypes = waLeaveTypeDo.getLeaveTypesByIds(tenantId, leaveTypeIds);
            leaveTypes = leaveTypes.stream().filter(l -> l.getCancelTime() != null && l.getCancelTimeType() != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leaveTypes)) {
                log.error("leaveTypes is empty");
                return;
            }
            Map<Integer, WaLeaveTypeDo> leaveTypeMap = leaveTypes.stream().collect(Collectors.toMap(WaLeaveTypeDo::getLeaveTypeId, Function.identity(), (v1, v2) -> v2));
            List<String> businessKeys = leaveTypes.stream().map(l -> l.getLeaveTypeId().toString()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(businessKeys)) {
                log.error("leaveTypes is empty");
                return;
            }
            //查询需要满足自动销假的规则
            List<EmployeeGroupDto> employeeGroupList = employeeGroupService.getEmployeeGroups(businessKeys, "wa_leave_type", tenantId);
            if (CollectionUtils.isEmpty(employeeGroupList)) {
                log.error("auto cancel leave rule is empty");
                return;
            }
            long curDate = DateUtil.getOnlyDate();
            //查询租户下的当前截止的考勤周期
            Map<Long, WaSobDo> empSobMap = getEmpSobMap(tenantId, curDate);
            List<WaEmpLeave> updList = new ArrayList<>();
            List<Integer> updLeave = new ArrayList<>();
            for (EmployeeGroupDto employeeGroupDto : employeeGroupList) {
                String groupExp = employeeGroupDto.getGroupExp();
                if (StringUtils.isNotBlank(groupExp)) {
                    Integer leaveTypeId = Integer.valueOf(employeeGroupDto.getBusinessKey());
                    WaLeaveTypeDo waLeaveType = leaveTypeMap.get(leaveTypeId);
                    if (groupExp.contains("total_time_duration") && waLeaveType != null && waLeaveType.getAcctTimeType() == 2) {
                        groupExp = PatternUtil.replaceTotalTimeDurationFromRegex(groupExp, "total_time_duration");
                    }
                    List<Integer> leaveIds = sysEmpInfoDo.getLeaveIdsByGroupExp(tenantId, null, null, leaveTypeId, corpId, groupExp);
                    if (CollectionUtils.isEmpty(leaveIds)) {
                        log.error("leaveIds is empty tenantId:{}, leaveTypeId:{}, groupExp：{}", tenantId, leaveTypeId, groupExp);
                        continue;
                    }
                    List<WaEmpLeaveDo> filterLeaveList = empLeaveList.stream().filter(l -> leaveIds.contains(l.getLeaveId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(filterLeaveList)) {
                        log.error("filterLeaveList is empty tenantId:{}, leaveTypeId:{}, groupExp：{}", tenantId, leaveTypeId, groupExp);
                        continue;
                    }
                    log.info("filterLeaveList size {}", filterLeaveList.size());
                    for (WaEmpLeaveDo empLeave : filterLeaveList) {
                        WaLeaveTypeDo leaveType = leaveTypeMap.get(empLeave.getLeaveTypeId());
                        if (leaveType == null) {
                            continue;
                        }
                        if (LeaveCancelSettingTypeEnum.AUTOMATIC.getIndex().equals(leaveType.getLeaveCancelType())) {
                            continue;
                        }
                        Integer cancelTime = leaveType.getCancelTime();
                        Integer cancelTimeType = leaveType.getCancelTimeType();
                        if (cancelTime == null || cancelTimeType == null) {
                            continue;
                        }
                        Integer cancelTimeDay = leaveType.getCancelTimeDay();
                        Long cancelDate = DateUtil.getOnlyDate(new Date((cancelTime == 1 ? empLeave.getLastApprovalTime() : empLeave.getEndTime()) * 1000));
                        if (cancelTimeType == 3) {//考核截止日当天自动销假
                            if (!empSobMap.containsKey(empLeave.getEmpid())) {
                                //未查到考勤周期，不自动销假
                                continue;
                            }
                            WaSobDo empSob = empSobMap.get(empLeave.getEmpid());
                            if (empLeave.getEndTime() < empSob.getStartDate() || empLeave.getEndTime() > empSob.getEndDate()) {
                                //不满足休假结束时间在考勤周期内，则不自动销假
                                continue;
                            }
                            if (cancelDate >= empSob.getStartDate() && cancelDate <= empSob.getEndDate() && !updLeave.contains(empLeave.getLeaveId())) {
                                //满足审批结束日期/休假结束日期在考勤周期内，则自动销假
                                updLeave.add(empLeave.getLeaveId());
                                updList.add(getWaEmpLeave(empLeave.getLeaveId(), curDate));
                            }
                        } else {
                            if (null != cancelTimeDay && cancelTimeType != 1) {
                                //指定天数后自动销假
                                cancelDate = DateUtil.addDate(cancelDate * 1000, cancelTimeDay);
                            }
                            if (curDate >= cancelDate && !updLeave.contains(empLeave.getLeaveId())) {
                                updLeave.add(empLeave.getLeaveId());
                                updList.add(getWaEmpLeave(empLeave.getLeaveId(), curDate));
                            }
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(updList)) {
                importService.fastUpdList(WaEmpLeave.class, "leaveId", updList);
            }
        }
    }

    private WaEmpLeave getWaEmpLeave(Integer leaveId, long curDate) {
        WaEmpLeave waEmpLeave = new WaEmpLeave();
        waEmpLeave.setLeaveId(leaveId);
        waEmpLeave.setLeaveStatus(LeaveCancelStatusEnum.LEAVE_CANCEL_PASSED.getIndex());
        waEmpLeave.setUpdtime(curDate);
        return waEmpLeave;
    }

    /**
     * 查询员工当前所属考勤周期
     *
     * @param tenantId 租户
     * @param curDate  当前时间
     * @return
     */
    private Map<Long, WaSobDo> getEmpSobMap(String tenantId, long curDate) {
        Map<Long, WaSobDo> empSobMap = new HashMap<>();
        List<WaSobDo> sobList = sobDo.getWaSobByEndDateAndTenantId(tenantId, null, null);
        if (null != sobList && sobList.size() > 0) {
            sobList = sobList.stream().filter(s -> curDate == DateUtil.getOnlyDate(new Date(s.getSobEndDate() * 1000))).collect(Collectors.toList());
            for (WaSobDo waSob : sobList) {
                Boolean isDefault = false;
                WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waSob.getWaGroupId());
                if (waGroup.getIsDefault() != null) {
                    isDefault = waGroup.getIsDefault();
                }
                List<Long> empGroupEmpIds = sobDo.getEmpIdListByGroup(tenantId, curDate, isDefault, waGroup.getWaGroupId(), waSob.getStartDate(), waSob.getEndDate(), null);
                empGroupEmpIds = empGroupEmpIds.stream().distinct().collect(Collectors.toList());
                for (Long empGroupEmpId : empGroupEmpIds) {
                    empSobMap.put(empGroupEmpId, waSob);
                }
            }
        }
        return empSobMap;
    }
}