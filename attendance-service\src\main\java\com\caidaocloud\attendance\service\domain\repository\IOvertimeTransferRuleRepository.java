package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.infrastructure.repository.po.WaOvertimeTransferRulePo;

import java.util.List;

public interface IOvertimeTransferRuleRepository {

    List<WaOvertimeTransferRulePo> getOvertimeTransferRules(String tenantId, Long ruleId, String ruleName);

    void save(WaOvertimeTransferRulePo overtimeTransferRulePo);

    void update(WaOvertimeTransferRulePo overtimeTransferRulePo);

    WaOvertimeTransferRulePo getOvertimeTransferRule(Long ruleId);

    void delete(WaOvertimeTransferRulePo po);

    List<WaOvertimeTransferRulePo> getOvertimeTransferRules(List<Long> ruleIds);
}

