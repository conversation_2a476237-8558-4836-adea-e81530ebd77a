package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("销假申请结果DTO")
public class LeaveCancelResultDto {
    @ApiModelProperty("单位：1 天 2 小时")
    private Integer timeUnit;
    @ApiModelProperty("时长：单位为天返回天数，单位为小时，返回分钟数")
    private Float timeDuration;
}
