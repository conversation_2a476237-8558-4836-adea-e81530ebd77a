package com.caidaocloud.attendance.service.application.service;


import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.utils.ConvertHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IDataInputService implements ScriptBindable {

    private static final String WA_REGISTER_DATA_INPUT_KEY = "syncDataInputRegisterRecordEmpMap_";

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IClockSignService clockSignService;

    @Value("${wechat.wa.datainput.note:企业微信打卡接口}")
    private String weChatWaDataInputNote;

    @Transactional(rollbackFor = Exception.class)
    public void AnalysisEnterpriseWechatAttendance(String belongId) {
        log.info("开始执行企业微信打卡接入后执行考勤分析...");
        try {
            log.info("by dataInput starting config get cache data , belongId [{}]", belongId);
            String weChatDataInputKey = WA_REGISTER_DATA_INPUT_KEY + belongId;
            Map<String, List<Long>> map = redisTemplate.opsForHash().entries(weChatDataInputKey);
            if (map != null && !map.isEmpty()) {
                log.info("get weChat register dataInput emp cache data result success , belongId [{}], data[{}]", belongId, map.entrySet());
                Iterator<Map.Entry<String, List<Long>>> iterator = map.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, List<Long>> next = iterator.next();
                    if (CollectionUtils.isNotEmpty(next.getValue())) {
                        log.info("weChat analyseRegisterRecord starting... belongId[{}], empIds [{}], belongDate [{}]", belongId, next.getValue().toString(), next.getKey());
                        clockSignService.analyseRegisterRecord(belongId, next.getValue().stream().distinct().collect(Collectors.toList()), ConvertHelper.longConvert(next.getKey()), null);
                        log.info("weChat analyseRegisterRecord end");
                    }
                }
                log.info("weChat analyseRegisterRecord belongId[{}] ending... delete redisKey", belongId);
                redisTemplate.delete(weChatDataInputKey);
            } else {
                log.info("get weChat register dataInput emp cache data result is null , belongId [{}]", belongId);
            }

        } catch (Exception e) {
            log.error("企业微信打卡接入后执行考勤分析 error ,{}", e.getMessage(), e);
        }
        log.info("结束执行企业微信打卡接入后执行考勤分析...");
    }
}
