package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaReportFieldRuleDo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/2/5
 */
public interface IWaReportFieldRuleRepository {
    List<WaReportFieldRuleDo> selectList(String tenantId, Integer parseGroupId);

    void save(WaReportFieldRuleDo reportFieldRule);

    void update(WaReportFieldRuleDo reportFieldRule);

    void delete(String tenantId, Integer parseGroupId);
}
