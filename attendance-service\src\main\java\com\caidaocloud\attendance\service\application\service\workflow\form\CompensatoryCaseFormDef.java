package com.caidaocloud.attendance.service.application.service.workflow.form;

import com.caidaocloud.workflow.annotation.WfMetaFunFormDef;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.googlecode.totallylazy.Lists;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class CompensatoryCaseFormDef extends WfMetaFunFormDef {
    @NotNull
    @Override
    public List<WfMetaFunFormFieldDto> formList() {
        return Lists.list(
                new WfMetaFunFormFieldDto("initiator", "发起人", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("applicant", "申请人", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("org", "任职组织", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("empType", "员工类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("workplace", "工作地", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("hireDate", "入职日期", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("applyDuration", "付现失效额度", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("validDuration", "付现生效中额度", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("totalDuration", "申请付现额度", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("applyTime", "申请日期", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("approvalStatus", "审批状态", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("note", "备注", WfFieldDataTypeEnum.Text));
    }
}
