package com.caidaocloud.attendance.service.application.service.workflow;

import com.caidaocloud.attendance.service.application.service.workflow.component.*;
import com.caidaocloud.workflow.annotation.WfSeqCondition;
import com.caidaocloud.workflow.enums.WfSeqConditionCallTypeEnum;
import com.caidaocloud.workflow.enums.WfSeqConditionOperatorEnum;
import com.caidaocloud.workflow.enums.WfValueComponentEnum;
import org.springframework.stereotype.Service;

/**
 * 工作流序列流注册
 */
@Service
public class WorkflowSequenceRegisterService {

    @WfSeqCondition(name = "休假类型",
            code = "LEAVE_TYPE",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leave",
            component = WfValueComponentEnum.DATA_SOURCE,
            serviceId = "caidaocloud-attendance-service",
            dataSourceAddress = "/api/attendance/leavetype/v1/getLeaveTypeDefList?dataType=data",
            funcCode = {"ATTENDANCE-LEAVE"})
    public void leaveType() {
    }

    @WfSeqCondition(name = "总时长",
            code = "LEAVE_TOTAL_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leave",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-LEAVE"})
    public void leaveTotalTimeDuration() {
    }

    @WfSeqCondition(name = "单位(小时、天)",
            code = "LEAVE_TIME_UNIT",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leave",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = TimeUnitComponent.class,
            funcCode = {"ATTENDANCE-LEAVE"})
    public void leaveTimeUnit() {
    }

    @WfSeqCondition(name = "休假类型",
            code = "BATCH_LEAVE_TYPE",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/batchLeave",
            component = WfValueComponentEnum.DATA_SOURCE,
            serviceId = "caidaocloud-attendance-service",
            dataSourceAddress = "/api/attendance/leavetype/v1/getLeaveTypeDefList?dataType=data",
            funcCode = {"ATTENDANCE-BATCH-LEAVE"})
    public void batchLeaveType() {
    }

    @WfSeqCondition(name = "总时长",
            code = "BATCH_LEAVE_TOTAL_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/batchLeave",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-BATCH-LEAVE"})
    public void batchLeaveTotalTimeDuration() {
    }

    @WfSeqCondition(name = "单位(小时、天)",
            code = "BATCH_LEAVE_TIME_UNIT",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/batchLeave",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = TimeUnitComponent.class,
            funcCode = {"ATTENDANCE-BATCH-LEAVE"})
    public void batchLeaveTimeUnit() {
    }

    @WfSeqCondition(name = "加班类型",
            code = "OVERTIME_TYPE",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/overtime",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = OvertimeTypeComponent.class,
            funcCode = {"ATTENDANCE-OVERTIME", "ATTENDANCE-OVERTIME-REVOKE", "ATTENDANCE-OVERTIME-ABOLISH"})
    public void overtimeType() {
    }

    @WfSeqCondition(name = "补偿方式",
            code = "COMPENSATE_TYPE",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/overtime",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = CompensateTypeComponent.class,
            funcCode = {"ATTENDANCE-OVERTIME", "ATTENDANCE-OVERTIME-REVOKE", "ATTENDANCE-OVERTIME-ABOLISH"})
    public void overtimeCompensateType() {
    }

    @WfSeqCondition(name = "总时长",
            code = "OVERTIME_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/overtime",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-OVERTIME", "ATTENDANCE-OVERTIME-REVOKE", "ATTENDANCE-OVERTIME-ABOLISH"})
    public void overtimeDuration() {
    }

    @WfSeqCondition(name = "单位（小时、天）",
            code = "OVERTIME_UNIT",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/overtime",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = TimeUnitComponent.class,
            funcCode = {"ATTENDANCE-OVERTIME", "ATTENDANCE-OVERTIME-REVOKE", "ATTENDANCE-OVERTIME-ABOLISH"})
    public void overtimeUnit() {
    }

    @WfSeqCondition(name = "考勤周期内加班总时长",
            code = "CYCLE_OVERTIME_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/overtime",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-OVERTIME"})
    public void overtimeAttendanceCycleTotalDuration() {
    }

    @WfSeqCondition(name = "总时长",
            code = "BATCH_OVERTIME_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/batchOvertime",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-BATCH-OVERTIME"})
    public void batchOvertimeDuration() {
    }

    @WfSeqCondition(name = "单位（小时、天）",
            code = "BATCH_OVERTIME_UNIT",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/batchOvertime",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = TimeUnitComponent.class,
            funcCode = {"ATTENDANCE-BATCH-OVERTIME"})
    public void batchOvertimeUnit() {
    }

    @WfSeqCondition(name = "出差类型",
            code = "TRAVEL_TYPE",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/travel",
            component = WfValueComponentEnum.DATA_SOURCE,
            serviceId = "caidaocloud-attendance-service",
            dataSourceAddress = "/api/attendance/emptravel/v1/getTravelTypes?dataType=data",
            funcCode = {"ATTENDANCE-TRAVEL", "ATTENDANCE-TRAVEL-REVOKE", "ATTENDANCE-TRAVEL-ABOLISH"})
    public void travelType() {
    }

    @WfSeqCondition(name = "总时长",
            code = "TRAVEL_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/travel",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-TRAVEL", "ATTENDANCE-TRAVEL-REVOKE", "ATTENDANCE-TRAVEL-ABOLISH"})
    public void travelDuration() {
    }

    @WfSeqCondition(name = "单位（小时、天）",
            code = "TRAVEL_UNIT",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/travel",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = TimeUnitComponent.class,
            funcCode = {"ATTENDANCE-TRAVEL", "ATTENDANCE-TRAVEL-REVOKE", "ATTENDANCE-TRAVEL-ABOLISH"})
    public void travelUnit() {
    }

    @WfSeqCondition(name = "出差类型",
            code = "BATCH_TRAVEL_TYPE",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/batchTravel",
            component = WfValueComponentEnum.DATA_SOURCE,
            serviceId = "caidaocloud-attendance-service",
            dataSourceAddress = "/api/attendance/emptravel/v1/getTravelTypes?dataType=data",
            funcCode = {"ATTENDANCE-BATCH-TRAVEL"})
    public void batchTravelType() {
    }


    @WfSeqCondition(name = "是否为员工成本中心",
            code = "BATCH_TRAVEL_COST_CENTER",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/batchTravel",
            component = WfValueComponentEnum.ENUM,
            componentValueEnum = BooleanEnumComponent.class,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-BATCH-TRAVEL"})
    public void batchTravelCostCenter() {
    }

    @WfSeqCondition(name = "总时长",
            code = "BATCH_TRAVEL_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/batchTravel",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-BATCH-TRAVEL"})
    public void batchTravelDuration() {
    }

    @WfSeqCondition(name = "单位（小时、天）",
            code = "BATCH_TRAVEL_UNIT",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/batchTravel",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = TimeUnitComponent.class,
            funcCode = {"ATTENDANCE-BATCH-TRAVEL"})
    public void batchTravelUnit() {
    }

    @WfSeqCondition(name = "销假类型",
            code = "LEAVE_CANCEL_TYPE",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leaveCancel",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = LeaveCancelComponent.class,
            funcCode = {"ATTENDANCE-VACATION"})
    public void leaveCancelType() {
    }

    @WfSeqCondition(name = "销假时长",
            code = "LEAVE_CANCEL_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leaveCancel",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-VACATION"})
    public void leaveCancelTimeDuration() {
    }

    @WfSeqCondition(name = "休假时长",
            code = "LEAVE_TIME_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leaveCancel",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-VACATION"})
    public void leaveTimeDuration() {
    }

    @WfSeqCondition(name = "单位（小时、天）",
            code = "LEAVE_CANCEL_UNIT",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leaveCancel",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = TimeUnitComponent.class,
            funcCode = {"ATTENDANCE-VACATION"})
    public void leaveCancelUnit() {
    }

    @WfSeqCondition(name = "休假类型",
            code = "CANCEL_LEAVE_TYPE",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leaveCancel",
            component = WfValueComponentEnum.DATA_SOURCE,
            serviceId = "caidaocloud-attendance-service",
            dataSourceAddress = "/api/attendance/leavetype/v1/getLeaveTypeDefList?dataType=data",
            funcCode = {"ATTENDANCE-VACATION"})
    public void leaveCancelLeaveType() {
    }

    @WfSeqCondition(name = "申请付现总时长",
            code = "COMPENSATORY_TOTAL_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/compensatory",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-COMPENSATORY"})
    public void compensatoryTotalTimeDuration() {
    }

    @WfSeqCondition(name = "单位(小时、天)",
            code = "COMPENSATORY_TIME_UNIT",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/compensatory",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = TimeUnitComponent.class,
            funcCode = {"ATTENDANCE-COMPENSATORY"})
    public void compensatoryTimeUnit() {
    }

    @WfSeqCondition(name = "延期假期类型",
            code = "LEAVE_EXTENSION_TYPE",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leaveExtension",
            component = WfValueComponentEnum.DATA_SOURCE,
            serviceId = "caidaocloud-attendance-service",
            dataSourceAddress = "/api/attendance/leaveExtension/v1/quotaTypeWorkflow",
            funcCode = {"ATTENDANCE-LEAVE-EXTENSION"})
    public void leaveExtensionLeaveType() {
    }

    @WfSeqCondition(name = "延期额度",
            code = "LEAVE_EXTENSION_TOTAL_DURATION",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leaveExtension",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-LEAVE-EXTENSION"})
    public void leaveExtensionTotalTimeDuration() {
    }

    @WfSeqCondition(name = "延期额度单位(小时、天)",
            code = "LEAVE_EXTENSION_TIME_UNIT",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/leaveExtension",
            component = WfValueComponentEnum.ENUM,
            serviceId = "caidaocloud-attendance-service",
            componentValueEnum = TimeUnitComponent.class,
            funcCode = {"ATTENDANCE-LEAVE-EXTENSION"})
    public void leaveExtensionTimeUnit() {
    }

    @WfSeqCondition(name = "考勤周期内补卡次数",
            code = "NUMBER_OF_CARD_REPLACEMENT",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/registerRecordCardReplacement",
            component = WfValueComponentEnum.NUMBER_INPUT,
            serviceId = "caidaocloud-attendance-service",
            funcCode = {"ATTENDANCE-REGISTER"})
    public void registerRecordCardReplacement() {
    }

    @WfSeqCondition(name = "工作属性",
            code = "OVERTIME_JOB_ATTRIBUTES",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                    WfSeqConditionOperatorEnum.GT, WfSeqConditionOperatorEnum.GE,
                    WfSeqConditionOperatorEnum.LT, WfSeqConditionOperatorEnum.LE,
                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/attendance/workflowSequence/v1/overtime",
            component = WfValueComponentEnum.DATA_SOURCE,
            serviceId = "caidaocloud-attendance-service",
            dataSourceAddress = "/api/attendance/dict/v1/getEnableDictList?belongModule=Employee&typeCode=workAttribute",
            funcCode = {"ATTENDANCE-OVERTIME"})
    public void jobAttributes() {
    }
}