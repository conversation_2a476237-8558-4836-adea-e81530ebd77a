package com.caidaocloud.attendance.service.application.custom.service;

import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidaocloud.attendance.service.application.dto.WaCustomParseRuleDto;
import com.caidaocloud.attendance.service.domain.entity.WaReportFieldRuleDo;
import com.caidaocloud.attendance.service.domain.service.WaReportFieldRuleDomainService;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 考勤数据汇总定制逻辑
 *
 * <AUTHOR>
 * @Date 2024/2/2
 */
@Slf4j
@Service
public class WaStatisticeCustomService implements ScriptBindable {
    @Autowired
    private WaReportFieldRuleDomainService waReportFieldRuleDomainService;

    /**
     * 铁通-定制-迟到早退转旷工
     *
     * @param empWaStatisticeMap
     * @param empDailyWaAnalyzeList
     * @param waParseGroup
     * @param tenantId
     */
    private void doLateEarlyTimeToKgForTieTong(Map empWaStatisticeMap, List<Map> empDailyWaAnalyzeList,
                                               WaParseGroup waParseGroup, String tenantId) {
        if(null == waParseGroup){
            log.debug("doLateEarlyTimeToKgForTieTong fail waParseGroup empty, tenantId={}", tenantId);
            return;
        }
        // 查询自定义的分析规则
        List<WaReportFieldRuleDo> waReportFieldRuleDos = waReportFieldRuleDomainService.selectList(tenantId, waParseGroup.getParseGroupId());
        if (CollectionUtils.isEmpty(waReportFieldRuleDos)) {
            log.debug("doLateEarlyTimeToKgForTieTong fail waReportFieldRuleDos empty, tenantId={}", tenantId);
            return;
        }
        WaReportFieldRuleDo waReportFieldRule = waReportFieldRuleDos.get(0);
        if (StringUtils.isBlank(waReportFieldRule.getLatePaseRule())
                || StringUtils.isBlank(waReportFieldRule.getEarlyPaseRule())
                || StringUtils.isBlank(waReportFieldRule.getAbsentPaseRule())) {
            log.debug("doLateEarlyTimeToKgForTieTong fail waReportFieldRule error, tenantId={}", tenantId);
            return;
        }

        empDailyWaAnalyzeList.sort(Comparator.comparing(o -> Long.valueOf(o.get("belong_date").toString())));

        WaCustomParseRuleDto latePaseRule = FastjsonUtil.convertObject(waReportFieldRule.getLatePaseRule(), WaCustomParseRuleDto.class);
        WaCustomParseRuleDto earlyPaseRule = FastjsonUtil.convertObject(waReportFieldRule.getEarlyPaseRule(), WaCustomParseRuleDto.class);
        WaCustomParseRuleDto absentPaseRule = FastjsonUtil.convertObject(waReportFieldRule.getAbsentPaseRule(), WaCustomParseRuleDto.class);

        String lateCountKey = "latecount";// 迟到次数
        String earlyCountKey = "earlycount";// 早退次数
        String kgCountKey = "is_kg";// 旷工次数
        String kgTimekey = "kg_work_time";

        int lateCount = 0;
        int earlyCount = 0;
        int absentCount = 0;
        for (Map map : empDailyWaAnalyzeList) {
            String lateKey = "late_time";
            double lateTime = 0d;
            if (map.get(lateKey) != null) {
                lateTime = Double.parseDouble(map.get(lateKey).toString());
            }

            String eaylykey = "early_time";
            double earlyTime = 0d;
            if (map.get(eaylykey) != null) {
                earlyTime = Double.parseDouble(map.get(eaylykey).toString());
            }

            if (lateTime == 0 && earlyTime == 0) {
                continue;
            }

            BigDecimal kgWorkTime = new BigDecimal(0);
            if (map.get(kgTimekey) != null) {
                kgWorkTime = new BigDecimal(String.valueOf(map.get(kgTimekey)));
            }

            if (ifAbsent(absentPaseRule, lateTime, earlyTime)) {
                // 迟到早退转旷工
                map.put(lateKey, 0d);
                map.remove(lateCountKey);

                map.put(eaylykey, 0d);
                map.remove(earlyCountKey);

                map.put(kgCountKey, 1);
                kgWorkTime = kgWorkTime.add(new BigDecimal(lateTime)).add(new BigDecimal(earlyTime));
                map.put(kgTimekey, kgWorkTime.intValue());
            } else {
                boolean lateFlag = ifLate(latePaseRule, lateTime);
                if (lateFlag) {
                    lateCount = lateCount + 1;
                } else {
                    map.put(lateKey, 0d);
                    map.remove(lateCountKey);
                }

                boolean earlyFlag = ifEarly(earlyPaseRule, earlyTime);
                if (earlyFlag) {
                    earlyCount = earlyCount + 1;
                } else {
                    map.put(eaylykey, 0d);
                    map.remove(earlyCountKey);
                }

                if (lateCount + earlyCount > 4) {
                    if (lateFlag) {
                        absentCount = absentCount + 1;
                    }
                    if (earlyFlag) {
                        absentCount = absentCount + 1;
                    }

                    map.put(lateKey, 0d);
                    map.remove(lateCountKey);

                    map.put(eaylykey, 0d);
                    map.remove(earlyCountKey);
                }
            }
        }
        if (absentCount > 0) {
            empWaStatisticeMap.put(kgCountKey, absentCount);
            empWaStatisticeMap.put(kgTimekey, 240 * absentCount);
        }
    }

    private boolean ifLate(WaCustomParseRuleDto latePaseRule, Double lateTime) {
        if (StringUtils.isBlank(latePaseRule.getOpt()) || latePaseRule.getDuration() == null || lateTime <= 0) {
            return false;
        }
        if (">".equals(latePaseRule.getOpt())) {
            return lateTime > getDurationMin(latePaseRule);
        } else if ("<".equals(latePaseRule.getOpt())) {
            return lateTime < getDurationMin(latePaseRule);
        } else if (">=".equals(latePaseRule.getOpt())) {
            return lateTime >= getDurationMin(latePaseRule);
        } else if ("<=".equals(latePaseRule.getOpt())) {
            return lateTime <= getDurationMin(latePaseRule);
        }
        return false;
    }

    private boolean ifEarly(WaCustomParseRuleDto earlyPaseRule, Double earlyTime) {
        if (StringUtils.isBlank(earlyPaseRule.getOpt()) || earlyPaseRule.getDuration() == null || earlyTime <= 0) {
            return false;
        }
        if (">".equals(earlyPaseRule.getOpt())) {
            return earlyTime > getDurationMin(earlyPaseRule);
        } else if ("<".equals(earlyPaseRule.getOpt())) {
            return earlyTime < getDurationMin(earlyPaseRule);
        } else if (">=".equals(earlyPaseRule.getOpt())) {
            return earlyTime >= getDurationMin(earlyPaseRule);
        } else if ("<=".equals(earlyPaseRule.getOpt())) {
            return earlyTime <= getDurationMin(earlyPaseRule);
        }
        return false;
    }

    private boolean ifAbsent(WaCustomParseRuleDto absentPaseRule, Double lateTime, Double earlyTime) {
        if (StringUtils.isBlank(absentPaseRule.getOpt()) || absentPaseRule.getDuration() == null) {
            return false;
        }
        lateTime = null == lateTime ? 0d : lateTime;
        earlyTime = null == earlyTime ? 0d : earlyTime;
        if (lateTime <= 0 && earlyTime <= 0) {
            return false;
        }
        // 旷工规则类型：1 晚来+早走 、2 晚来/早走
        Integer type = absentPaseRule.getType();
        if (type == 1) {
            double lateAndEarlySum = lateTime + earlyTime;
            if (">".equals(absentPaseRule.getOpt())) {
                return lateAndEarlySum > getDurationMin(absentPaseRule);
            } else if ("<".equals(absentPaseRule.getOpt())) {
                return lateAndEarlySum < getDurationMin(absentPaseRule);
            } else if (">=".equals(absentPaseRule.getOpt())) {
                return lateAndEarlySum >= getDurationMin(absentPaseRule);
            } else if ("<=".equals(absentPaseRule.getOpt())) {
                return lateAndEarlySum <= getDurationMin(absentPaseRule);
            }
        } else {
            if (">".equals(absentPaseRule.getOpt())) {
                return lateTime > getDurationMin(absentPaseRule) || earlyTime > getDurationMin(absentPaseRule);
            } else if ("<".equals(absentPaseRule.getOpt())) {
                return lateTime < getDurationMin(absentPaseRule) || earlyTime < getDurationMin(absentPaseRule);
            } else if (">=".equals(absentPaseRule.getOpt())) {
                return lateTime >= getDurationMin(absentPaseRule) || earlyTime >= getDurationMin(absentPaseRule);
            } else if ("<=".equals(absentPaseRule.getOpt())) {
                return lateTime <= getDurationMin(absentPaseRule) || earlyTime <= getDurationMin(absentPaseRule);
            }
        }
        return false;
    }

    private Double getDurationMin(WaCustomParseRuleDto ruleDto) {
        if (null == ruleDto.getDuration()) {
            return 0d;
        }
        if ("hour".equals(ruleDto.getUnit())) {
            return ruleDto.getDuration() * 60d;
        }
        return Double.valueOf(ruleDto.getDuration());
    }
}
