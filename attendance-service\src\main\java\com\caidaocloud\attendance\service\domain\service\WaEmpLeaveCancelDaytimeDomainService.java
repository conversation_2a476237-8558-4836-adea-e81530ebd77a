package com.caidaocloud.attendance.service.domain.service;

import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveCancelDaytimeDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WaEmpLeaveCancelDaytimeDomainService {
    @Autowired
    private WaEmpLeaveCancelDaytimeDo waEmpLeaveCancelDaytimeDo;

    public void saveBatch(List<WaEmpLeaveCancelDaytimeDo> list) {
        waEmpLeaveCancelDaytimeDo.saveBatch(list);
    }

    public List<WaEmpLeaveCancelDaytimeDo> getByLeaveCancelIds(List<Long> cancelIds){
        return waEmpLeaveCancelDaytimeDo.getByLeaveCancelIds(cancelIds);
    }

    public List<WaEmpLeaveCancelDaytimeDo> getByLeaveCancelId(Long cancelId) {
        return waEmpLeaveCancelDaytimeDo.getByLeaveCancelId(cancelId);
    }

    public List<WaEmpLeaveCancelDaytimeDo> getLeaveCancelDaytimeList(String tenantId, Long startTime, Long endTime) {
        return waEmpLeaveCancelDaytimeDo.getLeaveCancelDaytimeList(tenantId, startTime, endTime);
    }
}
