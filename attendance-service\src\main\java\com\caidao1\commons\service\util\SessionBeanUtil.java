package com.caidao1.commons.service.util;

import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.mobile.bean.SessionBean;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.dto.UserInfo;

/**
 * 兼容1.0
 */
public class SessionBeanUtil {

    public static SessionBean getSessionBean(Long empid) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        SessionBean sessionBean = new SessionBean();
        sessionBean.setEmpid(empid);
        sessionBean.setCorpid(Long.valueOf(userInfo.getTenantId()));
        sessionBean.setBelongid(userInfo.getTenantId());
        sessionBean.setUserid(userInfo.getUserId());
        sessionBean.setLanguage(SessionHolder.getLang());
        return sessionBean;
    }

    public static String getLanguage() {
        // TODO 优化不要每次都获取
        return SessionBeanUtil.getSessionBean(null).getLanguage();
    }
}
