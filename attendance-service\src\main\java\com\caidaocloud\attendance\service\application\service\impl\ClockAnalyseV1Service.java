package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.commons.utils.StringUtil;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.dto.EmpOverInfo;
import com.caidaocloud.attendance.core.wa.dto.MobileEnum;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiWorkTimeBaseDto;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.application.dto.clock.ClockAnalyseDataCacheDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.enums.shift.ShiftClockAnalysisRuleTypeEnum;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.WebUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 打卡分析服务（专注于打卡分析-V1版本）
 *
 * <AUTHOR>
 * @Date 2025/2/13
 */
@Slf4j
@Service
@Deprecated
public class ClockAnalyseV1Service implements IClockAnalyseService {

    private static final int PAGE_SIZE = 1000;

    @Autowired
    private IRegisterRecordService registerRecordService;
    @Autowired
    private IScheduleQueryService scheduleQueryService;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private IOvertimeApplyService overtimeApplyService;
    @Autowired
    private ILeaveApplyService leaveApplyService;

    @Override
    public String getVersionType() {
        return "V1";
    }

    /**
     * 分页查询员工打卡记录
     *
     * @param belongOrgId
     * @param empIds
     * @param startDate
     * @param endDate
     * @param type
     * @return
     */
    @Override
    public List<WaRegisterRecordDo> getRegisterRecordList(String belongOrgId, List<Long> empIds,
                                                          Long startDate, Long endDate, Integer type) {
        // 参数校验
        if (StringUtils.isBlank(belongOrgId) || CollectionUtils.isEmpty(empIds) ||
                startDate == null || endDate == null) {
            return new ArrayList<>();
        }

        List<Integer> types = null != type ? Lists.newArrayList(type) : Lists.newArrayList();

        try {
            // 第一页
            AttendanceBasePage basePage = new AttendanceBasePage();
            basePage.setPageSize(PAGE_SIZE);
            basePage.setPageNo(1);
            AttendancePageResult<WaRegisterRecordDo> pageResult = registerRecordService.getPageList(basePage,
                    belongOrgId, startDate, endDate + 86399, empIds, types, ValidStatusEnum.VALID.getIndex());

            // 总条数
            int totalCount = pageResult.getTotal();
            if (totalCount <= 0) {
                return new ArrayList<>();
            }

            List<WaRegisterRecordDo> firstPageItems = pageResult.getItems();
            if (CollectionUtils.isEmpty(firstPageItems)) {
                return new ArrayList<>();
            }

            if (totalCount <= PAGE_SIZE) {
                return new ArrayList<>(firstPageItems);
            } else {
                List<WaRegisterRecordDo> allRecordList = new ArrayList<>(firstPageItems);

                //总页数
                int totalPage = (int) Math.ceil((double) totalCount / PAGE_SIZE);

                for (int pageNo = 2; pageNo <= totalPage; pageNo++) {
                    basePage.setPageNo(pageNo);
                    pageResult = registerRecordService.getPageList(basePage,
                            belongOrgId, startDate, endDate + 86399, empIds, types, ValidStatusEnum.VALID.getIndex());

                    List<WaRegisterRecordDo> currentPageItems = pageResult.getItems();
                    if (CollectionUtils.isNotEmpty(currentPageItems)) {
                        allRecordList.addAll(currentPageItems);
                    }
                }
                return allRecordList;
            }
        } catch (Exception e) {
            log.error("Failed to get register record list: belongOrgId={}, empIds={}, startDate={}, endDate={}, type={}",
                    belongOrgId, empIds.size(), startDate, endDate, type, e);
            return new ArrayList<>();
        }
    }

    /**
     * 分析打卡记录
     *
     * @param belongOrgId
     * @param empIds
     * @param date        打卡日期
     * @param type        打卡类型：1 GPS签到 2 扫码签到 3 外勤签到  4蓝牙签到  5 WIFI签到  6 补打卡
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void analyseRegisterRecord(String belongOrgId,
                                      List<Long> empIds,
                                      Long date,
                                      Integer type,
                                      ClockAnalyseDataCacheDto dataCacheDto) {
        log.debug("ClockAnalyseV1 start params={}, time={}",
                String.format("%s_%s_%s", belongOrgId, date, type), System.currentTimeMillis());
        log.debug("ClockAnalyseV1 start empIds={}, time={}", FastjsonUtil.toJsonStr(empIds), System.currentTimeMillis());

        if (CollectionUtils.isEmpty(empIds)) {
            log.debug("ClockAnalyseV1 fail emp empty, time={}", System.currentTimeMillis());
            return;
        }

        if (StringUtils.isBlank(belongOrgId)) {
            log.debug("ClockAnalyseV1 fail belongOrgId empty, time={}", System.currentTimeMillis());
            return;
        }

        Long clockDate = Optional.ofNullable(date).orElse(DateUtil.getOnlyDate());
        Long yesterday = DateUtil.addDate(clockDate * 1000, -1);
        Long nextDay = DateUtil.addDate(clockDate * 1000, 1);

        // 查询员工打卡记录
        List<WaRegisterRecordDo> waRegList;
        if (CollectionUtils.isEmpty(dataCacheDto.getWaRegList())) {
            List<Integer> types = null != type ? Lists.newArrayList(type) : Lists.newArrayList();
            waRegList = registerRecordService.getRegisterRecordPageList(new PageBean(true),
                    belongOrgId, yesterday, nextDay + 86399, empIds, types, ValidStatusEnum.VALID.getIndex());
        } else {
            waRegList = dataCacheDto.getWaRegList().stream()
                    .filter(it -> empIds.contains(it.getEmpid())
                            && it.getRegDateTime() >= yesterday
                            && it.getRegDateTime() <= nextDay + 86399)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(waRegList)) {
            log.debug("ClockAnalyseV1 fail record empty, time={}", System.currentTimeMillis());
            return;
        }

        waRegList.forEach(row -> row.setBelongDate(DateUtil.getOnlyDate(new Date(row.getRegDateTime() * 1000))));

        // 查询员工排班
        Map<String, WaShiftDo> empShiftDoMap;
        if (MapUtils.isEmpty(dataCacheDto.getEmpShiftDoMap())) {
            empShiftDoMap = scheduleQueryService.getEmpCalendarShiftMap(belongOrgId, yesterday, nextDay, empIds);
        } else {
            empShiftDoMap = dataCacheDto.getEmpShiftDoMap();
        }
        if (MapUtils.isEmpty(empShiftDoMap)) {
            log.debug("ClockAnalyseV1 fail shift empty, time={}", System.currentTimeMillis());
            return;
        }

        // 当日员工出勤规则
        List<EmpParseGroup> empParseListForToday;
        if (CollectionUtils.isEmpty(dataCacheDto.getEmpParseList())) {
            empParseListForToday = registerRecordService.selectEmpParseGroupListByDate(belongOrgId, empIds, clockDate);
        } else {
            empParseListForToday = dataCacheDto.getEmpParseList().stream()
                    .filter(it -> clockDate >= it.getStartTime() && clockDate <= it.getEndTime())
                    .collect(Collectors.toList());
        }

        // 如果班次上开启了打卡分析联动加班单据时（设置了任意加班）则去查询加班记录
        if (null == dataCacheDto.getEmpOtList()) {
            List<Long> empIdListByOt = getEmpIdListByOt(empShiftDoMap);
            List<EmpOverInfo> empOtList = overtimeApplyService.getEmpDailyOtList(belongOrgId, empIdListByOt,
                    yesterday, nextDay + 86399);
            dataCacheDto.initEmpOtList(empOtList);
        }

        // 打卡分析联动休假
        if (null == dataCacheDto.getEmpLeaveDayTimeMap()) {
            dataCacheDto.initEmpLeaveMaxTimeMap(leaveApplyService.getEmpLeaveDayTimeExtDtoList(belongOrgId, empIds,
                    yesterday, clockDate, empShiftDoMap), empShiftDoMap);
        }

        // 当日一次卡打卡员工
        Map<Long, EmpParseGroup> signOnceEmpForToday = CollectionUtils.isNotEmpty(empParseListForToday)
                ? empParseListForToday.stream().filter(o -> ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(o.getClockType())).collect(Collectors.toMap(EmpParseGroup::getEmpId, Function.identity(), (v1, v2) -> v1))
                : new HashMap<>();

        // 当日打卡记录
        Map<Long, List<WaRegisterRecordDo>> todayRegMap = waRegList.stream()
                .filter(row -> clockDate.equals(row.getBelongDate())).collect(Collectors.groupingBy(WaRegisterRecordDo::getEmpid));
        if (MapUtils.isEmpty(todayRegMap)) {
            log.debug("ClockAnalyseV1 fail clockDate={} record empty, time={}", clockDate, System.currentTimeMillis());
            return;
        }

        // 分析当日打卡记录
        List<WaRegisterRecordDo> updList = new ArrayList<>();
        todayRegMap.forEach((empId, regList) -> {
            List<WaRegisterRecordDo> analyzedRegList = analyzeClockRecords(empId, empShiftDoMap, regList, clockDate, signOnceEmpForToday, dataCacheDto);
            Set<Integer> analyzedRegIdSet = analyzedRegList.stream()
                    .map(WaRegisterRecordDo::getRecordId)
                    .collect(Collectors.toSet());
            updList.addAll(analyzedRegList);
            if (CollectionUtils.isNotEmpty(regList)) {
                List<WaRegisterRecordDo> unanalyzedRegList = regList.stream()
                        .filter(record -> !analyzedRegIdSet.contains(record.getRecordId()))
                        .collect(Collectors.toList());
                updList.addAll(unanalyzedRegList);
            }
        });
        fastUpdList(updList);
        log.debug("ClockAnalyseV1 finish params={}, time={}",
                String.format("%s_%s_%s", belongOrgId, date, type),
                System.currentTimeMillis());
    }

    /**
     * 过滤出开启打卡分析联动加班单据的员工ID
     *
     * @param empShiftDoMap 员工班次映射，key格式为 "empId_date"
     * @return 员工ID列表
     */
    @Override
    public List<Long> getEmpIdListByOt(Map<String, WaShiftDo> empShiftDoMap) {
        if (MapUtils.isEmpty(empShiftDoMap)) {
            return new ArrayList<>();
        }

        return empShiftDoMap.entrySet().stream().filter(entry -> {
            WaShiftDo shift = entry.getValue();
            return !DateTypeEnum.DATE_TYP_1.getIndex().equals(shift.getDateType())
                    && StringUtils.isNotBlank(shift.getClockAnalysisRule())
                    && shift.getClockAnalysisRule().contains(ShiftClockAnalysisRuleTypeEnum.BY_OT.getCode());
        }).map(entry -> {
            try {
                String[] keys = entry.getKey().split("_");
                if (keys.length > 0) {
                    return Long.valueOf(keys[0]);
                } else {
                    log.debug("Invalid key format, expected 'empId_date': {}", entry.getKey());
                    return null;
                }
            } catch (NumberFormatException e) {
                log.debug("Failed to parse empId from key: {}", entry.getKey(), e);
                return null;
            }
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    /**
     * 分析打卡记录（一次卡或多次卡）
     *
     * @param empId               员工ID
     * @param empShiftDoMap       员工班次信息
     * @param regList             打卡记录列表
     * @param clockDate           打卡日期
     * @param signOnceEmpForToday 一次卡员工信息
     * @return 分析后的打卡记录列表
     */
    private List<WaRegisterRecordDo> analyzeClockRecords(Long empId,
                                                         Map<String, WaShiftDo> empShiftDoMap,
                                                         List<WaRegisterRecordDo> regList,
                                                         Long clockDate,
                                                         Map<Long, EmpParseGroup> signOnceEmpForToday,
                                                         ClockAnalyseDataCacheDto dataCacheDto) {
        if (signOnceEmpForToday.containsKey(empId)) {
            // 一次卡分析
            EmpParseGroup empParseGroup = signOnceEmpForToday.get(empId);
            List<WaRegisterRecordDo> onceRegUpdList = analyseOnceClock(empShiftDoMap, empParseGroup, regList, empId, clockDate, dataCacheDto);
            return onceRegUpdList != null ? onceRegUpdList : new ArrayList<>();
        } else {
            // 多次卡分析
            List<WaRegisterRecordDo> multiRegUpdList = analyseMultiClock(empShiftDoMap, regList, empId, clockDate, dataCacheDto);
            return multiRegUpdList != null ? multiRegUpdList : new ArrayList<>();
        }
    }

    private List<WaRegisterRecordDo> filterByShiftOtTime(Map<String, WaShiftDo> empShiftDoMap,
                                                         List<WaRegisterRecordDo> regList,
                                                         Long empId,
                                                         Long clockDate,
                                                         ClockAnalyseDataCacheDto dataCacheDto) {
        List<WaRegisterRecordDo> afterFilterRegList = regList;
        WaShiftDo clockDateShiftDo = empShiftDoMap.get(empId + "_" + clockDate);
        if (null != clockDateShiftDo) {
            List<WaShiftDo> clockDateShiftDoList = clockDateShiftDo.doGetShiftDoList();
            if (clockDateShiftDoList.size() == 1) {
                WaShiftDef clockDateShiftDef = ObjectConverter.convert(clockDateShiftDo, WaShiftDef.class);
                Long minOtStartTime;
                Integer clockTimeRange; // 取卡时间范围，单位小时
                if (null != (clockTimeRange = WaShiftDo.ifEnableAnalyseClockByOt(clockDateShiftDef))
                        && null != (minOtStartTime = dataCacheDto.doGetMinOtStartTime(empId, clockDate))) {
                    // 根据加班时间范围进行过滤
                    long criticalTime = minOtStartTime - (clockTimeRange * 60 * 60);
                    log.debug("filterByShift.criticalTime={}", criticalTime);
                    afterFilterRegList = regList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() < criticalTime).collect(Collectors.toList());
                }
            }
        }
        return afterFilterRegList;
    }

    /**
     * 多次卡打卡分析：前一日打卡分析
     *
     * @param updList
     * @param empShiftDoMap
     * @param regList
     * @param empId
     * @param clockDate
     * @return 前一日打卡记录ID
     */
    public List<Integer> analyseMultiClockOfYesterday(List<WaRegisterRecordDo> updList,
                                                      Map<String, WaShiftDo> empShiftDoMap,
                                                      List<WaRegisterRecordDo> regList,
                                                      Long empId,
                                                      Long clockDate,
                                                      ClockAnalyseDataCacheDto dataCacheDto) {
        // 当前一天班次跨夜或者打卡区间跨夜时，判断是否为前一天的考勤打卡: 如果当天打卡时间在前一天班次的下班打卡范围内，则此卡归属于前一天
        List<Integer> yesterdayRegIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regList)) {
            log.debug("yesterday multi ClockAnalyseV1 fail clockDate={} record empty, time={}", clockDate, System.currentTimeMillis());
            return yesterdayRegIdList;
        }

        List<WaRegisterRecordDo> afterFilterRegList = filterByShiftOtTime(empShiftDoMap, regList, empId, clockDate, dataCacheDto);
        if (CollectionUtils.isEmpty(afterFilterRegList)) {
            log.debug("yesterday multi ClockAnalyseV1 fail clockDate={} afterFilterRegList empty, time={}", clockDate, System.currentTimeMillis());
            return yesterdayRegIdList;
        }
        afterFilterRegList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

        Long yesterday = DateUtil.addDate(clockDate * 1000, -1);
        WaShiftDo yesterdayShiftDo = empShiftDoMap.get(empId + "_" + yesterday);
        if (null == yesterdayShiftDo) {
            log.debug("yesterday multi ClockAnalyseV1 fail clockDate={} shift empty, time={}", yesterday, System.currentTimeMillis());
            return yesterdayRegIdList;
        }
        List<WaShiftDo> shiftDoList = yesterdayShiftDo.doGetShiftDoList();
        List<Integer> shiftDefIds = shiftDoList.stream().map(WaShiftDo::getShiftDefId).collect(Collectors.toList());
        log.debug("yesterday multi ClockAnalyseV1 yesterday={}, shift={}, time={}",
                yesterday, FastjsonUtil.toJsonStr(shiftDefIds), System.currentTimeMillis());

        if (shiftDoList.size() > 1) {// 一天多个班
            // 最晚班次
            WaShiftDef lastShiftDef = yesterdayShiftDo.doGetLastShiftDef();
            WaShiftDef lastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(lastShiftDef);

            // 最晚班次的最晚班段的下班打卡时间
            if (null == lastShiftWorkTime.getOffDutyStartTime() || null == lastShiftWorkTime.getOffDutyEndTime()) {
                log.debug("yesterday multi ClockAnalyseV1 fail multi signtime empty shiftid={}, time={}", lastShiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                return yesterdayRegIdList;
            }
            long lastOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(lastShiftWorkTime, lastShiftWorkTime.getDateType())
                    ? yesterday + (lastShiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                    : yesterday + lastShiftWorkTime.getOffDutyEndTime() * 60L;

            // 最晚班次内的打卡记录
            List<WaRegisterRecordDo> yesterdayRegList = afterFilterRegList.stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= lastOffDutyEndTime).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(yesterdayRegList)) {
                return yesterdayRegIdList;
            }

            for (WaShiftDo shiftDo : shiftDoList) {
                if (CollectionUtils.isEmpty(yesterdayRegList)) {
                    break;
                }
                WaShiftDef shiftDef = ObjectConverter.convert(shiftDo, WaShiftDef.class);
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
                if (!CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())) {
                    continue;
                }

                // 当前班次最晚班段
                List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
                multiWorkTimeList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime).reversed());
                MultiWorkTimeBaseDto lastWorkTimeDto = multiWorkTimeList.get(0);

                // 当前班次最晚班段内的上下班打卡时间
                if (null == lastWorkTimeDto.getOnDutyStartTime()
                        || null == lastWorkTimeDto.getOnDutyEndTime()
                        || null == lastWorkTimeDto.getOffDutyStartTime()
                        || null == lastWorkTimeDto.getOffDutyEndTime()) {
                    log.debug("yesterday multi ClockAnalyseV1 fail multi signtime empty shiftid: {}, time={}", shiftDef.getShiftDefId(), System.currentTimeMillis());
                    continue;
                }

                long onDutyStartTime = yesterday + lastWorkTimeDto.doGetRealOnDutyStartTime() * 60;
                long onDutyEndTime = yesterday + lastWorkTimeDto.doGetRealOnDutyEndTime() * 60;
                long offDutyStartTime = yesterday + lastWorkTimeDto.doGetRealOffDutyStartTime() * 60L;
                long offDutyEndTime = yesterday + lastWorkTimeDto.doGetRealOffDutyEndTime() * 60L;

                // 当前班次最晚班段内的打卡记录
                List<WaRegisterRecordDo> currentShiftRegList;
                if (lastShiftWorkTime.getShiftDefId().equals(shiftWorkTime.getShiftDefId())) {
                    currentShiftRegList = new ArrayList<>(yesterdayRegList);
                } else {
                    currentShiftRegList = yesterdayRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime).collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(currentShiftRegList)) {
                    continue;
                }

                for (WaRegisterRecordDo wrrd : currentShiftRegList) {
                    wrrd.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                    wrrd.setBelongDate(yesterday);
                    wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                    wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                    wrrd.setStartTime(shiftWorkTime.getStartTime());
                    wrrd.setEndTime(shiftWorkTime.getEndTime());
                    wrrd.setNormalDate(String.format("%s-%s",
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyStartTime()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyEndTime())));
                    if (multiWorkTimeList.size() > 1) {
                        checkClockTime(wrrd, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                    } else {
                        checkClockTime(wrrd, shiftWorkTime, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                    }
                    updList.add(wrrd);
                    yesterdayRegIdList.add(wrrd.getRecordId());
                    yesterdayRegList.removeIf(it -> it.getRecordId().equals(wrrd.getRecordId()));
                }
            }
        } else {// 一天排一个班
            WaShiftDef shiftDef = ObjectConverter.convert(yesterdayShiftDo, WaShiftDef.class);
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

            // 最晚班段
            List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
            multiWorkTimeList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime).reversed());
            MultiWorkTimeBaseDto lastWorkTimeDto = multiWorkTimeList.get(0);

            // 最晚班段内的上下班打卡时间
            if (null == lastWorkTimeDto.getOnDutyStartTime()
                    || null == lastWorkTimeDto.getOnDutyEndTime()
                    || null == lastWorkTimeDto.getOffDutyStartTime()
                    || null == lastWorkTimeDto.getOffDutyEndTime()) {
                log.debug("yesterday multi ClockAnalyseV1 fail signtime empty shiftid={}, time={}", shiftDef.getShiftDefId(), System.currentTimeMillis());
                return yesterdayRegIdList;
            }
            long onDutyStartTime = yesterday + lastWorkTimeDto.doGetRealOnDutyStartTime() * 60;
            long onDutyEndTime = yesterday + lastWorkTimeDto.doGetRealOnDutyEndTime() * 60;
            long offDutyStartTime = yesterday + lastWorkTimeDto.doGetRealOffDutyStartTime() * 60L;
            long offDutyEndTime = yesterday + lastWorkTimeDto.doGetRealOffDutyEndTime() * 60L;

            // 筛选出归属于前一天班次的打卡数据
            List<WaRegisterRecordDo> yesterdayRegList = new ArrayList<>();
            Long maxOtEndTime = dataCacheDto.doGetMaxOtEndTime(empId, yesterday);// 当日加班最晚结束时间
            Integer clockTimeRange = WaShiftDo.ifEnableAnalyseClockByOt(shiftDef); // 取卡时间范围，单位小时
            Long maxLtEndTime = dataCacheDto.doGetMaxLtEndTime(empId, yesterday);
            boolean crossNight = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType());
            long shiftEndTime = crossNight ? clockDate + shiftWorkTime.getEndTime() * 60 : yesterday + shiftWorkTime.getEndTime() * 60;

            if (null != clockTimeRange && null == maxOtEndTime) {
                // 前一天是休息日且开启打卡按照加班时间分析且前一天无加班单时，则不分析
                return yesterdayRegIdList;
            } else if (null != clockTimeRange) {
                // 前一天是休息日且开启打卡按照加班时间分析且前一天有加班单时，则根据当日加班时间范围进行过滤
                long criticalTime = maxOtEndTime + (clockTimeRange * 60 * 60);
                yesterdayRegList = afterFilterRegList.stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= criticalTime).collect(Collectors.toList());
            } else {
                boolean continueFilter = maxLtEndTime == null || maxLtEndTime < shiftEndTime;
                if (null != maxLtEndTime) {
                    if (crossNight) {
                        if (maxLtEndTime >= shiftEndTime) {
                            yesterdayRegList = afterFilterRegList.stream()
                                    .filter(wrrd -> wrrd.getRegDateTime() <= shiftEndTime).collect(Collectors.toList());
                        }
                    } else {
                        if (maxLtEndTime >= shiftEndTime) {
                            return yesterdayRegIdList;
                        }
                    }
                }

                if (continueFilter) {
                    // 检查班次的下班打卡结束时间是否跨夜
                    boolean crossNightForSignOff = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType());
                    if (!crossNightForSignOff) {
                        return yesterdayRegIdList;
                    }

                    // 最晚班段内的打卡记录
                    yesterdayRegList = afterFilterRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime).collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isEmpty(yesterdayRegList)) {
                return yesterdayRegIdList;
            }

            for (WaRegisterRecordDo wrrd : yesterdayRegList) {
                wrrd.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                wrrd.setBelongDate(yesterday);
                wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                wrrd.setStartTime(lastWorkTimeDto.getStartTime());
                wrrd.setEndTime(lastWorkTimeDto.getEndTime());
                wrrd.setNormalDate(String.format("%s-%s",
                        DateUtil.convertMinuteToTime(lastWorkTimeDto.getOffDutyStartTime()),
                        DateUtil.convertMinuteToTime(lastWorkTimeDto.getOffDutyEndTime())));
                if (multiWorkTimeList.size() > 1) {
                    checkClockTime(wrrd, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                } else {
                    checkClockTime(wrrd, shiftWorkTime, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                }
                updList.add(wrrd);
                yesterdayRegIdList.add(wrrd.getRecordId());
            }
        }
        return yesterdayRegIdList;
    }

    /**
     * 多次卡打卡分析：当日打卡分析
     *
     * @param empShiftDoMap
     * @param regList
     * @param empId
     * @param clockDate
     * @param updList
     * @return 当日打卡记录ID
     */
    public List<Integer> analyseMultiClockOfToday(Map<String, WaShiftDo> empShiftDoMap,
                                                  List<WaRegisterRecordDo> regList,
                                                  Long empId,
                                                  Long clockDate,
                                                  List<WaRegisterRecordDo> updList,
                                                  ClockAnalyseDataCacheDto dataCacheDto) {
        List<Integer> todayRegIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(regList)) {
            log.debug("clockDate multi ClockAnalyseV1 fail clockDate={} record empty, time={}", clockDate, System.currentTimeMillis());
            return todayRegIdList;
        }

        // 当天排班
        WaShiftDo clockDateShiftDo;
        if (null == (clockDateShiftDo = empShiftDoMap.get(empId + "_" + clockDate))) {
            log.debug("clockDate multi ClockAnalyseV1 fail clockDate={} shift empty, time={}", clockDate, System.currentTimeMillis());
            return todayRegIdList;
        }

        List<WaShiftDo> shiftDoList = clockDateShiftDo.doGetShiftDoList();
        List<Integer> shiftDefIds = shiftDoList.stream().map(WaShiftDo::getShiftDefId).collect(Collectors.toList());
        log.debug("clockDate multi ClockAnalyseV1 clockDate={}, shift={}, time={}",
                clockDate, FastjsonUtil.toJsonStr(shiftDefIds), System.currentTimeMillis());

        Long nextDay = DateUtil.addDate(clockDate * 1000, 1);
        List<WaRegisterRecordDo> afterFilterRegList = filterByShiftOtTime(empShiftDoMap, regList, empId, nextDay, dataCacheDto);
        if (CollectionUtils.isEmpty(afterFilterRegList)) {
            log.debug("clockDate multi ClockAnalyseV1 fail clockDate={} afterFilterRegList empty, time={}", clockDate, System.currentTimeMillis());
            return todayRegIdList;
        }
        afterFilterRegList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

        if (shiftDoList.size() > 1) {// 一天多个班
            shiftDoList.sort(Comparator.comparing(WaShiftDo::doGetRealStartTime));
            WaShiftDef lastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(clockDateShiftDo.doGetLastShiftDef());

            List<WaRegisterRecordDo> todayRegList = afterFilterRegList;

            // 查询次日最早上班打卡时间
            long nextDayFirstOnDutyStartTime = getNextDatFirstOnDutyStartTime(empId, clockDate, empShiftDoMap);
            if (nextDayFirstOnDutyStartTime > 0) {
                long lastShiftLastOffDutyEndTime;
                if (null != lastShiftWorkTime.getOffDutyEndTime()) {
                    lastShiftLastOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(lastShiftWorkTime, lastShiftWorkTime.getDateType())
                            ? clockDate + (lastShiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                            : clockDate + lastShiftWorkTime.getOffDutyEndTime() * 60L;
                } else {
                    lastShiftLastOffDutyEndTime = clockDate + 86399;
                }

                todayRegList = afterFilterRegList.stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= lastShiftLastOffDutyEndTime
                                || wrrd.getRegDateTime() < nextDayFirstOnDutyStartTime)
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(todayRegList)) {
                log.debug("clockDate multi shift ClockAnalyseV1 fail clockDate={} todayRegList empty, time={}", clockDate, System.currentTimeMillis());
                return todayRegIdList;
            }

            for (WaShiftDo shiftDo : shiftDoList) {
                if (CollectionUtils.isEmpty(todayRegList)) {
                    log.debug("clockDate multi ClockAnalyseV1 for multiShift End regList empty");
                    break;
                }

                WaShiftDef shiftDef = ObjectConverter.convert(shiftDo, WaShiftDef.class);
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

                // 当前班次最班下班打卡时间
                if (null == shiftWorkTime.getOffDutyEndTime()) {
                    log.debug("clockDate multi ClockAnalyseV1 fail multi signofftime empty shiftid={}, time={}",
                            shiftDef.getShiftDefId(), System.currentTimeMillis());
                    continue;
                }
                long lastOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())
                        ? clockDate + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                        : clockDate + shiftWorkTime.getOffDutyEndTime() * 60L;

                // 当前班次内的打卡记录
                List<WaRegisterRecordDo> currentShiftRegList;
                if (lastShiftWorkTime.getShiftDefId().equals(shiftWorkTime.getShiftDefId())) {// 最晚一个班次
                    currentShiftRegList = new ArrayList<>(todayRegList);
                } else {
                    currentShiftRegList = todayRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= lastOffDutyEndTime).collect(Collectors.toList());
                    todayRegList.removeIf(wrrd -> wrrd.getRegDateTime() <= lastOffDutyEndTime);
                }
                if (CollectionUtils.isEmpty(currentShiftRegList)) {
                    continue;
                }

                // 当前班次的所有班段
                List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
                int size = multiWorkTimeList.size();
                for (int i = 0; i < multiWorkTimeList.size(); i++) {
                    MultiWorkTimeBaseDto workTimeDto = multiWorkTimeList.get(i);
                    if (CollectionUtils.isEmpty(currentShiftRegList)) {
                        log.debug("clockDate multi ClockAnalyseV1 for multiWork End currentShiftRegList empty");
                        break;
                    }

                    // 当前班段内的上下班打卡时间
                    if (null == workTimeDto.getOnDutyStartTime()
                            || null == workTimeDto.getOnDutyEndTime()
                            || null == workTimeDto.getOffDutyStartTime()
                            || null == workTimeDto.getOffDutyEndTime()) {
                        log.debug("clockDate multi ClockAnalyseV1 fail multi signtime empty shiftid={}, time={}",
                                shiftDef.getShiftDefId(), System.currentTimeMillis());
                        continue;
                    }
                    long onDutyStartTime = clockDate + workTimeDto.doGetRealOnDutyStartTime() * 60;
                    long onDutyEndTime = clockDate + workTimeDto.doGetRealOnDutyEndTime() * 60;
                    long offDutyStartTime = clockDate + workTimeDto.doGetRealOffDutyStartTime() * 60L;
                    long offDutyEndTime = clockDate + workTimeDto.doGetRealOffDutyEndTime() * 60L;

                    // 当前班段内的打卡记录
                    List<WaRegisterRecordDo> currentWorkTimeRegList;
                    if (i == size - 1) {// 最晚一段班
                        currentWorkTimeRegList = new ArrayList<>(currentShiftRegList);
                    } else {
                        currentWorkTimeRegList = currentShiftRegList.stream()
                                .filter(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime).collect(Collectors.toList());
                        currentShiftRegList.removeIf(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime);
                    }
                    if (CollectionUtils.isEmpty(currentWorkTimeRegList)) {
                        log.debug("clockDate multi ClockAnalyseV1 for multiWork End currentWorkTimeRegList empty");
                        continue;
                    }
                    currentWorkTimeRegList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

                    // 签到
                    WaRegisterRecordDo signInReg = currentWorkTimeRegList.get(0);
                    signInReg.setRegisterType(ClockTypeEnum.SIGN_IN.getIndex());
                    signInReg.setIfValid(ValidStatusEnum.VALID.getIndex());
                    signInReg.setShiftDefId(shiftWorkTime.getShiftDefId());
                    signInReg.setStartTime(workTimeDto.getStartTime());
                    signInReg.setEndTime(workTimeDto.getEndTime());
                    signInReg.setNormalDate(String.format("%s-%s",
                            DateUtil.convertMinuteToTime(workTimeDto.doGetOnDutyStartTimeForView()),
                            DateUtil.convertMinuteToTime(workTimeDto.getOnDutyEndTime())));
                    if (multiWorkTimeList.size() > 1) {
                        checkClockTime(signInReg, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                    } else {
                        checkClockTime(signInReg, shiftWorkTime, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                    }
                    updList.add(signInReg);
                    todayRegIdList.add(signInReg.getRecordId());

                    // 签退
                    if (currentWorkTimeRegList.size() > 1) {
                        WaRegisterRecordDo signOffReg = currentWorkTimeRegList.get(currentWorkTimeRegList.size() - 1);
                        signOffReg.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                        signOffReg.setIfValid(ValidStatusEnum.VALID.getIndex());
                        signOffReg.setShiftDefId(shiftWorkTime.getShiftDefId());
                        signOffReg.setStartTime(workTimeDto.getStartTime());
                        signOffReg.setEndTime(workTimeDto.getEndTime());
                        signOffReg.setNormalDate(String.format("%s-%s",
                                DateUtil.convertMinuteToTime(workTimeDto.getOffDutyStartTime()),
                                DateUtil.convertMinuteToTime(workTimeDto.getOffDutyEndTime())));
                        if (multiWorkTimeList.size() > 1) {
                            checkClockTime(signOffReg, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                        } else {
                            checkClockTime(signOffReg, shiftWorkTime, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                        }
                        updList.add(signOffReg);
                        todayRegIdList.add(signOffReg.getRecordId());

                        // 无效卡
                        List<WaRegisterRecordDo> otherRegList = currentWorkTimeRegList.stream()
                                .filter(row -> (!row.getRecordId().equals(signInReg.getRecordId())
                                        && !row.getRecordId().equals(signOffReg.getRecordId())))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(otherRegList)) {
                            otherRegList.forEach(wrrd -> {
                                wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                                wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                                wrrd.setStartTime(workTimeDto.getStartTime());
                                wrrd.setEndTime(workTimeDto.getEndTime());
                                todayRegIdList.add(wrrd.getRecordId());
                            });
                            updList.addAll(otherRegList);
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(todayRegList)) {
                List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(todayRegIdList)
                        ? todayRegList.stream().filter(it -> !todayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                        : todayRegList;
                if (CollectionUtils.isNotEmpty(unanalyzedRegList)) {
                    updList.addAll(unanalyzedRegList);
                    List<Integer> unanalyzedRegIdList = unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList());
                    todayRegIdList.addAll(unanalyzedRegIdList);
                }
            }
        } else {// 一天一个班
            WaShiftDef shiftDef = ObjectConverter.convert(clockDateShiftDo, WaShiftDef.class);
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

            // 筛选出归属于当日的打卡数据
            List<WaRegisterRecordDo> todayRegList = afterFilterRegList;

            Long maxOtEndTime;// 当日加班最晚结束时间
            Integer clockTimeRange; // 取卡时间范围，单位小时
            if (null != (clockTimeRange = WaShiftDo.ifEnableAnalyseClockByOt(shiftDef))
                    && null != (maxOtEndTime = dataCacheDto.doGetMaxOtEndTime(empId, clockDate))) {
                // 根据当日加班时间范围进行过滤
                long criticalTime = maxOtEndTime + (clockTimeRange * 60 * 60);
                todayRegList = afterFilterRegList.stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= criticalTime).collect(Collectors.toList());
            } else {
                // 查询次日最早上班打卡时间
                long nextDayFirstOnDutyStartTime = getNextDatFirstOnDutyStartTime(empId, clockDate, empShiftDoMap);
                if (nextDayFirstOnDutyStartTime > 0) {
                    long lastOffDutyEndTime;
                    if (null != shiftWorkTime.getOffDutyEndTime()) {
                        lastOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())
                                ? clockDate + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                                : clockDate + shiftWorkTime.getOffDutyEndTime() * 60L;
                    } else {
                        lastOffDutyEndTime = clockDate + 86399;
                    }

                    todayRegList = afterFilterRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= lastOffDutyEndTime
                                    || wrrd.getRegDateTime() < nextDayFirstOnDutyStartTime)
                            .collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isEmpty(todayRegList)) {
                log.debug("clockDate shift ClockAnalyseV1 fail clockDate={} todayRegList empty, time={}", clockDate, System.currentTimeMillis());
                return todayRegIdList;
            }

            // 当前班次内的所有班段
            List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
            int size = multiWorkTimeList.size();
            for (int i = 0; i < multiWorkTimeList.size(); i++) {
                if (CollectionUtils.isEmpty(todayRegList)) {
                    log.debug("clockDate multi ClockAnalyseV1 End regList empty");
                    break;
                }

                MultiWorkTimeBaseDto workTimeDto = multiWorkTimeList.get(i);
                // 当前班段内的上下班打卡时间
                if (null == workTimeDto.getOnDutyStartTime()
                        || null == workTimeDto.getOnDutyEndTime()
                        || null == workTimeDto.getOffDutyStartTime()
                        || null == workTimeDto.getOffDutyEndTime()) {
                    log.debug("clockDate multi ClockAnalyseV1 fail signtime empty shiftid={}, time={}",
                            shiftDef.getShiftDefId(), System.currentTimeMillis());
                    continue;
                }
                long onDutyStartTime = clockDate + workTimeDto.doGetRealOnDutyStartTime() * 60;
                long onDutyEndTime = clockDate + workTimeDto.doGetRealOnDutyEndTime() * 60;
                long offDutyStartTime = clockDate + workTimeDto.doGetRealOffDutyStartTime() * 60L;
                long offDutyEndTime = clockDate + workTimeDto.doGetRealOffDutyEndTime() * 60L;

                // 当前班段内的打卡记录
                List<WaRegisterRecordDo> currentWorkTimeRegList;
                if (i == size - 1) {// 最晚一段班
                    currentWorkTimeRegList = new ArrayList<>(todayRegList);
                } else {
                    currentWorkTimeRegList = todayRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime).collect(Collectors.toList());
                    todayRegList.removeIf(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime);
                }
                if (CollectionUtils.isEmpty(currentWorkTimeRegList)) {
                    log.debug("clockDate multi ClockAnalyseV1 End currentWorkTimeRegList empty");
                    continue;
                }
                currentWorkTimeRegList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

                // 签到
                WaRegisterRecordDo signInReg = currentWorkTimeRegList.get(0);
                signInReg.setRegisterType(ClockTypeEnum.SIGN_IN.getIndex());
                signInReg.setIfValid(ValidStatusEnum.VALID.getIndex());
                signInReg.setShiftDefId(shiftWorkTime.getShiftDefId());
                signInReg.setStartTime(workTimeDto.getStartTime());
                signInReg.setEndTime(workTimeDto.getEndTime());
                signInReg.setNormalDate(String.format("%s-%s",
                        DateUtil.convertMinuteToTime(workTimeDto.doGetOnDutyStartTimeForView()),
                        DateUtil.convertMinuteToTime(workTimeDto.getOnDutyEndTime())));
                if (multiWorkTimeList.size() > 1) {
                    checkClockTime(signInReg, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                } else {
                    checkClockTime(signInReg, shiftWorkTime, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                }
                updList.add(signInReg);
                todayRegIdList.add(signInReg.getRecordId());

                // 签退
                if (currentWorkTimeRegList.size() > 1) {
                    WaRegisterRecordDo signOffReg = currentWorkTimeRegList.get(currentWorkTimeRegList.size() - 1);
                    signOffReg.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                    signOffReg.setIfValid(ValidStatusEnum.VALID.getIndex());
                    signOffReg.setShiftDefId(shiftWorkTime.getShiftDefId());
                    signOffReg.setStartTime(workTimeDto.getStartTime());
                    signOffReg.setEndTime(workTimeDto.getEndTime());
                    signOffReg.setNormalDate(String.format("%s-%s",
                            DateUtil.convertMinuteToTime(workTimeDto.getOffDutyStartTime()),
                            DateUtil.convertMinuteToTime(workTimeDto.getOffDutyEndTime())));
                    if (multiWorkTimeList.size() > 1) {
                        checkClockTime(signOffReg, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                    } else {
                        checkClockTime(signOffReg, shiftWorkTime, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                    }
                    updList.add(signOffReg);
                    todayRegIdList.add(signOffReg.getRecordId());

                    // 无效卡
                    List<WaRegisterRecordDo> otherRegList = currentWorkTimeRegList.stream()
                            .filter(row -> (!row.getRecordId().equals(signInReg.getRecordId())
                                    && !row.getRecordId().equals(signOffReg.getRecordId())))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(otherRegList)) {
                        otherRegList.forEach(wrrd -> {
                            wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                            wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                            wrrd.setStartTime(workTimeDto.getStartTime());
                            wrrd.setEndTime(workTimeDto.getEndTime());
                            todayRegIdList.add(wrrd.getRecordId());
                        });
                        updList.addAll(otherRegList);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(todayRegList)) {
                List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(todayRegIdList)
                        ? todayRegList.stream().filter(it -> !todayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                        : todayRegList;
                if (CollectionUtils.isNotEmpty(unanalyzedRegList)) {
                    updList.addAll(unanalyzedRegList);
                    List<Integer> unanalyzedRegIdList = unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList());
                    todayRegIdList.addAll(unanalyzedRegIdList);
                }
            }
        }
        return todayRegIdList;
    }

    /**
     * 多次卡打卡分析: 次日打卡分析
     *
     * @param updList
     * @param empShiftDoMap
     * @param regList
     * @param empId
     * @param clockDate
     * @return 次日打卡记录ID
     */
    public List<Integer> analyseMultiClockOfNextDay(List<WaRegisterRecordDo> updList,
                                                    Map<String, WaShiftDo> empShiftDoMap,
                                                    List<WaRegisterRecordDo> regList,
                                                    Long empId,
                                                    Long clockDate) {
        List<Integer> nextDayRegIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regList)) {
            log.debug("nextDay multi ClockAnalyseV1 fail clockDate={} record empty, time={}", clockDate, System.currentTimeMillis());
            return nextDayRegIdList;
        }
        regList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

        // 次日打卡数据
        List<WaRegisterRecordDo> nextDayRegList = regList;

        // 次日排班
        Long nextDay = DateUtil.addDate(clockDate * 1000, 1);
        WaShiftDo nextDayShiftDo = empShiftDoMap.get(empId + "_" + nextDay);
        if (null == nextDayShiftDo) {
            log.debug("nextDay multi ClockAnalyseV1 fail clockDate={} shift empty, time={}", nextDay, System.currentTimeMillis());
            return nextDayRegIdList;
        }
        List<WaShiftDo> shiftDoList = nextDayShiftDo.doGetShiftDoList();
        List<Integer> shiftDefIds = shiftDoList.stream().map(WaShiftDo::getShiftDefId).collect(Collectors.toList());
        log.debug("nextDay multi ClockAnalyseV1 clockDate={}, shift={}, time={}",
                nextDay, FastjsonUtil.toJsonStr(shiftDefIds), System.currentTimeMillis());

        if (shiftDoList.size() > 1) {// 一天多个班
            WaShiftDef firstShiftDef = nextDayShiftDo.doGetFirstShiftDef();
            WaShiftDef firstShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(firstShiftDef);

            List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(firstShiftDef);
            multiWorkTimeList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime));
            MultiWorkTimeBaseDto firstWorkTimeDto = multiWorkTimeList.get(0);

            Long onDutyStartTime = null != firstWorkTimeDto.getOnDutyStartTime() ? nextDay + firstWorkTimeDto.doGetRealOnDutyStartTime() * 60 : null;
            Long onDutyEndTime = null != firstWorkTimeDto.getOnDutyEndTime() ? nextDay + firstWorkTimeDto.doGetRealOnDutyEndTime() * 60 : null;
            Long offDutyStartTime = null != firstWorkTimeDto.getOffDutyStartTime() ? nextDay + firstWorkTimeDto.doGetRealOffDutyStartTime() * 60L : null;
            Long offDutyEndTime = null != firstWorkTimeDto.getOffDutyEndTime() ? nextDay + firstWorkTimeDto.doGetRealOffDutyEndTime() * 60L : null;

            for (WaRegisterRecordDo wrrd : nextDayRegList) {
                wrrd.setRegisterType(ClockTypeEnum.SIGN_IN.getIndex());
                wrrd.setBelongDate(nextDay);
                wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                wrrd.setShiftDefId(firstShiftWorkTime.getShiftDefId());
                wrrd.setStartTime(firstWorkTimeDto.getStartTime());
                wrrd.setEndTime(firstWorkTimeDto.getEndTime());
                if (null != firstWorkTimeDto.getOnDutyStartTime() && null != firstWorkTimeDto.getOnDutyEndTime()) {
                    wrrd.setNormalDate(String.format("%s-%s",
                            DateUtil.convertMinuteToTime(firstWorkTimeDto.doGetOnDutyStartTimeForView()),
                            DateUtil.convertMinuteToTime(firstWorkTimeDto.getOnDutyEndTime())));
                }
                if (multiWorkTimeList.size() > 1) {
                    checkClockTime(wrrd, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                } else {
                    checkClockTime(wrrd, firstShiftWorkTime, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                }
                updList.add(wrrd);
                nextDayRegIdList.add(wrrd.getRecordId());
            }
        } else {// 一天排一个班
            WaShiftDef shiftDef = ObjectConverter.convert(nextDayShiftDo, WaShiftDef.class);
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

            List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
            multiWorkTimeList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime));
            MultiWorkTimeBaseDto firstWorkTimeDto = multiWorkTimeList.get(0);

            Long onDutyStartTime = null != firstWorkTimeDto.getOnDutyStartTime() ? nextDay + firstWorkTimeDto.doGetRealOnDutyStartTime() * 60 : null;
            Long onDutyEndTime = null != firstWorkTimeDto.getOnDutyEndTime() ? nextDay + firstWorkTimeDto.doGetRealOnDutyEndTime() * 60 : null;
            Long offDutyStartTime = null != firstWorkTimeDto.getOffDutyStartTime() ? nextDay + firstWorkTimeDto.doGetRealOffDutyStartTime() * 60L : null;
            Long offDutyEndTime = null != firstWorkTimeDto.getOffDutyEndTime() ? nextDay + firstWorkTimeDto.doGetRealOffDutyEndTime() * 60L : null;

            for (WaRegisterRecordDo wrrd : nextDayRegList) {
                wrrd.setRegisterType(ClockTypeEnum.SIGN_IN.getIndex());
                wrrd.setBelongDate(nextDay);
                wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                wrrd.setStartTime(firstWorkTimeDto.getStartTime());
                wrrd.setEndTime(firstWorkTimeDto.getEndTime());
                if (null != firstWorkTimeDto.getOnDutyStartTime() && null != firstWorkTimeDto.getOnDutyEndTime()) {
                    wrrd.setNormalDate(String.format("%s-%s",
                            DateUtil.convertMinuteToTime(firstWorkTimeDto.doGetOnDutyStartTimeForView()),
                            DateUtil.convertMinuteToTime(firstWorkTimeDto.getOnDutyEndTime())));
                }
                if (multiWorkTimeList.size() > 1) {
                    checkClockTime(wrrd, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                } else {
                    checkClockTime(wrrd, shiftWorkTime, onDutyStartTime, onDutyEndTime, offDutyStartTime, offDutyEndTime);
                }
                updList.add(wrrd);
                nextDayRegIdList.add(wrrd.getRecordId());
            }
        }
        return nextDayRegIdList;
    }

    /**
     * 多次卡打卡分析
     *
     * @param empShiftDoMap
     * @param regList
     * @param empId
     * @param clockDate
     */
    public List<WaRegisterRecordDo> analyseMultiClock(Map<String, WaShiftDo> empShiftDoMap,
                                                      List<WaRegisterRecordDo> regList,
                                                      Long empId,
                                                      Long clockDate,
                                                      ClockAnalyseDataCacheDto dataCacheDto) {
        List<WaRegisterRecordDo> updList = new ArrayList<>();

        // 前一日打卡分析
        List<Integer> yesterdayRegIdList = analyseMultiClockOfYesterday(updList, empShiftDoMap, regList, empId, clockDate,
                dataCacheDto);
        List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(yesterdayRegIdList)
                ? regList.stream().filter(it -> !yesterdayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : regList;

        // 当日打卡分析
        List<Integer> todayRegIdList = analyseMultiClockOfToday(empShiftDoMap, unanalyzedRegList, empId, clockDate, updList,
                dataCacheDto);
        unanalyzedRegList = CollectionUtils.isNotEmpty(todayRegIdList)
                ? unanalyzedRegList.stream().filter(it -> !todayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : unanalyzedRegList;

        // 次日打卡分析
        List<Integer> nextDayRegIdList = analyseMultiClockOfNextDay(updList, empShiftDoMap, unanalyzedRegList, empId, clockDate);
        unanalyzedRegList = CollectionUtils.isNotEmpty(nextDayRegIdList)
                ? unanalyzedRegList.stream().filter(it -> !nextDayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : unanalyzedRegList;
        List<Integer> unanalyzedRegIdList = CollectionUtils.isNotEmpty(unanalyzedRegList)
                ? unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList())
                : Lists.newArrayList();
        log.debug("analyseMultiClock.unanalyzedRegList={}", FastjsonUtil.toJsonStr(unanalyzedRegIdList));
        return updList;
    }

    /**
     * 一次卡打卡分析
     *
     * @param empShiftInfo
     * @param empParseGroup
     * @param regList
     * @param empId
     * @param clockDate
     * @return
     */
    public List<WaRegisterRecordDo> analyseOnceClock(Map<String, WaShiftDo> empShiftInfo,
                                                     EmpParseGroup empParseGroup,
                                                     List<WaRegisterRecordDo> regList,
                                                     Long empId,
                                                     Long clockDate,
                                                     ClockAnalyseDataCacheDto dataCacheDto) {
        // 考勤方案规则: 有效打卡取值规则
        String clockRule = empParseGroup.getClockRule();
        Map ruleMap = getRuleMap(clockRule);
        Integer parseClockRule = null;
        if (ruleMap.get("clockRule") != null) {
            parseClockRule = Integer.valueOf(ruleMap.get("clockRule").toString());
        }
        log.debug("once ClockAnalyseV1 start rule={}, time={}", parseClockRule, System.currentTimeMillis());

        // 当日排班
        WaShiftDo clockDateShiftDo = empShiftInfo.get(empId + "_" + clockDate);
        Integer clockDateShiftId = null != clockDateShiftDo ? clockDateShiftDo.getShiftDefId() : null;
        log.debug("once ClockAnalyseV1 info: emp={}, clockDate={} shift={}, time={}", empId, clockDate, clockDateShiftId, System.currentTimeMillis());
        if (null != clockDateShiftDo && !DateTypeEnum.DATE_TYP_1.getIndex().equals(clockDateShiftDo.getDateType())) {
            parseClockRule = ParseGroupClockRuleEnum.SIGN_TIME_RANGE.getIndex();
        }
        parseClockRule = null == parseClockRule ? ParseGroupClockRuleEnum.SIGN_TIME_RANGE.getIndex() : parseClockRule;

        regList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));
        if (ParseGroupClockRuleEnum.WORK_TIME_RANGE.getIndex().equals(parseClockRule)) {
            return analyseOnceClockForWorkTimeRule(empShiftInfo, regList, empId, clockDate, dataCacheDto);
        } else if (ParseGroupClockRuleEnum.SIGN_TIME_RANGE.getIndex().equals(parseClockRule)) {
            return analyseOnceClockForSignTimeRule(empShiftInfo, regList, empId, clockDate, dataCacheDto);
        } else {
            log.debug("The unknown clockrule is resolved, emp={}, clockDate={}, clockRule={} time={}", empId, clockDate, clockRule, System.currentTimeMillis());
            return Lists.newArrayList();
        }
    }

    /**
     * 一次卡打卡分析（在班次打卡时段内存在打卡、补卡记录）：前一日打卡分析
     *
     * @param updList
     * @param empShiftInfo
     * @param regList
     * @param empId
     * @param clockDate
     * @return
     */
    public List<Integer> analyseOnceClockForSignTimeRuleOfYesterday(List<WaRegisterRecordDo> updList,
                                                                    Map<String, WaShiftDo> empShiftInfo,
                                                                    List<WaRegisterRecordDo> regList,
                                                                    Long empId,
                                                                    Long clockDate,
                                                                    ClockAnalyseDataCacheDto dataCacheDto) {
        // 当前一天班次跨夜或者打卡区间跨夜时，判断是否为前一天的考勤打卡
        List<Integer> yesterdayRegIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regList)) {
            log.debug("yesterday once clock for signtime analyse fail regs empty time={}", System.currentTimeMillis());
            return yesterdayRegIdList;
        }

        List<WaRegisterRecordDo> afterFilterRegList = filterByShiftOtTime(empShiftInfo, regList, empId, clockDate, dataCacheDto);
        if (CollectionUtils.isEmpty(afterFilterRegList)) {
            log.debug("yesterday once clock for signtime analyse fail afterFilterRegList empty time={}", System.currentTimeMillis());
            return yesterdayRegIdList;
        }
        afterFilterRegList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

        // 前一天排班
        Long yesterday = DateUtil.addDate(clockDate * 1000, -1);
        WaShiftDo yesterdayShiftDo = empShiftInfo.get(empId + "_" + yesterday);
        if (null == yesterdayShiftDo) {
            log.debug("yesterday once clock for signtime analyse fail shift empty time={}", System.currentTimeMillis());
            return yesterdayRegIdList;
        }

        List<WaShiftDo> shiftDoList = yesterdayShiftDo.doGetShiftDoList();
        List<Integer> shiftDefIds = shiftDoList.stream().map(WaShiftDo::getShiftDefId).collect(Collectors.toList());
        log.debug("yesterday once clock for signtime analyse yesterday={}, shift={}, time={}",
                yesterday, FastjsonUtil.toJsonStr(shiftDefIds), System.currentTimeMillis());

        if (shiftDoList.size() > 1) {// 一天排多个班
            WaShiftDef firstShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(yesterdayShiftDo.doGetFirstShiftDef());
            WaShiftDef lastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(yesterdayShiftDo.doGetLastShiftDef());

            if (null == firstShiftWorkTime.getOnDutyStartTime()
                    || null == firstShiftWorkTime.getOnDutyEndTime()
                    || null == lastShiftWorkTime.getOffDutyStartTime()
                    || null == lastShiftWorkTime.getOffDutyEndTime()) {
                log.debug("yesterday once clock for signtime analyse fail multi signtime empty shiftid in {}, time={}",
                        firstShiftWorkTime.getShiftDefId() + "," + lastShiftWorkTime.getShiftDefId(),
                        System.currentTimeMillis());
                return yesterdayRegIdList;
            }

            long lastOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(lastShiftWorkTime, lastShiftWorkTime.getDateType())
                    ? yesterday + (lastShiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                    : yesterday + lastShiftWorkTime.getOffDutyEndTime() * 60L;

            List<WaRegisterRecordDo> yesterdayRegList = afterFilterRegList.stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= lastOffDutyEndTime)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(yesterdayRegList)) {
                return yesterdayRegIdList;
            }

            for (WaShiftDo shiftDo : shiftDoList) {
                if (CollectionUtils.isEmpty(yesterdayRegList)) {
                    break;
                }
                WaShiftDef shiftDef = ObjectConverter.convert(shiftDo, WaShiftDef.class);
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
                boolean crossNightForSignOff = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType());
                if (!crossNightForSignOff) {
                    continue;
                }

                if (null == shiftWorkTime.getOnDutyStartTime() || null == shiftWorkTime.getOnDutyEndTime()
                        || null == shiftWorkTime.getOffDutyStartTime() || null == shiftWorkTime.getOffDutyEndTime()) {
                    log.debug("yesterday once clock for signtime analyse fail multi signtime empty shiftid={}, time={}",
                            shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                    continue;
                }

                long ondutyStartTime = yesterday + shiftWorkTime.doGetRealOnDutyStartTime() * 60L;
                long ondutyEndTime = yesterday + shiftWorkTime.doGetRealOnDutyEndTime() * 60L;
                long offDutyStartTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                        ? yesterday + (shiftWorkTime.getOffDutyStartTime() + 1440) * 60L
                        : yesterday + shiftWorkTime.getOffDutyStartTime() * 60L;
                long offDutyEndTime = yesterday + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L;

                // 当前班次内的打卡记录
                List<WaRegisterRecordDo> currentShiftRegList;
                if (lastShiftWorkTime.getShiftDefId().equals(shiftWorkTime.getShiftDefId())) {
                    currentShiftRegList = new ArrayList<>(yesterdayRegList);
                } else {
                    currentShiftRegList = yesterdayRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime)
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(currentShiftRegList)) {
                    continue;
                }

                // 当前班次内的所有班段
                List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
                boolean isMultiWork = multiWorkTimeList.size() > 1;

                for (WaRegisterRecordDo wrrd : currentShiftRegList) {
                    wrrd.setBelongDate(yesterday);
                    wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                    wrrd.setStartTime(shiftWorkTime.getStartTime());
                    wrrd.setEndTime(shiftWorkTime.getEndTime());
                    wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                    String normalDate = String.format("%s-%s;%s-%s",
                            DateUtil.convertMinuteToTime(shiftWorkTime.doGetOnDutyStartTimeForView()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOnDutyEndTime()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyStartTime()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyEndTime()));
                    wrrd.setNormalDate(normalDate);
                    boolean ifValid;
                    if (isMultiWork) {
                        ifValid = wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime;
                    } else {
                        ifValid = (wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= ondutyEndTime)
                                || (wrrd.getRegDateTime() >= offDutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime);
                    }
                    if (ifValid) {
                        wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                        wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                        wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                    }
                    updList.add(wrrd);
                    yesterdayRegIdList.add(wrrd.getRecordId());
                    yesterdayRegList.removeIf(it -> it.getRecordId().equals(wrrd.getRecordId()));
                }
            }
        } else {// 一天排一个班
            WaShiftDef shiftDef = ObjectConverter.convert(yesterdayShiftDo, WaShiftDef.class);
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

            if (null == shiftWorkTime.getOnDutyStartTime()
                    || null == shiftWorkTime.getOnDutyEndTime()
                    || null == shiftWorkTime.getOffDutyStartTime()
                    || null == shiftWorkTime.getOffDutyEndTime()) {
                log.debug("yesterday once clock for signtime analyse fail signtime empty shiftid={}, time={}",
                        shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                return yesterdayRegIdList;
            }

            long ondutyStartTime = yesterday + shiftWorkTime.doGetRealOnDutyStartTime() * 60;
            long ondutyEndTime = yesterday + shiftWorkTime.doGetRealOnDutyEndTime() * 60;
            long offDutyStartTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                    ? yesterday + (shiftWorkTime.getOffDutyStartTime() + 1440) * 60L
                    : yesterday + shiftWorkTime.getOffDutyStartTime() * 60L;
            long offDutyEndTime = yesterday + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L;

            // 筛选出归属于前一天班次的打卡数据
            List<WaRegisterRecordDo> yesterdayRegList = new ArrayList<>();
            Long maxOtEndTime = dataCacheDto.doGetMaxOtEndTime(empId, yesterday);// 当日加班最晚结束时间
            Integer clockTimeRange = WaShiftDo.ifEnableAnalyseClockByOt(shiftDef); // 取卡时间范围，单位小时
            Long maxLtEndTime = dataCacheDto.doGetMaxLtEndTime(empId, yesterday);
            boolean crossNight = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType());
            long shiftEndTime = crossNight ? clockDate + shiftWorkTime.getEndTime() * 60 : yesterday + shiftWorkTime.getEndTime() * 60;

            if (null != clockTimeRange && null == maxOtEndTime) {
                // 前一天是休息日且开启打卡按照加班时间分析且前一天无加班单时，则不分析
                return yesterdayRegIdList;
            } else if (null != clockTimeRange) {
                // 前一天是休息日且开启打卡按照加班时间分析且前一天有加班单时，则根据当日加班时间范围进行过滤
                long criticalTime = maxOtEndTime + (clockTimeRange * 60 * 60);
                yesterdayRegList = afterFilterRegList.stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= criticalTime).collect(Collectors.toList());
            } else {
                boolean continueFilter = maxLtEndTime == null || maxLtEndTime < shiftEndTime;
                if (null != maxLtEndTime) {
                    if (crossNight) {
                        if (maxLtEndTime >= shiftEndTime) {
                            yesterdayRegList = afterFilterRegList.stream()
                                    .filter(wrrd -> wrrd.getRegDateTime() <= shiftEndTime).collect(Collectors.toList());
                        }
                    } else {
                        if (maxLtEndTime >= shiftEndTime) {
                            return yesterdayRegIdList;
                        }
                    }
                }

                if (continueFilter) {
                    // 检查班次的下班打卡结束时间是否跨夜
                    boolean crossNightForSignOff = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType());
                    if (!crossNightForSignOff) {
                        return yesterdayRegIdList;
                    }
                    yesterdayRegList = afterFilterRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime)
                            .collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isEmpty(yesterdayRegList)) {
                return yesterdayRegIdList;
            }

            // 当前班次内的所有班段
            List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
            boolean isMultiWork = multiWorkTimeList.size() > 1;

            for (WaRegisterRecordDo wrrd : yesterdayRegList) {
                wrrd.setBelongDate(yesterday);
                wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                wrrd.setStartTime(shiftWorkTime.getStartTime());
                wrrd.setEndTime(shiftWorkTime.getEndTime());
                wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                String normalDate = String.format("%s-%s;%s-%s",
                        DateUtil.convertMinuteToTime(shiftWorkTime.doGetOnDutyStartTimeForView()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getOnDutyEndTime()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyStartTime()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyEndTime()));
                wrrd.setNormalDate(normalDate);
                boolean ifValid;
                if (isMultiWork) {
                    ifValid = wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime;
                } else {
                    ifValid = (wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= ondutyEndTime)
                            || (wrrd.getRegDateTime() >= offDutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime);
                }
                if (ifValid) {
                    wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                    wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                    wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                }
                updList.add(wrrd);
                yesterdayRegIdList.add(wrrd.getRecordId());
            }
        }
        return yesterdayRegIdList;
    }

    /**
     * 一次卡打卡分析（在班次打卡时段内存在打卡、补卡记录）：当日打卡分析
     *
     * @param updList
     * @param empShiftInfo
     * @param regList
     * @param empId
     * @param clockDate
     * @return 当日打卡记录ID
     */
    public List<Integer> analyseOnceClockForSignTimeRuleOfToday(List<WaRegisterRecordDo> updList,
                                                                Map<String, WaShiftDo> empShiftInfo,
                                                                List<WaRegisterRecordDo> regList,
                                                                Long empId,
                                                                Long clockDate,
                                                                ClockAnalyseDataCacheDto dataCacheDto) {
        List<Integer> todayRegIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regList)) {
            log.debug("clockDate once clock for signtime analyse fail regs empty time={}", System.currentTimeMillis());
            return todayRegIdList;
        }

        // 当天排班
        WaShiftDo clockDateShiftDo = empShiftInfo.get(empId + "_" + clockDate);
        if (null == clockDateShiftDo) {
            log.debug("clockDate once clock for signtime analyse fail shift empty time={}", System.currentTimeMillis());
            return todayRegIdList;
        }

        List<WaShiftDo> shiftDoList = clockDateShiftDo.doGetShiftDoList();
        List<Integer> shiftDefIds = shiftDoList.stream().map(WaShiftDo::getShiftDefId).collect(Collectors.toList());
        log.debug("clockDate once clock for signtime analyse clockDate={}, shift={}, time={}",
                clockDate, FastjsonUtil.toJsonStr(shiftDefIds), System.currentTimeMillis());

        Long nextDay = DateUtil.addDate(clockDate * 1000, 1);
        List<WaRegisterRecordDo> afterFilterRegList = filterByShiftOtTime(empShiftInfo, regList, empId, nextDay, dataCacheDto);
        if (CollectionUtils.isEmpty(afterFilterRegList)) {
            log.debug("clockDate once clock for signtime analyse fail afterFilterRegList empty time={}", System.currentTimeMillis());
            return todayRegIdList;
        }
        afterFilterRegList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

        if (shiftDoList.size() > 1) {// 一天排多个班
            WaShiftDef lastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(clockDateShiftDo.doGetLastShiftDef());

            List<WaRegisterRecordDo> todayRegList = afterFilterRegList;
            // 查询次日最早上班打卡时间
            long nextDayFirstOnDutyStartTime = getNextDatFirstOnDutyStartTime(empId, clockDate, empShiftInfo);
            if (nextDayFirstOnDutyStartTime > 0) {
                long lastOffDutyEndTime;
                if (null != lastShiftWorkTime.getOffDutyEndTime()) {
                    lastOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(lastShiftWorkTime, lastShiftWorkTime.getDateType())
                            ? clockDate + (lastShiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                            : clockDate + lastShiftWorkTime.getOffDutyEndTime() * 60L;
                } else {
                    lastOffDutyEndTime = clockDate + 86399;
                }
                todayRegList = afterFilterRegList.stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= lastOffDutyEndTime
                                || wrrd.getRegDateTime() < nextDayFirstOnDutyStartTime)
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(todayRegList)) {
                log.debug("clockDate once clock for multi signtime analyse fail todayRegList empty time={}", System.currentTimeMillis());
                return todayRegIdList;
            }

            for (WaShiftDo shiftDo : shiftDoList) {
                if (CollectionUtils.isEmpty(todayRegList)) {
                    break;
                }
                WaShiftDef shiftDef = ObjectConverter.convert(shiftDo, WaShiftDef.class);
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

                if (null == shiftWorkTime.getOnDutyStartTime()
                        || null == shiftWorkTime.getOnDutyEndTime()
                        || null == shiftWorkTime.getOffDutyStartTime()
                        || null == shiftWorkTime.getOffDutyEndTime()) {
                    log.debug("clockDate once clock for signtime analyse fail multi signtime empty shiftid={}, time={}",
                            shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                    continue;
                }

                long ondutyStartTime = clockDate + shiftWorkTime.doGetRealOnDutyStartTime() * 60;
                long ondutyEndTime = clockDate + shiftWorkTime.doGetRealOnDutyEndTime() * 60;
                long offDutyStartTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                        ? clockDate + (shiftWorkTime.getOffDutyStartTime() + 1440) * 60L
                        : clockDate + shiftWorkTime.getOffDutyStartTime() * 60L;
                long offDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())
                        ? clockDate + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                        : clockDate + shiftWorkTime.getOffDutyEndTime() * 60L;

                // 当前班次内的打卡记录
                List<WaRegisterRecordDo> currentShiftRegList;
                if (lastShiftWorkTime.getShiftDefId().equals(shiftWorkTime.getShiftDefId())) {
                    currentShiftRegList = new ArrayList<>(todayRegList);
                } else {
                    currentShiftRegList = todayRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime)
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(currentShiftRegList)) {
                    continue;
                }

                // 当前班次内的所有班段
                List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
                boolean isMultiWork = multiWorkTimeList.size() > 1;

                for (WaRegisterRecordDo wrrd : currentShiftRegList) {
                    wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                    wrrd.setStartTime(shiftWorkTime.getStartTime());
                    wrrd.setEndTime(shiftWorkTime.getEndTime());
                    wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                    String normalDate = String.format("%s-%s;%s-%s",
                            DateUtil.convertMinuteToTime(shiftWorkTime.doGetOnDutyStartTimeForView()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOnDutyEndTime()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyStartTime()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyEndTime()));
                    wrrd.setNormalDate(normalDate);
                    boolean ifValid;
                    if (isMultiWork) {
                        ifValid = wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime;
                    } else {
                        ifValid = (wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= ondutyEndTime)
                                || (wrrd.getRegDateTime() >= offDutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime);
                    }
                    if (ifValid) {
                        wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                        wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                        wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                    }
                    updList.add(wrrd);
                    todayRegIdList.add(wrrd.getRecordId());
                    todayRegList.removeIf(it -> it.getRecordId().equals(wrrd.getRecordId()));
                }
            }

            if (CollectionUtils.isNotEmpty(todayRegList)) {
                List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(todayRegIdList)
                        ? todayRegList.stream().filter(it -> !todayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                        : todayRegList;
                if (CollectionUtils.isNotEmpty(unanalyzedRegList)) {
                    updList.addAll(unanalyzedRegList);
                    List<Integer> unanalyzedRegIdList = unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList());
                    todayRegIdList.addAll(unanalyzedRegIdList);
                }
            }
        } else {
            WaShiftDef shiftDef = ObjectConverter.convert(clockDateShiftDo, WaShiftDef.class);
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

            if (null == shiftWorkTime.getOnDutyStartTime()
                    || null == shiftWorkTime.getOnDutyEndTime()
                    || null == shiftWorkTime.getOffDutyStartTime()
                    || null == shiftWorkTime.getOffDutyEndTime()) {
                log.debug("clockDate once clock for signtime analyse fail signtime empty shiftid={}, time={}", shiftWorkTime.getShiftDefId(),
                        System.currentTimeMillis());
                return todayRegIdList;
            }

            // 最晚班段内的上下班打卡时间
            long ondutyStartTime = clockDate + shiftWorkTime.doGetRealOnDutyStartTime() * 60L;
            long ondutyEndTime = clockDate + shiftWorkTime.doGetRealOnDutyEndTime() * 60L;
            long offDutyStartTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                    ? clockDate + (shiftWorkTime.getOffDutyStartTime() + 1440) * 60L
                    : clockDate + shiftWorkTime.getOffDutyStartTime() * 60L;
            long offDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())
                    ? clockDate + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                    : clockDate + shiftWorkTime.getOffDutyEndTime() * 60L;

            log.debug("clockDate once clock for signtime analyse shiftWorkTime={} , time={}", FastjsonUtil.toJsonStr(shiftWorkTime),
                    System.currentTimeMillis());

            // 筛选出归属于当日的打卡数据
            List<WaRegisterRecordDo> todayRegList = afterFilterRegList;

            Long maxOtEndTime;// 当日加班最晚结束时间
            Integer clockTimeRange; // 取卡时间范围，单位小时
            if (null != (clockTimeRange = WaShiftDo.ifEnableAnalyseClockByOt(shiftDef))
                    && null != (maxOtEndTime = dataCacheDto.doGetMaxOtEndTime(empId, clockDate))) {
                // 根据当日加班时间范围进行过滤
                long criticalTime = maxOtEndTime + (clockTimeRange * 60 * 60);
                todayRegList = afterFilterRegList.stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= criticalTime).collect(Collectors.toList());
            } else {
                // 查询次日最早上班打卡时间
                long nextDayFirstOnDutyStartTime = getNextDatFirstOnDutyStartTime(empId, clockDate, empShiftInfo);
                if (nextDayFirstOnDutyStartTime > 0) {
                    todayRegList = afterFilterRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime
                                    || wrrd.getRegDateTime() < nextDayFirstOnDutyStartTime)
                            .collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isEmpty(todayRegList)) {
                log.debug("clockDate once clock for signtime analyse fail todayRegList empty time={}", System.currentTimeMillis());
                return todayRegIdList;
            }

            // 当前班次内的所有班段
            List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
            boolean isMultiWork = multiWorkTimeList.size() > 1;

            for (WaRegisterRecordDo wrrd : todayRegList) {
                wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                wrrd.setStartTime(shiftWorkTime.getStartTime());
                wrrd.setEndTime(shiftWorkTime.getEndTime());
                wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                String normalDate = String.format("%s-%s;%s-%s",
                        DateUtil.convertMinuteToTime(shiftWorkTime.doGetOnDutyStartTimeForView()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getOnDutyEndTime()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyStartTime()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyEndTime()));
                wrrd.setNormalDate(normalDate);
                boolean ifValid;
                if (isMultiWork) {
                    ifValid = wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime;
                } else {
                    ifValid = (wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= ondutyEndTime)
                            || (wrrd.getRegDateTime() >= offDutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime);
                }
                if (ifValid) {
                    wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                    wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                    wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                }
                updList.add(wrrd);
                todayRegIdList.add(wrrd.getRecordId());
            }

            if (CollectionUtils.isNotEmpty(todayRegList)) {
                List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(todayRegIdList)
                        ? todayRegList.stream().filter(it -> !todayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                        : todayRegList;
                if (CollectionUtils.isNotEmpty(unanalyzedRegList)) {
                    updList.addAll(unanalyzedRegList);
                    List<Integer> unanalyzedRegIdList = unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList());
                    todayRegIdList.addAll(unanalyzedRegIdList);
                }
            }
        }
        return todayRegIdList;
    }

    /**
     * 一次卡打卡分析（在班次打卡时段内存在打卡、补卡记录）：次日打卡分析
     *
     * @param updList
     * @param empShiftInfo
     * @param regList
     * @param empId
     * @param clockDate
     * @return 次日打卡记录ID
     */
    public List<Integer> analyseOnceClockForSignTimeRuleOfNextDay(List<WaRegisterRecordDo> updList,
                                                                  Map<String, WaShiftDo> empShiftInfo,
                                                                  List<WaRegisterRecordDo> regList,
                                                                  Long empId,
                                                                  Long clockDate) {
        List<Integer> nextDayRegIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regList)) {
            log.debug("clockDate once clock for NextDay signtime analyse fail regs empty, empId={}, clockDate={}, time={}", empId, clockDate, System.currentTimeMillis());
            return nextDayRegIdList;
        }
        regList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

        List<WaRegisterRecordDo> nextDayRegList = regList;

        // 次日排班
        Long nextDay = DateUtil.addDate(clockDate * 1000, 1);
        WaShiftDo nextDayShiftDo = empShiftInfo.get(empId + "_" + nextDay);
        if (null == nextDayShiftDo) {
            log.debug("clockDate once clock for NextDay signtime analyse fail shift empty  empId={}, nextDay={}, time={}", empId, nextDay, System.currentTimeMillis());
            return nextDayRegIdList;
        }

        List<WaShiftDo> shiftDoList = nextDayShiftDo.doGetShiftDoList();
        List<Integer> shiftDefIds = shiftDoList.stream().map(WaShiftDo::getShiftDefId).collect(Collectors.toList());
        log.debug("clockDate once clock for NextDay signtime analyse clockDate={}, shift={}, time={}",
                nextDay, FastjsonUtil.toJsonStr(shiftDefIds), System.currentTimeMillis());

        if (shiftDoList.size() > 1) {// 一天排多个班
            WaShiftDef lastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(nextDayShiftDo.doGetLastShiftDef());

            for (WaShiftDo shiftDo : shiftDoList) {
                if (CollectionUtils.isEmpty(nextDayRegList)) {
                    break;
                }
                WaShiftDef shiftDef = ObjectConverter.convert(shiftDo, WaShiftDef.class);
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

                if (null == shiftWorkTime.getOnDutyStartTime()
                        || null == shiftWorkTime.getOnDutyEndTime()
                        || null == shiftWorkTime.getOffDutyStartTime()
                        || null == shiftWorkTime.getOffDutyEndTime()) {
                    log.debug("clockDate once clock for NextDay signtime analyse fail multi signtime empty shiftid={}, time={}",
                            shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                    continue;
                }

                long ondutyStartTime = nextDay + shiftWorkTime.doGetRealOnDutyStartTime() * 60;
                long ondutyEndTime = nextDay + shiftWorkTime.doGetRealOnDutyEndTime() * 60;
                long offDutyStartTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                        ? nextDay + (shiftWorkTime.getOffDutyStartTime() + 1440) * 60L
                        : nextDay + shiftWorkTime.getOffDutyStartTime() * 60L;
                long offDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())
                        ? nextDay + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                        : nextDay + shiftWorkTime.getOffDutyEndTime() * 60L;

                // 当前班次内的打卡记录
                List<WaRegisterRecordDo> currentShiftRegList;
                if (lastShiftWorkTime.getShiftDefId().equals(shiftWorkTime.getShiftDefId())) {
                    currentShiftRegList = new ArrayList<>(nextDayRegList);
                } else {
                    currentShiftRegList = nextDayRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime)
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(currentShiftRegList)) {
                    continue;
                }

                // 当前班次内的所有班段
                List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
                boolean isMultiWork = multiWorkTimeList.size() > 1;

                for (WaRegisterRecordDo wrrd : currentShiftRegList) {
                    wrrd.setBelongDate(nextDay);
                    wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                    wrrd.setStartTime(shiftWorkTime.getStartTime());
                    wrrd.setEndTime(shiftWorkTime.getEndTime());
                    wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                    String normalDate = String.format("%s-%s;%s-%s",
                            DateUtil.convertMinuteToTime(shiftWorkTime.doGetOnDutyStartTimeForView()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOnDutyEndTime()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyStartTime()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyEndTime()));
                    wrrd.setNormalDate(normalDate);
                    boolean ifValid;
                    if (isMultiWork) {
                        ifValid = wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime;
                    } else {
                        ifValid = (wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= ondutyEndTime)
                                || (wrrd.getRegDateTime() >= offDutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime);
                    }
                    if (ifValid) {
                        wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                        wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                        wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                    }
                    updList.add(wrrd);
                    nextDayRegIdList.add(wrrd.getRecordId());
                    nextDayRegList.removeIf(it -> it.getRecordId().equals(wrrd.getRecordId()));
                }
            }

            if (CollectionUtils.isNotEmpty(nextDayRegList)) {
                List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(nextDayRegIdList)
                        ? nextDayRegList.stream().filter(it -> !nextDayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                        : nextDayRegList;
                if (CollectionUtils.isNotEmpty(unanalyzedRegList)) {
                    unanalyzedRegList.forEach(it -> it.setBelongDate(nextDay));
                    updList.addAll(unanalyzedRegList);
                    List<Integer> unanalyzedRegIdList = unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList());
                    nextDayRegIdList.addAll(unanalyzedRegIdList);
                }
            }
        } else {
            WaShiftDef shiftDef = ObjectConverter.convert(nextDayShiftDo, WaShiftDef.class);
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

            if (null == shiftWorkTime.getOnDutyStartTime()
                    || null == shiftWorkTime.getOnDutyEndTime()
                    || null == shiftWorkTime.getOffDutyStartTime()
                    || null == shiftWorkTime.getOffDutyEndTime()) {
                log.debug("clockDate once clock for NextDay signtime analyse fail signtime empty shiftid={}, time={}", shiftWorkTime.getShiftDefId(),
                        System.currentTimeMillis());

                nextDayRegList.forEach(it -> it.setBelongDate(nextDay));
                updList.addAll(nextDayRegList);
                List<Integer> unanalyzedRegIdList = nextDayRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList());
                nextDayRegIdList.addAll(unanalyzedRegIdList);
                return nextDayRegIdList;
            }

            // 最晚班段内的上下班打卡时间
            long ondutyStartTime = nextDay + shiftWorkTime.doGetRealOnDutyStartTime() * 60L;
            long ondutyEndTime = nextDay + shiftWorkTime.doGetRealOnDutyEndTime() * 60L;
            long offDutyStartTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                    ? nextDay + (shiftWorkTime.getOffDutyStartTime() + 1440) * 60L
                    : nextDay + shiftWorkTime.getOffDutyStartTime() * 60L;
            long offDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())
                    ? nextDay + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                    : nextDay + shiftWorkTime.getOffDutyEndTime() * 60L;

            log.debug("clockDate once clock for NextDay signtime analyse shiftWorkTime={} , time={}", FastjsonUtil.toJsonStr(shiftWorkTime),
                    System.currentTimeMillis());

            // 当前班次内的所有班段
            List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
            boolean isMultiWork = multiWorkTimeList.size() > 1;

            for (WaRegisterRecordDo wrrd : nextDayRegList) {
                wrrd.setBelongDate(nextDay);
                wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                wrrd.setStartTime(shiftWorkTime.getStartTime());
                wrrd.setEndTime(shiftWorkTime.getEndTime());
                wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                String normalDate = String.format("%s-%s;%s-%s",
                        DateUtil.convertMinuteToTime(shiftWorkTime.doGetOnDutyStartTimeForView()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getOnDutyEndTime()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyStartTime()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getOffDutyEndTime()));
                wrrd.setNormalDate(normalDate);
                boolean ifValid;
                if (isMultiWork) {
                    ifValid = wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime;
                } else {
                    ifValid = (wrrd.getRegDateTime() >= ondutyStartTime && wrrd.getRegDateTime() <= ondutyEndTime)
                            || (wrrd.getRegDateTime() >= offDutyStartTime && wrrd.getRegDateTime() <= offDutyEndTime);
                }
                if (ifValid) {
                    wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                    wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                    wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                }
                updList.add(wrrd);
                nextDayRegIdList.add(wrrd.getRecordId());
            }
        }
        return nextDayRegIdList;
    }

    /**
     * 一次卡打卡分析（在班次打卡时段内存在打卡、补卡记录）
     *
     * @param empShiftInfo
     * @param regList
     * @param empId
     * @param clockDate
     * @return
     */
    public List<WaRegisterRecordDo> analyseOnceClockForSignTimeRule(Map<String, WaShiftDo> empShiftInfo,
                                                                    List<WaRegisterRecordDo> regList,
                                                                    Long empId,
                                                                    Long clockDate,
                                                                    ClockAnalyseDataCacheDto dataCacheDto) {
        List<WaRegisterRecordDo> updList = new ArrayList<>();

        // 前一日打卡分析
        List<Integer> yesterdayRegIdList = analyseOnceClockForSignTimeRuleOfYesterday(updList, empShiftInfo, regList
                , empId, clockDate, dataCacheDto);
        List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(yesterdayRegIdList)
                ? regList.stream().filter(it -> !yesterdayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : regList;

        // 当日打卡分析
        List<Integer> todayRegIdList = analyseOnceClockForSignTimeRuleOfToday(updList, empShiftInfo, unanalyzedRegList, empId,
                clockDate, dataCacheDto);
        unanalyzedRegList = CollectionUtils.isNotEmpty(todayRegIdList)
                ? unanalyzedRegList.stream().filter(it -> !todayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : unanalyzedRegList;

        // 次日打卡分析
        List<Integer> nextDayRegIdList = analyseOnceClockForSignTimeRuleOfNextDay(updList, empShiftInfo, unanalyzedRegList, empId, clockDate);
        unanalyzedRegList = CollectionUtils.isNotEmpty(nextDayRegIdList)
                ? unanalyzedRegList.stream().filter(it -> !nextDayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : unanalyzedRegList;
        List<Integer> unanalyzedRegIdList = CollectionUtils.isNotEmpty(unanalyzedRegList)
                ? unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList())
                : Lists.newArrayList();
        log.debug("analyseOnceClockForSignTimeRule.unanalyzedRegList={}", FastjsonUtil.toJsonStr(unanalyzedRegIdList));
        return updList;
    }

    /**
     * 一次卡打卡分析（在班次上班时间-下班时间内存在打卡、补卡记录）：前一日打卡分析
     *
     * @param updList
     * @param regList
     * @param empShiftInfo
     * @param empId
     * @param clockDate
     * @return 前一日打卡记录ID
     */
    public List<Integer> analyseOnceClockForWorkTimeRuleOfYesterday(List<WaRegisterRecordDo> updList,
                                                                    List<WaRegisterRecordDo> regList,
                                                                    Map<String, WaShiftDo> empShiftInfo,
                                                                    Long empId,
                                                                    Long clockDate,
                                                                    ClockAnalyseDataCacheDto dataCacheDto) {
        // 当前一天班次跨夜或者打卡区间跨夜时，判断是否为前一天的考勤打卡
        List<Integer> yesterdayRegIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regList)) {
            log.debug("yesterday once clock for worktime analyse fail regs empty time={}", System.currentTimeMillis());
            return yesterdayRegIdList;
        }

        List<WaRegisterRecordDo> afterFilterRegList = filterByShiftOtTime(empShiftInfo, regList, empId, clockDate, dataCacheDto);
        if (CollectionUtils.isEmpty(afterFilterRegList)) {
            log.debug("yesterday once clock for worktime analyse fail afterFilterRegList empty time={}", System.currentTimeMillis());
            return yesterdayRegIdList;
        }
        afterFilterRegList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

        Long yesterday = DateUtil.addDate(clockDate * 1000, -1);
        WaShiftDo yesterdayShiftDo = empShiftInfo.get(empId + "_" + yesterday);
        if (null == yesterdayShiftDo) {
            log.debug("yesterday once clock for worktime analyse fail shift empty time={}", System.currentTimeMillis());
            return yesterdayRegIdList;
        }

        List<WaShiftDo> yesterdayShiftDoList = yesterdayShiftDo.doGetShiftDoList();
        List<Integer> shiftDefIds = yesterdayShiftDoList.stream().map(WaShiftDo::getShiftDefId).collect(Collectors.toList());
        log.debug("yesterday once clock for worktime analyse yesterday={}, shift={}, time={}",
                yesterday, FastjsonUtil.toJsonStr(shiftDefIds), System.currentTimeMillis());

        if (yesterdayShiftDoList.size() > 1) {// 一天排多个班
            WaShiftDef firstShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(yesterdayShiftDo.doGetFirstShiftDef());
            WaShiftDef lastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(yesterdayShiftDo.doGetLastShiftDef());

            if (null == lastShiftWorkTime.getEndTime()) {
                log.debug("yesterday once clock for worktime analyse fail multi shift worktime empty shiftid in {}, time={}",
                        firstShiftWorkTime.getShiftDefId() + "," + lastShiftWorkTime.getShiftDefId(),
                        System.currentTimeMillis());
                return yesterdayRegIdList;
            }

            long lastShiftEndTime = CdWaShiftUtil.checkCrossNightV2(lastShiftWorkTime, lastShiftWorkTime.getDateType())
                    ? clockDate + lastShiftWorkTime.getEndTime() * 60L
                    : yesterday + lastShiftWorkTime.getEndTime() * 60L;

            List<WaRegisterRecordDo> yesterdayRegList = afterFilterRegList.stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= lastShiftEndTime)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(yesterdayRegList)) {
                return yesterdayRegIdList;
            }

            for (WaShiftDo shiftDo : yesterdayShiftDoList) {
                if (CollectionUtils.isEmpty(yesterdayRegList)) {
                    break;
                }
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(ObjectConverter.convert(shiftDo, WaShiftDef.class));
                if (null == shiftWorkTime.getStartTime() || null == shiftWorkTime.getEndTime()) {
                    log.debug("yesterday once clock for worktime analyse fail multi shift worktime empty shiftid={}, time={}",
                            shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                    continue;
                }

                long shiftStartTime = yesterday + shiftWorkTime.doGetRealStartTime() * 60;
                long shiftEndTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                        ? clockDate + shiftWorkTime.getEndTime() * 60
                        : yesterday + shiftWorkTime.getEndTime() * 60;

                // 当前班次内的打卡记录
                List<WaRegisterRecordDo> currentShiftRegList;
                if (lastShiftWorkTime.getShiftDefId().equals(shiftWorkTime.getShiftDefId())) {
                    currentShiftRegList = new ArrayList<>(yesterdayRegList);
                } else {
                    currentShiftRegList = yesterdayRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= shiftEndTime)
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(currentShiftRegList)) {
                    continue;
                }
                for (WaRegisterRecordDo wrrd : currentShiftRegList) {
                    wrrd.setBelongDate(yesterday);
                    wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                    wrrd.setStartTime(shiftWorkTime.getStartTime());
                    wrrd.setEndTime(shiftWorkTime.getEndTime());
                    wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                    wrrd.setNormalDate(String.format("%s-%s",
                            DateUtil.convertMinuteToTime(shiftWorkTime.getStartTime()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getEndTime())));
                    if (wrrd.getRegDateTime() >= shiftStartTime && wrrd.getRegDateTime() <= shiftEndTime) {
                        wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                        wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                        wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                    }
                    updList.add(wrrd);
                    yesterdayRegList.removeIf(it -> it.getRecordId().equals(wrrd.getRecordId()));
                    yesterdayRegIdList.add(wrrd.getRecordId());
                }
            }
        } else {// 一天排一个班
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(ObjectConverter.convert(yesterdayShiftDo, WaShiftDef.class));

            if (null == shiftWorkTime.getStartTime() || null == shiftWorkTime.getEndTime()) {
                log.debug("yesterday once clock for worktime analyse fail shift worktime empty shiftid={}, time={}",
                        shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                return yesterdayRegIdList;
            }

            long shiftStartTime = yesterday + shiftWorkTime.doGetRealStartTime() * 60;
            long shiftEndTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                    ? clockDate + shiftWorkTime.getEndTime() * 60
                    : yesterday + shiftWorkTime.getEndTime() * 60;

            // 筛选出归属于前一天班次的打卡数据
            List<WaRegisterRecordDo> yesterdayRegList;
            Long maxOtEndTime = dataCacheDto.doGetMaxOtEndTime(empId, yesterday);// 当日加班最晚结束时间
            Integer clockTimeRange = WaShiftDo.ifEnableAnalyseClockByOt(shiftWorkTime); // 取卡时间范围，单位小时

            if (null != clockTimeRange && null == maxOtEndTime) {
                // 前一天是休息日且开启打卡按照加班时间分析且前一天无加班单时，则不分析
                return yesterdayRegIdList;
            } else if (null != clockTimeRange) {
                // 前一天是休息日且开启打卡按照加班时间分析且前一天有加班单时，则根据当日加班时间范围进行过滤
                long criticalTime = maxOtEndTime + (clockTimeRange * 60 * 60);
                yesterdayRegList = afterFilterRegList.stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= criticalTime).collect(Collectors.toList());
            } else {
                boolean crossNight = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType());
                if (!crossNight) {
                    return yesterdayRegIdList;
                }
                yesterdayRegList = afterFilterRegList.stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= shiftEndTime)
                        .collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(yesterdayRegList)) {
                return yesterdayRegIdList;
            }

            for (WaRegisterRecordDo wrrd : yesterdayRegList) {
                wrrd.setBelongDate(yesterday);
                wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                wrrd.setStartTime(shiftWorkTime.getStartTime());
                wrrd.setEndTime(shiftWorkTime.getEndTime());
                wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                wrrd.setNormalDate(String.format("%s-%s",
                        DateUtil.convertMinuteToTime(shiftWorkTime.getStartTime()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getEndTime())));
                if (wrrd.getRegDateTime() >= shiftStartTime && wrrd.getRegDateTime() <= shiftEndTime) {
                    wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                    wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                    wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                }
                updList.add(wrrd);
                yesterdayRegIdList.add(wrrd.getRecordId());
            }
        }
        return yesterdayRegIdList;
    }

    /**
     * 查询次日最早上班打卡时间
     *
     * @param empId
     * @param clockDate
     * @param empShiftInfo
     * @return
     */
    private long getNextDatFirstOnDutyStartTime(Long empId, Long clockDate, Map<String, WaShiftDo> empShiftInfo) {
        Long nextDay = DateUtil.addDate(clockDate * 1000, 1);
        WaShiftDo nextDayShiftDo = empShiftInfo.get(empId + "_" + nextDay);
        if (null == nextDayShiftDo) {
            return 0L;
        }
        WaShiftDef firstShiftDef = nextDayShiftDo.doGetFirstShiftDef();
        List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(firstShiftDef);
        multiWorkTimeList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime));
        MultiWorkTimeBaseDto firstWorkTimeDto = multiWorkTimeList.get(0);
        if (null == firstWorkTimeDto.getOnDutyStartTime()) {
            return 0L;
        }
        long nextDayFirstOnDutyStartTime = nextDay + firstWorkTimeDto.doGetRealOnDutyStartTime() * 60;
        log.debug("getNextDatFirstOnDutyStartTime Params:{}, Result:{}", String.format("%s_%s", empId, clockDate), nextDayFirstOnDutyStartTime);
        return nextDayFirstOnDutyStartTime;
    }

    /**
     * 一次卡打卡分析（在班次上班时间-下班时间内存在打卡、补卡记录）：当日打卡分析
     *
     * @param updList
     * @param regList
     * @param empShiftInfo
     * @param empId
     * @param clockDate
     * @return 当日打卡记录ID
     */
    public List<Integer> analyseOnceClockForWorkTimeRuleOfToday(List<WaRegisterRecordDo> updList,
                                                                List<WaRegisterRecordDo> regList,
                                                                Map<String, WaShiftDo> empShiftInfo,
                                                                Long empId,
                                                                Long clockDate,
                                                                ClockAnalyseDataCacheDto dataCacheDto) {
        List<Integer> todayRegIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regList)) {
            log.debug("clockDate once clock for worktime analyse fail regs empty time={}", System.currentTimeMillis());
            return todayRegIdList;
        }

        // 查询排班
        WaShiftDo clockDateShiftDo = empShiftInfo.get(empId + "_" + clockDate);
        if (null == clockDateShiftDo) {
            log.debug("clockDate once clock for worktime analyse fail shift empty time={}", System.currentTimeMillis());
            return todayRegIdList;
        }
        List<WaShiftDo> shiftDoList = clockDateShiftDo.doGetShiftDoList();
        List<Integer> shiftDefIds = shiftDoList.stream().map(WaShiftDo::getShiftDefId).collect(Collectors.toList());
        log.debug("clockDate once clock for worktime analyse clockDate={}, shift={}, time={}",
                clockDate, FastjsonUtil.toJsonStr(shiftDefIds), System.currentTimeMillis());

        Long nextDay = DateUtil.addDate(clockDate * 1000, 1);
        List<WaRegisterRecordDo> afterFilterRegList = filterByShiftOtTime(empShiftInfo, regList, empId, nextDay, dataCacheDto);
        if (CollectionUtils.isEmpty(afterFilterRegList)) {
            log.debug("clockDate once clock for worktime analyse fail afterFilterRegList empty time={}", System.currentTimeMillis());
            return todayRegIdList;
        }
        afterFilterRegList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

        if (shiftDoList.size() > 1) {// 一天排多个班
            WaShiftDef lastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(clockDateShiftDo.doGetLastShiftDef());

            List<WaRegisterRecordDo> todayRegList = afterFilterRegList;

            // 查询次日最早上班打卡时间
            long nextDayFirstOnDutyStartTime = getNextDatFirstOnDutyStartTime(empId, clockDate, empShiftInfo);
            if (nextDayFirstOnDutyStartTime > 0) {
                long lastOffDutyEndTime;
                if (null != lastShiftWorkTime.getOffDutyEndTime()) {
                    lastOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(lastShiftWorkTime, lastShiftWorkTime.getDateType())
                            ? clockDate + (lastShiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                            : clockDate + lastShiftWorkTime.getOffDutyEndTime() * 60L;
                } else {
                    lastOffDutyEndTime = clockDate + 86399;
                }

                todayRegList = afterFilterRegList.stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= lastOffDutyEndTime
                                || wrrd.getRegDateTime() < nextDayFirstOnDutyStartTime)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(todayRegList)) {
                    log.debug("clockDate once clock for multi worktime analyse fail todayRegList empty empId={}, clockDate={}, shiftid={}, lastOffDutyEndTime={}, nextDayFirstOnDutyStartTime={}, time={}",
                            empId, clockDate, lastShiftWorkTime.getShiftDefId(), lastOffDutyEndTime, nextDayFirstOnDutyStartTime, System.currentTimeMillis());
                    return todayRegIdList;
                }
            }

            for (WaShiftDo shiftDo : shiftDoList) {
                if (CollectionUtils.isEmpty(todayRegList)) {
                    break;
                }
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(ObjectConverter.convert(shiftDo, WaShiftDef.class));
                if (null == shiftWorkTime.getStartTime() || null == shiftWorkTime.getEndTime()) {
                    log.debug("clockDate once clock for worktime analyse fail multi shift worktime empty shiftid={},time={}",
                            shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                    continue;
                }

                long shiftStartTime = clockDate + shiftWorkTime.getStartTime() * 60L;
                long shiftEndTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                        ? clockDate + (shiftWorkTime.getEndTime() + 1440) * 60L
                        : clockDate + shiftWorkTime.getEndTime() * 60L;

                // 当前班次内的打卡记录
                List<WaRegisterRecordDo> currentShiftRegList;
                if (lastShiftWorkTime.getShiftDefId().equals(shiftWorkTime.getShiftDefId())) {
                    currentShiftRegList = new ArrayList<>(todayRegList);
                } else {
                    currentShiftRegList = todayRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= shiftEndTime)
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(currentShiftRegList)) {
                    continue;
                }
                for (WaRegisterRecordDo wrrd : currentShiftRegList) {
                    wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                    wrrd.setStartTime(shiftWorkTime.getStartTime());
                    wrrd.setEndTime(shiftWorkTime.getEndTime());
                    wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                    wrrd.setNormalDate(String.format("%s-%s",
                            DateUtil.convertMinuteToTime(shiftWorkTime.getStartTime()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getEndTime())));
                    if (wrrd.getRegDateTime() >= shiftStartTime && wrrd.getRegDateTime() <= shiftEndTime) {
                        wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                        wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                        wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                    }
                    updList.add(wrrd);
                    todayRegIdList.add(wrrd.getRecordId());
                    todayRegList.removeIf(it -> it.getRecordId().equals(wrrd.getRecordId()));
                }
            }

            if (CollectionUtils.isNotEmpty(todayRegList)) {
                List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(todayRegIdList)
                        ? todayRegList.stream().filter(it -> !todayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                        : todayRegList;
                if (CollectionUtils.isNotEmpty(unanalyzedRegList)) {
                    updList.addAll(unanalyzedRegList);
                    List<Integer> unanalyzedRegIdList = unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList());
                    todayRegIdList.addAll(unanalyzedRegIdList);
                }
            }
        } else {// 一天排一个班
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(ObjectConverter.convert(clockDateShiftDo, WaShiftDef.class));
            if (null == shiftWorkTime.getStartTime() || null == shiftWorkTime.getEndTime()) {
                log.debug("clockDate once clock for worktime analyse fail shift worktime empty shiftid={},time={}",
                        shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                return todayRegIdList;
            }

            long shiftStartTime = clockDate + shiftWorkTime.getStartTime() * 60L;
            long shiftEndTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                    ? clockDate + (shiftWorkTime.getEndTime() + 1440) * 60L
                    : clockDate + shiftWorkTime.getEndTime() * 60L;

            // 筛选出归属于当日的打卡数据
            List<WaRegisterRecordDo> todayRegList = afterFilterRegList;
            Long maxOtEndTime;// 当日加班最晚结束时间
            Integer clockTimeRange; // 取卡时间范围，单位小时
            if (null != (clockTimeRange = WaShiftDo.ifEnableAnalyseClockByOt(shiftWorkTime))
                    && null != (maxOtEndTime = dataCacheDto.doGetMaxOtEndTime(empId, clockDate))) {
                // 根据当日加班时间范围进行过滤
                long criticalTime = maxOtEndTime + (clockTimeRange * 60 * 60);
                todayRegList = afterFilterRegList.stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= criticalTime).collect(Collectors.toList());
            } else {
                // 查询次日最早上班打卡时间
                long nextDayFirstOnDutyStartTime = getNextDatFirstOnDutyStartTime(empId, clockDate, empShiftInfo);
                if (nextDayFirstOnDutyStartTime > 0) {
                    long lastOffDutyEndTime;
                    if (null != shiftWorkTime.getOffDutyEndTime()) {
                        lastOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())
                                ? clockDate + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                                : clockDate + shiftWorkTime.getOffDutyEndTime() * 60L;
                    } else {
                        lastOffDutyEndTime = clockDate + 86399;
                    }

                    todayRegList = afterFilterRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= lastOffDutyEndTime
                                    || wrrd.getRegDateTime() < nextDayFirstOnDutyStartTime)
                            .collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isEmpty(todayRegList)) {
                log.debug("clockDate once clock for worktime analyse fail todayRegList empty empId={}, clockDate={}, shiftid={}, time={}",
                        empId, clockDate, shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                return todayRegIdList;
            }

            for (WaRegisterRecordDo wrrd : todayRegList) {
                wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                wrrd.setStartTime(shiftWorkTime.getStartTime());
                wrrd.setEndTime(shiftWorkTime.getEndTime());
                wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                wrrd.setNormalDate(String.format("%s-%s",
                        DateUtil.convertMinuteToTime(shiftWorkTime.getStartTime()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getEndTime())));
                if (wrrd.getRegDateTime() >= shiftStartTime && wrrd.getRegDateTime() <= shiftEndTime) {
                    wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                    wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                    wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                }
                updList.add(wrrd);
                todayRegIdList.add(wrrd.getRecordId());
            }

            if (CollectionUtils.isNotEmpty(todayRegList)) {
                List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(todayRegIdList)
                        ? todayRegList.stream().filter(it -> !todayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                        : todayRegList;
                if (CollectionUtils.isNotEmpty(unanalyzedRegList)) {
                    updList.addAll(unanalyzedRegList);
                    List<Integer> unanalyzedRegIdList = unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList());
                    todayRegIdList.addAll(unanalyzedRegIdList);
                }
            }
        }
        return todayRegIdList;
    }

    /**
     * 一次卡打卡分析（在班次上班时间-下班时间内存在打卡、补卡记录）：次日打卡分析
     *
     * @param updList
     * @param regList
     * @param empShiftInfo
     * @param empId
     * @param clockDate
     * @return 当日打卡记录ID
     */
    public List<Integer> analyseOnceClockForWorkTimeRuleOfNextDay(List<WaRegisterRecordDo> updList,
                                                                  List<WaRegisterRecordDo> regList,
                                                                  Map<String, WaShiftDo> empShiftInfo,
                                                                  Long empId,
                                                                  Long clockDate) {
        List<Integer> nextDayRegIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regList)) {
            log.debug("clockDate once clock for NextDay worktime analyse fail regs empty , empId={}, clockDate={}, time={}", empId, clockDate, System.currentTimeMillis());
            return nextDayRegIdList;
        }
        regList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));

        List<WaRegisterRecordDo> nextDayRegList = regList;

        // 查询排班
        Long nextDay = DateUtil.addDate(clockDate * 1000, 1);
        WaShiftDo clockDateShiftDo = empShiftInfo.get(empId + "_" + nextDay);
        if (null == clockDateShiftDo) {
            log.debug("clockDate once clock for NextDay worktime analyse fail shift empty, empId={}, nextDay={}, time={}", empId, nextDay, System.currentTimeMillis());
            return nextDayRegIdList;
        }
        List<WaShiftDo> shiftDoList = clockDateShiftDo.doGetShiftDoList();
        List<Integer> shiftDefIds = shiftDoList.stream().map(WaShiftDo::getShiftDefId).collect(Collectors.toList());
        log.debug("clockDate once clock for NextDay worktime analyse clockDate={}, shift={}, time={}",
                nextDay, FastjsonUtil.toJsonStr(shiftDefIds), System.currentTimeMillis());

        if (shiftDoList.size() > 1) {// 一天排多个班
            WaShiftDef lastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(clockDateShiftDo.doGetLastShiftDef());

            for (WaShiftDo shiftDo : shiftDoList) {
                if (CollectionUtils.isEmpty(nextDayRegList)) {
                    break;
                }
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(ObjectConverter.convert(shiftDo, WaShiftDef.class));
                if (null == shiftWorkTime.getStartTime() || null == shiftWorkTime.getEndTime()) {
                    log.debug("clockDate once clock for NextDay worktime analyse fail multi shift worktime empty shiftid={},time={}",
                            shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                    continue;
                }

                long shiftStartTime = nextDay + shiftWorkTime.getStartTime() * 60L;
                long shiftEndTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                        ? nextDay + (shiftWorkTime.getEndTime() + 1440) * 60L
                        : nextDay + shiftWorkTime.getEndTime() * 60L;

                // 当前班次内的打卡记录
                List<WaRegisterRecordDo> currentShiftRegList;
                if (lastShiftWorkTime.getShiftDefId().equals(shiftWorkTime.getShiftDefId())) {
                    currentShiftRegList = new ArrayList<>(nextDayRegList);
                } else {
                    currentShiftRegList = nextDayRegList.stream()
                            .filter(wrrd -> wrrd.getRegDateTime() <= shiftEndTime)
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(currentShiftRegList)) {
                    continue;
                }
                for (WaRegisterRecordDo wrrd : currentShiftRegList) {
                    wrrd.setBelongDate(nextDay);
                    wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                    wrrd.setStartTime(shiftWorkTime.getStartTime());
                    wrrd.setEndTime(shiftWorkTime.getEndTime());
                    wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                    wrrd.setNormalDate(String.format("%s-%s",
                            DateUtil.convertMinuteToTime(shiftWorkTime.getStartTime()),
                            DateUtil.convertMinuteToTime(shiftWorkTime.getEndTime())));
                    if (wrrd.getRegDateTime() >= shiftStartTime && wrrd.getRegDateTime() <= shiftEndTime) {
                        wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                        wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                        wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                    }
                    updList.add(wrrd);
                    nextDayRegIdList.add(wrrd.getRecordId());
                    nextDayRegList.removeIf(it -> it.getRecordId().equals(wrrd.getRecordId()));
                }
            }
            if (CollectionUtils.isNotEmpty(nextDayRegList)) {
                List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(nextDayRegIdList)
                        ? nextDayRegList.stream().filter(it -> !nextDayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                        : nextDayRegList;
                if (CollectionUtils.isNotEmpty(unanalyzedRegList)) {
                    unanalyzedRegList.forEach(it -> it.setBelongDate(nextDay));
                    updList.addAll(unanalyzedRegList);
                    List<Integer> unanalyzedRegIdList = unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList());
                    nextDayRegIdList.addAll(unanalyzedRegIdList);
                }
            }
        } else {// 一天排一个班
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(ObjectConverter.convert(clockDateShiftDo, WaShiftDef.class));
            if (null == shiftWorkTime.getStartTime() || null == shiftWorkTime.getEndTime()) {
                log.debug("clockDate once clock for NextDay worktime analyse fail shift worktime empty shiftid={},time={}",
                        shiftWorkTime.getShiftDefId(), System.currentTimeMillis());
                return nextDayRegIdList;
            }

            long shiftStartTime = nextDay + shiftWorkTime.getStartTime() * 60L;
            long shiftEndTime = CdWaShiftUtil.checkCrossNightV2(shiftWorkTime, shiftWorkTime.getDateType())
                    ? nextDay + (shiftWorkTime.getEndTime() + 1440) * 60L
                    : nextDay + shiftWorkTime.getEndTime() * 60L;

            for (WaRegisterRecordDo wrrd : nextDayRegList) {
                wrrd.setBelongDate(nextDay);
                wrrd.setShiftDefId(shiftWorkTime.getShiftDefId());
                wrrd.setStartTime(shiftWorkTime.getStartTime());
                wrrd.setEndTime(shiftWorkTime.getEndTime());
                wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                wrrd.setNormalDate(String.format("%s-%s",
                        DateUtil.convertMinuteToTime(shiftWorkTime.getStartTime()),
                        DateUtil.convertMinuteToTime(shiftWorkTime.getEndTime())));
                if (wrrd.getRegDateTime() >= shiftStartTime && wrrd.getRegDateTime() <= shiftEndTime) {
                    wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                    wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                    wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                }
                updList.add(wrrd);
                nextDayRegIdList.add(wrrd.getRecordId());
            }
        }
        return nextDayRegIdList;
    }

    /**
     * 一次卡打卡分析（在班次上班时间-下班时间内存在打卡、补卡记录）
     *
     * @param empShiftInfo
     * @param regList
     * @param empId
     * @param clockDate
     * @return
     */
    public List<WaRegisterRecordDo> analyseOnceClockForWorkTimeRule(Map<String, WaShiftDo> empShiftInfo,
                                                                    List<WaRegisterRecordDo> regList,
                                                                    Long empId,
                                                                    Long clockDate,
                                                                    ClockAnalyseDataCacheDto dataCacheDto) {
        List<WaRegisterRecordDo> updList = new ArrayList<>();

        // 前一日打卡分析
        List<Integer> yesterdayRegIdList = analyseOnceClockForWorkTimeRuleOfYesterday(updList, regList, empShiftInfo,
                empId, clockDate, dataCacheDto);
        List<WaRegisterRecordDo> unanalyzedRegList = CollectionUtils.isNotEmpty(yesterdayRegIdList)
                ? regList.stream().filter(it -> !yesterdayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : regList;

        // 当日打卡分析
        List<Integer> todayRegIdList = analyseOnceClockForWorkTimeRuleOfToday(updList, unanalyzedRegList, empShiftInfo, empId,
                clockDate, dataCacheDto);
        unanalyzedRegList = CollectionUtils.isNotEmpty(todayRegIdList)
                ? unanalyzedRegList.stream().filter(it -> !todayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : unanalyzedRegList;

        // 次日打卡分析
        List<Integer> nextDayRegIdList = analyseOnceClockForWorkTimeRuleOfNextDay(updList, unanalyzedRegList, empShiftInfo, empId, clockDate);
        unanalyzedRegList = CollectionUtils.isNotEmpty(nextDayRegIdList)
                ? unanalyzedRegList.stream().filter(it -> !nextDayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : unanalyzedRegList;
        List<Integer> unanalyzedRegIdList = CollectionUtils.isNotEmpty(unanalyzedRegList)
                ? unanalyzedRegList.stream().map(WaRegisterRecordDo::getRecordId).distinct().collect(Collectors.toList())
                : Lists.newArrayList();
        log.debug("analyseOnceClockForWorkTimeRule.unanalyzedRegList={}", FastjsonUtil.toJsonStr(unanalyzedRegIdList));
        return updList;
    }

    /**
     * 打卡时间校验（弹性打卡时间校验+标准打卡时间校验）
     *
     * @param register
     * @param shiftDef
     * @param onDutyStartTime
     * @param onDutyEndTime
     * @param offDutyStartTime
     * @param offDutyEndTime
     */
    public void checkClockTime(WaRegisterRecordDo register, WaShiftDef shiftDef,
                               Long onDutyStartTime, Long onDutyEndTime,
                               Long offDutyStartTime, Long offDutyEndTime) {
        if (null == shiftDef) {
            return;
        }
        // 打卡时间异常校验
        boolean isTimeError = false;
        // 弹性打卡时间校验
        Boolean flexibleValidate = validateFlexibleTimeError(shiftDef, register);
        if (null != flexibleValidate) {
            isTimeError = flexibleValidate;
        } else {
            // 标准打卡时间校验
            if (ClockTypeEnum.SIGN_IN.getIndex().equals(register.getRegisterType()) && null != onDutyEndTime) {
                isTimeError = register.getRegDateTime() > onDutyEndTime;
            } else if (ClockTypeEnum.SIGN_OUT.getIndex().equals(register.getRegisterType()) && null != offDutyStartTime) {
                isTimeError = register.getRegDateTime() < offDutyStartTime;
            }
        }
        if (isTimeError) {
            List<MobileEnum.RegisterErrorType> errorDescList = new ArrayList<>();
            errorDescList.add(MobileEnum.RegisterErrorType.TIME_ERR);
            register.setResultDesc(StringUtils.join(errorDescList, ","));
            register.setResultType(ClockResultEnum.ABNORMAL.getIndex());
        } else {
            register.setResultType(ClockResultEnum.NORMAL.getIndex());
        }
    }

    /**
     * 打卡时间校验（标准打卡时间校验）
     *
     * @param register
     * @param onDutyStartTime
     * @param onDutyEndTime
     * @param offDutyStartTime
     * @param offDutyEndTime
     */
    public void checkClockTime(WaRegisterRecordDo register,
                               Long onDutyStartTime, Long onDutyEndTime,
                               Long offDutyStartTime, Long offDutyEndTime) {
        // 打卡时间异常校验（标准打卡时间校验）
        boolean isTimeError = false;
        if (ClockTypeEnum.SIGN_IN.getIndex().equals(register.getRegisterType()) && null != onDutyEndTime) {
            isTimeError = register.getRegDateTime() > onDutyEndTime;
        } else if (ClockTypeEnum.SIGN_OUT.getIndex().equals(register.getRegisterType()) && null != offDutyStartTime) {
            isTimeError = register.getRegDateTime() < offDutyStartTime;
        }
        if (isTimeError) {
            List<MobileEnum.RegisterErrorType> errorDescList = new ArrayList<>();
            errorDescList.add(MobileEnum.RegisterErrorType.TIME_ERR);
            register.setResultDesc(StringUtils.join(errorDescList, ","));
            register.setResultType(ClockResultEnum.ABNORMAL.getIndex());
        } else {
            register.setResultType(ClockResultEnum.NORMAL.getIndex());
        }
    }

    /**
     * 弹性工作固定工作时常-计算下班最小时间
     */
    @Deprecated
    private Long calculateMinOffDutyTime(WaShiftDef shiftDef, Long workDate, Long regDateTime, Integer fixedWorkingTime) {
        if (shiftDef.getIsNoonRest()) {
            long noonRestStart = workDate + shiftDef.getNoonRestStart() * 60;
            long noonRestEnd = workDate + shiftDef.getNoonRestEnd() * 60;
            if (regDateTime <= noonRestStart) {
                int noonTotalTime = shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                fixedWorkingTime += Math.max(noonTotalTime, 0);
            } else if (regDateTime <= noonRestEnd) {
                regDateTime = workDate + (shiftDef.getNoonRestEnd() * 60);
            }
        }
        return regDateTime + (fixedWorkingTime * 60);
    }

    /**
     * 弹性打卡校验（TODO 此逻辑为1.0的弹性逻辑，应该不再使用了）
     *
     * @param shiftDef
     * @param register
     * @return
     */
    @Deprecated
    public Boolean validateFlexibleTimeError(WaShiftDef shiftDef, WaRegisterRecordDo register) {
        if (!BooleanUtils.isTrue(shiftDef.getIsFlexibleWork()) || null == shiftDef.getFlexibleWorkType()) {
            return null;
        }
        if (ClockTypeEnum.SIGN_IN.getIndex().equals(register.getRegisterType())) {
            if (shiftDef.getFlexibleWorkType() == 1 && shiftDef.getFlexibleOnDutyStartTime() != null
                    && shiftDef.getFlexibleOnDutyEndTime() != null) {
                // 弹性打卡区间
                long flexibleStartTime = register.getBelongDate() + shiftDef.getFlexibleOnDutyStartTime() * 60L;
                long flexibleEndTime = register.getBelongDate() + shiftDef.getFlexibleOnDutyEndTime() * 60L;
                return !(register.getRegDateTime() > flexibleStartTime && register.getRegDateTime() <= flexibleEndTime);
            }
        } else if (ClockTypeEnum.SIGN_OUT.getIndex().equals(register.getRegisterType())) {
            // 查询当天最早的签到时间
            List<WaRegisterRecord> signInList = registerRecordService.listWaRegisterRecord(register.getEmpid(),
                    register.getBelongDate(), null, ClockTypeEnum.SIGN_IN.getIndex(), "reg_date_time:asc");
            if (CollectionUtils.isEmpty(signInList)) {
                return null;
            }
            WaRegisterRecord firstSignIn = signInList.get(0);
            boolean crossNight = CdWaShiftUtil.checkCrossNightV2(shiftDef, shiftDef.getDateType());
            if (shiftDef.getFlexibleWorkType() == 1) {// 弹性打卡区间
                long flexibleOnDutyStartTime = register.getBelongDate() + shiftDef.getFlexibleOnDutyStartTime() * 60L;
                long flexibleOnDutyEndTime = register.getBelongDate() + shiftDef.getFlexibleOnDutyEndTime() * 60L;
                boolean inFlexibleTime = firstSignIn.getRegDateTime() > flexibleOnDutyStartTime
                        && firstSignIn.getRegDateTime() <= flexibleOnDutyEndTime;
                if (inFlexibleTime) {//弹性下班
                    Long flexibleOffStartTime = crossNight
                            ? register.getBelongDate() + (shiftDef.getFlexibleOffDutyStartTime() + 1440) * 60L
                            : register.getBelongDate() + shiftDef.getFlexibleOffDutyStartTime() * 60L;
                    return register.getRegDateTime() < flexibleOffStartTime;
                }
            } else if (shiftDef.getFlexibleWorkType() == 2) {// 固定工时
                Long minOffDutyTime = calculateMinOffDutyTime(shiftDef, register.getBelongDate(), firstSignIn.getRegDateTime(), shiftDef.getWorkTotalTime());
                Long offDutyStartTime = crossNight
                        ? register.getBelongDate() + (shiftDef.getOffDutyStartTime() + 1440) * 60L
                        : register.getBelongDate() + shiftDef.getOffDutyStartTime() * 60L;
                return register.getRegDateTime() < minOffDutyTime || register.getRegDateTime() < offDutyStartTime;
            }
        }
        return null;
    }

    public Map getRuleMap(String clockRule) {
        Map ruleMap;
        try {
            if (StringUtil.isNullOrEmpty(clockRule) || null == (ruleMap = JSONUtils.toMap(clockRule)) || ruleMap.isEmpty()) {
                log.error("The analysis rule of primary card is empty,clockRule={}", clockRule);
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_202814", WebUtil.getRequest()));
            }
        } catch (Exception e) {
            log.error("Failed to parse the analysis rules of a card, clockRule={}", clockRule);
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_202815", WebUtil.getRequest()));
        }
        return ruleMap;
    }

    public void fastUpdList(List<WaRegisterRecordDo> updList) {
        if (CollectionUtils.isEmpty(updList)) {
            log.info("ClockAnalyseV1 update fail record empty, time={}", System.currentTimeMillis());
            return;
        }
        log.info("ClockAnalyseV1 update start count={}, time={}", updList.size(), System.currentTimeMillis());
        List<WaRegisterRecord> registerUpdList = ObjectConverter.convertList(updList, WaRegisterRecord.class);
        List<List<WaRegisterRecord>> lists = ListTool.split(registerUpdList, 500);
        lists.forEach(list -> importService.fastUpdList(WaRegisterRecord.class, "recordId", list));
        log.info("ClockAnalyseV1 update finish count={}, time={}", updList.size(), System.currentTimeMillis());
    }
}
