package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.ISysJobRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.SysJobPo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Data
@Service
public class SysJobDo {
    private String sysTenantId;

    private Integer id;

    private String name;

    private Long parentId;

    private Integer deleted;

    private String pathName;

    private String tenantId;

    private String path;

    private String type;

    private String layerType;

    private String thirdId;

    private String thirdPart;

    public static List<SysJobDo> loadSysJob(String tenantId) {
        ISysJobRepository calRepo = SpringUtil.getBean(ISysJobRepository.class);
        List<SysJobPo> poList = calRepo.loadSysJob(tenantId);
        return ObjectConverter.convertList(poList, SysJobDo.class);
    }

    public static boolean checkTableExist(String tenantId) {
        ISysJobRepository calRepo = SpringUtil.getBean(ISysJobRepository.class);
        return calRepo.checkTableExist(tenantId);
    }
}
