package com.caidaocloud.attendance.service.application.event.subscribe;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.attendance.service.application.service.msg.MessageService;
import com.caidaocloud.mq.rabbitmq.consumer.AbsMQConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@RabbitListener(
        bindings = @QueueBinding(
                value = @Queue(value = "attendance.workcalendar.queue", durable = "true"),
                exchange = @Exchange(value = "maintenance.tenant.init.fanout.exchange"),
                key = {"routingKey.maintenance.tenant.init"}
        )
)
public class TenantSubscribe extends AbsMQConsumer {

    @Resource
    private MessageService messageService;

    @Override
    public void process(String message) {
        log.info("TenantSubscribe message={}", message);
        try {
            TenantDto msgInfo = JSON.parseObject(message, TenantDto.class);
            messageService.createTenant(msgInfo);
        } catch (Exception ex) {
            log.error("TenantSubscribe消息监听出现异常，异常原因:{}", ex.getMessage(), ex);
        }
    }
}
