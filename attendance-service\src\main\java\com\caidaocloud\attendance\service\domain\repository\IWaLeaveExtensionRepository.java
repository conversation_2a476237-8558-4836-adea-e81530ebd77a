package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveExtensionDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveExtension;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

public interface IWaLeaveExtensionRepository {
    WaLeaveExtension selectByPrimaryKey(Long id);

    int save(WaLeaveExtension model);

    int update(WaLeaveExtension model);

    int delete(WaLeaveExtension model);

    PageList<WaLeaveExtensionDo> getEmpLeaveExtensionList(MyPageBounds myPageBounds, Map params);

    WaLeaveExtensionDo getById(String tenantId, Long id);

    List<WaLeaveExtension> getLeaveExtensionList(String tenantId, List<Long> configIds, List<Long> quotaIds, List<Integer> status);
}
