package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWaReportFieldRuleRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 考勤报表字段规则表
 *
 * <AUTHOR>
 * @Date 2024/2/5
 */
@Slf4j
@Data
@Service
public class WaReportFieldRuleDo {
    /**
     * 主键
     */
    private Long id;
    /**
     * 考勤分析分组ID
     */
    private Integer parseGroupId;
    /**
     * 迟到分析规则，值为{"opt": "表达式: >、<、>=、<=", "duration": "时长", "unit": "单位: 分钟 minute、小时 hour"}
     */
    private String latePaseRule;
    /**
     * 早退分析规则，值为{"opt": "表达式: >、<、>=、<=", "duration": "时长", "unit": "单位: 分钟 minute、小时 hour"}
     */
    private String earlyPaseRule;
    /**
     * 旷工分析规则，值为{"opt": "表达式: >、<、>=、<=", "duration": "时长", "unit": "单位: 分钟 minute、小时 hour", "type":"旷工规则类型：1 晚来+早走 、2 晚来/早走"}
     */
    private String absentPaseRule;
    private String tenantId;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;

    @Autowired
    private IWaReportFieldRuleRepository waReportFieldRuleRepository;

    public List<WaReportFieldRuleDo> selectList(String tenantId, Integer parseGroupId) {
        return waReportFieldRuleRepository.selectList(tenantId, parseGroupId);
    }

    public void save(WaReportFieldRuleDo reportFieldRule) {
        waReportFieldRuleRepository.save(reportFieldRule);
    }

    public void update(WaReportFieldRuleDo reportFieldRule) {
        waReportFieldRuleRepository.update(reportFieldRule);
    }

    public void delete(String tenantId, Integer parseGroupId) {
        waReportFieldRuleRepository.delete(tenantId, parseGroupId);
    }
}
