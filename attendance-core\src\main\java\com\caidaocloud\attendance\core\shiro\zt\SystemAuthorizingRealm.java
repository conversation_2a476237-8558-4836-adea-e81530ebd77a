package com.caidaocloud.attendance.core.shiro.zt;

import com.caidaocloud.attendance.core.auth.service.UserService;
import com.caidao1.system.mybatis.model.SysUserInfo;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.beans.factory.annotation.Autowired;

public class SystemAuthorizingRealm extends AuthorizingRealm {

	@Autowired
	private UserService userService;
	
	@Override
	protected AuthorizationInfo doGetAuthorizationInfo(
			PrincipalCollection principals) {
		return null;
	}

	@Override
	protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authcToken) throws AuthenticationException {
        UsernamePasswordToken token = (UsernamePasswordToken) authcToken;  
        SysUserInfo user = userService.getUserByAccount(token.getUsername());
        if (user != null) {  
            return new SimpleAuthenticationInfo(user.getAccount(), user.getPasswd(), "");  
        } else {  
            return null;  
        }  
	}

}
