package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.dto.ProcessDto;
import com.caidaocloud.attendance.service.application.dto.ProcessRecordDto;
import com.caidaocloud.attendance.core.workflow.dto.WfDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.service.interfaces.dto.WfApprovalDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WfNoticeParameterItem;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WfTaskUrgeItem;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;

import java.util.List;
import java.util.Map;

/**
 * 工作流程
 *
 * <AUTHOR>
 * @Date 2021/3/22
 */
public interface IWfService {
    Result<Boolean> revokeWorkflowBegin(UserInfo userInfo, Long entityId, String revokeReason, BusinessCodeEnum workflowEnum, Long eventTime);

    WfDetailDto getWfFuncDetail(String businessKey, String nodeId, Integer funcType) throws Exception;

    void handleWorkflowEvent(String businessKey, String choice, Long eventTime);

    WfResponseDto getWfDetail(String tenantId, String businessKey, boolean summary) throws Exception;

    Result workflowApproval(WfApprovalDto dto);

    Result<Boolean> checkWorkflowEnabled(String funCode);

    void workflowTaskUrge(List<WfTaskUrgeItem> items);

    Map<String, String> workflowParameter(WfNoticeParameterItem item);

    ProcessRecordDto recordOfProcess(ProcessDto processDto) throws Exception;
}
