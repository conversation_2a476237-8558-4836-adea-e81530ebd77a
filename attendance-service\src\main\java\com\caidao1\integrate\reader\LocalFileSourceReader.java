package com.caidao1.integrate.reader;

import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.caidao1.ioc.util.IocCsvReader;
import com.caidao1.integrate.util.IntegrateUtil;
import com.weibo.api.motan.core.extension.SpiMeta;
import org.apache.commons.io.Charsets;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.output.StringBuilderWriter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

@SpiMeta(name = "localFile")
public class LocalFileSourceReader implements SourceReader {

    private static final Log logger = LogFactory.getLog(LocalFileSourceReader.class);
    @Override
    public List<Map<String, Object>> getSourceResult(Long corpId, String belongId, SysDataInput dataInput, Map<String, Object> mapJson, Map exparams, List<String> logList, Map returnMap) throws Exception {

        List<Map<String, Object>> sourceResult = new ArrayList<Map<String, Object>>();

        String fileUrl = (String) mapJson.get("local.url");
        String fileName = (String) mapJson.get("local.fileName");

        try{
            //格式化支持
            fileName = String.format(fileName, IntegrateUtil.formatExp((String) mapJson.get("local.filterParam"), "yyyyMMdd"));
            readFile(fileUrl + fileName,dataInput,mapJson,sourceResult,returnMap);
        } catch (Exception e){
            e.printStackTrace();
        }
        return sourceResult;
    }

    private void readFile(String readFile, SysDataInput dataInput, Map<String, Object> mapJson, List<Map<String, Object>> sourceResult, Map returnMap) throws FileNotFoundException {
        if (new File(readFile).isDirectory()) {
            for (File subFile : new File(readFile).listFiles()) {
                readFile(subFile.getAbsolutePath(), dataInput, mapJson, sourceResult, returnMap);
            }
            return;
        }

        try {
            IocCsvReader reader;
            boolean hasHeader = (boolean) mapJson.getOrDefault("file.hasHeader", true);
            String encoding = mapJson.get("file.encoding") == null ? null : mapJson.get("file.encoding").toString();
            if ("string".equals(mapJson.getOrDefault("file.read", "string"))) {
                File file = new File(readFile);
                String content = readFileToString(new File(readFile),encoding);
                if (mapJson.get("file.delimiter") != null){
                    content = content.replace(((String) mapJson.get("file.delimiter")),"|");
                }
                reader = new IocCsvReader(new StringReader(content), '|', hasHeader);
            } else {
                reader = new IocCsvReader(new FileInputStream(readFile), ((String) mapJson.get("file.delimiter")).charAt(0), hasHeader);
            }

            List<Map<String, Object>> items = new ArrayList<>();
            reader.getRows().stream().forEach(row -> {
                Map<String, Object> srow = new HashMap<>();
                if (hasHeader) {
                    for (int i = 0; i < reader.getTitle().size(); i++) {
                        if (StringUtils.isNotEmpty(reader.getTitle().get(i))) {
                            srow.put(reader.getTitle().get(i).replace("\uFEFF", ""), row.get(i));
                        }
                    }
                } else {
                    for (int i = 0; i < row.size(); i++) {
                        srow.put(i + "", row.get(i));
                    }
                }
                items.add(srow);
            });
            items.stream().forEach(item -> {
                Map<String, Object> row = new HashMap<String, Object>();
                for (String field : dataInput.getSourceExp().split(",")) {
                    if (field.indexOf(":TIMESTAMP") != -1) {
                        String key = field.substring(field.lastIndexOf("#") + 1);
                        String dataFormat = field.substring(field.indexOf("(") + 1, field.indexOf(")"));
                        SimpleDateFormat sf = new SimpleDateFormat(dataFormat.split("#")[1]);
                        try {
                            Object object = item.get(dataFormat.split("#")[0]);
                            if (object != null) {
                                Long time = sf.parse((String) object).getTime() / 1000;
                                row.put(key, time);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else {
                        String[] meta = field.split("#");
                        if (meta.length > 1) {
                            row.put(meta[1], item.get(meta[0]));
                        } else {
                            row.put(field, item.get(field));
                        }
                    }
                }
                sourceResult.add(row);
            });
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (returnMap != null) {
                    String fileName = readFile.substring(readFile.lastIndexOf(File.separator) + 1);
                    List<String> failFiles;
                    if (returnMap.containsKey("failFiles") && returnMap.get("failFiles") != null) {
                        failFiles = (List<String>) returnMap.get("failFiles");
                    } else {
                        failFiles = new ArrayList<>();
                    }
                    failFiles.add(fileName);
                    returnMap.put("failFiles", failFiles);
                }
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
    }

    private String readFileToString(File file,String encoding){
        String content = "";
        FileInputStream fileInputStream = null;

        StringBuilderWriter sw = null;
        InputStreamReader in = null;
        try{
            fileInputStream = new FileInputStream(file);
            sw = new StringBuilderWriter();
            in = new InputStreamReader(fileInputStream, Charsets.toCharset(StringUtils.defaultIfBlank(encoding,"utf-8")));
            char[] buffer = new char[2048];
            int n;
            for(; -1 != (n = in.read(buffer));) {
                sw.write(buffer, 0, n);
            }
            content = sw.toString();

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            IOUtils.closeQuietly(fileInputStream);
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(sw);
        }
        return content;
    }

    public static void main(String[] args) throws IOException {
        File file = new File("D:/AA.txt");
        FileInputStream fileInputStream = new FileInputStream(file);
        String content = "";
        try{
            // = IOUtils.toString(fileInputStream, Charsets.toCharset("UTF-8"));
            StringBuilderWriter sw = new StringBuilderWriter();
            InputStreamReader in = new InputStreamReader(fileInputStream, Charsets.toCharset("GBK"));
            char[] buffer = new char[2048];
            int n;
            for(; -1 != (n = in.read(buffer));) {
                sw.write(buffer, 0, n);
            }
            content = sw.toString();
            System.out.println(content);

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            IOUtils.closeQuietly(fileInputStream);
        }

    }
}
