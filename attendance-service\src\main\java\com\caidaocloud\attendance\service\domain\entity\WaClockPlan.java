package com.caidaocloud.attendance.service.domain.entity;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.attendance.service.domain.repository.IClockPlanRepository;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockPlanPo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo;
import com.caidaocloud.util.ObjectConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Data
@Service
public class WaClockPlan {
    private Long id;
    private Long corpId;
    private String belongOrgId;
    private String planName;
    private String clockWay;
    private Integer supplementCount;
    private Boolean isSupplement;
    private String gps;
    private String wifi;
    private String bluetooth;
    private Long creator;
    private Long createTime;
    private Long updater;
    private Long updateTime;
    private Long empId;
    private String empName;
    private String workNo;
    /**
     * 新增字段
     */
    private String groupExp;
    private String groupNote;
    /**
     * 20210830新增字段 by aaron.chen
     */
    @ApiModelProperty("是否允许外勤打卡:true/false，默认false")
    private Boolean allowFieldClockIn;
    @ApiModelProperty("外勤打卡需填写备注:true/false，默认false")
    private Boolean fieldClockInNote;
    @ApiModelProperty("外勤打卡需拍照:true/false，默认false")
    private Boolean fieldClockInEnclosure;
    /**
     * 202100907新增字段 by aaron.chen
     */
    @ApiModelProperty("打卡时间间隔")
    private Float timeInterval;
    /**
     * 202100923新增字段 by aaron.chen
     */
    @ApiModelProperty("补卡说明")
    private String description;
    @ApiModelProperty("补卡原因必填开关：true/false")
    private Boolean reasonMust;
    /**
     * 是否允许范围外打卡：true(允许)/false(false) 20220517
     */
    private Boolean clockInAllowed;
    @ApiModelProperty("补卡条数每次")
    private Integer supplementNumber;

    @ApiModelProperty("补卡原因字数")
    private Integer reasonWordNum;

    @ApiModelProperty("补卡附件必须开关：默认false，true必须，false非必须")
    private Boolean enclosureRequired;
    private String i18nPlanName;

    /**
     * 补打卡限制次数规则：1 仅限制员工申请、2 员工申请和考勤申请均限制
     */
    private Integer supplementCountRule;

    @Autowired
    private IClockPlanRepository clockPlanRepository;

    public int save(WaClockPlan plan) {
        WaClockPlanPo waClockPlanPo = ObjectConverter.convert(plan, WaClockPlanPo.class);
        return clockPlanRepository.save(waClockPlanPo);
    }

    public int update(WaClockPlan plan) {
        WaClockPlanPo waClockPlanPo = ObjectConverter.convert(plan, WaClockPlanPo.class);
        return clockPlanRepository.update(waClockPlanPo);
    }

    public WaClockPlan getClockPlanById(Long id) {
        if (null == id) {
            return null;
        }
        WaClockPlanPo waClockPlanPo = clockPlanRepository.selectById(id);
        if (null == waClockPlanPo) {
            return null;
        }
        return ObjectConverter.convert(waClockPlanPo, WaClockPlan.class);
    }

    public int deleteClockPlanById(Long id) {
        return clockPlanRepository.deleteById(id);
    }

    public AttendancePageResult<WaClockPlan> getPlanPageList(AttendanceBasePage basePage, Long corpId, String belongOrgId, String keywords) {
        AttendancePageResult<WaClockPlanPo> pageResult = clockPlanRepository.getClockPlanPageList(basePage, corpId, belongOrgId, keywords);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return new AttendancePageResult<>();
        }
        List<WaClockPlan> list = JSON.parseArray(JSON.toJSONString(pageResult.getItems()), WaClockPlan.class);
        return new AttendancePageResult<>(list, basePage.getPageNo(), basePage.getPageSize(), pageResult.getTotal());
    }

    public List<WaClockPlan> getPlanByParams(Long corpId, String belongOrgId, Long planId, String planName) {
        List<WaClockPlanPo> list = clockPlanRepository.getPlanByParams(corpId, belongOrgId, planId, planName);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(list, WaClockPlan.class);
    }

    public List<WaClockPlan> getPlanRelEmployeesByEmpIds(Long id, Long corpId, String belongOrgId, List<Long> empIds) {
        List<WaClockPlanPo> list = clockPlanRepository.getPlanRelEmployeesByEmpIds(id, corpId, belongOrgId, empIds);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(list, WaClockPlan.class);
    }

    public List<WaClockPlan> getPlanListBySiteId(Long corpId, String belongOrgId, Long siteId) {
        List<WaClockPlanPo> list = clockPlanRepository.getPlanListBySiteId(corpId, belongOrgId, siteId);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(list, WaClockPlan.class);
    }

    public WaClockPlan getMyWaClockPlan(Long corpId, String belongOrgId, Long empId, long currentTime){
        WaPlanEmpRelPo myWaPlanEmpRelPo = clockPlanRepository.getMyWaClockPlan(corpId, belongOrgId, empId, currentTime);
        if (null == myWaPlanEmpRelPo) {
            return null;
        }
        WaClockPlanPo waClockPlanPo = clockPlanRepository.getWaClockPlanPo(corpId, belongOrgId, myWaPlanEmpRelPo.getPlanId());
        return ObjectConverter.convert(waClockPlanPo, WaClockPlan.class);
    }
}