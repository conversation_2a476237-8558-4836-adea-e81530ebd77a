package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.wa.mybatis.mapper.WaGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveSettingMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveTypeMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.ILeaveTypeService;
import com.caidaocloud.attendance.service.application.service.IWaLeaveTypeDefService;
import com.caidaocloud.attendance.service.application.service.user.EmployeeGroupService;
import com.caidaocloud.attendance.service.domain.entity.LeaveQuotaConfigDo;
import com.caidaocloud.attendance.service.domain.entity.QuotaGenRuleDo;
import com.caidaocloud.attendance.service.domain.entity.WaEmpQuotaDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDo;
import com.caidaocloud.attendance.service.domain.service.LeaveTypeDomainService;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.common.KeyValueExt;
import com.caidaocloud.attendance.service.interfaces.dto.leave.LeaveEnabelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.LeaveTypeInfoDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.LeaveQuotaRuleConfigDto;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.DragSort;
import com.caidaocloud.dto.DragSortItem;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.jarvis.cache.annotation.Cache;
import com.jarvis.cache.annotation.CacheDelete;
import com.jarvis.cache.annotation.CacheDeleteKey;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 假期设置服务
 *
 * <AUTHOR>
 * @date 2021/1/22
 */
@Slf4j
@Service
public class LeaveTypeServiceImpl implements ILeaveTypeService {
    private final String WA_LEAVE_QUOTA_CONFIG = "wa_leave_quota_config";
    private final String WA_LEAVE_TYPE = "wa_leave_type";
    @Autowired
    private LeaveTypeDomainService leaveTypeDomainService;
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private LeaveQuotaConfigDo leaveQuotaConfigDo;
    @Autowired
    private QuotaGenRuleDo quotaGenRuleDo;
    @Autowired
    private WaLeaveSettingMapper waLeaveSettingMapper;
    @Autowired
    private WaEmpQuotaDo waEmpQuotaDo;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private EmployeeGroupService employeeGroupService;
    @Autowired
    private IWaLeaveTypeDefService leaveTypeDefService;

    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Override
    public LeaveTypeDto getLeaveTypeById(Integer leaveTypeId) {
        WaLeaveTypeDo leaveTypeDo = leaveTypeDomainService.getLeaveTypeById(leaveTypeId);
        if (null == leaveTypeDo) {
            return new LeaveTypeDto();
        }
        return convertToLeaveTypeDto(leaveTypeDo);
    }

    private LeaveTypeDto convertToLeaveTypeDto(WaLeaveTypeDo leaveTypeDo) {
        LeaveTypeDto leaveTypeDto = ObjectConverter.convert(leaveTypeDo, LeaveTypeDto.class);
        if (StringUtils.isNotBlank(leaveTypeDo.getI18nLeaveName())) {
            leaveTypeDto.setI18nLeaveName(FastjsonUtil.toObject(leaveTypeDo.getI18nLeaveName(), Map.class));
        } else if (StringUtils.isNotBlank(leaveTypeDo.getLeaveName())) {
            Map<String, String> i18nName = new HashMap<>();
            i18nName.put("default", leaveTypeDo.getLeaveName());
            leaveTypeDto.setI18nLeaveName(i18nName);
        }
        leaveTypeDto.setIfCheckTime(BooleanUtils.isTrue(leaveTypeDo.getIsCheckMinTime()) || BooleanUtils.isTrue(leaveTypeDo.getIsCheckMaxTime()));
        List<String> businessKeys = Collections.singletonList(String.valueOf(leaveTypeDto.getLeaveTypeId()));
        List<EmployeeGroupDto> empGroups = employeeGroupService.getEmployeeGroups(businessKeys, WA_LEAVE_TYPE, leaveTypeDo.getBelongOrgid());
        if (CollectionUtils.isNotEmpty(empGroups)) {
            EmployeeGroupDto employeeGroupDto = empGroups.get(0);
            leaveTypeDto.setGroupExp(employeeGroupDto.getGroupExp());
            leaveTypeDto.setGroupNote(employeeGroupDto.getGroupNote());
        }
        if (leaveTypeDto.getUpperLevel() != null) {
            leaveTypeDto.setUpperType(new String[]{LeaveUpperTypeEnum.LEAVE_TYPE.getCode(), String.valueOf(leaveTypeDto.getUpperLevel())});
        } else if (leaveTypeDto.getUpperLevelQuotaTypeId() != null) {
            leaveTypeDto.setUpperType(new String[]{LeaveUpperTypeEnum.QUOTA_TYPE.getCode(), String.valueOf(leaveTypeDto.getUpperLevelQuotaTypeId())});
        }
        return leaveTypeDto;
    }

    @Override
    public List<LeaveTypeDto> getLeaveTypeListByIds(List<Integer> leaveTypeIds) {
        List<WaLeaveTypeDo> leaveTypeDoList = leaveTypeDomainService.getLeaveTypeListById(leaveTypeIds);
        if (CollectionUtils.isEmpty(leaveTypeDoList)) {
            return new ArrayList<>();
        }
        return leaveTypeDoList.stream().map(this::convertToLeaveTypeDto).collect(Collectors.toList());
    }

    @Transactional
    @Override
    public Result<Boolean> saveOrUpdateLeaveType(LeaveTypeDto leaveTypeSetInfoDto) {
        if (leaveTypeSetInfoDto.getLeaveLimitSwitch() == 2 && leaveTypeSetInfoDto.getLeaveLimitDays() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.ENROLLMENT_DAYS_REQUIRED, Boolean.FALSE);
        }
        LogRecordContext.putVariable("name", leaveTypeSetInfoDto.getLeaveName());
        UserInfo userInfo = getUserInfo();
        //假期编码重复校验
        int count = waLeaveTypeDo.getLeaveTypeCountByCode(userInfo.getTenantId(), leaveTypeSetInfoDto.getLeaveTypeId(), leaveTypeSetInfoDto.getLeaveCode());
        if (count > 0) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CODE_EXIST, Boolean.FALSE);
        }
        int num = waLeaveTypeDo.getLeaveTypeCountByName(userInfo.getTenantId(), leaveTypeSetInfoDto.getLeaveTypeId(), leaveTypeSetInfoDto.getLeaveName());
        if (num > 0) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_NAME_EXIST, Boolean.FALSE);
        }
        // 新的上级假期逻辑
        if (null != leaveTypeSetInfoDto.getUpperType() && leaveTypeSetInfoDto.getUpperType().length > 1) {
            String[] upperTypeValues = leaveTypeSetInfoDto.getUpperType();
            String upperType = upperTypeValues[0];
            String upperId = upperTypeValues[1];
            if (LeaveUpperTypeEnum.LEAVE_TYPE.getCode().equals(upperType)) {
                leaveTypeSetInfoDto.setUpperLevel(Integer.valueOf(upperId));
                leaveTypeSetInfoDto.setUpperLevelQuotaTypeId(null);
            } else if (LeaveUpperTypeEnum.QUOTA_TYPE.getCode().equals(upperType)) {
                leaveTypeSetInfoDto.setUpperLevelQuotaTypeId(Long.valueOf(upperId));
                leaveTypeSetInfoDto.setUpperLevel(null);
            }
        }

        WaLeaveType waLeaveType = ObjectConverter.convert(leaveTypeSetInfoDto, WaLeaveType.class);
        if (null != leaveTypeSetInfoDto.getI18nLeaveName()) {
            waLeaveType.setI18nLeaveName(FastjsonUtil.toJson(leaveTypeSetInfoDto.getI18nLeaveName()));
        }
        waLeaveType.setIsWarnMsg(StringUtils.isNotBlank(waLeaveType.getWarnMsg()));
        if (BooleanUtils.isTrue(leaveTypeSetInfoDto.getIfCheckTime())) {
            waLeaveType.setIsCheckMinTime(true);
            waLeaveType.setIsCheckMaxTime(true);
        } else {
            waLeaveType.setIsCheckMinTime(false);
            waLeaveType.setIsCheckMaxTime(false);
        }
        if (null != waLeaveType.getTimelinessControlJsonb()) {
            waLeaveType.setTimelinessControlJsonb(JSONUtils.ObjectToJson(waLeaveType.getTimelinessControlJsonb()));
        }
        Long currentTime = DateUtil.getCurrentTime(true);
        waLeaveType.setUpdtime(currentTime);
        waLeaveType.setLeaveName(leaveTypeSetInfoDto.getLeaveName());
        waLeaveType.setLeaveCode(leaveTypeSetInfoDto.getLeaveCode());

        // 上级假期字段更新成空
        if (null != waLeaveType.getLeaveTypeId()) {
            Optional<WaLeaveTypeDo> optional = Optional.ofNullable(waLeaveTypeDo.selectById(waLeaveType.getLeaveTypeId()));
            if (optional.isPresent()) {
                WaLeaveTypeDo old = optional.get();
                boolean ifUpdate = false;
                if (null != old.getUpperLevel() && null == waLeaveType.getUpperLevel()) {
                    old.setUpperLevel(null);
                    ifUpdate = true;
                }
                if (null != old.getUpperLevelQuotaTypeId() && null == waLeaveType.getUpperLevelQuotaTypeId()) {
                    old.setUpperLevelQuotaTypeId(null);
                    old.setUpperLevelQuotaCheck(null);
                    old.setUpperLevelQuotaCheckSymbol(null);
                    ifUpdate = true;
                }
                if (ifUpdate) {
                    waLeaveTypeDo.update(old);
                }
            }
        }

        if (null != waLeaveType.getLeaveTypeId() && null != waLeaveType.getUpperLevel()) {
            List<Integer> upperLeaveTypeIds = Lists.newArrayList();
            upperLeaveTypeIds.add(waLeaveType.getLeaveTypeId());
            boolean flag = true;
            Integer upperLeaveTypeId = waLeaveType.getUpperLevel();
            while (flag) {
                WaLeaveTypeDo upperLeaveType = waLeaveTypeDo.selectById(upperLeaveTypeId);
                if (null == upperLeaveType) {
                    flag = false;
                    continue;
                }
                if (upperLeaveTypeIds.contains(upperLeaveTypeId)) {
                    return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_PRIORITY_ORDER_ERROR, Boolean.FALSE);
                }
                if (null == upperLeaveType.getUpperLevel()) {
                    flag = false;
                    continue;
                }
                upperLeaveTypeIds.add(upperLeaveTypeId);
                upperLeaveTypeId = upperLeaveType.getUpperLevel();
            }
        }
        waConfigService.saveLeaveType(waLeaveType, null);
        //员工分组，员工适用范围
        saveEmployeeGroup(String.valueOf(waLeaveType.getLeaveTypeId()), leaveTypeSetInfoDto.getGroupExp(), leaveTypeSetInfoDto.getGroupNote(), WA_LEAVE_TYPE);

        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }

    @Override
    public AttendancePageResult<LeaveTypeDto> getLeaveTypeList(LeaveTypeReqDto reqDto) throws Exception {
        UserInfo userInfo = getUserInfo();
        reqDto.setBelongOrgId(userInfo.getTenantId());
        AttendancePageResult<WaLeaveTypeDo> pageResult = waLeaveTypeDo.getWaLeaveTypePageList(reqDto);
        List<LeaveTypeDto> items = JSON.parseArray(JSON.toJSONString(pageResult.getItems()), LeaveTypeDto.class);
        return new AttendancePageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    public List<LeaveTypeInfoDto> getLeaveTypes(UserInfo userInfo, Integer groupId, Integer quotaRestrictionType, Integer quotaType) {
        List<WaLeaveTypeDo> items = waLeaveTypeDo.getLeaveTypes(userInfo.getTenantId(), quotaRestrictionType, quotaType);
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        List<LeaveTypeInfoDto> list = ObjectConverter.convertList(items, LeaveTypeInfoDto.class);
        list.forEach(row -> {
            row.setLeaveName(LangParseUtil.getI18nLanguage(row.getI18nLeaveName(), row.getLeaveName()));
            row.setLeaveTypeName(LangParseUtil.getI18nLanguage(row.getI18nLeaveTypeName(), row.getLeaveTypeName()));
            if (row.getQuotaRestrictionType() != null) {
                row.setQuotaRestrictionTypeName(QuotaRestrictionTypeEnum.getName(row.getQuotaRestrictionType()));
            }
            row.setStatus(LeaveStatusEnum.UNENABLE.getIndex());
            row.setStatusTxt(LeaveStatusEnum.getName(LeaveStatusEnum.UNENABLE.getIndex()));
        });
        //假期是否启用
        if (groupId != null) {
            Optional<WaGroup> optional = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(groupId));
            if (optional.isPresent()) {
                WaGroup waGroup = optional.get();
                if (waGroup.getLeaveTypeIds() != null) {
                    Integer[] ids = (Integer[]) waGroup.getLeaveTypeIds();
                    for (Integer id : ids) {
                        for (LeaveTypeInfoDto leaveTypeInfoDto : list) {
                            if (leaveTypeInfoDto.getLeaveTypeId().equals(id)) {
                                leaveTypeInfoDto.setStatus(LeaveStatusEnum.ENABLE.getIndex());
                                leaveTypeInfoDto.setStatusTxt(LeaveStatusEnum.getName(LeaveStatusEnum.ENABLE.getIndex()));
                                break;
                            }
                        }
                    }
                }
            }
        }
        //启用的在前面
        //list.sort(Comparator.comparing(LeaveTypeInfoDto::getStatus).reversed());
        return list;
    }

    @Override
    public List<LeaveTypeInfoDto> getLeaveTypes(Integer groupId, Integer quotaRestrictionType, Integer quotaType) {
        UserInfo userInfo = getUserInfo();
        return getLeaveTypes(userInfo, groupId, quotaRestrictionType, quotaType);
    }

    @Transactional
    @Override
    public void deleteLeaveTypeByIds(List<Integer> ids) {
        waLeaveTypeDo.deleteLeaveTypeByIds(getUserInfo().getTenantId(), ids);
    }

    @Transactional
    @Override
    public int saveOrUpdateLeaveQuotaConfig(LeaveQuotaConfigDto dto) {
        Integer leaveTypeId = dto.getLeaveTypeId();
        val leaveTypeDefId = getLeaveTypeById(leaveTypeId).getLeaveType();
        val leaveTypeDef = leaveTypeDefService.getWaLeaveTypeDefList().stream()
                .anyMatch(it -> it.getLeaveTypeDefId().equals(leaveTypeDefId)
                        && it.getLeaveTypeDefCode().equals("探亲假"));
        if (leaveTypeDef && null != dto.getQuotaGroups()) {
            if (dto.getQuotaGroups().size() > 1) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.FAMILY_VISIT_RULE_REPEAT, null).getMsg());
            }
            if (dto.getQuotaGroups().stream().anyMatch(it -> !it.getGroupExp().contains("visiting_reason"))) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.FAMILY_VISIT_REASON_MUST, null).getMsg());
            }
        }
        UserInfo userInfo = this.getUserInfo();
        if (dto.getValidityDuration() != null && dto.getValidityPeriodType() == null) {
            dto.setValidityPeriodType(ValidityPeriodTypeEnum.RESTRICTION.getIndex());
        }
        LeaveQuotaConfigDo oldConfig = leaveQuotaConfigDo.getConfigById(userInfo.getTenantId(), dto.getConfigId());
        int count;
        LeaveQuotaConfigDo quotaConfig = ObjectConverter.convert(dto, LeaveQuotaConfigDo.class);
        if (null != dto.getI18nRuleName()) {
            quotaConfig.setI18nRuleName(FastjsonUtil.toJson(dto.getI18nRuleName()));
        }
        if (null != dto.getGroupExpCondition()) {
            quotaConfig.setGroupExpCondition(FastjsonUtil.toJson(dto.getGroupExpCondition()));
        }
        if (null == oldConfig || oldConfig.getConfigId() == null) {
            Long curtime = System.currentTimeMillis();
            quotaConfig.setCreateBy(userInfo.getUserId());
            quotaConfig.setCreateTime(curtime);
            quotaConfig.setUpdateBy(userInfo.getUserId());
            quotaConfig.setUpdateTime(curtime);
            quotaConfig.setConfigId(snowflakeUtil.createId());
            quotaConfig.setTenantId(userInfo.getTenantId());
            count = leaveQuotaConfigDo.save(quotaConfig);
        } else {
            quotaConfig.setConfigId(oldConfig.getConfigId());
            quotaConfig.setUpdateBy(userInfo.getUserId());
            quotaConfig.setUpdateTime(System.currentTimeMillis());
            count = leaveQuotaConfigDo.update(quotaConfig);
        }
        //删除旧的配额规则
        quotaGenRuleDo.delete(userInfo.getTenantId(), quotaConfig.getConfigId());
        //保存配额生成规则
        if (CollectionUtils.isNotEmpty(dto.getQuotaGroups())) {
            List<QuotaEmpGroupDto> empGroupList = dto.getQuotaGroups();
            Long curtime = System.currentTimeMillis();
            empGroupList.stream().filter(r -> (StringUtils.isNotBlank(r.getGroupExp()) && r.getQuotaVal() != null)).forEach(rd -> {
                QuotaGenRuleDo ruleDo = new QuotaGenRuleDo();
                ruleDo.setTenantId(userInfo.getTenantId());
                ruleDo.setCreateTime(curtime);
                ruleDo.setCreateBy(userInfo.getUserId());
                ruleDo.setUpdateTime(curtime);
                ruleDo.setUpdateBy(userInfo.getUserId());
                ruleDo.setLeaveTypeId(quotaConfig.getLeaveTypeId());
                ruleDo.setConfigId(quotaConfig.getConfigId());
                ruleDo.setQuotaVal(rd.getQuotaVal());
                ruleDo.setConditionExp(rd.getGroupExp());
                ruleDo.setConditionNote(rd.getGroupNote());
                ruleDo.setQuotaRuleId(snowflakeUtil.createId());
                quotaGenRuleDo.save(ruleDo);
            });
        }
        //兼容老逻辑
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(quotaConfig.getLeaveTypeId());
        if (leaveType != null) {
            LogRecordContext.putVariable("name", leaveType.getLeaveName());
            WaLeaveSettingExample example = new WaLeaveSettingExample();
            example.createCriteria().andBelongOrgidEqualTo(userInfo.getTenantId()).andLeaveTypeIdEqualTo(quotaConfig.getLeaveTypeId());
            waLeaveSettingMapper.deleteByExample(example);
            WaLeaveSetting waLeaveSetting = new WaLeaveSetting();
            waLeaveSetting.setLeaveTypeId(quotaConfig.getLeaveTypeId());
            waLeaveSetting.setBelongOrgid(userInfo.getTenantId());
            waLeaveSetting.setCrttime(System.currentTimeMillis() / 1000);
            waLeaveSetting.setCrtuser(userInfo.getUserId());
            waLeaveSetting.setLeaveType((short) 0);
            waLeaveSetting.setIsEmpShow(true);
            waLeaveSetting.setQuotaSettingName(leaveType.getLeaveName());
            waLeaveSetting.setCarryOverType(3);
            waLeaveSetting.setQuotaPeriodType(1);
            waLeaveSetting.setFreezingRules((short) 0);
            waLeaveSetting.setIsCountInProbation(false);
            waLeaveSetting.setIsTrialConvert(false);
            waLeaveSetting.setIfConvert(false);
            waLeaveSetting.setQuotaSortNo(0);
            if (dto.getDisCycleStart() != null) {
                String md = DateUtil.convertDateTimeToStr(dto.getDisCycleStart(), "MMdd", true);
                waLeaveSetting.setStartDate(Long.valueOf(md));
            }
            if (dto.getDisCycleEnd() != null) {
                String md1 = DateUtil.convertDateTimeToStr(dto.getDisCycleEnd(), "MMdd", true);
                waLeaveSetting.setEndDate(Long.valueOf(md1));
            }
            waConfigService.saveLeaveSetting(waLeaveSetting);
        }
        if (leaveType != null) {
            //员工分组，员工适用范围
            buildEmployeeGroupDto(quotaConfig.getConfigId(), dto.getGroupExp(), dto.getGroupNote());
        }
        return count;
    }

    @Transactional
    @Override
    public int saveOrUpdateFixedLeaveQuotaConfig(LeaveQuotaConfigDto dto) {
        UserInfo userInfo = this.getUserInfo();
        if (dto.getValidityDuration() != null && dto.getValidityPeriodType() == null) {
            dto.setValidityPeriodType(ValidityPeriodTypeEnum.RESTRICTION.getIndex());
        }
        String tenantId = userInfo.getTenantId();
        LeaveQuotaConfigDo oldConfig = leaveQuotaConfigDo.getById(tenantId, dto.getLeaveTypeId());
        int count;
        LeaveQuotaConfigDo quotaConfig = ObjectConverter.convert(dto, LeaveQuotaConfigDo.class);
        if (null == oldConfig || oldConfig.getConfigId() == null) {
            Long curtime = System.currentTimeMillis();
            quotaConfig.setCreateBy(Long.valueOf(userInfo.getUserid()));
            quotaConfig.setCreateTime(curtime);
            quotaConfig.setUpdateBy(Long.valueOf(userInfo.getUserid()));
            quotaConfig.setUpdateTime(curtime);
            quotaConfig.setConfigId(snowflakeUtil.createId());
            quotaConfig.setTenantId(userInfo.getTenantId());
            count = leaveQuotaConfigDo.save(quotaConfig);
        } else {
            quotaConfig.setConfigId(oldConfig.getConfigId());
            quotaConfig.setUpdateBy(Long.valueOf(userInfo.getUserid()));
            quotaConfig.setUpdateTime(System.currentTimeMillis());
            count = leaveQuotaConfigDo.update(quotaConfig);
        }
        //删除旧的配额规则
        quotaGenRuleDo.delete(userInfo.getTenantId(), quotaConfig.getConfigId());
        //保存配额生成规则
        if (CollectionUtils.isNotEmpty(dto.getQuotaGroups())) {
            List<QuotaEmpGroupDto> empGroupList = dto.getQuotaGroups();
            Long curtime = System.currentTimeMillis();
            empGroupList.stream().filter(r -> (StringUtils.isNotBlank(r.getGroupExp()) && r.getQuotaVal() != null)).forEach(rd -> {
                QuotaGenRuleDo ruleDo = new QuotaGenRuleDo();
                ruleDo.setTenantId(userInfo.getTenantId());
                ruleDo.setCreateTime(curtime);
                ruleDo.setCreateBy(userInfo.getUserId());
                ruleDo.setUpdateTime(curtime);
                ruleDo.setUpdateBy(userInfo.getUserId());
                ruleDo.setLeaveTypeId(quotaConfig.getLeaveTypeId());
                ruleDo.setConfigId(quotaConfig.getConfigId());
                ruleDo.setQuotaVal(rd.getQuotaVal());
                ruleDo.setConditionExp(rd.getGroupExp());
                ruleDo.setConditionNote(rd.getGroupNote());
                ruleDo.setQuotaRuleId(snowflakeUtil.createId());
                quotaGenRuleDo.save(ruleDo);
            });
        }
        //兼容老逻辑
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(quotaConfig.getLeaveTypeId());
        if (leaveType != null) {
            LogRecordContext.putVariable("name", leaveType.getLeaveName());
            WaLeaveSettingExample example = new WaLeaveSettingExample();
            example.createCriteria().andBelongOrgidEqualTo(userInfo.getTenantId()).andLeaveTypeIdEqualTo(quotaConfig.getLeaveTypeId());
            waLeaveSettingMapper.deleteByExample(example);
            WaLeaveSetting waLeaveSetting = new WaLeaveSetting();
            waLeaveSetting.setLeaveTypeId(quotaConfig.getLeaveTypeId());
            waLeaveSetting.setBelongOrgid(userInfo.getTenantId());
            waLeaveSetting.setCrttime(System.currentTimeMillis() / 1000);
            waLeaveSetting.setCrtuser(userInfo.getUserId());
            waLeaveSetting.setLeaveType((short) 0);
            waLeaveSetting.setIsEmpShow(true);
            waLeaveSetting.setQuotaSettingName(leaveType.getLeaveName());
            waLeaveSetting.setCarryOverType(3);
            waLeaveSetting.setQuotaPeriodType(1);
            waLeaveSetting.setFreezingRules((short) 0);
            waLeaveSetting.setIsCountInProbation(false);
            waLeaveSetting.setIsTrialConvert(false);
            waLeaveSetting.setIfConvert(false);
            waLeaveSetting.setQuotaSortNo(0);
            if (dto.getDisCycleStart() != null) {
                String md = DateUtil.convertDateTimeToStr(dto.getDisCycleStart(), "MMdd", true);
                waLeaveSetting.setStartDate(Long.valueOf(md));
            }
            if (dto.getDisCycleEnd() != null) {
                String md1 = DateUtil.convertDateTimeToStr(dto.getDisCycleEnd(), "MMdd", true);
                waLeaveSetting.setEndDate(Long.valueOf(md1));
            }
            waConfigService.saveLeaveSetting(waLeaveSetting);
        }
        return count;
    }

    @Transactional
    @CacheDelete({@CacheDeleteKey(value = "'attendance-leave-quota-config-' + #args[0].configId")})
    @Override
    public void saveOrUpdateTxLeaveQuotaConfig(LeaveQuotaConfigDto dto) {
        UserInfo userInfo = this.getUserInfo();
        if (dto.getValidityDuration() != null && dto.getValidityPeriodType() == null) {
            dto.setValidityPeriodType(ValidityPeriodTypeEnum.RESTRICTION.getIndex());
        }
        LeaveQuotaConfigDo oldConfig = leaveQuotaConfigDo.getConfigById(userInfo.getTenantId(), dto.getConfigId());
        LeaveQuotaConfigDo quotaConfig = ObjectConverter.convert(dto, LeaveQuotaConfigDo.class);
        if (null != dto.getI18nRuleName()) {
            quotaConfig.setI18nRuleName(FastjsonUtil.toJson(dto.getI18nRuleName()));
        }
        if (null == oldConfig || oldConfig.getConfigId() == null) {
            Long curtime = System.currentTimeMillis();
            quotaConfig.setCreateBy(userInfo.getUserId());
            quotaConfig.setCreateTime(curtime);
            quotaConfig.setUpdateBy(userInfo.getUserId());
            quotaConfig.setUpdateTime(curtime);
            quotaConfig.setConfigId(snowflakeUtil.createId());
            quotaConfig.setTenantId(userInfo.getTenantId());
            leaveQuotaConfigDo.save(quotaConfig);
        } else {
            quotaConfig.setConfigId(oldConfig.getConfigId());
            quotaConfig.setUpdateBy(userInfo.getUserId());
            quotaConfig.setUpdateTime(System.currentTimeMillis());
            leaveQuotaConfigDo.update(quotaConfig);
        }
        //删除旧的配额规则
        quotaGenRuleDo.delete(userInfo.getTenantId(), quotaConfig.getConfigId());
        //保存配额生成规则
        if (CollectionUtils.isNotEmpty(dto.getQuotaGroups())) {
            List<QuotaEmpGroupDto> empGroupList = dto.getQuotaGroups();
            Long curtime = System.currentTimeMillis();
            empGroupList.stream().filter(r -> (StringUtils.isNotBlank(r.getGroupExp()) && r.getQuotaVal() != null)).forEach(rd -> {
                QuotaGenRuleDo ruleDo = new QuotaGenRuleDo();
                ruleDo.setTenantId(userInfo.getTenantId());
                ruleDo.setCreateTime(curtime);
                ruleDo.setCreateBy(userInfo.getUserId());
                ruleDo.setUpdateTime(curtime);
                ruleDo.setUpdateBy(userInfo.getUserId());
                ruleDo.setLeaveTypeId(quotaConfig.getLeaveTypeId());
                ruleDo.setConfigId(quotaConfig.getConfigId());
                ruleDo.setQuotaVal(rd.getQuotaVal());
                ruleDo.setConditionExp(rd.getGroupExp());
                ruleDo.setConditionNote(rd.getGroupNote());
                ruleDo.setQuotaRuleId(snowflakeUtil.createId());
                quotaGenRuleDo.save(ruleDo);
            });
        }
        //兼容老逻辑
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(quotaConfig.getLeaveTypeId());
        if (leaveType != null) {
            WaLeaveSettingExample example = new WaLeaveSettingExample();
            example.createCriteria().andBelongOrgidEqualTo(userInfo.getTenantId()).andLeaveTypeIdEqualTo(quotaConfig.getLeaveTypeId());
            waLeaveSettingMapper.deleteByExample(example);
            WaLeaveSetting waLeaveSetting = new WaLeaveSetting();
            waLeaveSetting.setLeaveTypeId(quotaConfig.getLeaveTypeId());
            waLeaveSetting.setBelongOrgid(userInfo.getTenantId());
            waLeaveSetting.setCrttime(System.currentTimeMillis() / 1000);
            waLeaveSetting.setCrtuser(userInfo.getUserId());
            waLeaveSetting.setLeaveType((short) 0);
            waLeaveSetting.setIsEmpShow(true);
            waLeaveSetting.setQuotaSettingName(leaveType.getLeaveName());
            waLeaveSetting.setCarryOverType(3);
            waLeaveSetting.setQuotaPeriodType(1);
            waLeaveSetting.setFreezingRules((short) 0);
            waLeaveSetting.setIsCountInProbation(false);
            waLeaveSetting.setIsTrialConvert(false);
            waLeaveSetting.setIfConvert(false);
            waLeaveSetting.setQuotaSortNo(0);
            if (dto.getDisCycleStart() != null) {
                String md = DateUtil.convertDateTimeToStr(dto.getDisCycleStart(), "MMdd", true);
                waLeaveSetting.setStartDate(Long.valueOf(md));
            }
            if (dto.getDisCycleEnd() != null) {
                String md1 = DateUtil.convertDateTimeToStr(dto.getDisCycleEnd(), "MMdd", true);
                waLeaveSetting.setEndDate(Long.valueOf(md1));
            }
            waConfigService.saveLeaveSetting(waLeaveSetting);
        }
        if (leaveType != null) {
            //员工分组，员工适用范围
            buildEmployeeGroupDto(quotaConfig.getConfigId(), dto.getGroupExp(), dto.getGroupNote());
        }
    }

    /**
     * 保存员工分组表达式
     *
     * @param configId
     * @param groupExp
     * @param groupNote
     */
    public void buildEmployeeGroupDto(Long configId, String groupExp, String groupNote) {
        saveEmployeeGroup(String.valueOf(configId), groupExp, groupNote, WA_LEAVE_QUOTA_CONFIG);
    }

    public void saveEmployeeGroup(String businessKey, String groupExp, String groupNote, String groupType) {
        EmployeeGroupDto employeeGroupDto = new EmployeeGroupDto();
        employeeGroupDto.setGroupType(groupType);
        employeeGroupDto.setBusinessKey(businessKey);
        employeeGroupDto.setGroupName(String.format("%s-%s", groupType, employeeGroupDto.getBusinessKey()));
        employeeGroupDto.bulidExpression(groupExp, groupNote);
        employeeGroupService.saveOrUpdate(employeeGroupDto);
    }

    @Transactional
    @Override
    public void deleteLeaveQuotaConfig(Long configId) {
        UserInfo userInfo = this.getUserInfo();
        //删除员工分组表达式
        employeeGroupService.removeBusKey(String.valueOf(configId), WA_LEAVE_QUOTA_CONFIG);
        //删除配额生成规则
        quotaGenRuleDo.delete(userInfo.getTenantId(), configId);
        //删除额度规则
        leaveQuotaConfigDo.delete(userInfo.getTenantId(), configId);
    }

    @Override
    public LeaveQuotaConfigDto getQuotaConfigDetail(Integer leaveTypeId) {
        UserInfo userInfo = this.getUserInfo();
        LeaveQuotaConfigDto dto = null;
        LeaveQuotaConfigDo configDo = leaveQuotaConfigDo.getById(userInfo.getTenantId(), leaveTypeId);
        if (configDo != null) {
            dto = ObjectConverter.convert(configDo, LeaveQuotaConfigDto.class);
            List<QuotaGenRuleDo> ruleDoList = quotaGenRuleDo.getListByConfigId(userInfo.getTenantId(), configDo.getConfigId());
            if (CollectionUtils.isNotEmpty(ruleDoList)) {
                List<QuotaEmpGroupDto> groupDtos = ruleDoList.stream().map(r -> new QuotaEmpGroupDto("", r.getConditionExp(), r.getConditionNote(), r.getQuotaVal())).collect(Collectors.toList());
                dto.setQuotaGroups(groupDtos);
            }
        }
        return dto;
    }

    @Override
    @Cache(expire = 3600, key = "'attendance-leave-quota-config-' + #args[0]")
    public Optional<LeaveQuotaConfigDto> getTxLeaveQuotaConfigDetail(Long configId) {
        UserInfo userInfo = this.getUserInfo();
        String tenantId = userInfo.getTenantId();
        Optional<LeaveQuotaConfigDo> configOpt = Optional.ofNullable(leaveQuotaConfigDo.getConfigById(tenantId, configId));
        if (!configOpt.isPresent()) {
            return Optional.empty();
        }
        LeaveQuotaConfigDo leaveQuotaConfig = configOpt.get();
        var dto = ObjectConverter.convert(leaveQuotaConfig, LeaveQuotaConfigDto.class);
        if (StringUtils.isNotBlank(leaveQuotaConfig.getI18nRuleName())) {
            dto.setI18nRuleName(FastjsonUtil.toObject(leaveQuotaConfig.getI18nRuleName(), Map.class));
        } else if (StringUtils.isNotBlank(leaveQuotaConfig.getRuleName())) {
            Map<String, String> i18nName = new HashMap<>();
            i18nName.put("default", leaveQuotaConfig.getRuleName());
            dto.setI18nRuleName(i18nName);
        }
        if (StringUtils.isNotBlank(leaveQuotaConfig.getGroupExpCondition())) {
            dto.setGroupExpCondition(FastjsonUtil.toObject(leaveQuotaConfig.getGroupExpCondition(), ConditionTree.class));
        }
        Map<Long, EmployeeGroupDto> groupRuleMap = getGroupRuleMap(tenantId, Collections.singletonList(String.valueOf(configId)));
        if (!groupRuleMap.isEmpty()) {
            EmployeeGroupDto employeeGroupDto = groupRuleMap.get(configId);
            dto.setGroupExp(employeeGroupDto.getGroupExp());
            dto.setGroupNote(employeeGroupDto.getGroupNote());
        }
        List<QuotaGenRuleDo> ruleDoList = quotaGenRuleDo.getListByConfigId(tenantId, leaveQuotaConfig.getConfigId());
        if (CollectionUtils.isNotEmpty(ruleDoList)) {
            List<QuotaEmpGroupDto> groupDtos = ruleDoList.stream().map(r -> new QuotaEmpGroupDto("", r.getConditionExp(), r.getConditionNote(), r.getQuotaVal())).collect(Collectors.toList());
            dto.setQuotaGroups(groupDtos);
        }
        return Optional.of(dto);
    }

    @Override
    public List<LeaveQuotaRuleConfigDto> getQuotaConfigs(Integer leaveTypeId) {
        UserInfo userInfo = this.getUserInfo();
        List<LeaveQuotaConfigDo> list = leaveQuotaConfigDo.getByLeaveTypeId(userInfo.getTenantId(), leaveTypeId);
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> configIds = list.stream().map(l -> l.getConfigId().toString()).collect(Collectors.toList());
            Map<Long, EmployeeGroupDto> groupRuleMap = getGroupRuleMap(userInfo.getTenantId(), configIds);
            List<LeaveQuotaRuleConfigDto> result = ObjectConverter.convertList(list, LeaveQuotaRuleConfigDto.class);
            result.forEach(r -> {
                if (groupRuleMap.containsKey(r.getConfigId())) {
                    EmployeeGroupDto groupDto = groupRuleMap.get(r.getConfigId());
                    r.setGroupExp(groupDto.getGroupExp());
                    r.setGroupNote(groupDto.getGroupNote());
                }
            });
            Map<Long, String> quotaConfigStatus =
                    leaveQuotaConfigDo.loadStatus(list.stream().map(LeaveQuotaConfigDo::getConfigId)
                                    .collect(Collectors.toList())).stream()
                            .collect(Collectors.toMap(
                                    it -> Long.valueOf(String.valueOf(it.get("config_id"))),
                                    it -> (String) it.get("status")));
            result.forEach(it -> it.setStatus(quotaConfigStatus.get(it.getConfigId())));
            return result;
        }
        return new ArrayList<>();
    }

    @Override
    public Map<Long, EmployeeGroupDto> getGroupRuleMap(String tenantId, List<String> businessKeys) {
        List<EmployeeGroupDto> empGroups = employeeGroupService.getEmployeeGroups(businessKeys, WA_LEAVE_QUOTA_CONFIG, tenantId);
        if (CollectionUtils.isEmpty(empGroups)) {
            return new HashMap<>();
        }
        return empGroups.stream().collect(Collectors.toMap(g -> Long.valueOf(g.getBusinessKey()), Function.identity(), (v1, v2) -> v1));
    }

    @Override
    public List<EmpFixLeaveTypeDto> getEmpFixLeaveTypes(Long empid) {
        val belongOrgId = sessionService.getUserInfo().getTenantId();
        List<EmpFixLeaveTypeDto> list = waLeaveTypeDo.getEmpFixLeaveTypes(empid, belongOrgId);
        return list;
    }

    @Override
    public LeaveTypeDto getLeaveTypeByType(Integer leaveType) {
        String belongOrgId = sessionService.getUserInfo().getTenantId();
        WaLeaveTypeDo leaveTypeDo = waLeaveTypeDo.getLeaveTypeByType(belongOrgId, leaveType);
        return leaveTypeDo == null ? null : ObjectConverter.convert(leaveTypeDo, LeaveTypeDto.class);
    }

    @Override
    public List<KeyValueExt> getLeaveTypeOptions(Integer compensateType, Integer quotaType, Integer quotaRestrictionType) {
        UserInfo userInfo = getUserInfo();
        List<WaLeaveTypeDo> items = waLeaveTypeDo.getLeaveTypes(userInfo.getTenantId(), quotaRestrictionType, quotaType);
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        if (null != compensateType && compensateType == CompensateTypeEnum.COMPENSATORY_LEAVE.ordinal()) {
            items = items.stream().filter(i -> i.getLeaveType() == 3).collect(Collectors.toList());
        }
        List<KeyValueExt> list = new ArrayList<>();
        items.forEach(item -> list.add(new KeyValueExt(item.getLeaveName(), item.getLeaveTypeId(), item.getAcctTimeType())));
        return list;
    }

    @Override
    public void deleteLeaveTypeById(Integer leaveTypeId) {
        UserInfo userInfo = getUserInfo();
        WaLeaveTypeDo typeDo = waLeaveTypeDo.selectById(leaveTypeId);
        LogRecordContext.putVariable("name", typeDo.getLeaveName());
        waLeaveTypeDo.deleteLeaveTypeById(userInfo.getTenantId(), leaveTypeId);

    }

    /**
     * 假期类型删除时的校验
     *
     * @param leaveTypeId
     * @return 0 可以删除 1 已生成配额无法删除
     */
    @Override
    public Integer checkBeforeDeleteLeaveType(Integer leaveTypeId) {
        UserInfo userInfo = getUserInfo();
        int a = waEmpQuotaDo.getWaEmpQuotaCountByLeaveTypeId(userInfo.getTenantId(), leaveTypeId);
        if (a > 0) {
            return 1;
        }
        int b = waEmpQuotaDo.getWaEmpCompensatoryQuotaCountByLeaveTypeId(userInfo.getTenantId(), leaveTypeId);
        if (b > 0) {
            return 1;
        }
        return 0;
    }

    @Override
    public Result<Boolean> enable(LeaveEnabelReqDto dto) {
        UserInfo userInfo = getUserInfo();
        Integer leaveTypeId = dto.getLeaveTypeId();
        String leaveName = dto.getLeaveName();
        Integer leaveType = dto.getLeaveType();
        Integer acctTimeType = dto.getAcctTimeType();
        //考勤方案
        Optional<WaGroup> optional = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(dto.getWaGroupId()));
        if (optional.isPresent()) {
            WaGroup group = optional.get();
            group.setUpduser(userInfo.getUserId());
            group.setUpdtime(System.currentTimeMillis());
            //考勤方案关联的假期规则
            if (group.getLeaveTypeIds() != null) {
                Integer[] leaveTypeIdArr = (Integer[]) group.getLeaveTypeIds();
                List<Integer> allIds = new ArrayList<>(Arrays.asList(leaveTypeIdArr));
                WaLeaveTypeExample example = new WaLeaveTypeExample();
                example.createCriteria().andLeaveTypeIdIn(allIds).andBelongOrgidEqualTo(userInfo.getTenantId());
                List<WaLeaveType> leaveTypeList = waLeaveTypeMapper.selectByExample(example);
                //校验同一种假期类型不能有两种单位
                boolean flag = false;
                for (WaLeaveType waLeaveType : leaveTypeList) {
                    if (leaveType.equals(waLeaveType.getLeaveType()) && !acctTimeType.equals(waLeaveType.getAcctTimeType())) {
                        flag = true;
                        break;
                    }
                }
                if (flag) {
                    return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ACCTIMETYPE_EXIST, Boolean.FALSE);
                }
                if (leaveType == 3 && leaveTypeList.stream().anyMatch(l -> l.getLeaveType() == 3 && !l.getLeaveTypeId().equals(leaveTypeId) && allIds.contains(l.getLeaveTypeId()))) {
                    return ResponseWrap.wrapResult(AttendanceCodes.GROUP_EXIST_COMPENSATORY_LEAVE, Boolean.FALSE);
                }
                if (!allIds.contains(leaveTypeId)) {
                    allIds.add(leaveTypeId);
                    List<String> names = leaveTypeList.stream().map(WaLeaveType::getLeaveName).collect(Collectors.toList());
                    names.add(leaveName);
                    group.setLeaveTypeNames(String.join(",", names));
                    group.setLeaveTypeIds(allIds.toArray(new Integer[allIds.size()]));
                }
            } else {
                group.setLeaveTypeIds(new Integer[]{leaveTypeId});
                group.setLeaveTypeNames(leaveName);
            }
            waGroupMapper.updateByPrimaryKeySelective(group);
        }
        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }

    @Override
    public void unenable(LeaveEnabelReqDto dto) {
        UserInfo userInfo = getUserInfo();
        Integer leaveTypeId = dto.getLeaveTypeId();
        //考勤方案
        Optional<WaGroup> optional = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(dto.getWaGroupId()));
        if (optional.isPresent()) {
            WaGroup group = optional.get();
            if (group.getLeaveTypeIds() != null) {
                group.setUpduser(userInfo.getUserId());
                group.setUpdtime(System.currentTimeMillis());
                Integer[] leaveTypeIdArr = (Integer[]) group.getLeaveTypeIds();
                if (leaveTypeIdArr != null && leaveTypeIdArr.length > 0) {
                    List<Integer> leaveTypeIds = Arrays.stream(leaveTypeIdArr).collect(Collectors.toList());
                    if (leaveTypeIds.contains(leaveTypeId)) {
                        String[] leaveTypeNameArr = group.getLeaveTypeNames().split(",");
                        List<String> leaveTypeNames = Arrays.stream(leaveTypeNameArr).collect(Collectors.toList());
                        for (int i = 0; i < leaveTypeIdArr.length; i++) {
                            if (leaveTypeId.equals(leaveTypeIdArr[i])) {
                                leaveTypeNames.remove(leaveTypeNameArr[i]);
                            }
                        }
                        leaveTypeIds.remove(leaveTypeId);
                        if (CollectionUtils.isEmpty(leaveTypeIds) || CollectionUtils.isEmpty(leaveTypeNames)) {
                            group.setLeaveTypeIds(null);
                            group.setLeaveTypeNames(null);
                        } else {
                            group.setLeaveTypeIds(leaveTypeIds.toArray(new Integer[leaveTypeIds.size()]));
                            group.setLeaveTypeNames(String.join(",", leaveTypeNames));
                        }
                    }
                }
                waGroupMapper.updateByPrimaryKey(group);
            }
        }
    }

    @Override
    public Result<Boolean> dragSort(DragSort dragSort) {
        List<DragSortItem> items = dragSort.getItems();
        Optional<Short> optional = items.stream().map(DragSortItem::getSortNum).min(Short::compare);
        if (optional.isPresent()) {
            Short minSortNum = optional.get();
            int size = 0;
            List<WaLeaveTypeDo> list = new ArrayList<>();
            for (DragSortItem item : items) {
                WaLeaveTypeDo typeDo = new WaLeaveTypeDo();
                typeDo.setLeaveTypeId(item.getId());
                int sortNum = minSortNum + size;
                typeDo.setOrders(sortNum);
                list.add(typeDo);
                size++;
            }
            waLeaveTypeDo.updateBatch(list);
        }
        return Result.ok(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> dragSortQuotaRule(DragSortDto dragSort) {
        List<DragSortItemDto> items = dragSort.getItems();
        Optional<Short> optional = items.stream().map(DragSortItemDto::getSortNum).min(Short::compare);
        if (optional.isPresent()) {
            Short minSortNum = optional.get();
            int size = 0;
            List<LeaveQuotaConfigDo> list = new ArrayList<>();
            for (DragSortItemDto item : items) {
                LeaveQuotaConfigDo quotaRule = new LeaveQuotaConfigDo();
                quotaRule.setConfigId((Long) item.getId());
                int sortNum = minSortNum + size;
                quotaRule.setSort(sortNum);
                list.add(quotaRule);
                size++;
            }
            leaveQuotaConfigDo.updateBatch(list);
        }
        return Result.ok(Boolean.TRUE);
    }

    @Override
    public void saveLeaveTypeRmk(WaLeaveType waLeaveType) {
        waLeaveTypeMapper.updateByPrimaryKeySelective(waLeaveType);
    }

    @Override
    public String getLeaveTypeRmk(Integer leaveTypeId) {
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveTypeId);
        return waLeaveType.getRmk();
    }

    @Override
    public List<LeaveQuotaRuleConfigDto> getConfigListByIds(List<Integer> leaveTypeIds) {
        UserInfo userInfo = sessionService.getUserInfo();
        String tenantId = userInfo.getTenantId();
        List<LeaveQuotaConfigDo> configDoList = leaveQuotaConfigDo.getConfigListByIds(tenantId, leaveTypeIds);
        if (CollectionUtils.isEmpty(configDoList)) {
            return new ArrayList<>();
        }
        List<String> configIds = configDoList.stream().map(c -> String.valueOf(c.getConfigId())).collect(Collectors.toList());
        Map<Long, EmployeeGroupDto> groupRuleMap = getGroupRuleMap(tenantId, configIds);
        List<LeaveQuotaRuleConfigDto> rules = ObjectConverter.convertList(configDoList, LeaveQuotaRuleConfigDto.class);
        rules.forEach(r -> {
            if (!groupRuleMap.isEmpty() && groupRuleMap.containsKey(r.getConfigId())) {
                EmployeeGroupDto employeeGroupDto = groupRuleMap.get(r.getConfigId());
                r.setGroupExp(employeeGroupDto.getGroupExp());
                r.setGroupNote(employeeGroupDto.getGroupNote());
            }
        });
        return rules;
    }

    @Override
    public List<LeaveQuotaRuleConfigDto> listByTenantId() {
        List<LeaveQuotaConfigDo> doList = leaveQuotaConfigDo.listByTenantId(UserContext.getTenantId());
        return ObjectConverter.convertList(doList, LeaveQuotaRuleConfigDto.class);
    }

    @Override
    public boolean checkLeaveQuotaRuleName(Integer leaveTypeId, Long configId, String ruleName) {
        UserInfo userInfo = this.getUserInfo();
        String tenantId = userInfo.getTenantId();
        return CollectionUtils.isNotEmpty(leaveQuotaConfigDo.getLeaveQuotaConfigs(tenantId, leaveTypeId, configId, ruleName));
    }

    @Override
    public void enableQuotaConfig(Long quotaConfigId) {
        leaveQuotaConfigDo.enableQuotaConfig(quotaConfigId);
    }

    @Override
    public void disableQuotaConfig(Long quotaConfigId) {
        leaveQuotaConfigDo.disableQuotaConfig(quotaConfigId);
    }
}