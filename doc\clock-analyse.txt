打卡归属分析逻辑（多次卡、一次卡）：
1. 前一天取卡判断：
   1.1 前一天是工作日：
       前一天有休假且有加班（待确认）
       前一天有休假：
           前一天班班次跨夜：
              休假结束时间>=前一天班次下班时间：
                   打卡时间<=前一天班次下班时间
              休假结束时间<前一天班次下班时间：
                   正常进行如下判断（未考虑休假场景）
           前一天班次不跨夜：
              休假结束时间>=前一天班次下班时间：
                   无卡归属前一天
              休假结束时间<前一天班次下班时间：
                   正常进行如下判断（未考虑休假场景）
       前一天无休假：
           正常进行如下判断（未考虑休假场景）

       以下逻辑未考虑休假场景
       当日工作日：
           有无交集判断：
               前一天班次的最晚下班打卡结束时间 <= 当天班次最早上班打卡时间（无交集）
                  打卡时间<=前一天班次的最晚下班打卡结束时间
               前一天班次的最晚下班打卡结束时间 > 当天班次最早上班打卡时间（有交集）
                   差值判断：
                       前一天班次差值A：
                           有班后加班： 打卡时间-班后加班结束时间
                           无班后加班： 打卡时间-下班时间
                       当天班次差值B：
                           有班前加班：打卡时间-班前加班开始时间
                           无班前加班：打卡时间 - 上班时间
                   A < B
       当日非工作日：
           有无交集判断：
               当天有加班单：
                   前一天的最晚下班打卡结束时间 <= 当天加班单开始时间-6（无交集）
                       打卡时间<=前一天班次的最晚下班打卡结束时间
                   前一天的最晚下班打卡结束时间 > 当天加班单开始时间-6（有交集）
                       差值判断：
                           前一天班次差值A：
                               有班后加班： 打卡时间-班后加班结束时间
                               无班后加班： 打卡时间-下班时间
                           当天班次差值B：
                               有加班：打卡时间-班前加班开始时间
                               无加班：
                                   打卡时间<=前一天班次的最晚下班打卡结束时间
                           A < B
               当天无加班单：
                   打卡时间<=(前一天班次的最晚下班打卡结束时间)
   1.2 前一天是非工作日：
        无加班：无卡归属前一天
        有加班：
           当日工作日：
               交集判断：
                   前一天加班结束时间+6 <= 当天班次最早上班打卡时间（无）
                       打卡时间<=(前一天加班结束时间+6)
                   前一天加班结束时间+6 > 当天班次最早上班打卡时间（有）
                       差值判断：
                           前一天班次差值A： 打卡时间-前一天加班结束时间
                           当天班次差值B：
                                有加班：打卡时间-班前加班开始时间
                                无加班： 打卡时间<=当天班次上班时间
                            A < B
           当日非工作日：
               当天无加班： 打卡时间<=(前一天加班结束时间+6)
               当天有加班：
                    交集判断：
                       (前一天加班结束时间+6) <= （当天加班开始时间-6）（无）
                           打卡时间<=(前一天加班结束时间+6)
                       (前一天加班结束时间+6) > （当天加班开始时间-6）（有）
                           差值判断：
                               前一天班次差值A： 打卡时间-前一天加班结束时间
                               当天班次差值B：打卡时间-当天加班开始时间
                                A < B
2. 当日取卡判断：
   2.1 当天工作日：
       次日工作日：
           有无交集判断：
               当天最晚下班打卡时间<=次日最早上班打卡时间（无）
                   打卡时间<=当天最晚下班打卡时间
               当天最晚下班打卡时间>次日最早上班打卡时间(有)
                   差值判断：
                       当天班次差值A：
                           有班后加班： 打卡时间-班后加班结束时间
                           无班后加班： 打卡时间-下班时间
                       次日班次差值B：
                           有班前加班：打卡时间-班前加班开始时间
                           无班前加班：打卡时间 - 上班时间
                   A < B
       次日非工作日：
           次日有无加班
               次日无加班：打卡时间<=当天最晚下班打卡时间
               次日有加班：
                   有无交集判断：
                       当天最晚下班打卡时间<=(次日加班开始时间-6)（无）
                           打卡时间<=当天最晚下班打卡时间
                       当天最晚下班打卡时间>(次日加班开始时间-6)（有）
                           差值判断：
                               当天班次差值A：
                                   有班后加班： 打卡时间-班后加班结束时间
                                   无班后加班： 打卡时间-下班时间
                               次日班次差值B: 打卡时间-加班开始时间
                               A < B

   2.2 当天非工作日：
         当天无加班：
           次日工作日：打卡时间 < 次日最早上班打卡时间
           次日非工作日：
               次日无加班：全部归属当日
               次日有加班：打卡时间 < (次日加班开始时间-6)
         当天有加班：
           次日工作日:
               交集判断：
                   （当天加班结束时间+6）<= 次日最早上班打卡时间（无）：
                          全部归属当日
                   （当天加班结束时间+6）> 次日最早上班打卡时间（有）：
                           差值判断：
                               当天班次差值A： 打卡时间-加班结束时间
                               次日班次差值B:
                                   有班前加班：打卡时间-次日班前加班开始时间
                                   无班前加班：打卡时间 - 上班时间
                               A < B

           次日非工作日：
               次日有无加班：
                   无：全部归属当日
                   有：
                      交集判断：
                       （当天加班结束时间+6）<= （次日加班开始时间-6）（无）：
                           全部归属当日
                       （当天加班结束时间+6）> （次日加班开始时间-6）（有）：
                           差值判断：
                               当天班次差值A： 打卡时间-加班结束时间
                               次日班次差值B: 打卡时间-加班开始时间
                               A < B
3. 次日取卡判断
   其余卡全部归属次日

   交集：差值相等时，优先归属前一个班次
