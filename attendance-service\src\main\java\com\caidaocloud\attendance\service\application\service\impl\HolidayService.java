package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.service.application.service.IHolidayService;
import com.caidaocloud.attendance.service.domain.entity.WaCalendarGroupDo;
import com.caidaocloud.attendance.service.domain.entity.WaWorktimeDo;
import com.caidaocloud.attendance.service.interfaces.dto.HolidayGroupDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class HolidayService implements IHolidayService {
    @Autowired
    private WaCalendarGroupDo waCalendarGroupDo;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaWorktimeDo waWorktimeDo;
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private WaHolidayCalendarMapper waHolidayCalendarMapper;
    @Autowired
    private WaLeaveCalendarDetailMapper waLeaveCalendarDetailMapper;
    @Autowired
    private WaCalendarGroupMapper waCalendarGroupMapper;
    @Autowired
    private WaCalendarGroupRelMapper waCalendarGroupRelMapper;
    @Autowired
    private WaMapper waMapper;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Override
    public AttendancePageResult<HolidayGroupDto> getCalendarGroupPageList(PageBean pageBean) {
        UserInfo userInfo = getUserInfo();
        PageList<WaCalendarGroupDo> pageList = waCalendarGroupDo.getCalendarGroupPageList(pageBean, userInfo.getTenantId());
        if (CollectionUtils.isNotEmpty(pageList)) {
            pageList.forEach(item -> {
                item.setGroupName(LangParseUtil.getI18nLanguage(item.getI18nGroupName(), item.getGroupName()));
            });
            List<HolidayGroupDto> list = ObjectConverter.convertList(pageList, HolidayGroupDto.class);
            return new AttendancePageResult<>(list, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        }
        return new AttendancePageResult<>(new ArrayList<>(), pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
    }

    @Override
    public List<WaWorktimeDo> getWorktimeListByCalendarGroupId(String belongOrgId, Integer calendarGroupId) {
        return waWorktimeDo.getWorktimeListByCalendarGroupId(belongOrgId, calendarGroupId);
    }

    @Transactional
    @Override
    public Result<Boolean> deleteHolidayGroupById(Integer id) throws Exception {
        UserInfo userInfo = getUserInfo();
        List<WaWorktimeDo> workTimeDoList = waWorktimeDo.getWorktimeListByCalendarGroupId(userInfo.getTenantId(), id);
        if (CollectionUtils.isNotEmpty(workTimeDoList)) {
            //return Result.fail("存在日历已使用，不允许删除");
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_NOT_ALLOW, Boolean.FALSE);
        }
        waCalendarGroupDo.delCalendarGroupRelByGroupId(id);
        this.delCalendarGroupById(id, userInfo.getTenantId());
        return Result.ok(true);
    }

    @Transactional
    public int delCalendarGroupById(Integer calendarGroupId, String belongOrgId) {
        WaCalendarGroupExample exx = new WaCalendarGroupExample();
        exx.createCriteria().andBelongOrgidEqualTo(belongOrgId).andCalendarGroupIdEqualTo(calendarGroupId);
        return waCalendarGroupMapper.deleteByExample(exx);
    }

    @Override
    public int getCalendarGroupCountByName(String belongOrgId, Integer excludeCalendarGroupId, String groupName) {
        return waWorktimeDo.getCalendarGroupCountByName(belongOrgId, excludeCalendarGroupId, groupName);
    }

    @Transactional
    @Override
    public int saveOrUpdateOtherDateType(WaHolidayCalendar holiday, String belongId, Long userId) {
        int row = 0;
        if (holiday.getHolidayCalendarId() == null) {
            holiday.setBelongOrgid(belongId);
            holiday.setCrttime(DateUtil.getCurrentTime(true));
            holiday.setCrtuser(userId);
            holiday.setUpdtime(DateUtil.getCurrentTime(true));
            holiday.setUpduser(userId);
            row = waHolidayCalendarMapper.insertSelective(holiday);
        } else {
            holiday.setUpdtime(DateUtil.getCurrentTime(true));
            holiday.setUpduser(userId);
            row = waHolidayCalendarMapper.updateByPrimaryKeySelective(holiday);
        }
        return row;
    }

    @Override
    public int checkName(String belongOrgId, String name, Integer id) {
        WaHolidayCalendarExample example = new WaHolidayCalendarExample();
        WaHolidayCalendarExample.Criteria criteria = example.createCriteria();
        criteria.andCalendarNameEqualTo(name).andBelongOrgidEqualTo(belongOrgId);
        if (id != null) {
            criteria.andHolidayCalendarIdNotEqualTo(id);
        }
        return waHolidayCalendarMapper.countByExample(example);
    }

    @Override
    public List getOtherDateList(String belongOrgId) {
        WaHolidayCalendarExample example = new WaHolidayCalendarExample();
        example.createCriteria().andBelongOrgidEqualTo(belongOrgId);
        return waHolidayCalendarMapper.selectByExample(example);
    }

    @Override
    public WaHolidayCalendar getHolidayCalendarInfo(Integer holidayCalendarId, String belongOrgId) {
        return waHolidayCalendarMapper.selectByPrimaryKey(holidayCalendarId);
    }

    @Override
    public int getWaLeaveCalendarDetailCount(Integer holidayCalendarId) {
        WaLeaveCalendarDetailExample example = new WaLeaveCalendarDetailExample();
        example.createCriteria().andHolidayCalendarEqualTo(holidayCalendarId);
        return waLeaveCalendarDetailMapper.countByExample(example);
    }

    @Override
    @Transactional
    public int delSetDateTypeById(Integer holidayCalendarId, String belongOrgId) {
        WaHolidayCalendarExample exx = new WaHolidayCalendarExample();
        exx.createCriteria().andBelongOrgidEqualTo(belongOrgId).andHolidayCalendarIdEqualTo(holidayCalendarId);
        return waHolidayCalendarMapper.deleteByExample(exx);
    }

    @Override
    @Transactional
    public int saveOtherDateDetailType(WaLeaveCalendarDetail holiday, String belongOrgId, Long userId) throws Exception {
        // 新增或修改
        int row = 0;
        if (holiday.getCalendarDetailId() == null) {
            row = waLeaveCalendarDetailMapper.insertSelective(holiday);
        } else {
            row = waLeaveCalendarDetailMapper.updateByPrimaryKeySelective(holiday);
        }
        if (holiday.getDateType() != null) {
            if (holiday.getDateType() == 1) {
                waMapper.updateReplaceShiftIsNUll(holiday.getCalendarDetailId());
            } else {
                waMapper.updateSpecDateIsNUll(holiday.getCalendarDetailId());
            }
        }
        waConfigService.genSpecWorkCalendar(holiday);
        return row;
    }

    @Override
    public List<Map> getOtherDateTypeDetailList(PageBean pageBean, Integer holidayCalendarId) {
        Map params = new HashMap();
        params.put("holidayCalendarId", holidayCalendarId);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        List<Map> list = waMapper.getOtherDateTypeDetailList(pageBounds, params);
        list.forEach(l -> {
            l.put("dateTypeName", DateTypeEnum.getName((Integer) l.get("dateType")));
            if (null != l.get("i18nShiftDefName")) {
                String i18n = LangParseUtil.getI18nLanguage(l.get("i18nShiftDefName").toString(), null);
                if (com.caidaocloud.util.StringUtil.isNotBlank(i18n)) {
                    l.put("replaceShiftName", i18n);
                }
            }
        });
        return list;
    }

    @Override
    public WaLeaveCalendarDetail getLeaveDetailByInfo(Integer detailId, String belongOrgId) {
        return waLeaveCalendarDetailMapper.selectByPrimaryKey(detailId);
    }

    @Override
    @Transactional
    public int delSetDateDetailById(Integer calendarDetailId, String belongOrgId) {
        return waLeaveCalendarDetailMapper.deleteByPrimaryKey(calendarDetailId);
    }

    @Override
    public List getDateGroupList(String belongOrgId) {
        WaCalendarGroupExample example = new WaCalendarGroupExample();
        example.createCriteria().andBelongOrgidEqualTo(belongOrgId);
        return waCalendarGroupMapper.selectByExample(example);
    }

    @Override
    @Transactional
    public int saveCalendarGroup(WaCalendarGroup group, String belongOrgId, Long userId) {
        int row = 0;
        if (group.getCalendarGroupId() == null) {
            group.setBelongOrgid(belongOrgId);
            group.setCrttime(DateUtil.getCurrentTime(true));
            group.setCrtuser(userId);
            row = waCalendarGroupMapper.insertSelective(group);
        } else {
            row = waCalendarGroupMapper.updateByPrimaryKeySelective(group);
        }
        return row;
    }

    @Override
    @Transactional
    public int delCalendarGroupRelById(Integer groupRelId) {
        WaCalendarGroupRelExample exx = new WaCalendarGroupRelExample();
        exx.createCriteria().andGroupRelEqualTo(groupRelId);
        return waCalendarGroupRelMapper.deleteByExample(exx);
    }

    @Override
    @Transactional
    public int saveGroupRel(WaCalendarGroupRel group, String belongOrgId, Long userId) {
        int row = 0;
        if (group.getGroupRel() == null) {
            group.setCrttime(DateUtil.getCurrentTime(true));
            group.setCrtuser(userId);
            row = waCalendarGroupRelMapper.insertSelective(group);
        } else {
            row = waCalendarGroupRelMapper.updateByPrimaryKeySelective(group);
        }
        return row;
    }

    @Override
    public WaCalendarGroup getCalendarGroupById(Integer id, String belongOrgId) {
        return waCalendarGroupMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<Map> getDateGroupReList(PageBean pageBean, Integer calendarGroupId, Integer groupRelId) {
        Map params = new HashMap();
        params.put("calendarGroupId", calendarGroupId);
        params.put("groupRelId", groupRelId);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        return waCalendarGroupRelMapper.getDateGroupRelList(pageBounds, params);
    }

    @Override
    public WaCalendarGroupRel getCalendarGroupRelById(Integer id, String belongOrgId) {
        return waCalendarGroupRelMapper.selectByPrimaryKey(id);
    }
}