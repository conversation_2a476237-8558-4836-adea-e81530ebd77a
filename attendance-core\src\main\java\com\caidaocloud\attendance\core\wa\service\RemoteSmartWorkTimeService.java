package com.caidaocloud.attendance.core.wa.service;


import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.dto.WorkTimeCalendar;

import java.util.List;

public interface RemoteSmartWorkTimeService {
    /**
     * 返回员工指定日期的排班信息
     *
     * @param
     * @return
     */
    WaShiftDef getEmployeeShiftByDate(String belongid, Long empid, Long workDate);

    /**
     * 签到打卡专用 返回员工指定日期的排班信息
     *
     * @param
     * @return
     */
    WaShiftDef getEmpRegisterShiftByDate(String belongid, Long empid, Long workDate);

    /**
     * 返回员工指定月的排班信息
     *
     * @param
     * @return
     */
    List<WorkTimeCalendar> getEmpWorkTimeCalendarByMonth(Long empid, Integer ym);

    List<WorkTimeCalendar> getEmpWorkTimeCalendarByYm(Long empid, Integer ym);


    /**
     * 返回指定的区间内的员工排班数据
     *
     * @param belongid
     * @param empids    可选返回指定的员工数据
     * @param startDate
     * @param endDate
     * @return
     */
    List<WorkTimeCalendar> getEmployeeShiftList(String belongid, List<Long> empids, Long startDate, Long endDate);

    /**
     * 检查租户是否开启只能排班
     *
     * @param belongid
     * @return
     */
    boolean validateEnableSmartShiftPlan(String belongid);
}
