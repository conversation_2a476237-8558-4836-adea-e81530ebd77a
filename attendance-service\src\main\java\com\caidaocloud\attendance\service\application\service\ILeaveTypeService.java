package com.caidaocloud.attendance.service.application.service;

import com.caidao1.wa.mybatis.model.WaLeaveType;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.common.KeyValueExt;
import com.caidaocloud.attendance.service.interfaces.dto.leave.LeaveEnabelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.LeaveTypeInfoDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.LeaveQuotaRuleConfigDto;
import com.caidaocloud.dto.DragSort;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 假期类型服务
 *
 * <AUTHOR>
 * @Date 2021/3/4
 */
public interface ILeaveTypeService {
    LeaveTypeDto getLeaveTypeById(Integer leaveTypeId);

    List<LeaveTypeDto> getLeaveTypeListByIds(List<Integer> leaveTypeIds);

    Result<Boolean> saveOrUpdateLeaveType(LeaveTypeDto leaveTypeSetInfoDto) throws Exception;

    AttendancePageResult<LeaveTypeDto> getLeaveTypeList(LeaveTypeReqDto reqDto) throws Exception;

    List<LeaveTypeInfoDto> getLeaveTypes(UserInfo userInfo, Integer groupId, Integer quotaRestrictionType, Integer quotaType);

    List<LeaveTypeInfoDto> getLeaveTypes(Integer groupId, Integer quotaRestrictionType, Integer quotaType);

    void deleteLeaveTypeByIds(List<Integer> ids);

    int saveOrUpdateLeaveQuotaConfig(LeaveQuotaConfigDto dto);

    int saveOrUpdateFixedLeaveQuotaConfig(LeaveQuotaConfigDto dto);

    void saveOrUpdateTxLeaveQuotaConfig(LeaveQuotaConfigDto dto);

    void deleteLeaveQuotaConfig(Long configId);

    LeaveQuotaConfigDto getQuotaConfigDetail(Integer leaveTypeId);

    Optional<LeaveQuotaConfigDto> getTxLeaveQuotaConfigDetail(Long configId);

    List<LeaveQuotaRuleConfigDto> getQuotaConfigs(Integer leaveTypeId);

    List<EmpFixLeaveTypeDto> getEmpFixLeaveTypes(Long empid);

    LeaveTypeDto getLeaveTypeByType(Integer leaveType);

    List<KeyValueExt> getLeaveTypeOptions(Integer compensateType, Integer quotaType, Integer quotaRestrictionType);

    void deleteLeaveTypeById(Integer leaveTypeId);

    Integer checkBeforeDeleteLeaveType(Integer leaveTypeId);

    Result<Boolean> enable(LeaveEnabelReqDto dto);

    void unenable(LeaveEnabelReqDto dto);

    Result<Boolean> dragSort(DragSort dragSort);

    Map<Long, EmployeeGroupDto> getGroupRuleMap(String tenantId, List<String> businessKeys);

    Result<Boolean> dragSortQuotaRule(DragSortDto dragSort);

    void saveLeaveTypeRmk(WaLeaveType waLeaveType);

    String getLeaveTypeRmk(Integer leaveTypeId);

    List<LeaveQuotaRuleConfigDto> getConfigListByIds(List<Integer> leaveTypeIds);

    List<LeaveQuotaRuleConfigDto> listByTenantId();

    boolean checkLeaveQuotaRuleName(Integer leaveTypeId, Long configId, String ruleName);

    void enableQuotaConfig(Long quotaConfigId);

    void disableQuotaConfig(Long quotaConfigId);
}
