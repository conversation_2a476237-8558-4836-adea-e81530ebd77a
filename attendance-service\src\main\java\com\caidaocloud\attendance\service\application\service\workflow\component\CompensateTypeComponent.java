package com.caidaocloud.attendance.service.application.service.workflow.component;

import com.caidaocloud.attendance.service.application.enums.CompensateTypeEnum;
import com.caidaocloud.workflow.annotation.WfComponentValueEnumDef;
import com.caidaocloud.workflow.dto.WfComponentValueDto;
import com.google.common.collect.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 注册补偿类型枚举清单
 */
@Component
public class CompensateTypeComponent extends WfComponentValueEnumDef {
    @NotNull
    @Override
    public List<WfComponentValueDto> enumList() {
//        return Lists.newArrayList(
//                new WfComponentValueDto(CompensateTypeEnum.WORK_FREE.getDesc(), String.valueOf(CompensateTypeEnum.WORK_FREE.ordinal())),
//                new WfComponentValueDto(CompensateTypeEnum.WORK_PAID.getDesc(), String.valueOf(CompensateTypeEnum.WORK_PAID.ordinal())),
//                new WfComponentValueDto(CompensateTypeEnum.COMPENSATORY_LEAVE.getDesc(), String.valueOf(CompensateTypeEnum.COMPENSATORY_LEAVE.ordinal())),
//                new WfComponentValueDto(CompensateTypeEnum.PAY_IN_ADVANCE.getDesc(), String.valueOf(CompensateTypeEnum.PAY_IN_ADVANCE.ordinal())),
//                new WfComponentValueDto(CompensateTypeEnum.CHOICE.getDesc(), String.valueOf(CompensateTypeEnum.CHOICE.ordinal())));

        return Lists.newArrayList(
                new WfComponentValueDto(CompensateTypeEnum.WORK_PAID.getDesc(), String.valueOf(CompensateTypeEnum.WORK_PAID.ordinal())),
                new WfComponentValueDto(CompensateTypeEnum.COMPENSATORY_LEAVE.getDesc(), String.valueOf(CompensateTypeEnum.COMPENSATORY_LEAVE.ordinal())));
    }
}
