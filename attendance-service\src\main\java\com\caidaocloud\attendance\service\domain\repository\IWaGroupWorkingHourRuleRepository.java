package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaGroupWorkingHourRuleDo;

import java.util.List;

public interface IWaGroupWorkingHourRuleRepository {
    void updateById(WaGroupWorkingHourRuleDo updateData);

    void insert(WaGroupWorkingHourRuleDo saveData);

    WaGroupWorkingHourRuleDo getById(Long id);

    void deleteById(Long id);

    List<WaGroupWorkingHourRuleDo> selectListByWaGroupIds(String tenantId, List<Integer> waGroupIds);
}
