package com.caidaocloud.attendance.service.application.dto.clock;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiWorkTimeBaseDto;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import lombok.Data;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class ClockAnalyseCalDto {
    /**
     * 查询次日最早上班打卡时间
     *
     * @param empId
     * @param clockDate
     * @param empShiftInfo
     * @return
     */
    public static long getNextDatFirstOnDutyStartTime(Long empId, Long clockDate, Map<String, WaShiftDo> empShiftInfo) {
        Long nextDay = DateUtil.addDate(clockDate * 1000, 1);
        WaShiftDo nextDayShiftDo = empShiftInfo.get(empId + "_" + nextDay);
        if (null == nextDayShiftDo) {
            return 0L;
        }
        WaShiftDef firstShiftDef = nextDayShiftDo.doGetFirstShiftDef();
        List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(firstShiftDef);
        multiWorkTimeList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime));
        MultiWorkTimeBaseDto firstWorkTimeDto = multiWorkTimeList.get(0);
        if (null == firstWorkTimeDto.getOnDutyStartTime()) {
            return 0L;
        }
        return nextDay + firstWorkTimeDto.doGetRealOnDutyStartTime() * 60;
    }

    public static Long doGetShiftMaxOtEndTime(Long empId,
                                              Long clockDate,
                                              ClockAnalyseDataCacheDto dataCacheDto,
                                              WaShiftDef clockDateShiftDef) {
        if (null == clockDateShiftDef) {
            return null;
        }
        Long maxOtEndTime;
        Integer clockTimeRange;
        if (null != (clockTimeRange = WaShiftDo.ifEnableAnalyseClockByOt(clockDateShiftDef))
                && null != (maxOtEndTime = dataCacheDto.doGetMaxOtEndTime(empId, clockDate))) {
            return maxOtEndTime + (clockTimeRange * 60 * 60);
        }
        return null;
    }

    public static Long doGetShiftMinOtStartTime(Long empId,
                                                Long clockDate,
                                                ClockAnalyseDataCacheDto dataCacheDto,
                                                WaShiftDef clockDateShiftDef) {
        if (null == clockDateShiftDef) {
            return null;
        }
        Long minOtStartTime;
        Integer clockTimeRange;
        if (null != (clockTimeRange = WaShiftDo.ifEnableAnalyseClockByOt(clockDateShiftDef))
                && null != (minOtStartTime = dataCacheDto.doGetMinOtStartTime(empId, clockDate))) {
            return minOtStartTime - (clockTimeRange * 60 * 60);
        }
        return null;
    }

    /**
     * 按时间范围过滤
     *
     * @param regList
     * @param endTime
     * @return
     */
    public static List<WaRegisterRecordDo> filterByTimeRange(List<WaRegisterRecordDo> regList, long endTime) {
        return regList.stream()
                .filter(record -> record.getRegDateTime() <= endTime)
                .collect(Collectors.toList());
    }
}
