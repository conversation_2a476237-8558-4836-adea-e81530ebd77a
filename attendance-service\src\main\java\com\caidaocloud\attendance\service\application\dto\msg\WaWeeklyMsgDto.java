package com.caidaocloud.attendance.service.application.dto.msg;

import lombok.Data;

/**
 * 周报报消息字段：排班工时（单位“小时”）、
 * 出勤时长（对应系统：实出勤时长，单位“小时”）、
 * 迟到时长（单位“分钟”）、早退时长（单位“分钟”）、
 * 缺卡次数（单位“次”）、旷工时长（单位“分钟”）、
 * 加班时长（单位“小时”）、休假次数（单位“次”）。
 * 保留1位小小数，“次数”的项目，保留整数
 */
@Data
public class WaWeeklyMsgDto {
    /**
     * 员工信息
     */
    private Long empid;

    /**
     * 周报日期
     */
    private long weeklyDate;

    /**
     * 日报汇总日期
     */
    private String summaryDate;

    /**
     * 排班工时
     */
    private String shiftWorkingHours;

    /**
     * tenantId
     */
    private String tenantId;

    /**
     * 出勤时长
     */
    private String checkHours;

    /**
     * 迟到时长
     */
    private String lateTime;

    /**
     * 早退时长
     */
    private String earlyLeaveHours;

    /**
     * 缺卡次数、补卡次数
     */
    private String checkMmissTimes;

    /**
     * 矿工时长
     */
    private String minerHours;

    /**
     * 加班时长
     */
    private String overtimeHours;

    /**
     * 休假次数
     */
    private String vacationCount;

    /**
     * 延迟时间
     */
    private Integer delay;
}
