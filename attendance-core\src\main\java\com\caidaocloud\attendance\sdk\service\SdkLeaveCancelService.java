package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.sdk.dto.SdkLeaveCancelDTO;
import com.caidaocloud.attendance.sdk.feign.ILeaveCancelFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 销假申请
 *
 * <AUTHOR>
 * @Date 2023/6/20
 */
@Slf4j
@Service
public class SdkLeaveCancelService {
    @Autowired
    private ILeaveCancelFeignClient leaveCancelFeignClient;

    /**
     * 销假申请
     *
     * @param dto
     * @return
     */
    public Result<?> apply(SdkLeaveCancelDTO dto) {
        return leaveCancelFeignClient.apply(dto);
    }

    /**
     * 获取销假时长
     *
     * @param dto
     * @return
     */
    public Result<?> getTime(SdkLeaveCancelDTO dto) {
        return leaveCancelFeignClient.getTime(dto);
    }
}
