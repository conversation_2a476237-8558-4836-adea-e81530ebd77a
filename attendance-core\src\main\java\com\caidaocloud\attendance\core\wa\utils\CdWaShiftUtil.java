package com.caidaocloud.attendance.core.wa.utils;

import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaWorktimeDetail;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.ShiftRestPeriods;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiWorkTimeBaseDto;
import com.caidaocloud.attendance.core.wa.dto.shift.RestPeriodDto;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.BeanUtils;

import java.util.*;

/**
 * 考勤班次工具类
 *
 * <AUTHOR>
 * @Date 2024/1/10
 */
public class CdWaShiftUtil {

    /**
     * 获取班次的多段上下班时间（兼容一段班）
     *
     * @param shiftDef
     * @return
     */
    public static List<MultiWorkTimeBaseDto> getAllMultiWorkTimeList(WaShiftDef shiftDef) {
        List<MultiWorkTimeBaseDto> multiWorkTimeList = getMultiWorkTimeList(shiftDef);
        if (CollectionUtils.isNotEmpty(multiWorkTimeList)) {
            return multiWorkTimeList;
        }
        MultiWorkTimeBaseDto workTimeBaseDto = new MultiWorkTimeBaseDto();
        workTimeBaseDto.setStartTime(shiftDef.getStartTime());
        workTimeBaseDto.setEndTime(shiftDef.getEndTime());
        workTimeBaseDto.setOnDutyStartTime(shiftDef.getOnDutyStartTime());
        workTimeBaseDto.setOnDutyEndTime(shiftDef.getOnDutyEndTime());
        workTimeBaseDto.setOffDutyStartTime(shiftDef.getOffDutyStartTime());
        workTimeBaseDto.setOffDutyEndTime(shiftDef.getOffDutyEndTime());
        boolean crossNightForShiftTime = CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType());
        workTimeBaseDto.setStartTimeBelong(Optional.ofNullable(shiftDef.getStartTimeBelong()).orElse(ShiftTimeBelongTypeEnum.TODAY.getIndex()));
        if (null != shiftDef.getEndTimeBelong()) {
            workTimeBaseDto.setEndTimeBelong(shiftDef.getEndTimeBelong());
        } else {
            workTimeBaseDto.setEndTimeBelong(crossNightForShiftTime ?
                    ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex() : ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
        workTimeBaseDto.setOnDutyStartTimeBelong(Optional.ofNullable(shiftDef.getOnDutyStartTimeBelong()).orElse(ShiftTimeBelongTypeEnum.TODAY.getIndex()));
        if (null != shiftDef.getOnDutyEndTimeBelong()) {
            workTimeBaseDto.setOnDutyEndTimeBelong(shiftDef.getOnDutyEndTimeBelong());
        } else {
            boolean crossNightForOndutyTime = null != shiftDef.getOnDutyStartTime()
                    && null != shiftDef.getOnDutyEndTime() && shiftDef.getOnDutyStartTime() >= shiftDef.getOnDutyEndTime();
            workTimeBaseDto.setOnDutyEndTimeBelong(crossNightForOndutyTime ?
                    ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex() : ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
        if (null != shiftDef.getOffDutyStartTimeBelong()) {
            workTimeBaseDto.setOffDutyStartTimeBelong(shiftDef.getOffDutyStartTimeBelong());
        } else {
            workTimeBaseDto.setOffDutyStartTimeBelong(crossNightForShiftTime ?
                    ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex() : ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
        if (null != shiftDef.getOffDutyEndTimeBelong()) {
            workTimeBaseDto.setOffDutyEndTimeBelong(shiftDef.getOffDutyEndTimeBelong());
        } else {
            boolean crossNightForOffDutyTime = null != shiftDef.getOffDutyStartTime()
                    && null != shiftDef.getOffDutyEndTime() && shiftDef.getOffDutyStartTime() >= shiftDef.getOffDutyEndTime();
            workTimeBaseDto.setOffDutyEndTimeBelong((crossNightForShiftTime || crossNightForOffDutyTime)
                    ? ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex()
                    : ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
        if (null != shiftDef.getIsNight()) {
            workTimeBaseDto.setIsNight(shiftDef.getIsNight());
        } else {
            workTimeBaseDto.setIsNight(ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shiftDef.getEndTimeBelong()));
        }
        workTimeBaseDto.setWorkTotalTime(shiftDef.getWorkTotalTime());
        return Lists.newArrayList(workTimeBaseDto);
    }

    /**
     * 获取班次的多段上下班时间
     *
     * @param shiftDef
     * @return
     */
    public static List<MultiWorkTimeBaseDto> getMultiWorkTimeList(WaShiftDef shiftDef) {
        if (null == shiftDef) {
            return null;
        }
        if (null != shiftDef.getMultiWorkTimes()) {
            PGobject pGobject = (PGobject) shiftDef.getMultiWorkTimes();
            List<MultiWorkTimeBaseDto> multiWorkTimeList = FastjsonUtil.toArrayList(pGobject.getValue(), MultiWorkTimeBaseDto.class);
            multiWorkTimeList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime));
            return multiWorkTimeList;
        }
        return null;
    }

    /**
     * 获取班次上下班时间（支持多段班）
     *
     * @param shiftDef
     * @return
     */
    public static WaShiftDef getShiftWorkTime(WaShiftDef shiftDef) {
        if (null == shiftDef) {
            return null;
        }
        List<MultiWorkTimeBaseDto> multiWorkTimeList = null;
        if (null != shiftDef.getMultiWorkTimes()) {
            PGobject pGobject = (PGobject) shiftDef.getMultiWorkTimes();
            multiWorkTimeList = FastjsonUtil.toArrayList(pGobject.getValue(), MultiWorkTimeBaseDto.class);
            multiWorkTimeList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime));
        }
        if (CollectionUtils.isNotEmpty(multiWorkTimeList) && multiWorkTimeList.size() > 1) {
            MultiWorkTimeBaseDto first = multiWorkTimeList.get(0);
            MultiWorkTimeBaseDto last = multiWorkTimeList.get(multiWorkTimeList.size() - 1);

            WaShiftDef newShiftDef = new WaShiftDef();
            BeanUtils.copyProperties(shiftDef, newShiftDef);
            newShiftDef.setStartTime(first.getStartTime());
            newShiftDef.setStartTimeBelong(first.getStartTimeBelong());
            newShiftDef.setEndTime(last.getEndTime());
            newShiftDef.setEndTimeBelong(last.getEndTimeBelong());
            newShiftDef.setOnDutyStartTime(first.getOnDutyStartTime());
            newShiftDef.setOnDutyEndTime(first.getOnDutyEndTime());
            newShiftDef.setOnDutyStartTimeBelong(first.getOnDutyStartTimeBelong());
            newShiftDef.setOnDutyEndTimeBelong(first.getOnDutyEndTimeBelong());
            newShiftDef.setOffDutyStartTime(last.getOffDutyStartTime());
            newShiftDef.setOffDutyEndTime(last.getOffDutyEndTime());
            newShiftDef.setOffDutyStartTimeBelong(last.getOffDutyStartTimeBelong());
            newShiftDef.setOffDutyEndTimeBelong(last.getOffDutyEndTimeBelong());
            return newShiftDef;
        } else {
            return shiftDef;
        }
    }

    /**
     * 获取班次工作休息时间（V2新版-支持次日&多段班）
     *
     * @param periodDto
     * @param shiftDef
     * @param dateType
     * @return
     */
    public static ShiftRestPeriods getShiftWorkRestTimeV2(RestPeriodDto periodDto,
                                                          EmpShiftInfo shiftDef,
                                                          Integer dateType) {
        List<MultiWorkTimeBaseDto> multiWorkTimeList = shiftDef.getMultiWorkTimeList();
        if (CollectionUtils.isNotEmpty(multiWorkTimeList)) {
            int restStart = Optional.ofNullable(periodDto.getNoonRestStart()).orElse(0);
            int restEnd = Optional.ofNullable(periodDto.getNoonRestEnd()).orElse(0);
            Integer shiftStart = Optional.ofNullable(shiftDef.getStartTime()).orElse(0);
            Integer shiftEnd = Optional.ofNullable(shiftDef.getEndTime()).orElse(0);

            boolean restTimeIsNight = ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(periodDto.getNoonRestStartBelong())
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(periodDto.getNoonRestEndBelong());
            boolean shiftIsNight = Optional.ofNullable(shiftDef.getIsNight()).orElse(Boolean.FALSE);

            if (restTimeIsNight) {
                // 休息时间跨夜
                restEnd += 24 * 60;
            } else if (shiftIsNight && restStart < shiftStart && restStart <= shiftEnd) {
                // 班次跨夜且休息时间在第二天
                restStart += 24 * 60;
                restEnd += 24 * 60;
            }
            ShiftRestPeriods restPeriods = new ShiftRestPeriods();
            restPeriods.setNoonRestStart(restStart);
            restPeriods.setNoonRestEnd(restEnd);
            return restPeriods;
        } else {
            return CdWaShiftUtil.getShiftWorkRestTime(periodDto.getNoonRestStart(),
                    periodDto.getNoonRestEnd(), shiftDef.getStartTime(), shiftDef.getEndTime(), dateType);
        }
    }

    /**
     * 获取班次工作休息时间（V2新版-支持次日&多段班）
     *
     * @param periodDto
     * @param shiftDef
     * @param dateType
     * @return
     */
    public static ShiftRestPeriods getShiftWorkRestTimeV2(RestPeriodDto periodDto,
                                                          WaShiftDef shiftDef,
                                                          Integer dateType) {
        List<MultiWorkTimeBaseDto> multiWorkTimeList = null;
        if (null != shiftDef.getMultiWorkTimes()) {
            PGobject pGobject = (PGobject) shiftDef.getMultiWorkTimes();
            multiWorkTimeList = FastjsonUtil.toArrayList(pGobject.getValue(), MultiWorkTimeBaseDto.class);
        }

        if (CollectionUtils.isNotEmpty(multiWorkTimeList)) {
            int restStart = Optional.ofNullable(periodDto.getNoonRestStart()).orElse(0);
            int restEnd = Optional.ofNullable(periodDto.getNoonRestEnd()).orElse(0);
            Integer shiftStart = Optional.ofNullable(shiftDef.getStartTime()).orElse(0);
            Integer shiftEnd = Optional.ofNullable(shiftDef.getEndTime()).orElse(0);

            boolean restTimeIsNight = ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(periodDto.getNoonRestStartBelong())
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(periodDto.getNoonRestEndBelong());
            boolean shiftIsNight = Optional.ofNullable(shiftDef.getIsNight()).orElse(Boolean.FALSE);

            if (restTimeIsNight) {
                // 休息时间跨夜
                restEnd += 24 * 60;
            } else if (shiftIsNight && restStart < shiftStart && restStart <= shiftEnd) {
                // 班次跨夜且休息时间在第二天
                restStart += 24 * 60;
                restEnd += 24 * 60;
            }
            ShiftRestPeriods restPeriods = new ShiftRestPeriods();
            restPeriods.setNoonRestStart(restStart);
            restPeriods.setNoonRestEnd(restEnd);
            return restPeriods;
        } else {
            return CdWaShiftUtil.getShiftWorkRestTime(periodDto.getNoonRestStart(),
                    periodDto.getNoonRestEnd(), shiftDef.getStartTime(), shiftDef.getEndTime(), dateType);
        }
    }

    /**
     * 获取班次工作休息时间，过期，建议使用V2新版CdWaShiftUtil#getShiftWorkRestTimeV2(RestPeriodDto, WaShiftDef, Integer)
     *
     * @param restStart
     * @param restEnd
     * @param shiftStart
     * @param shiftEnd
     * @param dateType
     * @return
     */
    @Deprecated
    public static ShiftRestPeriods getShiftWorkRestTime(Integer restStart, Integer restEnd,
                                                        Integer shiftStart, Integer shiftEnd,
                                                        Integer dateType) {
        restStart = Optional.ofNullable(restStart).orElse(0);
        restEnd = Optional.ofNullable(restEnd).orElse(0);
        shiftStart = Optional.ofNullable(shiftStart).orElse(0);
        shiftEnd = Optional.ofNullable(shiftEnd).orElse(0);

        if (dateType == 1 && restStart >= restEnd) {
            // 休息时间跨夜
            restEnd += 24 * 60;
        } else if (restStart > restEnd) {
            // 休息时间跨夜
            restEnd += 24 * 60;
        } else if (restStart < shiftStart && restStart <= shiftEnd) {
            // 班次跨夜且休息时间在第二天
            restStart += 24 * 60;
            restEnd += 24 * 60;
        }
        ShiftRestPeriods restPeriods = new ShiftRestPeriods();
        restPeriods.setNoonRestStart(restStart);
        restPeriods.setNoonRestEnd(restEnd);
        return restPeriods;
    }

    /**
     * 检查班次的下班打卡结束时间是否跨夜
     *
     * @param shiftDef
     * @param dateType
     * @return
     */
    public static boolean checkCrossNightForSignOffEndTime(WaShiftDef shiftDef, Integer dateType) {
        if (null == shiftDef) {
            return Boolean.FALSE;
        }
        // 跨夜班判断
        if (CdWaShiftUtil.checkCrossNightV2(shiftDef, dateType)) {
            return Boolean.TRUE;
        }
        // 不是跨夜班但打卡时间跨夜判断
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shiftDef.getOffDutyEndTimeBelong())) {
            return Boolean.TRUE;
        }
        // 兼容未设置次日的历史数据
        if (null == shiftDef.getOffDutyStartTime() || null == shiftDef.getOffDutyEndTime() || null == shiftDef.getOnDutyStartTime()) {
            return Boolean.FALSE;
        }
        if (shiftDef.getOffDutyStartTime() > shiftDef.getOffDutyEndTime()
                || shiftDef.getOffDutyEndTime() <= shiftDef.getOnDutyStartTime()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 班次跨夜判断（V2新版-支持次日&多段班）
     *
     * @param shiftDef
     * @param dateType
     * @return
     */
    public static boolean checkCrossNightV2(EmpShiftInfo shiftDef, Integer dateType) {
        if (null == shiftDef) {
            return Boolean.FALSE;
        }
        List<MultiWorkTimeBaseDto> multiWorkTimeList = shiftDef.getMultiWorkTimeList();
        if (CollectionUtils.isNotEmpty(multiWorkTimeList)) {
            return Optional.ofNullable(shiftDef.getIsNight()).orElse(Boolean.FALSE);
        } else {
            if (null != shiftDef.getStartTimeBelong() && null != shiftDef.getEndTimeBelong()) {
                return ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(shiftDef.getStartTimeBelong())
                        && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shiftDef.getEndTimeBelong());
            }
            return CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), dateType);
        }
    }

    /**
     * 班次跨夜判断（V2新版-支持次日&多段班）
     *
     * @param shiftDef
     * @param dateType
     * @return
     */
    public static boolean checkCrossNightV2(WaShiftDef shiftDef, Integer dateType) {
        if (null == shiftDef) {
            return Boolean.FALSE;
        }
        List<MultiWorkTimeBaseDto> multiWorkTimeList = null;
        if (null != shiftDef.getMultiWorkTimes()) {
            PGobject pGobject = (PGobject) shiftDef.getMultiWorkTimes();
            multiWorkTimeList = FastjsonUtil.toArrayList(pGobject.getValue(), MultiWorkTimeBaseDto.class);
        }
        if (CollectionUtils.isNotEmpty(multiWorkTimeList)) {
            return Optional.ofNullable(shiftDef.getIsNight()).orElse(Boolean.FALSE);
        } else {
            if (null != shiftDef.getStartTimeBelong() && null != shiftDef.getEndTimeBelong()) {
                return ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(shiftDef.getStartTimeBelong())
                        && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shiftDef.getEndTimeBelong());
            }
            return CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), dateType);
        }
    }

    /**
     * 班次跨夜判断（V3新版-支持次日&多段班&多个班）
     *
     * @param worktimeDetail
     * @param shiftMap
     * @return
     */
    public static boolean checkCrossNightV3(WaWorktimeDetail worktimeDetail, Map<Integer, WaShiftDef> shiftMap) {
        if (Objects.isNull(worktimeDetail) || MapUtils.isEmpty(shiftMap)) {
            return Boolean.FALSE;
        }
        List<Integer> shiftDefIdList = worktimeDetail.doGetShiftDefIdList();
        if (null != shiftDefIdList && shiftDefIdList.size() > 1) {
            // 多个班次
            for (Integer shiftDefId : shiftDefIdList) {
                WaShiftDef waShiftDef = shiftMap.get(shiftDefId);
                if (CdWaShiftUtil.checkCrossNightV2(waShiftDef, worktimeDetail.getDateType())) {
                    return Boolean.TRUE;
                }
            }
            return Boolean.FALSE;
        } else {
            // 单个班次
            WaShiftDef waShiftDef = shiftMap.get(worktimeDetail.getShiftDefId());
            return CdWaShiftUtil.checkCrossNightV2(waShiftDef, worktimeDetail.getDateType());
        }
    }

    /**
     * 班次跨夜判断，过期，建议使用V2新版checkCrossNightV2(com.caidao1.wa.mybatis.model.WaShiftDef, java.lang.Integer)
     *
     * @param start
     * @param end
     * @param dateType
     * @return
     */
    @Deprecated
    public static boolean checkCrossNight(Integer start, Integer end, Integer dateType) {
        if (dateType == 1) {
            return start != null && end != null && start >= end;
        } else {
            return start != null && end != null && start > end;
        }
    }

    /**
     * 班次跨夜判断
     *
     * @param start
     * @param end
     * @param dateType
     * @return
     */
    @Deprecated
    public static boolean checkCrossNight(Long start, Long end, Integer dateType) {
        if (dateType == 1) {
            return start != null && end != null && start >= end;
        } else {
            return start != null && end != null && start > end;
        }
    }
}
