package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.system.mybatis.model.SysCorpOrgExample;
import com.caidao1.wa.dto.EmpCalendarInfoDto;
import com.caidao1.wa.mybatis.mapper.EmployeeWorkTimeMapper;
import com.caidao1.wa.mybatis.model.WaEmpShift;
import com.caidao1.wa.mybatis.model.WaWorktimeGroup;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.dto.CalendarEventDto;
import com.caidaocloud.attendance.core.wa.dto.shift.EmpCalendarShiftForMonthDto;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpRelCalendarQueryDto;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.CalendarWorktimeTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.application.dto.clock.WaWorkCalendarDto;
import com.caidaocloud.attendance.service.application.enums.CalendarStatusEnum;
import com.caidaocloud.attendance.service.application.enums.ModuleTypeEnum;
import com.caidaocloud.attendance.service.application.event.publish.WorkCalendarPublish;
import com.caidaocloud.attendance.service.application.service.IWorkCalendarService;
import com.caidaocloud.attendance.service.application.service.user.EmployeeGroupService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.domain.service.WorkCalendarDomainService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkCalendarMapper;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.EmpShiftInfoDto.EmpShiftInfoDto;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.interfaces.dto.user.MyCalendarDateDto;
import com.caidaocloud.attendance.service.schedule.application.service.dto.EmpWorkInfo;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工作日历
 *
 * <AUTHOR>
 * @Date 2021/3/4
 */
@Slf4j
@Service
public class WorkCalendarServiceImpl implements IWorkCalendarService {
    @Autowired
    private WorkCalendarDomainService workCalendarDomainService;
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private WaWorktimeDo waWorktimeDo;
    @Autowired
    private WaEmpShiftDo waEmpShiftDo;
    @Resource
    private SysEmpInfoDo sysEmpInfoDo;
    @Autowired
    private WorkCalendarPublish workCalendarPublish;
    @Resource
    private EmployeeWorkTimeMapper employeeWorkTimeMapper;
    @Resource
    private EmployeeGroupService employeeGroupService;
    @Resource
    private WaLeaveDaytimeDo waLeaveDaytimeDo;
    @Resource
    private WaEmpOvertimeDo waEmpOvertimeDo;
    @Resource
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private DataBackupDo dataBackupDo;
    @Resource
    private RemoteImportService importService;
    @Autowired
    private WaEmpTravelDaytimeDo empTravelDaytimeDo;
    @Resource
    private WorkCalendarMapper workCalendarMapper;
    @Resource
    private WaCommonService waCommonService;

    private final String WA_WORK_CALENDAR_TYPE = "wa_work_calendar_type";

    private UserInfo getUserInfo() {
        // return sessionService.getUserInfo();
        return UserContext.preCheckUser();
    }

    private boolean checkEmpRepeat(WaWorktimeDto waWorktimeDto, UserInfo userInfo) {
        List<Long> groupEmpIds = Lists.newArrayList();
        if (StringUtils.isNotEmpty(waWorktimeDto.getGroupExp())) {
            groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(ConvertHelper.longConvert(userInfo.getTenantId()), waWorktimeDto.getGroupExp(), FastjsonUtil.toJson(waWorktimeDto.getGroupExpCondition()));
        }
        if (null != waWorktimeDto.getEmpIds()) {
            waWorktimeDto.getEmpIds().addAll(groupEmpIds);
        } else {
            waWorktimeDto.setEmpIds(groupEmpIds);
        }
        //groupEmpIds = waWorktimeDto.getEmpIds();
        // 所选适用人员是否已存在于其他方案
        /*if (!waWorktimeDto.getIsDefault() && CollectionUtils.isNotEmpty(groupEmpIds)) {
            //检查员工是否已经分配过日历
            if (CollectionUtils.isNotEmpty(waWorktimeDto.getEmpIds())) {
                List<WaEmpShiftDo> empShiftDoList = waEmpShiftDo
                        .getEmpShiftInfoList(ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(),
                                groupEmpIds, waWorktimeDto.getStartdate(),
                                waWorktimeDto.getEnddate() + 86399, waWorktimeDto.getWorkCalendarId());
                if (CollectionUtils.isNotEmpty(empShiftDoList)) {
                    return CommonConstant.TRUE;
                }
            }
        }*/
        return CommonConstant.FALSE;
    }

    @Transactional
    @Override
    public String saveOrUpdateWorkCalendar(WaWorktimeDto waWorktimeDto, UserInfo userInfo) throws Exception {
        waWorktimeDto.setWorkCalendarName(waWorktimeDto.getWorkCalendarName().trim());

        int num = waWorktimeDo.getWorkTimeCountByName(userInfo.getTenantId(), waWorktimeDto.getWorkCalendarId(), waWorktimeDto.getWorkCalendarName());
        if (num > 0) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.WORK_CALENDAR_EXISTED, null).getMsg(), waWorktimeDto.getWorkCalendarName().trim());
        }

        if (waWorktimeDto.getStartdate() != null) {
            waWorktimeDto.setStartdate(DateUtil.getOnlyDate(new Date(waWorktimeDto.getStartdate() * 1000)));
        }
        if (waWorktimeDto.getEnddate() != null) {
            waWorktimeDto.setEnddate(DateUtil.getOnlyDate(new Date(waWorktimeDto.getEnddate() * 1000)));
        }
        if (BooleanUtils.isTrue(waWorktimeDto.getIsDefault())) {
            // 适用范围校验，只允许存在一个 适用全部的日历
            WaWorktimeDo defaultCalendar = waWorktimeDo.getDefaultCalendar(userInfo.getTenantId(), waWorktimeDto.getWorkCalendarId());
            if (defaultCalendar != null) {
                return String.format(ResponseWrap.wrapResult(AttendanceCodes.WORK_CALENDAR_MATCH_ALL_EXISTED, null).getMsg(), defaultCalendar.getWorkCalendarName());
            }
        } else {
            // 检查员工是否已经分配过日历
            this.checkEmpRepeat(waWorktimeDto, userInfo);
            if (CollectionUtils.isEmpty(waWorktimeDto.getEmpIds())) {
                return ResponseWrap.wrapResult(AttendanceCodes.SELECTED_CONDITION_NO_MATCH_EMP, null).getMsg();
            }
        }

        if (waWorktimeDto.getWorkCalendarId() != null) {
            WaWorktimeDo oldWorktimeDo = waWorktimeDo.selectById(userInfo.getTenantId(), waWorktimeDto.getWorkCalendarId());
            if (null == oldWorktimeDo) {
                return MessageHandler.getMessage("caidao.exception.error_202790", WebUtil.getRequest());
            }
            Integer oldWorktimeType = Optional.ofNullable(oldWorktimeDo.getWorktimeType())
                    .orElse(CalendarWorktimeTypeEnum.FIXED_SCHEDULE.getIndex());
            if (!oldWorktimeType.equals(waWorktimeDto.getWorktimeType())) {
                return MessageHandler.getMessage("caidao.exception.error_202800", WebUtil.getRequest());
            }
        }

        // 保存日历
        WaWorktimeDo worktimeDo = ObjectConverter.convert(waWorktimeDto, WaWorktimeDo.class);
        if (null != waWorktimeDto.getI18nWorkCalendarName()) {
            worktimeDo.setI18nWorkCalendarName(FastjsonUtil.toJson(waWorktimeDto.getI18nWorkCalendarName()));
        }
        if (null != waWorktimeDto.getGroupExpCondition()) {
            worktimeDo.setGroupExpCondition(FastjsonUtil.toJson(waWorktimeDto.getGroupExpCondition()));
        }
        if (waWorktimeDto.getWorkCalendarId() == null) {
            worktimeDo.setBelongOrgid(userInfo.getTenantId());
            worktimeDo.setCrtuser(userInfo.getUserId());
            worktimeDo.setCrttime(DateUtil.getCurrentTime(true));
            worktimeDo.setUpdateTime(DateUtil.getCurrentTime(true));
            worktimeDo.setUpdateUser(userInfo.getUserId());
            worktimeDo.setDeleted((short) 0);
            waWorktimeDo.save(worktimeDo);
        } else {
            worktimeDo.setUpdateTime(DateUtil.getCurrentTime(true));
            worktimeDo.setUpdateUser(userInfo.getUserId());
            // 编辑时考勤类型不允许修改
            worktimeDo.setWorktimeType(null);
            waWorktimeDo.updateById(worktimeDo);
        }

        // 保存日历分组信息并生成日历明细
        if (waWorktimeDto.getStartdate() != null && waWorktimeDto.getEnddate() != null) {
            WaWorktimeGroup waWorktimeGroup = new WaWorktimeGroup();
            waWorktimeGroup.setStartdate(waWorktimeDto.getStartdate());
            waWorktimeGroup.setEnddate(waWorktimeDto.getEnddate());
            waWorktimeGroup.setWorkCalendarId(worktimeDo.getWorkCalendarId());
            waWorktimeGroup.setCrttime(DateUtil.getCurrentTime(true));
            waWorktimeGroup.setCrtuser(userInfo.getUserId());
            waConfigService.genWorkCalendar(waWorktimeGroup, userInfo.getTenantId(), userInfo.getUserId());
        }
        worktimeDo.setBelongOrgid(userInfo.getTenantId());
        worktimeDo.setCorpid(ConvertHelper.longConvert(userInfo.getTenantId()));

        // 保存日历适用的员工范围
        if (StringUtils.isNotEmpty(waWorktimeDto.getGroupExp())) {
            EmployeeGroupDto employeeGroupDto = new EmployeeGroupDto();
            employeeGroupDto.setGroupType(WA_WORK_CALENDAR_TYPE);
            employeeGroupDto.bulidExpression(waWorktimeDto.getGroupExp(), waWorktimeDto.getGroupNote());
            employeeGroupDto.setBusinessKey(String.valueOf(worktimeDo.getWorkCalendarId()));
            employeeGroupDto.setGroupName(String.format("%s-%s", WA_WORK_CALENDAR_TYPE, employeeGroupDto.getBusinessKey()));
            employeeGroupDto.setDefaultUserId(userInfo.getUserId());
            employeeGroupDto.setDefaultTenantId(Long.valueOf(userInfo.getBelongOrgId()));
            employeeGroupService.saveOrUpdate(employeeGroupDto);
        }
        return "";
    }

    @Override
    public WaWorktimeDto getWorkCalendarById(Integer id, Boolean queryGroupExp) {
        UserInfo userInfo = this.getUserInfo();
        val result = workCalendarDomainService.getWorkCalendarById(userInfo.getTenantId(), id);
        if (queryGroupExp) {
            EmployeeGroupDto employeeGroupDto = employeeGroupService.getEmployeeGroup(String.valueOf(result.getWorkCalendarId()), WA_WORK_CALENDAR_TYPE);
            if (null != employeeGroupDto) {
                result.setGroupExp(employeeGroupDto.getGroupExp());
                result.setGroupNote(employeeGroupDto.getGroupNote());
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteWorkCalendarById(Integer id) {
        UserInfo userInfo = this.getUserInfo();
        WaWorktimeDo worktimeDo = new WaWorktimeDo();
        worktimeDo.setWorkCalendarId(id);
        worktimeDo.setDeleted((short) 1);
        worktimeDo.setUpdateTime(System.currentTimeMillis() / 1000);
        worktimeDo.setUpdateUser(userInfo.getUserId());
        worktimeDo.setBelongOrgid(userInfo.getTenantId());
//        waWorktimeDo.updateById(worktimeDo);

        waWorktimeDo.deleteDetailById(id);
        waWorktimeDo.deleteWorkTimeGroupById(id);
        waEmpShiftDo.deleteByWorkCalendarId(id);
        waWorktimeDo.deleteById(id);

        worktimeDo.setCorpid(ConvertHelper.longConvert(userInfo.getTenantId()));

//        publishWorkCalendarMsg(worktimeDo);
    }

    @Override
    public List<EmpShiftVerifyDto> verifySelectedEmployees(EmpShiftVerifyReqDto reqDto) {
        UserInfo userInfo = this.getUserInfo();
        List<WaEmpShiftDo> empShiftDoList = waEmpShiftDo.getEmpShiftInfoList(ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), reqDto.getEmpIds(), reqDto.getStartTime(), reqDto.getEndTime(), reqDto.getWorkCalendarId());
        if (CollectionUtils.isNotEmpty(empShiftDoList)) {
            return ObjectConverter.convertList(empShiftDoList, EmpShiftVerifyDto.class);
        }
        return new ArrayList<>();
    }

    private void doParseFilterList(EmpShiftReqDto dto) {
        if (CollectionUtils.isEmpty(dto.getFilterList())) {
            return;
        }
        Iterator<FilterBean> it = dto.getFilterList().iterator();
        while (it.hasNext()) {
            FilterBean filterBean = it.next();
            // 方案状态
            if ("effectiveStatus".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    dto.setEffectiveStatus(filterBean.getMin());
                }
                it.remove();
            }
        }
    }

    @Override
    @CDText(exp = {"empStyle:empStyleName" + TextAspect.DICT_E, "empStatus:empStatusName" + TextAspect.STATUS_ENUM}, classType = EmpShiftDto.class)
    public AttendancePageResult<EmpShiftDto> getEmpShiftListByCalendarId(EmpShiftReqDto reqDto, UserInfo userInfo) {
        doParseFilterList(reqDto);
        AttendancePageResult<EmpShiftDto> dtoPageResult = new AttendancePageResult<>();
        AttendanceBasePage basePage = ObjectConverter.convert(reqDto, AttendanceBasePage.class);
        PageBean pageBean = PageUtil.getPageBean(reqDto);
        String filter = pageBean.getFilter();
        if (StringUtils.isNotBlank(filter)) {
            if (filter.contains("workCalendarId")) {
                filter = filter.replaceAll("\"workCalendarId\"", "es.work_calendar_id");
            }
            if (filter.contains("orgid")) {
                filter = filter.replaceAll("\"orgid\"", "ei.orgid");
            }
            if (filter.contains("hire_date")) {
                filter = filter.replaceAll("\"hire_date\"", "ei.hire_date");
            }
            if (filter.contains("termination_date")) {
                filter = filter.replaceAll("\"termination_date\"", "ei.termination_date");
            }
            if (filter.contains("stats")) {
                filter = filter.replaceAll("\"stats\"", "ei.stats");
            }
            if (filter.contains("employ_type")) {
                filter = filter.replaceAll("\"employ_type\"", "ei.employ_type");
            }
        }
        if (StringUtils.isNotBlank(reqDto.getDataScope())) {
            if (StringUtils.isNotBlank(filter)) {
                filter = filter + reqDto.getDataScope();
            } else {
                filter = reqDto.getDataScope();
            }
        }
        log.info("getEmpShiftListByCalendarId filter={}", filter);
        AttendancePageResult<WaEmpShiftDo> pageResult = waEmpShiftDo.getEmpShiftDetailListByCalendarId(basePage,
                userInfo.getTenantId(), reqDto.getWorkCalendarId(), filter, reqDto.getEffectiveStatusFilterValue());
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaEmpShiftDo> doList = pageResult.getItems();
            List<EmpShiftDto> dtoList = ObjectConverter.convertList(doList, EmpShiftDto.class);
            Long nowDate = DateUtil.getOnlyDate();
            dtoList.forEach(d -> {
                EmpInfoDTO empInfoDTO = ObjectConverter.convert(d, EmpInfoDTO.class);
                empInfoDTO.setName(d.getEmpName());
                d.setEmpInfo(empInfoDTO);
                d.setWorkCalendarName(LangParseUtil.getI18nLanguage(d.getI18nWorkCalendarName(), d.getWorkCalendarName()));
                d.doSetEffectiveStatus(nowDate);
                if (d.getWorktimeType() != null) {
                    d.setWorktimeTypeTxt(CalendarWorktimeTypeEnum.getName(d.getWorktimeType()));
                }
            });
            dtoPageResult.setItems(dtoList);
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setTotal(pageResult.getTotal());
        }
        return dtoPageResult;
    }

    private List convertListMapToCalendarModel(List<Map> detailList) throws ParseException {
        List<CalendarEventDto> eventList = new ArrayList<>();
        CalendarEventDto event;
        if (CollectionUtils.isNotEmpty(detailList)) {
            SimpleDateFormat ymdDf = new SimpleDateFormat("yyyyMMdd");
            for (Iterator var6 = detailList.iterator(); var6.hasNext(); eventList.add(event)) {
                Map detail = (Map) var6.next();
                event = new CalendarEventDto();
                Integer dateType = (Integer) detail.get("dateType");
                if (dateType == 1) {
                    if (null != detail.get("i18nShiftDefName")) {
                        String i18n = LangParseUtil.getI18nLanguage(detail.get("i18nShiftDefName").toString(), null);
                        if (StringUtil.isNotBlank(i18n)) {
                            event.setTitle(i18n);
                        }
                    } else {
                        event.setTitle((String) detail.get("name"));
                    }
                } else {
                    event.setTitle((String) detail.get("dateTypeName"));
                }
                event.setDateType(dateType);
                String workDate = (String) detail.get("date");
                Long dateL = ymdDf.parse(workDate).getTime() / 1000;
                event.setStart(DateUtil.getDateStrByTimesamp(dateL));
                event.setStartTimestamp(dateL);
            }
        }

        return eventList;
    }

    @Override
    public List getWorkCalendarDetailList(Integer workCalendarId, String start, String end) throws Exception {
        List<Map> detailList = waWorktimeDo.getWorkCalendarDetailList(workCalendarId, start, end);
        return convertListMapToCalendarModel(detailList);
    }

    @Override
    public List getEmpCalendarDetailListByYm(String belongOrgId, Long empId, String start, String end) throws Exception {
        Long startDate = DateUtil.convertStringToDateTime(start, "yyyy-MM-dd", Boolean.TRUE);
        Long endDate = DateUtil.convertStringToDateTime(end, "yyyy-MM-dd", Boolean.TRUE);
        List<Map> detailList = waWorktimeDo.getEmpCalendarList(belongOrgId, startDate, endDate, empId);
        //查询替换班次信息
        List<Map> changeList = waWorktimeDo.getEmpShiftChangeListByEmpId(belongOrgId, empId, startDate, endDate);
        if (CollectionUtils.isNotEmpty(changeList)) {
            List<String> changeDateList = changeList.stream().map(o -> (String) o.get("date")).collect(Collectors.toList());
            detailList = detailList.stream().filter(o -> {
                String date = (String) o.get("date");
                if (changeDateList.contains(date)) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            detailList.addAll(changeList);
        }
        return convertListMapToCalendarModel(detailList);
    }

    @Override
    public AttendancePageResult getWaShiftList(WorkCalendarReqDto workCalendarReqDto) {
        AttendanceBasePage basePage = workCalendarReqDto;
        String belongOrgId = workCalendarReqDto.getBelongOrgId();
        Long startDate = workCalendarReqDto.getStartDate();
        Long endDate = workCalendarReqDto.getEndDate();
        if (belongOrgId == null) {
            return AttendancePageResult.empty();
        }

        if (startDate == null || endDate == null) {
            return AttendancePageResult.empty();
        }

        return waWorktimeDo.getWaShiftList(basePage, belongOrgId, startDate, endDate);
    }

    private void publishWorkCalendarMsg(WaWorktimeDo worktimeDo) {
        WaWorkCalendarDto waWorkCalendarDto = ObjectConverter.convert(worktimeDo, WaWorkCalendarDto.class);
        // 发送删除工作日历消息
        workCalendarPublish.publish(JSON.toJSONString(waWorkCalendarDto));
    }

    private Integer getCurrentYearMonth() {
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        String nowData = format.format(date);
        return Integer.parseInt(nowData);
    }

    @Override
    public UserInfo checkSession() {
        UserInfo userInfo = getUserInfo();
        if (null == userInfo || null == userInfo.getStaffId() || null == userInfo.getTenantId()) {
            throw new ServerException("Token may have expired");
        }
        return userInfo;
    }

    private Long getWorkDate(Integer workDate, String key) {
        return DateUtilExt.getMonthDateByYm(Integer.valueOf(workDate.toString().substring(0, 6))).get(key);
    }

    @Override
    public List<MyCalendarDateDto> getMyShopCalendar(Integer month) {
        UserInfo userInfo = checkSession();
        if (null == month) {
            month = getCurrentYearMonth();
        }
        // 查询排班
        Long empId = userInfo.getStaffId();
        List<EmpCalendarShiftForMonthDto> calendarList = waCommonService.getEmpCalendarShiftByMonth(userInfo.getTenantId(), empId, month, null, null);
        if (CollectionUtils.isEmpty(calendarList)) {
            return null;
        }

        List<MyCalendarDateDto> dateDtoList = ObjectConverter.convertList(calendarList, MyCalendarDateDto.class);
        dateDtoList.forEach(calendarDateDto -> calendarDateDto.setWaResult(CalendarStatusEnum.NORMAL.getIndex()));

        Map<String, Long> monthDate = DateUtilExt.getMonthDateByYm(month);
        Long startDate = monthDate.get("start");
        Long endDate = monthDate.get("end");

        // 休假记录
        List<String> leaveDateList = new ArrayList<>();
        List<WaLeaveDaytimeDo> empLeaveDayList = waLeaveDaytimeDo.getEmpLeaveDay(empId, startDate, endDate,
                new ArrayList<>(Arrays.asList(ApprovalStatusEnum.IN_APPROVAL.getIndex(),
                        ApprovalStatusEnum.PASSED.getIndex())));
        if (CollectionUtils.isNotEmpty(empLeaveDayList)) {
            empLeaveDayList.forEach(e -> {
                leaveDateList.add(DateUtil.convertDateTimeToStr(Optional.ofNullable(e.getRealDate()).orElse(e.getLeaveDate()), "yyyyMMdd", true));
            });
        }
        if (CollectionUtils.isNotEmpty(leaveDateList)) {
            dateDtoList.forEach(calendarDateDto -> {
                if (leaveDateList.contains(calendarDateDto.getWorkDate())) {
                    calendarDateDto.setWaResult(CalendarStatusEnum.VACATION.getIndex());
                }
            });
        }

        // 出差记录
        List<String> travelDateList = new ArrayList<>();
        List<WaEmpTravelDaytimeDo> empTravelDayList = empTravelDaytimeDo.getTravelDayTimeList(userInfo.getTenantId(), empId, startDate, endDate);
        if (CollectionUtils.isNotEmpty(empTravelDayList)) {
            empTravelDayList.forEach(e -> {
                travelDateList.add(DateUtil.convertDateTimeToStr(Optional.ofNullable(e.getRealDate()).orElse(e.getTravelDate()), "yyyyMMdd", true));
            });
        }
        if (CollectionUtils.isNotEmpty(travelDateList)) {
            dateDtoList.forEach(calendarDateDto -> {
                if (travelDateList.contains(calendarDateDto.getWorkDate())) {
                    calendarDateDto.setWaResult(CalendarStatusEnum.TRAVEL.getIndex());
                }
            });
        }

        // 加班记录
        List<String> overtimeList = new ArrayList<>();
        List<WaEmpOvertimeDo> empOvertimeList = waEmpOvertimeDo.getEmpOvertimeListByYmdDate(empId, startDate, endDate);
        if (CollectionUtils.isNotEmpty(empOvertimeList)) {
            empOvertimeList.forEach(e -> {
                overtimeList.add(DateUtil.convertDateTimeToStr(Optional.ofNullable(e.getRealDate()).orElse(e.getStartTime()), "yyyyMMdd", true));
            });
        }
        if (CollectionUtils.isNotEmpty(overtimeList)) {
            dateDtoList.forEach(calendarDateDto -> {
                if (overtimeList.contains(calendarDateDto.getWorkDate())) {
                    calendarDateDto.setWaResult(CalendarStatusEnum.OVERTIME.getIndex());
                }
            });
        }

        List<Map> waResultList = employeeWorkTimeMapper.getWorkTimeCalendar(empId, startDate, endDate);
        if (CollectionUtils.isNotEmpty(waResultList)) {
            List<String> errDateList = new ArrayList<>();
            Long nowDate = DateUtil.getOnlyDate();
            waResultList.forEach(map -> {
                Long belongDate = (Long) map.get("belong_date");
                if (!nowDate.equals(belongDate)) {
                    //当日考勤不分析异常
                    Integer isKg = (Integer) map.get("is_kg");
                    BigDecimal lateTime = new BigDecimal(map.get("late_time") == null ? "0" : map.get("late_time").toString());
                    BigDecimal earlyTime = new BigDecimal(map.get("early_time") == null ? "0" : map.get("early_time").toString());
                    boolean addFlag = lateTime.doubleValue() > 0 || earlyTime.doubleValue() > 0 || (isKg != null && isKg > 0);
                    if (addFlag) {
                        errDateList.add(DateUtil.convertDateTimeToStr(belongDate, "yyyyMMdd", true));
                    }
                }
            });

            if (CollectionUtils.isNotEmpty(errDateList)) {
                dateDtoList.forEach(calendarDateDto -> {
                    if (errDateList.contains(calendarDateDto.getWorkDate())) {
                        calendarDateDto.setWaResult(CalendarStatusEnum.ABNORMAL.getIndex());
                    }
                });
            }
        }
        return dateDtoList;
    }

    @Override
    @Transactional
    public Result<Boolean> saveEmpShift(EmpShiftInfoDto dto) {
        UserInfo userInfo = getUserInfo();
        WaEmpShiftDo empShift = ObjectConverter.convert(dto, WaEmpShiftDo.class);
        long currentTime = System.currentTimeMillis() / 1000;
        empShift.setBelongOrgid(userInfo.getTenantId());
        empShift.setUpdtime(currentTime);
        empShift.setUpduser(userInfo.getUserId());
        empShift.setEmpid(dto.getEmpInfo().getEmpId());
        if (dto.getEmpShiftId() == null) {
            empShift.setCrttime(currentTime);
            empShift.setCrtuser(userInfo.getUserId());
            List<WaEmpShiftDo> empShiftList = waEmpShiftDo.getEmpShiftByPeriod(dto.getEmpInfo().getEmpId(), null, userInfo.getTenantId(), null, null);
            if (CollectionUtils.isNotEmpty(empShiftList)) {
                Optional<WaEmpShiftDo> empShiftOpt = empShiftList.stream().max(Comparator.comparing(WaEmpShiftDo::getStartTime));
                if (empShiftOpt.isPresent()) {
                    WaEmpShiftDo shift = empShiftOpt.get();
                    if (empShift.getStartTime() > shift.getStartTime() && empShift.getStartTime() <= shift.getEndTime() && empShift.getEndTime() >= shift.getEndTime()) {
                        shift.setEndTime(DateUtil.addDate(empShift.getStartTime() * 1000, -1));
                        shift.setUpdtime(currentTime);
                        shift.setUpduser(userInfo.getUserId());
                        waEmpShiftDo.saveEmpShift(shift);
                        waEmpShiftDo.saveEmpShift(empShift);
                    } else if (empShift.getStartTime() > shift.getStartTime() && empShift.getEndTime() > shift.getEndTime()) {
                        waEmpShiftDo.saveEmpShift(empShift);
                    } else {
                        log.error("员工该时间内已存在工作日历，请修改原日历时间！");
                        return ResponseWrap.wrapResult(AttendanceCodes.NEW_CLOCK_PLAN_TIME_OVERLAP, Boolean.TRUE);
                    }
                }
            } else {
                waEmpShiftDo.saveEmpShift(empShift);
            }
        } else {
            // 校验有效期开始时间结束时间是否存在重叠
            List<WaEmpShiftDo> empShiftList = waEmpShiftDo.getEmpShiftByPeriod(dto.getEmpInfo().getEmpId(), dto.getEmpShiftId(), userInfo.getTenantId(), dto.getStartTime(), dto.getEndTime());
            if (CollectionUtils.isNotEmpty(empShiftList)) {
                log.error("员工该时间内已存在工作日历，请修改原日历时间！");
                return ResponseWrap.wrapResult(AttendanceCodes.TIME_OVERLAP, Boolean.TRUE);
            }
            waEmpShiftDo.saveEmpShift(empShift);
        }
        return Result.ok(Boolean.TRUE);
    }

    @Override
    @Transactional
    public void deleteEmpShift(Integer empShiftId) {
        Optional<WaEmpShiftDo> opt = Optional.ofNullable(waEmpShiftDo.getEmpShift(empShiftId));
        waEmpShiftDo.deleteEmpShifts(Collections.singletonList(empShiftId));
        opt.ifPresent(s -> dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_SHIFT.name(), ModuleTypeEnum.EMP_SHIFT.getTable(), Collections.singletonList(opt.get()), getUserInfo())));
    }

    @Override
    public EmpShiftDto getEmpShift(Integer empShiftId) {
        WaEmpShiftDo empShift = waEmpShiftDo.getEmpShift(empShiftId);
        if (null == empShift) {
            return null;
        }
        EmpShiftDto dto = ObjectConverter.convert(empShift, EmpShiftDto.class);
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(getUserInfo().getTenantId(), empShift.getEmpid());
        EmpInfoDTO empInfoDTO = ObjectConverter.convert(empInfo, EmpInfoDTO.class);
        empInfoDTO.setEmpId(empInfo.getEmpid());
        empInfoDTO.setName(empInfo.getEmpName());
        dto.setEmpInfo(empInfoDTO);
        return dto;
    }

    @Override
    public List<KeyValue> getCalendarOptions() {
        UserInfo userInfo = getUserInfo();
        List<WaWorktimeDo> items = waWorktimeDo.getWorkCalendar(userInfo.getTenantId());
        List<KeyValue> list = new ArrayList<>();
        items.forEach(i -> list.add(new KeyValue(LangParseUtil.getI18nLanguage(i.getI18nWorkCalendarName(), i.getWorkCalendarName()), i.getWorkCalendarId())));
        return list;
    }

    @Override
    public void synchronizeEmpShift(String belongOrgId, Long userId, Long corpId) {
        // 查询所有的工作日历
        log.info("查询所有的工作日历开始");
        List<WaWorktimeDo> workCalendars = waWorktimeDo.getWorkCalendar(belongOrgId);
        log.info("查询所有的工作日历结束，总条数：[{}]", workCalendars.size());
        if (CollectionUtils.isNotEmpty(workCalendars)) {
            Map<String, String> conditionMap = workCalendars.stream().filter(row -> StringUtil.isNotBlank(row.getGroupExpCondition())).collect(Collectors.toMap(row -> row.getWorkCalendarId().toString(), WaWorktimeDo::getGroupExpCondition));
            List<String> businessKeys = workCalendars.stream().map(w -> String.valueOf(w.getWorkCalendarId())).collect(Collectors.toList());
            // 根据日历查询适用范围规则
            log.info("根据日历查询适用范围规则开始");
            List<EmployeeGroupDto> empGroups = employeeGroupService.getEmployeeGroups(businessKeys, WA_WORK_CALENDAR_TYPE, belongOrgId);
            log.info("根据日历查询适用范围规则结束，总条数：[{}]", empGroups.size());
            if (CollectionUtils.isNotEmpty(empGroups)) {
                // 查询已分配员工日历（排班）
                log.info("查询已分配员工日历（排班）开始");
                List<WaEmpShiftDo> list = waEmpShiftDo.getEmpShiftByPeriod(null, null, belongOrgId, null, null);
                log.info("查询已分配员工日历（排班）结束，总条数：[{}]", list.size());
                List<Long> assignedEmpIds = list.stream().map(WaEmpShiftDo::getEmpid).distinct().collect(Collectors.toList());
                log.info("更新已分配员工日历开始");
                updateAssignedEmpCalendars(corpId, belongOrgId, userId, assignedEmpIds, list, empGroups, conditionMap);
                log.info("更新已分配员工日历结束");
                List<Long> unAssignedEmpIds = Lists.newArrayList();
                List<WaEmpShiftDo> unAssignedEmpShifts = Lists.newArrayList();
                log.info("筛选未生成过日历的员工开始");
                for (EmployeeGroupDto empGroup : empGroups) {
                    if (StringUtils.isNotEmpty(empGroup.getGroupExp())) {
                        // 根据工作日历匹配规则筛选员工
                        List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, empGroup.getGroupExp(), conditionMap.get(empGroup.getBusinessKey()));
                        if (CollectionUtils.isNotEmpty(groupEmpIds)) {
                            groupEmpIds.removeAll(assignedEmpIds);
                            unAssignedEmpIds.addAll(groupEmpIds);
                            for (Long groupEmpId : groupEmpIds) {
                                // 初始化员工日历（排班）
                                unAssignedEmpShifts.add(getWaEmpShiftDo(belongOrgId, groupEmpId, userId, Integer.valueOf(empGroup.getBusinessKey()), null));
                            }
                        }
                    }
                }
                log.info("筛选未生成过日历的员工结束");
                // 将未生成过日历的员工自动生成员工日历
                log.info("未生成过日历的员工自动生成员工日历开始");
                if (CollectionUtils.isNotEmpty(unAssignedEmpShifts)) {
                    //人员去重
                    List<Long> empIdList = unAssignedEmpIds.stream().distinct().collect(Collectors.toList());
                    unAssignedEmpShifts = unAssignedEmpShifts.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WaEmpShiftDo::getEmpid))), ArrayList::new));
                    // 查询员工信息
                    List<SysEmpInfo> unAssignedEmployees = Lists.newArrayList();
                    List<List<Long>> lists = ListTool.split(empIdList, 500);
                    for (List<Long> empInfos : lists) {
                        if (CollectionUtils.isEmpty(empInfos)) {
                            continue;
                        }
                        unAssignedEmployees.addAll(sysEmpInfoDo.getEmpInfoByIds(belongOrgId, empInfos));
                    }
                    if (CollectionUtils.isNotEmpty(unAssignedEmployees)) {
                        // 员工信息Map
                        Map<Long, SysEmpInfo> unAssignedEmployeeMap = unAssignedEmployees.stream().collect(Collectors.toMap(SysEmpInfo::getEmpid, Function.identity(), (k1, k2) -> k2));
                        Iterator<WaEmpShiftDo> iterator = unAssignedEmpShifts.iterator();
                        while (iterator.hasNext()) {
                            WaEmpShiftDo empShiftDo = iterator.next();
                            SysEmpInfo unAssignedEmployee = unAssignedEmployeeMap.get(empShiftDo.getEmpid());
                            if (unAssignedEmployee != null) {
                                //生效时间默认1970-1-1
                                empShiftDo.setStartTime(0L);//unAssignedEmployee.getHireDate()
                            } else {
                                iterator.remove();
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(unAssignedEmpShifts)) {
                        // 保存员工日历（排班）
                        log.info("保存员工日历（排班）");
                        waEmpShiftDo.batchSave(unAssignedEmpShifts);
                        log.info("保存员工日历（排班）结束");
                    }
                }
                log.info("未生成过日历的员工自动生成员工日历结束");
                log.info("同步完工作日历刷新员工考勤类型开始..");
                workCalendarMapper.syncEmpAttendanceType(belongOrgId, DateUtil.getOnlyDate(), null);
                log.info("同步完工作日历刷新员工考勤类型结束..");
            }
        }
    }

    public void updateAssignedEmpCalendars(Long corpId, String tenantId, Long userId, List<Long> assignedEmpIds,
                                           List<WaEmpShiftDo> assignedEmpShifts, List<EmployeeGroupDto> empGroups,
                                           Map<String, String> conditionMap) {
        if (CollectionUtils.isEmpty(assignedEmpIds) || CollectionUtils.isEmpty(assignedEmpShifts) || CollectionUtils.isEmpty(empGroups)) {
            return;
        }
        List<EmpWorkInfo> assignedEmployees = sysEmpInfoDo.getEmpInfoList(tenantId, assignedEmpIds.stream().map(String::valueOf).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(assignedEmployees)) {
            return;
        }
        //已分配方案员工信息Map集合
        Map<Long, EmpWorkInfo> assignedEmployeeMap = assignedEmployees.stream().collect(Collectors.toMap(emp -> Long.valueOf(emp.getEmpId()), Function.identity(), (k1, k2) -> k2));
        List<WaEmpShiftDo> updateEmpShifts = Lists.newArrayList();
        List<Integer> deleteEmpShifts = Lists.newArrayList();
        //员工方案按照empId分组
        Map<Long, List<WaEmpShiftDo>> empShiftMap = assignedEmpShifts.stream().collect(Collectors.groupingBy(WaEmpShiftDo::getEmpid));
        List<Long> unAssignedEmpIds = Lists.newArrayList();
        Map<Integer, EmployeeGroupDto> empGroupsMap = empGroups.stream().collect(Collectors.toMap(g -> Integer.valueOf(g.getBusinessKey()), Function.identity(), (k1, k2) -> k2));
        List<WaEmpShiftDo> addEmpShifts = Lists.newArrayList();
        Map<Integer, List<Long>> matchEmpMap = new HashMap<>();
        for (Long empId : assignedEmpIds) {
            if (!assignedEmployeeMap.containsKey(empId)) {
                continue;
            }
            EmpWorkInfo empInfo = assignedEmployeeMap.get(empId);
            Long terminationDate = empInfo.getLeaveDate();
            List<WaEmpShiftDo> empShiftList = empShiftMap.get(empId);
            Long hireDate = empInfo.getHireDate();
            if (null == terminationDate) {//无离职日期
                long today = DateUtil.getOnlyDate();
                //生效中的方案
                Optional<WaEmpShiftDo> effectiveOpt = empShiftList.stream().filter(g -> today >= g.getStartTime() && today < g.getEndTime()).max(Comparator.comparing(WaEmpShiftDo::getStartTime));
                WaEmpShiftDo newEmpScheme;
                if (effectiveOpt.isPresent()) {//有生效中的方案
                    WaEmpShiftDo effective = effectiveOpt.get();
                    if (!checkEmpGroup(empId, corpId, effective.getWorkCalendarId(), empGroupsMap, conditionMap)) {//不满足生效中方案适用条件，则重新分配方案
                        //生效中的方案，因条件不匹配，过期掉，失效日期未当前日期（零点）前一秒
                        effective.setEndTime(today - 1);
                        if (effective.getEndTime() < effective.getStartTime()) {
                            effective.setEndTime(effective.getStartTime() + 1);
                            effective.setStartTime(effective.getEndTime());
                        }
                        effective.setUpduser(userId == null ? 0 : userId);
                        effective.setUpdtime(DateUtil.getCurrentTime(true));
                        updateEmpShifts.add(effective);
                        //查找是否有匹配的方案
                        newEmpScheme = getMatchEmpScheme(tenantId, empId, userId, corpId, today, empGroupsMap, empShiftList, matchEmpMap, conditionMap);
                        if (null != newEmpScheme) {//匹配到
                            setStartTime(hireDate, newEmpScheme, empShiftList, effective);
                            addEmpShifts.add(newEmpScheme);
                        }
                    }
                } else {//无生效中的方案
                    //查找是否有匹配的方案
                    newEmpScheme = getMatchEmpScheme(tenantId, empId, userId, corpId, today, empGroupsMap, empShiftList, matchEmpMap, conditionMap);
                    if (null != newEmpScheme) {//匹配到
                        setStartTime(hireDate, newEmpScheme, empShiftList, null);
                        addEmpShifts.add(newEmpScheme);
                    }
                }
            } else {
                if (null != hireDate && Optional.ofNullable(empInfo.getReentry()).orElse(false)) {
                    //再入职
                    if (assignedEmpShifts.stream()
                            .anyMatch(g -> g.getEmpid().equals(empId) && g.getStartTime() >= hireDate)) {
                        continue;
                    }
                    Optional<WaEmpShiftDo> empShiftOpt = empShiftList.stream()
                            .filter(g -> terminationDate > g.getStartTime() && terminationDate + 86399 < g.getEndTime())
                            .max(Comparator.comparing(WaEmpShiftDo::getStartTime));
                    if (empShiftOpt.isPresent()) {
                        WaEmpShiftDo empShift = empShiftOpt.get();
                        if (hireDate > empShift.getStartTime() && hireDate < empShift.getEndTime()) {
                            Integer workCalendarId = empShift.getWorkCalendarId();
                            if (checkEmpGroup(empId, corpId, workCalendarId, empGroupsMap, conditionMap)) {
                                addEmpShifts.add(getWaEmpShiftDo(tenantId, empId, userId, workCalendarId, hireDate));
                            } else {
                                unAssignedEmpIds.add(empId);
                            }
                        } else {
                            unAssignedEmpIds.add(empId);
                        }
                        empShift.setEndTime(DateUtil.addDate(terminationDate * 1000, 1) - 1);
                        empShift.setUpdtime(DateUtil.getCurrentTime(true));
                        empShift.setUpduser(userId == null ? 0 : userId);
                        updateEmpShifts.add(empShift);
                    } else {
                        unAssignedEmpIds.add(empId);
                    }
                } else if ("1".equals(empInfo.getEmpStatus().getValue())) {
                    //已离职
                    deleteEmpShifts.addAll(empShiftList.stream().filter(g -> g.getStartTime() > terminationDate).map(WaEmpShiftDo::getEmpShiftId).collect(Collectors.toList()));
                    Optional<WaEmpShiftDo> empShiftOpt = empShiftList.stream()
                            .filter(g -> terminationDate > g.getStartTime() && terminationDate + 86399 < g.getEndTime())
                            .max(Comparator.comparing(WaEmpShiftDo::getStartTime));
                    if (empShiftOpt.isPresent()) {
                        WaEmpShiftDo empShift = empShiftOpt.get();
                        empShift.setEndTime(DateUtil.addDate(terminationDate * 1000, 1) - 1);
                        empShift.setUpdtime(DateUtil.getCurrentTime(true));
                        empShift.setUpduser(userId == null ? 0 : userId);
                        updateEmpShifts.add(empShift);
                    }
                }
            }
        }
        //重新分配
        if (CollectionUtils.isNotEmpty(unAssignedEmpIds)) {
            List<WaEmpShiftDo> unAssignedEmpShifts = Lists.newArrayList();
            for (EmployeeGroupDto empGroup : empGroups) {
                if (StringUtils.isEmpty(empGroup.getGroupExp())) {
                    continue;
                }
                // 根据规则筛选员工
                List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, empGroup.getGroupExp(), conditionMap.get(empGroup.getBusinessKey()));
                if (CollectionUtils.isEmpty(groupEmpIds)) {
                    continue;
                }
                groupEmpIds.retainAll(unAssignedEmpIds);
                for (Long groupEmpId : groupEmpIds) {
                    EmpWorkInfo empInfo = assignedEmployeeMap.get(groupEmpId);
                    unAssignedEmpShifts.add(getWaEmpShiftDo(tenantId, groupEmpId, userId, Integer.valueOf(empGroup.getBusinessKey()), empInfo.getHireDate()));
                }
            }
            if (CollectionUtils.isNotEmpty(unAssignedEmpShifts)) {
                //保存员工分组
                addEmpShifts.addAll(unAssignedEmpShifts);
            }
        }
        if (CollectionUtils.isNotEmpty(addEmpShifts)) {
            addEmpShifts = addEmpShifts.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WaEmpShiftDo::getEmpid))), ArrayList::new));
            //保存员工日历
            waEmpShiftDo.batchSave(addEmpShifts);
        }
        //离职，在入职，更新
        if (CollectionUtils.isNotEmpty(updateEmpShifts)) {
            List<WaEmpShift> list = ObjectConverter.convertList(updateEmpShifts, WaEmpShift.class);
            //保存员工分组
            importService.fastUpdList(WaEmpShift.class, "empShiftId", list);
        }
        //离职，删除
        if (CollectionUtils.isNotEmpty(deleteEmpShifts)) {
            List<List<Integer>> deleteLists = ListTool.split(deleteEmpShifts, 50);
            for (List<Integer> list : deleteLists) {
                waEmpShiftDo.deleteEmpShifts(list);
            }
        }
    }

    private void setStartTime(Long hireDate, WaEmpShiftDo empScheme, List<WaEmpShiftDo> empShiftList, WaEmpShiftDo invalid) {
        if (null == empScheme) {
            return;
        }
        Long minDate = empScheme.getStartTime();
        if (null != hireDate) {
            minDate = Math.min(hireDate, minDate);
        }
        if (null != invalid) {
            minDate = DateUtil.getOnlyDate(new Date((invalid.getEndTime() + 86400) * 1000));
        } else if (CollectionUtils.isNotEmpty(empShiftList)) {
            Long latestStartTime = empShiftList.stream().max(Comparator.comparing(WaEmpShiftDo::getStartTime)).map(WaEmpShiftDo::getEndTime).orElse(null);
            if (null != latestStartTime && minDate < latestStartTime) {
                minDate = DateUtil.getOnlyDate(new Date((latestStartTime + 86400) * 1000));
            }
        }
        empScheme.setStartTime(minDate);
    }

    public boolean checkEmpGroup(Long empId, Long corpId, Integer workCalendarId, Map<Integer, EmployeeGroupDto> empGroupsMap,
                                 Map<String, String> conditionMap) {
        if (!empGroupsMap.containsKey(workCalendarId)) {
            return false;
        }
        EmployeeGroupDto assignedEmpGroup = empGroupsMap.get(workCalendarId);
        if (StringUtils.isEmpty(assignedEmpGroup.getGroupExp())) {
            return false;
        }
        // 根据规则筛选员工
        List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, assignedEmpGroup.getGroupExp(), conditionMap.get(assignedEmpGroup.getBusinessKey()));
        if (CollectionUtils.isEmpty(groupEmpIds)) {
            return false;
        }
        return groupEmpIds.contains(empId);
    }

    /**
     * 校验方案匹配条件
     *
     * @param empId        员工
     * @param corpId       集团
     * @param groupId      条件分组
     * @param exp          表达式
     * @param matchEmpMap  匹配的员工集合
     * @param conditionMap
     * @return
     */
    public boolean checkEmpSchemeMatchCondition(Long empId, Long corpId, Integer groupId, String exp, EmployeeGroupDto assignedEmpGroup, Map<Integer, List<Long>> matchEmpMap, Map<String, String> conditionMap) {
        if (StringUtil.isBlank(exp)) {
            return false;
        }
        //根据规则筛选员工
        List<Long> empIds;
        if (!matchEmpMap.containsKey(groupId)) {
            empIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, exp, conditionMap.get(assignedEmpGroup.getBusinessKey()));
            matchEmpMap.put(groupId, empIds);
        } else {
            empIds = matchEmpMap.get(groupId);
        }
        return empIds.contains(empId);
    }

    /**
     * 匹配的员工分组
     *
     * @param tenantId      租户
     * @param empId         员工
     * @param userId        用户
     * @param corpId        集团
     * @param today         当前日期（零点）
     * @param empSchemeMap  员工表达式分组
     * @param empSchemeList 员工方案
     * @param matchEmpMap
     * @param conditionMap
     * @return
     */
    public WaEmpShiftDo getMatchEmpScheme(String tenantId, Long empId, Long userId, Long corpId, Long today, Map<Integer, EmployeeGroupDto> empSchemeMap, List<WaEmpShiftDo> empSchemeList, Map<Integer, List<Long>> matchEmpMap, Map<String, String> conditionMap) {
        WaEmpShiftDo newEmpScheme = null;
        for (Map.Entry<Integer, EmployeeGroupDto> entry : empSchemeMap.entrySet()) {
            if (checkEmpSchemeMatchCondition(empId, corpId, entry.getKey(), entry.getValue().getGroupExp(), entry.getValue(), matchEmpMap, conditionMap)) {
                //匹配到方案
                newEmpScheme = getWaEmpShiftDo(tenantId, empId, userId, entry.getKey(), today);
                break;
            }
        }
        //判断是否有未生效方案，如有，则当前方案的失效日期取未生效方案开始日期（零点）前一秒
        Optional<WaEmpShiftDo> notYetEffectiveEmpSchemeOpt = empSchemeList.stream().filter(g -> g.getStartTime() > today).min(Comparator.comparing(WaEmpShiftDo::getStartTime));
        if (null != newEmpScheme && notYetEffectiveEmpSchemeOpt.isPresent()) {
            long startTime = notYetEffectiveEmpSchemeOpt.get().getStartTime();
            long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
            long endTime = notYetEffectiveEmpSchemeOpt.get().getEndTime();
            if (startTime == endTime && (startTime - startDate == 1)) {
                return newEmpScheme;
            }
            newEmpScheme.setEndTime(notYetEffectiveEmpSchemeOpt.get().getStartTime() - 1);
        }
        return newEmpScheme;
    }

    private WaEmpShiftDo getWaEmpShiftDo(String belongOrgId, Long empId, Long userId, Integer workCalendarId, Long hireDate) {
        long currentTime = System.currentTimeMillis() / 1000L;
        WaEmpShiftDo empShift = new WaEmpShiftDo();
        empShift.setWorkCalendarId(workCalendarId);
        empShift.setBelongOrgid(belongOrgId);
        empShift.setEmpid(empId);
        empShift.setStartTime(hireDate == null ? 1L : hireDate);
        // 9999-12-31
        empShift.setEndTime(253402271999L);
        empShift.setCrtuser(userId);
        empShift.setCrttime(currentTime);
        empShift.setUpduser(userId);
        empShift.setUpdtime(currentTime);
        return empShift;
    }

    @Override
    public boolean checkWorkCalendarIfAssigned(Integer workCalendarId) {
        UserInfo userInfo = this.getUserInfo();
        List<WaEmpShiftDo> items = waEmpShiftDo.getEmpShiftListByWorkCalendarId(userInfo.getTenantId(), workCalendarId);
        return CollectionUtils.isNotEmpty(items);
    }

    @Transactional
    @Override
    public void synchronizeEmpShift() {
        SysCorpOrgExample example = new SysCorpOrgExample();
        example.createCriteria().andOrgtype2EqualTo(1).andStatusEqualTo(1);
        List<SysCorpOrg> items = sysCorpOrgMapper.selectByExample(example);
        for (SysCorpOrg item : items) {
            log.info("自动同步员工日历，参数:[belongOrgId:{}，corpId:{}]", item.getBelongOrgId(), item.getCorpid());
            try {
                synchronizeEmpShift(item.getOrgid().toString(), null, item.getCorpid());
            } catch (Exception e) {
                log.error("自动同步员工日历异常:{}", e.getMessage(), e);
            }
            log.info("自动同步员工日历结束");
        }
    }

    @Override
    @Transactional
    public void deleteEmpShifts(List<Integer> ids) {
        List<WaEmpShiftDo> list = waEmpShiftDo.getEmpShiftByIds(ids);
        waEmpShiftDo.deleteEmpShifts(ids);
        if (CollectionUtils.isNotEmpty(list)) {
            dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_SHIFT.name(), ModuleTypeEnum.EMP_SHIFT.getTable(), list, getUserInfo()));
        }
    }

    @Override
    public List<EmpCalendarInfoDto> getEmpCalendarList(ListEmpRelCalendarQueryDto queryDto) {
        return waWorktimeDo.getEmpRelCalendarList(queryDto);
    }

    @Override
    public List<WaShiftDo> getEmpCalendarShiftList(String belongOrgId, Long startDate, Long endDate, List<Long> empIds) {
        return waWorktimeDo.getEmpCalendarShiftList(belongOrgId, startDate, endDate, empIds);
    }

    @Override
    public List<Map> getEmpCalendarShiftList(String belongOrgId, Long startDate, Long endDate, Long empId, String dataScope) {
        return waWorktimeDo.getEmpCalendarShiftList(belongOrgId, startDate, endDate, empId, dataScope);
    }

    @Override
    public List<Map> getChangeShiftDefListByEmpId(String belongOrgId, Long empId, Long startDate, Long endDate) {
        return waWorktimeDo.getChangeShiftDefListByEmpId(belongOrgId, empId, startDate, endDate);
    }
}
