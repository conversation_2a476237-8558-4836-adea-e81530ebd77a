package com.caidaocloud.attendance.core.annoation.feign;

import com.caidaocloud.attendance.core.annoation.dto.WorkplaceDto;
import com.caidaocloud.hr.service.vo.adapter.JobGradeDataOutVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "${feign.rename.caidaocloud-hr-service:caidaocloud-hr-service}",
        fallback = HrServiceFeignFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "attendanceHrServiceFeignClient")
public interface HrServiceFeignClient {
    @GetMapping("/api/hr/workplace/v1/selectList")
    Result<List<WorkplaceDto>> getWorkPlace();

    @GetMapping("/api/hr/job/output/v1/selectAll")
    Result<List<JobGradeDataOutVo>> getJobGrade();
}
