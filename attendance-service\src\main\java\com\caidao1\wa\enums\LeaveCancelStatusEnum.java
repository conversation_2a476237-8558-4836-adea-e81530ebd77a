package com.caidao1.wa.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

public enum LeaveCancelStatusEnum {

    LEAVE_CANCEL_IN(1, "销假中", AttendanceCodes.RETURNING_LEAVE),
    LEAVE_CANCEL_PASSED(2, "已销假", AttendanceCodes.ABANDONED_LEAVE),
    NO_LEAVE_CANCEL(3, "未销假", AttendanceCodes.UNRELEASED_LEAVE);

    private Integer index;
    private String name;
    private Integer code;

    LeaveCancelStatusEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(int index) {
        for (LeaveCancelStatusEnum c : LeaveCancelStatusEnum.values()) {
            if (c.getIndex() == index) {
                String msg = "";
                try {
                    msg = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(msg)) {
                    return msg;
                } else {
                    return c.name;
                }
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }
}
