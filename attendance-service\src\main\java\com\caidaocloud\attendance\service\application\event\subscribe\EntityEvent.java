package com.caidaocloud.attendance.service.application.event.subscribe;

import com.caidaocloud.msg.message.AbstractBasicMessage;
import lombok.Data;

import java.util.List;

@Data
public class EntityEvent extends AbstractBasicMessage {
    private List<EntityDataChange> data;
    private String dataOperationType;
    private String relationOperationType;
    private Boolean isRelationOperation;
    private String eventIdentifier;
    private String dataId;
}
