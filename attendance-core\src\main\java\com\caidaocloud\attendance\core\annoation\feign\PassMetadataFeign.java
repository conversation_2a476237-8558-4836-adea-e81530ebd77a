package com.caidaocloud.attendance.core.annoation.feign;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.TxFeignConfiguration;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "${feign.rename.caidaocloud-hr-paas-service:caidaocloud-hr-paas-service}",
        configuration = {FeignConfiguration.class, TxFeignConfiguration.class},
        contextId = "attendanceMetadataFeign"
)
public interface PassMetadataFeign {
    @GetMapping({"/api/hrpaas/v1/metadata"})
    Result one(@RequestParam("identifier") String identifier);
}
