package com.caidaocloud.attendance.service.application.enums;

public enum ClockWayEnum {

    GPS(1, "GPS"),
    CODE(2, "扫码签到"),
    FIELD(3, "外勤签到"),
    BLUETOOTH(4, "蓝牙"),
    WIFI(5, "WIFI"),
    FILLCLOCK(6, "补卡");

    private Integer index;

    private String name;

    ClockWayEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ClockWayEnum c : ClockWayEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
