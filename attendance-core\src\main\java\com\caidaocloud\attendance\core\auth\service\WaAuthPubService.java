package com.caidaocloud.attendance.core.auth.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.caidao1.auth.mybatis.mapper.AuthDataScopeMapper;
import com.caidao1.auth.mybatis.mapper.AuthGroupRoleRelMapper;
import com.caidao1.auth.mybatis.mapper.AuthUserRoleRelMapper;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.AuthUserRoleRel;
import com.caidao1.auth.mybatis.model.AuthUserRoleRelExample;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.service.AuthEmpGroupService;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.utils.CharmUtlis;
import com.caidao1.commons.utils.DateUtils;
import com.caidao1.commons.utils.JacksonJsonUtil;
import com.caidao1.form.mybatis.mapper.SysColCustomMapper;
import com.caidao1.ioc.util.SessionHolder;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 和caidao_common中的AuthPubService重名，故改名为WaAuthPubService
 */
@Service
@Slf4j
public class WaAuthPubService {
    private static final String PAYROLL_SPLIT_ROLE_ = "PAYROLL_SPLIT_ROLE_";
    @Autowired
    private AuthDataScopeMapper authDataScopeMapper;
    @Autowired
    private AuthUserRoleRelMapper authUserRoleRelMapper;
    @Autowired
    private SysColCustomMapper sysColCustomMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private AuthEmpGroupService authEmpGroupService;
    @Autowired
    private AuthGroupRoleRelMapper authGroupRoleRelMapper;

    @SuppressWarnings({"rawtypes", "unchecked"})
    public String getDataScope(UserInfo sessionBean, Integer resId, Integer pageId, String alias, Integer roleId, Integer wfFuncId) {
        Map map = new HashMap();
        map.put("userId", sessionBean.getUserid());
        map.put("roleId", roleId);
        map.put("pageId", pageId);
        map.put("resId", resId);

        List<Map> dataScopeList = authDataScopeMapper.getDataScope(map);
        return returnScopeExp(alias, dataScopeList, sessionBean.getTenantId(), sessionBean.getStaffId(), sessionBean.getOrgId(), wfFuncId, sessionBean.getUserId());
    }

    public String getDataScope(Integer resId, Integer pageId, String alias, String aliass, Integer roleId, Integer wfFuncId) {
        Long userId = SessionHolder.getUserId();
        String belongid = SessionHolder.getBelongOrgId();
        Long orgid = SessionHolder.getOrgId();
        Long empid = SessionHolder.getEmpId();

        Map map = new HashMap();
        map.put("userId", userId);
        map.put("roleId", roleId);
        map.put("pageId", pageId);
        map.put("resId", resId);

        List<Map> dataScopeList = authDataScopeMapper.getDataScope(map);
        return returnScopeExp(alias, aliass, dataScopeList, belongid, empid, orgid, wfFuncId, userId);
    }


    public String parmValStrToSql(String parmValStr, String alises) throws Exception {
        List<Map> list = (List<Map>) JacksonJsonUtil.jsonToBean(parmValStr, List.class);
        String key = alises;
        StringBuffer sb = new StringBuffer();
        sb.append(" and (");
        for (int i = 0; i < list.size(); i++) {
            Map map = list.get(i);

            //首次市为空
            if (i == 0 && CharmUtlis.isEmpty(map.get("cityId"))) {
                sb.append(" ( " + key + "prov_id=" + map.get("provId") + ")");
            }
            if (!CharmUtlis.isEmpty(map.get("cityId"))) {
                if (i > 0) {
                    sb.append(" or ");
                }
                sb.append("(" + key + "city_id =" + map.get("cityId") + " and " + key + "prov_id=" + map.get("provId") + ")");
            } else if (i > 0 && CharmUtlis.isEmpty(map.get("cityId"))) {
                sb.append("or( " + key + "prov_id=" + map.get("provId") + ")");
            } else {
                sb.append(" ");
            }
        }
        sb.append(")");
        return sb + "";

    }

    public static void getMapForJson(String jsonStr) {
        Map map = (Map) JSON.parse(jsonStr);
        for (Object maps : map.entrySet()) {
            Map.Entry maps1 = (Map.Entry) maps;
            if (maps1 != null && maps1.getValue() != null) {
                String value = maps1.getValue().toString();
                if (value.contains("{") && value.contains("}")) {
                    if (value.startsWith("[") && value.endsWith("]")) {
                        JSONArray jsonArray = JSONArray.parseArray(value);
                        Iterator<Object> iterator = jsonArray.iterator();
                        while (iterator.hasNext()) {
                            getMapForJson(iterator.next().toString());
                        }
                    } else {
                        getMapForJson(value);
                    }
                } else {
                }
            }
        }
    }

    private String returnScopeExp(String alias, String aliass, List<Map> dataScopeList, String belongid, Long empid, Long orgid, Integer wfFuncId, Long userid) {
        if (StringUtils.isBlank(alias)) {
            alias = "";
        } else {
            alias = alias + ".";
        }
        if (StringUtils.isBlank(aliass)) {
            aliass = "";
        } else {
            aliass = aliass + ".";
        }
        StringBuffer sqlApend = new StringBuffer();
        if (dataScopeList.size() == 0) {
            return "";
        } else {
            int i = 0;
            for (Map mapResult : dataScopeList) {
                String str = "  ";
                if (i > 0 && !sqlApend.toString().endsWith("and")) {
                    //str = str+" and ";
                    sqlApend.append(" and ");
                }
                String exp = mapResult.get("exp").toString();
                Integer matchType = (Integer) mapResult.get("match_type");
                Integer specType = (Integer) mapResult.get("spec_type");
                Boolean is_negate = (Boolean) mapResult.get("is_negate");

                //社保json
                String parm_var_str = mapResult.get("parm_val_str").toString();
                if (is_negate != null && is_negate.booleanValue()) {
                    exp = alias + mapResult.get("negate_exp").toString();
                }
                if (matchType.intValue() == 3) {
                    try {
                        str += exp.replaceAll("\\$\\{scope}", parmValStrToSql(parm_var_str, aliass));
                    } catch (Exception e) {
                        e.printStackTrace();
                        System.out.println(e.getMessage() + " sql : " + str);
                    }
                }
                sqlApend.append(str);
            }
        }

        return sqlApend + "";
    }

    private String returnScopeExp(String alias, List<Map> dataScopeList, String belongid, Long empid, Long orgid, Integer wfFuncId, Long userid) {
        if (StringUtils.isBlank(alias)) {
            alias = "";
        } else {
            alias = alias + ".";
        }
        StringBuffer sqlApend = new StringBuffer();
        if (dataScopeList.size() == 0) {
            return "";
        } else {
            int i = 0;
            for (Map mapResult : dataScopeList) {
                String str = "  ";
                if (i > 0 && !sqlApend.toString().endsWith("and")) {
                    //str = str+" and ";
                    sqlApend.append(" and ");
                }
                String exp = alias + mapResult.get("exp").toString();
                Integer matchType = (Integer) mapResult.get("match_type");
                Integer specType = (Integer) mapResult.get("spec_type");
                Boolean is_negate = (Boolean) mapResult.get("is_negate");

                //社保json
                String parm_var_str = mapResult.getOrDefault("parm_val_str", "[]").toString();
                if (is_negate != null && is_negate.booleanValue()) {
                    exp = alias + mapResult.get("negate_exp").toString();
                }
                if (matchType.intValue() == 3) {
                    try {
                        str += exp.replaceAll("\\$\\{scope}", parmValStrToSql(parm_var_str, ""));
                    } catch (Exception e) {
                        e.printStackTrace();
                        System.out.println(e.getMessage() + " sql : " + str);
                    }
                }

                if (matchType.intValue() == 1) {//自动匹配
                    if (specType == 9) {
                        //ext_custom_col->>${EXT_COL_FIELD} = any(${EXT_COL_VAL})
//					 		Jdbc4Array paramValArr = (Jdbc4Array)mapResult.get("parm_val");
                        String customField = (String) mapResult.get("custom_field");
                        //获取指定扩展字段值
                        Integer colCustomId = Integer.valueOf(customField.replaceAll(BaseConst.FIX_EXT_COLUMN, ""));
                        String paramValStr = getExtCustomValue(colCustomId, belongid, empid);

                        str += exp.replaceAll("\\$\\{EXT_COL_FIELD\\}", "'" + customField + "'").replaceAll("\\$\\{EXT_COL_VAL\\}", "'" + paramValStr + "'");
                    } else if (specType == 4) {
                        SysEmpInfo empinfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
                        String paramValStr = "{0}";
                        if (empinfo.getStoreId() != null) {
                            paramValStr = "{" + empinfo.getStoreId() + "}";
                        }
                        str += exp.replaceAll("\\$\\{storeId\\}", "'" + paramValStr + "'");
                    } else {
                        str += exp.replaceAll("\\$\\{belongOrgId\\}", "'{" + belongid + "}'").
                                replaceAll("\\$\\{orgid\\}", "'{" + orgid + "}'").replaceAll("\\$\\{empid\\}", String.valueOf(empid)).
                                replaceAll("\\$\\{belongid\\}", String.valueOf(belongid)).replaceAll("\\$\\{empids\\}", "'{" + empid + "}'").replaceAll("\\$\\{userid\\}", String.valueOf(userid));
                    }
                    if (wfFuncId != null) {
                        str = str.replaceAll("\\$\\{wfFuncId\\}", wfFuncId.toString());
                    }
                } else if (matchType.intValue() == 2) {//指定
                    try {
//						 Jdbc4Array paramValArr = (Jdbc4Array)mapResult.get("parm_val");
                        Object paramValArr = mapResult.get("parm_val");
                        String paramValStr = paramValArr.toString();
                        if (specType.intValue() == 1) {
                            //指定公司
                            str += exp.replaceAll("\\$\\{belongOrgId\\}", "'" + paramValStr + "'");
                        } else if (specType.intValue() == 2) {
                            //指定部门
                            str += exp.replaceAll("\\$\\{orgid\\}", "'" + paramValStr + "'");
                        } else if (specType.intValue() == 3) {
                            //查看指定岗位
                            str += exp.replaceAll("\\$\\{postId\\}", "'" + paramValStr + "'");
                        } else if (specType.intValue() == 4) {
                            //指定门店
                            //store_id=any (${storeId})
                            str += exp.replaceAll("\\$\\{storeId\\}", "'" + paramValStr + "'");
                        } else if (specType.intValue() == 5) {
                            //指定到员工
                            str += exp.replaceAll("\\$\\{empid\\}", "'" + paramValStr + "'");
                        } else if (specType.intValue() == 6) {
                            //指定办公地点
                            str += exp.replaceAll("\\$\\{siteId\\}", "'" + paramValStr + "'");
                        } else if (specType.intValue() == 7) {
                            //查看用工类型
                            str += exp.replaceAll("\\$\\{eeType\\}", "'" + paramValStr + "'").replaceAll("\\$\\{belongOrgId\\}", "'{" + belongid + "}'").replaceAll("\\$\\{alias\\}", alias);
                        } else if (specType.intValue() == 9) {
                            //ext_custom_col->>${EXT_COL_FIELD} = any(${EXT_COL_VAL})
                            Object paramVal = mapResult.get("parm_val");
                            String customField = (String) mapResult.get("custom_field");
                            String paramValStr2 = paramVal.toString();
                            str += exp.replaceAll("\\$\\{EXT_COL_FIELD\\}", "'" + customField + "'").replaceAll("\\$\\{EXT_COL_VAL\\}", "'" + paramValStr2 + "'");
                        } else if (specType.intValue() == 8) {
                            //查看所属第X级部门及子部门
                            String paramValT = paramValStr;
                            str += exp.replaceAll("\\$\\{orgLevel\\}",
                                    paramValT.replaceAll("\\{", "").replaceAll("\\}", "")).
                                    replaceAll("\\$\\{belongOrgId\\}", String.valueOf(belongid)).
                                    replaceAll("\\$\\{orgid\\}", orgid + "");
                        } else if (specType.intValue() == 50) {
                            //指定模版数据
                            str += exp.replaceAll("\\$\\{templateId\\}", "'" + paramValStr + "'");
                        } else if (specType.equals(10)) {
                            //指定员工类型
                            str += exp.replaceAll("\\$\\{employType\\}", "'" + paramValStr + "'").replaceAll("\\$\\{belongOrgId\\}", "'{" + belongid + "}'").replaceAll("\\$\\{alias\\}", alias);
                        } else if (specType.intValue() == 11) {
                            //查看指定多个岗位
                            str += exp.replaceAll("\\$\\{postId\\}", "'" + paramValStr + "'");
                        } else if (specType.intValue() == 12) {
                            //查看指定多个薪资结构
                            str += exp.replaceAll("\\$\\{strucMainId\\}", "'" + paramValStr + "'");
                        } else if (specType.intValue() == 13) {
                            //查看指定多个员工分组
                            str += exp.replaceAll("\\$\\{empGroupId\\}", "'" + paramValStr + "'").replaceAll("\\$\\{userid\\}", String.valueOf(userid));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        return "";
                    }
                } else if (matchType.intValue() == 4) {
                    try {
//						 Jdbc4Array paramValArr = (Jdbc4Array)mapResult.get("parm_val");
                        Object paramValArr = mapResult.get("parm_val");
                        String paramValStr = paramValArr.toString();
                        if (specType.intValue() == 9) {
                            // 查看指定多个成本中心
                            str += exp.replaceAll("\\$\\{costId\\}", "'" + paramValStr + "'");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        return "";
                    }
                } else { //社保公积金地区权限

                }
                //如果是启用了取反的情况
//				 if(is_negate && StringUtils.isNotBlank(str)){
//					 str = " NOT ("+str+") ";
//				 }
                if (is_negate != null && is_negate.booleanValue() && specType.intValue() > 1) {
                    str = str.replaceAll("\\$\\{belongOrgId\\}", "'{" + belongid + "}'");
                }
                i++;
                if (str.contains("${CYM}")) {
                    str = str.replaceAll("\\$\\{CYM\\}", DateUtils.format(new Date(), "yyyyMM"));
                }
                sqlApend.append(str.replace("${alias}", alias));
            }
        }
        if (sqlApend.toString().trim().endsWith("and")) {
            sqlApend.substring(0, sqlApend.length() - 3);
        }
        return " (" + sqlApend + ") ";
    }


    private String getExtCustomValue(Integer colCustomId, String belongid, Long empid) {
        String tablename = sysColCustomMapper.getTabelNameByCustomId(colCustomId, belongid);
        String extField = BaseConst.FIX_EXT_COLUMN + colCustomId;
        // 获取指定表的指定扩展字段的值
        String val = sysEmpInfoMapper.getEmpExtFieldValByField(tablename, extField, empid);
        if (StringUtils.isBlank(val)) {
            val = "{0}";
        } else {
            val = "{" + val + "}";
        }
        return val;
    }

    public String getDataScopeByRes(Integer resId, String alias, String defaultSql) {
        return getDataScope(resId, null, alias, defaultSql, null);
    }

    public String getDataScopeByRes(Integer resId, String alias, String aliass, String defaultSql) {
        return getDataScope(resId, null, alias, aliass, defaultSql, null);
    }

    public String getDataScopeByRes(Integer resId, String alias, String defaultSql, Integer wfFuncId) {
        return getDataScope(resId, null, alias, defaultSql, wfFuncId);
    }

    public String getDataScope(Integer pageId, String alias, String defaultSql) {
        return getDataScope(null, pageId, alias, defaultSql, null);
    }

    //社保公积金
    public String getDataScope(Integer resId, Integer pageId, String alias, String aliass, String defaultSql, Integer wfFuncId) {
        Long userId = SessionHolder.getUserId();

        if (StringUtils.isBlank(defaultSql)) {
            defaultSql = "belong_org_id=#{belongId}";
        }
        // 增加默认sql 是否必须
        String defaultBelongidSql = " ";
        if (StringUtils.isNotBlank(alias) && StringUtils.isNotBlank(defaultSql)) {
            defaultBelongidSql = " and " + getAliasStr(alias).concat(defaultSql);
        }
        StringBuffer rtSql = new StringBuffer();

        AuthUserRoleRelExample relExample = new AuthUserRoleRelExample();
        AuthUserRoleRelExample.Criteria relCriteria = relExample.createCriteria();
        relCriteria.andUseridEqualTo(userId);
        List<AuthUserRoleRel> relList = authUserRoleRelMapper.selectByExample(relExample);
        List<Integer> roleIds = authEmpGroupService.getUserRole(userId, SessionHolder.getEmpId(), SessionHolder.getBelongOrgId(), false, 2);

        String tmpStr = "";
        if (relList.size() == 0 && CollectionUtils.isEmpty(roleIds)) {
            if (StringUtils.isNotBlank(defaultSql)) {
                rtSql.append(defaultBelongidSql);
            } else {
                rtSql.append(" and " + getAliasStr(alias) + "belong_org_id=#{belongId}");
            }
        } else {
            int i = 0;
            for (AuthUserRoleRel rel : relList) {
                tmpStr = this.getDataScope(resId, pageId, alias, aliass, rel.getRoleId(), wfFuncId);

            }
        }
        rtSql.append(defaultBelongidSql);
        return rtSql.toString() + tmpStr;

    }

    @Autowired
    private ISessionService sessionService;

    public String getDataScope(Integer resId, Integer pageId, String alias, String defaultSql, Integer wfFuncId) {
        UserInfo sessionBean = sessionService.getUserInfo();
        if (StringUtils.isBlank(defaultSql)) {
            defaultSql = "belong_org_id=#{belongId}";
        }
        String defaultBelongidSql = " and " + getAliasStr(alias).concat(defaultSql);

        StringBuilder rtSql = new StringBuilder();

        AuthUserRoleRelExample relExample = new AuthUserRoleRelExample();
        AuthUserRoleRelExample.Criteria relCriteria = relExample.createCriteria();
        relCriteria.andUseridEqualTo(sessionBean.getUserId());
        List<AuthUserRoleRel> relList = authUserRoleRelMapper.selectByExample(relExample);
        List<Integer> roleIds = authEmpGroupService.getUserRole(sessionBean.getUserId(), sessionBean.getStaffId(), sessionBean.getTenantId(), false, 2);
        if (relList.size() == 0 && CollectionUtils.isEmpty(roleIds)) {
            if (StringUtils.isNotBlank(defaultSql)) {
                rtSql.append(defaultBelongidSql);
            } else {
                rtSql.append(" and ").append(getAliasStr(alias)).append("belong_org_id=#{belongId}");
            }
        } else {
            int i = 0;
            String str = "and (";
            for (AuthUserRoleRel rel : relList) {
                String tmpStr = this.getDataScope(sessionBean, resId, pageId, alias, rel.getRoleId(), wfFuncId);
                if (StringUtils.isNotBlank(tmpStr)) {
                    if (i > 0) {
                        str = str + " or " + tmpStr;
                    } else {
                        str = str + tmpStr;
                    }
                    i++;
                }
            }
            if (pageId != null) {
                for (Integer role : roleIds) {
                    Map map = new HashMap();
                    if (role != null) {
                        map.put("roleId", role);
                        map.put("pageId", pageId);
                        List<Map> roleGroupDataScope = authGroupRoleRelMapper.getAuthScopeData(map);
                        String tmpStr = this.returnScopeExp(alias, roleGroupDataScope, sessionBean.getTenantId(), sessionBean.getStaffId(), sessionBean.getOrgId(), wfFuncId, sessionBean.getUserId());
                        if (StringUtils.isNotBlank(tmpStr)) {
                            if (i > 0) {
                                str = str + " or " + tmpStr;
                            } else {
                                str = str + tmpStr;
                            }
                            i++;
                        }
                    }
                }
            }
            if (str.equals("and (")) {
                rtSql.append(defaultBelongidSql);
            } else {
                rtSql.append(str + ")");
            }
        }
        return rtSql.toString();
    }

    public String getAliasStr(String alias) {
        if (StringUtils.isBlank(alias)) {
            alias = "";
        } else {
            alias = alias + ".";
        }
        return alias;
    }

    public Map<Integer, List<Map>> getDataScope2(Integer pageId) {
        Long userId = SessionHolder.getUserId();

        AuthUserRoleRelExample relExample = new AuthUserRoleRelExample();
        AuthUserRoleRelExample.Criteria relCriteria = relExample.createCriteria();
        relCriteria.andUseridEqualTo(userId);
        List<AuthUserRoleRel> relList = authUserRoleRelMapper.selectByExample(relExample);

        if (relList.size() == 0) {
            return null;
        } else {
            Map<Integer, List<Map>> rtMap = new HashMap<Integer, List<Map>>();

            Map map = new HashMap();
            map.put("userId", userId);
            map.put("pageId", pageId);

            for (AuthUserRoleRel rel : relList) {
                Integer roleid = rel.getRoleId();
                map.put("roleId", roleid);
                List<Map> list = authDataScopeMapper.getDataScope(map);
                if (list != null && list.size() > 0) {
                    rtMap.put(roleid, list);
                }
            }
            return rtMap;
        }
    }

    public boolean isJustOpen(Integer belongid) {
        List<Integer> roles = SessionHolder.getRoles();
        boolean justOpen = false;
        if (CollectionUtils.isNotEmpty(roles)) {
            for (Integer role : roles) {
                String isSplit = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongid + "_" + PAYROLL_SPLIT_ROLE_ + role);
                if (isSplit != null && "1".equals(isSplit)) {
                    justOpen = true;
                    break;
                }
            }
        }
        return justOpen;
    }
}
