package com.caidaocloud.attendance.core.commons.utils;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MapUtil {

    public static Object getValue(Map map, String keyParam) {
        String[] keys = keyParam.split("\\.");
        Object result = null;
        for (String key : keys) {
            result = map.get(key);
            if (result instanceof Map) {
                map = (Map) result;
            }
        }
        return result;
    }

    public static Map listToMap(List<Map> list) {
        Map map =new HashMap();
        map.put("pos",0);
        map.put("total_count",list.size());
        map.put("rows",list);
        return map;
    }

    public static String getString(Map map, Object key, String defaultValue) {
        String answer = MapUtils.getString(map, key);
        if (StringUtils.isEmpty(answer)) {
            answer = defaultValue;
        }

        return answer;
    }
}
