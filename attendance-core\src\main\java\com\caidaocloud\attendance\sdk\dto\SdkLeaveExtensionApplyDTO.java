package com.caidaocloud.attendance.sdk.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SdkLeaveExtensionApplyDTO {
    @ApiModelProperty("员工ID")
    private Long empId;
    @ApiModelProperty("延期假期")
    private List<SdkLeaveExtensionApplyItem> items;
    @ApiModelProperty("附件名称，多个使用逗号隔开")
    private String fileName;
    @ApiModelProperty("附件ID，多个使用逗号隔开")
    private String file;
    @ApiModelProperty("延期假期类型")
    private Long configId;
}
