package com.caidaocloud.attendance.service.application.dto.clock;

import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FilterBelongYesRegDto extends ClockAnalyseFilterAbstractDto {
    public static FilterBelongYesRegDto doBuild(Long empId,
                                                Long clockDate,
                                                List<WaRegisterRecordDo> regList,
                                                ClockAnalyseDataCacheDto dataCacheDto,
                                                Map<String, WaShiftDo> empShiftDoMap) {
        FilterBelongYesRegDto dto = new FilterBelongYesRegDto();
        dto.setEmpId(empId);
        dto.setClockDate(clockDate);
        dto.setRegList(regList);
        dto.setDataCacheDto(dataCacheDto);
        dto.setEmpShiftDoMap(empShiftDoMap);
        return dto;
    }
}
