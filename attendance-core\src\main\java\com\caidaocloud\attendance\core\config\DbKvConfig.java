package com.caidaocloud.attendance.core.config;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

//@Order(-2147483637)
public class DbKvConfig implements EnvironmentPostProcessor, Ordered {

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment env, SpringApplication application) {

        MutablePropertySources propertySources = env.getPropertySources();
        Map<String, Object> propertySource = new HashMap<>();
        try {
            String url = env.getProperty("spring.datasource.url"),
                    username = env.getProperty("spring.datasource.username"),
                    password = env.getProperty("spring.datasource.password");

            if (null == url || "".equals(url)) {
                return;
            }

            DriverManagerDataSource ds = new DriverManagerDataSource(url, username, password);
            ds.setDriverClassName("org.postgresql.Driver");
            try (Connection connection = ds.getConnection()) {
                PreparedStatement preparedStatement = connection.prepareStatement("select key,value from sys_config_kv where corpid=0 and status=1 AND type=?");
                preparedStatement.setString(1, "SYS");
                ResultSet rs = preparedStatement.executeQuery();
                while (rs.next()) {
                    String propName = rs.getString("key");
                    propertySource.put(propName, rs.getObject("value"));
                }
                propertySources.addLast(new MapPropertySource("dbPropertySource", propertySource));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @Override
    public int getOrder() {
        // 2147483647
        return LOWEST_PRECEDENCE;
    }
}

