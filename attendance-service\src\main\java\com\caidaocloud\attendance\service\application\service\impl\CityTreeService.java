package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.StringUtil;
import com.caidao1.system.mybatis.mapper.SysUnitCityMapper;
import com.caidao1.system.mybatis.mapper.SysUnitCountryMapper;
import com.caidao1.system.mybatis.model.SysUnitCity;
import com.caidao1.system.mybatis.model.SysUnitCityExample;
import com.caidaocloud.attendance.service.interfaces.vo.CityTreeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/8/11 15:45
 * @Version 1.0
 */
@Service
@Slf4j
public class CityTreeService {

    @Autowired
    private SysUnitCityMapper sysUnitCityMapper;
    @Autowired
    private SysUnitCountryMapper sysUnitCountryMapper;

    public List<CityTreeVo> getCityTree() {
        SysUnitCityExample sysUnitCityExample = new SysUnitCityExample();
        SysUnitCityExample.Criteria criteria = sysUnitCityExample.createCriteria();
        criteria.andStatusEqualTo(1);//有效
        criteria.andTypeIn(Arrays.asList(new Integer[]{1, 2}));//省市
        List<SysUnitCity> sysUnitCities = sysUnitCityMapper.selectByExample(sysUnitCityExample);
        List<CityTreeVo> cityTreeVos = new ArrayList<>();
        for (SysUnitCity unitCity : sysUnitCities) {
            CityTreeVo cityTreeVo = new CityTreeVo();
            BeanUtils.copyProperties(unitCity, cityTreeVo);
            cityTreeVos.add(cityTreeVo);
        }
        if (null == cityTreeVos || cityTreeVos.isEmpty()) {
            return cityTreeVos;
        }
        cityTreeVos = listToTree(cityTreeVos);
        return cityTreeVos;
    }

    private List<CityTreeVo> listToTree(List<CityTreeVo> list) {
        List<CityTreeVo> treeList = new ArrayList<>(list.size());
        Map<Long, List<CityTreeVo>> orgTreeMap = getOrgTreeMap(list);
        List<CityTreeVo> parentList = orgTreeMap.getOrDefault(0L, null);
        if (!CollectionUtils.isEmpty(parentList)) {
            treeList.addAll(parentList);
            list.forEach(item -> {
                List<CityTreeVo> childList = orgTreeMap.getOrDefault(item.getCityId(), null);
                if (childList != null) {
                    item.setChildren(mergeList(childList));
                }
                orgTreeMap.remove(item.getCityId());
            });

            for (Long orgKey : orgTreeMap.keySet()) {
                if (null != orgKey && orgKey.equals(0L)) {
                    continue;
                }
                treeList.get(0).getChildren().addAll(orgTreeMap.get(orgKey));
            }
        }
        return treeList;
    }

    /**
     * 根据 parentId 分组
     */
    private Map<Long, List<CityTreeVo>> getOrgTreeMap(List<CityTreeVo> cityList) {
        if (!CollectionUtils.isEmpty(cityList)) {
            return cityList.stream().filter(item -> item.getCityPid() != null).collect(Collectors.groupingBy(CityTreeVo::getCityPid));
        }
        return new HashMap<>();
    }

    private List<CityTreeVo> mergeList(List<CityTreeVo> ch1) {
        ArrayList<CityTreeVo> mList = new ArrayList<>();
        if (ch1 != null) {
            mList.addAll(ch1);
        }
        return mList;
    }


    public List searchCityByType(Integer countryId, Integer cityType, Long cityPid, String lang) {
        List result = new ArrayList();
        Map params = new HashMap();
        params.put("lang", lang);
        try {
            if (cityType != null) {
                if (cityType == 0) {

                    List naionList = sysUnitCountryMapper.findNaionList(params);
                    for (int i = 0; i < naionList.size(); i++) {
                        Map map1 = (Map) naionList.get(i);
                        if (map1.get("text").toString().trim().contains("中国") || map1.get("text").toString().trim().contains("China")) {
                            result.add(map1);
                            naionList.remove(i);
                        }
                    }
                    Collections.reverse(result);
                    for (int i = 0; i < result.size(); i++) {
                        naionList.add(i, result.get(i));
                    }
                    for (int i = 0; i < naionList.size(); i++) {
                        Map map1 = (Map) naionList.get(i);
                        if (map1.get("text").toString().equals("中国香港")) {
                            naionList.remove(i);
                            naionList.add(1, map1);
                        }
                    }
                    return naionList;
                }
            }
            return sysUnitCityMapper.searchCityByType(countryId, cityType, cityPid, lang);
        } catch (Exception e) {
            log.error("error", e);
            return new ArrayList();
        }
    }


    public List searchCity(Integer countryId, Integer cityType, Long cityPid, Long pid, String lang, String isFirstNull) {
        if (countryId == null) {
            countryId = 1;  // 默认中国
        }
        if (pid != null) {
            cityPid = pid;
        }
        //--0,国籍  1、省、2市、3县
        List list = searchCityByType(countryId, cityType, cityPid, lang);

        if (!StringUtil.isEmptyOrNull(isFirstNull) && isFirstNull.equals("1")) {
            if (list != null && list.size() > 0) {
                Map map = new HashMap();
                map.put("text", "");
                map.put("value", "");
                list.add(0, map);
            }
        }
        return list;
    }
}
