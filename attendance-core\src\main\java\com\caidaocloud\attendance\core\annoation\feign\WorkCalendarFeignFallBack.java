package com.caidaocloud.attendance.core.annoation.feign;

import com.caidao1.wa.dto.EmpCalendarInfoDto;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpCalendarShiftQueryDto;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpRelCalendarQueryDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class WorkCalendarFeignFallBack implements WorkCalendarFeignClient {
    @Override
    public Result<List<EmpCalendarInfoDto>> listEmpRelCalendar(ListEmpRelCalendarQueryDto queryDto) {
        return Result.fail();
    }

    @Override
    public Result<List<Map>> getEmpCalendarShiftList(ListEmpCalendarShiftQueryDto queryDto) {
        return Result.fail();
    }

    @Override
    public Result<List<Map>> getEmpCalendarChangeShiftList(ListEmpCalendarShiftQueryDto queryDto) {
        return Result.fail();
    }
}
