package com.caidaocloud.attendance.core.wa.dto.clock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("打卡分析计算参数")
public class ClockAnalyseDto {
    @ApiModelProperty("租户ID")
    private String belongOrgId;
    @ApiModelProperty("员工ID集合")
    private List<Long> empIds;
    @ApiModelProperty("打卡日期")
    private Long date;
    @ApiModelProperty("打卡类型：1 GPS签到 2 扫码签到 3 外勤签到  4蓝牙签到  5 WIFI签到  6 补打卡")
    private Integer type;
    @ApiModelProperty("打卡日期范围，数据格式为：开始日期-结束日期，例如：1751299200-1751385600，可选")
    private String dateRange;

    public ClockAnalyseDto(String belongOrgId, List<Long> empIds, Long date, Integer type) {
        this.belongOrgId = belongOrgId;
        this.empIds = empIds;
        this.date = date;
        this.type = type;
    }

    public static String doContractDateRange(Long startDate, Long endDate) {
        if (null == startDate || null == endDate) {
            return null;
        }
        return String.format("%s-%s", startDate, endDate);
    }

    public List<Long> doGetDateRangeList() {
        if (StringUtils.isBlank(this.dateRange)) {
            return null;
        }
        String[] dateRangeArr = this.dateRange.split("-");
        if (dateRangeArr.length <= 1) {
            return null;
        }
        List<Long> dateRangeList = new ArrayList<>();
        dateRangeList.add(Long.valueOf(dateRangeArr[0]));
        dateRangeList.add(Long.valueOf(dateRangeArr[1]));
        return dateRangeList;
    }
}
