package com.caidaocloud.attendance.service.application.dto.msg;

import com.caidaocloud.attendance.service.application.enums.msg.MsgAccountType;
import com.caidaocloud.attendance.service.application.enums.msg.MsgPushType;
import lombok.Data;

@Data
public class AppMsgSenderDTO {

    private String userId;

    private MsgPushType msgType = MsgPushType.text;

    private String title;

    private String content;

    private Long createTime = System.currentTimeMillis() / 1000L;

    private MsgAccountType type = MsgAccountType.email;

    /**
     * 代办消息的图片地址
     */
    private String picUrls;

    private String url;

    private String msgId;

}
