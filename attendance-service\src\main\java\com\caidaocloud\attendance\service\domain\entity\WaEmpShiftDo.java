package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.repository.IEmpShiftRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpShiftPo;
import com.caidaocloud.util.ObjectConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 员工日历分配关系DO
 *
 * <AUTHOR>
 * @Date 2021/3/4
 */
@Slf4j
@Data
@Service
public class WaEmpShiftDo {
    private Integer empShiftId;
    private Integer workCalendarId;
    private Long empid;
    private String belongOrgid;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
    private Long crttime;
    private Long crtuser;
    private Long updtime;
    private Long upduser;

    //冗余信息
    private String workno;
    private String empName;
    private String workCalendarName;
    private String i18nWorkCalendarName;
    @ApiModelProperty("所属公司")
    private String corpName;
    @ApiModelProperty("所属部门")
    private String orgName;
    @ApiModelProperty("组织全路径")
    private String fullPath;
    @ApiModelProperty("操作人")
    private String updater;

    private Integer empStatus;
    private Long empStyle;
    private Long hireDate;
    private Long terminationDate;

    /**
     * 考勤类型：1 固定班次，2 排班制，3 自由打卡
     */
    private Integer worktimeType;

    @Autowired
    private IEmpShiftRepository empShiftRepository;

    public List<WaEmpShiftDo> getEmpShiftListByWorkCalendarId(String belongOrgId, Integer workCalendarId) {
        return empShiftRepository.getEmpShiftListByWorkCalendarId(belongOrgId, workCalendarId);
    }

    public int deleteByWorkCalendarId(Integer workCalendarId) {
        return empShiftRepository.deleteByWorkCalendarId(workCalendarId);
    }

    public List<WaEmpShiftDo> getEmpShiftInfoList(Long corpId, String belongOrgId, List<Long> empIds, Long startTime, Long endTime, Integer workCalendarId) {
        return empShiftRepository.getEmpShiftInfoList(corpId, belongOrgId, empIds, startTime, endTime, workCalendarId);
    }

    public AttendancePageResult<WaEmpShiftDo> getEmpShiftDetailListByCalendarId(AttendanceBasePage basePage,
                                                                                String belongOrgId, Integer calendarId,
                                                                                String filter, String effectiveStatus) {
        return empShiftRepository.getEmpShiftDetailListByCalendarId(basePage, belongOrgId, calendarId, filter, effectiveStatus);
    }

    public void saveEmpShift(WaEmpShiftDo waEmpShift) {
        if (null == waEmpShift) {
            return;
        }
        EmpShiftPo empShiftPo = ObjectConverter.convert(waEmpShift, EmpShiftPo.class);
        empShiftRepository.saveEmpShift(empShiftPo);
    }

    public List<WaEmpShiftDo> getEmpShiftByPeriod(Long empId, Integer empShiftId, String belongOrgId, Long startTime, Long endTime) {
        List<EmpShiftPo> list = empShiftRepository.getEmpShiftByPeriod(empId, empShiftId, belongOrgId, startTime, endTime);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(list, WaEmpShiftDo.class);
    }

    public void deleteEmpShifts(List<Integer> empShiftIds) {
        empShiftRepository.deleteEmpShifts(empShiftIds);
    }

    public WaEmpShiftDo getEmpShift(Integer empShiftId) {
        return empShiftRepository.getEmpShift(empShiftId);
    }

    public void batchSave(List<WaEmpShiftDo> empShifts) {
        empShiftRepository.batchSave(ObjectConverter.convertList(empShifts, EmpShiftPo.class));
    }

    public List<WaEmpShiftDo> getEmpShiftByIds(List<Integer> ids) {
        return ObjectConverter.convertList(empShiftRepository.getEmpShiftByIds(ids), WaEmpShiftDo.class);
    }
}
