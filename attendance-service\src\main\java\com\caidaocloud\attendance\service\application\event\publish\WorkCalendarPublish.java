package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WorkCalendarPublish {
    @Resource
    private MqMessageProducer<WorkCalendarMessage> producer;

    private final static String EXCHANGE = "attendance.workcalendar.fac.direct.exchange";

    private final static String ROUTING_KEY = "routingKey.workcalendar";

    public void publish(String msg) {
        WorkCalendarMessage message = new WorkCalendarMessage();
        message.setBody(msg);
        message.setExchange(EXCHANGE);
        message.setRoutingKey(ROUTING_KEY);
        producer.publish(message);
    }
}
