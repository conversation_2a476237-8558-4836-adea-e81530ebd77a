package com.caidaocloud.attendance.service.application.enums;

/**
 * 班次所属业务流程
 */
public enum ShiftBusinessProcessEnum {
    BLADE("blade", "叶片");

    private String code;
    private String name;

    ShiftBusinessProcessEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (ShiftBusinessProcessEnum c : ShiftBusinessProcessEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
