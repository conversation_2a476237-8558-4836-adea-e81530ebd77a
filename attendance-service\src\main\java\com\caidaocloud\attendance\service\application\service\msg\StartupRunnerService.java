package com.caidaocloud.attendance.service.application.service.msg;

import com.caidaocloud.attendance.service.application.service.workflow.RegisterMsgVarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class StartupRunnerService implements CommandLineRunner {

    @Value("${wf.msg.consumer.autoGenerate:false}")
    private boolean autoGenerateConsumer;
    @Value("${wf.msg.variable.autoRegister:false}")
    private boolean autoRegisterMsgVariable;
    @Resource
    private GenerateConsumerService generateConsumerService;
    @Resource
    private RegisterMsgVarService registerMsgVarService;

    @Override
    public void run(String... args) {
        if (autoGenerateConsumer) {
            try {
                log.info("********** Start to init consumer queue **********");
                initConsumer();
                log.info("********** End of initializing consumer queue **********");
            } catch (Exception ex) {
                log.error("Initialization information failed, exception information：{}", ex.getMessage(), ex);
            }
        }
        if (autoRegisterMsgVariable) {
            log.info("********** Start registering workflow message variables **********");
            registerMsgVarService.registerWorkflowMsgVar(null);
            log.info("********** Registration Message Variable End **********");
        }
    }

    /**
     * 初始化消费者队列
     *
     * @throws Exception
     */
    private void initConsumer() throws Exception {
        try {
            generateConsumerService.initGenerateConsumer();
        } catch (Exception ex) {
            log.error("初始化消费者信息失败，异常信息：{}", ex.getMessage(), ex);
            throw new Exception(ex);
        }
    }
}