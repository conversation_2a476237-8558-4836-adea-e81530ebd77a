package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaClockSiteDo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/26
 */
public interface IClockSiteRepository {
    int save(WaClockSiteDo clockSiteDo);

    int update(WaClockSiteDo clockSiteDo);

    int deleteById(Long id);

    WaClockSiteDo getById(Long id);

    AttendancePageResult<WaClockSiteDo> getClockSitePageList(AttendanceBasePage basePage, Long corpId, String belongOrgId);

    List<WaClockSiteDo> getClockSiteListByIds(List<Long> ids);
}
