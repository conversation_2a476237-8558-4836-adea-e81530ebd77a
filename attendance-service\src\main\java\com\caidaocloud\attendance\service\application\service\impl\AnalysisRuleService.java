package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaParseGroupMapper;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.service.application.dto.AbsentConditionDto;
import com.caidaocloud.attendance.service.application.dto.AnalysisDetailDto;
import com.caidaocloud.attendance.service.application.dto.OtPaseDto;
import com.caidaocloud.attendance.service.application.dto.WaCustomParseRuleDto;
import com.caidaocloud.attendance.service.application.enums.FlexibleEnum;
import com.caidaocloud.attendance.service.application.service.IAnalysisRuleService;
import com.caidaocloud.attendance.service.domain.entity.WaReportFieldRuleDo;
import com.caidaocloud.attendance.service.domain.service.WaReportFieldRuleDomainService;
import com.caidaocloud.attendance.service.infrastructure.util.KeyToFilterUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.github.miemiedev.mybatis.paginator.domain.Paginator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AnalysisRuleService implements IAnalysisRuleService {
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private WaParseGroupMapper waParseGroupMapper;
    @Autowired
    private WaReportFieldRuleDomainService waReportFieldRuleDomainService;

    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        //允许没有定义的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //允许使用未带引号的字段名
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //允许使用单引号
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
    }

    @Override
    public AttendancePageResult getAnalysisList(String belongId, AttendanceBasePage basePage) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        List<FilterBean> beans = KeyToFilterUtil.KeyToFilterList(basePage.getKeywords(), "parse_group_name", basePage.getFilterList());
        pageBean.setFilterList(beans);
        PageList<Map> pageList = (PageList<Map>) waConfigService.getParseGroupList(pageBean);
        if (CollectionUtils.isNotEmpty(pageList)) {
            List<AnalysisDetailDto> items = JSON.parseArray(JSON.toJSONString(pageList), AnalysisDetailDto.class);
            items.forEach(row -> {
                //迟到豁免类型：1 按照次数，2 按照分钟
                if (row.getLateAllowUnit() != null && row.getLateUnit() != null) {
                    String unitTxt = row.getLateUnit() == 1 ? "小时" : "分钟";
                    if (row.getLateAllowUnit() == 1) {
                        if (row.getLateAllowNumber() != null && row.getLateCount() != null) {
                            row.setLateAllowRuleTxt("每月迟到豁免" + row.getLateAllowNumber() + "次，每次" + row.getLateCount() + unitTxt);
                        }
                    } else {
                        if (row.getLateCount() != null) {
                            row.setLateAllowRuleTxt("每月迟到豁免" + row.getLateCount() + unitTxt);
                        }
                    }
                }
                //早退豁免类型：1 按照次数，2 按照分钟
                if (row.getEarlyAllowUnit() != null && row.getEarlyUnit() != null) {
                    String unitTxt = row.getEarlyUnit() == 1 ? "小时" : "分钟";
                    if (row.getEarlyAllowUnit() == 1) {
                        if (row.getEarlyAllowNumber() != null && row.getEarlyCount() != null) {
                            row.setEarlyAllowRuleTxt("每月早退豁免" + row.getEarlyAllowNumber() + "次，每次" + row.getEarlyCount() + unitTxt);
                        }
                    } else {
                        if (row.getEarlyCount() != null) {
                            row.setEarlyAllowRuleTxt("每月早退豁免" + row.getEarlyCount() + unitTxt);
                        }
                    }
                }
            });
            Paginator paginator = pageList.getPaginator();
            return new AttendancePageResult<>(items, basePage.getPageNo(), basePage.getPageSize(), paginator.getTotalCount());
        }
        return new AttendancePageResult<>(new ArrayList<>(), basePage.getPageNo(), basePage.getPageSize(), 0);
    }

    @Override
    public AnalysisDetailDto getParseGroupById(Integer id) throws Exception {
        WaParseGroup parseGroup = waConfigService.getParseGroup(id);
        if (parseGroup != null) {
            if (parseGroup.getIsAnalyzeLateEarly() == null) {
                parseGroup.setIsAnalyzeLateEarly(Boolean.FALSE);
            }
            if (parseGroup.getFlexibleWorkSwitch() == null) {
                parseGroup.setFlexibleWorkSwitch(FlexibleEnum.CLOSE.getIndex());
            }
            AnalysisDetailDto detailDto = ObjectConverter.convert(parseGroup, AnalysisDetailDto.class);
            detailDto.setClockRuleDto(JSON.parseObject(parseGroup.getClockRule()));
            if (parseGroup.getOtPaseJsonb() != null) {
                PGobject pGobject = (PGobject) parseGroup.getOtPaseJsonb();
                List<OtPaseDto> otParseVoList = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<OtPaseDto>>() {
                });
                detailDto.setOtPaseJsonb(otParseVoList);
            }
            if (parseGroup.getAbsentConditionJsonb() != null) {
                PGobject pGobject = (PGobject) parseGroup.getAbsentConditionJsonb();
                List<AbsentConditionDto> absentConditionVoList = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<AbsentConditionDto>>() {
                });
                detailDto.setAbsentConditionJsonb(absentConditionVoList);
            }
            // 查询自定义的分析规则
            List<WaReportFieldRuleDo> customRuleList = waReportFieldRuleDomainService.selectList(parseGroup.getBelongOrgid(), parseGroup.getParseGroupId());
            if (CollectionUtils.isNotEmpty(customRuleList)) {
                detailDto.setOpenCustomPaseRule(true);
                WaReportFieldRuleDo fieldRuleDo = customRuleList.get(0);
                detailDto.setCustomLatePaseRule(FastjsonUtil.convertObject(fieldRuleDo.getLatePaseRule(), WaCustomParseRuleDto.class));
                detailDto.setCustomEarlyPaseRule(FastjsonUtil.convertObject(fieldRuleDo.getEarlyPaseRule(), WaCustomParseRuleDto.class));
                detailDto.setCustomAbsentPaseRule(FastjsonUtil.convertObject(fieldRuleDo.getAbsentPaseRule(), WaCustomParseRuleDto.class));
            } else {
                detailDto.setOpenCustomPaseRule(false);
            }
            return detailDto;
        }
        return new AnalysisDetailDto();
    }

    @Override
    public void deleteRuleById(Integer id) {
        if (id == null) {
            return;
        }
        waConfigService.deleteParseGroup(id);
    }

    @Override
    public AnalysisDetailDto getGroupInfo(Integer id) {
        Optional<WaParseGroup> opt = Optional.ofNullable(waParseGroupMapper.selectByPrimaryKey(id));
        if (!opt.isPresent()) {
            return null;
        }
        WaParseGroup waParseGroup = opt.get();
        if (waParseGroup.getIsAnalyzeLateEarly() == null) {
            waParseGroup.setIsAnalyzeLateEarly(Boolean.FALSE);
        }
        return ObjectConverter.convert(waParseGroup, AnalysisDetailDto.class);
    }
}
