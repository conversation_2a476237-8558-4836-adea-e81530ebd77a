package com.caidaocloud.attendance.core.wa.vo;

import com.caidao1.wa.enums.ShiftTimeBelongTypeBaseEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 多段班-每段班详情VO
 *
 * <AUTHOR>
 * @Date 2025/2/14
 */
@Data
@ApiModel("多段班-每段班详情VO")
public class MultiWorkTimeInfoSimpleVo {
    @ApiModelProperty("上班时间(单位分钟),eg:780")
    private Integer startTime;
    @ApiModelProperty("下班时间(单位分钟),eg:1080")
    private Integer endTime;
    @ApiModelProperty("最早上班打卡时间(单位分钟),eg:540")
    private Integer onDutyStartTime;
    @ApiModelProperty("最晚上班打卡时间(单位分钟),eg:600")
    private Integer onDutyEndTime;
    @ApiModelProperty("最早下班打卡时间(单位分钟),eg:1200")
    private Integer offDutyStartTime;
    @ApiModelProperty("最晚下班打卡时间(单位分钟),eg:1380")
    private Integer offDutyEndTime;
    @ApiModelProperty("上班时间归属标记: 1 当日、2 次日")
    private Integer startTimeBelong;
    @ApiModelProperty("下班时间归属标记: 1 当日、2 次日")
    private Integer endTimeBelong;
    @ApiModelProperty("最早上班打卡时间归属标记: 1 当日、2 次日、3 前日")
    private Integer onDutyStartTimeBelong;
    @ApiModelProperty("最晚上班打卡时间归属标记: 1 当日、2 次日")
    private Integer onDutyEndTimeBelong;
    @ApiModelProperty("最早下班打卡时间归属标记: 1 当日、2 次日")
    private Integer offDutyStartTimeBelong;
    @ApiModelProperty("最晚下班打卡时间归属标记: 1 当日、2 次日")
    private Integer offDutyEndTimeBelong;
    @ApiModelProperty("工作时长(单位分钟)-计算项")
    private Integer workTotalTime;
    @ApiModelProperty("工时类型：1 标准工时、2 休息工时 3 加班工时")
    private Integer workType;
    @ApiModelProperty("实际上班时间(单位分钟, 如果是次日自动加上1440),eg:780")
    private Integer realStartTime;
    @ApiModelProperty("实际下班时间(单位分钟, 如果是次日自动加上1440),eg:1080")
    private Integer realEndTime;

    public Integer doGetRealStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.startTimeBelong)) {
            return startTime + 1440;
        }
        return startTime;
    }

    public Integer doGetRealEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.endTimeBelong)) {
            return endTime + 1440;
        }
        return endTime;
    }

    public Integer doGetRealOnDutyStartTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.onDutyStartTimeBelong)) {
            return onDutyStartTime + 1440;
        }
        return onDutyStartTime;
    }

    public Integer doGetRealOnDutyEndTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.onDutyEndTimeBelong)
                || this.onDutyEndTime < this.onDutyStartTime) {
            return onDutyEndTime + 1440;
        }
        return onDutyEndTime;
    }

    public Integer doGetRealOffDutyStartTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyStartTimeBelong)) {
            return offDutyStartTime + 1440;
        }
        return offDutyStartTime;
    }

    public Integer doGetRealOffDutyEndTime() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong)
                || this.offDutyEndTime < this.offDutyStartTime) {
            return offDutyEndTime + 1440;
        }
        return offDutyEndTime;
    }
}
