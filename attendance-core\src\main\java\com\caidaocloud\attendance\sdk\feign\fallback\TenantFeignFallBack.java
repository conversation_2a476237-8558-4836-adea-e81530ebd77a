package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.core.wa.dto.TenantCommonConfigDto;
import com.caidaocloud.attendance.sdk.dto.tenant.TenantDto;
import com.caidaocloud.attendance.sdk.feign.TenantFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TenantFeignFallBack implements TenantFeignClient {
    @Override
    public Result<List<TenantDto>> tenantList() {
        return Result.fail();
    }

    @Override
    public Result<TenantCommonConfigDto> tenantCommonConfig() {
        return null;
    }
}
