package com.caidaocloud.attendance.service.application.enums;

public enum ExpirationRuleEnum {
    CASH(1, "付现"),
    TO_VOID(2, "作废"),
    CARRY_FORWARD(3, "结转");
    
    private Integer index;
    private String name;

    ExpirationRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ExpirationRuleEnum c : ExpirationRuleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
