package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.commons.utils.MathUtil;
import com.caidao1.wa.mybatis.mapper.WaSobMapper;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.service.application.dto.StatisticsReportReqDataDto;
import com.caidaocloud.attendance.service.application.dto.WaAnalyzCalDTO;
import com.caidaocloud.attendance.service.application.dto.WaAnalyzeStatisticsReportDto;
import com.caidaocloud.attendance.service.domain.entity.WaAnalyzeDo;
import com.caidaocloud.attendance.service.domain.entity.WaAnalyzeStatisticsReportDo;
import com.caidaocloud.attendance.service.domain.entity.WaParseGroupDo;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordBdkDo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/16
 */
@Slf4j
@Service
public class StatisticsReportService {
    @Value("${caidaocloud.data.statisticssync:false}")
    private boolean statisticsSync;

    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        //允许没有定义的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //允许使用未带引号的字段名
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //允许使用单引号
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
    }

    private static final int PAGE_SIZE = 3000;
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    @Autowired
    private WaAnalyzeStatisticsReportDo waAnalyzeStatisticsReportDo;
    @Autowired
    private WaAnalyzeDo waAnalyzeDo;
    @Autowired
    private WaParseGroupDo waParseGroupDo;
    @Autowired
    private WaSobMapper waSobMapper;
    @Autowired
    private AnalyzeResultCalculateService analyzeResultCalculateService;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WaRegisterRecordBdkDo registerRecordBdkDo;

    /**
     * 分页查询考勤统计报表
     *
     * @param dto
     * @return
     */
    public AttendancePageResult<WaAnalyzeStatisticsReportDto> getAnalyzeStatisticsReportPageList(StatisticsReportReqDataDto dto) {
        AttendancePageResult<WaAnalyzeStatisticsReportDo> pageResult = waAnalyzeStatisticsReportDo.getAnalyzeStatisticsReportPageList(dto);
        return new AttendancePageResult<>(ObjectConverter.convertList(pageResult.getItems(), WaAnalyzeStatisticsReportDto.class), dto.getPageNo(), dto.getPageSize(), pageResult.getTotal());
    }

    /**
     * 查询员工每日考勤分析数据
     *
     * @param tenantId
     * @param empIdList
     * @param startDate
     * @param endDate
     * @return
     */
    private List<WaAnalyzeDo> getWaAnalyzeList(String tenantId, List<Long> empIdList, Long startDate, Long endDate) {
        AttendanceBasePage basePage = new AttendanceBasePage();
        basePage.setPageSize(PAGE_SIZE);
        basePage.setPageNo(1);
        AttendancePageResult<WaAnalyzeDo> pageResult = waAnalyzeDo.getWaAnalyzePageList(basePage, tenantId, empIdList, startDate, endDate);
        List<WaAnalyzeDo> dataList = pageResult.getItems();
        //总条数
        int totalCount = pageResult.getTotal();
        if (totalCount > PAGE_SIZE) {
            //总页数
            int totalPage = (int) Math.ceil((double) totalCount / PAGE_SIZE);
            for (int pageNo = 2; pageNo <= totalPage; pageNo++) {
                basePage.setPageNo(pageNo);
                pageResult = waAnalyzeDo.getWaAnalyzePageList(basePage, tenantId, empIdList, startDate, endDate);
                dataList.addAll(pageResult.getItems());
            }
        }
        return dataList;
    }

    /**
     * 查询员工分配的考勤分析分组
     *
     * @param belongOrgId
     * @param empIdList
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    private WaAnalyzCalDTO getWaAnalyzeCalDTO(String belongOrgId, List<Long> empIdList, Long startDate, Long endDate) throws Exception {
        WaAnalyzCalDTO analyzeCalDTO = new WaAnalyzCalDTO();
        analyzeCalDTO.setBelongid(belongOrgId);
        //分配的考勤分析分组
        List<WaParseGroupDo> waParseGroupDos = waParseGroupDo.getWaParseGroupList(belongOrgId, empIdList, startDate, endDate);
        if (CollectionUtils.isNotEmpty(waParseGroupDos)) {
            analyzeCalDTO.setEmpWaParseGroupDoMap(waParseGroupDos.stream().collect(Collectors.groupingBy(WaParseGroupDo::getEmpid)));
        }
        // 查询公司所有的班次
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongOrgId);
        analyzeCalDTO.setCorpShiftDefMap(shiftDefMap);

        //查询每个人的排班数据
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("startDate", startDate);
        paramsMap.put("endDate", endDate);
        paramsMap.put("belongid", belongOrgId);
        paramsMap.put("anyEmpid", "'{" + StringUtils.join(empIdList, ",") + "}'");
        paramsMap.put("anyEmpids", "'{" + StringUtils.join(empIdList, ",") + "}'");
        paramsMap.put("ymstart", Integer.valueOf(DateUtil.parseDateToPattern(new Date(startDate * 1000), "yyyyMM")));
        paramsMap.put("ymend", Integer.valueOf(DateUtil.parseDateToPattern(new Date(endDate * 1000), "yyyyMM")));
        Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();
        Map<String, EmpShiftInfo> empShift = waCommonService.getEmpShiftInfoListMaps(paramsMap, empShiftInfoByDateMap,
                empIdList, shiftDefMap);
        analyzeCalDTO.setEmpShift(empShift);
        analyzeCalDTO.setEmpShiftInfoByDateMap(empShiftInfoByDateMap);
        return analyzeCalDTO;
    }

    /**
     * 考勤统计-同步周报、月报数据
     *
     * @param empIdList
     * @param waSobId
     * @param belongOrgId
     * @param userId
     * @throws Exception
     */
    @Async
    public void syncWaAnalyzeStatisticsReport(List<Long> empIdList, Integer waSobId, String belongOrgId, Long userId) throws Exception {
        if (!statisticsSync) {
            // 未开启同步
            log.info("Attendance syncWaAnalyzeStatisticsReport synchronization setting is not enabled");
            return;
        }
        WaSob waSob = waSobMapper.selectByPrimaryKey(waSobId);
        if (waSob == null) {
            return;
        }
        Long startDate = DateUtil.addDate(waSob.getStartDate() * 1000, -7);
        Long endDate = DateUtil.addDate(waSob.getEndDate() * 1000, 7);
        //计算周
        List<Long[]> weekList = getWeekList(startDate, endDate);
        if (CollectionUtils.isNotEmpty(weekList)) {
            long start = weekList.stream().mapToLong(week -> week[0]).min().orElse(startDate);
            startDate = Math.min(start, startDate);
            long end = weekList.stream().mapToLong(week -> week[1]).max().orElse(endDate);
            endDate = Math.max(end, endDate);
        }
        //查询员工每日考勤分析数据
        List<WaAnalyzeDo> dataList = this.getWaAnalyzeList(belongOrgId, empIdList, startDate, endDate);
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        //按照员工ID分组 key = empId
        Map<Long, List<WaAnalyzeDo>> empAnalyzeMap = dataList.stream().collect(Collectors.groupingBy(WaAnalyzeDo::getEmpid));

        //查询员工考勤分析分组&不打卡信息
        WaAnalyzCalDTO analyzeCalDTO = this.getWaAnalyzeCalDTO(belongOrgId, empIdList, startDate, endDate);

        //月报
        this.genWaAnalyzeStatisticsReportForMonth(belongOrgId, userId, empAnalyzeMap, analyzeCalDTO, waSob.getStartDate(), waSob.getEndDate(), waSob.getSysPeriodMonth(), empIdList);

        //周报
        this.genWaAnalyzeStatisticsReportForWeek(belongOrgId, userId, empAnalyzeMap, analyzeCalDTO, startDate, endDate, empIdList, weekList);
    }

    /**
     * 每月考勤数据统计
     *
     * @param belongOrgId
     * @param userId
     * @param empAnalyzeMap
     * @param analyzeCalDTO
     * @param startDate
     * @param endDate
     * @param sysPeriodMonth
     * @throws Exception
     */
    private void genWaAnalyzeStatisticsReportForMonth(String belongOrgId, Long userId, Map<Long, List<WaAnalyzeDo>> empAnalyzeMap, WaAnalyzCalDTO analyzeCalDTO, Long startDate, Long endDate, Integer sysPeriodMonth, List<Long> empIdList) throws Exception {
        //补打卡次数查询
        List<WaRegisterRecordBdkDo> recordDoList = registerRecordBdkDo.getEmpBdkRegisterList(belongOrgId, empIdList, startDate, endDate);
        Map<Long, List<WaRegisterRecordBdkDo>> empBdkRegMap = null;
        if (CollectionUtils.isNotEmpty(recordDoList)) {
            empBdkRegMap = recordDoList.stream().collect(Collectors.groupingBy(WaRegisterRecordBdkDo::getEmpid));
        }
        Long monthBeginDate = DateUtil.convertStringToDateTime(sysPeriodMonth + "01", "yyyyMMdd", true);
        Integer rptType = 2;
        //考勤数据聚合计算
        List<WaAnalyzeStatisticsReportDo> addList = new ArrayList<>();
        Map<Long, List<WaRegisterRecordBdkDo>> finalEmpBdkRegMap = empBdkRegMap;
        empAnalyzeMap.forEach((empid, analyzeList) -> {
            analyzeList = analyzeList.stream().filter(o -> o.getBelongDate() >= startDate && o.getBelongDate() <= endDate).collect(Collectors.toList());
            // 迟到豁免单位：1 按照次数 、 2 按照时长（分钟）
            Integer lateAllowUnit = null;
            // 允许迟到xx次
            Integer lateAllowNumber = null;
            // 迟到时长豁免单位：1 小时 、 2 分钟
            Integer lateUnit = null;
            // 迟到豁免时（每次允许迟到时长最大值）
            BigDecimal lateCount = null;
            // 早退豁免单位：1 按照次数 、 2 按照时长（分钟）
            Integer earlyAllowUnit = null;
            // 允许早退xx次
            Integer earlyAllowNumber = null;
            // 早退时长豁免单位：1 小时 、 2 分钟
            Integer earlyUnit = null;
            // 早退豁免时长（每次允许早退时长最大值）
            BigDecimal earlyCount = null;
            //查询员工考勤分析分组
            WaParseGroupDo parseGroupDo = analyzeCalDTO.getEmpWaParseGroupDo(empid, endDate);
            if (parseGroupDo != null) {
                lateAllowUnit = parseGroupDo.getLateAllowUnit();
                lateAllowNumber = parseGroupDo.getLateAllowNumber();
                earlyAllowUnit = parseGroupDo.getEarlyAllowUnit();
                earlyAllowNumber = parseGroupDo.getEarlyAllowNumber();
                lateUnit = parseGroupDo.getLateUnit();
                lateCount = parseGroupDo.getLateCount();
                earlyUnit = parseGroupDo.getEarlyUnit();
                earlyCount = parseGroupDo.getEarlyCount();
            }
            lateAllowNumber = lateAllowNumber == null ? 0 : lateAllowNumber;
            earlyAllowNumber = earlyAllowNumber == null ? 0 : earlyAllowNumber;
            //汇总员工考勤分析数据
            Float totalLateTime = 0f, totalEarlyTime = 0f;
            int kgWorkTime = 0, workTime = 0, registerTime = 0, otTime = 0, leaveCount = 0, travelCount = 0, totalLateCount = 0, totalEarlyCount = 0, totalKgCount = 0;
            float actualWorkTime = 0f;
            List<Long> days = new ArrayList<>();
            for (WaAnalyzeDo analyzeDo : analyzeList) {
                days.add(analyzeDo.getBelongDate());
                if (parseGroupDo != null) {
                    //迟到时长
                    float lateTime = Optional.ofNullable(analyzeDo.getLateTime()).orElse(0f);
                    if (lateTime > 0) {
                        if (lateAllowUnit != null && lateAllowUnit == 1 && lateAllowNumber > 0 && lateUnit != null && lateCount != null) {
                            //抵扣迟到时长
                            Double count = lateCount.doubleValue();
                            if (lateUnit == 1) {
                                count *= 60;
                            }
                            // 如果迟到分钟数不在允许范围内则不予豁免
                            if (lateTime <= count) {
                                // 每月允许的迟到次数减1
                                lateAllowNumber--;
                                analyzeDo.setLateTime(0f);
                            }
                        }
                    }

                    //早退时长
                    float earlyTime = Optional.ofNullable(analyzeDo.getEarlyTime()).orElse(0f);
                    if (earlyTime > 0) {
                        if (earlyAllowUnit != null && earlyAllowUnit == 1 && earlyAllowNumber > 0 && earlyUnit != null && earlyCount != null) {
                            Double count = earlyCount.doubleValue();
                            if (earlyUnit == 1) {
                                count *= 60;
                            }
                            // 如果早退分钟数不在允许范围内则不予豁免
                            if (earlyTime <= count) {
                                // 每月允许的早退次数减1
                                earlyAllowNumber--;
                                analyzeDo.setEarlyTime(0f);
                            }
                        }
                    }
                }
                //查询员工排班
                EmpShiftInfo shiftdef = analyzeResultCalculateService.getEmpShiftDefByInfo(empid, analyzeDo.getShiftDefId(), analyzeDo.getBelongDate(), analyzeCalDTO);
                if (shiftdef != null) {
                    workTime += Optional.ofNullable(shiftdef.getWorkTotalTime()).orElse(0);
                }
                float late = Optional.ofNullable(analyzeDo.getLateTime()).orElse(0f);
                totalLateTime += late;
                if (late > 0) {
                    totalLateCount++;
                }
                float early = Optional.ofNullable(analyzeDo.getEarlyTime()).orElse(0f);
                totalEarlyTime += early;
                if (early > 0) {
                    totalEarlyCount++;
                }
                int kg = Optional.ofNullable(analyzeDo.getKgWorkTime()).orElse(0);
                kgWorkTime += kg;
                if (kg > 0) {
                    totalKgCount++;
                }
                actualWorkTime += Optional.ofNullable(analyzeDo.getActualWorkTime()).orElse(0f);
                registerTime += Optional.ofNullable(analyzeDo.getRegisterTime()).orElse(0);
                otTime += Optional.ofNullable(analyzeDo.getOtTime()).orElse(0);
                leaveCount += Optional.ofNullable(analyzeDo.getLeaveCount()).orElse(0);
                travelCount += Optional.ofNullable(analyzeDo.getTravelCount()).orElse(0);
            }
            for (long i = startDate; i <= endDate; i = i + 86400) {
                if (!days.contains(i)) {
                    //查询员工排班
                    EmpShiftInfo shiftDef = analyzeResultCalculateService.getEmpShiftDefByInfo(empid, null, i, analyzeCalDTO);
                    if (shiftDef != null && (shiftDef.getHireDate() == null || (shiftDef.getHireDate() != null && shiftDef.getWorkDate() >= shiftDef.getHireDate()))) {
                        workTime += shiftDef.getWorkTotalTime() == null ? 0 : shiftDef.getWorkTotalTime();
                    }
                }
            }
            if (parseGroupDo != null) {
                //迟到时长：按照豁免时长进行豁免
                if (totalLateTime > 0 && lateAllowUnit != null && lateAllowUnit == 2 && lateUnit != null && lateCount != null) {
                    Double count = lateCount.doubleValue();
                    if (lateUnit == 1) {
                        count *= 60;
                    }
                    Double value = 0d;
                    if (totalLateTime > count) {
                        value = MathUtil.sub(totalLateTime, count);
                    }
                    totalLateTime = new BigDecimal(String.valueOf(value)).setScale(2, RoundingMode.HALF_UP).floatValue();
                }
                //早退时长：按照豁免时长进行豁免
                if (totalEarlyTime > 0 && earlyAllowUnit != null && earlyAllowUnit == 2 && earlyUnit != null && earlyCount != null) {
                    Double count = earlyCount.doubleValue();
                    if (earlyUnit == 1) {
                        count *= 60;
                    }
                    Double value = 0d;
                    if (totalEarlyTime > count) {
                        value = MathUtil.sub(totalEarlyTime, count);
                    }
                    totalEarlyTime = new BigDecimal(String.valueOf(value)).setScale(2, RoundingMode.HALF_UP).floatValue();
                }
            }

            WaAnalyzeStatisticsReportDo reportDo = new WaAnalyzeStatisticsReportDo();
            reportDo.setStatisticsReportId(snowflakeUtil.createId());
            reportDo.setTenantId(belongOrgId);
            reportDo.setEmpId(empid);
            reportDo.setWorkTime(workTime);
            reportDo.setActualWorkTime(actualWorkTime);
            reportDo.setRegisterTime(registerTime);
            reportDo.setLateTime(new BigDecimal(String.valueOf(totalLateTime)).setScale(2, RoundingMode.HALF_UP).floatValue());
            reportDo.setEarlyTime(new BigDecimal(String.valueOf(totalEarlyTime)).setScale(2, RoundingMode.HALF_UP).floatValue());
            reportDo.setKgWorkTime(kgWorkTime);
            reportDo.setBdkCount(0);
            if (MapUtils.isNotEmpty(finalEmpBdkRegMap) && finalEmpBdkRegMap.containsKey(empid) && CollectionUtils.isNotEmpty(finalEmpBdkRegMap.get(empid))) {
                reportDo.setBdkCount(finalEmpBdkRegMap.get(empid).size());
            }
            reportDo.setOtTime(otTime);
            reportDo.setLeaveCount(leaveCount);
            reportDo.setTravelCount(travelCount);
            reportDo.setTotalLateCount(totalLateCount);
            reportDo.setTotalEarlyCount(totalEarlyCount);
            reportDo.setTotalKgCount(totalKgCount);
            reportDo.setRptType(rptType);
            reportDo.setStartDate(monthBeginDate);
            reportDo.setDeleted(0);
            reportDo.setCreateBy(Long.valueOf(userId));
            long curtime = System.currentTimeMillis() / 1000;
            reportDo.setCreateTime(curtime);
            reportDo.setUpdateBy(Long.valueOf(userId));
            reportDo.setUpdateTime(curtime);
            addList.add(reportDo);
        });

        //报表数据保存
        if (CollectionUtils.isNotEmpty(addList)) {
            // 查询系统中已经存在的统计数据
            List<Long> empids = addList.stream().map(WaAnalyzeStatisticsReportDo::getEmpId).distinct().collect(Collectors.toList());
            List<WaAnalyzeStatisticsReportDo> oldList = waAnalyzeStatisticsReportDo.getAnalyzeStatisticsReportList(belongOrgId, empids, rptType, monthBeginDate, null);
            if (CollectionUtils.isNotEmpty(oldList)) {
                List<Long> delIds = oldList.stream().map(WaAnalyzeStatisticsReportDo::getStatisticsReportId).collect(Collectors.toList());
                waAnalyzeStatisticsReportDo.deleteByIds(belongOrgId, delIds);
            }
            List<List<WaAnalyzeStatisticsReportDo>> lists = ListTool.split(addList, 500);
            for (List<WaAnalyzeStatisticsReportDo> list : lists) {
                waAnalyzeStatisticsReportDo.save(list);
            }
        }
    }

    /**
     * 每周考勤数据统计
     *
     * @param belongOrgId
     * @param userId
     * @param empAnalyzeMap
     * @param analyzeCalDTO
     * @param startDate
     * @param endDate
     * @param weekList
     * @throws Exception
     */
    private void genWaAnalyzeStatisticsReportForWeek(String belongOrgId, Long userId, Map<Long, List<WaAnalyzeDo>> empAnalyzeMap, WaAnalyzCalDTO analyzeCalDTO, Long startDate, Long endDate, List<Long> empIdList, List<Long[]> weekList) throws Exception {
        //补打卡次数查询
        List<WaRegisterRecordBdkDo> recordDoList = registerRecordBdkDo.getEmpBdkRegisterList(belongOrgId, empIdList, startDate, endDate);
        Map<Long, List<WaRegisterRecordBdkDo>> empBdkRegMap = null;
        if (CollectionUtils.isNotEmpty(recordDoList)) {
            empBdkRegMap = recordDoList.stream().collect(Collectors.groupingBy(WaRegisterRecordBdkDo::getEmpid));
        }
        Integer rptType = 1;
        //考勤数据聚合计算
        List<WaAnalyzeStatisticsReportDo> addList = new ArrayList<>();
        Map<Long, List<WaRegisterRecordBdkDo>> finalEmpBdkRegMap = empBdkRegMap;
        empAnalyzeMap.forEach((empid, analyzeList) -> {
            analyzeList.sort(Comparator.comparing(WaAnalyzeDo::getBelongDate));
            //汇总员工考勤分析数据
            for (Long[] longs : weekList) {
                Long start = longs[0];
                Long end = longs[1];
                List<WaAnalyzeDo> weekAnalyzeList = analyzeList.stream().filter(o -> o.getBelongDate() >= start && o.getBelongDate() <= end).collect(Collectors.toList());
                Float totalLateTime = 0f, totalEarlyTime = 0f;
                int kgWorkTime = 0, workTime = 0, registerTime = 0, otTime = 0, leaveCount = 0, travelCount = 0, totalLateCount = 0, totalEarlyCount = 0, totalKgCount = 0;
                float actualWorkTime = 0f;
                List<Long> days = new ArrayList<>();
                for (WaAnalyzeDo analyzeDo : weekAnalyzeList) {
                    days.add(analyzeDo.getBelongDate());
                    //查询员工排班
                    EmpShiftInfo shiftdef = analyzeResultCalculateService.getEmpShiftDefByInfo(empid, analyzeDo.getShiftDefId(), analyzeDo.getBelongDate(), analyzeCalDTO);
                    if (shiftdef != null) {
                        workTime += shiftdef.getWorkTotalTime() == null ? 0 : shiftdef.getWorkTotalTime();
                    }
                    float late = Optional.ofNullable(analyzeDo.getLateTime()).orElse(0f);
                    totalLateTime += late;
                    if (late > 0) {
                        totalLateCount++;
                    }
                    float early = Optional.ofNullable(analyzeDo.getEarlyTime()).orElse(0f);
                    totalEarlyTime += early;
                    if (early > 0) {
                        totalEarlyCount++;
                    }
                    int kg = Optional.ofNullable(analyzeDo.getKgWorkTime()).orElse(0);
                    kgWorkTime += kg;
                    if (kg > 0) {
                        totalKgCount++;
                    }
                    actualWorkTime += Optional.ofNullable(analyzeDo.getActualWorkTime()).orElse(0f);
                    registerTime += Optional.ofNullable(analyzeDo.getRegisterTime()).orElse(0);
                    otTime += Optional.ofNullable(analyzeDo.getOtTime()).orElse(0);
                    leaveCount += Optional.ofNullable(analyzeDo.getLeaveCount()).orElse(0);
                    travelCount += Optional.ofNullable(analyzeDo.getTravelCount()).orElse(0);
                }
                for (long i = startDate; i <= endDate; i = i + 86400) {
                    if (!days.contains(i)) {
                        //查询员工排班
                        EmpShiftInfo shiftDef = analyzeResultCalculateService.getEmpShiftDefByInfo(empid, null, i, analyzeCalDTO);
                        if (shiftDef != null && (shiftDef.getHireDate() == null || (shiftDef.getHireDate() != null && shiftDef.getWorkDate() >= shiftDef.getHireDate()))) {
                            workTime += Optional.ofNullable(shiftDef.getWorkTotalTime()).orElse(0);
                        }
                    }
                }
                WaAnalyzeStatisticsReportDo reportDo = new WaAnalyzeStatisticsReportDo();
                reportDo.setStatisticsReportId(snowflakeUtil.createId());
                reportDo.setTenantId(belongOrgId);
                reportDo.setEmpId(empid);
                reportDo.setWorkTime(workTime);
                reportDo.setActualWorkTime(actualWorkTime);
                reportDo.setRegisterTime(registerTime);
                reportDo.setLateTime(new BigDecimal(String.valueOf(totalLateTime)).setScale(2, RoundingMode.HALF_UP).floatValue());
                reportDo.setEarlyTime(new BigDecimal(String.valueOf(totalEarlyTime)).setScale(2, RoundingMode.HALF_UP).floatValue());
                reportDo.setKgWorkTime(kgWorkTime);
                reportDo.setBdkCount(0);
                if (MapUtils.isNotEmpty(finalEmpBdkRegMap) && finalEmpBdkRegMap.containsKey(empid)) {
                    List<WaRegisterRecordBdkDo> bdkList = finalEmpBdkRegMap.get(empid).stream().filter(o -> o.getBelongDate() >= start && o.getBelongDate() <= end).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(bdkList)) {
                        reportDo.setBdkCount(bdkList.size());
                    }
                }
                reportDo.setOtTime(otTime);
                reportDo.setLeaveCount(leaveCount);
                reportDo.setTravelCount(travelCount);
                reportDo.setTotalLateCount(totalLateCount);
                reportDo.setTotalEarlyCount(totalEarlyCount);
                reportDo.setTotalKgCount(totalKgCount);
                reportDo.setRptType(rptType);
                reportDo.setStartDate(start);
                reportDo.setDeleted(0);
                reportDo.setCreateBy(userId);
                long curtime = System.currentTimeMillis() / 1000;
                reportDo.setCreateTime(curtime);
                reportDo.setUpdateBy(userId);
                reportDo.setUpdateTime(curtime);
                addList.add(reportDo);
            }
        });
        //报表数据保存
        if (CollectionUtils.isNotEmpty(addList)) {
            // 查询系统中已经存在的统计数据
            List<Long> empIds = addList.stream().map(WaAnalyzeStatisticsReportDo::getEmpId).distinct().collect(Collectors.toList());
            List<WaAnalyzeStatisticsReportDo> oldList = waAnalyzeStatisticsReportDo.getAnalyzeStatisticsReportList(belongOrgId, empIds, rptType, startDate, endDate);
            if (CollectionUtils.isNotEmpty(oldList)) {
                List<Long> delIds = oldList.stream().map(WaAnalyzeStatisticsReportDo::getStatisticsReportId).collect(Collectors.toList());
                waAnalyzeStatisticsReportDo.deleteByIds(belongOrgId, delIds);
            }
            List<List<WaAnalyzeStatisticsReportDo>> lists = ListTool.split(addList, 500);
            for (List<WaAnalyzeStatisticsReportDo> list : lists) {
                waAnalyzeStatisticsReportDo.save(list);
            }
        }
    }

    private List<Long[]> getWeekList(Long startDate, Long endDate) {
        List<Long[]> weekList = new ArrayList<>();
        try {
            Calendar cal1 = Calendar.getInstance();
            cal1.setTimeInMillis(startDate * 1000);
            cal1.add(Calendar.WEEK_OF_MONTH, 0);
            cal1.set(Calendar.DAY_OF_WEEK, 2);
            Date time1 = cal1.getTime();
            long start = DateUtil.getOnlyDate(time1);
            Calendar cal = Calendar.getInstance();
            cal.setTimeInMillis(endDate * 1000);
            cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
            cal.add(Calendar.DAY_OF_WEEK, 1);
            Date time = cal.getTime();
            long end = DateUtil.getOnlyDate(time);
            int days = DateUtilExt.getDifferenceDay(start, end) + 1;
            if (days > 0) {
                int weekLength = 7;
                for (int i = 0; i < days; i = i + weekLength) {
                    Long monDayTimeStamp = DateUtil.addDate(start * 1000, i);
                    Long sunDayTimeStamp = DateUtil.addDate(start * 1000, i + 6);
                    weekList.add(new Long[]{monDayTimeStamp, sunDayTimeStamp});
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return weekList;
    }
}

