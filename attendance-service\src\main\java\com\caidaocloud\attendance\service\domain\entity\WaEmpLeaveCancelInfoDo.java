package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.domain.repository.IWaEmpLeaveCancelInfoRepository;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Data
@Service
public class WaEmpLeaveCancelInfoDo {
    private Long leaveCancelInfoId;
    private Long leaveCancelId;
    private Long startTime;
    private Long endTime;
    private String shalfDay;
    private String ehalfDay;
    private Short periodType;
    private Integer timeUnit;
    private Float timeDuration;
    private Long shiftStartTime;
    private Long shiftEndTime;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;

    /**
     * 销假开始时间班次
     */
    private String startShift;
    /**
     * 销假结束时间班次
     */
    private String endShift;
    /**
     * 开始日期所选的班次集合（一天排多个班次且假期时间类型为天时使用），取消部分销假
     */
    private List<Long> startShifts;
    /**
     * 结束日期所选的班次集合（一天排多个班次且假期时间类型为天时使用），取消部分销假
     */
    private List<Long> endShifts;

    @Autowired
    private IWaEmpLeaveCancelInfoRepository waEmpLeaveCancelInfoRepository;

    public Integer checkTimeRepeat(Long empId, Integer leaveId, Long startTime, Long endTime) {
        return waEmpLeaveCancelInfoRepository.checkTimeRepeat(empId, leaveId, startTime, endTime);
    }

    public void save(WaEmpLeaveCancelInfoDo cancelInfoDo) {
        waEmpLeaveCancelInfoRepository.save(cancelInfoDo);
    }

    public List<WaEmpLeaveCancelInfoDo> getLeaveCancelInfoList(Long leaveCancelId) {
        return waEmpLeaveCancelInfoRepository.getLeaveCancelInfoList(leaveCancelId);
    }

    public List<WaEmpLeaveCancelInfoDo> getListByLeaveCancelId(List<Long> leaveCancelIds) {
        return waEmpLeaveCancelInfoRepository.getListByLeaveCancelId(leaveCancelIds);
    }

    public List<String> getShiftDefNameList(String tenantId) {
        if (StringUtils.isBlank(this.startShift) && StringUtils.isBlank(this.endShift)) {
            return null;
        }
        Set<Long> userShiftIdSet = new LinkedHashSet<>();
        if (StringUtils.isNotBlank(this.startShift)) {
            List<Long> startShiftList = FastjsonUtil.toArrayList(this.startShift, Long.class);
            userShiftIdSet.addAll(startShiftList);
        }
        if (StringUtils.isNotBlank(this.endShift)) {
            List<Long> endShiftList = FastjsonUtil.toArrayList(this.endShift, Long.class);
            userShiftIdSet.addAll(endShiftList);
        }
        List<Integer> userShiftIdList = new ArrayList<>(userShiftIdSet).stream().map(Long::intValue).collect(Collectors.toList());
        Map<Integer, WaShiftDef> shiftDefMap = SpringUtil.getBean(WaCommonService.class).getCorpAllShiftDef(tenantId, userShiftIdList);
        if (MapUtils.isEmpty(shiftDefMap)) {
            return null;
        }
        return shiftDefMap.values().stream()
                .map(it -> LangParseUtil.getI18nLanguage(it.getI18nShiftDefName(), it.getShiftDefName()))
                .collect(Collectors.toList());
    }
}