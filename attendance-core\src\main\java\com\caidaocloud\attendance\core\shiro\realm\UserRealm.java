package com.caidaocloud.attendance.core.shiro.realm;

import com.caidao1.system.mybatis.model.SysUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.codec.Base64;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.util.SimpleByteSource;

/**
 * Created by darren on 16/4/16.
 */
@Slf4j
public class UserRealm extends AuthorizingRealm {

    public static String KAPTCHA_SESSION_KEY = "KAPTCHA_SESSION_KEY";

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principalCollection) {
        String username = (String) principalCollection.getPrimaryPrincipal();
        SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();
        //暂时不加权限
        return authorizationInfo;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return super.supports(token);
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
        String account = (String) token.getPrincipal();
        // 根据账号名，查询账户信息
        SysUserInfo user = null;//caidaoShiroService.getSysUserInfo(account);
        if (user == null) {
            throw new UnknownAccountException();//没找到帐号
        } else {
            if (user.getStatus() == 2) {
                SecurityUtils.getSubject().logout();
                throw new DisabledAccountException();
            } else if (user.getStatus() == 3) {
                throw new LockedAccountException("账号被锁定，请联系管理员");
            }
        }
        String baseAccount = new String(Base64.encode(account.getBytes()));
        //交给AuthenticatingRealm使用CredentialsMatcher进行密码匹配，如果觉得人家的不好可以自定义实现
        return new SimpleAuthenticationInfo(
                baseAccount, //用户名
                user.getPasswd(), //密码
                new SimpleByteSource(user.getCredentialsSalt().getBytes()),//salt=username+salt
                getName()); //realm name;
    }

    @Override
    protected void assertCredentialsMatch(AuthenticationToken token, AuthenticationInfo info) throws AuthenticationException {
        // 解决密码重置后登不了的问题 CLOUD-1266 密码重置后无法登录账号
//        if(!SecurityUtils.getSubject().isAuthenticated()){
//            if(SecurityUtils.getSecurityManager() instanceof CachingSecurityManager) {
//                CachingSecurityManager cachingSecurityManager = (CachingSecurityManager) SecurityUtils.getSecurityManager();
//                cachingSecurityManager.getCacheManager().getCache("authenticationCache").remove(token.getPrincipal());
//            }
//        }
//        try {
//            super.assertCredentialsMatch(token, info);
//        } catch (IncorrectCredentialsException e) {
//            // 捕获异常错误
//            final String account = (String) token.getPrincipal();
//            SysUserInfo userInfo = caidaoShiroService.getSysUserInfo(account);
//            passwordRuleService.updateLoginErrorCount(userInfo);
//            throw new IncorrectCredentialsException(e);
//        }
    }

    @Override
    public void clearCachedAuthorizationInfo(PrincipalCollection principals) {
        super.clearCachedAuthorizationInfo(principals);
    }

    @Override
    protected void doClearCache(PrincipalCollection principalcollection) {
        //  注销后回调的方法，从ehcach中移除登录过的用户
        Object principal = principalcollection.getPrimaryPrincipal();
        Cache<Object, AuthenticationInfo> cache = getAuthenticationCache();
        if (cache != null) {
            cache.remove(Base64.decodeToString(principal.toString()));
        }
    }

    @Override
    public void clearCachedAuthenticationInfo(PrincipalCollection principals) {
        super.clearCachedAuthenticationInfo(principals);
    }

    @Override
    public void clearCache(PrincipalCollection principals) {
        super.clearCache(principals);
    }

    public void clearAllCachedAuthorizationInfo() {
        getAuthorizationCache().clear();
    }

    public void clearAllCachedAuthenticationInfo() {
        getAuthenticationCache().clear();
    }

    public void clearAllCache() {
        clearAllCachedAuthenticationInfo();
        clearAllCachedAuthorizationInfo();
    }

}