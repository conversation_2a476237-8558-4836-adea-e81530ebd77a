package com.caidaocloud.attendance.core.wa.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyLeaveTimeBaseDto {
    @ApiModelProperty("单据ID")
    private Integer leaveId;
    @ApiModelProperty("请假日期")
    private Long leaveDate;
    @ApiModelProperty("半天开始")
    private String shalfDay;
    @ApiModelProperty("半天结束")
    private String ehalfDay;
    @ApiModelProperty("开始时间")
    private Integer startTime;
    @ApiModelProperty("结束时间")
    private Integer endTime;
    @ApiModelProperty("休假时间类型")
    private Short periodType;
    @ApiModelProperty("时长")
    private Float timeDuration;
    @ApiModelProperty("时长文本")
    private String timeDurationTxt;
    @ApiModelProperty("时间单位")
    private Short timeUnit;
    @ApiModelProperty("假期名称")
    private String leaveName;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("状态文本")
    private String statusName;
    @ApiModelProperty("休假时间文本")
    private String leaveTimeTxt;
    @ApiModelProperty("审批流程key")
    private String businessKey;
    @ApiModelProperty("审批流程ID")
    private String procInstId;
    @ApiModelProperty("申请时间")
    private Long applyTime;
    private Integer funcType;
    @ApiModelProperty("休假开始时间")
    private String startDate;
    @ApiModelProperty("休假结束时间")
    private String endDate;
    @ApiModelProperty("申请类型")
    private String applyName;
    @ApiModelProperty("假期类型编码")
    private String leaveTypeCode;
    @ApiModelProperty("休假单据实际开始时间")
    private Long shiftStartTime;
    @ApiModelProperty("休假单据实际结束时间")
    private Long shiftEndTime;
    @ApiModelProperty("销假时长")
    private Float cancelTimeDuration;
    @ApiModelProperty("假期多语言名称")
    private String i18nLeaveName;
    @ApiModelProperty("使用的班次ID")
    private Integer useShiftDefId;
}
