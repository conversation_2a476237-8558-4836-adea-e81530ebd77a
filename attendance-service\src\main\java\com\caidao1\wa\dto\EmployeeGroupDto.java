package com.caidao1.wa.dto;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class EmployeeGroupDto {
    /**
     * 分组id
     */
    @ApiModelProperty("表单ID")
    private Long empGroupId;

    /**
     * 分组名字
     */
    @ApiModelProperty("分组名称")
    private String groupName;

    /**
     * 分组表达式
     */
    @ApiModelProperty("分组表达式")
    private String expression;

    /**
     * 扩展字段
     */
    @ApiModelProperty("扩展字段")
    private String ext;

    /**
     * 业务key
     */
    @ApiModelProperty("业务key")
    private String businessKey;

    /**
     * 分组类型
     */
    @ApiModelProperty("分组类型")
    private String groupType;

    /**
     * 分组排序
     */
    @ApiModelProperty("分组排序")
    private Integer sortNum;

    /**
     * 分组备注
     */
    @ApiModelProperty("分组备注")
    private String remark;

    @ApiModelProperty("异步任务时传入用户ID")
    private Long defaultUserId;

    @ApiModelProperty("异步任务时传入租户ID")
    private Long defaultTenantId;

    private Map expMap;

    public void bulidExpression(String groupExp, String groupNote){
        Map map = new HashMap<>();
        map.put("groupExp", groupExp);
        map.put("groupNote", groupNote);
        this.setExpression(JSON.toJSONString(map));
    }

    public String getGroupExp(){
        initExpMap();

        if(null != expMap){
            return (String) expMap.get("groupExp");
        }

        return null;
    }

    public String getGroupNote(){
        initExpMap();

        if(null != expMap){
            return (String) expMap.get("groupNote");
        }

        return null;
    }

    private void initExpMap(){
        if(null == expMap){
            expMap = JSON.parseObject(this.getExpression(), Map.class);
        }
    }
}

