package com.caidaocloud.attendance.service.application.service;

import com.caidao1.wa.mybatis.model.WaEmpOvertime;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidao1.wa.mybatis.model.WaWorktimeDetail;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.dto.EmpOverInfo;
import com.caidaocloud.attendance.core.wa.dto.ot.GetOtTimeResultDto;
import com.caidaocloud.attendance.core.wa.dto.ot.OtCompensateTypeListDto;
import com.caidaocloud.attendance.core.wa.enums.AppTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.service.application.dto.OvertimeCarryForwardDto;
import com.caidaocloud.attendance.service.application.dto.overtime.OtLeftDurationDto;
import com.caidaocloud.attendance.service.application.dto.overtime.OvertimeLeftDurationDto;
import com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryQuotaDo;
import com.caidaocloud.attendance.service.interfaces.dto.OverApplyRevokeDto;
import com.caidaocloud.attendance.service.interfaces.dto.OverApplySaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.OvertimeDateQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OtLeftDurationRequestDto;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 加班申请
 *
 * <AUTHOR>
 * @Date 2021/3/20
 */
public interface IOvertimeApplyService {
    Map getOvertimeInfoById(Integer otId);

    Result<?> saveOtData(OverApplySaveDto dto, AppTypeEnum appType) throws Exception;

    List<KeyValue> getOvertimeDateList(OvertimeDateQueryDto queryDto);

    Result<GetOtTimeResultDto> getOtTotalTime(OverApplySaveDto overtimeSaveDto);

    Result<Boolean> revokeEmpOt(OverApplyRevokeDto dto, UserInfo userInfo) throws Exception;

    /**
     * 获取加班补偿方式
     *
     * @param empId
     * @param start
     * @param end
     * @param overtimeDate 加班日期（年月日unix时间戳，精确到秒）
     * @return
     */
    Result<List<OtCompensateTypeListDto>> getCompensateTypeList(Long empId, Long start, Long end, Long overtimeDate);

    Result<Boolean> revokeOvertimeWorkflow(String tenantId, Long userId, WaSob waSob, WaEmpOvertime empOvertime,
                                           String revokeReason, ApprovalStatusEnum approvalStatus,
                                           List<EmpCompensatoryQuotaDo> quotas, boolean ifBatch) throws Exception;

    List<OvertimeCarryForwardDto> getEmpOtCompensatoryList(String tenantId, Long quotaId);

    List<EmpCompensatoryQuotaDo> getOvertimeToCompensatoryQuotas(String tenantId, Integer otId);

    String checkQuotaUsed(String tenantId, List<EmpCompensatoryQuotaDo> quotas);

    OvertimeLeftDurationDto getEmpOvertimeLeftDuration(Long empId);

    AttendancePageResult<OtLeftDurationDto> getEmpOvertimeLeftDurationPageList(OtLeftDurationRequestDto dto, UserInfo userInfo);

    void deleteByBatchId(String tenantId, Long batchId);

    boolean checkIfNotInWorkTime(Map<Integer, WaShiftDef> shiftDefMap, WaWorktimeDetail worktimeDetail, long overtimeStartTime, long overtimeEndTime);

    boolean checkIfBelongPreDay(Map<Integer, WaShiftDef> shiftDefMap, WaWorktimeDetail preWorkTimeDetail, long startTime);

    boolean checkIfBelongPreDayOnSave(Map<Integer, WaShiftDef> shiftDefMap, WaWorktimeDetail preWorkTimeDetail, long endTime);

    long calRestTotalTime(String tenantId, Long otStartTime, Long otEndTime, WaWorktimeDetail worktimeDetail, Map<Integer, WaShiftDef> shiftDefMap);

    Long getShiftOffDutyEndTime(Map<Integer, WaShiftDef> shiftDefMap, WaWorktimeDetail worktimeDetail);

    List<EmpOverInfo> getEmpDailyOtList(String tenantId, List<Long> empIds, Long startTime, Long endTime);

    BigDecimal getEmpPeriodOtDuration(String tenantId, Long empId, Long startDate);
}