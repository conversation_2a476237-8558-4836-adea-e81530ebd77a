package com.caidaocloud.attendance.core.wa.dto;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.wa.enums.ShiftTimeBelongTypeBaseEnum;
import com.caidaocloud.attendance.core.wa.dto.shift.*;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 员工排班信息
 */
@Data
public class EmpShiftInfo {
    private Long empid;

    private Long workDate;

    /**
     * 班次ID（一天排一个班时使用）
     */
    private Integer shiftDefId;

    /**
     * 多班次信息集合（一天排多个班时使用）
     */
    private List<EmpShiftInfo> shiftDefList;

    private Integer dateType;

    private Boolean isNight;

    private Integer startTime;

    private Integer endTime;

    private Boolean isNoonRest;

    private Integer noonRestStart;

    private Integer noonRestEnd;

    private Integer restTotalTime;

    private Integer workTotalTime;

    private Integer onDutyStartTime;

    private Integer onDutyEndTime;

    private Integer offDutyStartTime;

    private Integer offDutyEndTime;

    private Boolean isHalfdayTime;

    private Integer halfdayTime;

    private Boolean isSpecial;

    private Integer specialWorkTime;

    private Boolean isFlexibleWork;

    private Integer flexibleOnDutyStartTime;

    private Integer flexibleOnDutyEndTime;

    private Integer flexibleOffDutyStartTime;

    private Integer flexibleOffDutyEndTime;

    private Integer flexibleWorkType;

    private Integer overtimeStartTime;

    private Integer overtimeEndTime;

    private List<ShiftRestPeriods> restPeriods;

    private List<ShiftRestPeriods> overtimeRestPeriods;

    private Integer flexibleWorkRule;

    private BigDecimal flexibleWorkLate;

    private BigDecimal flexibleWorkEarly;

    private String flexibleOffWorkRule;

    private Integer flexibleShiftSwitch;

    /**
     * 中途打卡时间段
     */
    private Object midwayClockTimes;

    /**
     * 中途打卡时间段
     */
    private List<MidwayClockTimeInfo> midwayClockTimeList;

    private List<MultiWorkTimeBaseDto> multiWorkTimeList;

    private List<MultiCheckinTimeInfo> multiCheckinTimeList;

    private Integer startTimeBelong;

    private Integer endTimeBelong;

    private Integer noonRestStartBelong;

    private Integer noonRestEndBelong;

    private Integer onDutyStartTimeBelong;

    private Integer onDutyEndTimeBelong;

    private Integer offDutyStartTimeBelong;

    private Integer offDutyEndTimeBelong;

    private Integer overtimeStartTimeBelong;

    private Integer overtimeEndTimeBelong;

    private Integer halfdayTimeBelong;

    private Integer halfdayType;

    private List<MultiOvertimeDto> multiOvertimeList;

    private String belongModule;

    private Boolean temporaryShift;

    /**
     * 入职日期
     */
    private Long hireDate;

    /**
     * 离职日期
     */
    private Long terminationDate;

    /**
     * 实习日期
     */
    private Long internshipDate;

    public Integer getFlexibleWorkType() {
        if (this.flexibleWorkType == null) return 0;
        return flexibleWorkType;
    }

    /**
     * 班次的有效开始时间
     *
     * @return
     */
    public Long getShiftStartTime() {
        Integer min = startTime;
        if (this.startTime > this.onDutyStartTime) {
            min = this.onDutyStartTime;
        }
        return workDate + (min * 60);
    }

    /**
     * 班次的有效截止时间
     *
     * @return
     */
    public Long getShiftEndTime() {
        Long time = workDate + (offDutyEndTime * 60);
        // 班次跨夜或下班打卡区间跨夜都算跨夜了
        if (CdWaShiftUtil.checkCrossNight(this.startTime, this.endTime, this.dateType) || CdWaShiftUtil.checkCrossNight(this.offDutyStartTime, this.offDutyEndTime, this.dateType)) { // 跨夜场景一
            time = DateUtil.addDate(this.workDate * 1000, 1) + (this.offDutyEndTime * 60);
        }
        //弹性工作 跨夜场景二  TODO ethan

        return time;
    }

    public Integer getDateType() {
        // 如果是公司特殊假日，则按休息日类型计算
        if (DateTypeEnum.DATE_TYP_4.getIndex().equals(dateType)) {
            return DateTypeEnum.DATE_TYP_2.getIndex();
        }
        return dateType;
    }

    public RestPeriodDto getNoonRestDto() {
        RestPeriodDto periodDto = new RestPeriodDto();
        periodDto.setNoonRestStart(this.noonRestStart);
        periodDto.setNoonRestEnd(this.noonRestEnd);
        periodDto.setNoonRestStartBelong(this.noonRestStartBelong);
        periodDto.setNoonRestEndBelong(this.noonRestEndBelong);
        return periodDto;
    }

    public Integer doGetRealStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.startTimeBelong)) {
            return startTime + 1440;
        }
        return startTime;
    }

    public Integer doGetRealEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.endTimeBelong)) {
            return endTime + 1440;
        }
        return endTime;
    }

    public Integer doGetRealNoonRestStart() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.noonRestStartBelong)) {
            return noonRestStart + 1440;
        }
        return noonRestStart;
    }

    public Integer doGetRealNoonRestEnd() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.noonRestEndBelong)) {
            return noonRestEnd + 1440;
        }
        return noonRestEnd;
    }

    public Integer doGetRealOnDutyStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.onDutyStartTimeBelong)) {
            return onDutyStartTime + 1440;
        }
        return onDutyStartTime;
    }

    public Integer doGetRealOnDutyEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.onDutyEndTimeBelong)) {
            return onDutyEndTime + 1440;
        }
        return onDutyEndTime;
    }

    public Integer doGetRealOffDutyStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.offDutyStartTimeBelong)) {
            return offDutyStartTime + 1440;
        }
        return offDutyStartTime;
    }

    public Integer doGetRealOffDutyEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong)) {
            return offDutyEndTime + 1440;
        }
        return offDutyEndTime;
    }

    public Integer doGetShiftLastWorkTimeRealOffDutyEndTime() {
        if (CollectionUtils.isNotEmpty(this.multiWorkTimeList)) {
            List<MultiWorkTimeBaseDto> workTimeList = this.multiWorkTimeList;
            workTimeList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime).reversed());
            MultiWorkTimeBaseDto lastWorkTimeDto = workTimeList.get(0);
            return lastWorkTimeDto.doGetRealOffDutyEndTime();
        }
        return doGetRealOffDutyEndTime();
    }

    public Integer doGetRealOvertimeStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.overtimeStartTimeBelong)) {
            return overtimeStartTime + 1440;
        }
        return overtimeStartTime;
    }

    public Integer doGetRealOvertimeEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.overtimeEndTimeBelong)) {
            return overtimeEndTime + 1440;
        }
        return overtimeEndTime;
    }

    public Integer doGetRealHalfdayTime() {
        if (null != this.halfdayTimeBelong) {
            if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.halfdayTimeBelong)) {
                return this.halfdayTime + 1440;
            } else {
                return this.halfdayTime;
            }
        } else {
            if (this.halfdayTime < this.startTime) {
                return this.halfdayTime + 1440;
            }
            return this.halfdayTime;
        }
    }

    public boolean checkClockTimeCrossNight() {
        if (this.onDutyStartTime == null || this.offDutyEndTime == null) {
            return Boolean.FALSE;
        }
        if (this.onDutyStartTimeBelong != null && this.offDutyEndTimeBelong != null) {
            return ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(this.onDutyStartTimeBelong)
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong);
        } else {
            return this.offDutyEndTime <= this.onDutyStartTime;
        }
    }

    public boolean checkOffDutyTimeCrossNight() {
        if (this.offDutyEndTime == null || this.offDutyStartTime == null) {
            return Boolean.FALSE;
        }
        if (this.offDutyEndTimeBelong != null && this.offDutyStartTimeBelong != null) {
            return ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(this.offDutyStartTimeBelong)
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong);
        } else {
            return this.offDutyEndTime < this.offDutyStartTime;
        }
    }

    public List<EmpShiftInfo> doGetShiftDefList() {
        if (CollectionUtils.isNotEmpty(this.shiftDefList)) {
            return this.shiftDefList;
        }
        return Lists.newArrayList(this);
    }

    public Integer doGetWorkTotalTime() {
        List<EmpShiftInfo> shiftInfoList = this.doGetShiftDefList();
        if (shiftInfoList == null || shiftInfoList.isEmpty()) {
            return this.workTotalTime;
        }
        return shiftInfoList.stream().mapToInt(info -> Optional.ofNullable(info.getWorkTotalTime()).orElse(0)).sum();
    }

    public String doGetShiftIds() {
        List<EmpShiftInfo> shiftInfoList = this.doGetShiftDefList();
        if (shiftInfoList == null || shiftInfoList.isEmpty()) {
            return Optional.ofNullable(this.shiftDefId).map(String::valueOf).orElse("");
        }
        return shiftInfoList.stream().map(EmpShiftInfo::getShiftDefId).filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(","));
    }

    public boolean doCheckMultiShift() {
        return CollectionUtils.isNotEmpty(this.shiftDefList) && this.shiftDefList.size() > 1;
    }

    public boolean doCheckMultiShiftFiled() {
        return CollectionUtils.isNotEmpty(this.multiWorkTimeList) && this.multiWorkTimeList.size() > 1;
    }

    public List<MultiWorkTimeBaseDto> doGetMultiWorkTimeList() {
        if (CollectionUtils.isNotEmpty(this.multiWorkTimeList)) {
            return this.multiWorkTimeList;
        }
        return Lists.newArrayList();
    }

    public List<MultiOvertimeDto> doGetMultiOvertimeList() {
        if (null != this.multiOvertimeList && !this.multiOvertimeList.isEmpty()) {
            List<MultiOvertimeDto> multiOvertimeDtoList = this.multiOvertimeList;
            multiOvertimeDtoList.sort(Comparator.comparing(MultiOvertimeDto::doGetRealOvertimeStartTime));
            return multiOvertimeDtoList;
        } else if (null != this.getOvertimeStartTime() && null != this.getOvertimeEndTime()) {
            MultiOvertimeDto overtimeDto = new MultiOvertimeDto();
            overtimeDto.setOvertimeStartTime(this.getOvertimeStartTime());
            overtimeDto.setOvertimeEndTime(this.getOvertimeEndTime());
            overtimeDto.setOvertimeStartTimeBelong(Optional.ofNullable(this.getOvertimeStartTimeBelong())
                    .orElse(ShiftTimeBelongTypeEnum.TODAY.getIndex()));
            if (null != this.getOvertimeEndTimeBelong()) {
                overtimeDto.setOvertimeEndTimeBelong(this.getOvertimeEndTimeBelong());
            } else {
                overtimeDto.setOvertimeEndTimeBelong(this.getOvertimeStartTime() > this.getOvertimeEndTime()
                        ? ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex()
                        : ShiftTimeBelongTypeEnum.TODAY.getIndex());
            }
            return Lists.newArrayList(overtimeDto);
        }
        return Lists.newArrayList();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EmpShiftInfo that = (EmpShiftInfo) o;
        return Objects.equals(this.empid, that.empid) && Objects.equals(this.workDate, that.workDate) && Objects.equals(this.shiftDefId, that.shiftDefId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.empid, this.workDate, this.shiftDefId);
    }

    public Integer doGetOnDutyStartTimeForView() {
        int onDutyStartTime = this.onDutyStartTime;
        if (onDutyStartTime >= 0) {
            return onDutyStartTime;
        }
        onDutyStartTime += 1440;
        return onDutyStartTime;
    }
}
