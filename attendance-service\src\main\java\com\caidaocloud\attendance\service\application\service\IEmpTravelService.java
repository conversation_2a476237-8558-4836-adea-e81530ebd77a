package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDo;
import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelSaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.RevokeEmpTraveDto;
import com.caidaocloud.attendance.service.interfaces.dto.user.MyTravelTimeDto;
import com.caidaocloud.attendance.core.wa.dto.shift.MyWorkDateShiftDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;

import java.util.List;

/**
 * 出差申请
 *
 * <AUTHOR>
 * @Date 2021/9/8
 */
public interface IEmpTravelService {
    PageResult<EmpTravelDto> getEmpTravelPageList(EmpTravelReqDto dto, UserInfo userInfo);

    PageResult<EmpTravelDto> getEmpTravelPageListOfPortal(QueryPageBean queryPageBean, Boolean ifBatch);

    Result<Boolean> preCheckBeforeRevoke(WaEmpTravelDo travelDo, WaTravelTypeDo travelTypeDo);

    Result<Boolean> revokeEmpTravel(RevokeEmpTraveDto dto, UserInfo userInfo);

    Result checkOrSaveTravelTime(EmpTravelSaveDto dto) throws Exception;

    List<MyTravelTimeDto> getTravelTimeList(String belongOrgId, Long empid, Long daytime, Long endTime, MyWorkDateShiftDto shiftDto);

    Result<Boolean> revokeTravelWorkflow(String tenantId, Long userId, WaEmpTravelDo empTravel, WaTravelTypeDo travelType, String revokeReason, ApprovalStatusEnum approvalStatus);

    void deleteByBatchId(String tenantId, Long batchTravelId);

    PageResult<EmpTravelDto> getEmpTravelPageListOfPortal(QueryPageBean queryPageBean, BusinessCodeEnum workflowEnum);
}
