package com.caidaocloud.attendance.service.wfm.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@ApiModel(value = "工序key")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ProcessKey {
    @ApiModelProperty("排班日期")
    private String schedulingTime;
    @ApiModelProperty("订单ID")
    private String orderId;
    @ApiModelProperty("工序ID")
    private String processId;
    @ApiModelProperty("班次ID")
    private String shiftId;
}
