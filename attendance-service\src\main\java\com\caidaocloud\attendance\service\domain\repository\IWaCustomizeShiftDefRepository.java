package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaCustomizeShiftDefDo;
import com.caidaocloud.attendance.service.interfaces.dto.shift.CustomizeShiftDefQueryDto;

import java.util.List;

/**
 * 自定义班次设置
 *
 * <AUTHOR>
 * @Date 2025/1/20
 */
public interface IWaCustomizeShiftDefRepository {
    void updateById(WaCustomizeShiftDefDo updateData);

    void insert(WaCustomizeShiftDefDo saveData);

    WaCustomizeShiftDefDo getById(Long id);

    void deleteById(Long id);

    List<WaCustomizeShiftDefDo> selectList(CustomizeShiftDefQueryDto queryDto);

    List<WaCustomizeShiftDefDo> selectList(String tenantId, String belongModule, Long startDate, Long endDate);

    List<WaCustomizeShiftDefDo> selectListByIds(List<Long> shiftDefIds);

    List<WaCustomizeShiftDefDo> selectListByRelShiftIds(List<Integer> waShiftDefIds);
}
