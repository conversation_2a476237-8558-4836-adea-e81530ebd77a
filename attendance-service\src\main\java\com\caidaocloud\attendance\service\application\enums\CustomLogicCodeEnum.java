package com.caidaocloud.attendance.service.application.enums;

/**
 * 考勤自定义逻辑配置-业务编码
 */
public enum CustomLogicCodeEnum {
    UPPER_LEVEL_CHECK("UPPER_LEVEL_CHECK", "申请休假时上级假期校验", CustomLogicBelongBusinessEnum.LEAVE);

    private String code;
    private String name;
    private CustomLogicBelongBusinessEnum belongBusiness;

    CustomLogicCodeEnum(String code, String name, CustomLogicBelongBusinessEnum belongBusiness) {
        this.code = code;
        this.name = name;
        this.belongBusiness = belongBusiness;
    }

    public static CustomLogicCodeEnum getByCode(String code) {
        for (CustomLogicCodeEnum c : CustomLogicCodeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public CustomLogicBelongBusinessEnum getBelongBusiness() {
        return belongBusiness;
    }

    public void setBelongBusiness(CustomLogicBelongBusinessEnum belongBusiness) {
        this.belongBusiness = belongBusiness;
    }
}
