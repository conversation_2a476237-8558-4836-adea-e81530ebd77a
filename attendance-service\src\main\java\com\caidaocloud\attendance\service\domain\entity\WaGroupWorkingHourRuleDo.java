package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.domain.repository.IWaGroupWorkingHourRuleRepository;
import com.caidaocloud.dto.UserInfo;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Data
@Slf4j
@Service
public class WaGroupWorkingHourRuleDo {
    private Long ruleId;

    private String workType;

    private Integer compStatPeriod;

    private Integer compStatInterval;

    private Integer compStatMethod;

    private Integer compStdCalcMethod;

    private BigDecimal compStdWorkingHours;

    private String compStdDateType;

    private Integer waGroupId;

    private String tenantId;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    @Autowired
    private IWaGroupWorkingHourRuleRepository waGroupWorkingHourRuleRepository;

    public void doInitCrtField() {
        UserInfo userInfo = UserContext.preCheckUser();
        this.setTenantId(userInfo.getTenantId());
        this.setDeleted(0);
        this.setCreateBy(userInfo.getUserId());
        this.setCreateTime(System.currentTimeMillis());
        this.setUpdateBy(this.getCreateBy());
        this.setUpdateTime(this.getCreateTime());
    }

    public void doClearCompField() {
        this.setCompStatPeriod(0);
        this.setCompStatInterval(0);
        this.setCompStatMethod(0);
        this.setCompStdCalcMethod(0);
        this.setCompStdWorkingHours(BigDecimal.ZERO);
        this.setCompStdDateType("");
    }

    public WaGroupWorkingHourRuleDo getById(Long ruleId) {
        return waGroupWorkingHourRuleRepository.getById(ruleId);
    }

    public void updateById(WaGroupWorkingHourRuleDo updateData) {
        waGroupWorkingHourRuleRepository.updateById(updateData);
    }

    public void save(WaGroupWorkingHourRuleDo addData) {
        waGroupWorkingHourRuleRepository.insert(addData);
    }

    public void deleteById(Long id) {
        waGroupWorkingHourRuleRepository.deleteById(id);
    }

    public List<WaGroupWorkingHourRuleDo> getListByWaGroupIds(String tenantId, List<Integer> waGroupIds) {
        return waGroupWorkingHourRuleRepository.selectListByWaGroupIds(tenantId, waGroupIds);
    }

    public WaGroupWorkingHourRuleDo getByWaGroupId(String tenantId, Integer waGroupId) {
        List<WaGroupWorkingHourRuleDo> list = getListByWaGroupIds(tenantId, Lists.newArrayList(waGroupId));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }
}