package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.sdk.dto.SdkLeaveExtensionApplyDTO;
import com.caidaocloud.attendance.sdk.dto.SdkLeaveExtensionQuotaDTO;
import com.caidaocloud.attendance.sdk.dto.SdkLeaveExtensionRevokeDTO;
import com.caidaocloud.attendance.sdk.feign.fallback.LeaveExtensionFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 假期延期
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = LeaveExtensionFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "leaveExtensionFeignClient")
public interface ILeaveExtensionFeignClient {

    /**
     * 假期延期申请
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/leaveExtension/v1/apply")
    Result<?> applyLeaveExtension(@RequestBody SdkLeaveExtensionApplyDTO dto);

    /**
     * 假期延期申请撤销
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/leaveExtension/v1/revoke")
    Result<?> revokeLeaveExtension(@RequestBody SdkLeaveExtensionRevokeDTO dto);

    /**
     * 假期延期申请假期类型
     *
     * @return
     */
    @GetMapping(value = "/api/attendance/leaveExtension/v1/quotaType")
    Result<?> getLeaveExtensionQuotaType();

    /**
     * 获取假期延期申请额度
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/leaveExtension/v1/quota")
    Result<?> getLeaveExtensionQuotaList(@RequestBody SdkLeaveExtensionQuotaDTO dto);
}
