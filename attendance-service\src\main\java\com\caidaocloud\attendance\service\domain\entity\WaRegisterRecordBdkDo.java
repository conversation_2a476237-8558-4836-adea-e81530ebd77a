package com.caidaocloud.attendance.service.domain.entity;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.service.domain.repository.IRegisterRecordBdkRepository;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordBdkPo;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;
import com.caidaocloud.util.ObjectConverter;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Service
public class WaRegisterRecordBdkDo {
    private Long recordId;
    private Long empid;
    private Integer registerType;
    private String resultDesc;
    private Integer resultType;
    private String reason;
    private String regAddr;
    private Long regDateTime;
    private BigDecimal lng;
    private BigDecimal lat;
    private Long belongDate;
    private String mobDeviceNum;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private String normalAddr;
    private String normalDate;
    private Integer type;
    private String owRmk;
    private String picList;
    private Long hisRegTime;
    private Integer shiftDefId;
    private Boolean isDeviceError;
    private String oriMobDeviceNum;
    private Boolean isWorkflow;
    private Integer approvalStatus;
    private String approvalReason;
    private String filePath;
    private String revokeReason;
    private Long siteId;
    private String province;
    private String city;
    private Integer startTime;
    private Integer endTime;
    private Long corpid;
    private String belongOrgId;
    private Long lastApprovalTime;
    private Integer ifValid;
    private String regDateTimes;
    /**
     * 班次ID（一天排多个班时会有多个值，每个班次ID使用英文逗号,分隔）
     */
    private String shiftDefIds;

    //其他字段
    private String workno;
    private String empName;
    private Long orgid;
    private Long approvalTime;
    private String orgName;
    private String fullPath;
    @ApiModelProperty("员工类型")
    private Long empStyle;
    @ApiModelProperty("员工类型名称")
    private String empStyleName;
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("员工状态")
    private Integer empStatus;
    @ApiModelProperty("员工状态名称")
    private String empStatusName;
    @ApiModelProperty("用户名称")
    private String userName;

    private String registerTypeName;
    private String shortName;
    private String statusName;
    private String files;
    private String fileNames;
    private String workCity;

    @Autowired
    private IRegisterRecordBdkRepository registerRecordBdkRepository;

    /**
     * 根据主键查询
     * @param recordId
     * @return
     */
    public WaRegisterRecordBdkDo getWaRegisterRecordById(Long recordId) {
        WaRegisterRecordBdkPo recordBdkPo = registerRecordBdkRepository.getWaRegisterRecordById(recordId);
        if (null == recordBdkPo) {
            return null;
        }
        return ObjectConverter.convert(recordBdkPo, WaRegisterRecordBdkDo.class);
    }

    /**
     * 更新
     * @param recordBdkDo
     */
    public void updateRegisterRecordBdk(WaRegisterRecordBdkDo recordBdkDo) {
        if (null == recordBdkDo) {
            return;
        }
        registerRecordBdkRepository.updateRegisterRecordBdk(ObjectConverter.convert(recordBdkDo, WaRegisterRecordBdkPo.class));
    }

    /**
     * 保存
     * @param recordBdkDo
     */
    public void save(WaRegisterRecordBdkDo recordBdkDo) {
        if (null == recordBdkDo) {
            return;
        }
        registerRecordBdkRepository.save(ObjectConverter.convert(recordBdkDo, WaRegisterRecordBdkPo.class));
    }

    /**
     * 获取补卡次数
     * @param empId
     * @param type
     * @param startTime
     * @param endTime
     * @return
     */
    public Integer getEmpDkCountByType(Long empId, Integer type, Long startTime, Long endTime) {
        return registerRecordBdkRepository.getEmpDkCountByType(empId, type, startTime, endTime);
    }

    public AttendancePageResult<WaRegisterRecordBdkDo> getRegisterPageList(RegisterRecordRequestDto requestDto) {
        return registerRecordBdkRepository.getRegisterPageList(requestDto);
    }

    public List<WaRegisterRecordBdkDo> getEmpBdkRegisterList(String belongOrgId, List<Long> empIdList, Long startDate, Long endDate) {
        return registerRecordBdkRepository.getEmpBdkRegisterList(belongOrgId, empIdList, startDate, endDate);
    }

    @CDText(exp = {"workCity" + TextAspect.PLACE}, classType = WaRegisterRecordBdkDo.class)
    public WaRegisterRecordBdkDo getRegisterDetailById(Long corpId, Long registerId) {
        Map map = registerRecordBdkRepository.getRegisterDetailById(corpId, registerId);
        if (null == map) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(map), WaRegisterRecordBdkDo.class);
    }

    public List<WaRegisterRecordBdkDo> getEmpRegisterRecordList(Long empId, Long startDate, Long endDate, List<Integer> statusList) {
        List<WaRegisterRecordBdkPo> list = registerRecordBdkRepository.getEmpRegisterRecordList(empId, startDate, endDate, statusList);
        return ObjectConverter.convertList(list, WaRegisterRecordBdkDo.class);
    }

    /**
     * 删除
     * @param recordId
     */
    public int delete(Long recordId) {
        return registerRecordBdkRepository.delete(recordId);
    }
}
