package com.caidaocloud.attendance.core.commons.utils;

import org.apache.commons.lang3.StringEscapeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Whitelist;

public class JsoupUtil {
    private static final Whitelist whitelist = Whitelist.basic();
    /** 配置过滤化参数,不对代码进行格式化 */
    private static final Document.OutputSettings outputSettings = new Document.OutputSettings().prettyPrint(false);
    static {
        //如果想给每一个标签添加一组属性，使用:all。例如： addAttributes(":all", "class").即给每个标签添加class属性。
        whitelist.addAttributes(":all", "style");
    }

    public static String clean(String content) {
        String result = Jsoup.clean(content, "", whitelist, outputSettings);
        return StringEscapeUtils.unescapeHtml4(result);
    }

    public static String getSimpleText(String content) {
        String result = Jsoup.clean(content, "", Whitelist.simpleText(), outputSettings);
        return StringEscapeUtils.unescapeHtml4(result);
    }

    public static void main(String[] args) throws Exception {
        String text = "<a href=\"http://www.baidu.com/a\" onclick=\"alert(1);\">sss</a><script>alert(0);" +
                "\n000sd</script> >9 & ==% <44 ";
        System.out.println(clean(text));
        System.out.println(StringEscapeUtils.escapeXml11(text));
        System.out.println(StringEscapeUtils.unescapeHtml4(clean(text)));
    }

}
