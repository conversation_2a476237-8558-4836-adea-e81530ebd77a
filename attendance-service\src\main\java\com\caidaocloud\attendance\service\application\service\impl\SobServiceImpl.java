package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.system.mybatis.model.SysCorpOrgExample;
import com.caidao1.wa.mybatis.mapper.WaGroupMapper;
import com.caidao1.wa.mybatis.model.WaGroup;
import com.caidao1.wa.mybatis.model.WaGroupExample;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.service.application.service.ISobService;
import com.caidaocloud.attendance.service.domain.entity.WaSobDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: Aaron.Chen
 * @Date: 2021/5/6 15:20
 * @Description:
 **/
@Slf4j
@Service
public class SobServiceImpl implements ISobService {

    @Autowired
    private WaSobDo sobDo;
    @Resource
    private WaGroupMapper groupMapper;
    @Resource
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private WaSobService waSobService;

    @Override
    public boolean checkSobName(String belongOrgId, Integer sobId, String sobName, List<Integer> statusList) {
        List<WaSobDo> list = sobDo.getList(belongOrgId, sobId, sobName, statusList);
        return CollectionUtils.isNotEmpty(list);
    }

    @Override
    public List<WaSobDo> getWaSobIdByDateRangeAndPeriodMonth(String belongOrgId, Long dateTime, List<Integer> periodMonths, Integer groupId) {
        return sobDo.getWaSobIdByDateRangeAndPeriodMonth(belongOrgId, dateTime, periodMonths, groupId);
    }

    @Override
    public List<WaSobDo> getWaSobByEndDateAndTenantId(String tenantId, Long startDate, Long endDate) {
        return sobDo.getWaSobByEndDateAndTenantId(tenantId, startDate, endDate);
    }

    @Override
    public void autoGenerateAttendancePeriod() {
        log.info("Start to execute autoGenerateAttendancePeriod");
        SysCorpOrgExample example = new SysCorpOrgExample();
        example.createCriteria().andOrgtype2EqualTo(1).andStatusEqualTo(1);
        log.info("Start to query tenants");
        List<SysCorpOrg> tenants = sysCorpOrgMapper.selectByExample(example);
        log.info("Select tenants:{}", JSONUtils.ObjectToJson(tenants));
        if (CollectionUtils.isEmpty(tenants)) {
            log.info("tenants is []");
            return;
        }
        for (SysCorpOrg tenant : tenants) {
            Long corpId = tenant.getCorpid();
            String tenantId = String.valueOf(tenant.getOrgid());
            WaGroupExample groupExample = new WaGroupExample();
            WaGroupExample.Criteria criteria = groupExample.createCriteria();
            criteria.andBelongOrgidEqualTo(tenantId);
            List<WaGroup> waGroups = groupMapper.selectByExample(groupExample);
            if (CollectionUtils.isEmpty(waGroups)) {
                log.info("tenantId:{} waGroups is [], not auto generate", tenantId);
                continue;
            }
            //查询租户下所有的考勤周期
            List<WaSobDo> waSobs = this.getWaSobByEndDateAndTenantId(tenantId, null, null);
            log.info("Select waSobs:{}", JSONUtils.ObjectToJson(waSobs));
            if (CollectionUtils.isEmpty(waSobs)) {
                log.info("tenantId:{} waSobs is [], not auto generate", tenantId);
                continue;
            }
            // 将所有的考勤周期转换成Map
            Map<String, WaSobDo> sobMap = waSobs.stream().collect(Collectors.toMap(s -> String.format("%s_%s", s.getWaGroupId(), s.getSysPeriodMonth()), Function.identity(), (v1, v2) -> v1));
            Integer curYear = this.getYear(new Date());
            Long curDate = DateUtil.getOnlyDate();
            // 本年当前时间之前的考勤周期
            waSobs = waSobs.stream().filter(s -> curYear.equals(this.getYear(new Date(DateUtil.convertStringToDateTime(s.getSysPeriodMonth().toString(), "yyyyMM", false))))
                    && curDate >= DateUtil.getOnlyDate(new Date(s.getEndDate() * 1000L))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(waSobs)) {
                log.info("tenantId:{} waSobs2 is [], not auto generate", tenantId);
                continue;
            }
            log.info("tenantId:{} , Filtered waSobs:{}", tenantId, JSONUtils.ObjectToJson(waSobs));
            log.info("Get latest waSob");
            Map<Integer, WaSobDo> oldLatestSobMap = waSobs.stream().collect(Collectors.toMap(WaSobDo::getWaGroupId, Function.identity(), (s1, s2) -> s1.getSysPeriodMonth() > s2.getSysPeriodMonth() ? s1 : s2));
            log.info("tenantId:{} , latest waSobs, oldLatestSobMap:{}", tenantId, JSONUtils.ObjectToJson(oldLatestSobMap));
            List<Integer> groupIds = new ArrayList<>(oldLatestSobMap.keySet());
            criteria.andWaGroupIdIn(groupIds);
            // 查询所有的考勤方案
            log.info("Start to query groups");
            waGroups = groupMapper.selectByExample(groupExample);
            if (CollectionUtils.isEmpty(waGroups)) {
                log.info("tenantId:{} waGroups is [], not auto generate", tenantId);
                continue;
            }
            log.info("Select groups, tenantId:{}, waGroups:{}", tenantId, JSONUtils.ObjectToJson(waGroups));
            Map<Integer, WaGroup> groupMap = waGroups.stream().collect(Collectors.toMap(WaGroup::getWaGroupId, Function.identity(), (v1, v2) -> v1));
            Integer curMonth = this.getMonth(new Date());
            oldLatestSobMap.forEach((k, v) -> {
                WaGroup g = groupMap.get(k);
                // 自动生成考勤周期
                if (null != g && g.getAutoPeriod()) {
                    Integer sysPeriodMonth = v.getSysPeriodMonth();
                    Long periodMonthLong = DateUtil.convertStringToDateTime(sysPeriodMonth.toString(), "yyyyMM", Boolean.TRUE);
                    Integer periodMonth = this.getMonth(new Date(periodMonthLong * 1000L));
                    for (int i = 0; i <= curMonth - periodMonth; i++) {
                        try {
                            sysPeriodMonth = Integer.valueOf(DateUtil.parseDateToPattern(new Date(DateUtil.addMonth(periodMonthLong, i + 1) * 1000), "yyyyMM"));
                        } catch (ParseException e) {
                            log.error("Get sysPeriodMonth exception {}", e.getMessage(), e);
                        }
                        String sobMapKey = String.format("%s_%s", k, sysPeriodMonth);
                        if (!sobMap.containsKey(sobMapKey)) {
                            Map groupCycle = waConfigService.getWaGroupCycle(sysPeriodMonth, k);
                            Long startDate = (Long) groupCycle.get("startDate");
                            Long endDate = (Long) groupCycle.get("endDate");
                            long sysPeriodMonthLong = DateUtil.convertStringToDateTime(sysPeriodMonth.toString(), "yyyyMM", Boolean.TRUE);
                            Integer genPeriodMonth = this.getMonth(new Date(sysPeriodMonthLong * 1000L));
                            boolean generate = curDate.equals(DateUtil.getOnlyDate(new Date(v.getEndDate() * 1000L))) || endDate <= curDate
                                    || (curMonth.equals(genPeriodMonth) && startDate <= curDate)
                                    || curDate.equals(DateUtil.addDate(startDate * 1000L, -1))
                                    || (g.getCyleMonth() == 1 && startDate <= curDate);
                            if (generate) {
                                WaSob sob = new WaSob();
                                sob.setWaSobName(sysPeriodMonth + g.getWaGroupName());
                                sob.setSysPeriodMonth(sysPeriodMonth);
                                sob.setWaGroupId(k);
                                sob.setStartDate(startDate);
                                sob.setEndDate(endDate + 86399);
                                Integer cycleMonth = g.getPeriodCycleMonth();
                                Integer periodStartDate = g.getPeriodStartDate();
                                // cycleMonth:1本月，2下月
                                if (cycleMonth == 2) {
                                    try {
                                        sysPeriodMonthLong = DateUtil.addMonth(sysPeriodMonthLong, 1);
                                    } catch (ParseException e) {
                                        e.printStackTrace();
                                    }
                                }
                                Long firstDayOfMonth = DateUtil.getFirstDayOfMonth(new Date(sysPeriodMonthLong * 1000L)).getTime();
                                sob.setSobEndDate(DateUtil.addDate(firstDayOfMonth, periodStartDate - 1) + 86399);
                                waSobService.saveOrUpWaSob(sob, corpId, 0L, tenantId);
                                sobMap.put(sobMapKey, null);
                            }
                        }
                    }
                }
            });
            log.info("Auto generate sob end, tenant：{}", JSONUtils.ObjectToJson(tenant));
        }
    }

    private Integer getYear(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.YEAR);
    }

    private Integer getMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.MONTH) + 1;
    }

    private Integer getDay(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.DAY_OF_MONTH);
    }
}
