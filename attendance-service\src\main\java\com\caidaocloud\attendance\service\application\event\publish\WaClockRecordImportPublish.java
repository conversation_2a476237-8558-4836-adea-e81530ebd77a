package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WaClockRecordImportPublish {
    @Resource
    private MqMessageProducer<RabbitBaseMessage> producer;

    private final static String EXCHANGE = "attendance.importclock.fac.direct.exchange";

    private final static String ROUTING_KEY = "routingKey.importclock";

    public void publish(String msg) {
        RabbitBaseMessage message = new RabbitBaseMessage();
        message.setBody(msg);
        message.setExchange(EXCHANGE);
        message.setRoutingKey(ROUTING_KEY);
        producer.publish(message);
    }
}
