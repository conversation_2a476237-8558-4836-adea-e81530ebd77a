package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.BaseConst;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.SpringUtils;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.WfConstant;
import com.caidaocloud.attendance.service.application.dto.clock.ClockListShiftDefDto;
import com.caidaocloud.attendance.service.application.enums.ClockResultEnum;
import com.caidaocloud.attendance.service.application.enums.ClockTypeEnum;
import com.caidaocloud.attendance.service.application.enums.ClockWayEnum;
import com.caidaocloud.attendance.service.application.service.IRegisterRecordBdkService;
import com.caidaocloud.attendance.service.application.service.IShiftService;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordBdkDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordBdkDto;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class RegisterRecordBdkServiceImpl implements IRegisterRecordBdkService {

    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaRegisterRecordBdkDo registerRecordBdkDo;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private IShiftService shiftService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Override
    public AttendancePageResult<RegisterRecordBdkDto> getRegisterRecordPageList(RegisterRecordRequestDto requestDto) {
        UserInfo userInfo = this.getUserInfo();
        return SpringUtils.getBean(RegisterRecordBdkServiceImpl.class).getRegisterRecordPageList(requestDto, userInfo);
    }

    @Override
    @Transactional(readOnly = true)
    @CDText(exp = {"empStyle:empStyleName" + TextAspect.DICT_E, "empStatus:empStatusName" + TextAspect.STATUS_ENUM}, classType = RegisterRecordBdkDto.class)
    public AttendancePageResult<RegisterRecordBdkDto> getRegisterRecordPageList(RegisterRecordRequestDto requestDto, UserInfo user) {
        AttendancePageResult<RegisterRecordBdkDto> result = new AttendancePageResult<>();
        UserInfo userInfo = user != null ? user : this.getUserInfo();
        requestDto.setBelongOrgId(userInfo.getTenantId());
        requestDto.setCorpId(ConvertHelper.longConvert(userInfo.getTenantId()));
        requestDto.setTypes(requestDto.getTypes());
        AttendancePageResult<WaRegisterRecordBdkDo> pageResult = registerRecordBdkDo.getRegisterPageList(requestDto);
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            List<WaRegisterRecordBdkDo> items = pageResult.getItems();
            List<RegisterRecordBdkDto> dtoList = ObjectConverter.convertList(items, RegisterRecordBdkDto.class);
            Map<Integer, WaShiftDef> corpShiftDefMap = waCommonService.getCorpAllShiftDef(requestDto.getBelongOrgId());
            Long nowDate = DateUtil.getOnlyDate();

            dtoList.forEach(row -> {
                row.setFuncType(WfConstant.WF_FUNC_TYPE_41);
                row.setBusinessKey(row.getRecordId() + "_" + BusinessCodeEnum.REGISTER.getCode());
                if (row.getRegisterType() != null) {
                    if (row.getRegisterType() == 1 || row.getRegisterType() == 2) {
                        row.setRegisterTypeName(ClockTypeEnum.getName(row.getRegisterType()));
                    } else if (row.getRegisterType() == 3) {
                        row.setRegisterTypeName("");
                    }
                }
                if (row.getType() != null) {
                    row.setTypeName(BaseConst.REGISTER_TYPE.get(row.getType()));
                    if (row.getType().equals(ClockWayEnum.FILLCLOCK.getIndex())) {
                        row.setBdkReason(row.getReason());
                        if (row.getApprovalStatus() != null) {
                            row.setApprovalStatusName(ApprovalStatusEnum.getName(row.getApprovalStatus()));
                        }
                    }
                }
                if (row.getResultType() != null) {
                    if (row.getResultType() == 1 || row.getResultType() == 2) {
                        row.setResultTypeName(ClockResultEnum.getName(row.getResultType()));
                    } else if (row.getResultType() == 0) {
                        row.setResultTypeName("");
                    } else {
                        row.setResultTypeName("OTHER");
                    }
                }
                if (StringUtils.isNotBlank(row.getNormalDate())) {
                    row.setNormalDate(row.getNormalDate().replace("-", "~"));
                }
                // 班次
                if (null != row.getShiftDefIds() && !row.getShiftDefIds().isEmpty()) {
                    String[] shiftDefIds = row.getShiftDefIds().split(",");
                    List<Integer> shiftDefIdList = Stream.of(shiftDefIds).map(Integer::valueOf)
                            .collect(Collectors.toCollection(java.util.ArrayList::new));
                    List<ClockListShiftDefDto> allShiftDefDtoList = new ArrayList<>();
                    for (Integer shiftDefId : shiftDefIdList) {
                        WaShiftDef shiftDef = corpShiftDefMap.get(shiftDefId);
                        if (null == shiftDef) {
                            continue;
                        }
                        List<MultiShiftSimpleVo> shiftSimpleVoList = shiftService.convertDoToSimpleVo(Lists.newArrayList(ObjectConverter.convert(shiftDef, WaShiftDo.class)));
                        List<ClockListShiftDefDto> shiftDefDtoList = ClockListShiftDefDto.getList(shiftSimpleVoList, nowDate,
                                null, null, requestDto.isIfExport());
                        allShiftDefDtoList.addAll(shiftDefDtoList);
                    }
                    row.setShiftDefList(allShiftDefDtoList);
                } else if (null != row.getShiftDefId() && null != corpShiftDefMap.get(row.getShiftDefId())) {
                    WaShiftDef shiftDef = corpShiftDefMap.get(row.getShiftDefId());
                    List<MultiShiftSimpleVo> shiftSimpleVoList = shiftService.convertDoToSimpleVo(Lists.newArrayList(ObjectConverter.convert(shiftDef, WaShiftDo.class)));
                    List<ClockListShiftDefDto> shiftDefDtoList = ClockListShiftDefDto.getList(shiftSimpleVoList, nowDate,
                            null, null, requestDto.isIfExport());
                    row.setShiftDefList(shiftDefDtoList);
                }
            });
            result.setItems(dtoList);
            result.setPageNo(pageResult.getPageNo());
            result.setPageSize(pageResult.getPageSize());
            result.setTotal(pageResult.getTotal());
        }
        return result;
    }


}
