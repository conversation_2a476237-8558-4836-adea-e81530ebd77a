package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWaEmpLeaveTimeRepository;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 休假详情
 */
@Slf4j
@Data
@Service
public class WaEmpLeaveTimeDo {
    private Integer leaveTimeId;

    private Integer leaveId;

    private Long startTime;

    private Long endTime;

    private String shalfDay;

    private String ehalfDay;

    private Short periodType;

    private Float timeDuration;

    private Integer timeUnit;

    private Long shiftStartTime;

    private Long shiftEndTime;

    private Long crttime;

    private Long crtuser;

    private Long updtime;

    private Long upduser;

    private Float cancelTimeDuration;

    /**
     * 休假开始日期使用的班次（格式为json数组字符串:[112,113]）
     */
    private String startShift;

    /**
     * 休假结束日期使用的班次（格式为json数组字符串:[112,113]）
     */
    private String endShift;

    @Autowired
    private IWaEmpLeaveTimeRepository waEmpLeaveTimeRepository;

    public WaEmpLeaveTimeDo getById(Integer leaveId) {
        return ObjectConverter.convert(waEmpLeaveTimeRepository.getByLeaveId(leaveId), WaEmpLeaveTimeDo.class);
    }

    public List<WaEmpLeaveTimeDo> getListByIds(List<Integer> leaveIds) {
        return ObjectConverter.convertList(waEmpLeaveTimeRepository.selectListByIds(leaveIds), WaEmpLeaveTimeDo.class);
    }

    public void update(WaEmpLeaveTimeDo leaveTimeDo) {
        waEmpLeaveTimeRepository.update(leaveTimeDo);
    }
}
