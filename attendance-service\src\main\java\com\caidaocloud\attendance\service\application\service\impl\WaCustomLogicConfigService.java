package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidaocloud.attendance.service.application.enums.CustomLogicTypeEnum;
import com.caidaocloud.attendance.service.domain.entity.WaCustomLogicConfigDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 考勤自定义逻辑配置服务层
 *
 * <AUTHOR>
 * @Date 2024/7/26
 */
@Slf4j
@Service
public class WaCustomLogicConfigService {
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private WaCustomLogicConfigDo waCustomLogicConfigDo;

    public Object doExecute(WaCustomLogicConfigDo waCustomLogicConfig, Map<String, Object> scriptParams) {
        if (StringUtils.isBlank(waCustomLogicConfig.getLogicType())
                || StringUtils.isBlank(waCustomLogicConfig.getLogicExp()) || waCustomLogicConfig.getStatus() != 1) {
            return null;
        }
        String logicType = waCustomLogicConfig.getLogicType();
        CustomLogicTypeEnum logicTypeEnum = CustomLogicTypeEnum.getByCode(logicType);
        if (null == logicTypeEnum) {
            return null;
        }
        switch (logicTypeEnum) {
            case SCRIPT:
                return groovyScriptEngine.executeObject(waCustomLogicConfig.getLogicExp(), scriptParams);
            case SQL:
                break;
            default:
                break;
        }
        return null;
    }


}
