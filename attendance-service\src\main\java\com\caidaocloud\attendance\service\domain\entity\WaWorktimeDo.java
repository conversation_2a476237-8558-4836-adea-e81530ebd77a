package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.wa.dto.EmpCalendarInfoDto;
import com.caidao1.wa.mybatis.model.WaWorktime;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.repository.IWorktimeRepository;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpRelCalendarQueryDto;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftPo;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 工作日历DO
 *
 * <AUTHOR>
 * @Date 2021/3/1
 */
@Slf4j
@Data
@Service
public class WaWorktimeDo {
    private Integer workCalendarId;
    private Integer calendarGroupId;
    private Integer workRoundId;
    private String workCalendarName;
    private Long corpid;
    private String belongOrgid;
    private Long crtuser;
    private Long crttime;
    private Boolean isDefault;
    private Boolean isStoreUse;
    private Short deleted;
    private Long updateUser;
    private Long updateTime;
    /**
     * 考勤类型：
     * 1 固定班次 (固定时间上下班: 按照相同的时间打卡，适用于办公室坐班，例如朝九晚五),
     * 2 排班制 (按排班上下班: 自定义设置每人每天的班次，适用于工厂、门店等，例如三班倒),
     * 3 自由打卡 (不固定时间上下班: 上下班时间不固定，可随时打卡，适用于销售以及装修、家政等计时工)
     */
    private Integer worktimeType;

    //冗余字段
    @ApiModelProperty("开始日期")
    private Long startdate;

    @ApiModelProperty("结束日期")
    private Long enddate;
    private String i18nWorkCalendarName;
    private String groupExpCondition;

    @Autowired
    private IWorktimeRepository worktimeRepository;

    public List<WaWorktimeDo> getWorktimeListByWorkRoundId(String belongOrgId, Integer workRoundId) {
        return worktimeRepository.getWorktimeListByWorkRoundId(belongOrgId, workRoundId);
    }

    public int save(WaWorktimeDo worktimeDo) {
        return worktimeRepository.save(worktimeDo);
    }

    public int updateById(WaWorktimeDo worktimeDo) {
        return worktimeRepository.updateById(worktimeDo);
    }

    public WaWorktimeDo selectById(String belongOrgid, Integer id) {
        return worktimeRepository.selectById(belongOrgid, id);
    }

    public int deleteById(Integer id) {
        return worktimeRepository.deleteById(id);
    }

    public WaWorktimeDo getDefaultCalendar(String belongOrgid, Integer excludeWorkCalendarId) {
        return worktimeRepository.getDefaultCalendar(belongOrgid, excludeWorkCalendarId);
    }

    public List<Map> getWorkCalendarDetailList(Integer workCalendarId, String start, String end) {
        return worktimeRepository.getWorkCalendarDetailList(workCalendarId, start, end);
    }

    public List<Map> getEmpCalendarList(String belongOrgId, Long startDate, Long endDate, Long empId) {
        return worktimeRepository.getEmpCalendarList(belongOrgId, startDate, endDate, empId);
    }

    public List<WaWorktimeDo> getWorktimeListByCalendarGroupId(String belongOrgId, Integer calendarGroupId) {
        return worktimeRepository.getWorktimeListByCalendarGroupId(belongOrgId, calendarGroupId);
    }

    public int deleteDetailById(Integer id) {
        return worktimeRepository.deleteDetailById(id);
    }

    public int deleteWorkTimeGroupById(Integer id) {
        return worktimeRepository.deleteWorkTimeGroupById(id);
    }

    public int getCalendarGroupCountByName(String belongOrgId, Integer excludeCalendarGroupId, String groupName) {
        return worktimeRepository.getCalendarGroupCountByName(belongOrgId, excludeCalendarGroupId, groupName);
    }

    public int getWorkTimeCountByName(String belongOrgId, Integer id, String calendarName) {
        return worktimeRepository.getWorkTimeCountByName(belongOrgId, id, calendarName);
    }

    public List<Map> getEmpShiftChangeListByEmpId(String belongOrgId, Long empId, Long startDate, Long endDate) {
        return worktimeRepository.getEmpShiftChangeListByEmpId(belongOrgId, empId, startDate, endDate);
    }

    public PageList<Map> getEmpShiftChangePageList(MyPageBounds pageBounds, Map params) {
        return worktimeRepository.getEmpShiftChangePageList(pageBounds, params);
    }

    public List<Map> getEmpCalendarShiftList(String belongOrgId, Long startDate, Long endDate, Long empId, String dataScope) {
        return worktimeRepository.getEmpCalendarShiftList(belongOrgId, startDate, endDate, empId, dataScope);
    }

    public List<Map> getChangeShiftDefListByEmpId(String belongOrgId, Long empId, Long startDate, Long endDate) {
        return worktimeRepository.getChangeShiftDefListByEmpId(belongOrgId, empId, startDate, endDate);
    }

    public List<WaWorktimeDo> getWorkCalendar(String belongOrgId) {
        List<WaWorktime> list = worktimeRepository.getWorkCalendar(belongOrgId);
        return ObjectConverter.convertList(list, WaWorktimeDo.class);
    }

    public List<EmpCalendarInfoDto> getEmpRelCalendarList(ListEmpRelCalendarQueryDto queryDto) {
        return worktimeRepository.selectEmpCalendarList(queryDto);
    }

    public List<WaShiftDo> getEmpCalendarShiftList(String belongOrgId, Long startDate, Long endDate, List<Long> empIds) {
        List<WaShiftPo> waShiftPos = worktimeRepository.selectEmpCalendarShiftList(belongOrgId, startDate, endDate, empIds);
        if (CollectionUtils.isEmpty(waShiftPos)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(waShiftPos, WaShiftDo.class);
    }

    public AttendancePageResult<WaShiftDo> getWaShiftList(AttendanceBasePage basePage, String belongOrgId, Long startDate, Long endDate) {
        AttendancePageResult<WaShiftPo> poList = worktimeRepository.selectWaShiftList(basePage, belongOrgId, startDate, endDate);
        List<WaShiftDo> items;
        if (CollectionUtils.isNotEmpty(poList.getItems())) {
            items = ObjectConverter.convertList(poList.getItems(), WaShiftDo.class);
        } else {
            items = new ArrayList<>();
        }
        return new AttendancePageResult<>(items, poList.getPageNo(), poList.getPageSize(), poList.getTotal());
    }
}
