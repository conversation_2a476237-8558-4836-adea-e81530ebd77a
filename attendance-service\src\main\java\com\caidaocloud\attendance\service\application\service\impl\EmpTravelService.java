package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.enums.LeaveStatusEnum;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JacksonJsonUtil;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.model.SysUnitCity;
import com.caidao1.wa.mybatis.mapper.WaCheckMapper;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidao1.wa.mybatis.model.WaWorktimeDetail;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.dto.ShiftRestPeriods;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.event.publish.AttendanceWorkflowMsgPublish;
import com.caidaocloud.attendance.service.application.event.publish.MessageParams;
import com.caidaocloud.attendance.service.application.service.IConfigService;
import com.caidaocloud.attendance.service.application.service.IEmpTravelService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.application.service.user.EmployeeGroupService;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelSaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.RevokeEmpTraveDto;
import com.caidaocloud.attendance.service.interfaces.dto.user.MyTravelTimeDto;
import com.caidaocloud.attendance.core.wa.dto.shift.MyWorkDateShiftDto;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.DeleteStatusEnum;
import com.caidaocloud.em.SysConfigsEnum;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 出差申请
 *
 * <AUTHOR>
 * @Date 2021/9/8
 */
@Slf4j
@Service
public class EmpTravelService implements IEmpTravelService {

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    @Autowired
    private WaEmpTravelDo waEmpTravelDo;
    @Autowired
    private WaEmpTravelDaytimeDo waEmpTravelDaytimeDo;
    @Autowired
    private WaTravelTypeDo waTravelTypeDo;
    @Autowired
    private WaSobService waSobService;
    @Resource
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private ISessionService sessionService;
    @Resource
    private WaCheckMapper waCheckMapper;
    @Autowired
    @Lazy
    private IWfService wfService;
    @Autowired
    private AttendanceWorkflowMsgPublish attendanceWorkflowMsgPublish;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private IConfigService configService;
    @Autowired
    private WorkflowCallBackService workflowCallBackService;
    @Autowired
    private WaWorkflowRevokeDo workflowRevokeDo;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Resource
    private EmployeeGroupService employeeGroupService;
    @Resource
    private SysEmpInfoDo sysEmpInfoDo;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @CDText(exp = {"travelMode:travelModeName" + TextAspect.DICT_TRAVEL_MODE}, classType = EmpTravelDto.class)
    @Override
    public PageResult<EmpTravelDto> getEmpTravelPageList(EmpTravelReqDto dto, UserInfo userInfo) {
        dto.setBelongOrgId(userInfo.getTenantId());
        Map params = new HashMap<>();
        AttendanceBasePage basePage = ObjectConverter.convert(dto, AttendanceBasePage.class);
        PageBean pageBean = PageUtil.getPageBean(dto);
        changeParam(pageBean, dto, params);
        params.put("datafilter", dto.getDataScope());
        PageResult<WaEmpTravelDo> pageResult = waEmpTravelDo.getWaEmpTravelList(basePage, params);
        return convert(pageResult, dto.isIfBatch());
    }

    @CDText(exp = {"travelMode:travelModeName" + TextAspect.DICT_TRAVEL_MODE}, classType = EmpTravelDto.class)
    @Override
    public PageResult<EmpTravelDto> getEmpTravelPageListOfPortal(QueryPageBean queryPageBean, Boolean ifBatch) {
        PageResult<WaEmpTravelDo> pageResult = waEmpTravelDo.getWaEmpTravelListOfPortal(queryPageBean, ifBatch);
        return convert(pageResult, ifBatch);
    }

    private PageResult<EmpTravelDto> convert(PageResult<WaEmpTravelDo> pageResult, Boolean ifBatch) {
        PageResult<EmpTravelDto> dtoPageResult = new PageResult<>();
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaEmpTravelDo> doList = pageResult.getItems();
            List<EmpTravelDto> dtoList = ObjectConverter.convertList(doList, EmpTravelDto.class);
            for (EmpTravelDto item : dtoList) {
                Short timeUnit = item.getTimeUnit();
                Float duration = item.getTimeDuration();
                item.setTimeUnitName(TravelTypeUnitEnum.getName(timeUnit));
                if (TravelTypeUnitEnum.HOUR.getIndex().equals(Integer.valueOf(timeUnit))) {
                    BigDecimal v = new BigDecimal(String.valueOf(duration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    item.setTimeDuration(v.floatValue());
                }
                Integer status = item.getStatus();
                if (status != null) {
                    item.setStatusName(ApprovalStatusEnum.getName(status));
                }
                item.setBusinessKey(String.valueOf(item.getTravelId()));
                item.setFunType(ifBatch ? FuncTypeEnum.BATCH_TRAVEL.getIndex() : BaseConst.WF_FUNC_TYPE_45);
                Integer revokeStatus = item.getRevokeStatus();
                String businessCode = ifBatch ? BusinessCodeEnum.BATCH_TRAVEL.getCode() : BusinessCodeEnum.TRAVEL.getCode();
                if (revokeStatus != null && (revokeStatus == 1 || revokeStatus == 2)) {
                    item.setBusinessKey(item.getTravelId() + "_" + businessCode);
                } else {
                    item.setBusinessKey(item.getTravelId() + "_" + businessCode);
                }
                Integer periodType = item.getPeriodType() != null ? Integer.valueOf(item.getPeriodType()) : null;
                Long shiftStartTime = item.getShiftStartTime();
                Long shiftEndTime = item.getShiftEndTime();
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    item.setStartDate(DateUtil.getDateStrByTimesamp(shiftStartTime));
                    item.setEndDate(DateUtil.getDateStrByTimesamp(shiftEndTime));
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    item.setStartDate(String.format("%s%s", DateUtil.getDateStrByTimesamp(item.getStartTime()), DayHalfTypeEnum.getDesc(item.getShalfDay())));
                    item.setEndDate(String.format("%s%s", DateUtil.getDateStrByTimesamp(item.getEndTime()), DayHalfTypeEnum.getDesc(item.getEhalfDay())));
                } else {
                    item.setStartDate(DateUtil.getTimeStrByTimesamp(shiftStartTime));
                    item.setEndDate(DateUtil.getTimeStrByTimesamp(shiftEndTime));
                }
                //地点
                SysUnitCity sysUnitCity = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, item.getProvince());
                SysUnitCity cityObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_CITY_KEY, item.getCity());
                if (sysUnitCity != null) {
                    item.setProvinceName(sysUnitCity.getChnName());
                }
                if (cityObj != null) {
                    item.setCityName(cityObj.getChnName());
                }
                item.setTravelType(LangParseUtil.getI18nLanguage(item.getI18nTravelTypeName(), item.getTravelType()));
            }
            dtoPageResult.setItems(dtoList);
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setTotal(pageResult.getTotal());
        }
        return dtoPageResult;
    }

    @Override
    public Result<Boolean> preCheckBeforeRevoke(WaEmpTravelDo travelDo, WaTravelTypeDo travelType) {
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(travelDo.getStatus())) {
            return ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE);
        }
        // 只允许撤销审批中和审批通过的单据
        Boolean revokePassed = Optional.ofNullable(travelType.getRevokePassed()).orElse(false);
        if (!revokePassed) {
            if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(travelDo.getStatus()) && !ApprovalStatusEnum.PASSED.getIndex().equals(travelDo.getStatus())) {
                // 审批中/审批通过的可撤销
                return ResponseWrap.wrapResult(AttendanceCodes.RAVEL_REVOKE_NOT_ALLOW, Boolean.FALSE);
            }
        } else {
            if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(travelDo.getStatus())) {
                // 审批中的可撤销
                return ResponseWrap.wrapResult(AttendanceCodes.RAVEL_REVOKE_NOT_ALLOW, Boolean.FALSE);
            }
        }
        //超过考勤截止日不允许撤销
        Long onlyDate = DateUtil.getOnlyDate();
        Long travelDate = travelDo.getStartTime();
        WaSob waSob = waSobService.getWaSob(travelDo.getEmpId(), travelDate);
        if (waSob != null) {
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            if (onlyDate > sobEndDate) {
                Integer sysPeriodMonth = waSob.getSysPeriodMonth();
                //throw new CDException("申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员。");
                throw new CDException(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
            }
        }
        return Result.ok(Boolean.TRUE);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> revokeEmpTravel(RevokeEmpTraveDto dto, UserInfo userInfo) {
        WaEmpTravelDo travelDo = waEmpTravelDo.getWaEmpTravelByPrimaryKey(dto.getTravelId());
        if (travelDo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TIME_NOT_EXIST, Boolean.FALSE);
        }
        LogRecordContext.putVariable("empId", travelDo.getEmpId());
        WaTravelTypeDo travelType = waTravelTypeDo.selectOneById(userInfo.getTenantId(), travelDo.getTravelTypeId());
        if (travelType == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        // 检查是否符合撤销要求
        Result<Boolean> checkResult = preCheckBeforeRevoke(travelDo, travelType);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        Long travelDate = travelDo.getStartTime();
        //如果开启撤销工作流流程，则需要发起撤销流程
        if (travelType.getRevokeWorkflow() != null && travelType.getRevokeWorkflow()) {
            String revokeAllowStatus = Optional.ofNullable(travelType.getRevokeAllowStatus()).orElse("");
            String[] revokeAllowStatusArr = revokeAllowStatus.split(",");
            List<Integer> revokeAllowStatusList = Arrays.stream(revokeAllowStatusArr).map(Integer::valueOf).collect(Collectors.toList());
            if (revokeAllowStatusList.contains(travelDo.getStatus())) {
                if (CollectionUtils.isNotEmpty(waEmpTravelDaytimeDo.getEmpTravelDaytimeList(travelDo.getTenantId(), Collections.singletonList(travelDo.getTravelId())))) {
                    //存在出差转调休，则不可进行撤销/废止
                    return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TO_COMPENSATORY_NOT_ALLOWED_REVOKE, Boolean.FALSE);
                }
                //根据出差单审批状态判断是走撤销还是废止流程
                BusinessCodeEnum workflowEnum = BusinessCodeEnum.TRAVEL_REVOKE;
                if (ApprovalStatusEnum.PASSED.getIndex().equals(travelDo.getStatus())) {
                    workflowEnum = BusinessCodeEnum.TRAVEL_ABOLISH;
                }
                if (CollectionUtils.isNotEmpty(workflowRevokeDo.getWorkflowRevokeList(travelDo.getTenantId(), travelDo.getTravelId(),
                        Collections.singletonList(ApprovalStatusEnum.IN_APPROVAL.getIndex()), Collections.singletonList(workflowEnum.name())))) {
                    //校验是否有审批中的撤销流程，已有审批中的撤销流程不可进行撤销
                    return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_REVOKING_NOT_ALLOWED_REVOKE, Boolean.FALSE);
                }
                //检查流程是否已启用
                Result<?> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(workflowEnum.getCode());
                if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
                    return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, Boolean.FALSE);
                }
                Boolean workflowEnabledResultData = (Boolean) checkWorkflowEnableResult.getData();
                if (!workflowEnabledResultData) {
                    return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, Boolean.FALSE);
                }
                //保存撤销流程
                WaWorkflowRevokeDo workflowRevoke = getWorkflowRevoke(userInfo, dto, workflowEnum);
                DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                // 发起新事务
                def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                TransactionStatus transactionStatus = platformTransactionManager.getTransaction(def);
                try {
                    //保存撤销流程
                    workflowRevokeDo.save(workflowRevoke);
                    // 提交新开事务
                    platformTransactionManager.commit(transactionStatus);
                } catch (Exception e) {
                    // 回滚新开事务
                    platformTransactionManager.rollback(transactionStatus);
                    log.error("申请出差撤销/废止失败:{}", e.getMessage(), e);
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.REVOKE_OR_ABOLISH_FAILED, null).getMsg());
                }
                String msg = "";
                try {
                    //发起工作流
                    WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
                    wfBeginWorkflowDto.setFuncCode(workflowEnum.getCode());
                    wfBeginWorkflowDto.setBusinessId(workflowRevoke.getId().toString());
                    SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(travelDo.getEmpId());
                    if (null == empInfo) {
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, null).getMsg());
                    }
                    wfBeginWorkflowDto.setApplicantId(empInfo.getEmpid().toString());
                    wfBeginWorkflowDto.setApplicantName(empInfo.getEmpName());
                    wfBeginWorkflowDto.setEventTime(DateUtil.getOnlyDate(new Date(travelDate * 1000)) * 1000);
                    wfBeginWorkflowDto.setTimeSlot(getTimeSlot(travelDo));
                    Result<?> result = wfRegisterFeign.begin(wfBeginWorkflowDto);
                    if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE).getMsg());
                    }
                } catch (Exception e) {
                    log.error("Employee travel revoke or abolish apply has exception:{}", e.getMessage(), e);
                    workflowRevokeDo.delete(workflowRevoke);
                    if (e instanceof CDException) {
                        if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                            msg = ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_CONFIG_ERR, null).getMsg();
                        } else {
                            msg = e.getMessage();
                        }
                    } else {
                        msg = ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, null).getMsg();
                    }
                }
                if (StringUtils.isNotBlank(msg)) {
                    return Result.fail(msg);
                }
                int code = workflowEnum.equals(BusinessCodeEnum.TRAVEL_ABOLISH) ? AttendanceCodes.WORKFLOW_ABOLISH_SUBMIT_SUCCESS : AttendanceCodes.WORKFLOW_REVOKE_SUBMIT_SUCCESS;
                return Result.status(true, 0, ResponseWrap.wrapResult(code, Boolean.TRUE).getMsg());
            }
        }
        Result<Boolean> result = revokeTravelWorkflow(userInfo.getTenantId(), userInfo.getUserId(), travelDo, travelType, dto.getRecokeReason(), ApprovalStatusEnum.REVOKED);

        return result;
    }

    public WaWorkflowRevokeDo getWorkflowRevoke(UserInfo userInfo, RevokeEmpTraveDto dto, BusinessCodeEnum workflowEnum) {
        WaWorkflowRevokeDo workflowRevoke = new WaWorkflowRevokeDo();
        workflowRevoke.setId(snowflakeUtil.createId());
        workflowRevoke.setTenantId(userInfo.getTenantId());
        workflowRevoke.setEntityId(dto.getTravelId());
        workflowRevoke.setModuleName(workflowEnum.name());
        workflowRevoke.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        workflowRevoke.setReason(dto.getRecokeReason());
        workflowRevoke.setDeleted(DeleteStatusEnum.UN_DELETED.getIndex());
        workflowRevoke.setCreateBy(userInfo.getUserId());
        workflowRevoke.setCreateTime(System.currentTimeMillis() / 1000);
        return workflowRevoke;
    }

    @Override
    public Result<Boolean> revokeTravelWorkflow(String tenantId, Long userId, WaEmpTravelDo empTravel, WaTravelTypeDo travelType, String revokeReason, ApprovalStatusEnum approvalStatus) {
        if (CollectionUtils.isNotEmpty(waEmpTravelDaytimeDo.getEmpTravelDaytimeList(empTravel.getTenantId(), Collections.singletonList(empTravel.getTravelId())))) {
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TO_COMPENSATORY_NOT_ALLOWED_REVOKE, Boolean.FALSE);
        }
        WfRevokeDto revokeDto = new WfRevokeDto();
        String businessKey = String.format("%s_%s", empTravel.getTravelId(), BusinessCodeEnum.TRAVEL.getCode());
        revokeDto.setBusinessKey(businessKey);
        Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
        if (null == result || !result.isSuccess()) {
            return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE);
        }
        // 更新单据信息
        empTravel.setStatus(approvalStatus.getIndex());
        empTravel.setRevokeReason(revokeReason);
        empTravel.setUpdateBy(userId);
        empTravel.setUpdateTime(DateUtil.getCurrentTime(true));
        waEmpTravelDo.update(empTravel);
        // todo 以下代码不清楚为啥这么写，感觉是有问题
        // 发送消息
        empTravel.setStatus(approvalStatus.getIndex());
        if (empTravel.getStatus().intValue() == LeaveStatusEnum.LEAVE_STATUS_2.value) {
            this.sendMsg(ConvertHelper.longConvert(tenantId), empTravel, travelType);
        }
        return Result.ok(Boolean.TRUE);
    }

    private void sendMsg(Long corpId, WaEmpTravelDo travelDo, WaTravelTypeDo travelType) {
        List<Long> receiver = new ArrayList<>();
        receiver.add(travelDo.getEmpId());
        MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                .type("msg")
                .subType("revoke")
                .title("出差审批结果通知")
                .funcType("45")
                .handlers(receiver)
                .customForms(getFormCustoms(ConvertHelper.longConvert(corpId), travelDo.getTravelId(), travelType.getTravelTypeName()));
        MessageParams messageParams = builder.build();
        attendanceWorkflowMsgPublish.publish(JSON.toJSONString(messageParams), corpId);
    }

    private List<KeyValue> getFormCustoms(Long corpId, Long travelId, String travelTypeName) {
        WaEmpTravelDo travelDo = waEmpTravelDo.getWaEmpTravelDetailById(travelId, corpId);
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(travelDo.getEmpId());
        List<KeyValue> list = new ArrayList<>();
        list.add(new KeyValue("申请人", empInfo.getWorkno().concat("(").concat(empInfo.getEmpName().concat(")"))));
        list.add(new KeyValue("员工类型", travelDo.getEmployType()));
        list.add(new KeyValue("出差类型", travelTypeName));
        list.add(new KeyValue("开始时间", DateUtil.getTimeStrByTimesamp4(travelDo.getStartTime())));
        list.add(new KeyValue("结束时间", DateUtil.getTimeStrByTimesamp4(travelDo.getEndTime())));
        if (travelDo.getTimeUnit() == 1) {
            list.add(new KeyValue("申请时长", travelDo.getTimeDuration() + "天"));
        } else {
            list.add(new KeyValue("申请时长", BigDecimal.valueOf(travelDo.getTimeDuration()).divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + "小时"));
        }
        list.add(new KeyValue("申请时间", DateUtil.getDateStrByTimesamp(travelDo.getCreateTime())));
        SysUnitCity proviceObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, travelDo.getProvince());
        SysUnitCity cityObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_CITY_KEY, travelDo.getCity());
        String province = "";
        String city = "";
        if (proviceObj != null) {
            province = proviceObj.getChnName();
        }
        if (cityObj != null) {
            city = cityObj.getChnName();
        }
        String location = province + "/" + city;
        list.add(new KeyValue("出行地点", location));
        list.add(new KeyValue("出行方式", travelDo.getTravelMode()));
        return list;
    }

    /**
     * 小时(过期，请使用新的逻辑方法：calLeaveTimeByPeriod3New)
     *
     * @param travelTypeDo
     * @param startDate
     * @param endDate
     * @param startTimeStr
     * @param endTimeStr
     * @param pbMap
     * @param dayTime
     * @param shiftMap
     * @throws Exception
     */
    @Deprecated
    public void calLeaveTimeByPeriod3(WaTravelTypeDo travelTypeDo, long startDate, long endDate,
                                      String startTimeStr, String endTimeStr, Map<Long, WaWorktimeDetail> pbMap,
                                      WaEmpTravelDaytimeDo dayTime, Map<Integer, WaShiftDef> shiftMap) throws Exception {
        //小时非整天
        long minDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long maxDate = DateUtil.getTimesampByDateStr2(endTimeStr);
        long firstTime = DateUtil.getTimesampByStr2(startTimeStr);
        long lastTime = DateUtil.getTimesampByStr2(endTimeStr);
        boolean includeNonWorkday = travelTypeDo.getIfIncludeNonWorkday() != null && travelTypeDo.getIfIncludeNonWorkday() == 1;

        WaWorktimeDetail detail = pbMap.get(startDate);//取得当天排班
        WaShiftDef shiftDef = shiftMap.get(detail.getShiftDefId());
        WaShiftDef substituteShiftDef = null;// 非工作日的替代班次
        if (null != shiftDef.getSubstituteShift() && shiftMap.containsKey(shiftDef.getSubstituteShift())) {
            substituteShiftDef = shiftMap.get(shiftDef.getSubstituteShift());
        }
        Integer dateType = detail.getDateType();
        if (dateType != 1 && includeNonWorkday && null != substituteShiftDef) {
            shiftDef = substituteShiftDef;
            dateType = substituteShiftDef.getDateType();
        }

        long workStime = startDate + shiftDef.getStartTime() * 60;
        long workEndDate = startDate;
        boolean isky = false;
        if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {
            workEndDate += 86400;
            isky = true;
        }
        long workEtime = workEndDate + shiftDef.getEndTime() * 60;

        if (startDate == endDate && minDate == maxDate) {//请一天，第一天也是最后一天
            Integer tmpS = getLeaveDateHour(firstTime);
            Integer tmpE = getLeaveDateHour(lastTime);

            if (tmpE < shiftDef.getStartTime() || dateType != 1) {//结束时间小于当天上班开始时间 或者 当天非工作日
                Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
                WaWorktimeDetail preEndDateWorkTimeDetail = pbMap.get(preEndDate);

                // 前一天班次信息
                WaShiftDef preShiftDef = null;
                Integer preDateType = null;
                if (null != preEndDateWorkTimeDetail) {
                    preShiftDef = shiftMap.get(preEndDateWorkTimeDetail.getShiftDefId());
                    // 非工作日的替代班次
                    WaShiftDef preSubstituteShiftDef = null;
                    if (null != preShiftDef.getSubstituteShift() && shiftMap.containsKey(preShiftDef.getSubstituteShift())) {
                        preSubstituteShiftDef = shiftMap.get(preShiftDef.getSubstituteShift());
                    }
                    preDateType = preEndDateWorkTimeDetail.getDateType();
                    if (preDateType != 1 && includeNonWorkday && null != preSubstituteShiftDef) {
                        preShiftDef = preSubstituteShiftDef;
                        preDateType = preSubstituteShiftDef.getDateType();
                    }
                }

                if (preShiftDef != null && preDateType == 1 &&
                        CdWaShiftUtil.checkCrossNight(preShiftDef.getStartTime(), preShiftDef.getEndTime(), preDateType)
                        && tmpS < preShiftDef.getEndTime()) {
                    // 前一天班次为工作日且班次跨夜
                    long travelStartTime = firstTime;
                    long travekEndTime = lastTime;
                    if (tmpE > preShiftDef.getEndTime()) {
                        tmpE = preShiftDef.getEndTime();
                        travekEndTime = maxDate + tmpE * 60;
                    }
                    if (preShiftDef.getIsNoonRest() != null && preShiftDef.getIsNoonRest()) {
                        Integer timeDuration = deductLeaveDayNooRest(preEndDateWorkTimeDetail, preShiftDef, travelStartTime, travekEndTime);
                        dayTime.setTimeDuration(new Float(timeDuration));
                    } else {
                        dayTime.setTimeDuration((float) (tmpE - tmpS));
                    }
                } else {
                    if (includeNonWorkday && dateType != 1) {
                        int timeDuration = tmpE - tmpS;
                        dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                    } else {
                        dayTime.setTimeDuration(0f);
                    }
                }
            } else {
                long stime = Math.max(workStime, firstTime);
                long etime = Math.min(workEtime, lastTime);

                tmpS = getLeaveDateHour(stime);
                tmpE = getLeaveDateHour(etime);

                if (shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) {
                    Integer timeDuration = deductLeaveDayNooRest(detail, shiftDef, stime, etime);
                    dayTime.setTimeDuration(new Float(timeDuration));
                } else {
                    dayTime.setTimeDuration((float) (tmpE - tmpS));
                }
            }
            dayTime.setStartTime(tmpS);
            dayTime.setEndTime(tmpE);
        } else {//请多天假
            if (startDate == endDate) {//最后一天
                Integer tmpS = getLeaveDateHour(firstTime);
                Integer tmpE = getLeaveDateHour(lastTime);

                if (tmpE < shiftDef.getStartTime() || dateType != 1) {//请假结束时间小于当天上班开始时间 或者 当天非工作日
                    Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
                    WaWorktimeDetail preEndDateWorkTimeDetail = pbMap.get(preEndDate);

                    // 前一天班次信息
                    WaShiftDef preShiftDef = null;
                    Integer preDateType = null;
                    if (null != preEndDateWorkTimeDetail) {
                        preShiftDef = shiftMap.get(preEndDateWorkTimeDetail.getShiftDefId());
                        // 非工作日的替代班次
                        WaShiftDef preSubstituteShiftDef = null;
                        if (null != preShiftDef.getSubstituteShift() && shiftMap.containsKey(preShiftDef.getSubstituteShift())) {
                            preSubstituteShiftDef = shiftMap.get(preShiftDef.getSubstituteShift());
                        }
                        preDateType = preEndDateWorkTimeDetail.getDateType();
                        if (preDateType != 1 && includeNonWorkday && null != preSubstituteShiftDef) {
                            preShiftDef = preSubstituteShiftDef;
                            preDateType = preSubstituteShiftDef.getDateType();
                        }
                    }

                    if (preShiftDef != null && preDateType == 1
                            && CdWaShiftUtil.checkCrossNight(preShiftDef.getStartTime(), preShiftDef.getEndTime(), preDateType)) {
                        //前一天班次为跨夜班
                        tmpS = 0;
                        long travelStartTime = maxDate;
                        long travekEndTime = lastTime;
                        if (tmpE > preShiftDef.getEndTime()) {
                            tmpE = preShiftDef.getEndTime();
                            travekEndTime = maxDate + tmpE * 60;
                        }
                        if (preShiftDef.getIsNoonRest() != null && preShiftDef.getIsNoonRest()) {
                            Integer timeDuration = deductLeaveDayNooRest(preEndDateWorkTimeDetail, preShiftDef, travelStartTime, travekEndTime);
                            dayTime.setTimeDuration(new Float(timeDuration));
                        } else {
                            dayTime.setTimeDuration((float) (tmpE - tmpS));
                        }
                    } else {
                        tmpS = 0;
                        if (includeNonWorkday && dateType != 1) {
                            int timeDuration = tmpE - tmpS;
                            dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                        } else {
                            dayTime.setTimeDuration(0f);
                        }
                    }
                } else {
                    long travelStartTime;
                    long travekEndTime;

                    if (isky) {
                        tmpS = 0;
                        tmpE = Math.min(this.getLeaveDateHour(lastTime), shiftDef.getEndTime());

                        travelStartTime = maxDate;
                        travekEndTime = maxDate + tmpE * 60;
                    } else {
                        tmpS = shiftDef.getStartTime();
                        long etime = Math.min(workEtime, lastTime);
                        tmpE = this.getLeaveDateHour(etime);

                        travelStartTime = maxDate + tmpS * 60;
                        travekEndTime = maxDate + tmpE * 60;
                    }

                    if (shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) {
                        Integer timeDuration = deductLeaveDayNooRest(detail, shiftDef, travelStartTime, travekEndTime);
                        dayTime.setTimeDuration(new Float(timeDuration));
                    } else {
                        dayTime.setTimeDuration((float) (tmpE - tmpS));
                    }
                }
                dayTime.setStartTime(tmpS);
                dayTime.setEndTime(tmpE);
            } else if (startDate == minDate) {//第一天
                Integer tmpS = this.getLeaveDateHour(firstTime);
                Integer tmpE = 24 * 60;

                if (includeNonWorkday && dateType != 1) {
                    int timeDuration = tmpE - tmpS;
                    dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                } else {
                    long stime = Math.max(workStime, firstTime);
                    tmpS = this.getLeaveDateHour(stime);
                    if (isky) {
                        tmpE = 24 * 60;
                    } else {
                        tmpE = shiftDef.getEndTime();
                    }

                    long travelStartTime = stime;
                    long travekEndTime = minDate + tmpE * 60;

                    if (stime < workStime) {
                        dayTime.setTimeDuration(shiftDef.getWorkTotalTime().floatValue());
                    } else if (stime > workEtime) {
                        dayTime.setTimeDuration(0f);
                    } else {
                        if (shiftDef.getIsNoonRest()) {
                            Integer timeDuration = deductLeaveDayNooRest(detail, shiftDef, travelStartTime, travekEndTime);
                            dayTime.setTimeDuration(new Float(timeDuration));
                        } else {
                            dayTime.setTimeDuration((float) (tmpE - tmpS));
                        }
                    }
                }
                dayTime.setStartTime(tmpS);
                dayTime.setEndTime(tmpE);
            } else {//是中间一天
                if (dateType == 1) {
                    dayTime.setTimeDuration(shiftDef.getWorkTotalTime().floatValue());
                    dayTime.setStartTime(shiftDef.getStartTime());
                    dayTime.setEndTime(shiftDef.getEndTime());
                } else {
                    dayTime.setTimeDuration(includeNonWorkday ? 8 * 60f : 0f);
                    dayTime.setStartTime(0);
                    dayTime.setEndTime(1440);
                }
            }
        }
        if (dayTime.getTimeDuration() == null || dayTime.getTimeDuration() < 0) {
            dayTime.setTimeDuration(0f);
        }
        //特殊班次工作时长
        if (shiftDef.getIsSpecial() != null && shiftDef.getIsSpecial() && shiftDef.getSpecialWorkTime() != null && dayTime.getTimeDuration().equals(Float.valueOf(shiftDef.getWorkTotalTime()))) {
            dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
        }
    }

    /**
     * 小时
     *
     * @param travelTypeDo
     * @param startDate
     * @param endDate
     * @param startTimeStr
     * @param endTimeStr
     * @param pbMap
     * @param dayTime
     * @param shiftMap
     * @throws Exception
     */
    public void calLeaveTimeByPeriod3New(WaTravelTypeDo travelTypeDo, long startDate, long endDate,
                                         String startTimeStr, String endTimeStr, Map<Long, WaWorktimeDetail> pbMap,
                                         WaEmpTravelDaytimeDo dayTime, Map<Integer, WaShiftDef> shiftMap) throws Exception {
        //小时非整天
        long minDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long maxDate = DateUtil.getTimesampByDateStr2(endTimeStr);
        long firstTime = DateUtil.getTimesampByStr2(startTimeStr);
        long lastTime = DateUtil.getTimesampByStr2(endTimeStr);
        boolean includeNonWorkday = travelTypeDo.getIfIncludeNonWorkday() != null && travelTypeDo.getIfIncludeNonWorkday() == 1;

        WaWorktimeDetail detail = pbMap.get(startDate);//取得当天排班
        WaShiftDef shiftDef = shiftMap.get(detail.getShiftDefId());
        WaShiftDef substituteShiftDef = null;// 非工作日的替代班次
        if (null != shiftDef.getSubstituteShift() && shiftMap.containsKey(shiftDef.getSubstituteShift())) {
            substituteShiftDef = shiftMap.get(shiftDef.getSubstituteShift());
        }
        Integer dateType = detail.getDateType();
        if (dateType != 1 && includeNonWorkday && null != substituteShiftDef) {
            shiftDef = substituteShiftDef;
            dateType = substituteShiftDef.getDateType();
        }

        long workStime = startDate + shiftDef.getStartTime() * 60;
        long workEndDate = startDate;
        boolean isky = false;
        if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {
            workEndDate += 86400;
            isky = true;
        }
        long workEtime = workEndDate + shiftDef.getEndTime() * 60;

        if (startDate == endDate && minDate == maxDate) {//请一天，第一天也是最后一天
            Integer tmpS = getLeaveDateHour(firstTime);
            Integer tmpE = getLeaveDateHour(lastTime);

            if (tmpE <= shiftDef.getStartTime() || dateType != 1) {//结束时间小于当天上班开始时间 或者 当天非工作日
                Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
                WaWorktimeDetail preEndDateWorkTimeDetail = pbMap.get(preEndDate);

                // 前一天班次信息
                WaShiftDef preShiftDef = null;
                Integer preDateType = null;
                if (null != preEndDateWorkTimeDetail) {
                    preShiftDef = shiftMap.get(preEndDateWorkTimeDetail.getShiftDefId());
                    // 非工作日的替代班次
                    WaShiftDef preSubstituteShiftDef = null;
                    if (null != preShiftDef.getSubstituteShift() && shiftMap.containsKey(preShiftDef.getSubstituteShift())) {
                        preSubstituteShiftDef = shiftMap.get(preShiftDef.getSubstituteShift());
                    }
                    preDateType = preEndDateWorkTimeDetail.getDateType();
                    if (preDateType != 1 && includeNonWorkday && null != preSubstituteShiftDef) {
                        preShiftDef = preSubstituteShiftDef;
                        preDateType = preSubstituteShiftDef.getDateType();
                    }
                }

                if (preShiftDef != null && preDateType == 1 &&
                        CdWaShiftUtil.checkCrossNight(preShiftDef.getStartTime(), preShiftDef.getEndTime(), preDateType)
                        && tmpS < preShiftDef.getEndTime()) {
                    // 前一天班次为工作日且班次跨夜
                    long travelStartTime = firstTime;
                    long travekEndTime = lastTime;
                    if (tmpE > preShiftDef.getEndTime()) {
                        tmpE = preShiftDef.getEndTime();
                        travekEndTime = maxDate + tmpE * 60;
                    }
                    if (preShiftDef.getIsNoonRest() != null && preShiftDef.getIsNoonRest()) {
                        Integer timeDuration = deductLeaveDayNooRest(preEndDateWorkTimeDetail, preShiftDef, travelStartTime, travekEndTime);
                        dayTime.setTimeDuration(new Float(timeDuration));
                    } else {
                        dayTime.setTimeDuration((float) (tmpE - tmpS));
                    }
                } else {
                    if (includeNonWorkday && dateType != 1) {
                        int timeDuration = tmpE - tmpS;
                        dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                    } else {
                        dayTime.setTimeDuration(0f);
                    }
                }
            } else {
                long stime = Math.max(workStime, firstTime);
                long etime = Math.min(workEtime, lastTime);

                tmpS = getLeaveDateHour(stime);
                tmpE = getLeaveDateHour(etime);

                if (shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) {
                    Integer timeDuration = deductLeaveDayNooRest(detail, shiftDef, stime, etime);
                    dayTime.setTimeDuration(new Float(timeDuration));
                } else {
                    dayTime.setTimeDuration((float) (tmpE - tmpS));
                }
            }
            dayTime.setStartTime(tmpS);
            dayTime.setEndTime(tmpE);
        } else {//请多天假
            //计算开始日期与结束日期相差的天数
            Integer dateDiff = DateUtilExt.getDifferenceDay(minDate, maxDate);
            if (startDate == endDate) {//最后一天
                // 前一天班次信息
                Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
                WaWorktimeDetail preEndDateWorkTimeDetail = pbMap.get(preEndDate);
                WaShiftDef preShiftDef = null;
                Integer preDateType = null;
                if (null != preEndDateWorkTimeDetail) {
                    preShiftDef = shiftMap.get(preEndDateWorkTimeDetail.getShiftDefId());
                    // 非工作日的替代班次
                    WaShiftDef preSubstituteShiftDef = null;
                    if (null != preShiftDef.getSubstituteShift() && shiftMap.containsKey(preShiftDef.getSubstituteShift())) {
                        preSubstituteShiftDef = shiftMap.get(preShiftDef.getSubstituteShift());
                    }
                    preDateType = preEndDateWorkTimeDetail.getDateType();
                    if (preDateType != 1 && includeNonWorkday && null != preSubstituteShiftDef) {
                        preShiftDef = preSubstituteShiftDef;
                        preDateType = preSubstituteShiftDef.getDateType();
                    }
                }
                boolean preDateKy = preShiftDef != null && preDateType == 1
                        && CdWaShiftUtil.checkCrossNight(preShiftDef.getStartTime(), preShiftDef.getEndTime(), preDateType);

                Integer tmpS;
                Integer tmpE = getLeaveDateHour(lastTime);

                if (dateType != 1) {
                    if (preDateKy) {
                        if (tmpE <= preShiftDef.getEndTime()) {
                            tmpS = 0;
                            tmpE = 0;
                            dayTime.setTimeDuration(0f);
                        } else {
                            tmpS = preShiftDef.getEndTime();
                            if (includeNonWorkday) {
                                int timeDuration = tmpE - tmpS;
                                dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                            } else {
                                dayTime.setTimeDuration(0f);
                            }
                        }
                    } else {
                        tmpS = 0;
                        if (includeNonWorkday) {
                            int timeDuration = tmpE - tmpS;
                            dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                        } else {
                            tmpE = 0;
                            dayTime.setTimeDuration(0f);
                        }
                    }
                } else {
                    if (tmpE <= shiftDef.getStartTime()) {
                        tmpS = 0;
                        tmpE = 0;
                        dayTime.setTimeDuration(0f);
                    } else {
                        long travelStartTime;
                        long travekEndTime;

                        if (isky) {
                            tmpS = getLeaveDateHour(workStime);
                            tmpE = Math.min(tmpE, 1440);

                            travelStartTime = workStime;
                            travekEndTime = maxDate + tmpE * 60;
                        } else {
                            tmpS = shiftDef.getStartTime();
                            long etime = Math.min(workEtime, lastTime);
                            tmpE = this.getLeaveDateHour(etime);

                            travelStartTime = maxDate + tmpS * 60;
                            travekEndTime = maxDate + tmpE * 60;
                        }

                        if (shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) {
                            Integer timeDuration = deductLeaveDayNooRest(detail, shiftDef, travelStartTime, travekEndTime);
                            dayTime.setTimeDuration(new Float(timeDuration));
                        } else {
                            dayTime.setTimeDuration((float) (tmpE - tmpS));
                        }
                    }
                }
                dayTime.setStartTime(tmpS);
                dayTime.setEndTime(tmpE);
            } else if (startDate == minDate) {//第一天
                Integer tmpS = this.getLeaveDateHour(firstTime);
                Integer tmpE = 24 * 60;

                if (dateType != 1) {
                    if (includeNonWorkday) {
                        int timeDuration = tmpE - tmpS;
                        dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                    } else {
                        tmpS = 0;
                        tmpE = 0;
                        dayTime.setTimeDuration(0f);
                    }
                } else {
                    long stime = Math.max(workStime, firstTime);
                    tmpS = this.getLeaveDateHour(stime);

                    long travelStartTime = stime;
                    long travekEndTime;

                    if (isky) {
                        if (dateDiff > 1) {
                            // 申请日期天数>2天
                            tmpE = shiftDef.getEndTime();
                            travekEndTime = minDate + 86400 + tmpE * 60;
                        } else {
                            // 申请日期天数=2天
                            tmpE = Math.min(this.getLeaveDateHour(lastTime), shiftDef.getEndTime());
                            travekEndTime = minDate + 86400 + tmpE * 60;
                        }
                    } else {
                        tmpE = shiftDef.getEndTime();
                        travekEndTime = minDate + tmpE * 60;
                    }

                    if (stime < workStime) {
                        // todo 此逻辑是从1.0延续过来的，暂不清楚什么场景下才会使用
                        dayTime.setTimeDuration(shiftDef.getWorkTotalTime().floatValue());
                    } else if (stime > workEtime) {
                        dayTime.setTimeDuration(0f);
                    } else {
                        if (shiftDef.getIsNoonRest()) {
                            Integer timeDuration = deductLeaveDayNooRest(detail, shiftDef, travelStartTime, travekEndTime);
                            dayTime.setTimeDuration(new Float(timeDuration));
                        } else {
                            long durationSec = travekEndTime - travelStartTime;
                            int timeDuration = new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
                            dayTime.setTimeDuration((float) timeDuration);
                        }
                    }
                }
                dayTime.setStartTime(tmpS);
                dayTime.setEndTime(tmpE);
            } else {//是中间一天
                if (dateType == 1) {
                    if (startDate == (maxDate - 86400)) {
                        // 倒数第二天
                        if (isky) {
                            Integer tmpE = getLeaveDateHour(lastTime);
                            if (tmpE >= shiftDef.getEndTime()) {
                                dayTime.setTimeDuration(shiftDef.getWorkTotalTime().floatValue());
                                dayTime.setStartTime(shiftDef.getStartTime());
                                dayTime.setEndTime(shiftDef.getEndTime());
                            } else {
                                long travelStartTime = workStime;
                                long travekEndTime = lastTime;
                                if (shiftDef.getIsNoonRest()) {
                                    Integer timeDuration = deductLeaveDayNooRest(detail, shiftDef, travelStartTime, travekEndTime);
                                    dayTime.setTimeDuration(new Float(timeDuration));
                                } else {
                                    long durationSec = travekEndTime - travelStartTime;
                                    int timeDuration = new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
                                    dayTime.setTimeDuration((float) timeDuration);
                                }
                                dayTime.setStartTime(shiftDef.getStartTime());
                                dayTime.setEndTime(tmpE);
                            }
                        } else {
                            dayTime.setTimeDuration(shiftDef.getWorkTotalTime().floatValue());
                            dayTime.setStartTime(shiftDef.getStartTime());
                            dayTime.setEndTime(shiftDef.getEndTime());
                        }
                    } else {
                        dayTime.setTimeDuration(shiftDef.getWorkTotalTime().floatValue());
                        dayTime.setStartTime(shiftDef.getStartTime());
                        dayTime.setEndTime(shiftDef.getEndTime());
                    }
                } else {
                    dayTime.setTimeDuration(includeNonWorkday ? 8 * 60f : 0f);
                    dayTime.setStartTime(0);
                    dayTime.setEndTime(1440);
                }
            }
        }
        if (dayTime.getTimeDuration() == null || dayTime.getTimeDuration() < 0) {
            dayTime.setTimeDuration(0f);
        }
        //特殊班次工作时长
        if (shiftDef.getIsSpecial() != null && shiftDef.getIsSpecial() && shiftDef.getSpecialWorkTime() != null && dayTime.getTimeDuration().equals(Float.valueOf(shiftDef.getWorkTotalTime()))) {
            dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
        }
    }

    /**
     * 小时整天
     *
     * @param travelTypeDo
     * @param worktimeDetail
     * @param dayTime
     * @param corpAllShiftDef
     */
    public void processLeaveByPeriod4(WaTravelTypeDo travelTypeDo, WaWorktimeDetail worktimeDetail, WaEmpTravelDaytimeDo dayTime,
                                      Map<Integer, WaShiftDef> corpAllShiftDef) {
        WaShiftDef shiftDef = corpAllShiftDef.get(worktimeDetail.getShiftDefId());
        // 非工作日申请出差的替代班次
        WaShiftDef substituteShiftDef = null;
        if (null != shiftDef.getSubstituteShift() && corpAllShiftDef.containsKey(shiftDef.getSubstituteShift())) {
            substituteShiftDef = corpAllShiftDef.get(shiftDef.getSubstituteShift());
        }
        boolean includeNonWorkday = travelTypeDo.getIfIncludeNonWorkday() != null && travelTypeDo.getIfIncludeNonWorkday() == 1;
        Integer dateType = worktimeDetail.getDateType();
        if (dateType != 1 && includeNonWorkday && null != substituteShiftDef) {
            shiftDef = substituteShiftDef;
            dateType = substituteShiftDef.getDateType();
        }

        boolean isSpecial = false;//特殊班次工时调整
        if (shiftDef.getIsSpecial() != null && shiftDef.getIsSpecial() && shiftDef.getSpecialWorkTime() != null) {
            isSpecial = true;
        }
        if (dateType == 1) {
            if (isSpecial) {
                dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
            } else {
                dayTime.setTimeDuration(shiftDef.getWorkTotalTime().floatValue());
            }
            dayTime.setStartTime(shiftDef.getStartTime());
            dayTime.setEndTime(shiftDef.getEndTime());
        } else {
            if (isSpecial) {
                dayTime.setTimeDuration(includeNonWorkday ? shiftDef.getSpecialWorkTime().floatValue() : 0f);
            } else {
                dayTime.setTimeDuration(includeNonWorkday ? 8 * 60f : 0f);
            }
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        }
    }

    /**
     * 半天
     *
     * @param travelTypeDo
     * @param detail
     * @param dayTime
     * @param shalfDay
     * @param ehalfDay
     * @param nowDate
     * @param startDate
     * @param endDate
     */
    public void processLeaveByPeriod9(WaTravelTypeDo travelTypeDo, WaWorktimeDetail detail, WaEmpTravelDaytimeDo dayTime,
                                      String shalfDay, String ehalfDay, long nowDate, long startDate, long endDate) {
        boolean includeNonWorkday = travelTypeDo.getIfIncludeNonWorkday() != null && travelTypeDo.getIfIncludeNonWorkday() == 1;
        if (nowDate == startDate && nowDate == endDate) {//第一天也是最后一天
            if ("P".equals(shalfDay)) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("P");
                dayTime.setEhalfDay("P");
            } else if ("A".equals(shalfDay) && "A".equals(ehalfDay)) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("A");
            } else {
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(1f);
            }
        } else if (nowDate == startDate && nowDate < endDate) {//第一天不是最后一天
            if ("A".equals(shalfDay)) {
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(1f);
            } else {
                dayTime.setShalfDay("P");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(0.5f);
            }
        } else if (nowDate > startDate && nowDate < endDate) {//中间一天
            if (detail.getDateType() == 1) {
                dayTime.setTimeDuration(1f);
            } else {
                dayTime.setTimeDuration(includeNonWorkday ? 1f : 0f);
            }
            dayTime.setShalfDay("A");
            dayTime.setEhalfDay("P");
        } else if (nowDate > startDate && nowDate == endDate) {
            if ("P".equals(ehalfDay)) {
                dayTime.setTimeDuration(1f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
            } else if ("A".equals(ehalfDay)) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("A");
            }
        }
    }

    /**
     * 计算扣除中午休息的时间
     *
     * @param workTimeDetail
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    private Integer deductLeaveDayNooRest(WaWorktimeDetail workTimeDetail, Long startTime, Long endTime) throws Exception {
        //计算扣除中午休息的时间，多段
        long durationSec = endTime - startTime;
        if (workTimeDetail.getIsNoonRest() != null && workTimeDetail.getIsNoonRest()) {
            List<Map> restList = new ArrayList<>();
            //定义半天的休息时间段
            Integer restStart = workTimeDetail.getNoonRestStart();
            Integer restEnd = workTimeDetail.getNoonRestEnd();
            if (restStart != null && restEnd != null) {
                Map noonRestMap = new LinkedHashMap();
                noonRestMap.put("noonRestStart", restStart);
                noonRestMap.put("noonRestEnd", restEnd);
                restList.add(noonRestMap);
            }
            if (workTimeDetail.getRestPeriods() != null) {
                List<Map> restPeriods = (List) JacksonJsonUtil.jsonToBean(workTimeDetail.getRestPeriods().toString(), List.class);
                if (CollectionUtils.isNotEmpty(restPeriods)) {
                    restList.addAll(restPeriods);
                }
            }
            if (CollectionUtils.isNotEmpty(restList)) {
                for (Map periodMap : restList) {
                    Integer noonRestStartMi = (Integer) periodMap.get("noonRestStart");
                    Integer noonRestEndMi = (Integer) periodMap.get("noonRestEnd");

                    ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMi, noonRestEndMi, workTimeDetail.getStartTime(), workTimeDetail.getEndTime(), workTimeDetail.getDateType());
                    noonRestStartMi = restPeriod.getNoonRestStart();
                    noonRestEndMi = restPeriod.getNoonRestEnd();

                    long noonRestStartTime = workTimeDetail.getWorkDate() + noonRestStartMi * 60;
                    long noonRestEndTime = workTimeDetail.getWorkDate() + noonRestEndMi * 60;

                    if (startTime <= noonRestEndTime && endTime >= noonRestStartTime) {
                        durationSec -= Math.min(noonRestEndTime, endTime) - Math.max(noonRestStartTime, startTime);
                    }
                }
            }
        }

        return new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
    }

    /**
     * 计算扣除中午休息的时间
     *
     * @param workTimeDetail
     * @param shiftDef
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    private Integer deductLeaveDayNooRest(WaWorktimeDetail workTimeDetail, WaShiftDef shiftDef, Long startTime, Long endTime) throws Exception {
        //计算扣除中午休息的时间，多段
        long durationSec = endTime - startTime;
        if (shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) {
            List<Map> restList = new ArrayList<>();
            //定义半天的休息时间段
            Integer restStart = shiftDef.getNoonRestStart();
            Integer restEnd = shiftDef.getNoonRestEnd();
            if (restStart != null && restEnd != null) {
                Map noonRestMap = new LinkedHashMap();
                noonRestMap.put("noonRestStart", restStart);
                noonRestMap.put("noonRestEnd", restEnd);
                restList.add(noonRestMap);
            }
            if (shiftDef.getRestPeriods() != null) {
                List<Map> restPeriods = (List) JacksonJsonUtil.jsonToBean(shiftDef.getRestPeriods().toString(), List.class);
                if (CollectionUtils.isNotEmpty(restPeriods)) {
                    restList.addAll(restPeriods);
                }
            }
            if (CollectionUtils.isNotEmpty(restList)) {
                for (Map periodMap : restList) {
                    Integer noonRestStartMi = (Integer) periodMap.get("noonRestStart");
                    Integer noonRestEndMi = (Integer) periodMap.get("noonRestEnd");

                    ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMi, noonRestEndMi, shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType());
                    noonRestStartMi = restPeriod.getNoonRestStart();
                    noonRestEndMi = restPeriod.getNoonRestEnd();

                    long noonRestStartTime = workTimeDetail.getWorkDate() + noonRestStartMi * 60;
                    long noonRestEndTime = workTimeDetail.getWorkDate() + noonRestEndMi * 60;

                    if (startTime <= noonRestEndTime && endTime >= noonRestStartTime) {
                        durationSec -= Math.min(noonRestEndTime, endTime) - Math.max(noonRestStartTime, startTime);
                    }
                }
            }
        }

        return new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
    }

    private Integer getLeaveDateHour(Long time) {
        String s = DateUtil.convertDateTimeToStr(time, "HH:mm", true);
        if (StringUtils.isNotBlank(s)) {
            String[] shmArr = s.split(":");
            return Integer.parseInt(shmArr[0]) * 60 + Integer.parseInt(shmArr[1]);
        }
        return 0;
    }

    /**
     * 校验出差类型：根据出差类型（规则）适用范围，匹配条件的员工才可已申请该类型的出差
     *
     * @param tenantId     租户
     * @param travelTypeId 出差类型（规则）
     * @param empId        员工
     * @return
     */
    public Boolean checkEmpTravelType(String tenantId, Long travelTypeId, Long empId) {
        List<String> businessKeys = Collections.singletonList(String.valueOf(travelTypeId));
        List<EmployeeGroupDto> empGroups = employeeGroupService.getEmployeeGroups(businessKeys, "wa_travel_type", tenantId);
        List<Long> empIds = Lists.newArrayList();
        for (EmployeeGroupDto empGroup : empGroups) {
            if (StringUtils.isNotEmpty(empGroup.getGroupExp())) {
                // 根据规则筛选员工
                List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(Long.valueOf(tenantId), empGroup.getGroupExp(), null);
                if (CollectionUtils.isEmpty(groupEmpIds)) {
                    continue;
                }
                empIds.addAll(groupEmpIds);
            }
        }
        return !empIds.contains(empId);
    }

    /**
     * 出差时间校验
     *
     * @param tenantId
     * @param realStart
     * @param realEnd
     * @param startDate
     * @param endDate
     * @param period
     * @param startDateShift
     * @param endDateShift
     * @param empid
     * @param corpAllShiftDef
     * @param empShiftMap
     * @return
     */
    public Result checkTime(String tenantId, Long realStart, Long realEnd, Long startDate, Long endDate,
                            Integer period, WaShiftDef startDateShift, WaShiftDef endDateShift, Long empid,
                            Map<Integer, WaShiftDef> corpAllShiftDef, Map<Long, WaWorktimeDetail> empShiftMap) {
        // 重叠校验一：检查是否和已有的出差数据时间重叠
        long checkStart = realStart;
        long checkEnd = realEnd;
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)
                || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
            checkStart = startDate + startDateShift.getStartTime() * 60;
            if (CdWaShiftUtil.checkCrossNight(endDateShift.getStartTime(), endDateShift.getEndTime(), endDateShift.getDateType())) {
                int endShiftEndTime = endDateShift.getEndTime() + 1440;
                checkEnd = endDate + endShiftEndTime * 60L;
            } else {
                checkEnd = endDate + endDateShift.getEndTime() * 60;
                // checkEnd = end + 86399;
            }
        }
        if (waEmpTravelDo.checkEmpTravelTimeRepeat(empid, checkStart, checkEnd) > 0) {
            return ResponseWrap.wrapResult(AttendanceCodes.EVENT_TIME_OVERLAP, Boolean.FALSE);
        }
        // 重叠校验二：查询有效的每日出差数据，用于出差时间重叠校验，只查询period_type=1和4这种数据，其他period_type=3和9 的这种数据在上面【重叠校验一】中已做过校验
        List<WaEmpTravelDaytimeDo> oldTravelDaytimeList = waEmpTravelDaytimeDo.getWaTravelDaytimeList(tenantId, empid,
                startDate, endDate, Lists.newArrayList(1, 2, 8), Lists.newArrayList(1, 4));
        if (CollectionUtils.isNotEmpty(oldTravelDaytimeList)) {
            for (WaEmpTravelDaytimeDo oldTravelDaytime : oldTravelDaytimeList) {
                // 根据班次计算实际的出差时间
                WaShiftDef oldDateShiftDef = corpAllShiftDef.get(oldTravelDaytime.getShiftDefId());
                if (null == oldDateShiftDef) {
                    WaWorktimeDetail worktimeDetail = empShiftMap.get(oldTravelDaytime.getTravelDate());
                    oldDateShiftDef = corpAllShiftDef.get(worktimeDetail.getShiftDefId());
                    if (null != oldDateShiftDef.getSubstituteShift() && corpAllShiftDef.containsKey(oldDateShiftDef.getSubstituteShift())) {
                        oldDateShiftDef = corpAllShiftDef.get(oldDateShiftDef.getSubstituteShift());
                    }
                }
                long realStartTime = oldTravelDaytime.getTravelDate() + oldDateShiftDef.getStartTime() * 60;
                long realEndTime = oldTravelDaytime.getTravelDate() + oldDateShiftDef.getEndTime() * 60;
                if (CdWaShiftUtil.checkCrossNight(oldDateShiftDef.getStartTime(), oldDateShiftDef.getEndTime(), oldDateShiftDef.getDateType())) {
                    int endShiftEndTime = oldDateShiftDef.getEndTime() + 1440;
                    realEndTime = oldTravelDaytime.getTravelDate() + endShiftEndTime * 60L;
                }
                if (realStartTime < checkEnd && realEndTime > checkStart) {
                    return ResponseWrap.wrapResult(AttendanceCodes.EVENT_TIME_OVERLAP, Boolean.FALSE);
                }
            }
        }
        // 重叠校验三：检查出差时间是否和休假时间重叠
        Map<String, Object> params = new HashMap<>();
        params.put("empid", empid);
        params.put("start", checkStart);
        params.put("end", checkEnd);
        if (waCheckMapper.checkLeaveRepeat(params) > 0) {
            return ResponseWrap.wrapResult(AttendanceCodes.EVENT_TIME_OVERLAP, Boolean.FALSE);
        }
        return Result.ok();
    }

    /**
     * 计算实际出差时间
     *
     * @param period
     * @param startDate
     * @param endDate
     * @param startDateShift
     * @param endDateShift
     * @param dto
     * @return
     */
    private Map<String, Object> calRealTravelTime(Integer period, Long startDate, Long endDate, WaShiftDef startDateShift,
                                                  WaShiftDef endDateShift, EmpTravelSaveDto dto) {
        long realStart;
        long realEnd;
        String startTimeStr = "", endTimeStr = "";

        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
            realStart = startDate;
            realEnd = endDate;
            if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
                startTimeStr = DateUtil.getTimeStrByTimesamp4(dto.getStartTime());
                endTimeStr = DateUtil.getTimeStrByTimesamp4(dto.getEndTime());
            } else {
                startTimeStr = DateUtil.getDateStrByTimesamp(dto.getStartTime());
                endTimeStr = DateUtil.getDateStrByTimesamp(dto.getEndTime());
            }
        } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            realStart = dto.getStartTime() + dto.getStime() * 60;
            realEnd = dto.getEndTime() + dto.getEtime() * 60;

            startTimeStr = DateUtil.getTimeStrByTimesamp4(realStart);
            endTimeStr = DateUtil.getTimeStrByTimesamp4(realEnd);
        } else {// 请半天
            realStart = startDate + startDateShift.getStartTime() * 60;
            String shalfday = dto.getShalfDay();
            if ("P".equals(shalfday)) {
                Integer startHalfdayTime = startDateShift.getHalfdayTime();
                if (startDateShift.getIsHalfdayTime() != null && startDateShift.getIsHalfdayTime() && startHalfdayTime > 0) {
                    startHalfdayTime = startHalfdayTime < startDateShift.getStartTime() ? startHalfdayTime + 1440 : startHalfdayTime;
                    realStart = startDate + startHalfdayTime * 60;
                } else if (startDateShift.getIsNoonRest() != null && startDateShift.getIsNoonRest()) {
                    Integer startNoonRestEnd = startDateShift.getNoonRestEnd();
                    startNoonRestEnd = startNoonRestEnd < startDateShift.getStartTime() ? startNoonRestEnd + 1440 : startNoonRestEnd;
                    realStart = startDate + startNoonRestEnd * 60;
                }
            }
            realEnd = endDate + endDateShift.getEndTime() * 60;
            String ehalfday = dto.getEhalfDay();
            if ("A".equals(ehalfday)) {
                Integer endHalfdayTime = endDateShift.getHalfdayTime();
                if (endDateShift.getIsHalfdayTime() != null && endDateShift.getIsHalfdayTime() && endHalfdayTime > 0) {
                    endHalfdayTime = endHalfdayTime < endDateShift.getStartTime() ? endHalfdayTime + 1440 : endHalfdayTime;
                    realEnd = endDate + endHalfdayTime * 60;
                } else if (endDateShift.getIsNoonRest() != null && endDateShift.getIsNoonRest()) {
                    Integer endNoonRestStart = endDateShift.getNoonRestStart();
                    endNoonRestStart = endNoonRestStart < endDateShift.getStartTime() ? endNoonRestStart + 1440 : endNoonRestStart;
                    realEnd = endDate + endNoonRestStart * 60;
                }
            } else if (CdWaShiftUtil.checkCrossNight(endDateShift.getStartTime(), endDateShift.getEndTime(), endDateShift.getDateType())) {
                int endShiftEndTime = endDateShift.getEndTime() + 1440;
                realEnd = endDate + endShiftEndTime * 60L;
            }
            startTimeStr = DateUtil.getDateStrByTimesamp(dto.getStartTime());
            endTimeStr = DateUtil.getDateStrByTimesamp(dto.getEndTime());
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("realStart", realStart);
        resultMap.put("realEnd", realEnd);
        resultMap.put("startTimeStr", startTimeStr);
        resultMap.put("endTimeStr", endTimeStr);
        return resultMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result checkOrSaveTravelTime(EmpTravelSaveDto dto) throws Exception {
        UserInfo userInfo = this.getUserInfo();
        WaTravelTypeDo travelTypeDo = waTravelTypeDo.selectOneById(userInfo.getTenantId(), dto.getTravelTypeId());
        if (travelTypeDo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        LogRecordContext.putVariable("name", travelTypeDo.getTravelTypeName());
        if (dto.getOpt() == 2 && travelTypeDo.getIfUploadFile() != null && travelTypeDo.getIfUploadFile() == 1 && StringUtils.isBlank(dto.getFile())) {
            return ResponseWrap.wrapResult(AttendanceCodes.PLEASE_UPLOAD_FILE, Boolean.FALSE);
        }
        if (dto.getOpt() == 2 && travelTypeDo.getIfWriteRemark() != null && travelTypeDo.getIfWriteRemark() == 1 && StringUtils.isBlank(dto.getReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.PLEASE_WRITE_REASON, Boolean.FALSE);
        }
        long startDate = DateUtil.getOnlyDate(new Date(dto.getStartTime() * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(dto.getEndTime() * 1000));
        //考勤截止日判断
        WaSob waSob = waSobService.getWaSob(dto.getEmpId(), startDate);
        if (waSob != null) {
            Integer sysPeriodMonth = waSob.getSysPeriodMonth();
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            Long onlyDate = DateUtil.getOnlyDate();
            if (onlyDate > sobEndDate) {
                //return Result.fail("申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员");
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
            }
        }
        //查询公司班次和员工排班
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(dto.getEmpId());
        if (empInfo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, Boolean.FALSE);
        }
        if (checkEmpTravelType(userInfo.getTenantId(), travelTypeDo.getTravelTypeId(), empInfo.getEmpid())) {
            // 不符合申请条件，无法申请出差
            return ResponseWrap.wrapResult(AttendanceCodes.NOT_MATCH_TRAVEL_RULE, Boolean.FALSE);
        }
        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
        tmType = tmType == 0 ? 1 : tmType;
        Map<Integer, WaShiftDef> corpAllShiftDef = waCommonService.getCorpAllShiftDef(userInfo.getTenantId());
        Map<Long, WaWorktimeDetail> empShiftMap = waCommonService.getEmpWaWorktimeDetail(userInfo.getTenantId(), dto.getEmpId(),
                tmType, startDate - 86400, endDate, empInfo.getWorktimeType(), true);
        if (MapUtils.isEmpty(empShiftMap)) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, Boolean.FALSE);
        }
        long tmpDate = startDate;
        while (tmpDate <= endDate) {
            if (!empShiftMap.containsKey(tmpDate)) {
                //return Result.fail("员工" + DateUtil.getDateStrByTimesamp(tmpDate) + "未排班");
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.EMP_DAY_NOT_SHIFT, "").getMsg(), DateUtil.getDateStrByTimesamp(tmpDate)));
            }
            tmpDate = tmpDate + 24 * 60 * 60;
        }
        //出差时间类型
        Integer period;
        if (LeaveTypeUnitEnum.DAY.getIndex().equals(travelTypeDo.getAcctTimeType())) {
            period = PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex();
            if (StringUtils.isNotBlank(dto.getShalfDay()) && StringUtils.isNotBlank(dto.getEhalfDay())) {
                period = PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex();
            }
        } else {
            period = PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex();
            if ((dto.getStime() != null && dto.getStime() > 0) || (dto.getEtime() != null && dto.getEtime() > 0)) {
                period = PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex();
                if (dto.getStime() == null) {
                    dto.setStime(0);
                }
                if (dto.getEtime() == null) {
                    dto.setEtime(0);
                }
            }
        }
        // 出差开始日期班次
        WaShiftDef startDateShift = corpAllShiftDef.get(empShiftMap.get(startDate).getShiftDefId());
        if (null != startDateShift.getSubstituteShift() && corpAllShiftDef.containsKey(startDateShift.getSubstituteShift())) {
            startDateShift = corpAllShiftDef.get(startDateShift.getSubstituteShift());
        }
        // 出差时结束日期班次
        WaShiftDef endDateShift = corpAllShiftDef.get(empShiftMap.get(endDate).getShiftDefId());
        if (null != endDateShift.getSubstituteShift() && corpAllShiftDef.containsKey(endDateShift.getSubstituteShift())) {
            endDateShift = corpAllShiftDef.get(endDateShift.getSubstituteShift());
        }
        // 计算实际出差时间
        Map<String, Object> realTravelTimeMap = calRealTravelTime(period, startDate, endDate, startDateShift, endDateShift, dto);
        Long realStart = (Long) realTravelTimeMap.get("realStart");
        Long realEnd = (Long) realTravelTimeMap.get("realEnd");
        String startTimeStr = (String) realTravelTimeMap.get("startTimeStr");
        String endTimeStr = (String) realTravelTimeMap.get("endTimeStr");
        // 出差时间重叠校验
        Result timeCheckResult = checkTime(userInfo.getTenantId(), realStart, realEnd, startDate, endDate, period,
                startDateShift, endDateShift, dto.getEmpId(), corpAllShiftDef, empShiftMap);
        if (!timeCheckResult.isSuccess()) {
            return timeCheckResult;
        }
        //计算每天的出差时长
        WaWorktimeDetail worktimeDetail;
        WaShiftDef shiftDef;
        WaEmpTravelDaytimeDo dayTime;
        BigDecimal totalTimeDuration = new BigDecimal(0);
        List<WaEmpTravelDaytimeDo> daytimeList = new ArrayList<>();
        boolean includeNonWorkday = travelTypeDo.getIfIncludeNonWorkday() != null && travelTypeDo.getIfIncludeNonWorkday() == 1;

        long tmpTravelDate = startDate;
        boolean flag = true;
        while (flag) {
            worktimeDetail = empShiftMap.get(tmpTravelDate);
            shiftDef = corpAllShiftDef.get(worktimeDetail.getShiftDefId());
            if (worktimeDetail.getDateType() == 4) {//公司特殊假日
                worktimeDetail.setDateType(shiftDef.getDateType());
            }
            // 非工作日出差申请替换班次
            if (worktimeDetail.getDateType() != 1 && includeNonWorkday && null != shiftDef.getSubstituteShift()
                    && null != corpAllShiftDef.get(shiftDef.getSubstituteShift())) {
                shiftDef = corpAllShiftDef.get(shiftDef.getSubstituteShift());
            }
            dayTime = new WaEmpTravelDaytimeDo();
            dayTime.setTravelDate(tmpTravelDate);
            dayTime.setExtCustomCol(dto.getDayTimeExtInfo(dayTime.getTravelDate()));
            dayTime.setRealDate(tmpTravelDate);
            dayTime.setPeriodType(period.shortValue());
            dayTime.setTimeUnit(travelTypeDo.getAcctTimeType().shortValue());
            dayTime.setShiftDefId(shiftDef.getShiftDefId());
            dayTime.setDateType(worktimeDetail.getDateType());
            dayTime.setTimeDuration(0f);
            boolean isCheckDateType = true;
            if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType()) &&
                    PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
                //非工作日并且休的是小时假
                Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
                WaWorktimeDetail preEndDateWorkTimeDetail = empShiftMap.get(preEndDate);
                if (preEndDateWorkTimeDetail != null && preEndDateWorkTimeDetail.getDateType() == 1 &&
                        CdWaShiftUtil.checkCrossNight(preEndDateWorkTimeDetail.getStartTime(), preEndDateWorkTimeDetail.getEndTime(), preEndDateWorkTimeDetail.getDateType())) {
                    //班次是跨夜班
                    isCheckDateType = false;
                }
            }
            if (DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType()) || includeNonWorkday || !isCheckDateType) {
                if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
                    try {
//                        this.calLeaveTimeByPeriod3(travelTypeDo, tmpTravelDate, endDate, startTimeStr, endTimeStr, empShiftMap, dayTime, corpAllShiftDef);
                        this.calLeaveTimeByPeriod3New(travelTypeDo, tmpTravelDate, endDate, startTimeStr, endTimeStr, empShiftMap, dayTime, corpAllShiftDef);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
                    this.processLeaveByPeriod4(travelTypeDo, worktimeDetail, dayTime, corpAllShiftDef);
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
                    this.processLeaveByPeriod9(travelTypeDo, worktimeDetail, dayTime, dto.getShalfDay(), dto.getEhalfDay(),
                            tmpTravelDate, DateUtil.getTimesampByDateStr2(startTimeStr),
                            DateUtil.getTimesampByDateStr2(endTimeStr));
                } else {
                    dayTime.setTimeDuration(1f);
                }
            }
            if (tmpTravelDate == endDate) {
                flag = false;
            } else {
                tmpTravelDate = tmpTravelDate + 24 * 60 * 60;
            }
            totalTimeDuration = totalTimeDuration.add(BigDecimal.valueOf(dayTime.getTimeDuration()));
            daytimeList.add(dayTime);
        }
        if (dto.getOpt() == 2) {// 保存出差申请单
            WaEmpTravelDo empTravel = new WaEmpTravelDo();
            empTravel.setTravelId(snowflakeUtil.createId());
            empTravel.setTenantId(userInfo.getTenantId());
            empTravel.setEmpId(dto.getEmpId());
            empTravel.setTravelTypeId(dto.getTravelTypeId());
            StringBuffer sb = new StringBuffer();
            Integer[] travelMode = dto.getTravelMode();
            if (null != travelMode && travelMode.length > 0) {
                for (Integer row : travelMode) {
                    sb.append(row).append(",");
                }
            }
            if (StringUtils.isNotBlank(sb)) {
                String txt = sb.substring(0, sb.length() - 1);
                empTravel.setTravelMode(txt);
            }
            empTravel.setProvince(dto.getProvince());
            empTravel.setCity(dto.getCity());
            empTravel.setPeriodType(period.shortValue());
            empTravel.setTimeUnit(travelTypeDo.getAcctTimeType().shortValue());
            empTravel.setFileId(dto.getFile());
            empTravel.setFileName(dto.getFileName());
            empTravel.setReason(dto.getReason());
            Long userId = userInfo.getUserId();
            long curTime = System.currentTimeMillis() / 1000L;
            empTravel.setCreateBy(userId);
            empTravel.setCreateTime(curTime);
            empTravel.setUpdateBy(userId);
            empTravel.setUpdateTime(curTime);
            empTravel.setDeleted(0);
            empTravel.setStatus(Integer.valueOf(LeaveStatusEnum.LEAVE_STATUS_1.value));
            if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
                empTravel.setStartTime(startDate);
                empTravel.setEndTime(endDate);
                empTravel.setShalfDay(dto.getShalfDay());
                empTravel.setEhalfDay(dto.getEhalfDay());
            } else {
                empTravel.setStartTime(realStart);
                empTravel.setEndTime(realEnd);
            }
            empTravel.setShiftStartTime(realStart);
            empTravel.setShiftEndTime(realEnd);
            empTravel.setTimeDuration(totalTimeDuration.floatValue());
            empTravel.setBusinessKey(String.format("%s_%s", empTravel.getTravelId(), BusinessCodeEnum.TRAVEL.getCode()));
            empTravel.setExtCustomCol(dto.getExtCustomCol());
            empTravel.setBatchTravelId(dto.getBatchTravelId());
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            // 发起新事务
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            // 获取新开事务状态
            TransactionStatus transactionStatus = platformTransactionManager.getTransaction(def);
            try {
                waEmpTravelDo.save(empTravel);
                daytimeList.forEach(daytime -> {
                    daytime.setTravelId(empTravel.getTravelId());
                    daytime.setTravelDaytimeId(snowflakeUtil.createId());
                    waEmpTravelDaytimeDo.save(daytime);
                });
                // 提交新开事务
                platformTransactionManager.commit(transactionStatus);
            } catch (Exception e) {
                // 回滚新开事务
                platformTransactionManager.rollback(transactionStatus);
                log.error("申请出差失败:{}", e.getMessage(), e);
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.APPLY_TRAVEL_FAILED, null).getMsg());
            }
            String businessKey = String.valueOf(empTravel.getTravelId());
            String wfBusKey = String.format("%s_%s", businessKey, BusinessCodeEnum.TRAVEL.getCode());
            if (!dto.isIfBtchTravel()) {
                // 检查流程是否已启用
                Result<Boolean> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.TRAVEL.getCode());
                if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
                    //throw new CDException("工作流检查异常，请联系管理员！");
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
                }
                Boolean workflowEnabledResultData = checkWorkflowEnableResult.getData();
                if (!workflowEnabledResultData) {
                    if (configService.checkSwitchStatus(SysConfigsEnum.TRAVEL_WORKFLOW_SWITCH.name())) {
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
                    }
                    WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
                    wfCallbackResultDto.setBusinessKey(wfBusKey);
                    wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
                    wfCallbackResultDto.setTenantId(empInfo.getBelongOrgId());
                    workflowCallBackService.saveTravelApproval(wfCallbackResultDto);
                } else {
                    try {
                        WfBeginWorkflowDto workflowDto = new WfBeginWorkflowDto();
                        workflowDto.setFuncCode(BusinessCodeEnum.TRAVEL.getCode());
                        workflowDto.setBusinessId(businessKey);
                        workflowDto.setApplicantId(empInfo.getEmpid().toString());
                        workflowDto.setApplicantName(empInfo.getEmpName());
                        workflowDto.setEventTime(startDate * 1000);
                        workflowDto.setEventEndTime(endDate * 1000);
                        workflowDto.setTimeSlot(getTimeSlot(empTravel));
                        Result<?> result = wfRegisterFeign.begin(workflowDto);
                        if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                            log.error("Startup workflow failed:{}", FastjsonUtil.toJsonStr(result));
                            if (configService.checkSwitchStatus(SysConfigsEnum.TRAVEL_WORKFLOW_SWITCH.name())) {
                                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
                            }
                            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, null).getMsg());
                        }
                    } catch (Exception e) {
                        log.error("Employee travel application has exception:{}", e.getMessage(), e);
                        waEmpTravelDo.delete(empTravel.getTravelId());
                        waEmpTravelDaytimeDo.deleteByTravelIds(Collections.singletonList(empTravel.getTravelId()));
                        if (e instanceof CDException) {
                            if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_CONFIG_ERR, null).getMsg());
                            } else {
                                throw e;
                            }
                        } else {
                            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, null).getMsg());
                        }
                    }
                }
            }
            return Result.ok(wfBusKey);
        } else {
            if (totalTimeDuration.floatValue() <= 0) {
                return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TIME_NOT_ZERO, Boolean.FALSE).getMsg());
            }
            if (travelTypeDo.getAcctTimeType() == 1) {
                if (travelTypeDo.getRoundTimeUnit() != null && totalTimeDuration.floatValue() % travelTypeDo.getRoundTimeUnit() > 0) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.MIN_UNIT_DAY, null).getMsg(), travelTypeDo.getTravelTypeName(), travelTypeDo.getRoundTimeUnit()));
                }
                return Result.ok(String.format(ResponseWrap.wrapResult(AttendanceCodes.UNIT_DAY, null).getMsg(), totalTimeDuration.floatValue()));
            } else {//小时
                if (travelTypeDo.getRoundTimeUnit() != null && totalTimeDuration.floatValue() % (60 * travelTypeDo.getRoundTimeUnit()) > 0) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.MIN_UNIT_HOUR, null).getMsg(), travelTypeDo.getTravelTypeName(), travelTypeDo.getRoundTimeUnit()));
                }
                return Result.ok(totalTimeDuration.floatValue() % 60 > 0 ?
                        String.format(ResponseWrap.wrapResult(AttendanceCodes.UNIT_HOUR_AND_MINUTE, null).getMsg(), totalTimeDuration.longValue() / 60, totalTimeDuration.longValue() % 60) :
                        String.format(ResponseWrap.wrapResult(AttendanceCodes.UNIT_HOUR, null).getMsg(), totalTimeDuration.floatValue() / 60));
            }
        }
    }

    public String getTimeSlot(WaEmpTravelDo empTravel) {
        Long shiftStartTime = empTravel.getShiftStartTime();
        Long shiftEndTime = empTravel.getShiftEndTime();
        Integer periodType = empTravel.getPeriodType().intValue();
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
            return String.format("%s~%s", DateUtil.getDateStrByTimesamp(shiftStartTime), DateUtil.getDateStrByTimesamp(shiftEndTime));
        } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
            return String.format("%s %s~%s %s", DateUtil.getDateStrByTimesamp(empTravel.getStartTime()), DayHalfTypeEnum.getDesc(empTravel.getShalfDay()), DateUtil.getDateStrByTimesamp(empTravel.getEndTime()), DayHalfTypeEnum.getDesc(empTravel.getEhalfDay()));
        } else {
            return String.format("%s~%s", DateUtil.getTimeStrByTimesamp(shiftStartTime), DateUtil.getTimeStrByTimesamp(shiftEndTime));
        }
    }

    @Override
    public List<MyTravelTimeDto> getTravelTimeList(String belongOrgId, Long empid, Long daytime, Long endTime, MyWorkDateShiftDto shiftDto) {
        List<WaEmpTravelDo> travelInfoList = waEmpTravelDo.getTravelInfoList(belongOrgId, empid, daytime, endTime);
        if (CollectionUtils.isNotEmpty(travelInfoList)) {
            Map<Long, WaEmpTravelDo> travelInfoMap = travelInfoList.stream().collect(Collectors.toMap(WaEmpTravelDo::getTravelId, t -> t));
            List<MyTravelTimeDto> dtoList = ObjectConverter.convertList(travelInfoList, MyTravelTimeDto.class);
            for (MyTravelTimeDto row : dtoList) {
                row.setBusinessKey(String.format("%s_%s", row.getTravelId(), BusinessCodeEnum.TRAVEL.getCode()));
                row.setStatusName(ApprovalStatusEnum.getName(row.getStatus()));
                //默认值
                if (shiftDto != null) {
                    row.setTravelTimeTxt(String.format("%s~%s", shiftDto.getStartTimeTxt(), shiftDto.getEndTimeTxt()));
                }
                Float duration = row.getTimeDuration();
                String unitName = TravelTypeUnitEnum.getName(row.getTimeUnit().intValue());
                String minuteName = ResponseWrap.wrapResult(AttendanceCodes.MINUTE, null).getMsg();
                String durationStr = row.getTimeUnit() == 1 ? duration + unitName : (duration % 60 > 0 ? (duration.intValue() / 60 + unitName + duration % 60 + minuteName) : ((duration.intValue() / 60) + unitName));
                row.setTimeDurationTxt(durationStr);
                Short periodType = row.getPeriodType();
                Long startDate = travelInfoMap.get(row.getTravelId()).getShiftStartTime();
                Long endDate = travelInfoMap.get(row.getTravelId()).getShiftEndTime();
                if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(periodType.intValue())) {
                    String start = DateUtil.convertDateTimeToStr(startDate, "HH:mm", true);
                    String end = DateUtil.convertDateTimeToStr(endDate, "HH:mm", true);
                    row.setTravelTimeTxt(String.format("%s~%s", start, end));
                    row.setStartDate(DateUtil.getTimeStrByTimesamp(startDate));
                    row.setEndDate(DateUtil.getTimeStrByTimesamp(endDate));
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType.intValue()) && row.getShalfDay() != null && row.getEhalfDay() != null && row.getTimeDuration() < 1) {
                    if (row.getShalfDay().equals(BaseConst.LEAVE_HALF_SHALF_DAY) && row.getEhalfDay().equals(BaseConst.LEAVE_HALF_SHALF_DAY)) {
                        //row.setTravelTimeTxt("上午");
                        row.setTravelTimeTxt(ResponseWrap.wrapResult(AttendanceCodes.AM, null).getMsg());
                    }
                    if (row.getShalfDay().equals(BaseConst.LEAVE_HALF_EHALF_DAY) && row.getEhalfDay().equals(BaseConst.LEAVE_HALF_EHALF_DAY)) {
                        //row.setTravelTimeTxt("下午");
                        row.setTravelTimeTxt(ResponseWrap.wrapResult(AttendanceCodes.PM, null).getMsg());
                    }
                    row.setStartDate(DateUtil.getTimeStrByTimesamp(startDate));
                    row.setEndDate(DateUtil.getTimeStrByTimesamp(endDate));
                } else {
                    row.setStartDate(DateUtil.getDateStrByTimesamp(startDate));
                    row.setEndDate(DateUtil.getDateStrByTimesamp(endDate));
                }
                row.setApplyTime(row.getCreateTime());
                row.setApplyName(row.getTravelType());
                row.setTravelType(LangParseUtil.getI18nLanguage(row.getI18nTravelTypeName(), row.getTravelType()));
            }
            return dtoList;
        }
        return new ArrayList<>();
    }

    private void changeParam(PageBean pageBean, EmpTravelReqDto dto, Map params) {
        params.put("ifBatch", dto.isIfBatch());
        params.put("belongOrgId", dto.getBelongOrgId());
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            params.put("keywords", dto.getKeywords());
        }
        if (null != dto.getEntityId()) {
            params.put("entityId", dto.getEntityId());
        }
        params.put("empId", dto.getEmpId());
        params.put("startDateTime", dto.getStartDateTime());
        params.put("endDateTime", dto.getEndDateTime());

        List<FilterBean> list = pageBean.getFilterList();
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator<FilterBean> it = list.iterator();
            while (it.hasNext()) {
                FilterBean filterBean = it.next();
                if ("travel_time".equals(filterBean.getField())) {
                    if (StringUtils.isNotBlank(filterBean.getMin())) {
                        params.put("startDateTime", Long.valueOf(filterBean.getMin()));
                    }
                    if (StringUtils.isNotBlank(filterBean.getMax())) {
                        params.put("endDateTime", Long.valueOf(filterBean.getMax()));
                    }
                    it.remove();
                }
                if ("travel_mode".equals(filterBean.getField()) && StringUtil.isNotEmpty(filterBean.getMin())) {
                    params.put("travelMode", filterBean.getMin());
                    it.remove();
                }
            }
            String filter = pageBean.getFilter();
            if (filter != null && filter.contains("orgid")) {
                filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
            }
            params.put("filter", filter);
        }
    }

    public void deleteByBatchId(String tenantId, Long batchTravelId) {
        List<WaEmpTravelDo> waEmpTravelDos = waEmpTravelDo.listByBatchId(tenantId, batchTravelId);
        if (CollectionUtils.isEmpty(waEmpTravelDos)) {
            return;
        }
        waEmpTravelDos.forEach(empTravel -> {
            waEmpTravelDo.delete(empTravel.getTravelId());
            waEmpTravelDaytimeDo.deleteByTravelIds(Collections.singletonList(empTravel.getTravelId()));
        });
    }

    @CDText(exp = {"travelMode:travelModeName" + TextAspect.DICT_TRAVEL_MODE}, classType = EmpTravelDto.class)
    @Override
    public PageResult<EmpTravelDto> getEmpTravelPageListOfPortal(QueryPageBean queryPageBean, BusinessCodeEnum workflowEnum) {
        PageResult<WaEmpTravelDo> pageResult = waEmpTravelDo.getWaEmpTravelListOfPortal(queryPageBean, workflowEnum);
        return convert(pageResult, workflowEnum);
    }

    private PageResult<EmpTravelDto> convert(PageResult<WaEmpTravelDo> pageResult, BusinessCodeEnum workflowEnum) {
        PageResult<EmpTravelDto> dtoPageResult = new PageResult<>();
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaEmpTravelDo> doList = pageResult.getItems();
            List<EmpTravelDto> dtoList = ObjectConverter.convertList(doList, EmpTravelDto.class);
            for (EmpTravelDto item : dtoList) {
                Short timeUnit = item.getTimeUnit();
                Float duration = item.getTimeDuration();
                item.setTimeUnitName(TravelTypeUnitEnum.getName(timeUnit.intValue()));
                if (timeUnit == 2) {
                    BigDecimal v = new BigDecimal(String.valueOf(duration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    item.setTimeDuration(v.floatValue());
                }
                Integer status = item.getStatus();
                if (status != null) {
                    item.setStatusName(ApprovalStatusEnum.getName(status));
                }
                item.setBusinessKey(String.format("%s_%s", item.getRevokeId(), workflowEnum.getCode()));
                Integer periodType = item.getPeriodType() != null ? Integer.valueOf(item.getPeriodType()) : null;
                Long shiftStartTime = item.getShiftStartTime();
                Long shiftEndTime = item.getShiftEndTime();
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    item.setStartDate(DateUtil.getDateStrByTimesamp(shiftStartTime));
                    item.setEndDate(DateUtil.getDateStrByTimesamp(shiftEndTime));
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    item.setStartDate(String.format("%s%s", DateUtil.getDateStrByTimesamp(item.getStartTime()), DayHalfTypeEnum.getDesc(item.getShalfDay())));
                    item.setEndDate(String.format("%s%s", DateUtil.getDateStrByTimesamp(item.getEndTime()), DayHalfTypeEnum.getDesc(item.getEhalfDay())));
                } else {
                    item.setStartDate(DateUtil.getTimeStrByTimesamp(shiftStartTime));
                    item.setEndDate(DateUtil.getTimeStrByTimesamp(shiftEndTime));
                }
                //地点
                SysUnitCity sysUnitCity = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, item.getProvince());
                SysUnitCity cityObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_CITY_KEY, item.getCity());
                if (sysUnitCity != null) {
                    item.setProvinceName(sysUnitCity.getChnName());
                }
                if (cityObj != null) {
                    item.setCityName(cityObj.getChnName());
                }
                item.setTravelType(LangParseUtil.getI18nLanguage(item.getI18nTravelTypeName(), item.getTravelType()));
            }
            dtoPageResult.setItems(dtoList);
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setTotal(pageResult.getTotal());
        }
        return dtoPageResult;
    }
}