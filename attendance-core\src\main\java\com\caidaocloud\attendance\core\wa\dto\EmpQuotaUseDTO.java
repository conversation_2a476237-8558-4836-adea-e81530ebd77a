package com.caidaocloud.attendance.core.wa.dto;

import java.io.Serializable;

/**
 * 员工假期配额
 */
public class EmpQuotaUseDTO implements Serializable {
    private static final long serialVersionUID = 3996208185520283602L;
    private Integer empQuotaId;
    private Integer quotaSettingId;//配额ID
    private Float quotaDay;//总配额
    private Float keQuota;//可用配额
    private Float usedDay;//已使用
    private Long startDate;//配额有效期开始日期
    private Long lastDate;//配额有效期结束日期
    private Integer id;//主键ID
    private Integer year;//年份
    private Integer type;//配额类型 0 留存 1 本年 2 调整
    private Integer sort;//配额扣减顺序

    public Integer getQuotaSettingId() {
        return quotaSettingId;
    }

    public void setQuotaSettingId(Integer quotaSettingId) {
        this.quotaSettingId = quotaSettingId;
    }

    public Float getQuotaDay() {
        return quotaDay;
    }

    public void setQuotaDay(Float quotaDay) {
        this.quotaDay = quotaDay;
    }

    public Float getKeQuota() {
        return keQuota;
    }

    public void setKeQuota(Float keQuota) {
        this.keQuota = keQuota;
    }

    public Float getUsedDay() {
        return usedDay;
    }

    public void setUsedDay(Float usedDay) {
        this.usedDay = usedDay;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getLastDate() {
        return lastDate;
    }

    public void setLastDate(Long lastDate) {
        this.lastDate = lastDate;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getEmpQuotaId() {
        return empQuotaId;
    }

    public void setEmpQuotaId(Integer empQuotaId) {
        this.empQuotaId = empQuotaId;
    }
}
