package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LeaveCancelTimePeriodDto {
    private Long leaveCancelId;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
    @ApiModelProperty("半天开始")
    private String shalfDay;
    @ApiModelProperty("半天结束")
    private String ehalfDay;
    @ApiModelProperty("日期类型")
    private Short periodType;
    @ApiModelProperty("时长单位")
    private Integer timeUnit;
    @ApiModelProperty("时长")
    private Float timeDuration;
    @ApiModelProperty("实际开始时间")
    private Long shiftStartTime;
    @ApiModelProperty("实际结束时间")
    private Long shiftEndTime;
    @ApiModelProperty("审批状态")
    private Short status;
    @ApiModelProperty("文件Id")
    private String fileId;
    @ApiModelProperty("文件名称")
    private String fileName;
    @ApiModelProperty("事由")
    private String reason;
    @ApiModelProperty("销假类型Id")
    private Long typeId;
}
