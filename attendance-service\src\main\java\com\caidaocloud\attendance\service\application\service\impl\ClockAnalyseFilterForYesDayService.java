package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.attendance.service.application.dto.clock.ClockAnalyseCalDto;
import com.caidaocloud.attendance.service.application.dto.clock.ClockAnalyseFilterAbstractDto;
import com.caidaocloud.attendance.service.application.dto.clock.FilterBelongYesRegDto;
import com.caidaocloud.attendance.service.application.dto.clock.YesterdayClockFilterContext;
import com.caidaocloud.attendance.service.application.enums.ClockAnalyseFilterTypeEnum;
import com.caidaocloud.attendance.service.application.service.IClockAnalyseFilterService;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClockAnalyseFilterForYesDayService implements IClockAnalyseFilterService {
    @Override
    public String getFilterType() {
        return ClockAnalyseFilterTypeEnum.YESTERDAY.getCode();
    }

    /**
     * 筛选出归属于前一天的打卡数据
     *
     * @param absFilterDto
     * @return
     */
    @Override
    public List<WaRegisterRecordDo> doFilter(ClockAnalyseFilterAbstractDto absFilterDto) {
        FilterBelongYesRegDto filterDto = (FilterBelongYesRegDto) absFilterDto;
        if (CollectionUtils.isEmpty(filterDto.getRegList())) {
            return Collections.emptyList();
        }
        YesterdayClockFilterContext context = YesterdayClockFilterContext.doBuild(filterDto);
        if (context.isYesterdayWorkday()) {
            return processYesterdayWorkday(context);
        } else {
            return processYesterdayNonWorkday(context);
        }
    }

    /**
     * 处理前一天是工作日的场景
     *
     * @param context
     * @return
     */
    private List<WaRegisterRecordDo> processYesterdayWorkday(YesterdayClockFilterContext context) {
        // 有休假
        Long leaveEndTime = context.getDataCacheDto().doGetMaxLtEndTime(context.getEmpId(), context.getYesterday());
        if (null != leaveEndTime) {
            return processYesterdayWorkdayWithLeave(context, leaveEndTime);
        }

        // 无休假
        if (context.isTodayWorkday()) {
            return processYesterdayWorkdayToTodayWorkday(context);
        } else {
            return processYesterdayWorkdayToTodayNonWorkday(context);
        }
    }

    /**
     * 前一天工作日有休假的处理
     *
     * @param context
     * @param leaveEndTime
     * @return
     */
    private List<WaRegisterRecordDo> processYesterdayWorkdayWithLeave(YesterdayClockFilterContext context, Long leaveEndTime) {
        if (leaveEndTime >= context.getYesterdayShiftEndTime()) {
            if (context.isYesterdayCrossNight()) {
                return context.getRegList().stream()
                        .filter(wrrd -> wrrd.getRegDateTime() <= context.getYesterdayShiftEndTime())
                        .collect(Collectors.toList());
            } else {
                return Lists.newArrayList();
            }
        }
        if (context.isTodayWorkday()) {
            return processYesterdayWorkdayToTodayWorkday(context);
        } else {
            return processYesterdayWorkdayToTodayNonWorkday(context);
        }
    }

    /**
     * 前一天工作日 -> 当日工作日
     *
     * @param context
     * @return
     */
    private List<WaRegisterRecordDo> processYesterdayWorkdayToTodayWorkday(YesterdayClockFilterContext context) {
        // 有无交集判断
        if (context.getYesterdayOffDutyEndTime() <= context.getTodayOnDutyStartTime()) {
            return context.getRegList().stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= context.getYesterdayOffDutyEndTime())
                    .collect(Collectors.toList());
        }
        // 有交集：差值判断
        return context.getRegList().stream().filter(record -> {
            long regDateTime = record.getRegDateTime();
            if (regDateTime <= context.getTodayOnDutyStartTime()) {
                return true;
            }
            long diffToYesterday = (context.getYesterdayOvertimeEndTime() != null && context.getYesterdayOvertimeEndTime() >= context.getYesterdayShiftEndTime())
                    ? Math.abs(regDateTime - context.getYesterdayOvertimeEndTime())
                    : Math.abs(regDateTime - context.getYesterdayShiftEndTime());
            long diffToToday = (context.getTodayOvertimeStartTime() != null && context.getTodayOvertimeStartTime() < context.getTodayShiftStartTime())
                    ? Math.abs(regDateTime - context.getTodayOvertimeStartTime())
                    : Math.abs(regDateTime - context.getTodayShiftStartTime());
            return diffToYesterday <= diffToToday;
        }).collect(Collectors.toList());
    }

    /**
     * 前一天工作日 -> 当日非工作日
     *
     * @param context
     * @return
     */
    private List<WaRegisterRecordDo> processYesterdayWorkdayToTodayNonWorkday(YesterdayClockFilterContext context) {
        // 当日无加班
        if (null == context.getTodayOvertimeStartTime()) {
            return context.getRegList().stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= context.getYesterdayOffDutyEndTime())
                    .collect(Collectors.toList());
        }
        // 当日有加班
        long todayOvertimeClockStartTime = Optional.ofNullable(
                        ClockAnalyseCalDto.doGetShiftMinOtStartTime(context.getEmpId(), context.getClockDate(), context.getDataCacheDto(), context.getTodayShiftDef()))
                .orElse(context.getTodayOvertimeStartTime());
        // 无交集
        if (context.getYesterdayOffDutyEndTime() <= todayOvertimeClockStartTime) {
            return context.getRegList().stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= context.getYesterdayOffDutyEndTime())
                    .collect(Collectors.toList());
        }
        // 有交集：差值判断
        return context.getRegList().stream().filter(record -> {
            long regDateTime = record.getRegDateTime();
            if (regDateTime <= todayOvertimeClockStartTime) {
                return true;
            }
            long diffToYes = (context.getYesterdayOvertimeEndTime() != null && context.getYesterdayOvertimeEndTime() >= context.getYesterdayShiftEndTime())
                    ? Math.abs(regDateTime - context.getYesterdayOvertimeEndTime())
                    : Math.abs(regDateTime - context.getYesterdayShiftEndTime());
            long diffToToday = Math.abs(regDateTime - context.getTodayOvertimeStartTime());
            return diffToYes <= diffToToday;
        }).collect(Collectors.toList());
    }

    /**
     * 处理前一天是非工作日的场景
     *
     * @param context
     * @return
     */
    private List<WaRegisterRecordDo> processYesterdayNonWorkday(YesterdayClockFilterContext context) {
        // 无加班
        if (null == context.getYesterdayOvertimeEndTime()) {
            return Lists.newArrayList();
        }
        // 有加班
        Long yesterdayOvertimeClockEndTime = Optional.ofNullable(
                        ClockAnalyseCalDto.doGetShiftMaxOtEndTime(context.getEmpId(), context.getYesterday(), context.getDataCacheDto(), context.getYesterdayShiftDef()))
                .orElse(context.getYesterdayOvertimeEndTime());
        if (context.isTodayWorkday()) {
            return processYesterdayNonWorkdayToTodayWorkday(context, yesterdayOvertimeClockEndTime);
        } else {
            return processYesterdayNonWorkdayToTodayNonWorkday(context, yesterdayOvertimeClockEndTime);
        }
    }

    /**
     * 前一天非工作日 -> 当日工作日
     *
     * @param context
     * @param yesterdayOvertimeClockEndTime
     * @return
     */
    private List<WaRegisterRecordDo> processYesterdayNonWorkdayToTodayWorkday(YesterdayClockFilterContext context,
                                                                              long yesterdayOvertimeClockEndTime) {
        // 无交集
        if (yesterdayOvertimeClockEndTime <= context.getTodayOnDutyStartTime()) {
            return context.getRegList().stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= yesterdayOvertimeClockEndTime).collect(Collectors.toList());
        }
        // 有交集：差值判断
        return context.getRegList().stream().filter(record -> {
            long regDateTime = record.getRegDateTime();
            if (regDateTime <= context.getTodayOnDutyStartTime()) {
                return true;
            }
            long diffToYesterday = Math.abs(regDateTime - context.getYesterdayOvertimeEndTime());
            long diffToToday = (context.getTodayOvertimeStartTime() != null && context.getTodayOvertimeStartTime() < context.getTodayShiftStartTime())
                    ? Math.abs(regDateTime - context.getTodayOvertimeStartTime())
                    : Math.abs(regDateTime - context.getTodayShiftStartTime());
            return diffToYesterday <= diffToToday;
        }).collect(Collectors.toList());
    }

    /**
     * 前一天非工作日 -> 当日非工作日
     *
     * @param context
     * @param yesterdayOvertimeClockEndTime
     * @return
     */
    private List<WaRegisterRecordDo> processYesterdayNonWorkdayToTodayNonWorkday(YesterdayClockFilterContext context,
                                                                                 long yesterdayOvertimeClockEndTime) {
        // 当日无加班
        if (null == context.getTodayOvertimeStartTime()) {
            return context.getRegList().stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= yesterdayOvertimeClockEndTime)
                    .collect(Collectors.toList());
        }
        // 当日有加班
        long todayOvertimeClockStartTime = Optional.ofNullable(
                        ClockAnalyseCalDto.doGetShiftMinOtStartTime(context.getEmpId(), context.getClockDate(), context.getDataCacheDto(), context.getTodayShiftDef()))
                .orElse(context.getTodayOvertimeStartTime());
        // 无交集
        if (yesterdayOvertimeClockEndTime <= todayOvertimeClockStartTime) {
            return context.getRegList().stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= yesterdayOvertimeClockEndTime)
                    .collect(Collectors.toList());
        }
        // 有交集：差值判断
        return context.getRegList().stream().filter(record -> {
            long regDateTime = record.getRegDateTime();
            if (regDateTime <= todayOvertimeClockStartTime) {
                return true;
            }
            long diffToYesterday = Math.abs(regDateTime - context.getYesterdayOvertimeEndTime());
            long diffToToday = Math.abs(regDateTime - context.getTodayOvertimeStartTime());
            return diffToYesterday <= diffToToday;
        }).collect(Collectors.toList());
    }
}
