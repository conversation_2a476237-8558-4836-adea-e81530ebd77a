package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaParseGroupDo;

import java.util.List;

/**
 * 考勤分析规则
 *
 * <AUTHOR>
 * @Date 2021/9/21
 */
public interface IWaParseGroupRepository {
    List<WaParseGroupDo> getWaParseGroupList(String belongOrgid, List<Long> empIds, Long startDate, Long endDate);

    WaParseGroupDo selectById(Integer parseGroupId);
}
