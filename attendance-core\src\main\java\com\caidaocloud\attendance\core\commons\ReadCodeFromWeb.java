package com.caidaocloud.attendance.core.commons;

import com.caidao1.system.mybatis.model.SysUnitCity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * 从国家统计局网站行政区
 */
@Component
public class ReadCodeFromWeb {
    //设置utf-8发现有部分字符有乱码
    private static final String CHARSET = "GBK";

    private static final String BASEURL = "http://www.stats.gov.cn/tjsj/tjbz/tjyqhdmhcxhfdm/2018/";

    private static RedisTemplate redisTemplate;

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        ReadCodeFromWeb.redisTemplate = redisTemplate;
    }

    /**
     * 读省的信息
     *
     * @param
     * @throws Exception
     */
    public static List<SysUnitCity> readProInfo(String baseUrl) throws Exception {
        if (StringUtils.isBlank(baseUrl)) {
            baseUrl = BASEURL;
        }
        if (redisTemplate.opsForValue().get(baseUrl) != null) {
            return (List<SysUnitCity>) redisTemplate.opsForValue().get(baseUrl);
        }
        List<SysUnitCity> cityList = new ArrayList<>();
        String url = baseUrl + "index.html";

        String str = getContent(url).toUpperCase();
        String[] arrs = str.split("<A");


        for (String s : arrs) {
            if (s.indexOf("HREF") != -1 && s.indexOf(".HTML") != -1) {
                String proUrl = s.substring(7, s.indexOf("'>"));
                String name = s.substring(s.indexOf("'>") + 2, s.indexOf("<BR/>"));
                String code = readShi(proUrl, cityList, baseUrl);
                SysUnitCity unitCity = new SysUnitCity();
                unitCity.setCode(code);
                unitCity.setChnName(name);
                unitCity.setType(1);
                unitCity.setCountryId(1L);
                unitCity.setStatus(1);
                unitCity.setIsParent(true);
                unitCity.setCityPid(0L);
                cityList.add(unitCity);
            }
        }
        Collections.sort(cityList, (c1, c2) -> {
            return c1.getType().intValue() > c2.getType().intValue() ? 1 : c1.getType().intValue() == c2.getType().intValue() ? 0 : -1;
        });
        redisTemplate.opsForValue().set(baseUrl, cityList);
        return cityList;
    }

    /**
     * 读市的数据
     *
     * @param
     * @throws Exception
     */
    public static String readShi(String url, List cityList, String baseUrl) throws Exception {
        String pCode = null;//上级行政代码 由于该网页省级没有显示行政代码，所以手动获取
        String content = getContent(baseUrl + url).toUpperCase();
        String[] citys = content.split("CITYTR");
        for (int c = 1, len = citys.length; c < len; c++) {
            String[] strs = citys[c].split("<A HREF='");
            String cityUrl = null;
            String cityCode = null;
            SysUnitCity unitCity = new SysUnitCity();
            unitCity.setStatus(1);
            unitCity.setCountryId(1L);
            unitCity.setIsParent(true);
            unitCity.setType(2);
            for (int si = 1; si < 3; si++) {
                if (si == 1) {//取链接和编码
                    cityUrl = strs[si].substring(0, strs[si].indexOf("'>"));
                    cityCode = strs[si].substring(strs[si].indexOf("'>") + 2, strs[si].indexOf("</A>")).substring(0, 6);
                    if (pCode == null) {
                        pCode = cityCode.substring(0, 2) + "0000";
                    }
                    unitCity.setCode(cityCode);
                } else {
                    unitCity.setChnName(strs[si].substring(strs[si].indexOf("'>") + 2, strs[si].indexOf("</A>")));
                }
            }
            unitCity.setCityPid(1L);
            cityList.add(unitCity);
            readXian(cityUrl, cityCode, cityList, baseUrl);
        }
        return pCode;
    }

    /**
     * 读县的数据
     *
     * @param url
     * @throws Exception
     */
    public static void readXian(String url, String pCode, List cityList, String baseUrl) throws Exception {
        String content = getContent(baseUrl + url).toUpperCase();
        String[] citys = content.split("COUNTYTR");
        for (int i = 1; i < citys.length; i++) {
            SysUnitCity unitCity = new SysUnitCity();
            unitCity.setStatus(1);
            unitCity.setType(3);
            unitCity.setCountryId(1L);
            unitCity.setCityPid(1L);
            //特殊县/市辖区处理
            if (citys[i].indexOf("<A HREF='") == -1) {
                unitCity.setCode(citys[i].substring(6, 12));
                unitCity.setChnName(citys[i].substring(citys[i].indexOf("</TD><TD>") + 9, citys[i].lastIndexOf("</TD>")));
            } else {
                String[] strs = citys[i].split("<A HREF='");
                for (int si = 1; si < 3; si++) {
                    if (si == 1) {//取链接和编码
                        String cityCode = strs[si].substring(strs[si].indexOf("'>") + 2, strs[si].indexOf("</A>")).substring(0, 6);
                        unitCity.setCode(cityCode);
                    } else {
                        unitCity.setChnName(strs[si].substring(strs[si].indexOf("'>") + 2, strs[si].indexOf("</A>")));
                    }
                }
            }
            cityList.add(unitCity);
        }
    }


    //获取网页的内容
    public static String getContent(String strUrl) throws Exception {
        try {
            URL url = new URL(strUrl);
            BufferedReader br = new BufferedReader(new InputStreamReader(url.openStream(), Charset.forName(CHARSET)));
            String s = "";
            StringBuffer sb = new StringBuffer("");
            while ((s = br.readLine()) != null) {
                sb.append(s);
            }


            br.close();
            return sb.toString();
        } catch (Exception e) {
            System.out.println("can't open url:" + strUrl);
            throw e;
        }
    }
}

