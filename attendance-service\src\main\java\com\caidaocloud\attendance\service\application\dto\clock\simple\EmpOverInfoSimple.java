package com.caidaocloud.attendance.service.application.dto.clock.simple;

import com.caidaocloud.attendance.core.wa.dto.EmpOverInfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 简化的员工加班信息对象
 *
 * <AUTHOR>
 * @Date 2025/01/08
 */
@Data
public class EmpOverInfoSimple {
    /**
     * 员工ID
     */
    private Long empid;

    /**
     * 加班单据ID
     */
    private Integer ot_id;

    /**
     * 加班时长
     */
    private Integer ot_duration;

    /**
     * 开始时间
     */
    private Long start_time;

    /**
     * 结束时间
     */
    private Long end_time;

    /**
     * 实际归属日期
     */
    private Long real_date;

    /**
     * 日期类型
     */
    private Integer date_type;

    /**
     * 补偿类型 CompensateTypeEnum
     */
    private Integer compensate_type;

    /**
     * 加班归属日期
     */
    private Long belongDate;

    /**
     * 表示取哪天的签到记录联动分析
     */
    private Long regdate;

    /**
     * 从完整对象转换为简化对象
     *
     * @param source 源对象
     * @return 简化对象
     */
    public static EmpOverInfoSimple fromFull(EmpOverInfo source) {
        if (source == null) {
            return null;
        }

        EmpOverInfoSimple simple = new EmpOverInfoSimple();
        simple.setEmpid(source.getEmpid());
        simple.setOt_id(source.getOt_id());
        simple.setOt_duration(source.getOt_duration());
        simple.setStart_time(source.getStart_time());
        simple.setEnd_time(source.getEnd_time());
        simple.setReal_date(source.getReal_date());
        simple.setDate_type(source.getDate_type());
        simple.setCompensate_type(source.getCompensate_type());
        simple.setBelongDate(source.getBelongDate());
        simple.setRegdate(source.getRegdate());

        return simple;
    }

    /**
     * 批量转换
     */
    public static List<EmpOverInfoSimple> fromFullList(List<EmpOverInfo> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return new ArrayList<>();
        }

        return sourceList.stream()
                .map(EmpOverInfoSimple::fromFull)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
} 