package com.caidaocloud.attendance.service.application.dto.clock;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 打卡分析定时任务计算参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ClockAnalyseTaskParamDto {
    private String belongOrgId;
    private Long date;// 打卡分析日期（分析一天时使用）
    private Long startDate;// 打卡分析开始日期（分析多天时使用）
    private Long endDate;// 打卡分析结束日期（分析多天时使用）
    private boolean ifMultiDay;// 是否分析多天

    public ClockAnalyseTaskParamDto(String belongOrgId, Long date) {
        this.belongOrgId = belongOrgId;
        this.date = date;
    }
}
