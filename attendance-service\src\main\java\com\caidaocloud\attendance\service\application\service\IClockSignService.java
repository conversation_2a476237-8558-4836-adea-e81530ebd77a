package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.interfaces.dto.BatchClockAnalyseDto;
import com.caidaocloud.attendance.service.interfaces.dto.BatchClockAnalyseResultDto;

import java.util.List;

/**
 * 打卡服务
 *
 * @Author: <PERSON><PERSON>
 * @Date: 2021/6/16 11:26
 * @Description:
 **/
public interface IClockSignService {
    /**
     * 打卡分析
     *
     * @param belongOrgId 租户ID
     * @param empIds      员工ID集合
     * @param date        打卡日期
     * @param type        打卡类型：1 GPS签到 2 扫码签到 3 外勤签到  4蓝牙签到  5 WIFI签到  6 补打卡
     */
    void analyseRegisterRecord(String belongOrgId, List<Long> empIds, Long date, Integer type);

    /**
     * 批量打卡分析
     *
     * @param dto
     */
    BatchClockAnalyseResultDto analyseByDateRange(BatchClockAnalyseDto dto);

    /**
     * 执行批量打卡分析
     *
     * @param dto
     */
    void doExeBatchAnalyse(BatchClockAnalyseDto dto);
}
