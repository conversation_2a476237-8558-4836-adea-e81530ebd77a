package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.attendance.service.application.dto.clock.ClockAnalyseFilterAbstractDto;
import com.caidaocloud.attendance.service.application.dto.clock.FilterBelongTodayRegDto;
import com.caidaocloud.attendance.service.application.dto.clock.TodayClockFilterContext;
import com.caidaocloud.attendance.service.application.enums.ClockAnalyseFilterTypeEnum;
import com.caidaocloud.attendance.service.application.service.IClockAnalyseFilterService;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClockAnalyseFilterForTodayService implements IClockAnalyseFilterService {
    @Override
    public String getFilterType() {
        return ClockAnalyseFilterTypeEnum.TODAY.getCode();
    }

    /**
     * 筛选出归属于当日的打卡数据
     *
     * @param absFilterDto
     * @return
     */
    @Override
    public List<WaRegisterRecordDo> doFilter(ClockAnalyseFilterAbstractDto absFilterDto) {
        FilterBelongTodayRegDto filterDto = (FilterBelongTodayRegDto) absFilterDto;
        if (CollectionUtils.isEmpty(filterDto.getRegList())) {
            return Collections.emptyList();
        }
        TodayClockFilterContext context = TodayClockFilterContext.doBuild(filterDto);
        // 次日未排班
        if (context.getNextDayShiftDo() == null) {
            return context.getRegList();
        }
        // 当日未排班
        if (context.getTodayShiftDo() == null) {
            return processTodayNoShift(context);
        }
        // 当日、次日有排班
        if (context.isTodayWorkday()) {
            return processTodayWorkday(context);
        } else {
            return processTodayNonWorkday(context);
        }
    }

    /**
     * 处理当日未排班的场景
     *
     * @param context
     * @return
     */
    private List<WaRegisterRecordDo> processTodayNoShift(TodayClockFilterContext context) {
        long limitTime = (null != context.getNextDayOvertimeClockStartTime() && context.getNextDayOvertimeClockStartTime() < context.getNextDayOnDutyStartTime())
                ? context.getNextDayOvertimeClockStartTime()
                : context.getNextDayOnDutyStartTime();
        return context.getRegList().stream()
                .filter(record -> record.getRegDateTime() <= limitTime)
                .collect(Collectors.toList());
    }

    /**
     * 处理当天工作日的场景
     */
    private List<WaRegisterRecordDo> processTodayWorkday(TodayClockFilterContext context) {
        if (context.isNextDayWorkday()) {
            return processTodayWorkdayToNextDayWorkday(context);
        } else {
            return processTodayWorkdayToNextDayNonWorkday(context);
        }
    }

    /**
     * 当天工作日 -> 次日工作日
     *
     * @param context
     * @return
     */
    private List<WaRegisterRecordDo> processTodayWorkdayToNextDayWorkday(TodayClockFilterContext context) {
        // 有无交集判断
        if (context.getTodayOffDutyEndTime() <= context.getNextDayOnDutyStartTime()) {
            // 无交集
            return context.getRegList().stream()
                    .filter(record -> record.getRegDateTime() <= context.getTodayOffDutyEndTime())
                    .collect(Collectors.toList());
        }
        // 有交集：差值判断
        return context.getRegList().stream().filter(record -> {
            long regDateTime = record.getRegDateTime();
            if (regDateTime <= context.getNextDayOnDutyStartTime()) {
                return true;
            }
            long diffToToday = (null != context.getTodayOvertimeEndTime() && context.getTodayOvertimeEndTime() >= context.getTodayShiftEndTime())
                    ? Math.abs(regDateTime - context.getTodayOvertimeEndTime())
                    : Math.abs(regDateTime - context.getTodayShiftEndTime());
            long diffToNextDay = (context.getNextDayOvertimeStartTime() != null && context.getNextDayOvertimeStartTime() < context.getNextDayShiftStartTime())
                    ? Math.abs(regDateTime - context.getNextDayOvertimeStartTime())
                    : Math.abs(regDateTime - context.getNextDayShiftStartTime());
            return diffToToday <= diffToNextDay;
        }).collect(Collectors.toList());
    }

    /**
     * 当天工作日 -> 次日非工作日
     *
     * @param context
     * @return
     */
    private List<WaRegisterRecordDo> processTodayWorkdayToNextDayNonWorkday(TodayClockFilterContext context) {
        // 次日无加班
        if (null == context.getNextDayOvertimeStartTime()) {
            return context.getRegList().stream()
                    .filter(record -> record.getRegDateTime() <= context.getTodayOffDutyEndTime())
                    .collect(Collectors.toList());
        }
        // 次日有加班
        // 无交集
        if (context.getTodayOffDutyEndTime() <= context.getNextDayOvertimeClockStartTime()) {
            return context.getRegList().stream()
                    .filter(record -> record.getRegDateTime() <= context.getTodayOffDutyEndTime())
                    .collect(Collectors.toList());
        }
        // 有交集：差值判断
        return context.getRegList().stream().filter(record -> {
            long regDateTime = record.getRegDateTime();
            if (regDateTime <= context.getNextDayOvertimeClockStartTime()) {
                return true;
            }
            long diffToToday = (null != context.getTodayOvertimeEndTime() && context.getTodayOvertimeEndTime() >= context.getTodayShiftEndTime())
                    ? Math.abs(regDateTime - context.getTodayOvertimeEndTime())
                    : Math.abs(regDateTime - context.getTodayShiftEndTime());
            long diffToNextDay = Math.abs(regDateTime - context.getNextDayOvertimeStartTime());
            return diffToToday <= diffToNextDay;
        }).collect(Collectors.toList());
    }

    /**
     * 处理当天非工作日的场景
     *
     * @param context
     * @return
     */
    private List<WaRegisterRecordDo> processTodayNonWorkday(TodayClockFilterContext context) {
        if (context.getTodayOvertimeEndTime() == null) {
            // 当天无加班
            return processTodayNonWorkdayNoOvertime(context);
        } else {
            // 当天有加班
            return processTodayNonWorkdayWithOvertime(context);
        }
    }

    /**
     * 当天非工作日无加班的处理
     *
     * @param context
     * @return
     */
    private List<WaRegisterRecordDo> processTodayNonWorkdayNoOvertime(TodayClockFilterContext context) {
        if (context.isNextDayWorkday()) {
            // 次日工作日
            return context.getRegList().stream()
                    .filter(record -> record.getRegDateTime() <= context.getNextDayOnDutyStartTime())
                    .collect(Collectors.toList());
        } else {
            // 次日非工作日
            if (null == context.getNextDayOvertimeStartTime()) {
                // 次日无加班
                return context.getRegList();
            }
            // 次日有加班
            return context.getRegList().stream()
                    .filter(record -> record.getRegDateTime() <= context.getNextDayOvertimeClockStartTime())
                    .collect(Collectors.toList());
        }
    }

    /**
     * 当天非工作日有加班的处理
     *
     * @param context
     * @return
     */
    private List<WaRegisterRecordDo> processTodayNonWorkdayWithOvertime(TodayClockFilterContext context) {
        if (context.isNextDayWorkday()) {
            return processTodayNonWorkdayOvertimeToNextDayWorkday(context, context.getTodayOvertimeClockEndTime());
        } else {
            return processTodayNonWorkdayOvertimeToNextDayNonWorkday(context, context.getTodayOvertimeClockEndTime());
        }
    }

    /**
     * 当天非工作日有加班 -> 次日工作日
     *
     * @param context
     * @param todayOvertimeClockEndTime
     * @return
     */
    private List<WaRegisterRecordDo> processTodayNonWorkdayOvertimeToNextDayWorkday(TodayClockFilterContext context,
                                                                                    Long todayOvertimeClockEndTime) {
        // 无交集
        if (todayOvertimeClockEndTime <= context.getNextDayOnDutyStartTime()) {
            return context.getRegList();
        }
        // 有交集：差值判断
        return context.getRegList().stream().filter(record -> {
            long regDateTime = record.getRegDateTime();
            if (regDateTime <= context.getNextDayOnDutyStartTime()) {
                return true;
            }
            long diffToToday = Math.abs(regDateTime - context.getTodayOvertimeEndTime());
            long diffToNextDay = (context.getNextDayOvertimeStartTime() != null && context.getNextDayOvertimeStartTime() < context.getNextDayShiftStartTime())
                    ? Math.abs(regDateTime - context.getNextDayOvertimeStartTime())
                    : Math.abs(regDateTime - context.getNextDayShiftStartTime());
            return diffToToday <= diffToNextDay;
        }).collect(Collectors.toList());
    }

    /**
     * 当天非工作日有加班 -> 次日非工作日
     *
     * @param context
     * @param todayOvertimeClockEndTime
     * @return
     */
    private List<WaRegisterRecordDo> processTodayNonWorkdayOvertimeToNextDayNonWorkday(TodayClockFilterContext context,
                                                                                       Long todayOvertimeClockEndTime) {
        // 次日无加班
        if (null == context.getNextDayOvertimeStartTime()) {
            return context.getRegList();
        }
        // 次日有加班
        // 无交集
        if (todayOvertimeClockEndTime <= context.getNextDayOvertimeClockStartTime()) {
            return context.getRegList();
        }
        // 有交集：差值判断
        return context.getRegList().stream().filter(record -> {
            long regDateTime = record.getRegDateTime();
            if (regDateTime <= context.getNextDayOvertimeClockStartTime()) {
                return true;
            }
            long diffToToday = Math.abs(regDateTime - context.getTodayOvertimeEndTime());
            long diffToNextDay = Math.abs(regDateTime - context.getNextDayOvertimeStartTime());
            return diffToToday <= diffToNextDay;
        }).collect(Collectors.toList());
    }
}
