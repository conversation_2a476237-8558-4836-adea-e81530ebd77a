package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.wa.mybatis.model.WaOvertimeType;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.domain.repository.IOverTimeTypeRepository;
import com.caidaocloud.util.ObjectConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 加班类型
 *
 * @Author: Aaron.Chen
 * @Date: 2021/7/14 19:19
 * @Description:
 **/
@Slf4j
@Data
@Service
public class OverTimeTypeDo {
    private Integer overtimeTypeId;
    private Integer overtimeType;
    private Integer dateType;
    private Integer compensateType;
    private Integer offUnit;
    private Float overtimeNum;
    private Float offNum;
    private Boolean isTimeoutCheck;
    private String belongOrgid;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private Integer maxOvertimeNum;
    private Float minApplyNum;
    private Integer parseRule;
    private Float minUnit;
    private Boolean isOpenTimeControl;
    private Integer timeControlType;
    private Float controlTimeDuration;
    private Boolean isUploadFile;
    private Float minFileCheckTime;
    private Integer usefulTime;
    private Integer usefulTimeUnit;
    private Boolean isPreTimecontrol;
    private Integer preControlType;
    private Float preControlDuration;
    private String description;
    private Boolean lvParse;
    private Float minOvertimeUnit;
    @ApiModelProperty("有效时长计算： 1 按审批（申请）时长、2 取加班时间段和打卡时间段的交集、3 按打卡时长计算")
    private Integer validTimeCalType;
    @ApiModelProperty("有效时长上限")
    private Float maxValidTime;
    @ApiModelProperty("时效控制时间单位")
    private Integer controlTimeUnit;
    @ApiModelProperty("有效打卡类型 1:同种打卡类型 2:所有打卡类型")
    private Integer validPunchType;
    @ApiModelProperty("加班次数限制开关：true/false,开/关")
    private Boolean overtimeControl;
    @ApiModelProperty("是否关联出差单：true/false,开/关")
    private Boolean isOpenTravel;
    private String typeName;
    private Long startDate;
    private Long endDate;
    private Integer roundingRule;
    @ApiModelProperty("加班时长计算 1:申请时长与打卡时长取小值 2:以打卡时长为准")
    private Integer overtimeCalType;
    private Long ruleId;
    @ApiModelProperty("撤销是否需要走审批流：true是/false否，默认值false")
    private Boolean revokeWorkflow;
    @ApiModelProperty("审批通过单据是否允许撤销：true是/false否，默认值false")
    private Boolean revokePassed;
    @ApiModelProperty("撤销是否需要走审批流,允许撤销的状态多选，逗号分隔：1审批中2已通过")
    private String revokeAllowStatus;
    @ApiModelProperty("最小加班单位时间单位:1分钟2小时")
    private Integer minOvertimeUnitType;
    private String i18nTypeName;
    @ApiModelProperty("是否默认：是/否（true/false）,默认值false")
    private Boolean defaultType;
    /**
     * 有效时长计算单位：SECOND 秒，MINUTE 分钟
     */
    private String validTimeCalUnit;

    @Autowired
    private IOverTimeTypeRepository overTimeTypeRepository;

    public void deleteOtTypeByIds(List<Integer> otIds) {
        if (CollectionUtils.isNotEmpty(otIds)) {
            overTimeTypeRepository.deleteOtTypeByIds(otIds);
        }
    }

    public void deleteById(Integer id) {
        overTimeTypeRepository.deleteOtTypeById(id);
    }

    public List<OverTimeTypeDo> getOtTypes(String belongOrgId, Integer waGroupId, Long date, Boolean summary) {
        List<WaOvertimeType> list = overTimeTypeRepository.getOtTypes(belongOrgId, waGroupId, date, summary);
        return ObjectConverter.convertList(list, OverTimeTypeDo.class);
    }

    public Float getOtTypeMinOvertimeUnit(String belongOrgId, Integer waGroupId, Integer dateType, Integer compensateType, Long date) {
        List<WaOvertimeType> list = overTimeTypeRepository.getOtTypes(belongOrgId, waGroupId, date, null);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Optional<Float> optional = list.stream()
                .filter(l -> l.getDateType().equals(dateType) && l.getCompensateType().equals(compensateType) && l.getMinOvertimeUnit() != null)
                .map(WaOvertimeType::getMinOvertimeUnit).findFirst();
        return optional.orElse(null);
    }

    public List<WaOvertimeType> getOvertimeTypeList(String belongOrgId, Long empId, Long date, Integer dateType) {
        return overTimeTypeRepository.getOvertimeTypeList(belongOrgId, empId, date, dateType);
    }

    public List<WaOvertimeType> getOvertimeTypeByName(String belongOrgId, Integer waGroupId, String typeName, Integer overtimeTypeId) {
        return overTimeTypeRepository.getOvertimeTypeByName(belongOrgId, waGroupId, typeName, overtimeTypeId);
    }

    public List<WaOvertimeType> getOvertimeTypeByIds(List<Integer> overtimeTypeIds) {
        List<WaOvertimeType> overtimeTypes = overTimeTypeRepository.getOvertimeTypeByIds(overtimeTypeIds);
        overtimeTypes.forEach(overtimeType -> {
            overtimeType.setTypeName(LangParseUtil.getI18nLanguage(overtimeType.getI18nTypeName(), overtimeType.getTypeName()));
        });
        return overtimeTypes;
    }

    public List<OverTimeTypeDo> getAllOtTypes(String belongOrgId, Integer status, Long curDate) {
        List<WaOvertimeType> list = overTimeTypeRepository.getAllOtTypes(belongOrgId, status, curDate);
        return ObjectConverter.convertList(list, OverTimeTypeDo.class);
    }

    public List<WaOvertimeType> getOtTypeByNameAndTime(String belongOrgId, Long startDate, Long endDate, String typeName, Integer overtimeTypeId) {
        return overTimeTypeRepository.getOtTypeByNameAndTime(belongOrgId, startDate, endDate, typeName, overtimeTypeId);
    }

    public List<WaOvertimeType> getOtTypeByRuleId(Long ruleId) {
        return overTimeTypeRepository.getOtTypeByRuleId(ruleId);
    }

    public List<WaOvertimeType> getOtTypeAnalyseRule(String tenantId, Integer waGroupId) {
        return overTimeTypeRepository.getOtTypeAnalyseRule(tenantId, waGroupId);
    }

    public List<WaOvertimeType> getOvertimeType(Long empId, Integer dateType, Long endDate) {
        return overTimeTypeRepository.getOvertimeType(empId, dateType, endDate);
    }

    public List<WaOvertimeType> getOtherTransferRuleOvertimeType(String tenantId) {
        return overTimeTypeRepository.getOtherTransferRuleOvertimeType(tenantId);
    }

    public List<WaOvertimeType> getOtTypes(String tenantId, Integer overtimeTypeId, Integer dateType, Boolean defaultType, Long date) {
        return overTimeTypeRepository.getOtTypes(tenantId, overtimeTypeId, dateType, defaultType, date);
    }
}
