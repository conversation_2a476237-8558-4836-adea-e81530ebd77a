package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.payroll.mybatis.mapper.PayEmpGroupMapper;
import com.caidao1.payroll.mybatis.model.PayEmpGroupExample;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaConfigMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpQuotaMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveQuotaMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveSettingMapper;
import com.caidao1.wa.mybatis.model.WaEmpQuotaExample;
import com.caidao1.wa.mybatis.model.WaLeaveQuota;
import com.caidao1.wa.mybatis.model.WaLeaveQuotaExample;
import com.caidao1.wa.mybatis.model.WaLeaveSetting;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.IQuotaTypeService;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveQuotaDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveSettingDo;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveQuotaDto;
import com.caidaocloud.attendance.service.interfaces.dto.QuotaEmpGroupDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/3/2
 */
@Service
@Slf4j
public class QuotaTypeServiceImpl implements IQuotaTypeService {
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private PayEmpGroupMapper payEmpGroupMapper;
    @Autowired
    private WaLeaveQuotaMapper waLeaveQuotaMapper;
    @Autowired
    private WaEmpQuotaMapper waEmpQuotaMapper;
    @Autowired
    private WaLeaveSettingMapper waLeaveSettingMapper;
    @Autowired
    private WaConfigMapper waConfigMapper;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaLeaveSettingDo waLeaveSettingDo;
    @Autowired
    private WaLeaveQuotaDo waLeaveQuotaDo;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Transactional
    @Override
    public void saveOrUpdateLeaveQuota(LeaveQuotaDto leaveQuotaDto) {
        UserInfo userInfo = getUserInfo();
        if (userInfo == null) {
            return;
        }
        if (leaveQuotaDto.getStartDate() != null) {
            String md = DateUtil.convertDateTimeToStr(leaveQuotaDto.getStartDate(), "MMdd", true);
            leaveQuotaDto.setStartDate(Long.valueOf(md));
        }
        if (leaveQuotaDto.getEndDate() != null) {
            String md = DateUtil.convertDateTimeToStr(leaveQuotaDto.getEndDate(), "MMdd", true);
            leaveQuotaDto.setEndDate(Long.valueOf(md));
        }
        //保存配额类型
        WaLeaveSetting waLeaveSetting = ObjectConverter.convert(leaveQuotaDto, WaLeaveSetting.class);
        if (waLeaveSetting.getIsEmpShow() == null) {
            //默认员工端显示
            waLeaveSetting.setIsEmpShow(true);
        }
        if (waLeaveSetting.getIsTrialConvert() == null) {
            //默认试用期折算
            waLeaveSetting.setIsTrialConvert(true);
        }
        if (waLeaveSetting.getIsCountInProbation() == null) {
            //试用期不累积 默认 false
            waLeaveSetting.setIsCountInProbation(false);
        }
        if (leaveQuotaDto.getQuotaSettingId() == null && waLeaveSetting.getQuotaSortNo() == null) {
            waLeaveSetting.setQuotaSortNo(0);
        }
        waConfigService.saveLeaveSetting(waLeaveSetting);
        //删除旧的配额规则
        if (leaveQuotaDto.getQuotaSettingId() != null) {
            WaLeaveQuotaExample example = new WaLeaveQuotaExample();
            example.createCriteria().andBelongOrgidEqualTo(userInfo.getTenantId()).
                    andQuotaSettingIdEqualTo(leaveQuotaDto.getQuotaSettingId());
            waLeaveQuotaMapper.deleteByExample(example);
        }
        //保存配额生成规则
        if (CollectionUtils.isNotEmpty(leaveQuotaDto.getQuotaGroups())) {
            List<QuotaEmpGroupDto> empGroupList = leaveQuotaDto.getQuotaGroups();
            empGroupList.forEach(row -> {
                if (StringUtils.isNotBlank(row.getGroupExp())) {
                    try {
                        WaLeaveQuota leaveQuota = new WaLeaveQuota();
                        leaveQuota.setBelongOrgid(userInfo.getTenantId());
                        leaveQuota.setCrttime(DateUtil.getCurrentTime(true));
                        leaveQuota.setCrtuser(userInfo.getUserId());
                        leaveQuota.setLeaveType(Integer.valueOf(waLeaveSetting.getLeaveType()));
                        leaveQuota.setQuotaSettingId(waLeaveSetting.getQuotaSettingId());
                        leaveQuota.setQuotaVal(row.getQuotaVal());
                        Map<String, String> exp = new HashMap<>();
                        exp.put("group_exp", row.getGroupExp());
                        exp.put("group_note", row.getGroupNote());
                        PGobject jsonObject = new PGobject();
                        jsonObject.setType("jsonb");
                        jsonObject.setValue(JSON.toJSONString(exp));
                        leaveQuota.setEmpGroupJsonb(jsonObject);
                        waLeaveQuotaMapper.insertSelective(leaveQuota);
                    } catch (SQLException e) {
                        log.error(e.getMessage(), e);
                    }
                }
            });
        }
    }

    private String replaceGroupExp(String groupExp) {
        if (StringUtils.isBlank(groupExp)) {
            return groupExp;
        }
        return groupExp.replaceAll("subOrg='(\\d+)'", "orgid in ( SELECT getsuborgstr('{$1}'))")
                .replaceAll("subOrg<>'(\\d+)'", "orgid not in (" +
                        " SELECT cast(getsuborgstr as integer ) as t FROM getsuborgstr('{$1}')" +
                        ")"
                );
    }

    @Transactional
    @Override
    public void deleteLeaveQuota(Integer id) {
        UserInfo userInfo = getUserInfo();
        if (userInfo == null) {
            return;
        }
        WaEmpQuotaExample quotaExample = new WaEmpQuotaExample();
        quotaExample.createCriteria().andQuotaSettingIdEqualTo(id);
        int count = waEmpQuotaMapper.countByExample(quotaExample);
        if (count > 0) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.DELETE_NOT_ALLOW, null).getMsg());
        }
        WaLeaveQuotaExample example = new WaLeaveQuotaExample();
        example.createCriteria().andBelongOrgidEqualTo(userInfo.getTenantId()).
                andQuotaSettingIdEqualTo(id).andEmpGroupIdIsNotNull();
        List<WaLeaveQuota> leaveQuotaList = waLeaveQuotaMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(leaveQuotaList)) {
            List<Long> empGroupIds = leaveQuotaList.stream().map(WaLeaveQuota::getEmpGroupId).collect(Collectors.toList());
            PayEmpGroupExample payEmpGroupExample = new PayEmpGroupExample();
            payEmpGroupExample.createCriteria().andEmpGroupIdIn(empGroupIds);
            payEmpGroupMapper.deleteByExample(payEmpGroupExample);
            waLeaveQuotaMapper.deleteByExample(example);
        }
        waLeaveSettingMapper.deleteByPrimaryKey(id);
    }

    @Override
    public LeaveQuotaDto getLeaveQuotaById(Integer id) {
        UserInfo userInfo = this.getUserInfo();
        WaLeaveSetting leaveSetting = waLeaveSettingMapper.selectByPrimaryKey(id);
        if (leaveSetting != null) {
            if (leaveSetting.getStartDate() != null && leaveSetting.getEndDate() != null) {
                Calendar cal = Calendar.getInstance();
                int month = leaveSetting.getStartDate().intValue() / 100;
                int day = leaveSetting.getStartDate().intValue() % 100;
                cal.set(Calendar.MONTH, month - 1);
                cal.set(Calendar.DATE, day);
                leaveSetting.setStartDate(cal.getTimeInMillis() / 1000);
                int month1 = leaveSetting.getEndDate().intValue() / 100;
                int day1 = leaveSetting.getEndDate().intValue() % 100;
                cal.set(Calendar.MONTH, month1 - 1);
                cal.set(Calendar.DATE, day1);
                leaveSetting.setEndDate(cal.getTimeInMillis() / 1000);
            }
            List<QuotaEmpGroupDto> empGroupDtoList = new ArrayList<>();
            LeaveQuotaDto quotaDto = ObjectConverter.convert(leaveSetting, LeaveQuotaDto.class);
            List<WaLeaveQuotaDo> leaveQuotaDoList = waLeaveQuotaDo.getLeaveQuotaList(userInfo.getTenantId(), id);
            if (CollectionUtils.isNotEmpty(leaveQuotaDoList)) {
                leaveQuotaDoList.forEach(row -> {
                    if (row.getEmpGroupJsonb() != null) {
                        PGobject pGobject = (PGobject) row.getEmpGroupJsonb();
                        Map<String, Object> stringObjectMap = JSONUtils.convertPGobjectToMap(pGobject);
                        if (MapUtils.isNotEmpty(stringObjectMap)) {
                            QuotaEmpGroupDto empGroupDto = new QuotaEmpGroupDto();
                            empGroupDto.setGroupExp((String) stringObjectMap.get("group_exp"));
                            empGroupDto.setGroupNote((String) stringObjectMap.get("group_note"));
                            empGroupDto.setQuotaVal(row.getQuotaVal());
                            empGroupDtoList.add(empGroupDto);
                        }
                    }
                });
            }
            quotaDto.setQuotaGroups(empGroupDtoList);
            return quotaDto;
        }
        return new LeaveQuotaDto();
    }

    @Transactional
    @Override
    public void updateQuotaSort(Integer id, Integer sort) {
        WaLeaveSetting original = waLeaveSettingMapper.selectByPrimaryKey(id);
        if (original != null) {
            WaLeaveSetting leaveSetting = new WaLeaveSetting();
            leaveSetting.setQuotaSettingId(original.getQuotaSettingId());
            leaveSetting.setQuotaSortNo(sort);
            leaveSetting.setUpdtime(DateUtil.getCurrentTime(true));
            leaveSetting.setUpduser(getUserInfo().getUserId());
            waLeaveSettingMapper.updateByPrimaryKeySelective(leaveSetting);
        }
    }

    @Override
    public List<Map> getLeaveSettingList(PageBean pageBean, Integer leaveTypeId) {
        Map<String, Object> params = new HashMap<>();
        params.put("belongId", this.getUserInfo().getTenantId());
        params.put("filter", pageBean.getFilter());
        params.put("leaveTypeId", leaveTypeId);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "quota_sort_no" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        return this.waConfigMapper.getLeaveSettingList(pageBounds, params);
    }

    @Override
    public AttendancePageResult<LeaveQuotaDto> getLeaveQuotaPageList(AttendanceBasePage basePage, Integer leaveTypeId) {
        UserInfo userInfo = this.getUserInfo();
        AttendancePageResult<LeaveQuotaDto> dtoPageResult = new AttendancePageResult<>();
        AttendancePageResult<WaLeaveSettingDo> pageResult = waLeaveSettingDo.getLeaveSettingPageList(basePage, userInfo.getTenantId(), leaveTypeId);
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaLeaveSettingDo> doList = pageResult.getItems();
            List<LeaveQuotaDto> dtoList = ObjectConverter.convertList(doList, LeaveQuotaDto.class);
            dtoPageResult.setItems(dtoList);
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setTotal(pageResult.getTotal());
        }
        return dtoPageResult;
    }
}
