package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.dto.clock.ClockPlanInfoDto;
import com.caidaocloud.attendance.service.application.dto.clock.VerifyResultDto;
import com.caidaocloud.attendance.service.interfaces.dto.clock.*;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;

import java.util.List;

public interface IClockPlanService {

    Result<Boolean> saveClockPlan(ClockPlanDto dto);

    ClockPlanInfoDto getClockPlan(Long id);

    void deleteClockPlan(Long id);

    AttendancePageResult<ClockPlanPageInfoDto> getClockPlanList(ClockPlanPageDto dto);

    List<VerifyResultDto> verifySelectedEmployees(Long planId, List<Long> empIds);

    List<VerifyResultDto> getSelectedEmployees(Long planId, List<Long> empIds);

    List<ClockPlanDto> getPlanListBySiteId(Long corpId, String belongOrgId, Long id);

    AttendancePageResult<EmpClockPlanDto> getEmpClockPlans(EmpClockPlanReqDto reqDto, UserInfo userInfo);

    Result<Boolean> saveEmpClockPlan(EmpClockPlanInfoDto dto);

    void deleteEmpClockPlan(Long id);

    EmpClockPlanDto getEmpClockPlan(Long id);

    List<KeyValue> getClockPlanOptions();

    void synchronizeClockPlan(String belongOrgId, Long userId, Long corpId);

    void synchronizeClockPlan();

    ClockPlanInfoDto getEmpPlan();

    void deleteEmpClockPlans(List<Long> ids);
}
