package com.caidaocloud.attendance.service.application.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidao1.xss.test.cache.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.util.Pool;

import java.util.HashSet;
import java.util.Set;

@ConditionalOnProperty(
    prefix = "caidaocloud.cdCacheUtil",
    name = "isOpen",
    havingValue = "true",
    matchIfMissing = false
)
@Configuration
public class CdCacheUtilConfig {
    @NacosValue(value = "${spring.redis.host:localhost}", autoRefreshed = true)
    private String host;

    @NacosValue(value = "${spring.redis.port:6379}", autoRefreshed = true)
    private int port;

    @NacosValue(value = "${spring.redis.password:}", autoRefreshed = true)
    private String password;

    @NacosValue(value = "${spring.redis.timeout}", autoRefreshed = true)
    private int timeout;

    @NacosValue(value = "${spring.redis.pool.max-idle}", autoRefreshed = true)
    private int maxIdle;

    @NacosValue(value = "${spring.redis.pool.min-idle}", autoRefreshed = true)
    private int minIdle;

    @NacosValue(value = "${spring.redis.pool.max-active}", autoRefreshed = true)
    private int maxActive;

    @NacosValue(value = "${spring.redis.pool.max-wait}", autoRefreshed = true)
    private long maxWait;

    @NacosValue(value = "${spring.redis.database:0}", autoRefreshed = true)
    private int database;

    @NacosValue(value = "${spring.redis.sentinel.master:}", autoRefreshed = true)
    private String master;

    @NacosValue(value = "${spring.redis.sentinel.nodes:}", autoRefreshed = true)
    private String nodes;

    @NacosValue(value = "${spring.redis.ssl:false}", autoRefreshed = true)
    private boolean ssl;

    @Autowired
    private RedisService redisService;

    @Bean
    public Pool<Jedis> jedisPool() {
        Pool pool = null;
        if (StringUtils.isNotEmpty(master)) {
            Set<String> nodeSet = new HashSet<>();
            //获取到节点信息
            String[] nodeArray = nodes.split(",");
            //循环注入至Set中
            for (String node : nodeArray) {
                nodeSet.add(node);
            }
            //创建连接池对象
            JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
            jedisPoolConfig.setMaxIdle(maxIdle);
            jedisPoolConfig.setMaxTotal(maxActive);
            jedisPoolConfig.setMinIdle(minIdle);
            jedisPoolConfig.setMaxWaitMillis(maxWait);
            if (StringUtils.isNotEmpty(password)) {
                pool = new JedisSentinelPool(master, nodeSet, jedisPoolConfig, timeout, password, database);
            } else {
                pool = new JedisSentinelPool(master, nodeSet, jedisPoolConfig, timeout, null, database);
            }
        } else {
            JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
            jedisPoolConfig.setMaxIdle(maxIdle);
            jedisPoolConfig.setMaxTotal(maxActive);
            jedisPoolConfig.setMinIdle(minIdle);
            jedisPoolConfig.setMaxWaitMillis(maxWait);
            if (StringUtils.isNotEmpty(password)) {
                if (ssl) {
                    pool = new JedisPool(jedisPoolConfig, host, port, timeout, password, database, true);
                } else {
                    pool = new JedisPool(jedisPoolConfig, host, port, timeout, password, database);
                }
            } else {
                if (ssl) {
                    pool = new JedisPool(jedisPoolConfig, host, port, timeout, null, database, true);
                } else {
                    pool = new JedisPool(jedisPoolConfig, host, port, timeout, null, database);
                }
            }
        }

        redisService.setJedisPool(pool);
        return pool;
    }
}
