package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.service.domain.repository.IWaEmpTravelDaytimeRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpTravelDaytimeDetail;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Data
@Service
public class WaEmpTravelDaytimeDo {
    private Long travelDaytimeId;

    private Long travelId;

    private Long travelDate;

    private String shalfDay;

    private String ehalfDay;

    private Integer startTime;

    private Integer endTime;

    private Short periodType;

    private Float timeDuration;

    private Short timeUnit;

    private Integer dateType;

    private Long realDate;

    private Integer shiftDefId;

    private Float applyTimeDuration;

    private Float beforeAdjustTimeDuration;

    private Long entityId;

    private String extCustomCol;

    //其他表字段

    //WaEmpTravel 字段
    private Long empId;

    private Long travelTypeId;

    private Integer province;

    private Integer city;

    private Integer county;

    //WaTravelType 字段
    private Integer acctTimeType;
    private Integer overtimeRule;
    private String autoTransferRule;

    //班次信息
    private EmpShiftInfo empShiftInfo;

    //班次是否弹性
    private Boolean isFlexibleWorking;

    private String travelType;

    @Autowired
    private IWaEmpTravelDaytimeRepository waEmpTravelDaytimeRepository;

    public int save(WaEmpTravelDaytimeDo daytimeDo) {
        return waEmpTravelDaytimeRepository.save(daytimeDo);
    }

    public List<WaEmpTravelDaytimeDo> getEmpTravelDaytimeDetailList(String tenantId, String anyEmpIds, Long startDate, Long endDate, List<Integer> approvalStatusList) {
        return waEmpTravelDaytimeRepository.getEmpTravelDaytimeDetailList(tenantId, anyEmpIds, startDate, endDate, approvalStatusList);
    }

    public int deleteByTravelIds(List<Long> travelIds) {
        if (null == travelIds || travelIds.size() == 0) {
            return 0;
        }
        return waEmpTravelDaytimeRepository.deleteByTravelId(travelIds);
    }

    public List<WaEmpTravelDaytimeDo> getEmpTravelDaytimeList(String tenantId, List<Long> empIds, Long startDate, Long endDate, List<Integer> status) {
        return waEmpTravelDaytimeRepository.getEmpTravelDaytimeList(tenantId, empIds, startDate, endDate, status);
    }

    public void batchUpdate(List<WaEmpTravelDaytimeDo> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<EmpTravelDaytimeDetail> models = ObjectConverter.convertList(records, EmpTravelDaytimeDetail.class);
        waEmpTravelDaytimeRepository.batchUpdate(models);
    }

    public List<WaEmpTravelDaytimeDo> getEmpTravelDaytimeByTravelId(List<Long> travelIds) {
        if (CollectionUtils.isEmpty(travelIds)) {
            return Lists.newArrayList();
        }
        return waEmpTravelDaytimeRepository.getEmpTravelDaytimeByTravelId(travelIds);
    }

    public List<WaEmpTravelDaytimeDo> getEmpTravelDaytimeList(String tenantId, List<Long> travelIds) {
        if (CollectionUtils.isEmpty(travelIds)) {
            return Lists.newArrayList();
        }
        return waEmpTravelDaytimeRepository.getEmpTravelDaytimeList(tenantId, travelIds);
    }

    public List<WaEmpTravelDaytimeDo> listByTravelId(Long travelId) {
        return waEmpTravelDaytimeRepository.selectListByTravelId(travelId);
    }

    public List<WaEmpTravelDaytimeDo> getTravelDayTimeList(String tenantId, Long empId, Long dayTime, Long endTime) {
        return waEmpTravelDaytimeRepository.getTravelDayTimeList(tenantId, empId, dayTime, endTime);
    }

    public List<WaEmpTravelDaytimeDo> getWaTravelDaytimeList(String tenantId, Long empId,
                                                             Long startDate, Long endDate,
                                                             List<Integer> status, List<Integer> periodTypes) {
        return waEmpTravelDaytimeRepository.selectTravelDaytimeList(tenantId, empId, startDate, endDate, status, periodTypes);
    }
}