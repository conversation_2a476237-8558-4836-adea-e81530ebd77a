package com.caidaocloud.attendance.service.application.service.workflow;

import com.caidaocloud.attendance.core.workflow.dto.WfBusinessDataDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.service.application.service.impl.WfService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.workflow.service.IBusinessDetailService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class BusinessDetailService extends IBusinessDetailService {
    @Autowired
    private WfService wfService;

    @Override
    protected Map<String, Object> getBusinessDetail(String businessKey) {
        log.info("BusinessDetailService.getBusinessDetail start businessKey={}", businessKey);
        if (StringUtils.isBlank(businessKey)) {
            return null;
        }
        var userInfo = SecurityUserUtil.getSecurityUserInfo();
        WfResponseDto wfDetail = wfService.getWfDetail(userInfo.getTenantId(), businessKey, true);
        if (wfDetail == null || CollectionUtils.isEmpty(wfDetail.getDetailList())) {
            log.info("BusinessDetailService.getBusinessDetail wfDetail empty businessKey={}", businessKey);
            return null;
        }
        Map<String, Object> detailMap = Maps.newHashMap();
        for (WfBusinessDataDetailDto detailDto : wfDetail.getDetailList()) {
            detailMap.put(detailDto.getKey(), detailDto.getValue());
        }
        return detailMap;
    }

    @Override
    protected List<String> belongFunCode() {
        return Lists.newArrayList("ATTENDANCE-LEAVE", "ATTENDANCE-OVERTIME",
                "ATTENDANCE-TRAVEL", "ATTENDANCE-BATCH-TRAVEL", "ATTENDANCE-REGISTER",
                "ATTENDANCE-SHIFT", "ATTENDANCE-VACATION", "ATTENDANCE-COMPENSATORY",
                "ATTENDANCE-OVERTIME-REVOKE", "ATTENDANCE-OVERTIME-ABOLISH",
                "ATTENDANCE-TRAVEL-REVOKE", "ATTENDANCE-TRAVEL-ABOLISH", "ATTENDANCE-BATCH-TRAVEL", "ATTENDANCE-BATCH-LEAVE", "ATTENDANCE-BATCH-OVERTIME", "ATTENDANCE-BATCH-ANALYSE-ADJUST"
                , "ATTENDANCE-LEAVE-EXTENSION");
    }
}