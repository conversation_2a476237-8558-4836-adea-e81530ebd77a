package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class AnalyseClockRecordPublish {

    @Resource
    private MqMessageProducer<ClockMessage> producer;

    private final static String EXCHANGE = "attendance.clock.analyse.pc.fac.direct.exchange";

    private final static String ROUTING_KEY = "routingKey.clock.analyse.pc";

    public void publish(String msg) {
        log.info("PC:AnalyseClockRecordPublish.publish  push register record time:{},param:{}", System.currentTimeMillis(), msg);
        try {
            ClockMessage message = new ClockMessage();
            message.setBody(msg);
            message.setExchange(EXCHANGE);
            message.setRoutingKey(ROUTING_KEY);
            producer.publish(message);
        }catch (Exception e) {
            log.error("PC:AnalyseClockRecordPublish.publish  push register record error!! reason:{}:", e.getMessage(), e);
            e.printStackTrace();
        }

    }
}
