package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.dto.EmpShiftBaseInfoForPortalDto;
import com.caidaocloud.attendance.service.application.dto.EmpShiftRecordDto;
import com.caidaocloud.attendance.service.application.service.IEmpInfoService;
import com.caidaocloud.attendance.service.application.service.IScheduleQueryService;
import com.caidaocloud.attendance.service.domain.entity.WaClockPlan;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordDto;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;
import com.caidaocloud.dto.UserInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: Aaron.Chen
 * @Date: 2022/4/1 10:38
 * @Description:
 **/
@Slf4j
@Service
public class EmpInfoServiceImpl implements IEmpInfoService {
    @Autowired
    private RegisterRecordService registerRecordService;
    @Resource
    private WaClockPlan waClockPlanService;
    @Autowired
    private IScheduleQueryService scheduleQueryService;

    @Override
    public EmpShiftRecordDto getShiftAndRecords(Long regDate, Long empId, String tenantId, List<Integer> shiftDefIdList) {
        UserInfo userInfo = UserContext.preCheckUser();
        if (userInfo != null && userInfo.getStaffId() == null) {
            userInfo.setStaffId(empId);
        }

        EmpShiftRecordDto empShiftRecordDto = new EmpShiftRecordDto();

        // 查询员工排班
        Long startDate = DateUtil.getOnlyDate(new Date(regDate * 1000));
        Long endDate = startDate + 86399;
        Map<String, WaShiftDo> empShiftDoMap = scheduleQueryService.getEmpCalendarShiftMap(tenantId, startDate, endDate, Lists.newArrayList(empId));
        WaShiftDo shiftDo;
        if (MapUtils.isNotEmpty(empShiftDoMap) && null != (shiftDo = empShiftDoMap.get(empId + "_" + startDate))) {
            List<WaShiftDo> shiftDoList = shiftDo.doGetShiftDoList();
            if (CollectionUtils.isNotEmpty(shiftDefIdList)) {
                shiftDoList = shiftDoList.stream().filter(o -> shiftDefIdList.contains(o.getShiftDefId())).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(shiftDoList)) {
                List<EmpShiftBaseInfoForPortalDto> shiftInfoDtoList = shiftDoList.stream().map(shiftDoIt -> {
                    EmpShiftBaseInfoForPortalDto shiftInfoDto = new EmpShiftBaseInfoForPortalDto();
                    shiftInfoDto.setShiftName(LangParseUtil.getI18nLanguage(shiftDoIt.getI18nShiftDefName(), shiftDoIt.getShiftDefName()));
                    if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDoIt.getDateType())) {
                        shiftInfoDto.setStartTime(shiftDoIt.getStartTime());
                        shiftInfoDto.setEndTime(shiftDoIt.getEndTime());
                    }
                    List<String> timePeriodList = WaShiftDo.getShiftMultiWorkTimePeriodList(shiftDoIt);
                    if (CollectionUtils.isNotEmpty(timePeriodList)) {
                        shiftInfoDto.setShiftTimePeriod(StringUtils.join(timePeriodList, "、"));
                    }
                    return shiftInfoDto;
                }).collect(Collectors.toList());
                empShiftRecordDto.setShifts(shiftInfoDtoList);

                List<String> shiftNameList = shiftInfoDtoList.stream().map(EmpShiftBaseInfoForPortalDto::getShiftName).collect(Collectors.toList());
                empShiftRecordDto.setShiftName(StringUtils.join(shiftNameList, "，"));

                List<String> allShiftTimePeriods = shiftInfoDtoList.stream()
                        .map(EmpShiftBaseInfoForPortalDto::getShiftTimePeriod)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(allShiftTimePeriods)) {
                    empShiftRecordDto.setShiftTimePeriod(StringUtils.join(allShiftTimePeriods, "、"));
                }
            }
        }

        // 查询打卡记录
        RegisterRecordRequestDto requestDto = new RegisterRecordRequestDto();
        requestDto.setPageNo(1);
        requestDto.setPageSize(10000);
        requestDto.setStartDate(startDate.intValue());
        requestDto.setEndDate(endDate.intValue());
        AttendancePageResult<RegisterRecordDto> registerRecord = registerRecordService.getRegisterRecordListByEmpId(requestDto, userInfo);
        List<Long> regDates = registerRecord.getItems().stream().map(RegisterRecordDto::getRegDateTime).sorted().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(regDates)) {
            empShiftRecordDto.setRecords(regDates);
        }
        empShiftRecordDto.setSupplementNumber(1);
        WaClockPlan waClockPlan = waClockPlanService.getMyWaClockPlan(ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), userInfo.getStaffId(), DateUtil.getOnlyDate());
        if (null != waClockPlan && waClockPlan.getIsSupplement() != null && waClockPlan.getIsSupplement() && null != waClockPlan.getSupplementNumber()) {
            empShiftRecordDto.setSupplementNumber(waClockPlan.getSupplementNumber());
        }
        return empShiftRecordDto;
    }
}