package com.caidaocloud.attendance.service.application.enums;

/**
 * 休假&出差-时间类型
 */
public enum PeriodTypeEnum {
    PERIOD_TYPE_ONE(1, "时间单位为天的整天"),
    PERIOD_TYPE_THREE(3, "时间单位为小时的非整天"),
    PERIOD_TYPE_FOUR(4, "时间单位为小时的整天"),
    PERIOD_TYPE_NINE(9, "时间单位为天的非整天");

    private Integer index;
    private String name;

    PeriodTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (PeriodTypeEnum c : PeriodTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
