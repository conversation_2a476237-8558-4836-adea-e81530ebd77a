package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.domain.entity.WaEmpWorkingHoursSettlementDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpWorkingHoursSettlement;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;

public interface IEmpWorkingHoursSettlementRepository {
    void updateById(WaEmpWorkingHoursSettlementDo updateData);

    void insert(WaEmpWorkingHoursSettlementDo saveData);

    WaEmpWorkingHoursSettlementDo getById(Long id);

    void deleteById(Long id);

    PageList<WaEmpWorkingHoursSettlementDo> getPageList(String tenantId, Integer attendanceMonth, Integer waGroupId, Long organize,
                                                        List<String> workHours, List<String> salaryWorkHours, String keywords,
                                                        String dataScope, MyPageBounds pageBounds);

    int deleteEmpWorkingHourSettlementList(String tenantId, Integer attendanceMonth, Integer waGroupId, List<Long> empIds);

    int saveEmpWorkingHourSettlementList(List<WaEmpWorkingHoursSettlement> models);
}
