package com.caidaocloud.attendance.core.workflow.dto;

import com.caidaocloud.hr.workflow.enums.WfAttachmentTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 流程详情-附件组件信息DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WfAttachmentDto {
    /**
     * 文件类型(值为WfAttachmentTypeEnum)
     */
    private String type;

    /**
     * 文件路径
     */
    private String fileUrl;

    /**
     * 文件名
     */
    private String fileName;

    public static List<WfAttachmentDto> convert2WfAttachmentDto(String files, String fileNames) {
        if (StringUtils.isBlank(files) || StringUtils.isBlank(fileNames)) {
            return new ArrayList<>();
        }
        String[] fileNameArray = fileNames.split(",");
        List<String> names = new ArrayList<>(Arrays.asList(fileNameArray));

        String[] fileArray = files.split(",");
        List<String> urls = new ArrayList<>(Arrays.asList(fileArray));

        List<WfAttachmentDto> fileList = new ArrayList<>();
        for (int i = 0; i < urls.size(); i++) {
            fileList.add(new WfAttachmentDto(WfAttachmentTypeEnum.image.name(), urls.get(i), names.get(i)));
        }
        return fileList;
    }
}
