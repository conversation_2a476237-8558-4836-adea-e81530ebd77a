package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftRecordDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ChangeShiftReqDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2022/03/17
 * @Description:
 **/
public interface IWaShiftApplyService {

    /**
     * 调班申请
     *
     * @param dto
     * @return
     */
    Result<String> apply(ApplyShiftDto dto, UserInfo userInfo) throws Exception;

    /**
     * 调班记录
     *
     * @param dto
     * @return
     */
    List<ApplyShiftRecordDto> list(ApplyShiftRecordDto dto);

    /**
     * 调班记录分页查询
     *
     * @param dto
     * @return
     */
    PageResult<ApplyShiftRecordDto> pageList(ChangeShiftReqDto dto, UserInfo userInfo);

    /**
     * 撤销申请
     *
     * @param recId
     * @param revokeReason
     * @param userInfo
     * @return
     */
    Result<Boolean> revoke(Long recId, String revokeReason, UserInfo userInfo);

    /**
     * 流程审批
     *
     * @param businessKey
     * @param operationEnum
     * @return
     */
    boolean saveShiftApprovalWorkFlow(Long businessKey, WfCallbackTriggerOperationEnum operationEnum);
}
