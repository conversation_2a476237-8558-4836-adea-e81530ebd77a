package com.caidaocloud.attendance.core.wa.enums;

import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

/**
 * 工作日历：考勤类型
 */
public enum CalendarWorktimeTypeEnum {
    FIXED_SCHEDULE(1, "固定班次", 202700),
    SCHEDULE(2, "排班制", 202710),
    FREE_SCHEDULE(3, "自由打卡", 202720);

    private Integer index;
    private String name;
    /**
     * 假勤多语言编码
     */
    private Integer code;

    CalendarWorktimeTypeEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(int index) {
        for (CalendarWorktimeTypeEnum c : CalendarWorktimeTypeEnum.values()) {
            if (c.getIndex() == index) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return c.name;
            }
        }
        return "";
    }

    public static CalendarWorktimeTypeEnum getByIndex(int index) {
        for (CalendarWorktimeTypeEnum c : CalendarWorktimeTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c;
            }
        }
        return CalendarWorktimeTypeEnum.FIXED_SCHEDULE;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
