package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

public enum MaternityLeaveTypeEnum {

    EASY_LABOUR(1, "顺产", "easy labour", AttendanceCodes.EASY_LABOUR),
    DIFFICULT_LABOUR(2, "难产", "difficult labour", AttendanceCodes.DIFFICULT_LABOUR);

    private int index;
    private String cnName;
    private String enName;
    private Integer code;

    MaternityLeaveTypeEnum(int index, String cnName, String enName, Integer code) {
        this.index = index;
        this.cnName = cnName;
        this.enName = enName;
        this.code = code;
    }

    public static String getNameByIndex(int index) {
        for (MaternityLeaveTypeEnum value : MaternityLeaveTypeEnum.values()) {
            if (value.getIndex() == index) {
                String msg = ResponseWrap.wrapResult(value.code, null).getMsg();
                if (StringUtil.isNotBlank(msg)) {
                    return msg;
                } else {
                    return value.getCnName();
                }
            }
        }
        return "";
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getCnName() {
        return cnName;
    }

    public void setCnName(String cnName) {
        this.cnName = cnName;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
