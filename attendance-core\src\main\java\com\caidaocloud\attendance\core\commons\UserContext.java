package com.caidaocloud.attendance.core.commons;

import com.caidao1.commons.utils.ConvertHelper;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class UserContext {
    private final static ThreadLocal<UserInfo> USER_CONTEXT = new ThreadLocal<>();

    public static void setCurrentUser(UserInfo user) {
        USER_CONTEXT.remove();
        if (user != null) {
            USER_CONTEXT.set(user);
        }
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    public static UserInfo getCurrentUser() {
        return USER_CONTEXT.get();
    }

    /**
     * 清除用户信息
     */
    public static void remove() {
        USER_CONTEXT.remove();
    }

    public static UserInfo getAndCheckUser() {
        UserInfo userInfo = preCheckUser();
        PreCheck.preCheckArgument(checkUser(userInfo), "登录失效");
        return userInfo;
    }

    public static UserInfo preCheckUser() {
        UserInfo user = getCurrentUser();
        if (null != user) {
            return user;
        }
        UserInfo userInfo = null;
        SecurityUserInfo ui = SecurityUserUtil.getSecurityUserInfo();
        if (null != ui) {
            userInfo = new UserInfo();
            userInfo.setTenantId(ui.getTenantId());
            userInfo.setUserid(null != ui.getUserId() ? ui.getUserId().intValue() : null);
            userInfo.doSetUserId(ui.getUserId());
            userInfo.setStaffId(ui.getEmpId());
        }
        return userInfo;
    }

    public static Long getUserId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId();
    }

    public static String getTenantId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
    }

    public static Long getCorpId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getTenantId() ? null : ConvertHelper.longConvert(userInfo.getTenantId());
    }

    public static Long getStaffId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getStaffId() ? null : userInfo.getStaffId();
    }

    public static boolean checkUser(UserInfo userInfo) {
        return null == userInfo || null == userInfo.getUserId();
    }

    public static void doInitSecurityUserInfo(String tenantId, String userId, String staffId,
                                              String empName, String userName, String mobnum) {
        Long empId = null == staffId || "null".equals(staffId) ? 0L : Long.valueOf(staffId);
        Long userid = null == userId || "null".equals(userId) ? 0L : Long.valueOf(userId);
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        userInfo.setEmpId(empId);
        userInfo.setUserId(userid);// 默认系统超级管理员
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        UserInfo user = new UserInfo();
        user.setTenantId(tenantId);
        // 默认系统超级管理员
        user.setStaffId(empId);
        user.doSetUserId(userid);
        user.setUserid(userid.intValue());
        user.setEmpid(empId.intValue());
        user.setEmpname(empName);
        user.setUserName(userName);
        user.setMobnum(mobnum);
        UserContext.setCurrentUser(user);
    }

    public static void removeSecurityUserInfo() {
        SecurityUserUtil.removeSecurityUserInfo();
        UserContext.remove();
    }
}
