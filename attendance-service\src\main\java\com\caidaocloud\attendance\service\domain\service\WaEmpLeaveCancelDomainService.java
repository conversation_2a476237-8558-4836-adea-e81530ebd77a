package com.caidaocloud.attendance.service.domain.service;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.service.application.dto.LeaveCancelTimePeriodDto;
import com.caidaocloud.attendance.service.application.enums.LeaveCancelTypeEnum;
import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveCancelDo;
import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveCancelInfoDo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WaEmpLeaveCancelDomainService {
    @Autowired
    private WaEmpLeaveCancelDo waEmpLeaveCancelDo;
    @Autowired
    private WaEmpLeaveCancelInfoDomainService waEmpLeaveCancelInfoDomainService;

    public void save(WaEmpLeaveCancelDo cancelDo) {
        waEmpLeaveCancelDo.save(cancelDo);
    }

    public void update(WaEmpLeaveCancelDo cancelDo) {
        waEmpLeaveCancelDo.update(cancelDo);
    }

    public PageResult<WaEmpLeaveCancelDo> pageList(AttendanceBasePage basePage, Map params) {
        return waEmpLeaveCancelDo.pageList(basePage, params);
    }

    public List<WaEmpLeaveCancelDo> getListByLeaveId(String tenantId, Integer leaveId) {
        return waEmpLeaveCancelDo.getListByLeaveId(tenantId, leaveId);
    }

    public WaEmpLeaveCancelDo getById(String tenantId, Long leaveCancelId) {
        return waEmpLeaveCancelDo.getById(tenantId, leaveCancelId);
    }

    public WaEmpLeaveCancelDo getInfoById(String tenantId, Long leaveCancelId) {
        return waEmpLeaveCancelDo.getInfoById(tenantId, leaveCancelId);
    }

    public List<WaEmpLeaveCancelDo> getListByLeaveIds(String tenantId, List<Integer> leaveIds) {
        return waEmpLeaveCancelDo.getListByLeaveIds(tenantId, leaveIds);
    }

    /**
     * 查询销假时间段详情
     *
     * @param leaveId
     * @return
     */
    public List<LeaveCancelTimePeriodDto> getLeaveCancelTimePeriodList(String tenantId, Integer leaveId) {
        List<WaEmpLeaveCancelDo> cancelDoList = this.getListByLeaveId(tenantId, leaveId);
        if (CollectionUtils.isEmpty(cancelDoList)) {
            return Lists.newArrayList();
        }
        Map<Long, WaEmpLeaveCancelDo> leaveCancelMap = cancelDoList.stream().collect(Collectors.toMap(WaEmpLeaveCancelDo::getLeaveCancelId, Function.identity(), (v1, v2) -> v1));
        //时间调整
        List<WaEmpLeaveCancelDo> timeListDo = cancelDoList.stream().filter(e -> Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST.getIndex()).equals(e.getTypeId())).collect(Collectors.toList());
        List<LeaveCancelTimePeriodDto> dtoList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(timeListDo)) {
            List<Long> idList = cancelDoList.stream().map(WaEmpLeaveCancelDo::getLeaveCancelId).collect(Collectors.toList());
            List<WaEmpLeaveCancelInfoDo> infoDoList = waEmpLeaveCancelInfoDomainService.getListByLeaveCancelId(idList);
            dtoList = ObjectConverter.convertList(infoDoList, LeaveCancelTimePeriodDto.class);
            dtoList.forEach(o -> {
                WaEmpLeaveCancelDo cancelDo = leaveCancelMap.get(o.getLeaveCancelId());
                o.setStatus(cancelDo.getStatus());
                o.setReason(cancelDo.getReason());
                o.setFileId(cancelDo.getFileId());
                o.setFileName(cancelDo.getFileName());
                o.setTypeId(Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST.getIndex()));
            });
        }
        cancelDoList.removeAll(timeListDo);
        //取消部分休假
        List<WaEmpLeaveCancelDo> cancelTimeAdjustPartialList = cancelDoList.stream().filter(e -> Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex()).equals(e.getTypeId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(cancelTimeAdjustPartialList)) {
            List<Long> idList = cancelTimeAdjustPartialList.stream().map(WaEmpLeaveCancelDo::getLeaveCancelId).collect(Collectors.toList());
            List<WaEmpLeaveCancelInfoDo> infoDoList = waEmpLeaveCancelInfoDomainService.getListByLeaveCancelId(idList);
            dtoList = ObjectConverter.convertList(infoDoList, LeaveCancelTimePeriodDto.class);
            dtoList.forEach(o -> {
                WaEmpLeaveCancelDo cancelDo = leaveCancelMap.get(o.getLeaveCancelId());
                o.setStatus(cancelDo.getStatus());
                o.setReason(cancelDo.getReason());
                o.setFileId(cancelDo.getFileId());
                o.setFileName(cancelDo.getFileName());
                o.setTypeId(Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST.getIndex()));
            });
        }
        cancelDoList.removeAll(cancelTimeAdjustPartialList);
        //其他
        if (!CollectionUtils.isEmpty(cancelDoList)) {
            for (WaEmpLeaveCancelDo cancelDo : cancelDoList) {
                LeaveCancelTimePeriodDto dto = new LeaveCancelTimePeriodDto();
                dto.setReason(cancelDo.getReason());
                dto.setFileId(cancelDo.getFileId());
                dto.setFileName(cancelDo.getFileName());
                dto.setStatus(cancelDo.getStatus());
                dto.setTypeId(cancelDo.getTypeId());
                dtoList.add(dto);
            }
        }
        return dtoList;
    }
}
