package com.caidaocloud.attendance.service.application.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
public class ThreadPoolConfig {

    @Data
    public static class ThreadPoolProperties {
        private String poolName;
        private String threadNamePrefix;
        private int corePoolSize;
        private int maxPoolSize;
        private int queueCapacity;
        private int keepAliveSeconds = 300;
        private int awaitTerminationSeconds = 60;
        private boolean allowCoreThreadTimeOut = true;
        private boolean waitForTasksToCompleteOnShutdown = true;
        
        public ThreadPoolProperties(String poolName, String threadNamePrefix, 
                                  int corePoolSize, int maxPoolSize, int queueCapacity) {
            this.poolName = poolName;
            this.threadNamePrefix = threadNamePrefix;
            this.corePoolSize = corePoolSize;
            this.maxPoolSize = maxPoolSize;
            this.queueCapacity = queueCapacity;
        }
    }

    public static ThreadPoolTaskExecutor createThreadPool(ThreadPoolProperties properties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        executor.setCorePoolSize(properties.getCorePoolSize());
        executor.setMaxPoolSize(properties.getMaxPoolSize());
        executor.setQueueCapacity(properties.getQueueCapacity());
        executor.setThreadNamePrefix(properties.getThreadNamePrefix());
        executor.setKeepAliveSeconds(properties.getKeepAliveSeconds());
        executor.setAllowCoreThreadTimeOut(properties.isAllowCoreThreadTimeOut());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(properties.isWaitForTasksToCompleteOnShutdown());
        executor.setAwaitTerminationSeconds(properties.getAwaitTerminationSeconds());
        
        executor.initialize();
        
        log.info("SUCCESS: Thread pool '{}' initialized - coreSize: {}, maxSize: {}, queueCapacity: {}",
                properties.getPoolName(), executor.getCorePoolSize(), executor.getMaxPoolSize(), 
                properties.getQueueCapacity());
        
        return executor;
    }

    public static String getThreadPoolStatus(String poolName, ThreadPoolTaskExecutor executor) {
        if (executor == null) {
            return String.format("%s: not initialized", poolName);
        }
        return String.format("%s[active: %d, pool: %d, queue: %d, completed: %d]",
                poolName, executor.getActiveCount(), executor.getPoolSize(),
                executor.getThreadPoolExecutor().getQueue().size(),
                executor.getThreadPoolExecutor().getCompletedTaskCount());
    }

    public static void checkThreadPoolHealth(String poolName, ThreadPoolTaskExecutor executor, 
                                           int queueWarningThreshold, double activeWarningRatio) {
        if (executor == null) return;

        int queueSize = executor.getThreadPoolExecutor().getQueue().size();
        int activeCount = executor.getActiveCount();
        int maxPoolSize = executor.getMaxPoolSize();

        if (queueSize > queueWarningThreshold) {
            log.warn("WARN: Thread pool '{}' queue is getting full - queueSize: {}", poolName, queueSize);
        }
        if (activeCount >= maxPoolSize * activeWarningRatio) {
            log.warn("WARN: Thread pool '{}' is near capacity - active: {}, max: {}", 
                    poolName, activeCount, maxPoolSize);
        }
    }

    public static ThreadPoolProperties createRabbitMQConsumerConfig(AttendanceMqProperties mqProperties) {
        return new ThreadPoolProperties("RabbitMQ-Consumer", 
                mqProperties.getConsumer().getTaskExecutorName(),
                mqProperties.getConsumer().getMaxConcurrentConsumers(),
                mqProperties.getConsumer().getMaxConcurrentConsumers() * 2, 50);
    }

    public static ThreadPoolProperties createAsyncProcessingConfig(AttendanceMqProperties mqProperties) {
        ThreadPoolProperties config = new ThreadPoolProperties("Async-Processing", "async-clock-analysis-",
                2, Math.max(4, mqProperties.getConsumer().getMaxConcurrentConsumers()), 100);
        config.setKeepAliveSeconds(600);
        config.setAwaitTerminationSeconds(120);
        return config;
    }

    public static ThreadPoolProperties createTimeoutCheckConfig(AttendanceMqProperties mqProperties) {
        return new ThreadPoolProperties("Timeout-Check", "timeout-checker-",
                1, mqProperties.getConsumer().getMaxConcurrentConsumers(),
                mqProperties.getConsumer().getMaxConcurrentConsumers() * 2);
    }

    public static ThreadPoolProperties createStartupTaskConfig() {
        ThreadPoolProperties config = new ThreadPoolProperties("Startup-Task", "startup-task-", 1, 2, 5);
        config.setKeepAliveSeconds(60);
        config.setAwaitTerminationSeconds(30);
        return config;
    }
} 