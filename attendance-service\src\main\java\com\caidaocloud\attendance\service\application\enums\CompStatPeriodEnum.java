package com.caidaocloud.attendance.service.application.enums;

/**
 * 考勤方案-工时规则：综合工时制-统计方式
 */
public enum CompStatPeriodEnum {
    ATTENDANCE_CYCLE(1, "考勤周期");

    private Integer index;

    private String name;

    CompStatPeriodEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (CompStatPeriodEnum c : CompStatPeriodEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
