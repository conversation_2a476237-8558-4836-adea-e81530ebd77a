package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IAttendanceEmpGroupRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.AttendanceEmpGroupPo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Data
@Component
public class AttendanceEmpGroupDo {
    /**
     * 分组id
     */
    private Long empGroupId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 分组名字
     */
    private String groupName;

    /**
     * 分组表达式
     */
    private String expression;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 业务key
     */
    private String businessKey;

    /**
     * 分组类型
     */
    private String groupType;

    /**
     * 分组排序
     */
    private Integer sortNum;

    /**
     * 分组备注
     */
    private String remark;

    /**
     * 是否启用：0 启用，1 禁用，默认 0
     */
    private Integer enabled;

    /**
     * 是否删除：0 正常，1 删除，默认 0
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 修改人
     */
    private Long updateBy;
    /**
     * 修改时间
     */
    private Long updateTime;

    @Resource
    private IAttendanceEmpGroupRepository attendanceEmpGroupRepository;
    @Resource
    private ISessionService sessionService;

    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public void insert(AttendanceEmpGroupDo empGroupDo, Long defaultUserId, Long defaultTenantId) {
        empGroupDo.setEmpGroupId(snowflakeUtil.createId());
        UserInfo userInfo = sessionService.getUserInfo();
        long createTime = System.currentTimeMillis();
        long userId = 0;
        long tenantId = 0;
        if (userInfo == null) {
            userId = defaultUserId;
            tenantId = defaultTenantId;
        } else {
            userId = null != userInfo.getUserId() ? userInfo.getUserId(): 0L;
            tenantId = null != userInfo.getTenantId() ? Long.parseLong(userInfo.getTenantId()) : 0L;
        }
        empGroupDo.setTenantId(tenantId);
        empGroupDo.setCreateBy(userId);
        empGroupDo.setCreateTime(createTime);
        empGroupDo.setUpdateBy(userId);
        empGroupDo.setUpdateTime(createTime);
        AttendanceEmpGroupPo empGroupPo = ObjectConverter.convert(empGroupDo, AttendanceEmpGroupPo.class);
        attendanceEmpGroupRepository.insert(empGroupPo);
    }

    public void updateById(AttendanceEmpGroupDo empGroupDo, Long defaultUserId) {
        AttendanceEmpGroupPo empGroupPo = updateEmpGroupPo(empGroupDo, defaultUserId);
        attendanceEmpGroupRepository.updateById(empGroupPo);
    }

    public void updateByBusKey(AttendanceEmpGroupDo empGroupDo, Long defaultUserId, Long defaultTenantId) {
        AttendanceEmpGroupPo empGroupPo = updateEmpGroupPo(empGroupDo, defaultUserId);
        attendanceEmpGroupRepository.updateByBusKey(empGroupPo, defaultTenantId);
    }

    private AttendanceEmpGroupPo updateEmpGroupPo(AttendanceEmpGroupDo empGroupDo, Long defaultUserId) {
        UserInfo userInfo = sessionService.getUserInfo();
        Long userId = defaultUserId;
        if (null != userInfo) {
            userId = null != userInfo.getUserId() ? userInfo.getUserId() : 0L;
        }
        long createTime = System.currentTimeMillis();
        empGroupDo.setUpdateBy(userId);
        empGroupDo.setUpdateTime(createTime);
        AttendanceEmpGroupPo empGroupPo = ObjectConverter.convert(empGroupDo, AttendanceEmpGroupPo.class);
        return empGroupPo;
    }

    public void deleteById(Long empGroupId) {
        attendanceEmpGroupRepository.deleteById(empGroupId);
    }

    public AttendanceEmpGroupDo selectById(Long empGroupId) {
        AttendanceEmpGroupPo empGroupPo = (AttendanceEmpGroupPo) attendanceEmpGroupRepository.selectById(empGroupId);
        return convertEmpGroupDo(empGroupPo);
    }

    public AttendanceEmpGroupDo selectByKeyAndGroupType(String businessKey, String groupType, Long tenantId) {
        AttendanceEmpGroupPo empGroupPo = (AttendanceEmpGroupPo) attendanceEmpGroupRepository.selectByKeyAndGroupType(businessKey, groupType, tenantId);
        return convertEmpGroupDo(empGroupPo);
    }

    private AttendanceEmpGroupDo convertEmpGroupDo(AttendanceEmpGroupPo empGroupPo) {
        AttendanceEmpGroupDo empGroupDo = ObjectConverter.convert(empGroupPo, AttendanceEmpGroupDo.class);
        return empGroupDo;
    }

    public List<AttendanceEmpGroupDo> selectByKeysAndGroupType(List<String> businessKeys, String groupType, Long tenantId) {
        List<AttendanceEmpGroupPo> list = attendanceEmpGroupRepository.selectByKeysAndGroupType(businessKeys, groupType, tenantId);
        return ObjectConverter.convertList(list, AttendanceEmpGroupDo.class);
    }
}
