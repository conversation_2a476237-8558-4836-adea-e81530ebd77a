package com.caidaocloud.attendance.core.integrate.listener;

import com.caidao1.commons.utils.SmsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.IOException;

public class SmsMessageListener implements MessageListener {
    @Autowired
    private RedisTemplate redisTemplate;
    @Value("${oa.mns.open:false}")
    private boolean oaOpen;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        // TODO Auto-generated method stub
        byte[] body = message.getBody();
        SmsMessage message1 = (SmsMessage) redisTemplate.getValueSerializer().deserialize(body);
        try {
            System.out.println("------调用发送短信------------------"+message1.getMobile()+",context="+message1.getContent());
            if (oaOpen){

            }else{
                new SmsUtil().sendSms(message1.getMobile(), message1.getContent());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
