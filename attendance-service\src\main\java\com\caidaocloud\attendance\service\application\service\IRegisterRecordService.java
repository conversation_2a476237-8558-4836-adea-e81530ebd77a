package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.attendance.service.interfaces.dto.ClockInAdjustDto;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordDto;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;
import com.caidaocloud.attendance.service.interfaces.dto.WaAnalyzeDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;

import java.util.List;

/**
 * 考勤打卡记录服务
 *
 * <AUTHOR>
 * @Date 2021/3/19
 */
public interface IRegisterRecordService {
    AttendancePageResult<RegisterRecordDto> getRegisterRecordPageList(RegisterRecordRequestDto requestDto);

    AttendancePageResult<RegisterRecordDto> getRegisterRecordPageList(RegisterRecordRequestDto requestDto, UserInfo userInfo);

    WaAnalyzeDto getAnalyzeDetailById(Integer analyzeId);

    AttendancePageResult<RegisterRecordDto> getRegisterRecordListByEmpId(RegisterRecordRequestDto requestDto, UserInfo userInfo);

    Result<Boolean> revokeEmpReg(Long id, String revokeReason);

    void deleteOutworkRecords(List<Integer> ids);

    int getWaAnalyzeUseRegisterRecordCount(List<Integer> recordIds);

    /**
     * 批量删除打卡记录
     *
     * @param recordIds
     * @return
     */
    int deleteRegisterRecordByIds(List<Integer> recordIds);

    /**
     * 批量修改打卡记录打卡地点状态
     *
     * @param recordIds
     * @param clockSiteStatus
     */
    void updateClockSiteStatus(List<Integer> recordIds, Integer clockSiteStatus);

    /**
     * 调整打卡数据
     *
     * @param adjustDto
     */
    void adjustClockIn(ClockInAdjustDto adjustDto);

    /**
     * 查询打卡记录
     *
     * @param empId
     * @param belongDate
     * @param type
     * @param registerType
     * @param sort
     * @return
     */
    List<WaRegisterRecord> listWaRegisterRecord(Long empId, Long belongDate, Integer type, Integer registerType, String sort);

    List<WaRegisterRecordDo> getRegisterRecordPageList(PageBean pageBean, String belongOrgId, Long startTime, Long endTime,
                                                       List<Long> empIds, List<Integer> types, Integer clockSiteStatus);

    AttendancePageResult<WaRegisterRecordDo> getPageList(AttendanceBasePage basePage, String belongOrgId, Long startTime, Long endTime,
                                                         List<Long> empIds, List<Integer> types, Integer clockSiteStatus);

    List<Long> getRegEmpIdList(String belongOrgId, Long startTime, Long endTime);

    /**
     * 查询员工考勤分析出勤规则
     *
     * @param belongOrgId
     * @param empIds
     * @param date
     * @return
     */
    List<EmpParseGroup> selectEmpParseGroupListByDate(String belongOrgId, List<Long> empIds, Long date);

    /**
     * 查询员工考勤分析出勤规则
     *
     * @param belongOrgId
     * @param empIds
     * @param startDate
     * @param endDate
     * @return
     */
    List<EmpParseGroup> selectEmpParseGroupListByDateRange(String belongOrgId, List<Long> empIds, Long startDate, Long endDate);

    /**
     * 批量更新员工考勤记录状态
     *
     * @param belongOrgId
     * @param ids
     * @param userId
     * @param ifValid
     * @return
     */
    int updateValidState(String belongOrgId, List<Integer> ids, Long userId, Integer ifValid);
}
