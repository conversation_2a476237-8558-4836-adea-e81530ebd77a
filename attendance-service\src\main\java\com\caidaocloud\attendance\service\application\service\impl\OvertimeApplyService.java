package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.service.util.SessionBeanUtil;
import com.caidao1.commons.utils.*;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidao1.xss.test.cache.RedisService;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.dto.EmpOverInfo;
import com.caidaocloud.attendance.core.wa.dto.OvertimeApplyBean;
import com.caidaocloud.attendance.core.wa.dto.ShiftRestPeriods;
import com.caidaocloud.attendance.core.wa.dto.clock.ClockAnalyseDto;
import com.caidaocloud.attendance.core.wa.dto.ot.GetOtTimeResultDto;
import com.caidaocloud.attendance.core.wa.dto.ot.OtCompensateTypeListDto;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiOvertimeDto;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiWorkTimeBaseDto;
import com.caidaocloud.attendance.core.wa.dto.shift.OvertimeRestPeriodsDto;
import com.caidaocloud.attendance.core.wa.dto.shift.RestPeriodDto;
import com.caidaocloud.attendance.core.wa.enums.AppTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.FileBaseInfoDto;
import com.caidaocloud.attendance.service.application.dto.OvertimeCarryForwardDto;
import com.caidaocloud.attendance.service.application.dto.overtime.OtLeftDurationDto;
import com.caidaocloud.attendance.service.application.dto.overtime.OvertimeLeftDurationDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.event.publish.AnalyseClockRecordPublish;
import com.caidaocloud.attendance.service.application.event.publish.AttendanceWorkflowMsgPublish;
import com.caidaocloud.attendance.service.application.event.publish.MessageParams;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.CheckMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.OtRecordMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkOvertimeMapper;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.OverApplyRevokeDto;
import com.caidaocloud.attendance.service.interfaces.dto.OverApplySaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.OverTimeDto;
import com.caidaocloud.attendance.service.interfaces.dto.OvertimeDateQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.group.GroupDetailDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OtLeftDurationRequestDto;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeApplyTimeVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.DeleteStatusEnum;
import com.caidaocloud.em.SysConfigsEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.github.miemiedev.mybatis.paginator.domain.Paginator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jettison.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OvertimeApplyService implements IOvertimeApplyService {
    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    @Resource
    private WaEmpOvertimeMapper waEmpOvertimeMapper;
    @Resource
    private WaOvertimeFileMapper waOvertimeFileMapper;
    @Resource
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Resource
    private ISessionService sessionService;
    @Resource
    private MessageResource messageResource;
    @Resource
    private WaGroupMapper waGroupMapper;
    @Resource
    private WaEmpGroupMapper waEmpGroupMapper;
    @Resource
    private WaMapper waMapper;
    @Resource
    private WaCommonService waCommonService;
    @Resource
    private OverTimeTypeDo overTimeTypeDo;
    @Resource
    private WaConfigMapper waConfigMapper;
    @Resource
    private CheckMapper checkMapper;
    @Resource
    private WaEmpOvertimeDetailMapper waEmpOvertimeDetailMapper;
    @Resource
    private PreEmpOvertimeMapper preEmpOvertimeMapper;
    @Autowired
    private WaSobService waSobService;
    @Autowired
    private EmpCompensatoryQuotaDo empCompensatoryQuotaDo;
    @Autowired
    private AttendanceWorkflowMsgPublish attendanceWorkflowMsgPublish;
    @Autowired
    private WaEmpOvertimeDo waEmpOvertimeDo;
    @Autowired
    private IGroupService groupService;
    @Autowired
    private QuotaService quotaService;
    @Autowired
    private IOverTimeService overTimeService;
    @Autowired
    private IConfigService configService;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private IWfService wfService;
    @Autowired
    private WorkflowCallBackService workflowCallBackService;
    @Autowired
    private WaEmpTravelDaytimeDo waEmpTravelDaytimeDo;
    @Autowired
    private WaWorkflowRevokeDo workflowRevokeDo;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private WorkOvertimeMapper workOvertimeMapper;
    @Autowired
    private WaCompensatoryQuotaRecordDo waCompensatoryQuotaRecordDo;
    @Autowired
    private WaCompensatoryQuotaUseMapper waCompensatoryQuotaUseMapper;
    @Autowired
    private WaEmpLeaveDo waEmpLeaveDo;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private EmpCompensatoryCaseApplyDo empCompensatoryCaseApplyDo;
    @Autowired
    private EmpOvertimeDetailDo empOvertimeDetailDo;
    @Autowired
    private SysEmpInfoDo sysEmpInfoDo;
    @Autowired
    private OtRecordMapper otRecordMapper;
    @Resource
    private AnalyseClockRecordPublish analyseClockRecordPublish;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Override
    public Map getOvertimeInfoById(Integer otId) {
        Map<String, Object> map = new HashMap<>();
        WaEmpOvertime overtime = this.waEmpOvertimeMapper.selectByPrimaryKey(otId);
        if (overtime != null) {
            Long startTime = overtime.getStartTime();
            Long endTime = overtime.getEndTime();
            Long startDate = DateUtil.getTimesampByDateStr2(DateUtil.getDateStrByTimesamp(startTime));
            Long endDate = DateUtil.getTimesampByDateStr2(DateUtil.getDateStrByTimesamp(endTime));
            Long startMin = startTime - startDate;
            Long endMin = endTime - endDate;
            try {
                map = JSONUtils.toMap(JSONUtils.ObjectToJson(overtime));
            } catch (JSONException e) {
                log.error(e.getMessage(), e);
            }
            map.put("startTime", startDate);
            map.put("endTime", endDate);
            map.put("stime", startMin / 60L);
            map.put("etime", endMin / 60L);
            WaOvertimeFileExample fileExample = new WaOvertimeFileExample();
            fileExample.createCriteria().andOtIdEqualTo(otId);
            List<WaOvertimeFile> fileList = this.waOvertimeFileMapper.selectByExample(fileExample);
            if (CollectionUtils.isNotEmpty(fileList)) {
                List<FileBaseInfoDto> fileBaseInfoDtoList = new ArrayList<>();
                fileList.forEach(row -> {
                    if (row.getUrl().contains(",")) {
                        String[] ids = row.getUrl().split(",");
                        String[] names = row.getFileName().split(",");
                        for (int i = 0; i < ids.length; i++) {
                            fileBaseInfoDtoList.add(new FileBaseInfoDto(ids[i], names[i]));
                        }
                    } else {
                        fileBaseInfoDtoList.add(new FileBaseInfoDto(row.getUrl(), row.getFileName()));
                    }
                });
                map.put("files", fileBaseInfoDtoList);
            }
            SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(overtime.getEmpid());
            if (empInfo != null) {
                map.put("empName", empInfo.getEmpName());
            }
        }
        return map;
    }

    @Override
    public Result<?> saveOtData(OverApplySaveDto overtimeSaveDto, AppTypeEnum appType) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();
        Long empId = overtimeSaveDto.getEmpId();
        if (null == overtimeSaveDto.getEmpId()) {
            empId = userInfo.getStaffId();
            overtimeSaveDto.setEmpId(empId);
        }

        // 加班规则
        Result<OverTimeDto> checkOtType = checkAndGetOtType(overtimeSaveDto.getOvertimeTypeId());
        if (!checkOtType.isSuccess()) {
            return Result.fail(checkOtType.getMsg());
        }
        OverTimeDto overTimeDto = checkOtType.getData();
        LogRecordContext.putVariable("content", overTimeDto.getTypeName());

        // 考勤方案规则
        Result<GroupDetailDto> checkWaGroup = checkWaGroup(overtimeSaveDto);
        if (!checkWaGroup.isSuccess()) {
            return Result.fail(checkWaGroup.getMsg());
        }
        GroupDetailDto groupDetail = checkWaGroup.getData();
        if (groupDetail.getReasonMust() != null
                && groupDetail.getReasonMust() && StringUtil.isEmptyOrNull(overtimeSaveDto.getReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_REASON_MUST, Boolean.FALSE);
        }

        // 保存加班单据
        OvertimeApplyBean overtimeApplyBean = new OvertimeApplyBean();
        overtimeApplyBean.setCorpId(ConvertHelper.longConvert(userInfo.getTenantId()));
        overtimeApplyBean.setBelongId(userInfo.getTenantId());
        overtimeApplyBean.setUserid(userInfo.getUserId());
        overtimeApplyBean.setEmpid(empId);
        overtimeApplyBean.setStartTime(overtimeSaveDto.doGetOtStartTime());
        overtimeApplyBean.setEndTime(overtimeSaveDto.doGetOtEndTime());
        overtimeApplyBean.setCompensateType(overTimeDto.getCompensateType());
        overtimeApplyBean.setIsValidateTimeControl(groupDetail.getIsOpenTimeControl());
        overtimeApplyBean.setOtId(overtimeSaveDto.getWaid());
        overtimeApplyBean.setReason(overtimeSaveDto.getReason());
        overtimeApplyBean.setFile(overtimeSaveDto.getFile());
        overtimeApplyBean.setFileName(overtimeSaveDto.getFileName());
        overtimeApplyBean.setOvertimeTypeId(overtimeSaveDto.getOvertimeTypeId());
        overtimeApplyBean.setBatchId(overtimeSaveDto.getBatchId());
        Result<?> result = SpringUtils.getBean(OvertimeApplyService.class)
                .saveOvertimeApply(overtimeApplyBean, appType, groupDetail, overtimeSaveDto.getOvertimeDate());
        if (!result.isSuccess() || null == result.getData()) {
            return result;
        }
        OvertimeApplyTimeVo applyTimeVo = (OvertimeApplyTimeVo) result.getData();
        if (applyTimeVo.isOpenWf() && null != applyTimeVo.getBeginWorkflowDto()) {
            startWorkflow(applyTimeVo.getBeginWorkflowDto());
            //this.updatePreOtStatus(applyTimeVo.getPreOtId(), 1);
        }

        // 推送打卡分析消息
        if (applyTimeVo.isIfAnalyseClock()) {
            long startDate = DateUtil.getOnlyDate(new Date(overtimeApplyBean.getStartTime() * 1000));
            long endDate = DateUtil.getOnlyDate(new Date(overtimeApplyBean.getEndTime() * 1000));
            ClockAnalyseDto dto = new ClockAnalyseDto(overtimeApplyBean.getBelongId(),
                    Lists.newArrayList(overtimeApplyBean.getEmpid()),
                    startDate,
                    null,
                    ClockAnalyseDto.doContractDateRange(startDate, endDate));
            analyseClockRecordPublish.publish(JSON.toJSONString(dto));
        }
        return result;
    }

    /**
     * 保存加班单时：补偿方式校验
     *
     * @param startWorkTimeDetail
     * @param endWorkTimeDetail
     * @param overtimeApplyBean
     * @param shiftDefMap
     * @return
     */
    private Result checkCompensateType(WaWorktimeDetail startWorkTimeDetail,
                                       WaWorktimeDetail endWorkTimeDetail,
                                       OvertimeApplyBean overtimeApplyBean,
                                       Map<Integer, WaShiftDef> shiftDefMap) {
        // 加班开始日期所在班次的最晚下班打卡时间
        Long startOffDutyEndTime = getShiftOffDutyEndTime(shiftDefMap, startWorkTimeDetail);
        if (overtimeApplyBean.getEndTime() <= startOffDutyEndTime) {
            return Result.ok(Boolean.TRUE);
        }

        // 检查加班开始结束日期所在班次的日期类型是否一致，如一致则不校验
        Integer startDateDayType = startWorkTimeDetail.getDateType();
        Integer endDateDayType = endWorkTimeDetail.getDateType();
        if (startDateDayType.equals(endDateDayType)) {
            return Result.ok(Boolean.TRUE);
        }

        // 获取开始日期对应班次的补偿类型
        boolean startFlag = false;
        List<Map> startDateCompensateTypeList = waConfigMapper.getCompensateTypeList(overtimeApplyBean.getEmpid(),
                startDateDayType);
        if (CollectionUtils.isNotEmpty(startDateCompensateTypeList)) {
            startFlag = startDateCompensateTypeList.stream()
                    .anyMatch(map -> overtimeApplyBean.getCompensateType().equals(Integer.valueOf(map.get("value").toString())));
        }

        if (!startFlag) {// 补偿类型不一致，请拆分加班单重新申请
            return Result.fail(messageResource.getMessage("L005722", new Object[]{}, new Locale(SessionHolder.getLang())));
        }

        // 获取结束日期对应的补偿类型
        boolean endFlag = false;
        List<Map> endDateCompensateTypeList = waConfigMapper.getCompensateTypeList(overtimeApplyBean.getEmpid(),
                endDateDayType);
        if (CollectionUtils.isNotEmpty(endDateCompensateTypeList)) {
            endFlag = endDateCompensateTypeList.stream()
                    .anyMatch(map -> overtimeApplyBean.getCompensateType().equals(Integer.valueOf(map.get("value").toString())));
        }

        if (!endFlag) {
            return Result.fail(messageResource.getMessage("L005722", new Object[]{}, new Locale(SessionHolder.getLang())));
        }

        return Result.ok(Boolean.TRUE);
    }

    /**
     * 保存加班单时：计算补偿方式
     *
     * @param overtimeApplyBean
     * @param dateType
     * @return
     */
    private Result<Boolean> calCompensateType(OvertimeApplyBean overtimeApplyBean, Integer dateType) {
        if (null != overtimeApplyBean.getCompensateType()) {
            return Result.ok(Boolean.TRUE);
        }
        // 如果补偿类型为空，查询默认的加班补偿类型（当且仅当该日期类型的补偿类型只有一种）
        List<Map> compensateTypeList = waConfigMapper.getCompensateTypeList(overtimeApplyBean.getEmpid(), dateType);
        if (CollectionUtils.isEmpty(compensateTypeList)) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.NOT_CHOOSE_COMPENSATORY_TYPE, Boolean.FALSE).getMsg());
        }
        Integer compensateType = (Integer) compensateTypeList.get(0).get("value");
        if (compensateType == 4) {//员工自由选择
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.NOT_CHOOSE_COMPENSATORY_TYPE, Boolean.FALSE).getMsg());
        }
        overtimeApplyBean.setCompensateType(compensateType);
        return Result.ok(Boolean.TRUE);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<?> saveOvertimeApply(OvertimeApplyBean overtimeApplyBean,
                                       AppTypeEnum appType,
                                       GroupDetailDto waGroupDto,
                                       Long selectOvertimeDate) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(overtimeApplyBean.getEmpid());
        int tmType = (empInfo.getTmType() == null || empInfo.getTmType() == 0) ? 1 : empInfo.getTmType();
        empInfo.setTmType(tmType);

        // 加班时间
        long startTime = overtimeApplyBean.getStartTime();
        long endTime = overtimeApplyBean.getEndTime();
        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(endTime * 1000));
        Long preDate = DateUtil.addDate(startDate * 1000, -1);
        Long nextDate = DateUtil.addDate(endDate * 1000, 1);

        // 员工排班
        Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(userInfo.getTenantId(),
                overtimeApplyBean.getEmpid(), tmType, preDate, nextDate);
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(userInfo.getTenantId());
        WaWorktimeDetail startWorkTimeDetail = pbMap.get(startDate);
        WaWorktimeDetail endWorkTimeDetail = pbMap.get(endDate);

        // 返回结果
        OvertimeApplyTimeVo overtimeApplyTimeVo = new OvertimeApplyTimeVo();

        // 查询员工当前时间所在的考勤方案
        Integer empWaGroupId = null;
        Map groupMap = null;
        List<Map> nowGroupRuleList = getNowEmpBelongGroup(empInfo.getEmpid());
        if (CollectionUtils.isNotEmpty(nowGroupRuleList)) {
            groupMap = nowGroupRuleList.get(0);
            empWaGroupId = (Integer) groupMap.get("wa_group_id");
        }
        // 跨夜加班是否归属至加班开始日期
        boolean overtimeBelongStartDate = checkOvertimeBelong(groupMap);

        // 指定加班归属日期
        WaWorktimeDetail selectOtDateWorktimeDetail = null != selectOvertimeDate ? pbMap.get(selectOvertimeDate) : null;

        // 计算并校验补偿方式
        if (null == selectOtDateWorktimeDetail) {
            // 检查加班开始日期所在班次的加班类型
            Result<Boolean> compensateTypeResult = calCompensateType(overtimeApplyBean, startWorkTimeDetail.getDateType());
            if (!compensateTypeResult.isSuccess()) {
                return compensateTypeResult;
            }
            // 加班跨夜时，检查加班开始和结束日期所在班次的加班类型
            if (endDate > startDate && !overtimeBelongStartDate) {
                Result<?> checkCompensateType = checkCompensateType(startWorkTimeDetail, endWorkTimeDetail, overtimeApplyBean,
                        shiftDefMap);
                if (!checkCompensateType.isSuccess()) {
                    return checkCompensateType;
                }
            }
        }

        // 判断加班时间是否在于前一日班次的范围内
        WaWorktimeDetail preWorkTimeDetail = pbMap.get(preDate);
        Long preOffDutyEndTime = getShiftOffDutyEndTime(shiftDefMap, preWorkTimeDetail);
        boolean belongPreDay = null != preOffDutyEndTime && endTime <= preOffDutyEndTime;

        // 计算加班时长
        String tenantId = userInfo.getTenantId();
        long totalDuration = endTime - startTime;
        if (null == selectOtDateWorktimeDetail) {
            if (endDate > startDate) {
                totalDuration -= calRestTotalTime(tenantId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
                totalDuration -= calRestTotalTime(tenantId, startTime, endTime, endWorkTimeDetail, shiftDefMap);
            } else {
                totalDuration -= calRestTotalTime(tenantId, startTime, endTime, preWorkTimeDetail, shiftDefMap);
                totalDuration -= calRestTotalTime(tenantId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
            }
        } else {
            totalDuration -= calRestTotalTime(tenantId, startTime, endTime, selectOtDateWorktimeDetail, shiftDefMap);
        }

        // 查询加班规则并进行校验
        OverTimeDto overTimeDto = overTimeService.getOtType(overtimeApplyBean.getOvertimeTypeId());
        Float minOvertimeUnit = overTimeDto.getMinOvertimeUnit();
        WaGroup waGroup = waGroupMapper.selectByPrimaryKey(empWaGroupId);
        if (waGroup != null && minOvertimeUnit != null) {
            BigDecimal workingTime = waGroup.getWorkingTime();
            Integer minOvertimeUnitType = Optional.ofNullable(overTimeDto.getMinOvertimeUnitType()).orElse(2);
            totalDuration = BigDecimal.valueOf(OvertimeUnitEnum.HOUR.getTime(workingTime, totalDuration, minOvertimeUnit, minOvertimeUnitType)).longValue();
        }

        // 保存加班单据
        WaEmpOvertime overtime = new WaEmpOvertime();
        overtime.setTenantId(userInfo.getTenantId());
        overtime.setCompensateType(overtimeApplyBean.getCompensateType());
        overtime.setCrttime(System.currentTimeMillis() / 1000);
        overtime.setCrtuser(overtimeApplyBean.getUserid());
        if (null == selectOtDateWorktimeDetail) {
            overtime.setDateType(belongPreDay
                    ? preWorkTimeDetail.getDateType().shortValue()
                    : startWorkTimeDetail.getDateType().shortValue());
        } else {
            overtime.setDateType(selectOtDateWorktimeDetail.getDateType().shortValue());
        }
        overtime.setApprovalNum(0);
        overtime.setEmpid(overtimeApplyBean.getEmpid());
        overtime.setStartTime(overtimeApplyBean.getStartTime());
        overtime.setEndTime(overtimeApplyBean.getEndTime());
        overtime.setOtFormNo("");
        overtime.setReason(overtimeApplyBean.getReason());
        overtime.setStatus((short) 1);
        overtime.setOtDuration((int) (totalDuration / 60));
        overtime.setOtId(overtimeApplyBean.getOtId());
        overtime.setOvertimeTypeId(overtimeApplyBean.getOvertimeTypeId());
        overtime.setPreOtId(overtimeApplyBean.getPreOtId());
        overtime.setBatchId(overtimeApplyBean.getBatchId());
        overtime.setDataSource("MANUAL");
        overtime.setPeriodOtDuration(getPeriodOtDuration(tenantId, empInfo.getEmpid(), startDate));

        // 时间重叠校验
        Map<String, Object> params = new HashMap<>();
        params.put("empid", overtime.getEmpid());
        params.put("start", overtime.getStartTime());
        params.put("end", overtime.getEndTime());
        if (checkMapper.checkOtRepeat(params) > 0) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.TIME_OVERAPPLY, Boolean.FALSE).getMsg());
        }

        // 根据考勤分组校验最小申请时长&最小单位
        String checkResult = checkOtMinApplyNum(overTimeDto, overtime, startDate);
        if (StringUtils.isNotEmpty(checkResult)) {
            return Result.fail(checkResult);
        }

        // 校验加班时效性、加班附件上传校验
        if (BooleanUtils.isTrue(overtimeApplyBean.getIsValidateTimeControl())) {
            Result<Boolean> validateOverTimePrescription = validateOverTimePrescription(startTime, appType, waGroupDto);
            if (!validateOverTimePrescription.isSuccess()) {
                return Result.fail(validateOverTimePrescription.getMsg());
            }
        }

        // 加班上传附件校验
        Result<Boolean> checkFile = checkFile(tenantId, overtimeApplyBean, totalDuration, startWorkTimeDetail);
        if (!checkFile.isSuccess()) {
            return Result.fail(checkFile.getMsg());
        }

        // 仅可申请一种补偿类型/当天
        String errMsg = checkCompensateType(overtimeApplyBean.getEmpid(), startTime, endTime, overtimeApplyBean.getCompensateType(), groupMap);
        if (StringUtils.isNotBlank(errMsg)) {
            return Result.fail(errMsg);
        }

        if (overtime.getOtId() != null) {// 重新发起
            deleteOt(overtime.getOtId());
        }

        // 开启事物
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(def);
        try {
            waEmpOvertimeMapper.insertSelective(overtime);
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            platformTransactionManager.rollback(transactionStatus);
            log.error("申请加班失败:{}", e.getMessage(), e);
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.APPLY_OVERTIME_FAILED, null).getMsg());
        }

        // 保存加班明细
        if (null == selectOtDateWorktimeDetail) {
            if (endDate > startDate) {
                WaEmpOvertimeDetail startDayDetail = new WaEmpOvertimeDetail();
                startDayDetail.setCrttime(DateUtil.getCurrentTime(true));
                startDayDetail.setCrtuser(overtime.getCrtuser());
                startDayDetail.setOvertimeId(overtime.getOtId());
                startDayDetail.setDateType(startWorkTimeDetail.getDateType().shortValue());
                startDayDetail.setOvertimeTypeId(overtime.getOvertimeTypeId());
                startDayDetail.setStartTime(overtimeApplyBean.getStartTime());
                startDayDetail.setEndTime(endDate);
                startDayDetail.setRealDate(startDate);
                long startTimeDuration = startDayDetail.getEndTime() - startDayDetail.getStartTime();
                startTimeDuration -= calRestTotalTime(tenantId, startDayDetail.getStartTime(), startDayDetail.getEndTime(), startWorkTimeDetail, shiftDefMap);
                startTimeDuration = startTimeDuration / 60;
                startDayDetail.setTimeDuration((int) startTimeDuration);

                WaEmpOvertimeDetail endDayDetail = new WaEmpOvertimeDetail();
                endDayDetail.setCrttime(DateUtil.getCurrentTime(true));
                endDayDetail.setCrtuser(overtime.getCrtuser());
                endDayDetail.setOvertimeId(overtime.getOtId());
                endDayDetail.setDateType(endWorkTimeDetail.getDateType().shortValue());
                endDayDetail.setOvertimeTypeId(overtime.getOvertimeTypeId());
                if (!Objects.equals(endWorkTimeDetail.getDateType(), startWorkTimeDetail.getDateType())) {
                    endDayDetail.setOvertimeTypeId(Optional.ofNullable(getSecondOvertimeTypeId(overtime.getEmpid(),
                            endWorkTimeDetail.getDateType(), endDate)).orElse(endDayDetail.getOvertimeTypeId()));
                }
                endDayDetail.setStartTime(endDate);
                endDayDetail.setEndTime(overtimeApplyBean.getEndTime());
                endDayDetail.setRealDate(endDate);
                long endTimeDuration = endDayDetail.getEndTime() - endDayDetail.getStartTime();
                endTimeDuration -= calRestTotalTime(tenantId, endDayDetail.getStartTime(), endDayDetail.getEndTime(), startWorkTimeDetail, shiftDefMap);
                endTimeDuration -= calRestTotalTime(tenantId, endDayDetail.getStartTime(), endDayDetail.getEndTime(), endWorkTimeDetail, shiftDefMap);
                endTimeDuration = endTimeDuration / 60;
                endDayDetail.setTimeDuration((int) endTimeDuration);

                if (overtimeBelongStartDate) {
                    Long startOffDutyEndTime = getShiftOffDutyEndTime(shiftDefMap, startWorkTimeDetail);
                    if (endTime <= startOffDutyEndTime) {
                        startDayDetail.setEndTime(overtimeApplyBean.getEndTime());
                        startDayDetail.setTimeDuration(overtime.getOtDuration());
                        waEmpOvertimeDetailMapper.insertSelective(startDayDetail);
                    } else {
                        startDayDetail.setEndTime(startOffDutyEndTime);
                        long timeDuration = startDayDetail.getEndTime() - startDayDetail.getStartTime();
                        timeDuration -= calRestTotalTime(tenantId, startDayDetail.getStartTime(), startDayDetail.getEndTime(), startWorkTimeDetail, shiftDefMap);
                        timeDuration -= calRestTotalTime(tenantId, startDayDetail.getStartTime(), startDayDetail.getEndTime(), endWorkTimeDetail, shiftDefMap);
                        timeDuration = timeDuration / 60;
                        startDayDetail.setTimeDuration((int) timeDuration);
                        waEmpOvertimeDetailMapper.insertSelective(startDayDetail);

                        endDayDetail.setStartTime(startOffDutyEndTime);
                        endDayDetail.setTimeDuration(overtime.getOtDuration() - startDayDetail.getTimeDuration());
                        waEmpOvertimeDetailMapper.insertSelective(endDayDetail);
                    }
                } else {
                    waEmpOvertimeDetailMapper.insertSelective(startDayDetail);
                    waEmpOvertimeDetailMapper.insertSelective(endDayDetail);
                }

                // 检查加班申请后是否进行打卡分析
                if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(startWorkTimeDetail.getDateType())
                        && null != WaShiftDo.ifEnableAnalyseClockByOt(shiftDefMap.get(startWorkTimeDetail.getShiftDefId()))) {
                    overtimeApplyTimeVo.setIfAnalyseClock(Boolean.TRUE);
                }
            } else {
                WaEmpOvertimeDetail currentDayDetail = new WaEmpOvertimeDetail();
                currentDayDetail.setCrttime(DateUtil.getCurrentTime(true));
                currentDayDetail.setCrtuser(overtime.getCrtuser());
                currentDayDetail.setOvertimeId(overtime.getOtId());
                currentDayDetail.setDateType(startWorkTimeDetail.getDateType().shortValue());
                currentDayDetail.setOvertimeTypeId(overtime.getOvertimeTypeId());
                currentDayDetail.setStartTime(overtimeApplyBean.getStartTime());
                currentDayDetail.setEndTime(overtimeApplyBean.getEndTime());
                currentDayDetail.setRealDate(startDate);
                currentDayDetail.setTimeDuration(overtime.getOtDuration());
                if (null != preOffDutyEndTime) {
                    if (endTime <= preOffDutyEndTime) {
                        currentDayDetail.setDateType(preWorkTimeDetail.getDateType().shortValue());
                        currentDayDetail.setRealDate(preDate);
                        waEmpOvertimeDetailMapper.insertSelective(currentDayDetail);
                    } else if (startTime >= preOffDutyEndTime) {
                        waEmpOvertimeDetailMapper.insertSelective(currentDayDetail);
                    } else {
                        WaEmpOvertimeDetail preDayDetail = FastjsonUtil.convertObject(currentDayDetail, WaEmpOvertimeDetail.class);
                        if (!Objects.equals(preWorkTimeDetail.getDateType(), startWorkTimeDetail.getDateType())) {
                            preDayDetail.setOvertimeTypeId(Optional.ofNullable(getSecondOvertimeTypeId(overtime.getEmpid(),
                                    preWorkTimeDetail.getDateType(), preDate)).orElse(currentDayDetail.getOvertimeTypeId()));
                        }
                        preDayDetail.setDateType(preWorkTimeDetail.getDateType().shortValue());
                        preDayDetail.setRealDate(preDate);
                        preDayDetail.setEndTime(preOffDutyEndTime);
                        long timeDuration = preDayDetail.getEndTime() - preDayDetail.getStartTime();
                        timeDuration -= calRestTotalTime(tenantId, preDayDetail.getStartTime(), preDayDetail.getEndTime(), preWorkTimeDetail, shiftDefMap);
                        timeDuration -= calRestTotalTime(tenantId, preDayDetail.getStartTime(), preDayDetail.getEndTime(), startWorkTimeDetail, shiftDefMap);
                        timeDuration = timeDuration / 60;
                        preDayDetail.setTimeDuration((int) timeDuration);
                        waEmpOvertimeDetailMapper.insertSelective(preDayDetail);

                        currentDayDetail.setStartTime(preOffDutyEndTime);
                        currentDayDetail.setTimeDuration(overtime.getOtDuration() - preDayDetail.getTimeDuration());
                        waEmpOvertimeDetailMapper.insertSelective(currentDayDetail);
                    }
                } else {
                    waEmpOvertimeDetailMapper.insertSelective(currentDayDetail);
                }

                // 检查加班申请后是否进行打卡分析
                if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(startWorkTimeDetail.getDateType())
                        && null != WaShiftDo.ifEnableAnalyseClockByOt(shiftDefMap.get(startWorkTimeDetail.getShiftDefId()))) {
                    overtimeApplyTimeVo.setIfAnalyseClock(Boolean.TRUE);
                }
            }
        } else {
            // 指定加班归属日期
            WaEmpOvertimeDetail currentDayDetail = new WaEmpOvertimeDetail();
            currentDayDetail.setCrttime(DateUtil.getCurrentTime(true));
            currentDayDetail.setCrtuser(overtime.getCrtuser());
            currentDayDetail.setOvertimeId(overtime.getOtId());
            currentDayDetail.setDateType(selectOtDateWorktimeDetail.getDateType().shortValue());
            currentDayDetail.setOvertimeTypeId(overtime.getOvertimeTypeId());
            currentDayDetail.setStartTime(overtimeApplyBean.getStartTime());
            currentDayDetail.setEndTime(overtimeApplyBean.getEndTime());
            currentDayDetail.setRealDate(selectOvertimeDate);
            currentDayDetail.setTimeDuration(overtime.getOtDuration());
            waEmpOvertimeDetailMapper.insertSelective(currentDayDetail);

            // 检查加班申请后是否进行打卡分析
            if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(selectOtDateWorktimeDetail.getDateType())
                    && null != WaShiftDo.ifEnableAnalyseClockByOt(shiftDefMap.get(selectOtDateWorktimeDetail.getShiftDefId()))) {
                overtimeApplyTimeVo.setIfAnalyseClock(Boolean.TRUE);
            }
        }

        // 保存附件
        if (StringUtils.isNotBlank(overtimeApplyBean.getMyFiles())) {
            String[] fileArr = overtimeApplyBean.getMyFiles().split(",");
            WaOvertimeFile otFile = new WaOvertimeFile();
            otFile.setOtId(overtime.getOtId());
            otFile.setUrl(overtimeApplyBean.getMyFiles());
            String s = "";
            for (String fileStr : fileArr) {
                if (s.length() > 0) {
                    s += ",";
                }
                s += fileStr.substring(fileStr.lastIndexOf("/") + 1);
            }
            otFile.setFileName(s);
            waOvertimeFileMapper.insertSelective(otFile);
        } else if (StringUtils.isNotBlank(overtimeApplyBean.getFile())) {
            WaOvertimeFile otFile = new WaOvertimeFile();
            otFile.setOtId(overtime.getOtId());
            otFile.setUrl(overtimeApplyBean.getFile());
            otFile.setFileName(overtimeApplyBean.getFileName());
            waOvertimeFileMapper.insertSelective(otFile);
        }

        // 开启流程
        if (null == overtimeApplyBean.getBatchId() && overtime.getOtId() != null) {
            Result<?> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.OVERTIME.getCode());
            if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
            }
            Boolean workflowEnabledResultData = (Boolean) checkWorkflowEnableResult.getData();
            if (!workflowEnabledResultData) {
                if (configService.checkSwitchStatus(SysConfigsEnum.OVERTIME_WORKFLOW_SWITCH.name())) {
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
                }
            }
            if (!workflowEnabledResultData) {
                WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
                wfCallbackResultDto.setBusinessKey(String.format("%s_%s", overtime.getOtId(), BusinessCodeEnum.OVERTIME.getCode()));
                wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
                wfCallbackResultDto.setTenantId(empInfo.getBelongOrgId());
                workflowCallBackService.saveWfOtApproval(wfCallbackResultDto);
                //this.updatePreOtStatus(overtime.getPreOtId(), 1);
            } else {
                WfBeginWorkflowDto dto = new WfBeginWorkflowDto();
                dto.setFuncCode(BusinessCodeEnum.OVERTIME.getCode());
                dto.setBusinessId(overtime.getOtId().toString());
                dto.setApplicantId(empInfo.getEmpid().toString());
                dto.setApplicantName(empInfo.getEmpName());
                dto.setEventTime(startDate * 1000);
                dto.setEventEndTime(endDate * 1000);
                dto.setTimeSlot(getTimeSlot(overtime));
                overtimeApplyTimeVo.setOpenWf(true);
                overtimeApplyTimeVo.setBeginWorkflowDto(dto);
                overtimeApplyTimeVo.setPreOtId(overtime.getPreOtId());
            }
        }
        overtimeApplyTimeVo.setOriginalOtDuration(overtime.getOtDuration().doubleValue());
        overtimeApplyTimeVo.setWfBusKey(String.format("%s_%s", overtime.getOtId(), BusinessCodeEnum.OVERTIME.getCode()));
        return Result.ok(overtimeApplyTimeVo);
    }

    public void startWorkflow(WfBeginWorkflowDto dto) {
        try {
            Result<?> result = wfRegisterFeign.begin(dto);
            log.info("Overtime startWf result={}", FastjsonUtil.toJsonStr(result));

            if (null == result || !result.isSuccess() || null == result.getData()
                    || StringUtils.isBlank(result.getData().toString())) {
                deleteOt(Integer.valueOf(dto.getBusinessId()));
                throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, null).getMsg());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);

            deleteOt(Integer.valueOf(dto.getBusinessId()));

            if (e instanceof CDException) {
                if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_CONFIG_ERR, null).getMsg());
                } else {
                    throw e;
                }
            } else {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, null).getMsg());
            }
        }
    }

    public Integer getSecondOvertimeTypeId(Long empId, Integer dateType, Long endDate) {
        List<WaOvertimeType> overtimeTypes = overTimeTypeDo.getOvertimeType(empId, dateType, endDate);
        if (CollectionUtils.isEmpty(overtimeTypes)) {
            return null;
        }
        WaOvertimeType overtimeType = overtimeTypes.get(0);
        return overtimeType.getOvertimeTypeId();
    }

    public String getTimeSlot(WaEmpOvertime overtime) {
        return String.format("%s~%s", DateUtil.getTimeStrByTimesamp4(overtime.getStartTime()), DateUtil.getTimeStrByTimesamp4(overtime.getEndTime()));
    }

    @Transactional
    public void deleteOt(Integer otId) {
        if (otId != null) {
            WaEmpOvertimeDetailExample example = new WaEmpOvertimeDetailExample();
            WaEmpOvertimeDetailExample.Criteria criteria = example.createCriteria();
            criteria.andOvertimeIdEqualTo(otId);
            waEmpOvertimeDetailMapper.deleteByExample(example);
            WaOvertimeFileExample fileExample = new WaOvertimeFileExample();
            fileExample.createCriteria().andOtIdEqualTo(otId);
            waOvertimeFileMapper.deleteByExample(fileExample);
            waEmpOvertimeMapper.deleteByPrimaryKey(otId);
        }
    }

    /**
     * 校验加班时效性
     *
     * @param startTime
     * @param appType
     * @param waGroupDto
     * @return
     */
    public Result<Boolean> validateOverTimePrescription(long startTime, AppTypeEnum appType, GroupDetailDto waGroupDto) {
        if (waGroupDto.getIsOpenTimeControl() == null || !waGroupDto.getIsOpenTimeControl() || AppTypeEnum.APP_BACK.equals(appType)) {
            return Result.ok(true);
        }
        //0 提前 1 延后
        Integer timeControlType = waGroupDto.getTimeControlType();
        Float controlTimeHour = waGroupDto.getControlTimeDuration();
        if (timeControlType != null && controlTimeHour != null) {
            //判断单位是天还是小时：1 天 2 小时
            if (PreTimeUnitEnum.DAY.getIndex().equals(waGroupDto.getControlTimeUnit())) {
                // 如果是天需要*24，后边逻辑不用修改
                controlTimeHour *= 24;
            }
            Long curDateTime = DateUtil.getCurrentTime(true);
            Long lastApplyTime = 0L;
            Float controlTimeSecond = controlTimeHour * 60 * 60;
            //判断申请休假时间是否在假期申请有效时间范围内，不在有效范围内不允许申请
            if (timeControlType.equals(TimeControlTypeEnum.ADVANCE.getIndex())) {
                //假期时效性设置为提前
                lastApplyTime = startTime - controlTimeSecond.intValue();
            } else if (timeControlType.equals(TimeControlTypeEnum.DELAY.getIndex())) {
                //假期时效性设置为延后
                lastApplyTime = startTime + controlTimeSecond.intValue();
            }
            if (curDateTime > lastApplyTime) {
                return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TIMELINESS, Boolean.FALSE).getMsg());
            }
        }
        return Result.ok(true);
    }

    /**
     * 加班上传附件校验
     *
     * @param belongOrgId
     * @param overtimeApplyBean
     * @param jg
     * @param workTimeDetail
     * @return
     */
    private Result<Boolean> checkFile(String belongOrgId, OvertimeApplyBean overtimeApplyBean, Long jg, WaWorktimeDetail workTimeDetail) {
        long startTime = overtimeApplyBean.getStartTime();
        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));

        List<WaOvertimeType> overtimeTypeList = overTimeTypeDo.getOvertimeTypeList(belongOrgId, overtimeApplyBean.getEmpid(), startDate, workTimeDetail.getDateType());
        if (CollectionUtils.isEmpty(overtimeTypeList)) {
            return Result.ok(true);
        }
        WaOvertimeType overtimeType = overtimeTypeList.get(0);

        String myFiles = StringUtils.isNotBlank(overtimeApplyBean.getMyFiles()) ? overtimeApplyBean.getMyFiles() : overtimeApplyBean.getFile();
        if (overtimeType.getIsUploadFile() != null && overtimeType.getIsUploadFile() && StringUtils.isBlank(myFiles)) {
            if (overtimeType.getMinFileCheckTime() == null) {
                return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.PLEASE_UPLOAD_ATTACHMENTS, Boolean.FALSE).getMsg());
            } else {
                if (overtimeType.getMinFileCheckTime() * 60 <= jg.floatValue() / 60) {
                    return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.PLEASE_UPLOAD_ATTACHMENTS, Boolean.FALSE).getMsg());
                }
            }
        }
        return Result.ok(true);
    }

    //根据考勤分组校验
    @Deprecated
    public String checkOtDateType(Long empId, Integer dateType, Integer compensateType, Integer inv) {
        String result = "";
        Map<String, Object> params = new HashMap<>();
        params.put("empid", empId);
        params.put("compensateType", compensateType);
        params.put("dateType", dateType);
        List<Map> otDateTypeList = checkMapper.checkOtDateType(params);
        if (CollectionUtils.isEmpty(otDateTypeList)) {
            //result = "权限有限，请联系管理员!";
            result = messageResource.getMessage("L005717", new Object[]{}, new Locale(SessionHolder.getLang()));
        } else {
            //加班最小申请时长校验
            Float minApplyNum = (Float) otDateTypeList.get(0).get("min_apply_num");
            if (minApplyNum != null && inv < minApplyNum * 60) {
                //result = "加班最小时长" + min_apply_num + "小时！";
                return messageResource.getMessage("L005718", new Object[]{minApplyNum}, new Locale(SessionHolder.getLang()));
            }
            //加班最小单位校验
            Float minUnit = (Float) otDateTypeList.get(0).get("min_unit");
            if (minUnit != null && inv % (minUnit * 60) != 0) {
                //result = "加班最小单位为" + min_unit + "小时！";
                return messageResource.getMessage("L005719", new Object[]{minUnit}, new Locale(SessionHolder.getLang()));
            }
        }
        return result;
    }

    /**
     * 根据考勤分组校验最小申请时长&最小单位
     *
     * @param overTimeDto
     * @param empOvertime
     * @param startDate
     * @return
     */
    public String checkOtMinApplyNum(OverTimeDto overTimeDto, WaEmpOvertime empOvertime, Long startDate) {
        String result = "";
        // 查询同加班类型同时间日期的加班单
        List<WaEmpOvertimeDo> overtimeList = waEmpOvertimeDo.getEmpOvertimes(overTimeDto.getBelongOrgid(),
                empOvertime.getEmpid(), startDate, startDate + 86399, overTimeDto.getOvertimeTypeId());
        boolean checkOvertimeDurationCondition = CollectionUtils.isNotEmpty(overtimeList)
                && (overtimeList.stream().anyMatch(overtime -> overtime.getEndTime().equals(empOvertime.getStartTime())
                || overtime.getStartTime().equals(empOvertime.getEndTime())));
        // 最小加班下限校验
        Integer inv = empOvertime.getOtDuration();
        Float minApplyNum = overTimeDto.getMinApplyNum();
        if (minApplyNum != null && inv < minApplyNum * 60 && !checkOvertimeDurationCondition) {
            result = String.format(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_LESS_THAN, Boolean.FALSE).getMsg(), minApplyNum);
        }
        return result;
    }

    /**
     * 仅可申请一种补偿类型/当天
     *
     * @param empid
     * @param startTime
     * @param endTime
     * @param compensateType
     * @param groupMap
     * @return
     */
    public String checkCompensateType(Long empid, Long startTime, Long endTime, Integer compensateType, Map groupMap) {
        if (MapUtils.isEmpty(groupMap)) {
            return "";
        }
        Boolean singleOvertimeType = (Boolean) groupMap.get("single_overtime_type");
        if (BooleanUtils.isTrue(singleOvertimeType)) {
            Long startDate = DateUtil.getTimesampByDateStr2(DateUtil.getDateStrByTimesamp(startTime));
            Long endDate = DateUtil.getTimesampByDateStr2(DateUtil.getDateStrByTimesamp(endTime));
            Map<String, Object> checkParams = new HashMap<>();
            checkParams.put("empid", empid);
            checkParams.put("startTime", startDate);
            checkParams.put("endTime", endDate + 86399);
            checkParams.put("compensateTypeIsNotEqualTo", compensateType);
            List<Map> compensateTypeMaplist = checkMapper.listEmpOvertimeCompensateTypes(checkParams);
            if (CollectionUtils.isNotEmpty(compensateTypeMaplist)) {
                String startDateYmStr = DateUtil.getDateStrByTimesamp(startTime);
                String endDateYmStr = DateUtil.getDateStrByTimesamp(endTime);
                for (Map map : compensateTypeMaplist) {
                    String date = (String) map.get("date");
                    if (startDateYmStr.equals(date) || endDateYmStr.equals(date)) {
                        // 同一天不可申请多种补偿类型的加班
                        return messageResource.getMessage("L006826", new Object[]{}, new Locale(SessionHolder.getLang()));
                    }
                }
            }
        }
        return "";
    }

    public void updatePreOtStatus(Integer preOtId, Integer status) {
        if (null == preOtId) {
            return;
        }
        PreEmpOvertime overtime = new PreEmpOvertime();
        overtime.setPreOtId(preOtId);
        overtime.setUpdtime(DateUtil.getCurrentTime(true));
        overtime.setUseStatus(status);
        preEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
    }

    /**
     * 加班申请时：校验考勤方案规则
     *
     * @param overtimeSaveDto
     * @return
     */
    private Result<GroupDetailDto> checkWaGroup(OverApplySaveDto overtimeSaveDto) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        Long empId = overtimeSaveDto.getEmpId();
        long startTime = overtimeSaveDto.doGetOtStartTime();

        // 查询员工当前时间所在的考勤方案
        GroupDetailDto groupDetail = groupService.getEmpGroup(userInfo.getTenantId(), empId, DateUtil.getCurrentTime(true));

        // 同一天是否允许申请多条加班单校验
        Boolean overtimeControl = Optional.ofNullable(groupDetail.getOvertimeControl()).orElse(Boolean.TRUE);
        if (!overtimeControl) {
            Long otStartTime = DateUtil.getOnlyDate(new Date(startTime * 1000));
            Long otEndTime = otStartTime + 86399;
            if (CollectionUtils.isNotEmpty(waEmpOvertimeDo.getEmpOvertimeListByBelongDate(empId, otStartTime, otEndTime))) {
                return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.ONE_OVERTIME_APPLY_CONTROL, Boolean.FALSE).getMsg());
            }
        } else {
            // 同一天是否可申请多种加班类型校验
            Boolean overtimeTypeControl = Optional.ofNullable(groupDetail.getOvertimeTypeControl()).orElse(Boolean.TRUE);
            if (!overtimeTypeControl) {
                Long otStartTime = DateUtil.getOnlyDate(new Date(startTime * 1000));
                Long otEndTime = otStartTime + 86399;
                List<WaEmpOvertimeDo> empOvertimeList = waEmpOvertimeDo.getEmpOvertimeListByBelongDate(empId, otStartTime, otEndTime);
                if (empOvertimeList.stream().anyMatch(over -> !overtimeSaveDto.getOvertimeTypeId().equals(over.getOvertimeTypeId()))) {
                    return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.ONE_OVERTIME_APPLY_CONTROL, Boolean.FALSE).getMsg());
                }
            }
        }
        return Result.ok(groupDetail);
    }

    /**
     * 加班申请时：校验员工信息
     *
     * @param empId
     * @return
     */
    private Result<SysEmpInfo> checkAndGetEmp(Long empId) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
        if (tmType == 0) {
            tmType = 1;
        } else if (tmType == 2) {//门店考勤人员
            Jedis jedis = RedisService.getResource();
            String appStoreEnable = jedis.get(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + userInfo.getTenantId() + "_APP_STORE_Enable");
            try {
                if (appStoreEnable == null || (!appStoreEnable.equals("1"))) {
                    //不支持门店考勤人员加班！
                    return Result.fail(messageResource.getMessage("L005712", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
                }
            } catch (RuntimeException e) {
                jedis.close();
                log.info(e.getMessage(), e);
            } finally {
                jedis.close();
            }
        }
        empInfo.setTmType(tmType);
        return Result.ok(empInfo);
    }

    /**
     * 加班申请时：校验加班类型
     *
     * @param overtimeTypeId
     * @return
     */
    private Result<OverTimeDto> checkAndGetOtType(Integer overtimeTypeId) {
        if (overtimeTypeId == null) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE_EMPTY, Boolean.FALSE).getMsg());
        }
        Optional<OverTimeDto> overtimeTypeOpt = Optional.ofNullable(overTimeService.getOtType(overtimeTypeId));
        if (!overtimeTypeOpt.isPresent()) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE_NOT_EXIST, Boolean.FALSE).getMsg());
        }
        OverTimeDto overTimeDto = overtimeTypeOpt.get();
        return Result.ok(overTimeDto);
    }

    private List<Map> getNowEmpBelongGroup(Long empId) {
        Map<String, Object> groupParams = new HashMap<>();
        groupParams.put("empid", empId);
        return waMapper.listEmpWaGroup(groupParams);
    }

    /**
     * 加班申请时：考勤方案规则校验
     *
     * @param empId
     * @param worktimeDetail
     * @return
     */
    private Result<Integer> checkNowGroupRule(Long empId, WaWorktimeDetail worktimeDetail) {
        // 查询员工当前时间所在的考勤方案
        List<Map> nowGroupRuleList = getNowEmpBelongGroup(empId);
        if (CollectionUtils.isEmpty(nowGroupRuleList)) {
            return Result.ok(null);
        }

        // 查询此考勤方案的加班规则
        Integer waGroupId = (Integer) nowGroupRuleList.get(0).get("wa_group_id");
        Integer[] dateTypes = waEmpGroupMapper.getDateTypeByGroupId(waGroupId);

        // 检查加班规则
        if (null == dateTypes || dateTypes.length == 0
                || !Arrays.asList(dateTypes).contains(worktimeDetail.getDateType())) { // 不允许加班
            return Result.fail(DateUtil.parseDateToPattern(new Date(worktimeDetail.getWorkDate() * 1000), "yyyy-MM-dd") + messageResource.getMessage("L005714", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
        }
        return Result.ok(waGroupId);
    }

    @Override
    public Long getShiftOffDutyEndTime(Map<Integer, WaShiftDef> shiftDefMap, WaWorktimeDetail worktimeDetail) {
        if (null == worktimeDetail) {
            return null;
        }
        Optional<WaShiftDef> shiftDefOpt = worktimeDetail.doGetShiftDefIdList().stream()
                .map(shiftDefMap::get)
                .filter(it -> !Objects.isNull(it))
                .max(Comparator.comparing(WaShiftDef::doGetRealStartTime));
        if (!shiftDefOpt.isPresent()) {
            return null;
        }
        WaShiftDef shiftDef = shiftDefOpt.get();
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        boolean crossNight = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType());
        long offEndTime = worktimeDetail.getWorkDate() + shiftWorkTime.getOffDutyEndTime() * 60;
        if (crossNight) {
            offEndTime += 86400L;
        }
        return offEndTime;
    }

    /**
     * 加班保存时：判断加班是否归属于前一天
     *
     * @param shiftDefMap
     * @param preWorkTimeDetail
     * @param endTime
     * @return
     */
    @Override
    public boolean checkIfBelongPreDayOnSave(Map<Integer, WaShiftDef> shiftDefMap, WaWorktimeDetail preWorkTimeDetail, long endTime) {
        Long preDayOffDutyEndTime = getShiftOffDutyEndTime(shiftDefMap, preWorkTimeDetail);
        if (null == preDayOffDutyEndTime) {
            return false;
        }
        return endTime <= preDayOffDutyEndTime;
    }

    /**
     * 加班申请时：判断加班是否归属于前一天
     *
     * @param shiftDefMap
     * @param preWorkTimeDetail
     * @param startTime
     * @return
     */
    public boolean checkIfBelongPreDay(Map<Integer, WaShiftDef> shiftDefMap,
                                       WaWorktimeDetail preWorkTimeDetail,
                                       long startTime) {
        if (null == preWorkTimeDetail) {
            return Boolean.FALSE;
        }

        // 获取加班开始日期前一天的班次，如有多个班则取最后一个班
        Optional<WaShiftDef> preShiftDefOpt = preWorkTimeDetail.doGetShiftDefIdList().stream()
                .map(shiftDefMap::get)
                .filter(it -> !Objects.isNull(it))
                .max(Comparator.comparing(WaShiftDef::doGetRealStartTime));
        if (!preShiftDefOpt.isPresent()) {
            return Boolean.FALSE;
        }
        WaShiftDef preShiftDef = preShiftDefOpt.get();

        // 加班时间校验
        List<MultiOvertimeDto> multiOvertime = StringUtils.isNotBlank(preShiftDef.getMultiOvertime())
                ? FastjsonUtil.toArrayList(preShiftDef.getMultiOvertime(), MultiOvertimeDto.class)
                : Lists.newArrayList();
        if (CollectionUtils.isEmpty(multiOvertime)) {
            return Boolean.FALSE;
        }

        // 打卡时间校验
        WaShiftDef preShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(preShiftDef);
        boolean crossNight = CdWaShiftUtil.checkCrossNightForSignOffEndTime(preShiftWorkTime, preShiftWorkTime.getDateType());
        long preOffDutyEndTime = preWorkTimeDetail.getWorkDate() + preShiftWorkTime.getOffDutyEndTime() * 60;
        if (crossNight) {
            preOffDutyEndTime += 86400L;
        }
        return startTime < preOffDutyEndTime;
    }

    private WaShiftDef getLastShiftDef(Map<Integer, WaShiftDef> shiftDefMap, WaWorktimeDetail worktimeDetail) {
        Optional<WaShiftDef> shiftDefOpt = worktimeDetail.doGetShiftDefIdList().stream()
                .map(shiftDefMap::get)
                .filter(it -> !Objects.isNull(it))
                .max(Comparator.comparing(WaShiftDef::doGetRealStartTime));
        return shiftDefOpt.orElse(null);
    }

    /**
     * 加班申请时：判断加班时间是否在加班允许的时间范围内
     *
     * @param shiftDefMap
     * @param worktimeDetail
     * @param startTime
     * @param endTime
     * @return
     */
    private Result<Boolean> checkOverTimeRange(Map<Integer, WaShiftDef> shiftDefMap,
                                               WaWorktimeDetail worktimeDetail,
                                               long startTime, long endTime) {
        List<MultiOvertimeDto> allShiftOvertimePeridList = Lists.newArrayList();
        for (Integer shiftDefId : worktimeDetail.doGetShiftDefIdList()) {
            WaShiftDef shiftDef = shiftDefMap.get(shiftDefId);
            if (null == shiftDef) {
                continue;
            }
            // 获取允许加班的时间范围
            List<MultiOvertimeDto> multiOvertime = StringUtils.isNotBlank(shiftDef.getMultiOvertime())
                    ? FastjsonUtil.toArrayList(shiftDef.getMultiOvertime(), MultiOvertimeDto.class)
                    : Lists.newArrayList();
            if (CollectionUtils.isEmpty(multiOvertime)) {
                continue;
            }
            multiOvertime.sort(Comparator.comparing(MultiOvertimeDto::doGetRealOvertimeStartTime));
            allShiftOvertimePeridList.addAll(multiOvertime);
            for (MultiOvertimeDto overtimeDto : multiOvertime) {
                long overtimeStartTime = worktimeDetail.getWorkDate() + overtimeDto.doGetRealOvertimeStartTime() * 60L;
                long overtimeEndTime = worktimeDetail.getWorkDate() + overtimeDto.doGetRealOvertimeEndTime() * 60L;
                if (startTime >= overtimeStartTime && endTime <= overtimeEndTime) {
                    return Result.ok(Boolean.TRUE);
                }
            }
        }
        if (CollectionUtils.isEmpty(allShiftOvertimePeridList)) {
            return Result.ok(Boolean.TRUE);
        }
        return Result.fail(MessageHandler.getMessage("caidao.exception.error_202830", WebUtil.getRequest()));
    }

    public Result<Boolean> checkByPreDayShift(Map<Integer, WaShiftDef> shiftDefMap,
                                              WaWorktimeDetail preWorkTimeDetail,
                                              long startTime,
                                              long endTime) {
        // 检查加班时间是否在加班允许的时间范围内
        Result<?> checkOverTimeRange = checkOverTimeRange(shiftDefMap, preWorkTimeDetail, startTime, endTime);
        if (!checkOverTimeRange.isSuccess()) {
            return Result.fail(checkOverTimeRange.getMsg());
        }
        // 检查加班时间是否包含上班时间
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(preWorkTimeDetail.getDateType()) && CdWaShiftUtil.checkCrossNightV3(preWorkTimeDetail, shiftDefMap)) {
            boolean notInWorkTime = checkIfNotInWorkTime(shiftDefMap, preWorkTimeDetail, startTime, endTime);
            if (!notInWorkTime) {
                return Result.fail(messageResource.getMessage("L005843", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
            }
        }
        // 检查加班结束时间是否超过前一天的最晚下班打卡时间
        WaShiftDef preLastShiftDef = getLastShiftDef(shiftDefMap, preWorkTimeDetail);
        WaShiftDef preLastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(preLastShiftDef);
        if (null != preLastShiftWorkTime) {
            boolean crossNight = CdWaShiftUtil.checkCrossNightForSignOffEndTime(preLastShiftWorkTime, preLastShiftWorkTime.getDateType());
            long preLastOffDutyEndTime = preWorkTimeDetail.getWorkDate() + preLastShiftWorkTime.getOffDutyEndTime() * 60;
            if (crossNight) {
                preLastOffDutyEndTime += 86400L;
            }
            if (endTime > preLastOffDutyEndTime) {
                return Result.fail(MessageHandler.getMessage("caidao.exception.error_202827", WebUtil.getRequest()));
            }
        }
        return Result.ok(true);
    }

    public Result<Boolean> checkByShift(Map<Integer, WaShiftDef> shiftDefMap,
                                        WaWorktimeDetail worktimeDetail,
                                        long startTime,
                                        long endTime) {
        // 检查加班时间是否在加班允许的时间范围内
        Result<?> checkOverTimeRange = checkOverTimeRange(shiftDefMap, worktimeDetail, startTime, endTime);
        if (!checkOverTimeRange.isSuccess()) {
            return Result.fail(checkOverTimeRange.getMsg());
        }
        // 检查加班时间是否包含上班时间
        boolean notInWorkTime = checkIfNotInWorkTime(shiftDefMap, worktimeDetail, startTime, endTime);
        if (!notInWorkTime) {
            return Result.fail(messageResource.getMessage("L005843", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
        }
        return Result.ok(true);
    }

    /**
     * 根据班次校验加班时间并计算加班时长（支持跨班次申请）
     *
     * @param tenantId
     * @param pbMap
     * @param shiftDefMap
     * @param startTime
     * @param endTime
     * @param overtimeDate
     * @return
     */
    public Result<Long> checkAndCalOtTimeV2(String tenantId, Map<Long, WaWorktimeDetail> pbMap,
                                            Map<Integer, WaShiftDef> shiftDefMap, long startTime, long endTime,
                                            Long overtimeDate) {
        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(endTime * 1000));
        long preDate = startDate - 86400L;

        WaWorktimeDetail startWorkTimeDetail = pbMap.get(startDate);
        WaWorktimeDetail endWorkTimeDetail = pbMap.get(endDate);
        WaWorktimeDetail selectOtDateWorktimeDetail = null != overtimeDate ? pbMap.get(overtimeDate) : null;

        // 计算加班时长
        long jg = endTime - startTime;

        // 指定加班归属日期
        if (null != selectOtDateWorktimeDetail) {
            Result<Boolean> checkResult = checkByShift(shiftDefMap, selectOtDateWorktimeDetail, startTime, endTime);
            if (!checkResult.isSuccess()) {
                return Result.fail(checkResult.getMsg());
            }
            jg -= calRestTotalTime(tenantId, startTime, endTime, selectOtDateWorktimeDetail, shiftDefMap);
            return Result.ok(jg);
        }

        // 默认逻辑
        if (startDate == endDate) {
            // 前一天班次判断
            WaWorktimeDetail preWorkTimeDetail = pbMap.get(preDate);
            if (null == preWorkTimeDetail) {
                // TODO  checkByShift
                jg -= calRestTotalTime(tenantId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
                return Result.ok(jg);
            }
            Optional<WaShiftDef> preShiftDefOpt = preWorkTimeDetail.doGetShiftDefIdList().stream()
                    .map(shiftDefMap::get)
                    .filter(it -> !Objects.isNull(it))
                    .max(Comparator.comparing(WaShiftDef::doGetRealStartTime));
            if (!preShiftDefOpt.isPresent()) {
                jg -= calRestTotalTime(tenantId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
                return Result.ok(jg);
            }
            WaShiftDef preShiftDef = preShiftDefOpt.get();
            WaShiftDef preShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(preShiftDef);
            boolean crossNight = CdWaShiftUtil.checkCrossNightForSignOffEndTime(preShiftWorkTime, preShiftWorkTime.getDateType());
            long preOffDutyEndTime = preWorkTimeDetail.getWorkDate() + preShiftWorkTime.getOffDutyEndTime() * 60;
            if (crossNight) {
                preOffDutyEndTime += 86400L;
            }
            if (endTime <= preOffDutyEndTime) {
                Result<?> checkResult = checkByPreDayShift(shiftDefMap, preWorkTimeDetail, startTime, endTime);
                if (!checkResult.isSuccess()) {
                    return Result.fail(checkResult.getMsg());
                }
                jg -= calRestTotalTime(tenantId, startTime, endTime, preWorkTimeDetail, shiftDefMap);
                jg -= calRestTotalTime(tenantId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
                return Result.ok(jg);
            } else if (startTime >= preOffDutyEndTime) {
                Result<?> checkResult = checkByShift(shiftDefMap, startWorkTimeDetail, startTime, endTime);
                if (!checkResult.isSuccess()) {
                    return Result.fail(checkResult.getMsg());
                }
                jg -= calRestTotalTime(tenantId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
                return Result.ok(jg);
            } else {
                Result<Boolean> checkResult = checkByPreDayShift(shiftDefMap, preWorkTimeDetail, startTime, preOffDutyEndTime);
                if (!checkResult.isSuccess()) {
                    return Result.fail(checkResult.getMsg());
                }
                checkResult = checkByShift(shiftDefMap, startWorkTimeDetail, preOffDutyEndTime, endTime);
                if (!checkResult.isSuccess()) {
                    return Result.fail(checkResult.getMsg());
                }
                jg -= calRestTotalTime(tenantId, startTime, preOffDutyEndTime, preWorkTimeDetail, shiftDefMap);
                jg -= calRestTotalTime(tenantId, startTime, preOffDutyEndTime, startWorkTimeDetail, shiftDefMap);
                jg -= calRestTotalTime(tenantId, preOffDutyEndTime, endTime, startWorkTimeDetail, shiftDefMap);
                return Result.ok(jg);
            }
        } else {// 加班跨夜
            // 加班开始日期班次判断
            Optional<WaShiftDef> startShiftDefOpt = startWorkTimeDetail.doGetShiftDefIdList().stream()
                    .map(shiftDefMap::get)
                    .filter(it -> !Objects.isNull(it))
                    .max(Comparator.comparing(WaShiftDef::doGetRealStartTime));
            WaShiftDef startShiftDef = startShiftDefOpt.get();
            WaShiftDef startShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(startShiftDef);
            boolean crossNight = CdWaShiftUtil.checkCrossNightForSignOffEndTime(startShiftWorkTime, startShiftWorkTime.getDateType());
            long startOffDutyEndTime = startWorkTimeDetail.getWorkDate() + startShiftWorkTime.getOffDutyEndTime() * 60;
            if (crossNight) {
                startOffDutyEndTime += 86400L;
            }
            if (endTime <= startOffDutyEndTime) {
                Result<Boolean> checkResult = checkByShift(shiftDefMap, startWorkTimeDetail, startTime, endTime);
                if (!checkResult.isSuccess()) {
                    return Result.fail(checkResult.getMsg());
                }
                jg -= calRestTotalTime(tenantId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
                jg -= calRestTotalTime(tenantId, startTime, endTime, endWorkTimeDetail, shiftDefMap);
                return Result.ok(jg);
            } else {
                Result<Boolean> checkResult = checkByShift(shiftDefMap, startWorkTimeDetail, startTime, startOffDutyEndTime);
                if (!checkResult.isSuccess()) {
                    return Result.fail(checkResult.getMsg());
                }
                checkResult = checkByShift(shiftDefMap, endWorkTimeDetail, startOffDutyEndTime, endTime);
                if (!checkResult.isSuccess()) {
                    return Result.fail(checkResult.getMsg());
                }
                jg -= calRestTotalTime(tenantId, startTime, startOffDutyEndTime, startWorkTimeDetail, shiftDefMap);
                jg -= calRestTotalTime(tenantId, startTime, startOffDutyEndTime, endWorkTimeDetail, shiftDefMap);

                jg -= calRestTotalTime(tenantId, startOffDutyEndTime, endTime, startWorkTimeDetail, shiftDefMap);
                jg -= calRestTotalTime(tenantId, startOffDutyEndTime, endTime, endWorkTimeDetail, shiftDefMap);
                return Result.ok(jg);
            }
        }
    }

    /**
     * 根据班次校验加班时间并计算加班时长（不支持跨班次申请）-- 废弃
     *
     * @param tenantId
     * @param pbMap
     * @param shiftDefMap
     * @param startTime
     * @param endTime
     * @return
     */
    @Deprecated
    public Result<Long> checkAndCalOtTime(String tenantId,
                                          Map<Long, WaWorktimeDetail> pbMap,
                                          Map<Integer, WaShiftDef> shiftDefMap,
                                          long startTime,
                                          long endTime) {
        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(endTime * 1000));
        long preDate = startDate - 86400L;
        boolean isSecondDay = endDate > startDate;

        WaWorktimeDetail startWorkTimeDetail = pbMap.get(startDate);
        WaWorktimeDetail secondDayWorkTimeDetail = pbMap.get(endDate);

        // 检查加班时间是否归属于前一天（在前一天班次的班次范围内）
        WaWorktimeDetail preWorkTimeDetail = pbMap.get(preDate);
        boolean belongPreDay = isSecondDay ? Boolean.FALSE : checkIfBelongPreDay(shiftDefMap, preWorkTimeDetail, startTime);
        if (belongPreDay) {
            Result<?> checkResult = checkByPreDayShift(shiftDefMap, preWorkTimeDetail, startTime, endTime);
            if (!checkResult.isSuccess()) {
                return Result.fail(checkResult.getMsg());
            }
        } else {
            Result<?> checkResult = checkByShift(shiftDefMap, startWorkTimeDetail, startTime, endTime);
            if (!checkResult.isSuccess()) {
                return Result.fail(checkResult.getMsg());
            }
            if (isSecondDay) {
                boolean notInWorkTime = checkIfNotInWorkTime(shiftDefMap, secondDayWorkTimeDetail, startTime, endTime);
                if (!notInWorkTime) {
                    return Result.fail(messageResource.getMessage("L005843", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
                }
            }
        }

        // 计算加班时长
        long jg = endTime - startTime;
        if (isSecondDay) {
            jg -= calRestTotalTime(tenantId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
            jg -= calRestTotalTime(tenantId, startTime, endTime, secondDayWorkTimeDetail, shiftDefMap);
        } else {
            if (belongPreDay) {
                jg -= calRestTotalTime(tenantId, startTime, endTime, preWorkTimeDetail, shiftDefMap);
            } else {
                jg -= calRestTotalTime(tenantId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
            }
        }
        return Result.ok(jg);
    }

    @Override
    public List<KeyValue> getOvertimeDateList(OvertimeDateQueryDto queryDto) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        Long empId = queryDto.getEmpId();

        // 查询员工当前时间所在的考勤方案
        GroupDetailDto groupDetail = groupService.getEmpGroup(userInfo.getTenantId(), empId, DateUtil.getCurrentTime(true));
        if (null == groupDetail || null == groupDetail.getSelectOvertimeDate() || !groupDetail.getSelectOvertimeDate()) {
            return Lists.newArrayList();
        }

        // 加班时间
        long startTime = queryDto.doGetOtStartTime();
        long endTime = queryDto.doGetOtEndTime();
        if (endTime < startTime) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.STARTTIME_CANNOTBE_GE_ENDTIME, Boolean.FALSE).getMsg());
        }
        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(endTime * 1000));
        boolean isSecondDay = endDate > startDate;
        if (isSecondDay && endTime > startTime + 86400L) {
            //申请加班不能超过24小时
            throw new ServerException(messageResource.getMessage("L005692", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
        }

        Long preDate = DateUtil.addDate(startDate * 1000, -1);
        Long nextDate = DateUtil.addDate(startDate * 1000, 1);

        return Lists.newArrayList(new KeyValue(DateUtil.getDateStrByTimesamp(preDate), preDate),
                new KeyValue(DateUtil.getDateStrByTimesamp(startDate), startDate),
                new KeyValue(DateUtil.getDateStrByTimesamp(nextDate), nextDate));
    }

    /**
     * 校验加班时间，获取加班总时长
     *
     * @param overtimeSaveDto
     * @return
     */
    @Override
    public Result<GetOtTimeResultDto> getOtTotalTime(OverApplySaveDto overtimeSaveDto) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        // 加班类型校验
        Result<OverTimeDto> checkOtType = checkAndGetOtType(overtimeSaveDto.getOvertimeTypeId());
        if (!checkOtType.isSuccess()) {
            return Result.fail(checkOtType.getMsg());
        }
        OverTimeDto overTimeDto = checkOtType.getData();

        // 考勤方案规则校验
        Result<GroupDetailDto> checkWaGroup = checkWaGroup(overtimeSaveDto);
        if (!checkWaGroup.isSuccess()) {
            return Result.fail(checkWaGroup.getMsg());
        }

        // 员工信息校验
        Long empId = overtimeSaveDto.getEmpId();
        Result<SysEmpInfo> checkEmp = checkAndGetEmp(empId);
        if (!checkEmp.isSuccess()) {
            return Result.fail(checkEmp.getMsg());
        }
        SysEmpInfo empInfo = checkEmp.getData();

        // 加班时间校验
        long startTime = overtimeSaveDto.doGetOtStartTime();
        long endTime = overtimeSaveDto.doGetOtEndTime();
        if (endTime < startTime) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.STARTTIME_CANNOTBE_GE_ENDTIME, Boolean.FALSE).getMsg());
        }
        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(endTime * 1000));
        if (endDate > startDate && endTime > startTime + 86400L) {
            //申请加班不能超过24小时
            return Result.fail(messageResource.getMessage("L005692", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
        }

        // 出差规则校验
        List<WaEmpTravelDaytimeDo> travelList = waEmpTravelDaytimeDo.getEmpTravelDaytimeList(userInfo.getTenantId(),
                Collections.singletonList(empId), startDate, endDate + 86399, Arrays.asList(1, 2));
        if (CollectionUtils.isNotEmpty(travelList)
                && travelList.stream().anyMatch(travel -> travel.getOvertimeRule() != null && travel.getOvertimeRule() != 2)) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.DURING_OUT_OVERTIME_APPLY_NOT_ALLOW, Boolean.FALSE).getMsg());
        }

        // 加班时间重叠校验
        Map<String, Object> params = new HashMap<>();
        params.put("empid", empId);
        params.put("start", startTime);
        params.put("end", endTime);
        if (checkMapper.checkOtRepeat(params) > 0) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.TIME_OVERAPPLY, Boolean.FALSE).getMsg());
        }

        // 查询员工排班
        Long preDate = DateUtil.addDate(startDate * 1000, -1);
        Long nextDate = DateUtil.addDate(endDate * 1000, 1);
        Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(userInfo.getTenantId(),
                empId, empInfo.getTmType(), preDate, nextDate);
        if (MapUtils.isEmpty(pbMap)) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, null).getMsg());
        }
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(userInfo.getTenantId());
        if (MapUtils.isEmpty(shiftDefMap)) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, null).getMsg());
        }
        long tmpDate = startDate;
        while (tmpDate <= endDate) {
            if (null == pbMap.get(tmpDate)) {
                // 没有排班记录，不能加班
                return Result.fail(DateUtil.getDateStrByTimesamp(tmpDate) + messageResource.getMessage("L005713", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
            }
            tmpDate = tmpDate + 24 * 60 * 60;
        }

        WaWorktimeDetail startWorkTimeDetail = pbMap.get(startDate);
        Integer waGroupId = null;

        // 指定加班归属日期
        WaWorktimeDetail selectOtDateWorktimeDetail = null;
        Long selectOvertimeDate = null;
        if (null != (selectOvertimeDate = overtimeSaveDto.getOvertimeDate())) {
            selectOtDateWorktimeDetail = pbMap.get(overtimeSaveDto.getOvertimeDate());
            if (null == selectOtDateWorktimeDetail) {
                return Result.fail(DateUtil.getDateStrByTimesamp(selectOvertimeDate)
                        + messageResource.getMessage("L005713", new Object[]{},
                        new Locale(SessionBeanUtil.getLanguage())));
            }

            GroupDetailDto groupRule = groupService.getEmpGroup(userInfo.getTenantId(), empId, selectOvertimeDate);
            if (null != groupRule) {
                waGroupId = groupRule.getWaGroupId();
            }
        } else {
            // 员工当前所属考勤方案规则校验
            Result<Integer> checkNowGroupRule = checkNowGroupRule(empId, startWorkTimeDetail);
            if (!checkNowGroupRule.isSuccess()) {
                return Result.fail(checkNowGroupRule.getMsg());
            }
            waGroupId = checkNowGroupRule.getData();

            // 加班跨夜时检查结束日期所在班次的考勤方案规则
            if (endDate > startDate && null != waGroupId) {
                GroupDetailDto endGroupRule = groupService.getEmpGroup(userInfo.getTenantId(), empId, endTime);
                if (null != endGroupRule
                        && !waGroupId.equals(endGroupRule.getWaGroupId())) {
                    waGroupId = endGroupRule.getWaGroupId();
                    Integer[] dateTypes = waEmpGroupMapper.getDateTypeByGroupId(waGroupId);
                    if (null == dateTypes
                            || dateTypes.length == 0
                            || !Arrays.asList(dateTypes).contains(startWorkTimeDetail.getDateType())) {// 不允许加班
                        return Result.fail(DateUtil.getDateStrByTimesamp(startTime)
                                + messageResource.getMessage("L005714", new Object[]{},
                                new Locale(SessionBeanUtil.getLanguage())));
                    }
                }
            }
        }
        WaGroup waGroup = null != waGroupId ? waGroupMapper.selectByPrimaryKey(waGroupId) : null;

        // 根据班次校验加班时间并计算加班时长（支持跨班次申请）
        Result<Long> checkAndCalOtTimeV2 = checkAndCalOtTimeV2(userInfo.getTenantId(), pbMap,
                shiftDefMap, startTime, endTime, selectOvertimeDate);
        if (!checkAndCalOtTimeV2.isSuccess()) {
            return Result.fail(checkAndCalOtTimeV2.getMsg());
        }
        long jg = checkAndCalOtTimeV2.getData();

        // 根据班次日期类型查询适用加班规则，最小加班单位校验
        Float minOvertimeUnit = overTimeDto.getMinOvertimeUnit();
        int minOvertimeUnitType = Optional.ofNullable(overTimeDto.getMinOvertimeUnitType()).orElse(2);
        if (null != minOvertimeUnit && minOvertimeUnit > 0
                && jg % (minOvertimeUnitType == 2 ? minOvertimeUnit * 60 * 60 : minOvertimeUnit * 60) != 0) {
            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_MUST_MULTIPLES, Boolean.FALSE).getMsg(), minOvertimeUnit, MinOvertimeUnitType.getName(minOvertimeUnitType), minOvertimeUnit));
        }
        if (waGroup != null && minOvertimeUnit != null) {
            BigDecimal workingTime = waGroup.getWorkingTime();
            jg = BigDecimal.valueOf(OvertimeUnitEnum.HOUR.getTime(workingTime, (float) jg, minOvertimeUnit, minOvertimeUnitType)).longValue();
        }

        // 查询同加班类型同时间日期的加班单
        List<WaEmpOvertimeDo> overtimeList = waEmpOvertimeDo.getEmpOvertimes(userInfo.getTenantId(), empId, startDate, startDate + 86399,
                overtimeSaveDto.getOvertimeTypeId());
        boolean checkOvertimeDurationCondition = CollectionUtils.isNotEmpty(overtimeList) && (overtimeList.stream().anyMatch(overtime -> overtime.getEndTime().equals(startTime) || overtime.getStartTime().equals(endTime)));

        // 最小加班下限校验
        Float minApplyNum = overTimeDto.getMinApplyNum();
        if (minApplyNum != null && jg < minApplyNum * 60 * 60 && !checkOvertimeDurationCondition) {
            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_LESS_THAN, Boolean.FALSE).getMsg(), minApplyNum));
        }

        // 最大加班上限校验
        Float maxValidTime = overTimeDto.getMaxValidTime();
        long st = startTime;
        long et = endTime;
        long totalDuration = 0;
        if (checkOvertimeDurationCondition) {
            List<WaEmpOvertimeDo> less = overtimeList.stream().filter(overtime -> overtime.getStartTime() <= startTime).sorted(Comparator.comparing(WaEmpOvertimeDo::getStartTime).reversed()).collect(Collectors.toList());
            for (WaEmpOvertimeDo empOvertime : less) {
                if (empOvertime.getEndTime().equals(st)) {
                    st = empOvertime.getStartTime();
                    totalDuration += empOvertime.getOtDuration() * 60;
                }
            }
            List<WaEmpOvertimeDo> greater = overtimeList.stream().filter(overtime -> overtime.getStartTime() >= endTime).sorted(Comparator.comparing(WaEmpOvertimeDo::getStartTime)).collect(Collectors.toList());
            for (WaEmpOvertimeDo empOvertime : greater) {
                if (empOvertime.getStartTime().equals(et)) {
                    et = empOvertime.getEndTime();
                    totalDuration += empOvertime.getOtDuration() * 60;
                }
            }
        }
        if (maxValidTime != null && jg + totalDuration > maxValidTime * 60 * 60) {
            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_GREATER_THAN, Boolean.FALSE).getMsg(), maxValidTime));
        }

        // 判断是否超过考勤截止日
        WaSob waSob = waSobService.getWaSob(empId, startDate);
        if (waSob != null) {
            Integer sysPeriodMonth = waSob.getSysPeriodMonth();
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            Long onlyDate = DateUtil.getOnlyDate();
            if (onlyDate > sobEndDate) {
                //return Result.fail("申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员。");
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
            }
        }

        // 校验加班上限&加班上限提示
        String warnMsg = "";
        try {
            Result<String> validateMap = this.validateOvertimeLimit(waGroup, empId, startTime, jg / 60);
            if (!validateMap.isSuccess()) {
                return Result.fail(validateMap.getMsg());
            } else if (StringUtils.isNotBlank(validateMap.getData())) {
                warnMsg = validateMap.getData();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.GET_OVERTIME_DURATION_FAILED, null).getMsg());
        }

        String otDurationDesc;
        if ((jg / 60) % 60 > 0) {
            if (jg / 3600 > 0) {
                otDurationDesc = messageResource.getMessage("L005572", new Object[]{(jg / 3600), ((jg / 60) % 60)}, new Locale(SessionBeanUtil.getLanguage()));
            } else {
                otDurationDesc = messageResource.getMessage("L006821", new Object[]{((jg / 60) % 60)}, new Locale(SessionBeanUtil.getLanguage()));
            }
        } else {
            otDurationDesc = messageResource.getMessage("L005573", new Object[]{(jg / 3600)}, new Locale(SessionBeanUtil.getLanguage()));
        }
        return Result.ok(new GetOtTimeResultDto(warnMsg, otDurationDesc, BigDecimal.valueOf(jg / 60).doubleValue()));
    }

    /**
     * 检查加班时间是否包含上班时间
     *
     * @param shiftDefMap
     * @param worktimeDetail
     * @param overtimeStartTime
     * @param overtimeEndTime
     * @return true 不包含 false 包含
     */
    public boolean checkIfNotInWorkTime(Map<Integer, WaShiftDef> shiftDefMap, WaWorktimeDetail worktimeDetail,
                                        long overtimeStartTime, long overtimeEndTime) {
        if (null == worktimeDetail || !DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType())) {
            return Boolean.TRUE;
        }
        boolean inWorkTime = checkIfInWorkTime(shiftDefMap, worktimeDetail, overtimeStartTime, overtimeEndTime);
        if (!inWorkTime) {
            return Boolean.TRUE;
        }
        // 如果在上班时间内，检查是否在上班时间内的休息时间段内
        return checkRestTime(shiftDefMap, worktimeDetail, overtimeStartTime, overtimeEndTime);
    }

    /**
     * 计算加班时间段内休息的时间
     *
     * @param tenantId
     * @param otStartTime
     * @param otEndTime
     * @param worktimeDetail
     * @param shiftDefMap
     * @return
     */
    @Override
    public long calRestTotalTime(String tenantId, Long otStartTime, Long otEndTime,
                                 WaWorktimeDetail worktimeDetail, Map<Integer, WaShiftDef> shiftDefMap) {
        long restTotalTime = 0L;
        if (null == worktimeDetail) {
            return restTotalTime;
        }
        // 检查法定假日加班是否要扣减休息时间
        boolean isHoliday = DateTypeEnum.DATE_TYP_3.getIndex().equals(worktimeDetail.getDateType())
                || DateTypeEnum.DATE_TYP_5.getIndex().equals(worktimeDetail.getDateType());
        if (isHoliday) {
            String ignoreHolidaykey = RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + tenantId + RedisKeyDefine.WA_HOLIDAY_IGNORE;
            String isIgnore = CDCacheUtil.getValue(ignoreHolidaykey);
            if (isIgnore != null && !"0".equals(isIgnore)) {
                return restTotalTime;
            }
        }
        for (Integer shiftDefId : worktimeDetail.doGetShiftDefIdList()) {
            WaShiftDef shiftDef = shiftDefMap.get(shiftDefId);
            if (null == shiftDef) {
                continue;
            }
            List<OvertimeRestPeriodsDto> overtimeRestPeriods = WaShiftDo.getOvertimeRestPeriodList(shiftDef);
            if (CollectionUtils.isEmpty(overtimeRestPeriods)) {
                continue;
            }
            WaShiftDo shiftDo = ObjectConverter.convert(shiftDef, WaShiftDo.class);
            List<MultiOvertimeDto> multiOvertimeList = shiftDo.getMultiOvertimeList();
            for (OvertimeRestPeriodsDto periodsDto : overtimeRestPeriods) {
                long restStartTime = worktimeDetail.getWorkDate() + periodsDto.doGetRealOvertimeRestStartTime(multiOvertimeList) * 60L;
                long restEndTime = worktimeDetail.getWorkDate() + periodsDto.doGetRealOvertimeRestEndTime(multiOvertimeList) * 60L;
                if (restStartTime >= otEndTime || restEndTime <= otStartTime) {
                    continue;
                }
                restTotalTime += Math.min(restEndTime, otEndTime) - Math.max(restStartTime, otStartTime);
            }
        }
        return restTotalTime;
    }

    private Map getOtCycleDate(Integer cycleStartDate, Long startTime, Long endTime) throws Exception {
        Map startCycle = this.calculateCycleTime(startTime, cycleStartDate);
        Map endCycle = this.calculateCycleTime(endTime, cycleStartDate);

        if (startCycle != null && endCycle != null) {
            Long cycleBegin = (Long) startCycle.get("cycleBegin");//考勤周期开始时间
            Long cycleEnd = (Long) startCycle.get("cycleEnd");//考勤周期结束时间
            Map result = new HashMap();
            result.put("cycleBegin", cycleBegin);
            result.put("cycleEnd", cycleEnd);
            return result;
        }
        return null;
    }

    /**
     * 根据时间计算当前时间所在的考勤周期
     *
     * @param dateTime
     * @param cycleStartDate
     * @return
     * @throws ParseException
     */
    private Map calculateCycleTime(Long dateTime, Integer cycleStartDate) throws ParseException {
        Long preMonthBegin = DateUtilExt.getMonth(dateTime, -1, 1);//上个月开始时间
        Long preMonthEnd = DateUtilExt.getMonthEnd(preMonthBegin);//上个月的结束时间
        Long curMonthBegin = DateUtilExt.getMonth(dateTime, 0, 1);//本月开始时间
        Long curMonthEnd = DateUtilExt.getMonthEnd(curMonthBegin);//本月结束时间
        Long curCycleBegin = null;//当前时间所在的考勤周期开始时间
        Long curCycleEnd = null;//当前时间所在的考勤周期结束时间
        if (cycleStartDate > 1) {
            Long addDayMin = (cycleStartDate - 1) * 86400L;
            preMonthBegin += addDayMin;
            preMonthEnd += addDayMin;
            curMonthBegin += addDayMin;
            curMonthEnd += addDayMin;
        }
        //加班的时间不允许跨考勤周期，校验加班时间是否跨考勤周期
        //当前时间的考勤周期
        if (dateTime >= preMonthBegin && dateTime <= preMonthEnd) {
            curCycleBegin = preMonthBegin;
            curCycleEnd = preMonthEnd;
        } else if (dateTime >= curMonthBegin && dateTime <= curMonthEnd) {
            curCycleBegin = curMonthBegin;
            curCycleEnd = curMonthEnd;
        } else {
            return null;
        }
        Map resultMap = new HashMap();
        resultMap.put("cycleBegin", curCycleBegin);
        resultMap.put("cycleEnd", curCycleEnd);
        return resultMap;
    }

    /**
     * 计算自然月的加班时间上限
     *
     * @param waGroup
     * @param empId
     * @param startTime
     * @param totalOtTime
     * @return
     * @throws Exception
     */
    private Result<String> validateOvertimeLimit(WaGroup waGroup, Long empId, Long startTime, Long totalOtTime) throws Exception {
        if (null == waGroup) {
            return Result.ok("");
        }

        //加班上限小时数，禁止提交
        Integer overtimeLimitH = waGroup.getOvertimeLimit();
        Boolean isSetOvertimeLimit = waGroup.getIsSetOvertimeLimit();
        Boolean isOpenOtLimitWarn = waGroup.getIsOpenOtlimitWarn();
        Integer otTopLimitH = waGroup.getOtToplimit(); //加班上限小时数，仅提示

        if ((isSetOvertimeLimit != null && isSetOvertimeLimit && overtimeLimitH != null) || (isOpenOtLimitWarn != null && isOpenOtLimitWarn && otTopLimitH != null)) {
            Long monthBegin = DateUtilExt.getMonthBegin(startTime);
            Long monthEnd = DateUtilExt.getMonthEnd(monthBegin);
            Map<String, Object> params = new HashMap<>();
            params.put("starttime", monthBegin);
            params.put("endtime", monthEnd);
            params.put("empid", empId);
            Long totalDuration = Optional.ofNullable(waEmpOvertimeMapper.getEmpOverTimeTotalDuration(params)).orElse(0L);
            if (isSetOvertimeLimit != null && isSetOvertimeLimit && overtimeLimitH != null) {
                //禁止提交并提示
                //剩余可用的加班时长（判断是否已经超过考勤周期的加班上限，如果超出了就不允许申请加班，反之可以）
                Long overtimeLimitMin = overtimeLimitH * 60L;//加班上限分钟
                Long surplusDuration = overtimeLimitMin - totalDuration;
                if (surplusDuration <= 0) {
                    //本月加班上限" + overtimeLimitH + "小时，已申请" + totalDuration.intValue() / 60 + "小时" + totalDuration % 60 + "分钟" + "，剩余0小时0分钟！
                    return Result.fail(messageResource.getMessage("L006223", new Object[]{overtimeLimitH, (totalDuration.intValue() / 60), (totalDuration % 60)}, new Locale(SessionHolder.getLang())));
                }
                if (totalOtTime > surplusDuration) {
                    //剩余可用加班时间为" + surplusDuration / 60 + "小时" + surplusDuration % 60 + "分钟，本次申请加班的时间超出了可用额度！
                    return Result.fail(messageResource.getMessage("L006224", new Object[]{(surplusDuration / 60), (surplusDuration % 60)}, new Locale(SessionHolder.getLang())));
                }
            } else {
                //加班上限提示校验，仅提示
                //加班上限分钟
                Long otTopLimitMin = otTopLimitH * 60L;
                Long surplusDuration = otTopLimitMin - totalDuration;
                if (surplusDuration <= 0) {
                    //本月申请加班时长已超过{0}小时
                    return Result.ok(messageResource.getMessage("L006226", new Object[]{otTopLimitH}, new Locale(SessionHolder.getLang())));
                }
                if (totalOtTime > surplusDuration) {
                    //本月申请加班时长已超过{0}小时
                    return Result.ok(messageResource.getMessage("L006226", new Object[]{otTopLimitH}, new Locale(SessionHolder.getLang())));
                }
            }
        }

        return Result.ok("");
    }

    /**
     * 校验加班时间是否在上班时间段内
     *
     * @param shiftDefMap
     * @param workTimeDetail
     * @param overtimeStartTime
     * @param overtimeEndTime
     * @return
     */
    private boolean checkIfInWorkTime(Map<Integer, WaShiftDef> shiftDefMap,
                                      WaWorktimeDetail workTimeDetail,
                                      Long overtimeStartTime, Long overtimeEndTime) {
        Long workDate = workTimeDetail.getWorkDate();
        List<Integer> shiftDefIdList = workTimeDetail.doGetShiftDefIdList();
        for (Integer shiftDefId : shiftDefIdList) {
            WaShiftDef shiftDef = shiftDefMap.get(shiftDefId);
            if (null == shiftDef) {
                continue;
            }
            List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
            for (MultiWorkTimeBaseDto shiftWorkTime : multiWorkTimeList) {
                long workStartTime = workDate + shiftWorkTime.doGetRealStartTime() * 60;
                long workEndTime = workDate + shiftWorkTime.doGetRealEndTime() * 60;
                if (overtimeStartTime < workEndTime && overtimeEndTime > workStartTime) {
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 校验加班时间是否在工作时间范围内的休息时间段中
     *
     * @param shiftDefMap
     * @param workTimeDetail
     * @param overtimeStartTime
     * @param overtimeEndTime
     * @return
     */
    private boolean checkRestTime(Map<Integer, WaShiftDef> shiftDefMap,
                                  WaWorktimeDetail workTimeDetail,
                                  Long overtimeStartTime, Long overtimeEndTime) {
        for (Integer shiftDefId : workTimeDetail.doGetShiftDefIdList()) {
            WaShiftDef shiftDef = shiftDefMap.get(shiftDefId);
            if (null == shiftDef) {
                continue;
            }
            Boolean isNoonRest = Optional.ofNullable(shiftDef.getIsNoonRest()).orElse(false);
            if (!isNoonRest) {
                continue;
            }
            // 获取班次休息时间
            List<RestPeriodDto> restPeriodList = WaShiftDo.doGetRestPeriodList(shiftDef);
            if (CollectionUtils.isNotEmpty(restPeriodList)) {
                // 检验加班时间是否在上班休息时间范围之间
                for (RestPeriodDto periodDto : restPeriodList) {
                    ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTimeV2(periodDto, shiftDef, workTimeDetail.getDateType());
                    Integer noonRestStartMi = restPeriod.getNoonRestStart();
                    Integer noonRestEndMi = restPeriod.getNoonRestEnd();

                    long noonRestStartTime = workTimeDetail.getWorkDate() + noonRestStartMi * 60;
                    long noonRestEndTime = workTimeDetail.getWorkDate() + noonRestEndMi * 60;

                    if (overtimeStartTime >= noonRestStartTime && overtimeEndTime <= noonRestEndTime) {
                        return Boolean.TRUE;
                    }
                }
            }
        }
        return Boolean.FALSE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> revokeEmpOt(OverApplyRevokeDto dto, UserInfo userInfo) throws Exception {
        WaEmpOvertime ot = waEmpOvertimeMapper.selectByPrimaryKey(dto.getWaid());
        if (ot == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_NOT_EXIST, Boolean.FALSE);
        }
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(ot.getStatus().intValue())) {
            return ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE);
        }
        Optional<OverTimeDto> overtimeTypeOpt = Optional.ofNullable(overTimeService.getOtType(ot.getOvertimeTypeId()));
        if (!overtimeTypeOpt.isPresent()) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        OverTimeDto overtimeType = overtimeTypeOpt.get();
        Boolean revokePassed = Optional.ofNullable(overtimeType.getRevokePassed()).orElse(false);
        if (!revokePassed) {
            if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(ot.getStatus().intValue()) && !ApprovalStatusEnum.PASSED.getIndex().equals(ot.getStatus().intValue())) {
                // 审批中/审批通过的可撤销
                return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_NOT_ALLOW, Boolean.FALSE);
            }
        } else {
            if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(ot.getStatus().intValue())) {
                // 审批中的可撤销
                return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_NOT_ALLOW, Boolean.FALSE);
            }
        }
        //超过考勤截止日不允许撤销
        Long onlyDate = DateUtil.getOnlyDate();
        WaSob waSob = waSobService.getWaSob(ot.getEmpid(), ot.getStartTime());
        if (waSob != null) {
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            if (onlyDate > sobEndDate) {
                Integer sysPeriodMonth = waSob.getSysPeriodMonth();
                //throw new CDException("申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员。");
                throw new CDException(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
            }
        }
        Long overTimeDate = DateUtil.getOnlyDate(new Date(ot.getStartTime() * 1000));
        String tenantId = userInfo.getTenantId();
        List<EmpCompensatoryQuotaDo> quotas = getOvertimeToCompensatoryQuotas(userInfo.getTenantId(), ot.getOtId());
        if (CollectionUtils.isEmpty(quotas)) {
            quotas = empCompensatoryQuotaDo.getQuotaByDate(userInfo.getTenantId(), Collections.singletonList(ot.getEmpid()), overTimeDate, overTimeDate, DataSourceEnum.AUTO.name(), Collections.singletonList(2));
        }
        quotas = quotas.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EmpCompensatoryQuotaDo::getQuotaId))), ArrayList::new));
        if (quotas.stream().anyMatch(q -> q.getUsedDay() > 0 || q.getInTransitQuota() > 0)) {
            String errMsg = checkQuotaUsed(tenantId, quotas);
            if (StringUtils.isNotBlank(errMsg)) {
                return Result.fail(AttendanceCodes.COMPENSATE_QUOTA_USED, errMsg);
            } else {
                // 额度已被使用不可撤销
                return ResponseWrap.wrapResult(AttendanceCodes.QUOTA_USED_REVOKE_NOT_ALLOW, Boolean.FALSE);
            }
        }

        // 如果开启撤销工作流流程，则需要发起撤销流程（非批量撤销入口）
        if (overtimeType.getRevokeWorkflow() != null && overtimeType.getRevokeWorkflow() && !dto.isIfBatch()) {
            String revokeAllowStatus = Optional.ofNullable(overtimeType.getRevokeAllowStatus()).orElse("");
            String[] revokeAllowStatusArr = revokeAllowStatus.split(",");
            List<Integer> revokeAllowStatusList = Arrays.stream(revokeAllowStatusArr).map(Integer::valueOf).collect(Collectors.toList());
            if (revokeAllowStatusList.contains(ot.getStatus().intValue())) {
                //根据加班单审批状态判断是走撤销还是废止流程
                BusinessCodeEnum workflowEnum = BusinessCodeEnum.OVERTIME_REVOKE;
                if (ApprovalStatusEnum.PASSED.getIndex().equals(ot.getStatus().intValue())) {
                    workflowEnum = BusinessCodeEnum.OVERTIME_ABOLISH;
                }
                if (CollectionUtils.isNotEmpty(workflowRevokeDo.getWorkflowRevokeList(userInfo.getTenantId(), Long.valueOf(ot.getOtId()),
                        Collections.singletonList(ApprovalStatusEnum.IN_APPROVAL.getIndex()), Collections.singletonList(workflowEnum.name())))) {
                    //校验是否有审批中的撤销流程，已有审批中的撤销流程不可进行撤销
                    return ResponseWrap.wrapResult(AttendanceCodes.DURING_REVOKING_OVERTIME_REVOKE_NOT_ALLOW, Boolean.FALSE);
                }
                //检查流程是否已启用
                Result<?> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(workflowEnum.getCode());
                if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
                    return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, Boolean.FALSE);
                }
                Boolean workflowEnabledResultData = (Boolean) checkWorkflowEnableResult.getData();
                if (!workflowEnabledResultData) {
                    return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, Boolean.FALSE);
                }
                //保存撤销流程
                WaWorkflowRevokeDo workflowRevoke = getWaWorkflowRevoke(userInfo, dto, workflowEnum);
                DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                // 发起新事务
                def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                TransactionStatus transactionStatus = platformTransactionManager.getTransaction(def);
                try {
                    //保存撤销流程
                    workflowRevokeDo.save(workflowRevoke);
                    // 提交新开事务
                    platformTransactionManager.commit(transactionStatus);
                } catch (Exception e) {
                    // 回滚新开事务
                    platformTransactionManager.rollback(transactionStatus);
                    log.error("申请加班撤销/废止失败:{}", e.getMessage(), e);
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_REVOKE_OR_ABOLISH_FAILED, null).getMsg());
                }
                String msg = "";
                try {
                    //发起工作流
                    WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
                    wfBeginWorkflowDto.setFuncCode(workflowEnum.getCode());
                    wfBeginWorkflowDto.setBusinessId(workflowRevoke.getId().toString());
                    SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(ot.getEmpid());
                    if (null == empInfo) {
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, null).getMsg());
                    }
                    wfBeginWorkflowDto.setApplicantId(empInfo.getEmpid().toString());
                    wfBeginWorkflowDto.setApplicantName(empInfo.getEmpName());
                    wfBeginWorkflowDto.setEventTime(overTimeDate * 1000);
                    wfBeginWorkflowDto.setTimeSlot(getTimeSlot(ot));
                    Result<?> result = wfRegisterFeign.begin(wfBeginWorkflowDto);
                    if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE).getMsg());
                    }
                    if (CollectionUtils.isNotEmpty(quotas)) {
                        //配额冻结
                        quotas.forEach(quota -> {
                            if (quota.getStatus() == 2) {
                                quota.setStatus(8);
                            }
                        });
                        empCompensatoryQuotaDo.batchUpdate(quotas);
                    }
                } catch (Exception e) {
                    log.error("Employee overtime revoke or abolish apply has exception:{}", e.getMessage(), e);
                    workflowRevokeDo.delete(workflowRevoke);
                    if (e instanceof CDException) {
                        if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                            msg = ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_CONFIG_ERR, Boolean.FALSE).getMsg();
                        } else {
                            msg = e.getMessage();
                        }
                    } else {
                        msg = ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE).getMsg();
                    }
                }
                if (StringUtils.isNotBlank(msg)) {
                    return Result.fail(msg);
                }
                int code = workflowEnum.equals(BusinessCodeEnum.OVERTIME_ABOLISH) ? AttendanceCodes.WORKFLOW_ABOLISH_SUBMIT_SUCCESS : AttendanceCodes.WORKFLOW_REVOKE_SUBMIT_SUCCESS;
                return Result.status(true, 0, ResponseWrap.wrapResult(code, Boolean.TRUE).getMsg());
            }
        }

        // 直接撤销加班单
        return revokeOvertimeWorkflow(userInfo.getTenantId(), userInfo.getUserId(), waSob, ot, dto.getRecokeReason(),
                ApprovalStatusEnum.REVOKED, quotas, dto.isIfBatch());
    }

    @Override
    public String checkQuotaUsed(String tenantId, List<EmpCompensatoryQuotaDo> quotas) {
        if (CollectionUtils.isEmpty(quotas)) {
            return "";
        }
        List<Long> quotaIds = quotas.stream().filter(q -> q.getUsedDay() > 0 || q.getInTransitQuota() > 0).map(EmpCompensatoryQuotaDo::getQuotaId).collect(Collectors.toList());
        QueryWrapper<WaCompensatoryQuotaUse> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("quota_id", quotaIds);
        queryWrapper.in("approval_status", Arrays.asList(ApprovalStatusEnum.IN_APPROVAL.getIndex(), ApprovalStatusEnum.PASSED.getIndex()));
        List<WaCompensatoryQuotaUse> useList = waCompensatoryQuotaUseMapper.selectList(queryWrapper);
        List<Long> leaveDaytimeIds = useList.stream().map(WaCompensatoryQuotaUse::getLeaveDaytimeId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaveDaytimeIds)) {
            return "";
        }
        List<WaEmpLeaveDo> empLeaveList = waEmpLeaveDo.getWaEmpLeaveListByLeaveDaytimeIds(leaveDaytimeIds);
        empLeaveList = empLeaveList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WaEmpLeaveDo::getLeaveId))), ArrayList::new));
        String empLeaveStr = empLeaveList.stream().map(el -> {
            Long startTime = el.getStartTime();
            Long endTime = el.getEndTime();
            Integer periodType = el.getPeriodType() != null ? Integer.valueOf(el.getPeriodType()) : null;
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                return String.format("%s~%s", DateUtil.getDateStrByTimesamp(startTime), DateUtil.getDateStrByTimesamp(endTime));
            } else {
                if (el.getShiftStartTime() != null && el.getShiftEndTime() != null) {
                    startTime = el.getShiftStartTime();
                    endTime = el.getShiftEndTime();
                }
                if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    Long start = el.getStartTime();
                    Long end = el.getEndTime();
                    return String.format("%s~%s", String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(el.getShalfDay())),
                            String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(el.getEhalfDay())));
                } else {
                    return String.format("%s~%s", DateUtil.getTimeStrByTimesamp4(startTime), DateUtil.getTimeStrByTimesamp4(endTime));
                }
            }
        }).collect(Collectors.joining(","));
        if (StringUtils.isNotEmpty(empLeaveStr)) {
            // 额度已被调休休假使用不可撤销
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_CARRIED_COMPENSATORY_REVOKE_NOT_ALLOWED, null).getMsg(), empLeaveStr);
        }
        List<EmpCompensatoryCaseApplyDo> list = empCompensatoryCaseApplyDo.getCompensatoryCaseList(tenantId, leaveDaytimeIds, Arrays.asList(ApprovalStatusEnum.IN_APPROVAL.getIndex(), ApprovalStatusEnum.PASSED.getIndex()));
        if (CollectionUtils.isNotEmpty(list)) {
            // 调休转付现
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_CARRIED_CASH_REVOKE_NOT_ALLOWED, null).getMsg();
        }
        return "";
    }

    @Transactional
    @Override
    public void deleteByBatchId(String tenantId, Long batchId) {
        WaEmpOvertimeExample otExample = new WaEmpOvertimeExample();
        WaEmpOvertimeExample.Criteria otCriteria = otExample.createCriteria();
        otCriteria.andTenantIdEqualTo(tenantId).andBatchIdEqualTo(batchId);
        List<WaEmpOvertime> otList = waEmpOvertimeMapper.selectByExample(otExample);
        if (CollectionUtils.isEmpty(otList)) {
            return;
        }
        List<Integer> otIdList = otList.stream().map(WaEmpOvertime::getOtId).distinct().collect(Collectors.toList());

        WaEmpOvertimeDetailExample detailExample = new WaEmpOvertimeDetailExample();
        WaEmpOvertimeDetailExample.Criteria detailCriteria = detailExample.createCriteria();
        detailCriteria.andOvertimeIdIn(otIdList);
        List<WaEmpOvertimeDetail> otDetailList = waEmpOvertimeDetailMapper.selectByExample(detailExample);
        if (CollectionUtils.isEmpty(otDetailList)) {
            return;
        }

        waEmpOvertimeDetailMapper.deleteByExample(detailExample);
        waEmpOvertimeMapper.deleteByExample(otExample);
    }

    @Override
    public Result<Boolean> revokeOvertimeWorkflow(String tenantId, Long userId, WaSob waSob, WaEmpOvertime empOvertime,
                                                  String revokeReason, ApprovalStatusEnum approvalStatus,
                                                  List<EmpCompensatoryQuotaDo> quotas, boolean ifBatch) throws Exception {
        if (!ifBatch) {
            // 撤销加班流程
            WfRevokeDto revokeDto = new WfRevokeDto();
            String businessKey = String.format("%s_%s", empOvertime.getOtId(), BusinessCodeEnum.OVERTIME.getCode());
            revokeDto.setBusinessKey(businessKey);
            Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
            if (null == result || !result.isSuccess()) {
                return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE);
            }
        }

        //更新单据状态
        WaEmpOvertime waEmpOvertime = new WaEmpOvertime();
        waEmpOvertime.setOtId(empOvertime.getOtId());
        waEmpOvertime.setStatus(approvalStatus.getIndex().shortValue());
        waEmpOvertime.setRevokeReason(revokeReason);
        waEmpOvertime.setUpdtime(DateUtil.getCurrentTime(true));
        waEmpOvertimeMapper.updateByPrimaryKeySelective(waEmpOvertime);

        //加班时长返还
        giveBackOvertimeDuration(tenantId, quotas);

        //查询加班单据
        Long startDate = DateUtil.getOnlyDate(new Date(empOvertime.getStartTime() * 1000));
        Long endDate = startDate + 86399;
        List<WaEmpOvertimeDo> waEmpOvertimes = waEmpOvertimeDo.getEmpOvertimeList(empOvertime.getEmpid(), startDate, endDate);
        if (CollectionUtils.isNotEmpty(waEmpOvertimes)) {
            quotas.forEach(q -> {
                q.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
                q.setUpdateTime(System.currentTimeMillis() / 1000L);
                q.setUpdateBy(userId);
            });
            // 更新配额
            empCompensatoryQuotaDo.batchUpdate(quotas);
            //重新计算调休配额
            if (waSob != null) {
                quotaService.genEmpQuotaForCompensatoryLeave(tenantId, userId, waSob.getWaSobId(), waEmpOvertime.getEmpid(), null, ConvertHelper.longConvert(tenantId));
            }
            // 发送消息
            this.sendMsg(ConvertHelper.longConvert(tenantId), empOvertime);
            return Result.ok(Boolean.TRUE);
        }

        // 状态设为已撤销
        if (CollectionUtils.isEmpty(waEmpOvertimes)) {
            quotas.forEach(q -> {
                q.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
                q.setUpdateTime(System.currentTimeMillis() / 1000L);
                q.setUpdateBy(userId);
            });
            // 更新配额
            empCompensatoryQuotaDo.batchUpdate(quotas);
        }

        // 发送消息
        this.sendMsg(ConvertHelper.longConvert(tenantId), empOvertime);
        return Result.ok(Boolean.TRUE);
    }

    @Override
    public List<EmpCompensatoryQuotaDo> getOvertimeToCompensatoryQuotas(String tenantId, Integer otId) {
        WaEmpOvertimeDetailExample example = new WaEmpOvertimeDetailExample();
        WaEmpOvertimeDetailExample.Criteria criteria = example.createCriteria();
        criteria.andOvertimeIdEqualTo(otId);
        List<WaEmpOvertimeDetail> details = waEmpOvertimeDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(details)) {
            return Lists.newArrayList();
        }
        List<Integer> detailIds = details.stream().map(WaEmpOvertimeDetail::getDetailId).distinct().collect(Collectors.toList());
        //查询调休转换扣减记录
        List<WaCompensatoryQuotaRecordDo> records = waCompensatoryQuotaRecordDo.getWaCompensatoryQuotaRecord(tenantId, detailIds, Collections.singletonList(ApprovalStatusEnum.PASSED.getIndex()));
        //查询相关调休配额
        List<Long> quotaIds = records.stream().map(WaCompensatoryQuotaRecordDo::getQuotaId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(quotaIds)) {
            return Lists.newArrayList();
        }
        return empCompensatoryQuotaDo.getEmpCompensatoryQuotaList(quotaIds);
    }

    public void giveBackOvertimeDuration(String tenantId, List<EmpCompensatoryQuotaDo> quotas) {
        log.info("Start to giveBackOvertimeDuration,tenantId:{},quotas:{}", tenantId, JSONUtils.ObjectToJson(quotas));
        if (CollectionUtils.isEmpty(quotas)) {
            return;
        }
        List<Long> quotaIds = quotas.stream().map(EmpCompensatoryQuotaDo::getQuotaId).distinct().collect(Collectors.toList());
        //查询调休转换扣减记录
        List<WaCompensatoryQuotaRecordDo> records = waCompensatoryQuotaRecordDo.getCompensatoryQuotaRecord(tenantId, quotaIds, Collections.singletonList(ApprovalStatusEnum.PASSED.getIndex()));
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        WaEmpOvertimeDetailExample example = new WaEmpOvertimeDetailExample();
        WaEmpOvertimeDetailExample.Criteria criteria = example.createCriteria();
        criteria.andDetailIdIn(records.stream().map(WaCompensatoryQuotaRecordDo::getDetailId).distinct().collect(Collectors.toList()));
        List<WaEmpOvertimeDetail> details = waEmpOvertimeDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(details)) {
            log.info("giveBackOvertimeDuration details is empty,tenantId:{},quotas:{}", tenantId, JSONUtils.ObjectToJson(quotas));
            return;
        }
        Map<Integer, List<WaCompensatoryQuotaRecordDo>> map = records.stream().collect(Collectors.groupingBy(WaCompensatoryQuotaRecordDo::getDetailId));
        List<WaEmpOvertimeDetail> updDetails = Lists.newArrayList();
        List<WaCompensatoryQuotaRecordDo> updRecords = Lists.newArrayList();
        for (WaEmpOvertimeDetail detail : details) {
            if (map.containsKey(detail.getDetailId())) {
                List<WaCompensatoryQuotaRecordDo> rows = map.get(detail.getDetailId());
                rows.forEach(row -> {
                    WaCompensatoryQuotaRecordDo record = new WaCompensatoryQuotaRecordDo();
                    record.setId(row.getId());
                    record.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
                    record.setUpdateTime(DateUtil.getCurrentTime(true));
                    updRecords.add(record);
                });
                WaEmpOvertimeDetail updDetail = new WaEmpOvertimeDetail();
                Float totalDuration = BigDecimal.valueOf(rows.stream().mapToDouble(WaCompensatoryQuotaRecordDo::getCarryDuration).sum()).floatValue();
                updDetail.setDetailId(detail.getDetailId());
                int compareTo = new BigDecimal(detail.getTimeDuration().toString()).compareTo(new BigDecimal(String.valueOf(detail.getLeftDuration() + totalDuration)));
                updDetail.setLeftDuration(compareTo == 0 ? 0f : detail.getLeftDuration() + totalDuration);
                updDetail.setCarriedForward(0);
                updDetail.setUpdtime(DateUtil.getCurrentTime(true));
                updDetails.add(updDetail);
            }
        }
        if (CollectionUtils.isNotEmpty(updRecords)) {
            waCompensatoryQuotaRecordDo.update(updRecords);
        }
        if (CollectionUtils.isNotEmpty(updDetails)) {
            importService.fastUpdList(WaEmpOvertimeDetail.class, "detailId", updDetails);
        }
        log.info("giveBackOvertimeDuration end,tenantId:{},quotas:{}", tenantId, JSONUtils.ObjectToJson(quotas));
    }

    public WaWorkflowRevokeDo getWaWorkflowRevoke(UserInfo userInfo, OverApplyRevokeDto dto, BusinessCodeEnum workflowEnum) {
        WaWorkflowRevokeDo workflowRevoke = new WaWorkflowRevokeDo();
        workflowRevoke.setId(snowflakeUtil.createId());
        workflowRevoke.setTenantId(userInfo.getTenantId());
        workflowRevoke.setEntityId(Long.valueOf(dto.getWaid()));
        workflowRevoke.setModuleName(workflowEnum.name());
        workflowRevoke.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        workflowRevoke.setReason(dto.getRecokeReason());
        workflowRevoke.setDeleted(DeleteStatusEnum.UN_DELETED.getIndex());
        workflowRevoke.setCreateBy(userInfo.getUserId());
        workflowRevoke.setCreateTime(System.currentTimeMillis() / 1000);
        return workflowRevoke;
    }

    public void sendMsg(Long corpId, WaEmpOvertime waEmpOvertime) {
        log.info("Start to send revoke message");
        List<Long> receiver = new ArrayList<>();
        receiver.add(waEmpOvertime.getEmpid());
        MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                .type("msg")
                .subType("revoke")
                .title(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_MSG_REMINDER, null).getMsg())
                .funcType("2")
                .handlers(receiver)
                .customForms(getFormCustoms(corpId, waEmpOvertime));
        MessageParams messageParams = builder.build();
        String jsonStr = JSON.toJSONString(messageParams);
        attendanceWorkflowMsgPublish.publish(jsonStr, corpId);
        log.info("Success to send revoke message, messageParams[{}]", FastjsonUtil.toJson(messageParams));
    }

    private List<KeyValue> getFormCustoms(Long corpId, WaEmpOvertime waEmpOvertime) {
        WaEmpOvertimeDo overtimeDo = waEmpOvertimeDo.getOtDetailById(corpId, Long.valueOf(waEmpOvertime.getOtId()));
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(waEmpOvertime.getEmpid());
        List<KeyValue> list = new ArrayList<>();
        String applicant = empInfo.getWorkno().concat("(").concat(empInfo.getEmpName().concat(")"));
        list.add(new KeyValue("申请人", applicant));
        list.add(new KeyValue("任职组织", overtimeDo.getOrgName()));
        list.add(new KeyValue("开始时间", DateUtil.getTimeStrByTimesamp4(waEmpOvertime.getStartTime())));
        list.add(new KeyValue("结束时间", DateUtil.getTimeStrByTimesamp4(waEmpOvertime.getEndTime())));
        list.add(new KeyValue("申请事由", StringUtils.isBlank(waEmpOvertime.getReason()) ? "-" : waEmpOvertime.getReason()));
        return list;
    }

    /**
     * 查询补偿方式
     *
     * @param empid
     * @param startTime
     * @param endTime
     * @param overtimeDate 加班日期（年月日unix时间戳，精确到秒）
     * @return
     */
    @Override
    public Result<List<OtCompensateTypeListDto>> getCompensateTypeList(Long empid, Long startTime, Long endTime, Long overtimeDate) {
        if (startTime > endTime) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.STARTTIME_CANNOTBE_GE_ENDTIME, Boolean.FALSE).getMsg());
        }

        // 员工信息
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
        if (empInfo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, Lists.newArrayList());
        }
        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
        tmType = tmType == 0 ? 1 : tmType;
        empInfo.setTmType(tmType);

        // 加班时间
        long otStartTime = startTime;
        long otEndTime = endTime;
        long otStartDate = DateUtil.getOnlyDate(new Date(otStartTime * 1000));
        long otEndDate = DateUtil.getOnlyDate(new Date(otEndTime * 1000));

        // 查询员工排班
        UserInfo userInfo = this.getUserInfo();
        Long preDate = DateUtil.addDate(otStartDate * 1000, -1);
        Long nextDate = DateUtil.addDate(otEndDate * 1000, 1);
        Map<Long, WaWorktimeDetail> empShiftMap = waCommonService.getEmpWaWorktimeDetail(userInfo.getTenantId(), empid,
                empInfo.getTmType(), preDate, nextDate, empInfo.getWorktimeType(), true);
        if (MapUtils.isEmpty(empShiftMap) || empShiftMap.get(otStartDate) == null || empShiftMap.get(otEndDate) == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, Lists.newArrayList());
        }

        // 查询班次定义信息
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(userInfo.getTenantId());
        if (MapUtils.isEmpty(shiftDefMap)) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, Lists.newArrayList());
        }

        // 查询员工当前时间所属的考勤方案
        Map groupMap = null;
        List<Map> nowGroupRuleList = getNowEmpBelongGroup(empInfo.getEmpid());
        if (CollectionUtils.isNotEmpty(nowGroupRuleList)) {
            groupMap = nowGroupRuleList.get(0);
        }

        // 跨夜加班是否归属至加班开始日期
        boolean overtimeBelongStartDate = checkOvertimeBelong(groupMap);

        // 指定加班归属日期
        if (null != overtimeDate) {
            overtimeDate = DateUtil.getOnlyDate(new Date(overtimeDate * 1000));
            // 查询指定加班归属日期当天班次所匹配的加班类型
            WaWorktimeDetail overtimeDateWorkDetail = empShiftMap.get(overtimeDate);
            List<WaOvertimeType> overtimeDateTypeList = overTimeTypeDo.getOvertimeTypeList(userInfo.getTenantId(), empid,
                    overtimeDate, overtimeDateWorkDetail.getDateType());
            if (CollectionUtils.isEmpty(overtimeDateTypeList)) {
                return ResponseWrap.wrapResult(AttendanceCodes.NOT_SET_OT_TYPE, Lists.newArrayList());
            }
            return Result.ok(doConvertOtCompensateType(overtimeDateTypeList));
        }

        // 查询加班开始日期当天班次所匹配的加班类型
        WaWorktimeDetail startWorkDetail = empShiftMap.get(otStartDate);
        List<WaOvertimeType> startDateTypeList = overTimeTypeDo.getOvertimeTypeList(userInfo.getTenantId(), empid,
                otStartDate, startWorkDetail.getDateType());
        if (CollectionUtils.isEmpty(startDateTypeList)) {
            return ResponseWrap.wrapResult(AttendanceCodes.NOT_SET_OT_TYPE, Lists.newArrayList());
        }

        if (otEndDate > otStartDate) {
            // 计算加班开始日期班次的最晚的下班打卡时间
            Optional<WaShiftDef> startShiftDefOpt = startWorkDetail.doGetShiftDefIdList().stream()
                    .map(shiftDefMap::get)
                    .filter(it -> !Objects.isNull(it))
                    .max(Comparator.comparing(WaShiftDef::doGetRealStartTime));
            if (!startShiftDefOpt.isPresent()) {
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, Lists.newArrayList());
            }
            WaShiftDef startShiftDef = startShiftDefOpt.get();
            WaShiftDef startShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(startShiftDef);
            long startDateOffDutyEndTime = otStartDate + startShiftWorkTime.getOffDutyEndTime() * 60;
            if (CdWaShiftUtil.checkCrossNightForSignOffEndTime(startShiftWorkTime, startShiftWorkTime.getDateType())) {
                startDateOffDutyEndTime += 86400L;
            }

            // 检查加班结束时间是否超过开始日期当天所在班次的最晚打卡时间时，如超过，则校验加班开始结束日期两天的加班类型对应的补偿方式是否一致
            WaWorktimeDetail endWorkDetail = empShiftMap.get(otEndDate);
            // TODO otEndTime > startDateOffDutyEndTime
            if (otEndTime > startDateOffDutyEndTime && !overtimeBelongStartDate) {
                List<WaOvertimeType> endDateTypeList = overTimeTypeDo.getOvertimeTypeList(userInfo.getTenantId(), empid,
                        otEndDate, endWorkDetail.getDateType());
                if (CollectionUtils.isEmpty(endDateTypeList)) {
                    return ResponseWrap.wrapResult(AttendanceCodes.COMPENSATE_TYPE_NOTEQ_RETRY, Lists.newArrayList());
                }
                List<Integer> startDateCompensateTypeList = startDateTypeList.stream()
                        .map(WaOvertimeType::getCompensateType).distinct().sorted().collect(Collectors.toList());
                List<Integer> endDateCompensateTypeList = endDateTypeList.stream()
                        .map(WaOvertimeType::getCompensateType).distinct().sorted().collect(Collectors.toList());
                if (startDateCompensateTypeList.size() != endDateCompensateTypeList.size()
                        || !startDateCompensateTypeList.toString().equals(endDateCompensateTypeList.toString())) {
                    return ResponseWrap.wrapResult(AttendanceCodes.COMPENSATE_TYPE_NOTEQ_RETRY, Lists.newArrayList());
                }
            }
        } else {
            // 检查加班时间是否跨到开始日期前一天的班次时间内
            WaWorktimeDetail preWorkDetail = empShiftMap.get(preDate);
            if (preWorkDetail != null) {
                Optional<WaShiftDef> preShiftDefOpt = preWorkDetail.doGetShiftDefIdList().stream()
                        .map(shiftDefMap::get)
                        .filter(it -> !Objects.isNull(it))
                        .max(Comparator.comparing(WaShiftDef::doGetRealStartTime));
                if (!preShiftDefOpt.isPresent()) {
                    return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, Lists.newArrayList());
                }
                WaShiftDef preShiftDef = preShiftDefOpt.get();
                WaShiftDef preShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(preShiftDef);

                // 计算前一天班次的最晚的下班打卡时间
                long preOffDutyEndTime = preDate + preShiftWorkTime.getOffDutyEndTime() * 60;
                if (CdWaShiftUtil.checkCrossNightForSignOffEndTime(preShiftWorkTime, preShiftWorkTime.getDateType())) {
                    preOffDutyEndTime += 86400L;
                }

                // 前一天的补偿方式
                List<WaOvertimeType> preDateTypeList = overTimeTypeDo.getOvertimeTypeList(userInfo.getTenantId(), empid,
                        preDate, preWorkDetail.getDateType());
                List<MultiOvertimeDto> multiOvertime = StringUtils.isNotBlank(preShiftDef.getMultiOvertime())
                        ? FastjsonUtil.toArrayList(preShiftDef.getMultiOvertime(), MultiOvertimeDto.class)
                        : Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(multiOvertime) && CollectionUtils.isNotEmpty(preDateTypeList)) {
                    if (otEndTime <= preOffDutyEndTime) {// 加班归属前一天班次
                        return Result.ok(doConvertOtCompensateType(preDateTypeList));
                    } else if (otStartTime < preOffDutyEndTime) {// 加班跨班
                        List<WaOvertimeType> endDateTypeList = overTimeTypeDo.getOvertimeTypeList(userInfo.getTenantId(), empid,
                                otEndDate, startWorkDetail.getDateType());
                        if (CollectionUtils.isEmpty(endDateTypeList)) {
                            return ResponseWrap.wrapResult(AttendanceCodes.COMPENSATE_TYPE_NOTEQ_RETRY, Lists.newArrayList());
                        }
                        List<Integer> startDateCompensateTypeList = preDateTypeList.stream().map(WaOvertimeType::getCompensateType).distinct().sorted().collect(Collectors.toList());
                        List<Integer> endDateCompensateTypeList = endDateTypeList.stream().map(WaOvertimeType::getCompensateType).distinct().sorted().collect(Collectors.toList());
                        if (!overtimeBelongStartDate
                                && (startDateCompensateTypeList.size() != endDateCompensateTypeList.size()
                                || !startDateCompensateTypeList.toString().equals(endDateCompensateTypeList.toString()))) {
                            return ResponseWrap.wrapResult(AttendanceCodes.COMPENSATE_TYPE_NOTEQ_RETRY, Lists.newArrayList());
                        }
                    }
                }
            }
        }

        return Result.ok(doConvertOtCompensateType(startDateTypeList));
    }

    public List<OtCompensateTypeListDto> doConvertOtCompensateType(List<WaOvertimeType> otTypeList) {
        return otTypeList.stream().map(o -> {
            OtCompensateTypeListDto compensateTypeDto = new OtCompensateTypeListDto();
            compensateTypeDto.setDescription(o.getDescription());
            compensateTypeDto.setText(o.getTypeName());
            if (null != o.getI18nTypeName()) {
                compensateTypeDto.setText(LangParseUtil.getI18nLanguage(o.getI18nTypeName(), o.getTypeName()));
            }
            compensateTypeDto.setValue(o.getOvertimeTypeId());
            compensateTypeDto.setCompensateType(o.getCompensateType());
            return compensateTypeDto;
        }).collect(Collectors.toList());
    }

    /**
     * 检查跨夜加班是否归属至加班开始日期
     *
     * @param groupMap
     * @return
     */
    public boolean checkOvertimeBelong(Map groupMap) {
        if (MapUtils.isEmpty(groupMap)) {
            return false;
        }
        Integer overtimeBelong = (Integer) groupMap.get("overtime_belong");
        return null != overtimeBelong && overtimeBelong.equals(OvertimeBelongType.OVERTIME_START_DATE.getIndex());
    }

    /**
     * 查询加班转调休记录详情
     *
     * @param tenantId 租户
     * @param quotaId  调休配额主键
     * @return
     */
    public List<OvertimeCarryForwardDto> getEmpOtCompensatoryList(String tenantId, Long quotaId) {
        List<OvertimeCarryForwardDto> list = Lists.newArrayList();
        List<WaEmpOvertimeDo> records = waEmpOvertimeDo.getEmpOtCompensatoryList(tenantId, quotaId);
        if (CollectionUtils.isEmpty(records)) {
            return list;
        }
        Integer timeUnit = PreTimeUnitEnum.HOUR.getIndex();
        String otIds = records.stream().map(WaEmpOvertimeDo::getOtId).map(String::valueOf).collect(Collectors.joining(","));
        List<Map> tdList = workOvertimeMapper.getTimeDurationByOtIds(otIds);
        Map<String, Float> realTimeDurationMap = tdList.stream().collect(Collectors.toMap(l -> String.format("%s_%s", l.get("overtime_id"), l.get("overtime_type_id")), l -> (Float) l.get("rel_time_duration"), (v1, v2) -> v1));
        records.forEach(record -> {
            OvertimeCarryForwardDto dto = new OvertimeCarryForwardDto();
            dto.setDetailId(record.getDetailId());
            dto.setOvertimeId(record.getOtId());
            dto.setApplyDate(record.getApplyTime());
            dto.setOvertimeTypeName(record.getOvertimeTypeName());
            dto.setStartTime(record.getStartTime());
            dto.setEndTime(record.getEndTime());
            dto.setTimeUnitName(PreTimeUnitEnum.getName(PreTimeUnitEnum.HOUR.getIndex()));
            Integer duration = Optional.ofNullable(record.getOtDuration()).orElse(0);
            BigDecimal v = (new BigDecimal(duration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
            dto.setDuration(v.floatValue());
            Float relTimeDuration = Optional.ofNullable(realTimeDurationMap.get(String.format("%s_%s", record.getOtId(), record.getOvertimeTypeId()))).orElse(0f);
            Float carryDuration = Optional.ofNullable(record.getCarryDuration()).orElse(0f);
            if (PreTimeUnitEnum.DAY.getIndex().equals(timeUnit)) {
                dto.setRelTimeDuration(relTimeDuration);
                dto.setCarryForwardDuration(carryDuration);
            } else {
                dto.setRelTimeDuration(BigDecimal.valueOf(relTimeDuration).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP).floatValue());
                dto.setCarryForwardDuration(BigDecimal.valueOf(carryDuration).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP).floatValue());
            }
            dto.setReason(record.getReason());
            dto.setStatusName(ApprovalStatusEnum.getName(record.getStatus()));
            dto.setLastApprovalTime(record.getLastApprovalTime());
            list.add(dto);
        });
        return list;
    }

    @Override
    public OvertimeLeftDurationDto getEmpOvertimeLeftDuration(Long empId) {
        OvertimeLeftDurationDto dto = new OvertimeLeftDurationDto();
        SysEmpInfo emp = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (null == emp) {
            return dto;
        }
        String tenantId = emp.getBelongOrgId();
        String empIds = null;
        if (null != empId) {
            List<Long> empIdList = Lists.newArrayList();
            empIdList.add(empId);
            empIds = "'{" + StringUtils.join(empIdList, ",").concat("}'");
        }
        // 查询加班调休转换方式为其他的加班类型
        List<WaOvertimeType> overtimeTypes = overTimeTypeDo.getOtherTransferRuleOvertimeType(tenantId);
        if (CollectionUtils.isEmpty(overtimeTypes)) {
            return dto;
        }
        List<Integer> overtimeTypeIds = overtimeTypes.stream().map(WaOvertimeType::getOvertimeTypeId).distinct().collect(Collectors.toList());
        String anyOvertimeTypeIds = "'{" + StringUtils.join(overtimeTypeIds, ",").concat("}'");
        // 查询符合条件的加班单详情
        List<EmpOvertimeDetailDo> details = empOvertimeDetailDo.getEmpLeftDurationOvertimeDetail(tenantId, empIds, anyOvertimeTypeIds);
        dto.setDisplay(CollectionUtils.isNotEmpty(details));
        if (dto.getDisplay()) {
            Float leftDuration = BigDecimal.valueOf(details.stream().filter(detail -> detail.getLeftDuration() != null).mapToDouble(EmpOvertimeDetailDo::getLeftDuration).sum()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN).floatValue();
            dto.setLeftDuration(leftDuration);
        }
        return dto;
    }

    @Override
    public AttendancePageResult<OtLeftDurationDto> getEmpOvertimeLeftDurationPageList(OtLeftDurationRequestDto dto, UserInfo userInfo) {
        String tenantId = userInfo.getTenantId();
        AttendanceBasePage basePage = ObjectConverter.convert(dto, AttendanceBasePage.class);
        // 查询加班调休转换方式为其他的加班类型
        List<WaOvertimeType> overtimeTypes = overTimeTypeDo.getOtherTransferRuleOvertimeType(tenantId);
        if (CollectionUtils.isEmpty(overtimeTypes)) {
            return new AttendancePageResult<>(new ArrayList<>(), basePage.getPageNo(), basePage.getPageSize(), 0);
        }
        List<Integer> overtimeTypeIds = overtimeTypes.stream().map(WaOvertimeType::getOvertimeTypeId).distinct().collect(Collectors.toList());
        String anyOvertimeTypeIds = "'{" + StringUtils.join(overtimeTypeIds, ",").concat("}'");
        PageBean pageBean = PageUtil.getPageBean(basePage);
        PageList<SysEmpInfoDo> pageList = sysEmpInfoDo.getEmpPageList(pageBean, tenantId, dto.getKeywords(), dto.getDataScope());
        if (CollectionUtils.isEmpty(pageList)) {
            return new AttendancePageResult<>(new ArrayList<>(), basePage.getPageNo(), basePage.getPageSize(), 0);
        }
        List<Long> empIdList = pageList.stream().map(SysEmpInfoDo::getEmpid).collect(Collectors.toList());
        String empIds = "'{" + StringUtils.join(empIdList, ",").concat("}'");
        // 查询符合条件的加班单详情
        List<EmpOvertimeDetailDo> details = empOvertimeDetailDo.getEmpLeftDurationOvertimeDetail(tenantId, empIds, anyOvertimeTypeIds);
        Map<Long, List<EmpOvertimeDetailDo>> detailMap = details.stream().collect(Collectors.groupingBy(EmpOvertimeDetailDo::getEmpid));
        List<OtLeftDurationDto> list = Lists.newArrayList();
        for (SysEmpInfoDo empInfo : pageList) {
            list.add(new OtLeftDurationDto(empInfo.getEmpid(), empInfo.getEmpName(), empInfo.getWorkno(), this.getLeftDuration(detailMap.get(empInfo.getEmpid())), 2));
        }
        Paginator paginator = pageList.getPaginator();
        return new AttendancePageResult<>(list, basePage.getPageNo(), basePage.getPageSize(), paginator.getTotalCount());
    }

    private Float getLeftDuration(List<EmpOvertimeDetailDo> items) {
        if (CollectionUtils.isEmpty(items)) {
            return 0f;
        }
        return BigDecimal.valueOf(items.stream().filter(detail -> detail.getLeftDuration() != null).mapToDouble(EmpOvertimeDetailDo::getLeftDuration).sum()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN).floatValue();
    }

    @Override
    public List<EmpOverInfo> getEmpDailyOtList(String tenantId, List<Long> empIds, Long startTime, Long endTime) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(empIds) || null == startTime || null == endTime) {
            return Lists.newArrayList();
        }

        // 查询加班单据
        Map<String, Object> params = new HashMap<>();
        List<Integer> approvalStatusList = new ArrayList<>();
        approvalStatusList.add(ApprovalStatusEnum.PASSED.getIndex());
        approvalStatusList.add(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        params.put("approvalStatusList", approvalStatusList);
        params.put("belongid", tenantId);
        params.put("anyEmpids2", "'{" + StringUtils.join(empIds, ",") + "}'");
        params.put("startDate", startTime);
        params.put("endDate", endTime);
        List<Map> empOverList = otRecordMapper.getEmpOverTimeByEmpid(params);
        if (CollectionUtils.isEmpty(empOverList)) {
            return Lists.newArrayList();
        }

        // 按零点拆分单据标记
        Map<Object, List<Map>> overtimeMap = empOverList.stream().collect(Collectors.groupingBy(row -> row.get("ot_id")));
        overtimeMap.forEach((otId, otList) -> {
            if (otList.size() == 2) {
                otList = otList.stream().sorted(Comparator.comparing(ot -> (Long) ot.get("end_time"))).collect(Collectors.toList());
                Map ot = otList.get(1);
                ot.put("zeroSplitting", true);
            }
        });

        // 对象类型转换
        List<EmpOverInfo> empOverInfoList = FastjsonUtil.convertList(empOverList, EmpOverInfo.class);
        empOverInfoList.forEach(of -> {
            if (null != of.getReal_date()) {
                of.setReal_date(DateUtil.getDateLong(of.getReal_date() * 1000, "yyyy-MM-dd", true));
            }
        });
        return empOverInfoList;
    }

    @Override
    public BigDecimal getEmpPeriodOtDuration(String tenantId, Long empId, Long startDate) {
        Integer duration = getPeriodOtDuration(tenantId, empId, startDate);
        if (duration <= 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(duration).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
    }

    private Integer getPeriodOtDuration(String tenantId, Long empId, Long startDate) {
        startDate = DateUtil.getOnlyDate(new Date(startDate * 1000));
        Optional<WaSob> optional = Optional.ofNullable(waSobService.getWaSob(empId, startDate));
        if (!optional.isPresent()) {
            return 0;
        }
        WaSob waSob = optional.get();
        Long sobStartDate = waSob.getStartDate();
        Long sobEndDate = waSob.getSobEndDate();
        WaEmpOvertimeExample overtimeExample = new WaEmpOvertimeExample();
        overtimeExample.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andEmpidEqualTo(empId)
                .andStatusIn(Arrays.asList((short) 1, (short) 2))
                .andStartTimeBetween(sobStartDate, sobEndDate);
        List<WaEmpOvertime> records = waEmpOvertimeMapper.selectByExample(overtimeExample);
        return records.stream().map(WaEmpOvertime::getOtDuration).filter(Objects::nonNull).reduce(0, Integer::sum);
    }
}