package com.caidaocloud.attendance.service.application.event.subscribe;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.workflow.dto.WorkflowCompleteDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@RabbitListener(
        bindings = @QueueBinding(
                value = @Queue(value = "wf.completed.queue", durable = "true"),
                exchange = @Exchange(value = "wf.completed.direct.exchange"),
                key = {"wf.completed.direct.routingKey"}
        )
)
public class WorkflowEventSubscribe {
    @Resource
    private IWfService wfService;

    @RabbitHandler
    public void process(String message) {
        log.info("Start WorkflowEventSubscribeMessage:{}", message);
        try {
            if (StringUtil.isNotEmpty(message)) {
                WorkflowCompleteDto dto = JSON.parseObject(message, WorkflowCompleteDto.class);
                if (StringUtil.isAnyBlank(dto.getBusinessKey(), dto.getChoice()) || dto.getCompletedTime() == null) {
                    log.info("WorkflowEventSubscribe message is null");
                    return;
                }
                wfService.handleWorkflowEvent(dto.getBusinessKey(), dto.getChoice(), dto.getCompletedTime());
            }
        } catch (Exception ex) {
            log.error("WorkflowEventSubscribe process err:{}", ex.getMessage(), ex);
        }
        log.info("End WorkflowEventSubscribeMessage:{}", message);
    }
}
