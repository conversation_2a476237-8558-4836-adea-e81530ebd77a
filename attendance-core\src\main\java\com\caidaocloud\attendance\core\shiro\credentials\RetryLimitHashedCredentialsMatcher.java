package com.caidaocloud.attendance.core.shiro.credentials;

import com.caidaocloud.attendance.core.shiro.realm.CustomToken;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.credential.HashedCredentialsMatcher;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheManager;
import org.springframework.beans.factory.annotation.Value;

import java.util.concurrent.atomic.AtomicInteger;

public class RetryLimitHashedCredentialsMatcher extends HashedCredentialsMatcher {

    /**
     * 是否开启错误次数限制
     */
    @Value("${spring.retry.limit.open:false}")
    private boolean isOpenLimit;

    /**
     * 错误尝试次数
     */
    @Value("${spring.retry.limit.count:5}")
    private int limitCount;

    private Cache<String, AtomicInteger> passwordRetryCache;

    public RetryLimitHashedCredentialsMatcher(CacheManager cacheManager) {
        passwordRetryCache = cacheManager.getCache("passwordRetryCache");
    }

    @Override
    public boolean doCredentialsMatch(AuthenticationToken token, AuthenticationInfo info) {
        if (token instanceof CustomToken) {
            return true;
        } else {
            boolean matches = super.doCredentialsMatch(token, info);
            if (isOpenLimit) {
                String username = (String) token.getPrincipal();
                AtomicInteger retryCount = passwordRetryCache.get(username);
                if (retryCount == null) {
                    retryCount = new AtomicInteger(0);
                    passwordRetryCache.put(username, retryCount);
                }
                if (retryCount.incrementAndGet() > limitCount) {
                    throw new ExcessiveAttemptsException();
                }
                if (matches) {
                    passwordRetryCache.remove(username);
                } else {
                    passwordRetryCache.put(username, new AtomicInteger( retryCount.incrementAndGet()));
                }
            }
            return matches;
        }
    }
}
