package com.caidaocloud.attendance.core.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "caidao.swagger", ignoreInvalidFields = true)
public class ConfigurationSwaggerProperties {
    private boolean enableSwagger = false;

    private List<String> basePackage;

    public boolean isEnableSwagger() {
        return enableSwagger;
    }

    public void setEnableSwagger(boolean enableSwagger) {
        this.enableSwagger = enableSwagger;
    }

    public List<String> getBasePackage() {
        return basePackage;
    }

    public void setBasePackage(List<String> basePackage) {
        this.basePackage = basePackage;
    }
}