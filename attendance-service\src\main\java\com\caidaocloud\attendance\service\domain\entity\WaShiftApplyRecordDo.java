package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.service.domain.repository.IWaShiftApplyRecordRepository;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftRecordDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: Jiang<PERSON>
 * @Date: 2022/03/17
 * @Description:
 **/
@Data
@Service
public class WaShiftApplyRecordDo {

    private Long recId;

    private String tenantId;

    private Long empId;

    private Long workDate;

    private Integer oldShiftDefId;

    private Integer newShiftDefId;

    private String reason;

    private Integer status;

    private String revokeReason;

    private String fileId;

    private String fileName;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    //其他冗余字段
    private String workNo;

    private String empName;

    private String orgName;

    private String fullPath;

    private String statusName;

    private String oldShift;

    private String newShift;

    private Long lastApprovalTime;

    private String employType;

    private String workCity;

    private Long hireDate;

    private Integer oldStartTime;

    private Integer oldEndTime;

    private Integer newStartTime;

    private Integer newEndTime;
    private String oldI18nShiftDefName;
    private String newI18nShiftDefName;


    @Resource
    private IWaShiftApplyRecordRepository waShiftApplyRecordRepository;

    public int save(WaShiftApplyRecordDo applyRecordDo) {
        return waShiftApplyRecordRepository.save(applyRecordDo);
    }

    public List<ApplyShiftRecordDto> list(ApplyShiftRecordDto dto) {
        List<WaShiftApplyRecordDo> list = waShiftApplyRecordRepository.selectList(dto);
        List<ApplyShiftRecordDto> shiftRecordDtoList = ObjectConverter.convertList(list, ApplyShiftRecordDto.class);
        return shiftRecordDtoList;
    }

    public WaShiftApplyRecordDo getById(Long recId) {
        return waShiftApplyRecordRepository.selectById(recId);
    }

    public int update(WaShiftApplyRecordDo applyRecordDo) {
        return waShiftApplyRecordRepository.update(applyRecordDo);
    }

    public PageResult<WaShiftApplyRecordDo> pageList(AttendanceBasePage basePage, Map params) {
        return waShiftApplyRecordRepository.selectPageList(basePage, params);
    }

    public PageResult<WaShiftApplyRecordDo> pageListOfPortal(QueryPageBean queryPageBean) {
        return waShiftApplyRecordRepository.pageListOfPortal(queryPageBean);
    }

    @CDText(exp = {"employType" + TextAspect.DICT_E, "workCity" + TextAspect.PLACE}, classType = WaShiftApplyRecordDo.class)
    public WaShiftApplyRecordDo getShiftApplyInfoById(Long Id, Long corpId) {
        return waShiftApplyRecordRepository.queryShiftInfoById(Id, corpId);
    }

    public List<WaShiftApplyRecordDo> getEmpShiftChangeApplyList(String tenantId, List<Long> empIds, Long startDate, Long endDate) {
        if (CollectionUtils.isEmpty(empIds)) {
            return new ArrayList<>();
        }
        return waShiftApplyRecordRepository.getEmpShiftChangeApplyList(tenantId, empIds, startDate, endDate);
    }
}
