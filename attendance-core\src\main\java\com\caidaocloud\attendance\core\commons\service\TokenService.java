package com.caidaocloud.attendance.core.commons.service;

import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.caidao1.commons.utils.StringUtil;
import com.caidao1.mobile.bean.SessionBean;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Map;

@Service
public class TokenService {
    private final static Logger LOGGER = LoggerFactory.getLogger(TokenService.class);

    @Autowired
    private RedisTemplate redisTemplate;

    public SessionBean getSessionBean(String tokenId) {
        try {
            if(StringUtil.isNullOrTrimEmpty(tokenId)){ return null;}
            Map tokenMap = redisTemplate.opsForHash().entries("TOKEN_" + tokenId);
            return BeanUtils.mapToBean(tokenMap,SessionBean.class);
        } catch (Exception e) {
            LOGGER.error("getSessionBean 操作异常，tokeid：{}，errorMessage：{}", tokenId, e.getMessage(), e);
            return null;
        }
    }
    public SessionBean getSessionBean(HttpServletRequest request) {
        String tokenId = request.getHeader("token_id");
        SessionBean sessionBean;
        if (tokenId == null) {
            HttpSession session = request.getSession();
            sessionBean = (SessionBean) session.getAttribute("sessionBean");
            LOGGER.info("tokenId is null，get sessionBean for HttpSession");
        } else {
            sessionBean = getSessionBean(tokenId);
            if (sessionBean != null && StringUtils.isNotEmpty(request.getHeader("language"))) {
                sessionBean.setLanguage(request.getHeader("language"));
            }
        }
        //如果没有登录，则获取多语言
        if (sessionBean == null){
            sessionBean = new SessionBean();
            String lang = request.getHeader("language");
            if (lang == null){
                lang = request.getLocale().getLanguage();
            }
            sessionBean.setLanguage(lang);
        }else if(StringUtil.isNullOrTrimEmpty(sessionBean.getLanguage())){
            sessionBean.setLanguage(request.getLocale().getLanguage());
        }
        return sessionBean;
    }

    public Long getEmpId(String tokenId) {
        return (Long) redisTemplate.opsForHash().get("TOKEN_" + tokenId, "empid");

    }

    public Long getEmpId(HttpServletRequest request) {
        String tokenId = request.getHeader("token_id");
        if (tokenId == null) {
            HttpSession session = request.getSession();
            return (Long) session.getAttribute("empid");
        } else {
            return this.getEmpId(tokenId);
        }
    }

    public Long getUserId(String tokenId) {
        return (Long) redisTemplate.opsForHash().get("TOKEN_" + tokenId, "userid");
    }

    public Long getUserId(HttpServletRequest request) {
        String tokenId = request.getHeader("token_id");
        if (tokenId == null) {
            HttpSession session = request.getSession();
            return (Long) session.getAttribute("userid");
        } else {
            return this.getUserId(tokenId);
        }
    }

    public Long getCorpId(String tokenId) {
        return (Long) redisTemplate.opsForHash().get("TOKEN_" + tokenId, "corpid");
    }

    public Long getCorpId(HttpServletRequest request) {
        String tokenId = request.getHeader("token_id");
        if (tokenId == null) {
            HttpSession session = request.getSession();
            return (Long) session.getAttribute("corpid");
        } else {
            return this.getCorpId(tokenId);
        }

    }

    public Integer getTmType(String tokenId) {
        return (Integer) redisTemplate.opsForHash().get("TOKEN_" + tokenId, "tmtype");
    }

    public Integer getTmType(HttpServletRequest request) {
        String tokenId = request.getHeader("token_id");
        if (tokenId == null) {
            HttpSession session = request.getSession();
            return (Integer) session.getAttribute("tmType");
        } else {
            return this.getTmType(tokenId);
        }

    }

    public Long getOrgId(String tokenId) {
        return (Long) redisTemplate.opsForHash().get("TOKEN_" + tokenId, "orgid");
    }

    public Long getOrgId(HttpServletRequest request) {
        String tokenId = request.getHeader("token_id");
        if (tokenId == null) {
            HttpSession session = request.getSession();
            return (Long) session.getAttribute("orgid");
        } else {
            return this.getOrgId(tokenId);
        }

    }

    public String getBelongId(String tokenId) {
        return (String) redisTemplate.opsForHash().get("TOKEN_" + tokenId, "belongid");
    }

    public String getBelongId(HttpServletRequest request) {
        String tokenId = request.getHeader("token_id");
        if (tokenId == null) {
            HttpSession session = request.getSession();
            return (String) session.getAttribute("belongid");
        } else {
            return this.getBelongId(tokenId);
        }

    }

    public String getEmpname(String tokenId) {
        return (String) redisTemplate.opsForHash().get("TOKEN_" + tokenId, "empname");
    }

    public String getEmpname(HttpServletRequest request) {
        String tokenId = request.getHeader("token_id");
        if (tokenId == null) {
            HttpSession session = request.getSession();
            return (String) session.getAttribute("empname");
        } else {
            return this.getEmpname(tokenId);
        }

    }

//    public String getLang(String tokenId) {
//        return (String) redisTemplate.opsForHash().get("TOKEN_" + tokenId, "language");
//    }

    public String getLang(HttpServletRequest request) {
        return request.getHeader("language");
    }
}
