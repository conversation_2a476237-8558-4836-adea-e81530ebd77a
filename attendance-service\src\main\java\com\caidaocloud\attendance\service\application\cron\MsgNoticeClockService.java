package com.caidaocloud.attendance.service.application.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.service.impl.ClockTaskWorkService;
import com.caidaocloud.attendance.service.application.service.msg.DailyWeeklyMsgService;
import com.caidaocloud.attendance.service.schedule.application.service.notice.WaShiftNoticeService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 消息通知类定时任务
 * 日报周报定时任务
 * 签到提醒定时任务
 */
@Slf4j
@Service
public class MsgNoticeClockService {
    @Resource
    private DailyWeeklyMsgService dailyWeeklyMsgService;
    @Resource
    private ClockTaskWorkService clockTaskWorkService;
    @Resource
    private WaShiftNoticeService waShiftNoticeService;

    /**
     * 日报跑批任务
     *
     * @return
     */
    @XxlJob("dailyClockJobHandler")
    //@Scheduled(cron = "0 0/2 * * * ?")
    public ReturnT<String> dailyClock() {
        XxlJobHelper.log("XxlJob dailyClockJobHandler start");
        log.info("Timed Task[Daily]------------------------Start exec,time {}", System.currentTimeMillis());
        dailyWeeklyMsgService.sendDailyTtlMsg();
        log.info("Timed Task[Daily]------------------------End exec,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob dailyClockJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 周报跑批任务
     *
     * @return
     */
    @XxlJob("weeklyClockJobHandler")
    public ReturnT<String> weeklyClock() {
        XxlJobHelper.log("XxlJob weeklyClockJobHandler start");
        log.info("Timed Task[Weekly]------------------------Start exec,time {}", System.currentTimeMillis());
        String params = XxlJobHelper.getJobParam();
        JSONObject paramsObj = JSON.parseObject(params);
        String tenantId = paramsObj.getString("belongOrgId");
        dailyWeeklyMsgService.sendWeeklyTtlMsg(tenantId);
        log.info("Timed Task[Weekly]------------------------End exec,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob weeklyClockJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 签到提醒跑批任务
     *
     * @return
     */
    @XxlJob("signInReminderClockJobHandler")
    public ReturnT<String> signInReminderClock() {
        XxlJobHelper.log("XxlJob signInReminderClockJobHandler start");
        if (log.isDebugEnabled()) {
            log.debug("Timed Task[Sign in reminder]------------------------Start exec,time {}", System.currentTimeMillis());
        }
        clockTaskWorkService.synchronizeEmpClockShiftInfo(null);
        if (log.isDebugEnabled()) {
            log.debug("Timed Task[Sign in reminder]------------------------End exec,time {}", System.currentTimeMillis());
        }
        XxlJobHelper.log("XxlJob signInReminderClockJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 销假提醒跑批任务
     *
     * @return
     */
    @XxlJob("leaveCancelClockJobHandler")
    public ReturnT<String> leaveCancelClock() {
        XxlJobHelper.log("XxlJob leaveCancelClockJobHandler start");
        log.info("Timed Task[leave Cancel]------------------------Start exec,time {}", System.currentTimeMillis());
        clockTaskWorkService.synchronizeEmpLeaveCancelInfo(null);
        log.info("Timed Task[leave Cancel]------------------------End exec,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob leaveCancelClockJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 考勤异常汇总提醒
     * @return
     */
    @XxlJob("attendanceAbnormalJobHandler")
    public ReturnT<String> attendanceAbnormalReminderJob() {
        XxlJobHelper.log("XxlJob attendanceAbnormalJobHandler start");
        log.info("定时任务【考勤异常汇总提醒】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            String params = XxlJobHelper.getJobParam();
            JSONObject paramsObj = JSON.parseObject(params);
            Long tenantId = paramsObj.getLong("belongOrgId");
            dailyWeeklyMsgService.attendanceAbnormalReminder(String.valueOf(tenantId));
        } catch (Exception e) {
            log.error("【考勤异常汇总提醒】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【考勤异常汇总提醒】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob attendanceAbnormalJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 考勤明细提醒
     * @return
     */
    @XxlJob("attendanceDetailJobHandler")
    public ReturnT<String> attendanceDetailReminderJob() {
        XxlJobHelper.log("XxlJob attendanceDetailJobHandler start");
        log.info("定时任务【考勤明细提醒】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            String params = XxlJobHelper.getJobParam();
            JSONObject paramsObj = JSON.parseObject(params);
            Long tenantId = paramsObj.getLong("belongOrgId");
            Integer type = paramsObj.getInteger("type");
            Integer day = paramsObj.getInteger("day");
            Integer time = paramsObj.getInteger("time");
            Integer waGroupId = paramsObj.getInteger("waGroupId");
            dailyWeeklyMsgService.attendanceDetailReminder(String.valueOf(tenantId), type, day, time, waGroupId);
        } catch (Exception e) {
            log.error("【考勤明细提醒】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【考勤明细提醒】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob attendanceDetailJobHandler end");
        return ReturnT.SUCCESS;
    }



    /**
     * 员工上班提醒
     * @return
     */
    @XxlJob("empShiftWorkTimeReminder")
    public ReturnT<String> empShiftWorkTimeReminder() {
        XxlJobHelper.log("XxlJob empShiftWorkTimeReminder start");
        log.info("定时任务【员工上班提醒】------------------------开始执行,time {}", System.currentTimeMillis());
        String params = XxlJobHelper.getJobParam();
        if (StringUtils.isEmpty(params)) {
            XxlJobHelper.log("xxl job tenant params is empty");
            return ReturnT.SUCCESS;
        }
        String[] tenantIds = params.split(",");
        for (String tenantId : tenantIds) {
            try {
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setUserId(0L);
                userInfo.setTenantId(tenantId);
                SecurityUserUtil.setSecurityUserInfo(userInfo);
                UserInfo contextUser = new UserInfo();
                contextUser.setTenantId(tenantId);
                contextUser.setUserId(0L);
                UserContext.setCurrentUser(contextUser);
                log.info("【员工上班提醒】定时执行tenantId:{}", tenantId);
                waShiftNoticeService.sendEmpWorkReminder(Objects.toString(tenantId));
            } catch (Exception e) {
                log.error("【员工上班提醒】定时任务执行异常，tenantId:{}, errorMsg: {}", tenantId, e.getMessage(), e);
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
                UserContext.remove();
            }
        }
        log.info("定时任务【员工上班提醒】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob empShiftWorkTimeReminder end");
        return ReturnT.SUCCESS;
    }

    /**
     * 工序扫码上班提醒
     * @return
     */
    @XxlJob("empProcessWorkTimeReminder")
    public ReturnT<String> empProcessWorkTimeReminder() {
        XxlJobHelper.log("XxlJob empProcessWorkTimeReminder start");
        log.info("定时任务【员工工序扫码提醒】------------------------开始执行,time {}", System.currentTimeMillis());
        String tenantParams = XxlJobHelper.getJobParam();
        if (StringUtils.isEmpty(tenantParams)) {
            XxlJobHelper.log("xxl job params is empty");
            return ReturnT.SUCCESS;
        }
        String[] tenantIds = tenantParams.split(",");
        for (String tenantId : tenantIds) {
            try {
                UserInfo contextUser = new UserInfo();
                contextUser.setTenantId(tenantId);
                contextUser.setUserId(0L);
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setUserId(0L);
                userInfo.setTenantId(tenantId);
                SecurityUserUtil.setSecurityUserInfo(userInfo);
                UserContext.setCurrentUser(contextUser);
                log.info("【员工工序扫码提醒】定时执行tenantId:{}", tenantId);
                waShiftNoticeService.sendProcessWorkReminder(Objects.toString(tenantId));
            } catch (Exception e) {
                log.error("【员工工序扫码提醒】定时任务执行异常，tenantId:{}, errorMsg:{}", tenantId, e.getMessage(), e);
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
                UserContext.remove();
            }
        }
        log.info("定时任务【员工工序扫码提醒】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob empProcessWorkTimeReminder end");
        return ReturnT.SUCCESS;
    }
}
