package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IPlanEmpRelRepository;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpClockPlanRel;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo;
import com.caidaocloud.dto.EmpInfoKeyValue;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Data
@Service
public class WaPlanEmpRel {

    private Long id;
    private Long corpId;
    private String belongOrgId;
    private Long planId;
    private Long empId;
    private Long creator;
    private Long createTime;
    private Long updater;
    private Long updateTime;
    /**
     * 2021/09/09 by <PERSON>.chen
     */
    private Long startTime;
    private Long endTime;
    private String planName;
    private String empName;
    private String workno;
    private String orgName;
    private String fullPath;
    private String updaterName;

    private Integer empStatus;
    private Long empStyle;
    private Long hireDate;
    private Long terminationDate;

    @Resource
    private IPlanEmpRelRepository planEmpRelRepository;

    public int saveBatch(List<WaPlanEmpRel> list) {
        return planEmpRelRepository.saveBatch(list);
    }

    public void deleteByPlanId(Long id, String belongOrgId) {
        planEmpRelRepository.deleteByPlanId(id, belongOrgId);
    }

    public void deleteByIds(String tenantId, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        planEmpRelRepository.deleteByIds(tenantId, ids);
    }

    public List<WaPlanEmpRel> getByPlanId(Long planId) {

        return planEmpRelRepository.getByPlanId(planId);
    }

    public List<EmpInfoKeyValue> getEmployeesByPlanId(String belongOrgId, Long planId) {

        if (null == planId) {
            return new ArrayList<>();
        }
        return planEmpRelRepository.getEmployeesByPlanId(belongOrgId, planId);
    }

    public List<EmpInfoKeyValue> getEmployeesByPlanIds(String belongOrgId, List<Long> planIds) {

        if (CollectionUtils.isEmpty(planIds)) {
            return new ArrayList<>();
        }
        return planEmpRelRepository.getEmployeesByPlanIds(belongOrgId, planIds);
    }

    public void updatePlanIdByParams(Long planId, Long userId, Long corpId, String belongOrgId, List<Long> empIds) {
        planEmpRelRepository.updatePlanIdByParams(planId, userId, corpId, belongOrgId, empIds);
    }

    public WaPlanEmpRel getPlanEmpRelById(Long id, String belongOrgId) {
        EmpClockPlanRel planEmpRelPo = planEmpRelRepository.getPlanEmpRelById(id, belongOrgId);
        if (null != planEmpRelPo) {
            return ObjectConverter.convert(planEmpRelPo, WaPlanEmpRel.class);
        }
        return null;
    }

    public List<WaPlanEmpRel> getEmpClockPlanByPeriod(Long empId, Long id, String belongOrgId, Long startTime, Long endTime) {
        List<EmpClockPlanRel> list = planEmpRelRepository.getEmpClockPlanByPeriod(empId, id, belongOrgId, startTime, endTime);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(list, WaPlanEmpRel.class);
    }

    public void save(WaPlanEmpRel model) {
        WaPlanEmpRelPo waPlanEmpRelPo = ObjectConverter.convert(model, WaPlanEmpRelPo.class);
        planEmpRelRepository.save(waPlanEmpRelPo);
    }

    public void update(WaPlanEmpRel model) {
        WaPlanEmpRelPo waPlanEmpRelPo = ObjectConverter.convert(model, WaPlanEmpRelPo.class);
        planEmpRelRepository.update(waPlanEmpRelPo);
    }

    public AttendancePageResult<WaPlanEmpRel> getEmpClockPlanList(AttendanceBasePage basePage,
                                                                  String belongOrgId, Long planId, String filter,
                                                                  String effectiveStatus) {
        AttendancePageResult<EmpClockPlanRel> pageResult = planEmpRelRepository.getEmpClockPlanList(basePage, belongOrgId, planId, filter, effectiveStatus);
        return new AttendancePageResult<WaPlanEmpRel>(ObjectConverter.convertList(pageResult.getItems(), WaPlanEmpRel.class), pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    public WaPlanEmpRel getPlanEmpRelByEmpIdAndPeriod(String tenantId, Long empId, Long time) {
        EmpClockPlanRel empClockPlanRel = planEmpRelRepository.getPlanEmpRelByEmpIdAndPeriod(tenantId, empId, time);
        if (null == empClockPlanRel) {
            return null;
        }
        return  ObjectConverter.convert(empClockPlanRel, WaPlanEmpRel.class);
    }

    public List<WaPlanEmpRel> getByIds(String tenantId, List<Long> ids) {
        List<WaPlanEmpRelPo> list = planEmpRelRepository.getByIds(tenantId, ids);
        return ObjectConverter.convertList(list, WaPlanEmpRel.class);
    }
}