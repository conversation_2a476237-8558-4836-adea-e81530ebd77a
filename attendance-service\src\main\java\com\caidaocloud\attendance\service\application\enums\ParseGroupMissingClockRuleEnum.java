package com.caidaocloud.attendance.service.application.enums;

public enum ParseGroupMissingClockRuleEnum {

    ABSENTEEISM(1, "旷工"),
    MISSINGCARD(2, "缺卡");

    private Integer index;

    private String name;

    ParseGroupMissingClockRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ParseGroupMissingClockRuleEnum c : ParseGroupMissingClockRuleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
