package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.ConvertHelper;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.dto.clock.WaClockSiteDto;
import com.caidaocloud.attendance.service.application.service.IClockSiteService;
import com.caidaocloud.attendance.service.domain.entity.WaClockSiteDo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/26
 */
@Service
public class ClockSiteServiceImpl implements IClockSiteService {
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaClockSiteDo waClockSiteDo;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);


    public UserInfo getUserInfo() {
        UserInfo userInfo = sessionService.getUserInfo();
        return userInfo;
    }

    @Override
    public void saveClockSite(WaClockSiteDto clockSiteDto) {
        WaClockSiteDo clockSiteDo = ObjectConverter.convert(clockSiteDto, WaClockSiteDo.class);
        UserInfo userInfo = this.getUserInfo();
        clockSiteDo.setId(snowflakeUtil.createId());
        Long curTime = System.currentTimeMillis();
        clockSiteDo.setCreateTime(curTime);
        clockSiteDo.setCreator(userInfo.getUserId());
        clockSiteDo.setUpdateTime(curTime);
        clockSiteDo.setUpdater(userInfo.getUserId());
        clockSiteDo.setBelongOrgId(userInfo.getTenantId());
        clockSiteDo.setCorpId(ConvertHelper.longConvert(userInfo.getTenantId()));
        waClockSiteDo.save(clockSiteDo);
    }

    @Override
    public void updateClockSite(WaClockSiteDto clockSiteDto) {
        WaClockSiteDo clockSiteDo = ObjectConverter.convert(clockSiteDto, WaClockSiteDo.class);
        UserInfo userInfo = this.getUserInfo();
        clockSiteDo.setUpdateTime(System.currentTimeMillis());
        clockSiteDo.setUpdater(userInfo.getUserId());
        waClockSiteDo.update(clockSiteDo);
    }

    @Override
    public void deleteClockSiteById(Long id) {
        waClockSiteDo.deleteById(id);
    }

    @Override
    public WaClockSiteDto getClockSiteById(Long id) {
        WaClockSiteDo clockSiteDo = waClockSiteDo.getSiteById(id);
        if (clockSiteDo != null) {
            return ObjectConverter.convert(clockSiteDo, WaClockSiteDto.class);
        }
        return null;
    }

    @Override
    public AttendancePageResult<WaClockSiteDto> getClockSitePageList(AttendanceBasePage basePage, Long corpId, String belongOrgId) {
        AttendancePageResult<WaClockSiteDto> dtoPageResult = new AttendancePageResult<>();
        AttendancePageResult<WaClockSiteDo> pageResult = waClockSiteDo.getPageList(basePage, corpId, belongOrgId);
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaClockSiteDo> doList = pageResult.getItems();
            List<WaClockSiteDto> dtoList = ObjectConverter.convertList(doList, WaClockSiteDto.class);
            dtoPageResult.setItems(dtoList);
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setTotal(pageResult.getTotal());
        }
        return dtoPageResult;
    }

    @Override
    public List<WaClockSiteDto> getClockSiteListByIds(List<Long> ids) {
        List<WaClockSiteDo> list = waClockSiteDo.getClockSiteListByIds(ids);
        if (CollectionUtils.isNotEmpty(list)) {
            return ObjectConverter.convertList(list, WaClockSiteDto.class);
        }
        return null;
    }
}
