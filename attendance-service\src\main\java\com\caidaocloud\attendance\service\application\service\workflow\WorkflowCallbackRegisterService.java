package com.caidaocloud.attendance.service.application.service.workflow;

import com.caidaocloud.workflow.annotation.WfCallback;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import org.springframework.stereotype.Service;

/**
 * 工作流回调接口注册
 */
@Service
public class WorkflowCallbackRegisterService {

    @WfCallback(name = "休假审批回调", code = "LEAVE_APPROVAL",
            address = "/api/attendance/workflow/v1/leave",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-LEAVE"})
    public void leave() {
    }

    @WfCallback(name = "批量休假审批回调", code = "BATCH_LEAVE_APPROVAL",
            address = "/api/attendance/workflow/v1/batchLeave",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-BATCH-LEAVE"})
    public void batchLeave() {
    }

    @WfCallback(name = "批量加班审批回调", code = "BATCH_OVERTIME_APPROVAL",
            address = "/api/attendance/workflow/v1/batchOvertime",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-BATCH-OVERTIME"})
    public void batchOvertime() {
    }

    @WfCallback(name = "批量考勤异常申请审批回调", code = "BATCH_ANALYSE_ADJUST_APPROVAL",
            address = "/api/attendance/workflow/v1/batchAnalyseAdjust",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-BATCH-ANALYSE-ADJUST"})
    public void batchAnalyseAdjust() {
    }

    @WfCallback(name = "加班审批回调", code = "OVERTIME_APPROVAL",
            address = "/api/attendance/workflow/v1/overtime",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-OVERTIME"})
    public void overtime() {
    }

    @WfCallback(name = "出差审批回调", code = "TRAVEL_APPROVAL",
            address = "/api/attendance/workflow/v1/travel",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-TRAVEL"})
    public void travel() {
    }

    @WfCallback(name = "批量出差审批回调", code = "BATCH_TRAVEL_APPROVAL",
            address = "/api/attendance/workflow/v1/batchTravel",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-BATCH-TRAVEL"})
    public void batchTravel() {
    }

    @WfCallback(name = "补卡审批回调", code = "CARD_REPLACEMENT_APPROVAL",
            address = "/api/attendance/workflow/v1/register",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-REGISTER"})
    public void bdk() {
    }

    @WfCallback(name = "调班审批回调", code = "SHIFT_APPROVAL",
            address = "/api/attendance/workflow/v1/shiftChange",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-SHIFT"})
    public void shiftChange() {
    }

    @WfCallback(name = "销假审批回调", code = "LEAVE_CANCEL_APPROVAL",
            address = "/api/attendance/workflow/v1/leaveCancel",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-VACATION"})
    public void leaveCancel() {
    }

    @WfCallback(name = "调休付现审批回调", code = "COMPENSATORY_CASE_APPROVAL",
            address = "/api/attendance/workflow/v1/compensatory",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-COMPENSATORY"})
    public void compensatoryToCase() {
    }

    @WfCallback(name = "加班撤销审批回调", code = "OVERTIME_REVOKE_APPROVAL",
            address = "/api/attendance/workflow/v1/overtimeRevoke",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-OVERTIME-REVOKE"})
    public void overtimeRevoke() {
    }

    @WfCallback(name = "加班废止审批回调", code = "OVERTIME_ABOLISH_APPROVAL",
            address = "/api/attendance/workflow/v1/overtimeAbolish",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-OVERTIME-ABOLISH"})
    public void overtimeAbolish() {
    }

    @WfCallback(name = "出差撤销审批回调", code = "TRAVEL_REVOKE_APPROVAL",
            address = "/api/attendance/workflow/v1/travelRevoke",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-TRAVEL-REVOKE"})
    public void travelRevoke() {
    }

    @WfCallback(name = "出差废止审批回调", code = "TRAVEL_ABOLISH_APPROVAL",
            address = "/api/attendance/workflow/v1/travelAbolish",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-TRAVEL-ABOLISH"})
    public void travelAbolish() {
    }

    @WfCallback(name = "假期延期审批回调", code = "LEAVE_EXTENSION_APPROVAL",
            address = "/api/attendance/workflow/v1/leaveExtension",
            serviceId = "caidaocloud-attendance-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"ATTENDANCE-LEAVE-EXTENSION"})
    public void leaveExtension() {
    }
}
