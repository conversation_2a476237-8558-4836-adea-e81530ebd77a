package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.mybatis.model.SysEmpInfoExample;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.org.mybatis.mapper.SysOrgPositionMapper;
import com.caidao1.org.mybatis.model.SysOrgPosition;
import com.caidao1.org.mybatis.model.SysOrgPositionExample;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidaocloud.attendance.service.application.dto.AuthRoleScopeFilterDetail;
import com.caidaocloud.attendance.service.application.dto.ShiftGroupAuthResultDto;
import com.caidaocloud.attendance.service.application.feign.IAuthFeignClient;
import com.caidaocloud.attendance.service.application.service.ICacheCommonService;
import com.caidaocloud.attendance.service.domain.entity.SysEmpInfoDo;
import com.caidaocloud.attendance.service.schedule.domain.entity.WaShiftGroupDo;
import com.caidaocloud.attendance.service.schedule.infrastructure.repository.mapper.WaEmpShiftGroupMapper;
import com.caidaocloud.attendance.service.schedule.infrastructure.repository.po.WaEmpShiftGroup;
import com.caidaocloud.attendance.service.wfm.application.service.OrgDataScopeService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.enums.AuthRoleScopeRestriction;
import com.caidaocloud.hrpaas.metadata.sdk.util.AuthScopeUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import com.jarvis.cache.annotation.Cache;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class CacheCommonServiceImpl implements ICacheCommonService {

    private IAuthFeignClient authFeignClient;
    private SysCorpOrgMapper corpOrgMapper;
    private SysEmpInfoDo sysEmpInfo;
    private SysEmpInfoMapper sysEmpInfoMapper;
    private SysOrgPositionMapper sysOrgPositionMapper;
    private WaEmpShiftGroupMapper waEmpShiftGroupMapper;
    private WaShiftGroupDo waShiftGroupDo;
    private SysEmpInfoDo sysEmpInfoDo;
    private OrgDataScopeService orgDataScopeService;
    private final static Map<String, String> menuCodeMap = new HashMap<String, String>();

    static {
        menuCodeMap.put("/api/attendance/batch/analyseResultAdjust/v1/page", "ATTENDANCE_BATCH_ANALYSEADJUST_LIST");
        menuCodeMap.put("/api/attendance/batch/overtime/v1/page", "ATTENDANCE_BATCH_OVERTIME_LIST");
        menuCodeMap.put("/api/attendance/batch/leave/v1/page", "ATTENDANCE_BATCH_LEAVE_LIST");
        menuCodeMap.put("/api/attendance/batch/travel/v1/list", "ATTENDANCE_BATCH_TRAVEL_LIST");
        menuCodeMap.put("/api/attendance/leaveapply/v1/getLeaveList", "ATTENDANCE_LEAVE_LIST");
        menuCodeMap.put("/api/attendance/leaveCancel/v1/list", "ATTENDANCE_LEAVE_CANCEL_LIST");
        menuCodeMap.put("/api/attendance/overtimeapply/v1/getEmpOtList", "ATTENDANCE_OVERTIME_LIST");
        menuCodeMap.put("/api/attendance/emptravel/v1/list", "ATTENDANCE_TRAVEL_LIST");
        menuCodeMap.put("/api/attendance/register/v1/getOutworkRegisterRecords", "ATTENDANCE_TRAVEL_REGISTER");
        menuCodeMap.put("/api/attendance/register/v1/list", "ATTENDANCE_REGISTER_DAILY");
        menuCodeMap.put("/api/attendance/register/v1/getBdkList", "ATTENDANCE_REGISTER_BDK");
        menuCodeMap.put("/api/attendance/register/v1/getAllList", "ATTENDANCE_REGISTER_ALL");
        menuCodeMap.put("/api/attendance/group/v1/getEmpWaGroupList", "ATTENDANCE_FILE_GROUP");
        menuCodeMap.put("/api/attendance/clockPlan/v1/getEmpClockPlans", "ATTENDANCE_FILE_CLOCK_PLAN");
        menuCodeMap.put("/api/attendance/workcalendar/v1/getCalendarEmpList", "ATTENDANCE_FILE_CALENDAR");
        menuCodeMap.put("/api/attendance/empworkcalendar/v1/searchEmpWorkTimeDetailsByMonth", "ATTENDANCE_SHIFT_LIST");
        menuCodeMap.put("/api/attendance/empworkcalendar/v1/getEmpShiftChangeList", "ATTENDANCE_SHIFT_CHANGE_LIST");
        menuCodeMap.put("/api/attendance/shiftApply/v1/list", "ATTENDANCE_SHIFT_APPLY_LIST");
//        menuCodeMap.put("/api/attendance/statistics/v1/getDayAnalyseList", "ATTENDANCE_DAILY_STATISTIC");
//        menuCodeMap.put("/api/attendance/statistics/v1/searchRegisterStatistics", "ATTENDANCE_MONTH_STATISTIC");
//        menuCodeMap.put("/api/attendance/statistics/v1/searchRegisterStatisticsAdvance", "ATTENDANCE_STATISTICS_DETAIL");
        menuCodeMap.put("/api/attendance/statistics/v1/getDayAnalyseList/dynamic", "ATTENDANCE_DAILY_STATISTIC");
        menuCodeMap.put("/api/attendance/statistics/v1/searchRegisterStatistics/dynamic", "ATTENDANCE_MONTH_STATISTIC");
        menuCodeMap.put("/api/attendance/statistics/v1/searchRegisterStatisticsAdvance/dynamic", "ATTENDANCE_STATISTICS_DETAIL");
        menuCodeMap.put("/api/attendance/quota/v1/getQuotaList", "ATTENDANCE_LEAVE_QUOTA");
        menuCodeMap.put("/api/attendance/annualLeave/v1/list", "ATTENDANCE_ANNUAL_QUOTA");
        menuCodeMap.put("/api/attendance/quota/v1/compensatory/list", "ATTENDANCE_COMPENSATORY_QUOTA");
        menuCodeMap.put("/api/attendance/quota/v1/fix/list", "ATTENDANCE_FIXED_QUOTA");
        menuCodeMap.put("/api/attendance/empCompensatory/v1/list", "ATTENDANCE_COMPENSATORY_APPLY_LIST");
        menuCodeMap.put("/api/attendance/travelCompensatory/v1/list", "ATTENDANCE_TRAVEL_COMPENSATORY_LIST");
        menuCodeMap.put("/api/attendance/statistics/v1/summaryCount", "ATTENDANCE_STATISTICS_SUMMARY");
        menuCodeMap.put("/api/attendance/statistics/v1/summaryWorkList", "ATTENDANCE_STATISTICS_SUMMARY");
        menuCodeMap.put("/api/attendance/statistics/v1/summaryLeaveList", "ATTENDANCE_STATISTICS_SUMMARY");
        menuCodeMap.put("/api/attendance/statistics/v1/summaryOtList", "ATTENDANCE_STATISTICS_SUMMARY");
        menuCodeMap.put("/api/attendance/statistics/v1/summaryTrList", "ATTENDANCE_STATISTICS_SUMMARY");
        menuCodeMap.put("/api/attendance/statistics/v1/summaryStatisticsList", "ATTENDANCE_STATISTICS_SUMMARY");
        menuCodeMap.put("/api/attendance/statistics/v1/summaryWorkRate", "ATTENDANCE_STATISTICS_SUMMARY");
        menuCodeMap.put("/api/attendance/statistics/v1/summaryTimeRate", "ATTENDANCE_STATISTICS_SUMMARY");
        menuCodeMap.put("/api/attendance/statistics/v1/summaryOtRate", "ATTENDANCE_STATISTICS_SUMMARY");
        menuCodeMap.put("/api/attendance/statistics/v1/summaryLtRate", "ATTENDANCE_STATISTICS_SUMMARY");
        //导出
        menuCodeMap.put("/api/attendance/export/v1/exportBatchAnalyseAdjustList", "ATTENDANCE_BATCH_ANALYSEADJUST_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportBatchOvertimeList", "ATTENDANCE_BATCH_OVERTIME_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportBatchLeaveList", "ATTENDANCE_BATCH_LEAVE_LIST");
        menuCodeMap.put("/api/attendance/batch/travel/v1/export", "ATTENDANCE_BATCH_TRAVEL_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportLeaveList", "ATTENDANCE_LEAVE_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportLeaveCancelList", "ATTENDANCE_LEAVE_CANCEL_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportOtList", "ATTENDANCE_OVERTIME_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportTravelList", "ATTENDANCE_TRAVEL_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportOutworkList", "ATTENDANCE_TRAVEL_REGISTER");
        menuCodeMap.put("/api/attendance/export/v1/exportRegisterList", "ATTENDANCE_REGISTER_DAILY");
        menuCodeMap.put("/api/attendance/export/v1/exportBdkList", "ATTENDANCE_REGISTER_BDK");
        menuCodeMap.put("/api/attendance/export/v1/exportAllRegisterList", "ATTENDANCE_REGISTER_ALL");
        menuCodeMap.put("/api/attendance/export/v1/exportEmpGroupList", "ATTENDANCE_FILE_GROUP");
        menuCodeMap.put("/api/attendance/export/v1/exportEmpClockPlanList", "ATTENDANCE_FILE_CLOCK_PLAN");
        menuCodeMap.put("/api/attendance/export/v1/exportEmpShiftList", "ATTENDANCE_FILE_CALENDAR");
        menuCodeMap.put("/api/attendance/empworkcalendar/v1/exportEmpShift", "ATTENDANCE_SHIFT_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportEmpShiftChangeList", "ATTENDANCE_SHIFT_CHANGE_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportDayAnalyseList", "ATTENDANCE_DAILY_STATISTIC");
        menuCodeMap.put("/api/attendance/export/v1/exportRegisterStatistics", "ATTENDANCE_MONTH_STATISTIC");
        menuCodeMap.put("/api/attendance/export/v1/exportDayAnalyseList/Dynamic", "ATTENDANCE_DAILY_STATISTIC");
        menuCodeMap.put("/api/attendance/export/v1/exportRegisterStatistics/dynamic", "ATTENDANCE_MONTH_STATISTIC");
        menuCodeMap.put("/api/attendance/export/v1/exportEmpQuotaList", "ATTENDANCE_LEAVE_QUOTA");
        menuCodeMap.put("/api/attendance/export/v1/exportAnnualQuotaList", "ATTENDANCE_ANNUAL_QUOTA");
        menuCodeMap.put("/api/attendance/export/v1/exportEmpCompensatoryQuotaList", "ATTENDANCE_COMPENSATORY_QUOTA");
        menuCodeMap.put("/api/attendance/export/v1/exportEmpFixQuotaList", "ATTENDANCE_FIXED_QUOTA");
        menuCodeMap.put("/api/attendance/export/v1/exportQuotaList", "ATTENDANCE_LEAVE_QUOTA");
        menuCodeMap.put("/api/attendance/statistics/v1/getDayAnalyseAbnormalList", "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportDayAnalyseAbnormalList", "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST");
        menuCodeMap.put("/api/attendance/export/v1/exportRegisterAdvanceStatistics", "ATTENDANCE_STATISTICS_DETAIL");
        menuCodeMap.put("/api/attendance/export/v1/exportRegisterAdvanceStatistics/dynamic", "ATTENDANCE_STATISTICS_DETAIL");
        //加班撤销记录
        menuCodeMap.put("/api/attendance/workflowRevoke/v1/overtimeRevokeList", "ATTENDANCE_OVERTIME_REVOKE");
        //加班撤销记录导出
        menuCodeMap.put("/api/attendance/export/v1/exportOvertimeRevokeList", "ATTENDANCE_OVERTIME_REVOKE");
        //出差撤销记录
        menuCodeMap.put("/api/attendance/workflowRevoke/v1/travelRevokeList", "ATTENDANCE_TRAVEL_REVOKE");
        //出差撤销记录导出
        menuCodeMap.put("/api/attendance/export/v1/exportTravelRevokeList", "ATTENDANCE_TRAVEL_REVOKE");
        //出差废止记录
        menuCodeMap.put("/api/attendance/workflowRevoke/v1/travelAbolishList", "ATTENDANCE_TRAVEL_ABOLISH");
        //出差废止记录导出
        menuCodeMap.put("/api/attendance/export/v1/exportTravelAbolishList", "ATTENDANCE_TRAVEL_ABOLISH");
        //加班废止记录
        menuCodeMap.put("/api/attendance/workflowRevoke/v1/overtimeAbolishList", "ATTENDANCE_OVERTIME_ABOLISH");
        //加班废止记录导出
        menuCodeMap.put("/api/attendance/export/v1/exportOvertimeAbolishList", "ATTENDANCE_OVERTIME_ABOLISH");
        //假期延期申请记录
        menuCodeMap.put("/api/attendance/leaveExtension/v1/list", "ATTENDANCE_LEAVE_EXTENSION");
        //假期延期申请记录导出
        menuCodeMap.put("/api/attendance/export/v1/exportLeaveExtensionList", "ATTENDANCE_LEAVE_EXTENSION");
        //考勤日历
        menuCodeMap.put("/api/attendance/statistics/v1/getAnalyzeCalendar", "ATTENDANCE_CALENDAR");
        //加班结余时长列表
        menuCodeMap.put("/api/attendance/overtimeapply/v1/getEmpOvertimeLeftDuration", "ATTENDANCE_OT_LEFT_DURATION");
        //导出加班结余时长列表
        menuCodeMap.put("/api/attendance/export/v1/exportEmpOtLeftDurationList", "ATTENDANCE_OT_LEFT_DURATION");

        //工时结算
        menuCodeMap.put("/api/attendance/statistics/whs/v1/getPageList", "ATTENDANCE_WORK_HOUR_SETTLEMENT");
        //导出工时结算
        menuCodeMap.put("/api/attendance/export/v1/exportWorkHourSettlement", "ATTENDANCE_WORK_HOUR_SETTLEMENT");

        //考勤明细，多方案，列表
        menuCodeMap.put("/api/attendance/statistics/v1/searchRegisterStatisticsAdvance/dynamicMultiGroup", "ATTENDANCE_STATISTICS_DETAIL");
        //考勤明细，多方案，导出
        menuCodeMap.put("/api/attendance/export/v1/exportRegisterAdvanceStatistics/dynamicMultiGroup", "ATTENDANCE_STATISTICS_DETAIL");
    }

    @Cache(expire = 300, key = "'attendance-orgScope-mapping-' + #args[0] + '-' + #args[1] + '-' + #args[2]")
    public String getOrgDataScope(String tenantId, Long userId, String requestURI, String alias, Long empId) {
        String dataScope = "";
        if (null == userId) {
            log.warn("userId is empty");
            return " and false ";
        }
        String identifier = null;
        if (StringUtil.isNotBlank(requestURI) && menuCodeMap.containsKey(requestURI)) {
            identifier = menuCodeMap.get(requestURI);
        }
        Result<List<AuthRoleScopeFilterDetail>> result;
        // boolean showDataWhenScopeNotSet = false;// 用户账号在不配置数据范围权限时展示全部数据: true 展示、 false 不展示
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        try {
            securityUserInfo.setTenantId(tenantId);
            securityUserInfo.setUserId(userId);
            securityUserInfo.setEmpId(empId != null ? empId : 0L);
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            result = authFeignClient.getScopeBySubject(AuthScopeUtil.convertAuthScopeIdentifier(identifier), userId);
            // showDataWhenScopeNotSet = orgDataScopeService.getNoAuthModeByTenantId();
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
        if (result.getCode() != 0) {
            log.info("Request for auth service data failed, identifier:{}, subjectId:{}, msg:{}", identifier, userId, result.getMsg());
            return dataScope;
        }
        List<AuthRoleScopeFilterDetail> dataScopes = Optional.ofNullable(result.getData())
                .map(it -> it.stream().filter(o1 -> o1.getRestriction() != AuthRoleScopeRestriction.NO_AUTH).collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
        boolean noAuthMode = Optional.ofNullable(result.getData())
                .map(it -> it.stream().anyMatch(o1 -> o1.getRestriction() == AuthRoleScopeRestriction.NO_AUTH)).orElse(false);
        if (CollectionUtils.isEmpty(dataScopes)) {
            log.info("Request for auth service data result is empty, noAuthMode: {}, result:{}", noAuthMode, JSONUtils.ObjectToJson(dataScopes));
            return noAuthMode ? " AND false " : dataScope;
        }
        log.info("Request for dataScope, result:{}", JSONUtils.ObjectToJson(result));
        //查看本组织及下级组织（MY_ORG_AND_BELONGINGS）、查看指定组织及下级组织（SELECTED_ORG_AND_BELONGINGS）
        List<Long> orgAndBelongIds = Lists.newArrayList();
        //查看指定组织（SELECTED_ORG）、查看本组织（MY_ORG）
        List<Long> orgList = Lists.newArrayList();
        // 查看员工信息数据范围
        List<Long> empIds = Lists.newArrayList();
        //查看指定工作地
        List<Long> workplace = Lists.newArrayList();
        //指定用工类型
        List<Long> employeeTypes = Lists.newArrayList();
        for (AuthRoleScopeFilterDetail dataScopeRow : dataScopes) {
            // 权限类型 枚举 查看本组织及下级组织（MY_ORG_AND_BELONGINGS）、查看本组织（MY_ORG）、查看指定组织（SELECTED_ORG）、
            // 查看指定组织及下级组织（SELECTED_ORG_AND_BELONGINGS）
            Optional<AuthRoleScopeRestriction> dataScopeOpt = Optional.ofNullable(dataScopeRow.getRestriction());
            if (dataScopeOpt.isPresent()) {
                AuthRoleScopeRestriction dataScopeType = dataScopeOpt.get();
                switch (dataScopeType) {
                    case MY_ORG:
                        // orgList.add(getOrgId(tenantId, empId));
                        List<String> myOrgIds = dataScopeRow.getRestriction().toValues(dataScopeRow.getSimpleValues());
                        log.info("Request for {} myorg dataScope, orgid:{}", identifier, FastjsonUtil.toJsonStr(myOrgIds));
                        if (CollectionUtils.isNotEmpty(myOrgIds)) {
                            orgList.addAll(myOrgIds.stream().map(Long::parseLong).collect(Collectors.toList()));
                        }
                        break;
                    case MY_ORG_AND_BELONGINGS:
                        // orgAndBelongIds.add(getOrgId(tenantId, empId));
                        List<String> myAndSubOrgIds = dataScopeRow.getRestriction().toValues(dataScopeRow.getSimpleValues());
                        log.info("Request for {} myandsuborg dataScope, orgid:{}", identifier, FastjsonUtil.toJsonStr(myAndSubOrgIds));
                        if (CollectionUtils.isNotEmpty(myAndSubOrgIds)) {
                            orgList.addAll(myAndSubOrgIds.stream().map(Long::parseLong).collect(Collectors.toList()));
                        }
                        break;
                    case SELECTED_ORG:
                        orgList.addAll(getSimpleValues(dataScopeRow.getSimpleValues()));
                        break;
                    case SELECTED_ORG_AND_BELONGINGS:
                        orgAndBelongIds.addAll(getSimpleValues(dataScopeRow.getSimpleValues()));
                        break;
                    case SELECTED_LEADER:
                        // 按照部门负责人查看
                        empIds.addAll(getLeaderOrgEmpIds(tenantId, empId));
                        break;
                    case DIRECT_SUBORDINATE:
                        // 直接下级
                        empIds.addAll(getEmpIdsByLeader(tenantId, empId));
                        break;
                    case MYSELF:
                        // 查看本人
                        empIds.addAll(getCurrentEmpId(empId));
                        break;
                    case SELECTED_POST:
                        // 指定岗位
                        empIds.addAll(getEmpByPost(tenantId, dataScopeRow.getSimpleValues()));
                        break;
                    case SELECTED_BENCH_POST:
                        // 指定基准岗位
                        empIds.addAll(getEmpByBenchPost(tenantId, dataScopeRow.getSimpleValues()));
                        break;
                    case SELECTED_COST_CENTER:
                        //指定成本中心
                        empIds.addAll(getEmpByCost(tenantId, dataScopeRow.getSimpleValues()));
                        break;
                    case SELECTED_WORKPLACE:
                        //指定工作地
                        workplace.addAll(getSimpleValues(dataScopeRow.getSimpleValues()));
                        break;
                    case SELECTED_HRBP:
                        //指定HRBP
                        orgList.addAll(getOrgIdByHrbp(dataScopeRow));
                        break;
                    case SELECTED_EMP_TYPE:
                        //指定用工类型
                        employeeTypes.addAll(getSimpleValues(dataScopeRow.getSimpleValues()));
                        break;
                    case SELECTED_EMP:
                        //指定员工
                        empIds.addAll(getSimpleValues(dataScopeRow.getSimpleValues()));
                        break;
                    default:
                        break;
                }
            }
        }
        alias = StringUtil.isBlank(alias) ? "" : alias + ".";
        String orgDataScope = "";
        if (CollectionUtils.isNotEmpty(orgList)) {
            orgList = orgList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            orgDataScope += getDataScopeSql(alias, orgList, Boolean.TRUE);
        }
        String orgBelongDataScope = "";
        if (CollectionUtils.isNotEmpty(orgAndBelongIds)) {
            orgAndBelongIds = orgAndBelongIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            orgBelongDataScope += getDataScopeSql(alias, orgAndBelongIds, Boolean.FALSE);
        }
        String empDataScope = "";
        if (CollectionUtils.isNotEmpty(empIds)) {
            empDataScope += String.format("%sempid in (%s)", alias, empIds.stream().map(Objects::toString).distinct().collect(Collectors.joining(",")));
        }
        String workplaceScope = "";
        if (CollectionUtils.isNotEmpty(workplace)) {
            workplaceScope += String.format(" workplace in (%s) ", workplace.stream().map(Objects::toString).distinct().collect(Collectors.joining(",")));
        }
        String employeeTypeScope = "";
        if (CollectionUtils.isNotEmpty(employeeTypes)) {
            workplaceScope += String.format(" employ_type in (%s) ", employeeTypes.stream().map(Objects::toString).distinct().collect(Collectors.joining(",")));
        }
        List<String> scopeList = Lists.newArrayList(orgDataScope, orgBelongDataScope, empDataScope, workplaceScope).stream().filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(scopeList)) {
            return Lists.newArrayList(orgDataScope, orgBelongDataScope, empDataScope, workplaceScope, employeeTypeScope)
                    .stream().filter(StringUtil::isNotEmpty)
                    .collect(Collectors.joining(" OR ", " AND (", ")"));
        } else {
            return noAuthMode ? " AND false " : dataScope;
        }
    }

    public String getShiftGroupDataScope(String tenantId, Long userId, String identifier, Long empId, List<Long> shiftGroupIds) {
        String dataScope = "";
        if (null == userId) {
            log.warn("getShiftGroupDataScope userId is empty");
            return " and false ";
        }
        ShiftGroupAuthResultDto authedEmpShiftGroup = getAuthedEmpShiftGroup(tenantId, userId, identifier, empId);
        if (authedEmpShiftGroup.getCode() != 0) {
//            return authedEmpShiftGroup.isNoAuthMode() ? " AND false " : dataScope;
            return "AND false";
        }
        List<Long> shiftGroup = authedEmpShiftGroup.getShiftGroup();
        boolean noAuthMode = authedEmpShiftGroup.isNoAuthMode();
        boolean showAllDate = authedEmpShiftGroup.isNoAuthMode()
                && OrgDataScopeService.noPermissionConfigured(authedEmpShiftGroup.getAuthRoleScopeFilterDetails(), 2);
        String shiftGroupScope = "";
        if (CollectionUtils.isNotEmpty(shiftGroup)) {
            if (CollectionUtils.isNotEmpty(shiftGroupIds)) {
                String shiftGroupDelimiter = shiftGroup.stream().filter(shiftGroupIds::contains).map(Objects::toString).distinct().collect(Collectors.joining(","));
                if (StringUtil.isNotBlank(shiftGroupDelimiter)) {
                    shiftGroupScope += String.format(" esg.shift_group_id in (%s) ", shiftGroupDelimiter);
                }
            } else {
                shiftGroupScope += String.format(" esg.shift_group_id in (%s) ", shiftGroup.stream().map(Objects::toString).distinct().collect(Collectors.joining(",")));
            }
        }
        List<String> scopeList = Lists.newArrayList(shiftGroupScope).stream().filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(scopeList)) {
            return Lists.newArrayList(shiftGroupScope)
                    .stream().filter(StringUtil::isNotEmpty)
                    .collect(Collectors.joining(" OR ", " AND (", ")"));
        } else {
            return showAllDate ? dataScope : " AND false ";
        }
    }

    @Override
    public List<String> getPerformanceDataScope(String tenantId, Long userId, Long empId, Long periodStartDate, Long periodEndDate) {
        if (null == userId) {
            log.warn("getPerformanceDataScope userId is empty");
            return Lists.newArrayList();
        }
        ShiftGroupAuthResultDto performancePageAuth = getAuthedEmpShiftGroup(tenantId, userId, "PERFORMANCE_LIST", empId);
        log.info("performancePageAuth:{}", performancePageAuth);
        if (performancePageAuth.getCode() != 0) {
            return Lists.newArrayList();
        }
        boolean noAuthModeShowAllData = performancePageAuth.isNoAuthMode();
        if (noAuthModeShowAllData && performancePageAuth.getAuthRoleScopeFilterDetails().size() == 0) {
            return Arrays.asList("all");
        }
        List<Long> shiftGroupList = performancePageAuth.getShiftGroup();
        if (CollectionUtils.isEmpty(shiftGroupList)) {
            return Lists.newArrayList();
        }
        List<String> empList = new ArrayList<>();
        List<Long> empLists = waEmpShiftGroupMapper.selectEmpListByDateSlotAndShiftGroupList(tenantId, periodStartDate, periodEndDate, shiftGroupList);
        if (CollectionUtils.isNotEmpty(empLists)) {
            empList = empLists.stream().map(Objects::toString).collect(Collectors.toList());
        }
        return empList;
    }

    @Cache(expire = 300, key = "'wfm-shiftGroupScope-mapping-' + #args[0] + '-' + #args[1] + '-' + #args[2]")
    public ShiftGroupAuthResultDto getAuthedEmpShiftGroup(String tenantId, Long userId, String identifier, Long empId) {
        ShiftGroupAuthResultDto resultDto = new ShiftGroupAuthResultDto();
        Result<List<AuthRoleScopeFilterDetail>> result;
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        try {
            securityUserInfo.setTenantId(tenantId);
            securityUserInfo.setUserId(userId);
            securityUserInfo.setEmpId(empId != null ? empId : 0L);
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            result = authFeignClient.getScopeBySubject(identifier, userId);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
        log.info("getShiftGroupDataScope Request for auth service data result:{}", JSONUtils.ObjectToJson(result));
        if (result.getCode() != 0) {
            log.info("getShiftGroupDataScope Request for auth service data failed, identifier:{}, subjectId:{}, msg:{}", identifier, userId, result.getMsg());
            resultDto.setCode(-1);
            return resultDto;
        }
        List<AuthRoleScopeFilterDetail> dataScopes = Optional.ofNullable(result.getData())
                .map(it -> it.stream().filter(o1 -> o1.getRestriction() != AuthRoleScopeRestriction.NO_AUTH).collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
//        boolean noAuthMode = Optional.ofNullable(result.getData())
//                .map(it -> it.stream().anyMatch(o1 -> o1.getRestriction() == AuthRoleScopeRestriction.NO_AUTH)).orElse(false);
        boolean noAuthMode = orgDataScopeService.getNoAuthModeByTenantId();
        if (CollectionUtils.isEmpty(dataScopes)) {
            log.info("getShiftGroupDataScope Request for auth service data result is empty, noAuthMode: {}, result:{}", noAuthMode, JSONUtils.ObjectToJson(dataScopes));
            resultDto.setNoAuthMode(noAuthMode);
            resultDto.setAuthRoleScopeFilterDetails(new ArrayList<>());
            return resultDto;
        }
        log.info("getShiftGroupDataScope Request for dataScope, result:{}", JSONUtils.ObjectToJson(result));
        //查看本班组，查看指定班组
        List<Long> shiftGroup = Lists.newArrayList();
        for (AuthRoleScopeFilterDetail dataScopeRow : dataScopes) {
            Optional<AuthRoleScopeRestriction> dataScopeOpt = Optional.ofNullable(dataScopeRow.getRestriction());
            if (dataScopeOpt.isPresent()) {
                AuthRoleScopeRestriction dataScopeType = dataScopeOpt.get();
                switch (dataScopeType) {
                    case MY_SCHEDULE_GROUP:
                        //查看本班组
                        if (empId != null) {
                            shiftGroup.addAll(getEmpShiftGroup(tenantId, empId));
                        }
                        break;
                    case SELECTED_SCHEDULE_GROUP:
                        //查看指定班组
                        shiftGroup.addAll(getSimpleValues(dataScopeRow.getSimpleValues()));
                        break;
                    case MY_SCHEDULE_GROUP_ADMIN:
                        //按班组管理员查看
                        if (Optional.ofNullable(empId).isPresent()) {
                            shiftGroup.addAll(getMyAdminEmpShiftGroup(tenantId, empId));
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        resultDto.setNoAuthMode(noAuthMode);
        resultDto.setShiftGroup(shiftGroup);
        resultDto.setAuthRoleScopeFilterDetails(dataScopes);
        return resultDto;
    }

    private List<Long> getEmpShiftGroup(String tenantId, Long empId) {
        List<WaEmpShiftGroup> empShiftGroups = waEmpShiftGroupMapper.queryEmShiftGroup(tenantId, Collections.singletonList(empId), 2, DateUtil.getCurrentTime(Boolean.TRUE));
        return empShiftGroups.stream().map(WaEmpShiftGroup::getShiftGroupId).distinct().collect(Collectors.toList());
    }

    private List<Long> getMyAdminEmpShiftGroup(String tenantId, Long empId) {
        List<WaEmpShiftGroup> empShiftGroups = waEmpShiftGroupMapper.queryEmShiftGroup(tenantId, null, 2, DateUtil.getCurrentTime(Boolean.TRUE));
        if (CollectionUtils.isEmpty(empShiftGroups)) {
            return Lists.newArrayList();
        }
        List<Long> groupIds = empShiftGroups.stream().map(WaEmpShiftGroup::getShiftGroupId).distinct().collect(Collectors.toList());
        List<WaShiftGroupDo> groups = waShiftGroupDo.getShiftGroups(tenantId, groupIds);
        if (CollectionUtils.isEmpty(groups)) {
            return Lists.newArrayList();
        }
        return groups.stream().filter(group -> StringUtil.isNotBlank(group.getShiftGroupAdmins())
                        && Arrays.stream(group.getShiftGroupAdmins().split(",")).map(Long::parseLong).anyMatch(g -> g.equals(empId)))
                .map(WaShiftGroupDo::getShiftGroupId).distinct().collect(Collectors.toList());
    }

    public List<Long> getOrgIdByHrbp(AuthRoleScopeFilterDetail scopeFilterDetail) {
        List<String> values = scopeFilterDetail.getRestriction().toValues(scopeFilterDetail.getSimpleValues());
        if (CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }
        return values.stream().filter(Objects::nonNull).map(Long::parseLong).collect(Collectors.toList());
    }

    private List<Long> getCurrentEmpId(Long empId) {
        if (null == empId) {
            return Collections.singletonList(-1L);
        }
        return Collections.singletonList(empId);
    }

    private Long getOrgId(String tenantId, Long empId) {
        if (null == empId) {
            return 1L;
        }
        SysEmpInfo empInfo = sysEmpInfo.getEmpInfoById(tenantId, empId);
        if (null == empInfo) {
            return 1L;
        }
        return empInfo.getOrgid();
    }

    private List<Long> getEmpByCost(String tenantId, String simpleValues) {
        if (StringUtil.isBlank(simpleValues)) {
            return Lists.newArrayList();
        }
        String[] costIds = simpleValues.split(",");
        List<Long> empIds = new ArrayList<>();
        for (String costId : costIds) {
            if (StringUtils.isNotEmpty(costId)) {
                empIds.addAll(sysEmpInfo.getEmpIdsByCost(tenantId, Long.valueOf(costId)));
            }
        }
        if (empIds.isEmpty()) {
            empIds.add(-1L);
        }
        return empIds;
    }

    public List<Long> getEmpByBenchPost(String tenantId, String simpleValues) {
        if (StringUtil.isBlank(simpleValues)) {
            return Lists.newArrayList();
        }
        String[] benchPosts = simpleValues.split(",");
        SysOrgPositionExample sysOrgPositionExample = new SysOrgPositionExample();
        sysOrgPositionExample.createCriteria().andBenchmarkPostIn(com.googlecode.totallylazy.Lists.list(benchPosts));
        List<Long> postIds = sysOrgPositionMapper.selectByExample(sysOrgPositionExample).stream().map(SysOrgPosition::getPostId).collect(Collectors.toList());

        SysEmpInfoExample example = new SysEmpInfoExample();
        example.createCriteria().andBelongOrgIdEqualTo(tenantId).andPostIdIn(postIds);
        final List<Long> collect = sysEmpInfoMapper.selectByExample(example).stream().map(SysEmpInfo::getEmpid).collect(Collectors.toList());
        return collect.isEmpty() ? Lists.newArrayList(-1L) : collect;
    }

    private List<Long> getEmpByPost(String tenantId, String simpleValues) {
        if (StringUtil.isBlank(simpleValues)) {
            return Lists.newArrayList();
        }
        String[] postIds = simpleValues.split(",");
        List<Long> collect = Arrays.stream(postIds).map(Long::valueOf).collect(Collectors.toList());
        SysEmpInfoExample example = new SysEmpInfoExample();
        example.createCriteria().andBelongOrgIdEqualTo(tenantId).andPostIdIn(collect);
        List<Long> result = sysEmpInfoMapper.selectByExample(example).stream().map(SysEmpInfo::getEmpid).collect(Collectors.toList());
        return result.isEmpty() ? Lists.newArrayList(-1L) : result;
    }

    private List<Long> getSimpleValues(String simpleValues) {
        if (StringUtil.isBlank(simpleValues)) {
            return Lists.newArrayList();
        }
        String[] orgIds = simpleValues.split(",");
        return Arrays.stream(orgIds).map(Long::valueOf).collect(Collectors.toList());
    }

    /**
     * 获取部门负责人
     *
     * @param tenantId    租户
     * @param leaderEmpId 上级
     */
    private List<Long> getLeaderOrgEmpIds(String tenantId, Long leaderEmpId) {
        List<Long> empIds = sysEmpInfo.getLeaderOrgEmpIds(tenantId, leaderEmpId);
        return CollectionUtils.isNotEmpty(empIds) ? empIds :
                Lists.newArrayList(Sequences.sequence(1L).iterator());
    }

    /**
     * 查看直接下级
     *
     * @param tenantId    租户
     * @param leaderEmpId 上级
     */
    private List<Long> getEmpIdsByLeader(String tenantId, Long leaderEmpId) {
        List<Long> empIds = sysEmpInfo.getEmpIdsByLeader(tenantId, leaderEmpId);
        return CollectionUtils.isNotEmpty(empIds) ? empIds :
                Lists.newArrayList(Sequences.sequence(1L).iterator());
    }

    /**
     * 获取数据权限sql
     *
     * @param alias  别名
     * @param orgIds 组织部门id
     * @return String
     */
    private String getDataScopeSql(String alias, List<Long> orgIds, boolean notIncludeBelongs) {
        String dataScope = "";
        if (CollectionUtils.isEmpty(orgIds)) {
            return dataScope;
        }
//        SysCorpOrgExample corpOrgExample = new SysCorpOrgExample();
//        SysCorpOrgExample.Criteria criteria = corpOrgExample.createCriteria();
//        criteria.andOrgtype2EqualTo(2).andStatusEqualTo(1).andOrgidIn(orgIds);
//        List<SysCorpOrg> orgList = corpOrgMapper.selectByExample(corpOrgExample);
//        if (CollectionUtils.isEmpty(orgList)) {
//            return dataScope;
//        }
//        orgIds = orgList.stream().filter(Objects::nonNull).map(SysCorpOrg::getOrgid).distinct().collect(Collectors.toList());
        if (!notIncludeBelongs) {
            return " " + String.format("%sorgid in (select * from getsuborgstr('{%s}'))", alias, StringUtils.join(orgIds, ",")) + " ";
        } else {
            return " " + String.format("%sorgid in (%s)", alias, StringUtils.join(orgIds, ",")) + " ";
        }
    }

    @Override
    public List<Long> getShiftGroupDataScopeEmpList(String identifier, Long startDate, Long endDate, String keywords, List<Long> shiftGroupIds, UserInfo userInfo) {
        String tenantId = userInfo != null ? userInfo.getTenantId() : SecurityUserUtil.getSecurityUserInfo().getTenantId();
        Long userId = userInfo != null ? userInfo.getUserId() : SecurityUserUtil.getSecurityUserInfo().getUserId();
        Long empId = userInfo != null ? userInfo.getStaffId() : SecurityUserUtil.getSecurityUserInfo().getEmpId();
        String dataFilter = getShiftGroupDataScope(tenantId, userId, identifier, empId, shiftGroupIds);
        return sysEmpInfoDo.getEmpList(tenantId, startDate, endDate, keywords, shiftGroupIds, null, dataFilter);
    }
}
