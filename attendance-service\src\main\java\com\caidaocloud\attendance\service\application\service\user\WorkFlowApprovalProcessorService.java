package com.caidaocloud.attendance.service.application.service.user;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.service.application.cron.ClockTaskService;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordBdkDo;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkFlowApprovalProcessorService implements ScriptBindable {
    @Resource
    private WaRegisterRecordDo waRegisterRecordDoService;
    @Resource
    private ClockTaskService clockTaskService;
    @Resource
    private WaRegisterRecordBdkDo registerRecordBdkDo;

    /**
     * 补打卡审批完后的回调--废弃
     *
     * @param businessKey
     * @param choice
     * @return
     * @throws Exception
     */
    @Deprecated
    public boolean finishBdkApproval(Integer businessKey, String choice) throws Exception {
        log.info("开始执行补打卡回掉WorkFlowApprovalProcessorService.finishBdkApproval");

        Integer recordId = businessKey;
        if (recordId == null) {
            throw new CDException("PK is null");
        }

        if ("yes".equals(choice)) {
            WaRegisterRecordDo originRec = waRegisterRecordDoService.getWaRegisterRecordDoById(recordId);
            if (originRec == null) {
                throw new CDException("DATA is null");
            }
            WaRegisterRecordDo registerRecordUpd = new WaRegisterRecordDo();
            registerRecordUpd.setRecordId(recordId);
            registerRecordUpd.setApprovalStatus(2);
            registerRecordUpd.setLastApprovalTime(DateUtil.getCurrentTime(true));
            registerRecordUpd.setUpdtime(DateUtil.getCurrentTime(true));
            waRegisterRecordDoService.updateByPrimaryKeySelective(registerRecordUpd);
            clockTaskService.analyseRegisterRecordByDate(originRec.getBelongDate(), originRec.getBelongOrgId(), Lists.newArrayList(originRec.getEmpid()));
        } else if ("no".equals(choice)) {
            WaRegisterRecordDo registerRecord = new WaRegisterRecordDo();
            registerRecord.setRecordId(recordId);
            // 审批拒绝
            registerRecord.setApprovalStatus(3);
            registerRecord.setLastApprovalTime(DateUtil.getCurrentTime(true));
            registerRecord.setUpdtime(DateUtil.getCurrentTime(true));
            waRegisterRecordDoService.updateByPrimaryKeySelective(registerRecord);
        } else if ("back".equals(choice)) {
            WaRegisterRecordDo registerRecord = new WaRegisterRecordDo();
            registerRecord.setRecordId(recordId);
            // 已退回
            registerRecord.setApprovalStatus(5);
            registerRecord.setLastApprovalTime(DateUtil.getCurrentTime(true));
            registerRecord.setUpdtime(DateUtil.getCurrentTime(true));
            waRegisterRecordDoService.updateByPrimaryKeySelective(registerRecord);
        } else if ("revoke".equals(choice)) {
            log.info("start revoke registerRecord.....");
            WaRegisterRecordDo originRec = waRegisterRecordDoService.getWaRegisterRecordDoById(recordId);
            if (originRec != null) {
                if (ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(originRec.getApprovalStatus())) {
                    originRec.setApprovalStatus(ApprovalStatusEnum.CANCELLATION.getIndex());
                    waRegisterRecordDoService.updateByPrimaryKeySelective(originRec);
                }
            }
            log.info("end revoke registerRecord.....");
        }
        log.info("执行补打卡回掉WorkFlowApprovalProcessorService.finishBdkApproval完成");
        return true;
    }

    /**
     * 补卡申请审批
     *
     * @param businessKey
     * @param operationEnum
     * @return
     */
    public boolean finishedBdkApproval(Long businessKey, WfCallbackTriggerOperationEnum operationEnum) {
        if (businessKey == null) {
            throw new CDException("PK is null");
        }
        // 补卡申请单据
        WaRegisterRecordBdkDo registerRecordBdk = registerRecordBdkDo.getWaRegisterRecordById(businessKey);
        if (registerRecordBdk == null) {
            throw new CDException("DATA is null");
        }
        WaRegisterRecordBdkDo registerRecordBdkUpd = new WaRegisterRecordBdkDo();
        registerRecordBdkUpd.setRecordId(registerRecordBdk.getRecordId());
        registerRecordBdkUpd.setLastApprovalTime(DateUtil.getCurrentTime(true));
        registerRecordBdkUpd.setUpdtime(DateUtil.getCurrentTime(true));

        // 补卡明细
        List<WaRegisterRecordDo> registers = waRegisterRecordDoService.getWaRegisterRecordByBdkId(registerRecordBdk.getBelongOrgId(), businessKey);
        if (operationEnum == WfCallbackTriggerOperationEnum.APPROVED) {
            registerRecordBdkUpd.setApprovalStatus(ApprovalStatusEnum.PASSED.getIndex());
            for (WaRegisterRecordDo originRec : registers) {
                WaRegisterRecordDo registerRecordUpd = new WaRegisterRecordDo();
                registerRecordUpd.setLastApprovalTime(DateUtil.getCurrentTime(true));
                registerRecordUpd.setRecordId(originRec.getRecordId());
                registerRecordUpd.setApprovalStatus(ApprovalStatusEnum.PASSED.getIndex());
                registerRecordUpd.setUpdtime(DateUtil.getCurrentTime(true));
                waRegisterRecordDoService.updateByPrimaryKeySelective(registerRecordUpd);

            }
            // 补卡分析
            Map<String, List<WaRegisterRecordDo>> bdkMap = registers.stream()
                    .collect(Collectors.groupingBy(o -> String.format("%s_%s", o.getBelongOrgId(), o.getBelongDate())));
            bdkMap.forEach((key, list) -> {
                String[] keys = key.split("_");
                String belongOrgId = keys[0];
                Long belongDate = Long.valueOf(keys[1]);
                List<Long> empIds = list.stream().map(WaRegisterRecordDo::getEmpid).distinct().collect(Collectors.toList());
                clockTaskService.analyseRegisterRecordByDate(belongDate, belongOrgId, empIds);
            });
        } else if (operationEnum == WfCallbackTriggerOperationEnum.REVOKE) {
            registerRecordBdkUpd.setApprovalStatus(ApprovalStatusEnum.REVOKED.getIndex());
            for (WaRegisterRecordDo originRec : registers) {
                if (originRec != null) {
                    if (ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(originRec.getApprovalStatus())) {
                        originRec.setApprovalStatus(ApprovalStatusEnum.REVOKED.getIndex());
                        originRec.setLastApprovalTime(DateUtil.getCurrentTime(true));
                        originRec.setUpdtime(DateUtil.getCurrentTime(true));
                        waRegisterRecordDoService.updateByPrimaryKeySelective(originRec);
                    }
                }
            }
        } else if (operationEnum == WfCallbackTriggerOperationEnum.REFUSED) {
            registerRecordBdkUpd.setApprovalStatus(ApprovalStatusEnum.REJECTED.getIndex());
            for (WaRegisterRecordDo originRec : registers) {
                WaRegisterRecordDo registerRecord = new WaRegisterRecordDo();
                registerRecord.setRecordId(originRec.getRecordId());
                registerRecord.setLastApprovalTime(DateUtil.getCurrentTime(true));
                registerRecord.setUpdtime(DateUtil.getCurrentTime(true));
                registerRecord.setApprovalStatus(ApprovalStatusEnum.REJECTED.getIndex());
                waRegisterRecordDoService.updateByPrimaryKeySelective(registerRecord);
            }
        }
        registerRecordBdkDo.updateRegisterRecordBdk(registerRecordBdkUpd);
        return true;
    }
}