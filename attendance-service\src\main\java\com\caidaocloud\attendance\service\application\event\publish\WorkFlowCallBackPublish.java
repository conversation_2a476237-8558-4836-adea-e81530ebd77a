package com.caidaocloud.attendance.service.application.event.publish;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.attendance.service.application.dto.workflow.WfCallbackResultExtDto;
import com.caidaocloud.attendance.service.application.event.constant.MqConstant;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 工作流回调-消息生产者
 *
 * <AUTHOR>
 * @Date 2024/11/18
 */
@Slf4j
@Service
public class WorkFlowCallBackPublish {
    /**
     * 延迟时间，单位：秒
     */
    @NacosValue("${caidaocloud.attendance.wfcallback.delayTime:3}")
    private int delayTime;

    @Resource
    private MqMessageProducer<WorkFlowCallBackMessage> producer;

    public void publish(WfCallbackResultExtDto messageDto) {
        log.info("WorkFlowCallBackPublish publish message={}", FastjsonUtil.toJsonStr(messageDto));
        WorkFlowCallBackMessage message = new WorkFlowCallBackMessage();
        message.setBody(FastjsonUtil.toJson(messageDto));
        message.setExchange(MqConstant.EXCHANGE_WFCALLBACK);
        message.setRoutingKey(MqConstant.ROUTING_KEY_WFCALLBACK);
        producer.convertAndSend(message, delayTime * 1000);
    }
}
