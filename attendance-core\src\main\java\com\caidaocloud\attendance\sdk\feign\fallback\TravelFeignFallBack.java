package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.sdk.dto.SdkEmpTravelSaveDTO;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeEmpTravelDTO;
import com.caidaocloud.attendance.sdk.feign.ITravelFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

/**
 * 出差申请
 *
 * <AUTHOR>
 * @Date 2023/6/19
 */
@Component
public class TravelFeignFallBack implements ITravelFeignClient {
    @Override
    public Result<?> getTravelTime(SdkEmpTravelSaveDTO dto) {
        return Result.fail();
    }

    @Override
    public Result<?> saveTravelTime(SdkEmpTravelSaveDTO dto) {
        return Result.fail();
    }

    @Override
    public Result<?> revokeEmpTravel(SdkRevokeEmpTravelDTO dto) {
        return Result.fail();
    }
}
