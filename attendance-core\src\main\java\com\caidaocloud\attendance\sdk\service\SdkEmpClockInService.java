package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.sdk.dto.SdkSaveBdkDTO;
import com.caidaocloud.attendance.sdk.feign.IEmpClockInFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 员工打卡
 *
 * <AUTHOR>
 * @Date 2023/6/27
 */
@Slf4j
@Service
public class SdkEmpClockInService {
    @Autowired
    private IEmpClockInFeignClient empClockInFeignClient;

    public Result<?> saveBdkRegister(SdkSaveBdkDTO dto) {
        return empClockInFeignClient.saveBdkRegister(dto);
    }
}
