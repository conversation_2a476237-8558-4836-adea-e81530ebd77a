package com.caidaocloud.attendance.service.application.cron;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.caidao1.ioc.util.SessionHolder;
import com.caidaocloud.attendance.service.application.config.XxlJobClientConfigProperties;
import com.caidaocloud.attendance.service.application.dto.xxljob.ExecutorParam;
import com.caidaocloud.attendance.service.application.dto.xxljob.XxlJobGroup;
import com.caidaocloud.attendance.service.application.dto.xxljob.XxlJobInfo;
import com.caidaocloud.attendance.service.application.dto.xxljob.XxlJobParams;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.httpclient.Cookie;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.PostMethod;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class XxlJobService {
    private static final String CONTENT_TYPE = "application/x-www-form-urlencoded; charset=UTF-8";
    private static OkHttpClient client = new OkHttpClient().newBuilder().build();
    private static HttpClient httpClient = new HttpClient();
    private static ThreadLocal<Integer> JOB_GROUP_LOCAL = new ThreadLocal<>();
    private static ThreadLocal<String> COOKIE_LOCAL = new ThreadLocal<>();

    @NacosValue(value = "${xxl.job.admin.addresses:}", autoRefreshed = true)
    public String adminAddress;
    @NacosValue(value = "${xxl.job.client.username:}", autoRefreshed = true)
    public String username;
    @NacosValue(value = "${xxl.job.client.password:}", autoRefreshed = true)
    public String password;

    private Response saveOrUpdXxlJobRequest(String url, String content) throws Exception {
        MediaType mediaType = MediaType.parse(CONTENT_TYPE);
        RequestBody body = RequestBody.create(mediaType, content);
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("Cookie", getCookie())
                .addHeader("Content-Type", CONTENT_TYPE)
                .build();
        return client.newCall(request).execute();
    }

    public String getCookie() throws Exception {
        log.info("xxlJobAdminAddresses:{}, userName:{} ,password:{}", adminAddress, username, password);
        if (COOKIE_LOCAL.get() == null) {
            HttpMethod postMethod = new PostMethod(XxlJobClientConfigProperties.getLoginUrl(adminAddress, username, password));
            httpClient.executeMethod(postMethod);
            if (postMethod.getStatusCode() == HttpStatus.SC_OK) {
                Cookie[] cookies = httpClient.getState().getCookies();
                StringBuilder tmpCookies = new StringBuilder();
                for (Cookie c : cookies) {
                    tmpCookies.append(c.toString()).append(";");
                }
                COOKIE_LOCAL.set(tmpCookies.toString());
                log.info("xxlJob 获取cookie成功");
            } else {
                log.info("xxlJob 获取cookie失败:{}", postMethod.getStatusCode());
            }
        }
        return COOKIE_LOCAL.get();
    }

    /**
     * 获取考勤定时任务执行器ID
     *
     * @return
     * @throws Exception
     */
    public Integer getAttendanceJobGroupId() throws Exception {
        if (JOB_GROUP_LOCAL.get() == null) {
            Request getJobGroupList = new Request.Builder()
                    .url(XxlJobClientConfigProperties.getJobGroupPageListUrl(adminAddress))
                    .get()
                    .addHeader("Cookie", getCookie())
                    .build();
            Response getJobGroupResponse = client.newCall(getJobGroupList).execute();
            if (getJobGroupResponse.isSuccessful()) {
                List<XxlJobGroup> data = FastjsonUtil.toList(JSONObject.parseObject(getJobGroupResponse.body().string()).get("data").toString(), XxlJobGroup.class);
                log.info("获取xxlJob执行器信息:{}", FastjsonUtil.toJson(data));
                Integer jobGroup = data.stream().filter(xxlJobGroup -> xxlJobGroup.getAppname().equals("caidaocloud-attendance-service")).findFirst().get().getId();
                JOB_GROUP_LOCAL.set(jobGroup);
            } else {
                log.error("获取xxlJob考勤定时任务执行器失败");
            }
        }
        return JOB_GROUP_LOCAL.get();
    }


    /**
     * 获取xxlJobId 以及xxlJob任务参数 根据任务参数中的数据接入ID关联
     *
     * @param sysDataInputId
     * @return
     * @throws Exception
     */
    public ExecutorParam getXxlJobIdAndParamsBySysDataInputId(Integer sysDataInputId) throws Exception {
        log.info("根据数据接入ID获取xxlJob定时任务信息 sysDataInputId:{}", sysDataInputId);
        Request getJobInfoList = new Request.Builder()
                .url(XxlJobClientConfigProperties.getJobInfoPageListUrl(adminAddress, getAttendanceJobGroupId(), "startDataInputJobHandler"))
                .get()
                .addHeader("Cookie", getCookie())
                .build();
        Response getJobInfoListResponse = client.newCall(getJobInfoList).execute();
        if (getJobInfoListResponse.isSuccessful()) {
            List<XxlJobInfo> data = FastjsonUtil.toList(JSONObject.parseObject(getJobInfoListResponse.body().string()).get("data").toString(), XxlJobInfo.class);
            log.info("根据数据接入ID获取xxlJob定时任务信息result:{}", FastjsonUtil.toJson(data));
            for (XxlJobInfo xxlJobInfo : data) {
                ExecutorParam executorParam = FastjsonUtil.toObject(xxlJobInfo.getExecutorParam(), ExecutorParam.class);
                if (sysDataInputId.toString().equals(executorParam.getSysDataInputId().toString())) {
                    executorParam.setXxlJobId(xxlJobInfo.getId());
                    return executorParam;
                }
            }
        }
        return null;
    }


    /**
     * 更新或者新增 xxlJob信息 据数据接入ID
     *
     * @param sysDataInput
     * @throws Exception
     */
    public void addOrUpdJobInfoBySysDataInput(SysDataInput sysDataInput, Boolean add) throws Exception {
        log.info("新增或更新xxlJob信息 sysDataInput:{}", FastjsonUtil.toJson(sysDataInput));
        String content = null;
        String url = null;
        String belongOrgId = SessionHolder.getBelongOrgId();
        if (add) {
            ExecutorParam executorParam = new ExecutorParam();
            executorParam.setBelongId(belongOrgId);
            executorParam.setCorpId(SessionHolder.getCorpId());
            executorParam.setSysDataInputId(sysDataInput.getSysDataInputId());
            url = XxlJobClientConfigProperties.getJobInfoAddUrl(adminAddress);
            content = XxlJobClientConfigProperties.getJobInfoAddParams(
                    getAttendanceJobGroupId(),
                    sysDataInput.getNote() + belongOrgId,
                    sysDataInput.getTrigger(),
                    "startDataInputJobHandler",
                    FastjsonUtil.toJsonStr(executorParam));
            log.info("新增xxlJob JobExecutorParam:{}", FastjsonUtil.toJson(executorParam));
        } else {
            ExecutorParam executorParam = getXxlJobIdAndParamsBySysDataInputId(sysDataInput.getSysDataInputId());
            if (executorParam == null) {
                log.info("sysDataInput isOldJob notXxlJob return:{}", sysDataInput.getSysDataInputId());
                return;
            }
            log.info("修改xxlJob jobExecutorParam:{}", FastjsonUtil.toJson(executorParam));
            url = XxlJobClientConfigProperties.getJobInfoUpdateUrl(adminAddress);
            content = XxlJobClientConfigProperties.getJobInfoUpdParams(
                    executorParam.getXxlJobId(),
                    getAttendanceJobGroupId(),
                    sysDataInput.getNote() + belongOrgId,
                    sysDataInput.getTrigger(),
                    "startDataInputJobHandler",
                    FastjsonUtil.toJsonStr(executorParam));
        }

        Response response = saveOrUpdXxlJobRequest(url, content);
        if (response.isSuccessful()) {
            log.info("addOrUpdJobInfo success:{}", response.body().string());
        } else {
            log.error("addOrUpdJobInfo 失败");
        }
    }


    /**
     * 启动任务根据数据接入ID
     */
    public void startJobBySysDataInputId(Integer sysDataInputId) throws Exception {
        ExecutorParam executorParam = getXxlJobIdAndParamsBySysDataInputId(sysDataInputId);
        if (executorParam == null) {
            log.warn("startXxlJobFail but xxlJobId getNull dataInputId:{}", sysDataInputId);
            return;
        }
        Request startJob = new Request.Builder()
                .url(XxlJobClientConfigProperties.getJobInfoStartJobUrl(adminAddress, executorParam.getXxlJobId()))
                .get()
                .addHeader("Cookie", getCookie())
                .build();
        Response startJobResponse = client.newCall(startJob).execute();
        if (startJobResponse.isSuccessful()) {
            log.info("startXxlJobFail sysDataInputId:{} id:{} result:{}", sysDataInputId, executorParam.getXxlJobId(), startJobResponse.body().string());
        } else {
            log.error("根据接入id:{} 启动XxlJob任务失败", sysDataInputId);
        }
    }

    /**
     * 启动任务根据xxlJobId
     */
    public Boolean startJobByXxlJobId(Integer xxlJobId) throws Exception {
        Request startJob = new Request.Builder()
                .url(XxlJobClientConfigProperties.getJobInfoStartJobUrl(adminAddress, xxlJobId))
                .get()
                .addHeader("Cookie", getCookie())
                .build();
        Response startJobResponse = client.newCall(startJob).execute();
        if (startJobResponse.isSuccessful()) {
            log.info("startXxlJobFail xxlJobId:{}  result:{}", xxlJobId, startJobResponse.body().string());
            return true;
        } else {
            log.error("根据接入id:{} 启动XxlJob任务失败", xxlJobId);
        }
        return false;
    }

    /**
     * 停止任务根据数据接入ID
     */
    public void stopJobBySysDataInputId(Integer sysDataInputId) throws Exception {
        ExecutorParam executorParam = getXxlJobIdAndParamsBySysDataInputId(sysDataInputId);
        if (executorParam == null) {
            log.warn("stopXxlJobFail but xxlJobId getNull dataInputId:{}", sysDataInputId);
            return;
        }
        Request stopJob = new Request.Builder()
                .url(XxlJobClientConfigProperties.getJobInfoStopJobUrl(adminAddress, executorParam.getXxlJobId()))
                .get()
                .addHeader("Cookie", getCookie())
                .build();
        Response stopJobResponse = client.newCall(stopJob).execute();
        if (stopJobResponse.isSuccessful()) {
            log.info("stopXxlJobFail sysDataInputId:{} id:{} result:{}", sysDataInputId, executorParam.getXxlJobId(), stopJobResponse.body().string());
        } else {
            log.error("根据接入id:{}停止XxlJob任务失败", sysDataInputId);
        }
    }

    /**
     * 停止任务根据xxlJobId
     */
    public Boolean stopJobByXxlJob(Integer xxlJobId) throws Exception {
        Request stopJob = new Request.Builder()
                .url(XxlJobClientConfigProperties.getJobInfoStopJobUrl(adminAddress, xxlJobId))
                .get()
                .addHeader("Cookie", getCookie())
                .build();
        Response stopJobResponse = client.newCall(stopJob).execute();
        if (stopJobResponse.isSuccessful()) {
            log.info("stopXxlJobFail sysDataInputId:{} result:{}", xxlJobId, Objects.requireNonNull(stopJobResponse.body()).string());
            return true;
        } else {
            log.error("根据xxlJobId:{} 停止XxlJob任务失败", xxlJobId);
        }
        return false;
    }

    /**
     * 删除任务根据数据接入ID
     */
    public void deleteJobBySysDataInputId(Integer sysDataInputId) throws Exception {
        ExecutorParam executorParam = getXxlJobIdAndParamsBySysDataInputId(sysDataInputId);
        if (executorParam == null) {
            log.warn("deleteXxlJobFail but xxlJobId getNull dataInputId:{}", sysDataInputId);
            return;
        }
        Request stopJob = new Request.Builder()
                .url(XxlJobClientConfigProperties.getJobInfoDeleteUrl(adminAddress, executorParam.getXxlJobId()))
                .get()
                .addHeader("Cookie", getCookie())
                .build();
        Response stopJobResponse = client.newCall(stopJob).execute();
        if (stopJobResponse.isSuccessful()) {
            log.info("删除XxlJob sysDataInputId:{} id:{} result:{}", sysDataInputId, executorParam.getXxlJobId(), stopJobResponse.body().string());
        } else {
            log.error("根据接入id:{} 删除XxlJob任务失败", sysDataInputId);
        }
    }

    /**
     * 删除任务根据xxlJobId
     */
    public Boolean deleteJobByXxlJobId(Integer xxlJobId) throws Exception {
        Request stopJob = new Request.Builder()
                .url(XxlJobClientConfigProperties.getJobInfoDeleteUrl(adminAddress, xxlJobId))
                .get()
                .addHeader("Cookie", getCookie())
                .build();
        Response stopJobResponse = client.newCall(stopJob).execute();
        if (stopJobResponse.isSuccessful()) {
            log.info("删除XxlJob xxlJobId:{}  result:{}", xxlJobId, stopJobResponse.body().string());
            return true;
        } else {
            log.error("根据xxlJobId:{} 删除XxlJob任务失败", xxlJobId);
        }
        return false;
    }

    public Boolean deleteAttendanceXxlJob(String tenantId, String jobDesc, String xxlJobHandler) throws Exception {
        // 根据任务描述获取任务,多租户配置任务的描述必须添加belongId用于区分任务多租户
        String jobDescStr = jobDesc + tenantId;
        Optional<XxlJobInfo> job = getAttendanceXxlJobInfoByJobDesc(jobDescStr, xxlJobHandler);
        if (job != null && job.isPresent()) {
            XxlJobInfo xxlJobInfo = job.get();
            if (null != xxlJobInfo.getId()) {
                deleteJobByXxlJobId(xxlJobInfo.getId());
            }
        }
        return false;
    }

    /**
     * 根据考勤定时任务描述获取xxlJob信息
     *
     * @param jobDesc
     * @return
     * @throws Exception
     */
    public Optional<XxlJobInfo> getAttendanceXxlJobInfoByJobDesc(String jobDesc, String executorHandler) throws Exception {
        log.info("根据考勤定时任务名称获取定时任务信息 jobName:{}", jobDesc);
        Request getJobInfoList = new Request.Builder()
                .url(XxlJobClientConfigProperties.getJobInfoPageListUrl(adminAddress, getAttendanceJobGroupId(), executorHandler))
                .get()
                .addHeader("Cookie", getCookie())
                .build();
        Response getJobInfoListResponse = client.newCall(getJobInfoList).execute();
        if (getJobInfoListResponse.isSuccessful()) {
            List<XxlJobInfo> data = FastjsonUtil.toList(JSONObject.parseObject(getJobInfoListResponse.body().string()).get("data").toString(), XxlJobInfo.class);
            log.info("根据考勤定时任务名称获取定时任务信息 jobName:{} result:{}", jobDesc, FastjsonUtil.toJson(data));
            return data.stream().filter(xxlJobInfo -> xxlJobInfo.getJobDesc().equals(jobDesc)).findFirst();
        }
        return null;
    }

    /**
     * 新增考勤定时任务
     *
     * @param jobDesc       任务描述
     * @param corn          任务corn时间
     * @param xxlJobHandler 任务实际执行handler
     * @param xxlJobParams  任务参数
     */
    public Boolean addAttendanceXxlJob(String jobDesc, String corn, String xxlJobHandler, XxlJobParams xxlJobParams, boolean autoStart) throws Exception {
        String url = XxlJobClientConfigProperties.getJobInfoAddUrl(adminAddress);
        String content = XxlJobClientConfigProperties.getJobInfoAddParams(
                getAttendanceJobGroupId(),
                jobDesc + xxlJobParams.getBelongOrgId(),
                corn,
                xxlJobHandler,
                FastjsonUtil.toJsonStr(xxlJobParams));
        log.info("新增xxlJob url:{} content:{}", url, content);
        Response response = saveOrUpdXxlJobRequest(url, content);
        if (response.isSuccessful()) {
            log.info("addAttendanceXxlJob success:{}", response.body().string());
            // 获取 xxlJobId
            Optional<XxlJobInfo> attendanceXxlJobInfoByJobDesc = getAttendanceXxlJobInfoByJobDesc(jobDesc + xxlJobParams.getBelongOrgId(), xxlJobHandler);
            if (null != attendanceXxlJobInfoByJobDesc && attendanceXxlJobInfoByJobDesc.isPresent()) {
                if (autoStart) {
                    Boolean startSuccess = startJobByXxlJobId(attendanceXxlJobInfoByJobDesc.get().getId());
                    if (!startSuccess) {
                        throw new ServerException(String.format("xxlJob %s 自动新增后自动启动失败", jobDesc));
                    }
                }
            } else {
                throw new ServerException(String.format("xxlJob %s 新增后根据任务描述获取任务失败", jobDesc));
            }
            return true;
        } else {
            log.error("addAttendanceXxlJob 失败");
        }
        return false;
    }

    /**
     * 更新考勤定时任务
     *
     * @param jobDesc       任务描述 !!!!!!!!!!!!!!!!!!!!! jobDesc一定不能修改不然无法匹配到任务
     * @param corn          任务corn时间
     * @param xxlJobHandler 任务实际执行handler
     * @param xxlJobParams  任务参数
     */
    public Boolean addOrUpdateAttendanceXxlJob(String jobDesc, String corn, String xxlJobHandler, XxlJobParams xxlJobParams, boolean autoStart) throws Exception {
        // 根据任务描述获取任务          多租户配置任务的描述必须添加belongId用于区分任务多租户
        String jobDescStr = jobDesc + xxlJobParams.getBelongOrgId();
        Optional<XxlJobInfo> job = getAttendanceXxlJobInfoByJobDesc(jobDescStr, xxlJobHandler);
        if (job != null && job.isPresent()) {
            XxlJobInfo xxlJobInfo = job.get();
            // 修改基本上就是修改 corn 时间
            String url = XxlJobClientConfigProperties.getJobInfoUpdateUrl(adminAddress);
            String content = XxlJobClientConfigProperties.getJobInfoUpdParams(
                    xxlJobInfo.getId(),
                    getAttendanceJobGroupId(),
                    jobDescStr,
                    corn,
                    xxlJobHandler,
                    FastjsonUtil.toJsonStr(xxlJobParams));
            Response response = saveOrUpdXxlJobRequest(url, content);
            if (response.isSuccessful() && autoStart) {
                log.info("updateAttendanceXxlJob success:{}", response.body().string());
                // 更新corn时间后后 自动停止 ->然后启动 不然corn时间不生效
                Boolean stopJobByXxlJobSuccess = stopJobByXxlJob(xxlJobInfo.getId());
                if (!stopJobByXxlJobSuccess) {
                    throw new ServerException("更新任务后停止任务失败");
                }
                Boolean startJobByXxlJobSuccess = startJobByXxlJobId(xxlJobInfo.getId());
                if (!startJobByXxlJobSuccess) {
                    throw new ServerException("更新任务后停止任务再启动任务失败");
                }
            } else {
                log.error("updateAttendanceXxlJob 失败");
                return false;
            }
        } else {
            log.info("xxlJob 不存在自动新增:{}", jobDescStr);
            // 新增
            addAttendanceXxlJob(jobDesc, corn, xxlJobHandler, xxlJobParams, autoStart);
        }
        return false;
    }

    /**
     * 校验是否存在xxljob任务
     * @param jobDesc 任务名称
     * @param xxlJobParams 任务参数
     * @return
     * @throws Exception
     */
    public boolean checkXxlJobExisted(String jobDesc, XxlJobParams xxlJobParams, String executorHandler) throws Exception {
        String jobDescStr = jobDesc + xxlJobParams.getBelongOrgId();
        Optional<XxlJobInfo> job = getAttendanceXxlJobInfoByJobDesc(jobDescStr, executorHandler);
        return job != null && job.isPresent();
    }
}
