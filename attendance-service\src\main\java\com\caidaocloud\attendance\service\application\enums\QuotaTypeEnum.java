package com.caidaocloud.attendance.service.application.enums;

/**
 * 假期类型-额度类型
 */
public enum QuotaTypeEnum {
    ISSUED_ANNUALLY(1, "按年发放"),
    COMPENSATORY_LEAVE(2, "调休"),
    FIXED_QUOTA(3, "固定额度"),
    PARENTAL_LEAVE(4, "育儿假");

    private Integer index;
    private String name;

    QuotaTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (QuotaTypeEnum c : QuotaTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
