package com.caidaocloud.attendance.core.wa.service;

import com.caidao1.commons.cache.RedisCache;
import com.caidao1.wa.mybatis.mapper.WaStoreTimeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WaStoreTimeService {

    @Autowired
    private WaStoreTimeMapper waStoreTimeMapper;

    /**
     * @param empid
     * @param searchMonth 如果查询20160127。则这里为201601
     * @param searchDay   如果查询20160127。则这里为27
     * @return
     */
    @RedisCache(expire = 3600)
    public Map getStorePb(Long empid, Integer searchMonth, Integer searchDay) {
        Map mapParm = new HashMap();
        mapParm.put("empid", empid);
        mapParm.put("searchMonth", searchMonth);
        mapParm.put("searchDay", searchDay);
        List<Map> list = waStoreTimeMapper.getStorePb(mapParm);
        if (list != null && list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }
}
