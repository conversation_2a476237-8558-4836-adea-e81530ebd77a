package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.core.wa.dto.MyLeaveTimeBaseDto;
import com.caidaocloud.attendance.sdk.dto.SdkLeaveApplySaveDTO;
import com.caidaocloud.attendance.sdk.dto.SdkMaternityLeaveRange;
import com.caidaocloud.attendance.sdk.feign.ILeaveFeignClient;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 休假申请
 *
 * <AUTHOR>
 * @Date 2023/6/19
 */
@Slf4j
@Service
public class SdkLeaveService {
    @Autowired
    private ILeaveFeignClient leaveFeignClient;

    /**
     * 申请休假时，计算休假时长
     *
     * @param leaveApplySaveDto
     * @return
     */
    public Result<?> getLeaveTotalTime(SdkLeaveApplySaveDTO leaveApplySaveDto) {
        return leaveFeignClient.getLeaveTotalTime(leaveApplySaveDto);
    }

    /**
     * 申请休假
     *
     * @param leaveApplySaveDto
     */
    public Result<?> saveLeaveApply(SdkLeaveApplySaveDTO leaveApplySaveDto) {
        return leaveFeignClient.saveLeaveApply(leaveApplySaveDto);
    }

    /**
     * 申请休假时，计算休假时长
     *
     * @param leaveApplySaveDto
     * @return
     */
    public Result<?> getUserLeaveTotalTime(SdkLeaveApplySaveDTO leaveApplySaveDto) {
        return leaveFeignClient.getUserLeaveTotalTime(leaveApplySaveDto);
    }

    /**
     * 申请休假时，获取产假可选时间范围
     *
     * @param maternityLeaveRange
     * @return
     */
    public Result<?> getMaternityLeaveRange(SdkMaternityLeaveRange maternityLeaveRange) {
        return leaveFeignClient.getMaternityLeaveRange(maternityLeaveRange);
    }

    /**
     * 我的休假信息
     *
     * @param date
     * @param empId
     * @return
     */
    public List<MyLeaveTimeBaseDto> getMyLeaveList(Long date, Long empId) {
        Result<List<MyLeaveTimeBaseDto>> result = leaveFeignClient.listMyEmpLeave(date, empId);
        if (null == result || !result.isSuccess()) {
            return Lists.newArrayList();
        }
        return result.getData();
    }
}
