package com.caidao1.employee.service;

import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidao1.employee.mybatis.mapper.EmpInfoMapper;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
public class EmployeeService {
    @Autowired
    private EmpInfoMapper empInfoMapper;
    @Autowired
    private WaAttendanceConfigService waConfigService;

    /**
     * 查询员工司龄
     *
     * @param belongId
     * @param empid
     * @param corpAgeSql
     * @return
     */
    public Float getEmpCorpAge(String belongId, Long empid, String corpAgeSql) {
        if (StringUtils.isEmpty(corpAgeSql)) {
            corpAgeSql = getCorpAgeFuncSql(belongId);
        }
        BigDecimal corpAge = empInfoMapper.getEmpCorpAgeByFuncSql(empid, belongId, corpAgeSql);
        if (corpAge != null) {
            return corpAge.floatValue();
        }
        return null;
    }

    /**
     * 员工信息司龄计算逻辑
     *
     * @param belongId
     * @return
     */
    public String getCorpAgeFuncSql(String belongId) {
        String corpAgeSql = waConfigService.getFuncExpForNoCache(belongId, "corp_age");
        if (StringUtils.isEmpty(corpAgeSql)) {
            corpAgeSql = "extract(year from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))+cast(extract(month from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/12  as  decimal(10, 1))";
        }
        try {
            corpAgeSql = waConfigService.initExp(corpAgeSql);
            corpAgeSql = corpAgeSql.replaceAll("#\\{nowTime\\}", DateUtil.getOnlyDate().toString()).replaceAll("#nowTime#", DateUtil.getOnlyDate().toString()).replaceAll("#\\{nowYearEndTime\\}", DateUtilExt.getYearsEndTime(DateUtil.getOnlyDate()).toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return corpAgeSql;
    }

}
