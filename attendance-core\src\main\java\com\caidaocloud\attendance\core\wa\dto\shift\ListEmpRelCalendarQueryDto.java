package com.caidaocloud.attendance.core.wa.dto.shift;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询员工所匹配的工作日历
 *
 * <AUTHOR>
 * @Date 2025/2/14
 */
@Data
@ApiModel("查询员工所匹配的工作日历")
public class ListEmpRelCalendarQueryDto {
    @ApiModelProperty("租户ID")
    private String belongOrgid;
    @ApiModelProperty("员工ID集合，每次最多查询500个员工")
    private List<Long> empids;
    @ApiModelProperty("考勤类型：1 固定班次，2 排班制，3 自由打卡")
    private Integer worktimeType;
    @ApiModelProperty("开始日期")
    private Long startDate;
    @ApiModelProperty("结束日期")
    private Long endDate;
}
