package com.caidaocloud.attendance.core.wa.dto;

import java.util.Map;

public class MobileEmployeeInfoBean {
	private String empid;
	private Integer orgid;
	private String onboard_date; //入职日期
	private String probation; //试用期
	private String positive_date;
	//--Approach:"社会招聘", //入司途径 目前不需要
	private String employ_entity; //用工单位
	private String employ_type;//用工类型
	private String work_status; //用工状态
	private String work_stats_code;
	private String work_city; //工作城市
	private String department; //所在部门
	private String position_name; //职位
	private String first_work_date;//首次参加工作时间
	private String social_work_age;//社会工龄
	private String work_age;//本司工龄
	private Map lead;
	
	
	public Integer getOrgid() {
		return orgid;
	}
	public void setOrgid(Integer orgid) {
		this.orgid = orgid;
	}
	public String getEmpid() {
		return empid;
	}
	public void setEmpid(String empid) {
		this.empid = empid;
	}
	public String getWork_stats_code() {
		return work_stats_code;
	}
	public void setWork_stats_code(String work_stats_code) {
		this.work_stats_code = work_stats_code;
	}
	public String getOnboard_date() {
		return onboard_date;
	}
	public void setOnboard_date(String onboard_date) {
		this.onboard_date = onboard_date;
	}
	public String getProbation() {
		return probation;
	}
	public void setProbation(String probation) {
		this.probation = probation;
	}
	public String getPositive_date() {
		return positive_date;
	}
	public void setPositive_date(String positive_date) {
		this.positive_date = positive_date;
	}
	public String getEmploy_entity() {
		return employ_entity;
	}
	public void setEmploy_entity(String employ_entity) {
		this.employ_entity = employ_entity;
	}
	public String getEmploy_type() {
		return employ_type;
	}
	public void setEmploy_type(String employ_type) {
		this.employ_type = employ_type;
	}
	public String getWork_status() {
		return work_status;
	}
	public void setWork_status(String work_status) {
		this.work_status = work_status;
	}
	public String getWork_city() {
		return work_city;
	}
	public void setWork_city(String work_city) {
		this.work_city = work_city;
	}
	public String getDepartment() {
		return department;
	}
	public void setDepartment(String department) {
		this.department = department;
	}
	public String getPosition_name() {
		return position_name;
	}
	public void setPosition_name(String position_name) {
		this.position_name = position_name;
	}
	public String getFirst_work_date() {
		return first_work_date;
	}
	public void setFirst_work_date(String first_work_date) {
		this.first_work_date = first_work_date;
	}
	public String getSocial_work_age() {
		return social_work_age;
	}
	public void setSocial_work_age(String social_work_age) {
		this.social_work_age = social_work_age;
	}
	public String getWork_age() {
		return work_age;
	}
	public void setWork_age(String work_age) {
		this.work_age = work_age;
	}
	public Map getLead() {
		return lead;
	}
	public void setLead(Map lead) {
		this.lead = lead;
	}
}
