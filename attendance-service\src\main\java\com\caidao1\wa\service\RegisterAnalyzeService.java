package com.caidao1.wa.service;

import com.alibaba.fastjson.JSONObject;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.mybatis.model.SysEmpInfoExample;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.utils.*;
import com.caidao1.ext.entity.SysDynamicColumns;
import com.caidao1.ext.service.ISysDynamicColumnsService;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.system.mybatis.model.SysCorpOrgExample;
import com.caidao1.system.mybatis.model.SysUnitCity;
import com.caidao1.wa.enums.WaOtValidTimeCalTypeEnum;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.auth.service.WaAuthPubService;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.dto.*;
import com.caidaocloud.attendance.core.wa.service.RemoteSmartWorkTimeService;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mchange.lang.IntegerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RegisterAnalyzeService {
    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        //允许没有定义的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //允许使用未带引号的字段名
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //允许使用单引号
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
    }

    @Autowired
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Autowired
    private WaAnalyzeMapper waAnalyzeMapper;
    @Autowired
    private WaWorktimeMapper waWorktimeMapper;
    @Autowired
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private WaEmpOvertimeMapper waEmpOvertimeMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaParseGroupMapper waParseGroupMapper;
    @Autowired
    private WaSobMapper waSobMapper;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private ISysDynamicColumnsService iSysDynamicColumnsService;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaShiftCustomizedMapper waShiftCustomizedMapper;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private RemoteSmartWorkTimeService remoteSmartWorkTimeService;
    @Autowired
    private RemoteImportService importService;
    @Value("${project.cus.identity:caidaocloud}")
    private String projectCusIdentity;
    @Autowired
    private WaAuthPubService waAuthPubService;
    @Autowired
    private WaOvertimeTypeMapper waOvertimeTypeMapper;

    /**
     * 考勤分析-休假分析新逻辑开启配置：true 开启 false 不开启
     */
    @Value("${caidaocloud.data.ltanalyze:false}")
    private boolean ltanalyze;

    public static String formatDuring(long mss) {
        long days = mss / (1000 * 60 * 60 * 24);
        long hours = (mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
        long minutes = (mss % (1000 * 60 * 60)) / (1000 * 60);
        long seconds = (mss % (1000 * 60)) / 1000;
        return days + " days " + hours + " hours " + minutes + " minutes "
                + seconds + " seconds ";
    }

    @Transactional
    public List<Long> asyncRegister(String belongid, Long startDate, Long endDate, Long[] empids, Integer wa_sob_id) throws Exception {
        // 分析签到签退数据
        long send = endDate;
        Long currentDate = endDate;
        if (currentDate == null) {
            currentDate = DateUtil.getOnlyDate(new Date());
        }
        if (endDate != null) {
            int m = (23 * 60 * 60) + (59 * 60) + 59;
            endDate += m;
        }
        Integer row = 0;
        if (startDate == null || endDate == null) {
            startDate = DateUtil.addDate(DateUtil.getOnlyDate(new Date()) * 1000, -1);
            endDate = startDate;
            if (endDate != null) {
                int m = (23 * 60 * 60) + (59 * 60) + 59;
                endDate += m;
            }
        }
        Boolean isDefault = false;
        Integer waGroupId = 0;
        Long sobEndDate = null;
        List<Long> empGroupEmpids = null;
        if (wa_sob_id != null) {
            WaSob waSob = waSobMapper.selectByPrimaryKey(wa_sob_id);

            if (waSob.getStatus() != null && waSob.getStatus() == 1) {
                throw new CDException("已封存的考勤账套不能再次核算");
            }
            if (startDate < waSob.getStartDate() || (send > waSob.getEndDate())) {
                String normalSatrt = DateUtil.getDateStrByTimesamp(waSob.getStartDate());
                String normalEnd = DateUtil.getDateStrByTimesamp(waSob.getEndDate());
                throw new CDException("核算周期时间范围应在考勤周期内（" + normalSatrt + "~" + normalEnd + "）");
            }
            // 2.判断当前的考勤分组是否是默认的
            WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waSob.getWaGroupId());
            waGroupId = waGroup.getWaGroupId();
            if (waGroup.getIsDefault() != null) {
                isDefault = waGroup.getIsDefault();
            }
            sobEndDate = waSob.getSobEndDate();
            WaParseGroup parseGroup = waParseGroupMapper.selectByPrimaryKey(waGroup.getParseGroupId());
            // 1 按人分批分析 2 按门店和非门店分类
            Integer tmType2 = 1;
            // 获取指定员工分组的数据
            empGroupEmpids = sysEmpInfoMapper.getEmpidByTmTypeAndEmpGroup(tmType2, belongid, currentDate, isDefault, waGroupId, startDate, endDate);
            if (empids != null && empids.length > 0) {
                List<Long> selectEmpids = new ArrayList<>();
                for (Long id : empids) {
                    if (empGroupEmpids.contains(id)) {
                        selectEmpids.add(id);
                    }
                }
                empGroupEmpids.clear();
                empGroupEmpids.addAll(selectEmpids);
            }
            // 如果启用了按帐套区间来控制加班，请假时间是否落再当天，默认是落在审批日期上
            String key = RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongid + "_LT_OT_ON_THE_EVENT_DATE";
            String isIgnore = CDCacheUtil.getValue(key);
            if (StringUtils.isBlank(isIgnore) || "1".equals(isIgnore)) {
                waCommonService.waGroupAnaly(waGroupId, startDate, endDate, sobEndDate);
            }
            row = analyzeStep1(belongid, startDate, endDate, tmType2, empGroupEmpids, parseGroup, waSob);
        } else {
            row = analysisWaRegisterStep(belongid, empids, startDate, endDate, null, null, null);
        }
        log.info("=====================Accounting completed=============rowCount={}", row);
        return empGroupEmpids;
    }

    public String getAsyncRegisterDataFilter(String belongid, Long curEmpId, Integer pageId) {
        String defaultSql = "belong_org_id='" + belongid + "'";
        if (curEmpId != null) {
            defaultSql = "empid = any (select getTeamEmpid('{" + curEmpId + "}'))";
        }
        return waAuthPubService.getDataScope(pageId, "e", defaultSql);
    }


    @Deprecated
    public int analyzeStep1(String belongid, Long startDate, Long endDate, Integer tmType, List<Long> empidList, WaParseGroup parseGroup, WaSob waSob) throws Exception {
        Integer row = 0;
        if (CollectionUtils.isNotEmpty(empidList)) {
            int len = 650;
            int size = empidList.size();
            int num = size / len;
            if (size % len != 0) {
                num++;
            }
            log.info("===tmType=" + tmType + " 分" + num + "次执行");
            int fromIndex = 0;
            for (int i = 1; i <= num; i++) {
                int toIndex = i * len;
                if (toIndex > size) {
                    toIndex = size;
                }
                Long[] empidArrs = empidList.subList(fromIndex, toIndex).toArray(new Long[]{});
                fromIndex = toIndex;
                row += analysisWaRegisterStep(belongid, empidArrs, startDate, endDate, tmType, parseGroup, waSob);
                log.info("===执行到第" + i + "批完成");
            }
        }
        return row;
    }

    public List<Map> getEmpLeaveList(Map<String, Object> map) {
        //跨夜班休假自动归属到前一天
        String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + map.get("belongid") + "_KY_LT_AUTOMATICALLY_ATTRIBUTED_TO_PREVIOUSDAY");
        if (StringUtils.isBlank(isOpen)) {
            isOpen = "1";
        }
        if ("1".equals(isOpen)) {
            Map<String, Object> params = new HashMap<>();
            params.putAll(map);
            Long endDate = (Long) params.get("endDate");
            params.put("endDate", endDate + 86400);
            return waEmpLeaveMapper.getEmpLeaveByEmpid(params);
        } else {
            return waEmpLeaveMapper.getEmpLeaveByEmpid(map);
        }
    }

    @Deprecated
    public Integer analysisWaRegisterStep(String belongid, Long[] empids, Long startDate, Long endDate, Integer tmType, WaParseGroup parseGroup, WaSob waSob) throws Exception {
        /**
         * 考勤逻辑：
         * 第一步：取出每个人的签到签退数据（签到取最早的，签退记录取最晚的）
         * 第二步：组合签到签退记录  当没有签到签退记录时，走判断下面的判断逻辑
         * 第三步：查询每个人所属的考勤分组及对应的考勤分析分组
         */
        Integer row = 0;
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("belongid", belongid);
        paramsMap.put("startDate", startDate);
        paramsMap.put("endDate", endDate);
        if (parseGroup != null) {
            Boolean ignoreLocationExp = parseGroup.getIgnoreLocationExp();
            paramsMap.put("ignoreLocationExp", ignoreLocationExp);// 是否忽略地点异常的签到签退数据
            paramsMap.put("registerMiss", parseGroup.getRegisterMiss());
            paramsMap.put("clockType", parseGroup.getClockType());
        } else {
            paramsMap.put("registerMiss", 2);// 默认当旷工
            paramsMap.put("clockType", 2);//默认二次卡
        }
        if (empids != null && empids.length > 0) {
            paramsMap.put("anyEmpids2", "'{" + StringUtils.join(empids, ",") + "}'");
        }
        Integer ymdstart = Integer.valueOf(DateUtil.parseDateToPattern(new Date(startDate * 1000), "yyyyMMdd"));
        Integer ymstart = Integer.valueOf(ymdstart.toString().substring(0, 6));
        paramsMap.put("ymstart", ymstart);
        paramsMap.put("ymdstart", ymdstart);

        Integer ymdend = Integer.valueOf(DateUtil.parseDateToPattern(new Date(endDate * 1000), "yyyyMMdd"));
        Integer ymend = Integer.valueOf(ymdend.toString().substring(0, 6));
        paramsMap.put("ymend", ymend);
        paramsMap.put("ymdend", ymdend);
        paramsMap.put("crtuser", 0);
        paramsMap.put("crttime", DateUtil.getCurrentTime(true));
        log.info("查询条件：" + paramsMap);
        paramsMap.put("registerType", 1);
        // 1.查询最早到签到纪录
        List<Map> singInList = waRegisterRecordMapper.searchRegAnalyseList(paramsMap);
        if (log.isInfoEnabled()) {
            log.info(String.valueOf("查询到的签到数据：" + singInList != null ? singInList.size() : 0));
        }
        // 2.查询最晚的签退纪录
        paramsMap.put("registerType", 2);
        List<Map> singOffList = waRegisterRecordMapper.searchRegAnalyseList(paramsMap);
        log.info(String.valueOf(singInList.size()));
        // 3.查询一次卡的签到纪录
        paramsMap.put("registerType", 4);
        List<Map> oneSignList = waRegisterRecordMapper.searchRegAnalyseList(paramsMap);
        log.info(String.valueOf(oneSignList.size()));
        //查询外勤签到数据
        paramsMap.put("registerType", 3);
        List<Map> outRegList = waRegisterRecordMapper.getOutWorkerRegisterList(paramsMap);
        Map<String, List<WaRegisterRecord>> outRegMap = genEmpOutRegMap(outRegList);
        Map<Long, Object> empidMaps = new HashMap<>();
        if (empids != null && empids.length > 0) {
            for (Long empid : empids) {
                empidMaps.put(empid, null);
            }
        }
        //3. 生成按时间+empid作为key的Map value 为签到签退数据
        Map<String, WaRegisterRecord> singIns = generatRegMap(singInList, empidMaps);
        Map<String, WaRegisterRecord> singOffs = generatRegMap(singOffList, empidMaps);
        Map<String, WaRegisterRecord> oneSignIns = generatRegMap(oneSignList, empidMaps);
        //后期嵌入流程  确定员工所属的考勤分组以及考勤的默认分组 根据考勤分组取得考勤分析分组
        if (empidMaps.size() > 0) {
            String anyEmpid2 = "'{" + StringUtils.join(empidMaps.keySet(), ",").concat("}'");// empidMaps.keySet().toString().replace("[", "'{").replace("]", "}'");
            paramsMap.put("anyEmpids", anyEmpid2);
        } else {
            paramsMap.remove("anyEmpids");
        }
        //4  获取考勤信息和考勤分析分组信息
        WaAnalyzDTO dto = getWaAnalyzInfo(paramsMap);
        // 判断是否为一次卡或不打卡的逻辑
        Optional<WaAnalyzInfo> optional = dto.getEmpAnalyzInfos().stream().findFirst();
        if (optional.isPresent()) {
            WaAnalyzInfo analyzeInfo = optional.get();
            if (analyzeInfo.getClock_type() == 1 || analyzeInfo.getClock_type() == 3) {
                // 取第一条打卡记录
                List<Map> regRecords = waRegisterRecordMapper.searchRegList(paramsMap);
                // 取最后一条打卡记录
                paramsMap.put("isLast", true);
                regRecords.addAll(waRegisterRecordMapper.searchRegList(paramsMap));
                // 根据empid+"_"+belongDate为key进行分组
                Map<String, List<Map>> regRecordMap = regRecords.stream().distinct().collect(Collectors.groupingBy(m -> String.format("%s_%s", m.get("empid"), m.get("belong_date"))));
                dto.setRegisterRecordMap(regRecordMap);
            }
        }
        dto.setWaSob(waSob);

        paramsMap.put("leaveTypeIsNotAnalyze", false);//不分析出差类型的单据

        // 如果按弹性工作时间进行分析，先取出请假数据进行分析
        if (parseGroup != null && BooleanUtils.isTrue(parseGroup.getIsFlexibleWorking())) {
            List<Map> empLeaveList = getEmpLeaveList(paramsMap);
            dto.setEmpLeaveInfo(getEmpLeaveFilterList(dto, empLeaveList));
        }
        dto.setBelongid(belongid);
        // 5.组合签到签退纪录 计算签到签退 迟到  早退，工作小时，异常描述，签到签退ID
        //不打卡方案时不用分析签到签退
        List<WaAnalyze> resultWa = new ArrayList<>();
        Object clockType = paramsMap.get("clockType");
        if (clockType != null) {
            if (Integer.parseInt(clockType.toString()) == 1) {
                resultWa = analyzeOneCardResult(oneSignIns, dto, paramsMap);
            } else if (Integer.parseInt(clockType.toString()) == 2) {
                resultWa = analyzeResult(singIns, singOffs, oneSignIns, dto, paramsMap);
            }
        } else {
            resultWa = analyzeResult(singIns, singOffs, oneSignIns, dto, paramsMap);
        }
        dto.setWaAnalyzeList(resultWa);

        dto.setEmpOutRegMap(outRegMap);

//		CLOUD-2178 考勤分析中把请假，加班分析逻辑独立出来 20180804
        analyzeLeaveOtData(paramsMap, dto, resultWa, belongid);

        for (int i = resultWa.size() - 1; i >= 0; i--) {
            WaAnalyze d = resultWa.get(i);
            if (!(d.getBelongDate() >= startDate && d.getBelongDate() <= endDate)) {
                resultWa.remove(i);
            }
            empidMaps.put(d.getEmpid(), d.getShiftDefId());
        }
        if (empidMaps.size() > 0) {
            String anyEmpid2 = empidMaps.keySet().toString().replace("[", "'{").replace("]", "}'");
            paramsMap.put("anyEmpids", anyEmpid2);
        } else {
            paramsMap.remove("anyEmpids");
        }

        convertLeaveTimeToInteger(resultWa);

        // 8.保存分析纪录到db，如果分析的数据已经存在则当更新处理 要指定 belongOrgId 创建时间，创建人
        saveOrUpdateAnalyze(resultWa, belongid, 0L, paramsMap, dto);

        //9. 旷工的话生成旷工纪录 空纪录 生成的纪录只能生成当前日期前
        dto.setClockType((Integer) clockType);
        paramsMap.put("empIdList", new ArrayList<>(Arrays.asList(empids)));
        absentEmpRecord(paramsMap, startDate, endDate, dto);

        //10 加入未排班但有签到记录的处理；
        saveNoShiftRecord(paramsMap);

        //11 插入核算区间内 没有记录的数据，以空记录显示出来
        long currentime = DateUtil.getCurrentTime(true);
        if (startDate <= currentime) {
            StringBuffer dateRangeStr = new StringBuffer(startDate + "");
            for (long d = startDate; d <= endDate; ) {
                d = DateUtil.addDate(d * 1000, 1);
                if (d > currentime) break;
                if (dateRangeStr.toString().length() > 0) dateRangeStr.append(",");
                dateRangeStr.append(d);
            }
            paramsMap.put("dateRange", dateRangeStr);
            insertEmptyAnalyzeRecord(paramsMap);
        }

        //12、同步实际加班时长
        Map<String, Integer> relOtTimeDurationMap = dto.getRelOtTimeDurationMap();
        if (MapUtils.isNotEmpty(relOtTimeDurationMap)) {
            List<WaEmpOvertimeDetail> overtimeDetails = new ArrayList<>();
            relOtTimeDurationMap.forEach((k, v) -> {
                String ks[] = k.split("_");
                if (ks.length > 1) {
                    WaEmpOvertimeDetail detail = new WaEmpOvertimeDetail();
                    detail.setDetailId(Integer.valueOf(ks[1]));
                    detail.setRelTimeDuration(v.floatValue());
                    overtimeDetails.add(detail);
                }
            });

            importService.fastUpdList(WaEmpOvertimeDetail.class, "detailId", overtimeDetails);
        }

        //开启出差联动外勤签到，更新没有外勤签到或者地点异常请假数据为失效数据，并且加上失效原因
        if (CollectionUtils.isNotEmpty(dto.getInvalidLeaves())) {
            importService.fastUpdList(WaEmpLeave.class, "leaveId", dto.getInvalidLeaves());
        }

        //开启出差联动外勤签到，初始化失效单据为有效数据
        List<EmpLeaveInfo> leaveInfos = dto.getEmpLeaveInfoList();
        if (CollectionUtils.isNotEmpty(leaveInfos)) {
            List<Integer> leaveIds = new ArrayList<>();
            for (EmpLeaveInfo leaveInfo : leaveInfos) {
                if (leaveInfo.getLeave_type() == 11) {
                    leaveIds.add(leaveInfo.getLeave_id());
                }
            }
            if (CollectionUtils.isNotEmpty(leaveInfos)) {
                List<List<Integer>> lists = ListTool.split(leaveIds, 500);
                lists.forEach(list -> {
                    Map params = new HashMap();
                    params.put("anyLeaveIds", "'{" + StringUtils.join(list, ",") + "}'");
                    waMapper.updateLeaveInvalidStatus(params);
                });
            }
        }
        row = resultWa.size();
        return row;
    }

    /**
     * 请假时长数据类型转换
     *
     * @param resultWa
     */
    public void convertLeaveTimeToInteger(List<WaAnalyze> resultWa) {
        if (CollectionUtils.isNotEmpty(resultWa)) {
            for (WaAnalyze analyze : resultWa) {
                Map<String, Object> ltJsonMap = (Map<String, Object>) analyze.getLevelColumnJsonb();
                if (MapUtils.isNotEmpty(ltJsonMap)) {
                    Map<String, Object> map = new HashMap<>();
                    ltJsonMap.forEach((k, v) -> {
                        map.put(k, v);
                        if (!k.contains("_name")) {
                            if (v != null) {
                                Float time = Float.valueOf(v.toString());
                                map.put(k, time.intValue());
                            }
                        }
                    });
                    analyze.setLevelColumnJsonb(map);
                }

                Map<String, Object> originLtJsonMap = (Map<String, Object>) analyze.getOriginLevelColumnJsonb();
                if (MapUtils.isNotEmpty(originLtJsonMap)) {
                    Map<String, Object> map = new HashMap<>();
                    originLtJsonMap.forEach((k, v) -> {
                        map.put(k, v);
                        if (!k.contains("_name")) {
                            if (v != null) {
                                Float time = Float.valueOf(v.toString());
                                map.put(k, time.intValue());
                            }
                        }
                    });
                    analyze.setOriginLevelColumnJsonb(map);
                }
            }
        }
    }

    @Transactional
    public void insertEmptyAnalyzeRecord(Map<String, Object> paramsMap) {
        Integer row = waAnalyzeMapper.insertEmptyAnalyzeRecord(paramsMap);
        log.info("插入了 {} 条空记录", row);
    }

    /**
     * @param paramsMap 需要传递的参数： startDate，endDate ，belongid，[anyEmpids2（员工empid)]
     * @param dto
     * @param resultWa
     */
    public void analyzeLeaveOtData(Map<String, Object> paramsMap, WaAnalyzDTO dto, List<WaAnalyze> resultWa, String belongid) throws Exception {
        // 6.计算请假小时数  班次ID --以及冲抵迟到早退小时数
        analyzeLeaveData(resultWa, paramsMap, dto);
        // 7.计算加班小时数  班次ID --以及冲抵迟到早退小时数
        analyzeOverTimeData(resultWa, paramsMap, dto);
    }

    /**
     * 自动插入没有对应班次的记录
     *
     * @param paramsMap
     */
    @Transactional
    public void saveNoShiftRecord(Map<String, Object> paramsMap) {
        Integer row = waAnalyzeMapper.saveNoShiftRecord(paramsMap);
        log.info("插入了" + row + "条未设置班次的记录");
    }

    /**
     * 分析加班数据
     *
     * @param resultWa
     * @param params
     * @param dto
     */
    public void analyzeOverTimeData(List<WaAnalyze> resultWa, Map<String, Object> params, WaAnalyzDTO dto) throws Exception {
        if (dto == null) {
            dto = new WaAnalyzDTO();
        }
        String belongid = ConvertHelper.stringConvert(params.get("belongid"));
        // 根据empid去查询某天的排班纪录，统计加班小时数 如果有加班并且启用了按加班来分析，则需要判断加班数据是否有效
        if (CollectionUtils.isNotEmpty(resultWa)) {
            // 查询 加班单据
            List<Map> empOverlist = waEmpOvertimeMapper.getEmpOverTimeByEmpid(params);
            // 合并单据 避免跨夜时计算不到的情况
            Map<String, List<EmpOverInfo>> empOverAfterMaps = new HashMap<>();
            Map<String, List<EmpOverInfo>> overListMaps = getEmpOvers(empOverlist, empOverAfterMaps);
            // 计算加班小时数
            Map<String, Object> orginOtMap;
            for (WaAnalyze wa : resultWa) {
                String key = wa.getEmpid() + "_" + wa.getBelongDate();
                if (overListMaps.containsKey(key)) {
                    WaAnalyzInfo analyzInfo = dto.getEmpWaAnalyz(wa.getEmpid(), wa.getBelongDate());

                    analyzInfo.setOtMaps(dto.getOtMaps());

                    EmpShiftInfo empShiftDef = this.getEmpShiftDefByInfo(wa.getEmpid(), wa.getShiftDefId(), wa.getBelongDate(), dto);

                    List<EmpOverInfo> ofs = overListMaps.get(key);

                    orginOtMap = getOtColumnJsonB(ofs, analyzInfo, wa, params, empShiftDef, dto);
                    // 原加班单据时长
                    wa.setOriginOtColumnJsonb(orginOtMap);
                    //加班联动分析
                    Map<String, Object> overMap = processEmpOver(wa, analyzInfo, overListMaps, belongid, empShiftDef, dto);
                    if (overMap.size() == 0) {
                        wa.setOtColumnJsob(orginOtMap);
                    } else {
                        // 经分析后的加班单时长
                        wa.setOtColumnJsob(overMap);
                    }
                    overListMaps.remove(key);
                }
            }
            // 加班单分析，分两步执行 1 审批日期在申请日期之前的加班单 2 审批日期在申请日期之后的加班单
            // 剩余的加班单 存储加班的纪录
            overtimeAnalyze(resultWa, dto, overListMaps, params);
            //追加 加班记录 即：审批时间大于申请时间的记录 追加到审批时间上
            overtimeAnalyze(resultWa, dto, empOverAfterMaps, params);
        } else {
            // 当没有签到签退数据时，记录加班数据
            List<Map> empOverlist = waEmpOvertimeMapper.getEmpOverTimeByEmpid(params);
            Map<String, List<EmpOverInfo>> empOverAfterMaps = new HashMap<>();
            Map<String, List<EmpOverInfo>> overListMaps = getEmpOvers(empOverlist, empOverAfterMaps);
            // 加班单分析，分两步执行 1 审批日期在申请日期之前的加班单 2 审批日期在申请日期之后的加班单
            overtimeAnalyze(resultWa, dto, overListMaps, params);
            overtimeAnalyze(resultWa, dto, empOverAfterMaps, params);
        }
    }

    private void overtimeAnalyze(List<WaAnalyze> resultWa, WaAnalyzDTO dto, Map<String, List<EmpOverInfo>> empOverMaps, Map<String, Object> params) throws Exception {
        if (empOverMaps != null && empOverMaps.size() > 0) {
            WaAnalyze analyze = null;
            for (Map.Entry<String, List<EmpOverInfo>> entry : empOverMaps.entrySet()) {
                String key = entry.getKey();
                Long empid = Long.valueOf(key.split("_")[0]);
                Long overDate = Long.valueOf(key.split("_")[1]);
                WaAnalyzInfo analyzInfo = dto.getEmpWaAnalyz(empid, overDate);

                analyzInfo.setOtMaps(dto.getOtMaps());
                // 查找是否已有存在的分析数据
                analyze = existsWaAnalyz(resultWa, empid, overDate);

                if (analyze != null) {
                    EmpShiftInfo empShiftDef = this.getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);

                    Map<String, Object> otJsonbMap = getOtColumnJsonB(entry.getValue(), analyzInfo, analyze, params, empShiftDef, dto);

                    Object otJsonb = mergeOtMap(analyze, otJsonbMap);
                    // 保存原始加班单数据
                    analyze.setOriginOtColumnJsonb(otJsonb);
                    // 保存分析后的加班数据
                    analyze.setOtColumnJsob(otJsonb);
                    if (empShiftDef != null && analyze.getWorkTime() == null)
                        analyze.setWorkTime(empShiftDef.getWorkTotalTime());
                } else {
                    analyze = new WaAnalyze();
                    analyze.setEmpid(empid);
                    analyze.setBelongDate(overDate);
                    EmpShiftInfo empShiftDef = this.getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);

                    Map otJsonb = getOtColumnJsonB(entry.getValue(), analyzInfo, analyze, params, empShiftDef, dto);
                    // 保存原始加班单数据
                    analyze.setOriginOtColumnJsonb(otJsonb);
                    if (empShiftDef != null && (empShiftDef.getDateType() == 1 || empShiftDef.getDateType() == 4)) {
                        analyze.setWorkTime(empShiftDef.getWorkTotalTime());
                        analyze.setShiftDefId(empShiftDef.getShiftDefId());
                        // add 20180706  场景：没有签到签退，请假记录，有加班单的情况，记录是否旷工
                        WaAnalyzInfo analyzeConfig = dto.getEmpWaAnalyz(empid, analyze.getBelongDate());
                        if (analyzeConfig.getRegister_miss() != null && analyzeConfig.getRegister_miss() == 2) {
                            if (empShiftDef.getWorkTotalTime() != null) {
                                analyze.setIsKg(empShiftDef.getWorkTotalTime() > 0 ? 1 : 0);
                            }
                            analyze.setKgWorkTime(empShiftDef.getWorkTotalTime());
                        }
                        if (analyzeConfig.getClock_type() == 3) {
                            analyze.setActualWorkTime(empShiftDef.getWorkTotalTime().floatValue());
                        }
                    }
                    analyze.setOtColumnJsob(otJsonb);
                    resultWa.add(analyze);
                }
            }
        }
    }

    private Object mergeOtMap(WaAnalyze an, Map<String, Object> otJsonbMap) throws Exception {
        Map<String, Object> originMap = null;
        if (an.getOtColumnJsob() == null) {
            return otJsonbMap;
        } else {
            if (otJsonbMap == null) {
                return an.getOtColumnJsob();
            }

            Object obj = an.getOtColumnJsob();
            if (obj instanceof Map) {
                originMap = (Map) obj;
            } else if (obj instanceof PGobject) {
                PGobject pg = (PGobject) obj;
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                String json = pg.getValue();
                try {
//					json = objectMapper.writeValueAsString(pg.getValue());
                    originMap = objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {
                    });

                } catch (Exception e) {
                    e.printStackTrace();
                    throw e;
                }
            }
            if (originMap == null) {
                originMap = new HashMap<String, Object>();
            }
            // 把加班记录合并原先的map里
            for (Map.Entry<String, Object> entry : otJsonbMap.entrySet()) {
                String key = entry.getKey();

                if (key.contains("key") && originMap.containsKey(key)) {
                    Integer f = (Integer) originMap.get(key);
                    if (f == null) {
                        f = 0;
                    }
                    Integer value = (Integer) entry.getValue();
                    if (value == null) {
                        value = 0;
                    }
                    originMap.put(key, value + f);
                } else {
                    originMap.put(key, entry.getValue());
                }
            }
        }
        return originMap;
    }

    /**
     * 加班时长分析计算
     *
     * @param of
     * @param waAnalyze
     * @param analyzInfo
     * @param overListMaps
     * @param belongid
     * @param empShiftDef
     * @param dto
     * @param otMap
     * @param relOtTimeDurationMap
     * @param legalHolidaysOtRule
     * @param overtimeType
     */
    public void parseOtTime(EmpOverInfo of, WaAnalyze waAnalyze, WaAnalyzInfo analyzInfo, Map<String, List<EmpOverInfo>> overListMaps,
                            String belongid, EmpShiftInfo empShiftDef, WaAnalyzDTO dto, Map<String, Object> otMap, Map<String, Integer> relOtTimeDurationMap,
                            Integer legalHolidaysOtRule, WaOvertimeType overtimeType) {
        String ot_key = waAnalyze.getEmpid() + "_" + waAnalyze.getBelongDate();
        of.setBelongid(belongid);
        analyzInfo = dto.getEmpWaAnalyz(of.getEmpid(), of.getRegdate());
        if (analyzInfo != null && analyzInfo.getOtMaps() == null) {
            analyzInfo.setOtMaps(dto.getOtMaps());
        }
        waAnalyze = getEmpAnalyze(belongid, of.getEmpid(), of.getBelongDate(), dto);
        processOt2(waAnalyze, overListMaps, otMap, ot_key, of, analyzInfo, empShiftDef, dto, relOtTimeDurationMap, legalHolidaysOtRule, overtimeType);
    }

    /**
     * 1.判断是否启用了加班联动分析
     * 2.没有启用联动分析的情况,直接记录加班小时数
     * 记录加班小时数分为5类，
     * 增加工作日加班付现小时数
     * 增加工作日加班调休小时数
     * 增加休息日加班付现小时数
     * 增加休息日加班调休小时数
     * 法定节假日加班小时数
     */
    private Map<String, Object> processEmpOver(WaAnalyze waAnalyze, WaAnalyzInfo analyzInfo, Map<String, List<EmpOverInfo>> overListMaps,
                                               String belongid, EmpShiftInfo empShiftDef, WaAnalyzDTO dto) throws Exception {
        Map<String, Object> otMap = new HashMap<>();

        if (analyzInfo == null) {
            return otMap;
        }

        Map<String, Integer> relOtTimeDurationMap = dto.getRelOtTimeDurationMap();
        if (relOtTimeDurationMap == null) {
            relOtTimeDurationMap = new HashMap<>();
        }

        // 如果 1.启用加班联动分析 则需要判断 加班单是否有效 2.并判断加班单是否跨夜了，
        //跨夜的情况处理加班小时数，如果加班单上的加班小时小于了实际加班区间的小时数以加班单上的小时为准，反之则以实际小时为准
        String ot_key = waAnalyze.getEmpid() + "_" + waAnalyze.getBelongDate();
        List<EmpOverInfo> ovlist = overListMaps.get(ot_key);
        if (CollectionUtils.isEmpty(ovlist)) {
            return otMap;
        }

        if (BooleanUtils.isTrue(analyzInfo.getOt_sum_parse())) {
            //开启加班单日汇总分析
            //根据考勤周期拆分加班单据，每个考勤周期的加班单据单独分析，最后分析结果合并在一起
            //判读逻辑：事件日期<考勤开始日期 && 实际加班日期在本次核算范围内（帐套开始日期<=审批日期<=帐套截止日期）
            //考勤开始日期：如果考勤帐套有值，取考勤帐套开始日期，反之：根据日期+考勤分组去反推考勤开始日期
            List<EmpOverInfo> otherPeriodOtList = new ArrayList<>();//其他考勤周期的加班单据（eg:事件日期是上月&审批日期是本月）
            List<EmpOverInfo> currentPeriodOtList = this.getOtListByCycle(ovlist, otherPeriodOtList, analyzInfo, dto.getWaSob());//当前计算周期的加班单据

            //当前计算周期的加班单据分析
            if (CollectionUtils.isNotEmpty(currentPeriodOtList)) {
                Map<String, Object> currentPeriodOtMap = new HashMap<>();
                for (EmpOverInfo of : currentPeriodOtList) {
                    of.setBelongid(belongid);
                    //1、工作日，2休息日，3法定假日,4特殊日期
                    Integer date_type = of.getDate_type();
                    if (date_type == null) {
                        continue;
                    }

                    //查询加班分析规则
                    Map<Integer, WaOvertimeType> otRuleMap = getOtTypeAnalyseRuleMap(belongid, analyzInfo.getWa_group_id(), dto);
                    Integer validTimeCalType = null;
                    WaOvertimeType overtimeType = null;
                    if (otRuleMap.containsKey(date_type)) {
                        overtimeType = otRuleMap.get(date_type);
                        validTimeCalType = overtimeType.getValidTimeCalType();
                    }
                    if (validTimeCalType == null || WaOtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
                        //按审批（申请）时长
                        calOtByParseRule(of, waAnalyze, analyzInfo, belongid, empShiftDef, dto, currentPeriodOtMap, relOtTimeDurationMap, 2);
                    } else {
                        parseOtTime(of, waAnalyze, analyzInfo, overListMaps, belongid, empShiftDef, dto, currentPeriodOtMap, relOtTimeDurationMap, 2, overtimeType);
                    }
                }

                if (MapUtils.isNotEmpty(currentPeriodOtMap)) {
                    final Integer[] totalDuration = {0};//加班总时长
                    currentPeriodOtMap.forEach((key, v) -> {
                        //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                        if (key.contains("_key")) {
                            Integer duration = Integer.valueOf(v.toString());
                            String[] keyArray = key.split("_");
                            Integer dateType = Integer.valueOf(keyArray[1]);
                            Integer compensateType = Integer.valueOf(keyArray[2]);
                            //根据加班分析规则计算加班时长
                            duration = analyzeOtRule(duration, analyzInfo, dateType, compensateType);
                            totalDuration[0] += duration;
                            currentPeriodOtMap.put(key, duration);
                        }
                    });
                    currentPeriodOtMap.put("time_duration", totalDuration[0]);
                    //数据组合
                    otMap.putAll(currentPeriodOtMap);
                }
            }
            //上月加班单据分析
            if (CollectionUtils.isNotEmpty(otherPeriodOtList)) {
                Map<String, Object> otherPeriodOtMap = new HashMap<>();
                for (EmpOverInfo of : otherPeriodOtList) {
                    of.setBelongid(belongid);
                    //1、工作日，2休息日，3法定假日,4特殊日期
                    Integer date_type = of.getDate_type();
                    if (date_type == null) {
                        continue;
                    }
                    //查询加班分析规则
                    Map<Integer, WaOvertimeType> otRuleMap = getOtTypeAnalyseRuleMap(belongid, analyzInfo.getWa_group_id(), dto);
                    Integer validTimeCalType = null;
                    WaOvertimeType overtimeType = null;
                    if (otRuleMap.containsKey(date_type)) {
                        overtimeType = otRuleMap.get(date_type);
                        validTimeCalType = overtimeType.getValidTimeCalType();
                    }
                    if (validTimeCalType == null || WaOtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
                        //按审批（申请）时长
                        calOtByParseRule(of, waAnalyze, analyzInfo, belongid, empShiftDef, dto, otherPeriodOtMap, relOtTimeDurationMap, 2);
                    } else {
                        parseOtTime(of, waAnalyze, analyzInfo, overListMaps, belongid, empShiftDef, dto, otherPeriodOtMap, relOtTimeDurationMap, 2, overtimeType);
                    }
                }

                if (MapUtils.isNotEmpty(otherPeriodOtMap)) {
                    final Integer[] totalDuration = {0};//加班总时长
                    otherPeriodOtMap.forEach((key, v) -> {
                        //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                        if (key.contains("_key")) {
                            Integer duration = Integer.valueOf(v.toString());
                            String[] keyArray = key.split("_");
                            Integer dateType = Integer.valueOf(keyArray[1]);
                            Integer compensateType = Integer.valueOf(keyArray[2]);
                            //根据加班分析规则计算加班时长
                            duration = analyzeOtRule(duration, analyzInfo, dateType, compensateType);
                            totalDuration[0] += duration;
                            otherPeriodOtMap.put(key, duration);
                        }
                    });
                    otherPeriodOtMap.put("time_duration", totalDuration[0]);
                    //数据组合
                    otherPeriodOtMap.forEach((key, v) -> {
                        //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                        if (key.contains("_key") || "time_duration".equals(key)) {
                            Integer duration = Integer.valueOf(v.toString());
                            if (otMap.containsKey(key)) {
                                int otminute = (Integer) otMap.get(key);
                                otMap.put(key, duration + otminute);
                            } else {
                                otMap.put(key, duration);
                            }
                        } else {
                            if (!otMap.containsKey(key)) {
                                otMap.put(key, v);
                            }
                        }
                    });
                }
            }

            if (MapUtils.isNotEmpty(otMap)) {
                //法定假日加班汇总
                List<String> delKeys = new ArrayList<>();
                final Integer[] legalHolidayDuration = {0};//法定假日加班总时长
                otMap.forEach((k, v) -> {
                    if (!"time_duration".equals(k)) {
                        //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                        String[] keyArray = k.split("_");
                        Integer dateType = Integer.valueOf(keyArray[1]);

                        if (dateType == 3) {
                            legalHolidayDuration[0] += Integer.parseInt(v.toString());
                            delKeys.add(k);
                        }
                    }
                });
                if (delKeys.size() > 0) {
                    otMap.keySet().removeIf(k -> delKeys.contains(k));
                    otMap.put("ot_3_key", legalHolidayDuration[0]);
                    otMap.put("ot_3_name", BaseConst.WA_OT_COMPENSATE.get("3"));
                }
            }
        } else {
            for (EmpOverInfo of : ovlist) {
                of.setBelongid(belongid);
                if (of.getDate_type() == null) {
                    continue;
                }
                Integer dateType = of.getDate_type();
                //查询加班分析规则
                Map<Integer, WaOvertimeType> otRuleMap = getOtTypeAnalyseRuleMap(belongid, analyzInfo.getWa_group_id(), dto);
                Integer validTimeCalType = null;
                WaOvertimeType overtimeType = null;
                if (otRuleMap.containsKey(dateType)) {
                    overtimeType = otRuleMap.get(dateType);
                    validTimeCalType = overtimeType.getValidTimeCalType();
                }
                if (validTimeCalType == null || WaOtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
                    //按审批（申请）时长
                    calOtByParseRule(of, waAnalyze, analyzInfo, belongid, empShiftDef, dto, otMap, relOtTimeDurationMap, 1);
                } else {
                    parseOtTime(of, waAnalyze, analyzInfo, overListMaps, belongid, empShiftDef, dto, otMap, relOtTimeDurationMap, 1, overtimeType);
                }
            }
        }

        dto.setRelOtTimeDurationMap(relOtTimeDurationMap);
        return otMap;
    }

    /**
     * 加班联动打卡记录分析
     *
     * @param start_time
     * @param end_time
     * @param time_duration
     * @param siginTime        签到时间 格式：yyyy-MM-dd HH:mm
     * @param siginOffTime     签退时间 格式：yyyy-MM-dd HH:mm
     * @param empShiftDef
     * @param validTimeCalType
     * @return
     */
    public Integer calRealOtTime(Long start_time, Long end_time, Integer time_duration, Long siginTime, Long siginOffTime, EmpShiftInfo empShiftDef, Integer validTimeCalType) {
        if (validTimeCalType == null || WaOtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
            //按照申请时长计算
            return time_duration;
        }
        if (empShiftDef == null) {
            log.info("calRealOtTime get empShiftDef empty");
            return 0;
        }
        if (start_time == null) {
            start_time = 0L;
        }
        if (end_time == null) {
            end_time = 0L;
        }
        Long realOtStartTime = 0L;
        Long realOtEndTime = 0L;
        Long validateTime = 0L;
        if (WaOtValidTimeCalTypeEnum.APPLY_REG_INTERSECTION.getIndex().equals(validTimeCalType)) {
            if (siginOffTime != null && siginTime != null && start_time <= siginOffTime && end_time >= siginTime) {
                // 有效工作时长
                // 判断签到时间是否小于等于加班开始时间
                if (siginTime <= start_time && siginOffTime >= end_time) {
                    realOtStartTime = start_time;
                    realOtEndTime = end_time;

                    validateTime = end_time - start_time;
                } else if (siginTime <= start_time && siginOffTime <= end_time) {
                    realOtStartTime = start_time;
                    realOtEndTime = siginOffTime;

                    validateTime = siginOffTime - start_time;
                } else if (siginOffTime <= end_time) {
                    realOtStartTime = siginTime;
                    realOtEndTime = siginOffTime;

                    validateTime = siginOffTime - siginTime;
                    // 比较签到签退是否在休息时间段内,如果和休息是段存在重叠则扣除休息时间段的时长
                } else if (siginOffTime >= end_time) {
                    realOtStartTime = siginTime;
                    realOtEndTime = end_time;

                    validateTime = end_time - siginTime;
                }
            }
        } else if (WaOtValidTimeCalTypeEnum.REG_TIME.getIndex().equals(validTimeCalType)) {
            //按打卡时长计算
            if (empShiftDef.getDateType() == 1) {
                Long shiftEndDate = empShiftDef.getWorkDate();
                if (CdWaShiftUtil.checkCrossNight(empShiftDef.getStartTime(), empShiftDef.getEndTime(), empShiftDef.getDateType())) {//跨夜
                    shiftEndDate += 86400;
                }
                Long shiftEndTime = shiftEndDate + (empShiftDef.getEndTime() * 60);

                if (siginOffTime <= shiftEndTime) {
                    validateTime = 0L;
                } else {
                    realOtStartTime = Math.max(shiftEndTime, siginTime);
                    realOtEndTime = siginOffTime;
                    validateTime = siginOffTime - Math.max(shiftEndTime, siginTime);
                }
            } else {
                realOtStartTime = siginTime;
                realOtEndTime = siginOffTime;

                validateTime = siginOffTime - siginTime;
            }
        }

        Integer validateTimeMinute = 0;
        if (validateTime > 0) {
            // 扣除加班休息时段的时长
            Integer shiftDate = empShiftDef.getWorkDate().intValue();
            List<ShiftRestPeriods> overtimeRestPeriods = empShiftDef.getOvertimeRestPeriods();
            if (CollectionUtils.isNotEmpty(overtimeRestPeriods)) {
                for (ShiftRestPeriods rest : overtimeRestPeriods) {
                    Integer restStart = shiftDate + rest.getOvertimeRestStartTime() * 60;
                    Integer restEnd;
                    if (rest.getOvertimeRestStartTime() > rest.getOvertimeRestEndTime() ||
                            CdWaShiftUtil.checkCrossNight(empShiftDef.getStartTime(), empShiftDef.getEndTime(), empShiftDef.getDateType())) {//跨夜
                        restEnd = shiftDate + 86400 + rest.getOvertimeRestEndTime() * 60;
                    } else {
                        restEnd = shiftDate + rest.getOvertimeRestEndTime() * 60;
                    }

                    if (realOtStartTime <= restEnd && realOtEndTime >= restStart) {
                        Long tm = Math.min(realOtEndTime, restEnd) - Math.max(realOtStartTime, restStart);
                        validateTime -= tm;
                        log.info(String.format("休息日和法定节假日休息时间扣减：员工ID={0},日期={1},排班={2} 扣除了{3}分钟", empShiftDef.getEmpid(), empShiftDef.getWorkDate(), empShiftDef.getShiftDefId(), tm / 60));
                    }
                }
            }

            validateTimeMinute = Long.valueOf(validateTime / 60).intValue();

            if (WaOtValidTimeCalTypeEnum.APPLY_REG_INTERSECTION.getIndex().equals(validTimeCalType) && validateTimeMinute > time_duration) {
                //如果计算出的加班时长>申请的加班时长，两者取最小值
                validateTimeMinute = time_duration;
            }
        }

        return validateTimeMinute;
    }

    /**
     * 跨夜加班分析
     *
     * @param of
     * @param empShiftDef
     * @param dto
     * @param validTimeCalType
     * @return
     */
    public Integer calRealOtTime2(EmpOverInfo of, EmpShiftInfo empShiftDef, WaAnalyzDTO dto, Integer validTimeCalType) {
        Integer time_duration = of.getTime_duration();
        Long start_time = of.getStart_time();
        if (start_time == null) {
            start_time = 0L;
        }
        Long end_time = of.getEnd_time();
        if (end_time == null) {
            end_time = 0L;
        }
        Long preDate = DateUtil.addDate(of.getBelongDate() * 1000, -1);
        WaAnalyze preAnalyze = existsWaAnalyz(dto.getWaAnalyzeList(), of.getEmpid(), preDate);
        if (preAnalyze != null && preAnalyze.getRegSignoffTime() != null) {
            Long preSignOffTime = DateUtil.convertStringToDateTime(DateUtil.convertDateTimeToStr(preAnalyze.getRegSignoffTime(), "yyyy-MM-dd HH:mm", true), "yyyy-MM-dd HH:mm", true);
            if (start_time < preSignOffTime) {
                time_duration = calRealOtTime(start_time, end_time, time_duration, 0L, preSignOffTime, empShiftDef, validTimeCalType);
            } else {
                time_duration = 0;
            }
        } else {
            time_duration = 0;
        }
        return time_duration;
    }

    /**
     * 加班联动签到分析
     *
     * @param wa
     * @param overListMaps
     * @param otMap
     * @param key
     * @param of
     * @param analyzInfo
     * @param empShiftDef
     * @param dto
     * @param relOtTimeDurationMap
     * @param legalHolidaysOtRule  法定假日加班分析规则 1 汇总分析 2 不汇总分析
     * @param overtimeType
     */
    private void processOt2(WaAnalyze wa, Map<String, List<EmpOverInfo>> overListMaps, Map<String, Object> otMap, String key,
                            EmpOverInfo of, WaAnalyzInfo analyzInfo, EmpShiftInfo empShiftDef, WaAnalyzDTO dto, Map<String, Integer> relOtTimeDurationMap,
                            Integer legalHolidaysOtRule, WaOvertimeType overtimeType) {
        if (wa == null) {
            return;
        }
        String ot_type_key = String.valueOf(of.getDate_type());
        if (of.getDate_type() != 3 || legalHolidaysOtRule == 2) {
            ot_type_key += "_" + of.getCompensate_type();
        }
        if (ot_type_key == null) {
            return;
        }

        Long start_time = of.getStart_time();
        if (start_time == null) {
            start_time = 0L;
        }
        Long end_time = of.getEnd_time();
        if (end_time == null) {
            end_time = 0L;
        }
        Long siginOffTime = wa.getRegSignoffTime();
        Long siginTime = wa.getRegSigninTime();
        if (analyzInfo.getClock_type() == 1 || analyzInfo.getClock_type() == 3) {
            Map<String, List<Map>> registerRecordMap = dto.getRegisterRecordMap();
            String regKey = String.format("%s_%s", wa.getEmpid(), wa.getBelongDate());
            List<Map> records = registerRecordMap.get(regKey);
            if (CollectionUtils.isNotEmpty(records)) {
                records.sort(Comparator.comparing(r -> (Long) r.get("reg_date_time")));
                siginTime = (Long) records.get(0).get("reg_date_time");
                siginOffTime = (Long) records.get(records.size() - 1).get("reg_date_time");
            }
        }
        if (siginOffTime != null) {
            // 签退时间精确到分钟
            siginOffTime = DateUtil.convertStringToDateTime(DateUtil.convertDateTimeToStr(siginOffTime, "yyyy-MM-dd HH:mm", true), "yyyy-MM-dd HH:mm", true);
        }
        if (siginTime != null) {
            // 签到时间精确到分钟
            siginTime = DateUtil.convertStringToDateTime(DateUtil.convertDateTimeToStr(siginTime, "yyyy-MM-dd HH:mm", true), "yyyy-MM-dd HH:mm", true);
        }

        Calendar c = Calendar.getInstance();
        c.clear();
        Integer timeDuration = of.getTime_duration();

        Integer validTimeCalType = null;
        Integer maxValidTimeMinute = null;
        if (overtimeType != null) {
            validTimeCalType = overtimeType.getValidTimeCalType();
            if (overtimeType.getMaxValidTime() != null) {
                maxValidTimeMinute = Float.valueOf(overtimeType.getMaxValidTime() * 60).intValue();
            }
        }
        //加班联动打卡记录
        if (validTimeCalType != null && !WaOtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
            if (WaOtValidTimeCalTypeEnum.REG_TIME.getIndex().equals(validTimeCalType)) {
                //按打卡时长计算
                if (siginOffTime != null && siginTime != null) {
                    timeDuration = calRealOtTime(start_time, end_time, timeDuration, siginTime, siginOffTime, empShiftDef, validTimeCalType);
                } else {
                    timeDuration = 0;
                }
            } else {
                if (siginOffTime != null && siginTime != null && start_time <= siginOffTime && end_time >= siginTime) {
                    timeDuration = calRealOtTime(start_time, end_time, timeDuration, siginTime, siginOffTime, empShiftDef, validTimeCalType);
                } else {
                    //CLOUD-8841 跨夜加班分析
                    timeDuration = calRealOtTime2(of, empShiftDef, dto, validTimeCalType);
                }
            }
        }

        Integer realTimeDuration = timeDuration;
        // CLOUD-3398 加班分析规则的判断逻辑
        if (timeDuration > 0) {
            // 根据设置的加班规则进行分析
            Integer dateType = of.getDate_type();
            Integer compensateType = of.getCompensate_type();
            realTimeDuration = analyzeOtRule(timeDuration, analyzInfo, dateType, compensateType);
        }

        if (BooleanUtils.isFalse(analyzInfo.getOt_sum_parse())) {//未开启加班单日汇总分析
            timeDuration = realTimeDuration;
        }

        if (maxValidTimeMinute != null && timeDuration > maxValidTimeMinute) {
            timeDuration = maxValidTimeMinute;
        }

        if (maxValidTimeMinute != null && realTimeDuration > maxValidTimeMinute) {
            realTimeDuration = maxValidTimeMinute;
        }

        //记录每条单据的实际加班时长
        relOtTimeDurationMap.put(of.getEmpid() + "_" + of.getOt_detail_id(), realTimeDuration);

        int overTime = timeDuration;

        // 如果加班小时数还有剩余，则继续比较跨夜的数据
        // 查询第二天的加班明细记录
        String otName = BaseConst.WA_OT_COMPENSATE.get(ot_type_key);
        String key1 = "ot_" + ot_type_key + "_key";

        if (otMap.containsKey(key1)) {
            int otminute = (Integer) otMap.get(key1);
            Integer f = (Integer) otMap.get("time_duration");
            if (f == null) {
                f = 0;
            }
            otMap.put("time_duration", overTime + f);
            otMap.put(key1, overTime + otminute);

        } else {
            otMap.put(key1, overTime);

            if (of.getDate_type() != 3 || legalHolidaysOtRule != 2) {
                otMap.put("ot_" + ot_type_key + "_name", otName);
            }

            Integer f = (Integer) otMap.get("time_duration");
            if (f == null) {
                f = 0;
            }
            otMap.put("time_duration", f + overTime);
        }
    }

    /**
     * 根据考勤周期拆分加班单据
     *
     * @param otlist
     * @param otherPeriodOtList
     * @param analyzInfo
     * @param waSob
     * @return
     * @throws Exception
     */
    public List<EmpOverInfo> getOtListByCycle(List<EmpOverInfo> otlist, List<EmpOverInfo> otherPeriodOtList, WaAnalyzInfo analyzInfo, WaSob waSob) throws Exception {
        List<EmpOverInfo> currentPeriodOtList = new ArrayList<>();//当前计算周期的加班单据
        if (waSob != null) {
            otlist.forEach(of -> {
                Long over_date = of.getBelongDate();//加班单据申请日期
                over_date = DateUtil.getDateLong(over_date * 1000, "yyyy-MM-dd", true);
                if (over_date.compareTo(waSob.getStartDate()) < 0) {
                    otherPeriodOtList.add(of);
                } else {
                    currentPeriodOtList.add(of);
                }
            });
        } else {
            for (EmpOverInfo of : otlist) {
                Long real_date = of.getReal_date();
                Long over_date = of.getBelongDate();//加班单据申请日期
                over_date = DateUtil.getDateLong(over_date * 1000, "yyyy-MM-dd", true);
                real_date = DateUtil.getDateLong(real_date * 1000, "yyyy-MM-dd", true);

                //根据日期+考勤周期反推考勤开始日期
                //事件日期
                Map over_date_cycle = waCommonService.calculateCycleTime(over_date, analyzInfo.getCyle_startdate());
                //实际加班日期
                Map real_date_cycle = waCommonService.calculateCycleTime(real_date, analyzInfo.getCyle_startdate());

                boolean isOtherPeriod = false;
                if (MapUtils.isNotEmpty(over_date_cycle) && MapUtils.isNotEmpty(real_date_cycle)) {
                    Long curCycleBegin = (Long) real_date_cycle.get("cycleBegin");
                    Long cycleBegin = (Long) over_date_cycle.get("cycleBegin");

                    if (cycleBegin.compareTo(curCycleBegin) < 0) {//实际加班日期对用的考勤周期开始日期>事件日期对应的考勤周期开始日期
                        isOtherPeriod = true;
                    }
                }
                if (isOtherPeriod) {
                    otherPeriodOtList.add(of);
                } else {
                    currentPeriodOtList.add(of);
                }
            }
        }
        return currentPeriodOtList;
    }

    /**
     * 获取加班分析规则
     *
     * @param belongId
     * @param waGroupId
     * @param dto
     * @return
     */
    private Map<Integer, WaOvertimeType> getOtTypeAnalyseRuleMap(String belongId, Integer waGroupId, WaAnalyzDTO dto) {
        if (waGroupId == null) {
            return new HashMap<>();
        }
        Map<Integer, Map<Integer, WaOvertimeType>> waGroupOtRuleMap = dto.getWaGroupOtRuleMap();
        if (MapUtils.isEmpty(waGroupOtRuleMap) || !waGroupOtRuleMap.containsKey(waGroupId)) {
            if (waGroupOtRuleMap == null) {
                waGroupOtRuleMap = new HashMap<>();
            }
            List<WaOvertimeType> overtimeTypeList = waMapper.getOtTypeAnalyseRule(belongId, waGroupId);
            if (CollectionUtils.isNotEmpty(overtimeTypeList)) {
                Map<Integer, WaOvertimeType> overtimeTypeMap = overtimeTypeList.stream().collect(Collectors.toMap(WaOvertimeType::getDateType, Function.identity(), (v1, v2) -> v1));
                waGroupOtRuleMap.put(waGroupId, overtimeTypeMap);
                dto.setWaGroupOtRuleMap(waGroupOtRuleMap);
                return overtimeTypeMap;
            }
        }
        return waGroupOtRuleMap.get(waGroupId);
    }

    /**
     * 加班时长分析
     *
     * @param of
     * @param waAnalyze
     * @param analyzInfo
     * @param belongid
     * @param empShiftDef
     * @param dto
     * @param otMap
     * @param relOtTimeDurationMap
     * @param legalHolidaysOtRule  法定假日加班分析规则 1 汇总分析 2 不汇总分析
     */
    public void calOtByParseRule(EmpOverInfo of, WaAnalyze waAnalyze, WaAnalyzInfo analyzInfo, String belongid,
                                 EmpShiftInfo empShiftDef, WaAnalyzDTO dto, Map<String, Object> otMap, Map<String, Integer> relOtTimeDurationMap, Integer legalHolidaysOtRule) {
        String ot_type_key = String.valueOf(of.getDate_type());
        if (of.getDate_type() != 3 || legalHolidaysOtRule == 2) {
            ot_type_key += "_" + of.getCompensate_type();
        }
        String key = "ot_" + ot_type_key + "_key";
        Integer time_duration = of.getTime_duration();
        /**
         * 1. 判断是否是属于这一天的加班，判断加班申请时间是否相等
         * 2. 如果审批时间大于申请时间,则需要去找申请时间上的签到签退记录并且是工作日的加班，在计算有效加班时间
         */
        // 如果是跨夜加班的情况，时长归属在次日，实际按前一天的班次信息进行分析
        analyzInfo = dto.getEmpWaAnalyz(of.getEmpid(), of.getRegdate());
        if (analyzInfo != null && analyzInfo.getOtMaps() == null) {
            analyzInfo.setOtMaps(dto.getOtMaps());
        }

        waAnalyze = getEmpAnalyze(belongid, of.getEmpid(), of.getBelongDate(), dto);

        if (empShiftDef != null && waAnalyze != null && waAnalyze.getShiftDefId() == null) {
            waAnalyze.setShiftDefId(empShiftDef.getShiftDefId());
        }

        Integer dateType = of.getDate_type();
        Integer validTimeCalType = null;
        Integer maxValidTimeMinute = null;
        WaOvertimeType overtimeType = null;

        //查询加班分析规则
        if (analyzInfo != null) {
            Map<Integer, WaOvertimeType> otRuleMap = getOtTypeAnalyseRuleMap(belongid, analyzInfo.getWa_group_id(), dto);
            if (otRuleMap.containsKey(dateType)) {
                overtimeType = otRuleMap.get(dateType);
                validTimeCalType = overtimeType.getValidTimeCalType();
                if (overtimeType.getMaxValidTime() != null) {
                    maxValidTimeMinute = Float.valueOf(overtimeType.getMaxValidTime() * 60).intValue();
                }
            }
        } else {
            log.info("calOtByParseRule empid :" + of.getEmpid() + ",Regdate:" + of.getRegdate() + "get analyzInfo empty");
        }

        //如果启用的加班联动考勤分析，则需要检查加班申请日期当天是否有签到签退记录
        // 1.根据申请日期查询到签到签退记录 2.根据签到数据分析有效时长 3.用有效时长跟申请单上的时长进行比较取小值
        if (validTimeCalType != null && !WaOtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
            processOt2(waAnalyze, null, otMap, key, of, analyzInfo, empShiftDef, dto, relOtTimeDurationMap, legalHolidaysOtRule, overtimeType);
            return;
        }
        if (time_duration == null) {
            time_duration = 0;
        }
        if (maxValidTimeMinute != null && time_duration > maxValidTimeMinute) {
            time_duration = maxValidTimeMinute;
        }
        String otName = BaseConst.WA_OT_COMPENSATE.get(ot_type_key);
        if (otMap.containsKey(key)) {
            int otminute = (Integer) otMap.get(key);
            Integer f = (Integer) otMap.get("time_duration");
            if (f == null) {
                f = 0;
            }
            otMap.put("time_duration", time_duration + f);
            otMap.put(key, time_duration + otminute);
        } else {
            otMap.put(key, time_duration);

            if (of.getDate_type() != 3 || legalHolidaysOtRule != 2) {
                otMap.put("ot_" + ot_type_key + "_name", otName);
            }

            Integer f = (Integer) otMap.get("time_duration");
            if (f == null) {
                f = 0;
            }
            otMap.put("time_duration", f + time_duration);
        }
        relOtTimeDurationMap.put(of.getEmpid() + "_" + of.getOt_detail_id(), time_duration);
    }

    /**
     * 获取加班数据， 如果审批时间在申请时间之后的情况，要查询申请时间上的签到签退数据来分析有效的加班小时数 只有申请时间在核算区间之外的才需要查询
     *
     * @param otlist
     * @param analyzInfo
     * @param waAnalyze
     * @param params
     * @param empShiftDef
     * @param dto
     * @return
     * @throws Exception
     */
    private Map<String, Object> getOtColumnJsonB(List<EmpOverInfo> otlist, WaAnalyzInfo analyzInfo, WaAnalyze waAnalyze,
                                                 Map<String, Object> params, EmpShiftInfo empShiftDef, WaAnalyzDTO dto) throws Exception {

        Map<String, Object> otMap = new HashMap<>();
        if (CollectionUtils.isEmpty(otlist)) {
            return otMap;
        }
        String belongid = ConvertHelper.stringConvert(params.get("belongid"));
        // 记录实际加班时长
        Map<String, Integer> relOtTimeDurationMap = dto.getRelOtTimeDurationMap();
        if (relOtTimeDurationMap == null) {
            relOtTimeDurationMap = new HashMap<>();
        }
        WaSob waSob = dto.getWaSob();

        if (analyzInfo != null && BooleanUtils.isTrue(analyzInfo.getOt_sum_parse())) {
            //开启加班单日汇总分析
            //根据考勤周期拆分加班单据，每个考勤周期的加班单据单独分析，最后分析结果合并在一起
            //判读逻辑：事件日期<考勤开始日期 && 实际加班日期在本次核算范围内（帐套开始日期<=审批日期<=帐套截止日期）
            //考勤开始日期：如果考勤帐套有值，取考勤帐套开始日期，反之：根据日期+考勤分组去反推考勤开始日期
            List<EmpOverInfo> otherPeriodOtList = new ArrayList<>();//其他考勤周期的加班单据（eg:事件日期是上月&审批日期是本月）
            List<EmpOverInfo> currentPeriodOtList = this.getOtListByCycle(otlist, otherPeriodOtList, analyzInfo, waSob);//当前计算周期的加班单据

            //当前计算周期的加班单据分析
            if (CollectionUtils.isNotEmpty(currentPeriodOtList)) {
                Map<String, Object> currentPeriodOtMap = new HashMap<>();
                for (EmpOverInfo of : currentPeriodOtList) {
                    of.setBelongid(belongid);
                    //1、工作日，2休息日，3法定假日,4特殊日期
                    Integer date_type = of.getDate_type();
                    if (date_type == null) {
                        continue;
                    }

                    calOtByParseRule(of, waAnalyze, analyzInfo, belongid, empShiftDef, dto, currentPeriodOtMap, relOtTimeDurationMap, 2);
                }

                if (MapUtils.isNotEmpty(currentPeriodOtMap)) {
                    //根据加班分析规则计算加班时长
                    final Integer[] totalDuration = {0};//加班总时长
                    currentPeriodOtMap.forEach((key, v) -> {
                        //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                        if (key.contains("_key")) {
                            Integer duration = Integer.valueOf(v.toString());
                            String[] keyArray = key.split("_");
                            Integer dateType = Integer.valueOf(keyArray[1]);
                            Integer compensateType = Integer.valueOf(keyArray[2]);

                            duration = analyzeOtRule(duration, analyzInfo, dateType, compensateType);

                            totalDuration[0] += duration;

                            currentPeriodOtMap.put(key, duration);
                        }
                    });
                    currentPeriodOtMap.put("time_duration", totalDuration[0]);
                    //数据组合
                    otMap.putAll(currentPeriodOtMap);
                }
            }
            //上月加班单据分析
            if (CollectionUtils.isNotEmpty(otherPeriodOtList)) {
                Map<String, Object> otherPeriodOtMap = new HashMap<>();
                for (EmpOverInfo of : otherPeriodOtList) {
                    of.setBelongid(belongid);
                    //1、工作日，2休息日，3法定假日,4特殊日期
                    Integer date_type = of.getDate_type();
                    if (date_type == null) {
                        continue;
                    }

                    calOtByParseRule(of, waAnalyze, analyzInfo, belongid, empShiftDef, dto, otherPeriodOtMap, relOtTimeDurationMap, 2);
                }

                if (MapUtils.isNotEmpty(otherPeriodOtMap)) {
                    //根据加班分析规则计算加班时长
                    final Integer[] totalDuration = {0};//加班总时长
                    otherPeriodOtMap.forEach((key, v) -> {
                        //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                        if (key.contains("_key")) {
                            Integer duration = Integer.valueOf(v.toString());
                            String[] keyArray = key.split("_");
                            Integer dateType = Integer.valueOf(keyArray[1]);
                            Integer compensateType = Integer.valueOf(keyArray[2]);

                            duration = analyzeOtRule(duration, analyzInfo, dateType, compensateType);

                            totalDuration[0] += duration;

                            otherPeriodOtMap.put(key, duration);
                        }
                    });
                    otherPeriodOtMap.put("time_duration", totalDuration[0]);

                    //数据组合
                    otherPeriodOtMap.forEach((key, v) -> {
                        //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                        if (key.contains("_key") || "time_duration".equals(key)) {
                            Integer duration = Integer.valueOf(String.valueOf(v));
                            if (otMap.containsKey(key)) {
                                int otminute = (Integer) otMap.get(key);
                                otMap.put(key, duration + otminute);
                            } else {
                                otMap.put(key, duration);
                            }
                        } else {
                            if (!otMap.containsKey(key)) {
                                otMap.put(key, v);
                            }
                        }
                    });
                }
            }

            if (MapUtils.isNotEmpty(otMap)) {
                //法定假日加班汇总
                final Integer[] legalHolidayDuration = {0};//法定假日加班总时长
                List<String> delKeys = new ArrayList<>();
                otMap.forEach((k, v) -> {
                    if (!"time_duration".equals(k)) {
                        //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                        String[] keyArray = k.split("_");
                        Integer dateType = Integer.valueOf(keyArray[1]);

                        if (dateType == 3) {
                            legalHolidayDuration[0] += Integer.valueOf(v.toString());
                            delKeys.add(k);
                        }
                    }
                });

                if (delKeys.size() > 0) {
                    otMap.keySet().removeIf(k -> delKeys.indexOf(k) != -1);

                    otMap.put("ot_3_key", legalHolidayDuration[0]);
                    otMap.put("ot_3_name", BaseConst.WA_OT_COMPENSATE.get("3"));
                }
            }
        } else {
            for (EmpOverInfo of : otlist) {
                of.setBelongid(belongid);
                //1、工作日，2休息日，3法定假日,4特殊日期
                Integer date_type = of.getDate_type();
                if (date_type == null) {
                    continue;
                }

                calOtByParseRule(of, waAnalyze, analyzInfo, belongid, empShiftDef, dto, otMap, relOtTimeDurationMap, 1);
            }
        }
        dto.setRelOtTimeDurationMap(relOtTimeDurationMap);
        return otMap;
    }

    private Integer analyzeOtRule(Integer time_duration, WaAnalyzInfo analyzInfo, Integer dateType, Integer compensateType) {
        List<OtAnalyzeRule> otParseRules = analyzInfo.getOtParseRules();

        if (CollectionUtils.isNotEmpty(otParseRules)) {
            for1:
            for (int i = 0; i < otParseRules.size(); i++) {
                OtAnalyzeRule otAnalyzeRule = otParseRules.get(i);
                if (StringUtils.isNotBlank(otAnalyzeRule.getOtTypeIds())) {

                    String[] otypeids = otAnalyzeRule.getOtTypeIds().split(",");
                    for2:
                    for (int j = 0; j < otypeids.length; j++) {
                        Integer ottypeid = Integer.valueOf(otypeids[j]);

                        if (analyzInfo != null && analyzInfo.getOtMaps() != null && analyzInfo.getOtMaps().containsKey(ottypeid)) {
                            WaOvertimeType overtimeType = analyzInfo.getOtMaps().get(ottypeid);

                            if (overtimeType.getDateType().equals(dateType) && overtimeType.getCompensateType().equals(compensateType)) {
//								规则 1=无, 2=向下取整15分钟 ,3 = 向上取整15分钟 ,4=向下取整 30分钟 ,5=向上取整30 分钟
                                //如果实际加班小时数小于（最小有效时长）则加班时长归零
                                if (otAnalyzeRule.getMinValidTime() != null && time_duration < otAnalyzeRule.getMinValidTime()) {
                                    // 如果小于最小时长，则跳出循环
                                    time_duration = 0;
                                    break for1;
                                }
                                switch (otAnalyzeRule.getRule()) {
                                    case 1:
                                        break;
                                    case 2:
                                        time_duration = getOtRoundingRule(time_duration.doubleValue(), false, 15);
                                        break;
                                    case 3:
                                        time_duration = getOtRoundingRule(time_duration.doubleValue(), true, 15);
                                        break;
                                    case 4:
                                        time_duration = getOtRoundingRule(time_duration.doubleValue(), false, 30);
                                        break;
                                    case 5:
                                        time_duration = getOtRoundingRule(time_duration.doubleValue(), true, 30);
                                        break;
                                    default:
                                        break;
                                }
                                break for1;
                            }
                        }
                    }
                }
            }
        }
        return time_duration;
    }

    private WaAnalyze getEmpAnalyze(String belongid, Long empid, long otBelongDate, WaAnalyzDTO dto) {

        WaAnalyze analyze = existsWaAnalyz(dto.getWaAnalyzeList(), empid, otBelongDate);
        if (analyze == null) {

            WaAnalyzeExample analyzeExample = new WaAnalyzeExample();
            analyzeExample.createCriteria().andEmpidEqualTo(empid).andBelongDateEqualTo(otBelongDate);
            List<WaAnalyze> analyzes = waAnalyzeMapper.selectByExample(analyzeExample);
            if (CollectionUtils.isNotEmpty(analyzes)) {
                return analyzes.get(0);
            }

            Map<String, Object> map = new HashMap<String, Object>();
            map.put("belongid", belongid);
            map.put("startDate", otBelongDate);
            map.put("endDate", otBelongDate + (23 * 60 * 60) + (59 * 60) + 59);
            map.put("empid", empid);
            // 查询请假当天的签到记录
            List<Map> reglist = waRegisterRecordMapper.getRegisterRecord(map);

            if (reglist != null && reglist.size() > 1) {
                List<WaRegisterRecord> registerRecords = new ArrayList<WaRegisterRecord>();
                for (Map regMap : reglist) {
                    WaRegisterRecord record2 = new WaRegisterRecord();
                    Integer recordId = (Integer) regMap.get("record_id");
                    Integer resultType = (Integer) regMap.get("result_type");
                    String resultDesc = (String) regMap.get("result_desc");
                    Long regDateTime = (Long) regMap.get("reg_date_time");
                    Long belongDate = (Long) regMap.get("belong_date");
                    Long empid2 = ConvertHelper.longConvert(regMap.get("empid"));
                    Integer shift_def_id = (Integer) regMap.get("shift_def_id");
                    Integer registerType = (Integer) regMap.get("register_type");

                    record2.setShiftDefId(shift_def_id);
                    record2.setRecordId(recordId);
                    record2.setEmpid(empid2);
                    record2.setRegisterType(registerType);
                    record2.setResultType(resultType);
                    record2.setResultDesc(resultDesc);
                    record2.setRegDateTime(regDateTime);
                    record2.setBelongDate(belongDate);

                    registerRecords.add(record2);
                }
                WaRegisterRecord singin = registerRecords.get(0);
                WaRegisterRecord singoff = registerRecords.get(1);
                Long siginOffTime = singoff.getRegDateTime();
                Long siginTime = singin.getRegDateTime();

                analyze = new WaAnalyze();
                analyze.setRegSigninTime(siginTime);
                analyze.setRegSignoffTime(siginOffTime);
                analyze.setBelongDate(DateUtil.getDateLong(siginTime * 1000, "yyyy-MM-dd", true));

            }
        }
        return analyze;
    }

    /**
     * 根据empid＋belongdate 获取这一天的分析数据
     *
     * @param empid
     * @param preDate
     * @return
     */
    private WaAnalyze getWaEmpAnzlyzeByBelongDate(Long empid, Long preDate) {
        WaAnalyzeExample example = new WaAnalyzeExample();
        example.createCriteria().andEmpidEqualTo(empid).andBelongDateEqualTo(preDate);
        List<WaAnalyze> analyzs = waAnalyzeMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(analyzs)) {
            return analyzs.get(0);
        } else {
            return null;
        }
    }

    @SuppressWarnings("rawtypes")
    private Map<String, List<EmpOverInfo>> getEmpOvers(List<Map> empOverlist, Map<String, List<EmpOverInfo>> empOverAfterMaps) {
        Map<String, List<EmpOverInfo>> overListMaps = new HashMap<String, List<EmpOverInfo>>();
        if (empOverlist != null && empOverlist.size() > 0) {
            ObjectMapper objectMapper = new ObjectMapper();
            for (Map<String, Object> empOver : empOverlist) {

                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                String json = null;
                EmpOverInfo of = null;
                try {
                    json = objectMapper.writeValueAsString(empOver);
                    of = objectMapper.readValue(json, new TypeReference<EmpOverInfo>() {
                    });
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                Long empid = of.getEmpid();
                // date_type 1、工作日，2休息日，3法定假日
                Long real_date = of.getReal_date();
                Long over_date = of.getBelongDate();//加班单据申请日期
                over_date = DateUtil.getDateLong(over_date * 1000, "yyyy-MM-dd", true);
                real_date = DateUtil.getDateLong(real_date * 1000, "yyyy-MM-dd", true);
                of.setReal_date(real_date);

                // 1.如果最后审批完成时间大于加班期
                String key = empid + "_" + over_date;
                if (real_date > over_date) { // 如加班事件开始时间小于应归属时间 会包含跨夜加班的数据
                    String key1 = empid + "_" + real_date;
                    if (empOverAfterMaps.containsKey(key1)) {
                        empOverAfterMaps.get(key1).add(of);
                    } else {
                        List<EmpOverInfo> list = new ArrayList<EmpOverInfo>();
                        list.add(of);
                        empOverAfterMaps.put(key1, list);
                    }
                } else {
                    if (overListMaps.containsKey(key)) {
                        overListMaps.get(key).add(of);
                    } else {
                        List<EmpOverInfo> list = new ArrayList<EmpOverInfo>();
                        list.add(of);
                        overListMaps.put(key, list);
                    }
                }
            }
        }
        return overListMaps;
    }

    public List<WaLeaveType> getLeaveTypes(String belongid) {
        WaLeaveTypeExample example = new WaLeaveTypeExample();
        example.createCriteria().andBelongOrgidEqualTo(belongid);
        example.setOrderByClause("orders asc");
        return waLeaveTypeMapper.selectByExample(example);
    }

    public List<WaOvertimeType> getOtTypes(String belongid) {
        return waOvertimeTypeMapper.getOtTypes(belongid);
    }

    /**
     * 获取员工的所属的考勤分组级考勤分组对应的考勤分析分组
     * 获取假期类型
     * 获取加班类型
     *
     * @param paramsMap
     * @return
     */
    @Deprecated
    @SuppressWarnings("unchecked")
    public WaAnalyzDTO getWaAnalyzInfo(Map<String, Object> paramsMap) throws Exception {
        String belongid = ConvertHelper.stringConvert(paramsMap.get("belongid"));
        WaAnalyzDTO dto = new WaAnalyzDTO();

        // 查询所属belognid的所有班次
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongid);
        dto.setCorpShiftDefMap(shiftDefMap);

        List<Map> empWaGropMaps = waParseGroupMapper.getEmpWaGroupAndParseGrops(paramsMap);
        if (empWaGropMaps != null) {
            log.debug("********分配的考勤分析分组*********empWaGropMaps:" + empWaGropMaps.toString());
            try {
                String json = objectMapper.writeValueAsString(empWaGropMaps);
                List<WaAnalyzInfo> empAnalyzInfos = objectMapper.readValue(json, new TypeReference<List<WaAnalyzInfo>>() {
                });
                for (int i = 0; i < empWaGropMaps.size(); i++) {
                    Map empanalyze = empWaGropMaps.get(i);
                    convertAnalyzeRule(empAnalyzInfos.get(i), empanalyze);
                }
                dto.setEmpAnalyzInfos(empAnalyzInfos);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        Map defaultWaGroup = waParseGroupMapper.getDefaultEmpWaGroupAndParseGrop(paramsMap);

        if (defaultWaGroup != null) {
            log.debug("********默认的考勤分析分组*********defaultWaGroup:" + defaultWaGroup);
            String json = objectMapper.writeValueAsString(defaultWaGroup);
            WaAnalyzInfo defaultAnalyz = objectMapper.readValue(json, new TypeReference<WaAnalyzInfo>() {
            });
            convertAnalyzeRule(defaultAnalyz, defaultWaGroup);
            dto.setDefaultAnalyz(defaultAnalyz);
        }
        if (!paramsMap.containsKey("import")) {
            // 假期类型
            List<WaLeaveType> leaveTypes = this.getLeaveTypes(belongid);
            if (leaveTypes != null && leaveTypes.size() > 0) {
                for (WaLeaveType lt : leaveTypes) {
                    dto.getLtMaps().put(lt.getLeaveTypeId(), lt);
                }
            }
        }
        //加班类型
        List<WaOvertimeType> otTypes = this.getOtTypes(belongid);
        if (otTypes != null && otTypes.size() > 0) {
            for (WaOvertimeType ot : otTypes) {
                dto.getOtMaps().put(ot.getOvertimeTypeId(), ot);
            }
        }
        Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();
        // 查询每个人的排班数据
        String anyEmpids = (String) paramsMap.get("anyEmpids");
        List<Long> empIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(anyEmpids)) {
            String[] empidList = anyEmpids.replace("'{", "").replace("}'", "").split(",");
            for (String id : empidList) {
                empIdList.add(Long.valueOf(id));
            }
        }
        Map<String, EmpShiftInfo> empShift = waCommonService.getEmpShiftInfoListMaps(paramsMap, empShiftInfoByDateMap, empIdList, shiftDefMap);
        dto.setEmpShift(empShift);
        dto.setEmpShiftInfoByDateMap(empShiftInfoByDateMap);

        //查询员工信息
        List<SysEmpInfo> empInfoList = getEmpInfoList(belongid, empIdList);
        if (CollectionUtils.isNotEmpty(empInfoList)) {
            Map<Long, SysEmpInfo> empInfoMap = new HashMap<>();
            empInfoList.forEach(row -> empInfoMap.put(row.getEmpid(), row));
            dto.setEmpInfoMap(empInfoMap);
        }
        return dto;
    }

    public List<SysEmpInfo> getEmpInfoList(String belongId, List<Long> empids) {
        SysEmpInfoExample example = new SysEmpInfoExample();
        SysEmpInfoExample.Criteria criteria = example.createCriteria();
        criteria.andBelongOrgIdEqualTo(belongId).andDeletedEqualTo((short) 0);
        if (empids.size() <= 1000) {
            criteria.andEmpidIn(empids);
        }
        return sysEmpInfoMapper.selectByExample(example);
    }

    private void convertAnalyzeRule(WaAnalyzInfo analyzInfo, Map analyzemap) throws Exception {
        String key = "absent_condition_jsonb";
        if (analyzemap.containsKey(key)) {
            PGobject pGobject = (PGobject) analyzemap.get(key);
            List<AbsenceAnalyzeRule> absenceAnalyzeRules = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<AbsenceAnalyzeRule>>() {
            });
            analyzInfo.setAbsentConditionRules(absenceAnalyzeRules);
        }
        key = "ot_pase_jsonb";
        if (analyzemap.containsKey(key)) {
            PGobject pGobject = (PGobject) analyzemap.get(key);
            List<OtAnalyzeRule> otAnalyzeRules = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<OtAnalyzeRule>>() {
            });
            analyzInfo.setOtParseRules(otAnalyzeRules);
        }

    }

    private List<SysEmpInfo> getEmpinfoListByEmpids(String belongid, List<Long> empids) {
        SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
        empInfoExample.createCriteria().andBelongOrgIdEqualTo(belongid).andEmpidIn(empids)
                .andDeletedEqualTo((short) 0);
        List<SysEmpInfo> empInfoList = sysEmpInfoMapper.selectByExample(empInfoExample);
        return empInfoList;
    }

    /**
     * 考勤分析-生成旷工记录
     *
     * @param paramsMap
     * @param startDate
     * @param endDate
     * @param dto
     */
    private void absentEmpRecord(Map<String, Object> paramsMap, Long startDate, Long endDate, WaAnalyzDTO dto) {
        List<Long> empIds = (List<Long>) paramsMap.get("empIdList");
        if (CollectionUtils.isEmpty(empIds)) {
            return;
        }
        String belongid = (String) paramsMap.get("belongid");
        if (startDate == null && endDate == null) {
            endDate = DateUtil.getCurrentTime(false) - (24 * 60 * 60);
            startDate = DateUtil.addDate(endDate, -365);
            endDate = (endDate / 1000) + (23 * 60 * 60) + (59 * 60) + 59;//除以1000转成unix时间＋23:59:59秒
        } else {
            Long currtime = DateUtil.getCurrentTime(true);
            if (endDate.intValue() > currtime.intValue()) {
                endDate = currtime;
            }
        }
        //如果开始时间截止时间都等于空，插入所有人的纪录
        paramsMap.put("startDate", startDate);
        paramsMap.put("endDate", endDate);

        //查询已经分析过的日期数据
        List<String> empDateList = waAnalyzeMapper.getAnalyseEmpDate(paramsMap);

        //遍历每个人每个日期去生成对应的考勤分析数据（排除生成过的日期）
        Integer registerMiss = (Integer) paramsMap.get("registerMiss");
        List<WaAnalyze> analyzeAddList = new ArrayList<>();
        log.info("旷工计算员工个数，" + empIds.size());

        //查询员工入离职信息
        List<SysEmpInfo> empInfoList = getEmpinfoListByEmpids(belongid, empIds);

        if (CollectionUtils.isEmpty(empInfoList)) {
            return;
        }

        Integer clockType = dto.getClockType();

        for (SysEmpInfo empInfo : empInfoList) {
            long start = DateUtilExt.getTimeByPattern(startDate, "yyyy-MM-dd");
            long end = DateUtilExt.getTimeByPattern(endDate, "yyyy-MM-dd");

            Long hireDate = empInfo.getHireDate();
            if (hireDate != null && start <= hireDate) {
                //考勤分析开始日期小于入职日期
                start = hireDate;
            }

            if (empInfo.getStats() == 1 && empInfo.getTerminationDate() != null && end > empInfo.getTerminationDate()) {
                end = empInfo.getTerminationDate();
            }

            Long empId = empInfo.getEmpid();

            while (start <= end) {
                if (!empDateList.contains(empId + "_" + start)) {
                    //1、查询员工排班信息
                    EmpShiftInfo shiftdef = dto.getEmpShiftByDate(empId, start);
                    if (shiftdef != null) {
                        WaAnalyze analyze = new WaAnalyze();
                        analyze.setBelongOrgId(belongid);
                        analyze.setEmpid(empId);
                        analyze.setBelongDate(start);
                        analyze.setWorkTime(shiftdef.getWorkTotalTime());
                        analyze.setCrtuser((Long) paramsMap.get("crtuser"));
                        analyze.setCrttime(DateUtil.getCurrentTime(true));
                        analyze.setShiftDefId(shiftdef.getShiftDefId());

                        if (clockType != null) {
                            if (clockType == 1) {
                                //一次卡
                                WaAnalyzInfo analyzInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                                if (analyzInfo != null && analyzInfo.getClock_rule() != null) {
                                    JSONObject clockJson = JSONObject.parseObject(analyzInfo.getClock_rule());
                                    Integer missingClockRule = (Integer) clockJson.get("missingClockRule");
                                    if (missingClockRule != null && missingClockRule == 1) {
                                        //缺卡 视为旷工
                                        if (shiftdef.getWorkTotalTime() > 0) {
                                            analyze.setIsKg(1);
                                        } else {
                                            analyze.setIsKg(0);
                                        }
                                        analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                                    }
                                } else {
                                    if (shiftdef.getWorkTotalTime() > 0) {
                                        analyze.setIsKg(1);
                                    } else {
                                        analyze.setIsKg(0);
                                    }
                                    analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                                }
                            } else if (clockType == 2) {
                                //两次卡
                                if (registerMiss != null) {
                                    if (registerMiss == 0) {
                                        //缺卡 视为正常
                                        analyze.setIsKg(0);
                                        analyze.setKgWorkTime(0);
                                    } else {
                                        //缺卡 视为旷工
                                        if (shiftdef.getWorkTotalTime() > 0) {
                                            analyze.setIsKg(1);
                                        } else {
                                            analyze.setIsKg(0);
                                        }
                                        analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                                    }
                                } else {
                                    if (shiftdef.getWorkTotalTime() > 0) {
                                        analyze.setIsKg(1);
                                    } else {
                                        analyze.setIsKg(0);
                                    }
                                    analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                                }
                            } else {
                                //不打卡
                                analyze.setActualWorkTime(shiftdef.getWorkTotalTime().floatValue());
                                analyze.setIsKg(0);
                                analyze.setKgWorkTime(0);
                            }
                        }

                        analyzeAddList.add(analyze);
                    }
                }
                start += 86400;
            }
        }

        log.info("旷工工批量插入开始，" + System.currentTimeMillis());
        importService.fastInsertList(WaAnalyze.class, "analyzeId", analyzeAddList);
        log.info("旷工批量插入结束，" + System.currentTimeMillis());
    }


    @Transactional
    public void saveOrUpdateAnalyze(List<WaAnalyze> resultWa,
                                    String belongid, Long userid, Map<String, Object> paramsMap, WaAnalyzDTO dto) {
        List<SysDynamicColumns> dynamicColumns = iSysDynamicColumnsService.findDynamicColumns(belongid, "179");
//		if(resultWa != null && resultWa.size()>0){
        //现在获取所有，在比较，不存在的则新增，存在的则修改
        List<WaAnalyze> walist = waAnalyzeMapper.findWaanalyzeList(paramsMap);
        Map<String, WaAnalyze> waMaps = new HashMap<>();
        List<Integer> listDelAnalyzeId = new ArrayList<>();
        if (walist != null && walist.size() > 0) {
            for (WaAnalyze waAnalyze : walist) {
                String key = waAnalyze.getEmpid() + "_" + waAnalyze.getBelongDate();
                if (waMaps.containsKey(key)) {
                    //					int delRow = waAnalyzeMapper.deleteByPrimaryKey(waAnalyze.getAnalyzeId());
                    listDelAnalyzeId.add(waAnalyze.getAnalyzeId());
                    //					log.info("===========================del:"+delRow+"条==========================");
                    continue;
                }
                waMaps.put(key, waAnalyze);
            }
        }
        if (CollectionUtils.isNotEmpty(listDelAnalyzeId)) {
            WaAnalyzeExample delExample = new WaAnalyzeExample();
            delExample.createCriteria().andAnalyzeIdIn(listDelAnalyzeId);
            int delRow = waAnalyzeMapper.deleteByExample(delExample);
            log.info("===========================del:" + delRow + "条==========================");
        }

        List<WaAnalyze> analyzeInsertList = new ArrayList<WaAnalyze>();
        List<WaAnalyze> analyzeUpdList = new ArrayList<WaAnalyze>();
        for (WaAnalyze waAnalyze : resultWa) {
            waAnalyze.setBelongOrgId(belongid);
            String key = waAnalyze.getEmpid() + "_" + waAnalyze.getBelongDate();
            if (waMaps.containsKey(key)) {
                WaAnalyze old = waMaps.get(key);
                BeanUtils.copyProperties(waAnalyze, old, new String[]{"analyzeId", "crtuser", "crttime"});
                old.setUpduser(userid);
                old.setUpdtime(DateUtil.getCurrentTime(true));
                calculateDynamicRule(old, dynamicColumns, dto);
                waAnalyzeMapper.updateByPrimaryKey(old);
                analyzeUpdList.add(old);
                waMaps.remove(key);
            } else {
                waAnalyze.setCrtuser(userid);
                waAnalyze.setCrttime(DateUtil.getCurrentTime(true));
                calculateDynamicRule(waAnalyze, dynamicColumns, dto);
                waAnalyzeMapper.insertSelective(waAnalyze);
                analyzeInsertList.add(waAnalyze);
            }
				/*WaAnalyzeExample example = new WaAnalyzeExample();
				example.createCriteria().andEmpidEqualTo(waAnalyze.getEmpid()).andBelongDateEqualTo(waAnalyze.getBelongDate()).andBelongOrgIdEqualTo(waAnalyze.getBelongOrgId());
				List<WaAnalyze> list = waAnalyzeMapper.selectByExample(example);
				if(list==null || list.size()==0){
					waAnalyze.setCrtuser(userid);
					waAnalyze.setCrttime(DateUtil.getCurrentTime(true));
					waAnalyzeMapper.insertSelective(waAnalyze);
				}else{
					WaAnalyze old = list.get(0);
					old.setUpduser(userid);
					old.setUpdtime(DateUtil.getCurrentTime(true));
					BeanUtils.copyProperties(waAnalyze, old, new String[]{"analyzeId","crtuser","crttime"});
					waAnalyzeMapper.updateByPrimaryKeySelective(old);
				}*/
        }

        //批量更新
        log.info("开始批量更新");
//			importService.fastUpdList(WaAnalyze.class, "analyzeId", analyzeUpdList);
        log.info("开始批量更新结束");
        //批量插入
        log.info("开始批量插入");
//			importService.fastInsertList(WaAnalyze.class, "analyzeId", analyzeInsertList);
        log.info("开始批量插入结束");
        if (MapUtils.isNotEmpty(waMaps)) {
            List<Integer> listId = new ArrayList<>();
            for (Map.Entry<String, WaAnalyze> entry : waMaps.entrySet()) {
                WaAnalyze analyze = entry.getValue();
                listId.add(analyze.getAnalyzeId());
            }
            //Tried to send an out-of-range integer as a 2-byte   最大32367个参数
            int len = 10000;
            if (listId.size() > len) {
                int size = listId.size() / len;
                for (int i = 0; i <= size; i++) {
                    int from = i * len;
                    int to = (i + 1) * len;
                    if (to > listId.size()) {
                        to = listId.size();
                    }
                    List<Integer> sublist = listId.subList(from, to);
                    WaAnalyzeExample exp = new WaAnalyzeExample();
                    exp.createCriteria().andAnalyzeIdIn(sublist);
                    int delRow = waAnalyzeMapper.deleteByExample(exp);
                    log.info("===========================删除2：" + delRow + "条==========================");
                }
            } else {
                WaAnalyzeExample exp = new WaAnalyzeExample();
                exp.createCriteria().andAnalyzeIdIn(listId);
                int delRow = waAnalyzeMapper.deleteByExample(exp);
                log.info("===========================删除1：" + delRow + "条==========================");
            }
        }
//		}else{
//		}
    }

    /**
     * 计算动态列的值
     *
     * @param waAnalyze
     * @param dynamicColumns
     */
    private void calculateDynamicRule(WaAnalyze waAnalyze, List<SysDynamicColumns> dynamicColumns, WaAnalyzDTO dto) {
        if (CollectionUtils.isNotEmpty(dynamicColumns)) {
            Map extcusttomecoljson = new HashMap();
//			AtomicReference<Double> total_duration = new AtomicReference<>(0.0);

            dynamicColumns.forEach(columns -> {

                String EXT_ID = "EXT_COL_?_ID".replace("?", columns.getDynamicColumnsId().toString());
                String EXT_TXT = "EXT_COL_?_TXT".replace("?", columns.getDynamicColumnsId().toString());

                if (columns.getIsCalculateItem()) {
                    BigDecimal decimal = groovyScriptCalculateValue(waAnalyze, columns.getLogicExp(), dto);
                    if (decimal != null) {
                        decimal = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);//四舍五入保留两位小数
                    }
                    extcusttomecoljson.put(EXT_ID, decimal);
                    extcusttomecoljson.put(EXT_TXT, columns.getColumnZhName());
                }
//				Integer dou = calculateValue(waAnalyze,columns.getLogicExp());

            });
//			extcusttomecoljson.put("duration",total_duration.get());

            waAnalyze.setExtCustomColJson(extcusttomecoljson);
        }
    }

    private BigDecimal groovyScriptCalculateValue(WaAnalyze analyze, String logicExp, WaAnalyzDTO dto) {
        if (StringUtils.isBlank(logicExp)) return null;
        Map binding = new HashMap();

        WaLeaveTypeExample leaveTypeExample = new WaLeaveTypeExample();
        leaveTypeExample.createCriteria().andBelongOrgidEqualTo(analyze.getBelongOrgId());
        leaveTypeExample.setOrderByClause("leave_type_id");
        List<WaLeaveType> leaveTypeList = waLeaveTypeMapper.selectByExample(leaveTypeExample);
        if (CollectionUtils.isNotEmpty(leaveTypeList)) {
            for (WaLeaveType leaveType : leaveTypeList) {
                binding.put("lt_" + leaveType.getLeaveTypeId() + "_key", 0);
            }
        }
        BaseConst.WA_OT_COMPENSATE.forEach((key, value) -> {
            binding.put("ot_" + key + "_key", 0);
        });
        Map<String, Object> otJsonMap = (Map<String, Object>) analyze.getOtColumnJsob();
        if (otJsonMap != null) {
            binding.putAll(otJsonMap);
        }
        Map<String, Object> ltJsonMap = (Map<String, Object>) analyze.getLevelColumnJsonb();
        if (ltJsonMap != null) {
            binding.putAll(ltJsonMap);
        }
        binding.put("actualWorkTime", analyze.getActualWorkTime() == null ? 0f : analyze.getActualWorkTime());
        binding.put("workTime", analyze.getWorkTime() == null ? 0 : analyze.getWorkTime());
        binding.put("registerTime", analyze.getRegisterTime() == null ? 0 : analyze.getRegisterTime());
        binding.put("shiftDefId", analyze.getShiftDefId() == null ? 0 : analyze.getShiftDefId());
        EmpShiftInfo shiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
        binding.put("shiftDef", shiftDef);
        //查询前一天班次
        Long preBelongDate = DateUtil.addDate(analyze.getBelongDate() * 1000, -1);
        EmpShiftInfo preShiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), null, preBelongDate, dto);
        binding.put("preShiftDef", preShiftDef);
        binding.put("analyze", analyze);
        return groovyScriptEngine.executeBigDecimal(logicExp, binding);
    }

    @SuppressWarnings("unchecked")
    private void analyzeLeaveData(List<WaAnalyze> resultWa, Map params, WaAnalyzDTO dto) {
        String belongid = (String) params.get("belongid");
        Long startDate = (Long) params.get("startDate");
        Long endDate = (Long) params.get("endDate");
        if (dto == null) {
            dto = new WaAnalyzDTO();
            // 假期类型
            List<WaLeaveType> leaveTypes = this.getLeaveTypes(belongid);
            if (CollectionUtils.isNotEmpty(leaveTypes)) {
                Map<Integer, WaLeaveType> leaveTypeMap = leaveTypes.stream().collect(Collectors.toMap(WaLeaveType::getLeaveTypeId, Function.identity(), (v1, v2) -> v1));
                dto.getLtMaps().putAll(leaveTypeMap);
            }
        }
        // 根据empid去查询某天的排班纪录，统计请假小时数 如果有请假，则需要抵消迟到早退小时数 按请假迟到有重合的段进行折算
        if (CollectionUtils.isNotEmpty(resultWa)) {
            if (dto.getEmpLeaveInfoList() == null) {
                List<Map> empLeaveList = getEmpLeaveList(params);
                dto.setEmpLeaveInfo(getEmpLeaveFilterList(dto, empLeaveList));
            }
            // 默认正常的请假数据
            // 审批时间在请假时间后的请假数据
            Map<String, List<EmpLeaveInfo>> empleaveAfterMap = new HashMap<>();
            //组合员工的请假单 key ： empid+"_"+leave_date;
            Map<String, List<EmpLeaveInfo>> empleaveMap = getEmpLeaveInfos(dto.getEmpLeaveInfoList(), dto, empleaveAfterMap);
            // 计算请假小时数
            for (WaAnalyze analyze : resultWa) {
                Long belongDate = analyze.getBelongDate();
                String key = analyze.getEmpid() + "_" + belongDate;

                WaAnalyzInfo empAnanlyzInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());

                // 去查找请假日期的数据
                EmpShiftInfo currentShiftdef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
                List<EmpLeaveInfo> empLeaveInfos = null;
                // 查找这一天，是否有请假记录
                if (empleaveMap.containsKey(key)) {
                    List<EmpLeaveInfo> leaveInfos = empleaveMap.get(key);

                    //1 先判断需不需要进行抵扣（如果迟到早退异常时才需要抵扣）
                    if (leaveInfos != null && leaveInfos.size() > 0 && empAnanlyzInfo != null && BooleanUtils.isTrue(empAnanlyzInfo.getLv_parse())) {
                        if (analyze.getLateTime() != null && analyze.getLateTime() > 0 && analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            //扣减迟到早退小时数
                            deductionLateAndEarlyHours2(analyze, leaveInfos, empAnanlyzInfo, 1, belongid);
                        } else if (analyze.getLateTime() != null && analyze.getLateTime() > 0) {
                            deductionLateAndEarlyHours2(analyze, leaveInfos, empAnanlyzInfo, 2, belongid);
                        } else if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            deductionLateAndEarlyHours2(analyze, leaveInfos, empAnanlyzInfo, 3, belongid);
                        }
                    }
                    analyze.setLevelColumnJsonb(getLevelColumnJson(leaveInfos, dto, false));

                    //查找 leaveInfos 中 real_date == 申请日期的数据 存入 原始记录中 在拿请假单数据做分析
                    empLeaveInfos = leaveInfos;
                    empleaveMap.remove(key);
                }
                //2 获取请假当天的数据 做扣减
                List<EmpLeaveInfo> empLeaveInfos2 = null;
                if (empleaveAfterMap.containsKey(key)) {
                    List<EmpLeaveInfo> leaveInfos = empleaveAfterMap.get(key);

                    //1 先判断需不需要进行抵扣（如果迟到早退异常时才需要抵扣）
                    if (leaveInfos != null && leaveInfos.size() > 0 && empAnanlyzInfo != null && BooleanUtils.isTrue(empAnanlyzInfo.getLv_parse())) {
                        Map map = null;
                        if (analyze.getLateTime() != null && analyze.getLateTime() > 0 && analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            //扣减迟到早退小时数
                            map = deductionLateAndEarlyHours2(analyze, leaveInfos, empAnanlyzInfo, 1, belongid);
                        } else if (analyze.getLateTime() != null && analyze.getLateTime() > 0) {
                            map = deductionLateAndEarlyHours2(analyze, leaveInfos, empAnanlyzInfo, 2, belongid);
                        } else if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            map = deductionLateAndEarlyHours2(analyze, leaveInfos, empAnanlyzInfo, 3, belongid);
                        }
                    }
                    // 只用请假记录扣减迟到早退小时数，不记录请假申请时间<审批时间的请假记录
                    //查找 leaveInfos 中 real_date == 申请日期的数据 存入 原始记录中 在拿请假单数据做分析
                    empLeaveInfos2 = leaveInfos;
                    //旷工  员工当天没上班，请一天假，假期单据由9:00-18:00 旷工	考勤异常，旷工和请假单对冲—>当旷工时间=0
                    empleaveAfterMap.remove(key);
                }

                //计算当天的请假单数据
                if (currentShiftdef != null) {
                    Map<String, Object> orginLevelJson = calculateOriginLevel(dto, empLeaveInfos, empLeaveInfos2);
                    analyze.setOriginLevelColumnJsonb(orginLevelJson);
                    analyze.setShiftDefId(currentShiftdef.getShiftDefId());
                }

                // 新的旷工逻辑，旷工时长判断逻辑
                if (currentShiftdef != null && currentShiftdef.getDateType() == 1) {
                    absentAnalyzeRule(analyze, empAnanlyzInfo, currentShiftdef);
                }

                // 4 判断是否有请假记录，如果有则抵消矿工记录
                cancelKgRecord(analyze, empLeaveInfos, empLeaveInfos2);
            }
            // 记录剩余的请假单数据
            calculateEmpLeave(resultWa, empleaveMap, dto);

            //审批时间大于请假申请时间的记录，默认添加一个条分析记录
            addEmpLeaveAfterWaAnalyze(resultWa, empleaveAfterMap, dto, startDate, endDate);
        } else {
            if (dto.getEmpLeaveInfoList() == null) {
                List<Map> empLeaveList = getEmpLeaveList(params);
                dto.setEmpLeaveInfo(getEmpLeaveFilterList(dto, empLeaveList));
            }
            // 默认正常的请假数据
            // 审批时间在请假时间后的请假数据
            Map<String, List<EmpLeaveInfo>> empleaveAfterMap = new HashMap<>();
            //组合员工的请假单 key ： empid+"_"+leave_date;
            Map<String, List<EmpLeaveInfo>> empleaveMap = getEmpLeaveInfos(dto.getEmpLeaveInfoList(), dto, empleaveAfterMap);
            // 记录剩余的请假单数据
            calculateEmpLeave(resultWa, empleaveMap, dto);
            //审批时间大于请假申请时间的记录，默认添加一个条分析记录
            addEmpLeaveAfterWaAnalyze(resultWa, empleaveAfterMap, dto, startDate, endDate);
        }
    }

    private List<Map> getEmpLeaveFilterList(WaAnalyzDTO dto, List<Map> leaveList) {
        //如果开启了出差联动外勤签到，则剔除没有外勤签到或者签到地点和出差地不符的出差单据
        if (CollectionUtils.isNotEmpty(leaveList)) {
            List<WaEmpLeave> invalidLeaves = new ArrayList<>();
            Map<String, List<WaRegisterRecord>> outRegMap = dto.getEmpOutRegMap();

            List<Map> filterList = leaveList.stream().filter(lf -> {
                Boolean isCal = true;
                Integer leave_type = (Integer) lf.get("leave_type");
                Boolean link_outside_sign = (Boolean) lf.get("link_outside_sign");
                Long province = (Long) lf.get("province");
                Long city = (Long) lf.get("city");
                Long empid = (Long) lf.get("empid");
                Long leave_date = (Long) lf.get("leave_date");

                if (leave_type == 11 && BooleanUtils.isTrue(link_outside_sign) && province != null && city != null) {//出差联动外勤签到
                    isCal = false;
                    String invalidReason = "缺少外勤打卡";

                    SysUnitCity proviceObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, province);
                    SysUnitCity cityObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_CITY_KEY, city);
                    if (proviceObj != null && cityObj != null) {
                        if (MapUtils.isNotEmpty(outRegMap) && CollectionUtils.isNotEmpty(outRegMap.get(empid + "_" + leave_date))) {
                            invalidReason = "外勤打卡地址异常";
                            List<WaRegisterRecord> regList = outRegMap.get(empid + "_" + leave_date);
                            for (WaRegisterRecord registerRecord : regList) {
                                List<String> specialCityList = new ArrayList<>(Arrays.asList("上海市", "北京市", "天津市", "重庆市", "台湾", "台湾省", "香港", "香港岛", "香港特别行政区", "澳门", "澳门半岛", "澳门特别行政区"));

                                if (specialCityList.contains(proviceObj.getChnName()) || specialCityList.contains(cityObj.getChnName())) {
                                    if (proviceObj.getChnName().equals(registerRecord.getProvince()) || cityObj.getChnName().equals(registerRecord.getCity())) {
                                        isCal = true;
                                        break;
                                    } else if (StringUtils.isNotBlank(registerRecord.getRegAddr()) && (registerRecord.getRegAddr().contains(proviceObj.getChnName()) || registerRecord.getRegAddr().contains(cityObj.getChnName()))) {
                                        isCal = true;
                                        break;
                                    } else if (StringUtils.isNotBlank(registerRecord.getProvince()) && (registerRecord.getProvince().contains(proviceObj.getChnName()) || registerRecord.getProvince().contains(cityObj.getChnName()))) {
                                        isCal = true;
                                        break;
                                    } else if (StringUtils.isNotBlank(registerRecord.getCity()) && (registerRecord.getCity().contains(proviceObj.getChnName()) || registerRecord.getCity().contains(cityObj.getChnName()))) {
                                        isCal = true;
                                        break;
                                    }
                                } else {
                                    if (cityObj.getChnName().equals(registerRecord.getCity())) {
                                        isCal = true;
                                        break;
                                    } else if (StringUtils.isNotBlank(registerRecord.getRegAddr()) && registerRecord.getRegAddr().contains(cityObj.getChnName())) {
                                        isCal = true;
                                        break;
                                    }
                                }
                            }
                        }
                    } else {
                        invalidReason = "出差地点异常";
                    }

                    if (!isCal) {//记录没有外勤签到的出差单据ID
                        WaEmpLeave leave = new WaEmpLeave();
                        leave.setLeaveId((Integer) lf.get("leave_id"));
                        leave.setInvalidReason(invalidReason);
                        leave.setIsInvalid(true);
                        invalidLeaves.add(leave);
                    }
                }
                return isCal;
            }).collect(Collectors.toList());

            dto.setInvalidLeaves(invalidLeaves);

            return filterList;
        }
        return leaveList;
    }

    public void absentAnalyzeRule(WaAnalyze wa, WaAnalyzInfo anlyzeInfo, EmpShiftInfo currentShiftdef) {
        // 新的规则
        float late = wa.getLateTime() == null ? 0 : wa.getLateTime();
        float early = wa.getEarlyTime() == null ? 0 : wa.getEarlyTime();
        float errorTime = late > early ? late : early; // 如果有迟到早退，则以大的为准
        if (errorTime <= 0) {
            return;
        }
        List<AbsenceAnalyzeRule> absenceAnalyzeRules = anlyzeInfo.getAbsentConditionRules();
        if (CollectionUtils.isNotEmpty(absenceAnalyzeRules)) {
            for (int i = 0; i < absenceAnalyzeRules.size(); i++) {
                AbsenceAnalyzeRule absenceRule = absenceAnalyzeRules.get(i);
                Double start = absenceRule.getStart() * 60; // 目前只有按小时
                Double end = absenceRule.getEnd() * 60;
                if (errorTime >= start && errorTime <= end) {
                    wa.setIsKg(1);
                    switch (absenceRule.getDurationUnit()) {//1 小时/ 2 天
                        case 1:
                            Double duration = absenceRule.getDuration() * 60.0;
                            wa.setKgWorkTime(duration.intValue());
                            if (duration >= currentShiftdef.getWorkTotalTime()) {
                                wa.setLateTime(0f);
                                wa.setEarlyTime(0f);
                            }
                            break;
                        case 2:
                            Double duration2 = absenceRule.getDuration() * currentShiftdef.getWorkTotalTime();
                            wa.setKgWorkTime(duration2.intValue());
                            if (duration2 >= currentShiftdef.getWorkTotalTime()) {
                                wa.setLateTime(0f);
                                wa.setEarlyTime(0f);
                            }
                            break;
                    }
                    break;
                }
            }
        } else {
            //兼容旧的规则 抵扣完成，再次计算 旷工判断 迟到/早退超过 超过多少算旷工
            if (currentShiftdef != null && currentShiftdef.getDateType() == 1 && anlyzeInfo != null && anlyzeInfo.getAbsent_limit() != null) {
                if (Math.round(errorTime / 60) > anlyzeInfo.getAbsent_limit()) {
                    wa.setIsKg(1);
                    wa.setKgWorkTime(currentShiftdef.getWorkTotalTime());
                    // CLOUD-389
                    wa.setLateTime(0f);
                    wa.setEarlyTime(0f);
                }
            }
        }
    }

    @Deprecated
    private Map<String, Object> calculateOriginLevel(WaAnalyzDTO dto, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2) {
//		log.info("calculateOriginLevel ltanalyze {}",ltanalyze);
        //TODO 考勤分析-休假分析新逻辑开启配置：true 开启 false 不开启
        if (ltanalyze) {
            return calculateOriginLevel2(dto, empLeaveInfos, empLeaveInfos2);
        }
        //TODO 以下逻辑为老的分析逻辑，暂时保留，上线后删除
        Map<String, Object> map = new HashMap<String, Object>();
        List<Integer> levelIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
            for (EmpLeaveInfo lf : empLeaveInfos) {
                if (lf.getLeave_date().equals(lf.getReal_date())) {

                    Float time_duration = lf.getTime_duration();
                    if (time_duration == null) time_duration = 0f;
                    Integer time_unit = lf.getTime_unit();
                    if (time_unit == 1) {
                        Integer workTotalTime = getWorkTotalTime(lf);
                        time_duration = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), time_duration, workTotalTime);
                    }
                    WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());
                    String key1 = "lt_" + lf.getLeave_type_id() + "_key";
                    if (map.containsKey(key1)) {
                        Float f = (Float) map.get("time_duration");
                        Float leaveCount = (Float) map.get(key1);
                        map.put(key1, leaveCount + time_duration);
                        map.put("time_duration", f + time_duration);
                    } else {
                        map.put("lt_" + leaveType.getLeaveTypeId() + "_key", time_duration);
                        map.put("lt_" + leaveType.getLeaveTypeId() + "_name", leaveType.getLeaveName());
                        Float f = (Float) map.get("time_duration");
                        if (f == null) f = 0f;
                        map.put("time_duration", f + time_duration);
                    }
                    levelIds.add(lf.getLeave_id());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
            for (EmpLeaveInfo lf : empLeaveInfos2) {
                if (!levelIds.contains(lf.getLeave_id())) {
                    Float time_duration = lf.getTime_duration();
                    if (time_duration == null) time_duration = 0f;
                    Integer time_unit = lf.getTime_unit();
                    if (time_unit == 1) {
                        Integer workTotalTime = getWorkTotalTime(lf);
                        time_duration = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), time_duration, workTotalTime);
                    }
                    WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());
                    String key1 = "lt_" + lf.getLeave_type_id() + "_key";
                    if (map.containsKey(key1)) {
                        Float f = (Float) map.get("time_duration");
                        Float leaveCount = (Float) map.get(key1);
                        map.put(key1, leaveCount + time_duration);
                        map.put("time_duration", f + time_duration);
                    } else {
                        map.put("lt_" + leaveType.getLeaveTypeId() + "_key", time_duration);
                        map.put("lt_" + leaveType.getLeaveTypeId() + "_name", leaveType.getLeaveName());
                        //map.put("lt_"+leaveType.getLeaveTypeId()+"_date", lf.getStart_time());
                        Float f = (Float) map.get("time_duration");
                        if (f == null) f = 0f;
                        map.put("time_duration", f + time_duration);
                    }
                }
            }
        }
        return map;
    }

    /**
     * 计算原始的休假时长
     *
     * @param dto
     * @param empLeaveInfos
     * @param empLeaveInfos2
     * @return
     */
    private Map<String, Object> calculateOriginLevel2(WaAnalyzDTO dto, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2) {
        Map<String, Object> map = new HashMap<>();
        List<Integer> levelIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
            for (EmpLeaveInfo lf : empLeaveInfos) {
                if (lf.getLeave_date().equals(lf.getReal_date())) {
                    Float originalTimeDuration = lf.getTime_duration();
                    if (originalTimeDuration == null) {
                        originalTimeDuration = 0f;
                    }
                    Integer time_unit = lf.getTime_unit();
                    map.put("lt_" + lf.getLeave_type() + "_key_unit", time_unit);

                    Float timeDurationMinute = originalTimeDuration;
                    if (time_unit == 1) {
                        Integer workTotalTime = getWorkTotalTime(lf);
                        timeDurationMinute = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), originalTimeDuration, workTotalTime);
                    }
                    WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());

                    String ltKey = "lt_" + lf.getLeave_type() + "_key";
                    String ltMinuteKey = "lt_" + lf.getLeave_type() + "_key_minute";

                    if (map.containsKey(ltMinuteKey)) {
                        Float ltTime = (Float) map.get(ltKey);
                        map.put(ltMinuteKey, ltTime + originalTimeDuration);

                        Float ltTimeMinute = (Float) map.get(ltMinuteKey);
                        map.put(ltMinuteKey, ltTimeMinute + timeDurationMinute);

                        Float f = (Float) map.get("time_duration");
                        map.put("time_duration", f + timeDurationMinute);
                    } else {
                        map.put(ltKey, originalTimeDuration);
                        map.put(ltMinuteKey, timeDurationMinute);
                        map.put("lt_" + leaveType.getLeaveType() + "_name", leaveType.getLeaveName());

                        Float f = (Float) map.get("time_duration");
                        if (f == null) {
                            f = 0f;
                        }
                        map.put("time_duration", f + timeDurationMinute);
                    }
                    levelIds.add(lf.getLeave_id());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
            for (EmpLeaveInfo lf : empLeaveInfos2) {
                if (!levelIds.contains(lf.getLeave_id())) {
                    Float originalTimeDuration = lf.getTime_duration();
                    if (originalTimeDuration == null) {
                        originalTimeDuration = 0f;
                    }
                    Integer time_unit = lf.getTime_unit();

                    map.put("lt_" + lf.getLeave_type() + "_key_unit", time_unit);

                    Float timeDurationMinute = originalTimeDuration;
                    if (time_unit == 1) {
                        Integer workTotalTime = getWorkTotalTime(lf);
                        timeDurationMinute = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), originalTimeDuration, workTotalTime);
                    }
                    WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());

                    String ltKey = "lt_" + lf.getLeave_type() + "_key";
                    String ltMinuteKey = "lt_" + lf.getLeave_type() + "_key_minute";

                    if (map.containsKey(ltMinuteKey)) {
                        Float ltTime = (Float) map.get(ltKey);
                        map.put(ltMinuteKey, ltTime + originalTimeDuration);

                        Float ltTimeMinute = (Float) map.get(ltMinuteKey);
                        map.put(ltMinuteKey, ltTimeMinute + timeDurationMinute);

                        Float f = (Float) map.get("time_duration");
                        map.put("time_duration", f + timeDurationMinute);
                    } else {
                        map.put(ltKey, originalTimeDuration);
                        map.put(ltMinuteKey, timeDurationMinute);
                        map.put("lt_" + leaveType.getLeaveType() + "_name", leaveType.getLeaveName());

                        Float f = (Float) map.get("time_duration");
                        if (f == null) {
                            f = 0f;
                        }
                        map.put("time_duration", f + timeDurationMinute);
                    }
                }
            }
        }
        return map;
    }

    private Integer getWorkTotalTime(EmpLeaveInfo lf) {
        Integer workTotalTime = null;
        EmpShiftInfo empShiftInfo = lf.getEmpShiftInfo();
        Boolean isSpecial = false;//特殊班次工时调整
        if (empShiftInfo != null && empShiftInfo.getIsSpecial() != null && empShiftInfo.getIsSpecial() && empShiftInfo.getSpecialWorkTime() != null) {
            isSpecial = true;
        }

        if (lf.getEmpShiftInfo() != null) {
            if (isSpecial) {
                workTotalTime = lf.getEmpShiftInfo().getSpecialWorkTime();
            } else {
                workTotalTime = lf.getEmpShiftInfo().getWorkTotalTime();
            }
        }
        // 当是休息日的连续请假时，工作时长目前固定是8小时
        if (lf.getDate_type() != null && lf.getDate_type() == 2 && (workTotalTime == null || workTotalTime == 0)) {
            if (isSpecial) {
                workTotalTime = lf.getEmpShiftInfo().getSpecialWorkTime();
            } else {
                workTotalTime = 480;
            }
        }
        return workTotalTime;
    }

    /**
     * 考勤分析-分析休假数据
     *
     * @param resultWa
     * @param empleaveMap
     * @param dto
     */
    private void calculateEmpLeave(List<WaAnalyze> resultWa, Map<String, List<EmpLeaveInfo>> empleaveMap, WaAnalyzDTO dto) {
        if (MapUtils.isEmpty(empleaveMap)) {
            return;
        }
        WaAnalyze analyze;
        for (Map.Entry<String, List<EmpLeaveInfo>> entry : empleaveMap.entrySet()) {
            analyze = new WaAnalyze();
            String key = entry.getKey();
            Long empid = Long.valueOf(key.split("_")[0]);
            Long leaveDate = Long.valueOf(key.split("_")[1]);

            analyze.setEmpid(empid);
            analyze.setBelongDate(leaveDate);

            List<EmpLeaveInfo> empLeaveInfos = entry.getValue();

            //计算是否旷工，如果请假小时大于等于了应该工作时长则抵消矿工，否则标识了旷工和旷工时长
            Map jsonbMap = getLevelColumnJson(empLeaveInfos, dto, false);
            analyze.setLevelColumnJsonb(jsonbMap);

            //计算当天请假时长及是否旷工
            EmpShiftInfo currentShiftdef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
            leaveCalculate(dto, analyze, empLeaveInfos, null, currentShiftdef);
            if (currentShiftdef != null) {
                analyze.setWorkTime(currentShiftdef.getWorkTotalTime());
            }
            resultWa.add(analyze);
        }
    }

    /**
     * 计算当天请假时长及是否旷工
     *
     * @param dto
     * @param analyze
     * @param empLeaveInfos
     * @param shiftdef
     */
    private void leaveCalculate(WaAnalyzDTO dto, WaAnalyze analyze, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2, EmpShiftInfo shiftdef) {
        //计算当天请假时长
        Map<String, Object> orginLevelJson = calculateOriginLevel(dto, empLeaveInfos, empLeaveInfos2);
        analyze.setOriginLevelColumnJsonb(orginLevelJson);

        // 如果没有签到签退记录或者考勤分析设置上需要判断是否旷工，则需要把没有签到记录，但是请假或加班的数据标识为旷工
        if ((analyze.getSigninId() == null || analyze.getSignoffId() == null) && shiftdef != null && shiftdef.getDateType() == 1 && shiftdef.getWorkTotalTime() > 0) {
            WaAnalyzInfo analyzInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
            if (analyzInfo != null && analyzInfo.getRegister_miss() != null && analyzInfo.getRegister_miss() == 2) { // 没有签到记录算旷工
                analyze.setIsKg(1);
                analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
            }
        }

        //计算旷工时长 用请假小时数和班次上的时长比较
        calculateKg(analyze, shiftdef, empLeaveInfos, empLeaveInfos2);
    }

    /**
     * 获取实际请假小时数
     *
     * @param empLeaveInfos
     * @param empLeaveInfos2
     * @return
     */
    private Float getRealLeaveTime(List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2) {
        Float total_time_duration = 0f;
        List<Integer> levelIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
            for (EmpLeaveInfo lf : empLeaveInfos) {
                if (lf.getLeave_date().equals(lf.getReal_date())) {
                    Float time_duration = lf.getTime_duration();

                    if (lf.getPeriod_type() == 3) {
                        if (lf.getBefore_adjust_time_duration() != null && lf.getBefore_adjust_time_duration() > 0) {//CLOUD-8503
                            time_duration = lf.getBefore_adjust_time_duration();
                        }
                    }

                    if (time_duration != null && time_duration > 0) {
                        Integer time_unit = lf.getTime_unit();
                        if (time_unit == 1) {
                            time_duration = getRealLeaveHour(lf);
                        }
                        total_time_duration += time_duration;
                    }
                    levelIds.add(lf.getLeave_id());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
            for (EmpLeaveInfo lf : empLeaveInfos2) {
                if (!levelIds.contains(lf.getLeave_id())) {
                    Float time_duration = lf.getTime_duration();

                    if (lf.getPeriod_type() == 3) {
                        if (lf.getBefore_adjust_time_duration() != null && lf.getBefore_adjust_time_duration() > 0) {//CLOUD-8503
                            time_duration = lf.getBefore_adjust_time_duration();
                        }
                    }

                    if (time_duration != null && time_duration > 0) {
                        Integer time_unit = lf.getTime_unit();
                        if (time_unit == 1) {
                            time_duration = getRealLeaveHour(lf);
                        }
                        total_time_duration += time_duration;
                    }
                }
            }
        }
        return total_time_duration;
    }

    /**
     * 单位为天的假-请假时长转换成小时
     *
     * @param lf
     * @return
     */
    private Float getRealLeaveHour(EmpLeaveInfo lf) {
        int periodType = lf.getPeriod_type();
        EmpShiftInfo shiftDef = lf.getEmpShiftInfo();
        Float time_duration = lf.getTime_duration();
        Integer workTotalTime = getWorkTotalTime(lf);

        Float leaveTime = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), time_duration, workTotalTime);
        if (periodType == 9 && time_duration != 1) {//半天假，需进行请假时长折算
            if (StringUtils.isNotBlank(lf.getShalf_day()) && StringUtils.isNotBlank(lf.getEhalf_day())) {
                Integer halfWorkTime = leaveTime.intValue();
                //上半天
                if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                    halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                }
                //下半天
                if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                    halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                }
                return Float.valueOf(halfWorkTime);
            }
        }
        return leaveTime;
    }

    /**
     * 旷工时长抵扣
     *
     * @param analyze
     * @param shiftdef
     * @param empLeaveInfos
     * @param empLeaveInfos2
     */
    private void calculateKg(WaAnalyze analyze, EmpShiftInfo shiftdef, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2) {
        if (shiftdef != null && shiftdef.getDateType() != null && shiftdef.getDateType() == 1) {
            analyze.setShiftDefId(shiftdef.getShiftDefId());
            if (analyze.getKgWorkTime() != null && analyze.getKgWorkTime() > 0) {// 如果有旷工小时，则和请假小时进行抵消
                //获取实际请假时长
                Float leavetime = getRealLeaveTime(empLeaveInfos, empLeaveInfos2);
                BigDecimal leaveDec = new BigDecimal(leavetime);
                BigDecimal kgDec = new BigDecimal(analyze.getKgWorkTime());
                BigDecimal diff = kgDec.subtract(leaveDec);

                if (diff.doubleValue() > 0) {
                    if (analyze.getWorkTime() == null) {
                        analyze.setWorkTime(shiftdef.getWorkTotalTime());
                    }
                    analyze.setIsKg(1);
                    analyze.setKgWorkTime(diff.intValue());
                } else {
                    analyze.setIsKg(0);
                    analyze.setKgWorkTime(0);
                }
            }
        }
    }

    /**
     * 旷工时长抵扣逻辑2
     *
     * @param wa
     * @param empLeaveInfos
     * @param empLeaveInfos2
     */
    private void cancelKgRecord(WaAnalyze wa, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2) {
        if ((wa.getIsKg() != null && wa.getIsKg() == 1) || (wa.getKgWorkTime() != null && wa.getKgWorkTime() > 0)) {
            //获取实际休假时长
            Float leaveTime = getRealLeaveTime(empLeaveInfos, empLeaveInfos2);
            BigDecimal leaveDec = new BigDecimal(leaveTime);
            BigDecimal kgDec = new BigDecimal(wa.getKgWorkTime());
            BigDecimal diff = kgDec.subtract(leaveDec);
            if (diff.doubleValue() > 0) {
                wa.setIsKg(1);
                wa.setKgWorkTime(diff.intValue());
            } else {
                wa.setIsKg(0);
                wa.setKgWorkTime(0);
            }
        }
    }

    private void addEmpLeaveAfterWaAnalyze(List<WaAnalyze> resultWa, Map<String, List<EmpLeaveInfo>> empleaveAfterMap, WaAnalyzDTO dto, Long startDate, Long endDate) {
        if (MapUtils.isEmpty(empleaveAfterMap)) {
            return;
        }
        WaAnalyze analyze = null;
        for (Map.Entry<String, List<EmpLeaveInfo>> entry : empleaveAfterMap.entrySet()) {
            String key = entry.getKey();
            Long empid = Long.valueOf(key.split("_")[0]);
            Long leaveDate = Long.valueOf(key.split("_")[1]);

            // 不在核算区间内的数据不进行核算
            if (!(leaveDate >= startDate && leaveDate <= endDate)) {
                continue;
            }

            // 查找是否已有存在的分析数据
            analyze = existsWaAnalyz(resultWa, empid, leaveDate);
            if (analyze == null) {
                analyze = new WaAnalyze();
                analyze.setEmpid(empid);
                analyze.setBelongDate(leaveDate);

                List<EmpLeaveInfo> empLeaveInfos = entry.getValue();

                Map jsonbMap = getLevelColumnJson(empLeaveInfos, dto, true);
                analyze.setLevelColumnJsonb(jsonbMap);

                EmpShiftInfo currentShiftdef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
                if (currentShiftdef == null) {
                    log.info("addEmpLeaveAfterWaAnalyze empid=" + analyze.getEmpid() + ",shifid=" + analyze.getSignoffId() + ",belongDate=" + analyze.getBelongDate());
                }
                //计算当天请假时长及是否旷工
                leaveCalculate(dto, analyze, null, empLeaveInfos, currentShiftdef);
                // 记录应工作时长
                if (currentShiftdef != null) {
                    analyze.setWorkTime(currentShiftdef.getWorkTotalTime());
                }
                resultWa.add(analyze);
            }
        }
    }

    /**
     * 统计请假记录
     *
     * @param leaveInfos          请假记录
     * @param dto
     * @param isApprovlLeaveAfter 是否把请假小时记录在这一天，并且在审批时间大于请假日期时。如果 true 比较审批时间和申请时间
     * @return
     */
    @Deprecated
    private Map getLevelColumnJson(List<EmpLeaveInfo> leaveInfos, WaAnalyzDTO dto, boolean isApprovlLeaveAfter) {
//		log.info("getLevelColumnJson ltanalyze {}",ltanalyze);
        //TODO 考勤分析-休假分析新逻辑开启配置：true 开启 false 不开启
        if (ltanalyze) {
            return getLevelColumnJson2(leaveInfos, dto, isApprovlLeaveAfter);
        }
        //TODO 以下逻辑为老的分析逻辑，暂时保留，上线后删除
        Map<String, Object> map = new HashMap<String, Object>();

        if (leaveInfos != null && leaveInfos.size() > 0)
            for (EmpLeaveInfo lf : leaveInfos) {
                //审批时间大于请假时间 不记录请假记录
                if (isApprovlLeaveAfter) {
                    if (!(Objects.equals(lf.getReal_date(), lf.getLeave_date()))) {
                        continue;
                    }
                }
//			if(lf.getLeave_date().equals(leaveDate)){
//				map.put("empShiftId",lf.getEmpShiftInfo().getShiftDefId());
//			}
                Float time_duration = lf.getTime_duration();
                if (time_duration == null) time_duration = 0f;
                Integer time_unit = lf.getTime_unit();
                if (time_unit == 1) {
                    Integer workTotalTime = getWorkTotalTime(lf);
                    time_duration = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), time_duration, workTotalTime);
                }
                WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());
                if (leaveType == null) {
                    log.info("假期类型不存在 ::ID=" + lf.getLeave_type_id());
                    continue;
                }
                String key1 = "lt_" + lf.getLeave_type_id() + "_key";
                if (map.containsKey(key1)) {
                    Float f = (Float) map.get("time_duration");
                    Float leaveCount = (Float) map.get(key1);
                    map.put(key1, leaveCount + time_duration);
                    map.put("time_duration", f + time_duration);
                } else {
                    map.put("lt_" + leaveType.getLeaveTypeId() + "_key", time_duration);
                    map.put("lt_" + leaveType.getLeaveTypeId() + "_name", leaveType.getLeaveName());
                    //map.put("lt_"+leaveType.getLeaveTypeId()+"_date", lf.getStart_time());
                    Float f = (Float) map.get("time_duration");
                    if (f == null) f = 0f;
                    map.put("time_duration", f + time_duration);
                }
            }
        return map;
    }

    /**
     * 统计请假记录
     *
     * @param leaveInfos          请假记录
     * @param dto
     * @param isApprovlLeaveAfter 是否把请假小时记录在这一天，并且在审批时间大于请假日期时。如果 true 比较审批时间和申请时间
     * @return
     */
    private Map getLevelColumnJson2(List<EmpLeaveInfo> leaveInfos, WaAnalyzDTO dto, boolean isApprovlLeaveAfter) {
        Map<String, Object> map = new HashMap<>();

        if (CollectionUtils.isEmpty(leaveInfos)) {
            return map;
        }

        for (EmpLeaveInfo lf : leaveInfos) {
            //审批时间大于请假时间 不记录请假记录
            if (isApprovlLeaveAfter) {
                if (!(Objects.equals(lf.getReal_date(), lf.getLeave_date()))) {
                    continue;
                }
            }

            WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());
            if (leaveType == null) {
                log.info("假期类型不存在 ::ID=" + lf.getLeave_type_id());
                continue;
            }

            Float originalTimeDuration = lf.getTime_duration();
            if (originalTimeDuration == null) {
                originalTimeDuration = 0f;
            }

            //休假时长（分钟）
            Float timeDurationMinute = originalTimeDuration;
            Integer time_unit = lf.getTime_unit();

            map.put("lt_" + lf.getLeave_type() + "_key_unit", time_unit);

            if (time_unit == 1) {
                Integer workTotalTime = getWorkTotalTime(lf);
                timeDurationMinute = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), originalTimeDuration, workTotalTime);
            }

            String ltKey = "lt_" + lf.getLeave_type() + "_key";
            String ltMinuteKey = "lt_" + lf.getLeave_type() + "_key_minute";

            if (map.containsKey(ltMinuteKey)) {
                Float ltTime = (Float) map.get(ltKey);
                map.put(ltKey, ltTime + originalTimeDuration);

                Float ltTimeMinute = (Float) map.get(ltMinuteKey);
                map.put(ltMinuteKey, ltTimeMinute + timeDurationMinute);

                Float f = (Float) map.get("time_duration");
                map.put("time_duration", f + timeDurationMinute);
            } else {
                map.put(ltKey, originalTimeDuration);
                map.put(ltMinuteKey, timeDurationMinute);
                map.put("lt_" + leaveType.getLeaveType() + "_name", leaveType.getLeaveName());

                Float f = (Float) map.get("time_duration");
                if (f == null) {
                    f = 0f;
                }
                map.put("time_duration", f + timeDurationMinute);
            }
        }
        return map;
    }


    /**
     * @param wa
     * @param leaveInfos
     * @param empAnanlyzInfo
     * @param type           1 有迟到早退  2有迟到  3有早退
     * @return
     */
    private Map deductionLateAndEarlyHours2(WaAnalyze wa, List<EmpLeaveInfo> leaveInfos, WaAnalyzInfo empAnanlyzInfo, int type, String belongid) {

        Boolean isFlexibleWorking = BooleanUtils.toBoolean(empAnanlyzInfo.getIs_flexible_working()); // 是否按弹性时间分析迟到分钟数
        //按照系统标准逻辑抵扣迟到早退时长
        String decByStandardRule = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongid + "_LEAVETIME_DEC_LATEANDEARLYHOURS_BYSTANDARD");
        if ("1".equals(decByStandardRule)) {
            isFlexibleWorking = false;
        }
        //type 1 有迟到早退  2有迟到  3有早退
        //a.leave_id,a.empid,a.time_slot,a.leave_type_id,b.leave_date,b.start_time,b.end_time,
        //b.time_duration,b.period_type,b.date_type,b.time_unit,b.real_date
        //period_type 1、时间单位为天的整天，9、时间单位为天的非整天，4、为时间单位为小时的整天，3、时间单位为小时的非整天
        //time_duration 例如1天,0.5天，或24小时，8.5小时。小时的以分钟为单位
        //time_unit  1天，2小时
        // date_type 1、工作日，2休息日，3法定假日
        for (EmpLeaveInfo lf : leaveInfos) {
            if (lf.getDate_type() == null || lf.getDate_type() != 1) continue; // 当是工作日的请假才进行扣减
            //如果有迟到早退才进行扣减
            if ((wa.getLateTime() != null && wa.getLateTime() > 0) || (wa.getEarlyTime() != null && wa.getEarlyTime() > 0)) {
                // 如果请假记录是等于当天的才进行扣减
                if (lf.getLeave_date().equals(wa.getBelongDate()) && lf.getEmpShiftInfo() != null) {
                    //扣减签到签退小时数
                    if (isFlexibleWorking && lf.getEmpShiftInfo() != null && lf.getEmpShiftInfo().getIsFlexibleWork() && lf.getEmpShiftInfo().getFlexibleWorkType() == 1) {
                        // 弹性判断扣减
                        log.info("------对迟到早退根据弹性规则计算");
                        deductionLateAndEarly2(wa, type, lf);
                    } else {
                        deductionLateAndEarly(wa, type, lf);
                    }
                }
            }
        }
        return null;
    }

    private void deductionLateAndEarly2(WaAnalyze analyze, int type, EmpLeaveInfo lf) {

        EmpShiftInfo empShiftDef = lf.getEmpShiftInfo();

        //type 1 有迟到早退  2有迟到  3有早退

        //a.leave_id,a.empid,a.time_slot,a.leave_type_id,b.leave_date,b.start_time,b.end_time,
        //b.time_duration,b.period_type,b.date_type,b.time_unit,b.real_date

        //period_type 1、时间单位为天的整天，9、时间单位为天的非整天，4、时间单位为小时的整天，3、时间单位为小时的非整天
        //time_duration 例如1天,0.5天，或24小时，8.5小时。小时的以分钟为单位
        //time_unit  1天，2小时
        // date_type 1、工作日，2休息日，3法定假日
        EmpShiftInfo shiftDef = lf.getEmpShiftInfo();
        Integer workTotalTime = lf.getEmpShiftInfo().getWorkTotalTime();

        int halfWorkTime = workTotalTime / 2; // 半天的时长等于班次上工作时长的二分之一

        int periodType = lf.getPeriod_type();
        switch (type) {
            case 1:
                // 扣减迟到早退小时数
                if (periodType == 1) {
                    //时间单位为天的整天
                    analyze.setLateTime(0f);
                    analyze.setEarlyTime(0f);
                    analyze.setIsExp(0);
                } else if (periodType == 9) {//时间单位为天的非整天
                    // 需要判断是上半天下半天 时间段
                    // 1 先得求出上半天 是几点到几点 下半天是几点到几点  A 代表上半天  P 代表下半天
                    if (lf.getTime_duration() == 1) {
                        if (analyze.getLateTime() != null && analyze.getLateTime() > 0) analyze.setLateTime(0f);
                        if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) analyze.setEarlyTime(0f);
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null) {//CLOUD-8450
                            jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                        } else {
                            jsLateOrEarlyTime9(analyze, lf, empShiftDef);
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    analyze.setLateTime(0f);
                    analyze.setEarlyTime(0f);
                    analyze.setIsExp(0);
                } else if (periodType == 3) {
                    jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                }
                break;
            case 2:
                // 扣减迟到小时数
                // 1。判断请假类型
                if (periodType == 1) {
                    //时间单位为天的整天
                    analyze.setLateTime(0f);
                } else if (periodType == 9) { // 时间单位为天的非整天
                    // 需要判断是上半天下半天 时间段
                    if (lf.getTime_duration() == 1) {
                        if (analyze.getLateTime() != null && analyze.getLateTime() > 0) analyze.setLateTime(0f);
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null) {//CLOUD-8450
                            jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                        } else {
                            jsLateOrEarlyTime9(analyze, lf, empShiftDef);
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    analyze.setLateTime(0f);
                    //wa.setIsExp(0);
                } else if (periodType == 3) {

//                    休小时假的情况：
//
//                    1) 如果是休2小时假（休*班次的上班开始时间*后2小时和*班次下班时间*前2小时，），上午2小时：签到记录晚于“班次上班时间+2小时”，则为迟到，签退早于弹性下班开始时间则为早退；下午2小时：签到晚于弹性上班结束时间的，为迟到，签退早于“班次下班时间-2小时”，为早退；
//                    2) 如果是4小时的假期，按照（3）休半天假的逻辑去分析
//
//                    休小时假只有2小时和4小时这两种
                    // 判断是上午的假还是下午的假 如果请假时间在中午休息时间前的算上午的，否则算下午的

                    jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                }
                break;
            case 3:
                //扣减早退小时数
                // 1。判断请假类型
                if (periodType == 1) {
                    //时间单位为天的整天
                    analyze.setEarlyTime(0f);
                } else if (periodType == 9) {
                    // 需要判断是上半天下半天 时间段
                    if (lf.getTime_duration() == 1) {
                        if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) analyze.setEarlyTime(0f);
                        //if(wa.getEarlyTime()!= null && wa.getEarlyTime()>0) wa.setEarlyTime(0);
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null) {//CLOUD-8450
                            jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                        } else {
                            jsLateOrEarlyTime9(analyze, lf, empShiftDef);
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    analyze.setEarlyTime(0f);
                    //wa.setIsExp(0);
                } else if (periodType == 3) {
                    jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                }
                break;
            default:
                break;
        }
    }

    private void jsLateOrEarlyTime9(WaAnalyze analyze, EmpLeaveInfo lf, EmpShiftInfo empShiftDef) {
        if (StringUtils.isBlank(lf.getShalf_day()) || StringUtils.isBlank(lf.getEhalf_day())) {
            log.info("case 1 ***************************请假单未设置 上半天 下半天的标识会导致计算有误 leaveId=" + lf.getLeave_id());
        }
        //休上半天的假，下午出勤无弹性（下班时间以班次定义的下班时间为标准，去分析是否早退）:中午签到有异常，此时判断是否有审批通过的上半天的假，
        // 下午的上班开始时间即为班次上定义的半天时间，签到时间晚于定义的半天时间的即为迟到，签退时间早于班次设置的下班时间则为早退。
        if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
            Long halftime;
            if (empShiftDef.getIsHalfdayTime()) {
                halftime = analyze.getBelongDate() + (empShiftDef.getHalfdayTime() * 60);
            } else {
                halftime = analyze.getBelongDate() + (empShiftDef.getNoonRestEnd() * 60);
            }
            Long siginTime = analyze.getRegSigninTime();
            Long signoffTime = analyze.getRegSignoffTime();

            Long shiftEndTime = analyze.getBelongDate() + (empShiftDef.getEndTime() * 60);

            if (siginTime > halftime) {
                Float latetime = siginTime.floatValue() - halftime.floatValue();
                if (latetime < 0) latetime = 0f;
                else {
                    latetime /= 60;
                }
                analyze.setLateTime(latetime);
            }
            if (signoffTime < shiftEndTime) {
                Float earlytime = shiftEndTime.floatValue() - signoffTime.floatValue();
                if (earlytime < 0) earlytime = 0f;
                else {
                    earlytime /= 60;
                }
                analyze.setEarlyTime(earlytime);
            }
        }
        // 1) 休下半天假，上午出勤有弹性：重新计算实际工作时长  20190525 add CLOUD-3848
        if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
            // 统计实际签到时间 至 实际签退时间 的时长为实际出勤时长，实际出勤时长>=“工作时长/2”小时，就可以签退
            if (analyze.getRegisterTime() >= empShiftDef.getWorkTotalTime() / 2) {
                analyze.setEarlyTime(0f);
                if (analyze.getLateTime() <= 0) analyze.setIsExp(0);
            } else {//  如果 实际出勤时长<“工作时长/2”小时
                Float early = 0f;
//              如果 实际出勤时长<“工作时长/2”小时，则根据当天实际出勤时长与“正常签到+4小时”比较，得出早退分钟数；
                Long flexbleStartTime = analyze.getBelongDate() + (empShiftDef.getFlexibleOnDutyStartTime() * 60);
                Long flexbleEndTime = analyze.getBelongDate() + (empShiftDef.getFlexibleOnDutyEndTime() * 60);
                Long signinTime = analyze.getRegSigninTime();

//              如果签到时间超出弹性上班时间范围（7:30-9:30 ，如果7:00签到，实际是从弹性上班开始时间7:30去计算上午的出勤时长；如果签到时间大于弹性上班截止时间，
//              比如9:40签到，以9:30 + 4小时得出应签退时间，与实际签退时间比较，是否有早退情况，再计算9:30-9:40之间10分钟的迟到））CLOUD-5578

                if (signinTime < flexbleStartTime) {
                    signinTime = flexbleStartTime;
                    Long yqttime = signinTime + (empShiftDef.getWorkTotalTime() / 2) * 60;
                    early = yqttime - analyze.getRegSignoffTime().floatValue();
                } else if (signinTime > flexbleEndTime) {

                    Long yqttime = flexbleEndTime + (empShiftDef.getWorkTotalTime() / 2) * 60L;
                    early = yqttime - analyze.getRegSignoffTime().floatValue();

                    Float latetime = signinTime.floatValue() - flexbleEndTime;
                    if (latetime < 0) latetime = 0f;
                    else {
                        latetime = latetime / 60;
                    }
                    analyze.setLateTime(latetime);
                }
                if (early < 0) early = 0f;
                else {
                    early = early / 60;
                }
                analyze.setEarlyTime(early);
            }

            updateRegExpStatus(analyze);


//							halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY,shiftDef,halfWorkTime);
//							if(analyze.getEarlyTime() > halfWorkTime){
//								analyze.setEarlyTime(analyze.getEarlyTime() - halfWorkTime);
//							}else{
//								analyze.setEarlyTime(0);
//							}

//							long start = analyze.getBelongDate()+(empShiftDef.getFlexibleOnDutyStartTime()*60);
//							long end = analyze.getBelongDate()+(empShiftDef.getOvertimeStartTime()==null?empShiftDef.getFlexibleOffDutyEndTime():empShiftDef.getOvertimeStartTime())*60;
//
//							Long start_time = Math.max(analyze.getRegSigninTime(),start);
//							Long end_time = Math.min(analyze.getRegSignoffTime(),end);
//
//							long timehour  =  (end_time - start_time)/60;
//							if(timehour < 0 ) timehour = 0l;
//							analyze.setRegisterTime(IntegerUtils.parseInt(timehour+"",0));
        }
    }

    //type 1 有迟到早退  2有迟到  3有早退
    private void jsLateOrEarlyTime3(int type, WaAnalyze analyze, EmpLeaveInfo lf, EmpShiftInfo shiftDef) {

        Long s = lf.getLeave_date() + lf.getStart_time() * 60;
        Long e = lf.getLeave_date() + lf.getEnd_time() * 60;

        Long halftime = lf.getLeave_date() + shiftDef.getHalfdayTime() * 60;
        boolean isAM = true; // 默认上午的假
        if (e > halftime) {
            isAM = false;
        }

        Float time_duration = lf.getTime_duration();
        if (lf.getBefore_adjust_time_duration() != null && lf.getBefore_adjust_time_duration() > 0) {//CLOUD-8503
            time_duration = lf.getBefore_adjust_time_duration();
        }
        if (time_duration != null && shiftDef.getWorkTotalTime() != null && time_duration >= shiftDef.getWorkTotalTime()) {
            analyze.setEarlyTime(0f);
            analyze.setLateTime(0f);
        } else if (time_duration >= (shiftDef.getWorkTotalTime() / 2)) {  // 请假时长大于4小时按半天的逻辑分析
            if (isAM && (type == 1 || type == 2)) {
                analyze.setLateTime(0f);
            }
            if (!isAM && (type == 1 || type == 3)) {
                analyze.setEarlyTime(0f);
            }
            //CLOUD-5587
            if (shiftDef.getIsHalfdayTime()) {
                halftime = analyze.getBelongDate() + (shiftDef.getHalfdayTime() * 60);
            } else {
                halftime = analyze.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
            }
            Long siginTime = analyze.getRegSigninTime();
            Long signoffTime = analyze.getRegSignoffTime();

            Long shiftEndTime = analyze.getBelongDate() + (shiftDef.getEndTime() * 60);

            if (siginTime > halftime) {
                Float latetime = siginTime.floatValue() - halftime.floatValue();
                if (latetime < 0) latetime = 0f;
                else {
                    latetime /= 60;
                }
                analyze.setLateTime(latetime);
            }
            //请上午假无弹性，才需要重新计算早退小时数 CLOUD-5867
            if (isAM && signoffTime < shiftEndTime) {
                Float earlytime = shiftEndTime.floatValue() - signoffTime.floatValue();
                if (earlytime < 0) earlytime = 0f;
                else {
                    earlytime /= 60;
                }
                analyze.setEarlyTime(earlytime);
            }
        } else if (time_duration < (shiftDef.getWorkTotalTime() / 2)) {// 请假时长小于4小时按小时假逻辑分析
            if (isAM && (type == 1 || type == 2)) {
                //1 如果有，签到记录晚于“班次上班时间+2小时”，则为迟到
                Long shiftStartTime = analyze.getBelongDate() + (shiftDef.getStartTime() * 60) + (time_duration.longValue() * 60);
                if (analyze.getRegSigninTime() > shiftStartTime) {
                    Float late = (analyze.getRegSigninTime().floatValue() - shiftStartTime.intValue()) / 60;
                    if (late < 0) late = 0f;
                    analyze.setLateTime(late);
                } else {
                    analyze.setIsExp(0);
                    analyze.setLateTime(0f);
                }
            }
            if (!isAM && (type == 1 || type == 3)) {
                // 下午2小时：签退早于“班次下班时间-2小时”，为早退；
                Long shiftStartTime = analyze.getBelongDate() + (shiftDef.getEndTime() * 60) - (time_duration.longValue() * 60);
                if (analyze.getRegSigninTime() < shiftStartTime) {
                    Float early = (shiftStartTime.floatValue() - analyze.getRegSignoffTime().floatValue()) / 60;
                    if (early > shiftDef.getWorkTotalTime()) {
                        early = shiftDef.getWorkTotalTime().floatValue();
                    }
                    if (early < 0) {
                        early = 0f;
                    }
                    analyze.setEarlyTime(early);
                } else {
                    analyze.setIsExp(0);
                    analyze.setEarlyTime(0f);
                }
            }
        }
        updateRegExpStatus(analyze);
    }

    private void updateRegExpStatus(WaAnalyze wa) {
        if (wa != null) {
            if ((wa.getLateTime() != null && wa.getLateTime() > 0) || (wa.getEarlyTime() != null && wa.getEarlyTime() > 0)) {
                wa.setIsExp(1);
            } else {
                wa.setIsExp(0);
            }
        }
    }

    /**
     * 休假时长扣减午休时间
     *
     * @param shiftDef   班次
     * @param belongDate 所属日期
     * @param startTime  请假开始时间
     * @param endTime    请假结束时时间
     * @return
     * @throws Exception
     */
    public Float deductLeaveDayNooRest(EmpShiftInfo shiftDef, Long belongDate, Long startTime, Long endTime) {
        //计算扣除中午休息的时间，多段
        Float timeDuration = endTime.floatValue() - startTime.floatValue();
        if (BooleanUtils.isTrue(shiftDef.getIsNoonRest())) {
            List<Map> restList = new ArrayList<>();
            //定义半天的休息时间段
            Integer restStart = shiftDef.getNoonRestStart();
            Integer restEnd = shiftDef.getNoonRestEnd();
            if (restStart != null && restEnd != null) {
                Map noonRestMap = new LinkedHashMap();
                noonRestMap.put("noonRestStart", restStart);
                noonRestMap.put("noonRestEnd", restEnd);
                restList.add(noonRestMap);
            }
            if (shiftDef.getRestPeriods() != null) {
                List<Map> restPeriods = null;
                try {
                    restPeriods = (List) JacksonJsonUtil.jsonToBean(shiftDef.getRestPeriods().toString(), List.class);
                } catch (Exception e) {
                    log.error("registerAnalyzeService.deductLeaveDayNooRest异常,{}", e.getMessage(), e);
                }
                if (CollectionUtils.isNotEmpty(restPeriods)) {
                    restList.addAll(restPeriods);
                }
            }
            restList = restList.stream().filter(periodMap -> periodMap.get("noonRestStart") != null && periodMap.get("noonRestEnd") != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(restList)) {
                for (Map periodMap : restList) {
                    Integer noonRestStartMin = (Integer) periodMap.get("noonRestStart");
                    Integer noonRestEndMin = (Integer) periodMap.get("noonRestEnd");
                    ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType());
                    noonRestStartMin = restPeriod.getNoonRestStart();
                    noonRestEndMin = restPeriod.getNoonRestEnd();
                    Long noonRestStart = belongDate + (noonRestStartMin * 60);
                    Long noonRestEnd = belongDate + (noonRestEndMin * 60);
                    if (startTime < noonRestEnd && endTime > noonRestStart) {
                        timeDuration -= Math.min(noonRestEnd, endTime) - Math.max(noonRestStart, startTime);
                    }
                }
            }
        }
        return timeDuration / 60;
    }

    /**
     * 休假时长抵扣迟到早退
     *
     * @param waAnalyze 考勤分析数据
     * @param lf        休假单
     * @param shiftDef  班次
     * @param startTime 需抵扣的开始时间点
     * @param endTime   需抵扣的结束时间点
     * @throws Exception
     */
    public Float getDeductionTimeDuration(WaAnalyze waAnalyze, EmpLeaveInfo lf, EmpShiftInfo shiftDef, Long startTime, Long endTime, Float timeDuration) {
        Long leaveStartTime = lf.getLeave_date() + lf.getStart_time() * 60;
        Long leaveEndTime = lf.getLeave_date() + lf.getEnd_time() * 60;
        if (leaveStartTime <= startTime && leaveEndTime >= endTime) {
            return timeDuration;
        } else if (leaveStartTime >= startTime && leaveEndTime <= endTime) {
            return lf.getTime_duration().floatValue();
        } else if (leaveStartTime < endTime && leaveEndTime > startTime) {
            Long start = Math.max(leaveStartTime, startTime);
            Long end = Math.min(leaveEndTime, endTime);
            return deductLeaveDayNooRest(shiftDef, waAnalyze.getBelongDate(), start, end);
        }
        return 0f;
    }

    /**
     * 时间单位为小时的非整天-抵扣迟到、早退 type 1
     *
     * @param wa
     * @param lf
     * @param shiftDef
     */
    public void decLateAndEarlyForType1PeriodType3(WaAnalyze wa, EmpLeaveInfo lf, EmpShiftInfo shiftDef) {
        //抵扣迟到时长
        if (wa.getLateTime() != null && wa.getLateTime() > 0) {
            Long shiftStartTime = wa.getBelongDate() + (shiftDef.getStartTime() * 60);
            Float deductionTime = getDeductionTimeDuration(wa, lf, shiftDef, shiftStartTime, wa.getRegSigninTime(), wa.getLateTime());
            if (deductionTime > 0) {
                Float lateTime = wa.getLateTime() - deductionTime;
                lateTime = lateTime < 0 ? 0 : lateTime;
                wa.setLateTime(lateTime);
            }
        }
        //抵扣早退时长
        if (wa.getEarlyTime() != null && wa.getEarlyTime() > 0) {
            Long shiftEndTime = wa.getBelongDate() + (shiftDef.getEndTime() * 60);
            Float deductionTime = getDeductionTimeDuration(wa, lf, shiftDef, wa.getRegSignoffTime(), shiftEndTime, wa.getEarlyTime());
            if (deductionTime > 0) {
                Float earlyTime = wa.getEarlyTime() - deductionTime;
                earlyTime = earlyTime < 0 ? 0 : earlyTime;
                wa.setEarlyTime(earlyTime);
            }
        }
    }

    public void decLateAndEarlyForType2PeriodType3(WaAnalyze wa, EmpLeaveInfo lf, EmpShiftInfo shiftDef) {
        //时间单位为小时的非整天
        Long s = lf.getLeave_date() + lf.getStart_time() * 60;
        Long e = lf.getLeave_date() + lf.getEnd_time() * 60;
        //迟到时 判断迟到时间是否在 请假区间内
        if (wa.getRegSigninTime() >= s && wa.getRegSigninTime() <= e) {
            // 迟到时间 － （签到时间－请假开始时间） ＝ 最终迟到小时数
            long regtime = wa.getRegSigninTime();
            if (shiftDef != null && shiftDef.getIsNoonRest()) {
                long rs = wa.getBelongDate() + (shiftDef.getNoonRestStart() * 60);
                long re = wa.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
                // 如果签到时间在休息时间内，则指定regtime 为休息开始时间
                if (regtime >= rs && regtime <= re) {
                    regtime = rs;
                }
            }

            Float late = wa.getLateTime() - (int) (regtime - s) / 60;
            if (late < 0) late = 0f;
            wa.setLateTime(late);
        } else {
            //当签到签退时间段和请假时间段没有交集时，用请假的小时数减去迟到的小时数
            if (lf.getBefore_adjust_time_duration() != null && lf.getBefore_adjust_time_duration() > 0) {
                //工时调整逻辑，CLOUD-8503
                float remainHours = wa.getLateTime() - lf.getBefore_adjust_time_duration().intValue();
                if (remainHours < 0) remainHours = 0;
                wa.setLateTime(remainHours);
            } else {
                float remainHours = wa.getLateTime() - lf.getTime_duration().intValue();
                if (remainHours < 0) remainHours = 0;
                wa.setLateTime(remainHours);
            }
        }
    }

    public void decLateAndEarlyForType3PeriodType3(WaAnalyze wa, EmpLeaveInfo lf, EmpShiftInfo shiftDef) {
        Long s = lf.getLeave_date() + lf.getStart_time() * 60;
        Long e = lf.getLeave_date() + lf.getEnd_time() * 60;
        //迟到时 判断迟到时间是否在 请假区间内
        long regtime = wa.getRegSignoffTime();
        if (regtime >= s && regtime <= e) {
            //  时间时间 等于 迟到时间 － （签到时间－请假开始时间） ＝ 最终迟到小时数
            if (shiftDef != null && shiftDef.getIsNoonRest()) {
                long rs = wa.getBelongDate() + (shiftDef.getNoonRestStart() * 60);
                long re = wa.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
                // 如果签到时间在休息时间内，则指定regtime 为休息截止时间
                if (regtime >= rs && regtime <= re) {
                    regtime = re;
                }
            }
            // eg: 1493352000 - 1493348557 12点整－11点2分37秒  精确到分钟
            Float early = wa.getEarlyTime() - (float) Math.ceil((e - regtime) / 60.0);
            if (early < 0) early = 0f;
            wa.setEarlyTime(early);
        } else {
            //当签到签退时间段和请假时间段没有交集时，用请假的小时数减去早退的小时数

            if (lf.getBefore_adjust_time_duration() != null && lf.getBefore_adjust_time_duration() > 0) {
                //工时调整逻辑，CLOUD-8503
                float remainHours = wa.getEarlyTime() - lf.getBefore_adjust_time_duration().intValue();
                if (remainHours < 0) remainHours = 0;
                wa.setEarlyTime(remainHours);
            } else {
                float remainHours = wa.getEarlyTime() - lf.getTime_duration().intValue();
                if (remainHours < 0) remainHours = 0;
                wa.setEarlyTime(remainHours);
            }
        }
    }

    /**
     * 半天假抵扣迟到时长
     *
     * @param wa
     * @param lf
     * @param shiftDef
     */
    public void decLateForPeriodType9(WaAnalyze wa, EmpLeaveInfo lf, EmpShiftInfo shiftDef) {
        if (shiftDef == null) return;
        //1、计算半天假时间请假时间
        Long s = null;
        Long e = null;
        String halfDay = "";
        int halfWorkTime = shiftDef.getWorkTotalTime() / 2;
        // 如果定义了半天时间点，则按这个时间拆分上半天和下半天
        if (BooleanUtils.isTrue(shiftDef.getIsHalfdayTime()) && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() > 0) {
            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                //上半天
                s = lf.getLeave_date() + (shiftDef.getStartTime() * 60);
                e = lf.getLeave_date() + (shiftDef.getHalfdayTime() * 60);
                halfDay = BaseConst.LEAVE_HALF_SHALF_DAY;
                // 上半天(时间) = 定义的时间点 - 上班开始时间
                halfWorkTime = shiftDef.getHalfdayTime() - shiftDef.getStartTime();
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) && shiftDef.getHalfdayTime() >= shiftDef.getNoonRestStart()) {
                    if (shiftDef.getHalfdayTime() > shiftDef.getNoonRestEnd()) {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                    } else {
                        halfWorkTime -= shiftDef.getHalfdayTime() - shiftDef.getNoonRestStart();
                    }
                }
            } else if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                //下半天
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {//跨夜
                    //班次跨夜
                    s = lf.getLeave_date() + (shiftDef.getHalfdayTime() * 60);
                    e = lf.getLeave_date() + (shiftDef.getEndTime() * 60) + 86400;
                } else {
                    s = lf.getLeave_date() + (shiftDef.getHalfdayTime() * 60);
                    e = lf.getLeave_date() + (shiftDef.getEndTime() * 60);
                }
                halfDay = BaseConst.LEAVE_HALF_EHALF_DAY;
                // 下半天(时间) = 下班截止时间 - 定义的时间点 【如果下班截止时间跨夜则设置为次日】
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {//跨夜
                    Integer nextDaytime = shiftDef.getEndTime() + (24 * 60);
                    halfWorkTime = nextDaytime - shiftDef.getHalfdayTime();
                } else {
                    halfWorkTime = shiftDef.getEndTime() - shiftDef.getHalfdayTime();
                }
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) && shiftDef.getHalfdayTime() < shiftDef.getNoonRestEnd()) {
                    if (shiftDef.getHalfdayTime() < shiftDef.getNoonRestStart()) {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                    } else {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getHalfdayTime();
                    }
                }
            }
        } else if (shiftDef.getIsNoonRest()) { //按中午休息时间来拆分
            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                s = lf.getLeave_date() + (shiftDef.getStartTime() * 60);
                e = lf.getLeave_date() + (shiftDef.getNoonRestStart() * 60);
                halfDay = BaseConst.LEAVE_HALF_SHALF_DAY;
                halfWorkTime = shiftDef.getNoonRestStart() - shiftDef.getStartTime();
            } else if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {//跨夜
                    s = lf.getLeave_date() + (shiftDef.getNoonRestEnd() * 60);
                    e = lf.getLeave_date() + (shiftDef.getEndTime() * 60) + 86400;
                } else {
                    s = lf.getLeave_date() + (shiftDef.getNoonRestEnd() * 60);
                    e = lf.getLeave_date() + (shiftDef.getEndTime() * 60);
                }
                halfDay = BaseConst.LEAVE_HALF_EHALF_DAY;
                halfWorkTime = shiftDef.getEndTime() - shiftDef.getNoonRestEnd();
            }
        }
        if (s != null) {
            //判断迟到时间是否在 请假区间内
            if (wa.getRegSigninTime() > s && wa.getRegSigninTime() < e) {
                // 迟到时间 － （签到时间－请假开始时间） ＝ 最终迟到小时数
                long regtime = wa.getRegSigninTime();
                long noonRestTime = 0;
                if (shiftDef.getIsNoonRest()) {
                    long rs = wa.getBelongDate() + (shiftDef.getNoonRestStart() * 60);
                    long re = wa.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
                    if (regtime >= rs && regtime <= re) {
                        // 如果签到时间在休息时间内，则指定regtime 为休息开始时间
                        regtime = rs;
                    } else if (regtime > re) {
                        noonRestTime = re - rs;
                    }
                }
                Float late = wa.getLateTime() - (int) (regtime - s - noonRestTime) / 60;
                if (late < 0) late = 0f;
                wa.setLateTime(late);
            } else if (wa.getRegSigninTime() >= e) {
                //清水定制需求，客户自定义半天上班时间
                Integer time = getQsLeaveTime(halfDay, shiftDef);
                if (time != null) {
                    halfWorkTime = time;
                }
                float remainHours = wa.getLateTime() - halfWorkTime;
                if (remainHours < 0) remainHours = 0;
                wa.setLateTime(remainHours);
            }
        }
    }

    private void deductionLateAndEarly(WaAnalyze wa, int type, EmpLeaveInfo lf) {
        //type 1 有迟到早退  2有迟到  3有早退

        //a.leave_id,a.empid,a.time_slot,a.leave_type_id,b.leave_date,b.start_time,b.end_time,
        //b.time_duration,b.period_type,b.date_type,b.time_unit,b.real_date

        //period_type 1、时间单位为天的整天，9、时间单位为天的非整天，4、时间单位为小时的整天，3、时间单位为小时的非整天
        //time_duration 例如1天,0.5天，或24小时，8.5小时。小时的以分钟为单位
        //time_unit  1天，2小时
        // date_type 1、工作日，2休息日，3法定假日
        EmpShiftInfo shiftDef = lf.getEmpShiftInfo();
        Integer workTotalTime = lf.getEmpShiftInfo().getWorkTotalTime();

        int halfWorkTime = workTotalTime / 2; // 半天的时长等于班次上工作时长的二分之一

        int periodType = lf.getPeriod_type();
        switch (type) {
            case 1:
                // 扣减迟到早退小时数
                if (periodType == 1) {
                    //时间单位为天的整天
//					if(wa.getLateTime() != null && wa.getLateTime() > 0){
//						float fOver = lf.getTotal_time_duration()-wa.getLateTime();
//						if(fOver<0) fOver = 0f;
//						lf.setTime_duration(fOver);
//					}
                    wa.setLateTime(0f);
                    wa.setEarlyTime(0f);
                    wa.setIsExp(0);
                } else if (periodType == 9) {//时间单位为天的非整天
                    // 需要判断是上半天下半天 时间段
                    // 1 先得求出上半天 是几点到几点 下半天是几点到几点  A 代表上半天  P 代表下半天
                    if (lf.getTime_duration() == 1) {
                        if (wa.getLateTime() != null && wa.getLateTime() > 0) wa.setLateTime(0f);
                        if (wa.getEarlyTime() != null && wa.getEarlyTime() > 0) wa.setEarlyTime(0f);
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null) {//杰尼亚-CLOUD-8450
                            decLateAndEarlyForType1PeriodType3(wa, lf, shiftDef);
                        } else {
                            if (StringUtils.isBlank(lf.getShalf_day()) || StringUtils.isBlank(lf.getEhalf_day())) {
                                log.info("case 1 ***************************请假单未设置 上半天 下半的标识会导致计算有误 leaveId=" + lf.getLeave_id());
                            }
                            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                                // 分析出来的上半天上班区间
//							Long s = wa.getBelongDate()+shiftDef.getStartTime()*60;
//							Long e = wa.getBelongDate()+shiftDef.getNoonRestStart()*60;
//							if(wa.getRegSigninTime() >= s && wa.getRegSigninTime() <= e){
//								wa.setLateTime(0);
//							}
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getLateTime() != null && wa.getLateTime() > 0) {
                                    if (wa.getLateTime() > halfWorkTime) {
                                        wa.setLateTime(wa.getLateTime() - halfWorkTime);
                                    } else {
                                        wa.setLateTime(0f);
                                    }
                                }
                            }
                            if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                                //抵扣迟到时长
                                if (wa.getLateTime() > 0) {
                                    decLateForPeriodType9(wa, lf, shiftDef);
                                }
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getEarlyTime() > halfWorkTime) {
                                    wa.setEarlyTime(wa.getEarlyTime() - halfWorkTime);
                                } else {
                                    wa.setEarlyTime(0f);
                                }
                            }
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    wa.setLateTime(0f);
                    wa.setEarlyTime(0f);
                    wa.setIsExp(0);
                } else if (periodType == 3) {
                    //时间单位为小时的非整天
                    decLateAndEarlyForType1PeriodType3(wa, lf, shiftDef);
                }
                break;
            case 2:
                // 扣减迟到小时数
                // 1。判断请假类型
                if (periodType == 1) {
                    //时间单位为天的整天
                    wa.setLateTime(0f);
                } else if (periodType == 9) { // 时间单位为天的非整天
                    // 需要判断是上半天下半天 时间段
                    if (lf.getTime_duration() == 1) {
                        if (wa.getLateTime() != null && wa.getLateTime() > 0) wa.setLateTime(0f);
                        //if(wa.getEarlyTime()!= null && wa.getEarlyTime()>0) wa.setEarlyTime(0);
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null) {//杰尼亚-CLOUD-8450
                            //时间单位为小时的非整天
                            decLateAndEarlyForType2PeriodType3(wa, lf, shiftDef);
                        } else {
                            if (StringUtils.isBlank(lf.getShalf_day()) || StringUtils.isBlank(lf.getEhalf_day())) {
                                log.info("case 2***************************请假单未设置 上半天 下半的标识会导致计算有误 leaveId=" + lf.getLeave_id());
                            }
                            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                                //上半天
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getLateTime() != null && wa.getLateTime() > 0) {
                                    if (wa.getLateTime() > halfWorkTime) {
                                        wa.setLateTime(wa.getLateTime() - halfWorkTime);
                                    } else {
                                        wa.setLateTime(0f);
                                    }
                                }
                            } else if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                                //下半天
                                decLateForPeriodType9(wa, lf, shiftDef);
                            }
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    wa.setLateTime(0f);
                    //wa.setIsExp(0);
                } else if (periodType == 3) {
                    //时间单位为小时的非整天
                    decLateAndEarlyForType2PeriodType3(wa, lf, shiftDef);
                }
                break;
            case 3:
                //扣减早退小时数
                // 1。判断请假类型
                if (periodType == 1) {
                    //时间单位为天的整天
                    wa.setEarlyTime(0f);
                } else if (periodType == 9) {
                    // 需要判断是上半天下半天 时间段
//					if(BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())){
//						Long s = wa.getBelongDate()+shiftDef.getNoonRestEnd()*60;
//						Long e = wa.getBelongDate()+shiftDef.getEndTime()*60;
//						if(wa.getRegSignoffTime() >= s && wa.getRegSignoffTime() <= e){
//							wa.setEarlyTime(0);
//						}
//					}
                    // 需要判断是上半天下半天 时间段
                    if (lf.getTime_duration() == 1) {
                        if (wa.getEarlyTime() != null && wa.getEarlyTime() > 0) wa.setEarlyTime(0f);
                        //if(wa.getEarlyTime()!= null && wa.getEarlyTime()>0) wa.setEarlyTime(0);
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null) {//杰尼亚-CLOUD-8450
                            //时间单位为小时的非整天 就是按小时请的假
                            decLateAndEarlyForType3PeriodType3(wa, lf, shiftDef);
                        } else {
                            if (StringUtils.isBlank(lf.getShalf_day()) || StringUtils.isBlank(lf.getEhalf_day())) {
                                log.info("case 3***************************请假单未设置 上半天 下半的标识会导致计算有误 leaveId=" + lf.getLeave_id());
                            }
                            if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                                //if(wa.getEarlyTime()!= null && wa.getEarlyTime()>0) wa.setEarlyTime(0);
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getEarlyTime() > halfWorkTime) {
                                    wa.setEarlyTime(wa.getEarlyTime() - halfWorkTime);
                                } else {
                                    wa.setEarlyTime(0f);
                                }
                            }
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    wa.setEarlyTime(0f);
                    //wa.setIsExp(0);
                } else if (periodType == 3) {
                    //时间单位为小时的非整天 就是按小时请的假
                    decLateAndEarlyForType3PeriodType3(wa, lf, shiftDef);
                }
                break;
            default:
                break;
        }
    }

    public Integer getQsLeaveTime(String halfDay, EmpShiftInfo shiftDef) {
        //清水上半天休假定制逻辑
        if (halfDay.equals(BaseConst.LEAVE_HALF_SHALF_DAY) && shiftDef.getShiftDefId() != null) {
            WaShiftCustomizedExample customizedExample = new WaShiftCustomizedExample();
            customizedExample.createCriteria().andShiftDefIdEqualTo(shiftDef.getShiftDefId());
            List<WaShiftCustomized> customizeds = waShiftCustomizedMapper.selectByExample(customizedExample);
            if (CollectionUtils.isNotEmpty(customizeds)) {
                WaShiftCustomized customized = customizeds.get(0);
                if (customized.getShalfTime() != null) {
                    //指定班次，请半天的假，上半天假默认时长为4个小时
                    return customized.getShalfTime();
                }
            }
        }
        return null;
    }

    /**
     * 求出是上半天或下半天的时长
     *
     * @param halfDay
     * @param shiftDef
     * @return
     */
    private int getHalfTime(String halfDay, EmpShiftInfo shiftDef, int halfWorkTime) {
        //清水定制逻辑
        Integer time = getQsLeaveTime(halfDay, shiftDef);
        if (time != null) {
            halfWorkTime = time;
            return halfWorkTime;
        }

        // 如果定义了半天时间点，则按这个时间拆分上半天和下半天
        if (BooleanUtils.isTrue(shiftDef.getIsHalfdayTime()) && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() > 0) {
            if (halfDay.equals(BaseConst.LEAVE_HALF_SHALF_DAY)) {
                // 上半天(时间) = 定义的时间点 - 上班开始时间
                halfWorkTime = shiftDef.getHalfdayTime() - shiftDef.getStartTime();
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) && shiftDef.getHalfdayTime() >= shiftDef.getNoonRestStart()) {
                    if (shiftDef.getHalfdayTime() > shiftDef.getNoonRestEnd()) {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                    } else {
                        halfWorkTime -= shiftDef.getHalfdayTime() - shiftDef.getNoonRestStart();
                    }
                }
            } else if (halfDay.equals(BaseConst.LEAVE_HALF_EHALF_DAY)) {
                // 下半天(时间) = 下班截止时间 - 定义的时间点 【如果下班截止时间跨夜则设置为次日】
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {//跨夜
                    Integer nextDaytime = shiftDef.getEndTime() + (24 * 60);
                    halfWorkTime = nextDaytime - shiftDef.getHalfdayTime();
                } else {
                    halfWorkTime = shiftDef.getEndTime() - shiftDef.getHalfdayTime();
                }
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) && shiftDef.getHalfdayTime() < shiftDef.getNoonRestEnd()) {
                    if (shiftDef.getHalfdayTime() < shiftDef.getNoonRestStart()) {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                    } else {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getHalfdayTime();
                    }
                }
            }
        } else if (shiftDef.getIsNoonRest()) { // 或者按 中午休息时间来拆分
            if (halfDay.equals(BaseConst.LEAVE_HALF_SHALF_DAY)) {
                halfWorkTime = shiftDef.getNoonRestStart() - shiftDef.getStartTime();
            } else if (halfDay.equals(BaseConst.LEAVE_HALF_EHALF_DAY)) {
                halfWorkTime = shiftDef.getEndTime() - shiftDef.getNoonRestEnd();
            }
        } else {
            // 以上条件都没有满足，则按工作时长的一半进行拆分
        }
        return halfWorkTime;
    }

    private WaAnalyze existsWaAnalyz(List<WaAnalyze> resultWa, Long empid,
                                     Long leaveDate) {

        if (resultWa != null)
            for (WaAnalyze waAnalyze : resultWa) {
                if (waAnalyze.getEmpid().longValue() == empid.longValue() && waAnalyze.getBelongDate().longValue() == leaveDate.longValue()) {
                    return waAnalyze;
                }
            }
        return null;
    }

    @SuppressWarnings("rawtypes")
    private Map<String, List<EmpLeaveInfo>> getEmpLeaveInfos(List<EmpLeaveInfo> empLeaves, WaAnalyzDTO dto, Map<String, List<EmpLeaveInfo>> empleaveAfterMap) {
        Map<String, List<EmpLeaveInfo>> empleaveMap = new HashMap<>();
        if (empLeaves != null && empLeaves.size() > 0) {
            for (EmpLeaveInfo lf : empLeaves) {
                Long empid = lf.getEmpid();
                //请假时间归属在那一天上，以审批时间为准，如果审批时间小于请假时间以请假时间为准，这个判断在审批时已经判断过
                Long real_date = lf.getReal_date();
                Long leave_date = lf.getLeave_date();
                if (real_date == null) {
                    real_date = leave_date;
                }

                //求每一天对应的班次信息
                EmpShiftInfo empShiftInfo = this.getEmpShiftDefByInfo(empid, null, lf.getLeave_date(), dto);
                lf.setEmpShiftInfo(empShiftInfo);

                //跨夜班休假自动归属到前一天
                String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + dto.getBelongid() + "_KY_LT_AUTOMATICALLY_ATTRIBUTED_TO_PREVIOUSDAY");
                if (StringUtils.isBlank(isOpen)) {
                    isOpen = "1";
                }
                if (real_date.equals(leave_date) && "1".equals(isOpen)) {
                    //先判断休假日期前一天是否为跨夜班，如果前一天为跨夜班并且休假类型为小时，则判断休假但是否归属到前一天，判断条件为：休假时间在前一天上班区间之内
                    if (lf.getPeriod_type() != null && lf.getPeriod_type() == 3) {
                        Long preDay = DateUtil.addDate(lf.getLeave_date() * 1000, -1);
                        EmpShiftInfo preDayShift = this.getEmpShiftDefByInfo(empid, null, preDay, dto);
                        if (preDayShift != null && preDayShift.getDateType() == 1 &&
                                CdWaShiftUtil.checkCrossNight(preDayShift.getStartTime(), preDayShift.getEndTime(), preDayShift.getDateType())) {//跨夜
                            Long leaveStart = lf.getLeave_date() + lf.getStart_time() * 60;
                            Long leaveEnd = lf.getLeave_date() + lf.getEnd_time() * 60;
                            Long shiftStart = preDay + preDayShift.getStartTime() * 60;
                            Long shiftEnd = lf.getLeave_date() + preDayShift.getEndTime() * 60;
                            if (leaveStart >= shiftStart && leaveEnd <= shiftEnd) {
                                real_date = preDay;
                            }
                        }
                    }
                }

                // 1.如果最后审批完成时间大于请假日期 则把请假时间计算在审批完成日期上
                //empleaveAfterMap 返回 审批时间 大于 请假时间的请假记录
                if (real_date > leave_date) {
                    String key = empid + "_" + leave_date;
                    if (empleaveAfterMap.containsKey(key)) {
                        empleaveAfterMap.get(key).add(lf);
                    } else {
                        List<EmpLeaveInfo> list = new ArrayList<>();
                        list.add(lf);
                        empleaveAfterMap.put(key, list);
                    }
                }
                // 以审批时间为key 返回请假记录
                String key = empid + "_" + real_date;
                if (!empleaveMap.containsKey(key)) {
                    List<EmpLeaveInfo> list = new ArrayList<>();
                    list.add(lf);
                    empleaveMap.put(key, list);
                } else {
                    empleaveMap.get(key).add(lf);
                }
            }
        }
        return empleaveMap;
    }

    /**
     * 查询请假所对应的班次,然后根据上班总小时，把天换算成小时数
     *
     * @param empid
     * @param leaveDate
     * @param time_duration
     * @return
     */
    private Float getLeaveHour(Long empid, Long leaveDate,
                               Float time_duration, Integer workTotalTime) {
        if (workTotalTime != null) {
            Float hourWork = workTotalTime * time_duration;
            return hourWork;
        } else {
            Map<String, Object> map = waEmpLeaveMapper.getWorkTimeDetail(empid, leaveDate);
            if (map != null && map.containsKey("work_total_time")) {
                Integer workTotalTime2 = (Integer) map.get("work_total_time");
                if (workTotalTime2 != null) {
                    Float hourWork = Float.valueOf(String.valueOf(workTotalTime2)) * time_duration;
                    return hourWork;
                }
            }
        }
        return 0f;
    }

    private List<WaAnalyze> analyzeResult(Map<String, WaRegisterRecord> singIns, Map<String, WaRegisterRecord> singOffs,
                                          Map<String, WaRegisterRecord> oneSignIns, WaAnalyzDTO dto, Map<String, Object> paramsMap) {
        String noShiftMsg = "缺少对应班次";
        List<WaAnalyze> resultWa = new ArrayList<WaAnalyze>();
        // 根据签到签退数据组合分析结果
        if (singIns != null && singIns.size() > 0) {
            for (Map.Entry<String, WaRegisterRecord> map : singIns.entrySet()) {
                String key = map.getKey();
                WaAnalyze analyze = new WaAnalyze();
                WaRegisterRecord reg = map.getValue();
                Integer regType = reg.getType(); // 用于统计补打卡次数 regType = 6 代表补打卡类型
                Long belongDate = reg.getBelongDate();
                if (belongDate == null) {
                    belongDate = DateUtil.getDateLong(reg.getRegDateTime() * 1000, "yyyy-MM-dd", true);
                }
                analyze.setBelongDate(belongDate);
                analyze.setSigninId(reg.getRecordId());
                analyze.setEmpid(reg.getEmpid());
                analyze.setRegSigninTime(reg.getRegDateTime());
                if (regType != null && regType == 6) analyze.setBdkCount(1); // 如果是补打卡类型则记录一次
                float lateTime = 0;
                EmpShiftInfo shiftdef = getEmpShiftDefByInfo(reg.getEmpid(), reg.getShiftDefId(), reg.getBelongDate(), dto);
                WaAnalyzInfo analyzInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                //在考勤分析中，按天分析内进行如果员工缺少班次信息，则提示员工缺少对应班次，在按月分析内，扩展一列”缺少班次天数”。进行缺少班次数据统计。（KEN）
                if (shiftdef == null || shiftdef.getShiftDefId() == null) {
                    analyze.setIsShift(1);// 是否缺少班次
                    analyze.setErrMsg(noShiftMsg);
                } else {
                    analyze.setIsShift(0);
                }
                if (shiftdef != null && reg.getShiftDefId() == null) {
                    reg.setShiftDefId(shiftdef.getShiftDefId());
                    //更新成正确的
                    waRegisterRecordMapper.updateByPrimaryKeySelective(reg);
                }
                Long regtime = reg.getRegDateTime().longValue();
                if (shiftdef != null) {
                    analyze.setShiftDefId(shiftdef.getShiftDefId());
                }
                // 当是工作日是才计算实际工作小时数，应工作小时数，迟到小时数
                if (shiftdef != null && shiftdef.getDateType() == 1) {
                    // AD-7 20170828 只有当异常是时间异常时才计算迟到小时数
                    Integer resultType = reg.getResultType() == null ? 2 : reg.getResultType();

                    //CLOUD-8623
                    Boolean isFlexibleWorking = false; // 是否按弹性时间分析迟到分钟数
                    if (analyzInfo != null) {
                        isFlexibleWorking = BooleanUtils.toBoolean(analyzInfo.getIs_flexible_working());
                    } else {
                        log.error("analyzeResult.analyzInfo为null");
                    }
//					背景：目前分析迟到时长时如果签到正常就不分析迟到，但是当签到时间属于弹性区间并且当天有请假，应再次分析迟到时长（即使签到时间正常也要再次分析迟到时长）
//					解决方案：增加系统开关：有假并弹性考勤分析迟到时长
                    boolean isAnalyzeLateFlag = false;
                    String calLateTimeFlag = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + dto.getBelongid() + "_ANALYZE_LATETIME_BY_FLEXIBLEWORKINGANDHAVELEAVE");
                    if ("1".equals(calLateTimeFlag)) {
                        Boolean leaveFlag = checkApplyLeaveByDate(dto, shiftdef.getEmpid(), belongDate, isFlexibleWorking, paramsMap);
                        if (leaveFlag && BooleanUtils.isTrue(isFlexibleWorking)) {
                            isAnalyzeLateFlag = true;
                        }
                    }
                    if ((isAnalyzeLateFlag || (resultType == 2 && (reg.getResultDesc() == null || reg.getResultDesc().indexOf("TIME_ERR") != -1)))
                            && shiftdef.getStartTime() != null && isAnalyzelateEarly(analyzInfo)) {
                        // 如果中间有休息时间，迟到时间不包含休息时间 即 如果签到时间在休息时间范围内则迟到时间＝休息开始时间－上班开始时间
                        lateTime = getLateTime(dto, regtime * 1000, shiftdef, isFlexibleWorking, paramsMap);
                        if (lateTime > shiftdef.getWorkTotalTime()) {
                            lateTime = shiftdef.getWorkTotalTime();
                        }
                        //如果迟到则说明异常
                        if (lateTime > 0) {
                            analyze.setIsExp(1);
                        }
                    }
                    analyze.setWorkTime(shiftdef.getWorkTotalTime());
                }
                analyze.setLateTime(lateTime);//迟到小时数

                if (lateTime > 0 && !StringUtils.isBlank(reg.getReason())) {
                    analyze.setErrMsg(reg.getReason());
                }
                boolean isShiftKy = false;
                //  根据时间 去匹配签退数据
                if (singOffs.containsKey(key)) {
                    if (shiftdef != null) {
                        isShiftKy = CdWaShiftUtil.checkCrossNight(shiftdef.getStartTime(), shiftdef.getEndTime(), shiftdef.getDateType());
                        // 如果班次是跨夜的情况
                        if (isShiftKy) {
                            belongDate = DateUtil.addDate(belongDate * 1000, 1);
                        } else if (IntegerUtils.parseInt(shiftdef.getOffDutyEndTime() + "", 0) < IntegerUtils.parseInt(shiftdef.getOffDutyStartTime() + "", 0)) {
                            belongDate = DateUtil.addDate(belongDate * 1000, 1);
                        }
                    } else {
                        log.info("未找到排班记录 empid=" + reg.getEmpid() + ",date=" + reg.getRegDateTime());
                    }
                    WaRegisterRecord reg2 = singOffs.get(key);

                    analyze.setSignoffId(reg2.getRecordId());
                    analyze.setRegSignoffTime(reg2.getRegDateTime());

                    if (reg2.getType() != null && reg2.getType() == 6) {
                        analyze.setBdkCount(analyze.getBdkCount() == null ? 1 : analyze.getBdkCount() + 1); // 如果是补打卡类型则记录一次
                    }
                    float earlyTime = 0;
//					Long regtime2 = reg2.getRegDateTime().longValue();
//					String normaldate2 = reg2.getNormalDate();
                    if (shiftdef != null) {
                        //考勤分析是否计算休息日实际工作时间
                        String isCalRestDay = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + dto.getBelongid() + RedisKeyDefine.IS_ANALYSE_RESTDAY_WORKTIME);
                        if ((shiftdef.getDateType() == 1) || ("1".equals(isCalRestDay) && shiftdef.getDateType() == 2)) {
                            Integer resultType = reg2.getResultType() == null ? 2 : reg2.getResultType();
//						if((resultType == 2 && (reg2.getResultDesc()==null || reg2.getResultDesc().indexOf("TIME_ERR") != -1 )) && shiftdef.getEndTime()!=null && isAnalyzelateEarly(analyzInfo)){
                            if ((resultType == 2 && (reg2.getResultDesc() == null || reg2.getResultDesc().indexOf("TIME_ERR") != -1)) && shiftdef.getEndTime() != null && isAnalyzelateEarly(analyzInfo)) {
                                //计算早退分钟数
                                Boolean isFlexibleWorking = BooleanUtils.toBoolean(analyzInfo.getIs_flexible_working()); // 是否按弹性时间分析迟到分钟数
                                earlyTime = getEarlyTime(dto, analyze, shiftdef, isFlexibleWorking, paramsMap);
                                if (earlyTime > shiftdef.getWorkTotalTime().longValue()) {
                                    earlyTime = shiftdef.getWorkTotalTime();
                                }
                                //如果早退则说明异常
                                if (earlyTime > 0) {
                                    analyze.setIsExp(1);
                                }
                            }
                            //获取实际（有效）工作时长
                            //String isOpenJnyRule = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + dto.getBelongid() + RedisKeyDefine.IS_OPEN_JNY_MEALHOURSDEDUCT_RULE);
                            Float actualWorkTime = calculateActualWorkTime(analyze, shiftdef, analyzInfo, dto.getBelongid());
                            analyze.setActualWorkTime(actualWorkTime);
                        } else if (shiftdef.getDateType() == 3 || shiftdef.getDateType() == 5) {
                            //法定节假日计算实际工作时长
                            Integer actualWorkTime = calculateActualWorkTimeForRestDay(analyze, shiftdef, analyzInfo);
                            actualWorkTime = actualWorkTime < 0 ? 0 : actualWorkTime;
                            analyze.setHolidayWorkTime(actualWorkTime);
                        }
                    }

                    Integer regTime = (analyze.getRegSignoffTime().intValue() - analyze.getRegSigninTime().intValue()) / 60;
                    analyze.setRegisterTime(regTime);

                    analyze.setEarlyTime(earlyTime);
                    if (earlyTime > 0 && !StringUtils.isBlank(reg2.getReason())) {
                        String errmsg = null;
                        if (!StringUtils.isBlank(analyze.getErrMsg())) {
                            errmsg = analyze.getErrMsg() + "     " + reg2.getReason();
                        } else {
                            errmsg = reg2.getReason();
                        }
                        analyze.setErrMsg(errmsg);
                    }
                    //移除map中的签退纪录，剩下的就是只有签退纪录，没有签到的纪录
                    singOffs.remove(key);
                } else {
                    // 员工当天对应的考勤分组及分析分组
                    WaAnalyzInfo empAnyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), belongDate);
                    if (shiftdef != null && shiftdef.getDateType() == 1 && empAnyzeInfo != null) {
                        //打卡记录缺失的情况处理逻辑 0 不处理 2 当旷工
                        if (empAnyzeInfo.getRegister_miss() != null && empAnyzeInfo.getRegister_miss() == 2) {
                            analyze.setIsKg(1);
                            analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                            analyze.setErrMsg(StringUtil.appendStr(analyze.getErrMsg(), "缺失签退记录当旷工处理", "   "));
                            analyze.setLateTime(0f);
                            analyze.setEarlyTime(0f);
                        }
                    }
                }
                resultWa.add(analyze);
            }
        }
        if (singOffs != null && singOffs.size() > 0) {
            for (Map.Entry<String, WaRegisterRecord> map : singOffs.entrySet()) {
                WaAnalyze analyze = new WaAnalyze();
                WaRegisterRecord reg = map.getValue();

                Integer regType = reg.getType();

                Long belongDate = reg.getBelongDate();
                if (belongDate == null) {
                    belongDate = DateUtil.getDateLong(reg.getRegDateTime() * 1000, "yyyy-MM-dd", true);
                }

                analyze.setBelongDate(belongDate);
                analyze.setEmpid(reg.getEmpid());
                analyze.setSignoffId(reg.getRecordId());
                analyze.setRegSignoffTime(reg.getRegDateTime());
                if (regType != null && regType == 6) {
                    analyze.setBdkCount(analyze.getBdkCount() == null ? 1 : analyze.getBdkCount() + 1); // 如果是补打卡类型则记录一次
                }
//				String normaldate = reg.getNormalDate();
                Long regtime = reg.getRegDateTime();
                float earlyTime = 0;
                EmpShiftInfo shiftdef = getEmpShiftDefByInfo(reg.getEmpid(), reg.getShiftDefId(), reg.getBelongDate(), dto);

                WaAnalyzInfo analyzInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());

                if (shiftdef == null || shiftdef.getShiftDefId() == null) {
                    analyze.setIsShift(1);// 是否缺少班次
                    if (!StringUtil.isNullOrTrimEmpty(analyze.getErrMsg()) && !analyze.getErrMsg().contains(noShiftMsg)) {
                        analyze.setErrMsg(noShiftMsg);
                    }
                } else {
                    analyze.setIsShift(0);
                    analyze.setShiftDefId(shiftdef.getShiftDefId());
                    analyze.setWorkTime(shiftdef.getWorkTotalTime());//应工作时间
                }
                // AD-7 20170828 只有当异常是时间异常时才计算早退小时数
                Integer resultType = reg.getResultType() == null ? 2 : reg.getResultType();
                if ((resultType == 2 && (reg.getResultDesc() == null || reg.getResultDesc().indexOf("TIME_ERR") != -1)) && shiftdef != null && shiftdef.getDateType() == 1 && shiftdef.getEndTime() != null && isAnalyzelateEarly(analyzInfo)) {
                    Boolean isFlexibleWorking = BooleanUtils.toBoolean(analyzInfo.getIs_flexible_working()); // 是否按弹性时间分析迟到分钟数
                    //计算早退分钟数
                    earlyTime = getEarlyTime(dto, analyze, shiftdef, isFlexibleWorking, paramsMap);
                    if (earlyTime > shiftdef.getWorkTotalTime()) {
                        earlyTime = shiftdef.getWorkTotalTime();
                    }
                    //如果早退则说明异常
                    if (earlyTime > 0) {
                        analyze.setIsExp(1);

						/*WaAnalyzInfo empAnanl	yzInfo = dto.getEmpWaAnalyz(reg.getEmpid(), belongDate);

						if(anlyzeInfo != null && anlyzeInfo.getAbsent_limit() != null){
							if(Math.round(earlyTime/60) > anlyzeInfo.getAbsent_limit()){
								analyze.setIsKg(1);
								analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
								//analyze.setErrMsg(analyze.getErrMsg()+",早退时间大于"+anlyzeInfo.getAbsent_limit()+"小时算旷工");
							}
						}*/
                    }
                }
                analyze.setEarlyTime(earlyTime);

                if (earlyTime > 0 && !StringUtils.isBlank(reg.getReason())) {
                    analyze.setErrMsg(reg.getReason());
                }
                // 员工当天对应的考勤分组及分析分组
                WaAnalyzInfo empAnyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), belongDate);
                if (empAnyzeInfo != null && shiftdef != null && shiftdef.getDateType() == 1) {
                    //打卡记录缺失的情况处理逻辑 0 不处理 2 当旷工
                    if (empAnyzeInfo.getRegister_miss() != null && empAnyzeInfo.getRegister_miss() == 2) {
                        analyze.setIsKg(1);
                        analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                        analyze.setErrMsg(StringUtil.appendStr(analyze.getErrMsg(), "缺失签到记录当旷工处理", "   "));
                        analyze.setLateTime(0f);
                        analyze.setEarlyTime(0f);
                    }
                }
                resultWa.add(analyze);
            }
        }
        return resultWa;
    }

    private List<WaAnalyze> analyzeOneCardResult(Map<String, WaRegisterRecord> oneSignIns, WaAnalyzDTO dto, Map<String, Object> paramsMap) {
        String noShiftMsg = "缺少对应班次";
        List<WaAnalyze> resultWa = new ArrayList<WaAnalyze>();
        //一次卡逻辑处理
        if (oneSignIns != null && oneSignIns.size() > 0) {
            for (Map.Entry<String, WaRegisterRecord> map : oneSignIns.entrySet()) {
                WaAnalyze analyze = new WaAnalyze();
                WaRegisterRecord reg = map.getValue();

                Integer regType = reg.getType(); // 用于统计补打卡次数 regType = 6 代表补打卡类型

                Long belongDate = reg.getBelongDate();
                if (belongDate == null) {
                    belongDate = DateUtil.getDateLong(reg.getRegDateTime() * 1000, "yyyy-MM-dd", true);
                }

                analyze.setBelongDate(belongDate);
                analyze.setSigninId(reg.getRecordId());
                analyze.setEmpid(reg.getEmpid());
                analyze.setRegSigninTime(reg.getRegDateTime());
                if (regType != null && regType == 6) analyze.setBdkCount(1); // 如果是补打卡类型则记录一次
                EmpShiftInfo shiftdef = getEmpShiftDefByInfo(reg.getEmpid(), reg.getShiftDefId(), reg.getBelongDate(), dto);

                WaAnalyzInfo analyzInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                //在考勤分析中，按天分析内进行如果员工缺少班次信息，则提示员工缺少对应班次，在按月分析内，扩展一列”缺少班次天数”。进行缺少班次数据统计。（KEN）
                if (shiftdef == null || shiftdef.getShiftDefId() == null) {
                    analyze.setIsShift(1);// 是否缺少班次
                    analyze.setErrMsg(noShiftMsg);
                } else {
                    analyze.setIsShift(0);
                }
                if (shiftdef != null && reg.getShiftDefId() == null) {
                    reg.setShiftDefId(shiftdef.getShiftDefId());
                    //更新成正确的
                    waRegisterRecordMapper.updateByPrimaryKeySelective(reg);
                }
                if (shiftdef != null) {
                    analyze.setShiftDefId(shiftdef.getShiftDefId());
                }

                if (shiftdef != null && shiftdef.getDateType() == 1) {
                    analyze.setWorkTime(shiftdef.getWorkTotalTime());
                    String rule = analyzInfo.getClock_rule();
                    if (rule != null) {
                        JSONObject json = JSONObject.parseObject(rule);
                        Object clockRule = json.get("clockRule");
                        Integer startTime;
                        Integer endTime;
                        //班次跨夜或者打卡区间跨夜
                        boolean isKy = CdWaShiftUtil.checkCrossNight(shiftdef.getStartTime(), shiftdef.getEndTime(), shiftdef.getDateType());
                        if (clockRule != null && "1".equals(clockRule.toString())) {
                            startTime = shiftdef.getOnDutyStartTime();
                            endTime = shiftdef.getOffDutyEndTime();
                            if (!isKy) {
                                isKy = shiftdef.getOffDutyStartTime() > shiftdef.getOffDutyEndTime();
                            }
                        } else {
                            startTime = shiftdef.getStartTime();
                            endTime = shiftdef.getEndTime();
                        }
                        boolean flag = isMissClock(analyze, startTime, endTime, isKy);
                        if (flag) {
                            analyze.setActualWorkTime(shiftdef.getWorkTotalTime().floatValue());
                            analyze.setRegisterTime(shiftdef.getWorkTotalTime());
                        } else {
                            analyze.setActualWorkTime(0f);
                            analyze.setRegisterTime(0);
                        }
                    }
                }
                resultWa.add(analyze);
            }
        }
        return resultWa;
    }

    /**
     * 一次卡判断是否缺卡
     *
     * @return
     */
    public boolean isMissClock(WaAnalyze analyze, Integer startTime, Integer endTime, boolean isKy) {
        long s = analyze.getBelongDate() + startTime * 60;
        long e = analyze.getBelongDate() + endTime * 60;
        if (isKy) {
            e = DateUtil.addDate(analyze.getBelongDate() * 1000, 1) + endTime * 60;
        }
        Long regTime = analyze.getRegSigninTime();
        if (regTime >= s && regTime <= e) {
            return true;
        }
        return false;
    }

    /**
     * 计算实际工作时长
     *
     * @param analyze
     * @param empShiftDef
     * @param analyzInfo
     * @return
     */
    private Float calculateActualWorkTime(WaAnalyze analyze, EmpShiftInfo empShiftDef, WaAnalyzInfo analyzInfo, String belongid) {
        Float actualWorkTime = 0f;
        //CLOUD-3848
        Boolean isFlexibleWorking = BooleanUtils.toBoolean(analyzInfo.getIs_flexible_working()); // 是否按弹性时间分析迟到分钟数
        //按照系统标准逻辑计算实际工作时长
        String decByStandardRule = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongid + "_CALCULATE_ACTUALWORKTIME_BYSTANDARDRULE");
        if ("1".equals(decByStandardRule)) {
            isFlexibleWorking = false;
        }
        if (isFlexibleWorking && BooleanUtils.isTrue(empShiftDef.getIsFlexibleWork()) && empShiftDef.getFlexibleWorkType() == 1) {
            //实际工作时长 = 弹性上班开始时间-设置的加班开始时间（7:30-21:30）【为弹性上班开始时间-加班开始时间】这段时间范围内有效的出勤时长(需扣除中午休息时间)
            long start = analyze.getBelongDate() + empShiftDef.getFlexibleOnDutyStartTime() * 60;
            long end = analyze.getBelongDate() + (empShiftDef.getOvertimeStartTime() == null ? empShiftDef.getFlexibleOffDutyEndTime() * 60 : empShiftDef.getOvertimeStartTime() * 60);
            Long start_time = Math.max(analyze.getRegSigninTime(), start);
            Long end_time = Math.min(analyze.getRegSignoffTime(), end);
            long timehour = end_time - start_time;
            if (empShiftDef.getIsNoonRest()) {
                long restStartTime = analyze.getBelongDate() + empShiftDef.getNoonRestStart() * 60;
                long restEndTime = analyze.getBelongDate() + empShiftDef.getNoonRestEnd() * 60;
                if (start_time <= restEndTime && end_time >= restStartTime) {
                    timehour -= Math.min(end_time, restEndTime) - Math.max(start_time, restStartTime);
                }
            }
            // 扣除休息时间段内的时长
            actualWorkTime = BigDecimal.valueOf(timehour).floatValue();
            if (actualWorkTime < 0) {
                actualWorkTime = 0f;
            } else {
                actualWorkTime /= 60f;
            }
        } else {
            // 计算实际工作分钟数
            long s = analyze.getBelongDate() + empShiftDef.getStartTime() * 60;
            long e = analyze.getBelongDate() + empShiftDef.getEndTime() * 60;
            boolean isShiftKy = CdWaShiftUtil.checkCrossNight(empShiftDef.getStartTime(), empShiftDef.getEndTime(), empShiftDef.getDateType());
            if (isShiftKy) {
                e = DateUtil.addDate(empShiftDef.getWorkDate() * 1000, 1) + empShiftDef.getEndTime() * 60;
            }
            Long regtime = analyze.getRegSigninTime();
            Long regtime2 = analyze.getRegSignoffTime();
            if (regtime < s) {
                regtime = s;
            } else if (regtime > e) {
                regtime = e;
            }
            if (regtime2 < s) {
                regtime2 = s;
            } else if (regtime2 > e) {
                regtime2 = e;
            }
            Float workTime = BigDecimal.valueOf((regtime2 - regtime) / 60f).setScale(2, RoundingMode.HALF_DOWN).floatValue();
            if (empShiftDef.getIsNoonRest()) {
                long nstart = analyze.getBelongDate() + empShiftDef.getNoonRestStart() * 60;
                long nend = analyze.getBelongDate() + empShiftDef.getNoonRestEnd() * 60;
                Integer nonetime = 0;
                //当签到时间在休息时间段内的情况，不计算工作时间
                if (regtime >= nstart && regtime2 <= nend) {
                    workTime = 0f;
                } else if (regtime <= nstart && regtime2 >= nend) {// 签到时间小于或等于休息开始时间 并且 签退时间大于等于休息截止时间的情况 时间工作小时数＝签退－签到－中午休息时间
                    nonetime = empShiftDef.getRestTotalTime();// 应减去的休息时间
                } else if (regtime <= nstart && regtime2 > nstart && regtime2 <= nend) {//签退时间在休息区间内的情况  把签退时间赋予为休息开始时间
                    regtime2 = nstart;
                } else if (regtime2 >= nend && regtime > nstart && regtime <= nend) {// 签到时间在休息区间内的情况 把签到时间赋予到休息截止时间
                    regtime = nend;
                } else {
                    nonetime = 0;
                }
                workTime = BigDecimal.valueOf((regtime2 - regtime) / 60f - nonetime).setScale(2, RoundingMode.HALF_DOWN).floatValue();
            }
            // 实际工作分钟数 ＝ 签退－签退时间－中午休息时间
            actualWorkTime = workTime;
            if (actualWorkTime > empShiftDef.getWorkTotalTime())
                actualWorkTime = empShiftDef.getWorkTotalTime().floatValue();
            if (actualWorkTime != null && actualWorkTime < 0) {
                actualWorkTime = 0f;
            }
        }
        return BigDecimal.valueOf(actualWorkTime).setScale(2, RoundingMode.HALF_DOWN).floatValue();
    }

    /**
     * 计算实际工作时长
     *
     * @param analyze
     * @param empShiftDef
     * @param analyzInfo
     * @return
     */
    private Integer calculateActualWorkTimeForRestDay(WaAnalyze analyze, EmpShiftInfo empShiftDef, WaAnalyzInfo analyzInfo) {
        // 计算实际工作分钟数
        Long regtime = analyze.getRegSigninTime();
        Long regtime2 = analyze.getRegSignoffTime();
        Long workTime = (regtime2 - regtime) / 60;

        if (empShiftDef.getIsNoonRest()) {
            long nstart = analyze.getBelongDate() + empShiftDef.getNoonRestStart() * 60;
            long nend = analyze.getBelongDate() + empShiftDef.getNoonRestEnd() * 60;
            Integer nonetime = 0;
            //当签到时间在休息时间段内的情况，不计算工作时间
            if (regtime >= nstart && regtime2 <= nend) {
                return 0;
            } else if (regtime <= nstart && regtime2 >= nend && empShiftDef.getRestTotalTime() != null) {// 签到时间小于或等于休息开始时间 并且 签退时间大于等于休息截止时间的情况 时间工作小时数＝签退－签到－中午休息时间
                nonetime = empShiftDef.getRestTotalTime();// 应减去的休息时间
            } else if (regtime <= nstart && regtime2 > nstart && regtime2 <= nend) {//签退时间在休息区间内的情况  把签退时间赋予为休息开始时间
                regtime2 = nstart;
            } else if (regtime2 >= nend && regtime > nstart && regtime <= nend) {// 签到时间在休息区间内的情况 把签到时间赋予到休息截止时间
                regtime = nend;
            } else {
                nonetime = 0;
            }
            workTime = (regtime2 - regtime) / 60 - nonetime;
        }
        return workTime.intValue();
    }

    /**
     * 判断是否需要进行签到签退分析
     *
     * @param analyzInfo
     * @return
     */
    protected boolean isAnalyzelateEarly(WaAnalyzInfo analyzInfo) {
        if (analyzInfo != null)
            return analyzInfo.getIs_analyze_late_early() == null ? true : analyzInfo.getIs_analyze_late_early();
        return true;
    }

    /**
     * 根据员工id，签到时间，班次 查询 （班次信息 日期类型）
     *
     * @param dto
     * @return
     */
    public EmpShiftInfo getEmpShiftDefByInfo(Long empid, Integer shiftDefId, Long workDate, WaAnalyzDTO dto) {
        EmpShiftInfo shiftdef = null;
        //如果对应的班次不存在，则去查询当前所属班次
        if (shiftDefId == null) {
            shiftdef = dto.getEmpShiftByDate(empid, workDate);
            if (shiftdef != null) {
                return shiftdef;
            }

//            WaShiftDef def = this.getEmpShiftInfo(workDate, empid);
//            if (def != null) {
//                shiftdef = new EmpShiftInfo();
//                BeanUtils.copyProperties(def, shiftdef);
//                shiftdef.setEmpid(empid);
//                shiftdef.setWorkDate(workDate);
//
//                String key = empid + "_" + def.getShiftDefId() + "_" + workDate;
//                dto.getEmpShift().put(key, shiftdef);
//            }
        } else {
            shiftdef = dto.getEmpShift(empid, shiftDefId, workDate);
            if (shiftdef == null) {
                shiftdef = dto.getEmpShiftByDate(empid, workDate);
            }
        }
        return shiftdef;
    }

    /**
     * 根据签到时间 empid 查询员工对应的排班，包括非门店，门店排班-- 废弃
     *
     * @param regTime
     * @param empid
     * @return
     */
    @Deprecated
    public WaShiftDef getEmpShiftInfo(Long regTime, Long empid) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("empid", empid);
        Long belongDate = DateUtil.getDateLong(regTime * 1000, "yyyy-MM-dd", true);
        String date = DateUtil.convertDateTimeToStr(regTime, "yyyy-MM-dd", true);
        Integer ym = Integer.valueOf(date.substring(0, date.lastIndexOf("-")).replaceAll("-", ""));
        Integer day = Integer.valueOf(date.substring(date.lastIndexOf("-") + 1));
        params.put("workDate", belongDate);// 年月日的秒
        params.put("startdate", ym);//年月
        params.put("day", day);//日

        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);

        WaShiftDef shiftdef = null;
        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(empInfo.getBelongOrgId())) {
            shiftdef = remoteSmartWorkTimeService.getEmployeeShiftByDate(empInfo.getBelongOrgId(), empid, belongDate);
            if (shiftdef != null && shiftdef.getDateType() != null && shiftdef.getDateType() == 4) {
                shiftdef.setDateType(1);
            }
        } else {
            List<Map> shiftinfos = waRegisterRecordMapper.getEmpShiftRecord(params);
            if (CollectionUtils.isNotEmpty(shiftinfos) && shiftinfos.size() > 0) {

                Map map = shiftinfos.get(0);
                Integer shift_def_id = (Integer) map.get("shift_def_id");
                if (shift_def_id != null) {
                    shiftdef = new WaShiftDef();
                } else {
                    return shiftdef;
                }
                shiftdef.setShiftDefId(shift_def_id);
                //签到打卡开始时间
                Integer on_duty_start_time = (Integer) map.get("on_duty_start_time");
                shiftdef.setOnDutyStartTime(on_duty_start_time);
                //打卡截止
                Integer on_duty_end_time = (Integer) map.get("on_duty_end_time");
                shiftdef.setOnDutyEndTime(on_duty_end_time);
                //签退打卡开始时间
                Integer off_duty_start_time = (Integer) map.get("off_duty_start_time");
                shiftdef.setOffDutyStartTime(off_duty_start_time);
                Integer off_duty_end_time = (Integer) map.get("off_duty_end_time");
                shiftdef.setOffDutyEndTime(off_duty_end_time);
                //1、工作日，2休息日，3法定假日 4 公司特殊假日
                Integer dateType = (Integer) map.get("date_type");
                //如果是公司特殊假日，则按工作日类型计算
                if (dateType != null && dateType == 4) {
                    dateType = 1;
                }

                shiftdef.setDateType(dateType);
                Integer rest_total_time = (Integer) map.get("rest_total_time");
                shiftdef.setRestTotalTime(rest_total_time);
                Integer work_total_time = (Integer) map.get("work_total_time");
                shiftdef.setWorkTotalTime(work_total_time);
                Integer start_time = (Integer) map.get("start_time");
                shiftdef.setStartTime(start_time);
                Integer end_time = (Integer) map.get("end_time");
                shiftdef.setEndTime(end_time);
                Boolean is_noon_rest = (Boolean) map.get("is_noon_rest");
                shiftdef.setIsNoonRest(is_noon_rest);
                Integer noon_rest_start = (Integer) map.get("noon_rest_start");
                shiftdef.setNoonRestStart(noon_rest_start);
                Integer noon_rest_end = (Integer) map.get("noon_rest_end");
                shiftdef.setNoonRestEnd(noon_rest_end);

                // 是否启用了半天时间定义
                Boolean isHalfdayTime = (Boolean) map.get("isHalfdayTime");
                shiftdef.setIsHalfdayTime(isHalfdayTime);

                Integer halfdayTime = (Integer) map.get("halfdayTime");
                shiftdef.setHalfdayTime(halfdayTime);

                Boolean is_special = (Boolean) map.get("is_special");
                shiftdef.setIsSpecial(is_special);

                Integer special_work_time = (Integer) map.get("special_work_time");
                shiftdef.setSpecialWorkTime(special_work_time);
            }
        }
        return shiftdef;
    }

    /**
     * 获取签到打卡区间类型
     *
     * @param belongDate
     * @param regTime
     * @param shift
     * @return
     */
    public Integer getOnDutyTimeRangeType(Long belongDate, Long regTime, EmpShiftInfo shift) {
        Integer onDutyType = 1;//1 标准时间 2 弹性时间
        Long onDutyEndTime = belongDate + (shift.getOnDutyEndTime() * 60);
        if (regTime <= onDutyEndTime) {
            onDutyType = 1;
        } else {
            onDutyType = 2;
        }
        return onDutyType;
    }

    /**
     * 扣减休息时间
     *
     * @param reBelongDate
     * @param regtime
     * @param shift
     * @param result
     * @return
     */
    public Long decRestTime(Long reBelongDate, Long regtime, EmpShiftInfo shift, Long result) {
        //判断是否在休息时间段内，如在区间范围内或小于了休息开始时间，则扣除中午休息时间
        if (shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {

            Integer noonRestStartMin = shift.getNoonRestStart();
            Integer noonRestEndMin = shift.getNoonRestEnd();
            ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
            noonRestStartMin = restPeriod.getNoonRestStart();
            noonRestEndMin = restPeriod.getNoonRestEnd();

            long restStartTime = reBelongDate + (noonRestStartMin * 60);
            long restEndTime = reBelongDate + (noonRestEndMin * 60);

            if (regtime <= restEndTime || regtime < restStartTime) {
                result -= restEndTime - Math.max(regtime, restStartTime);
            }
        }
        // 扣除休息时间段内的时长
        List<ShiftRestPeriods> restPeriods = shift.getRestPeriods();
        if (CollectionUtils.isNotEmpty(restPeriods)) {
            for (ShiftRestPeriods rest : restPeriods) {

                Integer noonRestStartMin = rest.getNoonRestStart();
                Integer noonRestEndMin = rest.getNoonRestEnd();
                ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
                noonRestStartMin = restPeriod.getNoonRestStart();
                noonRestEndMin = restPeriod.getNoonRestEnd();

                long restStart = reBelongDate + noonRestStartMin * 60;
                long restEnd = reBelongDate + noonRestEndMin * 60;

                if (regtime <= restEnd || regtime < restStart) {
                    long tm = restEnd - Math.max(regtime, restStart);
                    result -= tm;
                }
            }
        }
        return result;
    }

    /**
     * 根据标准打卡区间计算早退时长
     *
     * @param reBelongDate
     * @param regtime
     * @param shift
     * @return
     */
    public Integer getEarlyTimeForStandard(Long reBelongDate, Long regtime, EmpShiftInfo shift) {
        Integer minute = 0;
        //在标准下班开始时间前的签退，以标准下班开始时间为标准计算早退分钟数
        Long offDutyStartTime = reBelongDate + (shift.getOffDutyStartTime() * 60);
        if (regtime < offDutyStartTime) {
            Long result = offDutyStartTime - regtime;
            result = decRestTime(reBelongDate, regtime, shift, result);
            minute = result.intValue();
        }
        return minute;
    }

    /**
     * 根据弹性打卡区间计算早退时长
     *
     * @param reBelongDate
     * @param regtime
     * @param shift
     * @return
     */
    public Integer getEarlyTimeForFlexible(Long reBelongDate, Long regtime, EmpShiftInfo shift) {
        Integer minute = 0;
        //在弹性下班开始时间前的签退，以弹性下班开始时间为标准计算早退分钟数
        Long flexibleOffDutyStartTime = reBelongDate + (shift.getFlexibleOffDutyStartTime() * 60);
        if (regtime < flexibleOffDutyStartTime) {
            Long result = flexibleOffDutyStartTime - regtime;
            result = decRestTime(reBelongDate, regtime, shift, result);
            minute = result.intValue();
        }
        return minute;
    }

    private int getEarlyTime(WaAnalyzDTO dto, WaAnalyze analyze, EmpShiftInfo shift, Boolean isFlexibleWorking, Map<String, Object> paramsMap) {
        Long regtime = analyze.getRegSignoffTime();
//		Calendar c = Calendar.getInstance();
//		c.clear();
//		c.setTimeInMillis(regtime*1000);
//		Integer h = c.get(Calendar.HOUR_OF_DAY);
//		Integer m2 = c.get(Calendar.MINUTE);
//		int regMinute = h*60+m2;

        Long belongDate = shift.getWorkDate();//DateUtil.getDateLong(regtime * 1000, "yyyy-MM-dd", true);
        Long reBelongDate = shift.getWorkDate();
        boolean isShiftKy = CdWaShiftUtil.checkCrossNight(shift.getStartTime(), shift.getEndTime(), shift.getDateType());
        // 如果班次是跨夜的情况
        if (isShiftKy) {
            // 班次跨夜了所以日期加一天
            belongDate = DateUtil.addDate(belongDate * 1000, 1);
        }
        Integer minute = 0;

        //CLOUD-8623
        Boolean leaveFlag = checkApplyLeaveByDate(dto, shift.getEmpid(), reBelongDate, isFlexibleWorking, paramsMap);

        //CLOUD-3848
        if (!leaveFlag && isFlexibleWorking && BooleanUtils.isTrue(shift.getIsFlexibleWork()) && shift.getFlexibleWorkType() == 1) {
            //CLOUD-8623
            //早退分析逻辑
            if ("fujitsu".equals(projectCusIdentity)) {//富士通定制逻辑
                log.info("富士通定制逻辑-早退分析逻辑,getEarlyTimeForFlexible");
                minute = getEarlyTimeForFlexible(reBelongDate, regtime, shift);
            } else {//标准逻辑
                if (analyze.getRegSigninTime() != null) {//有签到
                    //判断签到时间是否在标准区间还是弹性区间
                    Integer onDutyType = getOnDutyTimeRangeType(reBelongDate, analyze.getRegSigninTime(), shift);
                    if (onDutyType == 1) {
                        //签到在标准打卡时间范围内，早退=标准下班打卡开始时间-签退时间
                        log.info("标准逻辑-早退分析逻辑,getEarlyTimeForStandard");
                        minute = getEarlyTimeForStandard(reBelongDate, regtime, shift);
                    } else {
                        //签到在弹性打卡时间范围内，早退=弹性下班打卡开始时间-签退时间
                        //签到时间超出弹性打卡结束时间，早退=弹性下班打卡开始时间-签退时间
                        log.info("标准逻辑-早退分析逻辑,getEarlyTimeForFlexible");
                        minute = getEarlyTimeForFlexible(reBelongDate, regtime, shift);
                    }
                } else {//无签到
                    minute = getEarlyTimeForFlexible(reBelongDate, regtime, shift);
                }
            }
        } else {
            Long yqtsj = (belongDate + (shift.getEndTime() * 60));
            // 如果 签退时间早于 应签退时间 则说明 提前下班了认为是早退了
            // 如果在休息时间范围内签退则早退时间＝0，如果在休息开始时间之前签退 早退时间＝下班时间－签退时间－休息时间
            Integer shiftEnd = reBelongDate.intValue() + (shift.getEndTime() * 60);

            if (BooleanUtils.isTrue(shift.getIsHalfdayTime()) && shift.getHalfdayTime() != null && shift.getHalfdayTime() > 0) {
                Integer halfdayTime = (belongDate.intValue() + (shift.getHalfdayTime() * 60));

                if (BooleanUtils.isTrue(shift.getIsNoonRest()) && shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {//设置了中午休息时间
                    Integer noonStart = reBelongDate.intValue() + (shift.getNoonRestStart() * 60);
                    Integer noonEnd = reBelongDate.intValue() + (shift.getNoonRestEnd() * 60);

                    if (shiftEnd > halfdayTime && shiftEnd > noonEnd) {
                        if (halfdayTime < noonStart) {
                            if (regtime < halfdayTime) {
                                minute = (shiftEnd - regtime.intValue()) - (noonEnd - noonStart);
                            } else if (regtime >= halfdayTime && regtime < noonStart) {
                                minute = (shiftEnd - regtime.intValue()) - (noonEnd - noonStart);
                            } else if (regtime >= noonStart && regtime <= noonEnd) {
                                minute = shiftEnd - noonEnd;
                            } else {
                                if (regtime < yqtsj) {
                                    minute = (yqtsj.intValue() - regtime.intValue());
                                }
                            }
                        } else if (halfdayTime >= noonStart && halfdayTime <= noonEnd) {
                            if (regtime < noonStart) {
                                minute = (shiftEnd - noonEnd) + (noonStart - regtime.intValue());
                            } else if (regtime >= noonStart && regtime <= noonEnd) {
                                minute = shiftEnd - noonEnd;
                            } else {
                                if (regtime < yqtsj) {
                                    minute = (yqtsj.intValue() - regtime.intValue());
                                }
                            }
                        } else {
                            if (regtime >= halfdayTime) {
                                if (regtime < yqtsj) {
                                    minute = (yqtsj.intValue() - regtime.intValue());
                                }
                            } else if (regtime < halfdayTime && regtime >= noonEnd) {
                                minute = shiftEnd - regtime.intValue();
                            } else if (regtime >= noonStart && regtime <= noonEnd) {
                                minute = shiftEnd - noonEnd;
                            } else if (regtime < noonStart) {
                                minute = (shiftEnd - noonEnd) + (noonStart - regtime.intValue());
                            }
                        }
                    }
                } else {
                    if (shiftEnd > halfdayTime) {
                        if (regtime < halfdayTime) {
                            minute = shiftEnd - regtime.intValue();
                        } else {
                            if (regtime < yqtsj) {
                                minute = (yqtsj.intValue() - regtime.intValue());
                            }
                        }
                    }
                }
            } else if (shift.getIsNoonRest()) {
                Integer noonStart = reBelongDate.intValue() + (shift.getNoonRestStart() * 60);
                Integer noonEnd = reBelongDate.intValue() + (shift.getNoonRestEnd() * 60);
                if (shiftEnd > noonEnd) { // 只有当 班次截止时间大于班次休息截止时间时，才需要考虑中午休息时间的情况
                    if (regtime < noonStart) {
                        minute = (shiftEnd - noonEnd) + (noonStart - regtime.intValue());
                    } else if (regtime >= noonStart && regtime <= noonEnd) {
                        minute = shiftEnd - noonEnd;
                    } else {
                        if (regtime < yqtsj) {
                            // 返回早退分钟数
                            minute = (yqtsj.intValue() - regtime.intValue());
                        }
                    }
                }
            } else {
                if (regtime < yqtsj) {
                    // 返回早退分钟数
                    minute = (yqtsj.intValue() - regtime.intValue());
                }
            }
        }
        return minute > 0 ? minute / 60 : 0;
    }

    /**
     * 检查当天是否有请假数据
     *
     * @param dto
     * @param empid
     * @param date
     * @return
     */
    public Boolean checkApplyLeaveByDate(WaAnalyzDTO dto, Long empid, Long date, Boolean isFlexibleWorking, Map<String, Object> params) {
        if (BooleanUtils.isTrue(isFlexibleWorking) && !"fujitsu".equals(projectCusIdentity)) {
            if (dto.getEmpLeaveInfoList() == null) {
                List<Map> empLeaveList = getEmpLeaveList(params);
                dto.setEmpLeaveInfo(getEmpLeaveFilterList(dto, empLeaveList));
            }
            //组合员工的请假单 key ： empid+"_"+leave_date;
            Map<String, List<EmpLeaveInfo>> empleaveAfterMap = new HashMap<>();
            Map<String, List<EmpLeaveInfo>> empleaveMap = getEmpLeaveInfos(dto.getEmpLeaveInfoList(), dto, empleaveAfterMap);
            String key = empid + "_" + date;
            if ((empleaveMap.containsKey(key) && CollectionUtils.isNotEmpty(empleaveMap.get(key))) ||
                    (empleaveAfterMap.containsKey(key) && CollectionUtils.isNotEmpty(empleaveAfterMap.get(key)))) {
                return true;
            }
        }
        return false;
    }

    // 获得迟到时间
    private int getLateTime(WaAnalyzDTO dto, Long regtime, EmpShiftInfo shift, Boolean isFlexibleWorking, Map<String, Object> paramsMap) {
        Calendar c = Calendar.getInstance();
        c.clear();
        c.setTimeInMillis(regtime);
        Integer h = c.get(Calendar.HOUR_OF_DAY);
        Integer m2 = c.get(Calendar.MINUTE);
        int regMinute = h * 60 + m2;

        Long yqdsj = DateUtil.getDateLong(regtime, "yyyy-MM-dd", true);
        yqdsj = (yqdsj + (shift.getStartTime() * 60)) * 1000;
        Long regDate = DateUtil.getDateLong(regtime, "yyyy-MM-dd", true);

        //CLOUD-8623
        Boolean leaveFlag = checkApplyLeaveByDate(dto, shift.getEmpid(), regDate, isFlexibleWorking, paramsMap);

        //CLOUD-3848 考勤分析选择“按弹性时间分析” 并且 班次上也启用了 弹性工作时间”
        if (!leaveFlag && isFlexibleWorking && BooleanUtils.isTrue(shift.getIsFlexibleWork()) && shift.getFlexibleWorkType() == 1) {
//			List<EmpLeaveInfo> leaveInfos = dto.getEmpLeaveInfoByDateEmpId(shift.getEmpid(),shift.getWorkDate());

            Integer restStartTime = shift.getNoonRestStart();
            Integer restEndTime = shift.getNoonRestEnd();
//			List<EmpLeaveInfo> leaveInfos =  dto.getEmpLeaveInfoByDateEmpId(shift.getEmpid(),shift.getWorkDate());

//			超过弹性上班结束时间的签到，以弹性上班结束时间为标准计算迟到分钟数
            if (regMinute > shift.getFlexibleOnDutyEndTime()) {
                Integer result = regMinute - shift.getFlexibleOnDutyEndTime();
                // 判断是否在休息时间段内，如在区间范围内或超过了休息截止时间，则扣除中午休息时间
                if ((regMinute <= restEndTime && regMinute >= restStartTime) || regMinute > restEndTime) {
                    result -= Math.min(regMinute, restEndTime) - restStartTime;
                }

                // 扣除休息时间段内的时长 计算迟到小时暂不考虑扣除以下休息时间段
//				List<ShiftRestPeriods> restPeriods = shift.getRestPeriods();
//				if(CollectionUtils.isNotEmpty(restPeriods)){
//					for (ShiftRestPeriods rest : restPeriods){
//						Integer restStart = rest.getNoonRestStart();
//						Integer restEnd = rest.getNoonRestEnd();
//						if ((regMinute <= restEnd && regMinute >= restStart) || regMinute>restEnd) {
//							Integer tm = Math.min(regMinute,restEnd) - restStart;
//							result -= tm;
//						}
//					}
//				}
                return result;
            }

        } else {
            Integer x = getLateTime(regtime, shift, regMinute, yqdsj);
            if (x != null) return x;
        }
        return 0;
    }

    private Integer getLateTime(Long regtime, EmpShiftInfo shift, int regMinute, Long yqdsj) {
        // 定义了半天时间点
        if (BooleanUtils.isTrue(shift.getIsHalfdayTime()) && shift.getHalfdayTime() != null && shift.getHalfdayTime() > 0) {
            Integer noonRestStart = shift.getNoonRestStart();
            Integer noonRestEnd = shift.getNoonRestEnd();
            Integer halfdayTime = shift.getHalfdayTime();
            Integer startTime = shift.getStartTime();

            if (BooleanUtils.isTrue(shift.getIsNoonRest()) && noonRestStart != null && noonRestEnd != null) {//设置了中午休息时间
                if (halfdayTime < noonRestStart) {
                    if (regMinute > halfdayTime) {
                        if (regMinute <= noonRestStart) {
                            return regMinute - startTime;
                        } else if (regMinute > noonRestStart && regMinute < noonRestEnd) {
                            return noonRestStart - startTime;
                        } else if (regMinute >= noonRestEnd) {
                            return (noonRestStart - startTime) + (regMinute - noonRestEnd);
                        }
                    } else {
                        if (regtime > yqdsj) {
                            return (regtime.intValue() - yqdsj.intValue()) / 1000 / 60;
                        }
                    }
                } else if (halfdayTime >= noonRestStart && halfdayTime <= noonRestEnd) {
                    if (regMinute >= shift.getNoonRestStart() && regMinute <= shift.getNoonRestEnd()) {
                        return noonRestStart - startTime;
                    } else if (regMinute > shift.getNoonRestEnd()) {
                        return (noonRestStart - startTime) + (regMinute - noonRestEnd);
                    } else {
                        if (regtime > yqdsj) {
                            return (regtime.intValue() - yqdsj.intValue()) / 1000 / 60;
                        }
                    }
                } else if (halfdayTime > noonRestEnd) {
                    if (regMinute < halfdayTime) {
                        if (regMinute > noonRestEnd) {
                            return (noonRestStart - startTime) + (regMinute - noonRestEnd);
                        } else if (regMinute >= noonRestStart && regMinute <= noonRestEnd) {
                            return noonRestStart - startTime;
                        } else {
                            if (regtime > yqdsj) {
                                return (regtime.intValue() - yqdsj.intValue()) / 1000 / 60;
                            }
                        }
                    } else {
                        return (noonRestStart - startTime) + (regMinute - noonRestEnd);
                    }
                }
            } else {
                if (regMinute > halfdayTime) {
                    return regMinute - startTime;
                } else {
                    if (regtime > yqdsj) {
                        return (regtime.intValue() - yqdsj.intValue()) / 1000 / 60;
                    }
                }
            }
        } else if (shift.getIsNoonRest()) {
            // 签到时间处在休息时间范围内
            if (regMinute >= shift.getNoonRestStart() && regMinute <= shift.getNoonRestEnd()) {
                return shift.getNoonRestStart() - shift.getStartTime();
            } else if (regMinute > shift.getNoonRestEnd()) {// 签到时间在休息截止时间后应减去休息时间
                return (shift.getNoonRestStart() - shift.getStartTime()) + (regMinute - shift.getNoonRestEnd());
            } else {
                if (regtime > yqdsj) {
                    // 返回迟到分钟数
                    return (regtime.intValue() - yqdsj.intValue()) / 1000 / 60;
                }
            }
        } else {
            //签到时间在应签到时间之后
            if (regtime > yqdsj) {
                // 返回迟到分钟数
                return (regtime.intValue() - yqdsj.intValue()) / 1000 / 60;
            }
        }
        return null;
    }

    // 把list转成Map 把签到时间作为key 时间精确到天
    private Map<String, WaRegisterRecord> generatRegMap(List<Map> list, Map<Long, Object> empidMaps) {
        Map<String, WaRegisterRecord> map = new HashMap<String, WaRegisterRecord>();
        if (list != null && list.size() > 0) {
            for (Map regMap : list) {
                WaRegisterRecord record = new WaRegisterRecord();
                Integer recordId = (Integer) regMap.get("record_id");
                Long empid = (Long) regMap.get("empid");
                Integer shift_def_id = (Integer) regMap.get("shift_def_id");
                Integer type = (Integer) regMap.get("type");
                Integer registerType = (Integer) regMap.get("register_type");
                Integer resultType = (Integer) regMap.get("result_type");
                String resultDesc = (String) regMap.get("result_desc");
                String normalAddr = (String) regMap.get("normal_addr");
                String regAddr = (String) regMap.get("reg_addr");
                String reason = (String) regMap.get("reason");
                String normalDate = (String) regMap.get("normal_date");
                Long regDateTime = (Long) regMap.get("reg_date_time");
                Long belongDate = (Long) regMap.get("belong_date");

                record.setShiftDefId(shift_def_id);
                record.setRecordId(recordId);
                record.setEmpid(empid);
                record.setRegisterType(registerType);
                record.setType(type);
                record.setResultType(resultType);
                record.setResultDesc(resultDesc);
                record.setNormalAddr(normalAddr);
                record.setRegAddr(regAddr);
                record.setReason(reason);
                record.setNormalDate(normalDate);
                record.setRegDateTime(regDateTime);
                record.setBelongDate(belongDate);
                map.put(empid + "_" + belongDate, record);
                empidMaps.put(empid, shift_def_id);
            }
        }
        return map;
    }

    private Map<String, List<WaRegisterRecord>> genEmpOutRegMap(List<Map> list) {
        Map<String, List<WaRegisterRecord>> map = new HashMap<>();
        if (list != null && list.size() > 0) {
            for (Map regMap : list) {
                String province = (String) regMap.get("province");
                String city = (String) regMap.get("city");
                String regAddr = (String) regMap.get("reg_addr");

                if (StringUtils.isEmpty(province) && StringUtils.isEmpty(city) && StringUtils.isEmpty(regAddr)) {
                    continue;
                }

                WaRegisterRecord record = new WaRegisterRecord();
                Integer recordId = (Integer) regMap.get("record_id");
                Long empid = (Long) regMap.get("empid");
                Integer shift_def_id = (Integer) regMap.get("shift_def_id");
                Integer type = (Integer) regMap.get("type");
                Integer registerType = (Integer) regMap.get("register_type");
                Integer resultType = (Integer) regMap.get("result_type");
                String resultDesc = (String) regMap.get("result_desc");
                String normalAddr = (String) regMap.get("normal_addr");
                String reason = (String) regMap.get("reason");
                String normalDate = (String) regMap.get("normal_date");
                Long regDateTime = (Long) regMap.get("reg_date_time");
                Long belongDate = (Long) regMap.get("belong_date");

                record.setShiftDefId(shift_def_id);
                record.setRecordId(recordId);
                record.setEmpid(empid);
                record.setRegisterType(registerType);
                record.setType(type);
                record.setResultType(resultType);
                record.setResultDesc(resultDesc);
                record.setNormalAddr(normalAddr);
                record.setRegAddr(regAddr);
                record.setReason(reason);
                record.setNormalDate(normalDate);
                record.setRegDateTime(regDateTime);
                record.setBelongDate(belongDate);
                record.setProvince(province);
                record.setCity(city);

                List<WaRegisterRecord> regList = map.get(empid + "_" + belongDate);
                if (regList == null) {
                    regList = new ArrayList<>();
                }
                regList.add(record);
                map.put(empid + "_" + belongDate, regList);
            }
        }
        return map;
    }

    @Deprecated
    public void analyzeRegister() {
        SysCorpOrgExample example = new SysCorpOrgExample();
        example.createCriteria().andOrgtype2EqualTo(1).andStatusEqualTo(1);
        long starDate = DateUtil.addDate(DateUtil.getOnlyDate(new Date()) * 1000, -1);
        long endDate = starDate;
        int m = (23 * 60 * 60) + (59 * 60) + 59;
        endDate += m;

        List<SysCorpOrg> belongids = sysCorpOrgMapper.selectByExample(example);
        for (SysCorpOrg sysCorpOrg : belongids) {
            String belongid = sysCorpOrg.getOrgid().toString();
            List<Integer> waSobids = waSobMapper.getWaSobIdByDateRange(belongid, starDate);
            if (CollectionUtils.isNotEmpty(waSobids)) {
                for (Integer wasobid : waSobids) {
                    try {
                        this.asyncRegister(belongid, starDate, endDate, null, wasobid);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                log.info("自动核算 ".concat(sysCorpOrg.getShortname() + " 的考勤记录belongid=").concat(belongid.toString()));
            }
        }
    }

    @Deprecated
    @Async
    public void asyncAnalyzeRegister(String belongid, Long startDate, Long endDate) {
        log.info("考勤分析 start ************************************************* ");
        List<Integer> waSobids = waSobMapper.getWaSobIdByDateRange(belongid, startDate);
        if (CollectionUtils.isNotEmpty(waSobids)) {
            for (Integer wasobid : waSobids) {
                try {
                    this.asyncRegister(belongid, startDate, endDate, null, wasobid);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        log.info("考勤分析 end ************************************************* ");
    }

    public Integer getOtRoundingRule(Double min, boolean isUp, Integer roundMin) {
        double mod = min % roundMin;
        //向上取整
        if (isUp) {
            min = min + (roundMin - mod);
        } else {
            //向下取整
            min = min - mod;
        }
        return min.intValue();
    }

}