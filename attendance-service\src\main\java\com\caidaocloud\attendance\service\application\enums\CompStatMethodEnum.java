package com.caidaocloud.attendance.service.application.enums;

/**
 * 考勤方案-工时规则：综合工时制-统计方式
 */
public enum CompStatMethodEnum {
    STAT_METHOD_1(1, "从每月的考勤周期起始日开始");

    private Integer index;

    private String name;

    CompStatMethodEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (CompStatMethodEnum c : CompStatMethodEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
