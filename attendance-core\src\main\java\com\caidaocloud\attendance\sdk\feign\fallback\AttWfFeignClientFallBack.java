package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.sdk.feign.AttWfFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

/**
 * 假勤-业务流程
 *
 * <AUTHOR>
 * @Date 2024/8/5
 */
@Component
public class AttWfFeignClientFallBack implements AttWfFeignClient {
    @Override
    public Result<WfResponseDto> getWfDetail(String businessKey) throws Exception {
        return Result.fail();
    }
}
