package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 旷工条件
 *
 * <AUTHOR>
 * @Date 2021/3/13
 */
@Data
public class AbsentConditionDto {
    @ApiModelProperty("迟到早退最小时长")
    private String start;
    @ApiModelProperty("迟到早退最大时长")
    private String end;
    @ApiModelProperty("时长单位：1 小时/ 2 天")
    private String durationUnit;
    @ApiModelProperty("旷工时长")
    private String duration;
    @ApiModelProperty("1:迟到2:早退")
    private Integer type;
}
