package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.dto.clock.WaClockSiteDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/26
 */
public interface IClockSiteService {
    void saveClockSite(WaClockSiteDto clockSiteDto);

    void updateClockSite(WaClockSiteDto clockSiteDto);

    void deleteClockSiteById(Long id);

    WaClockSiteDto getClockSiteById(Long Id);

    AttendancePageResult<WaClockSiteDto> getClockSitePageList(AttendanceBasePage basePage, Long corpId, String belongOrgId);

    List<WaClockSiteDto> getClockSiteListByIds(List<Long> ids);
}
