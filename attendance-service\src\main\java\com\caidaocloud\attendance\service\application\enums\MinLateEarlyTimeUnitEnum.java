package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

/**
 * 考勤分析规则（WaParseGroup）：最小迟到早退时长单位：1 秒 2 分钟 3 小时
 */
public enum MinLateEarlyTimeUnitEnum {
    SECOND((short) 1, "秒", AttendanceCodes.SECOND),
    MINUTE((short) 2, "分钟", AttendanceCodes.MINUTE),
    HOUR((short) 3, "小时", AttendanceCodes.HOUR);

    private Short index;
    private String name;
    private Integer code;

    MinLateEarlyTimeUnitEnum(Short index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(Short index) {
        for (MinLateEarlyTimeUnitEnum c : MinLateEarlyTimeUnitEnum.values()) {
            if (c.getIndex().equals(index)) {
                String msg = "";
                try {
                    msg = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(msg)) {
                    return msg;
                }
                return c.name;
            }
        }
        return "";
    }

    public Short getIndex() {
        return index;
    }

    public void setIndex(Short index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
