package com.caidao1.ext.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.caidaocloud.attendance.core.commons.BaseEntity;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-12
 */
@Data
public class SysDynamicColumns extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer dynamicColumnsId;

    private Long corpid;

    /**
     * 用工单位#0 代表通用的
     */
    private String belongOrgId;

    private String columnField;

    private String columnZhName;

    private String columnEnName;

    /**
     * 列的计算逻辑
     */
    private String logicExp;

    private String logicExpTxt;

    /**
     * 参数类型#number,text,date
     */
    private String columnType;

    /**
     * 模块类型#sys_table_reg 外键
     */
    private String tableRegId;

    /**
     * 1 有效 0 无效
     */
    private Integer status;

    /**
     * 创建人
     */
    private Integer crtuser;

    /**
     * 创建时间
     */
    private Long crttime;

    /**
     * 修改人
     */
    private Integer upduser;

    /**
     * 修改时间
     */
    private Long updtime;

    private Boolean isCalculateItem;

    private Boolean isShow;

    private String langCode;

    private Integer sortno;

    private String colRmk;

    @TableField(el = "extra,jdbcType=OTHER,typeHandler=com.caidao1.commons.mybatis.handler.JsonTypeHandler")
    private String extra;

    /*
     * 自定义字段配置
     */
    private String customFieldConfig;

    @Override
    public String toString() {
        return "SysDynamicColumns{" +
                "dynamicColumnsId=" + dynamicColumnsId +
                ", corpid=" + corpid +
                ", belongOrgId=" + belongOrgId +
                ", columnField=" + columnField +
                ", columnZhName=" + columnZhName +
                ", columnEnName=" + columnEnName +
                ", logicExp=" + logicExp +
                ", logicExpTxt=" + logicExpTxt +
                ", columnType=" + columnType +
                ", tableRegId=" + tableRegId +
                ", status=" + status +
                ", crtuser=" + crtuser +
                ", crttime=" + crttime +
                ", upduser=" + upduser +
                ", updtime=" + updtime +
                ", isCalculateItem=" + isCalculateItem +
                ", extra=" + extra +
                ", customFieldConfig=" + customFieldConfig +
                "}";
    }
}
