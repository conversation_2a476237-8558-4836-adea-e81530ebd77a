package com.caidaocloud.attendance.service.application.dto;

import com.caidaocloud.attendance.service.application.enums.HomeLeaveType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 员工假期额度
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmpLeaveQuotaDto {
    private String belongOrgId;
    private Long empId;
    private Integer leaveTypeId;
    private Long quotaRuleId;
    private BigDecimal quotaVal;
    private boolean isCross = false;
    private Map crossParams = new HashMap();
    private Long disCycleStart;
    private Long disCycleEnd;
    private Long hireDate;
    private Long prodeadLine;
    private Long terminationDate;
    private Integer acctTimeType;
    private Integer nowDistributeRule;
    private Integer nowRoundingRule;
    private Integer ifAdvance;
    // 是否跨额度规则 false 不跨 ，true 跨
    private Boolean ifCrossQuota;
    //首个跨额度规则折算日期
    private Long crossQuotaDate;
    //第二个跨额度规则折算日期
    private Long secCrossQuotaDate;
    //跨额度规则时，第一段时间对应的配额
    private Integer lastQuota;
    //跨额度规则时，第二段时间对应的配额
    private Integer nextQuota;
    //跨额度规则时，第三段时间对应的配额
    private Integer thirdQuota;
    //全年额度，记录跨额度规则员工的全年的额度
    private BigDecimal annualQuota;
    //跨维度折算规则 1 生成时折算 2 到期时折算
    private Integer convertRule;
    //工龄对应的配额
    private BigDecimal originalQuotaDay;
    // 计算当前配额的折算日期
    private Long calNowQuotaConvertDate;
    // 员工状态 0 在职 1 离职 2 试用期
    private Integer empStatus;
    //孩子ID
    private Long familyId;
    /**
     * 当年配额
     */
    private Float waEmpQuotaQuotaDay;
    /**
     * QuotaGenRuleDo的configId
     */
    private Long configId;
    /**
     * 是否根据当年配额计算当前配额
     */
    private boolean calNowQuotaByCurrentYearQuota;

    private HomeLeaveType homeLeaveType;

    /**
     * 本年额度发放规则：1 按全年额度发放、2 按入职日比例发放、3 按入职月比例发放
     */
    private Integer quotaDistributeRule;

    /**
     * 本年额度发放规则按照入职月比例发放时的临界日期
     */
    private Integer dayOfHireMonthDist;
}