package com.caidao1.ioc.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 打卡分析DTO（考勤打卡记录导入/接入）
 */
@Data
@Accessors(chain = true)
public class ImportClockAnalyseDto {
    /**
     * 租户ID
     */
    private String belongId;
    /**
     * 是否分析地点异常 (如果分析的打卡数据有地点异常则保留地点异常，暂时不增加地点异常分析逻辑，因为签到接入时，不会导入打卡地点)
     */
    private Boolean localErr;
    /**
     * 是否分析设备异常（如果分析的打卡数据有设备异常则保留设备异常，暂时不增加设备异常分析逻辑，因为签到接入时，不会导入设备号）
     */
    private Boolean deviceErr;
    /**
     * 数据分析类型 1 只分析导入的日期 2 分析所有日期
     */
    private Integer analyzeType;
    /**
     * 时间异常校验类型 1 精确匹配 2 模糊匹配
     */
    private Integer timeCheckType;
    /**
     * 是否分析补打卡数据
     */
    private Boolean includeBdkRecord;
    /**
     * 跨夜签退分析类型 1:当天如果存在签退卡，就不去第二天查找符合条件的签退卡 2 当天如果存在签退卡，也会去第二天查找符合条件的签退卡，如果找到了，则将第二天的卡作为前一天的签退卡
     */
    private Integer kyAnalyzeType;
    /**
     * 抓取跨夜签退过滤类型 1 返回最早的一条 2 返回最晚的一条
     */
    private Integer filterType;

    /**
     * 本次导入的最早打卡日期
     */
    private Long startDate;

    /**
     * 本次导入的最晚打卡日期
     */
    private Long endDate;

    /**
     * 本次导入/接入的员工打卡日期数据：key=本次导入的员工ID，value=此员工本次导入的打卡日期集合
     */
    private Map<Long, List<Long>> empRegBelongDates;
}
