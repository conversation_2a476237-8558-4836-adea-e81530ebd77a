package com.caidaocloud.attendance.service.application.dto.clock;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VerifyResultDto {

    @ApiModelProperty("方案id")
    private Long id;

    @ApiModelProperty("方案名称")
    private String planName;

    @ApiModelProperty("员工id")
    private Long empId;

    @ApiModelProperty("员工名称")
    private String empName;

    @ApiModelProperty("员工工号")
    private String workNo;
}
