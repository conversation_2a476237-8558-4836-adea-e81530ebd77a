package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaShiftApplyRecordDo;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftRecordDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;

import java.util.List;
import java.util.Map;

/**
 * @Author: Jiang<PERSON>
 * @Date: 2022/03/17
 * @Description:
 **/
public interface IWaShiftApplyRecordRepository {

    int save(WaShiftApplyRecordDo waShiftApplyRecordDo);

    List<WaShiftApplyRecordDo> selectList(ApplyShiftRecordDto shiftRecordDto);

    WaShiftApplyRecordDo selectById(Long id);

    int update(WaShiftApplyRecordDo waShiftApplyRecordDo);

    PageResult<WaShiftApplyRecordDo> selectPageList(AttendanceBasePage basePage, Map params);

    PageResult<WaShiftApplyRecordDo> pageListOfPortal(QueryPageBean queryPageBean);

    WaShiftApplyRecordDo queryShiftInfoById(Long id,Long corpId);

    List<WaShiftApplyRecordDo> getEmpShiftChangeApplyList(String tenantId, List<Long> empIds, Long startDate, Long endDate);
}
