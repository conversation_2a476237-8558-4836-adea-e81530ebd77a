package com.caidaocloud.attendance.core.wa.dto.shift;

import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.ResponseWrap;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
public class MyWorkDateShiftDto {
    @ApiModelProperty("日期类型")
    private Integer dateType;
    @ApiModelProperty("开始时间")
    private Integer startTime;
    @ApiModelProperty("开始时间")
    private Integer endTime;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("班次开始时间点，格式HH:mm")
    private String startTimeTxt;
    @ApiModelProperty("班次结束时间点，格式HH:mm")
    private String endTimeTxt;
    @ApiModelProperty("上班打卡开始时间")
    private Integer onDutyStartTime;
    @ApiModelProperty("上班打卡结束时间")
    private Integer onDutyEndTime;
    @ApiModelProperty("下班打卡开始时间")
    private Integer offDutyStartTime;
    @ApiModelProperty("下班打卡结束时间")
    private Integer offDutyEndTime;
    @ApiModelProperty("班次名称")
    private String shiftName;
    @ApiModelProperty("班次编码")
    private String shiftCode;
    private String i18nShiftDefName;
    @ApiModelProperty("是否休息")
    private Boolean isNoonRest;
    @ApiModelProperty("休息开始时间")
    private Integer noonRestStart;
    @ApiModelProperty("休息结束时间")
    private Integer noonRestEnd;
    @ApiModelProperty("休息时长")
    private Integer restTotalTime;
    @ApiModelProperty("工作时长")
    private Integer workTotalTime;
    @ApiModelProperty("班次休息开始时间点，格式HH:mm")
    private String restStartTimeTxt;
    @ApiModelProperty("班次休息结束时间点，格式HH:mm")
    private String restEndTimeTxt;
    @ApiModelProperty("工作时长")
    private String workTotalTimeTxt;
    @ApiModelProperty("班次ID")
    private Integer shiftDefId;
    @ApiModelProperty("上班时间归属标记: 1 当日、2 次日")
    private Integer startTimeBelong;
    @ApiModelProperty("下班时间归属标记: 1 当日、2 次日")
    private Integer endTimeBelong;
    @ApiModelProperty("休息开始时间归属标记: 1 当日、2 次日")
    private Integer noonRestStartBelong;
    @ApiModelProperty("休息结束时间归属标记: 1 当日、2 次日")
    private Integer noonRestEndBelong;
    @ApiModelProperty("多段班工作时间设置信息")
    private List<Map> multiWorkTimes;
    @ApiModelProperty("多段班工作时间")
    private String multiWorkTimeTxt;

    public static void doSetShiftTimeTxt(List<MyWorkDateShiftDto> shiftDtoList) {
        Long nowDate = DateUtil.getOnlyDate();
        shiftDtoList.forEach(shift -> {
            List<MultiWorkTimeBaseDto> multiWorkTimeDtoList = FastjsonUtil.convertList(shift.getMultiWorkTimes(), MultiWorkTimeBaseDto.class);
            if (CollectionUtils.isNotEmpty(multiWorkTimeDtoList) && multiWorkTimeDtoList.size() > 1) {
                List<String> timeTxtList = multiWorkTimeDtoList.stream().map(timeIt -> {
                    String start = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getStartTime() * 60), "HH:mm", true);
                    String end = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getEndTime() * 60), "HH:mm", true);
                    if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getStartTimeBelong())) {
                        start = ShiftTimeBelongTypeEnum.getName(timeIt.getStartTimeBelong()) + start;
                    }
                    if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getEndTimeBelong())) {
                        end = ShiftTimeBelongTypeEnum.getName(timeIt.getEndTimeBelong()) + end;
                    }
                    return String.format("%s - %s", start, end);
                }).collect(Collectors.toList());
                shift.setMultiWorkTimeTxt(StringUtils.join(timeTxtList, "，"));
            } else {
                if (shift.getStartTime() != null && shift.getStartTime() >= 0) {
                    String startTxt = DateUtil.convertDateTimeToStr(nowDate + (shift.getStartTime() * 60), "HH:mm", true);
                    if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shift.getStartTimeBelong())) {
                        startTxt = ShiftTimeBelongTypeEnum.getName(shift.getStartTimeBelong()) + startTxt;
                    }
                    shift.setStartTimeTxt(startTxt);
                }
                if (shift.getEndTime() != null && shift.getEndTime() >= 0) {
                    String endTxt = DateUtil.convertDateTimeToStr(nowDate + (shift.getEndTime() * 60), "HH:mm", true);
                    if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shift.getEndTimeBelong())) {
                        endTxt = ShiftTimeBelongTypeEnum.getName(shift.getEndTimeBelong()) + endTxt;
                    }
                    shift.setEndTimeTxt(endTxt);
                }
                if (StringUtils.isNotBlank(shift.getStartTimeTxt()) && StringUtils.isNotBlank(shift.getEndTimeTxt())) {
                    shift.setMultiWorkTimeTxt(String.format("%s - %s", shift.getStartTimeTxt(), shift.getEndTimeTxt()));
                }
            }
            if (null != shift.getIsNoonRest() && shift.getIsNoonRest()) {
                if (null != shift.getNoonRestStart() && shift.getNoonRestStart() >= 0) {
                    String restStartTxt = DateUtil.convertDateTimeToStr(nowDate + (shift.getNoonRestStart() * 60), "HH:mm", true);
                    if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shift.getNoonRestStartBelong())) {
                        restStartTxt = ShiftTimeBelongTypeEnum.getName(shift.getNoonRestStartBelong()) + restStartTxt;
                    }
                    shift.setRestStartTimeTxt(restStartTxt);
                }
                if (null != shift.getNoonRestEnd() && shift.getNoonRestEnd() >= 0) {
                    String restEndTxt = DateUtil.convertDateTimeToStr(nowDate + (shift.getNoonRestEnd() * 60), "HH:mm", true);
                    if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shift.getNoonRestEndBelong())) {
                        restEndTxt = ShiftTimeBelongTypeEnum.getName(shift.getNoonRestEndBelong()) + restEndTxt;
                    }
                    shift.setRestEndTimeTxt(restEndTxt);
                }
            }

            shift.setWorkTotalTimeTxt(String.format("%s%s",
                    BigDecimal.valueOf(Optional.ofNullable(shift.getWorkTotalTime()).orElse(0))
                            .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString(),
                    ResponseWrap.wrapResult(10032, null).getMsg()));
        });
    }
}
