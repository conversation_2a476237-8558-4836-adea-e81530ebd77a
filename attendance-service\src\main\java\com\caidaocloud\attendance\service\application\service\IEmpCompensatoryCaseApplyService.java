package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.CompensatoryCaseItemDto;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.CompensatoryCaseReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.EmpCompensatoryCaseDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.CompensatoryRevokeDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface IEmpCompensatoryCaseApplyService {

    PageList<CompensatoryCaseItemDto> getEmpCompensatoryCaseList(CompensatoryCaseReqDto dto, PageBean pageBean, String tenantId);

    Result<String> saveApply(EmpCompensatoryCaseDto dto) throws Exception;

    Result<Boolean> revokeCompensatoryApply(CompensatoryRevokeDto dto, UserInfo userInfo);

    Map<Long, BigDecimal> getApprovedOfCompensatoryCase(List<Long> empIdList, Long startDate, Long endDate);
}
