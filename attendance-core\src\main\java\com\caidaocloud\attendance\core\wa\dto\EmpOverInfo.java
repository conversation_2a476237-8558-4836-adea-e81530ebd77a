package com.caidaocloud.attendance.core.wa.dto;

import lombok.Data;

/**
 * 员工每日加班明细
 */
@Data
public class EmpOverInfo {
    /**
     * 员工ID
     */
    private Long empid;

    /**
     * 加班单据ID
     */
    private Integer ot_id;

    /**
     * 加班时长
     */
    private Integer ot_duration;

    /**
     * 开始时间
     */
    private Long start_time;

    /**
     * 结束时间
     */
    private Long end_time;

    /**
     * 加班时长
     */
    private Integer time_duration;

    /**
     * 实际归属日期
     */
    private Long real_date;

    /**
     * 日期类型
     */
    private Integer date_type;

    /**
     * 补偿类型 CompensateTypeEnum
     */
    private Integer compensate_type;

    /**
     * 取整规则
     * 无 按原逻辑计算
     * 1 向下取整15分钟
     * 2 向上取整15分钟
     * 3 向下取整30分钟
     * 4 向上取整30分钟
     */
    private Integer parse_rule;

    /**
     * 租户ID
     */
    private String belongid;

    /**
     * 加班归属日期
     */
    private Long belongDate;

    /**
     * 表示取哪天的签到记录联动分析
     */
    private Long regdate;

    /**
     * 加班明细单据ID
     */
    private Integer ot_detail_id;

    /**
     * 加班类型ID
     */
    private Integer overTypeId;

    /**
     * 是否零点拆分
     */
    private Boolean zeroSplitting;
}
