package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.wa.mybatis.model.WaLeaveType;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDo;
import com.caidaocloud.attendance.service.interfaces.dto.EmpFixLeaveTypeDto;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveTypeReqDto;

import java.util.List;
import java.util.Map;

/**
 * 假期类型设置
 *
 * <AUTHOR>
 * @Date 2021/3/4
 */
public interface ILeaveTypeRepository {
    WaLeaveTypeDo selectById(Integer id);

    List<WaLeaveTypeDo> selectListByIds(List<Integer> ids);

    AttendancePageResult<WaLeaveTypeDo> getWaLeaveTypePageList(LeaveTypeReqDto reqDto);

    int getLeaveTypeCountByCode(String belongOrgId, Integer excludeId, String code);

    List<WaLeaveTypeDo> getLeaveTypeByGroupId(String belongOrgId, Integer waGroupId);

    List<WaLeaveTypeDo> getLeaveTypeBySobId(String belongOrgId, Integer sobId);

    List<WaLeaveTypeDo> getLeaveTypes(String belongOrgId, Integer quotaRestrictionType, Integer quotaType);

    void deleteLeaveTypeByIds(String belongOrgId, List<Integer> ids);

    List<EmpFixLeaveTypeDto> getEmpFixLeaveTypes(Long empid, String belongOrgId);

    List<WaLeaveTypeDo> getLeaveTypesByIds(String belongOrgId, List<Integer> leaveTypeIds);

    WaLeaveTypeDo getLeaveTypeByType(String belongOrgid, Integer leaveType);

    List<Map> getLeaveTypeList(String belongOrgId, Long empId, Long gender, Integer quotaType, Integer cityId);

    List<WaLeaveTypeDo> getLeaveTypesByBelongOrgId(String belongOrgId, Integer quotaType);

    void update(WaLeaveType waLeaveType);

    void deleteLeaveTypeById(String belongOrgId, Integer leaveTypeId);

    void updateLeaveType(String leaveName, Integer leaveType, String belongOrgId);

    int getLeaveTypeCountByName(String belongOrgId, Integer excludeId, String name);

    void updateBatch(List<WaLeaveTypeDo> list);

    List<Map> getEmpLeaveDayTimeByEmpId(Map params);
}
