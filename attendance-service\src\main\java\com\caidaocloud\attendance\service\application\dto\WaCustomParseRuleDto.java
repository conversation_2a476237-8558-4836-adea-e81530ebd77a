package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤报表字段规则表-json字段对象
 *
 * <AUTHOR>
 * @Date 2024/2/5
 */
@Data
public class WaCustomParseRuleDto {
    @ApiModelProperty("表达式: >、<、>=、<=")
    private String opt;

    @ApiModelProperty("单位: 分钟 minute、小时 hour, 默认minute")
    private String unit = "minute";

    @ApiModelProperty("时长")
    private Integer duration;

    @ApiModelProperty("旷工规则类型：1 晚来+早走 、2 晚来/早走")
    private Integer type;
}
