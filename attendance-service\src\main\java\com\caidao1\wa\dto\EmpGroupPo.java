package com.caidao1.wa.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("sys_emp_group")
public class EmpGroupPo {
    @TableId(type = IdType.INPUT)
    private Long empGroupId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 分组名字
     */
    private String groupName;

    /**
     * 分组表达式
     */
    private String expression;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 业务key
     */
    private String businessKey;

    /**
     * 分组类型
     */
    private String groupType;

    /**
     * 分组排序
     */
    private Integer sortNum;

    /**
     * 分组备注
     */
    private String remark;

    /**
     * 是否启用：0 启用，1 禁用，默认 0
     */
    private Integer enabled;

    /**
     * 是否删除：0 正常，1 删除，默认 0
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 修改人
     */
    private Long updateBy;
    /**
     * 修改时间
     */
    private Long updateTime;
}
