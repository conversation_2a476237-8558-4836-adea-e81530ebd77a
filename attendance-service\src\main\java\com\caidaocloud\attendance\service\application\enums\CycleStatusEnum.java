package com.caidaocloud.attendance.service.application.enums;

public enum CycleStatusEnum {

    UNARCHIVED(0, "未封存"),
    ARCHIVED(1, "已封存");

    private Integer index;

    private String name;

    CycleStatusEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (CycleStatusEnum c : CycleStatusEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
