package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.application.dto.EmpShiftRecordDto;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2022/4/1 10:38
 * @Description:
 **/
public interface IEmpInfoService {
    /**
     * 班次以及打卡记录查询
     *
     * @param regDate
     * @param empId
     * @param tenantId
     * @param shiftDefIdList
     * @return
     */
    EmpShiftRecordDto getShiftAndRecords(Long regDate, Long empId, String tenantId, List<Integer> shiftDefIdList);
}
