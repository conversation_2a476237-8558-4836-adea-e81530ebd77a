package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.application.dto.clock.ClockAnalyseFilterAbstractDto;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public interface IClockAnalyseFilterService {

    ConcurrentHashMap<String, IClockAnalyseFilterService> filterTypeManager = new ConcurrentHashMap<>();

    @PostConstruct
    default void register() {
        String type = getFilterType();
        filterTypeManager.put(type, this);
    }

    String getFilterType();

    List<WaRegisterRecordDo> doFilter(ClockAnalyseFilterAbstractDto filterDto);
}
