package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * created by: FoAng
 * create time: 19/3/2024 10:25 上午
 */
@Getter
public enum   SummaryLabelEnum {
    REGISTER_NORMAL_TIME_TOTAL("正常工作日工时","正常工作日工时%s","registerNormalTimeTotal",false,60034),
    REGISTER_TIME_TOTAL("实际出勤总时长","实际出勤总时长%s","registerTimeTotal",false,60035),
    LATE_TIME("迟到时间", "迟到%s", "late_time", false, AttendanceCodes.LATE_TIME),
    LATE_ADJUST_TIME("迟到调整时间", "迟到调整%s", "late_time_adjust", false, AttendanceCodes.LATE_ADJUST_TIME),
    EARLY_TIME("早退时间", "早退%s", "early_time", false, AttendanceCodes.EARLY_TIME),
    EARLY_ADJUST_TIME("早退调整时间", "早退调整%s", "early_time_adjust", false, AttendanceCodes.EARLY_ADJUST_TIME),
    KG_TIME("旷工时长", "旷工%s", "kg_work_time", false, AttendanceCodes.KG_TIME),
    HOLIDAY_TIME("请假时长", "请假(%s)%s", "^lt_\\w+_key$", true, AttendanceCodes.HOLIDAY_TIME),
    TRAVEL_TIME("出差时长", "出差(%s)%s", "^tr_\\w+_key$", true, AttendanceCodes.TRAVEL_TIME),
    OVER_TIME_TIME("加班时长", "加班(%s)%s", "^ot_\\w+_key$", true, AttendanceCodes.OVER_TIME_TIME);

    final String label;
    final String format;
    final String key;
    final boolean regex;
    final Integer code;

    SummaryLabelEnum(String label, String format, String key, boolean regex, Integer code) {
        this.label = label;
        this.format = format;
        this.key = key;
        this.regex = regex;
        this.code = code;
    }

    public static String getFormatBy(SummaryLabelEnum item) {
        if (null == item) {
            return "";
        }
        String i18n = "";
        try {
            i18n = ResponseWrap.wrapResult(item.code, null).getMsg();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtil.isNotBlank(i18n)) {
            return i18n;
        }
        return item.getFormat();
    }

    public static SummaryLabelEnum indexLabel(String label) {
        if (StringUtils.isEmpty(label)) return null;
        for (SummaryLabelEnum value : SummaryLabelEnum.values()) {
            if (value.isRegex()) {
                boolean matchResult = Pattern.compile(value.getKey()).matcher(label).find();
                if (matchResult) {
                    return value;
                }
            } else if (label.equals(value.getKey())) {
                return value;
            }
        }
        return null;
    }
}
