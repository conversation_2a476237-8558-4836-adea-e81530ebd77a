package com.caidao1.integrate.reader;

import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.weibo.api.motan.core.extension.Spi;

import java.util.List;
import java.util.Map;

@Spi
public interface SourceReader {

    List<Map<String, Object>> getSourceResult(Long corpId, String belongId, SysDataInput dataInput, Map<String, Object> mapJson, Map exparams, List<String> logList,Map returnMap) throws Exception;
}