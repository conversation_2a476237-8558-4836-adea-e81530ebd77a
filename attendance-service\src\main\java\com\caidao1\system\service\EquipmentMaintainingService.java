package com.caidao1.system.service;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.mapper.SysIbeaconInfoMapper;
import com.caidao1.system.mybatis.mapper.SysWifiInfoMapper;
import com.caidao1.system.mybatis.model.SysIbeaconInfo;
import com.caidao1.system.mybatis.model.SysWifiInfo;
import com.caidao1.system.mybatis.model.SysWifiInfoExample;
import com.caidao1.system.service.dto.EquipmentMaintainingParam;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.web.ResponseWrap;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by wangwenling on 17-5-27.
 */
@Service
public class EquipmentMaintainingService {

    @Autowired
    private SysIbeaconInfoMapper sysIbeaconInfoMapper;

    @Autowired
    private SysWifiInfoMapper sysWifiInfoMapper;

    @Autowired
    private ISessionService sessionService;

    /**
     * 查询蓝牙设备信息
     *
     * @param param
     * @param pageBean
     * @return
     */
    public List<Map> querySysIbeaconInfo(EquipmentMaintainingParam param, PageBean pageBean) {
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "crttime.desc" : pageBean.getOrder();
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        return sysIbeaconInfoMapper.querySysIbeaconInfo(myPageBounds, param.getBelongOrgId(), param.getUserId(), param.getText_query());
    }

    /**
     * 查询wifi设备信息
     *
     * @param param
     * @param pageBean
     * @return
     */
    public List<Map> querySysWifiInfo(EquipmentMaintainingParam param, PageBean pageBean) {
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "crttime.desc" : pageBean.getOrder();
        MyPageBounds myPageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        return sysWifiInfoMapper.querySysWifiInfo(myPageBounds, param.getBelongOrgId(), param.getUserId(), param.getText_query());
    }

    /**
     * 添加或更新蓝牙设备
     *
     * @param sysIbeaconInfo
     */
    @Transactional
    public void saveIbeaconEquipment(SysIbeaconInfo sysIbeaconInfo) {
        UserInfo userInfo = getUserInfo();
        if (sysIbeaconInfo.getIbeaconId() != null) {//更新
            //判断删除的是否是属于同一个组织
            if (isSameOrg(sysIbeaconInfo)) {
                sysIbeaconInfo.setCrttime(null);
                sysIbeaconInfo.setCrtuser(null);
                sysIbeaconInfo.setUpdtime(System.currentTimeMillis() / 1000);
                sysIbeaconInfo.setUpduser(userInfo.getUserId());
                sysIbeaconInfoMapper.updateByPrimaryKeySelective(sysIbeaconInfo);
            }
        } else {
            sysIbeaconInfo.setIbeaconId(null);
            sysIbeaconInfo.setCrttime(System.currentTimeMillis() / 1000);
            sysIbeaconInfo.setBelongOrgId(userInfo.getTenantId());
            sysIbeaconInfo.setCrtuser(userInfo.getUserId());
            sysIbeaconInfoMapper.insertSelective(sysIbeaconInfo);
        }
    }

    /**
     * 添加或更新wifi设备
     *
     * @param sysWifiInfo
     */
    @Transactional
    public void saveWifiEquipment(SysWifiInfo sysWifiInfo) throws Exception {
        UserInfo userInfo = getUserInfo();
        if (sysWifiInfo.getWifiId() != null) {
            SysWifiInfoExample example = new SysWifiInfoExample();
            example.createCriteria().andBssidEqualTo(sysWifiInfo.getBssid()).andBelongOrgIdEqualTo(userInfo.getTenantId());
            //查询bssid mac地址是否有重复
            List<SysWifiInfo> result = sysWifiInfoMapper.selectByExample(example);
            if (result.size() > 1) {
                throw new CDException(String.format(ResponseWrap.wrapResult(AttendanceCodes.ALREADY_EXIST_SOME_BSSID, null).getMsg(), result.size()));
            }
            sysWifiInfo.setCrttime(null);
            sysWifiInfo.setCrtuser(null);
            sysWifiInfo.setUpduser(userInfo.getUserId());
            sysWifiInfo.setUpdtime(System.currentTimeMillis() / 1000);
            sysWifiInfoMapper.updateByPrimaryKeySelective(sysWifiInfo);
        } else {
            SysWifiInfoExample example = new SysWifiInfoExample();
            example.createCriteria().andBssidEqualTo(sysWifiInfo.getBssid()).andBelongOrgIdEqualTo(userInfo.getTenantId());
            //查询bssid mac地址是否有重复
            List<SysWifiInfo> result = sysWifiInfoMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(result)) {
                //防止手动提交主键
                sysWifiInfo.setWifiId(null);
                sysWifiInfo.setCrttime(System.currentTimeMillis() / 1000);
                sysWifiInfo.setBelongOrgId(userInfo.getTenantId());
                sysWifiInfo.setUpduser(userInfo.getUserId());
                sysWifiInfoMapper.insertSelective(sysWifiInfo);
            } else {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.ALREADY_EXIST_BSSID, null).getMsg());
            }
        }
    }

    /**
     * 删除蓝牙设备
     *
     * @param sysIbeaconInfo
     */
    @Transactional
    public void delIbeaconEquipment(SysIbeaconInfo sysIbeaconInfo) {
        if (sysIbeaconInfo.getIbeaconId() != null) {
            //判断删除的是否是属于同一个组织
            if (isSameOrg(sysIbeaconInfo)) {
                sysIbeaconInfoMapper.deleteByPrimaryKey(sysIbeaconInfo.getIbeaconId());
            }
        }
    }

    /**
     * 删除WiFi设备
     *
     * @param sysWifiInfo
     */
    @Transactional
    public void delWifiEquipment(SysWifiInfo sysWifiInfo) {
        if (sysWifiInfo.getWifiId() != null) {
            //判断删除的是否是属于同一个组织
            if (isSameOrg(sysWifiInfo)) {
                sysWifiInfoMapper.deleteByPrimaryKey(sysWifiInfo.getWifiId());
            }
        }
    }


    /**
     * 校验sysIbeaconInfo信息对应的组织和当前session中的用户为同一组织
     *
     * @param sysIbeaconInfo
     * @return
     */
    private boolean isSameOrg(SysIbeaconInfo sysIbeaconInfo) {
        //获取当前用户的组织id
        SysIbeaconInfo result = sysIbeaconInfoMapper.selectByPrimaryKey(sysIbeaconInfo.getIbeaconId());
        //判断删除的是否是属于同一个组织
        return result != null && Objects.equals(result.getBelongOrgId(), getUserInfo().getTenantId());
    }

    /**
     * 校验sysWifiInfo信息对应的组织和当前session中的用户为同一组织
     *
     * @param sysWifiInfo
     * @return
     */
    private boolean isSameOrg(SysWifiInfo sysWifiInfo) {
        //获取当前用户的组织id
        SysWifiInfo result = sysWifiInfoMapper.selectByPrimaryKey(sysWifiInfo.getWifiId());
        //判断删除的是否是属于同一个组织
        return result != null && Objects.equals(result.getBelongOrgId(), getUserInfo().getTenantId());
    }

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }
}
