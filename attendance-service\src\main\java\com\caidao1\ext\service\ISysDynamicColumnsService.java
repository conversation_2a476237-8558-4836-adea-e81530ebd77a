package com.caidao1.ext.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.caidao1.ext.entity.SysDynamicColumns;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-12
 */
public interface ISysDynamicColumnsService extends IService<SysDynamicColumns> {

    List<SysDynamicColumns> findDynamicColumns(String belongid, String tableRegId);

    List<SysDynamicColumns> findDynamicColumnsByTable(String tableRegId,String... belongid);

    Map getFuncParam2List(String belongOrgId, String tabelRegId);

    List<SysDynamicColumns> getDynamicFieldList(String pageId);

    List<String> getCusFieldListByPageIds(String pageIds);
}
