package com.caidaocloud.attendance.service.application.enums;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/7/15 17:36
 * @Description:
 **/
public enum OvertimeUnitEnum {
    MINUTE("按分钟加班") {
        public float getTime(BigDecimal workingTime, float applyTime, float minOvertimeUnit, int type) {
            return applyTime;
        }
    },
    HALF_HOUR("按半小时加班") {
        public float getTime(BigDecimal workingTime, float applyTime, float minOvertimeUnit, int type) {
            return new BigDecimal(applyTime / 1800L).setScale(0, RoundingMode.DOWN).multiply(new BigDecimal("1800")).longValue();
        }
    },
    HOUR("按小时加班") {
        public float getTime(BigDecimal workingTime, float applyTime, float minOvertimeUnit, int type) {
            if (type == 2) {
                return new BigDecimal(applyTime / (3600 * minOvertimeUnit))
                        .setScale(0, RoundingMode.DOWN)
                        .multiply(new BigDecimal(String.valueOf(minOvertimeUnit)))
                        .multiply(new BigDecimal("3600")).longValue();
            } else {
                return new BigDecimal(applyTime / (60 * minOvertimeUnit))
                        .setScale(0, RoundingMode.DOWN)
                        .multiply(new BigDecimal(String.valueOf(minOvertimeUnit)))
                        .multiply(new BigDecimal("60")).longValue();
            }
        }
    },
    HALF_DAY("按半天加班") {
        public float getTime(BigDecimal workingTime, float applyTime, float minOvertimeUnit, int type) {
            //workingTime一天的标准时长,1800是3600/2的结果
            BigDecimal halfDay = workingTime.multiply(new BigDecimal("1800")).setScale(0, RoundingMode.DOWN);
            return new BigDecimal(applyTime).divide(halfDay, 0, RoundingMode.DOWN).multiply(halfDay).longValue();
        }
    },
    DAY("按天加班") {
        public float getTime(BigDecimal workingTime, float applyTime, float minOvertimeUnit, int type) {
            //workingTime一天的标准时长
            BigDecimal day = workingTime.multiply(new BigDecimal("3600")).setScale(0, RoundingMode.DOWN);
            return new BigDecimal(applyTime).divide(day, 0, RoundingMode.DOWN).multiply(day).longValue();
        }
    };
    private String desc;

    OvertimeUnitEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByOrdinal(int ordinal) {
        for (OvertimeUnitEnum item : OvertimeUnitEnum.values()) {
            if (item.ordinal() == ordinal) {
                return item.getDesc();
            }
        }
        return "";
    }

    public abstract float getTime(BigDecimal workingTime, float applyTime, float minOvertimeUnit, int type);

    public static OvertimeUnitEnum getByOrdinal(int ordinal) {
        for (OvertimeUnitEnum item : OvertimeUnitEnum.values()) {
            if (item.ordinal() == ordinal) {
                return item;
            }
        }
        return OvertimeUnitEnum.MINUTE;
    }
}
