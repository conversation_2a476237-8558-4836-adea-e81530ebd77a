package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.enums.LeaveStatusEnum;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.wa.mybatis.mapper.WaEmpShiftApplyRecordMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpShiftChangeMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.IConfigService;
import com.caidaocloud.attendance.service.application.service.IScheduleQueryService;
import com.caidaocloud.attendance.service.application.service.IWaShiftApplyService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.domain.entity.WaShiftApplyRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftRecordDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ChangeShiftReqDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.SysConfigsEnum;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Author: Jiang.Hanley
 * @Date: 2022/03/17
 * @Description:
 **/
@Slf4j
@Service
public class WaShiftApplyService implements IWaShiftApplyService {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    @Autowired
    private WaEmpShiftChangeMapper waEmpShiftChangeMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaEmpShiftApplyRecordMapper waEmpShiftApplyRecordMapper;
    @Autowired
    private WaShiftApplyRecordDo waShiftApplyRecordDo;
    @Autowired
    private WaSobService waSobService;
    @Autowired
    private IConfigService configService;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    @Lazy
    private IWfService wfService;
    @Autowired
    private IScheduleQueryService scheduleQueryService;

    @Transactional
    @Override
    public Result<String> apply(ApplyShiftDto dto, UserInfo userInfo) throws Exception {
        //1.校验
        Result<String> result = validate(dto, userInfo);
        if (!result.isSuccess()) {
            return result;
        }
        //2.组装数据
        WaShiftApplyRecordDo applyRecordDo = buildRequest(dto, userInfo);
        //3.保存入库
        waShiftApplyRecordDo.save(applyRecordDo);
        //4.发起工作流
        process(applyRecordDo);
        return Result.ok(String.format("%s_%s", applyRecordDo.getRecId(), BusinessCodeEnum.SHIFT.getCode()));
    }

    @Override
    public List<ApplyShiftRecordDto> list(ApplyShiftRecordDto dto) {
        return waShiftApplyRecordDo.list(dto);
    }

    @Override
    public PageResult<ApplyShiftRecordDto> pageList(ChangeShiftReqDto dto, UserInfo userInfo) {
        Map params = new HashMap();
        params.put("belongOrgId", userInfo.getTenantId());
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            params.put("keywords", dto.getKeywords());
        }
        if (dto.getEmpId() != null) {
            params.put("empId", dto.getEmpId());
        }
        String filter = dto.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        params.put("filter", filter);
        params.put("datafilter", dto.getDataScope());
        AttendanceBasePage basePage = ObjectConverter.convert(dto, AttendanceBasePage.class);
        PageResult<WaShiftApplyRecordDo> pageResult = waShiftApplyRecordDo.pageList(basePage, params);
        return convertPage(pageResult);
    }

    public PageResult<ApplyShiftRecordDto> pageListOfPortal(QueryPageBean queryPageBean) {
        PageResult<WaShiftApplyRecordDo> waShiftApplyRecordDoPageResult = waShiftApplyRecordDo.pageListOfPortal(queryPageBean);
        return convertPage(waShiftApplyRecordDoPageResult);
    }

    private PageResult<ApplyShiftRecordDto> convertPage(PageResult<WaShiftApplyRecordDo> pageResult) {
        PageResult<ApplyShiftRecordDto> dtoPageResult = new PageResult<>();
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaShiftApplyRecordDo> doList = pageResult.getItems();
            List<ApplyShiftRecordDto> dtoList = ObjectConverter.convertList(doList, ApplyShiftRecordDto.class);
            for (ApplyShiftRecordDto shiftRecordDto : dtoList) {
                //审批状态
                Integer status = shiftRecordDto.getStatus();
                shiftRecordDto.setStatusName(ApprovalStatusEnum.getName(status));
                shiftRecordDto.setBusinessKey(shiftRecordDto.getRecId() + "_" + BusinessCodeEnum.SHIFT.getCode());
                shiftRecordDto.setOldShift(LangParseUtil.getI18nLanguage(shiftRecordDto.getOldI18nShiftDefName(), shiftRecordDto.getOldShift()));
                shiftRecordDto.setNewShift(LangParseUtil.getI18nLanguage(shiftRecordDto.getNewI18nShiftDefName(), shiftRecordDto.getNewShift()));
            }
            dtoPageResult.setItems(dtoList);
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setTotal(pageResult.getTotal());
        }
        return dtoPageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> revoke(Long recId, String revokeReason, UserInfo userInfo) {
        //调班记录是否存在
        WaShiftApplyRecordDo record = waShiftApplyRecordDo.getById(recId);
        if (null == record) {
            return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_APPLY_NOT_EXIST, Boolean.FALSE);
        }
        LogRecordContext.putVariable("empId", record.getEmpId());
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(record.getStatus())) {
            return ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE);
        }
        // 只允许撤销审批中和审批通过的单据
        if (record.getStatus().intValue() != LeaveStatusEnum.LEAVE_STATUS_1.value && record.getStatus().intValue() != LeaveStatusEnum.LEAVE_STATUS_2.value) {
            return ResponseWrap.wrapResult(AttendanceCodes.RAVEL_REVOKE_NOT_ALLOW, Boolean.FALSE);
        }
        //超过考勤截止日不允许撤销
        Long onlyDate = DateUtil.getOnlyDate();
        Long workDate = record.getWorkDate();
        WaSob waSob = waSobService.getWaSob(record.getEmpId(), workDate);
        if (waSob != null) {
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            if (onlyDate > sobEndDate) {
                Integer sysPeriodMonth = waSob.getSysPeriodMonth();
                //throw new CDException("申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员。");
                throw new CDException(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
            }
        }
        //新工作流
        WfRevokeDto revokeDto = new WfRevokeDto();
        String businessKey = recId + "_" + BusinessCodeEnum.SHIFT.getCode();
        revokeDto.setBusinessKey(businessKey);
        Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
        if (null == result || !result.isSuccess()) {
            //throw new CDException("工作流发起异常，请联系管理员！");
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE).getMsg());
        }
        //1.将调班的班次变更做失效处理
        if (record.getStatus().intValue() == LeaveStatusEnum.LEAVE_STATUS_2.value) {
            WaEmpShiftChangeExample empShiftChangeExample = new WaEmpShiftChangeExample();
            empShiftChangeExample.setOrderByClause("updtime desc");
            empShiftChangeExample.createCriteria().andBelongOrgIdEqualTo(record.getTenantId())
                    .andEmpidEqualTo(record.getEmpId())
                    .andWorkDateEqualTo(record.getWorkDate())
                    .andStatusEqualTo(ApprovalStatusEnum.PASSED.getIndex())
                    .andOldShiftDefIdEqualTo(record.getOldShiftDefId())
                    .andNewShiftDefIdEqualTo(record.getNewShiftDefId());
            List<WaEmpShiftChange> list = waEmpShiftChangeMapper.selectByExample(empShiftChangeExample);
            if (CollectionUtils.isNotEmpty(list)) {
                WaEmpShiftChange waEmpShiftChange = list.get(0);
                waEmpShiftChangeMapper.deleteByPrimaryKey(waEmpShiftChange.getRecId());
                //2.将之前失效的调班恢复成有效
                WaEmpShiftChangeExample example = new WaEmpShiftChangeExample();
                example.createCriteria().andBelongOrgIdEqualTo(record.getTenantId())
                        .andEmpidEqualTo(record.getEmpId())
                        .andWorkDateEqualTo(record.getWorkDate())
                        .andStatusEqualTo(ApprovalStatusEnum.CANCELLATION.getIndex());
                List<WaEmpShiftChange> shiftList = waEmpShiftChangeMapper.selectByExample(example);
                if (shiftList.size() > 0) {
                    WaEmpShiftChange shiftChange = shiftList.get(0);
                    shiftChange.setStatus(ApprovalStatusEnum.PASSED.getIndex());
                    waEmpShiftChangeMapper.updateByPrimaryKeySelective(shiftChange);
                }

            }
        }
        //2.更新单据状态
        record.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
        record.setRevokeReason(revokeReason);
        record.setUpdateBy(userInfo.getUserId());
        record.setUpdateTime(DateUtil.getCurrentTime(true));
        waShiftApplyRecordDo.update(record);

        return Result.ok(Boolean.TRUE);
    }


    /**
     * 数据校验
     *
     * @param dto
     * @return
     */
    public Result<String> validate(ApplyShiftDto dto, UserInfo userInfo) {
        Long date = DateUtil.getTimesampByDateStr2(dto.getDate());
        //请选择调班日期
        if (StringUtils.isBlank(dto.getDate())) {
            return ResponseWrap.wrapResult(AttendanceCodes.NO_SHIFT_ADJUSTMENT_DATE, null);
        }
        //请选择班次
        if (null == dto.getShiftDefId()) {
            return ResponseWrap.wrapResult(AttendanceCodes.NO_SELECT_SHIFT, null);
        }
        //请填写事由
        if (StringUtils.isBlank(dto.getReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.PLEASE_WRITE_REASON, null);
        }

        //考勤截止日判断
        long startDate = DateUtil.getOnlyDate(new Date(date * 1000));
        WaSob waSob = waSobService.getWaSob(userInfo.getStaffId(), startDate);
        if (waSob != null) {
            Integer sysPeriodMonth = waSob.getSysPeriodMonth();
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            Long onlyDate = DateUtil.getOnlyDate();
            if (onlyDate > sobEndDate) {
                //return Result.fail("申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员");
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
            }
        }

        //如同一日期存在审批中的调班，则不可申请该天的调班
        ApplyShiftRecordDto recordDto = new ApplyShiftRecordDto();
        recordDto.setTenantId(userInfo.getTenantId());
        recordDto.setDeleted(0);
        recordDto.setEmpId(userInfo.getStaffId());
        recordDto.setWorkDate(date);
        recordDto.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        List<ApplyShiftRecordDto> list = list(recordDto);
        if (CollectionUtils.isNotEmpty(list)) {
            return ResponseWrap.wrapResult(AttendanceCodes.EXISTING_IN_APPROVAL, null);
        }
        return Result.ok("");
    }

    /**
     * 组装数据
     *
     * @param dto
     * @param userInfo
     * @return
     */
    public WaShiftApplyRecordDo buildRequest(ApplyShiftDto dto, UserInfo userInfo) {
        Long date = DateUtil.getTimesampByDateStr2(dto.getDate());
        List<Long> list = new ArrayList<>();
        list.add(userInfo.getStaffId());
        Integer shiftDefId = 0;
        List<WaShiftDo> empCalendarList = scheduleQueryService.getEmpCalendarShiftList(userInfo.getTenantId(), date, date, list);
        if (CollectionUtils.isNotEmpty(empCalendarList)) {
            WaShiftDo waShiftDo = empCalendarList.get(0);
            shiftDefId = waShiftDo.getShiftDefId();
        }
        long curTime = System.currentTimeMillis() / 1000L;
        WaShiftApplyRecordDo applyRecordDo = new WaShiftApplyRecordDo();
        applyRecordDo.setRecId(snowflakeUtil.createId());
        applyRecordDo.setTenantId(userInfo.getTenantId());
        applyRecordDo.setDeleted(0);
        applyRecordDo.setWorkDate(date);
        applyRecordDo.setEmpId(userInfo.getStaffId());
        applyRecordDo.setOldShiftDefId(shiftDefId);
        applyRecordDo.setNewShiftDefId(dto.getShiftDefId());
        applyRecordDo.setFileId(dto.getFile());
        applyRecordDo.setFileName(dto.getFileName());
        applyRecordDo.setReason(dto.getReason());
        applyRecordDo.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        applyRecordDo.setCreateBy(userInfo.getUserId());
        applyRecordDo.setCreateTime(curTime);
        return applyRecordDo;
    }

    /**
     * 发起工作流
     *
     * @param applyRecordDo
     * @throws Exception
     */
    public void process(WaShiftApplyRecordDo applyRecordDo) throws Exception {
        // 检查流程是否已启用
        Result<Boolean> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.SHIFT.getCode());
        if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
        }
        String businessKey = String.valueOf(applyRecordDo.getRecId());
        Boolean workflowEnabledResultData = checkWorkflowEnableResult.getData();
        if (!workflowEnabledResultData) {
            if (configService.checkSwitchStatus(SysConfigsEnum.ADJUST_SHIFT_WORKFLOW_SWITCH.name())) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
            }
            this.saveShiftApprovalWorkFlow(applyRecordDo.getRecId(), WfCallbackTriggerOperationEnum.APPROVED);
        } else {
            try {
                WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
                wfBeginWorkflowDto.setFuncCode(BusinessCodeEnum.SHIFT.getCode());
                wfBeginWorkflowDto.setBusinessId(businessKey);
                SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(applyRecordDo.getEmpId());
                wfBeginWorkflowDto.setApplicantId(String.valueOf(empInfo.getEmpid()));
                wfBeginWorkflowDto.setApplicantName(empInfo.getEmpName());
                wfBeginWorkflowDto.setEventTime(applyRecordDo.getWorkDate() * 1000);
                Result<?> result = wfRegisterFeign.begin(wfBeginWorkflowDto);
                if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                    if (configService.checkSwitchStatus(SysConfigsEnum.ADJUST_SHIFT_WORKFLOW_SWITCH.name())) {
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
                    }
                    this.saveShiftApprovalWorkFlow(applyRecordDo.getRecId(), WfCallbackTriggerOperationEnum.APPROVED);
                }
            } catch (Exception e) {
                log.error("工作流启动异常，请检查流程配置,{}", e.getMessage(), e);
                if (e instanceof CDException) {
                    if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                        throw new CDException("工作流配置错误，请联系管理员");
                    } else {
                        throw e;
                    }
                } else {
                    throw new CDException("工作流启动异常，请检查流程配置");
                }
            }
        }
    }

    /**
     * 调班申请审批走工作流
     *
     * @param businessKey
     * @param operationEnum
     * @return
     */
    @Override
    public boolean saveShiftApprovalWorkFlow(Long businessKey, WfCallbackTriggerOperationEnum operationEnum) {
        if (operationEnum == WfCallbackTriggerOperationEnum.REVOKE) {
            waEmpShiftApplyRecordMapper.updateShiftApplyStatus(businessKey, ApprovalStatusEnum.CANCELLATION.getIndex(), System.currentTimeMillis() / 1000L);
            return true;
        }
        Integer status = null;
        if (operationEnum == WfCallbackTriggerOperationEnum.APPROVED) {
            status = ApprovalStatusEnum.PASSED.getIndex();
            updateEmpWorkCalendarShift(businessKey);
        } else if (operationEnum == WfCallbackTriggerOperationEnum.REFUSED) {
            status = ApprovalStatusEnum.REJECTED.getIndex();
        }
        waEmpShiftApplyRecordMapper.updateShiftApplyStatus(businessKey, status, System.currentTimeMillis() / 1000L);
        return true;
    }

    /**
     * 调班审批通过后更新班次，并插入一条调整明细
     *
     * @param businessKey
     */
    private void updateEmpWorkCalendarShift(Long businessKey) {
        WaShiftApplyRecord waShiftApplyRecord = waEmpShiftApplyRecordMapper.selectByPrimaryKey(businessKey);
        if (waShiftApplyRecord != null) {
            Long workDate = waShiftApplyRecord.getWorkDate();
            Long empId = waShiftApplyRecord.getEmpId();
            String tenantId = waShiftApplyRecord.getTenantId();
            Integer newShiftDefId = waShiftApplyRecord.getNewShiftDefId();
            String reason = waShiftApplyRecord.getReason();
            Long createBy = waShiftApplyRecord.getCreateBy();
            WaEmpShiftChangeExample empShiftChangeExample = new WaEmpShiftChangeExample();
            empShiftChangeExample.createCriteria().andBelongOrgIdEqualTo(tenantId).andEmpidEqualTo(empId).
                    andWorkDateEqualTo(workDate).andStatusEqualTo(2).andWorkDateEqualTo(workDate);
            //查询老的排班数据
            Integer oldShiftId = null;
            List<WaEmpShiftChange> empShiftChangeList = waEmpShiftChangeMapper.selectByExample(empShiftChangeExample);
            if (CollectionUtils.isNotEmpty(empShiftChangeList)) {
                oldShiftId = empShiftChangeList.get(0).getNewShiftDefId();
            } else {
                Map<Long, WaShiftDef> shiftDefMap = waCommonService.getEmpWorkShift(tenantId, empId, null, workDate, workDate);
                if (MapUtils.isNotEmpty(shiftDefMap)) {
                    oldShiftId = shiftDefMap.get(workDate).getShiftDefId();
                }
            }
            //将之前做的调整失效
            WaEmpShiftChange shiftChange = new WaEmpShiftChange();
            shiftChange.setStatus(4);
            shiftChange.setUpdtime(DateUtil.getCurrentTime(true));
            shiftChange.setUpduser(createBy);
            waEmpShiftChangeMapper.updateByExampleSelective(shiftChange, empShiftChangeExample);
            //添加新的班次记录
            WaEmpShiftChange waEmpShiftChange = new WaEmpShiftChange();
            waEmpShiftChange.setBelongOrgId(tenantId);
            waEmpShiftChange.setStatus(2);
            waEmpShiftChange.setCrttime(DateUtil.getCurrentTime(true));
            waEmpShiftChange.setCrtuser(createBy);
            waEmpShiftChange.setUpduser(createBy);
            waEmpShiftChange.setUpdtime(DateUtil.getCurrentTime(true));
            waEmpShiftChange.setEmpid(empId);
            waEmpShiftChange.setNewShiftDefId(newShiftDefId);
            waEmpShiftChange.setOldShiftDefId(oldShiftId);
            waEmpShiftChange.setWorkDate(workDate);
            waEmpShiftChange.setRemark(reason);
            waEmpShiftChangeMapper.insertSelective(waEmpShiftChange);
        }
    }
}