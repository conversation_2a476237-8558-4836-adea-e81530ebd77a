package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.domain.repository.IWaBatchLeaveRepository;
import com.caidaocloud.dto.UserInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 批量休假
 */
@Data
@Slf4j
@Service
public class WaBatchLeaveDo {
    private Long batchId;

    private Long empid;

    private Integer leaveTypeId;

    private Long startDate;

    private Long endDate;

    private String emergencyContact;

    private String filePath;

    private String fileName;

    private String timeSlot;

    private Float timeDuration;

    private Integer timeUnit;

    private String businessKey;

    private String reason;

    private String extCustomCol;

    private Integer status;

    private Long lastApprovalTime;

    private Long lastEmpid;

    private String revokeReason;

    private Integer revokeStatus;

    private String tenantId;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    private String processCode;

    private String multiLeaveTypeId;

    private String timeDurationTxt;

    @Autowired
    private IWaBatchLeaveRepository waBatchLeaveRepository;

    public WaBatchLeaveDo getById(Long batchId) {
        return waBatchLeaveRepository.getById(batchId);
    }

    public void updateById(WaBatchLeaveDo updateData) {
        waBatchLeaveRepository.updateById(updateData);
    }

    public void save(WaBatchLeaveDo addData) {
        waBatchLeaveRepository.insert(addData);
    }

    public void deleteById(Long batchId) {
        waBatchLeaveRepository.deleteById(batchId);
    }

    public void doInitCrtField() {
        UserInfo userInfo = UserContext.preCheckUser();
        this.setTenantId(userInfo.getTenantId());
        this.setDeleted(0);
        this.setCreateBy(userInfo.getUserId());
        this.setCreateTime(System.currentTimeMillis());
        this.setUpdateBy(this.getCreateBy());
        this.setUpdateTime(this.getCreateTime());
    }
}