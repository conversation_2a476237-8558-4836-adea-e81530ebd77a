package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.domain.repository.ICalendarGroupRepository;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2021/4/12
 */
@Slf4j
@Data
@Service
public class WaCalendarGroupDo {
    private Integer calendarGroupId;
    private String groupName;
    private String belongOrgid;
    private Long crtuser;
    private Long crttime;
    private String i18nGroupName;

    @Autowired
    private ICalendarGroupRepository calendarGroupRepository;

    public PageList<WaCalendarGroupDo> getCalendarGroupPageList(PageBean pageBean, String belongId) {
        return calendarGroupRepository.getCalendarGroupPageList(pageBean, belongId);
    }

    public int delCalendarGroupRelByGroupId(Integer groupId){
        return calendarGroupRepository.delCalendarGroupRelByGroupId(groupId);
    }
}
