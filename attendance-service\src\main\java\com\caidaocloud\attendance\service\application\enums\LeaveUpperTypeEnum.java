package com.caidaocloud.attendance.service.application.enums;

/**
 * 假期设置-上级类型
 */
public enum LeaveUpperTypeEnum {
    LEAVE_TYPE("leaveType", "假期类型"),
    QUOTA_TYPE("quotaType", "额度类型");

    private String code;
    private String name;

    LeaveUpperTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public static LeaveUpperTypeEnum getByCode(String code) {
        for (LeaveUpperTypeEnum c : LeaveUpperTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
