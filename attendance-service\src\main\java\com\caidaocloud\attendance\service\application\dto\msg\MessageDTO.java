package com.caidaocloud.attendance.service.application.dto.msg;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
public class MessageDTO {

    private List<MobileDto> mobile;

    /**
     * 短信内容
     */
    private String content;

    @Data
    @Accessors(chain = true)
    public static class MobileDto {

        /**
         * 手机
         */
        private String mobile;

        /**
         * 号码区域code, 空值默认:86
         */
        private String countryCode;

    }

}
