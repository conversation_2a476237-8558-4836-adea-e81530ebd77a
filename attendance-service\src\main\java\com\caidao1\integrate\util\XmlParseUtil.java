package com.caidao1.integrate.util;

import com.caidao1.commons.utils.DateUtil;
import org.dom4j.*;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;

public class XmlParseUtil {
    private static final Logger logger = LoggerFactory.getLogger(XmlParseUtil.class);

    public static Map<String,Object> xmlTomap(File file) throws Exception{
        // 创建xml解析器
        SAXReader saxReader = new SAXReader();
        // 加载文件,读取到document中
        Document document = saxReader.read(new FileInputStream(file));
        // 通过document对象获取根元素的信息
        Element rootEle = document.getRootElement();

        logger.info("根结点："+rootEle.getName());

        Map<String,Object> mapXml = new HashMap<>();

        logger.info("递归解析xml开始："+ DateUtil.getCurTimeStamp());
        elementToMap2(rootEle,mapXml);
        logger.info("递归解析xml结束："+ DateUtil.getCurTimeStamp());

        return mapXml;
    }

    /***
     * XmlToMap核心方法，里面有递归调用
     */
    private static Map<String, Object> elementToMap(Element outele, Map<String, Object> outmap) {
        List<Element> list = outele.elements();
        int size = list.size();
        if (size == 0) {
            outmap.put(outele.getName(), outele.getTextTrim());
        } else {
            Map<String, Object> innermap = new HashMap<>();
            for (Element ele1 : list) {
                String eleName = ele1.getName();
                Object obj = innermap.get(eleName);
                if (obj == null) {
                    elementToMap(ele1, innermap);
                } else {
                    if (obj instanceof java.util.Map) {
                        List<Map<String, Object>> list1 = new ArrayList<>();
                        list1.add((Map<String, Object>) innermap.remove(eleName));
                        elementToMap(ele1, innermap);
                        list1.add((Map<String, Object>) innermap.remove(eleName));
                        innermap.put(eleName, list1);
                    } else {
                        elementToMap(ele1, innermap);
                        ((List<Map<String, Object>>) obj).add(innermap);
                    }
                }
            }
            outmap.put(outele.getName(), innermap);
        }
        return outmap;
    }

    private static void elementToMap2(Element elmt, Map<String, Object> map) {
        if (null == elmt) {
            return;
        }
        String name = elmt.getName();
        logger.debug("递归解析xml节点："+ name);
        name = name.replaceAll("_","").toLowerCase();
        if(elmt.attributeCount() > 0){
            // 获取属性集合迭代器
            Iterator<Attribute> it = elmt.attributeIterator();
            while (it.hasNext()) {
                Attribute attr = it.next();
                map.put(name + toUpperCaseFirstOne(attr.getName()),attr.getValue());
            }
        }

        if (elmt.isTextOnly()) {
            map.put(name, elmt.getText());
        } else {
            Map<String, Object> mapSub = new HashMap<>();
            List<Element> elements = (List<Element>) elmt.elements();
            for (Element elmtSub : elements) {
                elementToMap2(elmtSub, mapSub);
            }
            Object first = map.get(name);
            if (null == first) {
                map.put(name, mapSub);
            } else {
                if (first instanceof List<?>) {
                    ((List) first).add(mapSub);
                } else {
                    List<Object> listSub = new ArrayList<>();
                    listSub.add(first);
                    listSub.add(mapSub);
                    map.put(name, listSub);
                }
            }
        }
    }


    /**
     * 使用递归调用将多层级xml转为map
     * @param map
     * @param rootElement
     */
    private static void elementToMap3(Element rootElement,Map<String, Object> map) {
        //获得当前节点的子节点
        List<Element> elements = rootElement.elements();
        if (elements.size() == 0) {
            //没有子节点说明当前节点是叶子节点，直接取值
            map.put(rootElement.getName(),rootElement.getText());
            if(rootElement.attributeCount() > 0){
                // 获取属性集合迭代器
                Iterator<Attribute> it = rootElement.attributeIterator();
                while (it.hasNext()) {
                    Attribute attr = it.next();
                    map.put(attr.getName(),attr.getValue());
                }
            }
        }else if (elements.size() == 1) {
            //只有一个子节点说明不用考虑list的情况，继续递归
            Map<String,Object> tempMap = new HashMap<String,Object>();
            elementToMap3(elements.get(0),tempMap);
            map.put(rootElement.getName(),tempMap);
            if(rootElement.attributeCount() > 0){
                // 获取属性集合迭代器
                Iterator<Attribute> it = rootElement.attributeIterator();
                while (it.hasNext()) {
                    Attribute attr = it.next();
                    map.put(attr.getName(),attr.getValue());
                }
            }
        }else {
            //多个子节点的话就要考虑list的情况了，特别是当多个子节点有名称相同的字段时
            Map<String,Object> tempMap = new HashMap<>();
            for (Element element : elements) {
                tempMap.put(element.getName(),null);
            }
            Set<String> keySet = tempMap.keySet();
            for (String string : keySet) {
                Namespace namespace = elements.get(0).getNamespace();
                List<Element> sameElements = rootElement.elements(new QName(string,namespace));
                //如果同名的数目大于1则表示要构建list
                if (sameElements.size() > 1) {
                    List<Map> list = new ArrayList<Map>();
                    for(Element element : sameElements){
                        Map<String,Object> sameTempMap = new HashMap<>();
                        elementToMap3(element,sameTempMap);
                        list.add(sameTempMap);
                    }
                    map.put(string,list);
                }else {
                    //同名的数量不大于1直接递归
                    Map<String,Object> sameTempMap = new HashMap<>();
                    elementToMap3(sameElements.get(0),sameTempMap);
                    map.put(string,sameTempMap);
                }
            }
        }
    }

    //首字母转大写
    public static String toUpperCaseFirstOne(String s){
        return (new StringBuilder()).append(Character.toUpperCase(s.charAt(0))).append(s.substring(1).toLowerCase()).toString();
    }
    public static void main(String[] args) throws Exception {
//        File file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "templates/xml/RSP_China_PI_Out_09_22_2019_1175.xml");
//        Map dataMap = XmlParseUtil.xmlTomap(file);
//        String jsonXml = JSONObject.fromObject(dataMap).toString();
//        logger.debug("Json >>> " + jsonXml);

    }
}
