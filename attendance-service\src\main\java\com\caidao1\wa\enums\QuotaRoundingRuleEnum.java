package com.caidao1.wa.enums;

public enum QuotaRoundingRuleEnum {

    //1 四舍五入保留整数、2 向上取整1、3 向下取整1、4 向上取整0.5、5 向下取整0.5
    ROUND_TO_RESERVED_INTEGER(1, "四舍五入保留整数"),
    ROUND_UP_1(2, "向上取整1"),
    ROUND_DOWN_1(3, "向下取整1"),
    ROUND_UP_HALF(4, "向上取整0.5"),
    ROUND_DOW_HALF(5, "向下取整0.5");

    private Integer index;
    private String name;

    QuotaRoundingRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (QuotaRoundingRuleEnum c : QuotaRoundingRuleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
