package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.IWorkRoundService;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.service.domain.entity.WaWorkRoundDo;
import com.caidaocloud.attendance.service.domain.entity.WaWorkRoundShiftDo;
import com.caidaocloud.attendance.service.domain.entity.WaWorktimeDo;
import com.caidaocloud.attendance.service.domain.service.WorkRoundDomainService;
import com.caidaocloud.attendance.service.domain.service.WorkRoundShiftDomainService;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.interfaces.dto.shift.RoundShiftDto;
import com.caidaocloud.attendance.service.interfaces.dto.WorkRoundDto;
import com.caidaocloud.attendance.service.interfaces.dto.WorkRoundGridDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/8
 */
@Slf4j
@Service
public class WorkRoundServiceImpl implements IWorkRoundService {
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WorkRoundDomainService workRoundDomainService;
    @Autowired
    private WorkRoundShiftDomainService workRoundShiftDomainService;
    @Autowired
    private WaWorktimeDo waWorktimeDo;
    @Autowired
    private WaWorkRoundDo waWorkRoundDo;
    @Autowired
    private WaWorkRoundShiftDo waWorkRoundShiftDo;
    @Autowired
    private ISessionService sessionService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    /**
     * 排班周期计划分页列表查询
     *
     * @param pageBean
     * @return
     */
    @Override
    public AttendancePageResult<WorkRoundGridDto> getWorkRoundPageList(PageBean pageBean) {
        UserInfo userInfo = this.getUserInfo();
        PageList<WaWorkRoundDo> roundPageList = waWorkRoundDo.getWorkRoundPageList(pageBean, userInfo.getTenantId());
        if (CollectionUtils.isNotEmpty(roundPageList)) {
            roundPageList.forEach(r -> {
                r.setRoundName(LangParseUtil.getI18nLanguage(r.getI18nRoundName(), r.getRoundName()));
            });
            List<WorkRoundGridDto> list = ObjectConverter.convertList(roundPageList, WorkRoundGridDto.class);
            list.forEach(row -> {
                List<String> shiftNameList = new ArrayList<>();
                List<Map> shiftList = waConfigService.getWorkRoundShiftList(new PageBean(true), row.getWorkRoundId());
                if (CollectionUtils.isNotEmpty(shiftList)) {
                    shiftList.sort(Comparator.comparing(o -> Integer.valueOf(o.get("round_no").toString())));
                    shiftList.forEach(shift -> shiftNameList.add((String) shift.get("shift_def_name")));
                }
                row.setSchedulingCycle(StringUtils.join(shiftNameList, ","));
            });
            return new AttendancePageResult<>(list, roundPageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        }
        return new AttendancePageResult<>(new ArrayList<>(), roundPageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
    }

    /**
     * 查询排班计划详情
     *
     * @param workRoundId
     * @return
     */
    @Override
    public WorkRoundDto getRounfShiftInfoByWorkRoundId(String belongId, Integer workRoundId) {
        WorkRoundDto workRoundDto = new WorkRoundDto();
        workRoundDto.setWorkRoundId(workRoundId);
        WaWorkRoundDo workRound = workRoundDomainService.selectById(workRoundId);
        if (workRound != null) {
            workRoundDto.setRoundName(workRound.getRoundName());
            if (StringUtils.isNotBlank(workRound.getI18nRoundName())) {
                workRoundDto.setI18nRoundName(FastjsonUtil.toObject(workRound.getI18nRoundName(), Map.class));
            } else if (StringUtils.isNotBlank(workRound.getRoundName())) {
                Map<String, String> i18nName = new HashMap<>();
                i18nName.put("default", workRound.getRoundName());
                workRoundDto.setI18nRoundName(i18nName);
            }
            List<RoundShiftDto> shiftDtos = new ArrayList<>();
            List<WaWorkRoundShiftDo> roundShiftList = workRoundShiftDomainService.selectListByWorkRoundId(workRoundId);
            if (CollectionUtils.isNotEmpty(roundShiftList)) {
                Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongId);
                if (MapUtils.isNotEmpty(shiftDefMap)) {
                    roundShiftList.forEach(shift -> {
                        if (shiftDefMap.get(shift.getShiftDefId()) != null) {
                            RoundShiftDto shiftDto = new RoundShiftDto();
                            shiftDto.setRoundNo(shift.getRoundNo());
                            shiftDto.setShiftDefId(shift.getShiftDefId());
                            shiftDto.setShiftDefName(shiftDefMap.get(shift.getShiftDefId()).getShiftDefName());
                            shiftDtos.add(shiftDto);
                        }
                    });
                }
            }
            workRoundDto.setRoundShiftList(shiftDtos);
        }
        return workRoundDto;
    }

    /**
     * 保存或修改排班计划
     *
     * @param workRoundDto
     * @return
     */
    @Override
    public Integer saveOrUpdateWorkRoundShift(WorkRoundDto workRoundDto) throws Exception {
        UserInfo userInfo = this.getUserInfo();
        workRoundDto.setCrtuser(userInfo.getUserId());
        workRoundDto.setBelongOrgid(userInfo.getTenantId());
        //班次名称重复校验
        int count = waWorkRoundDo.getRoundCountCountByName(userInfo.getTenantId(), workRoundDto.getWorkRoundId(), workRoundDto.getRoundName());
        if (count > 0) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NAME_DUPLICATED, null).getMsg());
        }
        WaWorkRoundDo round = ObjectConverter.convert(workRoundDto, WaWorkRoundDo.class);
        if (null != workRoundDto.getI18nRoundName()) {
            round.setI18nRoundName(FastjsonUtil.toJson(workRoundDto.getI18nRoundName()));
        }
        if (workRoundDto.getWorkRoundId() == null) {
            round.setCrttime(DateUtil.getCurrentTime(true));
            waWorkRoundDo.save(round);
        } else {
            waWorkRoundDo.updateById(round);
            workRoundShiftDomainService.deleteByWorkRoundId(round.getWorkRoundId());
        }
        //保存班次轮询数据
        if (CollectionUtils.isNotEmpty(workRoundDto.getRoundShiftList())) {
            List<WaWorkRoundShiftDo> list = ObjectConverter.convertList(workRoundDto.getRoundShiftList(), WaWorkRoundShiftDo.class);
            list.forEach(roundShift -> {
                roundShift.setWorkRoundId(round.getWorkRoundId());
                roundShift.setCrtuser(userInfo.getUserId());
                roundShift.setCrttime(DateUtil.getCurrentTime(true));

                waWorkRoundShiftDo.save(roundShift);
            });
        }
        return round.getWorkRoundId();
    }

    @Override
    public List<String> getWorktimeNameList(String belongOrgId, Integer id) {

        List<WaWorktimeDo> worktimeDoList = waWorktimeDo.getWorktimeListByWorkRoundId(belongOrgId, id);
        if (CollectionUtils.isNotEmpty(worktimeDoList)) {
            return worktimeDoList.stream().map(WaWorktimeDo::getWorkCalendarName).distinct().collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public Boolean checkWorkRoundIsUsed(Integer workRoundId) {
        List<WaWorkRoundShiftDo> roundShiftList = workRoundShiftDomainService.selectListByWorkRoundId(workRoundId);
        if (CollectionUtils.isNotEmpty(roundShiftList)) {
            return true;
        }
        return false;
    }

    /**
     * 删除排班计划
     *
     * @param id
     * @return
     */
    @Override
    public Integer deleteWorkRound(Integer id) {
        workRoundShiftDomainService.deleteByWorkRoundId(id);
        workRoundDomainService.deleteById(id);
        return 1;
    }

    /**
     * 根据班次ID查询关联的期间工作计划
     *
     * @param shiftId
     * @return
     */
    @Override
    public List<WaWorkRoundDo> getWorkRoundListByShiftId(Integer shiftId) {
        return workRoundDomainService.getWorkRoundListByShiftId(shiftId);
    }
}
