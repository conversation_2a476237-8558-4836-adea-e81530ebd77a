package com.caidaocloud.attendance.service.domain.service;

import com.caidaocloud.attendance.service.domain.entity.WaWorkRoundDo;
import com.caidaocloud.attendance.service.domain.entity.WaWorkRoundShiftDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/3/1
 */
@Slf4j
@Service
public class WorkRoundDomainService {
    @Autowired
    private WaWorkRoundDo waWorkRoundDo;
    @Autowired
    private WaWorkRoundShiftDo workRoundShiftDo;

    public WaWorkRoundDo selectById(Integer id) {
        return waWorkRoundDo.selectById(id);
    }

    public int deleteById(Integer id) {
        return waWorkRoundDo.deleteById(id);
    }

    public List<WaWorkRoundDo> getWorkRoundListByShiftId(Integer shiftId) {
        List<WaWorkRoundShiftDo> roundShiftDoList = workRoundShiftDo.selectListByShiftDefId(shiftId);
        if (CollectionUtils.isNotEmpty(roundShiftDoList)) {
            List<Integer> roundIdList = roundShiftDoList.stream().map(WaWorkRoundShiftDo::getWorkRoundId).distinct().collect(Collectors.toList());
            return waWorkRoundDo.getWorkRoundListByIds(roundIdList);
        }
        return new ArrayList<>();
    }
}
