package com.caidaocloud.attendance.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 补卡DTO
 */
@Data
public class SdkSaveBdkDTO {
    @ApiModelProperty("签到时间")
    private Long regDateTime;
    @ApiModelProperty("理由")
    private String reason;
    @ApiModelProperty("附件")
    private String file;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("签到时间")
    private List<Long> regDateTimes;
}
