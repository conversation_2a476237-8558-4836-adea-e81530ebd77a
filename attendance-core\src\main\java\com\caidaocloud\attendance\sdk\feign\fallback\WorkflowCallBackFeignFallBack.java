package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.sdk.dto.SdkWorkflowCallBackHandleLeaveDto;
import com.caidaocloud.attendance.sdk.feign.IWorkflowCallBackFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class WorkflowCallBackFeignFallBack implements IWorkflowCallBackFeignClient {
    @Override
    public Result<?> handleLeave(SdkWorkflowCallBackHandleLeaveDto dto) {
        return Result.fail();
    }
}
