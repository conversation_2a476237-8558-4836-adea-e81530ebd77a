package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.attendance.service.interfaces.dto.BatchClockAnalyseDto;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 多节点打卡分析消息生产者
 */
@Slf4j
@Service
public class MultiNodeAnalyseClockPublish {

    @Resource
    private MqMessageProducer<ClockMessage> producer;

    private final static String EXCHANGE = "attendance.clock.analyse.pc.fac.direct.exchange";

    private final static String ROUTING_KEY = "routingKey.clock.analyse.multinode";

    public void publishForObj(BatchClockAnalyseDto messageDto) {
        publish(FastjsonUtil.toJson(messageDto));
    }

    public void publish(String msg) {
        log.info("MultiNodeAnalyseClockPublish.publish  push msg:{}, time:{}", msg, System.currentTimeMillis());
        try {
            ClockMessage message = new ClockMessage();
            message.setBody(msg);
            message.setExchange(EXCHANGE);
            message.setRoutingKey(ROUTING_KEY);
            producer.publish(message);
        } catch (Exception e) {
            log.error("MultiNodeAnalyseClockPublish.publish push error!! errorMsg:{}:", e.getMessage(), e);
            log.error(e.getMessage(), e);
        }
    }
}
