package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.sdk.dto.SdkWorkflowCallBackHandleLeaveDto;
import com.caidaocloud.attendance.sdk.feign.fallback.LeaveFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = LeaveFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "workflowCallBackFeignClient")
public interface IWorkflowCallBackFeignClient {

    @PostMapping(value = "/api/attendance/workflow/v1/handleLeave")
    Result<?> handleLeave(@RequestBody SdkWorkflowCallBackHandleLeaveDto dto);
}
