package com.caidaocloud.attendance.core.annoation.feign;


import com.caidaocloud.attendance.core.annoation.dto.WorkplaceDto;
import com.caidaocloud.hr.service.vo.adapter.JobGradeDataOutVo;
import com.caidaocloud.web.Result;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HrServiceFeignFallback implements HrServiceFeignClient {
    @Override
    public Result<List<WorkplaceDto>> getWorkPlace() {
        return Result.fail();
    }

    @Override
    public Result<List<JobGradeDataOutVo>> getJobGrade() {
        return Result.fail();
    }
}
