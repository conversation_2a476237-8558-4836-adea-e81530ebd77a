package com.caidaocloud.attendance.core.wa.utils;

import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.LanguageUtil;
import com.caidaocloud.util.StringUtil;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

public class LangParseUtil {
    public static String getI18nLanguage(String i18n, String defaultName) {
        if (StringUtils.isBlank(i18n)) {
            return defaultName;
        }
        try {
            Map<String, String> i18nNameMap = FastjsonUtil.toObject(i18n, Map.class);
            String i18nName = LanguageUtil.getCurrentLangVal(i18nNameMap);
            if (StringUtil.isBlank(i18nName)) {
                return defaultName;
            }
            return i18nName;
        } catch (Exception e) {
            return defaultName;
        }
    }

    public static String getI18nLanguage(Map<String, String> i18n, String defaultName) {
        if (i18n == null) {
            return defaultName;
        }
        try {
            String i18nName = LanguageUtil.getCurrentLangVal(i18n);
            if (StringUtil.isBlank(i18nName)) {
                return defaultName;
            }
            return i18nName;
        } catch (Exception e) {
            return defaultName;
        }
    }
}
