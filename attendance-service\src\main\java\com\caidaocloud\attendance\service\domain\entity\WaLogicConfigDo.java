package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWaLogicConfigRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 考勤逻辑配置表
 *
 * <AUTHOR>
 * @Date 2024/2/4
 */
@Slf4j
@Data
@Service
public class WaLogicConfigDo {
    private Long id;
    private String logicName;
    private String logicCode;
    private String logicType;
    private String logicExp;
    private String logicParams;
    private String tenantId;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;

    @Autowired
    private IWaLogicConfigRepository waLogicConfigRepository;

    public List<WaLogicConfigDo> getListByCodes(String tenantId, List<String> logicCodes) {
        return waLogicConfigRepository.selectListByCodes(tenantId, logicCodes);
    }
}
