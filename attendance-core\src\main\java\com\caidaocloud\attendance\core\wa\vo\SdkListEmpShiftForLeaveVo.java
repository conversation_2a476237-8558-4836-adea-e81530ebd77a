package com.caidaocloud.attendance.core.wa.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("员工休假时展示的排班详情VO")
public class SdkListEmpShiftForLeaveVo {
    @ApiModelProperty("班次Id")
    private Long shiftDefId;
    @ApiModelProperty("日期类型：1、工作日、2休息日、3法定假日、4特殊休日、5 法定休日")
    private Integer dateType;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("班次编码")
    private String shiftDefCode;
    @ApiModelProperty("半天时间")
    private String halfdayTime;
    @ApiModelProperty("工作时间")
    private String workTimeTxt;
}
