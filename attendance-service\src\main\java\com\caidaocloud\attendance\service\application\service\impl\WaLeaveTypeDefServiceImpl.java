package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.JSONUtils;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.event.publish.PaySyncWaPublish;
import com.caidaocloud.attendance.service.application.service.IWaLeaveTypeDefService;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDefDo;
import com.caidaocloud.attendance.service.interfaces.dto.WaLeaveTypeDefDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WaLeaveTypeDefServiceImpl implements IWaLeaveTypeDefService {
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaLeaveTypeDefDo waLeaveTypeDefDo;
    @Resource
    private PaySyncWaPublish paySyncWaPublish;
    @Value("${caidaocloud.data.wasync:false}")
    private boolean waSync;

    @Override
    public List<WaLeaveTypeDefDto> getWaLeaveTypeDefList() {
        UserInfo userInfo = this.getUserInfo();
        String belongOrgId = userInfo.getTenantId();
        List<WaLeaveTypeDefDo> waLeaveTypeDefDoList = waLeaveTypeDefDo.getLeaveTypeDefList(belongOrgId);
        List<WaLeaveTypeDefDto> list = new ArrayList<>();
        waLeaveTypeDefDoList.forEach(item -> {
            WaLeaveTypeDefDto model = ObjectConverter.convert(item, WaLeaveTypeDefDto.class);
            if (StringUtils.isNotBlank(item.getI18nLeaveTypeDefName())) {
                model.setI18nLeaveTypeDefName(FastjsonUtil.toObject(item.getI18nLeaveTypeDefName(), Map.class));
            } else if (StringUtils.isNotBlank(item.getLeaveTypeDefName())) {
                Map<String, String> i18nName = new HashMap<>();
                i18nName.put("default", item.getLeaveTypeDefName());
                model.setI18nLeaveTypeDefName(i18nName);
            }
            model.setLeaveTypeDefName(LangParseUtil.getI18nLanguage(item.getI18nLeaveTypeDefName(), item.getLeaveTypeDefName()));
            list.add(model);
        });
        return list;
    }

    @Override
    public List<WaLeaveTypeDefDto> getWaLeaveTypeDefList(String tenantId) {
        List<WaLeaveTypeDefDo> waLeaveTypeDefDoList = waLeaveTypeDefDo.getLeaveTypeDefList(tenantId);
        List<WaLeaveTypeDefDto> list = new ArrayList<>();
        waLeaveTypeDefDoList.forEach(item -> {
            WaLeaveTypeDefDto model = ObjectConverter.convert(item, WaLeaveTypeDefDto.class);
            item.setLeaveTypeDefName(LangParseUtil.getI18nLanguage(item.getI18nLeaveTypeDefName(), item.getLeaveTypeDefName()));
            if (StringUtils.isNotBlank(item.getI18nLeaveTypeDefName())) {
                model.setI18nLeaveTypeDefName(FastjsonUtil.toObject(item.getI18nLeaveTypeDefName(), Map.class));
            } else if (StringUtils.isNotBlank(item.getLeaveTypeDefName())) {
                Map<String, String> i18nName = new HashMap<>();
                i18nName.put("default", item.getLeaveTypeDefName());
                model.setI18nLeaveTypeDefName(i18nName);
            }
            list.add(model);
        });
        return list;
    }

    @Override
    public void delete(Integer id) {
        UserInfo userInfo = this.getUserInfo();
        String belongOrgId = userInfo.getTenantId();
        WaLeaveTypeDefDo byId = waLeaveTypeDefDo.getById(belongOrgId, id);
        LogRecordContext.putVariable("name", byId.getLeaveTypeDefName());
        waLeaveTypeDefDo.delete(id, belongOrgId);

        // 同步假期类型到薪资
        syncWaLeaveTypeDefList();
    }

    @Override
    public void save(WaLeaveTypeDefDto dto) {
        UserInfo userInfo = getUserInfo();
        waLeaveTypeDefDo.save(dto, userInfo.getTenantId(), userInfo.getUserId());
        // 同步假期类型到薪资
        syncWaLeaveTypeDefList();
    }

    @Override
    public WaLeaveTypeDefDto getDetailList(Integer id) {
        return waLeaveTypeDefDo.getDetailList(id);
    }

    @Override
    public WaLeaveTypeDefDto getWaLeaveTypeDefByData(WaLeaveTypeDefDto dto) {
        UserInfo userInfo = getUserInfo();
        return waLeaveTypeDefDo.getWaLeaveTypeDefByData(dto, userInfo.getTenantId());
    }

    @Override
    public void syncWaLeaveTypeDefList() {
        if (!waSync) {
            // 未开启同步
            log.info("Attendance leave type synchronization setting is not enabled");
            return;
        }
        UserInfo userInfo = this.getUserInfo();
        String tenantId = userInfo.getTenantId();
        List<WaLeaveTypeDefDo> dbList = waLeaveTypeDefDo.getAllLeaveTypeDefList(tenantId, true);
        if (null == dbList || dbList.isEmpty()) {
            return;
        }
        List<WaLeaveTypeDefDto> list = ObjectConverter.convertList(dbList, WaLeaveTypeDefDto.class);
        paySyncWaPublish.publishWaLeaveTypeDef(JSONUtils.ObjectToJson(list));
    }

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

}
