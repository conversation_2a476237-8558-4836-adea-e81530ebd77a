package com.caidao1.ioc.service;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.employee.mybatis.mapper.EmpBankInfoMapper;
import com.caidao1.employee.mybatis.mapper.SysEmpContractMapper;
import com.caidao1.employee.mybatis.mapper.SysEmpPrivacyMapper;
import com.caidao1.employee.mybatis.model.*;
import com.caidao1.integrate.mybatis.mapper.IntegrateMapper;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.dto.CheckMessage;
import com.caidao1.ioc.dto.CheckRuleDto;
import com.caidao1.ioc.dto.ImportResult;
import com.caidao1.ioc.dto.ImportResultMessage;
import com.caidao1.ioc.mybatis.mapper.IocImportMapper;
import com.caidao1.ioc.util.*;
import com.caidao1.payroll.mybatis.mapper.PayFixInfoMapper;
import com.caidao1.payroll.mybatis.model.PayFixInfo;
import com.caidao1.payroll.mybatis.model.PayFixInfoExample;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.model.SysParmDict;
import com.caidao1.xss.test.cache.RedisService;
import com.caidao1.ioc.util.IocEnum;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.dto.UserInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.Jedis;

import java.io.FileInputStream;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 英迈定制导入
 */
@Service
public class ImImportService {

    private static final int BATCH_PAGE = 1000;
    private Logger log = LoggerFactory.getLogger(this.getClass());
    @Value("${uploadFilePath}")
    private String uploadFilePath;
    @Autowired
    private IocImportMapper iocImportMapper;
    @Autowired
    private SysEmpInfoMapper empInfoMapper;
    @Autowired
    private SysEmpPrivacyMapper empPrivacyMapper;
    @Autowired
    private EmpBankInfoMapper empBankInfoMapper;
    @Autowired
    private SysEmpContractMapper empContractMapper;
    @Autowired
    private PayFixInfoMapper payFixInfoMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private IntegrateMapper integrateMapper;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;

    private static ConcurrentHashMap<String, Object> checkCache = new ConcurrentHashMap<String, Object>();

    private Map<String, String> departMap = new HashMap() {{
        put("Advanced Solutions G", "Advanced Solutions Group");
        put("Consumer & Commercia", "Consumer & Commercial");
        put("Commerce & Fulfilmen", "Commerce & Fulfilment Solutions");
    }};

    /**
     * 导入文件
     *
     * @param file
     * @return
     * @throws Exception
     */
    public ImportResult importFile(MultipartFile file) throws Exception {
        ImportResult ir = new ImportResult();
        try {
            String rtnFileName = IocUtil.importFile(file, uploadFilePath);
            ir.setFileName(rtnFileName);
            ir.setResult(true);
        } catch (Exception e) {
            e.printStackTrace();
            ir.setResult(false);
            log.error(e.getMessage());
            ir.setMessage("导入文件发生错误！");
        }
        return ir;
    }

    /**
     * 数据检查
     *
     * @param rtnFileName
     * @param progress
     * @return
     * @throws Exception
     */
    public boolean checkImportData(String rtnFileName, String progress, ProgressListener iocProgressListener) throws Exception {
        Map<String, List<CheckRuleDto>> checkRuleMap = new HashMap<String, List<CheckRuleDto>>();
        List<CheckMessage> errorList = new ArrayList<CheckMessage>();

        List<String> tableList = Arrays.asList("sys_emp_info", "sys_emp_privacy", "emp_bank_info", "sys_emp_contract", "pay_fix_info");
        Map<String, List<String>> tableFieldMap = new HashMap<>();
        Map<String, Integer> fieldIdxMap = new HashMap<>();

        Jedis jedis = redisService.getResource();

        String fileName = rtnFileName.substring(0, rtnFileName.lastIndexOf("."));
        String fileType = rtnFileName.substring(rtnFileName.lastIndexOf(".") + 1, rtnFileName.length());
        IocFileReader reader = IocReaderFactory.getReader(fileType, new FileInputStream(uploadFilePath + rtnFileName));
        List<String> titles = reader.getTitle(0, 0);

        List<String> fieldList = Arrays.asList("pay_fix_info#start_time", "", "", "sys_emp_info#workno", "sys_emp_info#emp_name", "sys_emp_info#eng_name", "sys_emp_privacy#work_location", "", "", "", "sys_emp_info#job_grade", "sys_emp_info#leader_empid", "sys_emp_info#gender", "sys_emp_privacy#high_degree", "sys_emp_privacy#id_card_no", "sys_emp_privacy#birth_date", "sys_emp_info#hire_date", "sys_emp_info#prodead_line", "", "", "sys_emp_info#email", "pay_fix_info#amount", "", "emp_bank_info#bank_account");

        List<List<String>> rows = reader.getRows(0, 0);

        UserInfo userInfo = UserContext.getCurrentUser();

        Long corpid = ConvertHelper.longConvert(userInfo.getTenantId());
        String belongId = SessionHolder.getBelongOrgId();
        List<String> belongList = iocImportMapper.getBelongidList(belongId);
        belongList.add(belongId);
        try {
            for (String table : tableList) {
                //取得校验定义规则
                List<CheckRuleDto> checkRuleList = integrateMapper.selectCheckRuleListByTable(table, belongId);
                for (CheckRuleDto checkRule : checkRuleList) {
                    List<CheckRuleDto> crList = new ArrayList<CheckRuleDto>();
                    if (checkRuleMap.containsKey(checkRule.getFieldCode())) {
                        crList = checkRuleMap.get(checkRule.getFieldCode());
                    }
                    crList.add(checkRule);
                    checkRuleMap.put(table + "#" + checkRule.getFieldCode(), crList);
                }
            }

            for (int i = 0; i < rows.size(); i++) {
                List<String> row = rows.get(i);
                List<String> realRow = new ArrayList<String>();
                realRow.addAll(row);
//                    List<String> cols = tableFieldMap.get(table);
                for (int j = 0; j < fieldList.size(); j++) {
                    String field = fieldList.get(j);
                    if (StringUtils.isNotEmpty(field)) {
                        String cellValue = row.get(j);
                        List<CheckRuleDto> fieldCheckRuleList = checkRuleMap.get(field);
                        if (CollectionUtils.isNotEmpty(fieldCheckRuleList)) {
                            boolean checkError = false;
                            for (CheckRuleDto crDto : fieldCheckRuleList) {
                                String message = crDto.getMessage();
                                switch (IocEnum.valueOf(crDto.getCheckPattern())) {
                                    //非空校验
                                    case Required:
                                        if (StringUtils.isEmpty(cellValue)) {
                                            errorList.add(new CheckMessage(i + 1, j + 1, message));
                                            checkError = true;
                                        }
                                        break;
                                    //格式校验
                                    case Regex:
                                        String exp = crDto.getCheckExp();
                                        if (StringUtils.isNotEmpty(cellValue) && !cellValue.matches(exp)) {
                                            errorList.add(new CheckMessage(i + 1, j + 1, message));
                                            checkError = true;
                                        }
                                        break;
                                    //范围校验
                                    case Range:
                                        if (StringUtils.isNotEmpty(cellValue)) {
                                            String range = crDto.getCheckExp();
                                            if (StringUtils.isNotEmpty(range)) {
                                                Pattern pattern = Pattern.compile("^(\\(|\\[)(.*),(.*)(\\)|\\])$", Pattern.CASE_INSENSITIVE);
                                                Matcher matcher = pattern.matcher(range);
                                                message = MessageFormat.format(message, cellValue);
                                                if (matcher.find()) {
                                                    int c1 = new BigDecimal(cellValue).compareTo(new BigDecimal(matcher.group(2)));
                                                    int c2 = new BigDecimal(cellValue).compareTo(new BigDecimal(matcher.group(3)));
                                                    if (c1 < 0) {
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    } else if (c1 == 0 && "(".equals(matcher.group(1))) {
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    }
                                                    if (c2 > 0) {
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    } else if (c2 == 0 && ")".equals(matcher.group(4))) {
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    }
                                                } else {
                                                    if (!range.contains(cellValue)) {
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    //SQL校验
                                    case SQL:
                                        //外键校验
                                    case FK:
                                        try {
                                            String sql = crDto.getCheckExp();
                                            if (StringUtils.isNotEmpty(cellValue) && StringUtils.isNotEmpty(sql)) {
                                                Long existFk = null;
                                                if (StringUtils.isNotEmpty(cellValue) && crDto.getFieldCode().equals("orgid") && cellValue.contains("/")) {
//                                                    existFk = createFKObject(crDto.getFieldCode(), cellValue, false, null, belongId, null);
                                                } else {
                                                    if (sql.startsWith(IocUtil.REDIS_DICT_CACHE)) {
                                                        String sqlExp = IocUtil.formatExp(sql, cellValue, fileName);
                                                        Pattern pattern = Pattern.compile(IocUtil.REDIS_DICT_CACHE + "\\((.*)\\)+?", Pattern.CASE_INSENSITIVE);
                                                        Matcher matcher = pattern.matcher(sqlExp);
                                                        if (matcher.find()) {
                                                            String[] params = matcher.group(1).split(",");
                                                            SysParmDict dic = CDCacheUtil.getDictByName(jedis, ConvertHelper.longConvert(params[0].trim()), params[1].trim(), params[2].trim());
                                                            if (dic != null) {
                                                                existFk = dic.getDictId();
                                                            }
                                                        }
                                                    } else if (sql.equals(IocUtil.REDIS_EMPID_CACHE)) {
                                                        existFk = getEmpIdByWorkNo(jedis, belongList, cellValue);
                                                    } else {
//                                                        String sqlExp = IocUtil.formatExp(sql, val != null ? val.toString() : "", fileName);
//                                                        sqlExp = formatRowKey(sqlExp, row, fieldIdxMap, typeMap.get(crDto.getFieldCode()));
//                                                        existFk = getFkByExpCache(sqlExp);
                                                    }
                                                }
                                                if (existFk == null || existFk == -1) {
                                                    message = MessageFormat.format(message, cellValue);
                                                    errorList.add(new CheckMessage(i + 1, j + 1, message, crDto.getCheckOrder()));
                                                    checkError = true;
                                                } else {
                                                    realRow.set(j, String.valueOf(existFk));
                                                    //如果总部导入所有公司
                                                    if (crDto.getFieldCode().matches("belong_org_?id")) {
                                                        belongId = ConvertHelper.stringConvert(existFk);
                                                    }
                                                }
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            errorList.add(new CheckMessage(i + 1, j + 1, "导入配置存在问题，请联系管理员！"));
                                            checkError = true;
                                        }
                                        break;
                                    case Java:
                                        String javaScript = crDto.getCheckExp();
                                        if (StringUtils.isNotEmpty(javaScript)) {
                                            Map<String, Object> binding = new HashMap<String, Object>();
                                            binding.put("titles", titles);
                                            binding.put("row", row);
                                            binding.put("realRow", realRow);
                                            binding.put("rows", rows);
                                            binding.put("rowIdx", i);
//                                            binding.put("fieldMap", fieldMap);
                                            binding.put("fieldIdxMap", fieldIdxMap);
                                            binding.put("fieldCode", crDto.getFieldCode());
//                                        binding.put("title", title);
                                            binding.put("belongId", belongId);
                                            binding.put("corpId", SessionHolder.getCorpId());
                                            String javaExp = IocUtil.formatExp(javaScript, cellValue, fileName);
//                                            javaExp = formatRowKey(javaExp, row, fieldIdxMap, typeMap.get(crDto.getFieldCode()));
                                            boolean result = groovyScriptEngine.executeBoolean(javaExp, binding);
                                            if (!result) {
//                                            message = MessageFormat.format(message, title, cellValue);
                                                errorList.add(new CheckMessage(i + 1, j + 1, message, crDto.getCheckOrder()));
                                                checkError = true;
                                            }
                                        }
                                        break;
                                }

                                if (checkError) {
                                    break;
                                }
                            }
                        }
                    }
                }
                iocProgressListener.updatePercent((i + 1) * 1.0 / rows.size());
            }
            saveError2Cache(errorList, progress, jedis);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            jedis.close();
        }
        return errorList.size() == 0;
    }

    private Long getEmpIdByWorkNo(Jedis jedis, List<String> belongList, String cellValue) {
        Long empid = null;
        for (String subBelongId : belongList) {
            String empInfo = jedis.get(BaseConst.EMP_ + subBelongId + "_" + cellValue);
            if (empInfo != null) {
                empid = Long.valueOf(empInfo.split(",")[0]);
                break;
            }
        }
        return empid;
    }

    private Object getFkByExpCache(String sqlExp) {
        Object existFk = null;
        if (checkCache.containsKey(sqlExp)) {
            existFk = checkCache.get(sqlExp);
        } else {
            existFk = iocImportMapper.queryFKBySql(sqlExp);
            //checkCache.put(sqlExp, existFk == null ? -1 : existFk);
            if (null == existFk) {
                return existFk;
            }
        }
        return existFk;
    }

    private void saveError2Cache(List<CheckMessage> errorList, String progress, Jedis jedis) throws Exception {
        if (errorList.size() > 0) {
            String cacheKey = "IMP_" + progress;
            jedis.set(cacheKey, new ObjectMapper().writeValueAsString(errorList));
            jedis.expire(cacheKey, 3600);
        }
    }

    /**
     * 保存数据
     *
     * @param rtnFileName
     * @param progress
     * @param progressListener @return
     * @throws Exception
     */
    @Transactional
    public ImportResultMessage saveImportData(String rtnFileName, String progress, ProgressListener progressListener) throws
            Exception {
        long startTime = new Date().getTime();
        String fileName = rtnFileName.substring(0, rtnFileName.lastIndexOf("."));
        String fileType = rtnFileName.substring(rtnFileName.lastIndexOf(".") + 1, rtnFileName.length());
        IocFileReader reader = IocReaderFactory.getReader(fileType, new FileInputStream(uploadFilePath + rtnFileName));
        List<String> titles = reader.getTitle(0, 0);
        List<List<String>> rows = reader.getRows(0, 0);

        UserInfo userInfo = UserContext.getCurrentUser();


        Long corpid = ConvertHelper.longConvert(userInfo.getTenantId());
        String belongId = SessionHolder.getBelongOrgId();
        List<String> belongList = iocImportMapper.getBelongidList(userInfo.getTenantId());
        belongList.add(belongId);
        List<SysEmpInfo> empInfoAddList = new ArrayList<>();
        List<SysEmpInfo> empInfoUpdList = new ArrayList<>();
        List<SysEmpPrivacy> empPrivacyAddList = new ArrayList<>();
        List<SysEmpPrivacy> empPrivacyUpdList = new ArrayList<>();
        List<EmpBankInfo> empBankInfoAddList = new ArrayList<>();
        List<EmpBankInfo> empBankInfoUpdList = new ArrayList<>();
        List<SysEmpContract> empContractAddList = new ArrayList<>();
        List<SysEmpContract> empContractUpdList = new ArrayList<>();
        List<PayFixInfo> payFixInfoAddList = new ArrayList<>();
        List<PayFixInfo> payFixInfoUpdList = new ArrayList<>();

        Jedis jedis = RedisService.getResource();

        List<String> workNoList = new ArrayList<>();
        Long userId = SessionHolder.getUserId();

//        String sql = "select a.post_id from sys_org_position a JOIN sys_emp_info b ON a.orgid = b.orgid where a.status = 1 and b.belong_org_id in (select getsubbelongid(" + belongId + ")) and a.eng_name = '{0}"
//                + "' and b.empid ={1}";

        //合约工资
        Long detailId = 3890L;
        try {
            double cnt = 1.0;
            for (List<String> row : rows) {
                SysEmpInfo empInfo = new SysEmpInfo();
                Map extColMap = new HashMap<>();
                SysEmpPrivacy empPrivacy = new SysEmpPrivacy();
                EmpBankInfo empBankInfo = new EmpBankInfo();
                SysEmpContract empContract = new SysEmpContract();
                PayFixInfo payFixInfo = new PayFixInfo();

                Long empid = getEmpIdByWorkNo(jedis, belongList, row.get(3));
                empInfo.setCorpid(corpid);
                empInfo.setBelongOrgId(belongId);
                empInfo.setStats(0);
                empInfo.setIsexsitAccount(false);
                empInfo.setTmType(1);
                empInfo.setEmpid(empid);
                empPrivacy.setEmpid(empid);
                //empBankInfo.setEmpid(empid);
                //empBankInfo.setBelongOrgId(belongId);
                empBankInfo.setValid("Y");
                //empContract.setEmpid(empid);
                empContract.setContractId(-1);
                payFixInfo.setEmpid(empid);

                // 合约工资生效时间 Effective Date
                payFixInfo.setStarttime(DateUtil.convertStringToDateTime(row.get(0), "MM/dd/yyyy", true));
                payFixInfo.setEndtime(DateUtil.MAX_DATE);
                payFixInfo.setDetailId(detailId);
                // 公司编码 Company Code
                extColMap.put(BaseConst.FIX_EXT_COLUMN + 108, row.get(1));
                // 分公司编码 Branch Code
                extColMap.put(BaseConst.FIX_EXT_COLUMN + 109, row.get(2));
                // 工号 Payee Code
                empInfo.setWorkno(row.get(3));
                // 中文姓名 Employee Name
//                empInfo.setEmpName(row.get(4));
                // 英文姓名 English Name
                empInfo.setEngName(row.get(4));
                // 工作地
                empPrivacy.setWorkLocation(row.get(6));
                // Department
                String depart = row.get(7);
                if (departMap.containsKey(depart)) {
                    depart = departMap.get(depart);
                }
                extColMap.put(BaseConst.FIX_EXT_COLUMN + 110, depart);
                //empInfo.setOrgid(getFkByExpCache(" select orgid from sys_corp_org where orgtype2 = 2 and status =1 and belong_org_id = " + belongId + " AND shortname = ':VALUE'"));
                // Segment
                extColMap.put(BaseConst.FIX_EXT_COLUMN + 111, row.get(8));
                // 职位
//                empPrivacy.setHighDegree(getDictValue(jedis, corpid, "EduDegree", row.get(9)));
                // 职级
                //empInfo.setJobGrade(getDictValue(jedis, corpid, "JobGrade", row.get(10)));
                // 上级领导
                if (StringUtils.isNotEmpty(row.get(11))) {
                    empInfo.setLeaderEmpid(getEmpIdByWorkNo(jedis, belongList, row.get(11)));
                }
                // 性别
                //empInfo.setGender(getDictValue(jedis, corpid, "Gender", row.get(12)));
                // 学位
                //empPrivacy.setHighDegree(getDictValue(jedis, corpid, "EduDegree", row.get(13)));
                // 证件号码
                empPrivacy.setIdCardNo(row.get(14));
                // 出生日期
                empPrivacy.setBirthDate(DateUtil.convertStringToDateTime(row.get(15), "MM/dd/yyyy", true));
                // 入职日期
                empInfo.setHireDate(DateUtil.convertStringToDateTime(row.get(16), "MM/dd/yyyy", true));
                // 试用期到期日
//                empContract.setContractProb();
                empInfo.setProdeadLine(DateUtil.convertStringToDateTime(row.get(17), "MM/dd/yyyy", true));
                // 合同开始日期
                empContract.setStartTime(DateUtil.convertStringToDateTime(row.get(18), "MM/dd/yyyy", true));
                // 合同结束日期
                empContract.setEndTime(DateUtil.convertStringToDateTime(row.get(19), "MM/dd/yyyy", true));
                // 电子邮件
                empPrivacy.setCorpEmail(row.get(20));
                // 合约工资
                payFixInfo.setAmount(new BigDecimal(row.get(21)).toString());
                // PS编号
                extColMap.put(BaseConst.FIX_EXT_COLUMN + 118, row.get(23));
                // 银行帐号
                empBankInfo.setBankAccount(row.get(24));
                empInfo.setExtCustomCol(new ObjectMapper().writeValueAsString(extColMap));
//                empPrivacy.setExtCustomCol(extColMap2);

                if (empid == null) {
                    workNoList.add(empInfo.getWorkno());
                    empInfo.setCrttime(DateUtil.getCurrentTime(true));
                    empInfo.setCrtuser(userId);
                    empInfoMapper.insertSelective(empInfo);

//                    empInfoAddList.add(empInfo);
                    empPrivacy.setEmpid(empInfo.getEmpid());
                    empPrivacyAddList.add(empPrivacy);

                    //empBankInfo.setEmpid(empInfo.getEmpid());
                    //empBankInfo.setCrtuser(userId);
                    empBankInfo.setCrttime(DateUtil.getCurrentTime(true));
                    empBankInfoAddList.add(empBankInfo);

                    //empContract.setEmpid(empInfo.getEmpid());
                    //empContract.setCrtuser(userId);
                    empContract.setCrttime(DateUtil.getCurrentTime(true));
                    empContractAddList.add(empContract);

                    payFixInfo.setCrtuser(userId);
                    payFixInfo.setCrttime(DateUtil.getCurrentTime(true));
                    payFixInfo.setEmpid(empInfo.getEmpid());
                    payFixInfoAddList.add(payFixInfo);

                } else {
                    empInfo.setUpduser(userId);
                    empInfo.setUpdtime(DateUtil.getCurrentTime(true));
                    empInfoUpdList.add(empInfo);

                    SysEmpPrivacyExample empPrivacyExample = new SysEmpPrivacyExample();
                    empPrivacyExample.createCriteria().andEmpidEqualTo(empid);
                    if (empPrivacyMapper.countByExample(empPrivacyExample) == 0) {
                        empPrivacyAddList.add(empPrivacy);
                    } else {
                        empPrivacyUpdList.add(empPrivacy);
                    }

                    if (StringUtils.isNotEmpty(empBankInfo.getBankAccount())) {
                        EmpBankInfoExample empBankInfoExample = new EmpBankInfoExample();
                        //empBankInfoExample.createCriteria().andEmpidEqualTo(empid).andBankAccountEqualTo(empBankInfo.getBankAccount());
                        if (empBankInfoMapper.countByExample(empBankInfoExample) == 0) {
                            //empBankInfo.setCrtuser((userId));
                            empBankInfo.setCrttime(DateUtil.getCurrentTime(true));
                            empBankInfoAddList.add(empBankInfo);
                        } else {
//                        empBankInfoUpdList.add(empBankInfo);
                        }
                    }

                    if (empContract.getStartTime() != null) {
                        SysEmpContractExample empContractExample = new SysEmpContractExample();
                        //empContractExample.createCriteria().andEmpidEqualTo(empid).andStartTimeEqualTo(empContract.getStartTime());
                        List<SysEmpContract> empContractList = empContractMapper.selectByExample(empContractExample);
                        if (CollectionUtils.isEmpty(empContractList)) {
                            //empContract.setCrtuser((userId));
                            empContract.setCrttime(DateUtil.getCurrentTime(true));
                            empContractAddList.add(empContract);
                        } else {
                            empContract.setEmpContractId(empContractList.get(0).getEmpContractId());
                            //empContract.setUpduser(userId);
                            empContract.setUpdtime(DateUtil.getCurrentTime(true));
                            empContractUpdList.add(empContract);
                        }
                    }

                    PayFixInfoExample payFixInfoExample = new PayFixInfoExample();
                    payFixInfoExample.createCriteria().andEmpidEqualTo(empid).andDetailIdEqualTo(detailId).andStarttimeEqualTo(payFixInfo.getStarttime());
                    List<PayFixInfo> payFixInfoList = payFixInfoMapper.selectByExample(payFixInfoExample);
                    if (CollectionUtils.isEmpty(payFixInfoList)) {
                        payFixInfo.setCrtuser((userId));
                        payFixInfo.setCrttime(DateUtil.getCurrentTime(true));
                        payFixInfoAddList.add(payFixInfo);
                    } else {
                        payFixInfo.setPaybaseId(payFixInfoList.get(0).getPaybaseId());
                        payFixInfoUpdList.add(payFixInfo);
                    }
                }

                progressListener.updatePercent(cnt++ / rows.size() * 0.9);
            }
            progressListener.updatePercent(0.9);

//            importService.fastInsertList(SysEmpInfo.class, "empid", empInfoAddList);
            importService.fastUpdList(SysEmpInfo.class, "empid", empInfoUpdList, Arrays.asList("engName", "jobGrade", "leaderEmpid", "gender", "hireDate", "prodeadLine", "extCustomCol", "orgid"));
            progressListener.updatePercent(0.91);
            importService.fastInsertList(SysEmpPrivacy.class, null, empPrivacyAddList);
            importService.fastUpdList(SysEmpPrivacy.class, "empid", empPrivacyUpdList, Arrays.asList("workLocation", "highDegree", "idCardNo", "birthDate", "highDegree", "corpEmail"));
            progressListener.updatePercent(0.92);
            importService.fastInsertList(EmpBankInfo.class, "bankId", empBankInfoAddList);
//            importService.fastUpdList(EmpBankInfo.class, "bankId", empBankInfoUpdList, Arrays.asList("workLocation", "lastEduDegree", "idCardNo", "birthDate"));
            importService.fastInsertList(SysEmpContract.class, "empContractId", empContractAddList);
            progressListener.updatePercent(0.93);
            importService.fastUpdList(SysEmpContract.class, "empContractId", empBankInfoUpdList, Arrays.asList("startTime", "endTime", "upduser", "updtime"));
            progressListener.updatePercent(0.94);
            importService.fastInsertList(PayFixInfo.class, "paybaseId", payFixInfoAddList);

            List<PayFixInfo> allPaybaseList = new ArrayList<>();
            allPaybaseList.addAll(payFixInfoAddList);
            allPaybaseList.addAll(payFixInfoUpdList);

            double rowCnt = 0;
            for (PayFixInfo payFixInfo : allPaybaseList) {
                PayFixInfoExample example = new PayFixInfoExample();
                example.createCriteria().andDetailIdEqualTo(detailId).andEmpidEqualTo(payFixInfo.getEmpid()).andStarttimeLessThan(payFixInfo.getStarttime());
                example.setOrderByClause("starttime desc");
                List<PayFixInfo> res = payFixInfoMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(res)) {
                    Calendar cal = Calendar.getInstance();
                    cal.setTimeInMillis(payFixInfo.getStarttime() * 1000);
                    cal.add(Calendar.DATE, -1);
                    PayFixInfo last = res.get(0);
                    if (last.getEndtime() != cal.getTimeInMillis() / 1000) {
                        last.setEndtime(cal.getTimeInMillis() / 1000);
                        payFixInfoUpdList.add(last);
                    }
                }

                example = new PayFixInfoExample();
                example.createCriteria().andDetailIdEqualTo(detailId).andEmpidEqualTo(payFixInfo.getEmpid()).andStarttimeGreaterThanOrEqualTo(payFixInfo.getStarttime());
                example.setOrderByClause("starttime");
                res = payFixInfoMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(res) && res.size() > 1) {
                    PayFixInfo first = res.get(1);
                    Calendar cal = Calendar.getInstance();
                    cal.setTimeInMillis(first.getStarttime() * 1000);
                    cal.add(Calendar.DATE, -1);

                    PayFixInfo record = new PayFixInfo();
                    record.setPaybaseId(res.get(0).getPaybaseId());
                    record.setEndtime(cal.getTimeInMillis() / 1000);
                    record.setAmount(payFixInfo.getAmount());
                    payFixInfoUpdList.add(record);
                }
                progressListener.updatePercent(0.94 + 0.05 * ++rowCnt / allPaybaseList.size());
            }

            importService.fastUpdList(PayFixInfo.class, "paybaseId", payFixInfoUpdList, Arrays.asList("endtime", "amount"));

            for (SysEmpInfo empInfo : empInfoAddList) {
                String site = StringUtils.trimToEmpty(empInfo.getSiteids());
                if(StringUtils.isNotBlank(site)){
                    site = site.replace(",","#");
                }
                jedis.set(BaseConst.EMP_ + belongId + "_" + empInfo.getWorkno(), empInfo.getEmpid() + "," + empInfo.getEmpName() + "," + empInfo.getTmType()+","+site);
            }
            progressListener.updatePercent(1);
        } catch (Exception e) {
            throw e;
        } finally {
            jedis.close();
        }

        long endTime = new Date().getTime();
        String duration = IocUtil.formatTimeDuration(endTime - startTime);

        return new ImportResultMessage(0, "导入成功!", rows.size(), titles.size(), duration);
    }

    private Long getDictValue(Jedis jedis, Long corpid, String typeCode, String dictName) {
        SysParmDict dic = CDCacheUtil.getDictByName(jedis, corpid, typeCode, dictName);
        if (dic != null) {
            return dic.getDictId();
        }
        return null;
    }

    /**
     * 确认数据
     *
     * @param rtnFileName
     * @return
     * @throws Exception
     */
    public Map getImportDataList(PageBean pageBean, String rtnFileName) throws Exception {
        String fileType = rtnFileName.substring(rtnFileName.lastIndexOf(".") + 1, rtnFileName.length());
        IocFileReader reader = IocReaderFactory.getReader(fileType, new FileInputStream(uploadFilePath + rtnFileName));
        List<String> titles = reader.getTitle(0, 0);
        List<List<String>> rows = reader.getRows(pageBean.getPosStart(), 0, pageBean.getCount());
        List<Integer> includeList = new ArrayList<>();
        for (int i = 0; i < titles.size(); i++) {
            includeList.add(i);
        }
        return GridUtil.covertFile2Grid(titles, includeList, rows, pageBean.getPosStart(), reader.getTotalRows() - 1);
    }
}