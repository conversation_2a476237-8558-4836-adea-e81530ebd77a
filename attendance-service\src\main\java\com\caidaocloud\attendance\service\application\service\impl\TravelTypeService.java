package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.JSONUtils;
import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.application.service.ITravelTypeService;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.user.EmployeeGroupService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDo;
import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTypeDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTypeListReqDto;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.*;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/8
 */
@Slf4j
@Service
public class TravelTypeService implements ITravelTypeService {
    @Resource
    private WaTravelTypeDo waTravelTypeDo;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaEmpTravelDo empTravelDo;
    @Autowired
    private EmployeeGroupService employeeGroupService;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Override
    public Boolean checkBeforeSaveOrUpdate(TravelTypeDto dto) {
        UserInfo userInfo = this.getUserInfo();
        int count = waTravelTypeDo.selectCountByTravelName(userInfo.getTenantId(), dto.getTravelTypeName(), dto.getTravelTypeId());
        return count > 0;
    }

    @Override
    public int saveOrUpdate(TravelTypeDto dto) {
        UserInfo userInfo = this.getUserInfo();
        long curTime = System.currentTimeMillis();
        Long userId = userInfo.getUserId();
        String tenantId = userInfo.getTenantId();
        WaTravelTypeDo typeDo = ObjectConverter.convert(dto, WaTravelTypeDo.class);
        if (null != dto.getI18nTravelTypeName()) {
            typeDo.setI18nTravelTypeName(FastjsonUtil.toJson(dto.getI18nTravelTypeName()));
        }
        if (CollectionUtils.isNotEmpty(dto.getAutoTransferRules())) {
            typeDo.setAutoTransferRule(JSONUtils.ObjectToJson(dto.getAutoTransferRules()));
        } else {
            typeDo.setAutoTransferRule(JSONUtils.ObjectToJson(new ArrayList<>()));
        }
        //员工分组
        EmployeeGroupDto employeeGroupDto = new EmployeeGroupDto();
        employeeGroupDto.setGroupType("wa_travel_type");
        employeeGroupDto.bulidExpression(dto.getGroupExp(), dto.getGroupNote());
        buildEmployeeGroupDto(employeeGroupDto, typeDo.getTravelTypeId());
        employeeGroupService.saveOrUpdate(employeeGroupDto);
        if (typeDo.getTravelTypeId() == null) {
            //新增
            typeDo.setCreateTime(curTime);
            typeDo.setUpdateTime(curTime);
            typeDo.setCreateBy(userId);
            typeDo.setUpdateBy(userId);
            typeDo.setTenantId(tenantId);
            typeDo.setDeleted(0);
            typeDo.setTravelTypeId(snowflakeUtil.createId());
            return waTravelTypeDo.save(typeDo);
        } else {
            //修改
            typeDo.setUpdateTime(curTime);
            typeDo.setUpdateBy(userId);
            return waTravelTypeDo.update(typeDo);
        }
    }

    private void buildEmployeeGroupDto(EmployeeGroupDto employeeGroupDto, Long waGroupId) {
        employeeGroupDto.setBusinessKey(String.valueOf(waGroupId));
        employeeGroupDto.setGroupName(String.format("%s-%s", employeeGroupDto.getGroupType(), employeeGroupDto.getBusinessKey()));
    }

    @Override
    public Result<Boolean> deleteTravelTypeById(Long travelTypeId) {
        UserInfo userInfo = this.getUserInfo();
        List<WaEmpTravelDo> list = empTravelDo.getTravelInfoByTravelTypeId(userInfo.getTenantId(), travelTypeId);
        if (CollectionUtils.isNotEmpty(list)) {
            return ResponseWrap.wrapResult(AttendanceCodes.RULE_USED, Boolean.FALSE);
        }
        waTravelTypeDo.delete(travelTypeId, userInfo);
        WaTravelTypeDo typeDo = this.waTravelTypeDo.selectOneById(userInfo.getTenantId(), travelTypeId);
        LogRecordContext.putVariable("name", typeDo.getTravelTypeName());

        return Result.ok(Boolean.TRUE);
    }

    @Override
    public WaTravelTypeDo selectOneById(Long travelTypeId) {
        UserInfo userInfo = this.getUserInfo();
        return waTravelTypeDo.selectOneById(userInfo.getTenantId(), travelTypeId);
    }

    @Override
    public PageResult<WaTravelTypeDo> getTravelTypePageResult(TravelTypeListReqDto dto) {
        UserInfo userInfo = this.getUserInfo();
        dto.setTenantId(userInfo.getTenantId());
        return waTravelTypeDo.getTravelTypePageResult(dto);
    }

    @Override
    public List<KeyValue> getTravelTypeList() {
        UserInfo userInfo = this.getUserInfo();
        List<KeyValue> list = new ArrayList<>();
        List<WaTravelTypeDo> waTravelTypeList = waTravelTypeDo.getWaTravelTypeList(userInfo.getTenantId());
        waTravelTypeList.forEach(e -> {
            KeyValue keyValue = new KeyValue();
            keyValue.setText(LangParseUtil.getI18nLanguage(e.getI18nTravelTypeName(), e.getTravelTypeName()));
            keyValue.setValue(e.getTravelTypeId());
            list.add(keyValue);
        });
        return list;
    }
}
