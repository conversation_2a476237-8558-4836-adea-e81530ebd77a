package com.caidao1.integrate.reader;

import com.alibaba.fastjson.JSONObject;
import com.caidao1.commons.utils.*;
import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.caidao1.integrate.util.IntegrateUtil;
import com.caidao1.ioc.mybatis.mapper.IocImportMapper;
import com.caidao1.ioc.util.IocUtil;
import com.caidaocloud.attendance.core.commons.utils.MapUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.weibo.api.motan.core.extension.SpiMeta;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.data.util.Pair;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@SpiMeta(name = "api")
@Slf4j
public class ApiSourceReader implements SourceReader {
    /**
     * 请求时长配置
     */
    public static final String REQUEST_TIME_CONFIG = "requestTimeConfig";

    private IocImportMapper iocImportMapper;

    private static ThreadLocal<RequestConfig> requestConfigThreadLocal = new ThreadLocal<>();

    public RequestConfig initRequestConfig(Map sourceConfig) {
        return getRequestConfig(sourceConfig, requestConfigThreadLocal);
    }

    public static RequestConfig getRequestConfig(Map sourceConfig, ThreadLocal<RequestConfig> requestConfigThreadLocal) {
        if (requestConfigThreadLocal.get() == null) {
            Integer connectionRequestTimeout = 5000;
            Integer connectTimeout = 5000;
            Integer socketTimeout = 8000;
            if (sourceConfig.containsKey(REQUEST_TIME_CONFIG)) {
                Map reqTimeConfig = (Map) sourceConfig.get(REQUEST_TIME_CONFIG);
                connectionRequestTimeout = ConvertHelper.intConvert(reqTimeConfig.get("connectionRequestTimeout"));
                connectTimeout = ConvertHelper.intConvert(reqTimeConfig.get("connectTimeout"));
                socketTimeout = ConvertHelper.intConvert(reqTimeConfig.get("socketTimeout"));
            }
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectionRequestTimeout(connectionRequestTimeout)
                    .setConnectTimeout(connectTimeout)
                    .setSocketTimeout(socketTimeout).build();
            requestConfigThreadLocal.set(requestConfig);
        }
        return requestConfigThreadLocal.get();
    }

    public List<Map<String, Object>> getSourceResult(Long corpId, String belongId, SysDataInput dataInput, Map<String, Object> mapJson, Map exparams, List<String> logList,Map returnMap) throws Exception {
        if (iocImportMapper == null) iocImportMapper = SpringUtils.getBean(IocImportMapper.class);

        // 初始化当前线程任务的 requestConfig
        initRequestConfig(mapJson);

        List<Map<String, Object>> sourceResult = new ArrayList<Map<String, Object>>();

        Map authConfig = (Map) mapJson.get("auth");
        Map authMap = new HashMap();
        try (CloseableHttpClient http = HttpClients.createDefault()) {
            if (authConfig != null) {
                String method = authConfig.getOrDefault("method","").toString();
                HttpResponse response;
                if (HttpGet.METHOD_NAME.equalsIgnoreCase(method)) {
                    HttpGet httpGet = new HttpGet((String) authConfig.get("url"));
                    httpGet.setConfig(requestConfigThreadLocal.get());
                    response = http.execute(httpGet);
                } else {
                    HttpPost httpPost = new HttpPost((String) authConfig.get("url"));
                    httpPost.setConfig(requestConfigThreadLocal.get());

                    List<Map> paramsConfig =  (List<Map>) authConfig.get("params");
                    if ((Boolean) authConfig.getOrDefault("requestBody", false)) {
                        // 如果第三方接口请求参数接受方式是 RequestBody 请求体
                        JSONObject requestBody = new JSONObject();
                        for (Map map : paramsConfig) {
                            requestBody.put(map.get("name").toString(), map.get("value"));
                        }
                        httpPost.setEntity(new StringEntity(requestBody.toJSONString()));
                    }else {
                        // params 请求参数
                        List<NameValuePair> params = new ArrayList<>();
                        for (Map row : paramsConfig) {
                            params.add(new BasicNameValuePair((String) row.get("name"), (String) row.get("value")));
                        }
                        httpPost.setEntity(new UrlEncodedFormEntity(params));
                    }
                    List<Map> headersMapList = (List<Map>) authConfig.get("headers");
                    if (CollectionUtils.isNotEmpty(headersMapList)) {
                        for (Map row : headersMapList) {
                            if (row.get("value") != null) {
                                httpPost.addHeader(String.valueOf(row.get("name")), String.valueOf(row.get("value")));
                            }
                        }
                    }
                    response = http.execute(httpPost);
                }
                if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                    String content = EntityUtils.toString(response.getEntity(), "UTF-8");
                    authMap = new ObjectMapper().readValue(content, Map.class);
                }
            }

            final Map cacheMap = new HashMap();
            cacheMap.putAll(authMap);

            boolean cycle = true;
            boolean sqlCycle = false;
            Integer curPage = 1;
            while (cycle) {
                List<Pair> params = new ArrayList<>();
                List<Map> getparams = (List<Map>) mapJson.get("params");
                if (CollectionUtils.isNotEmpty(getparams)) {
                    String url = (String) mapJson.get("url");
                    for (Map row : getparams) {
                        if (row.get("value") != null) {
                            String nameKey = String.valueOf(row.get("name"));
                            Object valueKey = row.get("value");
                            //参数规则校验
                            if (row.containsKey("format")) {
                                String formatKey = String.valueOf(row.get("format"));
                                if ("sql_to_array".equalsIgnoreCase(formatKey)) {
                                    String sql = IocUtil.formatExp(corpId, belongId, valueKey.toString(), dataInput.getWorkTime());
                                    if (row.containsKey("page_size")) {
                                        sql = sql.replace(":page_size", row.get("page_size").toString()).replace(":page_start", String.valueOf((curPage - 1) * (Integer) row.get("page_size")));
                                    }
                                    List<Map<String, Object>> paramResult = iocImportMapper.queryKeyListBySql(sql);
                                    if (CollectionUtils.isNotEmpty(paramResult) && paramResult.get(0).keySet().size() == 1) {
                                        valueKey = paramResult.stream().map(pr -> pr.values().toArray()[0]).collect(Collectors.toList());
                                        if (row.containsKey("page_size")) {
                                            sqlCycle = true;
                                        } else {
                                            sqlCycle = false;
                                        }
                                    } else {
                                        valueKey = paramResult;
                                        sqlCycle = false;
                                        cycle = false;
                                    }
                                } else {
                                    //参数类型为时间
                                    String timetypeKey = String.valueOf(row.get("timetype"));
                                    if ("time_stamp".equalsIgnoreCase(timetypeKey)) {
                                        //参数为时间戳类型
                                        valueKey = String.valueOf(IntegrateUtil.getFormatTimeStamp(valueKey.toString(), formatKey));
                                    } else if("work_date".equalsIgnoreCase(timetypeKey)){
                                        if(dataInput.getWorkTime() != null){
                                            valueKey = new SimpleDateFormat(formatKey).format(new Date(dataInput.getWorkTime() * 1000L));
                                        }else{
                                            valueKey = new SimpleDateFormat(formatKey).format(new Date(0));
                                        }
                                    } else if("StringTime".equals(timetypeKey)){
                                        valueKey = IntegrateUtil.getStringTime(valueKey.toString(), formatKey, Long.valueOf(row.get("timeAdd").toString()));
                                    }else {
                                        valueKey = IntegrateUtil.formatExp(valueKey.toString(), formatKey);
                                    }
                                }
                            } else if (valueKey.equals(":pager.page")) {
                                if(mapJson.containsKey("IntegerPage")){
                                    valueKey = curPage;
                                }else{
                                    valueKey = curPage.toString();
                                }
                            } else if (valueKey.toString().startsWith(":params.")) {
                                valueKey = (String) MapUtil.getValue(exparams, valueKey.toString().substring(8));
                            }
                            if ((boolean) row.getOrDefault("location", false)) {
                                mapJson.put("url", url.replaceAll(":" + nameKey, valueKey.toString()));
                            } else {
                                params.add(Pair.of(nameKey, valueKey));
                            }
                        } else if (row.get("token_name") != null) {
                            if ((boolean) row.getOrDefault("location", false)) {
                                mapJson.put("url", url.replaceAll(":" + (String) row.get("token_name"), (String) MapUtil.getValue(authMap, (String) row.get("name"))));
                            } else {
                                params.add(Pair.of((String) row.get("token_name"), (String) MapUtil.getValue(authMap, (String) row.get("name"))));
                            }
                        } else {
                            if ((boolean) row.getOrDefault("location", false)) {
                                mapJson.put("url", url.replaceAll(":" + (String) row.get("name"), (String) MapUtil.getValue(authMap, (String) row.get("name"))));
                            } else {
                                params.add(Pair.of((String) row.get("name"), MapUtil.getValue(authMap, (String) row.get("name"))));
                            }
                        }
                    }
                }

                if (cycle) {
                    Header[] headers = null;
                    List<Map> getHeaders = (List<Map>) mapJson.get("headers");
                    if (CollectionUtils.isNotEmpty(getHeaders)) {
                        headers = new Header[getHeaders.size()];
                        for (int i = 0; i < getHeaders.size(); i++) {
                            Map header = getHeaders.get(i);
                            Header header1 = new Header() {
                                @Override
                                public String getName() {
                                    return (String) header.get("name");
                                }

                                @Override
                                public String getValue() {
                                    String ov = (String) header.get("value");
                                    if(ov.contains("#")){
                                        String lastNodeToken = IntegrateUtil.findLastNodeToken(cacheMap, ov);
                                        return lastNodeToken;
                                    }else {
                                        StringBuilder builder = new StringBuilder();
                                        Matcher m = Pattern.compile("\\$\\{(.+?)\\s\\}").matcher(ov);
                                        int start = 0;
                                        while(m.find()){
                                            builder.append(ov.substring(start, m.start())).append(cacheMap.get(m.group(1).trim()));
                                            start = m.end();
                                        }
                                        return builder.append(ov.substring(start)).toString();
                                    }
                                }
                                @Override
                                public HeaderElement[] getElements() throws ParseException {
                                    return new HeaderElement[0];
                                }
                            };
                            headers[i] = header1;
                        }
                    }

                    String methodType = String.valueOf(mapJson.get("method"));
                    HttpResponse response;
                    if ("GET".equalsIgnoreCase(methodType)) {
                        List<BasicNameValuePair> nvparams = params.stream().map(param -> new BasicNameValuePair(param.getFirst().toString(), param.getSecond().toString())).collect(Collectors.toList());
                        StringBuffer urlbuf = new StringBuffer(String.valueOf(mapJson.get("url")));
                        if (CollectionUtils.isNotEmpty(params)) {
                            if (urlbuf.toString().contains("?")) {
                                urlbuf = urlbuf.append("&");
                            } else {
                                urlbuf = urlbuf.append("?");
                            }
                            urlbuf.append(URLEncodedUtils.format(nvparams, "UTF-8"));
                        }
                        HttpGet httpGet = new HttpGet(urlbuf.toString());
                        httpGet.setConfig(requestConfigThreadLocal.get());
                        if (headers != null) {
                            httpGet.setHeaders(headers);
                        }
                        response = http.execute(httpGet);
                    } else {
                        HttpPost httppost = new HttpPost((String) mapJson.get("url"));
                        httppost.setConfig(requestConfigThreadLocal.get());
                        if (mapJson.getOrDefault("content_type", "").equals("application/json")) {
                            Map kvPrams = params.stream().collect(Collectors.toMap(Pair::getFirst, Pair::getSecond));
                            StringEntity stringEntity = new StringEntity(new ObjectMapper().writeValueAsString(kvPrams), "UTF-8");
                            stringEntity.setContentType((String) mapJson.get("content_type"));
                            httppost.setEntity(stringEntity);
                        } else {
                            List<BasicNameValuePair> nvparams = params.stream().map(param -> new BasicNameValuePair(param.getFirst().toString(), param.getSecond().toString())).collect(Collectors.toList());
                            UrlEncodedFormEntity encodedFormEntity = new UrlEncodedFormEntity(nvparams, "UTF-8");
                            httppost.setEntity(encodedFormEntity);
                        }
                        if (headers != null) {
                            httppost.setHeaders(headers);
                        }
                        response = http.execute(httppost);
                    }
                    if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                        String content = EntityUtils.toString(response.getEntity(), "UTF-8");
                        if (StringUtils.isNotEmpty(dataInput.getSourceGreen())) {
                            if (dataInput.getSourceGreen().startsWith("regex:")) {
                                if (content.replaceAll("\r?\n", "").matches(dataInput.getSourceGreen().substring(6))) {
                                    logList.add("执行成功:" + content);
                                } else {
                                    logList.add("执行失败:" + content);
                                    throw new RuntimeException(content);
                                }
                            }
                        }
                        Map result;
                        if (mapJson.containsKey("result_path")) {
                            result = new ObjectMapper().readValue(content, Map.class);
                            List<Map<String, Object>> list = (List<Map<String, Object>>) MapUtil.getValue(result, (String) mapJson.get("result_path"));
                            if(CollectionUtils.isNotEmpty(list)){
                                sourceResult.addAll(list);
                            }else{
                                break;
                            }
                        }else{
                            sourceResult = new ObjectMapper().readValue(content, List.class);
                            break;
                        }
                        if (mapJson.containsKey("pager")) {
                            if(result == null){
                                result = new ObjectMapper().readValue(content, Map.class);
                            }
                            Integer size;
                            if(StringUtils.isNumeric(MapUtil.getValue(mapJson, "pager.size").toString())){
                                size = Integer.valueOf(MapUtil.getValue(mapJson, "pager.size").toString());
                            }else{
                                size = (Integer) MapUtil.getValue(result, MapUtil.getValue(mapJson, "pager.size").toString());
                            }
                            Integer total = (Integer) MapUtil.getValue(result, (String) MapUtil.getValue(mapJson, "pager.total"));

                            Integer totalPage = (total + size - 1) / size;
                            if (totalPage > curPage) {
                                curPage++;
                            } else {
                                cycle = false;
                            }
                        } else {
                            if (!sqlCycle) {
                                cycle = false;
                            } else {
                                curPage++;
                            }
                        }
                    } else {
                        cycle = false;
                    }
                }
            }
        }finally {
            requestConfigThreadLocal.remove();
        }
        return sourceResult;
    }
}