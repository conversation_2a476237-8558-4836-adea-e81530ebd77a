package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.EmpOverInfo;
import com.caidaocloud.attendance.core.wa.dto.WaLeaveDaytimeExtDto;
import com.caidaocloud.attendance.service.application.dto.clock.ClockAnalyseDataCacheDto;
import com.caidaocloud.attendance.service.application.event.publish.MultiNodeAnalyseClockPublish;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.attendance.service.interfaces.dto.BatchClockAnalyseDto;
import com.caidaocloud.attendance.service.interfaces.dto.BatchClockAnalyseResultDto;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ClockSignService implements IClockSignService {
    @Value("${caidaocloud.data.clockanalyse.version:V1}")
    private String clockAnalyseVersion;
    @Value("${caidaocloud.data.clockanalyse.multinode:false}")
    private boolean multinode;
    @Value("${caidaocloud.data.clockanalyse.batchsize:300}")
    private Integer batchsize;

    @Autowired
    private IRegisterRecordService registerRecordService;
    @Autowired
    private IScheduleQueryService scheduleQueryService;
    @Autowired
    private ILeaveApplyService leaveApplyService;
    @Autowired
    private IOvertimeApplyService overtimeApplyService;
    @Autowired
    private MultiNodeAnalyseClockPublish multiNodeAnalyseClockPublish;
    @Autowired
    private CacheService cacheService;

    public IClockAnalyseService getClockAnalyseService() {
        return IClockAnalyseService.versionTypeManager.get(clockAnalyseVersion);
    }

    /**
     * 分析打卡记录
     *
     * @param belongOrgId
     * @param empIds
     * @param date
     * @param type
     */
    @Override
    public void analyseRegisterRecord(String belongOrgId, List<Long> empIds, Long date, Integer type) {
        if (CollectionUtils.isEmpty(empIds)) {
            log.warn("ClockSignService.analyseRegisterRecord fail msg: emp empty");
            return;
        }
        if (StringUtils.isBlank(belongOrgId)) {
            belongOrgId = UserContext.getTenantId();
        }
        List<List<Long>> empIdLists = ListTool.split(empIds, 300);
        for (List<Long> empIdList : empIdLists) {
            try {
                ClockAnalyseDataCacheDto dataCacheDto = ClockAnalyseDataCacheDto.doBuild();
                getClockAnalyseService().analyseRegisterRecord(belongOrgId, empIdList, date, type, dataCacheDto);
            } catch (Exception e) {
                log.error("ClockSignService.analyseRegisterRecord fail failDate={}, failEmp={}, msg={}",
                        date, FastjsonUtil.toJsonStr(empIdList), e.getMessage(), e);
            }
        }
    }

    @Override
    public BatchClockAnalyseResultDto analyseByDateRange(BatchClockAnalyseDto dto) {
        long startTime = System.currentTimeMillis();
        log.info("ClockSignService.analyseByDateRange begin belongOrgId={} startDate={} endDate={} empIds={}",
                dto.getBelongOrgId(), dto.getStartDate(), dto.getEndDate(),
                CollectionUtils.isEmpty(dto.getEmpIds()) ? "auto" : dto.getEmpIds().size());

        dto.validateAnalyseParams();
        dto.doInitDate();
        dto.validateDateRange();

        // 获取有效员工ID列表
        List<Long> empIds = getValidEmpIds(dto.getBelongOrgId(), dto.getStartDate(), dto.getEndDate(), dto.getEmpIds());
        if (CollectionUtils.isEmpty(empIds)) {
            log.warn("ClockSignService.analyseByDateRange no valid employees found");
            return BatchClockAnalyseResultDto.doBuild();
        }

        log.info("ClockSignService.analyseByDateRange processing {} employees", empIds.size());

        // 初始化任务进度
        int total = empIds.size();
        ClockAnalyseProgressUpd.initProgress(dto.getProgress(), total, cacheService);

        // 分批处理
        if (multinode && dto.isMultinode()) {
            // 多节点
            List<List<Long>> empIdBatches = ListTool.split(empIds, batchsize);
            for (int i = 0; i < empIdBatches.size(); i++) {
                List<Long> empIdBatch = empIdBatches.get(i);
                dto.setEmpIds(empIdBatch);
                multiNodeAnalyseClockPublish.publishForObj(dto);
            }
        } else {
            List<List<Long>> empIdBatches = ListTool.split(empIds, 300);
            for (int i = 0; i < empIdBatches.size(); i++) {
                List<Long> empIdBatch = empIdBatches.get(i);
                int currentSize = empIdBatch.size();
                try {
                    log.info("Processing batch {}/{} with {} employees", i + 1, empIdBatches.size(), empIdBatch.size());
                    processEmpBatch(dto.getBelongOrgId(), dto.getStartDate(), dto.getEndDate(), empIdBatch, dto.getType());
                } catch (Exception e) {
                    log.error("Failed to process batch {}/{}, empCount={}, error={}",
                            i + 1, empIdBatches.size(), empIdBatch.size(), e.getMessage(), e);
                }
                // 更新进度
                ClockAnalyseProgressUpd.doUpdateProgress(dto.getProgress(), currentSize, cacheService);
            }
        }

        log.info("ClockSignService.analyseByDateRange completed: totalEmps={}, duration={}ms", empIds.size(), System.currentTimeMillis() - startTime);
        return BatchClockAnalyseResultDto.doBuild(empIds);
    }

    private List<Long> getValidEmpIds(String belongOrgId, Long startDate, Long endDate, List<Long> empIds) {
        return CollectionUtils.isEmpty(empIds)
                ? registerRecordService.getRegEmpIdList(belongOrgId, startDate, endDate + 86399)
                : empIds;
    }

    @Override
    public void doExeBatchAnalyse(BatchClockAnalyseDto dto) {
        log.info("doExeBatchAnalyse.start time={}", System.currentTimeMillis());

        processEmpBatch(dto.getBelongOrgId(), dto.getStartDate(), dto.getEndDate(), dto.getEmpIds(), dto.getType());

        log.info("doExeBatchAnalyse.end time={}", System.currentTimeMillis());

        // TODO 更新进度， 会存在多节点多消费者同时消费并发问题
        ClockAnalyseProgressUpd.doUpdateProgress(dto.getProgress(), dto.getEmpIds().size(), cacheService);
    }

    public void processEmpBatch(String belongOrgId, Long startDate, Long endDate, List<Long> empIdList, Integer type) {
        long batchStartTime = System.currentTimeMillis();
        log.info("processEmpBatch.Processing employee batch: {} employees from {} to {}", empIdList.size(), startDate, endDate);

        ClockAnalyseDataCacheDto dataCacheDto = null;
        try {
            Long minDate = DateUtil.addDate(startDate * 1000, -1);
            Long maxDate = DateUtil.addDate(endDate * 1000, 1);

            log.info("processEmpBatch.query start time={}", System.currentTimeMillis());

            // 查询打卡记录
            List<WaRegisterRecordDo> waRegList = getClockAnalyseService().getRegisterRecordList(belongOrgId, empIdList, minDate, maxDate, type);
            if (CollectionUtils.isEmpty(waRegList)) {
                log.warn("No register records found for {} employees in date range", empIdList.size());
                return;
            }
            log.info("Found {} register records for batch", waRegList.size());

            // 查询排班信息
            Map<String, WaShiftDo> empShiftDoMap = scheduleQueryService.getEmpCalendarShiftMap(belongOrgId, minDate, maxDate, empIdList);
            if (MapUtils.isEmpty(empShiftDoMap)) {
                log.warn("No shift schedules found for {} employees in date range", empIdList.size());
                return;
            }
            log.info("Found {} shift schedules for batch", empShiftDoMap.size());

            // 查询考勤规则
            List<EmpParseGroup> empParseList = registerRecordService.selectEmpParseGroupListByDateRange(belongOrgId, empIdList, minDate, maxDate);
            log.info("Found {} attendance rules for batch", CollectionUtils.isEmpty(empParseList) ? 0 : empParseList.size());

            // 如果班次上开启了打卡分析联动加班单据时（设置了任意加班）则去查询加班记录
            List<Long> empIdListByOt = getClockAnalyseService().getEmpIdListByOt(empShiftDoMap);
            List<EmpOverInfo> empOtList = overtimeApplyService.getEmpDailyOtList(belongOrgId, empIdListByOt,
                    minDate, maxDate + 86399);

            // 查询休假数据
            List<WaLeaveDaytimeExtDto> leaveList = leaveApplyService.getEmpLeaveDayTimeExtDtoList(belongOrgId, empIdList,
                    minDate, endDate, empShiftDoMap);

            log.info("processEmpBatch.query end time={}", System.currentTimeMillis());

            // 创建数据缓存对象
            dataCacheDto = ClockAnalyseDataCacheDto.doBuild(waRegList, empShiftDoMap, empParseList, empOtList, leaveList);

            log.info("processEmpBatch.analyse start time={}", System.currentTimeMillis());

            // 按日期循环分析
            long totalDays = (endDate - startDate) / 86400 + 1;
            int processedDays = 0;

            long clockDate = startDate;
            while (clockDate <= endDate) {
                try {
                    processDateAnalyse(belongOrgId, empIdList, clockDate, type, dataCacheDto);
                    processedDays++;
                } catch (Exception e) {
                    log.error("Failed to process date {}: {}", clockDate, e.getMessage(), e);
                }
                clockDate += 86400;
            }

            log.info("processEmpBatch.analyse end time={}", System.currentTimeMillis());

            // 释放内存
            clearLargeCollections(waRegList, empShiftDoMap, empParseList, empOtList, leaveList);

            long batchDuration = System.currentTimeMillis() - batchStartTime;
            log.info("Batch processing completed: {} employees, {}/{} days processed, duration={}ms",
                    empIdList.size(), processedDays, totalDays, batchDuration);
        } catch (Exception e) {
            long batchDuration = System.currentTimeMillis() - batchStartTime;
            log.error("Employee batch processing failed after {}ms for {} employees: {}",
                    batchDuration, empIdList.size(), e.getMessage(), e);
            throw e;
        } finally {
            if (dataCacheDto != null) {
                ClockAnalyseDataCacheDto.doClear(dataCacheDto);
            }
        }
        log.info("Processing employee batch: {} employees from {} to {} End", empIdList.size(), startDate, endDate);
    }

    public void processDateAnalyse(String belongOrgId, List<Long> empIdList, Long clockDate, Integer type,
                                   ClockAnalyseDataCacheDto dataCacheDto) {
        long dateStartTime = System.currentTimeMillis();
        log.debug("Processing date analysis: clockDate={}, empCount={}", clockDate, empIdList.size());

        try {
            getClockAnalyseService().analyseRegisterRecord(belongOrgId, empIdList, clockDate, type, dataCacheDto);

            long dateProcessTime = System.currentTimeMillis() - dateStartTime;
            log.debug("Date analysis completed: clockDate={}, empCount={}, duration={}ms",
                    clockDate, empIdList.size(), dateProcessTime);

        } catch (Exception e) {
            long dateProcessTime = System.currentTimeMillis() - dateStartTime;
            log.error("Date analysis failed: clockDate={}, empCount={}, duration={}ms, error={}",
                    clockDate, empIdList.size(), dateProcessTime, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 清理大对象集合以释放内存
     *
     * @param waRegList     打卡记录列表
     * @param empShiftDoMap 员工排班映射
     * @param empParseList  考勤规则列表
     * @param empOtList     加班记录列表
     * @param leaveList     休假记录列表
     */
    private void clearLargeCollections(List<WaRegisterRecordDo> waRegList,
                                       Map<String, WaShiftDo> empShiftDoMap,
                                       List<EmpParseGroup> empParseList,
                                       List<EmpOverInfo> empOtList,
                                       List<WaLeaveDaytimeExtDto> leaveList) {
        log.info("Clearing large collections to reduce memory usage - waRegList: {}, empShiftDoMap: {}, empParseList: {}, empOtList: {}, leaveList: {}",
                waRegList.size(), empShiftDoMap.size(), empParseList.size(), empOtList.size(), leaveList.size());
        waRegList.clear();
        empShiftDoMap.clear();
        empParseList.clear();
        empOtList.clear();
        leaveList.clear();
        System.gc();
        log.info("Large collections cleared successfully. Memory optimization completed - all collections emptied");
    }
}
