package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IEmpGroupRepository;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpGroupPo;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupDto;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupReqDto;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Data
@Slf4j
@Service
public class AttEmpGroupDo {

    private Integer empGroupId;
    private Integer waGroupId;
    private Long empId;
    private Long startTime;
    private Long endTime;
    private Long createTime;
    private Long createUser;
    private Long updateTime;
    private Long updateUser;
    private String workNo;
    private String empName;
    private String fullPath;
    private String orgName;
    private String waGroupName;
    private String updWorkNo;
    private String operator;

    private Integer empStatus;
    private Long empStyle;
    private Long hireDate;
    private Long terminationDate;
    private String i18nWaGroupName;

    @Resource
    private IEmpGroupRepository empGroupRepository;

    public AttendancePageResult<AttEmpGroupDo> getWaEmpGroupList(AttEmpGroupReqDto dto) {
        return empGroupRepository.getWaEmpGroupList(dto);
    }

    public List<AttEmpGroupDo> getWaEmpGroupListByPeriod(AttEmpGroupDto dto) {
        List<AttEmpGroupDo> list = empGroupRepository.getWaEmpGroupListByPeriod(dto);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(list, AttEmpGroupDo.class);
    }

    public AttEmpGroupDo getWaEmpGroupById(Integer id) {
        return empGroupRepository.getWaEmpGroupById(id);
    }

    public void deleteWaEmpGroup(List<Integer> ids) {
        empGroupRepository.deleteWaEmpGroup(ids);
    }

    public void saveWaEmpGroup(AttEmpGroupDo attEmpGroup) {
        if (null == attEmpGroup) {
            return;
        }
        WaEmpGroupPo waEmpGroupPo = ObjectConverter.convert(attEmpGroup, WaEmpGroupPo.class);
        empGroupRepository.saveWaEmpGroup(waEmpGroupPo);

    }

    public void updateWaEmpGroup(AttEmpGroupDo attEmpGroup) {
        if (null == attEmpGroup) {
            return;
        }
        WaEmpGroupPo waEmpGroupPo = ObjectConverter.convert(attEmpGroup, WaEmpGroupPo.class);
        empGroupRepository.updateWaEmpGroup(waEmpGroupPo);
    }

    public List<AttEmpGroupDo> getWaEmpGroupByIds(List<Integer> ids) {
        return ObjectConverter.convertList(empGroupRepository.getWaEmpGroupByIds(ids), AttEmpGroupDo.class);
    }

    public List<AttEmpGroupDo> getEmpGroup(String belongOrgId, Long empId, Long currentTime) {
        return ObjectConverter.convertList(empGroupRepository.getEmpGroup(belongOrgId, empId, currentTime), AttEmpGroupDo.class);
    }

    public List<AttEmpGroupDo> getEmpGroupByEmpIds(String belongOrgId, List<Long> empIds, Long currentTime) {
        return ObjectConverter.convertList(empGroupRepository.getEmpGroupByEmpIds(belongOrgId, empIds, currentTime), AttEmpGroupDo.class);
    }

    public List<AttEmpGroupDo> getEmpGroupByEmpIds(String tenantId, List<Long> empIds, Long startDate, Long endDate) {
        return ObjectConverter.convertList(empGroupRepository.getEmpGroupByEmpIds(tenantId, empIds, startDate, endDate), AttEmpGroupDo.class);
    }
}
