package com.caidaocloud.attendance.sdk.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 销假申请
 */
@Data
public class SdkLeaveCancelDTO {
    @ApiModelProperty("休假单记录ID")
    private Integer leaveId;
    @ApiModelProperty("销假事由")
    private String reason;
    @ApiModelProperty("文件名称，多个文件使用逗号隔开")
    private String fileName;
    @ApiModelProperty("文件ID，多个文件使用逗号隔开")
    private String fileId;
    @ApiModelProperty("销假类型")
    private Integer leaveCancelType;
    @ApiModelProperty("销假时间段集合")
    private List<SdkLeaveCancelInfoDTO> timeInfoList;

    @ApiModelProperty("开始日期")
    private Long startTime;
    @ApiModelProperty("结束日期")
    private Long endTime;
    @ApiModelProperty("是否选择半天 1 选择 0 不选择")
    private Integer showDay;
    @ApiModelProperty("是否选择小时 1 选择 0 不选择")
    private Integer showMin;
    @ApiModelProperty("半天开始 A 上半天 P 下半天")
    private String shalfDay;
    @ApiModelProperty("半天结束 A 上半天 P 下半天")
    private String ehalfDay;
    @ApiModelProperty("开始时间 类型分钟 ")
    private Integer stime;
    @ApiModelProperty("结束时间 类型分钟 ")
    private Integer etime;
    @ApiModelProperty("持续时间")
    private String duration;
    @ApiModelProperty("每日申请时长")
    private Float dailyDuration;

    @ApiModelProperty("开始日期所选的班次集合（一天排多个班次且假期时间类型为天时使用），时间调整")
    private List<Long> startShifts;
    @ApiModelProperty("结束日期所选的班次集合（一天排多个班次且假期时间类型为天时使用），时间调整")
    private List<Long> endShifts;
}
