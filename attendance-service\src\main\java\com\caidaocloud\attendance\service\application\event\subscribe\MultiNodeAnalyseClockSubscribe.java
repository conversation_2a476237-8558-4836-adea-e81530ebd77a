package com.caidaocloud.attendance.service.application.event.subscribe;

import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.service.application.config.AttendanceMqProperties;
import com.caidaocloud.attendance.service.application.config.ThreadPoolConfig;
import com.caidaocloud.attendance.service.application.service.IClockSignService;
import com.caidaocloud.attendance.service.interfaces.dto.BatchClockAnalyseDto;
import com.caidaocloud.util.FastjsonUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 多节点打卡分析消息消费者
 */
@Slf4j
@Component
public class MultiNodeAnalyseClockSubscribe {

    @Autowired
    private IClockSignService clockSignService;

    @Autowired
    private AttendanceMqProperties mqProperties;

    @Autowired
    @Qualifier("asyncProcessingExecutor")
    private ThreadPoolTaskExecutor asyncProcessingExecutor;

    @Autowired
    @Qualifier("timeoutCheckExecutor")
    private ThreadPoolTaskExecutor timeoutCheckExecutor;

    @RabbitHandler
    @RabbitListener(
            queues = "attendance.clock.analyse.multinode.queue",  // 直接使用队列名，不重新声明
            containerFactory = "manualAckContainerFactory"  // 使用手动ACK的容器工厂
    )
    public void process(String message, Channel channel,
                        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                        Message amqpMessage) {

        long processStartTime = System.currentTimeMillis();
        log.info("MultiNodeAnalyseClockSubscribe consume msg={}, deliveryTag={}, processStartTime={}",
                message, deliveryTag, processStartTime);

        // 从Message对象获取headers
        Map<String, Object> headers = amqpMessage.getMessageProperties().getHeaders();

        // 获取当前重试次数
        int currentRetryCount = getCurrentRetryCount(headers);
        long consumerTimeout = mqProperties.getConsumer().getConsumerTimeout();
        log.info("Current retry count: {}, max retry count: {}, enableDLQ: {}, consumerTimeout: {}ms, " +
                        "timeoutAutoAck: {}",
                currentRetryCount, mqProperties.getMaxRetryCount(), mqProperties.isEnableDLQ(),
                consumerTimeout, mqProperties.getConsumer().isEnableTimeoutAutoAck());

        // 计算安全ACK时间
        long safeAckTime = (long) (consumerTimeout * mqProperties.getConsumer().getTimeoutAckRatio());

        try {
            BatchClockAnalyseDto dto = FastjsonUtil.convertObject(message, BatchClockAnalyseDto.class);

            // 预估业务处理时间
            long estimatedProcessTime = estimateProcessingTime(dto);
            log.info("Estimated processing time: {}ms, safe ACK time: {}ms, timeoutAckRatio: {}",
                    estimatedProcessTime, safeAckTime, mqProperties.getConsumer().getTimeoutAckRatio());

            // 检查是否启用超时自动ACK机制
            if (!mqProperties.getConsumer().isEnableTimeoutAutoAck()) {
                log.info("CONFIG: Timeout auto-ACK disabled, using traditional processing");
                processTraditionally(dto, channel, deliveryTag, processStartTime);
                return;
            }

            // 如果预估时间超过安全时间，立即ACK处理
            if (estimatedProcessTime > safeAckTime) {
                log.warn("WARN: Estimated processing time ({}ms) exceeds safe ACK time ({}ms). " +
                        "Will ACK immediately.", estimatedProcessTime, safeAckTime);

                // 立即ACK避免超时
                channel.basicAck(deliveryTag, false);
                log.info("SUCCESS: Message ACKed immediately to prevent timeout redelivery, deliveryTag={}", deliveryTag);

                // 根据配置决定是否继续处理
                if (mqProperties.getConsumer().isContinueProcessingAfterTimeoutAck()) {
                    processAsynchronously(dto, message, processStartTime);
                } else {
                    log.info("SKIP: Skipping business processing as continueProcessingAfterTimeoutAck=false");
                    recordSkippedProcessing(message, estimatedProcessTime, "Estimated time exceeds safe ACK time");
                }
                return;
            }

            // 执行同步业务处理（带超时检测）
            processSynchronouslyWithTimeout(dto, channel, deliveryTag, processStartTime, safeAckTime);

        } catch (Exception ex) {
            long totalDuration = System.currentTimeMillis() - processStartTime;
            log.error("MultiNodeAnalyseClockSubscribe consume fail，retryCount={}, enableDLQ={}, " +
                            "totalDuration={}ms, errorMsg:{}",
                    currentRetryCount, mqProperties.isEnableDLQ(), totalDuration, ex.getMessage(), ex);

            try {
                // 检查是否启用超时自动ACK且接近超时
                if (mqProperties.getConsumer().isEnableTimeoutAutoAck() && totalDuration > safeAckTime) {
                    log.warn("WARN: Processing failed near timeout ({}ms > {}ms). ACKing to prevent redelivery.",
                            totalDuration, safeAckTime);
                    channel.basicAck(deliveryTag, false);

                    // 记录超时失败情况
                    recordTimeoutFailure(message, ex, totalDuration);
                    return;
                }

                // 正常的重试和失败处理逻辑
                boolean shouldRetry = shouldRequeue(ex, currentRetryCount);

                if (shouldRetry) {
                    channel.basicNack(deliveryTag, false, true);
                    log.info("Message nacked and requeued for retry, deliveryTag={}, retryCount={}",
                            deliveryTag, currentRetryCount + 1);
                } else {
                    handleFailedMessage(channel, deliveryTag, currentRetryCount, ex);
                }

            } catch (Exception ackEx) {
                log.error("Failed to handle message acknowledgment, deliveryTag={}, error:{}", deliveryTag, ackEx.getMessage(), ackEx);
            }
        }
    }

    /**
     * 同步处理业务逻辑（带超时检测）
     */
    private void processSynchronouslyWithTimeout(BatchClockAnalyseDto dto, Channel channel, long deliveryTag,
                                                 long processStartTime, long safeAckTime) throws Exception {

        log.info("Starting synchronous business processing, empCount: {}, dateRange: {}-{}",
                dto.getEmpIds() != null ? dto.getEmpIds().size() : 0,
                dto.getStartDate(), dto.getEndDate());

        long businessStartTime = System.currentTimeMillis();

        // 检查超时检测线程池健康状态
        checkTimeoutCheckPoolHealth();

        // 启动超时检测任务（使用线程池）
        TimeoutChecker timeoutChecker = new TimeoutChecker(channel, deliveryTag, processStartTime, safeAckTime);
        java.util.concurrent.Future<?> timeoutTask = timeoutCheckExecutor.submit(timeoutChecker);
        
        log.debug("Timeout check task submitted - deliveryTag: {}, poolActive: {}, poolSize: {}, queueSize: {}",
                deliveryTag, timeoutCheckExecutor.getActiveCount(),
                timeoutCheckExecutor.getPoolSize(),
                timeoutCheckExecutor.getThreadPoolExecutor().getQueue().size());

        try {
            // 执行业务逻辑
            clockSignService.doExeBatchAnalyse(dto);
            long businessDuration = System.currentTimeMillis() - businessStartTime;

            // 停止超时检测任务
            timeoutChecker.stop();
            timeoutTask.cancel(true); // 中断任务执行

            // 检查是否已经被超时线程ACK了
            if (timeoutChecker.isAckedByTimeout()) {
                log.info("SUCCESS: Message was already ACKed by timeout checker, business completed in {}ms", businessDuration);
                return;
            }

            // 正常ACK
            channel.basicAck(deliveryTag, false);

            long totalDuration = System.currentTimeMillis() - processStartTime;
            log.info("SUCCESS: MultiNodeAnalyseClockSubscribe consume success and ack, businessDuration={}ms, " +
                            "totalDuration={}ms, end time {}",
                    businessDuration, totalDuration, DateUtil.getCurrentTime(true));

        } catch (Exception ex) {
            // 停止超时检测任务
            timeoutChecker.stop();
            timeoutTask.cancel(true); // 中断任务执行

            // 如果已经被超时线程ACK了，只记录错误，不重新处理
            if (timeoutChecker.isAckedByTimeout()) {
                log.error("ERROR: Business processing failed but message was already ACKed by timeout checker: {}",
                        ex.getMessage(), ex);
                recordTimeoutFailure(FastjsonUtil.toJson(dto), ex, System.currentTimeMillis() - processStartTime);
                return;
            }

            // 重新抛出异常给外层处理
            throw ex;
        }
    }

    /**
     * 异步处理业务逻辑
     * 使用线程池避免频繁创建线程导致的资源浪费
     */
    private void processAsynchronously(BatchClockAnalyseDto dto, String originalMessage, long processStartTime) {
        // 检查线程池健康状态
        checkAsyncProcessingPoolHealth();
        
        // 提交任务到线程池
        asyncProcessingExecutor.submit(() -> {
            try {
                long asyncStartTime = System.currentTimeMillis();
                int empCount = dto.getEmpIds() != null ? dto.getEmpIds().size() : 0;
                String threadName = Thread.currentThread().getName();
                
                log.info("PROCESSING: Starting asynchronous business processing - thread: {}, empCount: {}, " +
                        "poolActive: {}, poolSize: {}, queueSize: {}",
                        threadName, empCount, 
                        asyncProcessingExecutor.getActiveCount(),
                        asyncProcessingExecutor.getPoolSize(),
                        asyncProcessingExecutor.getThreadPoolExecutor().getQueue().size());

                clockSignService.doExeBatchAnalyse(dto);

                long asyncDuration = System.currentTimeMillis() - asyncStartTime;
                long totalDuration = System.currentTimeMillis() - processStartTime;

                log.info("SUCCESS: Asynchronous processing completed successfully! thread: {}, " +
                                "asyncDuration={}ms, totalDuration={}ms, end time {}, " +
                                "poolActive: {}, poolSize: {}",
                        threadName, asyncDuration, totalDuration, DateUtil.getCurrentTime(true),
                        asyncProcessingExecutor.getActiveCount(), asyncProcessingExecutor.getPoolSize());

            } catch (Exception ex) {
                long totalDuration = System.currentTimeMillis() - processStartTime;
                String threadName = Thread.currentThread().getName();
                
                log.error("ERROR: Asynchronous processing failed - thread: {}, duration: {}ms, " +
                        "poolActive: {}, error: {}",
                        threadName, totalDuration, asyncProcessingExecutor.getActiveCount(), ex.getMessage(), ex);

                // 记录异步处理失败
                recordAsyncProcessingFailure(originalMessage, ex, totalDuration);
            }
        });
    }

    /**
     * 记录超时失败情况
     */
    private void recordTimeoutFailure(String message, Exception ex, long duration) {
        log.error("RECORD: Recording timeout failure - duration: {}ms, error: {}, message: {}",
                duration, ex.getMessage(), message);

        // TODO: 根据业务需求实现以下逻辑
        // 1. 记录到数据库供后续分析
        // 2. 发送告警通知
        // 3. 标记相关业务数据状态
        // 4. 生成重新处理任务

        // 示例实现：
        // failedMessageService.recordTimeoutFailure(message, ex.getMessage(), duration);
        // alertService.sendTimeoutAlert(message, duration);
    }

    /**
     * 记录异步处理失败情况
     */
    private void recordAsyncProcessingFailure(String message, Exception ex, long duration) {
        log.error("RECORD: Recording async processing failure - duration: {}ms, error: {}, message: {}",
                duration, ex.getMessage(), message);

        // TODO: 根据业务需求实现
        // failedMessageService.recordAsyncFailure(message, ex.getMessage(), duration);
        // alertService.sendAsyncProcessingFailureAlert(message, duration);
    }

    /**
     * 传统处理方式（不启用超时自动ACK）
     */
    private void processTraditionally(BatchClockAnalyseDto dto, Channel channel, long deliveryTag,
                                      long processStartTime) throws Exception {
        log.info("Starting traditional business processing, empCount: {}, dateRange: {}-{}",
                dto.getEmpIds() != null ? dto.getEmpIds().size() : 0,
                dto.getStartDate(), dto.getEndDate());

        long businessStartTime = System.currentTimeMillis();

        try {
            // 执行业务逻辑
            clockSignService.doExeBatchAnalyse(dto);
            long businessDuration = System.currentTimeMillis() - businessStartTime;

            // 正常ACK
            channel.basicAck(deliveryTag, false);

            long totalDuration = System.currentTimeMillis() - processStartTime;
            log.info("SUCCESS: Traditional processing completed and ACKed, businessDuration={}ms, " +
                            "totalDuration={}ms, end time {}",
                    businessDuration, totalDuration, DateUtil.getCurrentTime(true));

        } catch (Exception ex) {
            // 重新抛出异常给外层处理
            throw ex;
        }
    }

    /**
     * 记录跳过处理的情况
     */
    private void recordSkippedProcessing(String message, long estimatedTime, String reason) {
        log.warn("RECORD: Recording skipped processing - estimatedTime: {}ms, reason: {}, message: {}",
                estimatedTime, reason, message);

        // TODO: 根据业务需求实现
        // skippedMessageService.record(message, estimatedTime, reason);
        // alertService.sendSkippedProcessingAlert(message, estimatedTime, reason);
    }

    /**
     * 超时检测器内部类
     */
    private class TimeoutChecker implements Runnable {
        private final Channel channel;
        private final long deliveryTag;
        private final long processStartTime;
        private final long safeAckTime;
        private final long checkInterval;
        private volatile boolean stopped = false;
        private volatile boolean ackedByTimeout = false;

        public TimeoutChecker(Channel channel, long deliveryTag, long processStartTime, long safeAckTime) {
            this.channel = channel;
            this.deliveryTag = deliveryTag;
            this.processStartTime = processStartTime;
            this.safeAckTime = safeAckTime;
            this.checkInterval = mqProperties.getConsumer().getTimeoutCheckInterval();
        }

        @Override
        public void run() {
            String threadName = Thread.currentThread().getName();
            try {
                log.debug("Timeout checker started - thread: {}, deliveryTag: {}, checkInterval: {}ms", 
                        threadName, deliveryTag, checkInterval);

                while (!stopped && !Thread.currentThread().isInterrupted()) {
                    long currentDuration = System.currentTimeMillis() - processStartTime;

                    // 检查是否接近超时
                    if (currentDuration >= safeAckTime) {
                        log.warn("TIMEOUT: Timeout detected! thread: {}, processingTime: {}ms, safeTime: {}ms, " +
                                "ratio: {}, deliveryTag: {}",
                                threadName, currentDuration, safeAckTime, 
                                mqProperties.getConsumer().getTimeoutAckRatio(), deliveryTag);

                        try {
                            channel.basicAck(deliveryTag, false);
                            ackedByTimeout = true;
                            log.info("SUCCESS: Message ACKed by timeout checker - thread: {}, deliveryTag: {}, duration: {}ms",
                                    threadName, deliveryTag, currentDuration);
                            break;
                        } catch (Exception ackEx) {
                            log.error("ERROR: Failed to ACK message in timeout checker - thread: {}, deliveryTag: {}, error: {}", 
                                    threadName, deliveryTag, ackEx.getMessage(), ackEx);
                            // 如果ACK失败，继续尝试（避免消息重复投递）
                            if (Thread.currentThread().isInterrupted()) {
                                break; // 如果被中断，立即退出
                            }
                            Thread.sleep(checkInterval);
                            continue;
                        }
                    }

                    // 检查中断状态，如果被中断则退出
                    if (Thread.currentThread().isInterrupted()) {
                        log.debug("Timeout checker interrupted during execution - thread: {}, deliveryTag: {}", 
                                threadName, deliveryTag);
                        break;
                    }

                    // 使用配置的检测间隔
                    Thread.sleep(checkInterval);
                }

                log.debug("Timeout checker stopped - thread: {}, deliveryTag: {}, stopped: {}, interrupted: {}", 
                        threadName, deliveryTag, stopped, Thread.currentThread().isInterrupted());

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复中断状态
                log.debug("Timeout checker interrupted - thread: {}, deliveryTag: {}", threadName, deliveryTag);
            } catch (Exception e) {
                log.error("Timeout checker error - thread: {}, deliveryTag: {}, error: {}", 
                        threadName, deliveryTag, e.getMessage(), e);
            }
        }

        public void stop() {
            this.stopped = true;
        }

        public boolean isAckedByTimeout() {
            return ackedByTimeout;
        }
    }

    /**
     * 获取当前重试次数
     */
    private int getCurrentRetryCount(Map<String, Object> headers) {
        if (headers == null) {
            return 0;
        }

        // 从消息头中获取死信重试次数
        Object deathHeader = headers.get("x-death");
        if (deathHeader instanceof java.util.List) {
            @SuppressWarnings("unchecked")
            java.util.List<Map<String, Object>> deaths = (java.util.List<Map<String, Object>>) deathHeader;
            if (!deaths.isEmpty()) {
                Object count = deaths.get(0).get("count");
                if (count instanceof Number) {
                    return ((Number) count).intValue();
                }
            }
        }

        return 0;
    }

    /**
     * 判断是否应该重新入队重试
     */
    private boolean shouldRequeue(Exception ex, int currentRetryCount) {
        // 如果已达到最大重试次数，不再重试
        if (currentRetryCount >= mqProperties.getMaxRetryCount()) {
            log.warn("Reached max retry count {}, will not retry", mqProperties.getMaxRetryCount());
            return false;
        }

        // 根据异常类型判断是否需要重试
        if (ex.getMessage() != null) {
            String errorMsg = ex.getMessage().toLowerCase();

            // 网络连接、超时等临时性错误可以重试
            if (errorMsg.contains("connection") ||
                    errorMsg.contains("timeout") ||
                    errorMsg.contains("socket") ||
                    errorMsg.contains("network")) {
                log.info("Temporary error detected, will retry: {}", ex.getMessage());
                return true;
            }

            // 数据库相关的临时性错误可以重试
            if (errorMsg.contains("deadlock") ||
                    errorMsg.contains("lock timeout") ||
                    errorMsg.contains("connection is closed")) {
                log.info("Database temporary error detected, will retry: {}", ex.getMessage());
                return true;
            }

            // 业务逻辑错误、数据格式错误等不需要重试
            if (errorMsg.contains("illegal") ||
                    errorMsg.contains("invalid") ||
                    errorMsg.contains("parse")) {
                log.warn("Business logic error detected, will not retry: {}", ex.getMessage());
                return false;
            }
        }

        // 未知错误类型，谨慎起见进行重试
        return true;
    }

    /**
     * 处理失败消息的逻辑
     * 根据是否启用死信队列和失败策略进行不同处理
     */
    private void handleFailedMessage(Channel channel, long deliveryTag, int currentRetryCount, Exception ex) throws Exception {
        if (mqProperties.isEnableDLQ()) {
            // 启用死信队列：拒绝消息，让其进入死信队列
            channel.basicNack(deliveryTag, false, false);
            log.warn("DLQ enabled - Message nacked and moved to DLQ, deliveryTag={}, finalRetryCount={}, error:{}",
                    deliveryTag, currentRetryCount, ex.getMessage());
        } else {
            // 未启用死信队列：根据策略处理
            AttendanceMqProperties.FailureStrategy strategy = mqProperties.getFailureStrategyEnum();

            switch (strategy) {
                case ACK:
                    // 确认消息（实际上是丢弃消息）
                    channel.basicAck(deliveryTag, false);
                    log.warn("DLQ disabled - Message acknowledged as processed (actually discarded), " +
                                    "deliveryTag={}, finalRetryCount={}, strategy=ACK, error:{}",
                            deliveryTag, currentRetryCount, ex.getMessage());
                    break;

                case NACK_REQUEUE:
                    // 拒绝消息并重新入队（可能导致无限循环，慎用）
                    channel.basicNack(deliveryTag, false, true);
                    log.warn("DLQ disabled - Message nacked and requeued (may cause infinite loop), " +
                                    "deliveryTag={}, finalRetryCount={}, strategy=NACK_REQUEUE, error:{}",
                            deliveryTag, currentRetryCount, ex.getMessage());
                    break;

                case NACK_DISCARD:
                default:
                    // 拒绝消息并丢弃（确保队列没有死信队列配置）
                    channel.basicNack(deliveryTag, false, false);
                    log.warn("DLQ disabled - Message nacked and discarded, " +
                                    "deliveryTag={}, finalRetryCount={}, strategy=NACK_DISCARD, error:{}",
                            deliveryTag, currentRetryCount, ex.getMessage());
                    break;
            }
        }
    }

    /**
     * 预估业务处理时间
     * 基于员工数量和日期范围进行简单预估
     */
    private long estimateProcessingTime(BatchClockAnalyseDto dto) {
        if (dto.getEmpIds() == null || dto.getStartDate() == null || dto.getEndDate() == null) {
            return 60000; // 默认1分钟
        }

        int empCount = dto.getEmpIds().size();
        long dayCount = (dto.getEndDate() - dto.getStartDate()) / 86400;

        // 简单预估：每个员工每天需要100ms处理时间
        long estimatedTime = empCount * dayCount * 100;

        // 最小30秒，最大30分钟
        return Math.max(30000, Math.min(estimatedTime, 1800000));
    }

    /**
     * 获取异步处理线程池状态信息
     */
    public String getAsyncProcessingPoolStatus() {
        return ThreadPoolConfig.getThreadPoolStatus("Async-Processing", asyncProcessingExecutor);
    }

    /**
     * 检查异步处理线程池健康状态
     */
    private void checkAsyncProcessingPoolHealth() {
        ThreadPoolConfig.checkThreadPoolHealth("Async-Processing", asyncProcessingExecutor, 50, 0.9);
        
        // 定期记录线程池状态
        if (System.currentTimeMillis() % 300000 < 1000) { // 大约每5分钟
            log.info("INFO: {}", ThreadPoolConfig.getThreadPoolStatus("Async-Processing", asyncProcessingExecutor));
        }
    }

    /**
     * 检查超时检测线程池健康状态
     */
    private void checkTimeoutCheckPoolHealth() {
        ThreadPoolConfig.checkThreadPoolHealth("Timeout-Check", timeoutCheckExecutor, 10, 1.0);
    }

    /**
     * 获取超时检测线程池状态信息
     */
    public String getTimeoutCheckPoolStatus() {
        return ThreadPoolConfig.getThreadPoolStatus("Timeout-Check", timeoutCheckExecutor);
    }

    /**
     * 获取所有线程池的综合状态信息
     * 用于全面监控线程池健康状况
     */
    public String getAllThreadPoolsStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== Thread Pools Status ===\n");
        status.append("1. ").append(getAsyncProcessingPoolStatus()).append("\n");
        status.append("2. ").append(getTimeoutCheckPoolStatus());
        return status.toString();
    }

    /**
     * 记录所有线程池状态到日志
     * 可用于定期监控或故障排查
     */
    public void logAllThreadPoolsStatus() {
        log.info("MONITOR: {}", getAllThreadPoolsStatus().replace("\n", " | "));
    }
}
