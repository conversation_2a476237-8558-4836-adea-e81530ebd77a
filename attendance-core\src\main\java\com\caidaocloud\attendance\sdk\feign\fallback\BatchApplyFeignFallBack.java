package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.sdk.dto.SdkRevokeBatchAnalyseResultAdjustDto;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeBatchLeaveDto;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeBatchOvertimeDto;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeEmpTraveDto;
import com.caidaocloud.attendance.sdk.feign.IBatchApplyFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

/**
 * 批量申请
 *
 * <AUTHOR>
 * @Date 2024/8/8
 */
@Component
public class BatchApplyFeignFallBack implements IBatchApplyFeignClient {
    @Override
    public Result<?> revokeBatchLeave(SdkRevokeBatchLeaveDto dto) {
        return Result.fail();
    }

    @Override
    public Result<?> revokeBatchOt(SdkRevokeBatchOvertimeDto dto) {
        return Result.fail();
    }

    @Override
    public Result<?> revokeBatchTravel(SdkRevokeEmpTraveDto dto) {
        return Result.fail();
    }

    @Override
    public Result<?> revokeAnalyseResultAdjust(SdkRevokeBatchAnalyseResultAdjustDto dto) {
        return Result.fail();
    }
}
