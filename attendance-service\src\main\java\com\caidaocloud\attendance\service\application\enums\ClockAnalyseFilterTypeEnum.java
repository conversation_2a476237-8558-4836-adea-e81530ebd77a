package com.caidaocloud.attendance.service.application.enums;

public enum ClockAnalyseFilterTypeEnum {
    YESTERDAY("YESTERDAY", "前日"),
    TODAY("TODAY", "当日");

    private String code;

    private String name;

    ClockAnalyseFilterTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public static String getName(String code) {
        for (ClockAnalyseFilterTypeEnum c : ClockAnalyseFilterTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
