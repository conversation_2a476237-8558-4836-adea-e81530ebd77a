package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.sdk.dto.SdkSaveBdkDTO;
import com.caidaocloud.attendance.sdk.feign.IEmpClockInFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

/**
 * 员工打卡
 *
 * <AUTHOR>
 * @Date 2023/6/27
 */
@Component
public class EmpClockInFeignFallBack implements IEmpClockInFeignClient {
    @Override
    public Result<?> saveBdkRegister(SdkSaveBdkDTO dto) {
        return Result.fail();
    }
}
