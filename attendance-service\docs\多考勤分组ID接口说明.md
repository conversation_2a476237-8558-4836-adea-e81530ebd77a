# 支持多个考勤分组ID的动态列接口

## 概述

基于现有的动态列方法，新增了支持多个考勤分组ID的接口，允许用户在查询考勤统计数据时传入多个考勤分组ID。

## 新增接口

### 1. 获取考勤明细汇总表——动态列（支持多个考勤分组ID）

**接口地址：** `POST /searchRegisterStatisticsAdvance/dynamicMultiGroup`

**请求参数：** `MonthAnalysePageDtoV2`

```json
{
  "timePeriod": "THIS_MONTH",
  "startDate": 1672531200,
  "endDate": 1675123199,
  "groupId": [1, 2, 3],  // 支持多个考勤分组ID
  "pageNo": 1,
  "pageSize": 20,
  "empIds": [100, 200, 300],
  "filterList": [],
  "statisticsType": 0
}
```

**响应数据：** `DynamicPageDto`

### 2. 获取考勤统计月度汇总字段-动态列（支持多个考勤分组ID）

**接口地址：** `GET /searchMonthHeaderList/dynamicMultiGroup`

**请求参数：**
- `waGroupIds`: 考勤分组ID列表，例如：`[1,2,3]`

**响应数据：** `List<KeyValue>`

## 实现说明

### 数据传输对象

使用 `MonthAnalysePageDtoV2` 作为请求参数，其中 `groupId` 字段类型为 `List<Integer>`，支持传入多个考勤分组ID。

### 服务层实现

1. **IStatisticsService 接口新增方法：**
   - `getRegisterStatisticsAdvancedForDynamicMultiGroup()` - 支持多个考勤分组ID的动态列数据查询
   - `searchMonthHeaderListForDynamicMultiGroup()` - 支持多个考勤分组ID的表头查询
   - `getMonthHeadersByDynamicForExportMultiGroup()` - 支持多个考勤分组ID的导出表头

2. **StatisticsService 实现类：**
   - `searchRegisterStatisticsMultiGroup()` - 支持多个考勤分组ID的底层查询方法
   - 当前实现暂时使用第一个groupId，后续需要在底层数据访问层支持多个groupId

### 控制器层

在 `StatisticsController` 中新增两个接口方法，提供对外的REST API。

## 使用示例

### 查询多个考勤分组的考勤明细

```javascript
// 请求示例
const requestData = {
  timePeriod: "THIS_MONTH",
  groupId: [1, 2, 3], // 查询考勤分组1、2、3的数据
  pageNo: 1,
  pageSize: 20
};

fetch('/searchRegisterStatisticsAdvance/dynamicMultiGroup', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(data => {
  console.log('考勤明细数据:', data);
});
```

### 获取多个考勤分组的表头信息

```javascript
// 请求示例
fetch('/searchMonthHeaderList/dynamicMultiGroup?waGroupIds=1,2,3')
.then(response => response.json())
.then(data => {
  console.log('表头信息:', data);
});
```

## 底层实现详情

### 数据访问层

1. **新增Mapper方法：**
   - `RegisterRecordMapper.searchEmpInfoListMultiGroup()` - 支持多个考勤分组ID的员工信息查询
   - SQL实现支持多个waGroupId的IN查询，返回员工与考勤分组的组合数据

2. **服务层实现：**
   - `WorkOvertimeService.searchRegMonthStatisticsMultiGroup()` - 支持多个考勤分组ID的月度统计查询
   - `combineEmpAndWaMultiGroup()` - 组合员工信息和考勤数据的多分组版本

### 核心逻辑

1. **遍历考勤分组：** 对传入的每个考勤分组ID，分别获取统计数据
2. **数据存储：** 将统计结果以 `empId_groupId` 为key存储到 `empWaStatisticsMap` 中
3. **员工查询：** 使用新的SQL查询多个考勤分组下的所有员工信息
4. **数据组合：** 根据员工ID和考勤分组ID从 `empWaStatisticsMap` 中获取对应的统计结果

### SQL实现

新增的 `searchEmpInfoListMultiGroup` SQL支持：
- 多个考勤分组ID的IN查询
- 员工与考勤分组的关联查询
- 返回格式：`empid_groupid` 作为bid字段

## 注意事项

1. **完整实现：** 底层数据访问层已完全支持多个groupId查询
2. **数据格式：** 每个员工在不同考勤分组中会产生独立的记录
3. **兼容性：** 新接口与现有单个groupId接口并存，不影响现有功能
4. **性能考虑：** 对于大量考勤分组，会循环调用单个分组的统计方法

## 已完成功能

✅ 接口层面的多分组支持
✅ 服务层的多分组实现
✅ 数据访问层的多分组SQL
✅ 数据组合逻辑的多分组版本
✅ 完整的端到端实现
