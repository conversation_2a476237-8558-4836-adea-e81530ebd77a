package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.sdk.dto.SdkApplyShiftDTO;
import com.caidaocloud.attendance.sdk.feign.fallback.EmpShiftChangeFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 员工班次变更
 *
 * <AUTHOR>
 * @Date 2023/6/27
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = EmpShiftChangeFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "empShiftChangeFeignClient")
public interface IEmpShiftChangeFeignClient {

    @ApiOperation("员工自己申请调班")
    @PostMapping(value = "/api/attendance/user/v1/applyShift")
    Result<?> userApplyShiftChange(@RequestBody SdkApplyShiftDTO dto);
}
