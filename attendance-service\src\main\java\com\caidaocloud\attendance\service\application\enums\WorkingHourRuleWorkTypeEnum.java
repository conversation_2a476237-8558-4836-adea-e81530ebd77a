package com.caidaocloud.attendance.service.application.enums;

/**
 * 考勤方案-工时规则：工时类型
 */
public enum WorkingHourRuleWorkTypeEnum {
    STD("STD", "标准工时制"),
    COMP("COMP", "综合工时制"),
    FLEXIBLE("FLEXIBLE", "弹性工时制");

    private String code;

    private String name;

    WorkingHourRuleWorkTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public static String getName(String code) {
        for (WorkingHourRuleWorkTypeEnum c : WorkingHourRuleWorkTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
