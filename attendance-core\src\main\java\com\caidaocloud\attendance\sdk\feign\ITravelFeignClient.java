package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.sdk.dto.SdkEmpTravelSaveDTO;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeEmpTravelDTO;
import com.caidaocloud.attendance.sdk.feign.fallback.TravelFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 出差申请
 *
 * <AUTHOR>
 * @Date 2023/6/19
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = TravelFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "travelFeignClient")
public interface ITravelFeignClient {
    /**
     * 获取出差时长
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/emptravel/v1/getTravelTime")
    Result<?> getTravelTime(@RequestBody SdkEmpTravelSaveDTO dto);

    /**
     * 保存出差申请单
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/emptravel/v1/saveTravelTime")
    Result<?> saveTravelTime(@RequestBody SdkEmpTravelSaveDTO dto);

    /**
     * 撤销出差单
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/emptravel/v1/revokeEmpTravel")
    Result<?> revokeEmpTravel(@RequestBody SdkRevokeEmpTravelDTO dto);
}
