package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWaBatchTravelRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 批量出差
 */
@Data
@Slf4j
@Service
public class WaBatchTravelDo {
    private Long batchTravelId;

    private String tenantId;

    private String businessKey;

    private String remarks;

    private Integer status;

    private Long lastApprovalTime;

    private Long lastEmpid;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    private String processCode;

    @Autowired
    private IWaBatchTravelRepository waBatchTravelRepository;

    public WaBatchTravelDo getById(Long batchTravelId) {
        return waBatchTravelRepository.getById(batchTravelId);
    }

    public void updateById(WaBatchTravelDo updateData) {
        waBatchTravelRepository.updateById(updateData);
    }

    public void save(WaBatchTravelDo addData) {
        waBatchTravelRepository.insert(addData);
    }

    public void deleteById(Long batchTravelId) {
        waBatchTravelRepository.deleteById(batchTravelId);
    }
}