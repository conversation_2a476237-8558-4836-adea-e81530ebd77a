package com.caidaocloud.attendance.service.application.dto.clock;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 前一天打卡分析上下文
 */
@Data
@Builder
public class YesterdayClockFilterContext {

    // 基础信息
    private Long empId;
    private Long clockDate;
    private Long yesterday;
    private List<WaRegisterRecordDo> regList;
    private ClockAnalyseDataCacheDto dataCacheDto;

    // 前一天信息
    private WaShiftDo yesterdayShiftDo;
    private WaShiftDef yesterdayShiftDef;
    private WaShiftDef yesterdayShiftWorkTime;
    private boolean isYesterdayWorkday;
    private Long yesterdayShiftStartTime;
    private Long yesterdayShiftEndTime;
    private long yesterdayOffDutyEndTime;
    private Long yesterdayOvertimeEndTime;
    private boolean yesterdayCrossNight;

    // 当日信息
    private WaShiftDo todayShiftDo;
    private WaShiftDef todayShiftDef;
    private WaShiftDef todayShiftWorkTime;
    private boolean isTodayWorkday;
    private Long todayShiftStartTime;
    private Long todayShiftEndTime;
    private long todayOnDutyStartTime;
    private Long todayOvertimeStartTime;

    public static YesterdayClockFilterContext doBuild(FilterBelongYesRegDto filterDto) {
        Long empId = filterDto.getEmpId();
        Long clockDate = filterDto.getClockDate();
        Long yesterday = DateUtil.addDate(clockDate * 1000, -1);
        ClockAnalyseDataCacheDto dataCacheDto = filterDto.getDataCacheDto();
        Map<String, WaShiftDo> empShiftDoMap = filterDto.getEmpShiftDoMap();

        // 前一天班次信息
        WaShiftDo yesterdayShiftDo = empShiftDoMap.get(empId + "_" + yesterday);
        WaShiftDef yesterdayShiftDef = yesterdayShiftDo.doGetLastShiftDef();
        WaShiftDef yesterdayShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(yesterdayShiftDef);

        boolean yesterdayCrossNight = CdWaShiftUtil.checkCrossNightV2(yesterdayShiftWorkTime, yesterdayShiftWorkTime.getDateType());
        boolean isYesterdayWorkday = DateTypeEnum.DATE_TYP_1.getIndex().equals(yesterdayShiftDef.getDateType());
        Long yesterdayShiftStartTime = isYesterdayWorkday ? yesterday + yesterdayShiftWorkTime.getStartTime() * 60 : null;
        Long yesterdayShiftEndTime = isYesterdayWorkday ? (yesterdayCrossNight
                ? clockDate + yesterdayShiftWorkTime.getEndTime() * 60L
                : yesterday + yesterdayShiftWorkTime.getEndTime() * 60L) : null;
        long yesterdayOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(yesterdayShiftWorkTime, yesterdayShiftWorkTime.getDateType())
                ? yesterday + (yesterdayShiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                : yesterday + yesterdayShiftWorkTime.getOffDutyEndTime() * 60L;
        Long yesterdayOvertimeEndTime = dataCacheDto.doGetMaxOtEndTime(filterDto.getEmpId(), yesterday);

        // 当日班次信息
        WaShiftDo todayShiftDo = empShiftDoMap.get(empId + "_" + clockDate);
        WaShiftDef todayShiftDef = null;
        WaShiftDef todayShiftWorkTime = null;
        Long todayShiftStartTime = null;
        Long todayShiftEndTime = null;
        if (null != todayShiftDo) {
            todayShiftDef = todayShiftDo.doGetFirstShiftDef();
            todayShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(todayShiftDef);

            todayShiftStartTime = clockDate + todayShiftWorkTime.getStartTime() * 60;
            todayShiftEndTime = CdWaShiftUtil.checkCrossNightV2(todayShiftWorkTime, todayShiftWorkTime.getDateType())
                    ? clockDate + (todayShiftWorkTime.getEndTime() + 1440) * 60L
                    : clockDate + todayShiftWorkTime.getEndTime() * 60L;
        }
        long todayOnDutyStartTime = ClockAnalyseCalDto.getNextDatFirstOnDutyStartTime(empId, yesterday, empShiftDoMap);
        Long todayOvertimeStartTime = dataCacheDto.doGetMinOtStartTime(empId, clockDate);

        return YesterdayClockFilterContext.builder()
                .regList(filterDto.getRegList())
                .empId(empId)
                .clockDate(clockDate)
                .yesterday(yesterday)
                .dataCacheDto(dataCacheDto)

                // 前一天信息
                .yesterdayShiftDo(yesterdayShiftDo)
                .yesterdayShiftDef(yesterdayShiftDef)
                .yesterdayShiftWorkTime(yesterdayShiftWorkTime)
                .isYesterdayWorkday(isYesterdayWorkday)
                .yesterdayShiftStartTime(yesterdayShiftStartTime)
                .yesterdayShiftEndTime(yesterdayShiftEndTime)
                .yesterdayOffDutyEndTime(yesterdayOffDutyEndTime)
                .yesterdayOvertimeEndTime(yesterdayOvertimeEndTime)
                .yesterdayCrossNight(yesterdayCrossNight)

                // 当日信息
                .todayShiftDo(todayShiftDo)
                .todayShiftDef(todayShiftDef)
                .todayShiftWorkTime(todayShiftWorkTime)
                .isTodayWorkday(null != todayShiftDo && DateTypeEnum.DATE_TYP_1.getIndex().equals(todayShiftDo.getDateType()))
                .todayShiftStartTime(todayShiftStartTime)
                .todayShiftEndTime(todayShiftEndTime)
                .todayOnDutyStartTime(todayOnDutyStartTime)
                .todayOvertimeStartTime(todayOvertimeStartTime)
                .build();
    }
}
