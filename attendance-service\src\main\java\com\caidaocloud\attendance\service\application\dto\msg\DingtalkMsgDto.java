package com.caidaocloud.attendance.service.application.dto.msg;

import com.caidaocloud.attendance.service.application.dto.PhoneSimple;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 钉钉通知
 * @Date 2023/2/23 上午10:19
 * @Version 1.0
 **/
@Data
@Accessors(chain = true)
public class DingtalkMsgDto {

    /**
     * 通知标题
     */
    private String subject;

    /**
     * 通知模板类型
     */
    private String templateType;

    /**
     * 通知url
     */
    private String url;

    /**
     * 钉钉内容
     */
    private String content;

    /**
     * 多个附件用逗号分割
     * 附件地址：/caidao-pa/259dtfgdb9cdb5ddab4b-jvm.png
     */
    private String affix;

    /**
     * 指定图标对应的名称
     * jvm.png
     */
    private String affixName;

    /**
     * 用户手机
     */
    private List<PhoneSimple> mobiles;
}

 
    
    
    
    