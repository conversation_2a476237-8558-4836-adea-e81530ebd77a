package com.caidaocloud.attendance.core.commons.utils;

import com.caidao1.commons.utils.JacksonJsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.security.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ClassCompareUtil {

    private  final  static Logger logger = LoggerFactory.getLogger(ClassCompareUtil.class);

    /**
     * 比较两个实体属性值，返回一个boolean,true则表时两个对象中的属性值无差异
     * @param oldObject 进行属性比较的对象1
     * @param newObject 进行属性比较的对象2
     * @return 属性差异比较结果boolean
     */
    public static boolean compareObject(Object oldObject, Object newObject) {
        Map<String, Map<String,Object>> resultMap=compareFields(oldObject,newObject);
        if(resultMap.size()>0) {
            return true;
        }else {
            return false;
        }
    }

    /**
     * 比较两个实体属性值，返回一个map以有差异的属性名为key，value为一个Map分别存oldObject,newObject此属性名的值
     * @param oldObject 进行属性比较的对象1
     * @param newObject 进行属性比较的对象2
     * @return 属性差异比较结果map
     */
    @SuppressWarnings("rawtypes")
    public static  Map<String, Map<String,Object>> compareFields(Object oldObject, Object newObject) {
        Map<String, Map<String, Object>> map =  new HashMap<>();
        try{
            /**
             * 只有两个对象都是同一类型的才有可比性
             */
            if (oldObject.getClass() == newObject.getClass()) {
                map = new HashMap<String, Map<String,Object>>();

                Class clazz = oldObject.getClass();
                //获取object的所有属性
                PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz, Object.class).getPropertyDescriptors();

                for (PropertyDescriptor pd : pds) {
                    //遍历获取属性名
                    String name = pd.getName();
                    if(        !name.equals("crtUser")  && !name.equals("crtuser")
                            && !name.equals("crtTime")  && !name.equals("crttime")
                            && !name.equals("updUser")  && !name.equals("upduser")
                            && !name.equals("updTime")  && !name.equals("updtime")
                            && !name.equals("belongOrgId")
                            && !name.equals("corpId")
                            && !name.equals("valid")
                    ){
                        //获取属性的get方法
                        Method readMethod = pd.getReadMethod();

                        // 在oldObject上调用get方法等同于获得oldObject的属性值
                        Object oldValue = readMethod.invoke(oldObject);
                        // 在newObject上调用get方法等同于获得newObject的属性值
                        Object newValue = readMethod.invoke(newObject);

                        if(oldValue instanceof List){
                            continue;
                        }

                        if(newValue instanceof List){
                            continue;
                        }

                        if(oldValue instanceof Timestamp){
                            oldValue = new Date(((Timestamp) oldValue).getTimestamp().getTime());
                        }

                        if(newValue instanceof Timestamp){
                            newValue = new Date(((Timestamp) newValue).getTimestamp().getTime());
                        }


                        if(oldValue == null && newValue == null){
                            continue;
                        }else if("extCustomCol".equals(name)){
                            Map<String,Object> valueMap = new HashMap<String,Object>();
                            if( null != newValue && null != oldValue ){

                                PGobject extCustomCol= (PGobject) oldValue;

                                Map<String,Object>  olDs =(Map<String, Object>) JacksonJsonUtil.jsonToBean(extCustomCol.getValue(), Map.class);
                                Map<String,Object>  news =(Map<String, Object>) JacksonJsonUtil.jsonToBean(newValue.toString(), Map.class);

                                for (Map.Entry<String, Object> stringEntry : news.entrySet()) {

                                    Object oldExtCol = olDs.get(stringEntry.getKey());
                                    Object newExtCol = stringEntry.getValue();
                                    if(null != oldExtCol && null != newExtCol){
                                        if( !oldExtCol.toString().equals(newExtCol.toString())
                                                ||  !newExtCol.toString().equals(oldExtCol.toString())){
                                            valueMap.put(stringEntry.getKey()+"_修改后值", newExtCol);
                                            valueMap.put(stringEntry.getKey()+"_修改前值", oldExtCol);
                                        }
                                    }
                                }
                                if(valueMap.size()>0){
                                    map.put(name, valueMap);
                                }
                            }
                            continue;
                        }else if(oldValue == null && newValue != null){

                            Map<String,Object> valueMap = new HashMap<String,Object>();
                            valueMap.put("修改前值", oldValue);
                            valueMap.put("修改后值", newValue);

                            map.put(name, valueMap);

                            continue;
                        }
                        if (!oldValue.equals(newValue)) {
                            boolean flag = true;

                            if(oldValue.equals("[]") && newValue == null){
                                flag = false;
                            }
                            if(oldValue != null){
                                if(oldValue.toString().equals("0") && newValue == null){
                                    flag = false;
                                }
                                if(StringUtils.isBlank(oldValue.toString()) && newValue == null){
                                    flag = false;
                                }
                                if(oldValue != null && newValue == null){
                                    flag = false;
                                }
                            }

                            if(flag){
                                Map<String,Object> valueMap = new HashMap<String,Object>();
                                valueMap.put("修改前值", oldValue);
                                valueMap.put("修改后值", newValue);
                                map.put(name, valueMap);
                            }
                        }
                    }
                }
            }
        }catch(Exception e){
            logger.error("compareFields err,{}", e.getMessage(), e);
        }

        return map;
    }

    public static  Map<String, Map<String,Object>> compareFields(Object oldObject, Object newObject, Boolean compare) {
        Map<String, Map<String, Object>> map =  new HashMap<>();
        try{
            /**
             * 只有两个对象都是同一类型的才有可比性
             */
            if (oldObject.getClass() == newObject.getClass() || compare) {

                map = new HashMap<String, Map<String,Object>>();

                Class clazz = oldObject.getClass();

                String className = oldObject.getClass().getName();

                PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz,Object.class).getPropertyDescriptors();

                for (PropertyDescriptor pd : pds) {

                    String name = pd.getName();
                    if(        !name.equals("crtUser")  && !name.equals("crtuser")
                            && !name.equals("crtTime")  && !name.equals("crttime")
                            && !name.equals("updUser")  && !name.equals("upduser")
                            && !name.equals("updTime")  && !name.equals("updtime")
                            && !name.equals("belongOrgId")
                            && !name.equals("corpId")
                            && !name.equals("valid")
                    ){

                        Method readMethod = pd.getReadMethod();

                        Object oldValue = readMethod.invoke(oldObject);
                        Object newValue = readMethod.invoke(newObject);

                        if(oldValue instanceof List){
                            continue;
                        }

                        if(newValue instanceof List){
                            continue;
                        }

                        if(oldValue instanceof Timestamp){
                            oldValue = new Date(((Timestamp) oldValue).getTimestamp().getTime());
                        }

                        if(newValue instanceof Timestamp){
                            newValue = new Date(((Timestamp) newValue).getTimestamp().getTime());
                        }

                        if(oldValue == null && newValue == null){
                            continue;
                        }else if("extCustomCol".equals(name)){
                            Map<String,Object> valueMap = new HashMap<String,Object>();
                            PGobject VV= (PGobject) oldValue;
                            if( null != newValue  ){
                                Map<String,Object>  olDs =(Map<String, Object>) JacksonJsonUtil.jsonToBean(VV.getValue(), Map.class);
                                Map<String,Object>  news =(Map<String, Object>) JacksonJsonUtil.jsonToBean(newValue.toString(), Map.class);
                                for (Map.Entry<String, Object> stringEntry : news.entrySet()) {

                                    Object oldExtCol = olDs.get(stringEntry.getKey());
                                    Object newExtCol = stringEntry.getValue();

                                    if(null != oldExtCol && null != newExtCol){
                                        if( !oldExtCol.toString().equals(newExtCol.toString())
                                                ||  !newExtCol.toString().equals(oldExtCol.toString())){
                                            valueMap.put(stringEntry.getKey()+"_修改后值", newExtCol);
                                            valueMap.put(stringEntry.getKey()+"_修改前值", oldExtCol);
                                        }
                                    }
                                }
                                if(valueMap.size()>0){
                                    map.put(name, valueMap);
                                }
                            }
                            continue;
                        }else if(oldValue == null && newValue != null){
                            map.put(name, bindBeforeAndAfterValues(oldValue, newValue));
                            continue;
                        }
                        if (!oldValue.equals(newValue)) {
                            boolean flag = true;

                            if(oldValue.equals("[]") && newValue == null){
                                flag = false;
                            }

                            if(oldValue !=null){
                                if(oldValue.toString().equals("0") && newValue == null){
                                    flag = false;
                                }
                                if(StringUtils.isBlank(oldValue.toString()) && newValue == null){
                                    flag = false;
                                }
                                if(oldValue != null && newValue == null){
                                    flag = false;
                                }
                                if(oldValue == null && StringUtils.isBlank(oldValue.toString())){
                                    flag = false;
                                }
                            }
                            if(flag){
                             if("com.caidao1.wa.mybatis.model.WaEmpQuota".equals(className) || "com.caidao1.wa.mybatis.model.WaEmpQuotaDetail".equals(className)) {
                                    if("com.caidao1.wa.mybatis.model.WaEmpQuota".equals(className)){
                                        if("quotaDay".equals(name)){
                                            name = "本年配额";
                                        }else if("nowQuota".equals(name)){
                                            name = "当前配额";
                                        }else if("adjustQuota".equals(name)){
                                            name = "调整配额";
                                        }else if("remainDay".equals(name)){
                                            name = "留存配额";
                                        }else if("usedDay".equals(name)){
                                            name = "本年已使用";
                                        }else if("remainUsedDay".equals(name)){
                                            name = "留存已使用";
                                        }else if("deduction_day".equals(name)){
                                            name = "抵扣额度";
                                        }else if("deduction_day".equals(name)){
                                            name = "抵扣额度";
                                        }else if("startDate".equals(name)){
                                            name = "生效日期";
                                        }else if("lastDate".equals(name)){
                                            name = "失效日期";
                                        }else if("periodYear".equals(name)){
                                            name = "所属年份";
                                        }
                                    }else {
                                        if("quotaDay".equals(name)){
                                            name = "调整配额";
                                        }else if("surplusQuota".equals(name)){
                                            name = "剩余配额";
                                        }else if("invalidQuota".equals(name)){
                                            name = "失效配额";
                                        }else if("startDate".equals(name)){
                                            name = "生效日期";
                                        }else if("endDate".equals(name)){
                                            name = "失效日期";
                                        }
                                    }
                                }
                              map.put(name, bindBeforeAndAfterValues(oldValue, newValue));
                            }
                        }
                    }
                }
            }
        }catch(Exception e){
            logger.error("compareFields err,{}", e.getMessage(), e);
        }

        return map;
    }

    private static Map bindBeforeAndAfterValues(Object oldValue, Object newValue){
        Map<String,Object> valueMap = new HashMap<String,Object>();
        valueMap.put("修改前值", oldValue);
        valueMap.put("修改后值", newValue);
        return valueMap;
    }

    public static Map getFieldNameAndValues(Object model) {
        Map<String,Object> returnValue = new HashMap<>();
        java.lang.reflect.Field[] fields = model.getClass().getDeclaredFields();
        String className = model.getClass().getName();
        if("com.caidao1.wa.mybatis.model.WaEmpQuota".equals(className) || "com.caidao1.wa.mybatis.model.WaEmpQuotaDetail".equals(className)){
            for(java.lang.reflect.Field f:fields){
                String fieldName = f.getName();
                String firstLetter = fieldName.substring(0, 1).toUpperCase();
                String getter = "get" + firstLetter + fieldName.substring(1);
                Method method = null;
                try {
                    method = model.getClass().getMethod(getter, new Class[] {});
                    Object value = method.invoke(model, new Object[] {});
                    if("com.caidao1.wa.mybatis.model.WaEmpQuota".equals(className)){
                        if("quotaDay".equals(fieldName)){
                            fieldName = "本年配额";
                        }else if("nowQuota".equals(fieldName)){
                            fieldName = "当前配额";
                        }else if("adjustQuota".equals(fieldName)){
                            fieldName = "调整配额";
                        }else if("remainDay".equals(fieldName)){
                            fieldName = "留存配额";
                        }else if("usedDay".equals(fieldName)){
                            fieldName = "本年已使用";
                        }else if("remainUsedDay".equals(fieldName)){
                            fieldName = "留存已使用";
                        }else if("deduction_day".equals(fieldName)){
                            fieldName = "抵扣额度";
                        }else if("deduction_day".equals(fieldName)){
                            fieldName = "抵扣额度";
                        }else if("startDate".equals(fieldName)){
                            fieldName = "生效日期";
                        }else if("lastDate".equals(fieldName)){
                            fieldName = "失效日期";
                        }else if("periodYear".equals(fieldName)){
                            fieldName = "所属年份";
                        }
                    }else {
                        if("quotaDay".equals(fieldName)){
                            fieldName = "调整配额";
                        }else if("surplusQuota".equals(fieldName)){
                            fieldName = "剩余配额";
                        }else if("invalidQuota".equals(fieldName)){
                            fieldName = "失效配额";
                        }else if("startDate".equals(fieldName)){
                            fieldName = "生效日期";
                        }else if("endDate".equals(fieldName)){
                            fieldName = "失效日期";
                        }
                    }
                    returnValue.put(fieldName,value);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
            }
        }else {
            for(java.lang.reflect.Field f:fields){
                String fieldName = f.getName();
                String firstLetter = fieldName.substring(0, 1).toUpperCase();
                String getter = "get" + firstLetter + fieldName.substring(1);
                Method method = null;
                try {
                    method = model.getClass().getMethod(getter, new Class[] {});
                    Object value = method.invoke(model, new Object[] {});
                    returnValue.put(fieldName,value);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
            }
        }
        return  returnValue;
    }
}
