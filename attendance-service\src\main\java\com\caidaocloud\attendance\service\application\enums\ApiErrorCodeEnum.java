package com.caidaocloud.attendance.service.application.enums;

public enum ApiErrorCodeEnum {
    SUCCESS(1, ""),
    LEAVE_TYPE_DOES_NOT_EXIST(80001, "假期类型不存在"),
    LEAVE_QUOTA_RULE_DOES_NOT_EXIST(80002, "假期额度规则不存在"),
    EMP_HIREDATE_ISNULL(80003, "员工没有维护入职日期"),
    EMP_ISNULL(80004, "员工不存在"),
    DATE_PARSE_ERROR(80005, "发放周期计算失败"),
    QUOTA_ISSUING_CYCLE_IS_NOT_SET(80006, "没有设置额度发放周期");
    private Integer index;

    private String name;

    ApiErrorCodeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ApiErrorCodeEnum c : ApiErrorCodeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
