package com.caidaocloud.attendance.service.application.enums;

/**
 * 考勤方案-工时规则：综合工时制-标准工时-统计方式
 */
public enum CompStdCalcMethodEnum {
    BY_FIX(1, "按固定值"),
    BY_CALENDAR(2, "按工作日历");

    private Integer index;

    private String name;

    CompStdCalcMethodEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (CompStdCalcMethodEnum c : CompStdCalcMethodEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
