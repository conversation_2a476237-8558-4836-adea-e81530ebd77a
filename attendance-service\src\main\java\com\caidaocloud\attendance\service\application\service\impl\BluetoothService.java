package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.system.mybatis.mapper.SysIbeaconInfoMapper;
import com.caidao1.system.mybatis.model.SysIbeaconInfo;
import com.caidao1.system.mybatis.model.SysIbeaconInfoExample;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-02-28
 */
@Service
public class BluetoothService {
    @Autowired
    private SysIbeaconInfoMapper sysIbeaconInfoMapper;

    public List<SysIbeaconInfo> getBluetoothListByIds(List<Integer> ids){
        if(CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }

        SysIbeaconInfoExample example = new SysIbeaconInfoExample();
        example.createCriteria().andIbeaconIdIn(ids);
        return sysIbeaconInfoMapper.selectByExample(example);
    }

}
