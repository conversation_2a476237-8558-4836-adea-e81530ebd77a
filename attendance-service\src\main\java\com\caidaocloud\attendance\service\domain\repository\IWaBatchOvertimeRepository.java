package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.domain.entity.WaBatchOvertimeDo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

/**
 * 批量加班
 *
 * <AUTHOR>
 * @Date 2024/6/18
 */
public interface IWaBatchOvertimeRepository {
    void updateById(WaBatchOvertimeDo batchOvertime);

    void insert(WaBatchOvertimeDo batchOvertime);

    WaBatchOvertimeDo getById(Long batchId);

    void deleteById(Long batchId);

    PageList<Map> selectPageList(MyPageBounds myPageBounds, Map params);

    List<Map> selectListGroupByCompensateType(String tenantId, Long empid, Long startTime, Long endTime,
                                              List<Integer> statusList);
}
