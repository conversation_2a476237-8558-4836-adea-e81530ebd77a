package com.caidaocloud.attendance.service.application.feign;

import com.caidaocloud.attendance.service.application.dto.AuthRoleScopeFilterDetail;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "${feign.rename.caidaocloud-auth-service:caidaocloud-auth-service}",
        fallback = AuthFeignFallBack.class, configuration = FeignConfiguration.class)
public interface IAuthFeignClient {
    @ApiOperation(value = "获取用户数据范围权限")
    @GetMapping("/api/auth/v1/subject/role/scope/list")
    Result<List<AuthRoleScopeFilterDetail>> getScopeBySubject(@RequestParam String identifier, @RequestParam Long subjectId);
}
