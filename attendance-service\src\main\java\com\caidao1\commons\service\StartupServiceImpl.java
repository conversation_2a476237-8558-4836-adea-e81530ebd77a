package com.caidao1.commons.service;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.mybatis.model.SysEmpInfoExample;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.utils.*;
import com.caidao1.ioc.mybatis.mapper.IocImportMapper;
import com.caidao1.mobile.mybatis.mapper.MobileV18EmployeeMapper;
import com.caidao1.system.mybatis.mapper.*;
import com.caidao1.system.mybatis.model.SysCorpOrgExample;
import com.caidao1.system.mybatis.model.SysParmDict;
import com.caidao1.system.mybatis.model.SysUnitCity;
import com.caidao1.system.mybatis.model.SysUnitCountry;
import com.caidao1.wx.mybatis.mapper.WxCorpInfoMapper;
import com.caidao1.wx.mybatis.model.WxCorpInfo;
import com.caidao1.wx.mybatis.model.WxCorpInfoExample;
import com.caidao1.xss.test.cache.RedisService;
import com.caidao1.system.mybatis.mapper.*;
import com.caidao1.system.mybatis.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import java.io.File;
import java.util.*;

@Slf4j
@Service
public class StartupServiceImpl implements StartupService, CommandLineRunner {
    private static Integer PAGE_COUNT = 10000;
    private Jedis jedis;
    @Autowired
    private SysCorpBaseMapper sysCorpBaseMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private SysParmTypeMapper sysParmTypeMapper;
    @Autowired
    private SysParmDictMapper sysParmDictMapper;
    @Autowired
    private SysUserCorpMapper sysUserCorpMapper;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private SysUnitCountryMapper sysUnitCountryMapper;
    @Autowired
    private SysCorpTaskRelMapper sysCorpTaskRelMapper;
    @Autowired
    private IocImportMapper iocImportMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private SysConfigKvMapper sysConfigKvMapper;
    @Autowired
    private WxCorpInfoMapper wxCorpInfoMapper;
    @Autowired
    private MobileV18EmployeeMapper mobileV18EmployeeMapper;
    @Value("${quartz.autoStartup:false}")
    private boolean autoStartup;

    @Value("${cd.async.task.start:true}")
    private boolean asyncTaskStart;

    @Value("${uploadAttachFilePath:}")
    private String uploadAttachFilePath;

    @Override
    public void init() {
        try {
            CacheInit();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("缓存加载失败。。。。。。。。。。。。。。。。。。");
        } finally {
            this.jedisClose();
        }
    }

    public Jedis getJedis() {
        if (jedis == null) {
            jedis = RedisService.getResource();
        }
        return jedis;
    }

    public void jedisClose() {
        if (jedis != null) {
            jedis.close();
            jedis = null;
        }
    }

    @Override
    public void run(String... args) throws Exception {
        if (autoStartup) {
            try {
                init();
            } catch (Exception e) {
                log.warn("初始化信息失败，异常信息：", e);
            }
        }
        // 初始化加密解密配置规则
        log.info("Initialize encryption and decryption configuration rules");
    }

    private void initUploadAttachFilePath() {
        log.info("initUploadAttachFilePath ... uploadAttachFilePath=[{}]", uploadAttachFilePath);

        if (null == uploadAttachFilePath || "".equals(uploadAttachFilePath)) {
            return;
        }

        File file = new File(uploadAttachFilePath);
        if (!file.exists()) {
            File parentFile = file.getParentFile();
            if (null != parentFile) {
                parentFile.mkdirs();
            }
        }
    }

    public void CacheInit() throws Exception {
        initSysProvinceAndCity();
        log.info("初始化省份城市县成功!");
        initSysParmType();
        log.info("初始化字典类型成功!");
        initSysParmInfo();
        log.info("初始化数据字典成功!");
        initEmp(null);
        log.info("初始化人员结束!");
        log.info("初始化私有云开始!");
        initUserCorpUrl();
        log.info("初始化私有云结束!");
        initSysConfig();
        log.info("初始化系统配置");
        initSysCountry();
        log.info("初始化国家信息");
        initCorpTask();
        log.info("初始化任务");
        initAllTablePkInfo();
        //initLogVersion();
        log.info("初始化表主键成功!");
        initWechatCorpid();
        log.info("初始化邮件服务器配置!");
        log.info("初始化员工信息字段配置");
        initEmpFieldConfig();
        log.info("初始化部门路径!");
        initSysOrgPath(null);
        log.info("初始化系统配置Key-Value!");
        initConfigKV();
        log.info("初始化企业微信信息!");
        initWxCorpInfo();
        log.info("清除报表cache");
        clearReportCache();
    }

    public void initEmpFieldConfig() {
        Map param = new HashMap();
        List<String> tableNames = Arrays.asList("emp_project_exp", "sys_emp_info", "sys_emp_privacy", "emp_edu", "emp_family", "emp_training_exp", "sys_emp_certificate", "emp_work_exp", "emp_bank_info", "emp_skill", "emp_language", "emp_emergency_contact", "sys_emp_social", "emp_files");
        for (String tableName : tableNames) {
            param.put("empTable", tableName);
            try {
                List<Map> tableField = mobileV18EmployeeMapper.searchAllFieldConfig(param);
                Map<Long, List<Map>> resultFieldConfig = new HashMap();
                for (Map m : tableField) {
                    Long corpId = ConvertHelper.longConvert(m.get("belongOrgId"));
                    if (resultFieldConfig.keySet().contains(corpId)) {
                        resultFieldConfig.get(corpId).add(m);
                    } else {
                        List<Map> l = new ArrayList<>();
                        l.add(m);
                        resultFieldConfig.put(corpId, l);
                    }
                }
                for (Long corpId : resultFieldConfig.keySet()) {
                    redisTemplate.opsForValue().set(RedisKeyDefine.EMP_FIELD_CONFIG_ + corpId + "_" + tableName + "_sysField", resultFieldConfig.get(corpId));
                }
            } catch (Exception e) {
                log.error("initEmpFieldConfig searchAllFieldConfig err,{}", e.getMessage(), e);
            }
            try {
                List<Map> colCustConfig = mobileV18EmployeeMapper.searchColCustomAuthConfig(param);
                Map<String, List<Map>> resultColConfig = new HashMap<>();
                for (Map m : colCustConfig) {
                    String belongOrgId = ConvertHelper.stringConvert(m.get("belongOrgId"));
                    if (resultColConfig.keySet().contains(belongOrgId)) {
                        resultColConfig.get(belongOrgId).add(m);
                    } else {
                        List<Map> l = new ArrayList<>();
                        l.add(m);
                        resultColConfig.put(belongOrgId, l);
                    }
                }
                for (String belongOrgId : resultColConfig.keySet()) {
                    redisTemplate.opsForValue().set(RedisKeyDefine.EMP_FIELD_CONFIG_ + belongOrgId + "_" + tableName + "_colField", resultColConfig.get(belongOrgId));
                }
            } catch (Exception e) {
                log.error("initEmpFieldConfig searchColCustomAuthConfig err,{}", e.getMessage(), e);
            }
        }
    }

    /**
     * 初始化所有的任务
     */
    private void initCorpTask() {
        List<Map> list = sysCorpTaskRelMapper.selectCorpTasks();
        if (list != null && list.size() > 0) {
            for (Map<String, Object> map : list) {
                String belongId =  map.get("belong_org_id").toString();
                Integer corp_task_id = (Integer) map.get("corp_task_id");
                String intercept_class_method = (String) map.get("intercept_class_method");
                // 指定前缀＋belongid＋拦截的方法名
                String key = RedisKeyDefine.PROCESS_TASK_PREFIX_ + belongId + "_" + intercept_class_method;
                Map<String, String> mm = new HashMap<String, String>();
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    if (entry.getValue() != null) {
                        mm.put(entry.getKey(), entry.getValue().toString());
                    }
                }
                CDCacheUtil.hmset(key, mm);
            }
        }
    }

    private void initSysCountry() {
        List<SysUnitCountry> countrys = sysUnitCountryMapper.selectByExample(null);
        for (SysUnitCountry country : countrys) {
            String chnname = country.getChnName();
            String engName = country.getEngName();
            String code = country.getCode();
            Long id = country.getCountryId();
            Map<String, String> map = new HashMap<String, String>();
            map.put("chnName", chnname);
            map.put("engName", engName);
            map.put("code", code);
            this.getJedis().hmset(RedisKeyDefine.SYS_COUNTRY_KEY_ + id, map);
        }
    }

    private void initSysConfig() {
        List<SysConfig> configs = sysConfigMapper.selectByExample(null);
        for (SysConfig sysConfig : configs) {
            String belongid = sysConfig.getBelongOrgId();
            String code = sysConfig.getConfigCode();
            int status = sysConfig.getStatus();
            // 只缓存belongid>0的配置
            if (Integer.parseInt(belongid) > 0) {
                this.getJedis().set(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongid + "_" + code, String.valueOf(status));
            }
        }
    }

    public void initSysParmType() throws Exception {
        List<Map> list = sysParmTypeMapper.getSysParamAllTypes(null);
        Map<Long, List<Map<String, Object>>> customParamTypeMaps = new LinkedHashMap<>();
        for (Map<String, Object> map : list) {
            Long typeId = ConvertHelper.longConvert(map.get("typeId"));
            Long domainId = ConvertHelper.longConvert(map.get("domainId"));
            String typeCode = (String) map.get("typeCode");
            if (domainId > 0) {
                typeCode += "_" + domainId;
            }
            Map<String, String> newMap = new HashMap();
            for (String key : map.keySet()) {
                newMap.put(key, MapUtils.getString(map, key, ""));
            }
            this.getJedis().hmset(RedisKeyDefine.SYS_PARM_TYPE_KEY + "_" + typeId, newMap);
            this.getJedis().hmset(RedisKeyDefine.SYS_PARM_TYPE_KEY + "_" + typeCode, newMap);
            if (customParamTypeMaps.containsKey(domainId)) {
                customParamTypeMaps.get(domainId).add(map);
            } else {
                List<Map<String, Object>> value = new ArrayList<Map<String, Object>>();
                value.add(map);
                customParamTypeMaps.put(domainId, value);
            }
        }
        for (Map.Entry<Long, List<Map<String, Object>>> entry : customParamTypeMaps.entrySet()) {
            String json = JSONUtils.ObjectToJson(entry.getValue());
            this.getJedis().set(RedisKeyDefine.SYS_PARM_TYPE_LIST_KEY + "_" + entry.getKey(), json);
        }
    }

    public void initSysParmInfo() {
        List<Map<String, Object>> parms = sysParmDictMapper.getCatchParmDicts();
        Map<String, List<SysParmDict>> systemParamDictMap = new HashMap<String, List<SysParmDict>>();
        Map<String, List<SysParmDict>> customParamDictMap = new HashMap<String, List<SysParmDict>>();
        for (Map<String, Object> map : parms) {
            String type_code = (String) map.get("type_code");
            String dict_code = (String) map.get("dict_code");
            String domain_id = map.get("domain_id").toString();
            Long dict_id = (Long) map.get("dict_id");
            Short sort_num = Short.valueOf(map.get("sort_num").toString());
            String zh_cn = (String) map.get("zh");
            String en_us = (String) map.get("en");
            String ja = (String) map.get("ja");
            String ko = (String) map.get("ko");
            SysParmDict dict = new SysParmDict();
            dict.setDictChnName(zh_cn);
            dict.setDictEngName(en_us);
            dict.setDictId(dict_id);
            dict.setSortNum(sort_num);
            dict.setDictCode(dict_code);
            dict.setDictNameLang(map.get("dict_name_lang"));
            dict.setZh(zh_cn);
            dict.setEn(en_us);
            dict.setJa(ja);
            dict.setKo(ko);
            String key = RedisKeyDefine.SYS_PARM_DICT_KEY + "_" + dict.getDictId();
            byte[] bs = jedis.get(SerializeUtil.serialize(key));
            if (null != bs) {
                jedis.del(SerializeUtil.serialize(key));
            }
            this.getJedis().set(SerializeUtil.serialize(key), SerializeUtil.serialize(dict));
            // 等于0代表是系统定义的
            if (domain_id.equals("0")) {
                if (systemParamDictMap.containsKey(type_code)) {
                    systemParamDictMap.get(type_code).add(dict);
                } else {
                    List<SysParmDict> value = new ArrayList<SysParmDict>();
                    value.add(dict);
                    systemParamDictMap.put(type_code, value);
                }
            } else {
                type_code = type_code + "_" + domain_id;
                if (customParamDictMap.containsKey(type_code)) {
                    customParamDictMap.get(type_code).add(dict);
                } else {
                    List<SysParmDict> value = new ArrayList<SysParmDict>();
                    value.add(dict);
                    customParamDictMap.put(type_code, value);
                }
            }
        }
        this.getJedis().set(SerializeUtil.serialize(RedisKeyDefine.SYS_PARM_DICT_MAP), SerializeUtil.serialize(systemParamDictMap));
        this.getJedis().set(SerializeUtil.serialize(RedisKeyDefine.SYS_CUST_PARM_DICT_MAP), SerializeUtil.serialize(customParamDictMap));
    }


    private void initSysProvinceAndCity() {
        SysUnitCityMapper sysUnitCityMapper = (SysUnitCityMapper) SpringUtils.getBean("sysUnitCityMapper");
        List<SysUnitCity> citylist = sysUnitCityMapper.selectByExample(null);
        for (SysUnitCity sysUnitCity : citylist) {
            //1、省、2市、3县
            if (sysUnitCity.getType() == 1) {
                String key = RedisKeyDefine.SYS_PROVINCE_KEY + "_" + sysUnitCity.getCityId();
                this.getJedis().set(SerializeUtil.serialize(key), SerializeUtil.serialize(sysUnitCity));
            } else if (sysUnitCity.getType() == 2) {
                String key = RedisKeyDefine.SYS_CITY_KEY + "_" + sysUnitCity.getCityId();
                this.getJedis().set(SerializeUtil.serialize(key), SerializeUtil.serialize(sysUnitCity));
            } else if (sysUnitCity.getType() == 3) {
                String key = RedisKeyDefine.SYS_COUNTY_KEY + "_" + sysUnitCity.getCityId();
                this.getJedis().set(SerializeUtil.serialize(key), SerializeUtil.serialize(sysUnitCity));
            }
        }
    }

    public void initEmp(String belongid) {
        try {
            SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
            SysEmpInfoExample.Criteria criteria = empInfoExample.createCriteria();
            if (belongid != null) {
                criteria.andBelongOrgIdEqualTo("");
            }
            criteria.andDeletedEqualTo((short) 0);
            Integer count = sysEmpInfoMapper.countByExample(empInfoExample);

            Integer num = count / PAGE_COUNT;

            if (count % PAGE_COUNT != 0) {
                num++;
            }
            String key = null;
            String value = null;
            String site = null;
            for (int i = 0; i < num; i++) {
                int offset = i * PAGE_COUNT;
                List<SysEmpInfo> empList = sysEmpInfoMapper.getEmpListBylimit("", PAGE_COUNT, offset);
                for (SysEmpInfo emp : empList) {
                    Integer tmtype = emp.getTmType();
                    if (tmtype == null) {
                        tmtype = 1;
                    }
                    site = StringUtils.trimToEmpty(emp.getSiteids());
                    if (StringUtils.isNotBlank(site)) {
                        site = site.replace(",", "#");
                    }
                    // 员工ID,员工姓名,门店类型,所属办公地点
                    value = emp.getEmpid() + "," + emp.getEmpName() + "," + tmtype + "," + site;
                    key = BaseConst.EMP_ + emp.getBelongOrgId() + "_" + emp.getWorkno();
                    this.getJedis().set(key, value);
                }
            }

            if (log.isInfoEnabled()) {
                log.info("Initialize employee end");
            }
        } finally {
            if (belongid != null) {
                this.jedisClose();
            }
        }
    }

    public void initUserCorpUrl() {
        List<Map> list = sysUserCorpMapper.getCorpRegUserList();
        for (Map map : list) {
            String account = (String) map.get("account");
            String corp_url = (String) map.get("corp_url");
            this.getJedis().set(RedisKeyDefine.SYS_USER_CORP_URL + "_" + account, corp_url);
        }
    }

    public void initAllTablePkInfo() {
        List<Map> fieldList = iocImportMapper.getPkFieldList();
        for (Map fieldReg : fieldList) {
            redisTemplate.opsForValue().set(fieldReg.get("table_name"), fieldReg);
        }
    }


    public void initWechatCorpid() {
        SysCorpBaseExample example = new SysCorpBaseExample();
        List<SysCorpBase> list = sysCorpBaseMapper.selectByExample(example);
        ValueOperations valueOperations = redisTemplate.opsForValue();
        for (SysCorpBase corpBase : list) {
            valueOperations.set("WECHAT_CORPID_" + corpBase.getWechatCorpid(), corpBase.getCorpcode());
        }
    }

    /**
     * 初始化部门全路径
     *
     * @param belongid
     */
    public void initSysOrgPath(String belongid) {
        SysCorpOrgExample orgExample = new SysCorpOrgExample();
        SysCorpOrgExample.Criteria criteria = orgExample.createCriteria();
        criteria.andOrgtype2EqualTo(2);
        if (belongid != null) {
            criteria.andBelongOrgIdEqualTo("");
        }
        Integer count = sysCorpOrgMapper.countByExample(orgExample);

        Integer num = count / PAGE_COUNT;

        if (count % PAGE_COUNT != 0) {
            num++;
        }
        Map<String, Map> redisMap = new HashMap();
        for (int i = 0; i < num; i++) {
            int offset = i * PAGE_COUNT;
            List<Map> orgList = sysCorpBaseMapper.getOrgPath(belongid, PAGE_COUNT, offset);
            if (CollectionUtils.isNotEmpty(orgList)) {
                for (Map map : orgList) {
                    String belongId = ConvertHelper.stringConvert(map.get("belongid"));
                    Long orgid = ConvertHelper.longConvert(map.get("orgid"));
                    String org_path = (String) map.get("org_path");
                    if (redisMap.containsKey(belongId)) {
                        redisMap.get(belongId).put(orgid, org_path);
                    } else {
                        Map subMap = new HashMap();
                        subMap.put(orgid, org_path);
                        redisMap.put(belongId, subMap);
                    }
                }
            }
        }
        for (Map.Entry<String, Map> orgPathMap : redisMap.entrySet()) {
            String key = orgPathMap.getKey();
            redisTemplate.opsForHash().putAll(RedisKeyDefine.ORG_PATH_ + key, orgPathMap.getValue());
        }
    }

    public void initConfigKV() {
        SysConfigKvExample kvExample = new SysConfigKvExample();
        kvExample.createCriteria().andStatusEqualTo(1);
        List<SysConfigKv> kvList = sysConfigKvMapper.selectByExample(null);
        if (CollectionUtils.isNotEmpty(kvList)) {
            Map<Long, Map> redisMap = new HashMap();
            for (SysConfigKv kv : kvList) {
                Long corpid = kv.getCorpid();
                if (redisMap.containsKey(corpid)) {
                    redisMap.get(corpid).put(kv.getKey(), kv.getValue());
                } else {
                    Map subMap = new HashMap();
                    subMap.put(kv.getKey(), kv.getValue());
                    redisMap.put(corpid, subMap);
                }
            }
            for (Map.Entry<Long, Map> configkv : redisMap.entrySet()) {
                Long key = configkv.getKey();
                redisTemplate.opsForHash().putAll(RedisKeyDefine.SYS_CONFIG_KV_ + key, configkv.getValue());
            }
        }
    }

    public void initWxCorpInfo() {
        WxCorpInfoExample example = new WxCorpInfoExample();
        List<WxCorpInfo> wxCorpInfoList = wxCorpInfoMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(wxCorpInfoList)) {
            for (WxCorpInfo corpInfo : wxCorpInfoList) {
                String wxCorpId = corpInfo.getWxCorpId();
                String appcode = corpInfo.getAppcode();
                String key = RedisKeyDefine.WX_CORP_INFO_ + wxCorpId + "_" + appcode;
                try {
                    redisTemplate.opsForValue().set(key, JSONUtils.ObjectToJson(corpInfo));
                } catch (Exception e) {
                    log.error("initWxCorpInfo err,{}", e.getMessage(), e);
                }
            }
        }
    }

    public void clearReportCache() {
        clearRedisCache("com.caidao1.report.service.ReportConfigService.saveReport2Cache");
        clearRedisCache("com.caidaocloud.imports.service.application.service.ioc.ImportConfigService.getAllFieldInfoList");
        clearRedisCache("com.caidao1.report.service.ReportCacheService.getAllReportTableList");
    }

    public void clearRedisCache(String cachekey) {
        Set<String> keys = redisTemplate.keys(SecurityUtil.getMd5Str(cachekey) + "*");
        if (CollectionUtils.isNotEmpty(keys)) {
            redisTemplate.delete(keys);
        }
    }
}
