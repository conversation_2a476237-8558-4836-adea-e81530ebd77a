package com.caidaocloud.attendance.core.commons.utils;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.DateUtil;
import org.apache.commons.beanutils.BeanUtils;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by lufangge on 2018/8/2.
 */
public class DateUtilExt {

    public static Map<String, Long> getMonthDateByYm(Integer ym) {
        Calendar c = Calendar.getInstance();
        c.clear();
        c.set(Integer.valueOf(ym.toString().substring(0, 4)), Integer.valueOf(ym.toString().substring(4, 6)) - 1, 1, 0, 0, 0);
        Integer maxday = c.getActualMaximum(Calendar.DAY_OF_MONTH);
        Long startdate = c.getTimeInMillis() / 1000;
        c.set(Calendar.DAY_OF_MONTH, maxday);
        Long enddate = c.getTimeInMillis() / 1000 + (24 * 60 * 60) - 1;
        Map<String, Long> map = new HashMap<>();
        map.put("start", startdate);
        map.put("end", enddate);
        return map;
    }

    /**
     * 获取指定日期所在月份开始的时间戳
     *
     * @param time 指定日期
     * @return
     */
    public static Long getMonthBegin(Long time) throws ParseException {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse(DateUtil.getDateStrByTimesamp(time));
        c.setTime(date);
        //设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        // 获取本月第一天的时间戳
        return Long.valueOf(c.getTimeInMillis() / 1000L);
    }

    /**
     * 获取指定日期所在月份结束的时间戳
     *
     * @param time 指定日期
     * @return
     */
    public static Long getMonthEnd(Long time) throws ParseException {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse(DateUtil.getDateStrByTimesamp(time));
        c.setTime(date);
        //设置为当月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND, 59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 999);
        // 获取本月最后一天的时间戳
        return Long.valueOf(c.getTimeInMillis() / 1000L);
    }


    /**
     * 获取指定日期所在月份结束的时间戳
     *
     * @param time 指定日期
     * @return
     */
    public static Long getMonthEndDay(Long time) throws ParseException {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse(DateUtil.getDateStrByTimesamp(time));
        c.setTime(date);
        //设置为当月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        // 获取本月最后一天的时间戳
        return Long.valueOf(c.getTimeInMillis() / 1000L);
    }

    /**
     * 获取指定日期的时间
     *
     * @param time
     * @param month
     * @param day
     * @return
     * @throws ParseException
     */
    public static Long getMonth(Long time, int month, int day) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse(DateUtil.getDateStrByTimesamp(time));
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, month);
        c.set(Calendar.DAY_OF_MONTH, day);
        Date m = c.getTime();
        return m.getTime() / 1000;
    }

    /**
     * 根据指定的时间戳获取指定天数的日期
     *
     * @param time
     * @return
     */
    public static Long getDateTimeByAmount(Long time, int amount) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date date = format.parse(DateUtil.getDateStrByTimesamp(time));
            Calendar c = Calendar.getInstance();

            c.setTime(date);
            c.add(Calendar.DATE, amount);
            Date m = c.getTime();
            return m.getTime() / 1000;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取明天的开始时间
     *
     * @return
     */
    public static Date getBeginDayOfTomorrow() {
        Calendar cal = new GregorianCalendar();
        cal.setTime(getDayBegin());
        cal.add(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    //获取明天的结束时间
    public static Date getEndDayOfTomorrow() {
        Calendar cal = new GregorianCalendar();
        cal.setTime(getDayEnd());
        cal.add(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    //获取当天的开始时间
    public static java.util.Date getDayBegin() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        System.out.println(cal.getTime());
        return cal.getTime();
    }

    //获取当天的结束时间
    public static java.util.Date getDayEnd() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        System.out.println(cal.getTime());
        return cal.getTime();
    }

    //获取当天的开始时间
    public static Long getDayBeginTime(String patten) {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        System.out.println(cal.getTime());
        DateFormat df = new SimpleDateFormat(patten);
        try {
            return df.parse(df.format(cal.getTime())).getTime() / 1000;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    //获取当天的结束时间
    public static Long getDayEndTime(String patten) {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        System.out.println(cal.getTime());
        DateFormat df = new SimpleDateFormat(patten);
        try {
            return df.parse(df.format(cal.getTime())).getTime() / 1000;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    //获取本周的开始时间
    public static Date getBeginDayOfWeek() {
        Date date = new Date();
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayofweek = cal.get(Calendar.DAY_OF_WEEK);
        if (dayofweek == 1) {
            dayofweek += 7;
        }
        cal.add(Calendar.DATE, 2 - dayofweek);
        return getDayStartTime(cal.getTime());
    }

    //获取本周的结束时间
    public static Date getEndDayOfWeek() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getBeginDayOfWeek());
        cal.add(Calendar.DAY_OF_WEEK, 6);
        Date weekEndSta = cal.getTime();
        return getDayEndTime(weekEndSta);
    }

    //获取某个日期的开始时间
    public static Timestamp getDayStartTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if (null != d) calendar.setTime(d);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return new Timestamp(calendar.getTimeInMillis());
    }

    //获取某个日期的结束时间
    public static Timestamp getDayEndTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if (null != d) calendar.setTime(d);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return new Timestamp(calendar.getTimeInMillis());
    }

    //获取本月的开始时间
    public static Date getBeginDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(getNowYear(), getNowMonth() - 1, 1);
        return getDayStartTime(calendar.getTime());
    }

    //获取本月的结束时间
    public static Date getEndDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(getNowYear(), getNowMonth() - 1, 1);
        int day = calendar.getActualMaximum(5);
        calendar.set(getNowYear(), getNowMonth() - 1, day);
        return getDayEndTime(calendar.getTime());
    }

    //获取今年是哪一年
    public static Integer getNowYear() {
        Date date = new Date();
        GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
        gc.setTime(date);
        return Integer.valueOf(gc.get(1));
    }

    //获取本月是哪一月
    public static int getNowMonth() {
        Date date = new Date();
        GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
        gc.setTime(date);
        return gc.get(2) + 1;
    }

    public static int getMonthInt(Date date) {
        GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
        gc.setTime(date);
        return gc.get(2) + 1;
    }

    //获取本月是哪一月
    public static int getNowMonth(Long dateTime) {
        Date date = new Date(dateTime * 1000);
        GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
        gc.setTime(date);
        return gc.get(2) + 1;
    }

    public static String getDateStrByTimesamp(Long ts) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(new Date(ts * 1000));
    }

    /**
     * 获取指定日期所在的年份
     *
     * @param time
     * @return
     * @throws ParseException
     */
    public static Integer getTimeYear(Long time) throws ParseException {
        GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse(DateUtil.getDateStrByTimesamp(time));
        gc.setTime(date);
        return Integer.valueOf(gc.get(1));
    }

    /**
     * 获取指定日期所在年份的开始时间
     *
     * @param time
     * @return
     * @throws ParseException
     */
    public static Long getYearBeginTime(Long time) throws ParseException {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse(DateUtil.getDateStrByTimesamp(time));
        c.setTime(date);
        c.set(Calendar.MONTH, 0);
        //设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        // 获取本年第一天的时间戳
        return Long.valueOf(c.getTimeInMillis() / 1000L);
    }

    /**
     * 获取指定日期所在年份结束的时间
     *
     * @param time 指定日期
     * @return
     */
    public static Long getYearsEndTime(Long time) throws ParseException {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse(DateUtil.getDateStrByTimesamp(time));
        c.setTime(date);
        c.set(Calendar.MONTH, 11);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        return Long.valueOf(c.getTimeInMillis() / 1000L);
    }

    /**
     * 获取两个日期相差的天数
     *
     * @param startTime
     * @param endTime
     * @return
     * @throws ParseException
     */
    public static Integer getDifferenceDay(Long startTime, Long endTime) throws ParseException {
        Calendar cal1 = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date1 = format.parse(DateUtil.getDateStrByTimesamp(startTime));
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        Date date2 = format.parse(DateUtil.getDateStrByTimesamp(endTime));
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2) {
            //同一年
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) {
                    //闰年
                    timeDistance += 366;
                } else {
                    //不是闰年
                    timeDistance += 365;
                }
            }
            return timeDistance + (day2 - day1);
        } else {
            //不同年
            return day2 - day1;
        }
    }

    /**
     * 在原有时间上添加或减去几个月或几天
     *
     * @param time
     * @param month
     * @param date
     * @return
     */
    public static Long addMonthDate(Long time, int month, int date) {
        Calendar c = Calendar.getInstance();
        c.clear();
        c.setTimeInMillis(time);
        c.add(Calendar.MONTH, month);
        c.add(Calendar.DATE, date);
        return c.getTimeInMillis() / 1000;
    }

    public static Long addMonthDate(Long time, int year, int month, int date) {
        Calendar c = Calendar.getInstance();
        c.clear();
        c.setTimeInMillis(time);
        c.add(Calendar.YEAR, year);
        c.add(Calendar.MONTH, month);
        c.add(Calendar.DATE, date);
        return c.getTimeInMillis() / 1000;
    }

    public static Long getAddYearOrMonthOrDateEndTime(Long time, int year, int month, int date) {
        Calendar c = Calendar.getInstance();
        c.clear();
        c.setTimeInMillis(time);
        c.add(Calendar.YEAR, year);
        c.add(Calendar.MONTH, month);
        c.add(Calendar.DATE, date);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTimeInMillis() / 1000;
    }

    public static Long addMonth(Long time, int month) throws ParseException {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse(DateUtil.getDateStrByTimesamp(time));
        c.setTime(date);
        c.add(Calendar.MONTH, month);
        return c.getTimeInMillis() / 1000L;
    }

    public static Long addYear(Long time, int year) throws ParseException {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = format.parse(DateUtil.getDateStrByTimesamp(time));
        c.setTime(date);
        c.add(Calendar.YEAR, year);
        return c.getTimeInMillis() / 1000L;
    }

    public static String getTimeStrByPattern(Long time, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(new Date(time * 1000));
    }

    public static Long getTimeByPattern(Long time, String pattern) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            String timeStr = sdf.format(new Date(time * 1000));
            Date date = sdf.parse(timeStr);
            return date.getTime() / 1000;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0l;
    }

    public static String getCurTimeStr() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(new Date());
    }

    public static Long getTimesampByDateStr3(String dateStr) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = format.parse(dateStr);
            return date.getTime() / 1000;
        } catch (Exception e) {
            e.printStackTrace();
            return 0L;
        }
    }

    public static String getDateStrByTimesamp5(Long ts) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(new Date(ts.longValue() * 1000L));
    }

    public static String getDateFormatStr(Long ts, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(ts.longValue() * 1000L));
    }

    public static List<String> getMonthBetween(Long minTime, Long maxTime) throws ParseException {
        List<String> result = new ArrayList<>();
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");//格式化为年月
        Date minDate = sdf.parse(sdf.format(new Date(minTime * 1000)));
        Date maxDate = sdf.parse(sdf.format(new Date(maxTime * 1000)));

        min.setTime(minDate);
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

        max.setTime(maxDate);
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }

    /**
     * 获取两个日期之间的所有日期
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @return
     */
    public static List<Long> listDateByTimePeriod(Long startTime, Long endTime) {
        List<Long> dateList = new ArrayList<>();

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date start = dateFormat.parse(dateFormat.format(new Date(startTime * 1000)));
            Date end = dateFormat.parse(dateFormat.format(new Date(endTime * 1000)));

            Calendar startInstance = Calendar.getInstance();
            startInstance.setTime(start);

            Calendar endInstance = Calendar.getInstance();
            endInstance.setTime(end);
            endInstance.add(Calendar.DATE, +1);// 日期加1(包含结束)

            while (startInstance.before(endInstance)) {
                dateList.add(startInstance.getTime().getTime() / 1000);
                startInstance.add(Calendar.DAY_OF_YEAR, 1);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return dateList;
    }

    /**
     * 获取两个日期之间的所有日期
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @return
     */
    public static List<Integer> listDateIntByTimePeriod(Long startTime, Long endTime) {
        List<Integer> dateList = new ArrayList<>();

        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        try {
            Date start = dateFormat.parse(dateFormat.format(new Date(startTime * 1000)));
            Date end = dateFormat.parse(dateFormat.format(new Date(endTime * 1000)));

            Calendar startInstance = Calendar.getInstance();
            startInstance.setTime(start);

            Calendar endInstance = Calendar.getInstance();
            endInstance.setTime(end);
            endInstance.add(Calendar.DATE, +1);// 日期加1(包含结束)

            while (startInstance.before(endInstance)) {
                dateList.add(Integer.valueOf(dateFormat.format(startInstance.getTime())));
                startInstance.add(Calendar.DAY_OF_YEAR, 1);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return dateList;
    }

    public static Object mapToObject(Map<String, Object> map, Class<?> beanClass) throws Exception {
        if (map == null)
            return null;

        Object obj = beanClass.newInstance();

        BeanUtils.populate(obj, map);

        return obj;
    }

    public static Map<?, ?> objectToMap(Object obj) {
        if (obj == null)
            return null;

        return new org.apache.commons.beanutils.BeanMap(obj);
    }

    /**
     * 获取两个日期相差的实际月数
     *
     * @param start
     * @param end
     * @return
     */
    public static int getMonthDiff(Long start, Long end) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.clear();
        c2.clear();
        c1.setTimeInMillis(start * 1000);
        c2.setTimeInMillis(end * 1000);

        int year1 = c1.get(Calendar.YEAR);
        int year2 = c2.get(Calendar.YEAR);
        int month1 = c1.get(Calendar.MONTH);
        int month2 = c2.get(Calendar.MONTH);
        int day1 = c1.get(Calendar.DAY_OF_MONTH);
        int day2 = c2.get(Calendar.DAY_OF_MONTH);

        //获取年的差值 
        int yearInterval = year1 - year2;
        // 如果d1的月-日小于d2的月-日,那么yearInterval--这样就得到了相差的年数
        if (month1 < month2 || month1 == month2 && day1 < day2) {
            yearInterval--;
        }
        // 获取月数差值
        int monthInterval = (month1 + 12) - month2;
        if (day1 < day2) {
            monthInterval--;
        }
        monthInterval %= 12;
        int monthsDiff = Math.abs(yearInterval * 12 + monthInterval);
        return monthsDiff;
    }

    public static Integer getTimeDay(Long time) {
        Date dt = new Date(time * 1000);
        return Integer.valueOf(String.format("%td", dt));
    }

    /**
     * 获取一个月中的某一天
     *
     * @param time
     * @return
     */
    public static Integer getTimeMonthDay(Long time) {
        Date dt = new Date(time * 1000);
        return Integer.valueOf(String.format("%te", dt));
    }

    public static String convertDateTimeZone(String time) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            Date date = formatter.parse(time);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String sDate = sdf.format(date);
            System.out.println(sDate);
            return sDate;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据一段时间区间，按月份拆分成多个时间段
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return
     */
    public static List<KeyValueForDate> splitDateForMonth(String startDate, String endDate) {
        List<KeyValueForDate> list = new ArrayList<>();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            String firstDay = "";
            String lastDay = "";
            Date d1 = sdf.parse(startDate);// 定义起始日期
            Date d2 = sdf.parse(endDate);// 定义结束日期

            Calendar dd = Calendar.getInstance();// 定义日期实例
            dd.setTime(d1);// 设置日期起始时间
            Calendar cale = Calendar.getInstance();

            Calendar c = Calendar.getInstance();
            c.setTime(d2);

            int startDay = d1.getDate();
            int endDay = d2.getDate();

            if ((startDate.endsWith(endDate)) || (dd.get(Calendar.YEAR) == c.get(Calendar.YEAR) && dd.get(Calendar.MONTH) == c.get(Calendar.MONTH))) {
                list.add(new KeyValueForDate(startDate, endDate));
                return list;
            }

            KeyValueForDate keyValueForDate = null;
            while (dd.getTime().before(d2)) {// 判断是否到结束日期
                keyValueForDate = new KeyValueForDate();
                cale.setTime(dd.getTime());
                if (dd.getTime().equals(d1)) {
                    cale.set(Calendar.DAY_OF_MONTH, dd.getActualMaximum(Calendar.DAY_OF_MONTH));
                    lastDay = sdf.format(cale.getTime());
                    keyValueForDate.setStartDate(sdf.format(d1));
                    keyValueForDate.setEndDate(lastDay);

                } else if (dd.get(Calendar.MONTH) == d2.getMonth() && dd.get(Calendar.YEAR) == c.get(Calendar.YEAR)) {
                    cale.set(Calendar.DAY_OF_MONTH, 1);//取第一天
                    firstDay = sdf.format(cale.getTime());

                    keyValueForDate.setStartDate(firstDay);
                    keyValueForDate.setEndDate(sdf.format(d2));

                } else {
                    cale.set(Calendar.DAY_OF_MONTH, 1);//取第一天
                    firstDay = sdf.format(cale.getTime());

                    cale.set(Calendar.DAY_OF_MONTH, dd.getActualMaximum(Calendar.DAY_OF_MONTH));
                    lastDay = sdf.format(cale.getTime());

                    keyValueForDate.setStartDate(firstDay);
                    keyValueForDate.setEndDate(lastDay);

                }
                list.add(keyValueForDate);
                dd.add(Calendar.MONTH, 1);// 进行当前日期月份加1

            }

            if (endDay < startDay) {
                keyValueForDate = new KeyValueForDate();

                cale.setTime(d2);
                cale.set(Calendar.DAY_OF_MONTH, 1);//取第一天
                firstDay = sdf.format(cale.getTime());

                keyValueForDate.setStartDate(firstDay);
                keyValueForDate.setEndDate(sdf.format(d2));
                list.add(keyValueForDate);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return list;
    }

    /**
     * 把对象转换成JSON字符串
     *
     * @param value
     * @return
     */
    public static String createJsonString(Object value) {
        String str = JSON.toJSONString(value);
        return str;
    }

    /**
     * JSON解析成实体对象
     *
     * @param jsonString
     * @param cls        实体对象Class类
     * @return
     */
    public static <T> T json2povo(String jsonString, Class<T> cls) {
        T t = null;
        try {
            t = JSON.parseObject(jsonString, cls);
        } catch (Exception e) {
        }
        return t;
    }

    /**
     * 利用反射将map集合封装成bean对象
     *
     * @param clazz
     * @return
     */
    public static <T> T mapToBean(Map<String, Object> map, Class<?> clazz) throws Exception {
        Object obj = clazz.newInstance();
        if (map != null && !map.isEmpty() && map.size() > 0) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String propertyName = entry.getKey();    // 属性名
                Object value = entry.getValue();        // 属性值
                String setMethodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
                Field field = getClassField(clazz, propertyName);    //获取和map的key匹配的属性名称
                if (field == null) {
                    continue;
                }
                Class<?> fieldTypeClass = field.getType();
                value = convertValType(value, fieldTypeClass);
                try {
                    clazz.getMethod(setMethodName, field.getType()).invoke(obj, value);
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
            }
        }
        return (T) obj;
    }

    /**
     * 根据给定对象类匹配对象中的特定字段
     *
     * @param clazz
     * @param fieldName
     * @return
     */
    private static Field getClassField(Class<?> clazz, String fieldName) {
        if (Object.class.getName().equals(clazz.getName())) {
            return null;
        }
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field field : declaredFields) {
            if (field.getName().equals(fieldName)) {
                return field;
            }
        }
        Class<?> superClass = clazz.getSuperclass();    //如果该类还有父类，将父类对象中的字段也取出
        if (superClass != null) {                        //递归获取
            return getClassField(superClass, fieldName);
        }
        return null;
    }

    /**
     * 将map的value值转为实体类中字段类型匹配的方法
     *
     * @param value
     * @param fieldTypeClass
     * @return
     */
    private static Object convertValType(Object value, Class<?> fieldTypeClass) {
        Object retVal = null;

        if (Long.class.getName().equals(fieldTypeClass.getName())
                || long.class.getName().equals(fieldTypeClass.getName())) {
            retVal = Long.parseLong(value.toString());
        } else if (Integer.class.getName().equals(fieldTypeClass.getName())
                || int.class.getName().equals(fieldTypeClass.getName())) {
            retVal = Integer.parseInt(value.toString());
        } else if (Float.class.getName().equals(fieldTypeClass.getName())
                || float.class.getName().equals(fieldTypeClass.getName())) {
            retVal = Float.parseFloat(value.toString());
        } else if (Double.class.getName().equals(fieldTypeClass.getName())
                || double.class.getName().equals(fieldTypeClass.getName())) {
            retVal = Double.parseDouble(value.toString());
        } else {
            retVal = value;
        }
        return retVal;
    }

    public static Integer getCurrentYearMonth() {
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        String nowData = format.format(date);
        return Integer.parseInt(nowData);
    }

    public static Map<String, String> getMothDay(Integer searchMonth) {
        Map<String, String> map = new HashMap();
        try {
            SimpleDateFormat format = new SimpleDateFormat(
                    "yyyyMMdd");
            String time = searchMonth + "01";
            Date firstDay = format.parse(time);
            Long fristTime = firstDay.getTime() / 1000 - 3600 * 24 * 7;
            Date lastDay = DateUtil.getLastDayOfMonth(firstDay);
            Long lastTime = lastDay.getTime() / 1000 + 3600 * 24 * 7;
            String start = DateUtil.convertDateTimeToStr(fristTime, "yyyy-MM-dd", true);
            String end = DateUtil.convertDateTimeToStr(lastTime, "yyyy-MM-dd", true);
            map.put("start", start);
            map.put("end", end);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    public static String getDateByStr(String date) {
        try {
            Date format = null;
            format = new SimpleDateFormat("yyyy-MM-dd").parse(date);
            String shortDate = new SimpleDateFormat("yyyyMMdd").format(format);
            return shortDate;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 计算年初日期时间戳
     *
     * @param year
     * @return
     */
    public static Long getYearBeginTimeByYear(Integer year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, 0, 1, 0, 0, 0);
        return calendar.getTime().getTime() / 1000;
    }

    /**
     * 计算年底日期时间戳
     *
     * @param year
     * @return
     * @throws ParseException
     */
    public static Long getYearEndTimeByYear(Integer year) throws ParseException {
        return DateUtilExt.getYearsEndTime(DateUtilExt.getYearBeginTimeByYear(year));
    }
}