package com.caidaocloud.attendance.service.application.dto.emp;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeParent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostCenterDto {
    @ApiModelProperty("成本中心ID")
    private String bid;
    @ApiModelProperty("成本中心名称")
    private String costCenterName;
    @ApiModelProperty("成本中心编码")
    private String costCenterCode;
    @ApiModelProperty("上级成本中心")
    private TreeParent pid;
    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("公司代码")
    private String companyCode;
    @ApiModelProperty("控制范围名称")
    private String controlRangeName;
    @ApiModelProperty("控制范围")
    private String controlRange;
    @ApiModelProperty("负责人")
    private EmpSimple leader;
    @ApiModelProperty("状态（0 已启用 1 已停用）")
    private EnumSimple status;
    @ApiModelProperty("生效日期")
    private long dataStartTime;
}
