package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.core.wa.dto.ot.GetOtTimeResultDto;
import com.caidaocloud.attendance.core.wa.dto.ot.OtCompensateTypeListDto;
import com.caidaocloud.attendance.sdk.dto.SdkOtRevokeDTO;
import com.caidaocloud.attendance.sdk.dto.SdkOverApplySaveDTO;
import com.caidaocloud.attendance.sdk.feign.fallback.OvertimeFeignFallBack;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * 加班
 *
 * <AUTHOR>
 * @Date 2023/6/19
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = OvertimeFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "overtimeFeignClient")
public interface IOvertimeFeignClient {

    /**
     * 获取加班时长
     *
     * @param empId
     * @param overtimeTypeId
     * @param startTime
     * @param endTime
     * @param stime
     * @param etime
     * @param overtimeDate   加班日期（年月日unix时间戳，精确到秒）
     * @return
     */
    @GetMapping(value = "/api/attendance/overtimeapply/v1/getOtTotaltime")
    Result<GetOtTimeResultDto> getOtTotaltime(@RequestParam(value = "empId", required = false) Long empId,
                                              @RequestParam("overtimeTypeId") Integer overtimeTypeId,
                                              @RequestParam("startTime") Integer startTime,
                                              @RequestParam("endTime") Integer endTime,
                                              @RequestParam("stime") Integer stime,
                                              @RequestParam("etime") Integer etime,
                                              @RequestParam(value = "overtimeDate", required = false) Long overtimeDate);

    /**
     * 获取加班日期列表
     *
     * @param empId
     * @param startTime
     * @param endTime
     * @param stime
     * @param etime
     * @return
     */
    @GetMapping(value = "/api/attendance/overtimeapply/v1/getOvertimeDateList")
    Result<List<KeyValue>> getOvertimeDateList(@RequestParam(value = "empId", required = false) Long empId,
                                               @RequestParam("startTime") Integer startTime,
                                               @RequestParam("endTime") Integer endTime,
                                               @RequestParam("stime") Integer stime,
                                               @RequestParam("etime") Integer etime);

    /**
     * 选人加班申请保存
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/overtimeapply/v1/saveOverApply")
    Result<?> saveOverApply(@RequestBody SdkOverApplySaveDTO dto);

    @PostMapping(value = "/api/attendance/overtimeapply/v1/revokeEmpOt")
    Result<?> revokeEmpOt(@RequestBody SdkOtRevokeDTO dto);

    /**
     * 加班结余时长
     *
     * @param empId
     * @return
     */
    @GetMapping(value = "/api/attendance/overtimeapply/v1/getEmpOvertimeLeftDuration")
    Result<?> getEmpOvertimeLeftDuration(@RequestParam(value = "empId", required = false) Long empId);

    /**
     * 加班申请时：获取补偿方式
     *
     * @param empid
     * @param start
     * @param end
     * @param overtimeDate 加班日期（年月日unix时间戳，精确到秒）
     * @return
     */
    @GetMapping(value = "/api/attendance/overtimeapply/v1/getCompensateTypeList")
    Result<List<OtCompensateTypeListDto>> getCompensateTypeList(@RequestParam(value = "empid", required = false) Long empid,
                                                                @RequestParam("start") Long start,
                                                                @RequestParam("end") Long end,
                                                                @RequestParam(value = "overtimeDate", required = false) Long overtimeDate);

    @ApiOperation("周期内加班总时长")
    @GetMapping("/api/attendance/overtimeapply/v1/getEmpPeriodOtDuration")
    Result<BigDecimal> getEmpPeriodOtDuration(@RequestParam(value = "empId", required = false) Long empId, @RequestParam("startDate") Long startDate);
}
