package com.caidaocloud.attendance.service.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 考勤服务消息队列配置属性类
 * 统一管理所有 MQ 相关的 Nacos 配置项
 * <p>
 * 对应的 Nacos 配置：
 * attendance:
 * mq:
 * enableDLQ: true
 * maxRetryCount: 3
 * failureStrategy: ACK
 */
@Data
@ConfigurationProperties(prefix = "attendance.mq")
public class AttendanceMqProperties {

    /**
     * 是否启用死信队列功能
     * 默认禁用，避免与现有队列产生参数冲突
     * 设置为 true 将创建完整的死信队列保护机制
     * 设置为 false 将创建普通队列，失败消息根据 failureStrategy 处理
     */
    private boolean enableDLQ = false;

    /**
     * 最大重试次数
     * 建议值：1-5，过高可能影响性能
     */
    @Min(value = 0, message = "最大重试次数不能小于0")
    private int maxRetryCount = 1;

    /**
     * 失败消息处理策略（仅在 enableDLQ=false 时生效）
     * ACK: 确认消息（丢弃消息，推荐用于生产环境）
     * NACK_REQUEUE: 拒绝并重新入队（可能导致无限循环，慎用）
     * NACK_DISCARD: 拒绝并丢弃（需要确保队列没有死信队列配置）
     */
    @NotBlank(message = "失败处理策略不能为空")
    private String failureStrategy = "ACK";

    /**
     * 消费者并发数配置
     */
    private Consumer consumer = new Consumer();

    /**
     * 消费者配置内嵌类
     */
    @Data
    public static class Consumer {
        /**
         * 并发消费者数量（最小值）
         */
        @Min(value = 1, message = "最小并发消费者数量不能小于1")
        private int concurrentConsumers = 1;

        /**
         * 最大并发消费者数量
         */
        @Min(value = 1, message = "最大并发消费者数量不能小于1")
        private int maxConcurrentConsumers = 3;

        /**
         * 预取数量，控制每个消费者一次性获取的消息数量
         */
        @Min(value = 1, message = "预取数量不能小于1")
        private int prefetchCount = 1;

        /**
         * 消费者超时时间（毫秒）
         * 0 表示无超时限制
         * 建议设置为业务处理时间的 2-3 倍
         * 例如：业务处理需要 10 分钟，设置为 1800000（30 分钟）
         */
        @Min(value = 0, message = "消费者超时时间不能小于0")
        private long consumerTimeout = 1800000; // 30 分钟

        /**
         * 接收超时时间（毫秒）
         * 消费者等待消息的最大时间
         */
        @Min(value = 0, message = "接收超时时间不能小于0")
        private long receiveTimeout = 60000; // 60 秒

        /**
         * 是否自动启动消费者
         */
        private boolean autoStartup = true;

        /**
         * 消费者任务执行器线程名前缀
         */
        private String taskExecutorName = "attendance-mq-executor-";

        /**
         * 是否启用超时自动ACK机制
         * true: 在接近超时时自动ACK消息避免重复投递
         * false: 使用传统的超时处理方式
         */
        private boolean enableTimeoutAutoAck = true;

        /**
         * 安全ACK时间比例
         * 在达到 consumerTimeout * timeoutAckRatio 时自动ACK
         * 建议值：0.8-0.9
         */
        private double timeoutAckRatio = 0.85;

        /**
         * 是否在立即ACK后继续异步处理
         * true: ACK后继续异步处理业务逻辑
         * false: ACK后不处理业务逻辑
         */
        private boolean continueProcessingAfterTimeoutAck = true;

        /**
         * 超时检测间隔（毫秒）
         * 超时检测线程检查间隔时间
         */
        @Min(value = 100, message = "超时检测间隔不能小于100ms")
        private long timeoutCheckInterval = 1000;
    }

    /**
     * 队列配置
     */
    private Queue queue = new Queue();

    /**
     * 队列配置内嵌类
     */
    @Data
    public static class Queue {
        /**
         * 消息TTL（毫秒），超时后进入死信队列
         * 0 表示不设置TTL
         */
        @Min(value = 0, message = "消息TTL不能小于0")
        private long messageTtl = 0;

        /**
         * 是否自动删除队列
         */
        private boolean autoDelete = false;
    }

    /**
     * 验证失败策略是否合法
     */
    public boolean isValidFailureStrategy() {
        return "ACK".equalsIgnoreCase(failureStrategy) ||
                "NACK_REQUEUE".equalsIgnoreCase(failureStrategy) ||
                "NACK_DISCARD".equalsIgnoreCase(failureStrategy);
    }

    /**
     * 获取失败策略枚举
     */
    public FailureStrategy getFailureStrategyEnum() {
        try {
            return FailureStrategy.valueOf(failureStrategy.toUpperCase());
        } catch (IllegalArgumentException e) {
            return FailureStrategy.ACK; // 默认返回ACK
        }
    }

    /**
     * 失败策略枚举
     */
    public enum FailureStrategy {
        /**
         * 确认消息（丢弃）
         */
        ACK,

        /**
         * 拒绝并重新入队
         */
        NACK_REQUEUE,

        /**
         * 拒绝并丢弃
         */
        NACK_DISCARD
    }
} 