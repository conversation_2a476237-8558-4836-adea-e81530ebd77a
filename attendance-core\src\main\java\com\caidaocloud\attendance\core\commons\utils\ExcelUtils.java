package com.caidaocloud.attendance.core.commons.utils;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.handler.inter.IWriter;
import com.caidaocloud.attendance.core.commons.handler.ExcelStyleHandler;
import com.caidaocloud.excption.ServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtils {
    public static void downloadDataMapExcel(List<ExcelExportEntity> colList, List<Map> list, String xlsFileName, HttpServletResponse response) {
        downloadDataListMapExcel(colList, list, xlsFileName, response);
    }

    public static void downloadDataListMapExcel(List<ExcelExportEntity> colList, List<?> list, String xlsFileName, HttpServletResponse response) {
        IWriter<Workbook> workbookIWriter = null;
        try {
            ExportParams exportParams = new ExportParams();
            exportParams.setStyle(ExcelStyleHandler.class);
            workbookIWriter = ExcelExportUtil.exportBigExcel(exportParams, colList);
            workbookIWriter.write(list);
            final Workbook workbook = workbookIWriter.get();
            downLoadExcel(xlsFileName, response, workbook);
        } catch (IOException e) {
            log.error("downLoadExcelErr:{}",e.getMessage(),e);
            throw new ServerException("download excel err.");
        } finally {
            if (null != workbookIWriter) {
                workbookIWriter.close();
            }
        }
    }

    /**
     * excel下载
     * @param fileName 下载时的文件名称
     * @param response
     * @param workbook excel数据
     */
    public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) throws IOException {
        try {
            ExcelStyleHandler.autoWidth(workbook);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    public static void downLoadSXSSFExcel(String fileName, HttpServletResponse response, Workbook workbook) throws IOException {
        try {
            ExcelStyleHandler.autoWidth(workbook);
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    public static void writeDataFile(File file, List<ExcelExportEntity> colList, List<Map<String, Object>> list, String xlsFileName) throws IOException {
        final Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), colList, list);
        workbook.write(new FileOutputStream(file));
    }

    public static List<ExcelExportEntity> buildExportEntity(Class clazz) {
        List<ExcelExportEntity> colList = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            Excel annotation = f.getAnnotation(Excel.class);
            if (null == annotation) {
                continue;
            }
            String name = annotation.name();
            String property = f.getName();
            String order = annotation.orderNum();
            double width = annotation.width();
            if (width == 0) {
                width = 13;
            }
            ExcelExportEntity entity = new ExcelExportEntity(name, property);
            entity.setWidth(width);
            if (null != order) {
                entity.setOrderNum(Integer.valueOf(order));
            }
            colList.add(entity);
        }
        return colList;
    }
}
