package com.caidaocloud.attendance.service.application.feign;

import com.caidaocloud.attendance.service.application.dto.transfer.ChangeDefDto;
import com.caidaocloud.attendance.service.application.dto.transfer.TransferApplyDetailDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class WaTransferFeignFallBack implements IWaTransferFeignClient {
    @Override
    public Result<TransferApplyDetailDto> getTransferDetail(String applyId, boolean paasMode) {
        return Result.fail();
    }

    @Override
    public Result<ChangeDefDto> getChangeDefInfo(String bid) {
        return Result.fail();
    }
}
