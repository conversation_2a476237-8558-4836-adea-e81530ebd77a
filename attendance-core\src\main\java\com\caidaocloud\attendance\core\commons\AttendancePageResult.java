package com.caidaocloud.attendance.core.commons;

import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.github.miemiedev.mybatis.paginator.domain.Paginator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttendancePageResult<T> extends AttendanceBasePage {
    private List<T> items = new ArrayList<T>();

    private List<String> head = new ArrayList<>();

    public AttendancePageResult(List<T> items, int pageNo, int pageSize, int total) {
        this.setItems(items);
        this.setPageNo(pageNo);
        this.setPageSize(pageSize);
        this.setTotal(total);
    }

    public AttendancePageResult(PageList<T> items, int pageNo, int pageSize) {
        this.setItems(items);
        this.setPageSize(pageSize);
        if (items.getPaginator() != null) {
            Paginator paginator = items.getPaginator();
            this.setPageNo(paginator.getPage());
            this.setTotal(paginator.getTotalCount());
        } else {
            this.setPageNo(pageNo);
            this.setTotal(0);
        }
    }

    public AttendancePageResult(List<T> items, Paginator paginator, int pageNo, int pageSize) {
        this.setItems(items);
        this.setPageSize(pageSize);
        if (paginator != null) {
            this.setPageNo(paginator.getPage());
            this.setTotal(paginator.getTotalCount());
        } else {
            this.setPageNo(pageNo);
            this.setTotal(0);
        }
    }

    public AttendancePageResult(List<String> head) {
        this.setHead(head);
    }

    public static AttendancePageResult empty() {
        return new AttendancePageResult(new ArrayList<>(), 0, 0, 0);
    }
}
