package com.caidaocloud.attendance.service.application.event.subscribe;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.attendance.service.application.service.msg.MessageService;
import com.caidaocloud.mq.rabbitmq.consumer.AbsMQConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class HrPaasEntityMsgSubscribe extends AbsMQConsumer {

    @Resource
    private MessageService messageService;

    @Value("${mq.hrpaas.entity.change.subscribe.switch:false}")
    private boolean subscribeSwitch;

    @Override
    public void process(String message) {
        /// TODO 假勤模块不消费PaaS消息
        if(!subscribeSwitch){
            return;
        }
        if (null == message) {
            return;
        }
        try {
            EntityEvent entityEvent = JSON.parseObject(message, EntityEvent.class);
            if (null == entityEvent || null == entityEvent.getData()) {
                return;
            }
            //延时30s，同步员工年假配额
            try {
                // 使当前线程睡眠30秒
                Thread.sleep(30000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            List<EntityDataChange> data = entityEvent.getData();
            doEntityEvent(data);
        } catch (Exception ex) {
            log.error("An exception occurred in HrPaasEntityMsgSubscribe message listening. Why:{}", ex.getMessage(), ex);
        }
    }

    private void doEntityEvent(List<EntityDataChange> data) {
        if (null == data || data.isEmpty()) {
            log.warn("EntityDataChange data is empty.");
            return;
        }
        try {
            messageService.handleHrPaasMsg(data);
        } catch (Exception e) {
            log.error("data Change msg Processing failed,{}", e.getMessage(), e);
        }
    }
}
