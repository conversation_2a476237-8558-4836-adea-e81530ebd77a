package com.caidaocloud.attendance.core.config;

import io.shardingjdbc.core.api.algorithm.sharding.ListShardingValue;
import io.shardingjdbc.core.api.algorithm.sharding.ShardingValue;
import io.shardingjdbc.core.api.algorithm.sharding.hint.HintShardingAlgorithm;

import java.util.Collection;
import java.util.Collections;

public class DbShardingAlgorithm implements HintShardingAlgorithm {

    @Override
    public Collection<String> doSharding(Collection<String> collection, ShardingValue shardingValue) {
        for (String each : collection) {
            for (String value : ((ListShardingValue<String>) shardingValue).getValues()) {
                if (each.equals(value)) {
                    return Collections.singletonList(each);
                }
            }
        }
        return Collections.singletonList("public");
    }
}