package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2022/3/21 9:54
 * @Description:
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysConfigurationDto {
    @ApiModelProperty("主键id")
    private Integer configId;
    @ApiModelProperty("开关：1开（启用），2关（不启用）")
    private Integer status;
    @ApiModelProperty("configCode")
    private String configCode;
}
