package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.enums.LeaveStatusEnum;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.employee.mybatis.model.EmpFamily;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.mobile.service.MobileV16Service;
import com.caidao1.payroll.common.PayEngineUtil;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.model.SysUnitCity;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.WaEmpQuota;
import com.caidao1.wa.mybatis.model.*;
import com.caidao1.wa.service.RegisterAnalyzeService;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.WaAnalyzDTO;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.*;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkOvertimeMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpQuotaDetail;
import com.caidaocloud.attendance.service.infrastructure.util.FileUtil;
import com.caidaocloud.attendance.service.infrastructure.util.NumberCheckUtil;
import com.caidaocloud.attendance.service.interfaces.dto.FixQuotaDto;
import com.caidaocloud.attendance.service.interfaces.dto.FixQuotaSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.interfaces.dto.group.GroupDetailDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.LeaveTypeInfoDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OvertimeTransferRuleDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.*;
import com.caidaocloud.attendance.service.interfaces.vo.EmpCompensatoryQuotaVo;
import com.caidaocloud.attendance.service.interfaces.vo.EmpFixQuotaVo;
import com.caidaocloud.attendance.service.wfm.domain.entity.WorkingHourAnalyzeDo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.OtValidTimeCalTypeEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.DataInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.caidaocloud.attendance.service.infrastructure.common.QuotaTimeFormat.formatFloat;

/**
 * 员工假期配额服务类
 *
 * <AUTHOR>
 * @Date 2021/4/19
 */
@Slf4j
@Service
public class QuotaService implements IQuotaService {
    /**
     * 配额规则生成条件唯一编码之司龄（至年底）
     */
    private static final String QUOTA_GEN_CON_OF_ENDYEARCORPAGE = "corp_year_end_age";

    @Autowired
    private WaEmpQuotaDo waEmpQuotaDo;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private EmpCompensatoryQuotaDo empCompensatoryQuotaDo;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private LeaveQuotaConfigDo leaveQuotaConfigDo;
    @Autowired
    private QuotaGenRuleDo quotaGenRuleDo;
    @Autowired
    private SysEmpInfoDo sysEmpInfo;
    @Autowired
    private WaEmpQuotaMapper waEmpQuotaMapper;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private RegisterAnalyzeService registerAnalyzeService;
    @Autowired
    private WaSobMapper waSobMapper;
    @Autowired
    private WaParseGroupMapper waParseGroupMapper;
    @Autowired
    private WorkOvertimeMapper workOvertimeMapper;
    @Autowired
    private WaRegisterRecordDo waRegisterRecordDo;
    @Autowired
    private OverTimeTypeDo overTimeTypeDo;
    @Autowired
    private WaLeaveSettingDo waLeaveSettingDo;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private DataBackupDo dataBackupDo;
    @Autowired
    private WaEmpLeaveDo waEmpLeaveDo;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaLeaveDaytimeMapper waLeaveDaytimeMapper;
    @Autowired
    private EmpCompensatoryQuotaMapper empCompensatoryQuotaMapper;
    @Autowired
    private WaLeaveQuotaUseMapper waLeaveQuotaUseMapper;
    @Autowired
    private WaCompensatoryQuotaUseMapper waCompensatoryQuotaUseMapper;
    @Autowired
    private MobileV16Service mobileV16Service;
    @Autowired
    private EmpFamilyInfoDo empFamilyInfoDo;
    @Autowired
    private WaEmpTravelDo waEmpTravelDo;
    @Autowired
    private ILeaveTypeService leaveTypeService;
    @Autowired
    private IGroupService groupService;
    @Autowired
    private IOvertimeTransferRuleService overtimeTransferRuleService;
    @Autowired
    private IWaLeaveTypeDefService leaveTypeDefService;
    @Autowired
    private TravelCompensatoryDo travelCompensatoryDo;
    @Autowired
    private WaCompensatoryQuotaRecordDo compensatoryQuotaRecordDo;
    @Autowired
    private WaEmpOvertimeDetailMapper waEmpOvertimeDetailMapper;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        //允许没有定义的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //允许使用未带引号的字段名
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //允许使用单引号
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
    }

    private static final int PAGE_SIZE = 1000;

    @Override
    public int getEmpQuotaCountByYearAndType(QuotaDto dto) {
        return waEmpQuotaDo.getEmpQuotaCountByYearAndType(dto);
    }

    @Transactional
    @Override
    @Deprecated
    public void genEmpQuota(String belongId, Integer waGroupId, Long empid, Short year, boolean all,
                            boolean isCarryForward, boolean isReCalQuota, Long userId, String dataScope) throws Exception {
        if (empid != null) {
            waConfigService.genEmpQuotaDetail(belongId, waGroupId, Collections.singletonList(empid), year, all, DateUtil.getCurrentTime(true), isCarryForward, isReCalQuota, userId);
        } else {
            Long curDate = DateUtil.getOnlyDate();
            List<Long> empIds = waEmpQuotaDo.getGroupEmpList(waGroupId, year, curDate, dataScope);
            if (CollectionUtils.isNotEmpty(empIds)) {
                waConfigService.genEmpQuotaDetail(belongId, waGroupId, empIds, year, all, DateUtil.getCurrentTime(true), isCarryForward, isReCalQuota, userId);
            }
        }
    }

    @Override
    @CDText(exp = {"status:status_txt" + TextAspect.STATUS_ENUM}, classType = Map.class)
    public List<Map> getQuotaList(PageBean pageBean, Integer year, Integer[] quotaSettingId, String belongOrgId, List<Integer> years, String dataScope) {
        Map params = new HashMap();
        params.put("belongId", belongOrgId);
        params.put("curTime", DateUtil.getOnlyDate());
        params.put("curDate", DateUtil.getOnlyDate());
        if (year != null) {
            params.put("year", year);
        }
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            params.put("keywords", pageBean.getKeywords());
        }
        if (quotaSettingId != null && quotaSettingId.length > 0) {
            params.put("quotaSettingIds", quotaSettingId);
        }
        params.put("filter", pageBean.getFilter());
        params.put("years", years);
        params.put("datafilter", dataScope);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "emp_quota_id.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        List<Map> empQuotaList = waMapper.listEmpQuotaByParams(pageBounds, params);
        for (Map row : empQuotaList) {
            Integer acctTimeType = (Integer) row.get("acct_time_type");
            BigDecimal quotaDay = row.get("quota_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("quota_day"));
            BigDecimal deductionDay = row.get("deduction_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("deduction_day"));
            BigDecimal adjustQuota = row.get("adjust_quota") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("adjust_quota"));
            BigDecimal adjustUsedDay = row.get("adjust_used_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("adjust_used_day"));
            BigDecimal usedDay = row.get("used_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("used_day"));
            BigDecimal nowQuota = row.get("now_quota") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("now_quota"));
            BigDecimal fixUsedDay = row.get("fix_used_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("fix_used_day"));
            //计算留存有效剩余额度
            BigDecimal remainKy = BigDecimal.ZERO;
            BigDecimal remain_day = row.get("remain_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("remain_day"));
            BigDecimal remain_used_day = row.get("remain_used_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("remain_used_day"));
            Long remain_valid_date = (Long) row.get("remain_valid_date");
            if (remain_valid_date != null) {
                if (remain_valid_date >= DateUtil.getOnlyDate()) {
                    remainKy = remain_day.subtract(remain_used_day);
                }
            }
            BigDecimal frozenDay = BigDecimal.valueOf((Float) row.get("frozen_day"));
            float used_day = usedDay.add(fixUsedDay).floatValue();
            float curRemain = quotaDay.add(adjustQuota).add(remainKy).subtract(deductionDay).subtract(new BigDecimal(used_day)).subtract(adjustUsedDay).subtract(frozenDay).floatValue();
            float now_remain = nowQuota.add(adjustQuota).add(remainKy).subtract(deductionDay).subtract(new BigDecimal(used_day)).subtract(adjustUsedDay).subtract(frozenDay).floatValue();
            BigDecimal currentYearTotalQuota = nowQuota.add(adjustQuota).add(remainKy);//本年享有
            BigDecimal usedTotalQuota = usedDay.add(fixUsedDay).add(adjustUsedDay).add(deductionDay);//已使用
            BigDecimal remainTotalQuota = currentYearTotalQuota.subtract(usedTotalQuota);//剩余
            Float inTransitQuota = 0f;//在途
            if (row.containsKey("inTransitQuota") && row.get("inTransitQuota") != null) {
                inTransitQuota = (Float) row.get("inTransitQuota");
            }
            row.put("timeUnitName", PreTimeUnitEnum.getName(acctTimeType));
            Integer carryRule = 1;//分钟转小时 进位规则 1 四舍五入
            timeFormat(acctTimeType, now_remain, row, "now_remain", carryRule);
            timeFormat(acctTimeType, (Float) row.get("quota_day"), row, "quota_day", carryRule);
            timeFormat(acctTimeType, used_day, row, "used_day", carryRule);
            timeFormat(acctTimeType, (Float) row.get("remain_day"), row, "remain_day", carryRule);
            timeFormat(acctTimeType, (Float) row.get("now_quota"), row, "now_quota", carryRule);
            timeFormat(acctTimeType, curRemain, row, "cur_remain", carryRule);
            timeFormat(acctTimeType, (Float) row.get("remain_used_day"), row, "remain_used_day", carryRule);
            timeFormat(acctTimeType, (Float) row.get("deduction_day"), row, "deduction_day", carryRule);
            timeFormat(acctTimeType, adjustQuota.floatValue(), row, "adjust_quota", carryRule);
            timeFormat(acctTimeType, adjustUsedDay.floatValue(), row, "adjust_used_day", carryRule);
            timeFormat(acctTimeType, currentYearTotalQuota.floatValue(), row, "currentYearTotalQuota", carryRule);
            timeFormat(acctTimeType, usedTotalQuota.floatValue(), row, "usedTotalQuota", carryRule);
            timeFormat(acctTimeType, remainTotalQuota.floatValue(), row, "remainTotalQuota", carryRule);
            timeFormat(acctTimeType, inTransitQuota, row, "inTransitQuota", carryRule);
            //冻结配额
            timeFormat(acctTimeType, frozenDay.floatValue(), row, "frozen_day", carryRule);
        }
        return empQuotaList;
    }

    @Transactional
    @Override
    public AttendancePageResult getEmpCompensatoryQuotaList(CompensatoryQuotaSearchDto dto, UserInfo userInfo) {
        val tenantId = userInfo.getTenantId();
        val pageList = empCompensatoryQuotaDo.getEmpCompensatoryQuotaList(dto, tenantId);
        if (CollectionUtils.isNotEmpty(pageList)) {
            //0失效 、1 生效 、2 撤销 、8 冻结
            List<EmpCompensatoryQuotaVo> list = pageList.stream().map(quotaDo -> {
                val vo = new EmpCompensatoryQuotaVo();
                BeanUtils.copyProperties(quotaDo, vo);
                vo.setLeaveTypeName(LangParseUtil.getI18nLanguage(quotaDo.getI18nLeaveTypeName(), quotaDo.getLeaveTypeName()));
                var total = vo.getQuotaDay();
                var adjustQuotaDay = vo.getAdjustQuotaDay();
                if (null == total) {
                    total = 0f;
                }
                var inTransit = vo.getInTransitQuota();
                if (null == inTransit) {
                    inTransit = 0f;
                }
                var used = vo.getUsedDay();
                if (null == used) {
                    used = 0f;
                }
                vo.setLeftQuota(total + adjustQuotaDay - inTransit - used);
                Long curtime = System.currentTimeMillis() / 1000;
                if (vo.getStatus() == 2 && vo.getLastDate() >= curtime) {
                    vo.setStatus(1);
                } else if (vo.getStatus() == 9) {
                    vo.setStatus(2);
                } else if (vo.getStatus() == 8) {
                    vo.setStatus(8);
                } else {
                    vo.setStatus(0);
                }
                if (quotaDo.getQuotaUnit() == 2) {
                    val leftQuota = new BigDecimal(String.valueOf(vo.getLeftQuota())).divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN);
                    vo.setLeftQuota(leftQuota.floatValue());
                    if (null != quotaDo.getQuotaDay()) {
                        val time = new BigDecimal(String.valueOf(quotaDo.getQuotaDay())).divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN);
                        vo.setQuotaDay(time.floatValue());
                    }
                    if (null != quotaDo.getUsedDay()) {
                        val time = new BigDecimal(String.valueOf(quotaDo.getUsedDay())).divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN);
                        vo.setUsedDay(time.floatValue());
                    }
                    if (null != quotaDo.getInTransitQuota()) {
                        val time = new BigDecimal(String.valueOf(quotaDo.getInTransitQuota())).divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN);
                        vo.setInTransitQuota(time.floatValue());
                    }
                    if (null != quotaDo.getAdjustQuotaDay()) {
                        val time = new BigDecimal(String.valueOf(quotaDo.getAdjustQuotaDay())).divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN);
                        vo.setAdjustQuotaDay(time.floatValue());
                    }
                } else {
                    vo.setLeftQuota(BigDecimal.valueOf(vo.getLeftQuota()).setScale(4, RoundingMode.HALF_DOWN).floatValue());
                }
                if (null != quotaDo.getOvertimeUnit() && quotaDo.getOvertimeUnit() == 2 && null != quotaDo.getOvertimeDuration()) {
                    val time = new BigDecimal(String.valueOf(quotaDo.getOvertimeDuration())).divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN);
                    vo.setOvertimeDuration(time.floatValue());
                }
                vo.setDataSource(DataSourceEnum.getDescByName(quotaDo.getDataSource()));
                return vo;
            }).collect(Collectors.toList());
            return new AttendancePageResult<>(list, pageList.getPaginator().getPage(), dto.getPageSize(), pageList.getPaginator().getTotalCount());
        }
        return AttendancePageResult.empty();
    }

    /**
     * @param timeUnit
     * @param timeDuration
     * @param map
     * @param key
     * @param carryRule    进位规则
     */
    private void timeFormat(Integer timeUnit, Float timeDuration, Map map, String key, Integer carryRule) {
        if (timeDuration == null) {
            return;
        }
        try {
            NumberFormat nf = new DecimalFormat("#.##");
            if (timeUnit == 1) {
                map.put(key, nf.parse(nf.format(timeDuration)));
                return;
            }
            if (timeUnit == 2) {
                if (carryRule != null && carryRule == 1) {//四舍五入
                    BigDecimal v = new BigDecimal(timeDuration).divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN);
                    map.put(key, v.doubleValue());
                    return;
                } else {
                    BigDecimal b = new BigDecimal(timeDuration / 60);
                    map.put(key, nf.format(b));
                    return;
                }
            }
        } catch (Exception e) {
            log.error("timeFormat 转换异常，{}", e.getMessage(), e);
        }
        map.put(key, "");
    }

    private void handleParentalLeaveByRule(String belongOrgId, Long empId, Integer distributionCycle, Long disCycleStart, Long disCycleEnd,
                                           Integer childRule, Map<Long, List<EmpLeaveQuotaDto>> empQuotaRuleMap, EmpLeaveQuotaDto leaveQuotaDto,
                                           QuotaGenDto genDto, StringBuffer sql, boolean hireYearGreaterThanGenYear, Long hireDate) {
        List<EmpLeaveQuotaDto> quotaDtoList = new ArrayList<>();
        List<EmpFamily> list = empFamilyInfoDo.getEmpFamilyList(empId, belongOrgId, sql.toString());
        for (EmpFamily empFamily : list) {
            EmpLeaveQuotaDto dto = new EmpLeaveQuotaDto();
            BeanUtils.copyProperties(leaveQuotaDto, dto);
            Long birthday = empFamily.getBirthday();
            Long familyId = empFamily.getFamilyId();
            dto.setFamilyId(familyId);
            //计算发放周期
            Calendar cal = Calendar.getInstance();
            cal.setTimeInMillis(birthday * 1000);
            Calendar cal1 = Calendar.getInstance();
            cal1.set(genDto.getYear(), cal.get(Calendar.MONTH), cal.get(Calendar.DATE), 0, 0, 0);
            if (DistributionCycleEnum.CHILD_YEAR.getIndex().equals(distributionCycle)) {
                disCycleStart = cal1.getTime().getTime() / 1000;
                disCycleEnd = DateUtilExt.addMonthDate(disCycleStart * 1000, 12, -1);
            }
            dto.setDisCycleStart(disCycleStart);
            dto.setDisCycleEnd(disCycleEnd);
            if (LeaveJobTypeEnum.PARENTING.getIndex().equals(genDto.getType())) {
                log.info("xxJob request coming....");
                Long onlyDate = DateUtil.getOnlyDate();
                if (disCycleStart > onlyDate || disCycleEnd < onlyDate) {
                    continue;
                }
            }
            if (hireYearGreaterThanGenYear && !(disCycleStart <= hireDate && disCycleEnd >= hireDate)) {
                continue;
            }
            quotaDtoList.add(dto);
        }
        List<EmpLeaveQuotaDto> childListByRule = getChildListByRule(childRule, quotaDtoList);
        empQuotaRuleMap.put(empId, childListByRule);
    }

    private EmpLeaveQuotaDto getLeaveQuotaDto(String belongOrgId, Long disCycleStart, Long disCycleEnd, Integer acctTimeType, QuotaGenRuleDo ruleDo, LeaveQuotaConfigDo quotaConfigDo, Map empRow) {
        EmpLeaveQuotaDto leaveQuotaDto = new EmpLeaveQuotaDto();
        leaveQuotaDto.setConfigId(ruleDo.getConfigId());
        leaveQuotaDto.setEmpId((Long) empRow.get("empid"));
        leaveQuotaDto.setBelongOrgId(belongOrgId);
        leaveQuotaDto.setHireDate((Long) empRow.get("hireDate"));
        leaveQuotaDto.setProdeadLine((Long) empRow.get("prodeadLine"));
        leaveQuotaDto.setTerminationDate((Long) empRow.get("terminationDate"));
        leaveQuotaDto.setLeaveTypeId(quotaConfigDo.getLeaveTypeId());
        leaveQuotaDto.setAcctTimeType(acctTimeType);
        leaveQuotaDto.setQuotaRuleId(ruleDo.getQuotaRuleId());
        leaveQuotaDto.setQuotaVal(BigDecimal.valueOf(ruleDo.getQuotaVal()));
        leaveQuotaDto.setDisCycleStart(disCycleStart);
        leaveQuotaDto.setDisCycleEnd(disCycleEnd);
        leaveQuotaDto.setConvertRule(quotaConfigDo.getConvertRule());
        leaveQuotaDto.setOriginalQuotaDay(leaveQuotaDto.getQuotaVal());
        leaveQuotaDto.setFamilyId(-1L);
        if (quotaConfigDo.getIfAdvance() == null) {
            leaveQuotaDto.setIfAdvance(0);
        } else {
            leaveQuotaDto.setIfAdvance(quotaConfigDo.getIfAdvance());
        }
        leaveQuotaDto.setEmpStatus((Integer) empRow.get("empStatus"));
        return leaveQuotaDto;
    }

    private long getCycleStart(Short year, Long hireDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(hireDate * 1000);
        Calendar cal1 = Calendar.getInstance();
        cal1.set(year, cal.get(Calendar.MONTH), cal.get(Calendar.DATE), 0, 0, 0);
        return cal1.getTime().getTime() / 1000;
    }

    /**
     * 根据工龄和司龄的额度生成规则计算员工假期配额
     *
     * @param groupExp          员工分组条件（sql 表达式）
     * @param leaveTypeDoMap    假期规则信息
     * @param quotaConfigDo     额度规则信息
     * @param genDto            配额生成参数
     * @param distributionCycle 发放周期：1 自然年、2 入职年、3 自定义周期
     * @param disCycleStart     发放周期开始日
     * @param disCycleEnd       发放周期结束日
     * @param empQuotaRuleMap   员工假期配额数据（用于存储配额数据）
     * @param ruleDo            配额生成规则
     * @param empIds            员工ID集合
     * @param midDate           配额计算时使用的最小日期，默认入职日期，如果配额计算规则时采用工龄计算的话，取员工首次参工日期，如果计算规则采用司龄计算的话，取员工入职日期，如果计算规则既有工龄又有司龄，取员工首次参公日期
     * @param firstDate         员工入职日期
     */
    private void calQuotaByJobAgeAndCorpAgeRule(String groupExp, Map<Integer, WaLeaveTypeDo> leaveTypeDoMap,
                                                LeaveQuotaConfigDo quotaConfigDo, QuotaGenDto genDto,
                                                Integer distributionCycle, Long disCycleStart, Long disCycleEnd,
                                                Map<Long, List<EmpLeaveQuotaDto>> empQuotaRuleMap,
                                                QuotaGenRuleDo ruleDo, List<Long> empIds, String midDate,
                                                String firstDate, StringBuffer sql, String dateStr, String conditionExp) {
        boolean homeLeave = false;
        boolean visitParents = false;
        boolean visitSpouse = false;
        boolean marriedHomeLeave = false;
        boolean notMarriedHomeLeave = false;
        if (groupExp.contains("visiting_reason")) {
            homeLeave = true;
            visitParents = groupExp.contains("visiting_reason='visiting_parents'") ||
                    groupExp.contains("visiting_reason<>'visiting_spouse'");
            visitSpouse = groupExp.contains("visiting_reason<>'visiting_parents'") ||
                    groupExp.contains("visiting_reason='visiting_spouse'");
            marriedHomeLeave = groupExp.contains("marriage='1'") ||
                    groupExp.replaceAll("marriage<>'1'", "").contains("marriage<>");
            notMarriedHomeLeave = groupExp.contains("marriage<>'1'") ||
                    groupExp.replaceAll("marriage='1'", "").contains("marriage=");

        }
        if (homeLeave) {
            groupExp = groupExp.replaceAll("visiting_reason='visiting_parents'", "1=1")
                    .replaceAll("visiting_reason<>'visiting_parents'", "1=1")
                    .replaceAll("visiting_reason='visiting_spouse'", "1=1")
                    .replaceAll("visiting_reason<>'visiting_spouse'", "1=1")
                    .replaceAll("marriage='\\d+'", "1=1")
                    .replaceAll("marriage<>'\\d+'", "1=1");
        }
        //查询符合条件的员工数据
        groupExp = replaceGroupExp(groupExp);
        Long gender = null;
        Integer leaveTypeId = quotaConfigDo.getLeaveTypeId();
        Long genderType = leaveTypeDoMap.get(leaveTypeId).getGenderType();
        Integer quotaType = leaveTypeDoMap.get(leaveTypeId).getQuotaType();
        if (genderType != null && genderType != 0) {
            gender = genderType;
        }
        if (groupExp.contains("parental_leave")) {
            if (QuotaTypeEnum.PARENTAL_LEAVE.getIndex().equals(quotaType)) {
                groupExp = "";
            } else {
                return;
            }
        }
        String belongOrgId = genDto.getBelongId();
        List<Map> quotaRuleEmpList = sysEmpInfo.getQuotaRuleEmpList(belongOrgId, groupExp, midDate, firstDate, leaveTypeId, null, gender, dateStr);
        quotaRuleEmpList = quotaRuleEmpList.stream().filter(r -> empIds.contains(Long.valueOf(r.get("empid").toString()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(quotaRuleEmpList)) {
            return;
        }
        //遍历员工，计算员工配额
        for (Map empRow : quotaRuleEmpList) {
            Long empId = (Long) empRow.get("empid");
            Long hireDate = (Long) empRow.get("hireDate");
            Short hireYear = Short.valueOf(DateUtil.convertDateTimeToStr(hireDate, "yyyy", true));
            boolean hireYearGreaterThanGenYear = hireYear > genDto.getYear();
            //发放周期是入职年
            if (DistributionCycleEnum.ENTRY_YEAR.getIndex().equals(distributionCycle) && hireDate != null) {
                disCycleStart = getCycleStart(genDto.getYear(), hireDate);
                disCycleEnd = DateUtilExt.addMonthDate(disCycleStart * 1000, 12, -1);
            }
            Integer acctTimeType = leaveTypeDoMap.get(leaveTypeId).getAcctTimeType();
            EmpLeaveQuotaDto leaveQuotaDto = getLeaveQuotaDto(belongOrgId, disCycleStart, disCycleEnd, acctTimeType, ruleDo, quotaConfigDo, empRow);
            if (homeLeave) {
                if (visitParents && visitSpouse) {
                    if (marriedHomeLeave && !notMarriedHomeLeave) {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.MARRIED_VISIT_BOTH);
                    } else {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.ILLEGAL);
                    }
                } else if (visitParents) {
                    if (marriedHomeLeave && notMarriedHomeLeave) {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.MARRIED_OR_NOT_VISIT_PARENTS);
                    } else if (marriedHomeLeave) {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.MARRIED_VISIT_PARENTS);
                    } else if (notMarriedHomeLeave) {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.NOT_MARRIED_VISIT_PARENTS);
                    } else {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.ILLEGAL);
                    }
                } else if (visitSpouse) {
                    if (marriedHomeLeave && !notMarriedHomeLeave) {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.MARRIED_VISIT_SPOUSE);
                    }
                } else {
                    leaveQuotaDto.setHomeLeaveType(HomeLeaveType.ILLEGAL);
                }
            }
            if (QuotaTypeEnum.PARENTAL_LEAVE.getIndex().equals(quotaType)) {
                //处理育儿假
                handleParentalLeaveByRule(belongOrgId, empId, distributionCycle, disCycleStart, disCycleEnd, quotaConfigDo.getChildRule(), empQuotaRuleMap, leaveQuotaDto, genDto, sql, hireYearGreaterThanGenYear, hireDate);
            } else {
                if (hireYearGreaterThanGenYear) {
                    continue;
                }
                Long midDateValue = (Long) empRow.get("midDate");//值的含义和入参的midDate一致
                Long firstDateValue = (Long) empRow.get("firstDate");//值的含义和入参的firstDate一致，目前代表入职日期
                if (midDateValue == null) {
                    midDateValue = firstDateValue;
                }
                Map<String, Object> crossParams = new HashMap<>();
                crossParams.put("midDate", midDateValue);
                crossParams.put("firstDate", firstDateValue);
                crossParams.put("year", genDto.getYear());
                crossParams.put("conditionExp", conditionExp);
                leaveQuotaDto.setCrossParams(crossParams);
                if (null != empRow.get("criticalPoint")) {
                    leaveQuotaDto.setCrossQuotaDate(Long.valueOf(empRow.get("criticalPoint").toString()));
                }
                List<EmpLeaveQuotaDto> quotaDtoList;
                if (!empQuotaRuleMap.containsKey(empId)) {
                    quotaDtoList = new ArrayList<>();
                    quotaDtoList.add(leaveQuotaDto);
                    empQuotaRuleMap.put(empId, quotaDtoList);
                } else {
                    List<EmpLeaveQuotaDto> list = empQuotaRuleMap.get(empId);
                    List<EmpLeaveQuotaDto> filterList = list.stream().filter(r -> r.getLeaveTypeId().equals(leaveTypeId)).sorted(Comparator.comparing(EmpLeaveQuotaDto::getCrossQuotaDate)).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(filterList)) {
                        quotaDtoList = new ArrayList<>();
                        quotaDtoList.add(leaveQuotaDto);
                        empQuotaRuleMap.put(empId, quotaDtoList);
                    } else if (empRow.get("firstDate") != null) {
                        Optional<EmpLeaveQuotaDto> optional = filterList.stream().max(Comparator.comparing(EmpLeaveQuotaDto::getCrossQuotaDate));
                        if (optional.isPresent()) {
                            EmpLeaveQuotaDto maxEmpLeaveQuotaDto = optional.get();
                            if (maxEmpLeaveQuotaDto.getQuotaVal().intValue() != ruleDo.getQuotaVal()) {
                                empQuotaRuleMap.get(empId).add(leaveQuotaDto);
                            } else if (filterList.size() > 1 && !leaveQuotaDto.getCrossQuotaDate().equals(disCycleEnd)) {
                                maxEmpLeaveQuotaDto.setCrossQuotaDate(leaveQuotaDto.getCrossQuotaDate());
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据额度生成规则计算员工假期配额
     *
     * @param groupExp          员工分组条件（sql 表达式）
     * @param leaveTypeDoMap    假期规则信息
     * @param quotaConfigDo     额度规则信息
     * @param genDto            配额生成参数
     * @param distributionCycle 发放周期：1 自然年、2 入职年、3 自定义周期
     * @param disCycleStart     发放周期开始日
     * @param disCycleEnd       发放周期结束日
     * @param empQuotaRuleMap   员工假期配额数据（用于存储配额数据）
     * @param ruleDo            配额生成规则
     * @param empIds            员工ID集合
     * @param midDate           配额计算时使用的最小日期，默认入职日期，如果配额计算规则时采用工龄计算的话，取员工首次参工日期，如果计算规则采用司龄计算的话，取员工入职日期，如果计算规则既有工龄又有司龄，取员工首次参公日期
     * @param firstDate         员工入职日期
     */
    private void calQuotaByRule(String groupExp, Map<Integer, WaLeaveTypeDo> leaveTypeDoMap, LeaveQuotaConfigDo quotaConfigDo, QuotaGenDto genDto,
                                Integer distributionCycle, Long disCycleStart, Long disCycleEnd, Map<Long, List<EmpLeaveQuotaDto>> empQuotaRuleMap,
                                QuotaGenRuleDo ruleDo, List<Long> empIds, String midDate, String firstDate, StringBuffer sql) {
        //查询符合条件的员工数据
        groupExp = replaceGroupExp(groupExp);
        Long gender = null;
        Long genderType = leaveTypeDoMap.get(quotaConfigDo.getLeaveTypeId()).getGenderType();
        Integer quotaType = leaveTypeDoMap.get(quotaConfigDo.getLeaveTypeId()).getQuotaType();
        if (genderType != null && genderType != 0) {
            gender = genderType;
        }
        if (groupExp.contains("parental_leave")) {
            if (QuotaTypeEnum.PARENTAL_LEAVE.getIndex().equals(quotaType)) {
                groupExp = "";
            } else {
                return;
            }
        }
        String belongOrgId = genDto.getBelongId();
        boolean homeLeave = false;
        boolean visitParents = false;
        boolean visitSpouse = false;
        boolean marriedHomeLeave = false;
        boolean notMarriedHomeLeave = false;
        if (groupExp.contains("visiting_reason")) {
            homeLeave = true;
            visitParents = groupExp.contains("visiting_reason='visiting_parents'") ||
                    groupExp.contains("visiting_reason<>'visiting_spouse'");
            visitSpouse = groupExp.contains("visiting_reason<>'visiting_parents'") ||
                    groupExp.contains("visiting_reason='visiting_spouse'");
            marriedHomeLeave = groupExp.contains("marriage='1'") ||
                    groupExp.replaceAll("marriage<>'1'", "").contains("marriage<>");
            notMarriedHomeLeave = groupExp.contains("marriage<>'1'") ||
                    groupExp.replaceAll("marriage='1'", "").contains("marriage=");

        }
        if (homeLeave) {
            groupExp = groupExp.replaceAll("visiting_reason='visiting_parents'", "1=1")
                    .replaceAll("visiting_reason<>'visiting_parents'", "1=1")
                    .replaceAll("visiting_reason='visiting_spouse'", "1=1")
                    .replaceAll("visiting_reason<>'visiting_spouse'", "1=1")
                    .replaceAll("marriage='\\d+'", "1=1")
                    .replaceAll("marriage<>'\\d+'", "1=1");
        }
        List<Map> quotaRuleEmpList = sysEmpInfo.getQuotaRuleEmpList(belongOrgId,
                groupExp, midDate, firstDate, quotaConfigDo.getLeaveTypeId(), null, gender, null);
        quotaRuleEmpList = quotaRuleEmpList.stream().filter(r -> empIds.contains(Long.valueOf(r.get("empid").toString()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(quotaRuleEmpList)) {
            return;
        }
        //遍历员工，计算员工配额
        for (Map empRow : quotaRuleEmpList) {
            Long empId = (Long) empRow.get("empid");
            Long hireDate = (Long) empRow.get("hireDate");
            Short hireYear = Short.valueOf(DateUtil.convertDateTimeToStr(hireDate, "yyyy", true));
            boolean hireYearGreaterThanGenYear = hireYear > genDto.getYear();
            //发放周期是入职年
            if (DistributionCycleEnum.ENTRY_YEAR.getIndex().equals(distributionCycle) && hireDate != null) {
                disCycleStart = getCycleStart(genDto.getYear(), hireDate);
                disCycleEnd = DateUtilExt.addMonthDate(disCycleStart * 1000, 12, -1);
            }
            Integer acctTimeType = leaveTypeDoMap.get(quotaConfigDo.getLeaveTypeId()).getAcctTimeType();
            EmpLeaveQuotaDto leaveQuotaDto = getLeaveQuotaDto(belongOrgId, disCycleStart, disCycleEnd, acctTimeType, ruleDo, quotaConfigDo, empRow);
            if (homeLeave) {
                if (visitParents && visitSpouse) {
                    if (marriedHomeLeave && !notMarriedHomeLeave) {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.MARRIED_VISIT_BOTH);
                    } else {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.ILLEGAL);
                    }
                } else if (visitParents) {
                    if (marriedHomeLeave && notMarriedHomeLeave) {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.MARRIED_OR_NOT_VISIT_PARENTS);
                    } else if (marriedHomeLeave) {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.MARRIED_VISIT_PARENTS);
                    } else if (notMarriedHomeLeave) {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.NOT_MARRIED_VISIT_PARENTS);
                    } else {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.ILLEGAL);
                    }
                } else if (visitSpouse) {
                    if (marriedHomeLeave && !notMarriedHomeLeave) {
                        leaveQuotaDto.setHomeLeaveType(HomeLeaveType.MARRIED_VISIT_SPOUSE);
                    }
                } else {
                    leaveQuotaDto.setHomeLeaveType(HomeLeaveType.ILLEGAL);
                }
            }
            if (QuotaTypeEnum.PARENTAL_LEAVE.getIndex().equals(quotaType)) {
                //处理育儿假
                handleParentalLeaveByRule(belongOrgId, empId, distributionCycle, disCycleStart, disCycleEnd, quotaConfigDo.getChildRule(), empQuotaRuleMap, leaveQuotaDto, genDto, sql, hireYearGreaterThanGenYear, hireDate);
            } else {
                if (hireYearGreaterThanGenYear) {
                    continue;
                }
                List<EmpLeaveQuotaDto> quotaDtoList;
                if (!empQuotaRuleMap.containsKey(empId)) {
                    quotaDtoList = new ArrayList<>();
                    quotaDtoList.add(leaveQuotaDto);
                    empQuotaRuleMap.put(empId, quotaDtoList);
                } else {
                    List<EmpLeaveQuotaDto> list = empQuotaRuleMap.get(empId);
                    List<EmpLeaveQuotaDto> list1 = list.stream().filter(r -> r.getLeaveTypeId().equals(quotaConfigDo.getLeaveTypeId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(list1)) {
                        quotaDtoList = new ArrayList<>();
                        quotaDtoList.add(leaveQuotaDto);
                        empQuotaRuleMap.put(empId, quotaDtoList);
                    } else if (empRow.get("firstDate") != null) {
                        // empRow.get("firstDate") 值的含义和入参的firstDate一致，目前代表入职日期
                        Optional<EmpLeaveQuotaDto> optional = list1.stream().filter(r -> r.getQuotaVal().intValue() != ruleDo.getQuotaVal()).findFirst();
                        if (optional.isPresent()) {
                            //配额跨维度计算
                            EmpLeaveQuotaDto quotaDto = optional.get();
                            Long midDateValue = (Long) empRow.get("midDate");//值的含义和入参的midDate一致
                            Long firstDateValue = (Long) empRow.get("firstDate");//值的含义和入参的firstDate一致，目前代表入职日期
                            crossQuotaHandle(genDto.getYear(), firstDateValue, midDateValue, disCycleStart, disCycleEnd, acctTimeType, quotaDto, leaveQuotaDto, quotaConfigDo);
                        }
                    }
                }
            }
        }
    }

    /**
     * 不规则生成育儿假
     *
     * @param childRule
     * @param list
     * @return
     */
    public List<EmpLeaveQuotaDto> getChildListByRule(Integer childRule, List<EmpLeaveQuotaDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        //按子女个数生成
        if (ChildRuleEnum.CHILD_COUNT.getIndex().equals(childRule)) {
            return list;
        }
        //按子女个数累加
        if (ChildRuleEnum.CHILD_SUM.getIndex().equals(childRule)) {
            List<EmpLeaveQuotaDto> quotaDtoList = new ArrayList<>();
            EmpLeaveQuotaDto dto = list.get(0);
            dto.setOriginalQuotaDay(dto.getQuotaVal().multiply(new BigDecimal(list.size())));
            dto.setQuotaVal(dto.getQuotaVal().multiply(new BigDecimal(list.size())));
            quotaDtoList.add(dto);
            return quotaDtoList;
        }
        //仅生成一条
        if (ChildRuleEnum.CHILD_ONE.getIndex().equals(childRule)) {
            List<EmpLeaveQuotaDto> quotaDtoList = new ArrayList<>();
            EmpLeaveQuotaDto dto = list.get(0);
            quotaDtoList.add(dto);
            return quotaDtoList;
        }
        return list;
    }

    private String getSqlExp(String belongOrgId, String code) {
        // 工龄
        if ("job_age".equals(code)) {
            String jobAgeSql = waConfigService.getFuncExp(belongOrgId, "job_age");
            if (StringUtils.isBlank(jobAgeSql)) {
                jobAgeSql = "CASE WHEN first_work_date IS NULL THEN COALESCE(ep.service_years,0) + EXTRACT(YEAR FROM age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))+CAST(EXTRACT(MONTH FROM age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/12 AS DECIMAL(10,1))+CAST(EXTRACT(DAY FROM age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/365 AS DECIMAL(10,3)) ELSE EXTRACT(YEAR FROM age(to_timestamp(#{nowTime}),to_timestamp(first_work_date)))+CAST(EXTRACT(MONTH FROM age(to_timestamp(#{nowTime}),to_timestamp(first_work_date)))/12 AS DECIMAL(10,1))+CAST(EXTRACT(DAY FROM age(to_timestamp(#{nowTime}),to_timestamp(first_work_date)))/365 AS DECIMAL(10,3)) END";
            }
            return jobAgeSql;
        }
        // 司龄
        if ("corp_age".equals(code)) {
            String corpAgeSql = waConfigService.getFuncExp(belongOrgId, "corp_age");
            if (StringUtils.isBlank(corpAgeSql)) {
                corpAgeSql = "extract(year from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))+cast(extract(month from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/12 as decimal(10,1))+cast(extract(day from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/365 as decimal(10,3))";
            }
            return corpAgeSql;
        }
        // 司龄（至年底）
        if (QUOTA_GEN_CON_OF_ENDYEARCORPAGE.equals(code)) {
            String corpYearEndAgeSql = waConfigService.getFuncExp(belongOrgId, QUOTA_GEN_CON_OF_ENDYEARCORPAGE);
            if (StringUtils.isBlank(corpYearEndAgeSql)) {
                corpYearEndAgeSql = "extract(year from age(to_timestamp(#{yearEndTime}),to_timestamp(hire_date)))+cast(extract(month from age(to_timestamp(#{yearEndTime}),to_timestamp(hire_date)))/12 as decimal(10,1))+cast(extract(day from age(to_timestamp(#{yearEndTime}),to_timestamp(hire_date)))/365 as decimal(10,3))";
            }
            return corpYearEndAgeSql;
        }
        return "";
    }

    private boolean checkCorpAgeCrossRange(String firstConditionExp, String secConditionExp) {
        if (firstConditionExp.contains("job_age") && firstConditionExp.contains("corp_age") && secConditionExp.contains("job_age") && secConditionExp.contains("corp_age")) {
            List<String> firstCorpAgeList = getFilterList(firstConditionExp, "corp_age");
            List<String> firstJobAgeList = getFilterList(firstConditionExp, "job_age");
            List<String> secCorpAgeList = getFilterList(secConditionExp, "corp_age");
            List<String> secJobAgeList = getFilterList(secConditionExp, "job_age");
            firstJobAgeList.removeAll(secJobAgeList);
            if (CollectionUtils.isEmpty(firstJobAgeList)) {
                if (firstCorpAgeList.size() >= secCorpAgeList.size()) {
                    firstCorpAgeList.removeAll(secCorpAgeList);
                    return CollectionUtils.isNotEmpty(firstCorpAgeList);
                } else {
                    secCorpAgeList.removeAll(firstCorpAgeList);
                    return CollectionUtils.isNotEmpty(secCorpAgeList);
                }
            }
        }
        return false;
    }

    private List<String> getFilterList(String conditionExp, String conditionField) {
        String[] firstConditionArr = conditionExp.split("AND|OR");
        return Arrays.stream(firstConditionArr).filter(c -> c.contains(conditionField))
                .map(String::trim).map(e -> e.replaceAll("\\(|\\)", ""))
                .collect(Collectors.toList());
    }

    /**
     * 生成按照年份发放的假期配额
     *
     * @param genDto
     * @throws Exception
     */
    @Transactional
    @Override
    public void genEmpQuotaForIssuedAnnually(QuotaGenDto genDto) throws Exception {
        //根据考勤分组查询本次需要计算的员工数据
        Long curDate = DateUtil.getOnlyDate();
        List<Long> empIds = waEmpQuotaDo.getGroupEmpList(genDto.getWaGroupId(), genDto.getYear(), curDate, genDto.getDataScope());
        if (CollectionUtils.isEmpty(empIds)) {
            log.info("genEmpQuotaForIssuedAnnually getGroupEmpList empty");
            return;
        }
        //检查所选择的员工是否属于这个考勤分组下的
        if (genDto.getEmpid() != null) {
            if (!empIds.contains(genDto.getEmpid())) {
                log.info("genEmpQuotaForIssuedAnnually emp not in waGroup");
                return;
            } else {
                empIds.removeIf(k -> !genDto.getEmpid().equals(k));
            }
        }
        //查询考勤分组下需要生成配额的假期类型
        WaGroup waGroup = waGroupMapper.selectByPrimaryKey(genDto.getWaGroupId());
        if (waGroup == null || waGroup.getLeaveTypeIds() == null) {
            log.info("genEmpQuotaForIssuedAnnually getWaGroup empty");
            return;
        }
        List<WaLeaveTypeDo> leaveTypes = waLeaveTypeDo.getLeaveTypeByGroupId(genDto.getBelongId(), genDto.getWaGroupId());
        if (CollectionUtils.isEmpty(leaveTypes)) {
            log.info("genEmpQuotaForIssuedAnnually getLeaveTypeByGroupId empty");
            return;
        }
        //只计算限额并且按年发放的假期额度
        if (LeaveJobTypeEnum.ANNUAL.getIndex().equals(genDto.getType())) {
            leaveTypes = leaveTypes.stream().filter(r -> QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(r.getQuotaRestrictionType())
                    && QuotaTypeEnum.ISSUED_ANNUALLY.getIndex().equals(r.getQuotaType())).collect(Collectors.toList());
        } else if (LeaveJobTypeEnum.PARENTING.getIndex().equals(genDto.getType())) {
            leaveTypes = leaveTypes.stream().filter(r -> QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(r.getQuotaRestrictionType())
                    && QuotaTypeEnum.PARENTAL_LEAVE.getIndex().equals(r.getQuotaType())).collect(Collectors.toList());
        } else {
            leaveTypes = leaveTypes.stream().filter(r -> QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(r.getQuotaRestrictionType())
                    && (QuotaTypeEnum.ISSUED_ANNUALLY.getIndex().equals(r.getQuotaType())
                    || QuotaTypeEnum.PARENTAL_LEAVE.getIndex().equals(r.getQuotaType()))).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(leaveTypes)) {
            log.info("genEmpQuotaForIssuedAnnually 只计算限额并且按年发放的假期额度 empty");
            return;
        }
        Map<Integer, WaLeaveTypeDo> leaveTypeDoMap = leaveTypes.stream().collect(Collectors.toMap(WaLeaveTypeDo::getLeaveTypeId, Function.identity()));
        val leaveDefList = leaveTypeDefService.getWaLeaveTypeDefList(genDto.getBelongId());
        List<Integer> leaveTypeIds = leaveTypes.stream().map(WaLeaveTypeDo::getLeaveTypeId).collect(Collectors.toList());
        //兼容quota_setting_id 字段值，后面上线稳定后移除此字段
        Map<Integer, Integer> leaveSettingMap = new HashMap<>();
        List<WaLeaveSettingDo> leaveSettingDos = waLeaveSettingDo.getLeaveSettingList(genDto.getBelongId(), leaveTypeIds);
        if (CollectionUtils.isNotEmpty(leaveSettingDos)) {
            leaveSettingMap = leaveSettingDos.stream().collect(Collectors.toMap(WaLeaveSettingDo::getLeaveTypeId, WaLeaveSettingDo::getQuotaSettingId));
        }
        //查询配额配置信息
        List<LeaveQuotaConfigDo> configDoList = leaveQuotaConfigDo.getConfigListByIds(genDto.getBelongId(), leaveTypeIds);
        if (CollectionUtils.isEmpty(configDoList)) {
            log.info("genEmpQuotaForIssuedAnnually 查询配额配置信息 empty");
            return;
        }
        //查询额度规则
        List<Long> ruleIds = configDoList.stream().map(LeaveQuotaConfigDo::getConfigId).collect(Collectors.toList());
        List<QuotaGenRuleDo> genRuleDoList = quotaGenRuleDo.getListByConfigIds(genDto.getBelongId(), ruleIds);
        if (CollectionUtils.isEmpty(genRuleDoList)) {
            log.info("genEmpQuotaForIssuedAnnually 查询额度规则 empty");
            return;
        }
        Map<Long, List<QuotaGenRuleDo>> quotaGenRuleMap = genRuleDoList.stream().collect(Collectors.groupingBy(QuotaGenRuleDo::getConfigId));
        //过滤掉没有生成规则的假期配置
        configDoList = configDoList.stream().filter(r -> quotaGenRuleMap.containsKey(r.getConfigId())).collect(Collectors.toList());

        //查询工龄、司龄、司龄（至年底）计算逻辑
        String jobAgeSql = getSqlExp(genDto.getBelongId(), "job_age");
        String corpAgeSql = getSqlExp(genDto.getBelongId(), "corp_age");
        String corpYearEndAgeSql = getSqlExp(genDto.getBelongId(), QUOTA_GEN_CON_OF_ENDYEARCORPAGE);

        Short year = genDto.getYear();
        List<com.caidaocloud.attendance.service.application.dto.WaEmpQuota> empQuotaAddList = new ArrayList<>();
        List<com.caidaocloud.attendance.service.application.dto.WaEmpQuota> empQuotaUpdList = new ArrayList<>();

        //查询已生成的本年配额
        WaEmpQuotaExample empQuotaExample = new WaEmpQuotaExample();
        empQuotaExample.createCriteria().andPeriodYearEqualTo(year).andEmpidIn(empIds).andLeaveTypeIdIn(leaveTypeIds);
        List<WaEmpQuota> oldQuotaList = waEmpQuotaMapper.selectByExample(empQuotaExample);
        if (!GenQuotaMode.NOT_GENERATED.getIndex().equals(genDto.getGenQuotaMode())) {
            oldQuotaList = oldQuotaList.stream().filter(quota -> quota.getValidityDuration() != null).collect(Collectors.toList());
        }
        Map<String, WaEmpQuota> oldEmpQuotaMap = oldQuotaList.stream().collect(Collectors.toMap(r -> r.getEmpid() + "_" + r.getConfigId() + "_" + r.getPeriodYear() + "_" + r.getFamilyId(), Function.identity(), (v1, v2) -> v1));
        groovyScriptEngine.clearCache();

        Map<Integer, Integer> finalLeaveSettingMap = leaveSettingMap;

        //查询适用员工表达式
        List<String> businessKeys = configDoList.stream().map(c -> c.getConfigId().toString()).collect(Collectors.toList());
        Map<Long, EmployeeGroupDto> leaveQuotaConfigMap = leaveTypeService.getGroupRuleMap(genDto.getBelongId(), businessKeys);

        // 查询额度规则状态信息
        val configStatus = leaveQuotaConfigDo.loadStatus(configDoList.stream().map(LeaveQuotaConfigDo::getConfigId)
                .collect(Collectors.toList())).stream().collect(Collectors.toMap(
                it -> Long.valueOf(String.valueOf(it.get("config_id"))),
                it -> (String) it.get("status")));

        //遍历假期类型，根据规则生成不同假期类型的配额
        for (LeaveQuotaConfigDo quotaConfigDo : configDoList) {
            if ("DISABLED".equals(configStatus.get(quotaConfigDo.getConfigId()))) {
                continue;
            }
            val leaveType = leaveTypeDoMap.get(quotaConfigDo.getLeaveTypeId());
            val leaveDefCode = leaveDefList.stream().filter(leaveDef ->
                    leaveType.getLeaveType().equals(leaveDef.getLeaveTypeDefId())
            ).findFirst().orElse(null);

            // 根据假期额度规则设置的适用员工条件查询员工数据
            List<Long> applicableEmpList = new ArrayList<>();
            if (leaveQuotaConfigMap.containsKey(quotaConfigDo.getConfigId())) {
                EmployeeGroupDto groupDto = leaveQuotaConfigMap.get(quotaConfigDo.getConfigId());
                if (StringUtil.isNotBlank(groupDto.getGroupExp())) {
                    applicableEmpList = sysEmpInfo.getEmpIdsByGroupExp(genDto.getCorpId(), groupDto.getGroupExp(), quotaConfigDo.getGroupExpCondition());
                    if (CollectionUtils.isEmpty(applicableEmpList)) {
                        continue;
                    }
                    applicableEmpList.retainAll(empIds);
                    if (CollectionUtils.isEmpty(applicableEmpList)) {
                        continue;
                    }
                }
            }

            //发放周期：1 自然年、2 入职年、3 自定义周期
            Integer distributionCycle = quotaConfigDo.getDistributionCycle();
            //发放周期开始日
            Long disCycleStart = null;
            //发放周期结束日
            Long disCycleEnd = null;
            if (distributionCycle.equals(DistributionCycleEnum.NATURAL_YEAR.getIndex())) {
                //自然年
                Calendar calendar = Calendar.getInstance();
                calendar.set(year, 0, 1, 0, 0, 0);
                disCycleStart = calendar.getTime().getTime() / 1000;
                disCycleEnd = DateUtilExt.getYearsEndTime(disCycleStart);
            } else if (distributionCycle.equals(DistributionCycleEnum.CUSTOM_CYCLE.getIndex())) {
                //自定义周期
                Calendar startCal = Calendar.getInstance();
                startCal.setTimeInMillis(quotaConfigDo.getDisCycleStart() * 1000);
                startCal.set(Calendar.YEAR, year);
                disCycleStart = startCal.getTime().getTime() / 1000;
                disCycleEnd = DateUtilExt.addMonthDate(disCycleStart * 1000, 1, 0, -1);
            }
            Long yearEndTime = DateUtilExt.getYearEndTimeByYear(Integer.valueOf(year));

            // 员工假期配额map key=empId,value=员工配额数据
            Map<Long, List<EmpLeaveQuotaDto>> empQuotaRuleMap = new HashMap<>();

            // 配额规则
            List<QuotaGenRuleDo> ruleDoList = quotaGenRuleMap.get(quotaConfigDo.getConfigId());
            ruleDoList = ruleDoList.stream().filter(row -> StringUtils.isNotBlank(row.getConditionExp())).collect(Collectors.toList());
            for (QuotaGenRuleDo ruleDo : ruleDoList) {
                //条件表达式
                String conditionExp = ruleDo.getConditionExp();
                //条件中是否含有工龄
                boolean isJobAge = conditionExp.contains("job_age");
                //条件中是否含有司龄
                boolean isCorpAge = conditionExp.contains("corp_age");
                //条件中是否有累计工作月份
                boolean isContinuousWorking = conditionExp.contains("total_working_month");
                //条件中是否有司龄（至年底）
                boolean isCorpYearEndAge = conditionExp.contains(QUOTA_GEN_CON_OF_ENDYEARCORPAGE);
                //配额计算时使用的最小日期，默认入职日期，如果配额计算规则时采用工龄计算的话，取员工首次参工日期，如果计算规则采用司龄计算的话，取员工入职日期，如果计算规则既有工龄又有司龄，取员工首次参公日期
                String midDate = "hire_date";
                if (isJobAge) {
                    midDate = "first_work_date";
                } else if (isCorpAge) {
                    midDate = "hire_date";
                }
                //CAIDAOM-3319科锐需求，福利起始日期
                boolean isFirstHireDate = StringUtil.isNotBlank(corpAgeSql) && corpAgeSql.contains("first_hire_date") && midDate.equals("hire_date");
                // 规则条件表达式
                String execSql = getExecSql(conditionExp, jobAgeSql, corpAgeSql, corpYearEndAgeSql);
                StringBuffer sf = new StringBuffer();
                execSql = dealGroupExp(execSql, sf, year, distributionCycle, disCycleEnd);
                StringBuilder sb = new StringBuilder();
                sb.append("and (").append(execSql).append(")");
                String conditionSql = sb.toString();
                if (isCorpYearEndAge) {
                    conditionSql = conditionSql.replaceAll("#\\{yearEndTime\\}", yearEndTime.toString());
                }

                //根据配额生成规则计算员工配额
                String firstDate = "hire_date";
                if (isJobAge || isCorpAge) {
                    if (distributionCycle.equals(DistributionCycleEnum.NATURAL_YEAR.getIndex()) || distributionCycle.equals(DistributionCycleEnum.CUSTOM_CYCLE.getIndex())) {
                        //自然年、自定义周期
                        List<String> dateStrList = new ArrayList<>();
                        dateStrList.add(String.valueOf(disCycleStart));
                        //特殊处理，刚好在临界点，额度并未跨档，按照加一天处理
                        String firstHireDate = "datestr_to_ymdtimestamp('" + year + "' || to_char(to_timestamp(hire_date), 'MMdd'))+86400";
                        if (isFirstHireDate) {
                            firstHireDate = "datestr_to_ymdtimestamp('" + year + "' || to_char(to_timestamp(coalesce(first_hire_date,hire_date)), 'MMdd'))+86400";
                        }
                        dateStrList.add(firstHireDate);
                        if (!"hire_date".equals(midDate)) {
                            dateStrList.add("datestr_to_ymdtimestamp('" + year + "' || to_char(to_timestamp(" + midDate + "), 'MMdd'))+86400");
                        }
                        dateStrList.add(String.valueOf(disCycleEnd));
                        if (isFirstHireDate) {
                            midDate = "coalesce(first_hire_date,hire_date)";
                        }
                        for (String dateStr : dateStrList) {
                            String groupExp = conditionSql.replaceAll("#\\{nowTime\\}", dateStr).replaceAll("#nowTime#", dateStr).replaceAll("#\\{nowhireDate\\}", dateStr).replaceAll("#nowhireDate#", dateStr);
                            calQuotaByJobAgeAndCorpAgeRule(groupExp, leaveTypeDoMap, quotaConfigDo, genDto, distributionCycle, disCycleStart, disCycleEnd, empQuotaRuleMap, ruleDo, applicableEmpList, midDate, firstDate, sf, dateStr, conditionExp);
                        }
                    } else {
                        //按照入职年
                        //当年入职日期
                        String nowTimeStr1 = "datestr_to_ymdtimestamp('" + year + "' || to_char(to_timestamp(hire_date), 'MMdd'))";
                        if (isFirstHireDate) {
                            nowTimeStr1 = "datestr_to_ymdtimestamp('" + year + "' || to_char(to_timestamp(coalesce(first_hire_date,hire_date)), 'MMdd'))";
                            midDate = "coalesce(first_hire_date,hire_date)";
                        }
                        //当年入职日期+1年-1天
                        String nowTimeStr2 = "to_char(to_timestamp(" + nowTimeStr1 + ") + INTERVAL '1 year' - INTERVAL '1 d', 'yyyyMMdd'), 'yyyyMMdd'";
                        //仅有司龄或者工龄，最多会有一次跨档，依然采用原有的判断逻辑
                        for (String nowTimeStr : new String[]{nowTimeStr1, nowTimeStr2}) {
                            String groupExp = conditionSql.replaceAll("#\\{nowTime\\}", nowTimeStr).replaceAll("#nowTime#", nowTimeStr).
                                    replaceAll("#\\{nowhireDate\\}", nowTimeStr).replaceAll("#nowhireDate#", nowTimeStr);
                            calQuotaByRule(groupExp, leaveTypeDoMap, quotaConfigDo, genDto, distributionCycle, disCycleStart, disCycleEnd, empQuotaRuleMap, ruleDo, applicableEmpList, midDate, firstDate, sf);
                        }
                    }
                } else {
                    String groupExp = conditionSql;
                    if (isContinuousWorking) {
                        String nowTimeStr = DateUtil.getOnlyDate().toString();
                        groupExp = groupExp.replaceAll("#\\{nowTime\\}", nowTimeStr);
                    }
                    calQuotaByRule(groupExp, leaveTypeDoMap, quotaConfigDo, genDto, distributionCycle, disCycleStart, disCycleEnd,
                            empQuotaRuleMap, ruleDo, applicableEmpList, midDate, firstDate, sf);
                }
            }

            // 额度跨档处理
            Integer quotaType = leaveTypeDoMap.get(quotaConfigDo.getLeaveTypeId()).getQuotaType();
            if (!empQuotaRuleMap.isEmpty() && !QuotaTypeEnum.PARENTAL_LEAVE.getIndex().equals(quotaType) && !"探亲假".equals(leaveDefCode)) {
                //育儿假不存在跨档的问题
                Integer acctTimeType = leaveTypeDoMap.get(quotaConfigDo.getLeaveTypeId()).getAcctTimeType();
                for (Map.Entry<Long, List<EmpLeaveQuotaDto>> entry : empQuotaRuleMap.entrySet()) {
                    List<EmpLeaveQuotaDto> quotaRules = entry.getValue();
                    int size = quotaRules.size();
                    if (size == 1) {
                        //配额不跨维度则不做处理，入职年只会跨一个档次，上述逻辑已处理，此后逻辑不再进行处理
                        continue;
                    }
                    //配额跨维度计算
                    if (size > 2) {
                        //配额跨维度计算，多次跨档
                        crossMultiQuotaHandle(genDto.getYear(), disCycleStart, disCycleEnd, acctTimeType, quotaConfigDo, empQuotaRuleMap, entry);
                    } else {
                        //配额跨维度计算，一次跨档
                        Optional<EmpLeaveQuotaDto> minOptional = quotaRules.stream().min(Comparator.comparing(EmpLeaveQuotaDto::getCrossQuotaDate));
                        Optional<EmpLeaveQuotaDto> maxOptional = quotaRules.stream().max(Comparator.comparing(EmpLeaveQuotaDto::getCrossQuotaDate));
                        if (minOptional.isPresent() && maxOptional.isPresent()) {
                            //跨档前配额
                            EmpLeaveQuotaDto minQuotaDto = minOptional.get();
                            //跨档后配额
                            EmpLeaveQuotaDto maxQuotaDto = maxOptional.get();
                            Map maxQuotaCrossParams = maxQuotaDto.getCrossParams();
                            Long firstDateValue = (Long) maxQuotaCrossParams.get("firstDate");//值的含义和入参的firstDate一致，目前代表入职日期
                            Long midDateValue = (Long) maxQuotaCrossParams.get("midDate");//值的含义和入参的midDate一致
                            //todo 临时解决方案，解决线上问题
                            String minConditionExp = (String) minQuotaDto.getCrossParams().get("conditionExp");
                            String maxConditionExp = (String) maxQuotaCrossParams.get("conditionExp");
                            if (checkCorpAgeCrossRange(minConditionExp, maxConditionExp)) {
                                midDateValue = firstDateValue;
                            }
                            crossQuotaHandle(genDto.getYear(), firstDateValue, midDateValue, disCycleStart, disCycleEnd, acctTimeType, minQuotaDto, maxQuotaDto, quotaConfigDo);
                            empQuotaRuleMap.put(entry.getKey(), Collections.singletonList(minQuotaDto));
                        }
                    }
                }
            }

            // 查询本次生成配额的员工信息
            List<Long> genEmpIds = new ArrayList<>(empQuotaRuleMap.keySet());
            List<List<Long>> genEmpIdLists = ListTool.split(genEmpIds, 300);
            Map<Long, Long> empMaps = new HashMap<>();
            for (List<Long> ids : genEmpIdLists) {
                List<SysEmpInfo> employees = sysEmpInfo.getEmpInfoByIds(genDto.getBelongId(), ids);
                if (CollectionUtils.isNotEmpty(employees)) {
                    empMaps.putAll(employees.stream().filter(e -> e.getGender() != null).collect(Collectors.toMap(SysEmpInfo::getEmpid, SysEmpInfo::getGender)));
                    employees.forEach(e -> {
                        //只更新离职人员的
                        if (GenQuotaMode.RESIGNED.getIndex().equals(genDto.getGenQuotaMode()) && e.getTerminationDate() == null) {
                            empQuotaRuleMap.remove(e.getEmpid());
                        }
                    });
                }
            }

            // 生成员工假期配额数据
            empQuotaRuleMap.forEach((empid, quotaList) -> {
                if (GenQuotaMode.NOT_GENERATED.getIndex().equals(genDto.getGenQuotaMode())) {
                    quotaList = quotaList.stream().filter(r -> !oldEmpQuotaMap.containsKey(empid + "_" + r.getConfigId() + "_" + year + "_" + r.getFamilyId())).collect(Collectors.toList());
                }
                Long gender = empMaps.get(empid);
                if (null != gender) {
                    quotaList = quotaList.stream()
                            .filter(q -> leaveTypeDoMap.get(q.getLeaveTypeId()).getGenderType() == 0 || gender.equals(leaveTypeDoMap.get(q.getLeaveTypeId()).getGenderType()))
                            .collect(Collectors.toList());
                }
                quotaList.forEach(lq -> {
                    com.caidaocloud.attendance.service.application.dto.WaEmpQuota empQuota = new com.caidaocloud.attendance.service.application.dto.WaEmpQuota();
                    empQuota.setBelongOrgId(lq.getBelongOrgId());
                    empQuota.setIfAdvance(lq.getIfAdvance());
                    empQuota.setQuotaDay(lq.getQuotaVal().floatValue());
                    empQuota.setNowQuota(lq.getQuotaVal().floatValue());
                    empQuota.setOriginalQuotaDay(lq.getOriginalQuotaDay().floatValue());
                    empQuota.setQuotaDay(lq.getQuotaVal().floatValue());
                    empQuota.setEmpid(empid);
                    empQuota.setPeriodYear(year);
                    empQuota.setLeaveTypeId(lq.getLeaveTypeId());
                    empQuota.setFamilyId(lq.getFamilyId());
                    empQuota.setConfigId(lq.getConfigId());
                    empQuota.setHomeLeaveType(lq.getHomeLeaveType());
                    empQuota.setCarryMerge(false);
                    // 是否跨额度规则 false 不跨 ，true 跨
                    if (BooleanUtils.isTrue(lq.getIfCrossQuota())) {
                        empQuota.setCrossQuotaDate(lq.getCrossQuotaDate());
                        empQuota.setAnnualQuota(lq.getAnnualQuota().floatValue());
                        empQuota.setLastQuota(lq.getLastQuota());
                        empQuota.setNextQuota(lq.getNextQuota());
                        empQuota.setSecCrossQuotaDate(lq.getSecCrossQuotaDate());
                        empQuota.setThirdQuota(lq.getThirdQuota());
                    } else {
                        empQuota.setCrossQuotaDate(0L);
                        empQuota.setAnnualQuota(0f);
                        empQuota.setLastQuota(0);
                        empQuota.setNextQuota(0);
                        empQuota.setSecCrossQuotaDate(0L);
                        empQuota.setThirdQuota(0);
                    }
                    if (finalLeaveSettingMap.containsKey(lq.getLeaveTypeId())) {
                        empQuota.setQuotaSettingId(finalLeaveSettingMap.get(lq.getLeaveTypeId()));
                    }
                    String rowKey = empid + "_" + lq.getConfigId() + "_" + year + "_" + lq.getFamilyId();
                    if (oldEmpQuotaMap.containsKey(rowKey) && oldEmpQuotaMap.get(rowKey) != null && oldEmpQuotaMap.get(rowKey).getEmpQuotaId() != null) {
                        empQuota.setEmpQuotaId(oldEmpQuotaMap.get(rowKey).getEmpQuotaId());
                    }
                    Long quotaStartDate = DateUtil.getOnlyDate(new Date(lq.getDisCycleStart() * 1000));
                    boolean naturalYear = DistributionCycleEnum.NATURAL_YEAR.getIndex().equals(distributionCycle);
                    //配额有效期
                    if (naturalYear && lq.getHireDate() != null) {
                        //入职年份2022等
                        Short hireYear = Short.valueOf(DateUtil.convertDateTimeToStr(lq.getHireDate(), "yyyy", true));
                        //生成配额年份与入职年份相同
                        if (genDto.getYear().equals(hireYear)) {
                            long hireDateOnlyDate = DateUtil.getOnlyDate(new Date(lq.getHireDate() * 1000));
                            quotaStartDate = Math.max(hireDateOnlyDate, quotaStartDate);
                        }
                    }
                    boolean hireYearGreaterThanGenYear = false;
                    if (lq.getHireDate() != null) {
                        //入职年份
                        Short hireYear = Short.valueOf(DateUtil.convertDateTimeToStr(lq.getHireDate(), "yyyy", true));
                        //生成配额年份与入职年份相同
                        hireYearGreaterThanGenYear = genDto.getYear() <= hireYear;
                    }
                    if (hireYearGreaterThanGenYear) {
                        long hireDateOnlyDate = DateUtil.getOnlyDate(new Date(lq.getHireDate() * 1000));
                        quotaStartDate = Math.max(hireDateOnlyDate, quotaStartDate);
                    }
                    empQuota.setStartDate(quotaStartDate);
                    Float validityDuration = quotaConfigDo.getValidityDuration();
                    Integer validityUnit = quotaConfigDo.getValidityUnit();
                    //若发放周期为自认年，则有效期结束时间为发放周期的开始时间+配额有效期
                    Long validStartDate = naturalYear || hireYearGreaterThanGenYear ? lq.getDisCycleStart() : empQuota.getStartDate();
                    //配额有效期结束时间
                    if (validityUnit.equals(ValidityUnitEnum.DAY.getIndex())) {
                        int time = validityDuration.intValue();
                        if (time <= 0) {
                            time = 0;
                        } else {
                            time -= 1;
                        }
                        empQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(validStartDate * 1000, 0, 0, time));
                    } else if (validityUnit.equals(ValidityUnitEnum.MONTH.getIndex())) {
                        empQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(validStartDate * 1000, 0, validityDuration.intValue(), -1));
                    } else if (validityUnit.equals(ValidityUnitEnum.YEAR.getIndex())) {
                        empQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(validStartDate * 1000, validityDuration.intValue(), 0, -1));
                    }
                    //发放周期的开始结束时间
                    empQuota.setDisCycleStart(lq.getDisCycleStart());
                    empQuota.setDisCycleEnd(lq.getDisCycleEnd());
                    empQuota.setValidityDuration(validityDuration);
                    empQuota.setValidityUnit(validityUnit);
                    //本年额度折算
                    Integer quotaDistributeRule = quotaConfigDo.getQuotaDistributeRule();
                    if ((quotaDistributeRule.equals(QuotaDistributeRuleEnum.CONVERT.getIndex())
                            || quotaDistributeRule.equals(QuotaDistributeRuleEnum.DIST_BY_HIRE_MONTH.getIndex()))
                            && BooleanUtils.isFalse(lq.isCross())) {
                        Integer quotaRoundingRule = quotaConfigDo.getQuotaRoundingRule();
                        if (quotaRoundingRule == null) {
                            if (lq.getAcctTimeType() == 1) {
                                quotaRoundingRule = QuotaRoundingRuleEnum.ROUND_DOW_HALF.getIndex();
                            } else {
                                quotaRoundingRule = QuotaRoundingRuleEnum.ROUND_DOWN_1.getIndex();
                            }
                        }
                        BigDecimal val = BigDecimal.valueOf(empQuota.getQuotaDay());
                        if (lq.getHireDate() != null) {
                            Long startDate = lq.getHireDate();
                            if (startDate != null && empQuota.getDisCycleStart() != null && startDate > empQuota.getDisCycleStart()) {
                                try {
                                    if (QuotaDistributeRuleEnum.DIST_BY_HIRE_MONTH.getIndex().equals(quotaDistributeRule)
                                            && null != quotaConfigDo.getDayOfHireMonthDist()) {
                                        Integer dayOfHireMonthDist = quotaConfigDo.getDayOfHireMonthDist();
                                        Integer hireDay = DateUtilExt.getTimeMonthDay(startDate);
                                        if (hireDay <= dayOfHireMonthDist) {
                                            startDate = DateUtilExt.getMonthBegin(startDate);
                                        } else {
                                            startDate = DateUtilExt.getMonthBegin(startDate);
                                            startDate = DateUtilExt.addMonth(startDate, 1);
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error(e.getMessage(), e);
                                }
                                val = val.multiply(getConvertRate(empQuota.getDisCycleStart(), empQuota.getDisCycleEnd(), startDate, empQuota.getDisCycleEnd()));
                                empQuota.setQuotaDay(handleMantissa(genDto.getBelongId(), val, lq.getAcctTimeType(), quotaRoundingRule).floatValue());
                            } else {
                                empQuota.setQuotaDay(handleMantissa(genDto.getBelongId(), val, lq.getAcctTimeType(), quotaRoundingRule).floatValue());
                            }
                        } else {
                            empQuota.setQuotaDay(handleMantissa(genDto.getBelongId(), val, lq.getAcctTimeType(), quotaRoundingRule).floatValue());
                        }
                    } else {
                        if (lq.getAcctTimeType() != null && 2 == lq.getAcctTimeType()) {
                            empQuota.setQuotaDay(empQuota.getQuotaDay() * 60);
                        }
                    }
                    empQuota.setOriginalQuotaDay(empQuota.getQuotaDay());

                    //当前额度折算
                    lq.setNowDistributeRule(quotaConfigDo.getNowDistributeRule());
                    lq.setNowRoundingRule(quotaConfigDo.getNowRoundingRule());
                    lq.setWaEmpQuotaQuotaDay(empQuota.getQuotaDay());
                    lq.setQuotaDistributeRule(quotaDistributeRule);
                    lq.setDayOfHireMonthDist(quotaConfigDo.getDayOfHireMonthDist());
                    empQuota.setNowQuota(this.calNowQuota(lq));

                    Long sysTime = System.currentTimeMillis() / 1000;
                    if (empQuota.getEmpQuotaId() != null) {
                        empQuota.setUpduser(genDto.getUserId());
                        empQuota.setUpdtime(sysTime);
                        empQuota.setRemarks(String.format(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_UPDATE, null).getMsg(), DateUtil.getDateStrByTimesamp(DateUtil.getOnlyDate())));
                        empQuotaUpdList.add(empQuota);
                    } else {
                        empQuota.setDeductionDay(0f);
                        empQuota.setUsedDay(0f);
                        empQuota.setAdjustQuota(0f);
                        empQuota.setRemainDay(0f);
                        empQuota.setRemainUsedDay(0f);
                        empQuota.setCrtuser(genDto.getUserId());
                        empQuota.setCrttime(sysTime);
                        empQuota.setUpduser(genDto.getUserId());
                        empQuota.setUpdtime(sysTime);
                        empQuotaAddList.add(empQuota);
                        oldEmpQuotaMap.put(empQuota.getEmpid() + "_" + empQuota.getConfigId() + "_" + empQuota.getPeriodYear() + "_" + empQuota.getFamilyId(), empQuota);
                    }
                });
            });
        }
        importService.fastInsertList(com.caidaocloud.attendance.service.application.dto.WaEmpQuota.class, "empQuotaId", empQuotaAddList);
        importService.fastUpdList(com.caidaocloud.attendance.service.application.dto.WaEmpQuota.class, "empQuotaId", empQuotaUpdList);
        groovyScriptEngine.clearCache();
    }

    /**
     * 一次跨档额度折算
     *
     * @param year           配额年份
     * @param firstDateValue 值的含义和入参的firstDate一致，目前代表入职日期
     * @param midDateValue   值的含义和入参的midDate一致
     * @param disCycleStart  发放周期开始日
     * @param disCycleEnd    发放周期结束日
     * @param acctTimeType   假期单位 1 天， 2 小时
     * @param minQuotaDto    跨档前配额
     * @param maxQuotaDto    跨档后配额
     * @param quotaConfigDo  额度规则信息
     */
    private void crossQuotaHandle(Short year, Long firstDateValue, Long midDateValue, Long disCycleStart, Long disCycleEnd, Integer acctTimeType,
                                  EmpLeaveQuotaDto minQuotaDto, EmpLeaveQuotaDto maxQuotaDto, LeaveQuotaConfigDo quotaConfigDo) {
        if (midDateValue == null) {
            midDateValue = firstDateValue;
        }
        int min = minQuotaDto.getQuotaVal().intValue();
        int max = maxQuotaDto.getQuotaVal().intValue();
        BigDecimal val = BigDecimal.ZERO;
        try {
            val = convertQuotaByDate(firstDateValue, midDateValue, year, min, max, disCycleStart, disCycleEnd, quotaConfigDo, acctTimeType, minQuotaDto);
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }
        minQuotaDto.setIfCrossQuota(true);
        minQuotaDto.setAnnualQuota(val);
        minQuotaDto.setOriginalQuotaDay(val);
        // 跨额度规则是否进行了折算 0 未折算 ， 1 已折算
        boolean now = ConvertRuleEnum.NOW.getIndex().equals(minQuotaDto.getConvertRule());
        boolean after = ConvertRuleEnum.AFTER.getIndex().equals(minQuotaDto.getConvertRule())
                && (minQuotaDto.getCrossQuotaDate() != null && minQuotaDto.getCrossQuotaDate() > 0
                && minQuotaDto.getCrossQuotaDate() <= DateUtil.getOnlyDate());
        if (now || after) {
            minQuotaDto.setQuotaVal(val);
            minQuotaDto.setCross(true);
            Map<String, Object> crossParams = new HashMap<>();
            crossParams.put("midDate", midDateValue);
            crossParams.put("firstDate", firstDateValue);
            crossParams.put("year", year);
            crossParams.put("last", min);
            crossParams.put("next", max);
            minQuotaDto.setCrossParams(crossParams);
        }
    }

    /**
     * 两次额度跨档折算
     *
     * @param year            配额年份
     * @param disCycleStart   发放周期开始日
     * @param disCycleEnd     发放周期结束日
     * @param acctTimeType    假期单位 1 天， 2 小时
     * @param quotaConfigDo   额度规则配置信息
     * @param empQuotaRuleMap List<EmpLeaveQuotaDto>>
     * @param entry           Map.Entry<Integer, List<EmpLeaveQuotaDto>>
     */
    private void crossMultiQuotaHandle(Short year, Long disCycleStart, Long disCycleEnd, Integer acctTimeType,
                                       LeaveQuotaConfigDo quotaConfigDo, Map<Long, List<EmpLeaveQuotaDto>> empQuotaRuleMap,
                                       Map.Entry<Long, List<EmpLeaveQuotaDto>> entry) {
        List<EmpLeaveQuotaDto> quotaRules = entry.getValue();
        Optional<EmpLeaveQuotaDto> minOptional = quotaRules.stream().min(Comparator.comparing(EmpLeaveQuotaDto::getCrossQuotaDate));
        Optional<EmpLeaveQuotaDto> maxOptional = quotaRules.stream().max(Comparator.comparing(EmpLeaveQuotaDto::getCrossQuotaDate));
        EmpLeaveQuotaDto minQuotaDto = minOptional.get();
        int minQuota = minQuotaDto.getQuotaVal().intValue();
        //首个临界点
        EmpLeaveQuotaDto midQuotaDto = quotaRules.get(1);
        int midQuota = midQuotaDto.getQuotaVal().intValue();
        Long firstMidDate = midQuotaDto.getCrossQuotaDate();
        //第二个临界点
        EmpLeaveQuotaDto maxQuotaDto = maxOptional.get();
        int maxQuota = maxQuotaDto.getQuotaVal().intValue();
        Long secMidDate = maxQuotaDto.getCrossQuotaDate();
        Map maxQuotaCrossParams = maxQuotaDto.getCrossParams();
        Long firstDateValue = (Long) maxQuotaCrossParams.get("firstDate");//值的含义和入参的firstDate一致，目前代表入职日期
        BigDecimal val = BigDecimal.ZERO;
        try {
            val = convertMultiQuotaByDate(year, firstDateValue, firstMidDate, secMidDate, minQuota, midQuota, maxQuota, disCycleStart, disCycleEnd, quotaConfigDo, acctTimeType, minQuotaDto);
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }
        minQuotaDto.setIfCrossQuota(true);
        minQuotaDto.setAnnualQuota(val);
        minQuotaDto.setOriginalQuotaDay(val);
        // 跨额度规则是否进行了折算 0 未折算 ， 1 已折算
        boolean now = ConvertRuleEnum.NOW.getIndex().equals(minQuotaDto.getConvertRule());
        boolean after = ConvertRuleEnum.AFTER.getIndex().equals(minQuotaDto.getConvertRule())
                && ((firstMidDate != null && firstMidDate > 0 && firstMidDate <= DateUtil.getOnlyDate())
                || (secMidDate != null && secMidDate > 0 && secMidDate <= DateUtil.getOnlyDate()));
        if (now || after) {
            minQuotaDto.setQuotaVal(val);
            minQuotaDto.setCross(true);
            Map<String, Object> crossParams = new HashMap<>();
            crossParams.put("midDate", firstMidDate);
            crossParams.put("secMidDate", secMidDate);
            crossParams.put("firstDate", firstDateValue);
            crossParams.put("year", year);
            crossParams.put("last", minQuota);
            crossParams.put("next", midQuota);
            crossParams.put("thirdQuota", maxQuota);
            minQuotaDto.setCrossParams(crossParams);
        }
        minQuotaDto.setCrossQuotaDate(firstMidDate);
        minQuotaDto.setSecCrossQuotaDate(secMidDate);
        empQuotaRuleMap.put(entry.getKey(), Collections.singletonList(minQuotaDto));
    }

    /**
     * 按年发放配额自动结转
     *
     * @param belongOrgId
     */
    @Transactional
    @Override
    public void autoCarryForwardQuota(String belongOrgId, Long userId) {
        //查询公司下需要结转的配额数据
        PageBean pageBean = new PageBean();
        pageBean.setPosStart(0);
        pageBean.setCount(5);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "crttime.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        Long curDate = DateUtil.getOnlyDate();
        int year = 0;
        try {
            year = DateUtilExt.getTimeYear(curDate);
        } catch (ParseException e) {
            log.error("autoCarryForwardQuota getTimeYear error msg {}", e.getMessage(), e);
            return;
        }
        log.info("autoCarryForwardQuota belongOrgId:{} curDate:{} year:{} pageStart:{} begin search", belongOrgId, curDate, year, pageBean.getPosStart());
        PageList<EmpQuotaDetail> pageList = waEmpQuotaDo.getEmpQuotaDetailPageList(pageBounds, belongOrgId, curDate, (short) year);
        if (CollectionUtils.isEmpty(pageList)) {
            log.info("autoCarryForwardQuota belongOrgId:{} getEmpQuotaDetailPageList empty", belongOrgId);
            return;
        }
        int total = pageList.getPaginator().getTotalCount();
        log.info("autoCarryForwardQuota belongOrgId:{} 查询数据量：{}", belongOrgId, total);

        int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
        pageBean.setCount(PAGE_SIZE);
        //分页查询数据
        List<WaLeaveTypeDo> leaveTypes = waLeaveTypeDo.getLeaveTypesByBelongOrgId(belongOrgId, QuotaTypeEnum.ISSUED_ANNUALLY.getIndex());
        if (CollectionUtils.isEmpty(leaveTypes)) {
            log.info("autoCarryForwardQuota.getLeaveTypeByGroupId empty");
            return;
        }
        List<EmpQuotaDetail> nextYearQuotas = waEmpQuotaDo.getNotExpiredEmpQuotaListByYear(belongOrgId, year, curDate);
        Map<String, EmpQuotaDetail> nextYearQuotaMap = nextYearQuotas.stream()
                .collect(Collectors.toMap(next -> String.format("%s_%s_%s_%s", next.getEmpid(), next.getLeaveTypeId(), next.getConfigId(), next.getPeriodYear()), Function.identity(), (k1, k2) -> k2));
        Map<Integer, WaLeaveTypeDo> leaveTypeDoMap = leaveTypes.stream().collect(Collectors.toMap(WaLeaveTypeDo::getLeaveTypeId, Function.identity()));
        List<WaEmpQuota> addList = new ArrayList<>();
        List<WaEmpQuota> updList = new ArrayList<>();
        for (int i = 1; i <= totalPage; i++) {
            pageBean.setPosStart((i - 1) * PAGE_SIZE);
            log.info("autoCarryForwardQuota posStart:{}", pageBean.getPosStart());
            pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
            PageList<EmpQuotaDetail> dataList = waEmpQuotaDo.getEmpQuotaDetailPageList(pageBounds, belongOrgId, curDate, (short) year);
            if (CollectionUtils.isNotEmpty(dataList)) {
                List<LeaveQuotaConfigDo> configList = leaveQuotaConfigDo.getLeaveQuotaConfigByIds(belongOrgId, null);
                Map<Long, LeaveQuotaConfigDo> leaveQuotaConfigMap = configList.stream().collect(Collectors.toMap(LeaveQuotaConfigDo::getConfigId, Function.identity(), (v1, v2) -> v1));
                for (EmpQuotaDetail empQuota : dataList) {
                    //计算剩余额度
                    BigDecimal surplusQuota = BigDecimal.valueOf(empQuota.getQuotaDay()).add(BigDecimal.valueOf(empQuota.getAdjustQuota())).
                            subtract(BigDecimal.valueOf(empQuota.getUsedDay())).subtract(BigDecimal.valueOf(empQuota.getFixUsedDay())).subtract(BigDecimal.valueOf(empQuota.getInTransitQuota()));
                    if (surplusQuota.floatValue() <= 0) {
                        log.info("autoCarryForwardQuota empId {} leaveTypeId {} 无剩余额度", empQuota.getEmpid(), empQuota.getLeaveTypeId());
                        continue;
                    }
                    WaEmpQuota carryQuota = new WaEmpQuota();
                    carryQuota.setEmpid(empQuota.getEmpid());
                    carryQuota.setBelongOrgId(belongOrgId);
                    carryQuota.setCrttime(System.currentTimeMillis() / 1000);
                    carryQuota.setCrtuser(userId);
                    carryQuota.setUpdtime(System.currentTimeMillis() / 1000);
                    carryQuota.setUpduser(userId);
                    carryQuota.setQuotaDay(surplusQuota.floatValue());
                    carryQuota.setNowQuota(surplusQuota.floatValue());
                    carryQuota.setUsedDay(0f);
                    carryQuota.setFixUsedDay(0f);
                    carryQuota.setAdjustQuota(0f);
                    carryQuota.setIfAdvance(1);
                    carryQuota.setRemainDay(0f);
                    WaLeaveTypeDo leaveTypeDo = leaveTypeDoMap.get(empQuota.getLeaveTypeId());
                    if (leaveTypeDo != null) {
                        carryQuota.setRemarks(empQuota.getPeriodYear() + "年" + leaveTypeDo.getLeaveName() + "结转");
                        if (null != empQuota.getTransferType() && empQuota.getTransferType() == 2) {
                            float maxTransferQuota = Optional.ofNullable(empQuota.getMaxTransferQuota()).orElse(0f);
                            if (PreTimeUnitEnum.HOUR.getIndex().equals(leaveTypeDo.getAcctTimeType())) {
                                maxTransferQuota = maxTransferQuota * 60;
                            }
                            if (maxTransferQuota < surplusQuota.floatValue()) {
                                carryQuota.setQuotaDay(maxTransferQuota);
                                carryQuota.setNowQuota(maxTransferQuota);
                            }
                        }
                    }
                    LeaveQuotaConfigDo carryOverToConfig = leaveQuotaConfigMap.get(empQuota.getCarryOverTo());
                    if (null == carryOverToConfig && null != empQuota.getCarryOverTo() && empQuota.getCarryOverTo() == -1) {
                        carryOverToConfig = leaveQuotaConfigMap.get(empQuota.getConfigId());
                    }
                    if (null == carryOverToConfig) {
                        continue;
                    }
                    carryQuota.setConfigId(carryOverToConfig.getConfigId());
                    boolean carryMerge = null != empQuota.getCarryOverTo() && empQuota.getCarryOverTo() == -1 && null != empQuota.getCarryToType() && empQuota.getCarryToType() == 2;
                    carryQuota.setCarryMerge(carryMerge);
                    carryQuota.setLeaveTypeId(carryOverToConfig.getLeaveTypeId());
                    carryQuota.setValidityDuration(empQuota.getCarryOverValidityDuration());
                    carryQuota.setValidityUnit(empQuota.getCarryOverValidityUnit());

                    Integer startType = empQuota.getCarryOverStartType();
                    if (CarryOverStartTypeEnum.ORIGINAL_START_DATE.getIndex().equals(startType)) {
                        //原配额生效日期
                        carryQuota.setStartDate(empQuota.getStartDate());
                    } else if (CarryOverStartTypeEnum.ORIGINAL_LAST_DATE.getIndex().equals(startType)) {
                        //原配额失效日期
                        carryQuota.setStartDate(DateUtil.getOnlyDate(new Date(empQuota.getLastDate() * 1000)));
                    } else if (CarryOverStartTypeEnum.ORIGINAL_LAST_DATE_AFTE.getIndex().equals(startType)) {
                        //原配额失效日期后一天
                        carryQuota.setStartDate(DateUtil.getOnlyDate(new Date(DateUtil.addDate(empQuota.getLastDate() * 1000, 1) * 1000)));
                    } else {
                        carryQuota.setStartDate(empQuota.getStartDate());
                    }
                    Float validityDuration = empQuota.getCarryOverValidityDuration();
                    Integer validityUnit = empQuota.getCarryOverValidityUnit();
                    if (validityUnit.equals(ValidityUnitEnum.DAY.getIndex())) {
                        int time = validityDuration.intValue();
                        if (time <= 0) {
                            time = 0;
                        } else {
                            time -= 1;
                        }
                        carryQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(carryQuota.getStartDate() * 1000, 0, 0, time));
                    } else if (validityUnit.equals(ValidityUnitEnum.MONTH.getIndex())) {
                        carryQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(carryQuota.getStartDate() * 1000, 0, validityDuration.intValue(), -1));
                    } else if (validityUnit.equals(ValidityUnitEnum.YEAR.getIndex())) {
                        carryQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(carryQuota.getStartDate() * 1000, validityDuration.intValue(), 0, -1));
                    }
                    Integer periodYear = null;
                    try {
                        periodYear = DateUtilExt.getTimeYear(carryQuota.getStartDate());
                    } catch (ParseException e) {
                        log.error("autoCarryForwardQuota periodYear parse error msg {}", e.getMessage(), e);
                        continue;
                    }
                    carryQuota.setPeriodYear(periodYear.shortValue());
                    carryQuota.setDisCycleStart(empQuota.getDisCycleStart());
                    carryQuota.setDisCycleEnd(empQuota.getDisCycleEnd());
                    //记录原配额ID
                    carryQuota.setOriginalEmpQuotaId(empQuota.getEmpQuotaId());
                    // 结转合并显示：
                    // 若未生成过次年配额，则不生成结转配额，
                    // 若结转配额失效日期大于次年配额失效日期，则不生成结转配额
                    if (carryMerge) {
                        if (carryQuota.getPeriodYear().equals(empQuota.getPeriodYear())) {
                            continue;
                        }
                        String carryQuotaKey = String.format("%s_%s_%s_%s", carryQuota.getEmpid(), carryQuota.getLeaveTypeId(), carryQuota.getConfigId(), carryQuota.getPeriodYear());
                        if (!nextYearQuotaMap.containsKey(carryQuotaKey)) {
                            continue;
                        }
                        EmpQuotaDetail quotaDetail = nextYearQuotaMap.get(carryQuotaKey);
                        if (carryQuota.getLastDate() > quotaDetail.getLastDate()) {
                            carryQuota.setLastDate(quotaDetail.getLastDate());
                        }
                        carryQuota.setMergeEmpQuotaId(quotaDetail.getEmpQuotaId());
                    }
                    addList.add(carryQuota);

                    WaEmpQuota originalQuota = new WaEmpQuota();
                    originalQuota.setEmpQuotaId(empQuota.getEmpQuotaId());
                    originalQuota.setRemainDay(carryQuota.getQuotaDay());
                    originalQuota.setUpdtime(System.currentTimeMillis() / 1000);
                    originalQuota.setUpduser(userId);
                    originalQuota.setIfCarryForward(true);
                    updList.add(originalQuota);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            importService.fastInsertList(WaEmpQuota.class, "empQuotaId", addList);
        }
        if (CollectionUtils.isNotEmpty(updList)) {
            importService.fastUpdList(WaEmpQuota.class, "empQuotaId", updList);
        }
    }

    @Override
    public void carryForwardQuota(QuotaCarryForwardDto dto, UserInfo userInfo, String dataScope) throws Exception {
        //根据考勤方案查询按年发放并且配置了结转规则的假期规则
        WaGroup waGroup = waGroupMapper.selectByPrimaryKey(dto.getWaGroupId());
        if (waGroup == null || waGroup.getLeaveTypeIds() == null) {
            log.info("QuotaService.carryForwardQuota getWaGroup empty");
            return;
        }
        List<WaLeaveTypeDo> leaveTypes = waLeaveTypeDo.getLeaveTypeByGroupId(userInfo.getTenantId(), dto.getWaGroupId());
        if (CollectionUtils.isEmpty(leaveTypes)) {
            log.info("QuotaService.carryForwardQuota getLeaveTypeByGroupId empty");
            return;
        }
        Map<Integer, WaLeaveTypeDo> leaveTypeDoMap = leaveTypes.stream().collect(Collectors.toMap(WaLeaveTypeDo::getLeaveTypeId, Function.identity()));
        //只计算限额并且按年发放的假期额度
        leaveTypes = leaveTypes.stream().filter(r -> QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(r.getQuotaRestrictionType())
                && QuotaTypeEnum.ISSUED_ANNUALLY.getIndex().equals(r.getQuotaType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaveTypes)) {
            log.info("QuotaService.carryForwardQuota 过滤掉只计算限额并且按年发放的假期额度后数据为空");
            return;
        }
        List<Integer> groupLeaveTypeIds = leaveTypes.stream().map(WaLeaveTypeDo::getLeaveTypeId).collect(Collectors.toList());
        //查询配额配置信息
        List<LeaveQuotaConfigDo> configDoList = leaveQuotaConfigDo.getConfigListByIds(userInfo.getTenantId(), groupLeaveTypeIds);
        if (CollectionUtils.isEmpty(configDoList)) {
            log.info("QuotaService.carryForwardQuota getConfigListByIds empty");
            return;
        }
        Map<Long, LeaveQuotaConfigDo> quotaConfigDoMap = configDoList.stream().collect(Collectors.toMap(LeaveQuotaConfigDo::getConfigId, Function.identity()));
        //过滤掉未配置结转规则的假期类型
        configDoList = configDoList.stream().filter(o -> ExpirationRuleEnum.CARRY_FORWARD.getIndex().equals(o.getExpirationRule()) &&
                o.getCarryOverTo() != null && o.getCarryOverStartType() != null && o.getCarryOverValidityDuration() != null && o.getCarryOverValidityUnit() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(configDoList)) {
            log.info("QuotaService.carryForwardQuota 过滤掉未配置结转规则的假期类型后数据为空");
            return;
        }
        //根据考勤方案查询方案下的员工数据
        Long curDate = DateUtil.getOnlyDate();
        List<Long> empIds = waEmpQuotaDo.getGroupEmpList(dto.getWaGroupId(), dto.getYear().shortValue(), curDate, dataScope);
        if (CollectionUtils.isEmpty(empIds)) {
            log.info("QuotaService.getGroupEmpList empty");
            return;
        }
        //检查所选择的员工是否属于这个考勤分组下的
        if (dto.getEmpId() != null) {
            if (!empIds.contains(dto.getEmpId())) {
                log.info("QuotaService.carryForwardQuota empId {} 在考勤分组中不存在", dto.getEmpId());
                return;
            }
            empIds.removeIf(k -> !dto.getEmpId().equals(k));
        }
        List<Integer> leaveTypeIds = configDoList.stream().map(LeaveQuotaConfigDo::getLeaveTypeId).distinct().collect(Collectors.toList());
        //查询已生成的本年配额
        WaEmpQuotaExample empQuotaExample = new WaEmpQuotaExample();
        empQuotaExample.createCriteria().andPeriodYearEqualTo(dto.getYear().shortValue()).andEmpidIn(empIds)
                .andLeaveTypeIdIn(leaveTypeIds).andLastDateLessThan(curDate);
        List<WaEmpQuota> oldQuotaList = waEmpQuotaMapper.selectByExample(empQuotaExample);
        if (CollectionUtils.isEmpty(oldQuotaList)) {
            log.info("QuotaService.carryForwardQuota getWaEmpQuota empty");
            return;
        }
        //生成结转额度:可用额度大于0且未结转
        List<WaEmpQuota> addList = new ArrayList<>();
        List<WaEmpQuota> updList = new ArrayList<>();
        Integer nextYear = dto.getYear() + 1;
        List<EmpQuotaDetail> nextYearQuotas = waEmpQuotaDo.getNotExpiredEmpQuotaListByYear(userInfo.getTenantId(), nextYear, curDate);
        Map<String, EmpQuotaDetail> nextYearQuotaMap = nextYearQuotas.stream()
                .collect(Collectors.toMap(next -> String.format("%s_%s_%s_%s", next.getEmpid(), next.getLeaveTypeId(), next.getConfigId(), next.getPeriodYear()), Function.identity(), (k1, k2) -> k2));
        for (WaEmpQuota empQuota : oldQuotaList) {
            if (BooleanUtils.isTrue(empQuota.getIfCarryForward())) {
                log.info("QuotaService.carryForwardQuota empId {} leaveTypeId {} 已结转", empQuota.getEmpid(), empQuota.getLeaveTypeId());
                continue;
            }
            if (null != empQuota.getOriginalEmpQuotaId()) {
                log.info("QuotaService.carryForwardQuota empId {} leaveTypeId {} 是结转额度，不可再次进行结转", empQuota.getEmpid(), empQuota.getLeaveTypeId());
                continue;
            }
            if (empQuota.getQuotaDay() == null) {
                empQuota.setQuotaDay(0f);
            }
            if (empQuota.getAdjustQuota() == null) {
                empQuota.setAdjustQuota(0f);
            }
            if (empQuota.getUsedDay() == null) {
                empQuota.setUsedDay(0f);
            }
            if (empQuota.getFixUsedDay() == null) {
                empQuota.setFixUsedDay(0f);
            }
            if (empQuota.getInTransitQuota() == null) {
                empQuota.setInTransitQuota(0f);
            }
            //计算剩余额度
            BigDecimal surplusQuota = BigDecimal.valueOf(empQuota.getQuotaDay()).add(BigDecimal.valueOf(empQuota.getAdjustQuota())).
                    subtract(BigDecimal.valueOf(empQuota.getUsedDay())).subtract(BigDecimal.valueOf(empQuota.getFixUsedDay())).subtract(BigDecimal.valueOf(empQuota.getInTransitQuota()));
            if (surplusQuota.floatValue() <= 0) {
                log.info("QuotaService.carryForwardQuota empId {} leaveTypeId {} 无剩余额度", empQuota.getEmpid(), empQuota.getLeaveTypeId());
                continue;
            }
            //查询此假期结转规则
            LeaveQuotaConfigDo quotaConfigDo = quotaConfigDoMap.get(empQuota.getConfigId());
            if (quotaConfigDo == null || (quotaConfigDo.getCarryOverTo() != -1 && !quotaConfigDoMap.containsKey(quotaConfigDo.getCarryOverTo()))) {
                log.info("QuotaService.carryForwardQuota targetQuotaConfig {} is empty", empQuota.getConfigId());
                continue;
            }
            //目标额度
            LeaveQuotaConfigDo targetQuotaConfig = quotaConfigDoMap.get(quotaConfigDo.getCarryOverTo());
            if (null != quotaConfigDo.getCarryOverTo() && quotaConfigDo.getCarryOverTo() == -1) {
                targetQuotaConfig = quotaConfigDo;
            }
            WaEmpQuota carryQuota = new WaEmpQuota();
            carryQuota.setEmpid(empQuota.getEmpid());
            carryQuota.setBelongOrgId(empQuota.getBelongOrgId());
            carryQuota.setCrttime(System.currentTimeMillis() / 1000);
            carryQuota.setCrtuser(userInfo.getUserId());
            carryQuota.setUpdtime(System.currentTimeMillis() / 1000);
            carryQuota.setUpduser(userInfo.getUserId());
            carryQuota.setQuotaDay(surplusQuota.floatValue());
            carryQuota.setNowQuota(surplusQuota.floatValue());
            carryQuota.setUsedDay(0f);
            carryQuota.setFixUsedDay(0f);
            carryQuota.setAdjustQuota(0f);
            carryQuota.setIfAdvance(1);
            carryQuota.setRemainDay(0f);
            carryQuota.setConfigId(targetQuotaConfig.getConfigId());
            WaLeaveTypeDo leaveTypeDo = leaveTypeDoMap.get(empQuota.getLeaveTypeId());
            if (leaveTypeDo != null) {
                carryQuota.setRemarks(empQuota.getPeriodYear() + "年" + leaveTypeDo.getLeaveName() + "结转");
                if (null != quotaConfigDo.getTransferType() && quotaConfigDo.getTransferType() == 2) {
                    float maxTransferQuota = Optional.ofNullable(quotaConfigDo.getMaxTransferQuota()).orElse(0f);
                    if (PreTimeUnitEnum.HOUR.getIndex().equals(leaveTypeDo.getAcctTimeType())) {
                        maxTransferQuota = maxTransferQuota * 60;
                    }
                    if (maxTransferQuota < surplusQuota.floatValue()) {
                        carryQuota.setQuotaDay(maxTransferQuota);
                        carryQuota.setNowQuota(maxTransferQuota);
                    }
                }
            }
            boolean carryMerge = null != quotaConfigDo.getCarryOverTo() && quotaConfigDo.getCarryOverTo() == -1 && null != quotaConfigDo.getCarryToType() && quotaConfigDo.getCarryToType() == 2;
            carryQuota.setCarryMerge(carryMerge);
            carryQuota.setLeaveTypeId(targetQuotaConfig.getLeaveTypeId());
            carryQuota.setValidityDuration(quotaConfigDo.getCarryOverValidityDuration());
            carryQuota.setValidityUnit(quotaConfigDo.getCarryOverValidityUnit());

            Integer startType = quotaConfigDo.getCarryOverStartType();
            if (CarryOverStartTypeEnum.ORIGINAL_START_DATE.getIndex().equals(startType)) {
                //原配额生效日期
                carryQuota.setStartDate(empQuota.getStartDate());
            } else if (CarryOverStartTypeEnum.ORIGINAL_LAST_DATE.getIndex().equals(startType)) {
                //原配额失效日期
                carryQuota.setStartDate(DateUtil.getOnlyDate(new Date(empQuota.getLastDate() * 1000)));
            } else if (CarryOverStartTypeEnum.ORIGINAL_LAST_DATE_AFTE.getIndex().equals(startType)) {
                //原配额失效日期后一天
                carryQuota.setStartDate(DateUtil.getOnlyDate(new Date(DateUtil.addDate(empQuota.getLastDate() * 1000, 1) * 1000)));
            } else {
                carryQuota.setStartDate(empQuota.getStartDate());
            }
            Float validityDuration = quotaConfigDo.getCarryOverValidityDuration();
            Integer validityUnit = quotaConfigDo.getCarryOverValidityUnit();
            if (validityUnit.equals(ValidityUnitEnum.DAY.getIndex())) {
                Integer time = validityDuration.intValue();
                if (time <= 0) {
                    time = 0;
                } else {
                    time -= 1;
                }
                carryQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(carryQuota.getStartDate() * 1000, 0, 0, time));
            } else if (validityUnit.equals(ValidityUnitEnum.MONTH.getIndex())) {
                carryQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(carryQuota.getStartDate() * 1000, 0, validityDuration.intValue(), -1));
            } else if (validityUnit.equals(ValidityUnitEnum.YEAR.getIndex())) {
                carryQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(carryQuota.getStartDate() * 1000, validityDuration.intValue(), 0, -1));
            }
            Integer periodYear = DateUtilExt.getTimeYear(carryQuota.getStartDate());
            carryQuota.setPeriodYear(periodYear.shortValue());
            carryQuota.setDisCycleStart(empQuota.getDisCycleStart());
            carryQuota.setDisCycleEnd(empQuota.getDisCycleEnd());
            //记录原配额ID
            carryQuota.setOriginalEmpQuotaId(empQuota.getEmpQuotaId());
            // 结转合并显示：
            // 若未生成过次年配额，则不生成结转配额，
            // 若结转配额失效日期大于次年配额失效日期，则不生成结转配额
            if (carryMerge) {
                if (carryQuota.getPeriodYear().equals(dto.getYear().shortValue())) {
                    continue;
                }
                String carryQuotaKey = String.format("%s_%s_%s_%s", carryQuota.getEmpid(), carryQuota.getLeaveTypeId(), carryQuota.getConfigId(), nextYear);
                if (!nextYearQuotaMap.containsKey(carryQuotaKey)) {
                    continue;
                }
                EmpQuotaDetail quotaDetail = nextYearQuotaMap.get(carryQuotaKey);
                if (carryQuota.getLastDate() > quotaDetail.getLastDate()) {
                    carryQuota.setLastDate(quotaDetail.getLastDate());
                }
                carryQuota.setMergeEmpQuotaId(quotaDetail.getEmpQuotaId());
            }
            addList.add(carryQuota);
            WaEmpQuota originalQuota = new WaEmpQuota();
            originalQuota.setEmpQuotaId(empQuota.getEmpQuotaId());
            originalQuota.setRemainDay(carryQuota.getQuotaDay());
            originalQuota.setIfCarryForward(true);
            originalQuota.setUpdtime(System.currentTimeMillis() / 1000);
            originalQuota.setUpduser(userInfo.getUserId());
            updList.add(originalQuota);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            importService.fastInsertList(WaEmpQuota.class, "empQuotaId", addList);
        }
        if (CollectionUtils.isNotEmpty(updList)) {
            importService.fastUpdList(WaEmpQuota.class, "empQuotaId", updList);
        }
    }

    @Transactional
    @Override
    public void genEmpQuotaForFixedQuota(String belongOrgId, Long userId, String dataScope) {
        /**
         * 婚假、产假设置时，额度限制=限额，额度类型=固定额度，有效期默认永久有效
         * 新员工入职后根据匹配的假期规则计算出可用额度并发放，生效日期=入职日期；入职日期当天定时任务触发执行
         * 固定额度假期默认为一次性发放，员工申请后，当已用大于0，状态由生效更新为失效
         * 如需要重新发放，可导入或新增进行发放；如需扣减可通过调整额度进行编辑输入负数
         * 查询员工以及对应的固定额度的假期配置信息
         *
         */
        Long curDate = DateUtil.getOnlyDate();
        List<Map> list = leaveQuotaConfigDo.getEmpFixedQuotaConfigList(belongOrgId, curDate, dataScope);
        if (CollectionUtils.isEmpty(list)) {
            log.info("genEmpQuotaForFixedQuota getEmpFixedQuotaConfigList is empty");
            return;
        }
        List<EmpFixedQuotaConfigDto> dtoList = JSONArray.parseArray(JSON.toJSONString(list), EmpFixedQuotaConfigDto.class);
        /**
         * CAIDAOM-1710 铁通需求
         * 产假固定额度不生成假期额度
         */
        dtoList = dtoList.stream().filter(d -> d.getLeaveType() != 4).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            log.info("genEmpQuotaForFixedQuota getEmpFixedQuotaConfigList is empty");
            return;
        }
        List<Long> configIds = dtoList.stream().map(EmpFixedQuotaConfigDto::getConfigId).distinct().collect(Collectors.toList());
        //查询配额生成规则
        List<QuotaGenRuleDo> genRuleDoList = quotaGenRuleDo.getListByConfigIds(belongOrgId, configIds);
        if (CollectionUtils.isEmpty(genRuleDoList)) {
            log.info("genEmpQuotaForFixedQuota genRuleDoList is empty");
            return;
        }
        List<Integer> leaveTypeIds = dtoList.stream().map(EmpFixedQuotaConfigDto::getLeaveTypeId).distinct().collect(Collectors.toList());
        List<Long> empIds = dtoList.stream().map(EmpFixedQuotaConfigDto::getEmpId).distinct().collect(Collectors.toList());

        List<WaLeaveTypeDo> leaveTypeDos = waLeaveTypeDo.getLeaveTypesByIds(belongOrgId, leaveTypeIds);
        if (CollectionUtils.isEmpty(leaveTypeDos)) {
            log.info("genEmpQuotaForFixedQuota leaveTypeDos is empty");
            return;
        }
        Map<Integer, WaLeaveTypeDo> leaveTypeDoMap = leaveTypeDos.stream().collect(Collectors.toMap(WaLeaveTypeDo::getLeaveTypeId, Function.identity()));
//        Map<String, EmpFixedQuotaConfigDto> empFixedQuotaConfigDtoMap = dtoList.stream().collect(Collectors.toMap(r -> r.getEmpId() + "_" + r.getLeaveTypeId(), Function.identity(), (v1, v2) -> v1));
        List<WaEmpQuota> oldQuotaList = Lists.newArrayList();
        List<List<Long>> empIdList = ListTool.split(empIds, 500);
        for (List<Long> rows : empIdList) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            WaEmpQuotaExample empQuotaExample = new WaEmpQuotaExample();
            empQuotaExample.createCriteria().andEmpidIn(rows).andLeaveTypeIdIn(leaveTypeIds);
            oldQuotaList.addAll(waEmpQuotaMapper.selectByExample(empQuotaExample));
        }
        Map<String, WaEmpQuota> oldEmpQuotaMap = oldQuotaList.stream().collect(Collectors.toMap(r -> r.getEmpid() + "_" + r.getLeaveTypeId(), Function.identity(), (v1, v2) -> v1));
        Map<Integer, List<QuotaGenRuleDo>> quotaGenRuleMap = genRuleDoList.stream().collect(Collectors.groupingBy(QuotaGenRuleDo::getLeaveTypeId));
        //兼容quota_setting_id 字段值，后面上线稳定后移除此字段
        Map<Integer, Integer> leaveSettingMap = new HashMap<>();
        List<WaLeaveSettingDo> leaveSettingDos = waLeaveSettingDo.getLeaveSettingList(belongOrgId, leaveTypeIds);
        if (CollectionUtils.isNotEmpty(leaveSettingDos)) {
            leaveSettingMap = leaveSettingDos.stream().collect(Collectors.toMap(WaLeaveSettingDo::getLeaveTypeId, WaLeaveSettingDo::getQuotaSettingId));
        }
        List<WaEmpQuota> empQuotaAddList = new ArrayList<>();
        Map<Integer, Integer> finalLeaveSettingMap = leaveSettingMap;
        quotaGenRuleMap.forEach((leaveTypeId, ruleList) -> {
            WaLeaveTypeDo waLeaveTypeDo = leaveTypeDoMap.get(leaveTypeId);
            for (QuotaGenRuleDo ruleDo : ruleList) {
                //条件表达式
                String execSql = getExecSql(ruleDo.getConditionExp(), null, null, null);
                StringBuilder sb = new StringBuilder();
                sb.append("and (").append(execSql).append(")");

                //查询符合条件的员工数据
                Long nowDate = DateUtil.getOnlyDate();
                // 司龄（至年底）计算使用参数
                Long yearEndTime = null;
                try {
                    yearEndTime = DateUtilExt.getYearsEndTime(nowDate);
                } catch (ParseException e) {
                    log.error(e.getMessage(), e);
                    yearEndTime = nowDate;
                }
                String nowTimeStr = nowDate.toString();
                String groupExp = sb.toString().replaceAll("#\\{nowTime\\}", nowTimeStr)
                        .replaceAll("#nowTime#", nowTimeStr)
                        .replaceAll("#\\{nowhireDate\\}", nowTimeStr)
                        .replaceAll("#nowhireDate#", nowTimeStr)
                        .replaceAll("#\\{yearEndTime\\}", yearEndTime.toString());

                groupExp = replaceGroupExp(groupExp);
                Long gender = null;
                Long genderType = waLeaveTypeDo.getGenderType();
                if (genderType != null && genderType != 0) {
                    gender = genderType;
                }
                List<Map> quotaRuleEmpList = sysEmpInfo.getQuotaRuleEmpList(belongOrgId, groupExp, null, null, ruleDo.getLeaveTypeId(), null, gender, null);
                if (CollectionUtils.isNotEmpty(quotaRuleEmpList)) {
                    quotaRuleEmpList = quotaRuleEmpList.stream().filter(o -> empIds.contains(Long.valueOf(o.get("empid").toString()))).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(quotaRuleEmpList)) {
                        log.info("genEmpQuotaForFixedQuota quotaRuleEmpList is empty");
                        return;
                    }
                    for (Map emp : quotaRuleEmpList) {
                        Long empid = (Long) emp.get("empid");
                        if (oldEmpQuotaMap.containsKey(empid + "_" + leaveTypeId)) {
                            continue;
                        }
                        Long hireDate = (Long) emp.get("hireDate");
                        Long systime = System.currentTimeMillis() / 1000;
                        WaEmpQuota empQuota = new WaEmpQuota();
                        empQuota.setBelongOrgId(belongOrgId);
                        empQuota.setCarryMerge(false);
                        empQuota.setConfigId(ruleDo.getConfigId());
                        float quotaDay = ruleDo.getQuotaVal().floatValue();
                        if (LeaveTypeUnitEnum.HOUR.getIndex().equals(waLeaveTypeDo.getAcctTimeType())) {
                            quotaDay = quotaDay * 60;
                        }
                        empQuota.setQuotaDay(quotaDay);
                        empQuota.setNowQuota(quotaDay);
                        empQuota.setOriginalQuotaDay(quotaDay);
                        empQuota.setEmpid(empid);
                        empQuota.setPeriodYear((short) 1);
                        empQuota.setLeaveTypeId(leaveTypeId);
                        if (finalLeaveSettingMap.containsKey(leaveTypeId)) {
                            empQuota.setQuotaSettingId(finalLeaveSettingMap.get(leaveTypeId));
                        }
                        empQuota.setStartDate(hireDate == null ? 1 : DateUtil.getOnlyDate(new Date(hireDate * 1000)));
                        empQuota.setLastDate(253402271999L);
                        empQuota.setDeductionDay(0f);
                        empQuota.setUsedDay(0f);
                        empQuota.setAdjustQuota(0f);
                        empQuota.setRemainDay(0f);
                        empQuota.setRemainUsedDay(0f);
                        empQuota.setCrtuser(userId);
                        empQuota.setCrttime(systime);
                        empQuota.setUpduser(userId);
                        empQuota.setUpdtime(systime);
                        empQuota.setIfAdvance(0);
                        empQuotaAddList.add(empQuota);
                        oldEmpQuotaMap.put(empQuota.getEmpid() + "_" + empQuota.getLeaveTypeId(), empQuota);
                    }
                }
            }
        });
        importService.fastInsertList(WaEmpQuota.class, "empQuotaId", empQuotaAddList);
    }

    @Transactional
    @Override
    public void genEmpQuotaForCompensatoryLeave(String belongOrgId, Long userId, Integer waSobId, Long empId, String dataScope, Long corpId) throws Exception {
        //考勤周期
        WaSobExample waSobExample = new WaSobExample();
        waSobExample.createCriteria().andBelongOrgIdEqualTo(belongOrgId).andWaSobIdEqualTo(waSobId);
        List<WaSob> waSobs = waSobMapper.selectByExample(waSobExample);
        if (CollectionUtils.isEmpty(waSobs)) {
            log.info("genEmpQuotaForCompensatoryLeave getWaSob is empty");
            return;
        }
        WaSob waSob = waSobs.get(0);
        //查询考勤分组
        WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waSob.getWaGroupId());
        if (waGroup == null) {
            log.info("genEmpQuotaForCompensatoryLeave getWaGroup is empty");
            return;
        }
        //查询考勤分析分组
        WaParseGroup parseGroup = waParseGroupMapper.selectByPrimaryKey(waGroup.getParseGroupId());
        if (parseGroup == null) {
            log.info("genEmpQuotaForCompensatoryLeave getWaParseGroup is empty");
            return;
        }
        Long waStartDate = waSob.getStartDate();
        Long waEndDate = waSob.getEndDate();
        Boolean isDefault = waGroup.getIsDefault() != null && waGroup.getIsDefault();
        //查询考勤分组下的人员列表
        List<Long> empList = sysEmpInfo.getEmpIdListByWaGroup(belongOrgId, waEndDate, isDefault, waGroup.getWaGroupId(), waStartDate, waEndDate, dataScope);
        if (CollectionUtils.isEmpty(empList)) {
            log.info("genEmpQuotaForCompensatoryLeave getEmpIdListByWaGroup is empty");
            return;
        }
        log.info("genEmpQuotaForCompensatoryLeave getEmpIdListByWaGroup count={}", empList.size());
        List<EmpCompensatoryQuotaDo> detailAddList = new ArrayList<>();
        List<EmpCompensatoryQuotaDo> detailUpdList = new ArrayList<>();
        List<WaEmpOvertimeDetail> overtimeDetails = Lists.newArrayList();
        List<WaCompensatoryQuotaRecordDo> compensatoryQuotaRecords = Lists.newArrayList();
        //查询公司班次数据
        Map<Integer, WaShiftDef> corpShiftDefMap = waCommonService.getCorpAllShiftDef(belongOrgId);
        //查询考勤分组下的加班分析规则
        List<OverTimeTypeDo> otTypeList = overTimeTypeDo.getOtTypes(belongOrgId, null, null, false);
        if (CollectionUtils.isEmpty(otTypeList)) {
            log.info("genEmpQuotaForCompensatoryLeave 未设置加班分析规则");
            return;
        }
        Map<Integer, OverTimeTypeDo> overTimeTypeDoMap = otTypeList.stream().collect(Collectors.toMap(OverTimeTypeDo::getOvertimeTypeId, Function.identity(), (o1, o2) -> o2));
        //人员集合拆分
        List<List<Long>> result = ListTool.split(empList, 660);
        for (List<Long> empGroupEmpIds : result) {
            //查询每个人的排班数据
            Map<String, Object> shiftMap = new HashMap<>();
            shiftMap.put("belongid", belongOrgId);
            shiftMap.put("startDate", waStartDate - 86400L);
            shiftMap.put("endDate", waEndDate);
            shiftMap.put("anyEmpids", "'{" + StringUtils.join(empGroupEmpIds, ",").concat("}'"));
            Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();
            Map<String, EmpShiftInfo> empShift = waCommonService.getEmpShiftInfoListMaps(shiftMap, empShiftInfoByDateMap, empGroupEmpIds, corpShiftDefMap);
            WaAnalyzDTO waAnalyzDTO = new WaAnalyzDTO();
            waAnalyzDTO.setEmpShift(empShift);
            waAnalyzDTO.setEmpShiftInfoByDateMap(empShiftInfoByDateMap);
            Map<String, Float> detailMap = new HashMap<>();
            //获取加班记录并且计算实际加班时长
            List<OvertimeQuotaDto> overTimeList = getEmpOverTimeList(belongOrgId, empGroupEmpIds, waStartDate, waEndDate,
                    waAnalyzDTO, overTimeTypeDoMap, detailMap, corpShiftDefMap, waGroup);
            if (CollectionUtils.isEmpty(overTimeList)) {
                log.info("genEmpQuotaForCompensatoryLeave 未取到加班记录");
                continue;
            }
            Map<String, List<Integer>> otDetailMap = new HashMap<>();
            Map<Integer, Float> otDetailDurationMap = new HashMap<>();
            for (Map.Entry<String, Float> entry : detailMap.entrySet()) {
                String[] arr = entry.getKey().split("-");
                String key = arr[0];
                Integer detailId = Integer.valueOf(arr[1]);
                if (!otDetailMap.containsKey(key)) {
                    List<Integer> detailIds = new ArrayList<>();
                    detailIds.add(detailId);
                    otDetailMap.put(key, detailIds);
                } else {
                    otDetailMap.get(key).add(detailId);
                }
                otDetailDurationMap.put(detailId, entry.getValue());
            }
            Map<String, EmpCompensatoryQuotaDo> oldQuotaMap = null;
            List<EmpCompensatoryQuotaDo> quotaDoList = empCompensatoryQuotaDo.getQuotaByDate(belongOrgId, empGroupEmpIds, waStartDate, waEndDate, DataSourceEnum.AUTO.name(), Arrays.asList(2, 8));
            if (CollectionUtils.isNotEmpty(quotaDoList)) {
                oldQuotaMap = quotaDoList.stream().collect(Collectors.toMap(o -> o.getEmpId() + "_" + o.getOvertimeDate(), k -> k, (oldValue, newValue) -> newValue));
            }
            //查询转换规则
            List<Long> transferRuleIds = overTimeList.stream().map(OvertimeQuotaDto::getRuleId).distinct().collect(Collectors.toList());
            List<OvertimeTransferRuleDto> transferRules = overtimeTransferRuleService.getOvertimeTransferRuleList(transferRuleIds);
            if (CollectionUtils.isEmpty(transferRules)) {
                log.info("genEmpQuotaForCompensatoryLeave transfer rule is empty");
                continue;
            }
            Map<Long, OvertimeTransferRuleDto> transferRuleMap = transferRules.stream().collect(Collectors.toMap(OvertimeTransferRuleDto::getRuleId, Function.identity(), (v1, v2) -> v1));
            //查询考勤分组下的调休规则
            List<WaLeaveTypeDo> leaveTypes = waLeaveTypeDo.getLeaveTypeByGroupId(belongOrgId, waGroup.getWaGroupId());
            if (CollectionUtils.isEmpty(leaveTypes)) {
                log.info("genEmpQuotaForCompensatoryLeave getLeaveTypeByGroupId is empty");
                return;
            }
            leaveTypes = leaveTypes.stream().filter(r -> QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(r.getQuotaRestrictionType())
                    && QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(r.getQuotaType())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leaveTypes)) {
                log.info("genEmpQuotaForCompensatoryLeave filter leaveTypes is empty");
                return;
            }
            Map<Integer, WaLeaveTypeDo> leaveTypeDoMap = leaveTypes.stream().collect(Collectors.toMap(WaLeaveTypeDo::getLeaveTypeId, Function.identity()));
            List<Integer> leaveTypeIds = leaveTypes.stream().map(WaLeaveTypeDo::getLeaveTypeId).collect(Collectors.toList());
            //查询配额配置信息
            List<LeaveQuotaConfigDo> configDoList = leaveQuotaConfigDo.getConfigListByIds(belongOrgId, leaveTypeIds);
            if (CollectionUtils.isEmpty(configDoList)) {
                log.info("genEmpQuotaForCompensatoryLeave 查询配额配置信息 is empty");
                return;
            }
            List<String> businessKeys = configDoList.stream().map(c -> c.getConfigId().toString()).collect(Collectors.toList());
            Map<Long, EmployeeGroupDto> leaveQuotaConfigMap = leaveTypeService.getGroupRuleMap(belongOrgId, businessKeys);
            //根据加班单生成调休配额明细数据
            EmpCompensatoryQuotaDo compensatoryQuotaDo = null;
            for (OvertimeQuotaDto ot : overTimeList) {
                //加班单归属日期
                long otDate = DateUtil.getOnlyDate(new Date(ot.getStartTime() * 1000L));
                //筛选加班单所属日期有效期范围内的调休假额度规则
                List<LeaveQuotaConfigDo> validLeaveQuotaConfig = configDoList.stream().filter(config -> config.getRuleStartDate() <= otDate
                        && otDate < config.getRuleEndDate() && config.getLeaveTypeId().equals(ot.getLeaveTypeId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(validLeaveQuotaConfig)) {
                    log.info("根据加班日期未取到有效期内的调休额度规则");
                    continue;
                }
                LeaveQuotaConfigDo configDo = null;
                //根据有效期范围内的调休假额度规则的员工分组规则判断额度规则是否适用该加班单所属员工
                for (LeaveQuotaConfigDo quotaConfig : validLeaveQuotaConfig) {
                    if (leaveQuotaConfigMap.containsKey(quotaConfig.getConfigId())) {
                        EmployeeGroupDto groupDto = leaveQuotaConfigMap.get(quotaConfig.getConfigId());
                        List<Long> empIds = sysEmpInfo.getEmpIdsByGroupExp(corpId, groupDto.getGroupExp(), quotaConfig.getGroupExpCondition());
                        if (CollectionUtils.isNotEmpty(empIds) && empIds.contains(ot.getEmpid())) {
                            configDo = quotaConfig;
                            break;
                        }
                    }
                }
                if (null == configDo) {
                    log.info("根据加班日期取得有效期内的调休额度规则未匹配到对应加班单的员工");
                    continue;
                }
                //有效期类型
                Integer validityPeriodType = configDo.getValidityPeriodType();
                Integer validityStartType = configDo.getValidityStartType();
                Integer validityUnit = configDo.getValidityUnit();
                Float validityDuration = configDo.getValidityDuration();
                //1、固定有效期，2、当年失效，3、次年失效
                Integer invalidType = configDo.getInvalidType();
                //失效日期
                String invalidDate = configDo.getInvalidDate();
                //失效日期是否延长至失效月月底
                Boolean validityExtension = configDo.getValidityExtension();
                WaLeaveTypeDo waLeaveType = leaveTypeDoMap.get(configDo.getLeaveTypeId());
                String key = ot.getEmpid() + "_" + ot.getBelongDate();
                //查询加班类型
                OverTimeTypeDo overTimeTypeDo = overTimeTypeDoMap.get(ot.getOvertimeTypeId());
                if (overTimeTypeDo == null) {
                    log.info("genEmpQuotaForCompensatoryLeave 获取加班类型失败 dateType : {}", ot.getDateType());
                    continue;
                }
                compensatoryQuotaDo = new EmpCompensatoryQuotaDo();
                compensatoryQuotaDo.setConfigId(configDo.getConfigId());
                compensatoryQuotaDo.setEmpId(ot.getEmpid());
                compensatoryQuotaDo.setOvertimeDate(ot.getBelongDate());
                compensatoryQuotaDo.setOvertimeDetailId(ot.getOvertimeDetailId());
                Float minOvertimeUnit = overTimeTypeDo.getMinOvertimeUnit();
                Float realOvertimeDuration = ot.getRealOvertimeDuration();
                if (minOvertimeUnit != null) {
                    float duration = ot.getRealOvertimeDuration() * 60;
                    Integer minOvertimeUnitType = Optional.ofNullable(overTimeTypeDo.getMinOvertimeUnitType()).orElse(2);
                    duration = OvertimeUnitEnum.HOUR.getTime(null, duration, minOvertimeUnit, minOvertimeUnitType) / 60;
                    realOvertimeDuration = duration;
                }
                compensatoryQuotaDo.setOvertimeDuration(realOvertimeDuration);
                compensatoryQuotaDo.setOvertimeType(ot.getDateType());
                compensatoryQuotaDo.setOvertimeUnit(LeaveTypeUnitEnum.HOUR.getIndex());
                compensatoryQuotaDo.setLeaveTypeId(waLeaveType.getLeaveTypeId());
                compensatoryQuotaDo.setQuotaUnit(waLeaveType.getAcctTimeType());
                compensatoryQuotaDo.setDeleted(0);
                compensatoryQuotaDo.setStatus(ApprovalStatusEnum.PASSED.getIndex());
                compensatoryQuotaDo.setTenantId(belongOrgId);
                //计算加班转调休时长
                BigDecimal otNum = BigDecimal.valueOf(realOvertimeDuration);
                //根据加班转换规则进行转换
                if (otNum.floatValue() > 0 && transferRuleMap.containsKey(ot.getRuleId())) {
                    OvertimeTransferRuleDto otRule = transferRuleMap.get(ot.getRuleId());
                    TransferRuleEnum transferRuleEnum = TransferRuleEnum.getTransferRuleEnum(otRule.getTransferRule());
                    if (null != transferRuleEnum) {
                        Map<String, Object> durMap = transferRuleEnum.calTimeDuration(otNum.floatValue(), otRule.getTransferPeriods(), otRule.getTransferTime());
                        Integer unit = new BigDecimal(durMap.get("unit").toString()).intValue();
                        if (unit == 1) {
                            otNum = new BigDecimal(durMap.get("duration").toString());
                        } else {
                            otNum = new BigDecimal(durMap.get("duration").toString()).multiply(BigDecimal.valueOf(60));
                        }
                    }
                }
                if (otNum.floatValue() <= 0) {
                    log.info("genEmpQuotaForCompensatoryLeave ID：{} 加班单有效时长为0", ot.getOvertimeDetailId());
                    continue;
                }
                compensatoryQuotaDo.setWorkingTime(waGroup.getWorkingTime().floatValue());
                //时长折算、舍位
                compensatoryQuotaDo.setQuotaDay(otNum.floatValue());
                if (ValidityPeriodTypeEnum.RESTRICTION.getIndex().equals(validityPeriodType)) {
                    compensatoryQuotaDo.setValidityPeriodType(validityStartType);
                    //限制
                    Long startDate = 1L;
                    if (ValidityStartTypeEnum.NATURAL_YEAR.getIndex().equals(validityStartType)) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(new Date(ot.getStartTime() * 1000));
                        calendar.set(calendar.get(Calendar.YEAR), 0, 1, 0, 0, 0);
                        startDate = calendar.getTime().getTime() / 1000;
                    } else if (ValidityStartTypeEnum.OVERTIME_DATE.getIndex().equals(validityStartType)) {
                        startDate = ot.getBelongDate();
                    } else if (ValidityStartTypeEnum.OVERTIME_MONTH.getIndex().equals(validityStartType)) {
                        startDate = DateUtilExt.getMonthBegin(ot.getBelongDate());
                    }
                    compensatoryQuotaDo.setStartDate(startDate);
                    Long lastDate = 0L;
                    if (InvalidTypeEnum.FIXED.getIndex().equals(invalidType)) {
                        if (validityUnit.equals(ValidityUnitEnum.DAY.getIndex())) {
                            Integer time = validityDuration.intValue() <= 0 ? 0 : validityDuration.intValue() - 1;
                            lastDate = DateUtilExt.getAddYearOrMonthOrDateEndTime(compensatoryQuotaDo.getStartDate() * 1000, 0, 0, time);
                        } else if (validityUnit.equals(ValidityUnitEnum.MONTH.getIndex())) {
                            lastDate = DateUtilExt.getAddYearOrMonthOrDateEndTime(compensatoryQuotaDo.getStartDate() * 1000, 0, validityDuration.intValue(), -1);
                        } else if (validityUnit.equals(ValidityUnitEnum.YEAR.getIndex())) {
                            lastDate = DateUtilExt.getAddYearOrMonthOrDateEndTime(compensatoryQuotaDo.getStartDate() * 1000, validityDuration.intValue(), 0, -1);
                        }
                        if (null != validityExtension && validityExtension) {
                            lastDate = DateUtilExt.getMonthEnd(lastDate);
                        }
                    } else if (InvalidTypeEnum.CURRENT_YEAR.getIndex().equals(invalidType)) {
                        lastDate = DateUtil.convertStringToDateTime(String.format("%s%s", DateUtilExt.getTimeYear(compensatoryQuotaDo.getStartDate()), invalidDate), "yyyyMMdd", true) + 86399;
                        //加班日期大于失效日期则不生成额度
                        if (lastDate < compensatoryQuotaDo.getOvertimeDate()) {
                            continue;
                        }
                    } else {
                        lastDate = DateUtil.convertStringToDateTime(String.format("%s%s", DateUtilExt.getTimeYear(compensatoryQuotaDo.getStartDate()), invalidDate), "yyyyMMdd", true);
                        lastDate = DateUtilExt.addYear(lastDate, 1) + 86399;
                    }
                    compensatoryQuotaDo.setLastDate(lastDate);
                    // 失效日期小于等生效日期则不生成额度
                    if (lastDate <= startDate) {
                        continue;
                    }
                } else {
                    compensatoryQuotaDo.setValidityPeriodType(0);
                    compensatoryQuotaDo.setStartDate(1L);
                    compensatoryQuotaDo.setLastDate(253402271999L);
                }
                //老数据处理
                Long sysTime = System.currentTimeMillis() / 1000;
                if (oldQuotaMap != null && oldQuotaMap.containsKey(key)) {
                    EmpCompensatoryQuotaDo oldQuota = oldQuotaMap.get(key);
                    if (oldQuota.getStatus() == 8) {
                        compensatoryQuotaDo.setStatus(oldQuota.getStatus());
                    }
                    compensatoryQuotaDo.setUsedDay(oldQuota.getUsedDay() == null ? 0f : oldQuota.getUsedDay());
                    compensatoryQuotaDo.setInTransitQuota(oldQuota.getInTransitQuota() == null ? 0f : oldQuota.getInTransitQuota());
                    compensatoryQuotaDo.setQuotaId(oldQuota.getQuotaId());
                    compensatoryQuotaDo.setUpdateBy(userId);
                    compensatoryQuotaDo.setUpdateTime(sysTime);
                    detailUpdList.add(compensatoryQuotaDo);
                } else {
                    compensatoryQuotaDo.setQuotaId(snowflakeUtil.createId());
                    compensatoryQuotaDo.setCreateBy(userId);
                    compensatoryQuotaDo.setCreateTime(sysTime);
                    compensatoryQuotaDo.setUpdateBy(userId);
                    compensatoryQuotaDo.setUpdateTime(sysTime);
                    compensatoryQuotaDo.setDataSource(DataSourceEnum.AUTO.name());
                    if (compensatoryQuotaDo.getUsedDay() == null) {
                        compensatoryQuotaDo.setUsedDay(0f);
                    }
                    if (compensatoryQuotaDo.getInTransitQuota() == null) {
                        compensatoryQuotaDo.setInTransitQuota(0f);
                    }
                    if (compensatoryQuotaDo.getAdjustQuotaDay() == null) {
                        compensatoryQuotaDo.setAdjustQuotaDay(0f);
                    }
                    detailAddList.add(compensatoryQuotaDo);
                }
                String otDetailKey = String.format("%s_%s", ot.getEmpid(), ot.getBelongDate());
                if (otDetailMap.containsKey(otDetailKey)) {
                    for (Integer detailId : otDetailMap.get(otDetailKey)) {
                        Float duration = Optional.ofNullable(otDetailDurationMap.get(detailId)).orElse(0f);
                        if (duration > 0) {
                            WaCompensatoryQuotaRecordDo compensatoryQuotaRecord = getWaCompensatoryQuotaRecordDo(belongOrgId, duration, detailId, userId);
                            compensatoryQuotaRecord.setQuotaId(compensatoryQuotaDo.getQuotaId());
                            compensatoryQuotaRecords.add(compensatoryQuotaRecord);
                            WaEmpOvertimeDetail detail = new WaEmpOvertimeDetail();
                            detail.setDetailId(detailId);
                            detail.setLeftDuration(0f);
                            detail.setCarriedForward(2);
                            overtimeDetails.add(detail);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(detailAddList)) {
            empCompensatoryQuotaDo.save(detailAddList);
        }
        if (CollectionUtils.isNotEmpty(detailUpdList)) {
            List<Long> quotaIds = detailUpdList.stream().map(EmpCompensatoryQuotaDo::getQuotaId).collect(Collectors.toList());
            List<List<Long>> lists = ListTool.split(quotaIds, 100);
            for (List<Long> list : lists) {
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                compensatoryQuotaRecordDo.deleteByQuotaIds(belongOrgId, list);
            }
            empCompensatoryQuotaDo.batchUpdate(detailUpdList);
        }
        //更新加班单详情
        if (CollectionUtils.isNotEmpty(overtimeDetails)) {
            importService.fastUpdList(WaEmpOvertimeDetail.class, "detailId", overtimeDetails);
        }
        //保存转换记录
        if (CollectionUtils.isNotEmpty(compensatoryQuotaRecords)) {
            List<List<WaCompensatoryQuotaRecordDo>> lists = ListTool.split(compensatoryQuotaRecords, 100);
            for (List<WaCompensatoryQuotaRecordDo> list : lists) {
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                compensatoryQuotaRecordDo.save(list);
            }
        }
        log.info("genEmpQuotaForCompensatoryLeave success");
        log.info("genEmpQuotaForCompensatory start");
        genEmpQuotaForCompensatory(belongOrgId, corpId, userId, empId, corpShiftDefMap, waGroup);
        log.info("genEmpQuotaForCompensatory success");
    }

    public void genEmpQuotaForCompensatory(String tenantId, Long corpId, Long userId, Long genEmpId,
                                           Map<Integer, WaShiftDef> corpShiftDefMap, WaGroup waGroup) throws Exception {
        //查询加班类型（规则）
        List<OverTimeTypeDo> otTypeList = overTimeTypeDo.getOtTypes(tenantId, null, null, true);
        if (CollectionUtils.isEmpty(otTypeList)) {
            log.info("genEmpQuotaForCompensatory 未设置加班分析规则");
            return;
        }
        List<Integer> overtimeTypeIds = otTypeList.stream().map(OverTimeTypeDo::getOvertimeTypeId).distinct().collect(Collectors.toList());
        List<Long> genEmpIds = Lists.newArrayList();
        if (null != genEmpId) {
            genEmpIds.add(genEmpId);
        }
        //获取加班记录并且计算实际加班时长
        List<Map> listOt = workOvertimeMapper.getEmpOtList(tenantId, null, null, genEmpIds, overtimeTypeIds, true);
        if (CollectionUtils.isEmpty(listOt)) {
            log.info("genEmpQuotaForCompensatory 未查到符合条件的加班单");
            return;
        }
        Map<Object, List<Map>> overtimeMap = listOt.stream().collect(Collectors.groupingBy(row -> row.get("otId")));
        overtimeMap.forEach((otId, otList) -> {
            if (otList.size() == 2) {
                otList = otList.stream().sorted(Comparator.comparing(ot -> (Long) ot.get("endTime"))).collect(Collectors.toList());
                Map ot = otList.get(1);
                ot.put("zeroSplitting", true);
            }
        });
        List<OvertimeQuotaDto> overtimeQuotaList = JSONArray.parseArray(JSONArray.toJSONString(listOt), OvertimeQuotaDto.class);
        overtimeQuotaList = overtimeQuotaList.stream().filter(overtime -> Optional.ofNullable(overtime.getCarriedForward()).orElse(0) == 0).collect(Collectors.toList());
        //查询加班转换规则
        List<Long> transferRuleIds = overtimeQuotaList.stream().map(OvertimeQuotaDto::getRuleId).distinct().collect(Collectors.toList());
        List<OvertimeTransferRuleDto> transferRules = overtimeTransferRuleService.getOvertimeTransferRuleList(transferRuleIds);
        if (CollectionUtils.isEmpty(transferRules)) {
            log.info("genEmpQuotaForCompensatory transfer rule is empty");
            return;
        }
        //查询考勤分组下的休假类型（规则）
        List<Integer> leaveTypeIds = transferRules.stream().map(OvertimeTransferRuleDto::getLeaveTypeId).distinct().collect(Collectors.toList());
        List<WaLeaveTypeDo> leaveTypes = waLeaveTypeDo.getLeaveTypesByIds(tenantId, leaveTypeIds);
        if (CollectionUtils.isEmpty(leaveTypes)) {
            log.info("genEmpQuotaForCompensatory get leaveTypes is empty");
            return;
        }
        leaveTypes = leaveTypes.stream().filter(r -> QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(r.getQuotaRestrictionType()) && QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(r.getQuotaType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaveTypes)) {
            log.info("genEmpQuotaForCompensatory filter leaveTypes is empty");
            return;
        }
        //查询配额配置信息
        List<LeaveQuotaConfigDo> configDoList = leaveQuotaConfigDo.getConfigListByIds(tenantId, leaveTypeIds);
        if (CollectionUtils.isEmpty(configDoList)) {
            log.info("genEmpQuotaForCompensatory query quota config rule is empty");
            return;
        }
        List<String> businessKeys = configDoList.stream().map(c -> c.getConfigId().toString()).collect(Collectors.toList());
        //查询额度规则
        Map<Long, EmployeeGroupDto> leaveQuotaConfigMap = leaveTypeService.getGroupRuleMap(tenantId, businessKeys);
        //休假类型（规则）
        Map<Integer, WaLeaveTypeDo> leaveTypeMap = leaveTypes.stream().collect(Collectors.toMap(WaLeaveTypeDo::getLeaveTypeId, Function.identity()));
        //加班转换规则
        Map<Long, OvertimeTransferRuleDto> transferRuleMap = transferRules.stream().collect(Collectors.toMap(OvertimeTransferRuleDto::getRuleId, Function.identity(), (v1, v2) -> v1));
        //加班类型（规则）
        Map<Integer, OverTimeTypeDo> overtimeTypeMap = otTypeList.stream().collect(Collectors.toMap(OverTimeTypeDo::getOvertimeTypeId, Function.identity(), (o1, o2) -> o2));
        //按照加班类型分组
        Map<Integer, List<OvertimeQuotaDto>> overtimeGroupByOvertimeTypeMap = overtimeQuotaList.stream().collect(Collectors.groupingBy(OvertimeQuotaDto::getOvertimeTypeId));
        List<EmpCompensatoryQuotaDo> detailAddList = Lists.newArrayList();
        List<WaEmpOvertimeDetail> overtimeDetails = Lists.newArrayList();
        List<WaCompensatoryQuotaRecordDo> compensatoryQuotaRecords = Lists.newArrayList();
        for (Map.Entry<Integer, List<OvertimeQuotaDto>> overtimeTypeEntry : overtimeGroupByOvertimeTypeMap.entrySet()) {
            Integer overtimeTypeId = overtimeTypeEntry.getKey();
            if (!overtimeTypeMap.containsKey(overtimeTypeId)) {
                log.error("overtime type not exist,tenantId:{} overtimeTypeId:{}", tenantId, overtimeTypeId);
                continue;
            }
            //加班规则
            OverTimeTypeDo overTimeType = overtimeTypeMap.get(overtimeTypeId);
            if (!transferRuleMap.containsKey(overTimeType.getRuleId())) {
                log.error("overtime transfer rule not exist,tenantId:{} overtimeTypeId:{} ruleId:{}", tenantId, overtimeTypeId, overTimeType.getRuleId());
                continue;
            }
            //加班转换规则
            OvertimeTransferRuleDto overtimeTransferRule = transferRuleMap.get(overTimeType.getRuleId());
            if (!leaveTypeMap.containsKey(overtimeTransferRule.getLeaveTypeId())) {
                log.error("leaveType not exist,tenantId:{} overtimeTypeId:{} ruleId:{} leaveTypeId:{}", tenantId, overtimeTypeId, overTimeType.getRuleId(), overtimeTransferRule.getLeaveTypeId());
                continue;
            }
            //假期规则
            WaLeaveTypeDo leaveType = leaveTypeMap.get(overtimeTransferRule.getLeaveTypeId());
            long startDateTime = overtimeTypeEntry.getValue().stream().min(Comparator.comparing(OvertimeQuotaDto::getBelongDate)).get().getBelongDate();
            long endDateTime = overtimeTypeEntry.getValue().stream().max(Comparator.comparing(OvertimeQuotaDto::getBelongDate)).get().getBelongDate();
            endDateTime += 86399;
            List<Long> empIds = overtimeTypeEntry.getValue().stream().map(OvertimeQuotaDto::getEmpid).distinct().collect(Collectors.toList());
            //获取加班记录并且计算实际加班时长
            WaAnalyzDTO waAnalyzeDTO = getWaAnalyzeDTO(tenantId, startDateTime, endDateTime, empIds, corpShiftDefMap);
            List<OvertimeQuotaDto> overtimeList = getOvertimeQuotaList(tenantId, startDateTime, endDateTime, empIds,
                    overtimeTypeEntry.getValue(), overtimeTypeMap, waAnalyzeDTO, false, null,
                    corpShiftDefMap, waGroup);
            for (OvertimeQuotaDto overtimeQuota : overtimeList) {
                if (overtimeQuota.getLeftDuration() > 0) {//结转过，未结转完
                    BigDecimal diff = BigDecimal.valueOf(overtimeQuota.getRealOvertimeDuration()).subtract(BigDecimal.valueOf(overtimeQuota.getRelTimeDuration()));
                    overtimeQuota.setLeftDuration(overtimeQuota.getLeftDuration() + diff.floatValue());
                    if (overtimeQuota.getLeftDuration() < 0) {
                        overtimeQuota.setLeftDuration(0f);
                    }
                } else {//未结转过
                    overtimeQuota.setLeftDuration(overtimeQuota.getRealOvertimeDuration());
                }
            }
            //按照员工分组
            Map<Long, List<OvertimeQuotaDto>> overtimeGroupByEmpIdMap = overtimeList.stream().collect(Collectors.groupingBy(OvertimeQuotaDto::getEmpid));
            for (Map.Entry<Long, List<OvertimeQuotaDto>> empEntry : overtimeGroupByEmpIdMap.entrySet()) {
                Long empId = empEntry.getKey();
                List<OvertimeQuotaDto> empOvertimeList = empEntry.getValue();
                empOvertimeList = empOvertimeList.stream().sorted(Comparator.comparing(OvertimeQuotaDto::getStartTime)).collect(Collectors.toList());
                //留存加班时长
                BigDecimal totalLeftDuration = BigDecimal.valueOf(empOvertimeList.stream().mapToDouble(OvertimeQuotaDto::getLeftDuration).sum());
                //转换调休加班时长比例单位
                BigDecimal timeDuration = BigDecimal.valueOf(Optional.ofNullable(overtimeTransferRule.getTimeDuration()).orElse(1f) * 60);
                if (totalLeftDuration.subtract(timeDuration).floatValue() < 0) {
                    log.error("overtime carry forward timeDuration is 0, tenantId:{}, empId:{}", tenantId, empId);
                    continue;
                }
                Long startTime = null;
                //倍数
                int multiple = totalLeftDuration.divide(timeDuration, 0, RoundingMode.DOWN).intValue();
                //将要结转的时长
                BigDecimal carryForwardDuration = timeDuration.multiply(BigDecimal.valueOf(multiple));
                float totalCarryForwardDuration = carryForwardDuration.floatValue();
                List<WaCompensatoryQuotaRecordDo> quotaRecords = Lists.newArrayList();
                List<WaEmpOvertimeDetail> currentOvertimeDetails = Lists.newArrayList();
                for (OvertimeQuotaDto overtimeQuota : empOvertimeList) {
                    WaEmpOvertimeDetail detail = new WaEmpOvertimeDetail();
                    detail.setDetailId(overtimeQuota.getOvertimeDetailId());
                    detail.setRelTimeDuration(overtimeQuota.getRealOvertimeDuration());
                    detail.setLeftDuration(overtimeQuota.getLeftDuration());
                    detail.setCarriedForward(detail.getLeftDuration() == 0 ? 1 : 0);
                    currentOvertimeDetails.add(detail);
                    if (carryForwardDuration.floatValue() <= 0) {
                        break;
                    }
                    if (carryForwardDuration.floatValue() > overtimeQuota.getLeftDuration()) {
                        startTime = overtimeQuota.getStartTime();
                        carryForwardDuration = carryForwardDuration.subtract(BigDecimal.valueOf(overtimeQuota.getLeftDuration()));
                        quotaRecords.add(getWaCompensatoryQuotaRecordDo(tenantId, overtimeQuota.getLeftDuration(), detail.getDetailId(), userId));
                        overtimeQuota.setLeftDuration(0f);
                    } else {
                        startTime = overtimeQuota.getStartTime();
                        overtimeQuota.setLeftDuration(BigDecimal.valueOf(overtimeQuota.getLeftDuration()).subtract(carryForwardDuration).floatValue());
                        quotaRecords.add(getWaCompensatoryQuotaRecordDo(tenantId, carryForwardDuration.floatValue(), detail.getDetailId(), userId));
                        carryForwardDuration = BigDecimal.ZERO;
                    }
                    detail.setLeftDuration(overtimeQuota.getLeftDuration());
                    detail.setCarriedForward(detail.getLeftDuration() == 0 ? 1 : 0);
                }
                if (null == startTime) {
                    log.info("没有可结转的加班时长");
                    continue;
                }
                //加班单归属日期
                long otDate = DateUtil.getOnlyDate(new Date(startTime * 1000L));
                //筛选加班单所属日期有效期范围内的调休假额度规则
                List<LeaveQuotaConfigDo> validLeaveQuotaConfig = configDoList.stream().filter(config -> config.getRuleStartDate() <= otDate
                        && otDate < config.getRuleEndDate() && config.getLeaveTypeId().equals(leaveType.getLeaveTypeId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(validLeaveQuotaConfig)) {
                    log.info("根据加班日期未取到有效期内的调休额度规则");
                    continue;
                }
                LeaveQuotaConfigDo configDo = null;
                //根据有效期范围内的调休假额度规则的员工分组规则判断额度规则是否适用该加班单所属员工
                for (LeaveQuotaConfigDo quotaConfig : validLeaveQuotaConfig) {
                    if (leaveQuotaConfigMap.containsKey(quotaConfig.getConfigId())) {
                        EmployeeGroupDto groupDto = leaveQuotaConfigMap.get(quotaConfig.getConfigId());
                        List<Long> applicableEmployees = sysEmpInfo.getEmpIdsByGroupExp(corpId, groupDto.getGroupExp(), quotaConfig.getGroupExpCondition());
                        if (CollectionUtils.isNotEmpty(applicableEmployees) && applicableEmployees.contains(empId)) {
                            configDo = quotaConfig;
                            break;
                        }
                    }
                }
                if (null == configDo) {
                    log.info("根据加班日期取得有效期内的调休额度规则未匹配到对应加班单的员工");
                    continue;
                }
                //加班转调休额度
                BigDecimal transferTime = BigDecimal.valueOf(overtimeTransferRule.getTransferTime() * multiple);
                //有效期类型
                Integer validityPeriodType = configDo.getValidityPeriodType();
                Integer validityStartType = configDo.getValidityStartType();
                Integer validityUnit = configDo.getValidityUnit();
                Float validityDuration = configDo.getValidityDuration();
                //1、固定有效期，2、当年失效，3、次年失效
                Integer invalidType = configDo.getInvalidType();
                //失效日期
                String invalidDate = configDo.getInvalidDate();
                //失效日期是否延长至失效月月底
                Boolean validityExtension = configDo.getValidityExtension();
                //根据加班单生成调休配额明细数据
                EmpCompensatoryQuotaDo quota = new EmpCompensatoryQuotaDo();
                quota.setQuotaId(snowflakeUtil.createId());
                quota.setConfigId(configDo.getConfigId());
                quota.setEmpId(empId);
                quota.setOvertimeDate(otDate);
                Float minOvertimeUnit = overTimeType.getMinOvertimeUnit();
                //合计加班时长
                Float realOvertimeDuration = totalCarryForwardDuration;
                if (minOvertimeUnit != null) {
                    float duration = realOvertimeDuration * 60;
                    Integer minOvertimeUnitType = Optional.ofNullable(overTimeType.getMinOvertimeUnitType()).orElse(2);
                    duration = OvertimeUnitEnum.HOUR.getTime(null, duration, minOvertimeUnit, minOvertimeUnitType) / 60;
                    realOvertimeDuration = duration;
                }
                quota.setOvertimeDuration(realOvertimeDuration);
                quota.setOvertimeType(overTimeType.getDateType());
                quota.setOvertimeUnit(LeaveTypeUnitEnum.HOUR.getIndex());
                quota.setLeaveTypeId(leaveType.getLeaveTypeId());
                quota.setQuotaUnit(leaveType.getAcctTimeType());
                quota.setDeleted(0);
                quota.setStatus(ApprovalStatusEnum.PASSED.getIndex());
                quota.setTenantId(tenantId);
                //计算加班转调休时长
                BigDecimal otNum = transferTime;
                if (PreTimeUnitEnum.HOUR.getIndex().equals(leaveType.getAcctTimeType())) {
                    otNum = otNum.multiply(BigDecimal.valueOf(60));
                }
                if (otNum.floatValue() <= 0) {
                    log.info("genEmpQuotaForCompensatoryLeave 加班单有效时长为0");
                    continue;
                }
                overtimeDetails.addAll(currentOvertimeDetails);
                quotaRecords.forEach(q -> q.setQuotaId(quota.getQuotaId()));
                compensatoryQuotaRecords.addAll(quotaRecords);
                //时长折算、舍位
                quota.setQuotaDay(otNum.floatValue());
                if (ValidityPeriodTypeEnum.RESTRICTION.getIndex().equals(validityPeriodType)) {
                    quota.setValidityPeriodType(validityStartType);
                    //限制
                    Long startDate = 1L;
                    if (ValidityStartTypeEnum.NATURAL_YEAR.getIndex().equals(validityStartType)) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(new Date(otDate * 1000));
                        calendar.set(calendar.get(Calendar.YEAR), 0, 1, 0, 0, 0);
                        startDate = calendar.getTime().getTime() / 1000;
                    } else if (ValidityStartTypeEnum.OVERTIME_DATE.getIndex().equals(validityStartType)) {
                        startDate = otDate;
                    } else if (ValidityStartTypeEnum.OVERTIME_MONTH.getIndex().equals(validityStartType)) {
                        startDate = DateUtilExt.getMonthBegin(otDate);
                    }
                    quota.setStartDate(startDate);
                    Long lastDate = 0L;
                    if (InvalidTypeEnum.FIXED.getIndex().equals(invalidType)) {
                        if (validityUnit.equals(ValidityUnitEnum.DAY.getIndex())) {
                            Integer time = validityDuration.intValue() <= 0 ? 0 : validityDuration.intValue() - 1;
                            lastDate = DateUtilExt.getAddYearOrMonthOrDateEndTime(quota.getStartDate() * 1000, 0, 0, time);
                        } else if (validityUnit.equals(ValidityUnitEnum.MONTH.getIndex())) {
                            lastDate = DateUtilExt.getAddYearOrMonthOrDateEndTime(quota.getStartDate() * 1000, 0, validityDuration.intValue(), -1);
                        } else if (validityUnit.equals(ValidityUnitEnum.YEAR.getIndex())) {
                            lastDate = DateUtilExt.getAddYearOrMonthOrDateEndTime(quota.getStartDate() * 1000, validityDuration.intValue(), 0, -1);
                        }
                        if (null != validityExtension && validityExtension) {
                            lastDate = DateUtilExt.getMonthEnd(lastDate);
                        }
                    } else if (InvalidTypeEnum.CURRENT_YEAR.getIndex().equals(invalidType)) {
                        lastDate = DateUtil.convertStringToDateTime(String.format("%s%s", DateUtilExt.getTimeYear(quota.getStartDate()), invalidDate), "yyyyMMdd", true) + 86399;
                        //加班日期大于失效日期则不生成额度
                        if (lastDate < quota.getOvertimeDate()) {
                            continue;
                        }
                    } else {
                        lastDate = DateUtil.convertStringToDateTime(String.format("%s%s", DateUtilExt.getTimeYear(quota.getStartDate()), invalidDate), "yyyyMMdd", true);
                        lastDate = DateUtilExt.addYear(lastDate, 1) + 86399;
                    }
                    quota.setLastDate(lastDate);
                    // 失效日期小于等生效日期则不生成额度
                    if (lastDate <= startDate) {
                        continue;
                    }
                } else {
                    quota.setValidityPeriodType(0);
                    quota.setStartDate(1L);
                    quota.setLastDate(253402271999L);
                }
                Long sysTime = System.currentTimeMillis() / 1000;
                quota.setCreateBy(userId);
                quota.setCreateTime(sysTime);
                quota.setUpdateBy(userId);
                quota.setUpdateTime(sysTime);
                quota.setDataSource(DataSourceEnum.AUTO.name());
                if (quota.getUsedDay() == null) {
                    quota.setUsedDay(0f);
                }
                if (quota.getInTransitQuota() == null) {
                    quota.setInTransitQuota(0f);
                }
                if (quota.getAdjustQuotaDay() == null) {
                    quota.setAdjustQuotaDay(0f);
                }
                detailAddList.add(quota);
            }
        }
        //保存调休配额
        if (CollectionUtils.isNotEmpty(detailAddList)) {
            List<List<EmpCompensatoryQuotaDo>> lists = ListTool.split(detailAddList, 100);
            for (List<EmpCompensatoryQuotaDo> list : lists) {
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                int num = empCompensatoryQuotaDo.save(list);
                log.info("save compensatory quota size:{}", num);
            }
        }
        //保存转换记录
        if (CollectionUtils.isNotEmpty(compensatoryQuotaRecords)) {
            List<List<WaCompensatoryQuotaRecordDo>> lists = ListTool.split(compensatoryQuotaRecords, 100);
            for (List<WaCompensatoryQuotaRecordDo> list : lists) {
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                int num = compensatoryQuotaRecordDo.save(list);
                log.info("save compensatory quota record size:{}", num);
            }
        }
        //更新加班单详情
        if (CollectionUtils.isNotEmpty(overtimeDetails)) {
            importService.fastUpdList(WaEmpOvertimeDetail.class, "detailId", overtimeDetails);
        }
        log.info("genEmpQuotaForCompensatory success");
    }

    private WaCompensatoryQuotaRecordDo getWaCompensatoryQuotaRecordDo(String tenantId, Float duration, Integer detailId, Long userId) {
        WaCompensatoryQuotaRecordDo record = new WaCompensatoryQuotaRecordDo();
        record.setId(snowflakeUtil.createId());
        record.setTenantId(tenantId);
        record.setDetailId(detailId);
        record.setCarryDuration(duration);
        record.setStatus(ApprovalStatusEnum.PASSED.getIndex());
        record.setDeleted(0);
        record.setCreateBy(userId);
        record.setCreateTime(DateUtil.getCurrentTime(true));
        return record;
    }

    private WaAnalyzDTO getWaAnalyzeDTO(String tenantId, Long startDate, Long endDate, List<Long> empIds, Map<Integer, WaShiftDef> corpShiftDefMap) {
        if (CollectionUtils.isEmpty(empIds)) {
            return new WaAnalyzDTO();
        }
        List<List<Long>> empIdList = ListTool.split(empIds, 500);
        Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();
        Map<String, EmpShiftInfo> empShift = new HashMap<>();
        for (List<Long> rows : empIdList) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            //查询每个人的排班数据
            Map<String, Object> shiftMap = new HashMap<>();
            shiftMap.put("belongid", tenantId);
            shiftMap.put("startDate", startDate);
            shiftMap.put("endDate", endDate);
            shiftMap.put("anyEmpids", "'{" + StringUtils.join(rows, ",").concat("}'"));
            Map<String, EmpShiftInfo> empShiftInfoByDateRowMap = new HashMap<>();
            Map<String, EmpShiftInfo> empShiftRow = waCommonService.getEmpShiftInfoListMaps(shiftMap, empShiftInfoByDateRowMap, rows, corpShiftDefMap);
            empShift.putAll(empShiftRow);
            empShiftInfoByDateMap.putAll(empShiftInfoByDateRowMap);
        }
        WaAnalyzDTO dto = new WaAnalyzDTO();
        dto.setEmpShift(empShift);
        dto.setEmpShiftInfoByDateMap(empShiftInfoByDateMap);
        return dto;
    }

    @Override
    public void deleteCompensatoryQuota(Long quotaId) {
        val quota = empCompensatoryQuotaDo.getByQuotaId(quotaId);
        if (quota == null) {
            //throw new ServerException("调休明细不存在");
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATORY_DETAIL_NOT_EXIST, null).getMsg());
        }
        if (quota.getUsedDay() != null && quota.getUsedDay() > 0) {
            //throw new ServerException("该配额已被使用，不可删除");
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_USED_DELETE_NOT_ALLOW, null).getMsg());
        }
        if (quota.getInTransitQuota() != null && quota.getInTransitQuota() > 0) {
            //throw new ServerException("该配额已被使用，不可删除");
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_USED_DELETE_NOT_ALLOW, null).getMsg());
        }
        empCompensatoryQuotaDo.delete(quotaId);
    }

    /**
     * 获取加班记录并且计算实际加班时长
     *
     * @param tenantId
     * @param empIds
     * @param startDate
     * @param endDate
     * @param waAnalyzDTO
     * @param overTimeDateTypeMap
     * @return
     * @throws Exception
     */
    private List<OvertimeQuotaDto> getEmpOverTimeList(String tenantId, List<Long> empIds, Long startDate, Long endDate,
                                                      WaAnalyzDTO waAnalyzDTO,
                                                      Map<Integer, OverTimeTypeDo> overTimeDateTypeMap,
                                                      Map<String, Float> detailMap,
                                                      Map<Integer, WaShiftDef> shiftDefMap,
                                                      WaGroup waGroup) throws Exception {
        //查询考勤周期内的所有加班单据
        List<Map> listOt = workOvertimeMapper.getEmpOtList(tenantId, startDate, endDate, empIds, null, false);
        if (CollectionUtils.isEmpty(listOt)) {
            return null;
        }
        Map<Object, List<Map>> overtimeMap = listOt.stream().collect(Collectors.groupingBy(row -> row.get("otId")));
        overtimeMap.forEach((otId, otList) -> {
            if (otList.size() == 2) {
                otList = otList.stream().sorted(Comparator.comparing(ot -> (Long) ot.get("endTime"))).collect(Collectors.toList());
                Map ot = otList.get(1);
                ot.put("zeroSplitting", true);
            }
        });
        List<OvertimeQuotaDto> overtimeQuotaList = JSONArray.parseArray(JSONArray.toJSONString(listOt), OvertimeQuotaDto.class);
        //分析加班记录
        return getOvertimeQuotaList(tenantId, startDate, endDate, empIds, overtimeQuotaList, overTimeDateTypeMap,
                waAnalyzDTO, true, detailMap, shiftDefMap, waGroup);
    }

    /**
     * 计算有效加班时长
     *
     * @param tenantId
     * @param startDate
     * @param endDate
     * @param empIds
     * @param overtimeQuotaList
     * @param overTimeDateTypeMap
     * @param waAnalyzDTO
     * @param dayByDay
     * @param detailMap
     * @param shiftDefMap
     * @return
     */
    private List<OvertimeQuotaDto> getOvertimeQuotaList(String tenantId, Long startDate, Long endDate,
                                                        List<Long> empIds,
                                                        List<OvertimeQuotaDto> overtimeQuotaList,
                                                        Map<Integer, OverTimeTypeDo> overTimeDateTypeMap,
                                                        WaAnalyzDTO waAnalyzDTO,
                                                        boolean dayByDay,
                                                        Map<String, Float> detailMap,
                                                        Map<Integer, WaShiftDef> shiftDefMap,
                                                        WaGroup waGroup) {
        if (CollectionUtils.isEmpty(overtimeQuotaList)) {
            return Lists.newArrayList();
        }
        Boolean selectOvertimeDate = null != waGroup
                ? Optional.ofNullable(waGroup.getSelectOvertimeDate()).orElse(Boolean.FALSE)
                : Boolean.FALSE;
        boolean searchRegRecords = overTimeDateTypeMap.values().stream()
                .anyMatch(row -> row.getValidTimeCalType() != null
                        && !row.getValidTimeCalType().equals(OtValidTimeCalTypeEnum.APPLY_TIME.getIndex()));
        //查询员工打卡记录
        Map<String, List<WaRegisterRecordDo>> empBelongDateRegMap = null;
        if (searchRegRecords) {
            List<WaRegisterRecordDo> recordDoList = Lists.newArrayList();
            List<List<Long>> empIdList = ListTool.split(empIds, 500);
            for (List<Long> rows : empIdList) {
                if (CollectionUtils.isEmpty(rows)) {
                    continue;
                }
                recordDoList.addAll(waRegisterRecordDo.getAllRecordListByDateRange(tenantId, rows, startDate - 86400, endDate));
            }
            if (CollectionUtils.isNotEmpty(recordDoList)) {
                List<WaRegisterRecordDo> invalidList = recordDoList.stream().filter(e -> e.getType().equals(ClockWayEnum.FILLCLOCK.getIndex())
                        && !ApprovalStatusEnum.PASSED.getIndex().equals(e.getApprovalStatus())).collect(Collectors.toList());
                recordDoList.removeAll(invalidList);
            }
            if (CollectionUtils.isNotEmpty(recordDoList)) {
                empBelongDateRegMap = recordDoList.stream().collect(Collectors.groupingBy(o -> (o.getEmpid() + "_" + o.getBelongDate())));
            }
        }
        Map<String, OvertimeQuotaDto> map = new HashMap<>();
        for (OvertimeQuotaDto overtimeQuotaDto : overtimeQuotaList) {
            String overtimeKey = String.format("%s_%s", overtimeQuotaDto.getEmpid(), overtimeQuotaDto.getBelongDate());
            if (!dayByDay) {
                overtimeKey = String.format("%s_%s_%s", overtimeQuotaDto.getEmpid(), overtimeQuotaDto.getBelongDate(), overtimeQuotaDto.getOvertimeDetailId());
            }
            Integer dateType = overtimeQuotaDto.getDateType();
            //获取加班规则
            OverTimeTypeDo overTimeTypeDo = overTimeDateTypeMap.get(overtimeQuotaDto.getOvertimeTypeId());
            if (overTimeTypeDo == null) {
                log.info("getEmpOverTimeList 获取加班类型失败 dateType : {}", dateType);
                continue;
            }
            //是否关联出差
            Boolean isOpenTravel = overTimeTypeDo.getIsOpenTravel();
            Long start = overtimeQuotaDto.getBelongDate();
            //是否有审批通过的出差单
            List<WaEmpTravelDo> travelInfoList = waEmpTravelDo.getTravelInfoList(tenantId, overtimeQuotaDto.getEmpid(), start, start + 86399);
            List<WaEmpTravelDo> travelDoList = travelInfoList.stream().filter(e -> ApprovalStatusEnum.PASSED.getIndex().equals(e.getStatus())).collect(Collectors.toList());
            Integer validTimeCalType = overTimeTypeDo.getValidTimeCalType();
            float maxValidTimeMinute = 0;
            if (overTimeTypeDo.getMaxValidTime() != null) {
                maxValidTimeMinute = overTimeTypeDo.getMaxValidTime() * 60;
            }
            // 计算有效加班时长
            Float realDuration = 0f;
            if (validTimeCalType == null || OtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
                Long overtimeStartTime = overtimeQuotaDto.getStartTime();
                Long overtimeEndTime = overtimeQuotaDto.getEndTime();
                if (null != overtimeStartTime && null != overtimeEndTime) {
                    long jg = overtimeEndTime - overtimeStartTime;//加班时间长度
                    if (jg > 0) {
                        long overtimeStartDate = DateUtil.getOnlyDate(new Date(overtimeStartTime * 1000));
                        long overtimeEndDate = DateUtil.getOnlyDate(new Date(overtimeEndTime * 1000));

                        // 获取加班日期当天的排班（开始日期）
                        EmpShiftInfo startShift = registerAnalyzeService.getEmpShiftDefByInfo(overtimeQuotaDto.getEmpid(), null,
                                overtimeQuotaDto.getBelongDate(), waAnalyzDTO);
                        if (startShift == null) {
                            overtimeQuotaDto.setRealOvertimeDuration(0f);
                            log.info("getEmpOverTimeList get startShift empty");
                            continue;
                        }

                        // 获取加班日期当天的排班（结束日期）
                        EmpShiftInfo endShift = null;
                        if (overtimeEndDate > overtimeStartDate) {
                            endShift = registerAnalyzeService.getEmpShiftDefByInfo(overtimeQuotaDto.getEmpid(), null,
                                    overtimeEndDate, waAnalyzDTO);
                            if (endShift == null) {
                                overtimeQuotaDto.setRealOvertimeDuration(0f);
                                log.info("getEmpOverTimeList get endShift empty");
                                continue;
                            }
                        }
                        if (selectOvertimeDate) {
                            Long realDate = null != overtimeQuotaDto.getRealDate()
                                    ? DateUtil.getOnlyDate(new Date(overtimeQuotaDto.getRealDate() * 1000))
                                    : null;
                            EmpShiftInfo realDateShift = null != realDate
                                    ? registerAnalyzeService.getEmpShiftDefByInfo(overtimeQuotaDto.getEmpid(), null, realDate, waAnalyzDTO)
                                    : startShift;
                            jg -= waCommonService.calOtRestTotalTime(tenantId, realDateShift, overtimeStartTime, overtimeEndTime);
                        } else {
                            if (overtimeEndDate > overtimeStartDate) {
                                jg -= waCommonService.calOtRestTotalTime(tenantId, startShift, overtimeStartTime, overtimeEndTime);
                                jg -= waCommonService.calOtRestTotalTime(tenantId, endShift, overtimeStartTime, overtimeEndTime);
                            } else {
                                Long preDate = startShift.getWorkDate() - 86400L;
                                EmpShiftInfo preShift = registerAnalyzeService.getEmpShiftDefByInfo(overtimeQuotaDto.getEmpid(),
                                        null, preDate, waAnalyzDTO);
                                if (null != preShift) {
                                    jg -= waCommonService.calOtRestTotalTime(tenantId, preShift, overtimeStartTime, overtimeEndTime);
                                }
                                jg -= waCommonService.calOtRestTotalTime(tenantId, startShift, overtimeStartTime, overtimeEndTime);
                            }
                        }

                        if (jg > 0) {
                            realDuration = BigDecimal.valueOf(jg / 60).setScale(0, RoundingMode.DOWN).floatValue();
                        }
                    }
                }
                //有效时长上限校验
                realDuration = realDuration < maxValidTimeMinute ? realDuration : BigDecimal.valueOf(maxValidTimeMinute).floatValue();
                if (null != detailMap) {
                    detailMap.put(String.format("%s_%s-%s", overtimeQuotaDto.getEmpid(), overtimeQuotaDto.getBelongDate(), overtimeQuotaDto.getOvertimeDetailId()), realDuration);
                }
                //按照申请时长计算
                if (map.containsKey(overtimeKey)) {
                    OvertimeQuotaDto overtime = map.get(overtimeKey);
                    overtime.setRealOvertimeDuration(overtime.getRealOvertimeDuration() + realDuration);
                } else {
                    overtimeQuotaDto.setRealOvertimeDuration(realDuration);
                    map.put(overtimeKey, overtimeQuotaDto);
                }
            } else {
                //获取加班打卡记录
                String empBelongRegKey = overtimeQuotaDto.getEmpid() + "_" + overtimeQuotaDto.getBelongDate();
                Boolean zeroSplitting = Optional.ofNullable(overtimeQuotaDto.getZeroSplitting()).orElse(false);
                if (zeroSplitting) {
                    empBelongRegKey = overtimeQuotaDto.getEmpid() + "_" + (overtimeQuotaDto.getBelongDate() - 86400);
                }
                if (empBelongDateRegMap == null || CollectionUtils.isEmpty(empBelongDateRegMap.get(empBelongRegKey))) {
                    overtimeQuotaDto.setRealOvertimeDuration(0f);
                    map.put(overtimeKey, overtimeQuotaDto);
                    continue;
                }
                List<WaRegisterRecordDo> regList = empBelongDateRegMap.get(empBelongRegKey);
                if (regList.size() <= 1) {
                    overtimeQuotaDto.setRealOvertimeDuration(0f);
                    map.put(overtimeKey, overtimeQuotaDto);
                    continue;
                }
                regList.sort(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));
                if (PunchTypeEnum.SAME.getIndex().equals(overTimeTypeDo.getValidPunchType())) {
                    List<WaRegisterRecordDo> travelList = regList.stream().filter(e -> ClockWayEnum.FIELD.getIndex().equals(e.getType())).collect(Collectors.toList());
                    List<WaRegisterRecordDo> list = regList.stream().filter(e -> !ClockWayEnum.FIELD.getIndex().equals(e.getType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(travelList) && CollectionUtils.isNotEmpty(list)) {
                        List<WaRegisterRecordDo> otherList = list.stream().filter(e -> !ClockWayEnum.FILLCLOCK.getIndex().equals(e.getType())).collect(Collectors.toList());
                        List<WaRegisterRecordDo> bdkList = list.stream().filter(e -> ClockWayEnum.FILLCLOCK.getIndex().equals(e.getType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(otherList) && CollectionUtils.isEmpty(bdkList)) {
                            regList = list;
                        }
                    }
                }
                if (PunchTypeEnum.WORK.getIndex().equals(overTimeTypeDo.getValidPunchType())) {
                    regList = regList.stream().filter(e -> !ClockWayEnum.FIELD.getIndex().equals(e.getType())).collect(Collectors.toList());
                }
                if (PunchTypeEnum.FIELD.getIndex().equals(overTimeTypeDo.getValidPunchType())) {
                    regList = regList.stream().filter(e -> (ClockWayEnum.FIELD.getIndex().equals(e.getType()) || ClockWayEnum.FILLCLOCK.getIndex().equals(e.getType()))).collect(Collectors.toList());
                }
                if (regList.size() <= 1) {
                    overtimeQuotaDto.setRealOvertimeDuration(0f);
                    continue;
                }
                if (isOpenTravel) {
                    if (CollectionUtils.isEmpty(travelDoList)) {
                        overtimeQuotaDto.setRealOvertimeDuration(0f);
                        continue;
                    }
                }
                WaRegisterRecordDo signIn = regList.get(0);
                WaRegisterRecordDo signOff = regList.get(regList.size() - 1);
                Long signInTime = signIn.getRegDateTime();
                if (zeroSplitting) {
                    signInTime = overtimeQuotaDto.getStartTime();
                }
                Long signOffTime = signOff.getRegDateTime();

                //有效加班时长
                Float validateTime = 0f;
                Long startTime = overtimeQuotaDto.getStartTime();
                Long endTime = overtimeQuotaDto.getEndTime();
                Integer timeDuration = overtimeQuotaDto.getOvertimeDuration();

                // 获取加班日期当天的排班（开始日期）
                EmpShiftInfo startShift = registerAnalyzeService.getEmpShiftDefByInfo(overtimeQuotaDto.getEmpid(), null,
                        overtimeQuotaDto.getBelongDate(), waAnalyzDTO);
                if (startShift == null) {
                    overtimeQuotaDto.setRealOvertimeDuration(0f);
                    log.info("getEmpOverTimeList get startShift empty empid={}, date={}", overtimeQuotaDto.getEmpid(), overtimeQuotaDto.getBelongDate());
                    continue;
                }

                // 计算加班的有效时间段
                Long realOtStartTime = 0L;
                Long realOtEndTime = 0L;
                if (OtValidTimeCalTypeEnum.APPLY_REG_INTERSECTION.getIndex().equals(validTimeCalType)) {
                    String validTimeCalUnit = Optional.ofNullable(overTimeTypeDo.getValidTimeCalUnit())
                            .orElse(OtRuleValidTimeCalUnitEnum.SECOND.getCode());
                    if (OtRuleValidTimeCalUnitEnum.MINUTE.getCode().equals(validTimeCalUnit)) {
                        if (signInTime != null) {
                            long timeDiff = Math.abs(signInTime - startTime);
                            if (timeDiff < 60) {
                                signInTime = Math.min(signInTime, startTime);
                            }
                        }
//                        if (signOffTime != null) {
//                            long timeDiff = Math.abs(signOffTime - endTime);
//                            if (timeDiff < 60) {
//                                signOffTime = Math.max(signOffTime, endTime);
//                            }
//                        }
                    }
                    if (startTime <= signOffTime && endTime >= signInTime) {
                        if (signInTime <= startTime && signOffTime >= endTime) {
                            realOtStartTime = startTime;
                            realOtEndTime = endTime;
                            validateTime = BigDecimal.valueOf(endTime - startTime).floatValue();
                        } else if (signInTime <= startTime) {
                            realOtStartTime = startTime;
                            realOtEndTime = signOffTime;
                            validateTime = BigDecimal.valueOf(signOffTime - startTime).floatValue();
                        } else if (signOffTime <= endTime) {
                            realOtStartTime = signInTime;
                            realOtEndTime = signOffTime;
                            validateTime = BigDecimal.valueOf(signOffTime - signInTime).floatValue();
                        } else {
                            realOtStartTime = signInTime;
                            realOtEndTime = endTime;
                            validateTime = BigDecimal.valueOf(endTime - signInTime).floatValue();
                        }
                    }
                } else if (OtValidTimeCalTypeEnum.REG_TIME.getIndex().equals(validTimeCalType)
                        || OtValidTimeCalTypeEnum.REG_TIME_NOT_DEDUCTION.getIndex().equals(validTimeCalType)) {
                    if (DateTypeEnum.DATE_TYP_1.getIndex().equals(startShift.getDateType())) {
                        // 计算当天班次的下班时间
                        Optional<EmpShiftInfo> empShiftDefOpt = startShift.doGetShiftDefList().stream().max(Comparator.comparing(EmpShiftInfo::doGetRealStartTime));
                        WaShiftDef shiftDef = shiftDefMap.get(empShiftDefOpt.get().getShiftDefId());
                        WaShiftDef shiftDefWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
                        Long shiftEndTime = startShift.getWorkDate() + (shiftDefWorkTime.getEndTime() * 60);
                        if (CdWaShiftUtil.checkCrossNightV2(shiftDef, startShift.getDateType())) {
                            shiftEndTime += 86400L;
                        }
                        if (signOffTime <= shiftEndTime) {
                            validateTime = 0f;
                        } else {
                            realOtStartTime = Math.max(shiftEndTime, signInTime);
                            realOtEndTime = signOffTime;
                            validateTime = BigDecimal.valueOf(signOffTime - Math.max(shiftEndTime, signInTime)).floatValue();
                        }
                    } else {
                        realOtStartTime = signInTime;
                        realOtEndTime = signOffTime;
                        validateTime = BigDecimal.valueOf(signOffTime - signInTime).floatValue();
                    }
                }

                // 加班有效时长计算
                Float validateTimeMinute = 0f;
                if (validateTime > 0) {
                    // 扣减加班休息时间
                    if (!OtValidTimeCalTypeEnum.REG_TIME_NOT_DEDUCTION.getIndex().equals(validTimeCalType)) {
                        if (selectOvertimeDate) {
                            Long realDate = null != overtimeQuotaDto.getRealDate()
                                    ? DateUtil.getOnlyDate(new Date(overtimeQuotaDto.getRealDate() * 1000))
                                    : null;
                            EmpShiftInfo realDateShift = null != realDate
                                    ? registerAnalyzeService.getEmpShiftDefByInfo(overtimeQuotaDto.getEmpid(), null, realDate, waAnalyzDTO)
                                    : startShift;
                            realDateShift = null != realDateShift ? realDateShift : startShift;
                            validateTime -= waCommonService.calOtRestTotalTime(tenantId, realDateShift, realOtStartTime, realOtEndTime);
                        } else {
                            Long preDate = startShift.getWorkDate() - 86400L;
                            EmpShiftInfo preShift = registerAnalyzeService.getEmpShiftDefByInfo(overtimeQuotaDto.getEmpid(), null,
                                    preDate, waAnalyzDTO);
                            if (null != preShift) {
                                validateTime -= waCommonService.calOtRestTotalTime(tenantId, preShift, realOtStartTime, realOtEndTime);
                            }
                            validateTime -= waCommonService.calOtRestTotalTime(tenantId, startShift, realOtStartTime, realOtEndTime);
                        }
                    }

                    if (OtValidTimeCalTypeEnum.REG_TIME.getIndex().equals(validTimeCalType)
                            || OtValidTimeCalTypeEnum.REG_TIME_NOT_DEDUCTION.getIndex().equals(validTimeCalType)) {
                        Integer overtimeCalType = overTimeTypeDo.getOvertimeCalType() == null ? OvertimeCalTypeEnum.MIN_APPLY_REGISTER.getIndex() : overTimeTypeDo.getOvertimeCalType();
                        if (OvertimeCalTypeEnum.MIN_APPLY_REGISTER.getIndex().equals(overtimeCalType)) {
                            validateTime = Math.min(validateTime, timeDuration * 60);
                        }
                    }
                    validateTimeMinute = validateTime / 60;

                    if (OtValidTimeCalTypeEnum.APPLY_REG_INTERSECTION.getIndex().equals(validTimeCalType) && validateTimeMinute > timeDuration) {
                        //如果计算出的加班时长>申请的加班时长，两者取最小值
                        validateTimeMinute = BigDecimal.valueOf(timeDuration).floatValue();
                    }
                    if (validateTimeMinute > 0) {
                        validateTimeMinute = calOtDurationByCarryRules(validateTimeMinute / 60, overTimeTypeDo) * 60;
                    }
                }

                //有效时长上限校验
                validateTimeMinute = validateTimeMinute < maxValidTimeMinute ? validateTimeMinute : BigDecimal.valueOf(maxValidTimeMinute).floatValue();
                overtimeQuotaDto.setRealOvertimeDuration(validateTimeMinute);
                if (null != detailMap) {
                    detailMap.put(String.format("%s_%s-%s", overtimeQuotaDto.getEmpid(), overtimeQuotaDto.getBelongDate(), overtimeQuotaDto.getOvertimeDetailId()), overtimeQuotaDto.getRealOvertimeDuration());
                }
                if (map.containsKey(overtimeKey)) {
                    // 取加班时间段和打卡时间段的交集
                    OvertimeQuotaDto oldOtQuota = map.get(overtimeKey);
                    Float oldRealOvertimeDuration = oldOtQuota.getRealOvertimeDuration();
                    oldRealOvertimeDuration = Optional.ofNullable(oldRealOvertimeDuration).orElse(0f);
                    overtimeQuotaDto.setRealOvertimeDuration(oldRealOvertimeDuration + validateTimeMinute);
                }
                map.put(overtimeKey, overtimeQuotaDto);
            }
        }
        List<OvertimeQuotaDto> list = Lists.newArrayList();
        for (Map.Entry<String, OvertimeQuotaDto> entry : map.entrySet()) {
            list.add(entry.getValue());
        }
        return list;
    }

    private Float calOtDurationByCarryRules(Float time_duration, OverTimeTypeDo overtimeType) {
        if (time_duration == null || overtimeType == null) {
            return time_duration;
        }
        if (null == overtimeType.getRoundingRule()) {
            return time_duration;
        }
        switch (overtimeType.getRoundingRule()) {
            case 1:
                time_duration = getOtRoundingRule(time_duration.doubleValue(), true, 0.25f);
                break;
            case 2:
                time_duration = getOtRoundingRule(time_duration.doubleValue(), true, 0.5f);
                break;
            case 3:
                time_duration = getOtRoundingRule(time_duration.doubleValue(), true, 1f);
                break;
            case 4:
                time_duration = getOtRoundingRule(time_duration.doubleValue(), false, 0.25f);
                break;
            case 5:
                time_duration = getOtRoundingRule(time_duration.doubleValue(), false, 0.5f);
                break;
            case 6:
                time_duration = getOtRoundingRule(time_duration.doubleValue(), false, 1f);
                break;
            case 7:
                time_duration = BigDecimal.valueOf(time_duration).setScale(0, RoundingMode.HALF_UP).floatValue();
                break;
            default:
                break;
        }
        return time_duration;
    }

    private Float getOtRoundingRule(Double min, boolean isUp, Float roundMin) {
        double mod = min % roundMin;
        //向上取整
        if (isUp) {
            if (mod > 0) {
                min = min + (roundMin - mod);
            }
        } else {
            //向下取整
            min = min - mod;
        }
        return new BigDecimal(min.toString()).floatValue();
    }

    private BigDecimal convertCompensatoryQuota(Integer acctTimeType, Float workingTime, BigDecimal otTotalNum, OverTimeTypeDo overTimeTypeDo) {
        if (acctTimeType == null) {
            return BigDecimal.ZERO;
        }
        workingTime = workingTime == null ? 0 : workingTime;
        BigDecimal workTimeHour = new BigDecimal(workingTime);
        Float unit = overTimeTypeDo.getMinOvertimeUnit();
        if (acctTimeType.equals(LeaveTypeUnitEnum.DAY.getIndex())) {
            //调休单位为天
            return OvertimeQuotaUnitEnum.DAY.getTime(workTimeHour, otTotalNum, unit);
        } else {
            return OvertimeQuotaUnitEnum.HOUR.getTime(workTimeHour, otTotalNum, unit);
        }
    }

    private String getExecSql(String execSql, String jobAgeSql, String corpAgeSql, String corpYearEndAgeSql) {
        if (StringUtils.isNotEmpty(jobAgeSql)) {
            execSql = initExp(execSql.replaceAll("job_age", jobAgeSql));
        } else {
            execSql = execSql.replaceAll("job_age", "CASE WHEN first_work_date IS NULL THEN COALESCE(ep.service_years,0) + EXTRACT(YEAR FROM age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))+CAST(EXTRACT(MONTH FROM age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/12 AS DECIMAL(10,1))+CAST(EXTRACT(DAY FROM age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/365 AS DECIMAL(10,3)) ELSE EXTRACT(YEAR FROM age(to_timestamp(#{nowTime}),to_timestamp(first_work_date)))+CAST(EXTRACT(MONTH FROM age(to_timestamp(#{nowTime}),to_timestamp(first_work_date)))/12 AS DECIMAL(10,1))+CAST(EXTRACT(DAY FROM age(to_timestamp(#{nowTime}),to_timestamp(first_work_date)))/365 AS DECIMAL(10,3)) END");
        }
        if (StringUtils.isNotEmpty(corpAgeSql)) {
            execSql = initExp(execSql.replaceAll("corp_age", corpAgeSql));
        } else {
            execSql = execSql.replaceAll("corp_age", "extract(year from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))+cast(extract(month from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/12 as decimal(10,1))+cast(extract(day from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/365 as decimal(10,3))");
        }
        if (execSql.contains("total_working_month")) {
            execSql = execSql.replaceAll("total_working_month", "extract(year from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))*12 + extract(month from age(to_timestamp(#{nowTime}),to_timestamp(hire_date))) + cast(extract(day from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/30 as decimal(10,2)) + COALESCE(continuous_working_month, 0)");
        }
        if (StringUtils.isNotEmpty(corpYearEndAgeSql)) {
            execSql = initExp(execSql.replaceAll(QUOTA_GEN_CON_OF_ENDYEARCORPAGE, corpYearEndAgeSql));
        } else {
            execSql = execSql.replaceAll(QUOTA_GEN_CON_OF_ENDYEARCORPAGE, "extract(year from age(to_timestamp(#{yearEndTime}),to_timestamp(hire_date)))+cast(extract(month from age(to_timestamp(#{yearEndTime}),to_timestamp(hire_date)))/12 as decimal(10,1))+cast(extract(day from age(to_timestamp(#{yearEndTime}),to_timestamp(hire_date)))/365 as decimal(10,3))");
        }
        execSql = initExp(execSql);
        return execSql;
    }

    private String initExp(String exp) {
        return exp.replaceAll("1#(" + BaseConst.FIX_EXT_COLUMN + "\\d+)#.*?=", "(case when (ei.ext_custom_col ->> '$1') = '' then null else ei.ext_custom_col ->> '$1' end)::BIGINT=").replaceAll("1#(" + BaseConst.FIX_EXT_COLUMN + "\\d+)#", "(case when (ei.ext_custom_col ->> '$1') = '' then null else ei.ext_custom_col ->> '$1' end)::BIGINT").replaceAll("9#(" + BaseConst.FIX_EXT_COLUMN + "\\d+)#", "(case when (ep.ext_custom_col ->> '$1') = '' then null else ep.ext_custom_col ->> '$1' end)::BIGINT").replaceAll("orgid='(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
    }

    private String replaceGroupExp(String groupExp) {
        if (StringUtils.isBlank(groupExp)) {
            return groupExp;
        }
        return groupExp.replaceAll("subOrg='(\\d+)'", "orgid in ( SELECT getsuborgstr('{$1}'))")
                .replaceAll("subOrg<>'(\\d+)'", "orgid not in (" +
                        " SELECT cast(getsuborgstr as integer ) as t FROM getsuborgstr('{$1}')" +
                        ")"
                );
    }

    /**
     * 针对育儿假的sql做特殊处理
     *
     * @param groupExp 分组表达式
     * @param sf
     * @return
     */
    private String dealGroupExp(String groupExp, StringBuffer sf, short year, Integer distributionCycle, Long endDate) {
        String sqlWhere = " extract(year from age(to_timestamp(#year#||'-'||to_char(#now#,'MM-DD'),'yyyy-MM-dd'),to_timestamp(birthday)))+cast(extract(month from age(to_timestamp(#year#||'-'||to_char(#now#,'MM-DD'),'yyyy-MM-dd'),to_timestamp(birthday)))/12 as decimal(10,1))+cast(extract(day from age(to_timestamp(#year#||'-'||to_char(#now#,'MM-DD'),'yyyy-MM-dd'),to_timestamp( birthday )))/365 as decimal(10,3))";
        if (groupExp.contains("parental_leave")) {
            String[] arr = groupExp.split("AND|OR");
            for (int i = 0; i < arr.length; i++) {
                if (arr[i].contains("parental_leave")) {
                    String sql = arr[i];
                    if (DistributionCycleEnum.NATURAL_YEAR.getIndex().equals(distributionCycle)) {
                        sqlWhere = sqlWhere.replace("#year#", String.valueOf(year)).replace("#now#", "to_timestamp(" + endDate + ")");
                    } else {
                        sqlWhere = sqlWhere.replace("#year#", String.valueOf(year + 1)).replace("#now#", "to_timestamp(birthday)");
                    }
                    sql = sql.replaceAll("\\(|\\)", "").replace("parental_leave", sqlWhere);
                    sf.append("and").append(sql).append("and").append(sqlWhere).append(">0");
                    String appendStr = arr[i].replaceAll("\\(|\\)", "").trim();
                    String a = "AND " + appendStr;
                    String b = "OR " + appendStr;
                    if (arr[0].contains("parental_leave")) {
                        a = appendStr + " AND";
                        b = appendStr + " OR";
                    }
                    if (groupExp.contains(a)) {
                        groupExp = groupExp.replace(a, "");
                    }
                    if (groupExp.contains(b)) {
                        groupExp = groupExp.replace(b, "");
                    }
                }
            }
        }
        return groupExp;
    }

    /**
     * 根据入职日期或者首次参工日期计算员工实际配额
     *
     * @param firstDate     入职日期
     * @param midDate       首次参公日期或入职日期（最小的一个日期）
     * @param year          生成配额年份
     * @param last          上一时间段的配额（最小）
     * @param next          下一时间段的配额（最大）
     * @param disCycleStart 发放周期开始日
     * @param disCycleEnd   发放周期结束日
     * @return 折算后的配额
     * @throws ParseException
     */
    private BigDecimal convertQuotaByDate(Long firstDate, Long midDate, Short year, Integer last, Integer next,
                                          Long disCycleStart, Long disCycleEnd, LeaveQuotaConfigDo quotaConfigDo,
                                          Integer acctTimeType, EmpLeaveQuotaDto quotaDto) throws ParseException {
        //跨维度折算：A和B分别是发放开始日工龄和发放结束日工龄对应的配额
        //舍位规则：
        //按自然年/自定义如果选择全年发放，存在跨维度情况，
        //单位为天：默认向下取整0.5天
        //单位为小时：默认向下取整1h
        //入职日期
        Calendar firstDateCalendar = Calendar.getInstance();
        firstDateCalendar.setTimeInMillis(firstDate * 1000);
        //首次参公日期或入职日期
        Calendar midDateCalendar = Calendar.getInstance();
        midDateCalendar.setTimeInMillis(midDate * 1000);
        int midDateCalendarMonth = midDateCalendar.get(Calendar.MONTH);
        int midDateCalendarDay = midDateCalendar.get(Calendar.DATE);
        //发放周期开始日期
        Calendar disCycleStartCalendar = Calendar.getInstance();
        disCycleStartCalendar.setTimeInMillis(disCycleStart * 1000);
        int disCycleStartCalendarMonth = disCycleStartCalendar.get(Calendar.MONTH);
        int disCycleStartCalendarDay = disCycleStartCalendar.get(Calendar.DATE);
        //发放周期结束日期
        Calendar disCycleEndCalendar = Calendar.getInstance();
        disCycleEndCalendar.setTimeInMillis(disCycleEnd * 1000);
        //日期临界点
        Calendar cal3 = Calendar.getInstance();
        if (midDateCalendarMonth < disCycleStartCalendarMonth || (midDateCalendarMonth == disCycleStartCalendarMonth && midDateCalendarDay < disCycleStartCalendarDay)) {
            cal3.set(disCycleEndCalendar.get(Calendar.YEAR), midDateCalendar.get(Calendar.MONTH), midDateCalendar.get(Calendar.DATE));
        } else {
            cal3.set(disCycleStartCalendar.get(Calendar.YEAR), midDateCalendar.get(Calendar.MONTH), midDateCalendar.get(Calendar.DATE));
        }
        /**
         CAIDAOM-123 年假生成逻辑优化:
         跨额度规则之前的日期，按照员工所属规则发放年假本年额度。
         如发放周期是2021-1-26 至 2022-1-25，在2021年6月25号之前，员工工龄为9年，对应年假5天，6月25号之后，员工工龄满10年，对应年假为10天。
         6月25号之前
         员工本年额度始终为5天，当前额度=（当前日期 - 20210126）/365*5。
         6月25号之后
         员工本年额度=（6月25-20210126）/365*5+（20220125- 20210625）/365*10；
         员工当前额度=（6月25-20210126）/365*5+（当前日期- 20210625）/365*10；
         以上365为（发放结束日-发放开始日+1）
         综上，计算本年额度和当前额度需先判断当前的发放周期会不会存在跨规则情况，如有这种情况，需按照上述规则进行计算
         **/
        // 跨额度规则折算日期（日期临界点）
        Long criticalDate = DateUtil.getOnlyDate(new Date(cal3.getTimeInMillis()));
        quotaDto.setCrossQuotaDate(criticalDate);
        quotaDto.setLastQuota(last);
        quotaDto.setNextQuota(next);
        //正常折算逻辑
        int maxDay = DateUtilExt.getDifferenceDay(disCycleStart, disCycleEnd) + 1;
        Integer distributionCycle = quotaConfigDo.getDistributionCycle();
        Integer quotaDistributeRule = quotaConfigDo.getQuotaDistributeRule();
        Integer quotaRoundingRule = quotaConfigDo.getQuotaRoundingRule();
        if (quotaRoundingRule == null) {
            if (acctTimeType == 1) {
                quotaRoundingRule = QuotaRoundingRuleEnum.ROUND_DOW_HALF.getIndex();
            } else {
                quotaRoundingRule = QuotaRoundingRuleEnum.ROUND_DOWN_1.getIndex();
            }
        }
        if (distributionCycle.equals(DistributionCycleEnum.NATURAL_YEAR.getIndex()) || distributionCycle.equals(DistributionCycleEnum.CUSTOM_CYCLE.getIndex())) {
            //自然年&自定义
            //如果是当年入职，按全年额度发放，则相当于非当年入职
            if (firstDateCalendar.get(Calendar.YEAR) == year
                    && (quotaDistributeRule.equals(QuotaDistributeRuleEnum.CONVERT.getIndex())
                    || quotaDistributeRule.equals(QuotaDistributeRuleEnum.DIST_BY_HIRE_MONTH.getIndex()))) {
                //当年入职
                int firstHireDay = cal3.get(Calendar.DAY_OF_YEAR);
                int hireDay;
                long calStartDate;

                if (QuotaDistributeRuleEnum.DIST_BY_HIRE_MONTH.getIndex().equals(quotaDistributeRule)
                        && null != quotaConfigDo.getDayOfHireMonthDist()) {
                    long convertFirstDate;
                    if (DateUtilExt.getTimeMonthDay(firstDate) <= quotaConfigDo.getDayOfHireMonthDist()) {
                        convertFirstDate = DateUtilExt.getMonthBegin(firstDate);
                    } else {
                        convertFirstDate = DateUtilExt.addMonth(DateUtilExt.getMonthBegin(firstDate), 1);
                    }
                    Calendar convertFirstDateCalendar = Calendar.getInstance();
                    convertFirstDateCalendar.setTimeInMillis(convertFirstDate * 1000);

                    hireDay = convertFirstDateCalendar.get(Calendar.DAY_OF_YEAR);
                    calStartDate = convertFirstDateCalendar.getTimeInMillis() / 1000;
                } else {
                    hireDay = firstDateCalendar.get(Calendar.DAY_OF_YEAR);
                    calStartDate = firstDateCalendar.getTimeInMillis() / 1000;
                }

                int diff = firstHireDay - hireDay;
                //a.入职<首次参工时间，（首-入）/365*A+(发放结束日期-首)/365*B
                //b.入职>首次参工时间，（发放结束日期-入职）/365*B
                if (diff > 0) {
                    Integer secondPeriodDay = DateUtilExt.getDifferenceDay(cal3.getTimeInMillis() / 1000, disCycleEnd) + 1;
                    BigDecimal rate = BigDecimal.valueOf(diff).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                    BigDecimal rate1 = BigDecimal.valueOf(secondPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                    BigDecimal val = BigDecimal.valueOf(last).multiply(rate);
                    BigDecimal val1 = BigDecimal.valueOf(next).multiply(rate1);
                    return handleMantissa(quotaConfigDo.getTenantId(), val.add(val1), acctTimeType, quotaRoundingRule);
                } else {
                    Integer periodDay = DateUtilExt.getDifferenceDay(calStartDate, disCycleEnd) + 1;
                    BigDecimal rate = BigDecimal.valueOf(periodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                    BigDecimal val = BigDecimal.valueOf(next).multiply(rate);
                    return handleMantissa(quotaConfigDo.getTenantId(), val, acctTimeType, quotaRoundingRule);
                }
            } else {
                //非当年入职
                //（首-发放开始时间）/365*A+(发放结束日期 - 首)/365*B
                Integer firstPeriodDay = DateUtilExt.getDifferenceDay(disCycleStart, cal3.getTimeInMillis() / 1000);
                Integer secondPeriodDay = DateUtilExt.getDifferenceDay(cal3.getTimeInMillis() / 1000, disCycleEnd) + 1;
                BigDecimal rate = BigDecimal.valueOf(firstPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                BigDecimal rate1 = BigDecimal.valueOf(secondPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                BigDecimal val = BigDecimal.valueOf(last).multiply(rate);
                BigDecimal val1 = BigDecimal.valueOf(next).multiply(rate1);
                return handleMantissa(quotaConfigDo.getTenantId(), val.add(val1), acctTimeType, quotaRoundingRule);
            }
        } else {
            //入职年
            //（首-发放开始时间）/365*A+(发放结束日期 - 首)/365*B
            Integer firstPeriodDay = DateUtilExt.getDifferenceDay(disCycleStart, cal3.getTimeInMillis() / 1000);
            Integer secondPeriodDay = DateUtilExt.getDifferenceDay(cal3.getTimeInMillis() / 1000, disCycleEnd) + 1;
            BigDecimal rate = BigDecimal.valueOf(firstPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
            BigDecimal rate1 = BigDecimal.valueOf(secondPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
            BigDecimal val = BigDecimal.valueOf(last).multiply(rate);
            BigDecimal val1 = BigDecimal.valueOf(next).multiply(rate1);
            return handleMantissa(quotaConfigDo.getTenantId(), val.add(val1), acctTimeType, quotaRoundingRule);
        }
    }

    /**
     * 根据入职日期或者首次参工日期计算员工实际配额
     *
     * @param year          生成配额年份
     * @param firstDate     入职日期
     * @param firstMidDate  第一个临界点
     * @param secMidDate    第二个临界点
     * @param firstQuota    第一个时间段的配额（最小）
     * @param secQuota      第二个时间段的配额
     * @param thirdQuota    第三个时间段的配额（最大）
     * @param disCycleStart 发放周期开始日
     * @param disCycleEnd   发放周期结束日
     * @param quotaConfigDo quotaConfigDo
     * @param acctTimeType  acctTimeType
     * @param quotaDto      quotaDto
     * @return 折算后的配额
     * @throws ParseException
     */
    private BigDecimal convertMultiQuotaByDate(Short year, Long firstDate, Long firstMidDate, Long secMidDate,
                                               Integer firstQuota, Integer secQuota, Integer thirdQuota,
                                               Long disCycleStart, Long disCycleEnd, LeaveQuotaConfigDo quotaConfigDo,
                                               Integer acctTimeType, EmpLeaveQuotaDto quotaDto) throws ParseException {
        /**
         * 跨维度折算：A、B和C分别是发放开始日工龄和发放结束日内工龄两次跨档对应的配额
         * 舍位规则：
         * 按自然年/自定义如果选择全年发放，存在跨维度情况，
         * 单位为天：默认向下取整0.5天
         * 单位为小时：默认向下取整1h
         */
        //入职日期
        Calendar firstDateCalendar = Calendar.getInstance();
        firstDateCalendar.setTimeInMillis(firstDate * 1000);
        //跨额度规则折算日期（日期临界点）
        quotaDto.setCrossQuotaDate(firstDate);
        quotaDto.setSecCrossQuotaDate(secMidDate);
        //跨档额度
        quotaDto.setLastQuota(firstQuota);
        quotaDto.setNextQuota(secQuota);
        quotaDto.setThirdQuota(thirdQuota);
        //正常折算逻辑
        int maxDay = DateUtilExt.getDifferenceDay(disCycleStart, disCycleEnd) + 1;
        Integer quotaDistributeRule = quotaConfigDo.getQuotaDistributeRule();
        Integer quotaRoundingRule = quotaConfigDo.getQuotaRoundingRule();
        if (quotaRoundingRule == null) {
            if (acctTimeType == 1) {
                quotaRoundingRule = QuotaRoundingRuleEnum.ROUND_DOW_HALF.getIndex();
            } else {
                quotaRoundingRule = QuotaRoundingRuleEnum.ROUND_DOWN_1.getIndex();
            }
        }
        //首个日期临界点
        Calendar firstMidDateCalendar = Calendar.getInstance();
        firstMidDateCalendar.setTimeInMillis(firstMidDate * 1000);
        //第二个日期临界点
        Calendar secondMidDateCalendar = Calendar.getInstance();
        secondMidDateCalendar.setTimeInMillis(secMidDate * 1000);
        //如果是当年入职，按全年额度发放，则相当于非当年入职
        if (firstDateCalendar.get(Calendar.YEAR) == year
                && (quotaDistributeRule.equals(QuotaDistributeRuleEnum.CONVERT.getIndex())
                || quotaDistributeRule.equals(QuotaDistributeRuleEnum.DIST_BY_HIRE_MONTH.getIndex()))) {
            //当年入职
            int hireDay = firstDateCalendar.get(Calendar.DAY_OF_YEAR);
            int firstHireDay = firstMidDateCalendar.get(Calendar.DAY_OF_YEAR);
            //a.入职<首次参工时间，（首-入）/365*A+(发放结束日期-首)/365*B
            //b.入职>首次参工时间，（发放结束日期-入职）/365*B
            if (firstDate <= firstMidDate) {
                BigDecimal firstRate = BigDecimal.valueOf(firstHireDay - hireDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                BigDecimal firstVal = BigDecimal.valueOf(firstQuota).multiply(firstRate);
                //(次临日期-首临日期)/365*B
                Integer secondPeriodDay = DateUtilExt.getDifferenceDay(firstMidDate, secMidDate);
                BigDecimal secondRate = BigDecimal.valueOf(secondPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                BigDecimal secondVal = BigDecimal.valueOf(secQuota).multiply(secondRate);
                //(发放结束日期-次临日期)/365*C
                Integer thirdPeriodDay = DateUtilExt.getDifferenceDay(secMidDate, disCycleEnd) + 1;
                BigDecimal thirdRate = BigDecimal.valueOf(thirdPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                BigDecimal thirdVal = BigDecimal.valueOf(thirdQuota).multiply(thirdRate);
                BigDecimal totalQuota = firstVal.add(secondVal).add(thirdVal);
                return handleMantissa(quotaConfigDo.getTenantId(), totalQuota, acctTimeType, quotaRoundingRule);
            } else if (firstDate <= secMidDate) {
                //(次临日期-首临日期)/365*B
                Integer secondPeriodDay = DateUtilExt.getDifferenceDay(firstDate, secMidDate);
                BigDecimal secondRate = BigDecimal.valueOf(secondPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                BigDecimal secondVal = BigDecimal.valueOf(secQuota).multiply(secondRate);
                //(发放结束日期-次临日期)/365*C
                Integer thirdPeriodDay = DateUtilExt.getDifferenceDay(secMidDate, disCycleEnd) + 1;
                BigDecimal thirdRate = BigDecimal.valueOf(thirdPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                BigDecimal thirdVal = BigDecimal.valueOf(thirdQuota).multiply(thirdRate);
                BigDecimal totalQuota = secondVal.add(thirdVal);
                return handleMantissa(quotaConfigDo.getTenantId(), totalQuota, acctTimeType, quotaRoundingRule);
            } else {
                Integer periodDay = DateUtilExt.getDifferenceDay(firstDateCalendar.getTimeInMillis() / 1000, disCycleEnd) + 1;
                BigDecimal rate = BigDecimal.valueOf(periodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
                BigDecimal val = BigDecimal.valueOf(secQuota).multiply(rate);
                return handleMantissa(quotaConfigDo.getTenantId(), val, acctTimeType, quotaRoundingRule);
            }
        } else {
            //非当年入职
            //首临日期-发放开始时间）/365*A+(次临日期-首临日期)/365*B+(发放结束日期-次临日期)/365*C
            //首临日期-发放开始时间）/365*A
            Integer firstPeriodDay = DateUtilExt.getDifferenceDay(disCycleStart, firstMidDate);
            BigDecimal firstRate = BigDecimal.valueOf(firstPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
            BigDecimal firstVal = BigDecimal.valueOf(firstQuota).multiply(firstRate);
            //(次临日期-首临日期)/365*B
            Integer secondPeriodDay = DateUtilExt.getDifferenceDay(firstMidDate, secMidDate);
            BigDecimal secondRate = BigDecimal.valueOf(secondPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
            BigDecimal secondVal = BigDecimal.valueOf(secQuota).multiply(secondRate);
            //(发放结束日期-次临日期)/365*C
            Integer thirdPeriodDay = DateUtilExt.getDifferenceDay(secMidDate, disCycleEnd) + 1;
            BigDecimal thirdRate = BigDecimal.valueOf(thirdPeriodDay).divide(BigDecimal.valueOf(maxDay), 10, RoundingMode.HALF_UP);
            BigDecimal thirdVal = BigDecimal.valueOf(thirdQuota).multiply(thirdRate);
            BigDecimal totalQuota = firstVal.add(secondVal).add(thirdVal);
            return handleMantissa(quotaConfigDo.getTenantId(), totalQuota, acctTimeType, quotaRoundingRule);
        }
    }

    /**
     * 按年发放的假期配额-计算当前配额
     *
     * @param lq
     * @return
     */
    @Override
    public Float calNowQuota(EmpLeaveQuotaDto lq) {
        Long now = lq.getCalNowQuotaConvertDate() == null ? DateUtil.getOnlyDate() : lq.getCalNowQuotaConvertDate();
        // CAIDAOM-1374 如果当前日期大于等于发放周期结束日期，当前额度直接等于本年额度
        if (now >= lq.getDisCycleEnd()) {
            return lq.getWaEmpQuotaQuotaDay();
        }
        BigDecimal originalQuotaDay = lq.getOriginalQuotaDay();
        Integer acctTimeType = lq.getAcctTimeType();
        if (acctTimeType != null && 2 == acctTimeType) {
            originalQuotaDay = originalQuotaDay.multiply(BigDecimal.valueOf(60));
        }
        Integer nowRoundingRule = lq.getNowRoundingRule();
        if (nowRoundingRule == null) {
            if (lq.getAcctTimeType() == 1) {
                nowRoundingRule = QuotaRoundingRuleEnum.ROUND_DOW_HALF.getIndex();
            } else {
                nowRoundingRule = QuotaRoundingRuleEnum.ROUND_DOWN_1.getIndex();
            }
        }
        Long hireDate = Optional.ofNullable(lq.getHireDate()).orElse(0L);
        Long startDate = lq.getDisCycleStart();

        if (startDate != null && hireDate > startDate) {
            startDate = hireDate;
            try {
                Integer quotaDistributeRule = lq.getQuotaDistributeRule();
                if (QuotaDistributeRuleEnum.DIST_BY_HIRE_MONTH.getIndex().equals(quotaDistributeRule)
                        && null != lq.getDayOfHireMonthDist()) {
                    Integer dayOfHireMonthDist = lq.getDayOfHireMonthDist();
                    Integer hireDay = DateUtilExt.getTimeMonthDay(startDate);
                    if (hireDay <= dayOfHireMonthDist) {
                        startDate = DateUtilExt.getMonthBegin(startDate);
                    } else {
                        startDate = DateUtilExt.getMonthBegin(startDate);
                        startDate = DateUtilExt.addMonth(startDate, 1);
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        BigDecimal nowQuota;
        if (!NowQuotaRule.DAY.getIndex().equals(lq.getNowDistributeRule())) {
            nowQuota = BigDecimal.valueOf(lq.getWaEmpQuotaQuotaDay());
        } else {
            BigDecimal currentYearQuota = BigDecimal.valueOf(lq.getWaEmpQuotaQuotaDay());
            Long terminationDate = lq.getTerminationDate();
            Long crossQuotaDate = Optional.ofNullable(lq.getCrossQuotaDate()).orElse(0L);
            Long secCrossQuotaDate = Optional.ofNullable(lq.getSecCrossQuotaDate()).orElse(0L);
            boolean multiCross = crossQuotaDate > 0 && secCrossQuotaDate > 0 && lq.getLastQuota() != null && lq.getNextQuota() != null && lq.getThirdQuota() != null;
            // 员工配额跨跨界时，根据临界点日期来判断当前配额的计算逻辑
            if (multiCross) {
                Long firstPreDayConvertDate = DateUtil.addDate(crossQuotaDate * 1000, -1);
                Long secPreDayConvertDate = DateUtil.addDate(secCrossQuotaDate * 1000, -1);
                if (now >= secCrossQuotaDate) {
                    if (terminationDate != null && terminationDate > hireDate) {
                        // 员工离职
                        if (terminationDate >= secCrossQuotaDate) {
                            BigDecimal last = new BigDecimal(lq.getLastQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, firstPreDayConvertDate));
                            BigDecimal next = new BigDecimal(lq.getNextQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), crossQuotaDate, secCrossQuotaDate));
                            Long convertDate = terminationDate <= now ? terminationDate : now;
                            BigDecimal third = BigDecimal.valueOf(lq.getThirdQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), secPreDayConvertDate, convertDate));
                            nowQuota = last.add(next).add(third);
                        } else if (terminationDate >= crossQuotaDate) {
                            BigDecimal last = new BigDecimal(lq.getLastQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, firstPreDayConvertDate));
                            BigDecimal next = new BigDecimal(lq.getNextQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), crossQuotaDate, terminationDate));
                            nowQuota = last.add(next);
                        } else {
                            if (hireDate > lq.getDisCycleStart() && lq.isCalNowQuotaByCurrentYearQuota()) {
                                nowQuota = currentYearQuota.multiply(getConvertRate(hireDate, lq.getDisCycleEnd(), startDate, terminationDate));
                            } else {
                                nowQuota = originalQuotaDay.multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, terminationDate));
                            }
                        }
                    } else {
                        BigDecimal last = new BigDecimal(lq.getLastQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, firstPreDayConvertDate));
                        BigDecimal next = new BigDecimal(lq.getNextQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), crossQuotaDate, secCrossQuotaDate));
                        BigDecimal third = BigDecimal.valueOf(lq.getThirdQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), secPreDayConvertDate, now));
                        nowQuota = last.add(next).add(third);
                    }
                } else if (crossQuotaDate <= now) {
                    if (terminationDate != null && terminationDate > hireDate) {
                        if (terminationDate >= secCrossQuotaDate || terminationDate >= crossQuotaDate) {
                            Long convertDate = terminationDate <= now ? terminationDate : now;
                            BigDecimal last = new BigDecimal(lq.getLastQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, firstPreDayConvertDate));
                            BigDecimal next = new BigDecimal(lq.getNextQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), crossQuotaDate, convertDate));
                            nowQuota = last.add(next);
                        } else {
                            if (hireDate > lq.getDisCycleStart() && lq.isCalNowQuotaByCurrentYearQuota()) {
                                nowQuota = currentYearQuota.multiply(getConvertRate(hireDate, lq.getDisCycleEnd(), startDate, terminationDate));
                            } else {
                                nowQuota = originalQuotaDay.multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, terminationDate));
                            }
                        }
                    } else {
                        BigDecimal last = new BigDecimal(lq.getLastQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, firstPreDayConvertDate));
                        BigDecimal next = new BigDecimal(lq.getNextQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), crossQuotaDate, now));
                        nowQuota = last.add(next);
                    }
                } else {
                    if (hireDate > lq.getDisCycleStart() && lq.isCalNowQuotaByCurrentYearQuota()) {
                        if (terminationDate != null && terminationDate > hireDate) {
                            Long convertDate = terminationDate <= now ? terminationDate : now;
                            nowQuota = currentYearQuota.multiply(getConvertRate(hireDate, lq.getDisCycleEnd(), startDate, convertDate));
                        } else {
                            nowQuota = currentYearQuota.multiply(getConvertRate(hireDate, lq.getDisCycleEnd(), startDate, now));
                        }
                    } else {
                        if (terminationDate != null && terminationDate > hireDate) {
                            Long convertDate = terminationDate <= now ? terminationDate : now;
                            nowQuota = originalQuotaDay.multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, convertDate));
                        } else {
                            nowQuota = originalQuotaDay.multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, now));
                        }
                    }
                }
            } else if (crossQuotaDate > 0 && crossQuotaDate <= now && lq.getLastQuota() != null && lq.getNextQuota() != null) {
                Long convertDate = lq.getCrossQuotaDate();
                Long preDayConvertDate = DateUtil.addDate(convertDate * 1000, -1);
                if (terminationDate != null && terminationDate > hireDate) {
                    // 员工离职
                    if (terminationDate >= convertDate) {
                        BigDecimal last = new BigDecimal(lq.getLastQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, preDayConvertDate));
                        BigDecimal next = new BigDecimal(lq.getNextQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), convertDate, terminationDate));
                        nowQuota = last.add(next);
                    } else {
                        if (hireDate > lq.getDisCycleStart() && lq.isCalNowQuotaByCurrentYearQuota()) {
                            nowQuota = currentYearQuota.multiply(getConvertRate(hireDate, lq.getDisCycleEnd(), startDate, terminationDate));
                        } else {
                            nowQuota = originalQuotaDay.multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, terminationDate));
                        }
                    }
                } else {
                    if (hireDate > convertDate) {
                        //入职年份
                        String hireYear = DateUtil.convertDateTimeToStr(hireDate, "yyyy", true);
                        //折算年份
                        String convertYear = DateUtil.convertDateTimeToStr(convertDate, "yyyy", true);
                        if (hireYear.equals(convertYear)) {
                            convertDate = hireDate;
                        }
                    }
                    BigDecimal last = new BigDecimal(lq.getLastQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, preDayConvertDate));
                    BigDecimal next = new BigDecimal(lq.getNextQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), convertDate, now));
                    nowQuota = last.add(next);
                }
            } else {
                if (hireDate > lq.getDisCycleStart() && lq.isCalNowQuotaByCurrentYearQuota()) {
                    if (terminationDate != null && terminationDate > hireDate) {
                        // 员工离职
                        nowQuota = currentYearQuota.multiply(getConvertRate(hireDate, lq.getDisCycleEnd(), startDate, terminationDate));
                    } else {
                        nowQuota = currentYearQuota.multiply(getConvertRate(hireDate, lq.getDisCycleEnd(), startDate, now));
                    }
                } else {
                    if (terminationDate != null && terminationDate > hireDate) {
                        // 员工离职
                        nowQuota = originalQuotaDay.multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, terminationDate));
                    } else {
                        nowQuota = originalQuotaDay.multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, now));
                    }
                }
            }
        }
        if (acctTimeType != null && 2 == acctTimeType && nowQuota.floatValue() > 0) {
            nowQuota = nowQuota.divide(new BigDecimal(60), 4, RoundingMode.DOWN);
        }
        if (nowQuota.floatValue() > 0) {
            nowQuota = handleMantissa(lq.getBelongOrgId(), nowQuota, lq.getAcctTimeType(), nowRoundingRule);
        }
        return nowQuota.floatValue();
    }

    @Transactional
    @Override
    public void autoCalEmpCrossQuota(String belongOrgId) {
        waEmpQuotaDo.updateQuotaDayByAnnualQuota(belongOrgId, DateUtil.getOnlyDate(), DateUtil.getCurrentTime(true));
    }

    /**
     * 处理折算-计算折算比率
     *
     * @param start
     * @param end
     * @param min
     * @param max
     * @return
     */
    @Override
    public BigDecimal getConvertRate(Long start, Long end, Long min, Long max) {
        if (start == null || end == null || min == null || max == null) {
            return new BigDecimal(1);
        }
        Long calStart = min;
        Long calEnd = max;
        if (calEnd < calStart || calEnd < start) {
            return BigDecimal.ZERO;
        }
        if (calStart > end) {
            return new BigDecimal(1);
        }
        if (calStart < start) {
            calStart = start;
        }
        if (calEnd > end) {
            calEnd = end;
        }
        try {
            Integer maxDay = DateUtilExt.getDifferenceDay(start, end) + 1;
            Integer periodDay = DateUtilExt.getDifferenceDay(calStart, calEnd) + 1;
            BigDecimal rate = new BigDecimal(periodDay).divide(new BigDecimal(maxDay), 4, RoundingMode.HALF_DOWN);
            if (rate.floatValue() > 1) {
                return new BigDecimal(1);
            }
            return rate;
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 处理进位
     *
     * @param val  配额数据 假期单位为天时，值单位为天 例如 2 天，假期单位小时时时，值单位为小时 例如 5 小时
     * @param unit 单位 1 天 2 小时
     * @param rule 折算规则
     * @return 假期单位为天时，返回值单位天，假期单位为小时时，返回值单位为分钟
     */
    @Override
    public BigDecimal handleMantissa(String belongId, BigDecimal val, Integer unit, Integer rule) {
        //1 四舍五入保留整数、2 向上取整1、3 向下取整1、4 向上取整0.5、5 向下取整0.5
        if (rule != null) {
            if (rule.equals(1)) {
                //四舍五入保留整数
                String func = waConfigService.getFuncExp(belongId, "HandleMantissa" + rule);
                if (StringUtils.isNotEmpty(func)) {
                    Map params = new HashMap();
                    params.put("value", val);
                    val = groovyScriptEngine.executeBigDecimal(func, params);
                } else {
                    val = PayEngineUtil.handleMantissa(val, (short) 1, (short) 0);
                }
            } else if (rule.equals(2)) {
                //向上取整1
                val = PayEngineUtil.handleMantissa(val, (short) 2, (short) 0);
            } else if (rule.equals(4)) {
                //向上取整0.5
                val = PayEngineUtil.handleMantissa(val.divide(BigDecimal.valueOf(0.5)), (short) 2, (short) 0).multiply(BigDecimal.valueOf(0.5));
            } else if (rule.equals(3)) {
                //向下取整1
                val = PayEngineUtil.handleMantissa(val, (short) 3, (short) 0);
            } else if (rule.equals(5)) {
                //向下取整0.5
                val = PayEngineUtil.handleMantissa(val.divide(BigDecimal.valueOf(0.5)), (short) 3, (short) 0).multiply(BigDecimal.valueOf(0.5));
            }
        }
        if (unit != null && 2 == unit) {
            // 假期单位为小时时返回分钟数
            val = val.multiply(BigDecimal.valueOf(60));
        }
        return val;
    }

    @Override
    public List<String> getQuotaHeaders(String belongOrgId) {
        List<String> list = new ArrayList<>();
        list.add("{\"id\": \"emp_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"" + ResponseWrap.wrapResult(AttendanceCodes.EMP_NAME, null).getMsg() + "\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"workno\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"" + ResponseWrap.wrapResult(AttendanceCodes.WORK_NO, null).getMsg() + "\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\":\"org_name\",\"width\":\"200px\",\"type\":\"ro\",\"sort\":\"str\",\"value\":\"" + ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg() + "\",\"filter\":{\"type\":\"combo\",\"name\":\"orgid\",\"attach\":{\"type\":\"tree\",\"config\":{\"connector\":\"org/getOrgTreeJson\",\"lazy\":\"org/getSubOrgTreeJson\"}}},\"ellipsis\":true}");
        list.add("{\"id\": \"hire_date\", \"width\": \"150px\", \"type\": \"date\", \"value\": \"" + ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg() + "\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"first_work_date\", \"width\": \"150px\", \"type\": \"date\", \"value\": \"" + ResponseWrap.wrapResult(AttendanceCodes.FIRST_WORK_DATE, null).getMsg() + "\",\"filter\":{\"type\":\"str\"}}");
        Map<String, String> map = new HashMap<>();
        map.put("belongOrgId", belongOrgId);
        List<Map> allLeaveType = waMapper.getAllLeaveType(map);
        if (CollectionUtils.isNotEmpty(allLeaveType)) {
            allLeaveType.forEach(lt -> list.add("{\"id\": \"lt" + lt.get("leave_type_def_id") + "_key\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + LangParseUtil.getI18nLanguage((String) lt.get("i18n_leave_type_def_name"), (String) lt.get("leave_type_def_name")) + "\"}"));
        }
        return list;
    }

    @Override
    public List<Map> queryEmpQuotaList(QuotaEmpDto dto, PageBean pageBean, String belongOrgId) {
        Long today = DateUtil.getOnlyDate(new Date());
        Map params = new HashMap();
        params.put("belongId", belongOrgId);
        String filter = pageBean.getFilter();
        if (filter != null && filter.indexOf("orgid") != -1) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        params.put("filter", filter);
        params.put("datafilter", dto.getDataScope());
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            params.put("keywords", dto.getKeywords());
        }
        //先查员工
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        List<Map> empList = waMapper.queryQuotaEmpInfo(pageBounds, params);
        List<Long> empIdList = new ArrayList<>();
        Map ap = new HashMap();
        for (Map map : empList) {
            Long empId = (Long) map.get("empId");
            empIdList.add(empId);
        }
        if (CollectionUtils.isEmpty(empIdList)) {
            return new ArrayList<>();
        }
        //然后查配额
        ap.put("empids", empIdList);
        ap.put("today", today);
        List<Map> leaveList = empCompensatoryQuotaDo.queryEmpQuotaList(ap);
        Map<String, List<Map>> empLeaveList = leaveList.stream().collect(Collectors.groupingBy(e -> e.get("empId").toString()));
        Map<String, Map<String, BigDecimal>> quotaMap = new HashMap<>();
        Map<String, Integer> accTypeMap = new HashMap<>();
        for (Map.Entry<String, List<Map>> entry : empLeaveList.entrySet()) {
            Map<String, BigDecimal> map = new HashMap<>();
            String key = entry.getKey();
            List<Map> leaveMapList = entry.getValue();
            for (Map leaveMap : leaveMapList) {
                //本年额度
                BigDecimal quotaDay = BigDecimal.valueOf((Float) leaveMap.get("quota_day"));
                //本年已使用
                BigDecimal usedDay = BigDecimal.valueOf((Float) leaveMap.get("used_day"));
                //当前配额
                BigDecimal nowQuota = BigDecimal.valueOf((Float) leaveMap.get("now_quota"));
                //调整配额
                BigDecimal adjustQuota = BigDecimal.valueOf((Float) leaveMap.get("adjust_quota"));
                //调整已使用
                BigDecimal fixUsedDay = BigDecimal.valueOf((Float) leaveMap.get("fix_used_day"));
                //在途配额
                BigDecimal inTransitQuota = BigDecimal.valueOf((Float) leaveMap.get("in_transit_quota"));
                //单位
                Integer acctTimeType = (Integer) leaveMap.get("acct_time_type");//时间单位
                //假期类型
                Integer leave_type = (Integer) leaveMap.get("leave_type");
                //是否可预支
                Integer ifAdvance = (Integer) leaveMap.get("if_advance");
                //发放类型
                Integer quotaType = (Integer) leaveMap.get("quota_type");

                BigDecimal annualLeave;
                BigDecimal fixLeave;
                BigDecimal restDay;
                String leaveKey = String.format("lt%s_key", leave_type);
                Long empId = (Long) leaveMap.get("empId");
                String acctTypeKey = String.format("%s_%s", empId, leaveKey);
                if (!accTypeMap.containsKey(acctTypeKey)) {
                    accTypeMap.put(acctTypeKey, acctTimeType);
                }
                if (QuotaTypeEnum.ISSUED_ANNUALLY.getIndex().equals(quotaType) || QuotaTypeEnum.PARENTAL_LEAVE.getIndex().equals(quotaType)) {
                    if (QuotaAdvanceEnum.NO_ADVANCE.getIndex().equals(ifAdvance)) {
                        annualLeave = nowQuota.add(adjustQuota).subtract(usedDay).subtract(fixUsedDay).subtract(inTransitQuota);
                    } else {
                        annualLeave = quotaDay.add(adjustQuota).subtract(usedDay).subtract(fixUsedDay).subtract(inTransitQuota);
                    }
                    if (map.get(leaveKey) == null) {
                        map.put(leaveKey, annualLeave);
                    } else {
                        map.put(leaveKey, map.getOrDefault(leaveKey, BigDecimal.ZERO).add(annualLeave));
                    }
                } else if (QuotaTypeEnum.FIXED_QUOTA.getIndex().equals(quotaType)) {
                    fixLeave = quotaDay.add(adjustQuota).subtract(usedDay).subtract(inTransitQuota).subtract(fixUsedDay);
                    if (usedDay.compareTo(BigDecimal.ZERO) > 0 || inTransitQuota.compareTo(BigDecimal.ZERO) > 0) {
                        // 针对固定额度，只要有已使用，则变为 0
                        fixLeave = BigDecimal.ZERO;
                    }
                    if (map.get(leaveKey) == null) {
                        map.put(leaveKey, fixLeave);
                    } else {
                        map.put(leaveKey, map.getOrDefault(leaveKey, BigDecimal.ZERO).add(fixLeave));
                    }
                } else if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(quotaType)) {
                    // 调休
                    restDay = quotaDay.add(adjustQuota).subtract(usedDay).subtract(inTransitQuota);
                    if (map.get(leaveKey) == null) {
                        map.put(leaveKey, restDay);
                    } else {
                        map.put(leaveKey, map.getOrDefault(leaveKey, BigDecimal.ZERO).add(restDay));
                    }
                }
            }
            quotaMap.put(key, map);
        }

        for (Map map : empList) {
            String empId = map.get("empId").toString();
            String workno = map.get("workno").toString();
            String empName = map.get("emp_name") == null ? "" : map.get("emp_name").toString();
            map.put("emp_name", empName);
            Map<String, BigDecimal> leaveMap = quotaMap.get(empId);
            Map<String, String> quotaLeaveMap = new HashMap<>();
            if (leaveMap != null) {
                for (Map.Entry<String, BigDecimal> entry : leaveMap.entrySet()) {
                    String key = entry.getKey();
                    BigDecimal value = entry.getValue();
                    String acctTypeKey = String.format("%s_%s", empId, key);
                    if (accTypeMap.get(acctTypeKey) != null) {
                        Integer accType = accTypeMap.get(acctTypeKey);
                        Float floatValue = formatFloat(accType, value.floatValue(), 1);
                        String accTypeUnit = PreTimeUnitEnum.getName(accType);
                        String str = String.valueOf(floatValue);
                        str = str + accTypeUnit;
                        quotaLeaveMap.put(key, str);
                    }
                }
                map.putAll(quotaLeaveMap);
            }
        }
        return empList;
    }

    private void mapValueNumberFormat(Map map, String mapKey, Float mapValue) {
        BigDecimal bValue = BigDecimal.valueOf(mapValue);
        float value = bValue.setScale(2, RoundingMode.HALF_DOWN).floatValue();
        map.put(mapKey, value);
    }

    @Override
    @CDText(exp = {"empStatus:empStatusName" + TextAspect.STATUS_ENUM}, classType = EmpFixQuotaVo.class)
    public AttendancePageResult<EmpFixQuotaVo> getEmpFixQuotaList(FixQuotaSearchDto dto, UserInfo userInfo) {
        val belongOrgId = userInfo.getTenantId();
        val doPageList = waEmpQuotaDo.getEmpFixQuotaList(dto, belongOrgId);
        List<EmpFixQuotaVo> voList = doPageList.stream().map(quotaDo -> {
            val vo = new EmpFixQuotaVo();
            BeanUtils.copyProperties(quotaDo, vo);
            var quota = vo.getQuotaDay();
            if (null == quota) {
                quota = 0f;
            }
            var adjustQuota = vo.getAdjustQuota();
            if (null == adjustQuota) {
                adjustQuota = 0f;
            }
            var used = vo.getUsedDay();
            if (null == used) {
                used = 0f;
            }
            var transit = vo.getInTransitQuota();
            if (null == transit) {
                transit = 0f;
            }
            vo.setLeftQuota(quota - used - transit + adjustQuota);
            // 状态 0 生效 1 失效
            if (used > 0 || transit > 0) {
                vo.setStatus(1);
            } else {
                vo.setStatus(0);
            }
            Integer carryRule = 1;
            vo.setQuotaDay(formatFloat(quotaDo.getAcctTimeType(), quota, carryRule));
            vo.setAdjustQuota(formatFloat(quotaDo.getAcctTimeType(), adjustQuota, carryRule));
            vo.setUsedDay(formatFloat(quotaDo.getAcctTimeType(), used, carryRule));
            vo.setInTransitQuota(formatFloat(quotaDo.getAcctTimeType(), transit, carryRule));
            vo.setLeftQuota(formatFloat(quotaDo.getAcctTimeType(), vo.getLeftQuota(), carryRule));
            vo.setSocialProvinceCityName("-");
            String provinceName = getProvinceName(quotaDo.getSocialProvince());
            String cityName = getProvinceName(quotaDo.getSocialCity());
            if (StringUtil.isNotEmpty(provinceName) && StringUtil.isNotEmpty(cityName)) {
                vo.setSocialProvinceCityName(String.format("%s/%s", provinceName, cityName));
            }
            EmpInfoDTO empInfo = new EmpInfoDTO();
            empInfo.setEmpId(quotaDo.getEmpid());
            empInfo.setName(vo.getEmpName());
            empInfo.setWorkno(vo.getWorkno());
            vo.setEmpInfo(empInfo);
            vo.setLeaveTypeName(LangParseUtil.getI18nLanguage(quotaDo.getI18nLeaveTypeName(), quotaDo.getLeaveTypeName()));
            return vo;
        }).collect(Collectors.toList());
        return new AttendancePageResult<EmpFixQuotaVo>(voList, dto.getPageNo(), dto.getPageSize(), doPageList.getPaginator().getTotalCount());
    }

    public String getProvinceName(Long id) {
        String provinceName = "";
        if (id != null) {
            SysUnitCity province = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, id);
            if (province != null) {
                provinceName = province.getChnName();
            }
        }
        return provinceName;
    }

    @Override
    public String saveOrUpdateFixQuota(FixQuotaDto dto) {
        val user = sessionService.getUserInfo();
        WaLeaveTypeDo leaveTypeDo = waLeaveTypeDo.selectById(dto.getLeaveTypeId().intValue());
        if (leaveTypeDo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_QUOTA_RULE_DOES_NOT_EXIST, null).getMsg();
        }
        if (LeaveTypeUnitEnum.HOUR.getIndex().equals(leaveTypeDo.getAcctTimeType())) {
            if (dto.getQuotaDay() != null) {
                dto.setQuotaDay(dto.getQuotaDay() * 60);
            }
            if (dto.getAdjustQuota() != null) {
                dto.setAdjustQuota(dto.getAdjustQuota() * 60);
            }
        }
        if (dto.getQuotaDay() == null) {
            dto.setQuotaDay(0f);
        }
        if (dto.getAdjustQuota() == null) {
            dto.setAdjustQuota(0f);
        }
        if (null == dto.getEmpQuotaId()) {
            LogRecordContext.putVariable("operate", "新增");
            waEmpQuotaDo.insertFixQuota(dto, user);
        } else {
            LogRecordContext.putVariable("operate", "编辑");
            waEmpQuotaDo.updateFixQuota(dto, user);
        }
        return "";
    }

    @Override
    public void deleteFixQuota(Long empQuotaId) {
        WaEmpQuotaDo quotaDo = waEmpQuotaDo.getEmpFixQuota(empQuotaId);
        if (quotaDo == null) {
            //throw new ServerException("固定配额不存在");
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_NOT_EXIST, null).getMsg());
        }
        if (quotaDo.getUsedDay() != null && quotaDo.getUsedDay() > 0) {
            //throw new ServerException("该配额已被使用，不可删除");
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_USED_DELETE_NOT_ALLOW, null).getMsg());
        }
        if (quotaDo.getInTransitQuota() != null && quotaDo.getInTransitQuota() > 0) {
            //throw new ServerException("该配额已被使用，不可删除");
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_USED_DELETE_NOT_ALLOW, null).getMsg());
        }
        waEmpQuotaDo.deleteFixQuota(empQuotaId);
    }

    /**
     * 计算当前可用配额（配额类型：按年发放）
     *
     * @param belongOrgId
     * @param date
     * @param empId
     */
    @Transactional
    @Override
    public void autoGenEmpNowQuotaForIssuedAnnuallyByOrg(String belongOrgId, Long date, Long empId) {
        Long convertDate = date == null ? DateUtil.getOnlyDate() : date;
        log.info("autoGenEmpNowQuotaForIssuedAnnuallyByOrg belongOrgId {} time {} convertDate {}", belongOrgId, System.currentTimeMillis(), convertDate);

        //查询当天或者前一天生效中的配额数据
        AttendanceBasePage basePage = new AttendanceBasePage();
        basePage.setPageSize(PAGE_SIZE);
        basePage.setPageNo(1);
        Long preDate = DateUtil.addDate(convertDate * 1000, -1);
        AttendancePageResult<Map> pageResult = waEmpQuotaDo.getIssuedAnnuallyQuotaListByEffectiveTime(basePage, belongOrgId, preDate, convertDate, empId);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getItems())) {
            log.info("autoGenEmpNowQuotaForIssuedAnnuallyByOrg getCurrentlyEffectEmpQuotaList is empty");
            return;
        }
        List<WaEmpQuota> empQuotaUpdList = new ArrayList<>();
        for (Map map : pageResult.getItems()) {
            addWaEmpQuotaToUpdList(map, convertDate, empQuotaUpdList);
        }
        //总条数
        int totalCount = pageResult.getTotal();
        if (totalCount > PAGE_SIZE) {
            //总页数
            int totalPage = (int) Math.ceil((double) totalCount / PAGE_SIZE);
            for (int pageNo = 2; pageNo <= totalPage; pageNo++) {
                basePage.setPageNo(pageNo);
                pageResult = waEmpQuotaDo.getIssuedAnnuallyQuotaListByEffectiveTime(basePage, belongOrgId, preDate, convertDate, empId);
                for (Map map : pageResult.getItems()) {
                    addWaEmpQuotaToUpdList(map, convertDate, empQuotaUpdList);
                }
            }
        }
        importService.fastUpdList(WaEmpQuota.class, "empQuotaId", empQuotaUpdList, Collections.singletonList("nowQuota"));
        log.info("autoGenEmpNowQuotaForIssuedAnnuallyByOrg finish time {}", System.currentTimeMillis());
    }

    private void addWaEmpQuotaToUpdList(Map map, Long convertDate, List<WaEmpQuota> empQuotaUpdList) {
        Long empid = (Long) map.get("empid");
        String belongOrgid = (String) map.get("belong_org_id");
        Long hireDate = (Long) map.get("hire_date");
        Long prodeadLine = (Long) map.get("prodead_line");
        Long terminationDate = (Long) map.get("termination_date");
        Integer acctTimeType = (Integer) map.get("acct_time_type");
        Float quotaDay = (Float) map.get("quota_day");
        Long disCycleStart = (Long) map.get("dis_cycle_start");
        Long disCycleEnd = (Long) map.get("dis_cycle_end");
        Float originalQuotaDay = (Float) map.get("originalQuotaDay");
        // crossQuotaDate 有值代表配额跨额度规则
        Long crossQuotaDate = (Long) map.get("crossQuotaDate");
        Integer lastQuota = (Integer) map.get("lastQuota");
        Integer nextQuota = (Integer) map.get("nextQuota");
        BigDecimal oldNowQuota = BigDecimal.valueOf((Float) map.get("now_quota"));
        Integer leaveTypeId = (Integer) map.get("leaveTypeId");
        Long secCrossQuotaDate = (Long) map.get("secCrossQuotaDate");
        Integer thirdQuota = (Integer) map.get("thirdQuota");
        // 假期规则信息
        Integer nowDistributeRule = (Integer) map.get("now_distribute_rule");
        Integer nowRoundingRule = (Integer) map.get("now_rounding_rule");
        Integer ifAdvance = (Integer) map.get("if_advance");
        Long quotaConfigId = (Long) map.get("quotaConfigId");
        Integer quotaDistributeRule = (Integer) map.get("quotaDistributeRule");
        Integer dayOfHireMonthDist = (Integer) map.get("dayOfHireMonthDist");
        if (null == quotaConfigId) {
            // 兼容历史数据
            List<LeaveQuotaConfigDo> quotaConfigDoList = leaveQuotaConfigDo.getByLeaveTypeId(belongOrgid, leaveTypeId);
            if (CollectionUtils.isNotEmpty(quotaConfigDoList)) {
                LeaveQuotaConfigDo quotaConfigDo = quotaConfigDoList.get(0);
                nowDistributeRule = quotaConfigDo.getNowDistributeRule();
                nowRoundingRule = quotaConfigDo.getNowRoundingRule();
                ifAdvance = quotaConfigDo.getIfAdvance();
                quotaConfigId = quotaConfigDo.getConfigId();
                quotaDistributeRule = quotaConfigDo.getQuotaDistributeRule();
                dayOfHireMonthDist = quotaConfigDo.getDayOfHireMonthDist();
            }
        }
        ifAdvance = ifAdvance == null ? 0 : ifAdvance;

        EmpLeaveQuotaDto leaveQuotaDto = new EmpLeaveQuotaDto();
        leaveQuotaDto.setEmpId(empid);
        leaveQuotaDto.setBelongOrgId(belongOrgid);
        leaveQuotaDto.setHireDate(hireDate);
        leaveQuotaDto.setProdeadLine(prodeadLine);
        leaveQuotaDto.setTerminationDate(terminationDate);
        leaveQuotaDto.setLeaveTypeId(leaveTypeId);
        leaveQuotaDto.setAcctTimeType(acctTimeType);
        leaveQuotaDto.setQuotaVal(new BigDecimal(quotaDay));
        leaveQuotaDto.setDisCycleStart(disCycleStart);
        leaveQuotaDto.setDisCycleEnd(disCycleEnd);
        leaveQuotaDto.setOriginalQuotaDay(new BigDecimal(originalQuotaDay));
        leaveQuotaDto.setIfAdvance(ifAdvance);
        leaveQuotaDto.setNowDistributeRule(nowDistributeRule);
        leaveQuotaDto.setNowRoundingRule(nowRoundingRule);
        leaveQuotaDto.setCrossQuotaDate(crossQuotaDate);
        leaveQuotaDto.setSecCrossQuotaDate(secCrossQuotaDate);
        leaveQuotaDto.setLastQuota(lastQuota);
        leaveQuotaDto.setNextQuota(nextQuota);
        leaveQuotaDto.setThirdQuota(thirdQuota);
        leaveQuotaDto.setCalNowQuotaConvertDate(convertDate);
        leaveQuotaDto.setEmpStatus((Integer) map.get("empStatus"));
        // 计算当前配额
        leaveQuotaDto.setWaEmpQuotaQuotaDay(quotaDay);
        leaveQuotaDto.setCalNowQuotaByCurrentYearQuota(true);
        leaveQuotaDto.setQuotaDistributeRule(quotaDistributeRule);
        leaveQuotaDto.setDayOfHireMonthDist(dayOfHireMonthDist);
        Float nowQuota = calNowQuota(leaveQuotaDto);
        nowQuota = nowQuota == null ? 0f : nowQuota;
        if (!nowQuota.equals(oldNowQuota.floatValue())) {
            WaEmpQuota empQuota = new WaEmpQuota();
            empQuota.setEmpQuotaId((Integer) map.get("emp_quota_id"));
            empQuota.setNowQuota(nowQuota);
            empQuota.setUpdtime(System.currentTimeMillis() / 1000);
            empQuotaUpdList.add(empQuota);
        }
    }

    @Override
    public Result<String> deleteCompensatoryQuotas(List<Long> quotaIds) {
        UserInfo userInfo = sessionService.getUserInfo();
        String tenantId = userInfo.getTenantId();
        List<EmpCompensatoryQuotaDo> quotas = empCompensatoryQuotaDo.getQuotaListByIds(tenantId, quotaIds);
        if (CollectionUtils.isEmpty(quotas)) {
            return ResponseWrap.wrapResult("");
        }
        LogRecordContext.putVariable("num", quotaIds.size());
        List<EmpCompensatoryQuotaDo> deleteQuotas = quotas.stream().filter(quota -> !((quota.getUsedDay() != null && quota.getUsedDay() > 0) || (quota.getInTransitQuota() != null && quota.getInTransitQuota() > 0))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteQuotas)) {
            List<Long> deleteQuotaIds = deleteQuotas.stream().map(EmpCompensatoryQuotaDo::getQuotaId).distinct().collect(Collectors.toList());
            empCompensatoryQuotaDo.deleteByIds(tenantId, deleteQuotaIds);
            List<Long> deleteTravelQuotaIds = deleteQuotas.stream().filter(quota -> DataSourceEnum.TRAVEL.name().equals(quota.getDataSource())).map(EmpCompensatoryQuotaDo::getQuotaId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deleteTravelQuotaIds)) {
                travelCompensatoryDo.batchDelete(deleteTravelQuotaIds, userInfo.getUserId());
            }
            //调休结转记录
            List<WaCompensatoryQuotaRecordDo> records = compensatoryQuotaRecordDo.getCompensatoryQuotaRecord(tenantId, deleteQuotaIds, Collections.singletonList(ApprovalStatusEnum.PASSED.getIndex()));
            if (CollectionUtils.isNotEmpty(records)) {
                WaEmpOvertimeDetailExample example = new WaEmpOvertimeDetailExample();
                WaEmpOvertimeDetailExample.Criteria criteria = example.createCriteria();
                criteria.andDetailIdIn(records.stream().map(WaCompensatoryQuotaRecordDo::getDetailId).distinct().collect(Collectors.toList()));
                List<WaEmpOvertimeDetail> details = waEmpOvertimeDetailMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(details)) {
                    Map<Integer, List<WaCompensatoryQuotaRecordDo>> map = records.stream().collect(Collectors.groupingBy(WaCompensatoryQuotaRecordDo::getDetailId));
                    List<WaEmpOvertimeDetail> updDetails = Lists.newArrayList();
                    for (WaEmpOvertimeDetail detail : details) {
                        if (map.containsKey(detail.getDetailId())) {
                            List<WaCompensatoryQuotaRecordDo> rows = map.get(detail.getDetailId());
                            WaEmpOvertimeDetail updDetail = new WaEmpOvertimeDetail();
                            Float totalDuration = BigDecimal.valueOf(rows.stream().mapToDouble(WaCompensatoryQuotaRecordDo::getCarryDuration).sum()).floatValue();
                            updDetail.setDetailId(detail.getDetailId());
                            int compareTo = new BigDecimal(detail.getTimeDuration().toString()).compareTo(new BigDecimal(String.valueOf(detail.getLeftDuration() + totalDuration)));
                            updDetail.setLeftDuration(compareTo == 0 ? 0f : detail.getLeftDuration() + totalDuration);
                            updDetail.setCarriedForward(0);
                            updDetail.setUpdtime(DateUtil.getCurrentTime(true));
                            updDetails.add(updDetail);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(updDetails)) {
                        importService.fastUpdList(WaEmpOvertimeDetail.class, "detailId", updDetails);
                    }
                }
                //删除调休结转记录
                compensatoryQuotaRecordDo.deleteByQuotaIds(tenantId, deleteQuotaIds);
            }

            return ResponseWrap.wrapResult(String.format(ResponseWrap.wrapResult(AttendanceCodes.DELETE_COMPENSATORY_REMINDER, null).getMsg(), deleteQuotaIds.size(), quotaIds.size() - deleteQuotaIds.size()));
        }
        return ResponseWrap.wrapResult(String.format(ResponseWrap.wrapResult(AttendanceCodes.DELETE_COMPENSATORY_REMINDER, null).getMsg(), deleteQuotas.size(), quotaIds.size()));
    }

    @Override
    public Result<String> deleteFixQuotas(List<Integer> empQuotaIds) {
        UserInfo userInfo = sessionService.getUserInfo();
        String tenantId = userInfo.getTenantId();
        LogRecordContext.putVariable("num", empQuotaIds.size());
        List<WaEmpQuotaDo> list = waEmpQuotaDo.getEmpFixQuotaByIds(tenantId, empQuotaIds);
        if (CollectionUtils.isEmpty(list)) {
            return ResponseWrap.wrapResult("success");
        }
        List<WaEmpQuotaDo> deleteQuotas = list.stream().filter(quota -> !((quota.getUsedDay() != null && quota.getUsedDay() > 0) || (quota.getInTransitQuota() != null && quota.getInTransitQuota() > 0))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteQuotas)) {
            List<Integer> deleteQuotaIds = deleteQuotas.stream().map(WaEmpQuotaDo::getEmpQuotaId).distinct().collect(Collectors.toList());
            dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_FIX_QUOTA.name(), ModuleTypeEnum.EMP_FIX_QUOTA.getTable(), deleteQuotas, userInfo));
            waEmpQuotaDo.deleteFixQuotas(tenantId, deleteQuotaIds);

            return ResponseWrap.wrapResult(String.format(ResponseWrap.wrapResult(AttendanceCodes.DELETE_COMPENSATORY_REMINDER, null).getMsg(), deleteQuotaIds.size(), empQuotaIds.size() - deleteQuotaIds.size()));
        }
        return ResponseWrap.wrapResult(String.format(ResponseWrap.wrapResult(AttendanceCodes.DELETE_COMPENSATORY_REMINDER, null).getMsg(), deleteQuotas.size(), empQuotaIds.size()));
    }

    @Transactional
    @Override
    public void reDecEmpLeaveQuota(ReDecEmpLeaveQuotaDto dto) {
        UserInfo user = sessionService.getUserInfo();
        String belongOrgId = dto.getBelongOrgId() == null ? user.getTenantId() : dto.getBelongOrgId();
        Long userId = dto.getUserid() == null ? user.getUserId() : dto.getUserid();
        if (belongOrgId == null || userId == null) {
            log.info("reDecEmpLeaveQuota param is empty");
            return;
        }
        // 只查调休
        dto.setQuotaType(QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex());
        // 查询休假单据
        AttendanceBasePage basePage = new AttendanceBasePage();
        basePage.setPageSize(PAGE_SIZE);
        basePage.setPageNo(1);
        AttendancePageResult<WaEmpLeaveDo> pageResult = waEmpLeaveDo.getWaEmpLeaveListForHaveQuota(basePage, belongOrgId, dto.getLeaveTypeId(),
                dto.getQuotaType(), dto.getLeaveId(), dto.getEmpId());
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getItems())) {
            return;
        }
        List<WaEmpLeaveDo> leaveAllList = pageResult.getItems();
        //总条数
        int totalCount = pageResult.getTotal();
        if (totalCount > PAGE_SIZE) {
            //总页数
            int totalPage = (int) Math.ceil((double) totalCount / PAGE_SIZE);
            for (int pageNo = 2; pageNo <= totalPage; pageNo++) {
                basePage.setPageNo(pageNo);
                pageResult = waEmpLeaveDo.getWaEmpLeaveListForHaveQuota(basePage, belongOrgId, dto.getLeaveTypeId(),
                        dto.getQuotaType(), dto.getLeaveId(), dto.getEmpId());
                leaveAllList.addAll(pageResult.getItems());
            }
        }
        log.info("reDecEmpLeaveQuota exe data size {}", leaveAllList.size());
        // 公司所有的假期类型
        WaLeaveTypeExample example = new WaLeaveTypeExample();
        example.createCriteria().andBelongOrgidEqualTo(belongOrgId);
        List<WaLeaveType> leaveTypeList = waLeaveTypeMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(leaveTypeList)) {
            return;
        }
        Map<Integer, WaLeaveType> corpAllLeaveTypeMap = leaveTypeList.stream().collect(Collectors.toMap(WaLeaveType::getLeaveTypeId, Function.identity()));
        // 目前只支持清除调休配额，年假配后面可继续支持
        // 清除配额使用明细，初始化配额已用为0
        List<String> empIdAndLeaveTypeIdList = new ArrayList<>();
        for (WaEmpLeaveDo empLeave : leaveAllList) {
            Integer leaveTypeId = empLeave.getLeaveTypeId();
            Long empId = empLeave.getEmpid();
            String empLtKey = String.format("%s_%s", empId, leaveTypeId);
            if (!empIdAndLeaveTypeIdList.contains(empLtKey)) {
                empIdAndLeaveTypeIdList.add(empLtKey);
                // 查询假期类型
                WaLeaveType waLeaveType = corpAllLeaveTypeMap.get(leaveTypeId);
                if (waLeaveType == null) {
                    continue;
                }
                //请假配额校验或者扣减 QuotaRestrictionType 额度限制类型： 1 限额、2 不限额
                if (waLeaveType.getQuotaRestrictionType() != null && QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType())) {
                    //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
                    Integer quotaType = waLeaveType.getQuotaType();
                    if (quotaType == null) {
                        if (waLeaveType.getLeaveType() == 3) {
                            //假期类型为调休
                            quotaType = QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex();
                        } else {
                            quotaType = QuotaTypeEnum.ISSUED_ANNUALLY.getIndex();
                        }
                    }
                    if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(quotaType)) {
                        // 调休
                        // 根据租户ID+员工ID+假期类型ID查询员工调休额度
                        List<EmpCompensatoryQuotaDo> quotaDoList = empCompensatoryQuotaDo.getQuotaListByEmpIdAndLeaveType(belongOrgId, empId, leaveTypeId);
                        if (CollectionUtils.isEmpty(quotaDoList)) {
                            continue;
                        }
                        List<Long> quotaIdList = quotaDoList.stream().map(EmpCompensatoryQuotaDo::getQuotaId).collect(Collectors.toList());
                        // 删除假期配额使用明细 wa_compensatory_quota_use
                        WaCompensatoryQuotaUseExample useDelExample = new WaCompensatoryQuotaUseExample();
                        useDelExample.createCriteria().andEmpIdEqualTo(empId).andQuotaIdIn(quotaIdList);
                        waCompensatoryQuotaUseMapper.deleteByExample(useDelExample);
                        // 更新员工假期配额的已使用和在途配额为0 wa_emp_compensatory_quota
                        List<EmpCompensatoryQuotaDo> quotaDoUpdList = quotaIdList.stream().map(id -> {
                            EmpCompensatoryQuotaDo quotaDo = new EmpCompensatoryQuotaDo();
                            quotaDo.setUsedDay(0f);
                            quotaDo.setInTransitQuota(0f);
                            quotaDo.setQuotaId(id);
                            return quotaDo;
                        }).collect(Collectors.toList());
                        empCompensatoryQuotaDo.batchUpdate(quotaDoUpdList);
                    } else {
                        // TODO 后面有需要再补充非调休逻辑
                    }
                }
            }
        }
        List<WaEmpLeaveDo> effectiveLeaveList = leaveAllList.stream().filter(o -> (LeaveStatusEnum.LEAVE_STATUS_1.value.equals(o.getStatus()) ||
                LeaveStatusEnum.LEAVE_STATUS_2.value.equals(o.getStatus()))).collect(Collectors.toList());
        // 遍历休假数据，进行额度扣减
        for (WaEmpLeaveDo empLeave : effectiveLeaveList) {
            Integer leaveTypeId = empLeave.getLeaveTypeId();
            Integer leaveId = empLeave.getLeaveId();

            // 查询假期类型
            WaLeaveType waLeaveType = corpAllLeaveTypeMap.get(leaveTypeId);
            if (waLeaveType == null) {
                continue;
            }
            // 查询休假明细
            WaLeaveDaytimeExample daytimeExample = new WaLeaveDaytimeExample();
            daytimeExample.createCriteria().andLeaveIdEqualTo(leaveId);
            List<WaLeaveDaytime> allDaytimeList = waLeaveDaytimeMapper.selectByExample(daytimeExample);
            if (CollectionUtils.isEmpty(allDaytimeList)) {
                continue;
            }
            allDaytimeList = allDaytimeList.stream().sorted(Comparator.comparing(WaLeaveDaytime::getLeaveDate)).collect(Collectors.toList());
            List<Integer> leaveDaytimeIdList = allDaytimeList.stream().map(WaLeaveDaytime::getLeaveDaytimeId).collect(Collectors.toList());
            if (waLeaveType.getLeaveType() != 4) {
                //请假配额校验或者扣减 QuotaRestrictionType 额度限制类型： 1 限额、2 不限额
                if (waLeaveType.getQuotaRestrictionType() != null && QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType())) {
                    //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
                    Integer quotaType = waLeaveType.getQuotaType();
                    if (quotaType == null) {
                        if (waLeaveType.getLeaveType() == 3) {
                            //假期类型为调休
                            quotaType = QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex();
                        } else {
                            quotaType = QuotaTypeEnum.ISSUED_ANNUALLY.getIndex();
                        }
                    }
                    if (LeaveStatusEnum.LEAVE_STATUS_1.value.equals(empLeave.getStatus())) {
                        // 审核中
                        if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(quotaType)) {
                            mobileV16Service.checkEmpCompensatoryQuota(userId, allDaytimeList, empLeave.getEmpid(), waLeaveType, false, Integer.valueOf(empLeave.getStatus()), true);
                            List<Long> ids = leaveDaytimeIdList.stream().map(Long::valueOf).collect(Collectors.toList());
                            empCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(userId, System.currentTimeMillis(), ids, Integer.valueOf(empLeave.getStatus()));
                        } else {
                            mobileV16Service.checkEmpQuota(userId, allDaytimeList, empLeave.getEmpid(), waLeaveType, false, Integer.valueOf(empLeave.getStatus()), true);
                            waLeaveQuotaUseMapper.updateWaEmpQuota(userId, System.currentTimeMillis(), leaveDaytimeIdList, Integer.valueOf(empLeave.getStatus()));
                        }
                    } else if (LeaveStatusEnum.LEAVE_STATUS_2.value.equals(empLeave.getStatus())) {
                        // 审核通过
                        if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(quotaType)) {
                            mobileV16Service.checkEmpCompensatoryQuota(userId, allDaytimeList, empLeave.getEmpid(), waLeaveType, false, Integer.valueOf(empLeave.getStatus()), true);
                            QueryWrapper<WaCompensatoryQuotaUse> qw = new QueryWrapper<>();
                            qw.in("leave_daytime_id", leaveDaytimeIdList);
                            WaCompensatoryQuotaUse waCompensatoryQuotaUse = new WaCompensatoryQuotaUse();
                            waCompensatoryQuotaUse.setApprovalStatus(Integer.valueOf(empLeave.getStatus()));
                            waCompensatoryQuotaUse.setUpdateBy(userId);
                            waCompensatoryQuotaUse.setUpdateTime(System.currentTimeMillis());
                            waCompensatoryQuotaUseMapper.update(waCompensatoryQuotaUse, qw);
                            List<Long> ids = leaveDaytimeIdList.stream().map(Long::valueOf).collect(Collectors.toList());
                            empCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(userId, System.currentTimeMillis(), ids, Integer.valueOf(empLeave.getStatus()));
                        } else {
                            mobileV16Service.checkEmpQuota(userId, allDaytimeList, empLeave.getEmpid(), waLeaveType, false, Integer.valueOf(empLeave.getStatus()), true);
                            WaLeaveQuotaUseExample useExample = new WaLeaveQuotaUseExample();
                            useExample.createCriteria().andLeaveDaytimeIdIn(leaveDaytimeIdList);
                            WaLeaveQuotaUse quotaUse = new WaLeaveQuotaUse();
                            quotaUse.setApprovalStatus(Integer.valueOf(empLeave.getStatus()));
                            quotaUse.setUpdateBy(userId);
                            quotaUse.setUpdateTime(System.currentTimeMillis());
                            waLeaveQuotaUseMapper.updateByExampleSelective(quotaUse, useExample);
                            waLeaveQuotaUseMapper.updateWaEmpQuota(userId, System.currentTimeMillis(), leaveDaytimeIdList, Integer.valueOf(empLeave.getStatus()));
                        }
                    }
                }
            }
        }
    }

    @Override
    public Result<Boolean> update(EmpCompensatoryQuotaDto dto) {
        UserInfo userInfo = sessionService.getUserInfo();
        EmpCompensatoryQuotaDo quotaDo = ObjectConverter.convert(dto, EmpCompensatoryQuotaDo.class);
        quotaDo.setEmpId(dto.getEmpInfo().getEmpId());
        quotaDo.setTenantId(userInfo.getTenantId());
        EmpCompensatoryQuotaDo empCompensatoryQuota = empCompensatoryQuotaDo.getByQuotaId(quotaDo.getQuotaId());
        if (null != empCompensatoryQuota) {
            if (DataSourceEnum.MANUAL.name().equals(empCompensatoryQuota.getDataSource())) {
                quotaDo.setDataSource(DataSourceEnum.MANUAL.name());
                if (CollectionUtils.isNotEmpty(empCompensatoryQuotaDo.getEmpQuotas(quotaDo))) {
                    /*return Result.fail(String.format("该员工已存在一条为%s至%s的数据。请检查后重新填写",
                            DateUtilExt.getTimeStrByPattern(quotaDo.getStartDate(), "yyyy-MM-dd"),
                            DateUtilExt.getTimeStrByPattern(quotaDo.getLastDate(), "yyyy-MM-dd")));*/
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_ALREADY_EXIST, null).getMsg(),
                            DateUtilExt.getTimeStrByPattern(quotaDo.getStartDate(), "yyyy-MM-dd"),
                            DateUtilExt.getTimeStrByPattern(quotaDo.getLastDate(), "yyyy-MM-dd")));
                }
            }
            if (quotaDo.getQuotaUnit() == 2) {
                quotaDo.setQuotaDay(quotaDo.getQuotaDay() * 60);
                quotaDo.setInTransitQuota(quotaDo.getInTransitQuota() * 60);
                quotaDo.setUsedDay(quotaDo.getUsedDay() * 60);
                Float adjustQuotaDay = Optional.ofNullable(quotaDo.getAdjustQuotaDay()).orElse(0f);
                quotaDo.setAdjustQuotaDay(adjustQuotaDay * 60);
                quotaDo.setUpdateTime(DateUtil.getCurrentTime(true));
                quotaDo.setUpdateBy(userInfo.getUserId());
            }
            quotaDo.setDataSource(null);
            empCompensatoryQuotaDo.update(quotaDo);
        }
        return Result.ok(Boolean.TRUE);
    }

    @Override
    public EmpCompensatoryQuotaDto getById(Long quotaId) {
        EmpCompensatoryQuotaDo empCompensatoryQuota = empCompensatoryQuotaDo.getByQuotaId(quotaId);
        EmpCompensatoryQuotaDto quotaDto = ObjectConverter.convert(empCompensatoryQuota, EmpCompensatoryQuotaDto.class);
        SysEmpInfo empInfo = sysEmpInfo.getEmpInfoById(sessionService.getUserInfo().getTenantId(), empCompensatoryQuota.getEmpId());
        EmpInfoDTO empInfoDTO = ObjectConverter.convert(empInfo, EmpInfoDTO.class);
        empInfoDTO.setEmpId(empInfo.getEmpid());
        empInfoDTO.setName(empInfo.getEmpName());
        quotaDto.setEmpInfo(empInfoDTO);
        quotaDto.calculateLeftDay();
        return quotaDto;
    }

    @Override
    public Result<String> importEmpCompensatory(MultipartFile file) {
        try {
            log.info("Start to import emp compensatory");
            DataInputStream dataInputStream = new DataInputStream(file.getInputStream());
            //Workbook wb = WorkbookFactory.create(dataInputStream);
            Workbook wb = FileUtil.createWorkbook(file.getOriginalFilename(), dataInputStream);
            if (null == wb) {
                return ResponseWrap.wrapResult(AttendanceCodes.PARSE_WORKBOOK_ERR, null);
            }
            Sheet sheet = wb.getSheet("sheet1");
            if (null == sheet) {
                return ResponseWrap.wrapResult(AttendanceCodes.PARSE_SHEET_ERR, null);
            }
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            if (lastRowNum == firstRowNum || lastRowNum <= 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_EMPTY, null);
            }
            UserInfo userInfo = sessionService.getUserInfo();
            String belongOrgId = userInfo.getTenantId();
            List<ImportCompensatoryDto> list = new ArrayList<>();
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    Cell workNoCell = row.getCell(0);
                    Cell overTimeDateCell = row.getCell(1);
                    Cell adjustQuotaDayCell = row.getCell(2);
                    Cell remarkCell = row.getCell(3);
                    String workNo = "";
                    String overTimeDate = "";
                    String adjustQuotaDay = "";
                    String remark = null;
                    if (workNoCell != null) {
                        workNoCell.setCellType(CellType.STRING);
                        workNo = workNoCell.getStringCellValue().trim();
                    }
                    if (overTimeDateCell != null) {
                        short dataFormat = overTimeDateCell.getCellStyle().getDataFormat();
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        if (HSSFDateUtil.isCellDateFormatted(overTimeDateCell) && dataFormat == HSSFDataFormat.getBuiltinFormat("yyyy-MM-dd")) {
                            Date date = overTimeDateCell.getDateCellValue();
                            overTimeDate = sdf.format(date);
                        } else if (dataFormat == 14 || dataFormat == 31 || dataFormat == 57 || dataFormat == 58) {
                            double value = overTimeDateCell.getNumericCellValue();
                            Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
                            overTimeDate = sdf.format(date);
                        } else {
                            overTimeDateCell.setCellType(CellType.STRING);
                            overTimeDate = overTimeDateCell.getStringCellValue().trim();
                        }
                    }
                    if (adjustQuotaDayCell != null) {
                        adjustQuotaDayCell.setCellType(CellType.STRING);
                        adjustQuotaDay = adjustQuotaDayCell.getStringCellValue().trim();
                    }
                    if (remarkCell != null) {
                        remarkCell.setCellType(CellType.STRING);
                        remark = remarkCell.getStringCellValue().trim();
                    }
                    Integer rowNum = i + 1;
                    //账号不能为空
                    if (StringUtils.isBlank(workNo)) {//第%s行账号为空，请检查后重新上传
                        return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_EMPTY, null).getMsg(), rowNum));
                    }
                    //加班日期不能为空
                    if (StringUtils.isBlank(overTimeDate)) {//第%s行加班日期为空，请检查后重新上传
                        return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_OVERTIME_DATE_EMPTY, null).getMsg(), rowNum));
                    } else {
                        boolean flag = NumberCheckUtil.checkDate(overTimeDate);
                        if (!flag) {//第%s行加班日期格式不正确，请检查后重新上传
                            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_OVERTIME_DATE_FORMAT_ERR, null).getMsg(), rowNum));
                        }
                    }
                    //调整额度不能为空
                    if (StringUtils.isBlank(adjustQuotaDay)) {//第%s行调整额度为空，请检查后重新上传
                        return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_ADJUST_QUOTA_EMPTY, null).getMsg(), rowNum));
                    } else {
                        boolean flag = NumberUtils.isCreatable(adjustQuotaDay);
                        if (!flag) {//第%s行调整额度格式不正确，必须为数字，请检查后重新上传
                            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_ADJUST_QUOTA_FORMAT_FORMAT_ERR, null).getMsg(), rowNum));
                        }
                    }
                    list.add(new ImportCompensatoryDto(workNo, overTimeDate, adjustQuotaDay, remark, rowNum));
                }
            }
            log.info("End to import emp compensatory, data:[rows:{}]", JSONUtils.ObjectToJson(list));
            //校验数据重复
            Map<String, Long> workNoCountMap = list.stream().collect(Collectors.groupingBy(o -> String.join("-", o.getWorkNo(), o.getOverTimeDate()), Collectors.counting()));
            Optional<String> optional = workNoCountMap.keySet().stream().filter(workNo -> workNoCountMap.get(workNo) > 1).findFirst();
            if (optional.isPresent()) {//导入文件数据%s存在重复，请检查后重新上传
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_OVERTIME_DATE_EMPTY, null).getMsg(), optional.get()));
            }
            List<String> workNos = list.stream().map(ImportCompensatoryDto::getWorkNo).distinct().collect(Collectors.toList());
            List<SysEmpInfo> empList = sysEmpInfo.getEmpInfoByWorkNos(belongOrgId, workNos);
            if (CollectionUtils.isEmpty(empList)) {
                return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_ERR, null);
            }
            Map<String, Long> workNoAndEmpIdMap = empList.stream().collect(Collectors.toMap(SysEmpInfo::getWorkno, SysEmpInfo::getEmpid));
            for (ImportCompensatoryDto dto : list) {
                String workNo = dto.getWorkNo();
                if (!workNoAndEmpIdMap.containsKey(workNo) || workNoAndEmpIdMap.get(workNo) == null) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_DATA_REPEAT, null).getMsg(), dto.getRowNum(), workNo));
                }
                Long empId = workNoAndEmpIdMap.get(workNo);
                Long overTimeDate = DateUtil.getTimesampByDateStr2(dto.getOverTimeDate());
                EmpCompensatoryQuotaDo compensatory = empCompensatoryQuotaDo.getCompensatoryByDate(userInfo.getTenantId(), empId, overTimeDate);
                if (compensatory == null) {//加班日期为%s,员工号为%s的调休额度不存在，请检查后重新上传
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_COMPENSATORY_NOT_EXIST, null).getMsg(), dto.getOverTimeDate(), dto.getWorkNo()));
                }
                BigDecimal adjustQuota = new BigDecimal(dto.getAdjustQuotaDay());
                //单位为小时
                if (compensatory.getQuotaUnit() == 2) {
                    adjustQuota = adjustQuota.multiply(new BigDecimal(60));
                }
                EmpCompensatoryQuotaDo quotaDo = new EmpCompensatoryQuotaDo();
                quotaDo.setQuotaId(compensatory.getQuotaId());
                quotaDo.setAdjustQuotaDay(adjustQuota.floatValue());
                quotaDo.setRemark(dto.getRemark());
                quotaDo.setUpdateTime(DateUtil.getCurrentTime(true));
                quotaDo.setUpdateBy(userInfo.getUserId());
                empCompensatoryQuotaDo.update(quotaDo);
            }
        } catch (FileNotFoundException fne) {
            log.error("File not exists {}", fne.getMessage(), fne);
            return Result.fail("File not exists");
        } catch (IOException ie) {
            log.error("File parsing failed {}", ie.getMessage(), ie);
            return Result.fail("File parsing failed");
        } catch (Exception e) {
            log.error("An exception occurred while importing the file, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FAILED, null);
        }
        return Result.ok("success");
    }

    @Override
    public Result<Boolean> save(EmpCompensatoryQuotaDto dto) {
        UserInfo userInfo = sessionService.getUserInfo();
        String tenantId = userInfo.getTenantId();
        GroupDetailDto groupDetail = groupService.getEmpGroup(tenantId, dto.getEmpInfo().getEmpId(), DateUtil.getCurrentTime(true));
        if (null == groupDetail) {
            return ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_PLAN_NOT_EXIST, Boolean.FALSE);
        }
        if (null == groupDetail.getLeaveTypeIds()) {
            return ResponseWrap.wrapResult(AttendanceCodes.GROUP_NOT_EXIST_COMPENSATORY_LEAVE, Boolean.FALSE);
        }
        List<LeaveTypeInfoDto> list = leaveTypeService.getLeaveTypes(groupDetail.getWaGroupId(), null, null);
        LeaveTypeInfoDto leaveType = list.stream().filter(l -> l.getLeaveType() == 3 && l.getStatus() == 1).findFirst().orElse(null);
        if (null == leaveType) {
            return ResponseWrap.wrapResult(AttendanceCodes.GROUP_NOT_EXIST_COMPENSATORY_LEAVE, Boolean.FALSE);
        }
        EmpCompensatoryQuotaDo quotaDo = ObjectConverter.convert(dto, EmpCompensatoryQuotaDo.class);
        quotaDo.setEmpId(dto.getEmpInfo().getEmpId());
        quotaDo.setTenantId(tenantId);
        quotaDo.setDataSource(DataSourceEnum.MANUAL.name());
        quotaDo.setDeleted(0);
        if (CollectionUtils.isNotEmpty(empCompensatoryQuotaDo.getEmpQuotas(quotaDo))) {
            String msg = String.format(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_ALREADY_EXIST, null).getMsg(),
                    DateUtilExt.getTimeStrByPattern(quotaDo.getStartDate(), "yyyy-MM-dd"),
                    DateUtilExt.getTimeStrByPattern(quotaDo.getLastDate(), "yyyy-MM-dd"));
            return ResponseWrap.wrapResult(AttendanceCodes.QUOTA_ALREADY_EXIST, msg, Boolean.FALSE);
        }
        quotaDo.setQuotaId(snowflakeUtil.createId());
        Integer acctTimeType = (Integer) leaveType.getAcctTimeType();
        quotaDo.setQuotaUnit(acctTimeType);
        quotaDo.setValidityPeriodType(0);
        quotaDo.setLeaveTypeId(leaveType.getLeaveTypeId());
        quotaDo.setStatus(2);
        if (LeaveTypeUnitEnum.HOUR.getIndex().equals(quotaDo.getQuotaUnit())) {
            //单位为小时
            quotaDo.setQuotaDay(Optional.ofNullable(quotaDo.getQuotaDay()).orElse(0f) * 60);
            quotaDo.setAdjustQuotaDay(Optional.ofNullable(quotaDo.getAdjustQuotaDay()).orElse(0f) * 60);
        }
        quotaDo.setCreateTime(DateUtil.getCurrentTime(true));
        quotaDo.setCreateBy(userInfo.getUserId());
        empCompensatoryQuotaDo.save(quotaDo);
        return Result.ok();
    }

    @Override
    public Result<String> importEmpCompensatoryQuota(MultipartFile file) {
        try {
            log.info("Start to import emp compensatory");
            Workbook wb = importExcelFile(file);
            Sheet sheet = wb.getSheet("sheet1");
            int lastRowNum = sheet.getLastRowNum();
            UserInfo userInfo = sessionService.getUserInfo();
            String tenantId = userInfo.getTenantId();
            List<ImportCompensatoryQuotaDto> list = new ArrayList<>();
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    Cell workNoCell = row.getCell(0);
                    Cell compensatoryQuotaDayCell = row.getCell(1);
                    Cell adjustQuotaDayCell = row.getCell(2);
                    Cell startDateCell = row.getCell(3);
                    Cell lastDateCell = row.getCell(4);
                    Cell remarkCell = row.getCell(5);
                    //账号，必填
                    String workNo = "";
                    if (workNoCell != null) {
                        workNoCell.setCellType(CellType.STRING);
                        workNo = workNoCell.getStringCellValue().trim();
                    }
                    //调休额度，必填
                    String compensatoryQuotaDay = "";
                    if (compensatoryQuotaDayCell != null) {
                        compensatoryQuotaDayCell.setCellType(CellType.STRING);
                        compensatoryQuotaDay = compensatoryQuotaDayCell.getStringCellValue().trim();
                    }
                    //调整额度，非必填
                    String adjustQuotaDay = "";
                    if (adjustQuotaDayCell != null) {
                        adjustQuotaDayCell.setCellType(CellType.STRING);
                        adjustQuotaDay = adjustQuotaDayCell.getStringCellValue().trim();
                    }
                    //备注，非必填
                    String remark = null;
                    if (remarkCell != null) {
                        remarkCell.setCellType(CellType.STRING);
                        remark = remarkCell.getStringCellValue().trim();
                    }
                    //生效日期，必填
                    String startDateStr = getCellDateString(startDateCell, "yyyy-MM-dd");
                    //失效日期，必填
                    String lastDateStr = getCellDateString(lastDateCell, "yyyy-MM-dd");
                    Integer rowNum = i + 1;
                    //必填校验
                    String checkCellErr = checkCellValue(workNo, compensatoryQuotaDay, startDateStr, lastDateStr, rowNum);
                    if (StringUtil.isNotBlank(checkCellErr)) {
                        return Result.fail(checkCellErr);
                    }
                    list.add(new ImportCompensatoryQuotaDto(workNo, compensatoryQuotaDay, adjustQuotaDay, startDateStr, lastDateStr, remark, rowNum));
                }
            }
            log.info("End to parse excel file, data:[size:{}]", list.size());
            //校验数据重复
            Map<String, Long> workNoCountMap = list.stream().collect(Collectors.groupingBy(o -> String.join("-", o.getWorkNo(), o.getStartDate(), o.getLastDate()), Collectors.counting()));
            Optional<String> optional = workNoCountMap.keySet().stream().filter(workNo -> workNoCountMap.get(workNo) > 1).findFirst();
            if (optional.isPresent()) {
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_DATA_REPEAT, null).getMsg(), optional.get()));
            }
            List<String> workNos = list.stream().map(ImportCompensatoryQuotaDto::getWorkNo).distinct().collect(Collectors.toList());
            List<SysEmpInfo> empList = sysEmpInfo.getEmpInfoByWorkNos(tenantId, workNos);
            if (CollectionUtils.isEmpty(empList)) {
                return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_ERR, null);
            }
            List<EmpCompensatoryQuotaDo> addList = new ArrayList<>();
            Map<String, Long> workNoAndEmpIdMap = empList.stream().collect(Collectors.toMap(SysEmpInfo::getWorkno, SysEmpInfo::getEmpid));
            for (ImportCompensatoryQuotaDto dto : list) {
                String workNo = dto.getWorkNo();
                if (!workNoAndEmpIdMap.containsKey(workNo) || workNoAndEmpIdMap.get(workNo) == null) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_NOT_EXIST, null).getMsg(), dto.getRowNum(), workNo));
                }
                Long empId = workNoAndEmpIdMap.get(workNo);
                Long startDate = DateUtil.getTimesampByDateStr2(dto.getStartDate());
                Long lastDate = DateUtil.getTimesampByDateStr2(dto.getLastDate()) + 86399;
                GroupDetailDto groupDetail = groupService.getEmpGroup(tenantId, empId, DateUtil.getCurrentTime(true));
                if (null == groupDetail) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.EMP_NO_VALID_GROUP, null).getMsg(), dto.getRowNum(), workNo));
                }
                if (null == groupDetail.getLeaveTypeIds()) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.EMP_GROUP_NO_COMPENSATORY_LEAVE, null).getMsg(), dto.getRowNum(), workNo));
                }
                List<LeaveTypeInfoDto> leaveTypeList = leaveTypeService.getLeaveTypes(groupDetail.getWaGroupId(), null, null);
                LeaveTypeInfoDto leaveType = leaveTypeList.stream().filter(l -> l.getLeaveType() == 3 && l.getStatus() == 1).findFirst().orElse(null);
                if (null == leaveType) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.EMP_GROUP_NO_COMPENSATORY_LEAVE, null).getMsg(), dto.getRowNum(), workNo));
                }
                BigDecimal compensatoryQuotaDay = new BigDecimal(dto.getCompensatoryQuotaDay());
                BigDecimal adjustQuota = null;
                if (StringUtil.isNotBlank(dto.getAdjustQuotaDay())) {
                    adjustQuota = new BigDecimal(dto.getAdjustQuotaDay());
                }
                Integer acctTimeType = (Integer) leaveType.getAcctTimeType();
                //单位为小时
                if (LeaveTypeUnitEnum.HOUR.getIndex().equals(acctTimeType)) {
                    if (null != adjustQuota) {
                        adjustQuota = adjustQuota.multiply(BigDecimal.valueOf(60));
                    }
                    compensatoryQuotaDay = compensatoryQuotaDay.multiply(BigDecimal.valueOf(60));
                }
                EmpCompensatoryQuotaDo quota = new EmpCompensatoryQuotaDo();
                quota.setTenantId(tenantId);
                quota.setEmpId(empId);
                quota.setQuotaDay(compensatoryQuotaDay.floatValue());
                if (null != adjustQuota) {
                    quota.setAdjustQuotaDay(adjustQuota.floatValue());
                }
                quota.setStartDate(startDate);
                quota.setLastDate(lastDate);
                quota.setDataSource(DataSourceEnum.MANUAL.name());
                quota.setRemark(dto.getRemark());
                quota.setStatus(2);
                quota.setDeleted(0);
                List<EmpCompensatoryQuotaDo> existQuotas = empCompensatoryQuotaDo.getEmpQuotas(quota);
                if (CollectionUtils.isNotEmpty(existQuotas)) {
                    for (EmpCompensatoryQuotaDo row : existQuotas) {
                        quota.setQuotaId(row.getQuotaId());
                        quota.setUpdateTime(DateUtil.getCurrentTime(true));
                        quota.setUpdateBy(userInfo.getUserId());
                        empCompensatoryQuotaDo.update(quota);
                    }
                } else {
                    quota.setQuotaId(snowflakeUtil.createId());
                    quota.setLeaveTypeId(leaveType.getLeaveTypeId());
                    quota.setQuotaUnit(acctTimeType);
                    quota.setValidityPeriodType(0);
                    quota.setCreateTime(DateUtil.getCurrentTime(true));
                    quota.setCreateBy(userInfo.getUserId());
                    addList.add(quota);
                }
            }
            if (CollectionUtils.isNotEmpty(addList)) {
                empCompensatoryQuotaDo.save(addList);
            }
        } catch (CDException cde) {
            log.error("Parse file error {}", cde.getMessage(), cde);
            return Result.fail(cde.getMessage());
        } catch (FileNotFoundException fne) {
            log.error("File not exists {}", fne.getMessage(), fne);
            return Result.fail("File not exists");
        } catch (IOException ie) {
            log.error("File parsing failed {}", ie.getMessage(), ie);
            return Result.fail("File parsing failed");
        } catch (Exception e) {
            log.error("An exception occurred while importing the file, {}", e.getMessage(), e);
            return Result.fail("An exception occurred while importing the file");
        }
        return Result.ok("success");
    }

    private String checkCellValue(String workNo, String compensatoryQuota, String startDate, String lastDate, Integer rowNum) {
        //账号不能为空
        if (StringUtils.isBlank(workNo)) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_EMPTY, null).getMsg(), rowNum);
        }
        //调休额度不能为空
        if (StringUtils.isBlank(compensatoryQuota)) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_COMPENSATORY_QUOTA_EMPTY, null).getMsg(), rowNum);
        } else {
            boolean flag = NumberUtils.isCreatable(compensatoryQuota);
            if (!flag) {
                return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_COMPENSATORY_QUOTA_FORMAT_ERR, null).getMsg(), rowNum);
            }
        }
        //生效日期不能为空
        if (StringUtils.isBlank(startDate)) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_START_DATE_EMPTY, null).getMsg(), rowNum);
        } else {
            boolean flag = NumberCheckUtil.checkDate(startDate);
            if (!flag) {
                return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_START_DATE_FORMAT_ERR, null).getMsg(), rowNum);
            }
        }
        //失效日期不能为空
        if (StringUtils.isBlank(lastDate)) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_END_DATE_EMPTY, null).getMsg(), rowNum);
        } else {
            boolean flag = NumberCheckUtil.checkDate(lastDate);
            if (!flag) {
                return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_END_DATE_FORMAT_ERR, null).getMsg(), rowNum);
            }
        }
        return "";
    }

    private String getCellDateString(Cell dateCell, String pattern) {
        String datePatternStr = "";
        if (dateCell != null) {
            short dataFormat = dateCell.getCellStyle().getDataFormat();
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            if (dataFormat == 14 || dataFormat == 31 || dataFormat == 57 || dataFormat == 58) {
                if (dateCell.getCellType() == CellType.STRING) {
                    String cellValue = dateCell.getStringCellValue().trim();
                    if (StringUtils.isNotBlank(cellValue)) {
                        long value = 0;
                        if (cellValue.contains("-")) {
                            value = DateUtil.convertStringToDateTime(cellValue, "yyyy-MM-dd", true);
                        } else if (cellValue.contains("/")) {
                            value = DateUtil.convertStringToDateTime(cellValue, "yyyy/MM/dd", true);
                        }
                        datePatternStr = DateUtil.getDateStrByTimesamp(value);
                    }
                } else if (dateCell.getCellType() == CellType.NUMERIC) {
                    double value = dateCell.getNumericCellValue();
                    Date date = HSSFDateUtil.getJavaDate(value);
                    datePatternStr = sdf.format(date);
                } else {
                    if (dateCell.toString().contains("-") && checkDate(dateCell.toString())) {
                        datePatternStr = sdf.format(dateCell.getDateCellValue());
                    }
                }
            } else if (HSSFDateUtil.isCellDateFormatted(dateCell) && dataFormat == HSSFDataFormat.getBuiltinFormat(pattern)) {
                Date date = dateCell.getDateCellValue();
                datePatternStr = sdf.format(date);
            } else {
                dateCell.setCellType(CellType.STRING);
                datePatternStr = dateCell.getStringCellValue().trim();
            }
        }
        return datePatternStr;
    }

    private boolean checkDate(String str) {
        String[] dataArr = str.split("-");
        try {
            if (dataArr.length == 3) {
                int day = Integer.parseInt(dataArr[0]);
                String month = dataArr[1];
                int year = Integer.parseInt(dataArr[2]);
                if (day > 0 && day < 32 && year > 0 && year < 10000 && month.endsWith("月")) {
                    return true;
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    public Workbook importExcelFile(MultipartFile file) throws Exception {
        try {
            DataInputStream dataInputStream = new DataInputStream(file.getInputStream());
            //Workbook wb = WorkbookFactory.create(dataInputStream);
            Workbook wb = FileUtil.createWorkbook(file.getOriginalFilename(), dataInputStream);
            if (null == wb) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.PARSE_WORKBOOK_ERR, null).getMsg());
            }
            Sheet sheet = wb.getSheet("sheet1");
            if (null == sheet) {
                sheet = wb.getSheetAt(0);
            }
            if (null == sheet) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.PARSE_SHEET_ERR, null).getMsg());
            }
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            if (lastRowNum == firstRowNum || lastRowNum <= 0) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_EMPTY, null).getMsg());
            }
            return wb;
        } catch (FileNotFoundException fne) {
            log.error("File not exists {}", fne.getMessage(), fne);
            throw fne;
        } catch (IOException ie) {
            log.error("File parsing failed {}", ie.getMessage(), ie);
            throw ie;
        } catch (Exception e) {
            log.error("An exception occurred while importing the file, {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Result<KeyValue> getCompensatoryLeaveUnit(Long empId) {
        UserInfo userInfo = sessionService.getUserInfo();
        String tenantId = userInfo.getTenantId();
        GroupDetailDto groupDetail = groupService.getEmpGroup(tenantId, empId, DateUtil.getCurrentTime(true));
        if (null == groupDetail) {
            return ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_PLAN_NOT_EXIST, null);
        }
        if (null == groupDetail.getLeaveTypeIds()) {
            return ResponseWrap.wrapResult(AttendanceCodes.GROUP_NOT_EXIST_COMPENSATORY_LEAVE, null);
        }
        List<LeaveTypeInfoDto> list = leaveTypeService.getLeaveTypes(groupDetail.getWaGroupId(), null, null);
        LeaveTypeInfoDto leaveType = list.stream().filter(l -> l.getLeaveType() == 3 && l.getStatus() == 1).findFirst().orElse(null);
        if (null == leaveType) {
            return ResponseWrap.wrapResult(AttendanceCodes.GROUP_NOT_EXIST_COMPENSATORY_LEAVE, null);
        }
        Integer acctTimeType = (Integer) leaveType.getAcctTimeType();
        return Result.ok(new KeyValue(LeaveTypeUnitEnum.getName(acctTimeType), acctTimeType));
    }

    /**
     * 生成员工工时转调休配额
     * 事务注解：确保数据一致性，异常时回滚
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void genEmpWorkingHourQuotaForCompensatoryLeave(String tenantId, Long userId, Long startTime, Long endTime, List<WorkingHourAnalyzeDo> analyzeList) throws ParseException {
        // 1. 查询并过滤默认加班类型规则
        List<OverTimeTypeDo> defaultOtTypeList = extractDefaultOtTypes(tenantId);
        if (CollectionUtils.isEmpty(defaultOtTypeList)) {
            log.info("【调休配额生成】未查询到默认加班分析规则，tenantId:{}", tenantId);
            return;
        }
        // 2. 查询加班转换规则
        List<OvertimeTransferRuleDto> transferRules = getTransferRules(defaultOtTypeList);
        if (CollectionUtils.isEmpty(transferRules)) {
            log.info("【调休配额生成】未查询到加班转换规则，tenantId:{}", tenantId);
            return;
        }
        // 3. 过滤调休类型（配额限制+调休类型）
        List<WaLeaveTypeDo> compensatoryLeaveTypes = filterCompensatoryLeaveTypes(tenantId, transferRules);
        if (CollectionUtils.isEmpty(compensatoryLeaveTypes)) {
            log.info("【调休配额生成】未查询到有效的调休假期规则，tenantId:{}", tenantId);
            return;
        }
        Map<Integer, WaLeaveTypeDo> leaveTypeMap = compensatoryLeaveTypes.stream().collect(Collectors.toMap(WaLeaveTypeDo::getLeaveTypeId, Function.identity()));
        List<Integer> validLeaveTypeIds = compensatoryLeaveTypes.stream().map(WaLeaveTypeDo::getLeaveTypeId).collect(Collectors.toList());
        // 4. 查询配额配置及关联的员工分组规则
        List<LeaveQuotaConfigDo> quotaConfigs = getQuotaConfigs(tenantId, validLeaveTypeIds);
        if (CollectionUtils.isEmpty(quotaConfigs)) {
            log.info("【调休配额生成】未查询到调休配额配置，tenantId:{}", tenantId);
            return;
        }
        Map<Long, EmployeeGroupDto> groupRuleMap = getGroupRuleMap(tenantId, quotaConfigs);
        // 5. 查询已存在的调休配额（用于更新或跳过）
        List<Long> employeeIds = analyzeList.stream().map(WorkingHourAnalyzeDo::getEmpId).distinct().collect(Collectors.toList());
        Map<String, EmpCompensatoryQuotaDo> existingQuotaMap = getExistingQuotaMap(tenantId, employeeIds, startTime, endTime);
        // 6. 遍历工时分析数据，生成/更新调休配额
        List<EmpCompensatoryQuotaDo> toAddList = new ArrayList<>(analyzeList.size());
        List<EmpCompensatoryQuotaDo> toUpdateList = new ArrayList<>(analyzeList.size());
        Long currentTime = System.currentTimeMillis() / 1000;
        for (WorkingHourAnalyzeDo analyze : analyzeList) {
            Long empId = analyze.getEmpId();
            long workDate = analyze.getBelongDate();
            // 6.1 匹配加班类型（工作日/休息日加班）
            OverTimeTypeDo matchedOtType = matchOverTimeType(defaultOtTypeList, analyze, workDate);
            if (matchedOtType == null) {
                log.info("【调休配额生成】未匹配加班类型，empId:{}, workDate:{}", empId, workDate);
                continue;
            }
            // 6.2 匹配加班转换规则
            OvertimeTransferRuleDto transferRule = getTransferRule(transferRules, matchedOtType);
            if (transferRule == null) {
                log.info("【调休配额生成】未匹配转换规则，empId:{}, workDate:{}, ruleId:{}", empId, workDate, matchedOtType.getRuleId());
                continue;
            }
            // 6.3 匹配有效的配额配置（含员工分组校验）
            LeaveQuotaConfigDo matchedConfig = matchQuotaConfig(quotaConfigs, groupRuleMap, transferRule, empId, workDate, tenantId);
            if (matchedConfig == null) {
                log.info("【调休配额生成】未匹配配额配置，empId:{}, workDate:{}", empId, workDate);
                continue;
            }
            // 6.4 计算加班转调休时长
            BigDecimal convertedQuota = calculateConvertedQuota(analyze, matchedOtType, transferRule);
            if (convertedQuota.floatValue() <= 0) {
                log.info("【调休配额生成】转换后时长为0，analyzeId:{}", analyze.getAnalyzeId());
                continue;
            }
            // 6.5 构建调休配额对象
            EmpCompensatoryQuotaDo quota = buildCompensatoryQuota(analyze, matchedOtType, transferRule, matchedConfig, leaveTypeMap, convertedQuota, tenantId, userId, currentTime);
            // 6.6 计算有效期（生效/失效日期）
            if (!calculateValidityPeriod(quota, matchedConfig, workDate)) {
                log.info("【调休配额生成】有效期无效，empId:{}, workDate:{}", empId, workDate);
                continue;
            }
            // 6.7 处理新旧数据（新增/更新）
            handleExistingQuota(quota, existingQuotaMap, toAddList, toUpdateList, empId, workDate, currentTime, userId);
        }
        // 7. 批量保存/更新
        if (!toAddList.isEmpty()) {
            empCompensatoryQuotaDo.save(toAddList);
            log.info("【调休配额生成】新增配额:{}条", toAddList.size());
        }
        if (!toUpdateList.isEmpty()) {
            empCompensatoryQuotaDo.batchUpdate(toUpdateList);
            log.info("【调休配额生成】更新配额:{}条", toUpdateList.size());
        }
        log.info("【调休配额生成】处理完成，tenantId:{}", tenantId);
    }

    /**
     * 提取默认加班类型
     */
    private List<OverTimeTypeDo> extractDefaultOtTypes(String tenantId) {
        List<OverTimeTypeDo> allOtTypes = overTimeTypeDo.getOtTypes(tenantId, null, null, false);
        return allOtTypes.stream().filter(otType -> BooleanUtils.isTrue(otType.getDefaultType())).collect(Collectors.toList());
    }

    /**
     * 获取加班转换规则
     */
    private List<OvertimeTransferRuleDto> getTransferRules(List<OverTimeTypeDo> otTypes) {
        List<Long> ruleIds = otTypes.stream().map(OverTimeTypeDo::getRuleId).distinct().collect(Collectors.toList());
        return overtimeTransferRuleService.getOvertimeTransferRuleList(ruleIds);
    }

    /**
     * 过滤调休类型（仅保留配额限制+调休类型）
     */
    private List<WaLeaveTypeDo> filterCompensatoryLeaveTypes(String tenantId, List<OvertimeTransferRuleDto> transferRules) {
        List<Integer> leaveTypeIds = transferRules.stream().map(OvertimeTransferRuleDto::getLeaveTypeId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaveTypeIds)) {
            return Collections.emptyList();
        }
        return waLeaveTypeDo.getLeaveTypesByIds(tenantId, leaveTypeIds).stream().filter(leaveType -> QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(leaveType.getQuotaRestrictionType())
                && QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(leaveType.getQuotaType())).collect(Collectors.toList());
    }

    /**
     * 查询配额配置
     */
    private List<LeaveQuotaConfigDo> getQuotaConfigs(String tenantId, List<Integer> leaveTypeIds) {
        return leaveQuotaConfigDo.getConfigListByIds(tenantId, leaveTypeIds);
    }

    /**
     * 获取员工分组规则映射
     */
    private Map<Long, EmployeeGroupDto> getGroupRuleMap(String tenantId, List<LeaveQuotaConfigDo> configs) {
        List<String> businessKeys = configs.stream().map(c -> c.getConfigId().toString()).collect(Collectors.toList());
        return leaveTypeService.getGroupRuleMap(tenantId, businessKeys);
    }

    /**
     * 查询已存在的配额
     */
    private Map<String, EmpCompensatoryQuotaDo> getExistingQuotaMap(String tenantId, List<Long> employeeIds, Long startTime, Long endTime) {
        return getWorkHourQuotaMap(tenantId, employeeIds, startTime, endTime);
    }

    /**
     * 匹配加班类型（工作日/休息日）
     */
    private OverTimeTypeDo matchOverTimeType(List<OverTimeTypeDo> otTypes, WorkingHourAnalyzeDo analyze, long workDate) {
        // 工作日加班
        if (BigDecimal.ZERO.compareTo(analyze.getWorkDayOtDuration()) < 0) {
            return otTypes.stream().filter(ot -> ot.getDateType() == 1 && ot.getStartDate() <= workDate && ot.getEndDate() >= workDate).findFirst().orElse(null);
        }
        // 休息日加班
        if (BigDecimal.ZERO.compareTo(analyze.getRestDayOtDuration()) < 0) {
            return otTypes.stream().filter(ot -> ot.getDateType() == 2 && ot.getStartDate() <= workDate && ot.getEndDate() >= workDate).findFirst().orElse(null);
        }
        return null;
    }

    /**
     * 获取对应的转换规则
     */
    private OvertimeTransferRuleDto getTransferRule(List<OvertimeTransferRuleDto> rules, OverTimeTypeDo otType) {
        return rules.stream().filter(rule -> rule.getRuleId().equals(otType.getRuleId())).findFirst().orElse(null);
    }

    /**
     * 匹配配额配置（含员工分组校验）
     */
    private LeaveQuotaConfigDo matchQuotaConfig(List<LeaveQuotaConfigDo> configs, Map<Long, EmployeeGroupDto> groupRuleMap,
                                                OvertimeTransferRuleDto transferRule, Long empId, long workDate, String tenantId) {
        // 过滤有效期内的配置
        List<LeaveQuotaConfigDo> validConfigs = configs.stream().filter(config -> transferRule.getLeaveTypeId().equals(config.getLeaveTypeId())
                && config.getRuleStartDate() <= workDate && workDate < config.getRuleEndDate()).collect(Collectors.toList());
        if (validConfigs.isEmpty()) {
            return null;
        }
        // 校验员工是否属于配置的分组
        for (LeaveQuotaConfigDo config : validConfigs) {
            EmployeeGroupDto groupDto = groupRuleMap.get(config.getConfigId());
            if (groupDto == null) {
                continue;
            }
            List<Long> groupEmpIds = sysEmpInfo.getEmpIdsByGroupExp(Long.parseLong(tenantId), groupDto.getGroupExp(), config.getGroupExpCondition());
            if (CollectionUtils.isNotEmpty(groupEmpIds) && groupEmpIds.contains(empId)) {
                return config;
            }
        }
        return null;
    }

    /**
     * 计算转换后的调休时长
     */
    private BigDecimal calculateConvertedQuota(WorkingHourAnalyzeDo analyze, OverTimeTypeDo otType, OvertimeTransferRuleDto transferRule) {
        float realOvertimeDuration = getRealOvertimeDuration(analyze, otType);
        if (realOvertimeDuration <= 0) {
            return BigDecimal.ZERO;
        }
        // 应用转换规则
        BigDecimal otNum = BigDecimal.valueOf(realOvertimeDuration);
        TransferRuleEnum ruleEnum = TransferRuleEnum.getTransferRuleEnum(transferRule.getTransferRule());
        if (ruleEnum == null) {
            return otNum;
        }
        Map<String, Object> durMap = ruleEnum.calTimeDuration(otNum.floatValue(), transferRule.getTransferPeriods(), transferRule.getTransferTime());
        int unit = new BigDecimal(durMap.get("unit").toString()).intValue();
        BigDecimal duration = new BigDecimal(durMap.get("duration").toString());
        return (unit == 1) ? duration : duration.multiply(BigDecimal.valueOf(60));
    }

    /**
     * 获取实际加班时长（考虑最小单位）
     */
    private float getRealOvertimeDuration(WorkingHourAnalyzeDo analyze, OverTimeTypeDo otType) {
        float duration = (BigDecimal.ZERO.compareTo(analyze.getWorkDayOtDuration()) < 0) ? analyze.getWorkDayOtDuration().floatValue() : analyze.getRestDayOtDuration().floatValue();
        // 处理最小加班单位
        Float minOvertimeUnit = otType.getMinOvertimeUnit();
        if (minOvertimeUnit == null) {
            return duration;
        }
        float durationInMinutes = duration * 60;
        int unitType = Optional.ofNullable(otType.getMinOvertimeUnitType()).orElse(2);
        return OvertimeUnitEnum.HOUR.getTime(null, durationInMinutes, minOvertimeUnit, unitType) / 60;
    }

    /**
     * 构建调休配额对象
     */
    private EmpCompensatoryQuotaDo buildCompensatoryQuota(WorkingHourAnalyzeDo analyze, OverTimeTypeDo otType, OvertimeTransferRuleDto transferRule, LeaveQuotaConfigDo config,
                                                          Map<Integer, WaLeaveTypeDo> leaveTypeMap, BigDecimal convertedQuota, String tenantId, Long userId, Long currentTime) {
        EmpCompensatoryQuotaDo quota = new EmpCompensatoryQuotaDo();
        WaLeaveTypeDo leaveType = leaveTypeMap.get(config.getLeaveTypeId());
        quota.setConfigId(config.getConfigId());
        quota.setEmpId(analyze.getEmpId());
        quota.setOvertimeDate(analyze.getBelongDate());
        quota.setOvertimeDuration(getRealOvertimeDuration(analyze, otType));
        quota.setOvertimeType(otType.getDateType());
        quota.setOvertimeUnit(LeaveTypeUnitEnum.HOUR.getIndex());
        quota.setLeaveTypeId(leaveType.getLeaveTypeId());
        quota.setQuotaUnit(leaveType.getAcctTimeType());
        quota.setDeleted(0);
        quota.setStatus(ApprovalStatusEnum.PASSED.getIndex());
        quota.setTenantId(tenantId);
        quota.setQuotaDay(convertedQuota.floatValue());
        // 初始化默认值
        quota.setUsedDay(0f);
        quota.setInTransitQuota(0f);
        quota.setAdjustQuotaDay(0f);
        quota.setCreateBy(userId);
        quota.setCreateTime(currentTime);
        quota.setUpdateBy(userId);
        quota.setUpdateTime(currentTime);
        quota.setDataSource(DataSourceEnum.MAN_HOUR.name());
        quota.setEntityId(String.format("%s_%s_%s", analyze.getProcessId(), analyze.getOrderId(), analyze.getShiftId()));
        return quota;
    }

    /**
     * 计算有效期（生效/失效日期）
     */
    private boolean calculateValidityPeriod(EmpCompensatoryQuotaDo quota, LeaveQuotaConfigDo config, long workDate) throws ParseException {
        Integer validityType = config.getValidityPeriodType();
        // 无限制有效期
        if (!QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(validityType)) {
            quota.setValidityPeriodType(0);
            quota.setStartDate(1L);
            quota.setLastDate(253402271999L);
            return true;
        }
        // 有限制有效期
        quota.setValidityPeriodType(validityType);
        long startDate = calculateStartDate(config.getValidityStartType(), workDate);
        long lastDate = calculateLastDate(config, startDate, workDate);
        // 校验有效期合法性
        if (lastDate <= startDate) {
            return false;
        }
        quota.setStartDate(startDate);
        quota.setLastDate(lastDate);
        return true;
    }

    /**
     * 计算生效日期
     */
    private long calculateStartDate(Integer startType, long workDate) throws ParseException {
        if (ValidityStartTypeEnum.NATURAL_YEAR.getIndex().equals(startType)) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date(workDate * 1000));
            calendar.set(calendar.get(Calendar.YEAR), 0, 1, 0, 0, 0);
            return calendar.getTime().getTime() / 1000;
        } else if (ValidityStartTypeEnum.OVERTIME_DATE.getIndex().equals(startType)) {
            return workDate;
        } else if (ValidityStartTypeEnum.OVERTIME_MONTH.getIndex().equals(startType)) {
            return DateUtilExt.getMonthBegin(workDate);
        }
        return 1L;
    }

    /**
     * 计算失效日期
     */
    private long calculateLastDate(LeaveQuotaConfigDo config, long startDate, long workDate) throws ParseException {
        Integer invalidType = config.getInvalidType();
        Boolean extendToMonthEnd = config.getValidityExtension();
        if (InvalidTypeEnum.FIXED.getIndex().equals(invalidType)) {
            // 固定有效期
            int unit = config.getValidityUnit();
            float duration = config.getValidityDuration();
            long lastDate = DateUtilExt.getAddYearOrMonthOrDateEndTime(startDate * 1000,
                    (unit == ValidityUnitEnum.YEAR.getIndex()) ? (int) duration : 0,
                    (unit == ValidityUnitEnum.MONTH.getIndex()) ? (int) duration : 0,
                    (unit == ValidityUnitEnum.DAY.getIndex()) ? (int) duration - 1 : 0);
            return (extendToMonthEnd != null && extendToMonthEnd) ? DateUtilExt.getMonthEnd(lastDate) : lastDate;
        } else if (InvalidTypeEnum.CURRENT_YEAR.getIndex().equals(invalidType)) {
            // 当年失效
            long lastDate = DateUtil.convertStringToDateTime(
                    String.format("%s%s", DateUtilExt.getTimeYear(startDate), config.getInvalidDate()),
                    "yyyyMMdd", true) + 86399;
            return (lastDate >= workDate) ? lastDate : -1; // 失效日期早于加班日期则无效
        } else {
            // 次年失效
            long baseDate = DateUtil.convertStringToDateTime(
                    String.format("%s%s", DateUtilExt.getTimeYear(startDate), config.getInvalidDate()),
                    "yyyyMMdd", true);
            return DateUtilExt.addYear(baseDate, 1) + 86399;
        }
    }

    /**
     * 处理已存在的配额（更新/新增）
     */
    private void handleExistingQuota(EmpCompensatoryQuotaDo quota, Map<String, EmpCompensatoryQuotaDo> existingQuotaMap,
                                     List<EmpCompensatoryQuotaDo> toAddList, List<EmpCompensatoryQuotaDo> toUpdateList,
                                     Long empId, long workDate, Long currentTime, Long userId) {
        String quotaKey = String.format("%s_%s_%s", empId, workDate, quota.getEntityId());
        if (existingQuotaMap.containsKey(quotaKey)) {
            // 更新现有配额
            EmpCompensatoryQuotaDo existing = existingQuotaMap.get(quotaKey);
            quota.setQuotaId(existing.getQuotaId());
            quota.setUsedDay(existing.getUsedDay() != null ? existing.getUsedDay() : 0f);
            quota.setInTransitQuota(existing.getInTransitQuota() != null ? existing.getInTransitQuota() : 0f);
            // 保留历史状态（如已取消）
            if (Objects.equals(existing.getStatus(), ApprovalStatusEnum.REVOKING.getIndex())) {
                quota.setStatus(existing.getStatus());
            }
            quota.setUpdateTime(currentTime);
            quota.setUpdateBy(userId);
            toUpdateList.add(quota);
        } else {
            // 新增配额
            quota.setQuotaId(snowflakeUtil.createId());
            toAddList.add(quota);
        }
    }

    private Map<String, EmpCompensatoryQuotaDo> getWorkHourQuotaMap(String tenantId, List<Long> empList, Long startDate, Long endDate) {
        if (CollectionUtils.isEmpty(empList)) {
            return Collections.emptyMap();
        }
        Map<String, EmpCompensatoryQuotaDo> quotaMap = new HashMap<>();
        //人员集合拆分
        List<List<Long>> result = ListTool.split(empList, 500);
        for (List<Long> empIds : result) {
            if (CollectionUtils.isEmpty(empIds)) {
                continue;
            }
            List<EmpCompensatoryQuotaDo> quotaDoList = empCompensatoryQuotaDo.getQuotaByDate(tenantId, empIds, startDate, endDate, DataSourceEnum.MAN_HOUR.name(), Arrays.asList(2, 8));
            quotaDoList = quotaDoList.stream().filter(quota -> quota.getEntityId() != null).collect(Collectors.toList());
            quotaMap.putAll(listGroupByKey(quotaDoList, quota -> String.format("%s_%s_%s", quota.getEmpId(), quota.getOvertimeDate(), quota.getEntityId())));
        }
        return quotaMap;
    }

    private <T> Map<String, T> listGroupByKey(List<T> list, Function<T, String> keyExtractor) {
        return list.stream().collect(Collectors.toMap(keyExtractor, Function.identity(), (v1, v2) -> v2));
    }
}