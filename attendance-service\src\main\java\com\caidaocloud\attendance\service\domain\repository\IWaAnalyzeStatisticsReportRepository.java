package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.application.dto.StatisticsReportReqDataDto;
import com.caidaocloud.attendance.service.domain.entity.WaAnalyzeStatisticsReportDo;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/16
 */
public interface IWaAnalyzeStatisticsReportRepository {
    WaAnalyzeStatisticsReportDo getAnalyzeStatisticReport(String tenantId, Long empId, Integer rptType, Long startDate);

    AttendancePageResult<WaAnalyzeStatisticsReportDo> getAnalyzeStatisticsReportPageList(StatisticsReportReqDataDto dto);

    int save(List<WaAnalyzeStatisticsReportDo> list);

    int delete(String tenantId, List<Long> empIds, List<Long> startDates, Integer rptType);

    List<WaAnalyzeStatisticsReportDo> getAnalyzeStatisticsReportList(String tenantId, List<Long> empIds, Integer rptType,Long startDate,Long endDate);

    int deleteByIds(String tenantId, List<Long> statisticsReportIds);

}
