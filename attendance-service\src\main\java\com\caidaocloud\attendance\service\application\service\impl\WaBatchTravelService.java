package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.system.mybatis.model.SysUnitCity;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.dto.WfBusinessDataDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.travel.BatchTravelDto;
import com.caidaocloud.attendance.service.application.dto.travel.BatchTravelTimeDetailDto;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.domain.entity.WaBatchTravelDo;
import com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDo;
import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.domain.service.WaBatchTravelDomainService;
import com.caidaocloud.attendance.service.interfaces.dto.common.WaKeyValue;
import com.caidaocloud.attendance.service.interfaces.dto.task.TaskDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelSaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.RevokeEmpTraveDto;
import com.caidaocloud.attendance.service.interfaces.vo.EmpTravelVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.SysConfigsEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.*;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class WaBatchTravelService {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    @Autowired
    private IEmpTravelService empTravelService;
    @Autowired
    private WaEmpTravelDo waEmpTravelDo;
    @Autowired
    private WaBatchTravelDomainService waBatchTravelDomainService;
    @Autowired
    private IWfService wfService;
    @Autowired
    private IConfigService configService;
    @Autowired
    private WorkflowCallBackService workflowCallBackService;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private WaTravelTypeDo waTravelTypeDo;

    public Result<?> getTimeDuration(BatchTravelDto travelDto) throws Exception {
        EmpTravelSaveDto empTravelSaveDto = travelDto.doConvert();
        empTravelSaveDto.setOpt(1);
        empTravelSaveDto.setIfBtchTravel(true);
        return empTravelService.checkOrSaveTravelTime(empTravelSaveDto);
    }

    public Result<?> save(BatchTravelDto travelDto) throws Exception {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(travelDto.getEmpId());
        if (empInfo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, Boolean.FALSE);
        }
        // 检查流程是否已启用
        Result<Boolean> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.BATCH_TRAVEL.getCode());
        if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
        }
        Boolean workflowEnabledResultData = checkWorkflowEnableResult.getData();
        if (!workflowEnabledResultData) {
            if (configService.checkSwitchStatus(SysConfigsEnum.TRAVEL_WORKFLOW_SWITCH.name())) {
                throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
            }
        }
        // 保存单据
        EmpTravelSaveDto empTravelSaveDto = travelDto.doConvert();
        WaBatchTravelDo batchTravel = SpringUtil.getBean(WaBatchTravelService.class)
                .saveApply(travelDto, empTravelSaveDto);
        // 开启流程
        String businessKey = String.valueOf(batchTravel.getBatchTravelId());
        String wfBusKey = String.format("%s_%s", businessKey, BusinessCodeEnum.BATCH_TRAVEL.getCode());
        if (!workflowEnabledResultData) {
            WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
            wfCallbackResultDto.setBusinessKey(wfBusKey);
            wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
            wfCallbackResultDto.setTenantId(batchTravel.getTenantId());
            workflowCallBackService.saveBatchTravelApproval(wfCallbackResultDto);
        } else {
            WfBeginWorkflowDto workflowDto = new WfBeginWorkflowDto();
            workflowDto.setFuncCode(BusinessCodeEnum.BATCH_TRAVEL.getCode());
            workflowDto.setBusinessId(businessKey);
            workflowDto.setApplicantId(empInfo.getEmpid().toString());
            workflowDto.setApplicantName(empInfo.getEmpName());
            long startDate = DateUtil.getOnlyDate(new Date(empTravelSaveDto.getStartTime() * 1000));
            long endDate = DateUtil.getOnlyDate(new Date(empTravelSaveDto.getEndTime() * 1000));
            workflowDto.setEventTime(startDate * 1000);
            workflowDto.setEventEndTime(endDate * 1000);
            startWorkflow(workflowDto, batchTravel);
        }
        return Result.ok(wfBusKey);
    }

    @Transactional(rollbackFor = Exception.class)
    public WaBatchTravelDo saveApply(BatchTravelDto travelDto, EmpTravelSaveDto empTravelSaveDto) throws Exception {
        UserInfo userInfo = UserContext.preCheckUser();
        // 保存批量单据
        WaBatchTravelDo batchTravel = new WaBatchTravelDo();
        batchTravel.setBatchTravelId(snowflakeUtil.createId());
        batchTravel.setTenantId(userInfo.getTenantId());
        batchTravel.setBusinessKey(String.format("%s_%s", batchTravel.getBatchTravelId(), BusinessCodeEnum.BATCH_TRAVEL.getCode()));
        batchTravel.setRemarks(travelDto.getRemarks());
        batchTravel.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        batchTravel.setDeleted(0);
        batchTravel.setCreateBy(userInfo.getUserId());
        batchTravel.setCreateTime(System.currentTimeMillis());
        batchTravel.setUpdateBy(batchTravel.getCreateBy());
        batchTravel.setUpdateTime(batchTravel.getCreateTime());
        waBatchTravelDomainService.save(batchTravel);
        // 保存明细
        empTravelSaveDto.setBatchTravelId(batchTravel.getBatchTravelId());
        empTravelSaveDto.setExtCustomCol(travelDto.getWaEmpTravelExtCustomCol());
        empTravelSaveDto.setTravelDayTimeExtInfoMap(travelDto.getTravelDayTimeExtInfoMap());
        empTravelSaveDto.setOpt(2);
        empTravelSaveDto.setIfBtchTravel(true);
        Result<?> saveResult = empTravelService.checkOrSaveTravelTime(empTravelSaveDto);
        if (!saveResult.isSuccess()) {
            throw new ServerException(saveResult.getMsg());
        }
        return batchTravel;
    }

    public void startWorkflow(WfBeginWorkflowDto workflowDto, WaBatchTravelDo batchTravel) {
        Result<?> result = null;
        try {
            result = wfRegisterFeign.begin(workflowDto);
            log.debug("BatchTravel startWorkflow result={}", FastjsonUtil.toJsonStr(result));
        } catch (Exception e) {
            log.error("Employee travel application has exception:{}", e.getMessage(), e);
            waBatchTravelDomainService.deleteById(batchTravel.getBatchTravelId());
            empTravelService.deleteByBatchId(batchTravel.getTenantId(), batchTravel.getBatchTravelId());
            if (e instanceof CDException || e instanceof ServerException) {
                if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                    throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
                } else {
                    throw e;
                }
            } else {
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
            }
        }
        if (null == result || !result.isSuccess() || null == result.getData()
                || StringUtils.isBlank(result.getData().toString())) {
            waBatchTravelDomainService.deleteById(batchTravel.getBatchTravelId());
            empTravelService.deleteByBatchId(batchTravel.getTenantId(), batchTravel.getBatchTravelId());
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, null).getMsg());
        }
    }

    public PageResult<EmpTravelVo> getPageList(EmpTravelReqDto dto, HttpServletRequest request) {
        UserInfo userInfo = UserContext.preCheckUser();
        dto.setIfBatch(Boolean.TRUE);
        PageResult<EmpTravelDto> pageResult = empTravelService.getEmpTravelPageList(dto, userInfo);
        List<EmpTravelDto> items = pageResult.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            items.forEach(item -> {
                item.setTravelId(item.getBatchTravelId());
                item.setBusinessKey(item.getBatchTravelId() + "_" + BusinessCodeEnum.BATCH_TRAVEL.getCode());
            });
        }
        List<EmpTravelVo> voList = ObjectConverter.convertList(pageResult.getItems(), EmpTravelVo.class);
        return new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    public TaskDto export(EmpTravelReqDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("BatchEmpTravel");
        TaskDto task = SpringUtil.getBean(ITaskService.class).save(taskDto);
        requestDto.setIfBatch(Boolean.TRUE);
        SpringUtil.getBean(IExportService.class).exportEmpTravelList(requestDto, task, UserContext.preCheckUser(), ResponseWrap.getLocale());
        return task;
    }

    public PageResult<EmpTravelVo> getPageListOfPortal(QueryPageBean queryPageBean) {
        PageResult<EmpTravelDto> pageResult = empTravelService.getEmpTravelPageListOfPortal(queryPageBean, Boolean.TRUE);
        PageResult<EmpTravelVo> empTravelVoPageResult = new PageResult<>();
        BeanUtils.copyProperties(pageResult, empTravelVoPageResult, "items");
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            List<EmpTravelDto> items = pageResult.getItems();
            items.forEach(item -> {
                item.setTravelId(item.getBatchTravelId());
                item.setBusinessKey(item.getBatchTravelId() + "_" + BusinessCodeEnum.BATCH_TRAVEL.getCode());
            });
            empTravelVoPageResult.setItems(ObjectConverter.convertList(items, EmpTravelVo.class));
        } else {
            empTravelVoPageResult.setItems(Lists.newArrayList());
        }
        return empTravelVoPageResult;
    }

    public Result<Boolean> revokeEmpTravel(RevokeEmpTraveDto dto) throws Exception {
        WaBatchTravelDo batchTravelDo = waBatchTravelDomainService.getById(dto.getTravelId());
        if (batchTravelDo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TIME_NOT_EXIST, Boolean.FALSE);
        }
        List<WaEmpTravelDo> waEmpTravelDoList = waEmpTravelDo.listByBatchId(batchTravelDo.getTenantId(), batchTravelDo.getBatchTravelId());
        if (CollectionUtils.isEmpty(waEmpTravelDoList)) {
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TIME_NOT_EXIST, Boolean.FALSE);
        }
        WaEmpTravelDo empTravelDo = waEmpTravelDoList.get(0);
        WaTravelTypeDo travelType = waTravelTypeDo.selectOneById(empTravelDo.getTenantId(), empTravelDo.getTravelTypeId());
        if (travelType == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE_NOT_EXIST, Boolean.FALSE);
        }

        // 撤销明细
        if (!ApprovalStatusEnum.REVOKED.getIndex().equals(empTravelDo.getStatus())) {
            Result<Boolean> checkResult = empTravelService.preCheckBeforeRevoke(empTravelDo, travelType);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
        }

        // 保存撤销原因
        empTravelDo.setRevokeReason(dto.getRecokeReason());
        empTravelDo.setUpdateBy(UserContext.getUserId());
        empTravelDo.setUpdateTime(DateUtil.getCurrentTime(true));
        waEmpTravelDo.update(empTravelDo);

        Integer status = batchTravelDo.getStatus();
        if (ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(status)) {
            // 撤销流程
            WfRevokeDto revokeDto = new WfRevokeDto();
            String businessKey = batchTravelDo.getBatchTravelId() + "_" + BusinessCodeEnum.BATCH_TRAVEL.getCode();
            revokeDto.setBusinessKey(businessKey);
            Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
            if (null == result || !result.isSuccess()) {
                // 工作流撤销异常
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201919", WebUtil.getRequest()));
            }
        } else if (ApprovalStatusEnum.PASSED.getIndex().equals(status)) {
            String businessKey = String.valueOf(batchTravelDo.getBatchTravelId());
            String wfBusKey = String.format("%s_%s", businessKey, BusinessCodeEnum.BATCH_TRAVEL.getCode());
            WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
            wfCallbackResultDto.setBusinessKey(wfBusKey);
            wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.REVOKE);
            wfCallbackResultDto.setTenantId(batchTravelDo.getTenantId());
            workflowCallBackService.saveBatchTravelApproval(wfCallbackResultDto);
        }

        return Result.ok(Boolean.TRUE);
    }

    /**
     * 详情（审批页面使用）
     *
     * @param corpId
     * @param businessId
     * @param summary
     * @return
     */
    public WfResponseDto getWfDetail(Long corpId, Long businessId, boolean summary) {
        WfResponseDto responseDto = new WfResponseDto();
        Optional<WaBatchTravelDo> optional = Optional.ofNullable(waBatchTravelDomainService.getById(businessId));
        if (!optional.isPresent()) {
            return responseDto;
        }
        WaBatchTravelDo batchTravel = optional.get();
        List<WaEmpTravelDo> waEmpTravelDos = waEmpTravelDo.listByBatchId(batchTravel.getTenantId(), batchTravel.getBatchTravelId());
        if (CollectionUtils.isEmpty(waEmpTravelDos)) {
            return responseDto;
        }
        WaEmpTravelDo empTravel = waEmpTravelDo.getWaEmpTravelDetailById(waEmpTravelDos.get(0).getTravelId(), corpId);

        List<WfBusinessDataDetailDto> list = new ArrayList<>();
        list.add(new WfBusinessDataDetailDto("workno", ResponseWrap.wrapResult(AttendanceCodes.WORK_NO, null).getMsg(), empTravel.getWorkNo(), null));
        list.add(new WfBusinessDataDetailDto("name", ResponseWrap.wrapResult(AttendanceCodes.EMP_NAME, null).getMsg(), empTravel.getEmpName(), null));
        list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.DEPLOY, null).getMsg(), empTravel.getFullPath(), null));
        list.add(new WfBusinessDataDetailDto("travelType", ResponseWrap.wrapResult(AttendanceCodes.BUSINESS_TRIP_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empTravel.getI18nTravelTypeName(), empTravel.getTravelType()), null));

        SysUnitCity proviceObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, empTravel.getProvince());
        SysUnitCity cityObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_CITY_KEY, empTravel.getCity());
        String province = null != proviceObj ? proviceObj.getChnName() : "";
        String city = null != cityObj ? cityObj.getChnName() : "";
        list.add(new WfBusinessDataDetailDto("address", ResponseWrap.wrapResult(AttendanceCodes.VISIT_LOCATION, null).getMsg(), province + " " + city, null));

        if (StringUtils.isNotBlank(empTravel.getExtCustomCol())) {
            BatchTravelDto travelDetail = FastjsonUtil.convertObject(empTravel.getExtCustomCol(), BatchTravelDto.class);
            list.add(new WfBusinessDataDetailDto("accessObject", ResponseWrap.wrapResult(AttendanceCodes.VISIT_OBJECT, null).getMsg(), travelDetail.getAccessObject(), null));
            list.add(new WfBusinessDataDetailDto("purpose", ResponseWrap.wrapResult(AttendanceCodes.OBJECTIVE, null).getMsg(), travelDetail.getPurpose(), null));
            list.add(new WfBusinessDataDetailDto("costCenter", ResponseWrap.wrapResult(AttendanceCodes.COST_BEARING, null).getMsg(), travelDetail.getCostCenterName(), null));
            list.add(new WfBusinessDataDetailDto("tel", ResponseWrap.wrapResult(AttendanceCodes.CONTACT_NUMBER, null).getMsg(), travelDetail.getTel(), null));
            list.add(new WfBusinessDataDetailDto("peerPeople", ResponseWrap.wrapResult(AttendanceCodes.COLLEAGUES, null).getMsg(), travelDetail.getPeerPeople(), null));
            list.add(new WfBusinessDataDetailDto("businessContact", ResponseWrap.wrapResult(AttendanceCodes.BUSINESS_CONTACT_DURING_ABSENCE, null).getMsg(), travelDetail.getBusinessContact(), null));
        }
        list.add(new WfBusinessDataDetailDto("remarks", ResponseWrap.wrapResult(AttendanceCodes.NOTE, null).getMsg(), Optional.ofNullable(empTravel.getReason()).orElse("-"), null));
        List<BatchTravelTimeDetailDto> travelTimes = waBatchTravelDomainService.getTravelTimes(empTravel);
        if (summary && CollectionUtils.isNotEmpty(travelTimes)) {
            // 出差状态 1 出发 2 中转 3 回归
            Optional<BatchTravelTimeDetailDto> start = travelTimes.stream().filter(o -> o.getTraveStatus() == 1).findFirst();
            Long startTravelDate = start.map(BatchTravelTimeDetailDto::getTravelDate).orElse(null);
            Optional<BatchTravelTimeDetailDto> end = travelTimes.stream().filter(o -> o.getTraveStatus() == 3).findFirst();
            Long endTravelDate = end.map(BatchTravelTimeDetailDto::getTravelDate).orElse(startTravelDate);
            if (null != startTravelDate) {
                list.add(new WfBusinessDataDetailDto("travelTimes", ResponseWrap.wrapResult(AttendanceCodes.BUSINESS_TRIP_DATE, null).getMsg(),
                        String.format("%s-%s", DateUtil.getDateStrByTimesamp(startTravelDate),
                                DateUtil.getDateStrByTimesamp(endTravelDate)), null));
            }
        } else {
            list.add(new WfBusinessDataDetailDto("travelTimes", ResponseWrap.wrapResult(AttendanceCodes.BUSINESS_TRIP_DATE, null).getMsg(), travelTimes, null));
        }
        list.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), batchTravel.getProcessCode() == null ? "-" : batchTravel.getProcessCode(), null));
        responseDto.setDetailList(list);
        return responseDto;
    }

    /**
     * 详情（业务列表页面使用）
     *
     * @param corpId
     * @param businessId
     * @return
     */
    public WfDetailDto getWfDetailDto(Long corpId, Long businessId) {
        WfResponseDto travelDetail = getWfDetail(corpId, businessId, false);
        if (CollectionUtils.isEmpty(travelDetail.getDetailList())) {
            return new WfDetailDto();
        }
        String orgFullPath = null;
        List<KeyValue> items = Lists.newArrayList();
        for (WfBusinessDataDetailDto o : travelDetail.getDetailList()) {
            if (o.getKey().equals("travelTimes")) {
                items.add(new WaKeyValue(o.getText(), o.getValue(), o.getKey()));
            } else {
                items.add(new KeyValue(o.getText(), o.getValue()));
                if (o.getKey().equals("org") && null != o.getValue()) {
                    orgFullPath = o.getValue().toString();
                }
            }
        }
        WfDetailDto detailDto = new WfDetailDto();
        detailDto.setItems(items);
        detailDto.setFullPath(orgFullPath);
        return detailDto;
    }
}