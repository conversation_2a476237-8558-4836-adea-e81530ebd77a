package com.caidao1.integrate.trigger;

import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.integrate.service.IntegratedService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;

@Configurable
@DisallowConcurrentExecution
public class DataInputJob implements Job {
    private final static Logger LOGGER = LoggerFactory.getLogger(DataInputJob.class);

    @Autowired
    private IntegratedService integratedService;

    @Override
    public void execute(JobExecutionContext context) {
        LOGGER.info("DataInputJob start execute ...");
        try {
            JobDataMap dataMap = context.getJobDetail().getJobDataMap();
            String dataInputId = dataMap.getString("dataInputId");
            String corpId = dataMap.getString("corpId");
            String belongId = dataMap.getString("belongId");

            LOGGER.info("DataInputJob execute. dataInputId=[{}], corpId=[{}], belongId=[{}], clearCache=true, exparams=null", dataInputId, corpId, belongId);
            integratedService.syncDataInput(ConvertHelper.intConvert(dataInputId), ConvertHelper.longConvert(corpId), belongId, true, null);
        } catch (Exception ex) {
            LOGGER.error("DataOutputJob execute err,{}", ex.getMessage(), ex);
        }
        LOGGER.info("DataInputJob end execute ...");
    }
}