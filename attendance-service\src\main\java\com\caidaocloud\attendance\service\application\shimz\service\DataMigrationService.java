package com.caidaocloud.attendance.service.application.shimz.service;

import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.wa.mybatis.mapper.WaAnalyzeMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveTypeMapper;
import com.caidao1.wa.mybatis.mapper.WaOvertimeTypeMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaAnalyzeDo;
import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.interfaces.shimz.dto.DataMigrationDto;
import com.caidaocloud.dto.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataMigrationService {

    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaOvertimeTypeMapper waOvertimeTypeMapper;
    @Autowired
    private WaTravelTypeDo waTravelTypeDo;
    @Autowired
    private WaAnalyzeDo waAnalyzeDo;
    @Autowired
    private WaAnalyzeMapper waAnalyzeMapper;

    private static final int PAGE_SIZE = 1000;
    private static final Map<String, Integer> OVERTIME_TRANSFER = new HashMap<>();

    static {
        OVERTIME_TRANSFER.put("2_1", 19);
        OVERTIME_TRANSFER.put("1_1", 20);
        OVERTIME_TRANSFER.put("3_1", 21);
        OVERTIME_TRANSFER.put("2_2", 22);
    }

    public void clearAnalyzeData(DataMigrationDto dto, UserInfo userInfo) {
        String tenantId = Optional.ofNullable(dto.getTenantId()).orElse(userInfo.getTenantId());
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", tenantId);
        params.put("empId", dto.getEmpId());
        log.info("start to clear analyze data, tenant:{},empId:{}", tenantId, dto.getEmpId());
        //基础配置数据准备
        //假期类型
        log.info("leave type, tenant:{}", tenantId);
        Map<String, WaLeaveType> leaveTypeMap = getLeaveTypeMap(tenantId);
        log.info("leave type, tenant:{},data:{}", tenantId, JSONUtils.ObjectToJson(leaveTypeMap));
        //加班类型
        log.info("overtime type, tenant:{}", tenantId);
        Map<String, WaOvertimeType> overtimeTypeMap = getOvertimeTypeMap(tenantId);
        log.info("overtime type, tenant:{},data:{}", tenantId, JSONUtils.ObjectToJson(overtimeTypeMap));
        //出差类型
        log.info("travel type, tenant:{}", tenantId);
        Map<String, WaTravelTypeDo> travelTypeMap = getTravelTypeMap(tenantId);
        log.info("travel type, tenant:{},data:{}", tenantId, JSONUtils.ObjectToJson(travelTypeMap));
        //获取需要清洗数据总数
        AttendanceBasePage basePage = new AttendanceBasePage();
        basePage.setPageSize(1);
        basePage.setPageNo(1);
        AttendancePageResult<WaAnalyze> pageResult = waAnalyzeDo.getAnalyseList(basePage, params);
        //总条数
        int totalCount = pageResult.getTotal();
        log.info("totalCount:{}, tenant:{}", totalCount, tenantId);
        //总页数
        int totalPage = (int) Math.ceil((double) totalCount / PAGE_SIZE);
        log.info("page size:{}, tenant:{}", totalPage, tenantId);
        for (int pageNo = 1; pageNo <= totalPage; pageNo++) {
            basePage.setPageSize(PAGE_SIZE);
            basePage.setPageNo(pageNo);
            pageResult = waAnalyzeDo.getAnalyseList(basePage, params);
            List<WaAnalyze> items = pageResult.getItems();
            if (CollectionUtils.isEmpty(items)) {
                break;
            }
            for (WaAnalyze item : items) {
                if (item.getLevelColumnJsonb() != null) {
                    Map<String, Object> ltJsonMap = new HashMap<>();
                    PGobject pGobject = (PGobject) item.getLevelColumnJsonb();
                    if (StringUtils.isNotBlank(pGobject.getValue())) {
                        ltJsonMap = JSONUtils.convertPGobjectToMap(pGobject);
                    }
                    if (MapUtils.isNotEmpty(ltJsonMap)) {
                        List<String> removeKey = new ArrayList<>();
                        Map<String, Object> addLtJsonMap = new HashMap<>();
                        Map<String, Object> addTravelJsonMap = new HashMap<>();
                        for (Map.Entry<String, Object> entry : ltJsonMap.entrySet()) {
                            String mapKey = entry.getKey();
                            if (mapKey.contains("_name")) {
                                String[] arr = mapKey.split("_");
                                Integer typeId = Integer.valueOf(arr[1]);
                                String value = entry.getValue().toString();
                                if (travelTypeMap.containsKey(value)) {
                                    WaTravelTypeDo type = travelTypeMap.get(value);
                                    String waitingReplaceKey = String.format("_%s_", typeId);
                                    for (String key : ltJsonMap.keySet()) {
                                        if (key.contains(waitingReplaceKey)) {
                                            String replaceKey = String.format("_%s_", type.getTravelTypeId());
                                            addTravelJsonMap.put("valid_status", 1);
                                            addTravelJsonMap.put(key.replaceAll(waitingReplaceKey, replaceKey), ltJsonMap.get(key));
                                            String travelTimeDurationKey = String.format("lt_%s_key_minute", typeId);
                                            if (key.equals(travelTimeDurationKey)) {
                                                Integer addTravelDuration = Integer.valueOf(ltJsonMap.get(travelTimeDurationKey).toString());
                                                if (!addTravelJsonMap.containsKey("time_duration")) {
                                                    addTravelJsonMap.put("time_duration", addTravelDuration);
                                                } else {
                                                    Integer travelDuration = Integer.valueOf(addTravelJsonMap.get("time_duration").toString());
                                                    addTravelJsonMap.put("time_duration", travelDuration + addTravelDuration);
                                                }
                                                Integer leaveDuration = Integer.valueOf(ltJsonMap.get("time_duration").toString());
                                                int left = leaveDuration - addTravelDuration;
                                                ltJsonMap.put("time_duration", Math.max(left, 0));
                                            }
                                            removeKey.add(key);
                                        }
                                    }
                                }
                                if (leaveTypeMap.containsKey(value)) {
                                    String waitingReplaceKey = String.format("_%s_", typeId);
                                    WaLeaveType type = leaveTypeMap.get(value);
                                    for (String key : ltJsonMap.keySet()) {
                                        if (key.contains(waitingReplaceKey)) {
                                            String replaceKey = String.format("_%s_", type.getLeaveType());
                                            addLtJsonMap.put(key.replaceAll(waitingReplaceKey, replaceKey), ltJsonMap.get(key));
                                            removeKey.add(key);
                                        }
                                    }
                                }
                            }
                        }
                        ltJsonMap.keySet().removeIf(removeKey::contains);
                        if (MapUtils.isNotEmpty(addLtJsonMap)) {
                            ltJsonMap.putAll(addLtJsonMap);
                        }
                        if (MapUtils.isNotEmpty(addTravelJsonMap)) {
                            item.setTravelColumnJsonb(addTravelJsonMap);
                            item.setOriginTravelColumnJsonb(addTravelJsonMap);
                        }
                        if (ltJsonMap.size() == 1) {
                            ltJsonMap.remove("time_duration");
                        }
                        item.setLevelColumnJsonb(ltJsonMap);
                        item.setOriginLevelColumnJsonb(ltJsonMap);
                    }
                }
                if (item.getOtColumnJsob() != null) {
                    Map<String, Object> otJsonMap = new HashMap<>();
                    PGobject pGobject = (PGobject) item.getOtColumnJsob();
                    if (StringUtils.isNotBlank(pGobject.getValue())) {
                        otJsonMap = JSONUtils.convertPGobjectToMap(pGobject);
                    }
                    if (MapUtils.isNotEmpty(otJsonMap)) {
                        Map<String, Object> addOtJsonMap = new HashMap<>();
                        for (Map.Entry<String, Integer> transferEntry : OVERTIME_TRANSFER.entrySet()) {
                            for (String otKey : otJsonMap.keySet()) {
                                String durationKey = String.format("ot_%s_key", transferEntry.getKey());
                                if (otKey.equals(durationKey)) {
                                    addOtJsonMap.put(String.format("ot_%s_key", transferEntry.getValue()), new BigDecimal(otJsonMap.get(otKey).toString()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN).toPlainString());
                                    addOtJsonMap.put(String.format("ot_%s_key_unit", transferEntry.getValue()), 2);
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(addOtJsonMap)) {
                            otJsonMap.putAll(addOtJsonMap);
                        }
                        item.setOtColumnJsob(otJsonMap);
                        item.setOriginOtColumnJsonb(otJsonMap);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(items)) {
                for (WaAnalyze item : items) {
                    waAnalyzeMapper.updateByPrimaryKeySelective(item);
                }
            }
            log.info("task process(%):{}, tenant:{}", (pageNo / totalPage) * 100, tenantId);
        }
        log.info("task finished, tenant:{}", tenantId);
    }

    private Map<String, WaLeaveType> getLeaveTypeMap(String tenantId) {
        WaLeaveTypeExample example = new WaLeaveTypeExample();
        example.createCriteria().andBelongOrgidEqualTo(tenantId);
        List<WaLeaveType> types = waLeaveTypeMapper.selectByExample(example);
        return types.stream().collect(Collectors.toMap(WaLeaveType::getLeaveName, Function.identity(), (v1, v2) -> v1));
    }

    private Map<String, WaTravelTypeDo> getTravelTypeMap(String tenantId) {
        List<WaTravelTypeDo> types = waTravelTypeDo.getWaTravelTypeList(tenantId);
        return types.stream().collect(Collectors.toMap(WaTravelTypeDo::getTravelTypeName, Function.identity(), (v1, v2) -> v1));
    }

    private Map<String, WaOvertimeType> getOvertimeTypeMap(String tenantId) {
        WaOvertimeTypeExample example = new WaOvertimeTypeExample();
        example.createCriteria().andBelongOrgidEqualTo(tenantId);
        List<WaOvertimeType> types = waOvertimeTypeMapper.selectByExample(example);
        return types.stream().collect(Collectors.toMap(WaOvertimeType::getTypeName, Function.identity(), (v1, v2) -> v1));
    }
}