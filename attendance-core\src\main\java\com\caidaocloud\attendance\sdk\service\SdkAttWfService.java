package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.sdk.feign.AttWfFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 假勤-业务流程服务层
 *
 * <AUTHOR>
 * @Date 2024/8/5
 */
@Slf4j
@Service
public class SdkAttWfService {
    @Autowired
    private AttWfFeignClient attWfFeignClient;

    public WfResponseDto getWfDetail(String businessKey) {
        Result<WfResponseDto> result = null;
        try {
            result = attWfFeignClient.getWfDetail(businessKey);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
        WfResponseDto wfResponseDto = null;
        if (null == result || !result.isSuccess() || null == (wfResponseDto = result.getData())) {
            return wfResponseDto;
        }
        return wfResponseDto;
    }
}
