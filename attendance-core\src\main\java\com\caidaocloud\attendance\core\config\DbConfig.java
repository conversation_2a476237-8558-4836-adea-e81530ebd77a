package com.caidaocloud.attendance.core.config;

import com.caidao1.integrate.datasource.CusDataSource;
import com.caidaocloud.attendance.core.commons.quartz.JobBeanJobFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.quartz.spi.JobFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.Properties;

@Configuration
@EnableTransactionManagement
@MapperScan(basePackages = {"com.caidao1.**.mapper"}, sqlSessionFactoryRef = "sqlSessionFactory")
@ConditionalOnProperty(name = "shardingjdbc.open", havingValue = "false")
public class DbConfig {

    @Bean(name = "cusDataSource")
    public CusDataSource cusDataSource() {
        return new CusDataSource();
    }

    @Bean(name = "cusParameterJdbcTemplate")
    public NamedParameterJdbcTemplate cusParameterJdbcTemplate() throws IOException {
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(cusDataSource());
        return jdbcTemplate;
    }

    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource")
    public DataSourceProperties dataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "dataSource")
    @Primary
    @ConfigurationProperties("spring.datasource")
    public DataSource dataSource() {
        return dataSourceProperties().initializeDataSourceBuilder().build();
    }

    @Bean(name = "jobBeanJobFactory")
    public JobFactory jobBeanJobFactory() {
        return new JobBeanJobFactory();
    }

    @Bean(name = "quartzSf")
    public SchedulerFactoryBean schedulerFactoryBean(@Value("${quartz.autoStartup}") Boolean auto) throws IOException {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setJobFactory(jobBeanJobFactory());
        factory.setApplicationContextSchedulerContextKey("applicationContextKey");
        factory.setQuartzProperties(quartzProperties());
        factory.setDataSource(dataSource());
        factory.setAutoStartup(true);
        return factory;
    }

    @Bean
    public Properties quartzProperties() throws IOException {
        PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
        propertiesFactoryBean.setLocation(new ClassPathResource("/quartz2db.properties"));
        propertiesFactoryBean.afterPropertiesSet();
        return propertiesFactoryBean.getObject();
    }
}
