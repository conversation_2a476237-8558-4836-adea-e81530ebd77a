package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.core.wa.dto.MyLeaveTimeBaseDto;
import com.caidaocloud.attendance.sdk.dto.SdkLeaveApplySaveDTO;
import com.caidaocloud.attendance.sdk.dto.SdkMaternityLeaveRange;
import com.caidaocloud.attendance.sdk.feign.ILeaveFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 休假申请
 *
 * <AUTHOR>
 * @Date 2023/6/19
 */
@Component
public class LeaveFeignFallBack implements ILeaveFeignClient {
    @Override
    public Result<?> getLeaveTotalTime(SdkLeaveApplySaveDTO leaveApplySaveDto) {
        return Result.fail();
    }

    @Override
    public Result<?> saveLeaveApply(SdkLeaveApplySaveDTO leaveApplySaveDto) {
        return Result.fail();
    }

    @Override
    public Result<?> getUserLeaveTotalTime(SdkLeaveApplySaveDTO leaveApplySaveDto) {
        return Result.fail();
    }

    @Override
    public Result<?> getMaternityLeaveRange(SdkMaternityLeaveRange dto) {
        return Result.fail();
    }

    @Override
    public Result<List<MyLeaveTimeBaseDto>> listMyEmpLeave(Long date, Long empId) {
        return Result.fail();
    }
}
