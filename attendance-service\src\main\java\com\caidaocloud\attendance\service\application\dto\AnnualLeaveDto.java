package com.caidaocloud.attendance.service.application.dto;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import lombok.Data;

@Data
public class AnnualLeaveDto {
    private Integer empQuotaId;

    private Long empid;

    private String empName;

    private String workno;

    /**
     * 假期类型：leave_type = 1 年假
     */
    private String leaveName;

    private Long hireDate;

    private Long firstWorkDate;

    private Integer periodYear;

    /**
     * 发放周期开始日期unix时间戳
     */
    private Long disCycleStart;

    /**
     * 发放周期结束日期unix时间戳
     */
    private Long disCycleEnd;

    /**
     * 有效期
     */
    private Float validityDuration;

    /**
     * 有效期单位：1 天、2 月、3 年
     */
    private Integer validityUnit;

    /**
     * 生效日期
     */
    private Long startDate;

    /**
     * 失效日期
     */
    private Long lastDate;

    private String timeUnitName;

    /**
     * 本年额度
     */
    private Float quotaDay;

    /**
     * 当前额度
     */
    private Float nowQuota;

    private Float nowRemain;

    // 可否预支
    private Integer ifAdvance;

    /**
     * 本年可用
     * 计算规则：
     * 本年可用 = 可预支 ？本年额度 ：当前额度
     */
    private Float availableYear;

    private Float usedYear;

    /**
     * 调整额度
     */
    private Float adjustQuota;

    /**
     * 单位或时间单位：1、天，2、小时
     */
    private Integer acctTimeType;

    /**
     * 本年已用
     */
    private Float usedDay;

    private Float adjustUsedDay;

    /**
     * 调整已使用额度|调整本年已用|固定已使用
     */
    private Float fixUsedDay;

    /**
     * 预支年假 = 本年已用 + 流程中 - 当前额度
     */
    private Float advanceAnnualLeave;

    /**
     * 状态：失效日期>=当期日期就是生效，否则就是失效
     */
    private Boolean annualLeaveStatus;

    private String remarks;

    /**
     * 当前剩余|本年余额
     * 计算规则 = 本年可用 - 本年已用 + 调整额度 - 调整本年已用 - 流程中
     */
    private Float curRemain;

    // 当年总额度
    private Float currentYearTotalQuota;

    // 本年抵扣
    private Float deductionDay;

    // 冻结额度
    private Float frozenDay;

    // 在途额度，流程中
    private Float inTransitQuota;

    // 假期类型id
    private Integer leaveTypeId;

    // 余额类型id
    private String quotaSettingId;

    // 余额类型名称
    private String quotaSettingName;

    // 余额排序
    private Integer quotaSortNo;

    // 留存总额度
    private Float remainTotalQuota;

    // 上年结转额度
    private Float remainDay;

    // 上年结转已用
    private Float remainUsedDay;

    // 上年结转有效期至
    private Long remainValidDate;

    // 余额类型id
    private Integer roundTimeUnit;

    // 员工状态：0在职1离职2试用期
    private Integer status;

    // 员工状态
    private String statusTxt;

    // 余额类型id
    private Integer useItType;

    // 已使用总额度
    private Float usedTotalQuota;

    private String fullPath;
    /**
     * 离职日期
     */
    private Long terminationDate;
    private Integer empStatus;
    private String empStatusName;

    private Float currentQuota;

    private String quotaName;

    private Long configId;

    // 员工信息
    private EmpInfoDTO empInfo;

    private boolean isHomeLeave;
    private Float retainDay;
    private Float retainUsedDay;
    private Float retainInTransitQuota;
    private Long retainValidDate;

    public void checkAnnualLeaveStatus() {
        if (null == this.lastDate) {
            this.setAnnualLeaveStatus(false);
            return;
        }

        Long nowTime = System.currentTimeMillis() / 1000;
        // 状态：失效日期>=当前日期就是生效，否则就是失效
        this.setAnnualLeaveStatus(this.lastDate >= nowTime);
    }

    /**
     * 计算预支年假
     * 预支年假 = 本年已用 + 流程中 - 当前额度
     */
    public void calAdvanceAnnualLeave() {

        if (null == this.usedDay) {
            // 本年已用
            this.setUsedDay(0f);
        }

        if (null == this.nowQuota) {
            // 当前额度
            this.setNowQuota(0f);
        }

        if (null == this.inTransitQuota) {
            this.setInTransitQuota(0f);
        }

        if (null == this.adjustUsedDay) {
            this.setAdjustUsedDay(0f);
        }

        if (null == this.fixUsedDay) {
            this.setFixUsedDay(0f);
        }

        // 预支年假 = 本年已用 + 流程中 - 当前额度，老逻辑
        // 已预支年假 = 调整本年已用 + 本年已用 + 流程中 - 当前额度 - 调整额度，新逻辑
        this.setAdvanceAnnualLeave(this.fixUsedDay + this.usedDay + this.inTransitQuota - this.nowQuota - this.adjustQuota);
        // CDC-724 预支年假小于0，则默认显示为 0
        if (this.advanceAnnualLeave < 0) {
            this.setAdvanceAnnualLeave(0f);
        }
    }

    /**
     * 计算本年可用
     * 本年可用 = 可预支 ？本年额度 ：当前额度
     */
    public void calAvailableYear() {
        if (null == this.ifAdvance) {
            // 0 不可预支
            this.setIfAdvance(0);
        }

        // 本年可用 = 可预支 ？本年额度 ：当前额度
        this.setAvailableYear(1 == this.ifAdvance ? this.quotaDay : this.nowQuota);
    }

    /**
     * 计算本年余额
     * 本年余额 = 本年可用 - 本年已用 + 调整额度 - 调整本年已用 - 流程中
     */
    public void calCurRemain() {
        if (null == this.availableYear) {
            // 本年可用
            this.setAvailableYear(0f);
        }

        if (null == this.usedDay) {
            // 本年已用
            this.setUsedDay(0f);
        }

        if (null == this.adjustQuota) {
            // 调整额度
            this.setAdjustQuota(0f);
        }

        if (null == this.fixUsedDay) {
            // 调整本年已用
            this.setFixUsedDay(0f);
        }

        if (null == this.inTransitQuota) {
            this.setInTransitQuota(0f);
        }

        if (null == this.retainDay) {
            //上年留存,已失效，则留存等于流程中+留存已使用
            this.setRetainDay(0f);
        }

        if (null == this.retainInTransitQuota) {
            //上年留存流程中
            this.setRetainInTransitQuota(0f);
        }

        if (null == this.retainUsedDay) {
            //上年留存已用
            this.setRetainUsedDay(0f);
        }

        //本年余额= 本年可用 + 调整 - 本年已用 - 调整已用 - 流程中
        this.setCurRemain(this.availableYear + this.adjustQuota + this.retainDay - this.usedDay - this.fixUsedDay - this.inTransitQuota - this.retainInTransitQuota - this.retainUsedDay);
        if (this.curRemain < 0) {
            this.setCurRemain(0f);
        }
    }

    /**
     * 计算本年余额
     * 本年余额 = 当前额度 + 调整额度 - 本年已用 - 调整本年已用 - 流程中
     */
    public void calCurRemain2() {
        if (null == this.nowQuota) {
            // 当前额度
            this.setNowQuota(0f);
        }

        if (null == this.usedDay) {
            // 本年已用
            this.setUsedDay(0f);
        }

        if (null == this.adjustQuota) {
            // 调整额度
            this.setAdjustQuota(0f);
        }

        if (null == this.fixUsedDay) {
            // 调整本年已用
            this.setFixUsedDay(0f);
        }

        if (null == this.inTransitQuota) {
            //流程中
            this.setInTransitQuota(0f);
        }

        if (null == this.retainDay) {
            //上年留存,已失效，则留存等于流程中+留存已使用
            this.setRetainDay(0f);
        }

        if (null == this.retainInTransitQuota) {
            //上年留存流程中
            this.setRetainInTransitQuota(0f);
        }

        if (null == this.retainUsedDay) {
            //上年留存已用
            this.setRetainUsedDay(0f);
        }

        // 本年余额 = 当前额度 + 调整额度 - 本年已用 - 调整本年已用 - 流程中
        this.setCurRemain(this.nowQuota + this.adjustQuota + this.retainDay - this.usedDay - this.fixUsedDay - this.inTransitQuota - this.retainInTransitQuota - this.retainUsedDay);
        if (this.curRemain < 0) {
            this.setCurRemain(0f);
        }
    }

    /**
     * 计算当前余额
     * 当前余额 = 当前额度 + 调整额度 - 本年已用 - 调整本年已用
     * 当前余额 = 当前额度 + 调整额度 + 留存 - 本年已用 - 调整本年已用 - 留存已用 2024-10-21
     */
    public void calCurrentQuota() {
        if (null == this.nowQuota) {
            // 当前额度
            this.setNowQuota(0f);
        }
        if (null == this.adjustQuota) {
            // 调整额度
            this.setAdjustQuota(0f);
        }
        if (null == this.usedDay) {
            // 本年已用
            this.setUsedDay(0f);
        }
        if (null == this.fixUsedDay) {
            // 调整本年已用
            this.setFixUsedDay(0f);
        }
        if (null == this.inTransitQuota) {
            //流程中
            this.setInTransitQuota(0f);
        }

        if (null == this.retainDay) {
            //上年留存,已失效，则留存等于流程中+留存已使用
            this.setRetainDay(0f);
        }

        if (null == this.retainInTransitQuota) {
            //上年留存流程中
            this.setRetainInTransitQuota(0f);
        }

        if (null == this.retainUsedDay) {
            //上年留存已用
            this.setRetainUsedDay(0f);
        }

        // 当前余额 = 当前额度 + 调整额度 - 本年已用 - 调整本年已用 - 流程中
        // 当前余额 = 当前额度 + 调整额度 + 留存 - 本年已用 - 调整本年已用 - 留存已用
        this.setCurrentQuota(this.nowQuota + this.adjustQuota + this.retainDay - this.usedDay - this.fixUsedDay - this.inTransitQuota - this.retainInTransitQuota - this.retainUsedDay);
        if (this.currentQuota < 0) {
            this.setCurrentQuota(0f);
        }
    }
}
