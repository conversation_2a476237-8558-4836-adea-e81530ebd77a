package com.caidaocloud.attendance.service.application.enums;

public enum AbnormalTypeEnum {
    LATE_AND_EARLY(1, "迟到+早退"),
    LATE_OR_EARLY(2, "迟到/早退");

    private Integer index;
    private String desc;

    AbnormalTypeEnum(Integer index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDesc(int index) {
        for (AbnormalTypeEnum c : AbnormalTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.desc;
            }
        }
        return null;
    }
}
