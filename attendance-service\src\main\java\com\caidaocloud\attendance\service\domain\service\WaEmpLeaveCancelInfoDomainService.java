package com.caidaocloud.attendance.service.domain.service;

import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveCancelInfoDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WaEmpLeaveCancelInfoDomainService {
    @Autowired
    private WaEmpLeaveCancelInfoDo waEmpLeaveCancelInfoDo;

    public Integer checkTimeRepeat(Long empId, Integer leaveId, Long startTime, Long endTime) {
        return waEmpLeaveCancelInfoDo.checkTimeRepeat(empId, leaveId, startTime, endTime);
    }

    public void save(WaEmpLeaveCancelInfoDo cancelInfoDo) {
        waEmpLeaveCancelInfoDo.save(cancelInfoDo);
    }

    public List<WaEmpLeaveCancelInfoDo> getLeaveCancelInfoList(Long leaveCancelId){
        return waEmpLeaveCancelInfoDo.getLeaveCancelInfoList(leaveCancelId);
    }

    public List<WaEmpLeaveCancelInfoDo> getListByLeaveCancelId(List<Long> leaveCancelIds) {
        return waEmpLeaveCancelInfoDo.getListByLeaveCancelId(leaveCancelIds);
    }
}
