package com.caidaocloud.attendance.core.wa.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.employee.mybatis.mapper.EmpInfoMapper;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.wa.dto.EmpQuotaDTO;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.schedule.service.ScheduleQueryCoreService;
import com.caidaocloud.attendance.core.wa.dto.*;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiOvertimeDto;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.TxQuotaReturnUtil;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 假勤公共服务
 */
@Slf4j
@Service
public class WaCommonService extends ScheduleQueryCoreService {
    @Autowired
    private RemoteSmartWorkTimeService remoteSmartWorkTimeService;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private WaWorktimeDetailMapper waWorktimeDetailMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaShiftDefMapper waShiftDefMapper;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private WaEmpQuotaMapper waEmpQuotaMapper;
    @Autowired
    private WaEmpQuotaDetailMapper waEmpQuotaDetailMapper;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private EmpInfoMapper empInfoMapper;
    @Autowired
    private WaLeaveDaytimeMapper waLeaveDaytimeMapper;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaStoreTimeDetailMapper waStoreTimeDetailMapper;
    @Autowired
    private WaLeaveSettingMapper waLeaveSettingMapper;
    @Autowired
    private WaEmpQuotaUseMapper waEmpQuotaUseMapper;
    @Autowired
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private WaEmpLeaveTimeMapper waEmpLeaveTimeMapper;
    @Autowired
    private WaCompensatoryQuotaUseMapper waCompensatoryQuotaUseMapper;
    @Autowired
    private EmpCompensatoryQuotaMapper waEmpCompensatoryQuotaMapper;
    @Autowired
    private WaLeaveQuotaUseMapper waLeaveQuotaUseMapper;
    @Autowired
    private ISessionService sessionService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Transactional(rollbackFor = Exception.class)
    public void waGroupAnaly(Integer waGroupId, Long startDate, Long endDate, Long limitDate) {
        WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waGroupId);
        String belongOrgId = waGroup.getBelongOrgid();
        Map map = new HashMap();
        map.put("waGroupId", waGroupId);
        map.put("startDate", startDate);
        map.put("endDate", endDate + 24 * 3600);
        map.put("limitDate", limitDate + 24 * 3600);
        map.put("belongOrgId", belongOrgId);
        if (waGroup.getIsDefault()) {//是默认分组
            map.put("isDefalut", 1);
        } else {//不是默认分组
            map.put("isDefalut", 0);
        }
        waGroupMapper.updategroupAnalyLeave(map);
        //todo 后续根据业务逻辑重新处理
        //waGroupMapper.updategroupAnalyOt(map);
    }

    /**
     * 获取假期配额扣减顺序
     *
     * @param quotaSorts
     * @return
     */
    public Map<String, Integer> getEmpQuotaDecSorts(String quotaSorts) {
        Map<String, Integer> map = new HashMap<>();
        if (StringUtils.isNotBlank(quotaSorts)) {
            String[] sortsArray = quotaSorts.split(",");
            for (int i = 0; i < sortsArray.length; i++) {
                map.put(sortsArray[i], i);
            }
        }
        return map;
    }

    /**
     * 请假额度扣减
     *
     * @param quotaList
     * @param daytimeList
     * @param empId
     * @param belongOrgId
     * @param leaveTypeId
     * @param isNegative         配额不足时是否扣减成负数
     * @param lang
     * @param onlyUseEffectQuota 只能使用没失效的配额
     * @return
     * @throws Exception
     */
    @Transactional
    public String deductLeaveQuota(List<EmpQuotaDTO> quotaList, List<WaLeaveDaytime> daytimeList, Long empId,
                                   String belongOrgId, Integer leaveTypeId, Boolean isNegative, String lang, Boolean onlyUseEffectQuota) throws Exception {
        if (CollectionUtils.isNotEmpty(quotaList)) {
            WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveTypeId);
            if (waLeaveType == null) {//该假期类型系统不存在
                return messageResource.getMessage("L006844", new Object[]{}, new Locale(lang));
            }
            Map<Integer, WaEmpQuotaDetail> detailMap = new HashMap<>();
            Map<Integer, WaEmpQuota> waEmpQuotaMap = new HashMap<>();

            if (StringUtils.isNotBlank(waLeaveType.getQuotaSorts())) {
                List<EmpQuotaUseDTO> quotaDetailList = this.getEmpQuotaDetailOrderBySorts(belongOrgId, empId, quotaList, onlyUseEffectQuota, waLeaveType.getQuotaSorts());
                if (CollectionUtils.isNotEmpty(quotaDetailList)) {
                    for (WaLeaveDaytime daytime : daytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                            for (EmpQuotaUseDTO quotaDTO : quotaDetailList) {
                                if (dayDuration.floatValue() <= 0) {
                                    break;
                                }
                                if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                    Float keQuota = quotaDTO.getKeQuota();
                                    BigDecimal surplusQuota = new BigDecimal(keQuota);//剩余配额
                                    BigDecimal usedQuota = new BigDecimal(0);//使用额度
                                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {
                                        //额度足够
                                        usedQuota = usedQuota.add(dayDuration);
                                        surplusQuota = surplusQuota.subtract(dayDuration);
                                        dayDuration = new BigDecimal(0);
                                    } else {
                                        //额度不够
                                        usedQuota = usedQuota.add(surplusQuota);
                                        dayDuration = dayDuration.subtract(surplusQuota);
                                        surplusQuota = new BigDecimal(0);
                                    }
                                    //更新可用配额
                                    quotaDTO.setKeQuota(surplusQuota.floatValue());
                                    //更新已使用
                                    Float oldUsedDay = quotaDTO.getUsedDay();
                                    oldUsedDay = oldUsedDay == null ? 0f : oldUsedDay;
                                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                                    quotaDTO.setUsedDay(oldUsedDayDec.add(usedQuota).floatValue());
                                    //记录扣减配额明细
                                    this.deductionQuota(quotaDTO, waEmpQuotaMap, detailMap);
                                }
                            }
                            //额度不足，如果有本年配额可用，就把本年配额扣减成负数，如果本年额度不可用，需将留存剩余配额扣减成负数
                            if (dayDuration.floatValue() > 0 && BooleanUtils.isTrue(isNegative)) {
                                Map<Integer, List<EmpQuotaUseDTO>> quotaYearMap = new HashMap<>();
                                quotaDetailList.forEach(row -> {
                                    Integer year = row.getYear();
                                    List<EmpQuotaUseDTO> list = quotaYearMap.get(year);
                                    if (list == null) {
                                        list = new ArrayList<>();
                                    }
                                    list.add(row);
                                    quotaYearMap.put(year, list);
                                });

                                List<EmpQuotaUseDTO> quotaYearList = quotaYearMap.get(DateUtilExt.getTimeYear(leaveDate));
                                quotaYearList.sort(Comparator.comparing(EmpQuotaUseDTO::getSort));
                                EmpQuotaUseDTO lastCurQuota = null;
                                if (CollectionUtils.isNotEmpty(quotaYearList)) {
                                    lastCurQuota = quotaYearList.get(quotaYearList.size() - 1);
                                } else if (waLeaveType.getLeaveType() == 3) {
                                    lastCurQuota = quotaDetailList.get(quotaDetailList.size() - 1);
                                }
                                if (lastCurQuota != null) {
                                    BigDecimal oldUsedDayDec = new BigDecimal(lastCurQuota.getUsedDay());
                                    lastCurQuota.setUsedDay(oldUsedDayDec.add(dayDuration).floatValue());
                                    this.deductionQuota(lastCurQuota, waEmpQuotaMap, detailMap);
                                    dayDuration = new BigDecimal(0);
                                }
                            }
                            daytime.setTimeDuration(dayDuration.floatValue());
                        }
                    }
                }
            } else {
                //查询可用配额详情列表
                Map<String, List<EmpQuotaUseDTO>> quotaDetailMap = this.listWaEmpQuotaDetail(belongOrgId, empId, quotaList, onlyUseEffectQuota);
                if (CollectionUtils.isEmpty(quotaDetailMap.get("remainQuotas")) && CollectionUtils.isEmpty(quotaDetailMap.get("curYearQuotas"))) {//没有额度信息，不能请假
                    return messageResource.getMessage("L005760", new Object[]{}, new Locale(lang));
                }
                //先扣留存
                List<EmpQuotaUseDTO> remainQuotas = quotaDetailMap.get("remainQuotas");
                if (CollectionUtils.isNotEmpty(remainQuotas)) {
                    for (WaLeaveDaytime daytime : daytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                            for (EmpQuotaUseDTO quotaDTO : remainQuotas) {
                                if (dayDuration.floatValue() <= 0) {
                                    break;
                                }
                                if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                    Float keQuota = quotaDTO.getKeQuota();
                                    BigDecimal surplusQuota = new BigDecimal(keQuota);//剩余配额
                                    BigDecimal usedQuota = new BigDecimal(0);//使用额度
                                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {
                                        //额度足够
                                        usedQuota = usedQuota.add(dayDuration);
                                        surplusQuota = surplusQuota.subtract(dayDuration);
                                        dayDuration = new BigDecimal(0);
                                    } else {
                                        //额度不够
                                        usedQuota = usedQuota.add(surplusQuota);
                                        dayDuration = dayDuration.subtract(surplusQuota);
                                        surplusQuota = new BigDecimal(0);
                                    }
                                    quotaDTO.setKeQuota(surplusQuota.floatValue());
                                    //更新已使用
                                    Float oldUsedDay = quotaDTO.getUsedDay();
                                    oldUsedDay = oldUsedDay == null ? 0f : oldUsedDay;
                                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                                    quotaDTO.setUsedDay(oldUsedDayDec.add(usedQuota).floatValue());
                                    this.deductionQuota(quotaDTO, waEmpQuotaMap, detailMap);
                                }
                            }
                            daytime.setTimeDuration(dayDuration.floatValue());
                        }
                    }
                }
                //再扣减本年、调整
                List<EmpQuotaUseDTO> curYearQuotas = quotaDetailMap.get("curYearQuotas");
                if (CollectionUtils.isNotEmpty(curYearQuotas)) {
                    for (WaLeaveDaytime daytime : daytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长

                            for (EmpQuotaUseDTO quotaDTO : curYearQuotas) {
                                if (dayDuration.floatValue() <= 0) {
                                    break;
                                }
                                if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                    Float keQuota = quotaDTO.getKeQuota();
                                    BigDecimal surplusQuota = new BigDecimal(keQuota);//剩余配额
                                    BigDecimal usedQuota = new BigDecimal(0);//使用额度
                                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {
                                        //额度足够
                                        usedQuota = usedQuota.add(dayDuration);
                                        surplusQuota = surplusQuota.subtract(dayDuration);
                                        dayDuration = new BigDecimal(0);
                                    } else {
                                        //额度不够
                                        usedQuota = usedQuota.add(surplusQuota);
                                        dayDuration = dayDuration.subtract(surplusQuota);
                                        surplusQuota = new BigDecimal(0);
                                    }
                                    quotaDTO.setKeQuota(surplusQuota.floatValue());
                                    //更新已使用
                                    Float oldUsedDay = quotaDTO.getUsedDay();
                                    oldUsedDay = oldUsedDay == null ? 0f : oldUsedDay;
                                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                                    quotaDTO.setUsedDay(oldUsedDayDec.add(usedQuota).floatValue());

                                    this.deductionQuota(quotaDTO, waEmpQuotaMap, detailMap);
                                }
                            }

                            if (dayDuration.floatValue() > 0 && BooleanUtils.isTrue(isNegative)) {
                                Map<Integer, List<EmpQuotaUseDTO>> curYearQuotaMap = new HashMap<>();
                                curYearQuotas.forEach(row -> {
                                    Integer year = row.getYear();
                                    List<EmpQuotaUseDTO> list = curYearQuotaMap.get(year);
                                    if (list == null) {
                                        list = new ArrayList<>();
                                    }
                                    list.add(row);
                                    curYearQuotaMap.put(year, list);
                                });

                                //额度不足，扣减成负数
                                List<EmpQuotaUseDTO> quotaYearList = curYearQuotaMap.get(DateUtilExt.getTimeYear(leaveDate));
                                EmpQuotaUseDTO lastQuotaDTO = null;
                                if (CollectionUtils.isNotEmpty(quotaYearList)) {
                                    //排序
                                    Comparator<EmpQuotaUseDTO> comparator = Comparator.comparing(EmpQuotaUseDTO::getLastDate);
                                    Comparator<EmpQuotaUseDTO> comparator2 = Comparator.comparing(EmpQuotaUseDTO::getStartDate);
                                    quotaYearList.sort(comparator.thenComparing(comparator2));

                                    lastQuotaDTO = quotaYearList.get(quotaYearList.size() - 1);
                                } else if (waLeaveType.getLeaveType() == 3) {
                                    lastQuotaDTO = curYearQuotas.get(curYearQuotas.size() - 1);
                                }
                                if (lastQuotaDTO != null) {
                                    Float oldUsedDay = lastQuotaDTO.getUsedDay();
                                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                                    lastQuotaDTO.setUsedDay(oldUsedDayDec.add(dayDuration).floatValue());
                                    this.deductionQuota(lastQuotaDTO, waEmpQuotaMap, detailMap);
                                    dayDuration = new BigDecimal(0);
                                }
                            }
                            daytime.setTimeDuration(dayDuration.floatValue());
                        }
                    }
                } else if (CollectionUtils.isNotEmpty(remainQuotas)) {//本年额度不可用，需将留存剩余配额扣减成负数
                    Map<Integer, EmpQuotaUseDTO> remainYearQuota = new HashMap<>();
                    remainQuotas.forEach(row -> remainYearQuota.put(row.getYear(), row));

                    for (WaLeaveDaytime daytime : daytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                            if (dayDuration.floatValue() > 0 && BooleanUtils.isTrue(isNegative)) {//额度不足，扣减成负数
                                EmpQuotaUseDTO remainQuotaDTO = remainYearQuota.get(DateUtilExt.getTimeYear(leaveDate));
                                if (waLeaveType.getLeaveType() == 3 && remainQuotaDTO == null) {
                                    remainQuotaDTO = remainQuotas.get(remainQuotas.size() - 1);
                                }
                                if (remainQuotaDTO != null) {
                                    Float oldUsedDay = remainQuotaDTO.getUsedDay();
                                    oldUsedDay = oldUsedDay == null ? 0f : oldUsedDay;
                                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                                    remainQuotaDTO.setUsedDay(oldUsedDayDec.add(dayDuration).floatValue());
                                    this.deductionQuota(remainQuotaDTO, waEmpQuotaMap, detailMap);
                                    daytime.setTimeDuration(0f);
                                }
                            }
                        }
                    }
                }
            }
            //扣减配额
            if (!detailMap.isEmpty()) {
                Iterator<Map.Entry<Integer, WaEmpQuotaDetail>> entries = detailMap.entrySet().iterator();
                while (entries.hasNext()) {
                    Map.Entry<Integer, WaEmpQuotaDetail> entry = entries.next();
                    WaEmpQuotaDetail waEmpQuotaDetail = entry.getValue();
                    waEmpQuotaDetailMapper.updateByPrimaryKeySelective(waEmpQuotaDetail);
                }
            }
            //扣减主配额
            if (!waEmpQuotaMap.isEmpty()) {
                Iterator<Map.Entry<Integer, WaEmpQuota>> entries = waEmpQuotaMap.entrySet().iterator();
                while (entries.hasNext()) {
                    Map.Entry<Integer, WaEmpQuota> entry = entries.next();
                    WaEmpQuota waEmpQuota = entry.getValue();
                    waEmpQuotaMapper.updateByPrimaryKeySelective(waEmpQuota);
                }
            }
            //计算并且更新当年调整
            List<Integer> empQuotaIdList = new ArrayList<>();
            quotaList.forEach(map -> empQuotaIdList.add(map.getEmpQuotaId()));
            this.updateEmpAdjustQuota(empId, empQuotaIdList);
        } else {
            return messageResource.getMessage("L005760", new Object[]{}, new Locale(lang));
        }
        return "";
    }

    private BigDecimal covertNull2Zero2(Float val) {
        return val == null ? new BigDecimal(0) : new BigDecimal(val);
    }

    /**
     * 重新计算留存配额-排除调休假
     *
     * @param empId
     * @param leaveTypeId
     * @param leaveType
     * @param leaveYearList
     */
    @Transactional
    public void updateRemainDay(Long userId, String beongOrgId, Long empId, Integer leaveTypeId, Integer leaveType, List<Integer> leaveYearList) throws Exception {

        String genQuotaByYear = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + beongOrgId + RedisKeyDefine.IF_GEN_TXQUOTA_BY_YEAR);
        if (leaveType == 3 && !"1".equals(genQuotaByYear)) {
            return;
        }
        if (userId == null) {
            userId = 0L;
        }
        //上年留存配额自动计算
        List<Integer> searchYearList = new ArrayList<>();
        searchYearList.addAll(leaveYearList);
        leaveYearList.forEach(year -> {
            Integer nextYear = year + 1;
            if (searchYearList.indexOf(nextYear) == -1) {
                searchYearList.add(nextYear);
            }
        });
        List<EmpQuotaDTO> quotaList = this.getEmpQuotaList(leaveTypeId, leaveType, empId, "asc", searchYearList);
        Map<String, List<EmpQuotaDTO>> empQuotaListMap = new HashMap<>();
        Map<String, EmpQuotaDTO> empQuotaDetailMap = new HashMap<>();
        quotaList.forEach(row -> {
            String empQuotaKey = row.getPeriodYear() + "_" + row.getEmpid();
            List<EmpQuotaDTO> quotaDTOList;
            if (empQuotaListMap.containsKey(empQuotaKey) && empQuotaListMap.get(empQuotaKey) != null) {
                quotaDTOList = empQuotaListMap.get(empQuotaKey);
            } else {
                quotaDTOList = new ArrayList<>();
            }
            quotaDTOList.add(row);
            empQuotaListMap.put(empQuotaKey, quotaDTOList);
            empQuotaDetailMap.put(row.getPeriodYear() + "_" + row.getEmpid() + "_" + row.getQuotaSettingId(), row);
        });
        //上年留存是否继续结转至次年
        boolean ifCarryforwardRemainQuota = false;
        String remainConfig = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + beongOrgId + RedisKeyDefine.IF_CARRYFORWARD_REMAIN_QUOTA);
        if ("1".equals(remainConfig)) {
            ifCarryforwardRemainQuota = true;
        }
        List<WaEmpQuota> empQuotaUpdList = new ArrayList<>();
        List<Integer> relDecQuotaYearList = new ArrayList<>();//需重新扣减休假数据的年份
        //如果是申请的去年的休假，那么需要重新计算本年的上年留存，如果申请的是本年的休假并且本年的剩余配额已结转到下一年，那么需重新计算下一年的上年留存配额
        for (Integer year : leaveYearList) {
            String empQuotaKey = year + "_" + empId;
            //先判断是否有配额
            if (empQuotaListMap.containsKey(empQuotaKey) && CollectionUtils.isNotEmpty(empQuotaListMap.get(empQuotaKey))) {
                List<EmpQuotaDTO> quotaDTOList = empQuotaListMap.get(empQuotaKey);
                for (EmpQuotaDTO quotaDTO : quotaDTOList) {
                    //再判断配额是否已结转，如已结转到次年，则需同步更新次年的上年留存、上年留存已使用字段
                    if (BooleanUtils.isTrue(quotaDTO.getIfCarryForward())) {
                        //计算留存有效剩余
                        BigDecimal remainSurplus = new BigDecimal(0);
                        if (ifCarryforwardRemainQuota && quotaDTO.getRemainValidDate() != null && quotaDTO.getRemainValidDate() >= DateUtil.getOnlyDate() &&
                                quotaDTO.getRemainValidDate() >= quotaDTO.getStartDate()) {
                            BigDecimal remainDay = covertNull2Zero2(quotaDTO.getRemainDay());
                            BigDecimal remainUsedDay = covertNull2Zero2(quotaDTO.getRemainUsedDay());
                            remainSurplus = remainDay.subtract(remainUsedDay);
                        }
                        //计算剩余配额
                        BigDecimal remain_day = covertNull2Zero2(quotaDTO.getQuotaDay()).add(remainSurplus).subtract(covertNull2Zero2(quotaDTO.getDeductionDay()))
                                .subtract(covertNull2Zero2(quotaDTO.getUsedDay())).subtract(covertNull2Zero2(quotaDTO.getFixUsedDay()));
                        //判断次年是否已生成配额，如已生成，则更新留存数据
                        Integer nextYear = year + 1;
                        String nextYearKey = nextYear + "_" + empId + "_" + quotaDTO.getQuotaSettingId();
                        if (empQuotaDetailMap.containsKey(nextYearKey) && empQuotaDetailMap.get(nextYearKey) != null) {
                            EmpQuotaDTO nextYearQuotaDTO = empQuotaDetailMap.get(nextYearKey);
                            WaEmpQuota nextYearQuotUpd = new WaEmpQuota();
                            nextYearQuotUpd.setEmpQuotaId(nextYearQuotaDTO.getEmpQuotaId());
                            nextYearQuotUpd.setUpdtime(DateUtil.getCurrentTime(true));
                            nextYearQuotUpd.setUpduser(getUserInfo().getUserId());
                            nextYearQuotUpd.setRemainDay(remain_day.floatValue());
                            if (nextYearQuotaDTO.getRemainUsedDay() != null && nextYearQuotaDTO.getRemainUsedDay() > 0 && nextYearQuotaDTO.getRemainUsedDay() > remain_day.floatValue()) {
                                nextYearQuotUpd.setRemainUsedDay(remain_day.floatValue());
                                BigDecimal usedDay = new BigDecimal(nextYearQuotaDTO.getRemainUsedDay()).subtract(remain_day).add(new BigDecimal(nextYearQuotaDTO.getUsedDay()));
                                nextYearQuotUpd.setUsedDay(usedDay.floatValue());
                            }
                            empQuotaUpdList.add(nextYearQuotUpd);

                            if (relDecQuotaYearList.indexOf(nextYear) == -1) {
                                relDecQuotaYearList.add(nextYear);
                            }
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(empQuotaUpdList)) {
            //上年留存配额更新
            importService.fastUpdList(WaEmpQuota.class, "empQuotaId", empQuotaUpdList);
            //重新扣减配额
            this.reDeductLeaveQuotaForUpdateRemainDay(beongOrgId, userId, leaveTypeId, leaveType, empId, relDecQuotaYearList);
        }
    }

    /**
     * 更新上年留存时-重新扣减配额
     *
     * @param beongOrgId
     * @param userId
     * @param leaveTypeId
     * @param leaveType
     * @param empId
     * @param periodYearList
     * @throws Exception
     */
    public void reDeductLeaveQuotaForUpdateRemainDay(String beongOrgId, Long userId, Integer leaveTypeId, Integer leaveType, Long empId, List<Integer> periodYearList) throws Exception {
        if (CollectionUtils.isNotEmpty(periodYearList)) {
            //重新查找配额
            List<EmpQuotaDTO> quotaNewList = this.getEmpQuotaList(leaveTypeId, leaveType, empId, "asc", periodYearList);
            Map<String, List<EmpQuotaDTO>> empQuotaNewListMap = new HashMap<>();
            quotaNewList.forEach(row -> {
                String empQuotaKey = row.getPeriodYear() + "_" + row.getEmpid();
                List<EmpQuotaDTO> quotaDTOList;
                if (empQuotaNewListMap.containsKey(empQuotaKey) && empQuotaNewListMap.get(empQuotaKey) != null) {
                    quotaDTOList = empQuotaNewListMap.get(empQuotaKey);
                } else {
                    quotaDTOList = new ArrayList<>();
                }
                quotaDTOList.add(row);
                empQuotaNewListMap.put(empQuotaKey, quotaDTOList);
            });

            for (Integer relDecYear : periodYearList) {
                //重新扣减假期配额
                Map quotaCycleTime = getEmpQuotaCycleTime(leaveTypeId, empId, relDecYear);
                if (quotaCycleTime != null && !quotaCycleTime.isEmpty()) {
                    Long startDate = DateUtil.getOnlyDate((Date) quotaCycleTime.get("startDate"));
                    Long endDate = DateUtil.getOnlyDate((Date) quotaCycleTime.get("endDate"));

                    String empQuotaKey = relDecYear + "_" + empId;
                    //先判断是否有配额
                    if (empQuotaNewListMap.containsKey(empQuotaKey) && CollectionUtils.isNotEmpty(empQuotaNewListMap.get(empQuotaKey))) {
                        List<EmpQuotaDTO> quotaDTOList = empQuotaNewListMap.get(empQuotaKey);
                        reDeductLeaveQuotaDetail(quotaDTOList, startDate, endDate, leaveTypeId, leaveType, empId, beongOrgId, userId, null);
                    }
                }
            }
        }
    }

    /**
     * 请假额度预扣减
     *
     * @param quotaList
     * @param daytimeList
     * @param empId
     * @param belongOrgId
     * @param leaveTypeId
     * @param isNegative  配额不足时是否扣减成负数
     * @param lang
     * @return
     * @throws Exception
     */
    @Transactional
    public String preDeductLeaveQuota(List<EmpQuotaDTO> quotaList, List<WaLeaveDaytime> daytimeList, Long empId,
                                      String belongOrgId, Integer leaveTypeId, Boolean isNegative, String lang, Boolean onlyUseEffectQuota) throws Exception {
        if (CollectionUtils.isNotEmpty(quotaList)) {
            WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveTypeId);
            if (waLeaveType == null) {//该假期类型系统不存在
                return messageResource.getMessage("L006844", new Object[]{}, new Locale(lang));
            }
            List<Integer> empQuotaIdList = new ArrayList<>();
            Map<Integer, Float> empInTransitQuotaMap = new HashMap<>();//在途配额
            quotaList.forEach(row -> {
                empQuotaIdList.add(row.getEmpQuotaId());
                empInTransitQuotaMap.put(row.getEmpQuotaId(), row.getInTransitQuota());
            });

            if (StringUtils.isNotBlank(waLeaveType.getQuotaSorts())) {
                List<EmpQuotaUseDTO> quotaDetailList = this.getEmpQuotaDetailOrderBySorts(belongOrgId, empId, quotaList, onlyUseEffectQuota, waLeaveType.getQuotaSorts());
                if (CollectionUtils.isNotEmpty(quotaDetailList)) {
                    for (WaLeaveDaytime daytime : daytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                            for (EmpQuotaUseDTO quotaDTO : quotaDetailList) {
                                if (dayDuration.floatValue() <= 0) {
                                    break;
                                }
                                if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                    Float keQuota = quotaDTO.getKeQuota();
                                    BigDecimal surplusQuota = new BigDecimal(keQuota);//剩余配额
                                    BigDecimal usedQuota = new BigDecimal(0);//使用额度
                                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {
                                        //额度足够
                                        usedQuota = usedQuota.add(dayDuration);
                                        surplusQuota = surplusQuota.subtract(dayDuration);
                                        dayDuration = new BigDecimal(0);
                                    } else {
                                        //额度不够
                                        usedQuota = usedQuota.add(surplusQuota);
                                        dayDuration = dayDuration.subtract(surplusQuota);
                                        surplusQuota = new BigDecimal(0);
                                    }
                                    //更新可用配额
                                    quotaDTO.setKeQuota(surplusQuota.floatValue());
                                    //更新已使用
                                    Float oldUsedDay = quotaDTO.getUsedDay();
                                    oldUsedDay = oldUsedDay == null ? 0f : oldUsedDay;
                                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                                    quotaDTO.setUsedDay(oldUsedDayDec.add(usedQuota).floatValue());
                                    //更新在途配额
                                    this.preDeductionQuota(quotaDTO.getEmpQuotaId(), usedQuota.floatValue(), empInTransitQuotaMap);
                                }
                            }
                            //额度不足，如果有本年配额可用，就把本年配额扣减成负数，如果本年额度不可用，需将留存剩余配额扣减成负数
                            if (dayDuration.floatValue() > 0 && BooleanUtils.isTrue(isNegative)) {
                                Map<Integer, List<EmpQuotaUseDTO>> quotaYearMap = new HashMap<>();
                                quotaDetailList.forEach(row -> {
                                    Integer year = row.getYear();
                                    List<EmpQuotaUseDTO> list = quotaYearMap.get(year);
                                    if (list == null) {
                                        list = new ArrayList<>();
                                    }
                                    list.add(row);
                                    quotaYearMap.put(year, list);
                                });

                                List<EmpQuotaUseDTO> quotaYearList = quotaYearMap.get(DateUtilExt.getTimeYear(leaveDate));
                                quotaYearList.sort(Comparator.comparing(EmpQuotaUseDTO::getSort));
                                EmpQuotaUseDTO lastCurQuota = null;
                                if (CollectionUtils.isNotEmpty(quotaYearList)) {
                                    lastCurQuota = quotaYearList.get(quotaYearList.size() - 1);
                                } else if (waLeaveType.getLeaveType() == 3) {
                                    lastCurQuota = quotaDetailList.get(quotaDetailList.size() - 1);
                                }
                                if (lastCurQuota != null) {
                                    BigDecimal oldUsedDayDec = new BigDecimal(lastCurQuota.getUsedDay());
                                    lastCurQuota.setUsedDay(oldUsedDayDec.add(dayDuration).floatValue());
                                    //更新在途配额
                                    this.preDeductionQuota(lastCurQuota.getEmpQuotaId(), dayDuration.floatValue(), empInTransitQuotaMap);
                                    dayDuration = new BigDecimal(0);
                                }
                            }
                            daytime.setTimeDuration(dayDuration.floatValue());
                        }
                    }
                }
            } else {
                //查询可用配额详情列表
                Map<String, List<EmpQuotaUseDTO>> quotaDetailMap = this.listWaEmpQuotaDetail(belongOrgId, empId, quotaList, onlyUseEffectQuota);
                if (CollectionUtils.isEmpty(quotaDetailMap.get("remainQuotas")) && CollectionUtils.isEmpty(quotaDetailMap.get("curYearQuotas"))) {//没有额度信息，不能请假
                    return messageResource.getMessage("L005760", new Object[]{}, new Locale(lang));
                }
                //先扣留存
                List<EmpQuotaUseDTO> remainQuotas = quotaDetailMap.get("remainQuotas");
                if (CollectionUtils.isNotEmpty(remainQuotas)) {
                    for (WaLeaveDaytime daytime : daytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                            for (EmpQuotaUseDTO quotaDTO : remainQuotas) {
                                if (dayDuration.floatValue() <= 0) {
                                    break;
                                }
                                if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                    Float keQuota = quotaDTO.getKeQuota();
                                    BigDecimal surplusQuota = new BigDecimal(keQuota);//剩余配额
                                    BigDecimal usedQuota = new BigDecimal(0);//使用额度
                                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {
                                        //额度足够
                                        usedQuota = usedQuota.add(dayDuration);
                                        surplusQuota = surplusQuota.subtract(dayDuration);
                                        dayDuration = new BigDecimal(0);
                                    } else {
                                        //额度不够
                                        usedQuota = usedQuota.add(surplusQuota);
                                        dayDuration = dayDuration.subtract(surplusQuota);
                                        surplusQuota = new BigDecimal(0);
                                    }
                                    quotaDTO.setKeQuota(surplusQuota.floatValue());
                                    //更新已使用
                                    Float oldUsedDay = quotaDTO.getUsedDay();
                                    oldUsedDay = oldUsedDay == null ? 0f : oldUsedDay;
                                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                                    quotaDTO.setUsedDay(oldUsedDayDec.add(usedQuota).floatValue());
                                    //更新在途配额
                                    this.preDeductionQuota(quotaDTO.getEmpQuotaId(), usedQuota.floatValue(), empInTransitQuotaMap);
                                }
                            }
                            daytime.setTimeDuration(dayDuration.floatValue());
                        }
                    }
                }
                //再扣减本年、调整
                List<EmpQuotaUseDTO> curYearQuotas = quotaDetailMap.get("curYearQuotas");
                if (CollectionUtils.isNotEmpty(curYearQuotas)) {
                    for (WaLeaveDaytime daytime : daytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长

                            for (EmpQuotaUseDTO quotaDTO : curYearQuotas) {
                                if (dayDuration.floatValue() <= 0) {
                                    break;
                                }
                                if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                    Float keQuota = quotaDTO.getKeQuota();
                                    BigDecimal surplusQuota = new BigDecimal(keQuota);//剩余配额
                                    BigDecimal usedQuota = new BigDecimal(0);//使用额度
                                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {
                                        //额度足够
                                        usedQuota = usedQuota.add(dayDuration);
                                        surplusQuota = surplusQuota.subtract(dayDuration);
                                        dayDuration = new BigDecimal(0);
                                    } else {
                                        //额度不够
                                        usedQuota = usedQuota.add(surplusQuota);
                                        dayDuration = dayDuration.subtract(surplusQuota);
                                        surplusQuota = new BigDecimal(0);
                                    }
                                    quotaDTO.setKeQuota(surplusQuota.floatValue());
                                    //更新已使用
                                    Float oldUsedDay = quotaDTO.getUsedDay();
                                    oldUsedDay = oldUsedDay == null ? 0f : oldUsedDay;
                                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                                    quotaDTO.setUsedDay(oldUsedDayDec.add(usedQuota).floatValue());
                                    //更新在途配额
                                    this.preDeductionQuota(quotaDTO.getEmpQuotaId(), usedQuota.floatValue(), empInTransitQuotaMap);
                                }
                            }

                            if (dayDuration.floatValue() > 0 && BooleanUtils.isTrue(isNegative)) {
                                Map<Integer, List<EmpQuotaUseDTO>> curYearQuotaMap = new HashMap<>();
                                curYearQuotas.forEach(row -> {
                                    Integer year = row.getYear();
                                    List<EmpQuotaUseDTO> list = curYearQuotaMap.get(year);
                                    if (list == null) {
                                        list = new ArrayList<>();
                                    }
                                    list.add(row);
                                    curYearQuotaMap.put(year, list);
                                });

                                //额度不足，扣减成负数
                                List<EmpQuotaUseDTO> quotaYearList = curYearQuotaMap.get(DateUtilExt.getTimeYear(leaveDate));
                                EmpQuotaUseDTO lastQuotaDTO = null;
                                if (CollectionUtils.isNotEmpty(quotaYearList)) {
                                    //排序
                                    Comparator<EmpQuotaUseDTO> comparator = Comparator.comparing(EmpQuotaUseDTO::getLastDate);
                                    Comparator<EmpQuotaUseDTO> comparator2 = Comparator.comparing(EmpQuotaUseDTO::getStartDate);
                                    quotaYearList.sort(comparator.thenComparing(comparator2));

                                    lastQuotaDTO = quotaYearList.get(quotaYearList.size() - 1);
                                } else if (waLeaveType.getLeaveType() == 3) {
                                    lastQuotaDTO = curYearQuotas.get(curYearQuotas.size() - 1);
                                }
                                if (lastQuotaDTO != null) {
                                    Float oldUsedDay = lastQuotaDTO.getUsedDay();
                                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                                    lastQuotaDTO.setUsedDay(oldUsedDayDec.add(dayDuration).floatValue());
                                    //更新在途配额
                                    this.preDeductionQuota(lastQuotaDTO.getEmpQuotaId(), dayDuration.floatValue(), empInTransitQuotaMap);
                                    dayDuration = new BigDecimal(0);
                                }
                            }
                            daytime.setTimeDuration(dayDuration.floatValue());
                        }
                    }
                } else if (CollectionUtils.isNotEmpty(remainQuotas)) {//本年额度不可用，需将留存剩余配额扣减成负数
                    Map<Integer, EmpQuotaUseDTO> remainYearQuota = new HashMap<>();
                    remainQuotas.forEach(row -> remainYearQuota.put(row.getYear(), row));

                    for (WaLeaveDaytime daytime : daytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                            if (dayDuration.floatValue() > 0 && BooleanUtils.isTrue(isNegative)) {//额度不足，扣减成负数
                                EmpQuotaUseDTO remainQuotaDTO = remainYearQuota.get(DateUtilExt.getTimeYear(leaveDate));
                                if (waLeaveType.getLeaveType() == 3 && remainQuotaDTO == null) {
                                    remainQuotaDTO = remainQuotas.get(remainQuotas.size() - 1);
                                }
                                if (remainQuotaDTO != null) {
                                    Float oldUsedDay = remainQuotaDTO.getUsedDay();
                                    oldUsedDay = oldUsedDay == null ? 0f : oldUsedDay;
                                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                                    remainQuotaDTO.setUsedDay(oldUsedDayDec.add(dayDuration).floatValue());
                                    //更新在途配额
                                    this.preDeductionQuota(remainQuotaDTO.getEmpQuotaId(), dayDuration.floatValue(), empInTransitQuotaMap);
                                    daytime.setTimeDuration(0f);
                                }
                            }
                        }
                    }
                }
            }
            //在途配额更新
            if (MapUtils.isNotEmpty(empInTransitQuotaMap)) {
                List<WaEmpQuota> empQuotaUpdList = new ArrayList<>();
                empInTransitQuotaMap.forEach((id, inTransitQuota) -> {
                    WaEmpQuota empQuota = new WaEmpQuota();
                    empQuota.setEmpQuotaId(id);
                    empQuota.setInTransitQuota(inTransitQuota);
                    empQuotaUpdList.add(empQuota);
                });
                importService.fastUpdList(WaEmpQuota.class, "empQuotaId", empQuotaUpdList);
            }
        } else {
            return messageResource.getMessage("L005760", new Object[]{}, new Locale(lang));
        }
        return "";
    }

    /**
     * 预扣减配额
     *
     * @param empQuotaId
     * @param inTransitQuota
     * @param empInTransitQuotaMap
     */
    public void preDeductionQuota(Integer empQuotaId, Float inTransitQuota, Map<Integer, Float> empInTransitQuotaMap) {
        if (inTransitQuota == null || inTransitQuota == 0) return;
        if (empInTransitQuotaMap.containsKey(empQuotaId) && empInTransitQuotaMap.get(empQuotaId) != null) {
            empInTransitQuotaMap.put(empQuotaId, (new BigDecimal(empInTransitQuotaMap.get(empQuotaId)).add(new BigDecimal(inTransitQuota))).floatValue());
        } else {
            empInTransitQuotaMap.put(empQuotaId, inTransitQuota);
        }
    }

    /**
     * 休假配额预扣减
     *
     * @param daytimeList
     * @param empId
     * @param belongOrgId
     * @param leaveTypeId
     * @param leaveType
     * @param isNegative
     * @param lang
     * @return
     * @throws Exception
     */
    @Transactional
    public String preDeductionEmpQuotaDetail(List<WaLeaveDaytime> daytimeList, Long empId, String belongOrgId, Integer leaveTypeId,
                                             Integer leaveType, Boolean isNegative, String lang) throws Exception {
        if (CollectionUtils.isNotEmpty(daytimeList)) {
            //提取休假年份
            List<Integer> periodYears = new ArrayList<>();
            daytimeList.forEach(row -> {
                try {
                    Integer leaveYear = DateUtilExt.getTimeYear(row.getLeaveDate());
                    if (periodYears.indexOf(leaveYear) == -1) {
                        periodYears.add(leaveYear);
                    }
                } catch (ParseException e) {
                    log.error(e.getMessage(), e);
                }
            });

            //员工假期配额
            List<EmpQuotaDTO> quotaList = getEmpQuotaList(leaveTypeId, leaveType, empId, "asc", periodYears);
            return this.preDeductLeaveQuota(quotaList, daytimeList, empId, belongOrgId, leaveTypeId, isNegative, lang, false);
        }
        return "";
    }

    /**
     * 查询员工请假数据
     *
     * @param leaveTypeId
     * @param empId
     * @param excludeLeaveId
     * @param firstDate
     * @param lastDate
     * @param status
     * @return
     */
    public List<WaLeaveDaytime> listWaEmpLeave(Integer leaveTypeId, Long empId, Integer excludeLeaveId, Long firstDate, Long lastDate, Integer[] status) {
        Map map = new HashMap();
        map.put("empId", empId);
        map.put("leaveTypeId", leaveTypeId);
        map.put("firstDate", firstDate);
        map.put("lastDate", lastDate);
        map.put("array", status);
        if (excludeLeaveId != null) {
            map.put("leaveId", excludeLeaveId);
        }
        return waLeaveDaytimeMapper.listLeaveDaytime(map);
    }

    /**
     * 更新员工在途配额
     *
     * @param belongOrgId
     * @param empid
     * @param leaveTypeId
     * @param leaveType
     * @throws Exception
     */
    @Transactional
    @Deprecated
    public void updateEmpInTransitQuota(Integer belongOrgId, Integer empid, Integer leaveTypeId, Integer leaveType) throws Exception {
//        WaLeaveSettingExample settingExample = new WaLeaveSettingExample();
//        settingExample.createCriteria().andLeaveTypeIdEqualTo(leaveTypeId).andBelongOrgidEqualTo(belongOrgId);
//        List<WaLeaveSetting> settingList = waLeaveSettingMapper.selectByExample(settingExample);
//        if(CollectionUtils.isNotEmpty(settingList)){
//            List<Integer> quotaSettingIdList = new ArrayList<>();
//            settingList.forEach(row -> quotaSettingIdList.add(row.getQuotaSettingId()));
//
//            WaEmpQuotaExample empQuotaExample = new WaEmpQuotaExample();
//            empQuotaExample.createCriteria().andEmpidEqualTo(empid).andQuotaSettingIdIn(quotaSettingIdList);
//
//            WaEmpQuota empQuota = new WaEmpQuota();
//            empQuota.setInTransitQuota(0f);
//            waEmpQuotaMapper.updateByExampleSelective(empQuota,empQuotaExample);
//
//        }
//        List<WaLeaveDaytime> leaveDaytimes = this.listWaEmpLeave(leaveTypeId, empid, null, null, null, new Integer[]{1});
//        if(CollectionUtils.isNotEmpty(leaveDaytimes)){
//            preDeductionEmpQuotaDetail(leaveDaytimes,empid,belongOrgId,leaveTypeId,leaveType,true, SessionHolder.getLang());
//        }
    }

    /**
     * 扣减配额
     *
     * @param quotaDTO
     * @param waEmpQuotaMap
     * @param detailMap
     */
    public void deductionQuota(EmpQuotaUseDTO quotaDTO, Map<Integer, WaEmpQuota> waEmpQuotaMap, Map<Integer, WaEmpQuotaDetail> detailMap) {
        Integer type = quotaDTO.getType();
        Integer id = quotaDTO.getId();
        Float usedDay = quotaDTO.getUsedDay();
        Float quotaDay = quotaDTO.getQuotaDay();

        if (type == 0 || type == 1) {
            WaEmpQuota empQuotaUpd;
            if (waEmpQuotaMap.containsKey(id)) {
                empQuotaUpd = waEmpQuotaMap.get(id);
            } else {
                empQuotaUpd = new WaEmpQuota();
                empQuotaUpd.setEmpQuotaId(id);
            }
            if (type == 0) {
                //留存
                empQuotaUpd.setRemainUsedDay(usedDay);
            } else {
                //本年
                empQuotaUpd.setUsedDay(usedDay);
            }
            waEmpQuotaMap.put(id, empQuotaUpd);
        } else if (type == 2) {
            //调整
            WaEmpQuotaDetail empQuotaDetailUpd;
            if (detailMap.containsKey(id)) {
                empQuotaDetailUpd = detailMap.get(id);
            } else {
                empQuotaDetailUpd = new WaEmpQuotaDetail();
                empQuotaDetailUpd.setEmpQuotaDetailId(id);
                empQuotaDetailUpd.setUpdtime(DateUtil.getCurrentTime(true));
                empQuotaDetailUpd.setQuotaDay(quotaDay);
            }
            empQuotaDetailUpd.setUsedDay(usedDay);
            empQuotaDetailUpd.setSurplusQuota(((new BigDecimal(quotaDay)).subtract(new BigDecimal(usedDay))).floatValue());
            detailMap.put(id, empQuotaDetailUpd);
        }
    }

    /**
     * 计算并且更新当年调整
     *
     * @param empid
     */
    @Transactional
    public void updateEmpAdjustQuota(Long empid, List<Integer> empQuotaIdList) {
        if (CollectionUtils.isEmpty(empQuotaIdList)) {
            log.debug("WaConfigService.updateEmpAdjustQuota：更新数据为空");
            return;
        }
        //计算并且更新当年调整
        Map adjustParams = new HashMap();
        adjustParams.put("curDate", DateUtil.getOnlyDate());
        adjustParams.put("curTime", DateUtil.getCurrentTime(true));
        adjustParams.put("empid", empid);
        adjustParams.put("anyEmpQuotaIds", "'{" + StringUtils.join(empQuotaIdList, ",") + "}'");
        waMapper.updateBatchInitAdjustQuota(adjustParams);
        waMapper.updateAdjustQuotaNowKy(adjustParams);
        waMapper.updateAdjustQuotaTotalKy(adjustParams);


    }

    /**
     * 查询员工假期配额明细
     *
     * @param belongOrgId
     * @param empId
     * @param quotaList
     * @param onlyUseEffectQuota
     * @return
     */
    public Map<String, List<EmpQuotaUseDTO>> listWaEmpQuotaDetail(String belongOrgId, Long empId, List<EmpQuotaDTO> quotaList, Boolean onlyUseEffectQuota) {
        Map<String, List<EmpQuotaUseDTO>> quotaMap = new HashMap<>();
        onlyUseEffectQuota = onlyUseEffectQuota == null ? false : onlyUseEffectQuota;
        List<EmpQuotaUseDTO> listCurYearQuota = new ArrayList<>();
        List<EmpQuotaUseDTO> listRemainQuota = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(quotaList)) {
            //计算试用期额度折算比
            Float ratio = this.getConvertRatio(belongOrgId, empId);
            Long nowDate = DateUtil.getOnlyDate();
            for (EmpQuotaDTO empQuota : quotaList) {
                Integer year = Integer.valueOf(empQuota.getPeriodYear());
                Long startDate = empQuota.getStartDate();
                Long lastDate = empQuota.getLastDate();
                Long remainValidDate = empQuota.getRemainValidDate();
                Float remainUsedDay = empQuota.getRemainUsedDay();
                Float deductionDay = empQuota.getDeductionDay();
                Float remainDay = empQuota.getRemainDay();
                Float quotaDay = empQuota.getQuotaDay();
                Float nowQuota = empQuota.getNowQuota();
                Float usedDay = empQuota.getUsedDay();
                Float fixUsedDay = empQuota.getFixUsedDay();
                Integer empQuotaId = empQuota.getEmpQuotaId();
                Boolean isTrialConvert = empQuota.getTrialConvert();//试用期额度是否折算
                isTrialConvert = isTrialConvert == null ? false : isTrialConvert;
                Integer freezingRules = Integer.valueOf(empQuota.getFreezingRules() == null ? 0 : empQuota.getFreezingRules());//冻结规则 0 不冻结 1 试用期冻结 2 本年配额冻结
                Integer quotaSettingId = empQuota.getQuotaSettingId();

                deductionDay = deductionDay == null ? 0f : deductionDay;//额度抵扣
                remainDay = remainDay == null ? 0f : remainDay;//留存配额
                remainUsedDay = remainUsedDay == null ? 0f : remainUsedDay;//留存已使用

                //留存
                if (remainValidDate != null && (!onlyUseEffectQuota || remainValidDate >= nowDate)) {
                    BigDecimal remainDayBig = new BigDecimal(remainDay);
                    BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(remainUsedDay));//可用留存数
                    if (isTrialConvert && ratio != null) {
                        kyRemain = kyRemain.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                    }
                    EmpQuotaUseDTO quotaDTO = new EmpQuotaUseDTO();
                    quotaDTO.setQuotaSettingId(quotaSettingId);
                    quotaDTO.setQuotaDay(remainDayBig.floatValue());
                    quotaDTO.setKeQuota(kyRemain.floatValue());
                    quotaDTO.setUsedDay(remainUsedDay);
                    quotaDTO.setStartDate(startDate);
                    quotaDTO.setLastDate(remainValidDate);
                    quotaDTO.setId(empQuotaId);
                    quotaDTO.setType(0);
                    quotaDTO.setYear(year);
                    quotaDTO.setEmpQuotaId(empQuotaId);
                    listRemainQuota.add(quotaDTO);
                }

                //当前配额&本年调整
                List<EmpQuotaUseDTO> quotaDTOList = new ArrayList<>();
                //当前配额
                fixUsedDay = fixUsedDay == null ? 0f : fixUsedDay;
                usedDay = usedDay == null ? 0f : usedDay;
                BigDecimal keQuota = new BigDecimal(nowQuota).subtract(new BigDecimal(deductionDay)).subtract(new BigDecimal(usedDay)).subtract(new BigDecimal(fixUsedDay));
                if ((freezingRules != 2 || !year.equals(DateUtilExt.getNowYear())) && (!onlyUseEffectQuota || lastDate >= nowDate)) {
                    if (isTrialConvert && ratio != null) {
                        keQuota = keQuota.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                    }
                    EmpQuotaUseDTO quotaDTO = new EmpQuotaUseDTO();
                    quotaDTO.setQuotaSettingId(quotaSettingId);
                    quotaDTO.setQuotaDay(quotaDay);
                    quotaDTO.setKeQuota(keQuota.floatValue());
                    quotaDTO.setUsedDay(usedDay);
                    quotaDTO.setStartDate(startDate);
                    quotaDTO.setLastDate(lastDate);
                    quotaDTO.setId(empQuotaId);
                    quotaDTO.setType(1);
                    quotaDTO.setYear(year);
                    quotaDTO.setEmpQuotaId(empQuotaId);
                    quotaDTOList.add(quotaDTO);
                }
                //本年调整
                List<Map> listAdjustQuota = this.listEmpQuotaDetail(belongOrgId, empQuota.getEmpQuotaId(), 2);
                if (CollectionUtils.isNotEmpty(listAdjustQuota)) {
                    Boolean finalIsFilter = onlyUseEffectQuota;
                    Boolean finalIsTrialConvert = isTrialConvert;
                    listAdjustQuota.forEach(row -> {
                        Long adjustEndDate = (Long) row.get("endDate");
                        BigDecimal surplusQuota = new BigDecimal((Float) row.get("surplusQuota"));
                        if (!finalIsFilter || adjustEndDate >= nowDate) {
                            if (finalIsTrialConvert && ratio != null) {
                                surplusQuota = surplusQuota.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                            }
                            EmpQuotaUseDTO quotaDTO = new EmpQuotaUseDTO();
                            quotaDTO.setQuotaSettingId(quotaSettingId);
                            if (row.containsKey("quotaDay") && row.get("quotaDay") != null) {
                                quotaDTO.setQuotaDay(Float.valueOf(row.get("quotaDay").toString()));
                            } else {
                                quotaDTO.setQuotaDay(0f);
                            }
                            quotaDTO.setKeQuota(surplusQuota.floatValue());
                            if (row.containsKey("usedDay") && row.get("usedDay") != null) {
                                quotaDTO.setUsedDay(Float.valueOf(row.get("usedDay").toString()));
                            } else {
                                quotaDTO.setUsedDay(0f);
                            }
                            if (row.containsKey("startDate") && row.get("startDate") != null) {
                                quotaDTO.setStartDate(Long.valueOf(row.get("startDate").toString()));
                            }
                            quotaDTO.setLastDate(adjustEndDate);
                            if (row.containsKey("empQuotaDetailId") && row.get("empQuotaDetailId") != null) {
                                quotaDTO.setId(Integer.valueOf(row.get("empQuotaDetailId").toString()));
                            }
                            quotaDTO.setType(2);
                            quotaDTO.setYear(year);
                            quotaDTO.setEmpQuotaId(empQuotaId);
                            quotaDTOList.add(quotaDTO);
                        }
                    });
                }

                if (CollectionUtils.isNotEmpty(quotaDTOList)) {
                    //排序
                    Comparator<EmpQuotaUseDTO> comparator = Comparator.comparing(EmpQuotaUseDTO::getLastDate);
                    Comparator<EmpQuotaUseDTO> comparator2 = Comparator.comparing(EmpQuotaUseDTO::getStartDate);
                    quotaDTOList.sort(comparator.thenComparing(comparator2));
                    listCurYearQuota.addAll(quotaDTOList);
                }
            }
        }
        quotaMap.put("curYearQuotas", listCurYearQuota);
        quotaMap.put("remainQuotas", listRemainQuota);
        return quotaMap;
    }

    /**
     * 查询员工假期配额详情并且按照配额扣减顺序排序
     *
     * @param belongOrgId
     * @param empId
     * @param quotaList
     * @param onlyUseEffectQuota
     * @param sorts
     * @return
     */
    public List<EmpQuotaUseDTO> getEmpQuotaDetailOrderBySorts(String belongOrgId, Long empId, List<EmpQuotaDTO> quotaList, Boolean onlyUseEffectQuota, String sorts) {
        onlyUseEffectQuota = onlyUseEffectQuota == null ? false : onlyUseEffectQuota;

        List<EmpQuotaUseDTO> quotaDetailList = new ArrayList<>();

        //查询可用配额详情列表
        Map<String, List<EmpQuotaUseDTO>> quotaDetailMap = this.listWaEmpQuotaDetail(belongOrgId, empId, quotaList, onlyUseEffectQuota);
        List<EmpQuotaUseDTO> remainQuotas = quotaDetailMap.get("remainQuotas");
        if (CollectionUtils.isNotEmpty(remainQuotas)) {
            quotaDetailList.addAll(remainQuotas);
        }
        List<EmpQuotaUseDTO> curYearQuotas = quotaDetailMap.get("curYearQuotas");
        if (CollectionUtils.isNotEmpty(curYearQuotas)) {
            quotaDetailList.addAll(curYearQuotas);
        }

        //排序
        Map<String, Integer> quotaDecSorts = getEmpQuotaDecSorts(sorts);

        quotaDetailList.forEach(row -> {
            Integer quotaSettingId = row.getQuotaSettingId();
            Integer type = row.getType();//类型 0 留存 1 本年 2 调整
            if (type == 0) {
                Integer sort = quotaDecSorts.get(quotaSettingId + "_0");
                sort = sort == null ? 8888 : sort;//未设置排序的自动排在最后
                row.setSort(sort);
            } else {
                Integer sort = quotaDecSorts.get(quotaSettingId + "_1");
                sort = sort == null ? 9999 : sort;
                row.setSort(sort);
            }
        });

        quotaDetailList.sort(Comparator.comparing(EmpQuotaUseDTO::getSort));
        return quotaDetailList;
    }

    /**
     * 根据年份查询员工假期配额
     *
     * @param leaveTypeIds
     * @param empIds
     * @param sortNo
     * @param year
     * @return
     */
    public List<EmpQuotaDTO> getEmpQuotaListByYear(List<Integer> leaveTypeIds, List<Long> empIds, String sortNo, Integer year) {
        Map map = new HashMap();
        map.put("anyLeaveTypeIds", "'{" + StringUtils.join(leaveTypeIds, ",") + "}'");
        map.put("anyEmpIds", "'{" + StringUtils.join(empIds, ",") + "}'");
        map.put("periodYear", year);
        map.put("sortNo", sortNo);
        return waMapper.listWaEmpQuotaByYear(map);
    }

    /**
     * 员工配额查询
     *
     * @param leaveTypeId
     * @param leaveType
     * @param empId
     * @return
     */
    public List<EmpQuotaDTO> getEmpQuotaList(Integer leaveTypeId, Integer leaveType, Long empId, String sortNo, List<Integer> periodYears) {
        if (StringUtils.isEmpty(sortNo)) {
            sortNo = "asc";
        }
        if (CollectionUtils.isEmpty(periodYears)) {
            Calendar calendar = Calendar.getInstance();
            int curYear = calendar.get(Calendar.YEAR);
            periodYears = new ArrayList<>(Arrays.asList(curYear - 1, curYear, curYear + 1));
        }
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("leaveTypeId", leaveTypeId);
        paramsMap.put("empId", empId);
        paramsMap.put("sortNo", sortNo);

        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveTypeId);
        if (waLeaveType == null) {
            throw new CDException("假期类型不存在");
        }
        //开启调休配额按照年份去生成维护
        String genQuotaByYear = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + waLeaveType.getBelongOrgid() + RedisKeyDefine.IF_GEN_TXQUOTA_BY_YEAR);
        //leaveType 3 调休 ，8000 移动休假（目前仅适用于清水建设）
        if ((leaveType == 3 && !"1".equals(genQuotaByYear)) || leaveType.intValue() == 8000) {
            paramsMap.put("nowDate", DateUtil.getOnlyDate());
        } else {
            if (CollectionUtils.isNotEmpty(periodYears)) {
                paramsMap.put("periodYears", "'{" + StringUtils.join(periodYears, ",") + "}'");
            } else {
                paramsMap.put("nowDate", DateUtil.getOnlyDate());
            }
        }
        return waMapper.listWaEmpQuotaByYear(paramsMap);
    }

    public List<WaEmpQuota> listWaEmpQuotaModel(Integer leaveTypeId, Long empId, String sortNo, Integer periodYear) {
        Map map = new HashMap();
        map.put("leaveTypeId", leaveTypeId);
        map.put("empId", empId);
        map.put("periodYear", periodYear);
        map.put("sortNo", sortNo);
        return waEmpQuotaMapper.getEmpQuotaModelByYear(map);
    }

    /**
     * 查询假期调整配额明细
     *
     * @param belongOrgId
     * @param empQuotaId
     * @return
     */
    public List<Map> listEmpQuotaDetail(String belongOrgId, Integer empQuotaId, Integer type) {
        Map params = new HashMap();
        params.put("belongOrgId", belongOrgId);
        params.put("empQuotaId", empQuotaId);
        params.put("sort", "\"endDate\" asc");
        params.put("quotaType", type);//1 配额详情类型 2 调整配额
        return waMapper.listEmpQuotaDetail(params);
    }

    /**
     * 计算员工试用期额度折算比
     *
     * @param empid
     * @return
     */
    public Float getConvertRatio(String belongid, Long empid) {
        Float ratio = null;
        try {
            Map params = new HashMap();
            params.put("belongid", belongid);
            params.put("empid", empid);
            List<Map> listEmpContract = empInfoMapper.listProbationEmpContract(params);
            if (CollectionUtils.isNotEmpty(listEmpContract)) {
                //根据员工合同中的试用期设置计算员工试用期的总天数
                for (Map map : listEmpContract) {
                    Long startTime = (Long) map.get("startTime");
                    Long prodeadLine = (Long) map.get("prodeadLine");
                    if (prodeadLine != null) {
                        Integer yearDays = null;
                        Integer proDays = null;

                        Integer startYear = DateUtilExt.getTimeYear(startTime);
                        Integer endYear = DateUtilExt.getTimeYear(prodeadLine);

                        Long startYearBeginTime = DateUtilExt.getYearBeginTime(startTime);
                        Long startYearEndTime = DateUtilExt.getYearsEndTime(startTime);

                        if (startYear < endYear) {
                            //试用期跨年
                            Long endYearBeginTime = DateUtilExt.getYearBeginTime(prodeadLine);
                            Long endYearEndTime = DateUtilExt.getYearsEndTime(prodeadLine);
                            Long curDate = DateUtil.getOnlyDate();
                            if (curDate <= startYearEndTime) {
                                //一年的总天数
                                yearDays = DateUtilExt.getDifferenceDay(startYearBeginTime, startYearEndTime) + 1;
                                //当年试用期剩余天数
                                proDays = DateUtilExt.getDifferenceDay(curDate, startYearEndTime) + 1;
                            } else {
                                //一年的总天数
                                yearDays = DateUtilExt.getDifferenceDay(endYearBeginTime, endYearEndTime) + 1;
                                //当年试用期剩余天数
                                proDays = DateUtilExt.getDifferenceDay(endYearBeginTime, prodeadLine) + 1;
                            }
                        } else {
                            //试用期不跨年
                            //一年的总天数
                            yearDays = DateUtilExt.getDifferenceDay(startYearBeginTime, startYearEndTime) + 1;
                            //试用期天数
                            proDays = DateUtilExt.getDifferenceDay(startTime, prodeadLine) + 1;
                        }
                        if (yearDays != null && proDays != null) {
                            ratio = new BigDecimal((float) proDays / yearDays).setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
                            return ratio;
                        }
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ratio;
    }

    /**
     * 假期配额返还-重新扣减假期配额
     *
     * @param belongOrgId
     * @param userId
     * @param leaveId
     * @throws Exception
     */
    @Transactional
    public void giveBackEmpLeaveQuota(String belongOrgId, Long userId, Integer leaveId) throws Exception {
        WaEmpLeave waEmpLeave = waEmpLeaveMapper.selectByPrimaryKey(leaveId);
        if (waEmpLeave == null) {
            return;
        }
        WaLeaveTypeExample leaveTypeExample = new WaLeaveTypeExample();
        leaveTypeExample.createCriteria().andBelongOrgidEqualTo(belongOrgId).andLeaveTypeIdEqualTo(waEmpLeave.getLeaveTypeId());
        List<WaLeaveType> leaveTypeList = waLeaveTypeMapper.selectByExample(leaveTypeExample);
        if (CollectionUtils.isEmpty(leaveTypeList)) {
            return;
        }
        WaLeaveType waLeaveType = leaveTypeList.get(0);
        //是否开启调休配额有效期
        String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongOrgId + RedisKeyDefine.IS_OPEN_TX_QUOTA_VALIDITY);
        if (isOpen != null && "1".equals(isOpen) && waLeaveType.getLeaveType() == 3) {
            //调休额度返还
            WaLeaveDaytimeExample daytimeExample = new WaLeaveDaytimeExample();
            daytimeExample.createCriteria().andLeaveIdEqualTo(waEmpLeave.getLeaveId());
            List<WaLeaveDaytime> daytimeList = waLeaveDaytimeMapper.selectByExample(daytimeExample);
            if (daytimeList == null) {
                return;
            }
            //查询员工调休配额
            WaEmpQuota empQuota = this.getEmpTxQuota(waEmpLeave.getLeaveTypeId(), waEmpLeave.getEmpid());
            if (empQuota == null) {
                return;
            }

            List<WaEmpQuotaDetail> empQuotaDetailList = new ArrayList<>();
            List<MonthAmountBean> monthAmountList = new ArrayList<>();
            List<Integer> detailIds = new ArrayList<>();

            //查询员工调休配额明细
            Map params = new HashMap();
            params.put("belongOrgId", belongOrgId);
            params.put("empQuotaId", empQuota.getEmpQuotaId());
            params.put("sort", "\"endDate\" asc");
//            params.put("quotaType", 1);//1 配额详情类型 2 调整配额
            List<Map> quotaDetailList = waMapper.listEmpQuotaDetail(params);
            for (Map map : quotaDetailList) {
                Integer empQuotaDetailId = (Integer) map.get("empQuotaDetailId");
                Float quotaDay = (Float) map.get("quotaDay");
                Long waStartDate = (Long) map.get("waStartDate");
                String waYm = DateUtilExt.getTimeStrByPattern(waStartDate, "yyyyMM");

                if (detailIds.indexOf(empQuotaDetailId) < 0) {
                    detailIds.add(empQuotaDetailId);
                }

                //查询配额使用情况
                Map params1 = new HashMap();
                params1.put("empQuotaDetailId", empQuotaDetailId);
                List<Map> detailUseInfo = waMapper.listEmpQuotaDetailUseInfo(params1);

                List<PeriodAmountBean> periodAmounts = new ArrayList<>();
                for (Map map1 : detailUseInfo) {
                    String ym = (String) map1.get("ym");
                    Float usedDay = (Float) map1.get("usedDay");
                    periodAmounts.add(new PeriodAmountBean(Integer.parseInt(ym), usedDay));
                }
                monthAmountList.add(new MonthAmountBean(Integer.parseInt(waYm), quotaDay, periodAmounts, empQuotaDetailId));
            }

            for (WaLeaveDaytime daytime : daytimeList) {
                Float timeDuration = daytime.getTimeDuration();
                Long leaveDate = daytime.getLeaveDate();
                Integer ym = Integer.parseInt(DateUtilExt.getTimeStrByPattern(leaveDate, "yyyyMM"));

                TxQuotaReturnUtil.cancelAmount(monthAmountList, ym, timeDuration);
            }

            for (MonthAmountBean monthAmountBean : monthAmountList) {
                Integer id = monthAmountBean.getId();
                for (PeriodAmountBean periodAmountBean : monthAmountBean.getPeriodAmounts()) {
                    Float usedDay = periodAmountBean.getAmount();
                    Integer ym = periodAmountBean.getPeriod();

                    WaEmpQuotaUse use = new WaEmpQuotaUse();
                    use.setUsedDay(usedDay);
                    use.setYm(String.valueOf(ym));
                    use.setEmpQuotaDetailId(id);

                    WaEmpQuotaUseExample waEmpQuotaUseExample = new WaEmpQuotaUseExample();
                    waEmpQuotaUseExample.createCriteria().andEmpQuotaDetailIdEqualTo(id).andYmEqualTo(String.valueOf(ym));
                    waEmpQuotaUseMapper.updateByExampleSelective(use, waEmpQuotaUseExample);
                }
            }

            List<Map> listDetailUsed = waMapper.listEmpQuotaDetailUse(detailIds);
            for (Map map : listDetailUsed) {
                Integer empQuotaDetailId = (Integer) map.get("empQuotaDetailId");
                BigDecimal quotaDay = new BigDecimal(String.valueOf(map.get("quotaDay")));
                BigDecimal usedDay = new BigDecimal(String.valueOf(map.get("usedDay")));

                WaEmpQuotaDetail detail = new WaEmpQuotaDetail();
                detail.setEmpQuotaDetailId(empQuotaDetailId);
                detail.setUsedDay(usedDay.floatValue());
                detail.setSurplusQuota(quotaDay.subtract(usedDay).floatValue());
                empQuotaDetailList.add(detail);
            }
            importService.fastUpdList(WaEmpQuotaDetail.class, "empQuotaDetailId", empQuotaDetailList);

            Map empTxQuota = waMapper.getEmpTxQuotaById(empQuota.getEmpQuotaId());
            Float usedDay = (Float) empTxQuota.get("usedDay");
            Float quotaDay = (Float) empTxQuota.get("quotaDay");
            empQuota.setQuotaDay(quotaDay);
            empQuota.setUsedDay(usedDay);
            empQuota.setNowQuota(quotaDay);
            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
        } else {
            //开启调休配额按照年份去生成维护
            String genQuotaByYear = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongOrgId + RedisKeyDefine.IF_GEN_TXQUOTA_BY_YEAR);
            //重新计算配额
            if (waLeaveType.getLeaveType() != 3 || "1".equals(genQuotaByYear)) {
                //获取被撤销的休假单对应的年份
                WaEmpLeaveTimeExample empLeaveTimeExample = new WaEmpLeaveTimeExample();
                empLeaveTimeExample.createCriteria().andLeaveIdEqualTo(leaveId);
                List<WaEmpLeaveTime> empLeaveTimeList = waEmpLeaveTimeMapper.selectByExample(empLeaveTimeExample);
                if (CollectionUtils.isNotEmpty(empLeaveTimeList)) {
                    List<Integer> periodYearList = new ArrayList<>();
                    empLeaveTimeList.forEach(row -> {
                        try {
                            Integer startLeaveYear = DateUtilExt.getTimeYear(row.getStartTime());
                            if (periodYearList.indexOf(startLeaveYear) == -1) {
                                periodYearList.add(startLeaveYear);
                            }
                            Integer endLeaveYear = DateUtilExt.getTimeYear(row.getEndTime());
                            if (periodYearList.indexOf(endLeaveYear) == -1) {
                                periodYearList.add(endLeaveYear);
                            }
                        } catch (ParseException e) {
                            log.error(e.getMessage(), e);
                        }
                    });
                    reCalLeaveQuotaDetail(belongOrgId, userId, waEmpLeave.getEmpid(), periodYearList, waEmpLeave.getLeaveTypeId(), waLeaveType.getLeaveType(), waEmpLeave.getLeaveId());
                }
            } else {//调休
                reCalTxLeaveQuotaDetail(belongOrgId, userId, waEmpLeave.getEmpid(), waEmpLeave.getLeaveTypeId(), waEmpLeave.getLeaveId());
            }
        }
    }

    /**
     * 重新扣减假期配额（非调休）
     *
     * @param belongOrgId
     * @param userId
     * @param empid
     * @param periodYearList
     * @param leaveTypeId
     * @param leaveType
     * @param leaveId
     * @throws Exception
     */
    @Transactional
    public void reCalLeaveQuotaDetail(String belongOrgId, Long userId, Long empid, List<Integer> periodYearList, Integer leaveTypeId, Integer leaveType, Integer leaveId) throws Exception {
        if (CollectionUtils.isEmpty(periodYearList)) {
            return;
        }
        //查询员工假期配额
        List<EmpQuotaDTO> allYearQuotaList = getEmpQuotaList(leaveTypeId, leaveType, empid, "asc", periodYearList);
        if (CollectionUtils.isEmpty(allYearQuotaList)) {
            return;
        }
        //按照年份汇总员工的假期配额
        Map<Integer, List<EmpQuotaDTO>> empYearQuotaListMap = new LinkedHashMap<>();
        allYearQuotaList.forEach(row -> {
            Integer periodYear = Integer.valueOf(row.getPeriodYear());
            if (empYearQuotaListMap.containsKey(periodYear)) {
                empYearQuotaListMap.get(periodYear).add(row);
            } else {
                empYearQuotaListMap.put(periodYear, new ArrayList<>(Arrays.asList(row)));
            }
        });
        //按照年份去重新扣减员工的假期配额
        for (Integer year : periodYearList) {
            Map quotaCycleTime = getEmpQuotaCycleTime(leaveTypeId, empid, year);
            if (quotaCycleTime != null && !quotaCycleTime.isEmpty()) {
                Long startDate = DateUtil.getOnlyDate((Date) quotaCycleTime.get("startDate"));
                Long endDate = DateUtil.getOnlyDate((Date) quotaCycleTime.get("endDate"));
                List<EmpQuotaDTO> quotaList = empYearQuotaListMap.get(year);
                reDeductLeaveQuotaDetail(quotaList, startDate, endDate, leaveTypeId, leaveType, empid, belongOrgId, userId, leaveId);
            }
        }
        //重新计算留存配额
        this.updateRemainDay(userId, belongOrgId, empid, leaveTypeId, leaveType, periodYearList);
    }

    /**
     * 重新扣减调休单据
     *
     * @param belongOrgId
     * @param userId
     * @param empid
     * @param leaveTypeId
     * @param leaveId
     * @throws Exception
     */
    @Transactional
    public void reCalTxLeaveQuotaDetail(String belongOrgId, Long userId, Long empid, Integer leaveTypeId, Integer leaveId) throws Exception {
        //查询员工假期配额
        List<EmpQuotaDTO> quotaDTOList = getEmpQuotaList(leaveTypeId, 3, empid, "asc", null);
        if (CollectionUtils.isEmpty(quotaDTOList)) {
            return;
        }
        EmpQuotaDTO empQuotaDTO = quotaDTOList.get(0);
        reDeductLeaveQuotaDetail(quotaDTOList, empQuotaDTO.getStartDate(), empQuotaDTO.getLastDate(), leaveTypeId, 3, empid, belongOrgId, userId, leaveId);
    }

    /**
     * 重新扣减假期配额明细
     *
     * @param quotaList
     * @param startDate
     * @param endDate
     * @param leaveTypeId
     * @param leaveType
     * @param empid
     * @param belongOrgId
     * @param userId
     * @param leaveId
     * @throws Exception
     */
    @Transactional
    public void reDeductLeaveQuotaDetail(List<EmpQuotaDTO> quotaList, Long startDate, Long endDate, Integer leaveTypeId, Integer leaveType, Long empid,
                                         String belongOrgId, Long userId, Integer leaveId) throws Exception {
        if (CollectionUtils.isNotEmpty(quotaList)) {
            Long minDate = null;//假期配额最小生效时间
            Long maxDate = null;//假期配额最大时效时间

            List<Integer> quotaIds = new ArrayList<>();
            for (EmpQuotaDTO quotaDTO : quotaList) {
                quotaDTO.setUsedDay(0f);
                quotaDTO.setRemainUsedDay(0f);
                quotaIds.add(quotaDTO.getEmpQuotaId());

                Long rowStartDate = quotaDTO.getStartDate();
                Long rowEndDate = quotaDTO.getLastDate();

                if (minDate == null || minDate > rowStartDate) {
                    minDate = rowStartDate;
                }
                if (maxDate == null || maxDate < rowEndDate) {
                    maxDate = rowEndDate;
                }
            }

            if (leaveType == 3) {
                startDate = minDate;
                endDate = maxDate;
            }

            //初始化已使用
            WaEmpQuota quotaUpd = new WaEmpQuota();
            quotaUpd.setRemainUsedDay(0f);
            quotaUpd.setUsedDay(0f);
            WaEmpQuotaExample quotaExample = new WaEmpQuotaExample();
            quotaExample.createCriteria().andEmpQuotaIdIn(quotaIds);
            waEmpQuotaMapper.updateByExampleSelective(quotaUpd, quotaExample);

            //批量初始化本年调整已使用额度
            List<List<Integer>> lists = ListTool.split(quotaIds, 500);
            lists.forEach(list -> {
                Map parm = new HashMap();
                parm.put("curTime", DateUtil.getCurrentTime(true));
                parm.put("userId", userId);
                parm.put("belongOrgId", belongOrgId);
                parm.put("anyEmpQuotaIds", "'{" + StringUtils.join(list, ",") + "}'");
                waMapper.initQuotaAdjust(parm);
            });

            //查询休假单据
            List<WaLeaveDaytime> leaveDaytimes;
            if (leaveType == 3) {//调休
                leaveDaytimes = this.listWaEmpLeave(leaveTypeId, empid, leaveId, startDate, endDate, new Integer[]{1, 2});
            } else {
                leaveDaytimes = this.listWaEmpLeave(leaveTypeId, empid, leaveId, startDate, endDate, new Integer[]{2});
            }
            if (CollectionUtils.isNotEmpty(leaveDaytimes)) {
                deductLeaveQuota(quotaList, leaveDaytimes, empid, belongOrgId, leaveTypeId, true, "zh_CN", false);
            }
        }
    }

    /**
     * 生成配额时-重新扣减休假数据
     *
     * @param belongOrgId
     * @param userId
     * @param year
     * @param allEmpQuotaList
     * @param leaveSettingList
     * @param empInfoMap
     * @throws Exception
     */
    @Transactional
    public void reCalLeaveQuota(String belongOrgId, Long userId, Integer year, List<WaEmpQuota> allEmpQuotaList, List<WaLeaveSetting> leaveSettingList,
                                Map<Long, SysEmpInfo> empInfoMap) throws Exception {
        if (CollectionUtils.isEmpty(allEmpQuotaList)) return;
        Map<Long, List<Map>> quotaCycleTime = this.getEmpQuotaCycleTime(year, leaveSettingList, empInfoMap);
        if (MapUtils.isNotEmpty(quotaCycleTime)) {
            List<Integer> empQuotaIds = new ArrayList<>();
            allEmpQuotaList.forEach(row -> empQuotaIds.add(row.getEmpQuotaId()));

            //批量初始化本年调整已使用额度
            List<List<Integer>> lists = ListTool.split(empQuotaIds, 500);
            lists.forEach(list -> {
                Map parm = new HashMap();
                parm.put("curTime", DateUtil.getCurrentTime(true));
                parm.put("userId", userId);
                parm.put("belongOrgId", belongOrgId);
                parm.put("anyEmpQuotaIds", "'{" + StringUtils.join(list, ",") + "}'");
                waMapper.initQuotaAdjust(parm);
            });

            //查询需重新计算的请假数据
            List<Integer> leaveTypeIdList = new ArrayList<>();
            List<Long> empIdList = new ArrayList<>();
            final Long[] minDate = {null};
            final Long[] maxDate = {null};
            leaveSettingList.forEach(row -> {
                if (leaveTypeIdList.indexOf(row.getLeaveTypeId()) < 0) {
                    leaveTypeIdList.add(row.getLeaveTypeId());
                }
            });
            Map<String, Map> empLeaveCycleMap = new HashMap<>();
            //计算查询请假数据的最大范围
            quotaCycleTime.forEach((empId, leaveTypeCycle) -> {
                empIdList.add(empId);
                leaveTypeCycle.forEach(row -> {
                    Long firstDate = (Long) row.get("startDate");
                    Long lastDate = (Long) row.get("endDate");

                    if (minDate[0] == null) {
                        minDate[0] = firstDate;
                        maxDate[0] = lastDate;
                    } else {
                        minDate[0] = minDate[0].compareTo(firstDate) > 0 ? firstDate : minDate[0];
                        maxDate[0] = maxDate[0].compareTo(lastDate) < 0 ? lastDate : maxDate[0];
                    }
                    empLeaveCycleMap.put(empId + "_" + row.get("leaveTypeId"), row);
                });
            });
            Map parm = new HashMap();
            parm.put("anyEmpids", "'{" + StringUtils.join(empIdList, ",") + "}'");
            parm.put("anyLeaveTypeIds", "'{" + StringUtils.join(leaveTypeIdList, ",") + "}'");
            parm.put("firstDate", minDate[0]);
            parm.put("lastDate", maxDate[0]);
            parm.put("array", new Integer[]{2});
            List<Map> leaveDaytimeList = waLeaveDaytimeMapper.listLeaveDaytimeMap(parm);
            if (CollectionUtils.isNotEmpty(leaveDaytimeList)) {
                Map<String, List<WaLeaveDaytime>> empLeaveMap = new HashMap<>();
                leaveDaytimeList.forEach(row -> {
                    String key = row.get("empid") + "_" + row.get("leave_type_id");
                    Map cycle = empLeaveCycleMap.get(key);
                    if (MapUtils.isNotEmpty(cycle)) {
                        Long firstDate = (Long) cycle.get("startDate");
                        Long lastDate = (Long) cycle.get("endDate");
                        Long leaveDate = (Long) row.get("leave_date");
                        if (leaveDate.longValue() >= firstDate.longValue() && leaveDate.longValue() <= lastDate.longValue()) {
                            List<WaLeaveDaytime> leaveList = empLeaveMap.get(key);
                            if (leaveList == null) {
                                leaveList = new ArrayList<>();
                            }
                            WaLeaveDaytime daytime = new WaLeaveDaytime();
                            daytime.setLeaveDate(leaveDate);
                            daytime.setTimeDuration((Float) row.get("time_duration"));
                            leaveList.add(daytime);
                            empLeaveMap.put(key, leaveList);
                        }
                    }
                });

                //查询员工假期配额
                List<EmpQuotaDTO> empQuotaList = this.getEmpQuotaListByYear(leaveTypeIdList, empIdList, "asc", year);

                if (CollectionUtils.isNotEmpty(empQuotaList)) {
                    Map<String, List<EmpQuotaDTO>> leaveQuotaMap = new HashMap<>();
                    empQuotaList.forEach(row -> {
                        String key = row.getEmpid() + "_" + row.getLeaveTypeId();
                        List<EmpQuotaDTO> quotaList = leaveQuotaMap.get(key);
                        if (quotaList == null) {
                            quotaList = new ArrayList<>();
                        }
                        quotaList.add(row);
                        leaveQuotaMap.put(key, quotaList);
                    });
                    List<WaEmpQuota> quotaUpdList = new ArrayList<>();
                    List<WaEmpQuotaDetail> detailUpdList = new ArrayList<>();
                    List<Integer> empQuotaUpdIdList = new ArrayList<>();

                    leaveQuotaMap.forEach((k, v) -> {
                        String ka[] = k.split("_");
                        Long empid = Long.valueOf(ka[0]);
                        Integer leaveTypeId = Integer.valueOf(ka[1]);

                        List<EmpQuotaDTO> quotaList = v;
                        quotaList.sort(Comparator.comparing(EmpQuotaDTO::getQuotaSortNo));

                        List<WaLeaveDaytime> daytimeList = empLeaveMap.get(k);
                        if (CollectionUtils.isNotEmpty(daytimeList)) {
                            daytimeList.sort(Comparator.comparing(WaLeaveDaytime::getLeaveDate));
                            quotaList.forEach(map1 -> empQuotaUpdIdList.add(map1.getEmpQuotaId()));
                            batchDeductLeaveQuota(quotaList, daytimeList, empid, belongOrgId, leaveTypeId, quotaUpdList, detailUpdList);
                        }
                    });

                    importService.fastUpdList(WaEmpQuotaDetail.class, "empQuotaDetailId", detailUpdList);
                    importService.fastUpdList(WaEmpQuota.class, "empQuotaId", quotaUpdList);

                    List<List<Integer>> lists1 = ListTool.split(empQuotaUpdIdList, 500);
                    lists1.forEach(list1 -> {
                        //计算并且更新当年调整
                        Map adjustParams = new HashMap();
                        adjustParams.put("curDate", DateUtil.getOnlyDate());
                        adjustParams.put("curTime", DateUtil.getCurrentTime(true));
                        adjustParams.put("anyEmpQuotaIds", "'{" + StringUtils.join(list1, ",") + "}'");
                        waMapper.updateBatchInitAdjustQuota(adjustParams);
                        waMapper.updateAdjustQuotaNowKy(adjustParams);
                        waMapper.updateAdjustQuotaTotalKy(adjustParams);
                    });
                }
            }
        }
    }

    /**
     * 生成配额时，批量扣减假期配额
     *
     * @param quotaList
     * @param daytimeList
     * @param empId
     * @param belongOrgId
     * @param leaveTypeId
     * @param quotaUpdList
     * @param detailUpdList
     * @return
     */
    public String batchDeductLeaveQuota(List<EmpQuotaDTO> quotaList, List<WaLeaveDaytime> daytimeList, Long empId, String belongOrgId, Integer leaveTypeId,
                                        List<WaEmpQuota> quotaUpdList, List<WaEmpQuotaDetail> detailUpdList) {
        //查询可用配额详情列表
        Map<String, List<EmpQuotaUseDTO>> quotaDetailMap = this.listWaEmpQuotaDetail(belongOrgId, empId, quotaList, false);
        List<EmpQuotaUseDTO> remainQuotas = quotaDetailMap.get("remainQuotas");
        List<EmpQuotaUseDTO> curYearQuotas = quotaDetailMap.get("curYearQuotas");

        Map<Integer, WaEmpQuotaDetail> detailMap = new HashMap<>();
        Map<Integer, WaEmpQuota> waEmpQuotaMap = new HashMap<>();

        //先扣留存
        for (WaLeaveDaytime daytime : daytimeList) {
            Long leaveDate = daytime.getLeaveDate();
            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
            for (EmpQuotaUseDTO quotaDTO : remainQuotas) {
                Float keQuota = quotaDTO.getKeQuota();
                BigDecimal surplusQuota = new BigDecimal(keQuota);//剩余配额

                if (dayDuration.floatValue() <= 0) {
                    break;
                }
                if (surplusQuota.floatValue() <= 0) {
                    //没有配额
                    continue;
                }
                Long startDate = quotaDTO.getStartDate();
                Long lastDate = quotaDTO.getLastDate();
                if (leaveDate >= startDate && leaveDate <= lastDate) {
                    BigDecimal usedQuota = new BigDecimal(0);//使用额度
                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {
                        //额度足够
                        usedQuota = usedQuota.add(dayDuration);
                        surplusQuota = surplusQuota.subtract(dayDuration);
                        dayDuration = new BigDecimal(0);
                    } else {
                        //额度不够
                        usedQuota = usedQuota.add(surplusQuota);
                        dayDuration = dayDuration.subtract(surplusQuota);
                        surplusQuota = new BigDecimal(0);
                    }
                    quotaDTO.setKeQuota(surplusQuota.floatValue());
                    //更新已使用
                    Float oldUsedDay = quotaDTO.getUsedDay();
                    oldUsedDay = oldUsedDay == null ? 0f : oldUsedDay;
                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                    quotaDTO.setUsedDay(oldUsedDayDec.add(usedQuota).floatValue());

                    this.deductionQuota(quotaDTO, waEmpQuotaMap, detailMap);
                }
            }
            daytime.setTimeDuration(dayDuration.floatValue());
        }
        //再扣减本年、调整
        if (CollectionUtils.isNotEmpty(curYearQuotas)) {
            for (WaLeaveDaytime daytime : daytimeList) {
                Long leaveDate = daytime.getLeaveDate();
                BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长

                for (EmpQuotaUseDTO quotaDTO : curYearQuotas) {
                    Float keQuota = quotaDTO.getKeQuota();
                    BigDecimal surplusQuota = new BigDecimal(keQuota);//剩余配额

                    if (dayDuration.floatValue() <= 0) {
                        break;
                    }
                    if (surplusQuota.floatValue() <= 0) {
                        //没有配额
                        continue;
                    }
                    Long startDate = quotaDTO.getStartDate();
                    Long lastDate = quotaDTO.getLastDate();
                    if (leaveDate >= startDate && leaveDate <= lastDate) {
                        BigDecimal usedQuota = new BigDecimal(0);//使用额度
                        if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {
                            //额度足够
                            usedQuota = usedQuota.add(dayDuration);
                            surplusQuota = surplusQuota.subtract(dayDuration);
                            dayDuration = new BigDecimal(0);
                        } else {
                            //额度不够
                            usedQuota = usedQuota.add(surplusQuota);
                            dayDuration = dayDuration.subtract(surplusQuota);
                            surplusQuota = new BigDecimal(0);
                        }
                        quotaDTO.setKeQuota(surplusQuota.floatValue());
                        //更新已使用
                        Float oldUsedDay = quotaDTO.getUsedDay();
                        oldUsedDay = oldUsedDay == null ? 0f : oldUsedDay;
                        BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                        quotaDTO.setUsedDay(oldUsedDayDec.add(usedQuota).floatValue());

                        this.deductionQuota(quotaDTO, waEmpQuotaMap, detailMap);
                    }
                }

                if (dayDuration.floatValue() > 0) {
                    //额度不足，扣减成负数
                    EmpQuotaUseDTO lastCurQuota = curYearQuotas.get(curYearQuotas.size() - 1);
                    Float oldUsedDay = lastCurQuota.getUsedDay();
                    BigDecimal oldUsedDayDec = new BigDecimal(oldUsedDay);
                    lastCurQuota.setUsedDay(oldUsedDayDec.add(dayDuration).floatValue());
                    this.deductionQuota(lastCurQuota, waEmpQuotaMap, detailMap);
                }
            }
        }

        //扣减配额
        if (!detailMap.isEmpty()) {
            Iterator<Map.Entry<Integer, WaEmpQuotaDetail>> entries = detailMap.entrySet().iterator();
            while (entries.hasNext()) {
                Map.Entry<Integer, WaEmpQuotaDetail> entry = entries.next();
                WaEmpQuotaDetail waEmpQuotaDetail = entry.getValue();
                detailUpdList.add(waEmpQuotaDetail);
//                waEmpQuotaDetailMapper.updateByPrimaryKeySelective(waEmpQuotaDetail);
            }
        }
        //扣减主配额
        if (!waEmpQuotaMap.isEmpty()) {
            Iterator<Map.Entry<Integer, WaEmpQuota>> entries = waEmpQuotaMap.entrySet().iterator();
            while (entries.hasNext()) {
                Map.Entry<Integer, WaEmpQuota> entry = entries.next();
                WaEmpQuota waEmpQuota = entry.getValue();
                quotaUpdList.add(waEmpQuota);
//                waEmpQuotaMapper.updateByPrimaryKeySelective(waEmpQuota);
            }
        }

        //计算并且更新当年调整
//        this.updateEmpAdjustQuota(empId, empQuotaIdList);
        return "";
    }

    /**
     * 获取调休配额
     *
     * @param leaveTypeId
     * @param empId
     * @return
     */
    public WaEmpQuota getEmpTxQuota(Integer leaveTypeId, Long empId) {
        Map quoMap = new HashMap();
        quoMap.put("empId", empId);
        quoMap.put("leaveType", 4);//调休
        quoMap.put("nowDate", System.currentTimeMillis() / 1000);
        if (leaveTypeId != null) {
            quoMap.put("leaveTypeId", leaveTypeId);
        }
        List<WaEmpQuota> quoList4 = waEmpQuotaMapper.getEmpQuota(quoMap);
        if (CollectionUtils.isNotEmpty(quoList4)) {
            return quoList4.get(0);
        }
        return null;
    }

    /**
     * 查询员工假期配额周期时间
     *
     * @param leaveTypeId
     * @param empid
     * @param year
     * @return
     * @throws ParseException
     */
    public Map getEmpQuotaCycleTime(Integer leaveTypeId, Long empid, Integer year) throws ParseException {
        WaLeaveSettingExample leaveSettingExample = new WaLeaveSettingExample();
        leaveSettingExample.createCriteria().andLeaveTypeIdEqualTo(leaveTypeId);
        List<WaLeaveSetting> leaveSettingList = waLeaveSettingMapper.selectByExample(leaveSettingExample);
        Date endDate = null, startDate = null;
        if (CollectionUtils.isNotEmpty(leaveSettingList)) {
            //日期不相等的情况无解决方案
            for (WaLeaveSetting leaveSetting : leaveSettingList) {
                if (startDate != null && endDate != null) {
                    break;
                }
                //周期类型1:自然年2:合同年3:自定义
                //自然年
                if (leaveSetting.getQuotaPeriodType() == 1) {
                    Date endDate1 = DateUtils.parseDate(year + StringUtils.leftPad(leaveSetting.getEndDate().toString(), 2, "0"), "yyyyMMdd");
                    Date startDate1 = DateUtils.addDays(DateUtils.addYears(endDate1, -1), 1);
                    if (startDate != null) {
                        startDate = startDate1.compareTo(startDate) > 0 ? startDate1 : startDate;
                        endDate = endDate1.compareTo(endDate) < 0 ? endDate1 : endDate;
                    } else {
                        startDate = startDate1;
                        endDate = endDate1;
                    }
                } else {
                    SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
                    Calendar hireDate = Calendar.getInstance();
                    hireDate.setTimeInMillis(empInfo.getHireDate() * 1000);

                    Calendar cal = Calendar.getInstance();
                    DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
                    cal.setTimeInMillis(df.parse(df.format(cal.getTime())).getTime());
                    if (hireDate.get(Calendar.MONTH) * 100 + hireDate.get(Calendar.DATE) > cal.get(Calendar.MONTH) * 100 + cal.get(Calendar.DATE)) {
                        cal.set(year - 1, hireDate.get(Calendar.MONTH), hireDate.get(Calendar.DATE));
                    } else {
                        cal.set(year, hireDate.get(Calendar.MONTH), hireDate.get(Calendar.DATE));
                    }
                    Date startDate1 = new Date(cal.getTimeInMillis());
                    Date endDate1 = DateUtils.addDays(DateUtils.addYears(startDate1, 1), -1);
                    if (startDate != null) {
                        startDate = startDate1.compareTo(startDate) > 0 ? startDate1 : startDate;
                        endDate = endDate1.compareTo(endDate) < 0 ? endDate1 : endDate;
                    } else {
                        startDate = startDate1;
                        endDate = endDate1;
                    }
                }
            }
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("startDate", startDate);
            dataMap.put("endDate", endDate);
            return dataMap;
        }
        return null;
    }

    /**
     * 查询员工假期配额周期时间
     *
     * @param year
     * @param leaveSettingList
     * @param empInfoMap
     * @return
     * @throws Exception
     */
    public Map<Long, List<Map>> getEmpQuotaCycleTime(Integer year, List<WaLeaveSetting> leaveSettingList, Map<Long, SysEmpInfo> empInfoMap) throws Exception {
        Map<Long, List<Map>> empCycleMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(leaveSettingList) && MapUtils.isNotEmpty(empInfoMap)) {
            //同一个假期类型下的所有配额类型配额周期应是一样的，周期不相等的情况无解决方案

            Map<Integer, List<WaLeaveSetting>> leaveTypeSettingMap = new HashMap<>();
            leaveSettingList.forEach(row -> {
                List<WaLeaveSetting> settingList = leaveTypeSettingMap.get(row.getLeaveTypeId());
                if (settingList == null) {
                    settingList = new ArrayList<>();
                }
                settingList.add(row);
                leaveTypeSettingMap.put(row.getLeaveTypeId(), settingList);
            });

            empInfoMap.forEach((empId, empInfo) -> {
                List<Map> list = new ArrayList<>();

                leaveTypeSettingMap.forEach((leaveTypeId, leaveSettings) -> {
                    Date endDate = null, startDate = null;
                    for (WaLeaveSetting leaveSetting : leaveSettings) {
                        try {
                            Date endDate1, startDate1;
                            //自然年
                            if (leaveSetting.getQuotaPeriodType() == 1) {
                                endDate1 = DateUtils.parseDate(year + StringUtils.leftPad(leaveSetting.getEndDate().toString(), 2, "0"), "yyyyMMdd");
                                startDate1 = DateUtils.addDays(DateUtils.addYears(endDate1, -1), 1);
                            } else {
                                if (empInfo.getHireDate() == null) {
                                    continue;
                                }
                                Calendar hireDate = Calendar.getInstance();
                                hireDate.setTimeInMillis(empInfo.getHireDate() * 1000);

                                Calendar cal = Calendar.getInstance();
                                DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
                                cal.setTimeInMillis(df.parse(df.format(cal.getTime())).getTime());
                                if (hireDate.get(Calendar.MONTH) * 100 + hireDate.get(Calendar.DATE) > cal.get(Calendar.MONTH) * 100 + cal.get(Calendar.DATE)) {
                                    cal.set(year - 1, hireDate.get(Calendar.MONTH), hireDate.get(Calendar.DATE));
                                } else {
                                    cal.set(year, hireDate.get(Calendar.MONTH), hireDate.get(Calendar.DATE));
                                }
                                startDate1 = new Date(cal.getTimeInMillis());
                                endDate1 = DateUtils.addDays(DateUtils.addYears(startDate1, 1), -1);
                            }

                            if (startDate != null) {
                                startDate = startDate1.compareTo(startDate) > 0 ? startDate1 : startDate;
                                endDate = endDate1.compareTo(endDate) < 0 ? endDate1 : endDate;
                            } else {
                                startDate = startDate1;
                                endDate = endDate1;
                            }
                        } catch (Exception e) {
                            log.error("WaCommonService.getEmpQuotaCycleTime 配额周期获取失败 {}", e.getMessage(), e);
                        }
                    }
                    if (startDate != null) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("leaveTypeId", leaveTypeId);
                        map.put("startDate", startDate.getTime() / 1000);
                        map.put("endDate", endDate.getTime() / 1000);
                        list.add(map);
                    }
                });
                empCycleMap.put(empId, list);
            });
        }
        return empCycleMap;
    }

    /**
     * 获取员工指定时间所在的考勤周期以及考勤帐套信息
     *
     * @param empid 员工ID
     * @param time  时间
     * @return
     * @throws ParseException
     */
    public Map getWaSobInfoByEmpIdAndDate(String belongOrgId, Long empid, Long time) throws Exception {
        Map map = new HashMap();
        //查询员工所在的考勤分组
        Map groupParams = new HashMap();
        groupParams.put("empid", empid);
        List<Map> listEmpWaGroup = waMapper.listEmpWaGroup(groupParams);
        if (CollectionUtils.isNotEmpty(listEmpWaGroup)) {
            Integer waGroupId = (Integer) listEmpWaGroup.get(0).get("wa_group_id");
            //2、查询考勤分组信息
            WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waGroupId);
            if (waGroup != null) {
                //根据考勤分组和日期查询考勤帐套
                Map params = new HashMap();
                params.put("belongOrgId", belongOrgId);
                params.put("waGroupId", waGroupId);
                params.put("date", time);
                Map sobMap = waMapper.getWaSobByEmpIdAndDate(params);
                if (MapUtils.isNotEmpty(sobMap)) {
                    map.putAll(sobMap);
                }
                //获取当前时间所在的考勤周期
                Map cycleMap = calculateCycleTime(time, waGroup.getCyleStartdate());
                if (MapUtils.isNotEmpty(cycleMap)) {
                    map.putAll(cycleMap);
                }
            }
        }
        return map;
    }

    /**
     * 根据时间计算当前时间所在的考勤周期
     *
     * @param dateTime
     * @param cyleStartdate
     * @return
     * @throws Exception
     */
    public Map calculateCycleTime(Long dateTime, Integer cyleStartdate) throws Exception {
        Long preMonthBegin = DateUtilExt.getMonth(dateTime, -1, 1);//上个月开始时间
        Long preMonthEnd = DateUtilExt.getMonthEnd(preMonthBegin);//上个月的结束时间
        Long curMonthBegin = DateUtilExt.getMonth(dateTime, 0, 1);//本月开始时间
        Long curMonthEnd = DateUtilExt.getMonthEnd(curMonthBegin);//本月结束时间
        Long curCycleBegin = null;//当前时间所在的考勤周期开始时间
        Long curCycleEnd = null;//当前时间所在的考勤周期结束时间
        if (cyleStartdate > 1) {
            Long addDayMin = Long.valueOf((cyleStartdate - 1) * 86400);
            preMonthBegin += addDayMin;
            preMonthEnd += addDayMin;
            curMonthBegin += addDayMin;
            curMonthEnd += addDayMin;
        }
        //加班的时间不允许跨考勤周期，校验加班时间是否跨考勤周期
        //当前时间的考勤周期
        if (dateTime >= preMonthBegin && dateTime <= preMonthEnd) {
            curCycleBegin = preMonthBegin;
            curCycleEnd = preMonthEnd;
        } else if (dateTime >= curMonthBegin && dateTime <= curMonthEnd) {
            curCycleBegin = curMonthBegin;
            curCycleEnd = curMonthEnd;
        } else {
            return null;
        }
        Map resultMap = new HashMap();
        resultMap.put("cycleBegin", curCycleBegin);
        resultMap.put("cycleEnd", curCycleEnd);
        return resultMap;
    }

    /**
     * 根据考勤周期统计员工加班时长
     *
     * @param belongOrgId
     * @param empid
     * @param ym
     * @return
     */
    public Float getEmpOtSumTimeByWaCycle(String belongOrgId, Long empid, Integer ym, Integer compensateType) {
        //获取考勤周期
        Map waCycleParam = new HashMap();
        waCycleParam.put("belongOrgId", belongOrgId);
        waCycleParam.put("ym", ym);
        waCycleParam.put("empId", empid);
        Map waCycle = waMapper.getEmpWaGroupCycle(waCycleParam);
        if (waCycle != null) {
            Long startDate = Long.valueOf(String.valueOf(waCycle.get("startDate")));
            Long endDate = Long.valueOf(String.valueOf(waCycle.get("endDate")));
            startDate = DateUtil.getTimesampByDateStr2(DateUtil.getDateStrByTimesamp(startDate));
            endDate = DateUtil.getTimesampByDateStr2(DateUtil.getDateStrByTimesamp(endDate));
            return getEmpOtSumTimeByDateRange(belongOrgId, empid, compensateType, startDate, endDate + 86399);
        }
        return 0f;
    }

    /**
     * 根据日期范围员工加班时长
     *
     * @param belongOrgId
     * @param empid
     * @param compensateType
     * @param startDate
     * @param endDate
     * @return
     */
    public Float getEmpOtSumTimeByDateRange(String belongOrgId, Long empid, Integer compensateType, Long startDate, Long endDate) {
        //查询员工本月加班数据
        Map otParam = new HashMap();
        otParam.put("belongOrgId", belongOrgId);
        otParam.put("empid", empid);
        otParam.put("startTime", startDate);
        otParam.put("endTime", endDate);
        otParam.put("compensateType", compensateType);
        Integer time = waMapper.getEmpOtSumTime(otParam);
        if (time != null && time > 0) {
            return new BigDecimal(time).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP).floatValue();
        }
        return 0f;
    }

    public List<Map> getOtListTime(String belongOrgId, Long empId, Long startDate, Long endDate) {
        Map mParams = new HashMap();
        mParams.put("belongOrgId", belongOrgId);
        mParams.put("empId", empId);
        mParams.put("startDate", startDate);
        mParams.put("endDate", endDate);
        return null;
    }

    /**
     * 按照日期类型+补偿类型统计加班时长
     *
     * @param belongOrgId
     * @param empid
     * @param startDate
     * @param endDate
     * @return
     */
    public List<Map> getEmpOtTimeListByOtType(String belongOrgId, Long empid, Long startDate, Long endDate) {
        //查询员工本月加班数据
        Map otParam = new HashMap();
        otParam.put("belongOrgId", belongOrgId);
        otParam.put("empid", empid);
        otParam.put("startTime", startDate);
        otParam.put("endTime", endDate);
        return waMapper.getEmpOtTimeListByOtType(otParam);
    }

    /**
     * 指定日期范围汇总加班总时长
     *
     * @param belongid
     * @param empid
     * @param startDate
     * @param endDate
     * @return
     */
    public Float getEmpOtSumTime(String belongid, Long empid, Long startDate, Long endDate) {
        Float otTime = 0f;
        String isOnlyPayCash = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongid + "_ISONLY_SHOW_OT_PAYCASH");
        if (isOnlyPayCash != null && "1".equals(isOnlyPayCash)) {//CLOUD-6784 是否只显示加班付现小时数
            //加班时长=法定节假日加班（付现+调休）+ 非法定节假日（付现）
            List<Map> otList = this.getEmpOtTimeListByOtType(belongid, empid, startDate, endDate);
            if (CollectionUtils.isNotEmpty(otList)) {
                final Integer[] totalTime = {0};
                otList.forEach(row -> {
                    Integer dateType = (Integer) row.get("date_type");
                    Integer compensateType = (Integer) row.get("compensate_type");
                    if ((dateType == 3 || compensateType == 1) && row.get("time_duration") != null) {
                        totalTime[0] = totalTime[0] + Integer.valueOf(row.get("time_duration").toString());
                    }
                });
                if (totalTime[0] > 0) {
                    otTime = new BigDecimal(totalTime[0]).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP).floatValue();
                }
            }
        } else {
            otTime = this.getEmpOtSumTimeByDateRange(belongid, empid, null, startDate, endDate);
        }
        return otTime;
    }

    /**
     * 指定日期范围计算请假总时长&请假详情信息
     *
     * @param belongOrgId
     * @param empid
     * @param startDate
     * @param endDate
     * @param convertToDay true 返回值单位为天 null 或者 false 单位按照假期类型返回
     * @return
     */
    public Map<String, Object> getEmpLeaveTotalDayInfo(String belongOrgId, Long empid, Long startDate, Long endDate, Boolean convertToDay) {
        Map params = new HashMap();
        params.put("belongOrgId", belongOrgId);
        params.put("empid", empid);
        params.put("startTime", startDate);
        params.put("endTime", endDate);

        List<Map> list = waMapper.getEmpLeaveTimeListByDate(params);
        if (CollectionUtils.isNotEmpty(list)) {
            SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
            int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
            //查询公司全部班次
            Map<Integer, WaShiftDef> shiftMap = this.getCorpAllShiftDef(belongOrgId);
            //查询员工排班信息
            Map<Long, WaWorktimeDetail> pbMap = this.getEmpWaWorktimeDetail(belongOrgId, empid, tmType, startDate, endDate, empInfo.getWorktimeType(), true);

            Map<Integer, Float> empLeaveTime = new HashMap<>();
            Map<Integer, Float> empBusinessTravelTime = new HashMap<>();//出差
            BigDecimal leaveTotalTime = new BigDecimal(0);//休假总天数
            BigDecimal businessTravelTotalTime = new BigDecimal(0);//出差总天数
            BigDecimal totalTime = new BigDecimal(0);//休假+出差总天数

            for (Map map : list) {
                Integer acctTimeType = (Integer) map.get("acct_time_type");
                Long leaveDate = (Long) map.get("leave_date");
                Integer leaveType = (Integer) map.get("leave_type");
                Integer leaveTypeId = (Integer) map.get("leave_type_id");

                BigDecimal timeDuration = new BigDecimal(map.get("time_duration").toString());
                BigDecimal timeDay = new BigDecimal(map.get("time_duration").toString());

                if (acctTimeType == 2) {//小时假
                    //根据班次时长去换算
                    WaWorktimeDetail detail = pbMap.get(leaveDate);
                    WaShiftDef startDateShift = shiftMap.get(detail.getShiftDefId());
                    Integer workTimeMin = startDateShift.getWorkTotalTime();
                    BigDecimal convertTime = timeDuration.divide(new BigDecimal(workTimeMin), 2, BigDecimal.ROUND_HALF_UP);
                    timeDay = convertTime;
                    if (BooleanUtils.isTrue(convertToDay)) {
                        timeDuration = convertTime;
                    }
                }

                //按照假期类型统计请假时长
                if (leaveType == 11) {//出差单据
                    if (empBusinessTravelTime.get(leaveTypeId) != null) {
                        empBusinessTravelTime.put(leaveTypeId, timeDuration.add(new BigDecimal(empBusinessTravelTime.get(leaveTypeId))).floatValue());
                    } else {
                        empBusinessTravelTime.put(leaveTypeId, timeDuration.floatValue());
                    }
                    //出差总天数
                    businessTravelTotalTime = businessTravelTotalTime.add(timeDay);
                } else {
                    if (empLeaveTime.get(leaveTypeId) != null) {
                        empLeaveTime.put(leaveTypeId, timeDuration.add(new BigDecimal(empLeaveTime.get(leaveTypeId))).floatValue());
                    } else {
                        empLeaveTime.put(leaveTypeId, timeDuration.floatValue());
                    }
                    //休假总天数
                    leaveTotalTime = leaveTotalTime.add(timeDay);
                }
                totalTime = totalTime.add(timeDay);
            }
            Map<String, Object> timeMap = new HashMap<>();
            timeMap.put("totalTime", totalTime.floatValue());
            timeMap.put("leaveTotalTime", leaveTotalTime.floatValue());
            timeMap.put("businessTravelTotalTime", businessTravelTotalTime.floatValue());
            timeMap.put("leaveData", empLeaveTime);
            timeMap.put("businessTravelData", empBusinessTravelTime);
            return timeMap;
        }
        return new HashMap();
    }

    /**
     * 指定日期范围计算请假总时长 （单位 小时）
     *
     * @param belongOrgId
     * @param empid
     * @param startDate
     * @param endDate
     * @return
     */
    public Float getEmpLeaveTotalHour(String belongOrgId, Long empid, Long startDate, Long endDate) {
        BigDecimal totalTime = new BigDecimal(0);

        Map params = new HashMap();
        params.put("belongOrgId", belongOrgId);
        params.put("empid", empid);
        params.put("startTime", startDate);
        params.put("endTime", endDate);

        List<Map> list = waMapper.getEmpLeaveTimeListByDate(params);
        if (CollectionUtils.isNotEmpty(list)) {
            SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
            int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
            //查询公司全部班次
            Map<Integer, WaShiftDef> shiftMap = this.getCorpAllShiftDef(belongOrgId);
            //查询员工排班信息
            Map<Long, WaWorktimeDetail> pbMap = this.getEmpWaWorktimeDetail(belongOrgId, empid, tmType, startDate, endDate, empInfo.getWorktimeType(), true);

            for (Map map : list) {
                Integer acctTimeType = (Integer) map.get("acct_time_type");
                Long leaveDate = (Long) map.get("leave_date");

                BigDecimal timeDuration = new BigDecimal(map.get("time_duration").toString());

                if (acctTimeType == 1) {//天
                    //根据班次时长去换算
                    WaWorktimeDetail detail = pbMap.get(leaveDate);
                    WaShiftDef startDateShift = shiftMap.get(detail.getShiftDefId());
                    Integer workTimeMin = startDateShift.getWorkTotalTime();
                    timeDuration = timeDuration.multiply(new BigDecimal(workTimeMin));
                }
                totalTime = totalTime.add(timeDuration);
            }
        }
        totalTime = totalTime.divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP);
        return totalTime.floatValue();
    }


    public List<Map> getEmpLeaveDateDetail(String belongOrgId, Long empId, Long startDate, Long endDate) {
        Map params = new HashMap();
        params.put("belongOrgId", belongOrgId);
        params.put("empid", empId);
        params.put("startTime", startDate);
        params.put("endTime", endDate);
        List<Map> list = waMapper.getEmpLeaveTimeListByDate(params);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Map> mResult = new ArrayList<>();
            SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
            int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
            //查询公司全部班次
            Map<Integer, WaShiftDef> shiftMap = this.getCorpAllShiftDef(belongOrgId);
            //查询员工排班信息
            Map<Long, WaWorktimeDetail> pbMap = this.getEmpWaWorktimeDetail(belongOrgId, empId, tmType, startDate, endDate, empInfo.getWorktimeType(), true);
            for (Map map : list) {
                Integer acctTimeType = (Integer) map.get("acct_time_type");
                Long leaveDate = (Long) map.get("leave_date");
                BigDecimal timeDuration = new BigDecimal(map.get("time_duration").toString());
                if (acctTimeType == 1) {//天
                    //根据班次时长去换算
                    WaWorktimeDetail detail = pbMap.get(leaveDate);
                    WaShiftDef startDateShift = shiftMap.get(detail.getShiftDefId());
                    Integer workTimeMin = startDateShift.getWorkTotalTime();
                    timeDuration = timeDuration.multiply(new BigDecimal(workTimeMin));
                }
                Map mItemMap = new HashMap();
                mItemMap.put("date", leaveDate);
                mItemMap.put("time_duration", timeDuration);
                mResult.add(mItemMap);
            }
            return mResult;
        }
        return null;
    }

    /**
     * 请假-查询班次总时长
     *
     * @param shiftDef
     * @return
     */
    public Integer getShiftWorkTotalTimeForLeave(WaShiftDef shiftDef) {
        if (shiftDef == null) return 0;
        Integer workTotalTime = shiftDef.getWorkTotalTime();
        if (BooleanUtils.isTrue(shiftDef.getIsSpecial()) && shiftDef.getSpecialWorkTime() != null) {
            workTotalTime = shiftDef.getSpecialWorkTime();
        }
        // 当是休息日、法定节假日的连续请假时，工作时长目前固定是8小时
        if (workTotalTime == null || workTotalTime == 0) {
            workTotalTime = 480;
        }
        return workTotalTime;
    }

    /**
     * 请假-查询班次总时长
     *
     * @param shiftDef
     * @return
     */
    public Integer getShiftWorkTotalTimeForLeave(EmpShiftInfo shiftDef) {
        if (shiftDef == null) return 0;
        Integer workTotalTime = shiftDef.getWorkTotalTime();
        if (BooleanUtils.isTrue(shiftDef.getIsSpecial()) && shiftDef.getSpecialWorkTime() != null) {
            workTotalTime = shiftDef.getSpecialWorkTime();
        }
        // 当是休息日、法定节假日的连续请假时，工作时长目前固定是8小时
        if (workTotalTime == null || workTotalTime == 0) {
            workTotalTime = 480;
        }
        return workTotalTime;
    }

    /**
     * 按照日期为key返回员工请假时长-单位为分钟
     *
     * @param empid
     * @param belongid
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<Long, Float> getLeaveTimeMap(Long empid, String belongid, Long startDate, Long endDate) {
        Map<Long, Float> leaveMap = new HashMap<>();
        //查询当前审批通过、撤销中的请假时长(单位按照小时汇总，单位为天的假，根据班次时长去折算)
        Map<String, Object> params = new HashMap<>();
        params.put("empid", empid);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("belongid", belongid);
        params.put("isCalWorktime", true);
        List<Map> leaveList = waMapper.getLeaveTimeListGroupByDateAndUnit(params);
        if (CollectionUtils.isNotEmpty(leaveList)) {
            Map<Long, WaShiftDef> shiftDefMap = getEmpWorkShift(belongid, empid, null, startDate, endDate);
            if (MapUtils.isNotEmpty(shiftDefMap)) {
                leaveList.forEach(row -> {
                    Long leaveDate = (Long) row.get("leave_date");
                    WaShiftDef shiftDef = shiftDefMap.get(leaveDate);
                    if (shiftDef != null) {
                        Integer workTotalTime = getShiftWorkTotalTimeForLeave(shiftDef);
                        Integer time_unit = (Integer) row.get("time_unit");
                        BigDecimal time_duration = new BigDecimal((Float) row.get("time_duration"));

                        if (time_unit == 1) {//天
                            time_duration = time_duration.multiply(new BigDecimal(workTotalTime));
                        }

                        Float totalLeaveTime = leaveMap.get(leaveDate);
                        if (totalLeaveTime == null) {
                            leaveMap.put(leaveDate, time_duration.floatValue());
                        } else {
                            leaveMap.put(leaveDate, new BigDecimal(totalLeaveTime).add(time_duration).floatValue());
                        }
                    } else {
                        log.error(leaveDate + "未排班");
                    }
                });
            } else {
                log.error(startDate + "~" + endDate + "未排班");
            }
        }
        return leaveMap;
    }

    /**
     * 按照empid为key返回员工请假时长-单位为分钟
     *
     * @param empids
     * @param belongid
     * @param startDate
     * @param endDate
     * @param isCalWorktime
     * @return
     */
    public Map<Long, Float> getEmpLeaveTimeMap(List<Long> empids, String belongid, Long startDate, Long endDate, Boolean isCalWorktime, Integer convertType) {
        Map<Long, Float> leaveMap = new HashMap<>();
        if (convertType == 1) {
            //查询当前审批通过、撤销中的请假时长(单位按照小时汇总，单位为天的假，根据班次时长去折算)
            Map params = new HashMap();
            params.put("empids", empids);
            params.put("startDate", startDate);
            params.put("endDate", endDate);
            params.put("belongid", belongid);
            params.put("isCalWorktime", isCalWorktime);
            List<Map> leaveList = waMapper.getEmpLeaveTimeListGroupByDateAndUnit(params);
            if (CollectionUtils.isNotEmpty(leaveList)) {
                Map<String, EmpShiftInfo> empShiftInfo = getEmpShiftInfoListMaps(belongid, empids, startDate, endDate);
                if (MapUtils.isNotEmpty(empShiftInfo)) {
                    leaveList.forEach(row -> {
                        Long empid = (Long) row.get("empid");
                        Long leaveDate = (Long) row.get("leave_date");
                        EmpShiftInfo shiftDef = empShiftInfo.get(empid + "_" + leaveDate);
                        if (shiftDef != null) {
                            Integer workTotalTime = getShiftWorkTotalTimeForLeave(shiftDef);
                            Integer time_unit = (Integer) row.get("time_unit");
                            BigDecimal time_duration = new BigDecimal((Float) row.get("time_duration"));

                            if (time_unit == 1) {//天
                                time_duration = time_duration.multiply(new BigDecimal(workTotalTime));
                            }

                            Float totalLeaveTime = leaveMap.get(empid);
                            if (totalLeaveTime == null) {
                                leaveMap.put(empid, time_duration.floatValue());
                            } else {
                                leaveMap.put(empid, new BigDecimal(totalLeaveTime).add(time_duration).floatValue());
                            }
                        } else {
                            log.error(leaveDate + "未排班");
                        }
                    });
                } else {
                    log.error(startDate + "~" + endDate + "未排班");
                }
            }
        } else {
            //计算不计工时的请假时长
            Map params1 = new HashMap();
            params1.put("empids", empids);
            params1.put("startDate", startDate);
            params1.put("endDate", endDate);
            params1.put("isCalWorktime", isCalWorktime);

            List<Map> leaveHourList = waMapper.getLeaveTimeGroupByTimeUnit(params1);
            if (CollectionUtils.isNotEmpty(leaveHourList)) {
                leaveHourList.forEach(leave -> {
                    BigDecimal f = new BigDecimal(leaveMap.get(leave.get("empid")));
                    if (f == null) {
                        f = new BigDecimal(0);
                    }
                    Float time_duration = Float.valueOf(leave.get("time_duration").toString());
                    Integer unit = (Integer) leave.get("time_unit");
                    Long empid = (Long) leave.get("empid");
                    if (unit == 1) {
                        time_duration = time_duration * 8 * 60;
                    }
                    f = f.add(new BigDecimal(time_duration));
                    leaveMap.put(empid, f.floatValue());
                });
            }
        }
        return leaveMap;
    }

    /**
     * 按照empid+belong_date为key返回员工请假时长-单位为分钟
     *
     * @param empids
     * @param belongid
     * @param startDate
     * @param endDate
     * @param isCalWorktime
     * @return
     */
    public Map<String, Float> getEmpLeaveTimeMapList(List<Long> empids, String belongid, Long startDate, Long endDate, Boolean isCalWorktime) {
        Map<String, Float> leaveMap = new HashMap<>();
        //查询当前审批通过、撤销中的请假时长(单位按照小时汇总，单位为天的假，根据班次时长去折算)
        Map params = new HashMap();
        params.put("empids", empids);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("belongid", belongid);
        params.put("isCalWorktime", isCalWorktime);
        List<Map> leaveList = waMapper.getEmpLeaveTimeListGroupByDateAndUnit(params);
        if (CollectionUtils.isNotEmpty(leaveList)) {
            Map<String, EmpShiftInfo> empShiftInfo = getEmpShiftInfoListMaps(belongid, empids, startDate, endDate);
            if (MapUtils.isNotEmpty(empShiftInfo)) {
                leaveList.forEach(row -> {
                    Long empid = (Long) row.get("empid");
                    Long leaveDate = (Long) row.get("leave_date");
                    EmpShiftInfo shiftDef = empShiftInfo.get(empid + "_" + leaveDate);
                    if (shiftDef != null) {
                        Integer workTotalTime = getShiftWorkTotalTimeForLeave(shiftDef);
                        Integer time_unit = (Integer) row.get("time_unit");
                        BigDecimal time_duration = new BigDecimal((Float) row.get("time_duration"));

                        if (time_unit == 1) {//天
                            time_duration = time_duration.multiply(new BigDecimal(workTotalTime));
                        }

                        Float totalLeaveTime = leaveMap.get(empid);
                        if (totalLeaveTime == null) {
                            leaveMap.put(empid + "_" + leaveDate, time_duration.floatValue());
                        } else {
                            leaveMap.put(empid + "_" + leaveDate, new BigDecimal(totalLeaveTime).add(time_duration).floatValue());
                        }
                    } else {
                        log.error(leaveDate + "未排班");
                    }
                });
            } else {
                log.error(startDate + "~" + endDate + "未排班");
            }
        }
        return leaveMap;
    }

    public Float getLeaveTimeByDate(Long empid, String belongid, Long leaveDate, Integer shiftId) {
        //查询当前审批通过、撤销中的请假时长(单位按照小时汇总，单位为天的假，根据班次时长去折算)
        Map params = new HashMap();
        params.put("empid", empid);
        params.put("startDate", leaveDate);
        params.put("endDate", leaveDate);
        params.put("belongid", belongid);
        List<Map> leaveList = waMapper.getLeaveTimeListGroupByDateAndUnit(params);
        if (CollectionUtils.isNotEmpty(leaveList)) {
            WaShiftDef shiftDef = waShiftDefMapper.selectByPrimaryKey(shiftId);
            if (shiftDef != null) {
                Integer workTotalTime = getShiftWorkTotalTimeForLeave(shiftDef);
                BigDecimal totalTime = new BigDecimal(0);
                for (Map map : leaveList) {
                    Integer time_unit = (Integer) map.get("time_unit");
                    BigDecimal time_duration = new BigDecimal((Float) map.get("time_duration"));

                    if (time_unit == 1) {//天
                        time_duration = time_duration.multiply(new BigDecimal(workTotalTime));
                    }
                    totalTime = totalTime.add(time_duration);
                }
                return totalTime.floatValue();
            } else {
                log.error(leaveDate + "未排班");
            }
        }
        return 0f;
    }

    /**
     * 查询员工排班工时
     *
     * @param start
     * @param end
     * @param empids
     * @return
     */
    public Map<Long, Integer> getEmpShiftWorkTime(Integer start, Integer end, List<Long> empids) {
        Map params = new HashMap();
        params.put("start", start);
        params.put("end", end);
        params.put("empids", empids);
        Map<Long, Integer> workmap = new HashMap<>();
        //查询员工排班总时长
        List<Map> worklist = waStoreTimeDetailMapper.getEmpWorkTotalHour(params);
        if (CollectionUtils.isNotEmpty(worklist)) {
            worklist.forEach(map -> {
                Long key = (Long) map.get("empid");
                Integer totalHour = Integer.valueOf(map.get("totalHour").toString());
                workmap.put(key, totalHour);
            });
        }
        return workmap;
    }

    /**
     * 查询员工工作日历每天的日期类型
     *
     * @param belongid
     * @param empid
     * @param tmType
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<Long, Integer> listEmpWorkCalendarDateType(String belongid, Long empid, Integer tmType, Long startDate, Long endDate) {
        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(belongid)) {
            Map<Integer, WaShiftDef> corpShiftMap = this.getCorpAllShiftDef(belongid);
            List<WorkTimeCalendar> calendarList = remoteSmartWorkTimeService.getEmployeeShiftList(belongid, new ArrayList<>(Arrays.asList(new Long[]{empid})), startDate, endDate);
            if (CollectionUtils.isNotEmpty(calendarList)) {
                Map<Long, Integer> dateTypeMap = new HashMap<>();
                calendarList.forEach(ec -> {
                    Long workDate = DateUtil.convertStringToDateTime(String.valueOf(ec.getDate()), "yyyyMMdd", true);
                    if (corpShiftMap.get(ec.getId()) != null) {
                        WaWorktimeDetail detail = new WaWorktimeDetail();
                        BeanUtils.copyProperties(corpShiftMap.get(ec.getId()), detail);
                        if (null == detail.getWorkDate()) {
                            detail.setWorkDate(workDate);
                        }
                        dateTypeMap.put(workDate, detail.getDateType());
                    }
                });
                return dateTypeMap;
            }
        } else {
            return this.listEmpWorkDateType(empid, tmType, startDate, endDate);
        }
        return new HashMap<>();
    }

    /**
     * 查询员工工作日历每天的日期类型
     *
     * @param empid
     * @param tmType
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<Long, Integer> listEmpWorkDateType(Long empid, Integer tmType, Long startDate, Long endDate) {
        Map params = new HashMap() {{
            put("empId", empid);
            put("startDate", startDate);
            put("endDate", endDate);
        }};
        List<WaWorktimeDetail> worktimeDetails;
        if (tmType == 1) {
            worktimeDetails = waWorktimeDetailMapper.listEmpWorkDateType(params);
        } else {
            worktimeDetails = waWorktimeDetailMapper.listStoreEmpWorkDateType(params);
        }
        if (CollectionUtils.isNotEmpty(worktimeDetails)) {
            Map<Long, Integer> dateTypeMap = new HashMap<>();
            worktimeDetails.forEach(row -> dateTypeMap.put(row.getWorkDate(), row.getDateType()));
            return dateTypeMap;
        }
        return new HashMap<>();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEmpCompensatoryQuotaApprovalResult(Long userId, WaEmpLeave waEmpLeave) {
        // 后期如果存在并发问题，新增分布式自旋锁
        WaLeaveDaytimeExample ex = new WaLeaveDaytimeExample();
        ex.createCriteria().andLeaveIdEqualTo(waEmpLeave.getLeaveId());
        List<WaLeaveDaytime> waLeaveDaytimes = waLeaveDaytimeMapper.selectByExample(ex);
        if (CollectionUtils.isNotEmpty(waLeaveDaytimes)) {
            userId = userId == null ? 0 : userId;
            Long current = DateUtil.getCurrentTime(false);
            List<Long> leaveDaytimeIdList = waLeaveDaytimes.stream().map(l -> Long.valueOf(l.getLeaveDaytimeId())).collect(Collectors.toList());
            QueryWrapper<WaCompensatoryQuotaUse> qw = new QueryWrapper<>();
            qw.in("leave_daytime_id", leaveDaytimeIdList);
            WaCompensatoryQuotaUse waCompensatoryQuotaUse = new WaCompensatoryQuotaUse();
            waCompensatoryQuotaUse.setApprovalStatus(Integer.valueOf(waEmpLeave.getStatus()));
            waCompensatoryQuotaUse.setUpdateBy(Long.valueOf(userId.toString()));
            waCompensatoryQuotaUse.setUpdateTime(current);
            waCompensatoryQuotaUseMapper.update(waCompensatoryQuotaUse, qw);
            waEmpCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(userId, System.currentTimeMillis(), leaveDaytimeIdList, Integer.valueOf(waEmpLeave.getStatus()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateWaLeaveQuotaUse(Long userId, WaEmpLeave waEmpLeave) {
        WaLeaveDaytimeExample ex = new WaLeaveDaytimeExample();
        ex.createCriteria().andLeaveIdEqualTo(waEmpLeave.getLeaveId());
        List<WaLeaveDaytime> waLeaveDaytimes = waLeaveDaytimeMapper.selectByExample(ex);
        if (CollectionUtils.isNotEmpty(waLeaveDaytimes)) {
            userId = userId == null ? 0 : userId;
            Long current = DateUtil.getCurrentTime(false);

            List<Integer> leaveDaytimeIdList = waLeaveDaytimes.stream().map(WaLeaveDaytime::getLeaveDaytimeId).collect(Collectors.toList());

            WaLeaveQuotaUseExample useExample = new WaLeaveQuotaUseExample();
            useExample.createCriteria().andLeaveDaytimeIdIn(leaveDaytimeIdList);
            WaLeaveQuotaUse quotaUse = new WaLeaveQuotaUse();
            quotaUse.setApprovalStatus(Integer.valueOf(waEmpLeave.getStatus()));
            quotaUse.setUpdateBy(Long.valueOf(userId));
            quotaUse.setUpdateTime(current);
            waLeaveQuotaUseMapper.updateByExampleSelective(quotaUse, useExample);

            waLeaveQuotaUseMapper.updateWaEmpQuota(userId, System.currentTimeMillis(), leaveDaytimeIdList, Integer.valueOf(waEmpLeave.getStatus()));
        }
    }

    /**
     * 计算加班休息时长
     *
     * @param tenantId
     * @param empShift
     * @param otStartTime
     * @param otEndTime
     * @return
     */
    public Long calOtRestTotalTime(String tenantId, EmpShiftInfo empShift, Long otStartTime, Long otEndTime) {
        long restTotalTime = 0L;
        if (null == empShift) {
            return restTotalTime;
        }
        // 检查法定假日加班是否要扣减休息时间
        boolean isHoliday = DateTypeEnum.DATE_TYP_3.getIndex().equals(empShift.getDateType())
                || DateTypeEnum.DATE_TYP_5.getIndex().equals(empShift.getDateType());
        if (isHoliday) {
            String ignoreHolidaykey = RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + tenantId + RedisKeyDefine.WA_HOLIDAY_IGNORE;
            String isIgnore = CDCacheUtil.getValue(ignoreHolidaykey);
            if (isIgnore != null && !"0".equals(isIgnore)) {
                return restTotalTime;
            }
        }
        for (EmpShiftInfo shift : empShift.doGetShiftDefList()) {
            if (CollectionUtils.isEmpty(shift.getOvertimeRestPeriods())) {
                continue;
            }
            List<MultiOvertimeDto> multiOvertimeList = shift.doGetMultiOvertimeList();
            for (ShiftRestPeriods periodsDto : shift.getOvertimeRestPeriods()) {
                long restStartTime = shift.getWorkDate() + periodsDto.doGetRealOvertimeRestStartTime(multiOvertimeList) * 60L;
                long restEndTime = shift.getWorkDate() + periodsDto.doGetRealOvertimeRestEndTime(multiOvertimeList) * 60L;
                if (restStartTime >= otEndTime || restEndTime <= otStartTime) {
                    continue;
                }
                restTotalTime += Math.min(restEndTime, otEndTime) - Math.max(restStartTime, otStartTime);
            }
        }
        return restTotalTime;
    }

    /**
     * 查询员工所在的考勤分组
     *
     * @param empId
     * @return
     */
    public WaGroup getEmpBelongWaGroup(Long empId) {
        Map<String, Object> groupParams = new HashMap<>();
        groupParams.put("empid", empId);
        List<Map> listEmpWaGroup = waMapper.listEmpWaGroup(groupParams);
        if (CollectionUtils.isEmpty(listEmpWaGroup)) {
            return null;
        }
        Integer empWaGroupId = (Integer) listEmpWaGroup.get(0).get("wa_group_id");
        return waGroupMapper.selectByPrimaryKey(empWaGroupId);
    }

    public Map<Integer, WaLeaveType> getCorpAllLeaveTypeMap(String belongOrgId) {
        WaLeaveTypeExample example = new WaLeaveTypeExample();
        example.createCriteria().andBelongOrgidEqualTo(belongOrgId);
        List<WaLeaveType> leaveTypeList = waLeaveTypeMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(leaveTypeList)) {
            return leaveTypeList.stream().collect(Collectors.toMap(WaLeaveType::getLeaveTypeId, Function.identity()));
        }
        return new HashMap<>();
    }

    public WaEmpLeave getWaEmpLeave(Integer leaveId) {
        return waEmpLeaveMapper.selectByPrimaryKey(leaveId);
    }
}
