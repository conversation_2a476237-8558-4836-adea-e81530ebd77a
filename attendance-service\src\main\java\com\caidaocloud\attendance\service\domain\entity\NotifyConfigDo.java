package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.INotifyConfigRepository;
import com.caidaocloud.dto.PageResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Data
@Service
public class NotifyConfigDo {

    private Long notifyConfigId;

    private String tenantId;

    private Integer clockNotifySwitch;

    private Integer workTime;

    private String workNotifyContent;

    private Integer offWorkTime;

    private String offWorkNotifyContent;

    /**
     * 日报开关，0 或 null 关闭状态，1 开启状态
     */
    private Integer dailyNotifySwitch;

    private Integer dailyNotifyRule;

    /**
     * 数据的单位是分钟
     */
    private Integer dailyNotifyTime;

    /**
     * 周报开关，0 或 null 关闭状态，1 开启状态
     */
    private Integer weeklyNotifySwitch;

    /**
     * 销假提醒开关 0 关闭状态，1 开启状态
     */
    private Integer leaveCancelSwitch;

    /**
     * 销假提醒内容
     */
    private String leaveCancelContent;

    /**
     * 销假提醒时间  休假结束后 xx天提醒
     */
    private Integer leaveCancelTime;

    /**
     * 销假提醒频率 每隔xx天提醒一次
     */
    private Integer leaveCancelFrequency;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    private Integer dailyNotifyAbnormal;
    private Integer weeklyNotifyFrequency;
    private Integer weeklyNotifyTime;
    private Integer weeklyNotifyType;

    private Integer abnormalSwitch;
    private Integer abnormalType;
    private String abnormalTime;
    private String abnormalMsg;

    private Integer attendanceDetailSwitch;
    private String attendanceDetailTime;

    @Resource
    private INotifyConfigRepository notifyConfigRepository;

    public NotifyConfigDo getNotifyConfigById(String tenantId) {
        return notifyConfigRepository.selectById(tenantId);
    }

    public int save(NotifyConfigDo configDo) {
        return notifyConfigRepository.save(configDo);
    }

    public int update(NotifyConfigDo configDo, String tenantId) {
        return notifyConfigRepository.update(configDo, tenantId);
    }

    public PageResult<NotifyConfigDo> getNotifyPageList(int pageNo, int pageSize){
        return  notifyConfigRepository.getPageList(pageNo, pageSize);
    }
}
