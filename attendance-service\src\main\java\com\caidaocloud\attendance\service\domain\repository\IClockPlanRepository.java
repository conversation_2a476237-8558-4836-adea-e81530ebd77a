package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockPlanPo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo;

import java.util.List;

public interface IClockPlanRepository {

    int save(WaClockPlanPo planPo);

    int update(WaClockPlanPo planPo);

    WaClockPlanPo selectById(Long id);

    int deleteById(Long id);

    AttendancePageResult<WaClockPlanPo> getClockPlanPageList(AttendanceBasePage basePage, Long corpId, String belongOrgId, String keywords);

    List<WaClockPlanPo> getPlanByParams(Long corpId, String belongOrgId, Long planId, String planName);

    List<WaClockPlanPo> getPlanRelEmployeesByEmpIds(Long id, Long corpId, String belongOrgId, List<Long> empIds);

    List<WaClockPlanPo> getPlanListBySiteId(Long corpId, String belongOrgId, Long siteId);

    WaPlanEmpRelPo getMyWaClockPlan(Long corpId, String belongOrgId, Long empId, long currentTime);

    WaClockPlanPo getWaClockPlanPo(Long corpId, String belongOrgId, Long planId);
}
