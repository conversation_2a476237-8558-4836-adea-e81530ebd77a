package com.caidao1.ioc.service;

import com.alibaba.fastjson.JSONObject;
import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.utils.BeanUtil;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.StringUtil;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.dto.ImportClockAnalyseDto;
import com.caidao1.ioc.dto.ImportClockDataDto;
import com.caidao1.ioc.dto.SaveItemDto;
import com.caidao1.ioc.dto.UpdRowDto;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.wa.mybatis.mapper.WaRegisterRecordMapper;
import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidao1.wa.service.RegisterAnalyzeService;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.MobileEnum;
import com.caidaocloud.attendance.core.wa.dto.WaAnalyzDTO;
import com.caidaocloud.attendance.core.wa.dto.WaAnalyzInfo;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.IClockSignService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 员工考勤记录（打卡）导入/接入 后置处理器
 */
@Slf4j
@Service
public class ImportClockTriggerService implements ScriptBindable {
    @Autowired
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Autowired
    private RegisterAnalyzeService registerAnalyzeService;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private IClockSignService clockSignService;

    /**
     * 考勤打卡记录导入/接入后-进行打卡分析-新版V2版本（支持多个班、多段班）
     *
     * @param belongId
     * @param addList
     * @param updList
     */
    @Transactional(rollbackFor = Exception.class)
    public void analyseRegisterRecordV2(String belongId,
                                        Map<String, List<UpdRowDto>> addList,
                                        Map<String, List<UpdRowDto>> updList) {
        log.info("Import.ClockV2 Start belongId={}, time={}", belongId, System.currentTimeMillis());
        // 本次导入/接入的数据
        List<UpdRowDto> currentImportList = getImportDataListForReg(addList, updList);
        ImportClockDataDto importClockDataDto = getImportClockData(currentImportList);
        if (null == importClockDataDto) {
            log.info("Import.ClockV2 Fail ImportData Empty");
            return;
        }
        Map<Long, List<Long>> regDateRelEmpIdMap = importClockDataDto.getRegDateRelEmpIdMap();

        // 排序
        List<Map.Entry<Long, List<Long>>> entryList = new ArrayList<>(regDateRelEmpIdMap.entrySet());
        entryList.sort(Map.Entry.comparingByKey());
        Map<Long, List<Long>> sortedRegDateRelEmpIdMap = new LinkedHashMap<>();
        for (Map.Entry<Long, List<Long>> entry : entryList) {
            sortedRegDateRelEmpIdMap.put(entry.getKey(), entry.getValue());
        }

        sortedRegDateRelEmpIdMap.forEach((date, empIds) -> {
            log.info("Import.ClockV2.Analyse Start date={}, empIds={}, time={}", date, empIds, System.currentTimeMillis());
            clockSignService.analyseRegisterRecord(belongId, empIds, date, null);
            log.info("Import.ClockV2.Analyse End date={}, empIds={}, time={}", date, empIds.size(), System.currentTimeMillis());
        });
        log.info("Import.ClockV2 End belongId={}, time={}", belongId, System.currentTimeMillis());
    }

    /**
     * 考勤打卡记录导入/接入后-进行打卡分析（老版：仅支持一个班一段班）
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param localErr         是否分析地点异常 (如果分析的打卡数据有地点异常则保留地点异常，暂时不增加地点异常分析逻辑，因为签到接入时，不会导入打卡地点)
     * @param deviceErr        是否分析设备异常（如果分析的打卡数据有设备异常则保留设备异常，暂时不增加设备异常分析逻辑，因为签到接入时，不会导入设备号）
     * @param analyzeType      数据分析类型 1 只分析导入的日期 2 分析所有日期
     * @param timeCheckType    时间异常校验类型 1 精确匹配 2 模糊匹配
     * @param includeBdkRecord 是否分析补打卡数据
     * @param kyAnalyzeType    跨夜签退分析类型 1:当天如果存在签退卡，就不去第二天查找符合条件的签退卡 2 当天如果存在签退卡，也会去第二天查找符合条件的签退卡，如果找到了，则将第二天的卡作为前一天的签退卡
     * @param filterType       抓取跨夜签退过滤类型 1 返回最早的一条 2 返回最晚的一条
     * @throws Exception
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void updateRegisterRecordDetail(String belongId,
                                           Map<String, List<UpdRowDto>> addList,
                                           Map<String, List<UpdRowDto>> updList,
                                           Boolean localErr,
                                           Boolean deviceErr,
                                           Integer analyzeType,
                                           Integer timeCheckType,
                                           Boolean includeBdkRecord,
                                           Integer kyAnalyzeType,
                                           Integer filterType) throws Exception {
        log.info("Import.Clock Start belongId={}, time={}", belongId, System.currentTimeMillis());
        analyzeType = 2;// 默认分析所有的日期（目前没有客户使用“只分析导入的日期”的逻辑，此逻辑是从1.0继承过来的，直接默认2）

        // 本次导入/接入的数据
        List<UpdRowDto> currentImportList = getImportDataListForReg(addList, updList);
        ImportClockDataDto importClockDataDto = getImportClockData(currentImportList);
        if (null == importClockDataDto) {
            log.info("Import.Clock Fail ImportData Empty");
            return;
        }

        List<Long> empIds = importClockDataDto.getEmpIds();
        Long startdate = importClockDataDto.getStartDate();
        Long enddate = importClockDataDto.getEndDate();
        if (CollectionUtils.isEmpty(empIds) || startdate == null || enddate == null) {
            log.info("Import.Clock Fail ImportEmp Empty");
            return;
        }
        enddate = DateUtil.getOnlyDate(new Date(enddate * 1000)) + (23 * 60 * 60) + (59 * 60) + 59;

        // 查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("belongid", belongId);
        params.put("anyEmpids", "'{" + StringUtils.join(empIds.toArray(), ",") + "}'");
        params.put("sql", StringUtil.getFieldItemInLong("a.empid ", 500, empIds));
        if (analyzeType != null) {// TODO 兼容历史配置 (2.0 analyzeType 一定会有值，因此此逻辑无用了)
            Long preDate = DateUtil.addDate(startdate * 1000, -1);
            params.put("startDate", preDate);
            params.put("ymstart", Integer.valueOf(DateUtil.parseDateToPattern(new Date(preDate * 1000), "yyyyMM")));
        } else {
            params.put("startDate", startdate);
            params.put("ymstart", Integer.valueOf(DateUtil.parseDateToPattern(new Date(startdate * 1000), "yyyyMM")));
        }
        params.put("endDate", enddate);
        params.put("ymend", Integer.valueOf(DateUtil.parseDateToPattern(new Date(enddate * 1000), "yyyyMM")));
        if (BooleanUtils.isTrue(includeBdkRecord)) {
            params.put("typeNotInList", new ArrayList<>(Collections.singletonList(3)));//排除外勤打卡
        }

        // 查询本次需要分析的打卡数据(key=empid + "_" + belongDate, value=regList)
        Map<String, List<WaRegisterRecord>> empDateRegListMap = getImportEmpDateRegListMap(params, analyzeType,
                importClockDataDto);
        if (MapUtils.isEmpty(empDateRegListMap)) {
            log.info("Import.Clock Fail empDateRegListMap Empty");
            return;
        }

        // 考勤规则&员工排班
        params.put("import", true);
        WaAnalyzDTO waAnalyzDTO = registerAnalyzeService.getWaAnalyzInfo(params);

        // TODO 更新班次ID
        updateRegisterShiftId(startdate, enddate, belongId);

        // 打卡分析
        List<WaRegisterRecord> updRegisterList = new ArrayList<>();

        // 一次卡
        List<String> signOncEmpAndBelongDateKeyList = analyseOnceClock(updRegisterList, empDateRegListMap, waAnalyzDTO);
        // 一次卡过滤
        if (CollectionUtils.isNotEmpty(signOncEmpAndBelongDateKeyList)) {
            signOncEmpAndBelongDateKeyList.forEach(empDateRegListMap::remove);
        }

        // 多次卡
        ImportClockAnalyseDto clockAnalyseDto = new ImportClockAnalyseDto();
        clockAnalyseDto.setBelongId(belongId).setLocalErr(localErr).setDeviceErr(deviceErr).setAnalyzeType(analyzeType)
                .setTimeCheckType(timeCheckType).setIncludeBdkRecord(includeBdkRecord).setKyAnalyzeType(kyAnalyzeType)
                .setFilterType(filterType).setStartDate(startdate).setEndDate(DateUtil.getOnlyDate(new Date(enddate * 1000)))
                .setEmpRegBelongDates(importClockDataDto.getEmpRelBelongDateMap());
        analyseMultiClock(updRegisterList, empDateRegListMap, waAnalyzDTO, clockAnalyseDto);

        // 剩余的其他打卡记录
        for (Map.Entry<String, List<WaRegisterRecord>> entry : empDateRegListMap.entrySet()) {
            List<WaRegisterRecord> records = entry.getValue();
            updRegisterList.addAll(records);
        }

        // 前一天跨夜打卡
        for (WaRegisterRecord re : updRegisterList) {
            Long today = DateUtil.getDateLong(re.getRegDateTime() * 1000, "yyyy-MM-dd", true);
            long yesterday = DateUtil.addDate(today * 1000, -1);
            EmpShiftInfo yesterdayShift = registerAnalyzeService.getEmpShiftDefByInfo(re.getEmpid(), null, yesterday, waAnalyzDTO);
            if (yesterdayShift != null) {
                if (yesterdayShift.getOffDutyStartTime() > yesterdayShift.getOffDutyEndTime()
                        || (yesterdayShift.getOnDutyStartTime() != null && yesterdayShift.getOffDutyEndTime() <= yesterdayShift.getOnDutyStartTime())) {
                    long start = yesterday + (yesterdayShift.getOffDutyStartTime() * 60);
                    long end = today + (yesterdayShift.getOffDutyEndTime() * 60);
                    if (re.getRegDateTime() >= start && re.getRegDateTime() <= end) {
                        re.setBelongDate(yesterday);
                    }
                }
            }
        }

        importService.fastUpdList(WaRegisterRecord.class, "recordId", updRegisterList);
        log.info("Import.Clock End belongId={}, time={}", belongId, System.currentTimeMillis());
    }

    @Transactional
    @Deprecated
    public void updateRegisterShiftId(long startdate, long enddate, String belongId) {
        Integer row = waRegisterRecordMapper.updateRegisterShiftId(startdate, enddate, belongId);
        log.info("update shiftdefid = [{}]", row);
        row = waRegisterRecordMapper.updateRegisterDefaultShiftId(startdate, enddate, belongId);
        log.info("update shiftdefid2 = [{}]", row);
        row = waRegisterRecordMapper.updateRegisterStoreShiftId(startdate, enddate, belongId);
        log.info("update shiftdefid3 = [{}]", row);
    }

    public List<String> analyseOnceClock(List<WaRegisterRecord> updRegisterList,
                                         Map<String, List<WaRegisterRecord>> empDateRegListMap,
                                         WaAnalyzDTO waAnalyzDTO) {
        List<String> signOncEmpAndBelongDateKeyList = new ArrayList<>();

        for (Map.Entry<String, List<WaRegisterRecord>> entry : empDateRegListMap.entrySet()) {
            String empAndBelongDateKey = entry.getKey();
            String[] empAndBelongDateKeys = empAndBelongDateKey.split("_");
            Long empid = ConvertHelper.longConvert(empAndBelongDateKeys[0]);
            Long belongDate = Long.valueOf(empAndBelongDateKeys[1]);

            // 当日打卡记录
            List<WaRegisterRecord> currenDateRegList = empDateRegListMap.get(empAndBelongDateKey);
            if (CollectionUtils.isEmpty(currenDateRegList)) {
                continue;
            }
            currenDateRegList.sort(Comparator.comparing(WaRegisterRecord::getRegDateTime));

            // 当日排班
            EmpShiftInfo shift = registerAnalyzeService.getEmpShiftDefByInfo(empid, null, belongDate, waAnalyzDTO);
            if (shift == null) {
                continue;
            }
            // 前一天排班
            Long yesterday = DateUtil.addDate(belongDate * 1000, -1);
            EmpShiftInfo yesterdayShift = registerAnalyzeService.getEmpShiftDefByInfo(empid, null, yesterday, waAnalyzDTO);

            // 考勤规则
            WaAnalyzInfo analyzeInfo = waAnalyzDTO.getEmpWaAnalyz(empid, belongDate);
            if (null == analyzeInfo || !ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(analyzeInfo.getClock_type())) {
                continue;
            }
            signOncEmpAndBelongDateKeyList.add(empAndBelongDateKey);

            // 考勤方案规则: 有效打卡取值规则
            String clockRuleJson = analyzeInfo.getClock_rule();
            if (StringUtils.isBlank(clockRuleJson)) {
                continue;
            }
            Object clockRule = JSONObject.parseObject(clockRuleJson).get("clockRule");
            if (null == clockRule) {
                continue;
            }
            Integer parseClockRule = Integer.valueOf(clockRule.toString());
            if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(shift.getDateType())) {
                parseClockRule = ParseGroupClockRuleEnum.SIGN_TIME_RANGE.getIndex();
            }

            // 打卡分析
            if (ParseGroupClockRuleEnum.WORK_TIME_RANGE.getIndex().equals(parseClockRule)) {
                analyseOnceClockForWorkTimeRule(updRegisterList, shift, yesterdayShift, currenDateRegList, yesterday);
            } else if (ParseGroupClockRuleEnum.SIGN_TIME_RANGE.getIndex().equals(parseClockRule)) {
                analyseOnceClockForSignTimeRule(updRegisterList, shift, yesterdayShift, currenDateRegList, yesterday);
            } else {
                log.warn("The unknown clockrule is resolved For Import, clockRule={} time={}", clockRule, System.currentTimeMillis());
            }
        }

        return signOncEmpAndBelongDateKeyList;
    }

    private void analyseOnceClockForWorkTimeRule(List<WaRegisterRecord> updRegisterList,
                                                 EmpShiftInfo shift,
                                                 EmpShiftInfo yesterdayShift,
                                                 List<WaRegisterRecord> regList,
                                                 Long yesterday) {
        Long clockDate = shift.getWorkDate();

        // 前一日打卡分析
        List<Integer> yesterdayRegIdList = Lists.newArrayList();
        if (yesterdayShift != null
                && (CdWaShiftUtil.checkCrossNightV2(yesterdayShift, yesterdayShift.getDateType())
                || (yesterdayShift.getOnDutyStartTime() != null && yesterdayShift.getOffDutyEndTime() <= yesterdayShift.getOnDutyStartTime()))) {
            Long yesterdayShiftStartTime = yesterdayShift.getWorkDate() + yesterdayShift.getStartTime() * 60L;
            Long yesterdayShiftEndTime = clockDate + yesterdayShift.getEndTime() * 60L;

            List<WaRegisterRecord> yesterdayRegList = regList.stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= yesterdayShiftEndTime)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(yesterdayRegList)) {
                for (WaRegisterRecord wrrd : yesterdayRegList) {
                    wrrd.setBelongDate(yesterday);
                    wrrd.setShiftDefId(yesterdayShift.getShiftDefId());
                    wrrd.setStartTime(yesterdayShift.getStartTime());
                    wrrd.setEndTime(yesterdayShift.getEndTime());
                    wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                    wrrd.setNormalDate(String.format("%s-%s",
                            DateUtil.convertMinuteToTime(yesterdayShift.getStartTime()),
                            DateUtil.convertMinuteToTime(yesterdayShift.getEndTime())));
                    if (DateTypeEnum.DATE_TYP_1.getIndex().equals(yesterdayShift.getDateType())
                            && wrrd.getRegDateTime() >= yesterdayShiftStartTime && wrrd.getRegDateTime() <= yesterdayShiftEndTime) {
                        wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                        wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                        wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                    }
                    BeanUtil.setUpdateFieldValue(wrrd, Optional.ofNullable(SessionHolder.getUserId()).orElse(0L));
                    updRegisterList.add(wrrd);
                    yesterdayRegIdList.add(wrrd.getRecordId());
                }
            }
        }

        // 当日打卡分析
        List<WaRegisterRecord> todayRegList = CollectionUtils.isNotEmpty(yesterdayRegIdList)
                ? regList.stream().filter(it -> !yesterdayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : regList;
        if (CollectionUtils.isNotEmpty(todayRegList)) {
            long shiftStartTime = clockDate + shift.getStartTime() * 60;
            long shiftEndTime = clockDate + shift.getEndTime() * 60;
            if (CdWaShiftUtil.checkCrossNightV2(shift, shift.getDateType())) {
                clockDate = DateUtil.addDate(clockDate * 1000, 1);
                shiftEndTime = clockDate + shift.getEndTime() * 60;
            }

            for (WaRegisterRecord wrrd : todayRegList) {
                wrrd.setShiftDefId(shift.getShiftDefId());
                wrrd.setStartTime(shift.getStartTime());
                wrrd.setEndTime(shift.getEndTime());
                wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                wrrd.setNormalDate(String.format("%s-%s",
                        DateUtil.convertMinuteToTime(shift.getStartTime()),
                        DateUtil.convertMinuteToTime(shift.getEndTime())));
                if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shift.getDateType())
                        && wrrd.getRegDateTime() >= shiftStartTime && wrrd.getRegDateTime() <= shiftEndTime) {
                    wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                    wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                    wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                }
                BeanUtil.setUpdateFieldValue(wrrd, Optional.ofNullable(SessionHolder.getUserId()).orElse(0L));
                updRegisterList.add(wrrd);
            }
        }
    }

    private void analyseOnceClockForSignTimeRule(List<WaRegisterRecord> updRegisterList,
                                                 EmpShiftInfo shift,
                                                 EmpShiftInfo yesterdayShift,
                                                 List<WaRegisterRecord> regList,
                                                 Long yesterday) {
        // 前一日打卡分析
        List<Integer> yesterdayRegIdList = Lists.newArrayList();
        if (yesterdayShift != null && (yesterdayShift.getOffDutyEndTime() < yesterdayShift.getOffDutyStartTime()
                || (yesterdayShift.getOnDutyStartTime() != null && yesterdayShift.getOffDutyEndTime() <= yesterdayShift.getOnDutyStartTime())
                || CdWaShiftUtil.checkCrossNightV2(yesterdayShift, yesterdayShift.getDateType()))) {
            long offDutyEndTime = yesterday + (yesterdayShift.getOffDutyEndTime() + 1440) * 60L;
            List<WaRegisterRecord> yesterdayRegList = regList.stream()
                    .filter(wrrd -> wrrd.getRegDateTime() <= offDutyEndTime)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(yesterdayRegList)) {
                for (WaRegisterRecord wrrd : yesterdayRegList) {
                    wrrd.setBelongDate(yesterday);
                    wrrd.setShiftDefId(yesterdayShift.getShiftDefId());
                    wrrd.setStartTime(yesterdayShift.getStartTime());
                    wrrd.setEndTime(yesterdayShift.getEndTime());
                    wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
                    String normalDate = String.format("%s-%s;%s-%s",
                            DateUtil.convertMinuteToTime(yesterdayShift.doGetOnDutyStartTimeForView()),
                            DateUtil.convertMinuteToTime(yesterdayShift.getOnDutyEndTime()),
                            DateUtil.convertMinuteToTime(yesterdayShift.getOffDutyStartTime()),
                            DateUtil.convertMinuteToTime(yesterdayShift.getOffDutyEndTime()));
                    wrrd.setNormalDate(normalDate);
                    if (checkSignOnceStatus(yesterdayShift, yesterdayShift.getWorkDate(), wrrd)) {
                        wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                        wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                        wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
                    }
                    BeanUtil.setUpdateFieldValue(wrrd, Optional.ofNullable(SessionHolder.getUserId()).orElse(0L));
                    updRegisterList.add(wrrd);
                    yesterdayRegIdList.add(wrrd.getRecordId());
                }
            }
        }

        // 当日打卡分析
        List<WaRegisterRecord> todayRegList = CollectionUtils.isNotEmpty(yesterdayRegIdList)
                ? regList.stream().filter(it -> !yesterdayRegIdList.contains(it.getRecordId())).collect(Collectors.toList())
                : regList;

        for (WaRegisterRecord wrrd : todayRegList) {
            wrrd.setShiftDefId(shift.getShiftDefId());
            wrrd.setStartTime(shift.getStartTime());
            wrrd.setEndTime(shift.getEndTime());
            wrrd.setIfValid(ValidStatusEnum.INVALID.getIndex());
            String normalDate = String.format("%s-%s;%s-%s",
                    DateUtil.convertMinuteToTime(shift.doGetOnDutyStartTimeForView()),
                    DateUtil.convertMinuteToTime(shift.getOnDutyEndTime()),
                    DateUtil.convertMinuteToTime(shift.getOffDutyStartTime()),
                    DateUtil.convertMinuteToTime(shift.getOffDutyEndTime()));
            wrrd.setNormalDate(normalDate);
            if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shift.getDateType())
                    && checkSignOnceStatus(shift, shift.getWorkDate(), wrrd)) {
                wrrd.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
                wrrd.setResultType(ClockResultEnum.NORMAL.getIndex());
                wrrd.setIfValid(ValidStatusEnum.VALID.getIndex());
            }
            BeanUtil.setUpdateFieldValue(wrrd, Optional.ofNullable(SessionHolder.getUserId()).orElse(0L));
            updRegisterList.add(wrrd);
        }
    }

    public void analyseMultiClock(List<WaRegisterRecord> updRegisterList,
                                  Map<String, List<WaRegisterRecord>> empDateRegListMap,
                                  WaAnalyzDTO waAnalyzDTO,
                                  ImportClockAnalyseDto clockAnalyseDto) {
        Long userid = SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId();
        Boolean localErr = clockAnalyseDto.getLocalErr();
        Boolean deviceErr = clockAnalyseDto.getDeviceErr();
        Integer timeCheckType = clockAnalyseDto.getTimeCheckType();
        int kyAnalyzeType = Optional.ofNullable(clockAnalyseDto.getKyAnalyzeType()).orElse(1);
        int filterType = Optional.ofNullable(clockAnalyseDto.getFilterType()).orElse(1);
        Long preDate = DateUtil.addDate(clockAnalyseDto.getStartDate() * 1000, -1);
        Map<Long, List<Long>> empRegBelongDates = clockAnalyseDto.getEmpRegBelongDates();

        for (Map.Entry<String, List<WaRegisterRecord>> entry : empDateRegListMap.entrySet()) {
            String empAndBelongDateKey = entry.getKey();
            String[] empAndBelongDateKeys = empAndBelongDateKey.split("_");
            Long empid = ConvertHelper.longConvert(empAndBelongDateKeys[0]);
            Long belongDate = Long.valueOf(empAndBelongDateKeys[1]);

            // 员工当天的打卡记录
            List<WaRegisterRecord> currenDateRegList = entry.getValue();
            if (CollectionUtils.isEmpty(currenDateRegList)) {
                continue;
            }

            if (clockAnalyseDto.getAnalyzeType() != null) {
                if (clockAnalyseDto.getAnalyzeType() == 1) {// 只分析本次导入的日期数据
                    List<Long> currentImportDateList = empRegBelongDates.get(empid);
                    if (!currentImportDateList.contains(belongDate)) {
                        continue;
                    }
                    // 判断此卡是不是为前一天的签退，如果是则将此卡归到前一天
                    Iterator<WaRegisterRecord> registerRecordIterator = currenDateRegList.iterator();
                    while (registerRecordIterator.hasNext()) {
                        WaRegisterRecord re = registerRecordIterator.next();
                        Boolean isBelongPreDate = checkRegIsBelongPreDateSignOff(re, empDateRegListMap,
                                waAnalyzDTO, userid, localErr, deviceErr, timeCheckType);
                        if (BooleanUtils.isTrue(isBelongPreDate)) {
                            updRegisterList.add(re);
                            registerRecordIterator.remove();
                        }
                    }
                    empDateRegListMap.put(entry.getKey(), currenDateRegList);
                } else if (clockAnalyseDto.getAnalyzeType() == 2) {//排除导入开始日期减一天的数据
                    if (preDate.equals(belongDate)) {
                        WaRegisterRecord re = currenDateRegList.get(0);
                        EmpShiftInfo shift = registerAnalyzeService.getEmpShiftDefByInfo(empid, null, belongDate, waAnalyzDTO);
                        //当班次跨夜或者打卡区间跨夜，主动去找是否有跨夜签退数据
                        if (shift.getOffDutyEndTime() < shift.getOffDutyStartTime()
                                || (shift.getOnDutyStartTime() != null && shift.getOffDutyEndTime() <= shift.getOnDutyStartTime())
                                || CdWaShiftUtil.checkCrossNightV2(shift, shift.getDateType())) {
                            Long tomorrow = DateUtil.addDate(belongDate * 1000, 1);
                            List<WaRegisterRecord> tomorrowRegList = empDateRegListMap.get(re.getEmpid() + "_" + tomorrow);
                            WaRegisterRecord filterReg = filterRegister(tomorrowRegList, shift, filterType, updRegisterList);
                            if (filterReg != null) {
                                filterReg.setBelongDate(belongDate);
                                BeanUtil.setUpdateFieldValue(filterReg, userid);
                                filterReg.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                                filterReg.setShiftDefId(shift.getShiftDefId());
                                changeRegisterInfo(filterReg, shift, waAnalyzDTO, localErr, deviceErr, timeCheckType);
                                updRegisterList.add(filterReg);
                                tomorrowRegList.remove(filterReg);
                            }
                        }
                        continue;
                    }
                }
            }

            if (CollectionUtils.isEmpty(currenDateRegList)) {
                continue;
            }

            if (currenDateRegList.size() == 1) {
                WaRegisterRecord re = currenDateRegList.get(0);
                Long belongdate = re.getBelongDate();

                // 员工排班
                EmpShiftInfo shift = registerAnalyzeService.getEmpShiftDefByInfo(re.getEmpid(), null, re.getBelongDate(), waAnalyzDTO);
                long yesterday = DateUtil.addDate(re.getBelongDate() * 1000, -1);
                if (shift == null) {
                    continue;
                }
                boolean isShiftKy = CdWaShiftUtil.checkCrossNightV2(shift, shift.getDateType());

                // 计算签到、签退正常打卡时间范围
                long onDutyStartTime = re.getBelongDate().intValue() + (shift.getOnDutyStartTime() * 60);
                long onDutyEndTime = re.getBelongDate().intValue() + (shift.getOnDutyEndTime() * 60);
                Long regBelongDate = DateUtil.getDateLong(re.getRegDateTime() * 1000, "yyyy-MM-dd", true);
                long offDutyStartTime = re.getBelongDate().intValue() + (shift.getOffDutyStartTime() * 60);
                if (isShiftKy) {
                    if (re.getBelongDate().longValue() == regBelongDate) {
                        regBelongDate = DateUtil.addDate(regBelongDate * 1000, 1);
                    }
                    offDutyStartTime = regBelongDate + (shift.getOffDutyStartTime() * 60);
                } else if (shift.getOffDutyEndTime() < shift.getOffDutyStartTime()
                        || (shift.getOnDutyStartTime() != null && shift.getOffDutyEndTime() <= shift.getOnDutyStartTime())) {
                    if (re.getBelongDate().longValue() == regBelongDate) {
                        regBelongDate = DateUtil.addDate(regBelongDate * 1000, 1);
                    }
                }
                long offDutyEndTime = regBelongDate + (shift.getOffDutyEndTime() * 60);

                // 打卡类型
                if ((re.getRegDateTime() >= onDutyStartTime && re.getRegDateTime() <= onDutyEndTime)) {
                    re.setRegisterType(ClockTypeEnum.SIGN_IN.getIndex());
                } else if (re.getRegDateTime() >= offDutyStartTime && re.getRegDateTime() <= offDutyEndTime) {
                    re.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                } else {
                    re.setRegisterType(ClockTypeEnum.SIGN_IN.getIndex());
                }

                changeRegisterInfo(re, shift, waAnalyzDTO, localErr, deviceErr, timeCheckType);
                re.setIfValid(ValidStatusEnum.VALID.getIndex());

                //在前一天的下班时间范围要归到前一天
                EmpShiftInfo yesterdayShift = registerAnalyzeService.getEmpShiftDefByInfo(re.getEmpid(), null, yesterday, waAnalyzDTO);
                if (yesterdayShift != null) {
                    if (yesterdayShift.getOffDutyStartTime() > yesterdayShift.getOffDutyEndTime()
                            || (yesterdayShift.getOnDutyStartTime() != null && yesterdayShift.getOffDutyEndTime() <= yesterdayShift.getOnDutyStartTime())) {
                        long start = yesterday + (yesterdayShift.getOffDutyStartTime() * 60);
                        long end = belongdate + (yesterdayShift.getOffDutyEndTime() * 60);
                        if (re.getRegDateTime() >= start && re.getRegDateTime() <= end) {
                            re.setBelongDate(yesterday);
                        }
                    }
                }

                BeanUtil.setUpdateFieldValue(re, userid);
                updRegisterList.add(re);

                //当班次跨夜或者打卡区间跨夜并且当前为签到卡时，主动去找是否有跨夜签退数据
                if ((shift.getOffDutyEndTime() < shift.getOffDutyStartTime()
                        || (shift.getOnDutyStartTime() != null && shift.getOffDutyEndTime() <= shift.getOnDutyStartTime())
                        || isShiftKy)
                        && re.getRegisterType() == 1) {
                    Long tomorrow = DateUtil.addDate(belongdate * 1000, 1);
                    String key2 = re.getEmpid() + "_" + tomorrow;
                    List<WaRegisterRecord> regs = empDateRegListMap.get(key2);
                    WaRegisterRecord filterReg = filterRegister(regs, shift, filterType, updRegisterList);
                    if (filterReg != null) {
                        filterReg.setBelongDate(belongdate);
                        BeanUtil.setUpdateFieldValue(filterReg, userid);
                        filterReg.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                        filterReg.setShiftDefId(shift.getShiftDefId());
                        changeRegisterInfo(filterReg, shift, waAnalyzDTO, localErr, deviceErr, timeCheckType);
                        filterReg.setIfValid(ValidStatusEnum.VALID.getIndex());

                        updRegisterList.add(filterReg);
                        regs.remove(filterReg);
                    }
                }
            } else {
                WaRegisterRecord singin = null;
                WaRegisterRecord singoff = null;
                WaRegisterRecord re = currenDateRegList.get(0);

                // 员工排班
                EmpShiftInfo shift = registerAnalyzeService.getEmpShiftDefByInfo(re.getEmpid(), null, re.getBelongDate(), waAnalyzDTO);
                if (shift == null) {
                    continue;
                }

                boolean isShiftKy = false;
                Long belongDateAfter = null;
                for (int i = 0; i < currenDateRegList.size(); i++) {
                    WaRegisterRecord record = currenDateRegList.get(i);
                    if (i == 0 && record.getBelongDate() != null
                            && record.getRegisterType() != null
                            && record.getShiftDefId() != null) {
                        Long msec = DateUtil.getDateLong(record.getRegDateTime() * 1000, "yyyy-MM-dd", true);
                        if (record.getBelongDate() < msec) {// 前一天打卡
                            continue;
                        }
                    }

                    belongDateAfter = DateUtil.getDateLong(record.getRegDateTime() * 1000, "yyyy-MM-dd", true);
                    isShiftKy = CdWaShiftUtil.checkCrossNightV2(shift, shift.getDateType());
                    if (isShiftKy
                            || shift.getOffDutyEndTime() < shift.getOffDutyStartTime()
                            || (shift.getOnDutyStartTime() != null && shift.getOffDutyEndTime() <= shift.getOnDutyStartTime())) {
                        belongDateAfter = DateUtil.addDate(belongDateAfter * 1000, 1);
                    }

                    // 上班、下班打卡区间
                    long onDutyStartTime = record.getBelongDate() + (shift.getOnDutyStartTime() * 60);
                    long onDutyEndTime = record.getBelongDate() + (shift.getOnDutyEndTime() * 60);
                    long offDutyStartTime = record.getBelongDate() + (shift.getOffDutyStartTime() * 60);
                    if (isShiftKy) {
                        offDutyStartTime = belongDateAfter + (shift.getOffDutyStartTime() * 60);
                    }
                    long offDutyEndTime = belongDateAfter + (shift.getOffDutyEndTime() * 60);

                    if (record.getRegDateTime() >= onDutyStartTime && (record.getRegDateTime() <= onDutyEndTime)) {
                        // 在上班打卡区间内
                        if (singin == null) {
                            singin = record;
                        } else {
                            singoff = record;
                        }
                    } else if ((record.getRegDateTime() >= offDutyStartTime) && record.getRegDateTime() <= offDutyEndTime) {
                        // 在下班打卡区间内
                        if (singoff != null) {
                            if (singoff.getRegDateTime() >= record.getRegDateTime()) {
                                continue;
                            }
                        }

                        if (kyAnalyzeType == 2
                                && ((shift.getOffDutyEndTime() < shift.getOffDutyStartTime())
                                || (shift.getOnDutyStartTime() != null && shift.getOffDutyEndTime() <= shift.getOnDutyStartTime())
                                || isShiftKy)) {
                            String key2 = record.getEmpid() + "_" + belongDateAfter;
                            List<WaRegisterRecord> regs = empDateRegListMap.get(key2);
                            WaRegisterRecord filterReg = filterRegister(regs, shift, filterType, updRegisterList);
                            if (filterReg != null) {
                                filterReg.setBelongDate(record.getBelongDate());
                                filterReg.setShiftDefId(shift.getShiftDefId());
                                singoff = filterReg;
                            } else {
                                if (singin == null) {
                                    singin = record;
                                } else {
                                    singoff = record;
                                }
                            }
                        } else {
                            if (singin == null) {
                                singin = record;
                            } else {
                                singoff = record;
                            }
                        }
                    } else {// 其他情况
                        if (record.getRegDateTime() > onDutyEndTime && record.getRegDateTime() < offDutyStartTime) {
                            if (kyAnalyzeType == 2
                                    && (isShiftKy
                                    || (shift.getOnDutyStartTime() != null && shift.getOffDutyEndTime() <= shift.getOnDutyStartTime())
                                    || shift.getOffDutyEndTime() < shift.getOffDutyStartTime()) && i == currenDateRegList.size() - 1) {// TODO
                                // 如果是第四种情况，则去匹配第二天的签到数据
                                String key2 = record.getEmpid() + "_" + belongDateAfter;
                                List<WaRegisterRecord> regs = empDateRegListMap.get(key2);
                                WaRegisterRecord filterReg = filterRegister(regs, shift, filterType, updRegisterList);
                                if (filterReg != null) {
                                    WaRegisterRecord newRecord = new WaRegisterRecord();
                                    BeanUtils.copyProperties(filterReg, newRecord);
                                    newRecord.setBelongDate(record.getBelongDate());
                                    // 以下判断会出现的情况，拿跨夜单据和班次打卡范围对比，如果在上班打卡范围内的算签到，下班打卡范围内的算签退
                                    if ((newRecord.getRegDateTime() >= onDutyStartTime && newRecord.getRegDateTime() <= onDutyEndTime)) {
                                        newRecord.setRegisterType(ClockTypeEnum.SIGN_IN.getIndex());
                                        singin = newRecord;
                                        regs.remove(filterReg);
                                    } else if (newRecord.getRegDateTime() >= offDutyStartTime && newRecord.getRegDateTime() <= offDutyEndTime) {
                                        newRecord.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                                        if (singin == null) {
                                            singin = record;
                                        }
                                        singoff = newRecord;
                                        regs.remove(filterReg);
                                    } else {
                                        if (singin == null) {
                                            singin = record;
                                        } else {
                                            singoff = record;
                                        }
                                    }
                                } else {
                                    if (singin == null) {
                                        singin = record;
                                    } else {
                                        singoff = record;
                                    }
                                }
                            } else {
                                if (singin == null) {
                                    singin = record;
                                } else {
                                    singoff = record;
                                }
                            }
                        } else {
                            if (singin == null) {
                                singin = record;
                            } else {
                                singoff = record;
                            }
                        }
                    }
                    if (singoff != null) {
                        updateRegisterStatus(singoff);
                    }
                }

                if (singin != null) {
                    BeanUtil.setUpdateFieldValue(singin, userid);
                    singin.setRegisterType(ClockTypeEnum.SIGN_IN.getIndex());
                    changeRegisterInfo(singin, shift, waAnalyzDTO, localErr, deviceErr, timeCheckType);
                    singin.setIfValid(ValidStatusEnum.VALID.getIndex());
                    updRegisterList.add(singin);
                    currenDateRegList.remove(singin);

                    if (singoff == null) {
                        if ((shift.getOffDutyEndTime() < shift.getOffDutyStartTime())
                                || (shift.getOnDutyStartTime() != null && shift.getOffDutyEndTime() <= shift.getOnDutyStartTime())
                                || isShiftKy) {
                            String key2 = singin.getEmpid() + "_" + belongDateAfter;
                            List<WaRegisterRecord> regs = empDateRegListMap.get(key2);
                            WaRegisterRecord filterReg = filterRegister(regs, shift, filterType, updRegisterList);
                            if (filterReg != null) {
                                filterReg.setBelongDate(singin.getBelongDate());
                                filterReg.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                                filterReg.setShiftDefId(shift.getShiftDefId());
                                singoff = filterReg;
                                regs.remove(singoff);
                            }
                        }
                    }
                }

                if (singoff != null) {
                    BeanUtil.setUpdateFieldValue(singoff, userid);
                    singoff.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                    changeRegisterInfo(singoff, shift, waAnalyzDTO, localErr, deviceErr, timeCheckType);
                    singoff.setIfValid(ValidStatusEnum.VALID.getIndex());
                    if (singoff.getRecordId() == null) {
                        waRegisterRecordMapper.insertSelective(singoff);
                    } else {
                        updRegisterList.add(singoff);
                        currenDateRegList.remove(singoff);
                    }
                }
            }
        }
    }

    private void updateRegisterStatus(WaRegisterRecord singoff) {
        if (StringUtils.isBlank(singoff.getMobDeviceNum())) {
            // 如果是非手机端的签到记录，把签到状态变更成空
            singoff.setResultType(0);
            // 如果是非手机端的签到记录，把签到状态变更成空
            singoff.setRegisterType(0);
            singoff.setResultDesc("");
        }
    }

    public void deleteRecordById(List<Integer> delRegIds) {
        if (CollectionUtils.isNotEmpty(delRegIds)) {
            String recordIds = StringUtil.getFieldItemIn("record_id ", 500, delRegIds);
            if (recordIds.trim().length() > 0) {
                Integer delrow = waRegisterRecordMapper.deleteRecordId(recordIds);
                log.info("删除了重复的数据[{}]条", delrow);
            }
        }
    }

    private boolean checkSignOnceStatus(EmpShiftInfo todayShift, Long today, WaRegisterRecord wrrd) {
        boolean signOnceStatus = false;
        long offDutyStartTime = 0L, offDutyEndTime = 0L;
        signOnceStatus = wrrd.getRegDateTime() >= (today + todayShift.getOnDutyStartTime() * 60) && wrrd.getRegDateTime() <= (today + todayShift.getOnDutyEndTime() * 60);

        long acrossNight = 0L;
        if (todayShift.getOffDutyStartTime() > todayShift.getOffDutyEndTime()
                || CdWaShiftUtil.checkCrossNightV2(todayShift, todayShift.getDateType())) {
            // 跨夜
            acrossNight = 1440L;
        }
        if (CdWaShiftUtil.checkCrossNightV2(todayShift, todayShift.getDateType())) {
            offDutyStartTime = today + (todayShift.getOffDutyStartTime() + acrossNight) * 60;
        } else {
            offDutyStartTime = today + (todayShift.getOffDutyStartTime()) * 60;
        }
        offDutyEndTime = today + (todayShift.getOffDutyEndTime() + acrossNight) * 60;
        signOnceStatus = (signOnceStatus || wrrd.getRegDateTime() >= (offDutyStartTime) && wrrd.getRegDateTime() <= offDutyEndTime);
        return signOnceStatus;
    }

    public List<UpdRowDto> getImportDataListForReg(Map<String, List<UpdRowDto>> addList,
                                                   Map<String, List<UpdRowDto>> updList) {
        List<UpdRowDto> dataList = new ArrayList<>();
        List<UpdRowDto> valueAddList = addList.get("wa_register_record");
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            dataList.addAll(valueAddList);
        }
        List<UpdRowDto> valueUpdList = updList.get("wa_register_record");
        if (CollectionUtils.isNotEmpty(valueUpdList)) {
            dataList.addAll(valueUpdList);
        }
        return dataList;
    }

    @Deprecated
    @Transactional
    public void analyzeRegisterRecord(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {

        List<UpdRowDto> allList = getImportDataListForReg(addList, updList);

        // 求最大最小值
        long startdate = 0, enddate = 0;
        List<Long> empids = new ArrayList<Long>();
        for (UpdRowDto updRowDto : allList) {
            //数据已经导入表中 分析数据
            Long empid = null;
            Long reg_date_time = null;
            for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                if (saveItemDto.getItemCode().equals("empid")) {
                    empid = ConvertHelper.longConvert(saveItemDto.getItemValue());
                }
                if (saveItemDto.getItemCode().equals("reg_date_time")) {
                    Object datatime = saveItemDto.getItemValue();
                    if (datatime instanceof BigDecimal) {
                        reg_date_time = ((BigDecimal) saveItemDto.getItemValue()).longValue();
                    } else {
                        reg_date_time = (Long) saveItemDto.getItemValue();
                    }
                }
            }
            if (empid == null) {
                continue;
            }
            if (reg_date_time.intValue() > enddate) {
                enddate = reg_date_time;
            }
            if (startdate == 0) {
                startdate = reg_date_time;
            }
            if (reg_date_time.intValue() < startdate) {
                startdate = reg_date_time;
            }
        }
        // 接入或导入了考勤签到签退数据，异步核算数据
        registerAnalyzeService.asyncAnalyzeRegister(belongId, startdate, enddate);
        log.info("--------------------------------签到数据核算完成");
    }

    private ImportClockDataDto getImportClockData(List<UpdRowDto> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }

        long minRegDateTime = 0L;
        long maxRegDateTime = 0L;
        List<Long> empIds = new ArrayList<>();
        List<String> empRelBelongDateList = new ArrayList<>();
        Map<Long, List<Long>> empRelBelongDateMap = new HashMap<>();
        Map<Long, List<Long>> regDateRelEmpIdMap = new HashMap<>();

        for (UpdRowDto updRowDto : dataList) {
            Long empId = null;
            Long regDateTime = null;
            for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                if (saveItemDto.getItemCode().equals("empid")) {
                    empId = ConvertHelper.longConvert(saveItemDto.getItemValue());
                }
                if (saveItemDto.getItemCode().equals("reg_date_time")) {
                    Object dataTime = saveItemDto.getItemValue();
                    if (dataTime instanceof BigDecimal) {
                        regDateTime = ((BigDecimal) saveItemDto.getItemValue()).longValue();
                    } else {
                        if (saveItemDto.getItemValue() instanceof Integer) {
                            regDateTime = ((Integer) saveItemDto.getItemValue()).longValue();
                        } else {
                            regDateTime = (Long) saveItemDto.getItemValue();
                        }
                    }
                }
            }
            if (empId == null || regDateTime == null) {
                continue;
            }

            if (!empIds.contains(empId)) {
                empIds.add(empId);
            }
            if (regDateTime > maxRegDateTime) {
                maxRegDateTime = regDateTime;
            }
            if (minRegDateTime == 0) {
                minRegDateTime = regDateTime;
            }
            if (regDateTime < minRegDateTime) {
                minRegDateTime = regDateTime;
            }

            Long belongDate = DateUtil.getDateLong(regDateTime * 1000, "yyyy-MM-dd", true);

            String empRelBelongDateItem = empId + "_" + belongDate;
            if (!empRelBelongDateList.contains(empRelBelongDateItem)) {
                empRelBelongDateList.add(empRelBelongDateItem);
            }

            if (empRelBelongDateMap.containsKey(empId)) {
                List<Long> belongDateList = empRelBelongDateMap.get(empId);
                if (!belongDateList.contains(belongDate)) {
                    belongDateList.add(belongDate);
                    empRelBelongDateMap.put(empId, belongDateList);
                }
            } else {
                empRelBelongDateMap.put(empId, Lists.newArrayList(belongDate));
            }

            if (regDateRelEmpIdMap.containsKey(belongDate)) {
                List<Long> belongEmpIdList = regDateRelEmpIdMap.get(belongDate);
                if (!belongEmpIdList.contains(empId)) {
                    belongEmpIdList.add(empId);
                    regDateRelEmpIdMap.put(belongDate, belongEmpIdList);
                }
            } else {
                regDateRelEmpIdMap.put(belongDate, Lists.newArrayList(empId));
            }
        }

        ImportClockDataDto clockDataDto = new ImportClockDataDto();
        clockDataDto.setStartDate(DateUtil.getOnlyDate(new Date(minRegDateTime * 1000)));
        clockDataDto.setEndDate(DateUtil.getOnlyDate(new Date(maxRegDateTime * 1000)));
        clockDataDto.setEmpIds(empIds);
        clockDataDto.setEmpRelBelongDateMap(empRelBelongDateMap);
        clockDataDto.setEmpRelBelongDateList(empRelBelongDateList);
        clockDataDto.setRegDateRelEmpIdMap(regDateRelEmpIdMap);
        return clockDataDto;
    }

    private Map<String, List<WaRegisterRecord>> getImportEmpDateRegListMap(Map<String, Object> params,
                                                                           Integer analyzeType,
                                                                           ImportClockDataDto importClockDataDto) {
        List<WaRegisterRecord> registerList = waRegisterRecordMapper.findRegisterList(params);
        if (CollectionUtils.isEmpty(registerList)) {
            return new LinkedHashMap<>();
        }
        registerList = registerList.stream()
                .filter(r -> !r.getType().equals(6) || (r.getType().equals(6) && r.getApprovalStatus().equals(2)))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(registerList)) {
            return new LinkedHashMap<>();
        }

        Long startdate = importClockDataDto.getStartDate();
        List<String> importEmpRegList = importClockDataDto.getEmpRelBelongDateList();
        Map<String, List<WaRegisterRecord>> empDateRegListMap = new LinkedHashMap<>();
        List<Integer> delRegIds = new ArrayList<>();

        for (WaRegisterRecord wrrd : registerList) {
            Long empid = wrrd.getEmpid();
            Long regTime = wrrd.getRegDateTime();

            if (analyzeType == null) {// TODO 兼容历史配置 (2.0 analyzeType 一定会有值，因此此逻辑无用了)
                // 排除掉前一天是分析出来的跨夜数据 当签到日期是在所属日期的后一天
                if (wrrd.getBelongDate() != null && wrrd.getBelongDate() == (startdate - (24 * 60 * 60))) {
                    continue;
                }
            }

            // 设置归属日期
            Long belongDate = DateUtil.getDateLong(regTime * 1000, "yyyy-MM-dd", true);
            if (wrrd.getBelongDate() == null || importEmpRegList.contains(empid + "_" + belongDate)) {
                wrrd.setBelongDate(belongDate);
            }

            String empDateKey = empid + "_" + belongDate;
            if (empDateRegListMap.containsKey(empDateKey)) {
                List<WaRegisterRecord> empRegList = empDateRegListMap.get(empDateKey);
                boolean ifAdd = true;
                // TODO 检查并删除是否有重复导入的数据
                for (WaRegisterRecord waRegisterRecord : empRegList) {
                    if (waRegisterRecord.getEmpid().equals(wrrd.getEmpid())
                            && waRegisterRecord.getRegDateTime().equals(wrrd.getRegDateTime())) {
                        delRegIds.add(wrrd.getRecordId());
                        ifAdd = false;
                        break;
                    }
                }
                if (ifAdd) {
                    empRegList.add(wrrd);
                }
            } else {
                empDateRegListMap.put(empDateKey, Lists.newArrayList(wrrd));
            }
        }
        deleteRecordById(delRegIds);
        return empDateRegListMap;
    }

    public void importRegister(Map<String, Object> params, Map<String, List<WaRegisterRecord>> listMap) throws Exception {
        Long userid = SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId();

        List<Integer> delRegIds = new ArrayList<Integer>();
        if (MapUtils.isNotEmpty(listMap)) {
            params.put("import", true);
            WaAnalyzDTO dto = registerAnalyzeService.getWaAnalyzInfo(params);
            // 用于记录当天是否已经有签到签退
            for (Map.Entry<String, List<WaRegisterRecord>> entry : listMap.entrySet()) {
                String key = entry.getKey();
                List<WaRegisterRecord> registers = entry.getValue();
                if (registers != null && registers.size() > 0) {
                    // 按签到-》签退方式依次循环，跨夜的签到记录统统归属到前一个班次上
                    EmpShiftInfo shift = null;
                    EmpShiftInfo preShift = null;
                    // 记录员工每天所属的班次
                    Map<String, EmpShiftInfo> empShiftList = new HashMap<>();
                    // 取前一天最后一次的签到类型
                    for (int i = 0; i < registers.size(); i++) {
                        WaRegisterRecord re = registers.get(i);
                        shift = registerAnalyzeService.getEmpShiftDefByInfo(re.getEmpid(), null, re.getBelongDate(), dto);
                        if (shift == null) {// 当班次为空的情况，不导入
                            if (re.getRegisterType() == null)
                                delRegIds.add(re.getRecordId());
                            continue;
                        }
                        String empKey = re.getEmpid() + "_" + re.getBelongDate();
                        empShiftList.put(empKey, shift);
                        Long preBelongDate = (DateUtil.addDate(re.getBelongDate() * 1000, -1));
                        String preEmpKey = re.getEmpid() + "_" + preBelongDate;
                        if (empShiftList.containsKey(preEmpKey)) {
                            preShift = empShiftList.get(preEmpKey);
                        } else {
                            // 前一天的班次
                            preShift = registerAnalyzeService.getEmpShiftDefByInfo(re.getEmpid(), null, preBelongDate, dto);
                            empShiftList.put(preEmpKey, preShift);
                        }
                        //计算签到签退，取最早和最晚的签到记录，必须在允许打开范围
                        BeanUtil.setUpdateFieldValue(re, userid);
                        // 签到签退记录归属
                        changeBelongDate(re, shift, preShift);
                        // 判断迟到早退异常情况
                        changeRegisterInfo(re, shift, dto);
                        //更新
                        waRegisterRecordMapper.updateByPrimaryKey(re);
                    }
                }
            }
            deleteRecordById(delRegIds);
            log.info("--------------------------------签到数据接入完成");
        }
    }

    private void changeBelongDate(WaRegisterRecord rd, EmpShiftInfo shift, EmpShiftInfo preShift) {
        if (preShift == null) return;
        // 如果签到记录 在前一个班次的打卡范围内，则归属日期变更为前一天的
        if (rd.getRegDateTime() >= preShift.getShiftStartTime() && rd.getRegDateTime() <= preShift.getShiftEndTime()) {
            rd.setBelongDate(preShift.getWorkDate());
        }
    }

    /**
     * 筛选出可能是第二天签退的记录
     *
     * @param regs
     * @param shiftdef
     * @param filterType 过滤类型 1 返回最早的一条 2 返回最晚的一条
     * @return
     */
    private WaRegisterRecord filterRegister(List<WaRegisterRecord> regs, EmpShiftInfo shiftdef, Integer filterType, List<WaRegisterRecord> updRegisterList) {
        filterType = filterType == null ? 1 : filterType;
        if (shiftdef == null) {
            return null;
        }
        int s = shiftdef.getWorkDate().intValue() + (shiftdef.getOffDutyStartTime() * 60);
        WaRegisterRecord sinoff = null;
        if (regs != null && regs.size() > 0) {
            if (filterType == 2) {
                for (int i = regs.size() - 1; i >= 0; i--) {
                    WaRegisterRecord re = regs.get(i);
                    int e = re.getBelongDate().intValue() + (shiftdef.getOffDutyEndTime() * 60);
                    // 如果在前一个班次的下班打卡范围内，则自动归属于前一天，并返回最后一条记录作为前一天的签退记录；在移出其他范围内的记录（只作记录，不做分析）
                    if (re.getRegDateTime() >= s && re.getRegDateTime() <= e) {
                        re.setBelongDate(shiftdef.getWorkDate());
                        updRegisterList.add(re);
                        if (sinoff == null) {
                            sinoff = re;
                        }
                        regs.remove(re);
                    }
                }
            } else {
                WaRegisterRecord re = regs.get(0);
                int e = re.getBelongDate().intValue() + (shiftdef.getOffDutyEndTime() * 60);
                if (re.getRegDateTime() >= s && re.getRegDateTime() <= e) {
                    re.setBelongDate(shiftdef.getWorkDate());
                    sinoff = re;
                    regs.remove(re);
                }
            }
        }
        return sinoff;
    }

    /**
     * 检查打卡是否是前一天的签退
     *
     * @param re
     * @param registerRecordListMap
     * @param dto
     * @return
     */
    private Boolean checkRegIsBelongPreDateSignOff(WaRegisterRecord re,
                                                   Map<String, List<WaRegisterRecord>> registerRecordListMap,
                                                   WaAnalyzDTO dto,
                                                   Long userid,
                                                   Boolean localErr,
                                                   Boolean deviceErr,
                                                   Integer timeCheckType) {
        Long currentDate = DateUtil.getDateLong(re.getRegDateTime() * 1000, "yyyy-MM-dd", true);
        Long preDate = DateUtil.addDate(currentDate * 1000, -1);
        List<WaRegisterRecord> preDateRegList = registerRecordListMap.get(re.getEmpid() + "_" + preDate);
        if (CollectionUtils.isEmpty(preDateRegList)) {
            return false;
        }
        preDateRegList = preDateRegList.stream()
                .filter(row -> preDate.equals(row.getBelongDate())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(preDateRegList)) {
            return false;
        }

        WaRegisterRecord signIn = null;
        WaRegisterRecord signOff = null;
        for (WaRegisterRecord regRow : preDateRegList) {
            if (regRow.getRegisterType() == null || regRow.getBelongDate() == null) {
                continue;
            }
            if (ClockTypeEnum.SIGN_IN.getIndex().equals(regRow.getRegisterType()) && preDate.equals(regRow.getBelongDate())) {
                signIn = regRow;
            }
            if (ClockTypeEnum.SIGN_OUT.getIndex().equals(regRow.getRegisterType()) && preDate.equals(regRow.getBelongDate())) {
                signOff = regRow;
            }
        }

        if (signIn == null || signOff != null) {
            return false;
        }

        EmpShiftInfo preDateShift = registerAnalyzeService.getEmpShiftDefByInfo(re.getEmpid(), null, preDate, dto);
        if (null == preDateShift) {
            return false;
        }
        if (preDateShift.getEndTime() <= preDateShift.getStartTime()
                || (preDateShift.getOnDutyStartTime() != null && preDateShift.getOffDutyEndTime() != null
                && preDateShift.getOnDutyStartTime() >= preDateShift.getOffDutyEndTime())
                || (preDateShift.getOffDutyEndTime() != null && preDateShift.getOffDutyStartTime() != null
                && preDateShift.getOffDutyEndTime() < preDateShift.getOffDutyStartTime())) {
            long preOffDutyStartTime = preDate.intValue() + (preDateShift.getOffDutyStartTime() * 60);
            long preOffDutyEndTime = re.getBelongDate().intValue() + (preDateShift.getOffDutyEndTime() * 60);
            if (re.getRegDateTime() >= preOffDutyStartTime && re.getRegDateTime() <= preOffDutyEndTime) {
                re.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                re.setBelongDate(preDate);
                BeanUtil.setUpdateFieldValue(re, userid);
                re.setShiftDefId(preDateShift.getShiftDefId());
                changeRegisterInfo(re, preDateShift, dto, localErr, deviceErr, timeCheckType);
                return true;
            }
        }
        return false;
    }

    /**
     * 打卡数据异常分析
     *
     * @param register
     * @param shiftdef
     * @param dto
     */
    private void changeRegisterInfo(WaRegisterRecord register, EmpShiftInfo shiftdef, WaAnalyzDTO dto) {
        analyzeRegisterInfo(register, shiftdef, dto, false, false, 1);
    }

    /**
     * 打卡数据异常分析
     *
     * @param register
     * @param shiftdef
     * @param dto
     * @param localErrorCheck
     * @param deviceErrorCheck
     */
    private void changeRegisterInfo(WaRegisterRecord register, EmpShiftInfo shiftdef, WaAnalyzDTO dto, Boolean localErrorCheck, Boolean deviceErrorCheck, Integer timeCheckType) {
        analyzeRegisterInfo(register, shiftdef, dto, localErrorCheck, deviceErrorCheck, timeCheckType);
    }

    /**
     * 打卡数据异常分析
     *
     * @param register
     * @param shiftdef
     * @param dto
     * @param localErrorCheck  地点异常分析 true 分析 false、null 不分析
     * @param deviceErrorCheck 设备异常分析 true 分析 false、null 不分析
     * @param timeCheckType    时间异常校验类型 1 精确匹配 2 模糊匹配
     */
    private void analyzeRegisterInfo(WaRegisterRecord register, EmpShiftInfo shiftdef, WaAnalyzDTO dto,
                                     Boolean localErrorCheck, Boolean deviceErrorCheck, Integer timeCheckType) {
        long regTime = register.getRegDateTime();
        Long belongDate = register.getBelongDate();
        //      5.如果当天对应的排班是休息日，则不要分析早退，迟到，请假，工作小时数
        if (shiftdef == null) {
            register.setRegisterType(null);
        } else {
            // 签到打卡开始时间
            long on_duty_start_time = belongDate.intValue() + shiftdef.getOnDutyStartTime() * 60;
            // 打卡截止
            long on_duty_end_time = belongDate.intValue() + shiftdef.getOnDutyEndTime() * 60;
            // 签退打卡开始时间
            long off_duty_start_time = belongDate.intValue() + shiftdef.getOffDutyStartTime() * 60;

            Boolean isShiftKy = CdWaShiftUtil.checkCrossNightV2(shiftdef, shiftdef.getDateType());

            Long belongDateAfter = belongDate;
            // 如果跨夜了
            if (isShiftKy || shiftdef.getOffDutyEndTime() < shiftdef.getOffDutyStartTime()) {
                // 第二天的时间点
                belongDateAfter = DateUtil.addDate(belongDateAfter * 1000, 1);
            }
            if (isShiftKy) {
                off_duty_start_time = belongDateAfter + shiftdef.getOffDutyStartTime() * 60;
            }
            long off_duty_end_time = belongDateAfter + shiftdef.getOffDutyEndTime() * 60;

            // 1、工作日，2休息日，3法定假日
            Integer dateType = shiftdef.getDateType();

            Integer registerType = register.getRegisterType();
            if (registerType == null) {
                // 当签到时间在打卡开始区间内则作为签到处理
                if (register.getRegDateTime() >= on_duty_start_time && register.getRegDateTime() <= on_duty_end_time) {
                    register.setRegisterType(ClockTypeEnum.SIGN_IN.getIndex());
                    registerType = 1;
                } else if (register.getRegDateTime() >= off_duty_start_time && register.getRegDateTime() <= off_duty_end_time) {
                    register.setRegisterType(ClockTypeEnum.SIGN_OUT.getIndex());
                    registerType = 2;
                } else {
                    registerType = 0;
                }
            }
            String normalDate = null;

            long dutyStime = 0;
            long dutyEtime = 0;
            if (registerType == 1) {
                dutyStime = on_duty_start_time;
                dutyEtime = on_duty_end_time;
            } else if (registerType == 2) {
                dutyStime = off_duty_start_time;
                dutyEtime = off_duty_end_time;
            }
            if (dutyStime > 0 && dutyEtime > 0) {
                if (registerType == 1) {
                    normalDate = DateUtil.convertMinuteToTime(shiftdef.doGetOnDutyStartTimeForView()) + "-" + DateUtil.convertMinuteToTime(shiftdef.getOnDutyEndTime());
                    if (normalDate.equals("00:00"))
                        normalDate = "";
                } else if (registerType == 2) {
                    normalDate = DateUtil.convertMinuteToTime(shiftdef.getOffDutyStartTime())
                            + "-" + DateUtil.convertMinuteToTime(shiftdef.getOffDutyEndTime());
                    if (normalDate.equals("00:00"))
                        normalDate = "";
                }
            }
            register.setNormalDate(normalDate);

            //打卡异常类型
            List<MobileEnum.RegisterErrorType> errorTypeList = new ArrayList<>();

            //如果分析的打卡数据已经分析出地点异常或者设备异常，则保留地点、设备异常信息
            if (register.getResultType() != null && register.getResultType() == 2 && StringUtils.isNotBlank(register.getResultDesc())) {
                if (BooleanUtils.isTrue(localErrorCheck) && register.getResultDesc().contains(MobileEnum.RegisterErrorType.LOCAL_ERR.toString())) {
                    errorTypeList.add(MobileEnum.RegisterErrorType.LOCAL_ERR);
                }
                if (BooleanUtils.isTrue(deviceErrorCheck) && register.getResultDesc().contains(MobileEnum.RegisterErrorType.DEVICE_ERR.toString())) {
                    errorTypeList.add(MobileEnum.RegisterErrorType.DEVICE_ERR);
                }
            }

            // 只有工作日才校验时间是否异常
            if (dateType == 1) {
                //判断是否校验时间异常
                Boolean checkTimeError = checkRegTimeError(register, dto, belongDate);
                if (checkTimeError) {
                    Boolean isFlexible = false;
                    // 如启用了弹性班次，则按弹性班次计算
                    if (BooleanUtils.isTrue(shiftdef.getIsFlexibleWork()) && shiftdef.getFlexibleWorkType() == 1) {
                        isFlexible = true;
                    }
                    if (registerType == 1) {
                        // CLOUD-3822 导入签到数据增加弹性判断
                        //弹性班次，超出“设置打卡时间范围”的签到签退、并且超过弹性上班结束时间的签到、早于弹性下班时间的签退都为异常。
                        if (isFlexible) {// 如启用了弹性班次，则按弹性班次计算
                            Long flexibleDutyEndTime = belongDate + shiftdef.getFlexibleOnDutyEndTime() * 60;
                            if (register.getRegDateTime() > flexibleDutyEndTime) {
                                errorTypeList.add(MobileEnum.RegisterErrorType.TIME_ERR);
                            }
                        } else {
                            if (timeCheckType != null && timeCheckType == 2) {
                                if (regTime > dutyEtime) {
                                    errorTypeList.add(MobileEnum.RegisterErrorType.TIME_ERR);
                                }
                            } else {
                                if (!(regTime >= dutyStime && regTime <= dutyEtime)) {
                                    errorTypeList.add(MobileEnum.RegisterErrorType.TIME_ERR);
                                }
                            }
                        }
                    } else if (registerType == 2) {
                        // 签退 如果不在签退打卡范围内，则说明是异常了
                        if (isFlexible) {
                            Long flexibleOffStartTime = belongDate + shiftdef.getFlexibleOffDutyStartTime() * 60;
                            if (register.getRegDateTime() < flexibleOffStartTime) {
                                errorTypeList.add(MobileEnum.RegisterErrorType.TIME_ERR);
                            }
                        } else {
                            if (timeCheckType != null && timeCheckType == 2) {
                                if (regTime < dutyStime) {
                                    errorTypeList.add(MobileEnum.RegisterErrorType.TIME_ERR);
                                }
                            } else {
                                if (!(regTime >= dutyStime && regTime <= dutyEtime)) {
                                    errorTypeList.add(MobileEnum.RegisterErrorType.TIME_ERR);
                                }
                            }
                        }
                    }
                }

            } else if (dateType == 2) {
                register.setReason("休息日");
            } else if (dateType == 3) {
                register.setReason("法定节假日");
            } else if (dateType == 4) {
                register.setReason("公司特殊假日");
            }

            if (errorTypeList.size() > 0) {
                //签到异常
                register.setResultType(2);
                register.setResultDesc(StringUtils.join(errorTypeList, ","));
            } else {
                register.setResultType(1);
                register.setResultDesc(null);
            }
        }
    }

    private Boolean checkRegTimeError(WaRegisterRecord register, WaAnalyzDTO dto, Long belongDate) {
        // 如果开启了不计算迟到早退，则不分析出异常 CLOUD-1390
        WaAnalyzInfo analyzInfo = dto.getEmpWaAnalyz(register.getEmpid(), belongDate);
        if (analyzInfo != null && (analyzInfo.getIs_analyze_late_early() == null || !analyzInfo.getIs_analyze_late_early())) {
            return false;
        } else {
            return true;
        }
    }
}
