package com.caidaocloud.attendance.service.application.enums;

public enum WorkHourEnum {
    STANDARD(0, "标准工时"),
    COMPREHENSIVE(1, "综合工时");

    private Integer index;
    private String name;

    public static String getName(int index) {
        for (WorkHourEnum c : WorkHourEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    WorkHourEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
