package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveQuotaDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/2
 */
public interface IQuotaTypeService {
    void saveOrUpdateLeaveQuota(LeaveQuotaDto leaveQuotaDto);

    void deleteLeaveQuota(Integer id);

    LeaveQuotaDto getLeaveQuotaById(Integer id);

    void updateQuotaSort(Integer id, Integer sort);

    List<Map> getLeaveSettingList(PageBean pageBean, Integer leaveTypeId);

    AttendancePageResult<LeaveQuotaDto> getLeaveQuotaPageList(AttendanceBasePage basePage, Integer leaveTypeId);
}
