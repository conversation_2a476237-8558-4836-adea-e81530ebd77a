package com.caidao1.integrate.service;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.mybatis.ReflectUtil;
import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.integrate.mybatis.mapper.*;
import com.caidao1.integrate.mybatis.model.*;
import com.caidao1.integrate.trigger.DataInputJob;
import com.caidao1.integrate.trigger.DataOutputJob;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.integrate.listener.RedisMessage;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.endpoint.dynamic.DynamicClientFactory;
import org.apache.cxf.transport.http.HTTPConduit;
import org.apache.cxf.transports.http.configuration.HTTPClientPolicy;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by darren on 16/4/26.
 */
@Slf4j
@Service
public class IntegratedService {

    @Autowired
    private IntegrateMapper integrateMapper;
    @Autowired
    private SysDataInputMapper dataInputMapper;
    @Autowired
    private SysDataOutputMapper dataOutputMapper;
    @Autowired
    private SchedulerFactoryBean quartzSf;
    @Autowired
    private LogDataInputMapper logDataInputMapper;
    @Autowired
    private LogDataOutputMapper logDataOutputMapper;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private IntegratedLogicService integrateLogicService;
    @Autowired
    private ISessionService sessionService;

    /**
     * 清空日志
     *
     * @param id
     * @throws Exception
     */
    public void clearInputLog(Integer id) throws Exception {
        LogDataInputExample example = new LogDataInputExample();
        example.createCriteria().andSysDataInputIdEqualTo(id);
        logDataInputMapper.deleteByExample(example);
    }

    /**
     * 清空日志
     *
     * @param id
     * @throws Exception
     */
    public void clearOutputLog(Integer id) throws Exception {
        LogDataOutputExample example = new LogDataOutputExample();
        example.createCriteria().andSysDataOutputIdEqualTo(id);
        logDataOutputMapper.deleteByExample(example);
    }


    /**
     * 查询数据接入
     *
     * @param pageBean
     * @return
     */
    public List<Map> getDataInputList(PageBean pageBean) {
        UserInfo userInfo = sessionService.getUserInfo();
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("filter", pageBean.getFilter());
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString("note.desc"));
        return integrateMapper.getDataInputList(pageBounds, params);
    }

    /**
     * 取得数据接入
     *
     * @param id
     */
    public SysDataInput getDataInput(Integer id) {
        return dataInputMapper.selectByPrimaryKey(id);
    }

    /**
     * 删除数据接入
     *
     * @param id
     */
    @Transactional
    public void deleteDataInput(Integer id) throws Exception {
        stopDataInputJob(id);
        clearInputLog(id);
        dataInputMapper.deleteByPrimaryKey(id);
    }

    /**
     * 保存数据接入
     *
     * @param record
     * @return
     */
    @Transactional
    public Integer saveDataInput(SysDataInput record) {
        UserInfo userInfo = sessionService.getUserInfo();
        if (record.getSysDataInputId() == null) {
            record.setBelongOrgId(userInfo.getTenantId());
            record.setCrtuser(userInfo.getUserId());
            record.setCrttime(DateUtil.getCurrentTime(true));
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));
            dataInputMapper.insertSelective(record);
        } else {
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));
            dataInputMapper.updateByPrimaryKeySelective(record);
        }
        return record.getSysDataInputId();
    }

    /**
     * 查询数据输出
     *
     * @param pageBean
     * @return
     */
    public List<Map> getDataOutputList(PageBean pageBean) {
        UserInfo userInfo = sessionService.getUserInfo();
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("filter", pageBean.getFilter());
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString("note"));
        return integrateMapper.getDataOutputList(pageBounds, params);
    }

    /**
     * 取得数据输出
     *
     * @param id
     */
    public SysDataOutput getDataOutput(Integer id) {
        return dataOutputMapper.selectByPrimaryKey(id);
    }

    /**
     * 删除数据输出
     *
     * @param id
     */
    @Transactional
    public void deleteDataOutput(Integer id) throws Exception {
        stopDataOutputJob(id);
        clearOutputLog(id);
        dataOutputMapper.deleteByPrimaryKey(id);
    }

    /**
     * 保存数据输出
     *
     * @param record
     * @return
     */
    @Transactional
    public Integer saveDataOutput(SysDataOutput record) {
        UserInfo userInfo = sessionService.getUserInfo();
        if (record.getSysDataOutputId() == null) {
            record.setBelongOrgId(userInfo.getTenantId());
            record.setCrtuser(userInfo.getUserId());
            record.setCrttime(DateUtil.getCurrentTime(true));
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));
            dataOutputMapper.insertSelective(record);
        } else {
            record.setUpduser(userInfo.getUserId());
            dataOutputMapper.updateByPrimaryKeySelective(record);
        }
        return record.getSysDataOutputId();
    }

    /**
     * 启动定时器
     *
     * @param id
     * @throws Exception
     */
    @Transactional
    public void startDataInputJob(Integer id) throws Exception {
        UserInfo userInfo = sessionService.getUserInfo();
        SysDataInput dataInput = dataInputMapper.selectByPrimaryKey(id);
        JobDetail jobDetail = JobBuilder.newJob(DataInputJob.class).withIdentity("dataInputJob" + id, "dataInputJobGroup").build();
        JobDataMap dataMap = jobDetail.getJobDataMap();
        dataMap.put("dataInputId", id.toString());
        dataMap.put("corpId", userInfo.getTenantId());
        dataMap.put("belongId", userInfo.getTenantId());
        CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity("dataInputTrigger" + id, "dataInputTriggerGroup").withSchedule(CronScheduleBuilder.cronSchedule(dataInput.getTrigger())).build();
        Scheduler scheduleruler = quartzSf.getScheduler();
        scheduleruler.scheduleJob(jobDetail, trigger);
        scheduleruler.start();
        dataInput.setStatus(1);
        dataInputMapper.updateByPrimaryKey(dataInput);
    }

    /**
     * 停止定时器
     *
     * @param id
     * @throws Exception
     */
    @Transactional
    public void stopDataInputJob(Integer id) throws Exception {
        Scheduler scheduler = quartzSf.getScheduler();
        scheduler.pauseTrigger(new TriggerKey("dataInputTrigger" + id, "dataInputTriggerGroup"));// 停止触发器
        scheduler.unscheduleJob(new TriggerKey("dataInputTrigger" + id, "dataInputTriggerGroup"));// 移除触发器
        scheduler.deleteJob(new JobKey("dataInputJob" + id, "dataInputJobGroup"));// 删除任务
        SysDataInput dataInput = dataInputMapper.selectByPrimaryKey(id);
        dataInput.setStatus(2);
        dataInputMapper.updateByPrimaryKey(dataInput);
    }

    @SuppressWarnings("unchecked")
    public Object doRefer(String address, String method) throws Exception {
        DynamicClientFactory factory = DynamicClientFactory.newInstance();
        Client proxy = factory.createClient(address);
        HTTPConduit conduit = (HTTPConduit) proxy.getConduit();
        HTTPClientPolicy policy = new HTTPClientPolicy();
        policy.setConnectionTimeout(30000);
        policy.setReceiveTimeout(30000);
        conduit.setClient(policy);
        Object result = proxy.invoke(method, "2016-06-01", "2016-06-01")[0];
        List itemList = ReflectUtil.getFieldValue(result, "item");
        return itemList;
    }

    /**
     * 数据流入接口
     *
     * @param id
     * @throws Exception
     */
    public void syncDataInputByRange(Integer id, Long corpId, String belongId, boolean clearCache, Long start, Long end) throws Exception {
        if (id != null && id == 3) {
            return;
        }
        LogDataInput dataInputLog = new LogDataInput();
        dataInputLog.setStatus("success");
        SysDataInput dataInput = dataInputMapper.selectByPrimaryKey(id);
        for (Long now = start; now <= end; now += 86400) {
            try {
                log.info(dataInput.getNote() + "开始调用" + DateUtil.getDateStrByTimesamp(now));
                dataInput.setWorkTime(now);
                dataInputMapper.updateByPrimaryKeySelective(dataInput);
                integrateLogicService.execDataInput(dataInputLog, dataInput, corpId, belongId, clearCache, null);
                log.info(dataInput.getNote() + "调用完成");
            } catch (Exception e) {
                e.printStackTrace();
                dataInputLog.setStatus("error");
                dataInputLog.setEndTime(DateUtil.getCurrentTime(true));
                dataInputLog.setLogList("接口发生异常:" + e.getMessage());
                try {
                    if (StringUtils.isNotEmpty(dataInput.getErrorTrigger())) {
                        Map<String, Object> params = new HashMap<String, Object>();
                        params.put("belongId", belongId);
                        params.put("message", e.getMessage());
                        groovyScriptEngine.execute(dataInput.getErrorTrigger(), params);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                throw new Exception(e);
            } finally {
                dataInputLog.setSysDataInputId(dataInput.getSysDataInputId());
                dataInputLog.setBelongOrgId(belongId);
                dataInputLog.setName(dataInput.getNote());
                logDataInputMapper.insertSelective(dataInputLog);
            }
        }
    }

    /**
     * 数据流入接口
     *
     * @param id
     * @throws Exception
     */
    public synchronized void syncDataInput(Integer id, Long corpId, String belongId, boolean clearCache, Map exparams) throws Exception {
        if (id != null && id == 3) {
            return;
        }
        log.info("syncDataInput start ...");
        LogDataInput dataInputLog = new LogDataInput();
        dataInputLog.setStatus("success");
        SysDataInput dataInput = null;
        try {
            if (null == id) {
                log.error("syncDataInput sysDataInputId is null");
                return;
            }

            dataInput = dataInputMapper.selectByPrimaryKey(id);
            LogRecordContext.putVariable("name", dataInput.getNote());
            if (null == dataInput) {
                log.error("syncDataInput dataInput is null");
                return;
            }

            log.info(dataInput.getNote() + "开始调用");

            if (exparams == null) {
                exparams = new HashMap();
            }

            integrateLogicService.execDataInput(dataInputLog, dataInput, corpId, belongId, clearCache, exparams);

            log.info(dataInput.getNote() + "调用完成");
        } catch (Exception e) {
            log.error("执行数据接入任务异常，{}", e.getMessage(), e);
            dataInputLog.setStatus("error");
            dataInputLog.setEndTime(DateUtil.getCurrentTime(true));
            dataInputLog.setLogList("接口发生异常:" + e.getMessage());
            try {
                if (null != dataInput && StringUtils.isNotEmpty(dataInput.getErrorTrigger())) {
                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put("belongId", belongId);
                    params.put("message", e.getMessage());
                    log.info("groovyScriptEngine.execute start... belongId={}", belongId);
                    groovyScriptEngine.execute(dataInput.getErrorTrigger(), params);
                }
            } catch (Exception ex) {
                log.error("执行数据接入，任务异常后置处理器执行异常，{}", e.getMessage(), e);
            }
            throw new Exception(e);
        } finally {
            log.info("syncDataInput end ... dataInput is null ? [{}]", null == dataInput);
            try {
                if (null != dataInput) {
                    dataInputLog.setSysDataInputId(dataInput.getSysDataInputId());
                    dataInputLog.setBelongOrgId(belongId);
                    dataInputLog.setName(dataInput.getNote());
                    logDataInputMapper.insertSelective(dataInputLog);
                }
            } catch (Exception e) {
                log.error("logDataInputMapper.insertSelective(dataInputLog) err,{}", e.getMessage(), e);
            }
        }
    }

    /**
     * 启动定时器
     *
     * @param id
     * @throws Exception
     */
    @Transactional
    public void startDataOutputJob(Integer id) throws Exception {
        UserInfo userInfo = sessionService.getUserInfo();
        SysDataOutput dataOutput = dataOutputMapper.selectByPrimaryKey(id);
        JobDetail jobDetail = JobBuilder.newJob(DataOutputJob.class).withIdentity("dataOutputJob" + id, "dataOutputJobGroup").build();
        JobDataMap dataMap = jobDetail.getJobDataMap();
        dataMap.put("dataOutputId", id);
        dataMap.put("corpId", userInfo.getCorpid());
        dataMap.put("belongId", userInfo.getTenantId());
        CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity("dataOutputTrigger" + id, "dataOutputTriggerGroup").withSchedule(CronScheduleBuilder.cronSchedule(dataOutput.getTrigger())).build();
        Scheduler scheduleruler = quartzSf.getScheduler();
        scheduleruler.scheduleJob(jobDetail, trigger);
        scheduleruler.start();
        dataOutput.setStatus(1);
        dataOutputMapper.updateByPrimaryKey(dataOutput);
    }

    /**
     * 停止定时器
     *
     * @param id
     * @throws Exception
     */
    public void stopDataOutputJob(Integer id) throws Exception {
        Scheduler scheduler = quartzSf.getScheduler();
        scheduler.pauseTrigger(new TriggerKey("dataOutputTrigger" + id, "dataOutputTriggerGroup"));// 停止触发器
        scheduler.unscheduleJob(new TriggerKey("dataOutputTrigger" + id, "dataOutputTriggerGroup"));// 移除触发器
        scheduler.deleteJob(new JobKey("dataOutputJob" + id, "dataOutputJobGroup"));// 删除任务
        SysDataOutput dataOutput = dataOutputMapper.selectByPrimaryKey(id);
        dataOutput.setStatus(2);
        dataOutputMapper.updateByPrimaryKey(dataOutput);
    }

    /**
     * 数据流出接口
     *
     * @param id
     * @throws Exception
     */
    public synchronized void syncDataOutput(Integer id, Long corpId, String belongId) throws Exception {
        syncDataOutput(id, corpId, belongId, null);
    }

    /**
     * 数据输出接口
     *
     * @param id
     * @throws Exception
     */
    public void syncDataOutput(Integer id, Long corpId, String belongId, Map<String, Object> outputParams) throws Exception {
        LogDataOutput integrateLog = new LogDataOutput();
        SysDataOutput dataOutput = dataOutputMapper.selectByPrimaryKey(id);
        try {
            log.info(dataOutput.getNote() + "开始调用");
            integrateLogicService.execDataOutput(id, corpId, belongId, outputParams, integrateLog);
            log.info(dataOutput.getNote() + "调用完成");
        } catch (Exception e) {
            e.printStackTrace();
//            integrateLog.setStatus("error");
            integrateLog.setEndTime(DateUtil.getCurrentTime(true));
            integrateLog.setLogList("接口发生异常:" + e.getMessage());
            try {
                if (StringUtils.isNotEmpty(dataOutput.getErrorTrigger())) {
                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put("belongId", belongId);
                    params.put("message", e.getMessage());
                    groovyScriptEngine.execute(dataOutput.getErrorTrigger(), params);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            throw new Exception(e);
        } finally {
            integrateLog.setSysDataOutputId(dataOutput.getSysDataOutputId());
            integrateLog.setBelongOrgId(belongId);
            integrateLog.setName(dataOutput.getNote());
            logDataOutputMapper.insertSelective(integrateLog);
        }
    }

    /**
     * 执行接口
     *
     * @return
     */
    public boolean handleOutputMessage(RedisMessage message) {
        boolean result = true;
        try {
            if (message.getBelongId() == null || message.getMsg() == null) return false;
            SysDataOutputExample example = new SysDataOutputExample();
            example.createCriteria().andBelongOrgIdEqualTo(message.getBelongId()).andTriggerEqualTo(message.getMsg());
            List<SysDataOutput> dataOutputList = dataOutputMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(dataOutputList)) {
                SysDataOutput dataOutput = dataOutputList.get(0);
                Map<String, Object> params = message.getParams();
                if (params == null) {
                    params = new HashMap<>();
                }
                if (params.containsKey("ids")) {
                    List<Integer> ids = (List<Integer>) params.get("ids");
                    params.put(":KEY", "ANY('{" + StringUtils.join(ids, ",") + "}')");
                } else {
                    params.put(":KEY", message.getId());
                }
                syncDataOutput(dataOutput.getSysDataOutputId(), message.getCorpId(), message.getBelongId(), params);
            } else {
                result = false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 执行接口
     *
     * @return
     */
    public Map handleInputMessage(Long corpId, String belongOrgId, Map params, String triggerName) {
        Map resultMap = new HashMap();
        resultMap.put("status", 0);
        resultMap.put("message", "success");
        try {
            SysDataInputExample example = new SysDataInputExample();
            example.createCriteria().andBelongOrgIdEqualTo(belongOrgId).andTriggerEqualTo(triggerName);
            List<SysDataInput> dataOutputList = dataInputMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(dataOutputList)) {
                SysDataInput dataOutput = dataOutputList.get(0);
                syncDataInput(dataOutput.getSysDataInputId(), corpId, belongOrgId, true, params);
            }
        } catch (Exception e) {
            e.printStackTrace();

            resultMap.put("status", -1);
            String msg = e.getMessage();
            if (StringUtils.isEmpty(msg)) {
                msg = "系统内部异常";
            } else {
                msg = msg.substring(msg.indexOf(":") + 1);
            }
            try {
                Map errMap = JSONUtils.toMap(msg);
                if (errMap == null || errMap.get("err_msg") == null || errMap.get("err_msg").toString().equals("")) {
                    throw new Exception();
                }
                resultMap.put("message", errMap.get("err_msg"));
            } catch (Exception ex) {
                resultMap.put("message", msg);
            }
        }
        return resultMap;
    }

    /**
     * 查询日志
     *
     * @param pageBean
     * @return
     */
    public List<LogDataInput> getLogDataInputList(PageBean pageBean, Integer id) {
        UserInfo userInfo = sessionService.getUserInfo();
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("filter", pageBean.getFilter());
        params.put("id", id);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "log_data_input_id.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        return logDataInputMapper.getLogDataInputList(pageBounds, params);
    }

    /**
     * 查询日志
     *
     * @param pageBean
     * @return
     */
    public List<LogDataOutput> getLogDataOutputList(PageBean pageBean, Integer id) {
        UserInfo userInfo = sessionService.getUserInfo();
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("filter", pageBean.getFilter());
        params.put("id", id);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "log_data_output_id.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        return logDataOutputMapper.getLogDataOutputList(pageBounds, params);
    }

    public LogDataInput getLogDataInput(Integer id) {
        return logDataInputMapper.selectByPrimaryKey(id);
    }

    public void updateCheckResult(Integer belongId) {
        List<Integer> logDataInputList = logDataInputMapper.getLogDataInputListByStatus(belongId, "warning");
        for (Integer logDataInputId : logDataInputList) {
            System.out.println(logDataInputId);
            logDataInputMapper.updateCheckResultById(logDataInputId);
        }
        logDataInputMapper.updateLogList(belongId);
    }

    public LogDataOutput getLogDataOutput(Integer id) {
        return logDataOutputMapper.selectByPrimaryKey(id);
    }

    /**
     * 日志查询
     *
     * @param pageBean
     * @param query
     * @return
     */
    public List getIntegrateLogList(PageBean pageBean, Query query, String collectionName) {
//        DBCollection collection = mongoTemplate.getCollection(collectionName);
//        DBCursor cursor = collection.find(query.getQueryObject()).skip(pageBean.getPosStart()).sort(new BasicDBObject("startTime", -1)).limit(pageBean.getCount());
//        return IteratorUtils.toList(cursor.iterator());
        return null;
    }

    /**
     * 查询Total
     *
     * @param query
     * @param collectionName
     * @return
     */
    public long getCount(Query query, String collectionName) {
//        return mongoTemplate.count(query, collectionName);
        return 0;
    }
}