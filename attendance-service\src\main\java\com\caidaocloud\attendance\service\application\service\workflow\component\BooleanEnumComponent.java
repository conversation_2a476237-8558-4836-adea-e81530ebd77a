package com.caidaocloud.attendance.service.application.service.workflow.component;

import com.caidaocloud.workflow.annotation.WfComponentValueEnumDef;
import com.caidaocloud.workflow.dto.WfComponentValueDto;
import com.google.common.collect.Lists;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class BooleanEnumComponent extends WfComponentValueEnumDef {
    @NotNull
    @Override
    public List<WfComponentValueDto> enumList() {
        return Lists.newArrayList(new WfComponentValueDto("是", "true"),
                new WfComponentValueDto("否", "false")
        );
    }
}