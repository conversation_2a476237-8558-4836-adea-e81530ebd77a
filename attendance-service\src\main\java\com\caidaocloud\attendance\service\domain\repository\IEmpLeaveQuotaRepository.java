package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.service.domain.entity.LeaveQuotaDo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;

public interface IEmpLeaveQuotaRepository {

    List<LeaveQuotaDo> getLeaveQuotaList(String belongOrgId,Long empId,Long curDate);

    List<LeaveQuotaDo> getAnnualLeaveList(String belongOrgId,Long empId,Integer leaveTypeId, Long configId, Integer quotaType,Long today, Boolean mergeCarry);

    List<LeaveQuotaDo> getCompensatoryQuotaList(String belongOrgId, Long empId, Long curDate, Integer leaveTypeId, Long configId, Boolean onlyAutoDataSource);

    PageList<LeaveQuotaDo> getInvalidCompensatory(AttendanceBasePage page, String belongOrgId, Long empId, Long curDate);

    List<LeaveQuotaDo> getInvalidCompensatoryQuotaList(String belongOrgId, Long empId, Long curDate, Integer leaveTypeId, Boolean onlyAutoDataSource);
}
