package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.application.dto.clock.ClockAnalyseDataCacheDto;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 打卡分析服务
 */
public interface IClockAnalyseService {
    ConcurrentHashMap<String, IClockAnalyseService> versionTypeManager = new ConcurrentHashMap<>();

    @PostConstruct
    default void register() {
        String type = getVersionType();
        versionTypeManager.put(type, this);
    }

    String getVersionType();

    List<WaRegisterRecordDo> getRegisterRecordList(String belongOrgId,
                                                   List<Long> empIds,
                                                   Long startDate,
                                                   Long endDate,
                                                   Integer type);

    void analyseRegisterRecord(String belongOrgId,
                               List<Long> empIds,
                               Long date,
                               Integer type,
                               ClockAnalyseDataCacheDto dataCacheDto);

    List<Long> getEmpIdListByOt(Map<String, WaShiftDo> empShiftDoMap);
}
