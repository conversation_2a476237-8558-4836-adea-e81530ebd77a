package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveCancelInfoDo;

import java.util.List;

public interface IWaEmpLeaveCancelInfoRepository {
    Integer checkTimeRepeat(Long empId, Integer leaveId, Long startTime, Long endTime);

    void save(WaEmpLeaveCancelInfoDo cancelInfo);

    List<WaEmpLeaveCancelInfoDo> getLeaveCancelInfoList(Long cancelLeaveId);


    List<WaEmpLeaveCancelInfoDo> getListByLeaveCancelId(List<Long> leaveCancelIds);
}
