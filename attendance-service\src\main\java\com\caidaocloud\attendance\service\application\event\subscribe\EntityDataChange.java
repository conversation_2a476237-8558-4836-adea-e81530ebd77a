package com.caidaocloud.attendance.service.application.event.subscribe;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import lombok.Data;

/**
 * 增量数据变更消息体
 */
@Data
public class EntityDataChange {
    /**
     * 变更前数据
     */
    private EntityDataDto before;
    /**
     * 变更后数据
     */
    private EntityDataDto after;

    public EntityDataChange() {
    }

    public EntityDataChange(EntityDataDto before, EntityDataDto after) {
        this.before = before;
        this.after = after;
    }

}
