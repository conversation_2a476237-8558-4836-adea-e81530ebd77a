package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

public enum DayHalfTypeEnum {
    A("上半天", AttendanceCodes.A_HALF_DAY),
    P("下半天", AttendanceCodes.P_HALF_DAY);

    private String desc;
    private Integer code;

    DayHalfTypeEnum(String desc, Integer code) {
        this.desc = desc;
        this.code = code;
    }

    public static String getDesc(String name) {
        for (DayHalfTypeEnum c : DayHalfTypeEnum.values()) {
            if (c.name().equals(name)) {
                String msg = "";
                try {
                    msg = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(msg)) {
                    return msg;
                }
                return c.desc;
            }
        }
        return "";
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
