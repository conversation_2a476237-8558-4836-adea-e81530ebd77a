package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.domain.repository.IWorkRoundRepository;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/1
 */
@Slf4j
@Data
@Service
public class WaWorkRoundDo {
    private Integer workRoundId;
    private String roundName;
    private String belongOrgid;
    private Integer defaultShiftDefId;
    private Long crtuser;
    private Long crttime;
    private String i18nRoundName;

    @Autowired
    private IWorkRoundRepository workRoundRepository;

    public WaWorkRoundDo selectById(Integer id) {
        return workRoundRepository.selectById(id);
    }

    public int save(WaWorkRoundDo wrDo) {
        return workRoundRepository.save(wrDo);
    }

    public int updateById(WaWorkRoundDo workRoundDo) {
        return workRoundRepository.updateById(workRoundDo);
    }

    public int deleteById(Integer id) {
        return workRoundRepository.deleteById(id);
    }

    public List<WaWorkRoundDo> getWorkRoundListByIds(List<Integer> ids) {
        return workRoundRepository.getWorkRoundListByIds(ids);
    }

    public int getRoundCountCountByName(String belongOrgid, Integer excludeId, String name) {
        return workRoundRepository.getRoundCountCountByName(belongOrgid, excludeId, name);
    }

    public PageList<WaWorkRoundDo> getWorkRoundPageList(PageBean pageBean, String belongId) {
        return workRoundRepository.getWorkRoundPageList(pageBean, belongId);
    }
}
