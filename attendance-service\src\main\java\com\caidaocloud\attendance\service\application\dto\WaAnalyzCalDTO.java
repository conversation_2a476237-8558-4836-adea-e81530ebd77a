package com.caidaocloud.attendance.service.application.dto;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.wa.dto.EmpAbnormalWorkTime;
import com.caidaocloud.attendance.core.wa.dto.EmpLeaveInfo;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.WaAnalyzInfo;
import com.caidao1.wa.mybatis.model.WaLeaveCancelDayTime;
import com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDaytimeDo;
import com.caidaocloud.attendance.service.domain.entity.WaParseGroupDo;
import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaOvertimeTransferRulePo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class WaAnalyzCalDTO {
    //补打卡信息
    @Getter
    @Setter
    private Map<Integer, Integer> fillColckMap;
    @Getter
    @Setter
    private Boolean job;
    //班次信息
    private Map<Integer, WaShiftDef> shiftDefMap;
    //公司所有班次信息
    private Map<Integer, WaShiftDef> corpShiftDefMap;
    private Integer clockType;
    // 每个员工所属的考勤分组对应的考勤分析分组
    private List<WaAnalyzInfo> empAnalyzInfos;
    // 默认考勤分组对应的考勤分析分组对象
    private WaAnalyzInfo defaultAnalyz;
    //每个员工所对应的考勤分组及考勤分析分析分组
    private Map<Long, List<WaAnalyzInfo>> empAnalyzMaps = new HashMap<>();
    @Getter
    @Setter
    private Map<Long, List<WaParseGroupDo>> empWaParseGroupDoMap;

    private WaSob waSob;

    private String belongid;

    private Long corpid;

    private Long userId;

    private Map<Integer, WaLeaveType> ltMaps = new HashMap<>();
    private Map<Integer, WaOvertimeType> otMaps = new HashMap<>();
    //加班转换规则
    @Setter
    @Getter
    private Map<Long, WaOvertimeTransferRulePo> otTransferRuleMaps = new HashMap<>();
    public WaOvertimeTransferRulePo getOtTransferRule(Long ruleId) {
        if (null == ruleId) {
            return null;
        }
        if (this.otTransferRuleMaps == null) {
            return null;
        }
        return this.otTransferRuleMaps.get(ruleId);
    }

    //出差类型
    private Map<Long, WaTravelTypeDo> travelTypeMaps;


    private Map<String, EmpShiftInfo> empShift = new HashMap<>();

    private Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();

    //异常申诉工时-缺工时补请数据
    private Map<String, EmpAbnormalWorkTime> empAbnormalWorkTimeMap;

    private List<WaAnalyze> waAnalyzeList;

    //实际加班时长
    private Map<String, Float> relOtTimeDurationMap;

    //转换时长
    @Getter
    @Setter
    private Map<String, String> transferMap;

    //晚走晚到豁免时长
    @Getter
    @Setter
    private Map<Long, Map<String, Double>> exemptDurationMaps = new HashMap<>();

    private Map<Integer, Map<String, WaOvertimeType>> waGroupOtRuleMap;

    //员工外勤签到记录（有打卡地点的）
    private Map<String, List<WaRegisterRecord>> empOutRegMap;

    private Map<Long, SysEmpInfo> empInfoMap;

    // 打卡记录（记录一次卡&不打卡员工打卡记录）
    private Map<String, List<Map>> registerRecordMap;

    //一次卡员工打卡记录
    private Map<String, List<WaRegisterRecord>> signOnceRegisterRecordMap;

    //不打卡员工打卡记录
    private Map<String, List<WaRegisterRecord>> signZeroRegisterRecordMap;

    //记录员工所有类型的打卡记录
    private Map<String, List<WaRegisterRecord>> empBelongDateRegListMap;

    @Getter
    @Setter
    private Map<String, List<WaLeaveCancelDayTime>> leaveCancelMap = new HashMap<>();

    @Getter
    @Setter
    private Map<String, Float> travelDurationMap = new HashMap<>();

    public Map<String, List<Map>> getRegisterRecordMap() {
        return registerRecordMap;
    }

    public void setRegisterRecordMap(Map<String, List<Map>> registerRecordMap) {
        this.registerRecordMap = registerRecordMap;
    }

    public Map<String, List<WaRegisterRecord>> getEmpOutRegMap() {
        return empOutRegMap;
    }

    public void setEmpOutRegMap(Map<String, List<WaRegisterRecord>> empOutRegMap) {
        this.empOutRegMap = empOutRegMap;
    }

    public List<WaRegisterRecord> getEmpOutRegByDateEmpId(Long empid, Long belongDate) {
        if (this.empOutRegMap == null) {
            return null;
        }
        return this.empOutRegMap.get(empid + "_" + belongDate);
    }

    //缺少外勤打卡 or 外勤打卡地址异常 的请假数据
    private List<WaEmpLeave> invalidLeaves = null;

    public List<WaEmpLeave> getInvalidLeaves() {
        return invalidLeaves;
    }

    public void setInvalidLeaves(List<WaEmpLeave> invalidLeaves) {
        this.invalidLeaves = invalidLeaves;
    }

    public Map<String, Float> getRelOtTimeDurationMap() {
        return relOtTimeDurationMap;
    }

    public void setRelOtTimeDurationMap(Map<String, Float> relOtTimeDurationMap) {
        this.relOtTimeDurationMap = relOtTimeDurationMap;
    }

    public List<WaAnalyze> getWaAnalyzeList() {
        return waAnalyzeList;
    }

    //出差记录
    @Getter
    @Setter
    private List<WaEmpTravelDaytimeDo> empTravelInfoList;
    private Map<String, List<WaEmpTravelDaytimeDo>> empTravelMap;

    public void setEmpTravelInfo(List<WaEmpTravelDaytimeDo> empTravels) {
        if (CollectionUtils.isNotEmpty(empTravels)) {
            this.empTravelInfoList = empTravels;
            this.empTravelMap = empTravels.stream().collect(Collectors.groupingBy(o -> String.join("-", String.valueOf(o.getEmpId()), String.valueOf(o.getTravelDate()))));
        } else {
            this.empTravelMap = new HashMap<>();
        }
    }

    public List<WaEmpTravelDaytimeDo> getEmpTravelInfoByDateEmpId(Long empid, Long belongDate) {
        if (empTravelMap == null) {
            return null;
        }
        return empTravelMap.get(empid + "_" + belongDate);
    }

    // 请假记录
    @Getter
    private List<EmpLeaveInfo> empLeaveInfoList;

    private Map<String, List<EmpLeaveInfo>> empLeaveMap;

    // 获取员工的请假记录
    public List<EmpLeaveInfo> getEmpLeaveInfoByDateEmpId(Long empid, Long belongDate) {
        if (empLeaveMap == null) {
            return null;
        }
        return empLeaveMap.get(empid + "_" + belongDate);
    }

    /**
     * @param empLeaves
     */
    public void setEmpLeaveInfo(List<Map> empLeaves) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        empLeaveMap = new HashMap<>();

        List<EmpLeaveInfo> list = new ArrayList<>();
        EmpLeaveInfo lf = null;
        String key = null;
        String json = null;
        for (Map leavemap : empLeaves) {
            lf = new EmpLeaveInfo();
            try {
                json = objectMapper.writeValueAsString(leavemap);
                lf = objectMapper.readValue(json, new TypeReference<EmpLeaveInfo>() {
                });
                list.add(lf);
                // 按员工+日期归类
                Long belongdata = (lf.getLeave_date() - (lf.getLeave_date() + 28800) % 86400);
                key = lf.getEmpid() + "_" + belongdata;
                if (empLeaveMap.containsKey(key)) {
                    empLeaveMap.get(key).add(lf);
                } else {
                    List<EmpLeaveInfo> lts = new ArrayList<>();
                    lts.add(lf);
                    empLeaveMap.put(key, lts);
                }
            } catch (Exception e) {
                log.error("setEmpLeaveInfo err,{}", e.getMessage(), e);
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            this.empLeaveInfoList = list;
        }
    }

    public void setWaAnalyzeList(List<WaAnalyze> waAnalyzeList) {
        this.waAnalyzeList = waAnalyzeList;
    }

    public void setEmpShiftInfoByDateMap(Map<String, EmpShiftInfo> empShiftInfoByDateMap) {
        this.empShiftInfoByDateMap = empShiftInfoByDateMap;
    }

    public Map<String, EmpShiftInfo> getEmpShiftInfoByDateMap() {
        if (null == this.empShiftInfoByDateMap) {
            return Collections.emptyMap();
        }
        return this.empShiftInfoByDateMap;
    }

    /**
     * @param empShift
     */
    public void setEmpShift(Map<String, EmpShiftInfo> empShift) {
        this.empShift = empShift;
    }

    public Map<String, EmpShiftInfo> getEmpShift() {
        return this.empShift;
    }

    public EmpShiftInfo getEmpShift(Long empid, Integer shiftDefId, Long workDate) {
        String key = empid + "_" + shiftDefId + "_" + workDate;
        return this.empShift.get(key);
    }

    /**
     * 传递empid和日期获得排班
     *
     * @param empid
     * @param workDate
     * @return
     */
    public EmpShiftInfo getEmpShiftByDate(Long empid, Long workDate) {
        String key = empid + "_" + workDate;
        EmpShiftInfo shiftInfo = this.empShiftInfoByDateMap.get(key);
        return shiftInfo;
    }

    /**
     * 传递empid和日期获得
     *
     * @param empid
     * @return
     */
    public WaAnalyze getEmpAnalzyeByDate(Long empid, Long belongData) {
        if (CollectionUtils.isNotEmpty(this.getWaAnalyzeList())) {
            for (WaAnalyze analyze : this.getWaAnalyzeList()) {
                if (analyze.getEmpid().toString().equals(empid.toString()) && analyze.getBelongDate().toString().equals(belongData) ){
                    return analyze;
                }
            }
        }
        return null;
    }

    public Map<Integer, WaLeaveType> getLtMaps() {
        return ltMaps;
    }

    public Map<Integer, WaOvertimeType> getOtMaps() {
        return otMaps;
    }

    public String getBelongid() {
        return belongid;
    }

    public void setBelongid(String belongid) {
        this.belongid = belongid;
    }

    public Long getCorpid() {
        return corpid;
    }

    public void setCorpid(Long corpid) {
        this.corpid = corpid;
    }

    public List<WaAnalyzInfo> getEmpAnalyzInfos() {
        return empAnalyzInfos;
    }

    public void setEmpAnalyzInfos(List<WaAnalyzInfo> empAnalyzInfos) {
        if (empAnalyzInfos != null && empAnalyzInfos.size() > 0) {
            for (WaAnalyzInfo waAnalyzInfo : empAnalyzInfos) {
                Long empid = waAnalyzInfo.getEmpid();
                if (empAnalyzMaps.containsKey(empid)) {
                    empAnalyzMaps.get(empid).add(waAnalyzInfo);
                } else {
                    List<WaAnalyzInfo> list = new ArrayList<WaAnalyzInfo>();
                    list.add(waAnalyzInfo);
                    empAnalyzMaps.put(empid, list);
                }
            }
        }
        this.empAnalyzInfos = empAnalyzInfos;
    }

    public WaAnalyzInfo getDefaultAnalyz() {
        return defaultAnalyz;
    }

    public void setDefaultAnalyz(WaAnalyzInfo defaultAnalyz) {
        this.defaultAnalyz = defaultAnalyz;
    }

    public Map<Integer, WaShiftDef> getShiftDefMap() {
        return shiftDefMap;
    }

    public void setShiftDefMap(Map<Integer, WaShiftDef> shiftDefMap) {
        this.shiftDefMap = shiftDefMap;
    }

    /**
     * 根据员工id 获取员工所属的考勤分组对应的考勤分析分组如果没有对应的考勤分组，则取默认考勤分组
     *
     * @param empId
     * @param date
     * @return
     */
    public WaAnalyzInfo getEmpWaAnalyz(Long empId, Long date) {
        List<WaAnalyzInfo> infos = empAnalyzMaps.get(empId);
        if (infos != null && infos.size() > 0) {
            for (WaAnalyzInfo waAnalyzInfo : infos) {
                // 如果是按月统计时 date传null 默认取第一个考勤周期作为他的分析依据 当没有所属考勤分组时 取默认考勤分组的对应的考勤分析分组
                if (date == null) {
                    return waAnalyzInfo;
                }

                if (date >= waAnalyzInfo.getStart_time() && date <= waAnalyzInfo.getEnd_time()) {
                    if (waAnalyzInfo.getLv_parse() == null) {
                        waAnalyzInfo.setLv_parse(false);
                    }
                    if (waAnalyzInfo.getOt_parse() == null) {
                        waAnalyzInfo.setOt_parse(false);
                    }
                    log.debug("***************************************匹配到的考勤分析分组:empid=" + empId + ";date=" + date);
                    return waAnalyzInfo;
                }
            }
        }
        log.debug("***************************************获取到默认的考勤分析分组:" + defaultAnalyz);
        return defaultAnalyz;
    }

    /**
     * 根据员工id 获取员工所属的考勤分组对应的考勤分析分组如果没有对应的考勤分组，则取默认考勤分组
     *
     * @param empid
     * @param date
     * @return
     */
    public WaParseGroupDo getEmpWaParseGroupDo(Long empid, Long date) {
        if (MapUtils.isEmpty(empWaParseGroupDoMap) || !empWaParseGroupDoMap.containsKey(empid)) {
            return null;
        }
        List<WaParseGroupDo> infos = empWaParseGroupDoMap.get(empid);
        if (CollectionUtils.isNotEmpty(infos)) {
            for (WaParseGroupDo waAnalyzInfo : infos) {
                // 如果是按月统计时 date传null 默认取第一个考勤周期作为他的分析依据 当没有所属考勤分组时 取默认考勤分组的对应的考勤分析分组
                if (date == null) {
                    return waAnalyzInfo;
                }

                if (date >= waAnalyzInfo.getStartTime() && date <= waAnalyzInfo.getEndTime()) {
                    if (waAnalyzInfo.getLvParse() == null) {
                        waAnalyzInfo.setLvParse(false);
                    }
                    if (waAnalyzInfo.getOtParse() == null) {
                        waAnalyzInfo.setOtParse(false);
                    }
                    log.debug("***************************************匹配到的考勤分析分组:empid=" + empid + ";date=" + date);
                    return waAnalyzInfo;
                }
            }
        }
        return null;
    }

    public WaSob getWaSob() {
        return waSob;
    }

    public void setWaSob(WaSob waSob) {
        this.waSob = waSob;
    }

    public Map<String, EmpAbnormalWorkTime> getEmpAbnormalWorkTimeMap() {
        return empAbnormalWorkTimeMap;
    }

    public void setEmpAbnormalWorkTimeMap(Map<String, EmpAbnormalWorkTime> empAbnormalWorkTimeMap) {
        this.empAbnormalWorkTimeMap = empAbnormalWorkTimeMap;
    }

    public Map<Long, SysEmpInfo> getEmpInfoMap() {
        return empInfoMap;
    }

    public void setEmpInfoMap(Map<Long, SysEmpInfo> empInfoMap) {
        this.empInfoMap = empInfoMap;
    }

    public SysEmpInfo getEmpInfo(Long empId) {
        if (null == empId || null == this.empInfoMap) {
            return null;
        }
        if (empInfoMap.containsKey(empId)) {
            return empInfoMap.get(empId);
        }
        return null;
    }

    public Map<Integer, WaShiftDef> getCorpShiftDefMap() {
        return corpShiftDefMap;
    }

    public void setCorpShiftDefMap(Map<Integer, WaShiftDef> corpShiftDefMap) {
        this.corpShiftDefMap = corpShiftDefMap;
    }

    public Integer getClockType() {
        return clockType;
    }

    public void setClockType(Integer clockType) {
        this.clockType = clockType;
    }

    public Map<Integer, Map<String, WaOvertimeType>> getWaGroupOtRuleMap() {
        return waGroupOtRuleMap;
    }

    public void setWaGroupOtRuleMap(Map<Integer, Map<String, WaOvertimeType>> waGroupOtRuleMap) {
        this.waGroupOtRuleMap = waGroupOtRuleMap;
    }

    public Map<String, List<WaRegisterRecord>> getSignOnceRegisterRecordMap() {
        return signOnceRegisterRecordMap;
    }

    public void setSignOnceRegisterRecordMap(Map<String, List<WaRegisterRecord>> signOnceRegisterRecordMap) {
        this.signOnceRegisterRecordMap = signOnceRegisterRecordMap;
    }

    public Map<String, List<WaRegisterRecord>> getSignZeroRegisterRecordMap() {
        return signZeroRegisterRecordMap;
    }

    public void setSignZeroRegisterRecordMap(Map<String, List<WaRegisterRecord>> signZeroRegisterRecordMap) {
        this.signZeroRegisterRecordMap = signZeroRegisterRecordMap;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Map<Long, WaTravelTypeDo> getTravelTypeMaps() {
        return travelTypeMaps;
    }

    public void setTravelTypeMaps(Map<Long, WaTravelTypeDo> travelTypeMaps) {
        this.travelTypeMaps = travelTypeMaps;
    }

    public Map<String, List<WaRegisterRecord>> getEmpBelongDateRegListMap() {
        return empBelongDateRegListMap;
    }

    public void setEmpBelongDateRegListMap(Map<String, List<WaRegisterRecord>> empBelongDateRegListMap) {
        this.empBelongDateRegListMap = empBelongDateRegListMap;
    }

    public List<WaRegisterRecord> getEmpBelongDateRegListByDateEmpId(Long empid, Long belongDate) {
        if (empBelongDateRegListMap == null) {
            return null;
        }
        return empBelongDateRegListMap.get(empid + "_" + belongDate);
    }

    public void setExemptDurationMaps(Long empId, Long belongDate, Double value) {
        if (null == empId || value == null) {
            return;
        }
        if (MapUtils.isEmpty(this.exemptDurationMaps)) {
            this.exemptDurationMaps = new HashMap<>();
        }
        if (!this.exemptDurationMaps.containsKey(empId)) {
            this.exemptDurationMaps.put(empId, new HashMap<>());
        }
        Map<String, Double> durationMap = this.exemptDurationMaps.get(empId);
        String key = String.format("%s_%s", empId, belongDate);
        durationMap.put(key, value);
        this.exemptDurationMaps.put(empId, durationMap);
    }

    public Double getExemptDuration(Long empId, Long belongDate) {
        if (null == empId || MapUtils.isEmpty(this.exemptDurationMaps) || !this.exemptDurationMaps.containsKey(empId)) {
            return 0d;
        }
        Map<String, Double> durationMap = this.exemptDurationMaps.get(empId);
        String key = String.format("%s_%s", empId, belongDate);
        if (MapUtils.isEmpty(durationMap) || !durationMap.containsKey(key)) {
            return 0d;
        }
        return durationMap.get(key);
    }

    private Map<String, WaRegisterRecord> empLateRegisterRecordMap = new HashMap<>();

    public void setEmpLateRegisterRecords(Long empId, Long workDate, Integer shiftId, Integer startTime, WaRegisterRecord record) {
        if (null == empLateRegisterRecordMap || empLateRegisterRecordMap.isEmpty()) {
            empLateRegisterRecordMap = new HashMap<>();
        }
        if (null == empId || null == workDate || null == shiftId || null == startTime || null == record) {
            return;
        }
        String key = String.format("%s_%s_%s_%s", empId, workDate, shiftId, startTime);
        empLateRegisterRecordMap.put(key, record);
    }

    public WaRegisterRecord getEmpLateRegisterRecords(Long empId, Long workDate, Integer shiftId, Integer startTime) {
        String key = String.format("%s_%s_%s_%s", empId, workDate, shiftId,startTime);
        if (null == empLateRegisterRecordMap || empLateRegisterRecordMap.isEmpty()) {
            return null;
        }
        return Optional.ofNullable(empLateRegisterRecordMap.get(key)).orElse(null);
    }
}
