package com.caidaocloud.attendance.core.commons.utils;

import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Pair;
import lombok.SneakyThrows;
import lombok.val;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class TimeRangeCheckUtil {

    @SneakyThrows
    private static Long dateToLong(String date) {
        val sdf = new SimpleDateFormat("yyyyMMdd-HHmmss");
        return sdf.parse(date).getTime() / 1000;
    }

    public static class ChangeData {
        private boolean add = true;//true为请  false为销
        private long start;
        private long end;

        public static ChangeData from(boolean add, long start, long end) {
            val result = new ChangeData();
            result.add = add;
            result.end = end;
            result.start = start;
            return result;
        }

        public static ChangeData from(boolean add, String start, String end) {
            val result = new ChangeData();
            result.add = add;
            result.end = dateToLong(end);
            result.start = dateToLong(start);
            return result;
        }
    }

    public static List<Pair<Long, Long>> asks(List<ChangeData> changes) {
        List<Pair<Long, Long>> availables = Lists.list();
        for (ChangeData change : changes) {
            if (change.add) {//请假
                if (availables.isEmpty()) {
                    availables = Lists.list(Pair.pair(change.start, change.end));
                } else {
                    availables = availables.stream()
                            .map(it -> union(it, Pair.pair(change.start, change.end)))
                            .flatMap(it -> it.stream()).distinct().collect(Collectors.toList());
                }
            } else {
                availables = availables.stream()
                        .map(it -> additional(it, Pair.pair(change.start, change.end)))
                        .flatMap(it -> it.stream()).distinct().collect(Collectors.toList());
            }
        }
        return availables;
    }

    public static boolean ask(long start, long end, List<ChangeData> changes) {
        List<Pair<Long, Long>> availables = Lists.list(Pair.pair(start, end));
        for (ChangeData change : changes) {
            if (change.add) {//请假
                availables = availables.stream()
                        .map(it -> supplementary(it, Pair.pair(change.start, change.end)))
                        .flatMap(it -> it.stream()).distinct().collect(Collectors.toList());
            } else {
                if (availables.isEmpty()) {
                    availables = Lists.list(Pair.pair(change.start, change.end));
                } else {
                    availables = availables.stream()
                            .map(it -> union(it, Pair.pair(change.start, change.end)))
                            .flatMap(it -> it.stream()).distinct().collect(Collectors.toList());
                }

            }
        }
        Optional<Pair<Long, Long>> resultOp = availables.stream().filter(it -> it.getKey() <= start && it.getValue() >= start)
                .findFirst();
        if (!resultOp.isPresent()) {
            return false;
        }
        Pair<Long, Long> result = resultOp.get();
        if (result.getValue() >= end) {
            return true;
        }
        while (true) {
            result = longer(result, availables);
            if (null == result) {
                return false;
            }
            if (result.getValue() >= end) {
                return true;
            }
        }
    }

    private static Pair<Long, Long> longer(Pair<Long, Long> available, List<Pair<Long, Long>> availables) {
        val op = availables.stream().filter(it -> it.getKey() <= available.getValue() + 1 && it.getValue() > available.getValue())
                .findFirst();
        if (op.isPresent()) {
            return Pair.pair(available.first(), op.get().second());
        } else {
            return null;
        }
    }


    private static List<Pair<Long, Long>> intersection(Pair<Long, Long> a, Pair<Long, Long> b) {//交集
        long start;
        long end;
        if (a.second() < b.first()) {
            return Lists.list();
        } else if (a.first() <= b.first()) {
            if (b.second() >= a.second()) {
                return Lists.list(Pair.pair(b.first(), a.second()));
            } else {
                return Lists.list(Pair.pair(b.first(), b.second()));
            }
        } else {
            if (b.second() >= a.second()) {
                return Lists.list(Pair.pair(a.first(), a.second()));
            } else if (b.second() >= a.first()) {
                return Lists.list(Pair.pair(a.first(), b.second()));
            } else {
                return Lists.list();
            }
        }
    }

    private static List<Pair<Long, Long>> union(Pair<Long, Long> a, Pair<Long, Long> b) {//并集
        if (b.first() > a.second()) {
            return Lists.list(a, b);
        } else if (b.first() >= a.first()) {
            if (b.second() >= a.second()) {
                return Lists.list(Pair.pair(a.first(), b.second()));
            } else {
                return Lists.list(Pair.pair(a.first(), a.second()));
            }
        } else {
            if (b.second() >= a.second()) {
                return Lists.list(Pair.pair(b.first(), b.second()));
            } else if (b.second() >= a.first()) {
                return Lists.list(Pair.pair(b.first(), a.second()));
            } else {
                return Lists.list(a, b);
            }
        }
    }

    private static List<Pair<Long, Long>> supplementary(Pair<Long, Long> a, Pair<Long, Long> b) {//补集
        if (b.first() > a.second()) {
            return Lists.list(a);
        } else if (b.first() > a.first()) {
            if (b.second() >= a.second()) {
                return Lists.list(Pair.pair(a.first(), b.first() - 1));
            } else {
                return Lists.list(Pair.pair(a.first(), a.first() - 1), Pair.pair(b.second() + 1, a.second()));
            }
        } else {
            if (b.second() >= a.second()) {
                return Lists.list();
            } else if (b.second() >= a.first()) {
                return Lists.list(Pair.pair(b.second() + 1, a.second()));
            } else {
                return Lists.list(a);
            }
        }
    }

    private static List<Pair<Long, Long>> additional(Pair<Long, Long> a, Pair<Long, Long> b) {//补集
        if (b.first() > a.second()) {
            return Lists.list(a);
        } else if (b.first() > a.first()) {
            if (b.second() >= a.second()) {
                return Lists.list(Pair.pair(a.first(), b.first()));
            } else {
                return Lists.list(Pair.pair(a.first(), b.first()), Pair.pair(b.second(), a.second()));
            }
        } else {
            if (b.second() >= a.second()) {
                return Lists.list();
            } else if (b.second() >= a.first()) {
                return Lists.list(Pair.pair(b.second(), a.second()));
            } else {
                return Lists.list(a);
            }
        }
    }

    public static void main(String[] args) {
        List<ChangeData> changes = Lists.list();
        changes.add(ChangeData.from(true, "20221221-090000", "20221221-145959"));
        changes.add(ChangeData.from(false, "20221221-090000", "20221221-095959"));
        changes.add(ChangeData.from(false, "20221221-100000", "20221221-125959"));
//        changes.add(ChangeData.from(true,"20221221-090000", "20221221-115959"));
//        changes.add(ChangeData.from(true,"20221221-090000", "20221221-105959"));
        System.out.println(ask(dateToLong("20221221-090000"), dateToLong("20221221-145959"), changes));
    }
}