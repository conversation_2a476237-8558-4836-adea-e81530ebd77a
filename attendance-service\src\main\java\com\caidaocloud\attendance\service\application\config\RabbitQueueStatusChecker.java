package com.caidaocloud.attendance.service.application.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * RabbitMQ 队列状态检查器
 * 验证队列和交换机是否按照配置正确创建
 */
@Slf4j
@Component
public class RabbitQueueStatusChecker {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private AttendanceMqProperties mqProperties;

    @Autowired
    @Qualifier("startupTaskExecutor")
    private ThreadPoolTaskExecutor startupTaskExecutor;

    @PostConstruct
    public void checkQueueStatus() {
        // 检查启动任务线程池状态
        checkStartupTaskPoolHealth();
        
        // 使用启动任务线程池延迟执行，确保所有Bean都已创建
        startupTaskExecutor.submit(() -> {
            try {
                String threadName = Thread.currentThread().getName();
                log.debug("Queue status check task started - thread: {}, poolActive: {}, poolSize: {}",
                        threadName, startupTaskExecutor.getActiveCount(), startupTaskExecutor.getPoolSize());
                
                // 等待2秒确保所有Bean都已创建
                Thread.sleep(2000);
                
                // 执行队列状态检查
                performQueueStatusCheck();
                
                log.debug("Queue status check task completed - thread: {}, poolActive: {}, poolCompleted: {}",
                        threadName, startupTaskExecutor.getActiveCount(), 
                        startupTaskExecutor.getThreadPoolExecutor().getCompletedTaskCount());
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.debug("Queue status check task interrupted - thread: {}", Thread.currentThread().getName());
            } catch (Exception e) {
                log.error("ERROR: Queue status check task failed - thread: {}, poolActive: {}, error: {}", 
                        Thread.currentThread().getName(), startupTaskExecutor.getActiveCount(), e.getMessage(), e);
            }
        });
    }

    private void performQueueStatusCheck() {
        log.info("=== RabbitMQ 队列状态检查 ===");

        try {
            // 检查主队列
            checkMainQueue();

            // 检查死信相关组件
            checkDlqComponents();

            log.info("队列状态检查完成");

        } catch (Exception e) {
            log.error("队列状态检查失败: {}", e.getMessage(), e);
        }

        log.info("============================");
    }

    private void checkMainQueue() {
        try {
            Queue mainQueue = applicationContext.getBean("mainQueue", Queue.class);
            if (mainQueue != null) {
                log.info("CHECKED: 主队列已创建: {}", MultiNodeClockAnalyseRabbitConfig.MAIN_QUEUE);

                // 检查队列参数
                if (mqProperties.isEnableDLQ()) {
                    Object dlxParam = mainQueue.getArguments().get("x-dead-letter-exchange");
                    if (dlxParam != null) {
                        log.info("CHECKED: 主队列包含死信交换机参数: {}", dlxParam);
                    } else {
                        log.warn("WARN: 主队列缺少死信交换机参数");
                    }
                } else {
                    boolean hasDlxParam = mainQueue.getArguments().containsKey("x-dead-letter-exchange");
                    if (!hasDlxParam) {
                        log.info("CHECKED: 主队列不包含死信参数（符合配置）");
                    } else {
                        log.warn("WARN: 主队列包含死信参数但DLQ已禁用");
                    }
                }
            }
        } catch (Exception e) {
            log.error("ERROR: 主队列检查失败: {}", e.getMessage());
        }
    }

    private void checkDlqComponents() {
        if (mqProperties.isEnableDLQ()) {
            // 应该创建死信组件
            checkDlqExchange();
            checkDlqQueue();
            checkDlqBinding();
        } else {
            // 不应该创建死信组件
            checkDlqComponentsNotPresent();
        }
    }

    private void checkDlqExchange() {
        try {
            Exchange dlqExchange = applicationContext.getBean("dlqExchange", Exchange.class);
            if (dlqExchange != null) {
                log.info("CHECKED: 死信交换机已创建: {}", MultiNodeClockAnalyseRabbitConfig.DLQ_EXCHANGE);
            }
        } catch (Exception e) {
            log.error("ERROR: 死信交换机未找到: {}", e.getMessage());
        }
    }

    private void checkDlqQueue() {
        try {
            Queue dlqQueue = applicationContext.getBean("dlqQueue", Queue.class);
            if (dlqQueue != null) {
                log.info("CHECKED: 死信队列已创建: {}", MultiNodeClockAnalyseRabbitConfig.DLQ_QUEUE);
            }
        } catch (Exception e) {
            log.error("ERROR: 死信队列未找到: {}", e.getMessage());
        }
    }

    private void checkDlqBinding() {
        try {
            applicationContext.getBean("dlqBinding");
            log.info("CHECKED: 死信队列绑定已创建");
        } catch (Exception e) {
            log.error("ERROR: 死信队列绑定未找到: {}", e.getMessage());
        }
    }

    private void checkDlqComponentsNotPresent() {
        // 检查死信组件是否正确地没有被创建
        try {
            applicationContext.getBean("dlqExchange");
            log.warn("WARN: 死信交换机被创建但DLQ已禁用");
        } catch (Exception e) {
            log.info("CHECKED: 死信交换机未创建（符合配置）");
        }

        try {
            applicationContext.getBean("dlqQueue");
            log.warn("WARN: 死信队列被创建但DLQ已禁用");
        } catch (Exception e) {
            log.info("CHECKED: 死信队列未创建（符合配置）");
        }

        try {
            applicationContext.getBean("dlqBinding");
            log.warn("WARN: 死信队列绑定被创建但DLQ已禁用");
        } catch (Exception e) {
            log.info("CHECKED: 死信队列绑定未创建（符合配置）");
        }
    }

    /**
     * 获取启动任务线程池状态信息
     */
    public String getStartupTaskPoolStatus() {
        return ThreadPoolConfig.getThreadPoolStatus("Startup-Task", startupTaskExecutor);
    }

    /**
     * 检查启动任务线程池健康状态
     */
    private void checkStartupTaskPoolHealth() {
        ThreadPoolConfig.checkThreadPoolHealth("Startup-Task", startupTaskExecutor, 2, 1.0);
        log.debug("Startup task pool status: {}", getStartupTaskPoolStatus());
    }
} 