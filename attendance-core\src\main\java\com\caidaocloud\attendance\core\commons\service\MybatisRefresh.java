package com.caidaocloud.attendance.core.commons.service;

import com.baomidou.mybatisplus.extension.spring.MybatisMapperRefresh;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(name = "mybatis-plus.mapper-refresh", havingValue = "true")
public class MybatisRefresh implements CommandLineRunner {

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Override
    public void run(String... args) throws Exception {
        Resource[] resources = new PathMatchingResourcePatternResolver().getResources("classpath*:/com/caidao1/**/sqlmap/*.xml");
        new MybatisMapperRefresh(resources, sqlSessionFactory, true).run();
    }
}
