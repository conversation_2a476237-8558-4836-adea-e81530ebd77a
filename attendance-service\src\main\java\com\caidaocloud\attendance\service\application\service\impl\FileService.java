package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.attendance.service.application.dto.file.AttachmentInfoDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

@Slf4j
@Service
public class FileService {

    @Autowired
    private OssService ossService;
    @Autowired
    private ISessionService sessionService;

    /**
     * 异步文件上传
     * @param multipartFile
     * @param userInfo
     * @return
     */
    @Async
    public AttachmentInfoDto asyncUpload(MultipartFile multipartFile, UserInfo userInfo){
        AttachmentInfoDto attachmentInfoDto = upload(multipartFile, userInfo);
        log.info("asyncUpload result={}", FastjsonUtil.toJson(attachmentInfoDto));
        return attachmentInfoDto;
    }

    /**
     * 文件上传
     * @param multipartFile
     * @return
     */
    public AttachmentInfoDto upload(MultipartFile multipartFile,UserInfo userInfo){
        userInfo = null != userInfo ? userInfo : sessionService.getUserInfo();
        String bucketName = userInfo.getCorpid() + "-" + userInfo.getBelongOrgId();
        val result = ossService.upload(bucketName, multipartFile);
        val attachment = new AttachmentInfoDto();
        attachment.setFileName(result.getFileName());
        attachment.setPath(result.getObjectPath());
        return attachment;
    }

    /**
     * 文件下载
     * @param path
     * @param fileName
     * @param response
     */
    public void download(String path, String fileName, HttpServletResponse response) {
        ossService.downloadFile(path, fileName, response);
    }
}
