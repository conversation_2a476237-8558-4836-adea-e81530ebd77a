package com.caidaocloud.attendance.service.application.feign;

import com.caidaocloud.attendance.service.application.dto.transfer.ChangeDefDto;
import com.caidaocloud.attendance.service.application.dto.transfer.TransferApplyDetailDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 人事异动
 *
 * <AUTHOR>
 * @Date 2024/5/24
 */
@FeignClient(
        value = "${feign.rename.caidaocloud-hr-service:caidaocloud-hr-service}",
        fallback = WaTransferFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "waTransferFeignClient"
)
public interface IWaTransferFeignClient {

    /**
     * 查询异动表单详情
     *
     * @param applyId
     * @param paasMode
     * @return
     */
    @GetMapping("/api/hr/transfer/v1/detail")
    Result<TransferApplyDetailDto> getTransferDetail(@RequestParam String applyId,
                                                     @RequestParam(value = "paasMode", required = false, defaultValue = "false") boolean paasMode);

    /**
     * 获取异动配置详情
     *
     * @param bid
     * @return
     */
    @GetMapping("/api/hr/transfer/config/v1/changeDef/one")
    Result<ChangeDefDto> getChangeDefInfo(@RequestParam String bid);

}
