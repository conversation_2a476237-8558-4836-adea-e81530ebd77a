package com.caidaocloud.attendance.service.application.constant;

/**
 * 数据权限唯一code值常量类
 *
 * <AUTHOR>
 * @Date 2021/5/26
 */
public class AuthDataScopeCode {
    //获取考勤统计汇总计算
    public static final String STATISTICS_SUMMARY_COUNT = "staticSummaryCount";
    //获取考勤统计每日明细列表
    public static final String STATISTICS_DAY_ANALYSE_LIST = "StatisticsDayAnalyseList";
    //获取考勤统计月度汇总列表
    public static final String STATISTICS_MONTHLY_ANALYSE_LIST = "StatisticsMonthlyAnalyseList";
    //获取考勤统计明细汇总列表
    public static final String STATISTICS_MONTHLY_ANALYSE_ADVANCE_LIST = "searchRegisterStatisticsAdvance";
    //获取上下班打卡记录列表
    public static final String REGISTER_RECORD_LIST = "RegisterRecordList";
    //获取补卡记录列表
    public static final String REGISTER_BDK_RECORD_LIST = "RegisterBdkRecordList";
    //获取所有类型记录列表
    public static final String ALL_REGISTER_BDK_RECORD_LIST = "AllRegisterBdkRecordList";
    //获取补卡记录列表
    public static final String OUTWORK_REGISTER_RECORD_LIST = "OutworkRegisterRecordList";
    //获取每日考勤列表
    public static final String REGISTER_ANALYZE_RECORD_LIST = "RegisterAnalyzeRecordList";
    //获取休假申请列表
    public static final String LEAVE_APPLY_RECORD_LIST = "LeaveApplyRecordList";
    //休假申请-员工列表查询
    public static final String LEAVE_APPLY_EMP_LIST = "LeaveApplyEmpList";
    //获取加班列表
    public static final String OVERTIME_APPLY_RECORD_LIST = "OvertimeApplyRecordList";
    //获取配额列表
    public static final String QUOTA_LIST = "QuotaList";
    //按年发放配额生成（年假）
    public static final String QUOTA_GEN = "QuotaGen";
    //调休配额生成
    public static final String COMPENSATORY_LEAVE_QUOTA_GEN = "CompensatoryLeaveQuotaGen";
    //固定额度配额生成
    public static final String FIXED_QUOTA_GEN = "FixedQuotaGen";
    //考勤分析核算
    public static final String STATISTICS_ANALYZE = "StatisticsAnalyze";
    //员工班次调整记录查询
    public static final String EMP_SHIFT_CHANGE_LIST = "EmpShiftChangeList";
    //员工日历（班次）导出
    public static final String EMP_SHIFT_LIST = "EmpShiftList";
    //员工打卡方案导出
    public static final String EMP_CLOCK_PLAN_LIST = "EmpClockPlanList";
    //员工出差申请导出
    public static final String EMP_TRAVEL_LIST = "EmpTravelList";
    //调休付现申请列表
    public static final String COMPENSATORY_CASH_APPLY_RECORD_LIST = "CompensatoryApplyRecordList";
    //异常汇总记录
    public static final String EMP_DAY_ANALYSE_ABNORMAL_LIST = "DayAnalyseAbnormalList";
    //出差转调休列表
    public static final String TRAVEL_COMPENSATORY_RECORD_LIST = "TravelCompensatoryRecordList";
    //加班流程撤销列表
    public static final String OVERTIME_WORKFLOW_REVOKE_LIST = "OvertimeWorkflowRevokeList";
    //加班流程废止列表
    public static final String OVERTIME_WORKFLOW_ABOLISH_LIST = "OvertimeWorkflowAbolishList";
    //出差流程撤销列表
    public static final String TRAVEL_WORKFLOW_REVOKE_LIST = "TravelWorkflowRevokeList";
    //出差流程废止列表
    public static final String TRAVEL_WORKFLOW_ABOLISH_LIST = "TravelWorkflowAbolishList";
    // 批量出差列表
    public static final String BATCH_EMP_TRAVEL_LIST = "BatchEmpTravelList";
    // 批量休假申请列表
    public static final String BATCH_LEAVE_APPLY_RECORD_LIST = "BatchLeaveApplyRecordList";
    // 批量加班申请列表
    public static final String BATCH_OVERTIME_APPLY_RECORD_LIST = "BatchOvertimeApplyRecordList";
    // 批量考勤异常申请列表
    public static final String BATCH_ANALYSE_RESULT_ADJUST_LIST = "BatchAnalyseResultAdjustList";
    // 假期延期申请列表
    public static final String LEAVE_EXTENSION_APPLY_LIST = "LeaveExtensionApplyList";
}
