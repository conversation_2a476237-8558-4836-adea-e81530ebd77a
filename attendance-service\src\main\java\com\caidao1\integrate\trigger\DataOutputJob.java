package com.caidao1.integrate.trigger;

import com.caidao1.integrate.service.IntegratedService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;

@Configurable
@DisallowConcurrentExecution
public class DataOutputJob implements Job {
    private final static Logger LOGGER = LoggerFactory.getLogger(DataOutputJob.class);

    @Autowired
    private IntegratedService integratedService;

    @Override
    public void execute(JobExecutionContext context) {
        LOGGER.info("DataOutputJob start execute ...");
        try {
            JobDataMap dataMap = context.getJobDetail().getJobDataMap();
            int dataOutputId = dataMap.getInt("dataOutputId");
            Long corpId = dataMap.getLong("corpId");
            String belongId = dataMap.getString("belongId");

            LOGGER.info("DataOutputJob execute. dataOutputId=[{}], corpId=[{}], belongId=[{}]", dataOutputId, corpId, belongId);
            integratedService.syncDataOutput(dataOutputId, corpId, belongId);
        } catch (Exception ex) {
            LOGGER.error("DataOutputJob execute err,{}", ex.getMessage(), ex);
        }
        LOGGER.info("DataOutputJob end execute ...");
    }
}