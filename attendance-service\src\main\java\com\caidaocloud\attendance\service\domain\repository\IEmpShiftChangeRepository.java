package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftChangePo;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/12/1 15:53
 * @Description:
 **/
public interface IEmpShiftChangeRepository {

    List<WaEmpShiftChangePo> getEmpShiftChanges(String tenantId, List<Long> empIds, Long startDate, Long endDate, Integer status);

    void saveEmpShiftChanges(List<WaEmpShiftChangePo> changes);

    void updateEmpShiftChanges(List<WaEmpShiftChangePo> changes);
}
