package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWorkHoursDetailRepository;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class WorkHoursDetailDo {

    private String waGroupId;

    private Long empId;

    private Long date;

    /**
     * 标准工时，单位分钟
     */
    private BigDecimal stdWorkTime;

    /**
     * 夜班天数
     */
    private BigDecimal nightShiftDays;

    public static void replaceAll(String tenantId, List<Long> empIds, Long startDate, Long endDate, List<WorkHoursDetailDo> hours) {
        repository().delete(tenantId, empIds, startDate, endDate);
        repository().addAll(tenantId, hours);
    }


    private static IWorkHoursDetailRepository repository(){
        return SpringUtil.getBean(IWorkHoursDetailRepository.class);
    }


    public static List<WorkHoursDetailDo> list(String tenantId, List<String> empIdList, Long startDate, Long endDate) {
        return repository().list(tenantId, empIdList.stream().map(it->Long.valueOf(it)).collect(Collectors.toList()), startDate, endDate);
    }
}
