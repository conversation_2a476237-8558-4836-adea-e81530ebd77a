package com.caidao1.integrate.entity.TieTong;

public class Constants {
    public static final String HMAC_SHA256 = "HmacSHA256";
    public static final String ENCODING = "UTF-8";
    public static final String USER_AGENT = "demo/aliyun/java";
    public static final String LF = "\n";
    public static final String SPE1 = ",";
    public static final String SPE2 = ":";
    public static final String SPE3 = "&";
    public static final String SPE4 = "=";
    public static final String SPE5 = "?";
    public static final String SPE6 = ";";
    public static int DEFAULT_TIMEOUT = 1000;
    public static final String CA_HEADER_TO_SIGN_PREFIX_SYSTEM = "x-ca-";
    public static final Double JDK_VERSION = 1.7;

    public Constants() {
    }
}
