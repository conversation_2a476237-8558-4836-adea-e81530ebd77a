package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.service.domain.repository.IEmpCompensatoryCaseApplyRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCaseApply;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Service
public class EmpCompensatoryCaseApplyDo {
    private Long id;
    private String tenantId;
    private Long empId;
    private Float applyDuration;
    private Float validDuration;
    private Integer timeUnit;
    private Integer status;
    private Long lastApprovalTime;
    private String note;
    private String revokeReason;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private String workNo;
    private String empName;

    private String fullPath;
    private String workCity;
    private String employType;
    private Long hireDate;

    @Autowired
    private IEmpCompensatoryCaseApplyRepository empCompensatoryCaseApplyRepository;

    @CDText(exp = {"employType" + TextAspect.DICT_E, "workCity" + TextAspect.PLACE}, classType = EmpCompensatoryCaseApplyDo.class)
    public EmpCompensatoryCaseApplyDo getDetailById(String tenantId, Long id) {
        return empCompensatoryCaseApplyRepository.getDetailById(tenantId, id);
    }

    public void save(EmpCompensatoryCaseApplyDo model) {
        if (null == model) {
            return;
        }
        empCompensatoryCaseApplyRepository.save(ObjectConverter.convert(model, WaEmpCompensatoryCaseApply.class));
    }

    public void update(EmpCompensatoryCaseApplyDo model) {
        if (null == model) {
            return;
        }
        empCompensatoryCaseApplyRepository.update(ObjectConverter.convert(model, WaEmpCompensatoryCaseApply.class));
    }

    public PageList<EmpCompensatoryCaseApplyDo> getEmpCompensatoryCaseList(MyPageBounds myPageBounds, Map params) {
        return empCompensatoryCaseApplyRepository.getEmpCompensatoryCaseList(myPageBounds, params);
    }

    public void batchSave(List<EmpCompensatoryCaseApplyDo> records) {
        if (null == records || records.size() <= 0) {
            return;
        }
        List<WaEmpCompensatoryCaseApply> models = ObjectConverter.convertList(records, WaEmpCompensatoryCaseApply.class);
        empCompensatoryCaseApplyRepository.batchSave(models);
    }

    public List<EmpCompensatoryCaseApplyDo> getApprovedOfCompensatoryCase(List<Long> empIdList, Long startDate, Long endDate) {
        if (CollectionUtils.isEmpty(empIdList)) {
            return Lists.newArrayList();
        }
        return empCompensatoryCaseApplyRepository.getApprovedOfCompensatoryCase(empIdList, startDate, endDate);
    }

    public List<EmpCompensatoryCaseApplyDo> getCompensatoryCaseList(String tenantId, List<Long> ids, List<Integer> status) {
        return empCompensatoryCaseApplyRepository.getCompensatoryCaseList(tenantId, ids, status);
    }
}
