package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.application.dto.StatisticsReportReqDataDto;
import com.caidaocloud.attendance.service.domain.repository.IWaAnalyzeStatisticsReportRepository;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Data
@Service
public class WaAnalyzeStatisticsReportDo {
    private Long statisticsReportId;

    private String tenantId;

    private Long empId;

    private Integer workTime;

    private Float actualWorkTime;

    private Integer registerTime;

    private Float lateTime;

    private Float earlyTime;

    private Integer kgWorkTime;

    private Integer bdkCount;

    private Integer otTime;

    private Integer leaveCount;

    private Integer travelCount;

    private Integer rptType;

    private Long startDate;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;
    /**
     * 迟到次数
     */
    private Integer totalLateCount;
    /**
     * 早退次数
     */
    private Integer totalEarlyCount;
    /**
     * 旷工次数
     */
    private Integer totalKgCount;

    //其他业务字段
    private Float otTimeHour;
    private Float workTimeHour;
    private Float actualWorkTimeHour;
    private Float registerTimeHour;

    @Autowired
    private IWaAnalyzeStatisticsReportRepository waAnalyzeStatisticsReportRepository;

    public AttendancePageResult<WaAnalyzeStatisticsReportDo> getAnalyzeStatisticsReportPageList(StatisticsReportReqDataDto dto) {
        return waAnalyzeStatisticsReportRepository.getAnalyzeStatisticsReportPageList(dto);
    }

    public int save(List<WaAnalyzeStatisticsReportDo> list) {
        return waAnalyzeStatisticsReportRepository.save(list);
    }

    @Deprecated
    public int delete(String tenantId, List<Long> empIds, List<Long> startDates, Integer rptType) {
        return waAnalyzeStatisticsReportRepository.delete(tenantId, empIds, startDates, rptType);
    }

    public List<WaAnalyzeStatisticsReportDo> getAnalyzeStatisticsReportList(String tenantId, List<Long> empIds, Integer rptType,Long startDate,Long endDate) {
        return waAnalyzeStatisticsReportRepository.getAnalyzeStatisticsReportList(tenantId, empIds, rptType,startDate,endDate);
    }

    public int deleteByIds(String tenantId, List<Long> statisticsReportIds) {
        return waAnalyzeStatisticsReportRepository.deleteByIds(tenantId, statisticsReportIds);
    }

    public WaAnalyzeStatisticsReportDo getStatisticsReportByEmpId(String tenantId, Long empId, Integer rptType, Long startDate){
        return waAnalyzeStatisticsReportRepository.getAnalyzeStatisticReport(tenantId,empId,rptType,startDate);
    }
}