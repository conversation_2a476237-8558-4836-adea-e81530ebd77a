package com.caidaocloud.attendance.service.application.dto;

import com.caidaocloud.attendance.service.application.enums.NotifyConfigTypeEnum;
import com.caidaocloud.attendance.service.domain.entity.NotifyConfigDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class NotifyConfigItemDto {
    @ApiModelProperty("序号")
    private Integer num;
    @ApiModelProperty("主键ID")
    private Long notifyConfigId;
    @ApiModelProperty("消息类型名称")
    private String name;
    @ApiModelProperty("状态： 0 关闭，1 开启")
    private Integer status;

    public static NotifyConfigItemDto getNotifyConfigItemDto(NotifyConfigTypeEnum type, NotifyConfigDo notifyConfig) {
        NotifyConfigItemDto notifyConfigItemDto = null;
        switch (type) {
            case CLOCK:
                notifyConfigItemDto = new NotifyConfigItemDto(type.getIndex(), notifyConfig.getNotifyConfigId(), NotifyConfigTypeEnum.getName(type.getIndex()), notifyConfig.getClockNotifySwitch());
                break;
            case DAILY_REPORT:
                notifyConfigItemDto = new NotifyConfigItemDto(type.getIndex(), notifyConfig.getNotifyConfigId(), NotifyConfigTypeEnum.getName(type.getIndex()), notifyConfig.getDailyNotifySwitch());
                break;
            case WEEKLY_REPORT:
                notifyConfigItemDto = new NotifyConfigItemDto(type.getIndex(), notifyConfig.getNotifyConfigId(), NotifyConfigTypeEnum.getName(type.getIndex()), notifyConfig.getWeeklyNotifySwitch());
                break;
            case LEAVE_CANCEL:
                notifyConfigItemDto = new NotifyConfigItemDto(type.getIndex(), notifyConfig.getNotifyConfigId(), NotifyConfigTypeEnum.getName(type.getIndex()), notifyConfig.getLeaveCancelSwitch());
                break;
            case ABNORMAL_SUMMARY:
                notifyConfigItemDto = new NotifyConfigItemDto(type.getIndex(), notifyConfig.getNotifyConfigId(), NotifyConfigTypeEnum.getName(type.getIndex()), notifyConfig.getAbnormalSwitch());
                break;
            case ATTENDANCE_DETAIL_PUSH:
                notifyConfigItemDto = new NotifyConfigItemDto(type.getIndex(), notifyConfig.getNotifyConfigId(), NotifyConfigTypeEnum.getName(type.getIndex()), notifyConfig.getAttendanceDetailSwitch());
                break;
        }
        return notifyConfigItemDto;
    }
}

