package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.application.dto.ShiftGroupAuthResultDto;
import com.caidaocloud.dto.UserInfo;

import java.util.List;

public interface ICacheCommonService {
    String getOrgDataScope(String tenantId, Long userId, String requestURI, String alias, Long empId);

    String getShiftGroupDataScope(String tenantId, Long userId, String identifier, Long empId, List<Long> shiftGroupIds);

    /**
     * 绩效权限
     */
    List<String> getPerformanceDataScope(String tenantId, Long userId, Long empId, Long periodStartDate, Long periodEndDate);

    ShiftGroupAuthResultDto getAuthedEmpShiftGroup(String tenantId, Long userId, String identifier, Long empId);

    List<Long> getShiftGroupDataScopeEmpList(String identifier, Long startDate, Long endDate, String keywords, List<Long> shiftGroupIds, UserInfo userInfo);
}
