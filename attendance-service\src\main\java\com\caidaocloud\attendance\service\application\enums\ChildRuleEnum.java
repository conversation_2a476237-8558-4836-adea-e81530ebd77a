package com.caidaocloud.attendance.service.application.enums;

/**
 * 发放周期
 */
public enum ChildRuleEnum {
    CHILD_COUNT(1, "按子女个数"),
    CHILD_SUM(2, "按子女个数累加"),
    CHILD_ONE(3, "仅生成一条");

    private Integer index;
    private String name;

    ChildRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ChildRuleEnum c : ChildRuleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
