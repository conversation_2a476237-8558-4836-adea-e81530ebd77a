package com.caidao1.ioc.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.mybatis.model.SysEmpInfoExample;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.enums.CompensateTypeEnum;
import com.caidao1.commons.enums.WaDateTypeEnum;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.utils.*;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.dto.DingTalkUserIdDto;
import com.caidao1.ioc.dto.SaveItemDto;
import com.caidao1.ioc.dto.UpdRowDto;
import com.caidao1.ioc.mybatis.mapper.ImportOtherMapper;
import com.caidao1.ioc.util.IocUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.mobile.service.MobileV16Service;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.wa.dto.EmpLeaveQuotaDto;
import com.caidao1.wa.enums.LeaveCancelStatusEnum;
import com.caidao1.wa.enums.WaDistributionCycleEnum;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidao1.wa.service.RegisterAnalyzeService;
import com.caidao1.wa.service.WaQuotaService;
import com.caidao1.xss.test.cache.RedisService;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.ClassCompareUtil;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.WaAnalyzDTO;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.enums.DayHalfTypeEnum;
import com.caidaocloud.attendance.service.application.enums.OvertimeUnitEnum;
import com.caidaocloud.attendance.service.application.enums.PeriodTypeEnum;
import com.caidaocloud.attendance.service.application.enums.QuotaRestrictionTypeEnum;
import com.caidaocloud.attendance.service.application.service.IOvertimeApplyService;
import com.caidaocloud.attendance.service.domain.entity.OverTimeTypeDo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ImportTriggerService implements ScriptBindable {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    /**
     * 考勤打卡记录导入/接入后：开启新的后置处理器
     */
    @Value("${caidaocloud.data.newClockImportTrigger:true}")
    private boolean openNewClockImportTrigger;

    @Autowired
    private WaShiftDefMapper waShiftDefMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RegisterAnalyzeService registerAnalyzeService;
    @Autowired
    private WaConfigMapper waConfigMapper;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private MobileV16Service mobileV16Service;
    @Autowired
    private WaAnalyzeMapper waAnalyzeMapper;
    @Autowired
    private SysEmpInfoMapper empInfoMapper;
    @Autowired
    private WaLeaveDaytimeMapper waLeaveDaytimeMapper;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private WaEmpQuotaDetailMapper waEmpQuotaDetailMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaEmpShiftChangeMapper waEmpShiftChangeMapper;
    @Autowired
    private WaEmpShiftMapper waEmpShiftMapper;
    @Autowired
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private WaEmpOvertimeMapper waEmpOvertimeMapper;
    @Autowired
    private EmpCompensatoryQuotaMapper waEmpCompensatoryQuotaMapper;
    @Autowired
    private WaCompensatoryQuotaUseMapper waCompensatoryQuotaUseMapper;
    @Autowired
    private WaLeaveQuotaUseMapper waLeaveQuotaUseMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaLeaveConfigMapper waLeaveQuotaConfigMapper;
    @Autowired
    private WaQuotaService quotaService;
    @Autowired
    private WaEmpGroupMapper waEmpGroupMapper;
    @Autowired
    private PlanEmpRelMapper waPlanEmpRelMapper;
    @Autowired
    private WaOvertimeTypeMapper waOvertimeTypeMapper;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Resource
    private ImportOtherMapper importOtherMapper;
    @Resource
    private OverTimeTypeDo overTimeTypeDo;
    @Autowired
    private IOvertimeApplyService overtimeApplyService;
    @Autowired
    private ImportClockTriggerService importClockTriggerService;

    /**
     * @param belongId
     * @param anyEmpids 格式: {12,12,12,12}
     * @return
     * @throws Exception
     */
    public void analyzeOverTimeData(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList, String anyEmpids) throws Exception {

        List<UpdRowDto> valueAddList = addList.get("wa_emp_overtime");
        List<UpdRowDto> valueUpdList = updList.get("wa_emp_overtime");
        List<UpdRowDto> allList = new ArrayList<UpdRowDto>();
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            allList.addAll(valueAddList);
        }
        if (CollectionUtils.isNotEmpty(valueUpdList)) {
            allList.addAll(valueUpdList);
        }

        if (CollectionUtils.isEmpty(allList)) {
            log.info("----------------------no Analyze OT");
            return;
        }

        // 求最大最小值
        long startdate = 0, enddate = 0;
        List<Long> empids = new ArrayList<Long>();
        for (UpdRowDto updRowDto : allList) {
            //数据已经导入表中 分析数据
            Long empid = null;
            Long reg_date_time = null;
            for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                if (saveItemDto.getItemCode().equals("empid")) {
                    empid = ConvertHelper.longConvert(saveItemDto.getItemValue());
                    empids.add(empid);
                }
                if (saveItemDto.getItemCode().equals("start_time")) {
                    Object datatime = saveItemDto.getItemValue();
                    if (datatime instanceof BigDecimal) {
                        reg_date_time = ((BigDecimal) saveItemDto.getItemValue()).longValue();
                    } else {
                        reg_date_time = (Long) saveItemDto.getItemValue();
                    }
                }
            }
            if (empid == null) {
                continue;
            }
            if (reg_date_time.intValue() > enddate) {
                enddate = reg_date_time;
            }
            if (startdate == 0) {
                startdate = reg_date_time;
            }
            if (reg_date_time.intValue() < startdate) {
                startdate = reg_date_time;
            }
        }

        Map paramsMap = new HashMap();
        paramsMap.put("startDate", startdate - (startdate + 28800) % 86400);
        paramsMap.put("endDate", enddate - (enddate + 28800) % 86400);
        paramsMap.put("belongid", belongId);
        if (StringUtil.isNullOrTrimEmpty(anyEmpids)) {
            if (CollectionUtils.isNotEmpty(empids))
                anyEmpids = "'{" + StringUtils.join(empids, ",") + "}'";
        }
        paramsMap.put("anyEmpids", anyEmpids);// 查询WaAnalyze用到
        paramsMap.put("anyEmpids2", anyEmpids);

        Integer ymstart = Integer.valueOf(DateUtil.parseDateToPattern(new Date(startdate * 1000), "yyyyMM"));
        paramsMap.put("ymstart", ymstart);

        Integer ymend = Integer.valueOf(DateUtil.parseDateToPattern(new Date(enddate * 1000), "yyyyMM"));
        paramsMap.put("ymend", ymend);

        WaAnalyzDTO dto = registerAnalyzeService.getWaAnalyzInfo(paramsMap);
        dto.setBelongid(belongId);
        // 5.组合签到签退纪录 计算签到签退 迟到  早退，工作小时，异常描述，签到签退ID
        List<WaAnalyze> resultWa = waAnalyzeMapper.findWaanalyzeList(paramsMap);
//        dto.setWaAnalyzeList(resultWa);
        registerAnalyzeService.analyzeOverTimeData(resultWa, paramsMap, dto);

        if (resultWa != null && resultWa.size() > 0) {
            for (WaAnalyze waAnalyze : resultWa) {
                waAnalyze.setBelongOrgId(belongId);
                if (waAnalyze.getAnalyzeId() != null) {
                    waAnalyze.setUpduser(SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId());
                    waAnalyze.setUpdtime(DateUtil.getCurrentTime(true));
                    waAnalyzeMapper.updateByPrimaryKey(waAnalyze);
                } else {
                    waAnalyze.setCrtuser(SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId());
                    waAnalyze.setCrttime(DateUtil.getCurrentTime(true));
                    waAnalyzeMapper.insertSelective(waAnalyze);
                }

            }
        }

    }

    /**
     * 更新排班结束时间
     *
     * @param addList
     * @param updList
     */
    @Transactional
    public void updateEmpShiftEndTime(Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        List<UpdRowDto> valueAddList = addList.get("wa_emp_shift");
        List<UpdRowDto> valueUpdList = updList.get("wa_emp_shift");
        List<UpdRowDto> allList = new ArrayList<UpdRowDto>();
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            allList.addAll(valueAddList);
        }
        if (CollectionUtils.isNotEmpty(valueUpdList)) {
            allList.addAll(valueUpdList);
        }
        if (CollectionUtils.isNotEmpty(allList)) {
            for (UpdRowDto updRowDto : allList) {
                Long startTime = null;
                Long endTime = null;
                Long empid = null;
                for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                    if (saveItemDto.getItemCode().equals("empid")) {
                        empid = ConvertHelper.longConvert(saveItemDto.getItemValue());
                    } else if (saveItemDto.getItemCode().equals("starttime")) {
                        startTime = (Long) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("endtime")) {
                        endTime = (Long) saveItemDto.getItemValue();
                    }
                }
                if (startTime != null) {
                    WaEmpShiftExample example = new WaEmpShiftExample();
                    example.createCriteria().andEmpidEqualTo(empid).andStartTimeLessThan(startTime);
                    example.setOrderByClause("starttime desc");
                    List<WaEmpShift> res = waEmpShiftMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(res)) {
                        Calendar cal = Calendar.getInstance();
                        cal.setTimeInMillis(startTime * 1000);
                        cal.add(Calendar.DATE, -1);
                        WaEmpShift last = res.get(0);
                        if (last.getEndTime() != cal.getTimeInMillis() / 1000) {
                            last.setEndTime(cal.getTimeInMillis() / 1000 + 86399);
                            waEmpShiftMapper.updateByPrimaryKeySelective(last);
                        }
                    }

                    example = new WaEmpShiftExample();
                    example.createCriteria().andEmpidEqualTo(empid).andStartTimeGreaterThan(startTime);
                    example.setOrderByClause("starttime");
                    res = waEmpShiftMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(res)) {
                        WaEmpShift first = res.get(0);
                        Calendar cal = Calendar.getInstance();
                        cal.setTimeInMillis(first.getStartTime() * 1000);
                        cal.add(Calendar.DATE, -1);
                        WaEmpShift record = new WaEmpShift();
                        record.setEndTime(cal.getTimeInMillis() / 1000 + 86399);
                        if (record.getEndTime() < endTime) {
                            example = new WaEmpShiftExample();
                            example.createCriteria().andEmpidEqualTo(empid).andStartTimeEqualTo(startTime);
                            waEmpShiftMapper.updateByExampleSelective(record, example);
                        }
                    }
                }
            }
        }
    }

    /**
     * 更新组织信息
     */
    public void updateOrgInfo(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("belongId", belongId);
        sysCorpOrgMapper.refreshOrgLevel(params);
    }

    /**
     * 更新员工信息缓存
     */
    @Transactional
    public void updateEmpInfoCache(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) throws Exception {
        List<UpdRowDto> valueAddList = addList.get("sys_emp_info");
        List<UpdRowDto> valueUpdList = updList.get("sys_emp_info");
        List<UpdRowDto> allList = new ArrayList<UpdRowDto>();
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            allList.addAll(valueAddList);
        }
        if (CollectionUtils.isNotEmpty(valueUpdList)) {
            allList.addAll(valueUpdList);
        }
        if (CollectionUtils.isNotEmpty(allList)) {
            SysEmpInfoExample empExample = new SysEmpInfoExample();
            if (allList.size() > 500) {
                empExample.createCriteria().andBelongOrgIdEqualTo(belongId);
            } else {
                List<String> worknoList = new ArrayList<String>();
                for (UpdRowDto updRowDto : allList) {
                    String workno = null;
                    for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                        if (saveItemDto.getItemCode().equals("workno")) {
                            workno = (String) saveItemDto.getItemValue();
                            break;
                        }
                    }
                    if (workno != null) {
                        worknoList.add(workno);
                    }
                }
                empExample.createCriteria().andBelongOrgIdEqualTo(belongId).andWorknoIn(worknoList);
            }
            List<SysEmpInfo> empInfoList = empInfoMapper.selectByExample(empExample);
            Jedis jedis = redisService.getResource();

            try {
                for (SysEmpInfo emp : empInfoList) {
                    Integer tmtype = emp.getTmType();
                    if (tmtype == null) tmtype = 1;
                    String site = StringUtils.trimToEmpty(emp.getSiteids());
                    if (StringUtils.isNotBlank(site)) {
                        site = site.replace(",", "#");
                    }
                    jedis.set(BaseConst.EMP_ + emp.getBelongOrgId() + "_" + emp.getWorkno(), emp.getEmpid() + "," + emp.getEmpName() + "," + tmtype + "," + site);
                }
            } catch (Exception e) {
                throw e;
            } finally {
                jedis.close();
            }
        }
    }

    /**
     * 更新配额结束时间
     *
     * @param addList
     * @param updList
     */
    @Transactional
    public void updateEmpQuota(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        if (log.isDebugEnabled()) {
            log.debug("updateEmpQuota belongId={}", belongId);
        }
        List<UpdRowDto> valueAddList = addList.get("wa_emp_quota");
        List<UpdRowDto> valueUpdList = updList.get("wa_emp_quota");
        List<UpdRowDto> allList = new ArrayList<UpdRowDto>();
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            allList.addAll(valueAddList);
        }
        if (CollectionUtils.isNotEmpty(valueUpdList)) {
            allList.addAll(valueUpdList);
        }
        List<WaEmpQuota> empQuotaList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allList)) {
            // 兼容老逻辑，后续移除
            Map<Integer, Integer> quotaSettingMap = new HashMap<>();
            Map<Integer, Integer> quotaSettingLeaveTypeMap = new HashMap<>();
            List<Map> configList = waConfigMapper.getLeaveSettingConfig(belongId);
            if (CollectionUtils.isNotEmpty(configList)) {
                quotaSettingMap = configList.stream().collect(Collectors.toMap(o -> (Integer) o.get("quota_setting_id"), o -> (Integer) o.get("acct_time_type"), (v1, v2) -> v1));
                quotaSettingLeaveTypeMap = configList.stream().collect(Collectors.toMap(o -> (Integer) o.get("leave_type_id"), o -> (Integer) o.get("quota_setting_id"), (v1, v2) -> v1));
            }
            // 查询公司假期类型
            Map<Integer, WaLeaveType> leaveTypeMap = new HashMap<>();
            WaLeaveTypeExample leaveTypeExample = new WaLeaveTypeExample();
            leaveTypeExample.createCriteria().andBelongOrgidEqualTo(belongId);
            List<WaLeaveType> leaveTypeList = waLeaveTypeMapper.selectByExample(leaveTypeExample);
            if (CollectionUtils.isNotEmpty(leaveTypeList)) {
                leaveTypeMap = leaveTypeList.stream().collect(Collectors.toMap(WaLeaveType::getLeaveTypeId, Function.identity()));
            }
            for (UpdRowDto updRowDto : allList) {
                Integer empQuotaId = ConvertHelper.intConvert(updRowDto.getId());
                Long startTime = null;
                Long endTime = null;
                Integer quotaSettingId = null;
                Long empid = null;
                Float quota_day = null;
                Float remain_day = null;
                Float deduction_day = null;
                Float used_day = null;
                Float now_quota = null;
                Float remain_used_day = null;
                Float fix_used_day = null;
                Integer leave_type_id = null;
                Integer period_year = null;
                Long configId = null;
                for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                    if (saveItemDto.getItemCode().equals("empid")) {
                        empid = (Long) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("quota_setting_id")) {
                        quotaSettingId = ConvertHelper.intConvert(saveItemDto.getItemValue());
                    } else if (saveItemDto.getItemCode().equals("start_date") && null != saveItemDto.getItemValue()) {
                        startTime = saveItemDto.getItemValue() instanceof Long ? (Long) saveItemDto.getItemValue() : ((Integer) saveItemDto.getItemValue()).longValue();
                    } else if (saveItemDto.getItemCode().equals("last_date") && null != saveItemDto.getItemValue()) {
                        endTime = saveItemDto.getItemValue() instanceof Long ? (Long) saveItemDto.getItemValue() : ((Integer) saveItemDto.getItemValue()).longValue();
                    } else if (saveItemDto.getItemCode().equals("quota_day")) {
                        quota_day = (Float) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("remain_day")) {
                        remain_day = (Float) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("deduction_day")) {
                        deduction_day = (Float) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("used_day")) {
                        used_day = (Float) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("now_quota")) {
                        now_quota = (Float) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("remain_used_day")) {
                        remain_used_day = (Float) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("fix_used_day")) {
                        fix_used_day = (Float) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("leave_type_id")) {
                        leave_type_id = ConvertHelper.intConvert(saveItemDto.getItemValue());
                    } else if (saveItemDto.getItemCode().equals("period_year")) {
                        period_year = (Integer) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("emp_quota_id")) {
                        empQuotaId = (Integer) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("config_id")) {
                        configId = (Long) saveItemDto.getItemValue();
                    }
                }
                WaEmpQuota empQuota = new WaEmpQuota();
                empQuota.setEmpQuotaId(empQuotaId);
                empQuota.setUpdtime(DateUtil.getCurrentTime(true));
                empQuota.setUpduser(SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId());
                empQuota.setConfigId(configId);
                Integer acctTimeType = null;
                if (leave_type_id != null && leaveTypeMap.containsKey(leave_type_id)) {
                    acctTimeType = leaveTypeMap.get(leave_type_id).getAcctTimeType();
                } else if (quotaSettingId != null && quotaSettingMap.containsKey(quotaSettingId)) {
                    acctTimeType = quotaSettingMap.get(quotaSettingId);
                }
                // 兼容老逻辑 quotaSettingId ，后续移除
                if (quotaSettingId == null && leave_type_id != null && quotaSettingLeaveTypeMap.containsKey(leave_type_id)) {
                    empQuota.setQuotaSettingId(quotaSettingLeaveTypeMap.get(leave_type_id));
                }
                if (null != acctTimeType && 2 == acctTimeType) {
                    //单位为小时
                    if (quota_day != null) {
                        empQuota.setQuotaDay(quota_day * 60);
                    }
                    if (now_quota != null) {
                        empQuota.setNowQuota(now_quota * 60);
                    }
                    if (remain_day != null) {
                        empQuota.setRemainDay(remain_day * 60);
                    }
                    if (deduction_day != null) {
                        empQuota.setDeductionDay(deduction_day * 60);
                    }
                    if (used_day != null) {
                        empQuota.setUsedDay(used_day * 60);
                    }
                    if (remain_used_day != null) {
                        empQuota.setRemainUsedDay(remain_used_day * 60);
                    }
                    if (endTime != null) {
                        empQuota.setLastDate(endTime + 86399);
                    }
                    if (fix_used_day != null) {
                        empQuota.setFixUsedDay(fix_used_day * 60);
                    }
                } else {
                    empQuota.setFixUsedDay(fix_used_day);
                    empQuota.setQuotaDay(quota_day);
                    empQuota.setNowQuota(now_quota);
                    empQuota.setRemainDay(remain_day);
                    empQuota.setDeductionDay(deduction_day);
                    empQuota.setUsedDay(used_day);
                    empQuota.setRemainUsedDay(remain_used_day);
                    if (endTime != null) {
                        empQuota.setLastDate(endTime + 86399);
                    }
                }
                empQuota.setOriginalQuotaDay(empQuota.getQuotaDay());
                WaLeaveType waLeaveType = leaveTypeMap.get(leave_type_id);
                if (startTime != null && null != waLeaveType && null != waLeaveType.getQuotaType() && 3 == waLeaveType.getQuotaType()) {
                    // 固定额度导入 失效日期默认 9999-12-31 23:59:59
                    endTime = 253402271999L;
                    empQuota.setLastDate(endTime);
                    // 固定额度导入 now_quota,quota_day,original_quota_day保持一致
                    empQuota.setNowQuota(empQuota.getQuotaDay());
                    if (log.isDebugEnabled()) {
                        log.debug("empQuota:{}", JSONUtils.ObjectToJson(empQuota));
                    }
                }
                if (null != waLeaveType && null != waLeaveType.getQuotaType() && (1 == waLeaveType.getQuotaType() || 4 == waLeaveType.getQuotaType())) {
                    //1,查询配额配置信息
                    QueryWrapper<WaLeaveQuotaConfig> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("tenant_id", belongId);
                    queryWrapper.eq("leave_type_id", waLeaveType.getLeaveTypeId());
                    queryWrapper.eq("config_id", configId);
                    WaLeaveQuotaConfig waLeaveQuotaConfig = waLeaveQuotaConfigMapper.selectOne(queryWrapper);
                    //2,查询员工信息
                    SysEmpInfo sysEmpInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
                    //3,计算周期
                    //发放周期开始日
                    Long disCycleStart = null;
                    //发放周期结束日
                    Long disCycleEnd = null;
                    if (waLeaveQuotaConfig != null) {
                        // 发放周期
                        Integer distributionCycle = waLeaveQuotaConfig.getDistributionCycle();
                        if (WaDistributionCycleEnum.NATURAL_YEAR.getIndex().equals(distributionCycle)) {
                            //自然年
                            Calendar calendar = Calendar.getInstance();
                            calendar.set(period_year, 0, 1, 0, 0, 0);
                            disCycleStart = calendar.getTime().getTime() / 1000;
                            try {
                                disCycleEnd = DateUtilExt.getYearsEndTime(disCycleStart);
                            } catch (ParseException e) {
                                log.error("ImportTriggerService.updateEmpQuota error msg {}", e.getMessage(), e);
                            }
                        } else if (WaDistributionCycleEnum.ENTRY_YEAR.getIndex().equals(distributionCycle)) {
                            //入职年
                            Long hireDate = sysEmpInfo.getHireDate();
                            Calendar cal = Calendar.getInstance();
                            cal.setTimeInMillis(hireDate * 1000);

                            Calendar cal1 = Calendar.getInstance();
                            cal1.set(period_year, cal.get(Calendar.MONTH), cal.get(Calendar.DATE), 0, 0, 0);
                            disCycleStart = cal1.getTime().getTime() / 1000;
                            disCycleEnd = DateUtilExt.addMonthDate(disCycleStart * 1000, 12, -1);
                        } else if (WaDistributionCycleEnum.CUSTOM_CYCLE.getIndex().equals(distributionCycle)) {
                            //自定义周期
                            if (waLeaveQuotaConfig.getDisCycleStart() != null) {
                                Calendar startCal = Calendar.getInstance();
                                startCal.setTimeInMillis(waLeaveQuotaConfig.getDisCycleStart() * 1000);
                                startCal.set(Calendar.YEAR, period_year);
                                disCycleStart = startCal.getTime().getTime() / 1000;
                                disCycleEnd = DateUtilExt.addMonthDate(disCycleStart * 1000, 1, 0, -1);
                            }
                        } else if (WaDistributionCycleEnum.CHILD_YEAR.getIndex().equals(distributionCycle)) {
                            // 子女出生年
                            disCycleStart = null != empQuota.getStartDate() ? empQuota.getStartDate() : startTime;
                            disCycleEnd = null != empQuota.getLastDate() ? empQuota.getLastDate() : endTime;
                        }
                        empQuota.setDisCycleStart(disCycleStart);
                        empQuota.setDisCycleEnd(disCycleEnd);
                        // 计算当前配额
                        if (empQuota.getQuotaDay() > 0) {
                            EmpLeaveQuotaDto leaveQuotaDto = new EmpLeaveQuotaDto();
                            leaveQuotaDto.setEmpId(empid);
                            leaveQuotaDto.setBelongOrgId(belongId);
                            leaveQuotaDto.setHireDate(sysEmpInfo.getHireDate());
                            leaveQuotaDto.setProdeadLine(sysEmpInfo.getProdeadLine());
                            leaveQuotaDto.setTerminationDate(sysEmpInfo.getTerminationDate());
                            leaveQuotaDto.setLeaveTypeId(waLeaveQuotaConfig.getLeaveTypeId());
                            leaveQuotaDto.setAcctTimeType(acctTimeType);
                            leaveQuotaDto.setQuotaVal(BigDecimal.valueOf(empQuota.getQuotaDay()));
                            leaveQuotaDto.setDisCycleStart(disCycleStart);
                            leaveQuotaDto.setDisCycleEnd(disCycleEnd);
                            leaveQuotaDto.setOriginalQuotaDay(BigDecimal.valueOf(empQuota.getQuotaDay()));
                            leaveQuotaDto.setIfAdvance(0);
                            leaveQuotaDto.setNowDistributeRule(waLeaveQuotaConfig.getNowDistributeRule());
                            leaveQuotaDto.setNowRoundingRule(waLeaveQuotaConfig.getNowRoundingRule());
                            leaveQuotaDto.setEmpStatus(sysEmpInfo.getStats());
                            leaveQuotaDto.setWaEmpQuotaQuotaDay(empQuota.getQuotaDay());
                            empQuota.setNowQuota(quotaService.calNowQuota(leaveQuotaDto));
                        }
                    }
                    empQuota.setIfAdvance(waLeaveQuotaConfig.getIfAdvance());
                }
                empQuotaList.add(empQuota);
            }
        }

        importService.fastUpdList(WaEmpQuota.class, "empQuotaId", empQuotaList);
    }


    /**
     * 更新配额结束时间
     *
     * @param addList
     * @param updList
     */
    @Transactional
    public void updateEmpQuotaDetail(Long corpId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        List<UpdRowDto> valueAddList = addList.get("wa_emp_quota_detail");
        List<UpdRowDto> valueUpdList = updList.get("wa_emp_quota_detail");
        List<UpdRowDto> allList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            allList.addAll(valueAddList);
        }
        if (CollectionUtils.isNotEmpty(valueUpdList)) {
            allList.addAll(valueUpdList);
        }

        if (CollectionUtils.isNotEmpty(allList)) {
            List<Integer> empQuotaIdList = new ArrayList<>();
            for (UpdRowDto updRowDto : allList) {
                Integer empQuotaId = null;
                for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                    if (saveItemDto.getItemCode().equals("emp_quota_id")) {
                        empQuotaId = ConvertHelper.intConvert(saveItemDto.getItemValue());
                    }
                }
                empQuotaIdList.add(empQuotaId);
            }

            Map params = new HashMap() {{
                put("corpid", corpId);
                put("nowTime", DateUtil.getCurrentTime(true));
                put("empQuotaids", "'{" + StringUtils.join(empQuotaIdList, ",") + "}'");
            }};

            waEmpQuotaDetailMapper.updateSurplusQuota(params);

            Map adjustParams = new HashMap();
            adjustParams.put("curDate", DateUtil.getOnlyDate());
            adjustParams.put("curTime", DateUtil.getCurrentTime(true));
            adjustParams.put("anyEmpQuotaIds", "'{" + StringUtils.join(empQuotaIdList, ",") + "}'");
            waMapper.updateBatchInitAdjustQuota(adjustParams);
            waMapper.updateAdjustQuotaNowKy(adjustParams);
            waMapper.updateAdjustQuotaTotalKy(adjustParams);
        }
    }


    /**
     * 更新日工作计划
     *
     * @param addList
     * @param updList
     */
    @Transactional
    public void updateShiftDef(Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        List<UpdRowDto> valueAddList = addList.get("wa_shift_def");
        List<UpdRowDto> valueUpdList = updList.get("wa_shift_def");
        List<UpdRowDto> allList = new ArrayList<UpdRowDto>();
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            allList.addAll(valueAddList);
        }
        if (CollectionUtils.isNotEmpty(valueUpdList)) {
            allList.addAll(valueUpdList);
        }
        for (UpdRowDto updRowDto : allList) {
            String code = null;
            for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                if (saveItemDto.getItemCode().equals("shift_def_code")) {
                    code = (String) saveItemDto.getItemValue();
                }
            }
            if (code != null) {
                WaShiftDefExample example = new WaShiftDefExample();
                example.createCriteria().andShiftDefCodeEqualTo(code).andBelongOrgidEqualTo(SessionHolder.getBelongOrgId());
                List<WaShiftDef> res = waShiftDefMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(res)) {
                    WaShiftDef record = res.get(0);
                    if (record.getNoonRestEnd() != null && record.getNoonRestStart() != null) {
                        record.setIsNoonRest(true);
                        record.setRestTotalTime(record.getNoonRestEnd() - record.getNoonRestStart());
                    } else {
                        record.setRestTotalTime(0);
                    }
                    if (record.getEndTime() != null && record.getStartTime() != null) {
                        record.setWorkTotalTime(record.getEndTime() - record.getStartTime() - record.getRestTotalTime());
                    }
                    waShiftDefMapper.updateByPrimaryKeySelective(record);
                }
            }
        }
    }


    /**
     * 打卡数据接入分析--兼容老版
     *
     * @param belongId
     * @param addList
     * @param updList
     * @throws Exception
     */
    @Transactional
    public void updateRegisterRecord(String belongId,
                                     Map<String, List<UpdRowDto>> addList,
                                     Map<String, List<UpdRowDto>> updList) throws Exception {
        updateRegisterRecordDetail(belongId, addList, updList, false, false, null, 1, false, null, null);
    }

    /**
     * 打卡数据接入分析
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param localErr
     * @param deviceErr
     * @param analyzeType
     * @throws Exception
     */
    @Transactional
    public void updateRegisterRecord(String belongId,
                                     Map<String, List<UpdRowDto>> addList,
                                     Map<String, List<UpdRowDto>> updList,
                                     Boolean localErr,
                                     Boolean deviceErr,
                                     Integer analyzeType) throws Exception {
        updateRegisterRecordDetail(belongId, addList, updList, localErr, deviceErr, analyzeType, 1, false, null, null);
    }

    /**
     * 打卡数据接入分析
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param localErr
     * @param deviceErr
     * @param isOnlyAnalyzeImportData
     * @throws Exception
     */
    @Transactional
    public void updateRegisterRecord(String belongId,
                                     Map<String, List<UpdRowDto>> addList,
                                     Map<String, List<UpdRowDto>> updList,
                                     Boolean localErr,
                                     Boolean deviceErr,
                                     Boolean isOnlyAnalyzeImportData) throws Exception {
        Integer analyzeType = null;
        if (isOnlyAnalyzeImportData != null) {
            if (isOnlyAnalyzeImportData) {
                analyzeType = 1;
            } else {
                analyzeType = 2;
            }
        }
        updateRegisterRecordDetail(belongId, addList, updList, localErr, deviceErr, analyzeType, 1, false, null, null);
    }

    @Transactional
    public void saveRegisterRecordEmpMapToCache(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        List<UpdRowDto> allList = importClockTriggerService.getImportDataListForReg(addList, updList);
        Map<String, List<Long>> belongDateAndEmpList = new HashedMap();
        for (UpdRowDto updRowDto : allList) {
            Long empid = null;
            Long reg_date_time = null;
            for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                if (saveItemDto.getItemCode().equals("empid")) {
                    empid = ConvertHelper.longConvert(saveItemDto.getItemValue());
                }
                if (saveItemDto.getItemCode().equals("reg_date_time")) {
                    reg_date_time = ConvertHelper.longConvert(saveItemDto.getItemValue());
                }
            }
            if (empid == null || reg_date_time == null) {
                continue;
            }
            Long date = DateUtil.getDateLong(reg_date_time * 1000, "yyyy-MM-dd", true);
            belongDateAndEmpList.computeIfAbsent(date.toString(), k -> new ArrayList<>());
            belongDateAndEmpList.get(date.toString()).add(empid);
        }
        final String key = "syncDataInputRegisterRecordEmpMap_" + belongId;
        redisTemplate.delete(key);
        redisTemplate.opsForHash().putAll(key, belongDateAndEmpList);
    }

    /**
     * 打卡数据接入分析
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param localErr
     * @param deviceErr
     * @param analyzeType
     * @param timeCheckType
     * @throws Exception
     */
    @Transactional
    public void updateRegisterRecord(String belongId,
                                     Map<String, List<UpdRowDto>> addList,
                                     Map<String, List<UpdRowDto>> updList,
                                     Boolean localErr,
                                     Boolean deviceErr,
                                     Integer analyzeType,
                                     Integer timeCheckType) throws Exception {
        updateRegisterRecordDetail(belongId, addList, updList, localErr, deviceErr, analyzeType, timeCheckType, false, null, null);
    }

    /**
     * 打卡数据接入分析
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param localErr
     * @param deviceErr
     * @param analyzeType
     * @param timeCheckType
     * @param includeBdkRecord
     * @throws Exception
     */
    @Transactional
    public void updateRegisterRecord(String belongId,
                                     Map<String, List<UpdRowDto>> addList,
                                     Map<String, List<UpdRowDto>> updList,
                                     Boolean localErr,
                                     Boolean deviceErr,
                                     Integer analyzeType,
                                     Integer timeCheckType,
                                     Boolean includeBdkRecord) throws Exception {
        updateRegisterRecordDetail(belongId, addList, updList, localErr, deviceErr, analyzeType, timeCheckType, includeBdkRecord, 2, 2);
    }

    /**
     * 打卡数据接入分析
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param localErr
     * @param deviceErr
     * @param analyzeType
     * @param timeCheckType
     * @param includeBdkRecord
     * @param kyAnalyzeType
     * @param filterType
     * @throws Exception
     */
    @Transactional
    public void updateRegisterRecord(String belongId,
                                     Map<String, List<UpdRowDto>> addList,
                                     Map<String, List<UpdRowDto>> updList,
                                     Boolean localErr,
                                     Boolean deviceErr,
                                     Integer analyzeType,
                                     Integer timeCheckType,
                                     Boolean includeBdkRecord,
                                     Integer kyAnalyzeType,
                                     Integer filterType) throws Exception {
        updateRegisterRecordDetail(belongId, addList, updList, localErr, deviceErr, analyzeType, timeCheckType, includeBdkRecord, kyAnalyzeType, filterType);
    }

    /**
     * 考勤打卡记录导入/接入后-进行打卡分析
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param localErr         是否分析地点异常 (如果分析的打卡数据有地点异常则保留地点异常，暂时不增加地点异常分析逻辑，因为签到接入时，不会导入打卡地点)
     * @param deviceErr        是否分析设备异常（如果分析的打卡数据有设备异常则保留设备异常，暂时不增加设备异常分析逻辑，因为签到接入时，不会导入设备号）
     * @param analyzeType      数据分析类型 1 只分析导入的日期 2 分析所有日期
     * @param timeCheckType    时间异常校验类型 1 精确匹配 2 模糊匹配
     * @param includeBdkRecord 是否分析补打卡数据
     * @param kyAnalyzeType    跨夜签退分析类型 1:当天如果存在签退卡，就不去第二天查找符合条件的签退卡 2 当天如果存在签退卡，也会去第二天查找符合条件的签退卡，如果找到了，则将第二天的卡作为前一天的签退卡
     * @param filterType       抓取跨夜签退过滤类型 1 返回最早的一条 2 返回最晚的一条
     * @throws Exception
     */
    @Transactional
    public void updateRegisterRecordDetail(String belongId,
                                           Map<String, List<UpdRowDto>> addList,
                                           Map<String, List<UpdRowDto>> updList,
                                           Boolean localErr,
                                           Boolean deviceErr,
                                           Integer analyzeType,
                                           Integer timeCheckType,
                                           Boolean includeBdkRecord,
                                           Integer kyAnalyzeType,
                                           Integer filterType) throws Exception {
        if (openNewClockImportTrigger) {
            importClockTriggerService.analyseRegisterRecordV2(belongId, addList, updList);
        } else {
            importClockTriggerService.updateRegisterRecordDetail(belongId, addList, updList, localErr, deviceErr,
                    analyzeType, timeCheckType, includeBdkRecord, kyAnalyzeType, filterType);
        }
    }

    @Deprecated
    @Transactional
    public void analyzeRegisterRecord(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        importClockTriggerService.analyzeRegisterRecord(belongId, addList, updList);
    }

    @Transactional
    @Deprecated
    public void updateRegisterShiftId(long startdate, long enddate, String belongId) {
        importClockTriggerService.updateRegisterShiftId(startdate, enddate, belongId);
    }

    public void importRegister(Map<String, Object> params, Map<String, List<WaRegisterRecord>> listMap) throws Exception {
        importClockTriggerService.importRegister(params, listMap);
    }

    /**
     * 加班时长折算
     *
     * @param timeDuration
     * @param parseRule    1 无 2 '向下取整15分钟' 3 '向上取整15分钟' 4 '向下取整30分钟' 5 '向上取整30分钟'
     * @return
     */
    public Integer analyzeOtTimeByRule(Integer timeDuration, Integer parseRule) {
        if (parseRule == null || parseRule == 1) {
            return timeDuration;
        } else if (parseRule == 2) {
            return getOtRoundingRule(timeDuration.doubleValue(), false, 15);
        } else if (parseRule == 3) {
            return getOtRoundingRule(timeDuration.doubleValue(), true, 15);
        } else if (parseRule == 4) {
            return getOtRoundingRule(timeDuration.doubleValue(), false, 30);
        } else if (parseRule == 5) {
            return getOtRoundingRule(timeDuration.doubleValue(), true, 30);
        } else {
            return timeDuration;
        }
    }

    public Integer getOtRoundingRule(Double min, boolean isUp, Integer roundMin) {
        double mod = min % roundMin;
        //向上取整
        if (isUp) {
            min = min + (roundMin - mod);
        } else {
            //向下取整
            min = min - mod;
        }
        return min.intValue();
    }

    /**
     * 加班数据导入
     *
     * @param belongId
     * @param addList
     * @param updList
     * @throws Exception
     */
    public void updateEmpOvertime(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) throws Exception {
        updateEmpOvertimeDetail(belongId, addList, updList, null, null, null);
    }

    /**
     * 加班数据导入(支持构造登录用户信息)
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param userId
     * @param empId
     * @throws Exception
     */
    public void updateEmpOvertime(String belongId,
                                  Map<String, List<UpdRowDto>> addList,
                                  Map<String, List<UpdRowDto>> updList,
                                  Long userId,
                                  Long empId) throws Exception {
        log.info("update Emp Overtime when Import belongId={} userId={}, empId={}", belongId, userId, empId);
        try {
            UserContext.doInitSecurityUserInfo(belongId, null != userId ? userId.toString() : null,
                    null != empId ? empId.toString() : null, null, null, null);
            updateEmpOvertimeDetail(belongId, addList, updList, null, null, null);
        } finally {
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        UserContext.removeSecurityUserInfo();
                    }
                });
            } else {
                UserContext.removeSecurityUserInfo();
            }
        }
    }

    /**
     * 加班数据导入
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param checkList   数据接入时，数据接入失败校验信息
     * @param parseRule   加班时长折算规则 1 无 2 '向下取整15分钟' 3 '向上取整15分钟' 4 '向下取整30分钟' 5 '向上取整30分钟'
     * @param smallMinute 最小加班时长
     * @throws Exception
     */
    public void updateEmpOvertime(String belongId,
                                  Map<String, List<UpdRowDto>> addList,
                                  Map<String, List<UpdRowDto>> updList,
                                  List<Map> checkList,
                                  Integer parseRule,
                                  Integer smallMinute) throws Exception {
        updateEmpOvertimeDetail(belongId, addList, updList, checkList, parseRule, smallMinute);
    }

    /**
     * 加班数据导入(支持构造登录用户信息)
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param checkList
     * @param parseRule
     * @param smallMinute
     * @param userId
     * @param empId
     * @throws Exception
     */
    public void updateEmpOvertime(String belongId,
                                  Map<String, List<UpdRowDto>> addList,
                                  Map<String, List<UpdRowDto>> updList,
                                  List<Map> checkList,
                                  Integer parseRule,
                                  Integer smallMinute,
                                  Long userId,
                                  Long empId) throws Exception {
        log.info("updateEmpOvertime2 belongId={} userId={}, empId={}", belongId, userId, empId);
        try {
            UserContext.doInitSecurityUserInfo(belongId, null != userId ? userId.toString() : null,
                    null != empId ? empId.toString() : null, null, null, null);

            updateEmpOvertimeDetail(belongId, addList, updList, checkList, parseRule, smallMinute);
        } finally {
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        UserContext.removeSecurityUserInfo();
                    }
                });
            } else {
                UserContext.removeSecurityUserInfo();
            }
        }
    }

    /**
     * 加班数据导入明细
     *
     * @param belongId
     * @param addList
     * @param updList
     * @param parseRule   1 无 2 '向下取整15分钟' 3 '向上取整15分钟' 4 '向下取整30分钟' 5 '向上取整30分钟'
     * @param smallMinute 加班最小分钟数限制
     * @param checkList   效验日志集合
     * @throws Exception
     */
    public void updateEmpOvertimeDetail(String belongId,
                                        Map<String, List<UpdRowDto>> addList,
                                        Map<String, List<UpdRowDto>> updList,
                                        List<Map> checkList,
                                        Integer parseRule,
                                        Integer smallMinute) throws Exception {
        if (addList != null && !addList.isEmpty()) {
            List<UpdRowDto> valueAddList = addList.get("wa_emp_overtime");
            if (CollectionUtils.isNotEmpty(valueAddList)) {
                List<WaEmpOvertime> overtimeList = new ArrayList<>();
                List<WaEmpOvertimeDetail> detailList = new ArrayList<>();
                List<Integer> removeOverTimeList = new ArrayList<>();
                Map<String, Integer> compensateTypeMap = new HashMap<>();
                Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongId);

                for (UpdRowDto rowDto : valueAddList) {
                    Long empid = null;
                    Long startTime = null;
                    Long endTime = null;
                    Integer compensateType = null;
                    Long lastApprovalTime = null;
                    Short status = null;
                    Integer overtimeTypeId = null;
                    String reason = null;
                    Long belongDate = null;// 加班归属日期
                    for (SaveItemDto saveItemDto : rowDto.getRow()) {
                        if ("start_time".equals(saveItemDto.getItemCode())) {
                            startTime = (Long) saveItemDto.getItemValue();
                        } else if ("end_time".equals(saveItemDto.getItemCode())) {
                            endTime = (Long) saveItemDto.getItemValue();
                        } else if ("empid".equals(saveItemDto.getItemCode())) {
                            empid = ConvertHelper.longConvert(saveItemDto.getItemValue());
                        } else if ("last_approval_time".equals(saveItemDto.getItemCode())) {
                            lastApprovalTime = (Long) saveItemDto.getItemValue();
                        } else if ("compensate_type".equals(saveItemDto.getItemCode())) {
                            compensateType = (Integer) saveItemDto.getItemValue();
                        } else if ("status".equals(saveItemDto.getItemCode())) {
                            status = Short.valueOf(saveItemDto.getItemValue().toString());
                        } else if ("overtime_type_id".equals(saveItemDto.getItemCode())) {
                            overtimeTypeId = ConvertHelper.intConvert(saveItemDto.getItemValue());
                        } else if ("reason".equals(saveItemDto.getItemCode())) {
                            reason = (String) saveItemDto.getItemValue();
                        } else if ("belong_date".equals(saveItemDto.getItemCode())) {
                            belongDate = (Long) saveItemDto.getItemValue();
                        }
                    }

                    Integer otId = ConvertHelper.intConvert(rowDto.getId());
                    if (null == startTime || null == endTime) {
                        log.info("ImportTriggerService.updateEmpOvertimeDetail empid={} time params empty", empid);
                        removeOverTimeList.add(otId);
                        continue;
                    }

                    WaOvertimeType overtimeType = waOvertimeTypeMapper.selectByPrimaryKey(overtimeTypeId);
                    if (null != overtimeType) {
                        compensateType = overtimeType.getCompensateType();
                    }

                    SysEmpInfo empInfo = empInfoMapper.selectByPrimaryKey(empid);
                    int tmtype = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
                    if (tmtype == 0) {
                        tmtype = 1;
                    }
                    empInfo.setTmType(tmtype);

                    // 加班时间
                    long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
                    long endDate = DateUtil.getOnlyDate(new Date(endTime * 1000));
                    Long preDate = DateUtil.addDate(startDate * 1000, -1);
                    Long nextDate = DateUtil.addDate(endDate * 1000, 1);

                    // 查询排班
                    Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(belongId, empid, tmtype,
                            preDate, nextDate);
                    if (MapUtils.isEmpty(pbMap) || pbMap.get(startDate) == null || pbMap.get(endDate) == null) {
                        log.info("ImportTriggerService.updateEmpOvertimeDetail empid={} getEmpWaWorktimeDetail empty", empid);
                        removeOverTimeList.add(otId);
                        continue;
                    }

                    WaWorktimeDetail startWorkTimeDetail = pbMap.get(startDate);
                    WaWorktimeDetail endWorkTimeDetail = pbMap.get(endDate);

                    // 指定加班归属日期
                    WaWorktimeDetail selectOtDateWorktimeDetail = null;
                    Long selectOvertimeDate = null;
                    if (null != belongDate && belongDate > 0) {
                        selectOvertimeDate = belongDate;
                        selectOtDateWorktimeDetail = pbMap.get(selectOvertimeDate);
                    } else if (StringUtils.isNotBlank(reason) && reason.contains("|")) {
                        // 兼容历史逻辑：将加班归属日期放到理由一列中，并使用竖线符号分割，如：理由|2025-07-17
                        String[] reasonSplit = reason.split("\\|");
                        reason = reasonSplit[0];
                        selectOvertimeDate = DateUtil.getTimesampByDateStr2(reasonSplit[1]);
                        selectOtDateWorktimeDetail = selectOvertimeDate > 0 ? pbMap.get(selectOvertimeDate) : null;
                    }

                    // 生成加班主数据
                    WaEmpOvertime overtime = new WaEmpOvertime();
                    overtime.setStartTime(startTime);
                    overtime.setEndTime(endTime);
                    overtime.setOtId(otId);
                    overtime.setEmpid(empid);
                    overtime.setCompensateType(compensateType);
                    overtime.setStatus(status);
                    overtime.setDataSource("IMPORT");
                    if (null != reason && "HIS_IMPORT".equals(reason.trim())) {
                        overtime.setDataSource("HIS_IMPORT");
                        overtime.setReason("");
                    } else {
                        overtime.setReason(reason);
                    }
                    if (lastApprovalTime == null) {
                        lastApprovalTime = DateUtil.getOnlyDate();
                        overtime.setLastApprovalTime(lastApprovalTime);
                    }

                    // 前一日班次
                    WaWorktimeDetail preWorkTimeDetail = pbMap.get(preDate);
                    Long preOffDutyEndTime = overtimeApplyService.getShiftOffDutyEndTime(shiftDefMap, preWorkTimeDetail);

                    if (null == selectOtDateWorktimeDetail) {
                        // 判断加班是否属于前一天的班次
                        boolean belongPreDay = null != preOffDutyEndTime && endTime <= preOffDutyEndTime;
                        overtime.setDateType(belongPreDay
                                ? preWorkTimeDetail.getDateType().shortValue()
                                : startWorkTimeDetail.getDateType().shortValue());
                    } else {
                        overtime.setDateType(selectOtDateWorktimeDetail.getDateType().shortValue());
                    }

                    // 计算加班时长
                    long totalDuration = endTime - startTime;
                    if (null == selectOtDateWorktimeDetail) {
                        if (endDate > startDate) {
                            totalDuration -= overtimeApplyService.calRestTotalTime(belongId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
                            totalDuration -= overtimeApplyService.calRestTotalTime(belongId, startTime, endTime, endWorkTimeDetail, shiftDefMap);
                        } else {
                            totalDuration -= overtimeApplyService.calRestTotalTime(belongId, startTime, endTime, preWorkTimeDetail, shiftDefMap);
                            totalDuration -= overtimeApplyService.calRestTotalTime(belongId, startTime, endTime, startWorkTimeDetail, shiftDefMap);
                        }
                    } else {
                        totalDuration -= overtimeApplyService.calRestTotalTime(belongId, startTime, endTime, selectOtDateWorktimeDetail, shiftDefMap);
                    }
                    totalDuration = totalDuration / 60;
                    overtime.setOtDuration(analyzeOtTimeByRule((int) totalDuration, parseRule));
                    if (overtimeType != null) {
                        overtime.setOvertimeTypeId(overtimeType.getOvertimeTypeId());
                    }

                    // 加班最小时长校验
                    if (null != smallMinute && overtime.getOtDuration() < smallMinute) {
                        String errorMsg = "工号【" + empInfo.getWorkno() + "】姓名【" + empInfo.getEmpName() + "】加班分钟数为【" + overtime.getOtDuration() + "】小于最小加班分钟数【" + smallMinute + "】,请检查数据后再进行导入";
                        if (null != checkList) {
                            removeOverTimeList.add(overtime.getOtId());
                            Map modelMap = ClassCompareUtil.getFieldNameAndValues(overtime);
                            this.addErrorMsg(modelMap, checkList, errorMsg);
                            continue;
                        } else {
                            throw new CDException(errorMsg);
                        }
                    }

                    // 设置加班补偿类型，如果补偿类型为空，查询默认的加班补偿类型（当且仅当该日期类型的补偿类型只有一种）
                    if (compensateType == null) {
                        Integer belongDateDaytype = null == selectOtDateWorktimeDetail
                                ? startWorkTimeDetail.getDateType() : selectOtDateWorktimeDetail.getDateType();

                        if (compensateTypeMap.containsKey(empid + "_" + belongDateDaytype)) {
                            overtime.setCompensateType(compensateTypeMap.get(empid + "_" + belongDateDaytype));
                        } else {
                            List<Map> firstDayListMap = waConfigMapper.getCompensateTypeList(empid, belongDateDaytype);
                            if (CollectionUtils.isNotEmpty(firstDayListMap)) {
                                Integer comType = (Integer) firstDayListMap.get(0).get("value");
                                // 员工自由选择 默认付现
                                compensateType = comType == 4 ? 1 : comType;
                                overtime.setCompensateType(compensateType);
                                compensateTypeMap.put(empid + "_" + belongDateDaytype, overtime.getCompensateType());
                            }
                        }
                    }

                    // 检查跨夜加班是否归属至加班开始日期
                    boolean overtimeBelongStartDate = mobileV16Service.checkOvertimeBelong(empid);

                    // 保存加班明细
                    if (null == selectOtDateWorktimeDetail) {
                        if (endDate > startDate) {
                            WaEmpOvertimeDetail startDayDetail = new WaEmpOvertimeDetail();
                            startDayDetail.setCrttime(DateUtil.getCurrentTime(true));
                            startDayDetail.setCrtuser(SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId());
                            startDayDetail.setOvertimeId(overtime.getOtId());
                            startDayDetail.setDateType(startWorkTimeDetail.getDateType().shortValue());
                            startDayDetail.setOvertimeTypeId(overtime.getOvertimeTypeId());
                            startDayDetail.setStartTime(startTime);
                            startDayDetail.setEndTime(endDate);
                            startDayDetail.setRealDate(startDate);
                            long startTimeDuration = startDayDetail.getEndTime() - startDayDetail.getStartTime();
                            startTimeDuration -= overtimeApplyService.calRestTotalTime(belongId, startDayDetail.getStartTime(), startDayDetail.getEndTime(), startWorkTimeDetail, shiftDefMap);
                            startTimeDuration = startTimeDuration / 60;
                            startDayDetail.setTimeDuration(analyzeOtTimeByRule((int) startTimeDuration, parseRule));

                            WaEmpOvertimeDetail endDayDetail = new WaEmpOvertimeDetail();
                            endDayDetail.setCrttime(DateUtil.getCurrentTime(true));
                            endDayDetail.setCrtuser(SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId());
                            endDayDetail.setOvertimeId(overtime.getOtId());
                            endDayDetail.setDateType(endWorkTimeDetail.getDateType().shortValue());
                            endDayDetail.setOvertimeTypeId(overtime.getOvertimeTypeId());
                            if (!Objects.equals(endWorkTimeDetail.getDateType(), startWorkTimeDetail.getDateType())) {
                                endDayDetail.setOvertimeTypeId(Optional.ofNullable(getSecondOvertimeTypeId(overtime.getEmpid(),
                                        endWorkTimeDetail.getDateType(), endDate)).orElse(endDayDetail.getOvertimeTypeId()));
                            }
                            endDayDetail.setStartTime(endDate);
                            endDayDetail.setEndTime(endTime);
                            endDayDetail.setRealDate(endDate);
                            endDayDetail.setTimeDuration((overtime.getOtDuration() - startDayDetail.getTimeDuration()));

                            if (overtimeBelongStartDate) {
                                Long startOffDutyEndTime = overtimeApplyService.getShiftOffDutyEndTime(shiftDefMap, startWorkTimeDetail);
                                if (endTime <= startOffDutyEndTime) {
                                    startDayDetail.setEndTime(endTime);
                                    startDayDetail.setTimeDuration(overtime.getOtDuration());
                                    detailList.add(startDayDetail);
                                } else {
                                    startDayDetail.setEndTime(startOffDutyEndTime);
                                    long timeDuration = startDayDetail.getEndTime() - startDayDetail.getStartTime();
                                    timeDuration -= overtimeApplyService.calRestTotalTime(belongId, startDayDetail.getStartTime(), startDayDetail.getEndTime(), startWorkTimeDetail, shiftDefMap);
                                    timeDuration -= overtimeApplyService.calRestTotalTime(belongId, startDayDetail.getStartTime(), startDayDetail.getEndTime(), endWorkTimeDetail, shiftDefMap);
                                    timeDuration = timeDuration / 60;
                                    startDayDetail.setTimeDuration(analyzeOtTimeByRule((int) timeDuration, parseRule));
                                    detailList.add(startDayDetail);

                                    endDayDetail.setStartTime(startOffDutyEndTime);
                                    endDayDetail.setTimeDuration(overtime.getOtDuration() - startDayDetail.getTimeDuration());
                                    detailList.add(endDayDetail);
                                }
                            } else {
                                detailList.add(startDayDetail);
                                detailList.add(endDayDetail);
                            }
                        } else {
                            WaEmpOvertimeDetail currentDayDetail = new WaEmpOvertimeDetail();
                            currentDayDetail.setCrttime(DateUtil.getCurrentTime(true));
                            currentDayDetail.setCrtuser(SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId());
                            currentDayDetail.setOvertimeId(overtime.getOtId());
                            currentDayDetail.setDateType(startWorkTimeDetail.getDateType().shortValue());
                            currentDayDetail.setOvertimeTypeId(overtime.getOvertimeTypeId());
                            currentDayDetail.setStartTime(startTime);
                            currentDayDetail.setEndTime(endTime);
                            currentDayDetail.setRealDate(startDate);
                            currentDayDetail.setTimeDuration(overtime.getOtDuration());
                            if (null != preOffDutyEndTime) {
                                if (endTime <= preOffDutyEndTime) {
                                    currentDayDetail.setDateType(preWorkTimeDetail.getDateType().shortValue());
                                    currentDayDetail.setRealDate(preDate);
                                    detailList.add(currentDayDetail);
                                } else if (startTime >= preOffDutyEndTime) {
                                    detailList.add(currentDayDetail);
                                } else {
                                    WaEmpOvertimeDetail preDayDetail = FastjsonUtil.convertObject(currentDayDetail, WaEmpOvertimeDetail.class);
                                    if (!Objects.equals(preWorkTimeDetail.getDateType(), startWorkTimeDetail.getDateType())) {
                                        preDayDetail.setOvertimeTypeId(Optional.ofNullable(getSecondOvertimeTypeId(overtime.getEmpid(),
                                                preWorkTimeDetail.getDateType(), preDate)).orElse(currentDayDetail.getOvertimeTypeId()));
                                    }
                                    preDayDetail.setDateType(preWorkTimeDetail.getDateType().shortValue());
                                    preDayDetail.setRealDate(preDate);
                                    preDayDetail.setEndTime(preOffDutyEndTime);
                                    long timeDuration = preDayDetail.getEndTime() - preDayDetail.getStartTime();
                                    timeDuration -= overtimeApplyService.calRestTotalTime(belongId, preDayDetail.getStartTime(), preDayDetail.getEndTime(), preWorkTimeDetail, shiftDefMap);
                                    timeDuration -= overtimeApplyService.calRestTotalTime(belongId, preDayDetail.getStartTime(), preDayDetail.getEndTime(), startWorkTimeDetail, shiftDefMap);
                                    timeDuration = timeDuration / 60;
                                    preDayDetail.setTimeDuration(analyzeOtTimeByRule((int) timeDuration, parseRule));
                                    detailList.add(preDayDetail);

                                    currentDayDetail.setStartTime(preOffDutyEndTime);
                                    currentDayDetail.setTimeDuration(overtime.getOtDuration() - preDayDetail.getTimeDuration());
                                    detailList.add(currentDayDetail);
                                }
                            } else {
                                detailList.add(currentDayDetail);
                            }
                        }
                    } else {
                        // 指定加班归属日期
                        WaEmpOvertimeDetail currentDayDetail = new WaEmpOvertimeDetail();
                        currentDayDetail.setCrttime(DateUtil.getCurrentTime(true));
                        currentDayDetail.setCrtuser(SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId());
                        currentDayDetail.setOvertimeId(overtime.getOtId());
                        currentDayDetail.setDateType(selectOtDateWorktimeDetail.getDateType().shortValue());
                        currentDayDetail.setOvertimeTypeId(overtime.getOvertimeTypeId());
                        currentDayDetail.setStartTime(startTime);
                        currentDayDetail.setEndTime(endTime);
                        currentDayDetail.setRealDate(selectOvertimeDate);
                        currentDayDetail.setTimeDuration(overtime.getOtDuration());
                        detailList.add(currentDayDetail);
                    }

                    // 当加班单审批状态为审批中时则发起加班单审批流
                    if (status != null && status == 1) {
                        String businessKey = String.valueOf(overtime.getOtId());
                        Map<String, String> map = BeanUtil.objectToMap(overtime);
                        map.put("empid", empid.toString());
                        MathContext mc = new MathContext(2, RoundingMode.HALF_DOWN);
                        BigDecimal a = new BigDecimal(overtime.getOtDuration());
                        BigDecimal b = a.divide(new BigDecimal(60), mc);
                        map.put("totalTime", b.toString());
                        if (compensateType != null) {
                            map.put("otType", WaDateTypeEnum.getDesc(overtime.getDateType().intValue()) + CompensateTypeEnum.getDesc(compensateType));
                        }
                        WfBeginWorkflowDto dto = new WfBeginWorkflowDto();
                        dto.setFuncCode(BusinessCodeEnum.OVERTIME.getCode());
                        dto.setBusinessId(businessKey);
                        dto.setApplicantId(empInfo.getEmpid().toString());
                        dto.setApplicantName(empInfo.getEmpName());
                        dto.setEventTime(startDate * 1000);
                        dto.setEventEndTime(endDate * 1000);
                        dto.setTimeSlot(getOvertimeTimeSlot(overtime));
                        Result result = wfRegisterFeign.begin(dto);
                        if (null == result || !result.isSuccess()) {
                            mobileV16Service.addOtQuota(overtime);
                        }
                        overtime.setLastApprovalTime(null);
                    }

                    if (overtimeType != null && null != overtimeType.getMinOvertimeUnit()) {
                        float convertDuration = OvertimeUnitEnum.HOUR.getTime(null,
                                overtime.getOtDuration().longValue() * 60, overtimeType.getMinOvertimeUnit(),
                                Optional.ofNullable(overtimeType.getMinOvertimeUnitType()).orElse(2));
                        overtime.setOtDuration(BigDecimal.valueOf(convertDuration / 60).intValue());
                    }
                    overtimeList.add(overtime);
                }

                if (!removeOverTimeList.isEmpty()) {
                    WaEmpOvertimeExample example = new WaEmpOvertimeExample();
                    example.createCriteria().andOtIdIn(removeOverTimeList);
                    log.info("接入加班记录过滤无效数据Id为:{" + StringUtils.join(removeOverTimeList, ",") + "}");
                    waEmpOvertimeMapper.deleteByExample(example);
                }
                if (CollectionUtils.isNotEmpty(overtimeList)) {
                    importService.fastUpdList(WaEmpOvertime.class, "otId", overtimeList);
                }
                if (CollectionUtils.isNotEmpty(detailList)) {
                    importService.fastInsertList(WaEmpOvertimeDetail.class, "detailId", detailList);
                }
            }
        }
    }

    public Integer getSecondOvertimeTypeId(Long empId, Integer dateType, Long endDate) {
        List<WaOvertimeType> overtimeTypes = overTimeTypeDo.getOvertimeType(empId, dateType, endDate);
        if (CollectionUtils.isEmpty(overtimeTypes)) {
            return null;
        }
        WaOvertimeType overtimeType = overtimeTypes.get(0);
        return overtimeType.getOvertimeTypeId();
    }

    public String getOvertimeTimeSlot(WaEmpOvertime overtime) {
        return String.format("%s~%s", DateUtil.getTimeStrByTimesamp4(overtime.getStartTime()), DateUtil.getTimeStrByTimesamp4(overtime.getEndTime()));
    }

    public void addErrorMsg(Map m, List<Map> checkList, String errorMsg) {
        Map checkRow = new HashMap();
        checkRow.putAll(m);
        checkRow.put("msg", errorMsg);
        checkList.add(checkRow);
    }

    /**
     * 请假额度扣减
     *
     * @param leaveList
     * @throws Exception
     */
    public void processQuotaSigle(List<WaEmpLeave> leaveList, Map<Integer, WaLeaveType> corpAllLeaveTypeMap, Long userId) throws Exception {
        if (CollectionUtils.isNotEmpty(leaveList)) {
            List<Integer> leaveIds = leaveList.stream().map(WaEmpLeave::getLeaveId).collect(Collectors.toList());
            //查询请假具体每天的时间
            WaLeaveDaytimeExample daytimeExample = new WaLeaveDaytimeExample();
            daytimeExample.createCriteria().andLeaveIdIn(leaveIds);
            List<WaLeaveDaytime> listDaytime = waLeaveDaytimeMapper.selectByExample(daytimeExample);
            if (CollectionUtils.isEmpty(listDaytime)) {
                return;
            }
            Map<Integer, List<WaLeaveDaytime>> daytimeMap = listDaytime.stream().collect(Collectors.groupingBy(WaLeaveDaytime::getLeaveId));
            for (WaEmpLeave empLeave : leaveList) {
                if (!corpAllLeaveTypeMap.containsKey(empLeave.getLeaveTypeId())) {
                    continue;
                }
                WaLeaveType waLeaveType = corpAllLeaveTypeMap.get(empLeave.getLeaveTypeId());
                if (waLeaveType != null && waLeaveType.getLeaveType() != 4) {
                    //请假配额校验或者扣减 QuotaRestrictionType 额度限制类型： 1 限额、2 不限额
                    if (waLeaveType.getQuotaRestrictionType() != null && QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType())) {
                        //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
                        Integer quotaType = waLeaveType.getQuotaType();
                        if (quotaType == null) {
                            if (waLeaveType.getLeaveType() == 3) {
                                //假期类型为调休
                                quotaType = 2;
                            } else {
                                quotaType = 1;
                            }
                        }
                        List<WaLeaveDaytime> daytimeList = daytimeMap.get(empLeave.getLeaveId());
                        daytimeList = daytimeList.stream().sorted(Comparator.comparing(WaLeaveDaytime::getLeaveDate)).collect(Collectors.toList());
                        List<Integer> leaveDaytimeIdList = daytimeList.stream().map(WaLeaveDaytime::getLeaveDaytimeId).collect(Collectors.toList());
                        if (empLeave.getStatus() == 1) {
                            if (quotaType == 2) {
                                mobileV16Service.checkEmpCompensatoryQuota(userId, daytimeList, empLeave.getEmpid(), waLeaveType, false, Integer.valueOf(empLeave.getStatus()), true);
                                List<Long> ids = leaveDaytimeIdList.stream().map(Long::valueOf).collect(Collectors.toList());
                                waEmpCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(userId, System.currentTimeMillis(), ids, Integer.valueOf(empLeave.getStatus()));
                            } else {
                                mobileV16Service.checkEmpQuota(userId, daytimeList, empLeave.getEmpid(), waLeaveType, false, Integer.valueOf(empLeave.getStatus()), true);
                                waLeaveQuotaUseMapper.updateWaEmpQuota(userId, System.currentTimeMillis(), leaveDaytimeIdList, Integer.valueOf(empLeave.getStatus()));
                            }
                        } else if (empLeave.getStatus() == 2) {
                            if (quotaType == 2) {
                                mobileV16Service.checkEmpCompensatoryQuota(userId, daytimeList, empLeave.getEmpid(), waLeaveType, false, Integer.valueOf(empLeave.getStatus()), true);
                                QueryWrapper<WaCompensatoryQuotaUse> qw = new QueryWrapper<>();
                                qw.in("leave_daytime_id", leaveDaytimeIdList);
                                WaCompensatoryQuotaUse waCompensatoryQuotaUse = new WaCompensatoryQuotaUse();
                                waCompensatoryQuotaUse.setApprovalStatus(Integer.valueOf(empLeave.getStatus()));
                                waCompensatoryQuotaUse.setUpdateBy(Long.valueOf(userId.toString()));
                                waCompensatoryQuotaUse.setUpdateTime(System.currentTimeMillis());
                                waCompensatoryQuotaUseMapper.update(waCompensatoryQuotaUse, qw);
                                List<Long> ids = leaveDaytimeIdList.stream().map(Long::valueOf).collect(Collectors.toList());
                                waEmpCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(userId, System.currentTimeMillis(), ids, Integer.valueOf(empLeave.getStatus()));
                            } else {
                                mobileV16Service.checkEmpQuota(userId, daytimeList, empLeave.getEmpid(), waLeaveType, false, Integer.valueOf(empLeave.getStatus()), true);
                                WaLeaveQuotaUseExample useExample = new WaLeaveQuotaUseExample();
                                useExample.createCriteria().andLeaveDaytimeIdIn(leaveDaytimeIdList);
                                WaLeaveQuotaUse quotaUse = new WaLeaveQuotaUse();
                                quotaUse.setApprovalStatus(Integer.valueOf(empLeave.getStatus()));
                                quotaUse.setUpdateBy(userId);
                                quotaUse.setUpdateTime(System.currentTimeMillis());
                                waLeaveQuotaUseMapper.updateByExampleSelective(quotaUse, useExample);
                                waLeaveQuotaUseMapper.updateWaEmpQuota(userId, System.currentTimeMillis(), leaveDaytimeIdList, Integer.valueOf(empLeave.getStatus()));
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 请假数据导入
     *
     * @param addList
     * @param updList
     * @throws Exception
     */
    @Transactional
    public void updateEmpLeave(String belongId, Map<String, List<UpdRowDto>> addList,
                               Map<String, List<UpdRowDto>> updList, Long userId, Long empId) throws Exception {
        log.info("updateEmpLeave belongId={} userId={}, empId={}", belongId, userId, empId);
        try {
            UserContext.doInitSecurityUserInfo(belongId, null != userId ? userId.toString() : null,
                    null != empId ? empId.toString() : null, null, null, null);
            updateEmpLeave(belongId, addList, updList);
        } finally {
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        UserContext.removeSecurityUserInfo();
                    }
                });
            } else {
                UserContext.removeSecurityUserInfo();
            }
        }
    }

    /**
     * 请假数据导入
     *
     * @param addList
     * @param updList
     * @throws Exception
     */
    @Transactional
    public void updateEmpLeave(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) throws Exception {
        if (addList != null && addList.size() > 0) {
            List<UpdRowDto> leaveAddList = addList.get("wa_emp_leave");
            List<UpdRowDto> leaveTimeAddList = addList.get("wa_emp_leave_time");

            //查询公司下所有的假期类型
            Map<Integer, WaLeaveType> corpAllLeaveTypeMap = waCommonService.getCorpAllLeaveTypeMap(belongId);
            //查询公司全部班次
            Map<Integer, WaShiftDef> shiftMap = waCommonService.getCorpAllShiftDef(belongId);

            Map<Long, SysEmpInfo> empInfoMap = new HashMap<>();
            Map<Integer, Long> startDateMap = new HashMap<>();
            Map<Integer, Long> endDateMap = new HashMap<>();

            List<WaLeaveDaytime> allLeaveDaytimeAddList = new ArrayList<>();
            List<WaEmpLeaveTime> allLeaveTimeUpdList = new ArrayList<>();
            List<WaEmpLeave> allLeaveUpdList = new ArrayList<>();

            int cnt = 0;
            boolean hisImport = false;
            for (UpdRowDto rowDto : leaveTimeAddList) {
                Long empid = null;
                Integer leaveTypeId = null;
                Long startTime = null;
                Long endTime = null;
                Integer leaveId = null;
                BigDecimal totalTimeDuration = new BigDecimal(0);
                Long lastApprovalTime = null;
                String timeSlot = "";
                Short period = null;
                String shalfDay = null;
                String ehalfDay = null;
                Long leaveTimeId = rowDto.getId();
                Short leaveStatus = ApprovalStatusEnum.PASSED.getIndex().shortValue();
                String reason = null;
                for (SaveItemDto saveItemDto : rowDto.getRow()) {
                    if ("start_time".equals(saveItemDto.getItemCode())) {
                        startTime = (Long) saveItemDto.getItemValue();
                    } else if ("end_time".equals(saveItemDto.getItemCode())) {
                        endTime = (Long) saveItemDto.getItemValue();
                    } else if ("leave_id".equals(saveItemDto.getItemCode())) {
                        leaveId = ConvertHelper.intConvert(saveItemDto.getItemValue());
                    } else if ("period_type".equals(saveItemDto.getItemCode())) {
                        period = (Short) saveItemDto.getItemValue();
                    } else if ("shalf_day".equals(saveItemDto.getItemCode())) {
                        shalfDay = (String) saveItemDto.getItemValue();
                    } else if ("ehalf_day".equals(saveItemDto.getItemCode())) {
                        ehalfDay = (String) saveItemDto.getItemValue();
                    } else if ("leave_time_id".equals(saveItemDto.getItemCode()) && null == leaveTimeId) {
                        leaveTimeId = Long.valueOf(Objects.toString(saveItemDto.getItemValue()));
                    }
                }
                int realRowCnt = cnt;
                if (CollectionUtils.isNotEmpty(leaveAddList) && !leaveAddList.get(cnt).getRowCnt().equals(rowDto.getRowCnt())) {
                    int i = 0;
                    for (UpdRowDto dto : leaveAddList) {
                        if (dto.getRowCnt().equals(rowDto.getRowCnt())) {
                            realRowCnt = i;
                        }
                        i++;
                    }
                }
                if (CollectionUtils.isNotEmpty(leaveAddList)) {
                    for (SaveItemDto saveItemDto : leaveAddList.get(realRowCnt).getRow()) {
                        if ("leave_type_id".equals(saveItemDto.getItemCode())) {
                            leaveTypeId = ConvertHelper.intConvert(saveItemDto.getItemValue());
                        } else if ("empid".equals(saveItemDto.getItemCode())) {
                            empid = ConvertHelper.longConvert(saveItemDto.getItemValue());
                        } else if ("time_slot".equals(saveItemDto.getItemCode())) {
                            timeSlot = (String) saveItemDto.getItemValue();
                        } else if ("last_approval_time".equals(saveItemDto.getItemCode())) {
                            lastApprovalTime = (Long) saveItemDto.getItemValue();
                        } else if ("status".equals(saveItemDto.getItemCode())) {
                            leaveStatus = (Short) saveItemDto.getItemValue();
                        } else if ("leave_id".equals(saveItemDto.getItemCode()) && null == leaveId) {
                            leaveId = (Integer) saveItemDto.getItemValue();
                        } else if ("reason".equals(saveItemDto.getItemCode())) {
                            reason = (String) saveItemDto.getItemValue();
                        }
                    }
                }
                if (lastApprovalTime == null) {
                    lastApprovalTime = DateUtil.getOnlyDate();
                }
                Long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
                startDateMap.put(leaveId, startDate);
                Long endDate = DateUtil.getOnlyDate(new Date(endTime * 1000));
                endDateMap.put(leaveId, endDate);

                //查询员工排班
                int tmType = 1;
                SysEmpInfo empInfo = empInfoMapper.selectByPrimaryKey(empid);
                if (empInfo != null && empInfo.getTmType() != null) {
                    tmType = empInfo.getTmType();
                    if (tmType == 0) {
                        tmType = 1;
                    }
                }
                empInfoMap.put(empInfo.getEmpid(), empInfo);
                Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(belongId, empid, tmType, startDate - 86400, endDate, null, true);
                if (MapUtils.isNotEmpty(pbMap)) {
                    pbMap.forEach((date, worktimeDetail) -> {
                        WaShiftDef detailShift = shiftMap.get(worktimeDetail.getShiftDefId());
                        if (DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType())) {
                            worktimeDetail.setDateType(detailShift.getDateType());
                        }
                    });
                }

                // 计算休假时间类型
                WaLeaveType waLeaveType = corpAllLeaveTypeMap.get(leaveTypeId);
                waLeaveType.setIsRestDay(Optional.ofNullable(waLeaveType.getIsRestDay()).orElse(false));
                waLeaveType.setIsLegalHoliday(Optional.ofNullable(waLeaveType.getIsLegalHoliday()).orElse(false));

                Integer acctimeType = waLeaveType.getAcctTimeType();
                if (acctimeType == 1) {//天
                    if (StringUtils.isNotBlank(shalfDay) && StringUtils.isNotBlank(ehalfDay)) {
                        period = PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().shortValue();
                    } else {
                        period = PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().shortValue();
                    }
                } else if (acctimeType == 2) {//小时
                    period = PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().shortValue();
                    long startSec = startTime - startDate;
                    long endSec = endTime - endDate;
                    if (startSec <= 0 && endSec <= 0) {
                        period = PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().shortValue();
                    }
                }

                // 计算并组装休假时间参数信息
                Map<String, Object> leaveMap = getLeaveTimeStr(Integer.valueOf(period), startTime, endTime,
                        shalfDay, ehalfDay);

                timeSlot = getLeaveTimeSlot(leaveMap);

                // 休假主表信息
                WaEmpLeave empLeave = new WaEmpLeave();
                empLeave.setLeaveId(leaveId);
                empLeave.setApprovalNum((short) 1);
                empLeave.setLastApprovalTime(lastApprovalTime);
                empLeave.setTimeSlot(timeSlot);
                empLeave.setStartDate(DateUtil.convertDateTimeToStr(startTime, "yyyy-MM-dd", true));
                empLeave.setTotalTimeDuration(totalTimeDuration.floatValue());
                empLeave.setLeaveTypeId(waLeaveType.getLeaveTypeId());
                empLeave.setEmpid(empid);
                empLeave.setStatus(leaveStatus);
                empLeave.setLeaveStatus(LeaveCancelStatusEnum.LEAVE_CANCEL_PASSED.getIndex());
                empLeave.setDataSource("IMPORT");
                if (null != reason && "HIS_IMPORT".equals(reason.trim())) {
                    empLeave.setDataSource("HIS_IMPORT");
                    empLeave.setReason("");
                    hisImport = Boolean.TRUE;
                }
                if (ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(Integer.valueOf(leaveStatus))) {
                    empLeave.setLeaveStatus(LeaveCancelStatusEnum.NO_LEAVE_CANCEL.getIndex());
                }

                // 休假详情
                WaEmpLeaveTime leaveTime = doContractWaEmpLeaveTime(empLeave, leaveMap, pbMap, shiftMap);
                leaveTime.setLeaveTimeId(ConvertHelper.intConvert(leaveTimeId));

                // 休假每日明细
                long leaveDate = startDate;
                while (leaveDate <= endDate) {
                    WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
                    // 查询当天休假班次
                    List<Integer> shiftDefIdList = worktimeDetail.doGetShiftDefIdList();
                    // 计算每个休假班次的休假时长
                    for (Integer shiftDefId : shiftDefIdList) {
                        WaLeaveDaytime dayTime = doBuildLeaveDaytime(empLeave, leaveTime, waLeaveType, pbMap,
                                shiftMap, leaveMap, leaveDate, shiftDefId);

                        long reldate = lastApprovalTime;
                        if (reldate < leaveDate) {
                            reldate = leaveDate;
                        }
                        dayTime.setRealDate(DateUtil.getOnlyDate(new Date(reldate * 1000)));

                        if (dayTime.getTimeDuration() != null) {
                            totalTimeDuration = totalTimeDuration.add(BigDecimal.valueOf(dayTime.getTimeDuration()));
                        }
                        allLeaveDaytimeAddList.add(dayTime);
                    }
                    leaveDate = leaveDate + 86400;
                }

                // 更新休假主表
                empLeave.setTimeUnit(waLeaveType.getAcctTimeType());
                empLeave.setTotalTimeDuration(totalTimeDuration.floatValue());
                empLeave.setUpdtime(System.currentTimeMillis() / 1000);
                if (null != UserContext.getUserId()) {
                    empLeave.setUpduser(UserContext.getUserId());
                }
                allLeaveUpdList.add(empLeave);

                // 更新休假详情表
                leaveTime.setTimeUnit(waLeaveType.getAcctTimeType());
                leaveTime.setTimeDuration(totalTimeDuration.floatValue());
                leaveTime.setUpdtime(System.currentTimeMillis() / 1000);
                if (null != UserContext.getUserId()) {
                    leaveTime.setUpduser(UserContext.getUserId());
                }
                if (waLeaveType.getAcctTimeType() == 1
                        && PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {//工时折算
                    leaveTime.setPeriodType(PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().shortValue());
                }
                allLeaveTimeUpdList.add(leaveTime);
                cnt++;
            }

            // 批量保存
            if (!allLeaveUpdList.isEmpty()) {
                for (WaEmpLeave empLeave : allLeaveUpdList) {
                    if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(Integer.valueOf(empLeave.getStatus()))) {
                        continue;
                    }
                    String businessKey = String.valueOf(empLeave.getLeaveId());
                    SysEmpInfo empInfo = empInfoMap.get(empLeave.getEmpid());
                    long startDate = startDateMap.get(empLeave.getLeaveId());
                    long endDate = endDateMap.get(empLeave.getLeaveId());
                    WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
                    wfBeginWorkflowDto.setFuncCode(BusinessCodeEnum.LEAVE.getCode());
                    wfBeginWorkflowDto.setBusinessId(businessKey);
                    wfBeginWorkflowDto.setApplicantId(empLeave.getEmpid().toString());
                    wfBeginWorkflowDto.setApplicantName(empInfo.getEmpName());
                    wfBeginWorkflowDto.setEventTime(startDate * 1000);
                    wfBeginWorkflowDto.setEventEndTime(endDate * 1000);
                    wfBeginWorkflowDto.setTimeSlot(getTimeSlot(empLeave.getLeaveId(), allLeaveTimeUpdList));
                    log.info("import Leave begin wf businessKey={}", businessKey);

                    redisTemplate.opsForValue().set("import_leave_id_" + businessKey,
                            empLeave.getLeaveTypeId() + "," + empLeave.getTotalTimeDuration() + "," + empLeave.getTimeUnit(),
                            120, TimeUnit.SECONDS);

                    Result<?> result = wfRegisterFeign.begin(wfBeginWorkflowDto);
                    if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                        throw new CDException("工作流配置错误，请修改工作流配置");
                    }
                    empLeave.setLastApprovalTime(null);
                }
            }
            importService.fastInsertList(WaLeaveDaytime.class, "leaveDaytimeId", allLeaveDaytimeAddList);
            importService.fastUpdList(WaEmpLeaveTime.class, "leaveTimeId", allLeaveTimeUpdList);
            importService.fastUpdList(WaEmpLeave.class, "leaveId", allLeaveUpdList);

            // 额度扣减
            Long userId = SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId();
            if (!hisImport) {
                this.processQuotaSigle(allLeaveUpdList, corpAllLeaveTypeMap, userId);
            }
        }
    }

    private WaEmpLeaveTime doContractWaEmpLeaveTime(WaEmpLeave empLeave,
                                                    Map<String, Object> leaveMap,
                                                    Map<Long, WaWorktimeDetail> pbMap,
                                                    Map<Integer, WaShiftDef> shiftMap) {
        Integer period = (Integer) leaveMap.get("period");
        String startTimeStr = (String) leaveMap.get("starttime");
        String endTimeStr = (String) leaveMap.get("endtime");
        long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);

        // 查询排班
        WaWorktimeDetail sdetail = pbMap.get(startDate);
        WaWorktimeDetail edetail = pbMap.get(endDate);

        // 构建休假详情
        WaEmpLeaveTime leaveTime = new WaEmpLeaveTime();
        leaveTime.setPeriodType(period.shortValue());
        leaveTime.setLeaveId(empLeave.getLeaveId());
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)) {
            leaveTime.setStartTime(startDate);
            leaveTime.setEndTime(endDate);
        } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
            leaveTime.setStartTime(startDate);
            leaveTime.setEndTime(endDate);
        } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            leaveTime.setStartTime((Long) IocUtil.getDateValue(startTimeStr, "BIGINT"));
            leaveTime.setEndTime((Long) IocUtil.getDateValue(endTimeStr, "BIGINT"));
        }
        leaveTime.setShiftStartTime(leaveTime.getStartTime());
        leaveTime.setShiftEndTime(leaveTime.getEndTime());
        if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
            leaveTime.setStartTime(startDate);
            leaveTime.setEndTime(endDate);
            leaveTime.setShalfDay(leaveMap.get("shalfday").toString());
            leaveTime.setEhalfDay(leaveMap.get("ehalfday").toString());
            // 计算休假时间
            // 开始日期
            List<Integer> startShiftDefIdList = sdetail.doGetShiftDefIdList();
            Optional<WaEmpLeaveTime> startDateTime = startShiftDefIdList.stream().map(shiftDefId -> {
                WaShiftDef shiftDef = shiftMap.get(shiftDefId);
                return calLeaveShiftTimeForPeriodTypeNine(startDate, startDate,
                        shiftDef, shiftDef, leaveTime.getShalfDay(), leaveTime.getEhalfDay());
            }).min(Comparator.comparing(WaEmpLeaveTime::getShiftStartTime));
            startDateTime.ifPresent(waEmpLeaveTime -> leaveTime.setShiftStartTime(waEmpLeaveTime.getShiftStartTime()));
            // 结束日期
            List<Integer> endShiftDefIdList = edetail.doGetShiftDefIdList();
            Optional<WaEmpLeaveTime> endDateTime = endShiftDefIdList.stream().map(shiftDefId -> {
                WaShiftDef shiftDef = shiftMap.get(shiftDefId);
                return calLeaveShiftTimeForPeriodTypeNine(endDate, endDate,
                        shiftDef, shiftDef, leaveTime.getShalfDay(), leaveTime.getEhalfDay());
            }).max(Comparator.comparing(WaEmpLeaveTime::getShiftEndTime));
            endDateTime.ifPresent(waEmpLeaveTime -> leaveTime.setShiftEndTime(waEmpLeaveTime.getShiftEndTime()));
        }

        return leaveTime;
    }

    // TODO 同com.caidaocloud.attendance.service.application.service.impl.LeaveApplyService.getLeaveTimeSlot
    public String getLeaveTimeSlot(Map<String, Object> leaveMap) {
        Integer period = (Integer) leaveMap.get("period");
        String startTimeStr = (String) leaveMap.get("starttime");
        String endTimeStr = (String) leaveMap.get("endtime");
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)) {
            return startTimeStr + " -> " + endTimeStr;
        } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
            return startTimeStr + " -> " + endTimeStr;
        } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            return startTimeStr + " -> " + endTimeStr;
        } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
            String shalfday = leaveMap.get("shalfday").toString();
            String ehalfday = leaveMap.get("ehalfday").toString();
            return startTimeStr + BaseConst.LEAVE_HALF_MAPS.get(shalfday) + " -> "
                    + endTimeStr + BaseConst.LEAVE_HALF_MAPS.get(ehalfday);
        }
        return startTimeStr + " -> " + endTimeStr;
    }

    // TODO 同 com.caidaocloud.attendance.service.application.service.impl.LeaveApplyService.doBuildLeaveDaytime
    public WaLeaveDaytime doBuildLeaveDaytime(WaEmpLeave empLeave, WaEmpLeaveTime leaveTime, WaLeaveType waLeaveType,
                                              Map<Long, WaWorktimeDetail> pbMap,
                                              Map<Integer, WaShiftDef> shiftMap,
                                              Map<String, Object> leaveMap,
                                              long leaveDate, Integer useShiftDefId) throws Exception {
        Integer period = (Integer) leaveMap.get("period");
        String startTimeStr = (String) leaveMap.get("starttime");
        String endTimeStr = (String) leaveMap.get("endtime");

        // 查询排班
        WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
        WaShiftDef shiftDef = shiftMap.get(Optional.ofNullable(useShiftDefId).orElse(worktimeDetail.getShiftDefId()));

        // TODO 检查休假时间是否属于当天的班次
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);
        boolean belongToday = checkLeaveTimeIfBelongToday(pbMap, shiftMap, worktimeDetail, period, endDate);

        // 构造每日明细数据对象
        WaLeaveDaytime dayTime = new WaLeaveDaytime();
        dayTime.setUseShiftDefId(shiftDef.getShiftDefId());
        dayTime.setLeaveId(empLeave.getLeaveId());
        dayTime.setLeaveTimeId(leaveTime.getLeaveTimeId());
        dayTime.setLeaveDate(leaveDate);
        dayTime.setPeriodType(period.shortValue());
        dayTime.setCancelTimeDuration(0f);
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period) || PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
            dayTime.setTimeUnit((short) 1);
        }
        if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period) || PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            dayTime.setTimeUnit((short) 2);
        }
        //检查排班是否是系统默认班次，如果是则证明员工未排班, 请假使用系统默认班次时需记录下默认班次ID，其他场景则不记录
        WaShiftDef defaultShift = waCommonService.getDefaultShiftDef(shiftMap);
        if (defaultShift != null && defaultShift.getShiftDefId().equals(shiftDef.getShiftDefId())) {
            dayTime.setShiftDefId(shiftDef.getShiftDefId());
        }
        dayTime.setDateType(worktimeDetail.getDateType());
        // 计算休假时长
        if (belongToday && DateTypeEnum.DATE_TYP_2.getIndex().equals(worktimeDetail.getDateType()) && !waLeaveType.getIsRestDay()) {
            dayTime.setTimeDuration(0f);
        } else if (belongToday && DateTypeEnum.DATE_TYP_3.getIndex().equals(worktimeDetail.getDateType()) && !waLeaveType.getIsLegalHoliday()) {
            dayTime.setTimeDuration(0f);
        } else if (belongToday && DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType()) && !waLeaveType.getIsRestDay()) {
            dayTime.setTimeDuration(0f);
        } else if (belongToday && DateTypeEnum.DATE_TYP_5.getIndex().equals(worktimeDetail.getDateType()) && !waLeaveType.getIsLegalHoliday()) {
            dayTime.setTimeDuration(0f);
        } else {
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)) {
                dayTime.setTimeDuration(1f);
            } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
                mobileV16Service.calLeaveTimeByPeriod3(startTimeStr, endTimeStr, dayTime, waLeaveType, pbMap, shiftMap);
                if (BooleanUtils.isTrue(waLeaveType.getIsAdjustWorkHour())) {
                    Float timeDuration = mobileV16Service.getAdjustWorkTime(dayTime.getStartTime(), dayTime.getEndTime(), shiftDef);
                    if (timeDuration != null) {
                        dayTime.setBeforeAdjustTimeDuration(dayTime.getTimeDuration());
                        dayTime.setTimeDuration(timeDuration);
                    }
                }
            } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
                mobileV16Service.processLeaveByPeriod4(waLeaveType, worktimeDetail, shiftDef, dayTime);
            } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
                mobileV16Service.processLeaveByPeriod9(waLeaveType, worktimeDetail, dayTime,
                        leaveTime.getShalfDay(),
                        leaveTime.getEhalfDay(),
                        DateUtil.getTimesampByDateStr2(startTimeStr),
                        DateUtil.getTimesampByDateStr2(endTimeStr));
            }
            //请假时长折算逻辑
            mobileV16Service.convertLtTime(period, dayTime, waLeaveType, shiftDef);
        }
        return dayTime;
    }

    // TODO 同com.caidaocloud.attendance.service.application.service.impl.LeaveApplyService.checkLeaveTimeIfBelongToday
    public boolean checkLeaveTimeIfBelongToday(Map<Long, WaWorktimeDetail> pbMap,
                                               Map<Integer, WaShiftDef> shiftMap,
                                               WaWorktimeDetail worktimeDetail,
                                               Integer period,
                                               long endDate) {
        boolean belongToday = true;
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType())
                && PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            // 当天非工作日并且休的是小时假时，需要判断前一天是否为跨夜班，如果前一天是跨夜班，说明当天的休假时间有可能属于前一天班次
            Long preLeaveDate = DateUtil.addDate(endDate * 1000, -1);
            WaWorktimeDetail preLeaveDateWorkTime = pbMap.get(preLeaveDate);
            // TODO 多班次时查找跨夜的那个班次
            WaShiftDef preLeaveDateShift = null != preLeaveDateWorkTime
                    ? shiftMap.get(preLeaveDateWorkTime.getShiftDefId()) : null;
            if (preLeaveDateShift != null
                    && DateTypeEnum.DATE_TYP_1.getIndex().equals(preLeaveDateWorkTime.getDateType())
                    && CdWaShiftUtil.checkCrossNightV2(preLeaveDateShift, preLeaveDateWorkTime.getDateType())) {
                belongToday = false;
            }
        }
        return belongToday;
    }

    /**
     * 计算实际休假时间
     * TODO 同LeaveApplyService#calLeaveShiftTimeForPeriodTypeNine(long, long, com.caidao1.wa.mybatis.model.WaShiftDef,
     * TODO com.caidao1.wa.mybatis.model.WaShiftDef, java.lang.String, java.lang.String)
     *
     * @param startDate
     * @param endDate
     * @param startDateShift
     * @param endDateShift
     * @param shalfday
     * @param ehalfday
     * @return
     */
    public WaEmpLeaveTime calLeaveShiftTimeForPeriodTypeNine(long startDate,
                                                             long endDate,
                                                             WaShiftDef startDateShift,
                                                             WaShiftDef endDateShift,
                                                             String shalfday,
                                                             String ehalfday) {
        WaShiftDef startShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(startDateShift);
        WaShiftDef endShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(endDateShift);

        WaEmpLeaveTime leaveTime = new WaEmpLeaveTime();
        // 半天开始
        if ("P".equals(shalfday)) {
            if (startDateShift.getIsHalfdayTime() != null && startDateShift.getIsHalfdayTime()
                    && startDateShift.getHalfdayTime() != null && startDateShift.getHalfdayTime() > 0) {
                leaveTime.setShiftStartTime(startDate + startDateShift.doGetRealHalfdayTime() * 60);
            } else if (startDateShift.getIsNoonRest() != null && startDateShift.getIsNoonRest()) {
                leaveTime.setShiftStartTime(startDate + startDateShift.doGetRealNoonRestEnd() * 60);
            } else {
                leaveTime.setShiftStartTime(startDate + startShiftWorkTime.getStartTime() * 60);
            }
        } else {
            leaveTime.setShiftStartTime(startDate + startShiftWorkTime.getStartTime() * 60);
        }

        // 半天结束
        if ("A".equals(ehalfday)) {
            if (endDateShift.getIsHalfdayTime() != null && endDateShift.getIsHalfdayTime()
                    && endDateShift.getHalfdayTime() != null && endDateShift.getHalfdayTime() > 0) {
                leaveTime.setShiftEndTime(endDate + endDateShift.doGetRealHalfdayTime() * 60);
            } else if (endDateShift.getIsNoonRest() != null && endDateShift.getIsNoonRest()) {
                leaveTime.setShiftEndTime(endDate + endDateShift.doGetRealNoonRestStart() * 60);
            } else {
                leaveTime.setShiftEndTime(endDate + endShiftWorkTime.getEndTime() * 60);
            }
        } else {
            long endTime = endDate;
            if (CdWaShiftUtil.checkCrossNightV2(endDateShift, endDateShift.getDateType())) {
                endTime = DateUtil.addDate(endTime * 1000, 1);
            }
            leaveTime.setShiftEndTime(endTime + endShiftWorkTime.getEndTime() * 60);
        }

        return leaveTime;
    }

    public String getTimeSlot(Integer leaveId, List<WaEmpLeaveTime> leaveTimeList) {
        if (null == leaveTimeList || leaveTimeList.size() == 0) {
            return "";
        }
        WaEmpLeaveTime empLeaveTime = leaveTimeList.stream().filter(row -> row.getLeaveId().equals(leaveId)).findFirst().orElse(null);
        if (null == empLeaveTime) {
            return "";
        }
        Long shiftStartTime = empLeaveTime.getShiftStartTime();
        Long shiftEndTime = empLeaveTime.getShiftEndTime();
        Integer periodType = empLeaveTime.getPeriodType().intValue();
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
            return String.format("%s~%s", DateUtil.getDateStrByTimesamp(shiftStartTime), DateUtil.getDateStrByTimesamp(shiftEndTime));
        } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
            Long start = empLeaveTime.getStartTime();
            Long end = empLeaveTime.getEndTime();
            return String.format("%s %s~%s %s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeaveTime.getShalfDay()), DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeaveTime.getEhalfDay()));
        } else {
            return String.format("%s~%s", DateUtil.getTimeStrByTimesamp(shiftStartTime), DateUtil.getTimeStrByTimesamp(shiftEndTime));
        }
    }

    public Integer getTimeMin(String timeStr) {
        String shm = timeStr.substring(10).trim();//如2015-09-16 09:15  会变成09:15
        String[] shmArr = shm.split(":");
        return Integer.parseInt(shmArr[0]) * 60 + Integer.parseInt(shmArr[1]);
    }

    public Map getRealLeaveTime(Long startDate, Long endDate, String shalfday, String ehalfday, WaShiftDef waShiftDef,
                                WaShiftDef waShiftDef2) {
        Map map = new HashMap();

        Long shiftStartTime = null;
        Long shiftEndTime = null;

        if (shalfday != null && shalfday.equals("P")) {
            if (waShiftDef.getIsHalfdayTime() != null && waShiftDef.getIsHalfdayTime() && waShiftDef.getHalfdayTime() != null && waShiftDef.getHalfdayTime().intValue() > 0) {
                shiftStartTime = startDate + waShiftDef.getHalfdayTime() * 60;
            } else if (waShiftDef.getIsNoonRest() != null && waShiftDef.getIsNoonRest()) {
                shiftStartTime = startDate + waShiftDef.getNoonRestEnd() * 60;
            } else {
                shiftStartTime = startDate + waShiftDef.getStartTime() * 60;
            }
        } else {
            shiftStartTime = startDate + waShiftDef.getStartTime() * 60;
        }
        if (ehalfday != null && ehalfday.equals("A")) {
            if (waShiftDef2.getIsHalfdayTime() != null && waShiftDef2.getIsHalfdayTime() && waShiftDef2.getHalfdayTime() != null && waShiftDef2.getHalfdayTime().intValue() > 0) {
                shiftEndTime = endDate + waShiftDef2.getHalfdayTime() * 60;
            } else if (waShiftDef2.getIsNoonRest() != null && waShiftDef2.getIsNoonRest()) {
                shiftEndTime = endDate + waShiftDef2.getNoonRestStart() * 60;
            } else {
                shiftEndTime = endDate + waShiftDef2.getEndTime() * 60;
            }
        } else {
            shiftEndTime = endDate + waShiftDef2.getEndTime() * 60;
        }

        map.put("shiftStartTime", shiftStartTime);
        map.put("shiftEndTime", shiftEndTime);
        return map;
    }

    /**
     * 获取请假时间字符串
     *
     * @param period
     * @param startTime
     * @param endTime
     * @param shalfDay
     * @param ehalfDay
     * @return
     */
    public Map<String, Object> getLeaveTimeStr(Integer period, Long startTime, Long endTime, String shalfDay, String ehalfDay) {
        Map<String, Object> map = new HashMap();
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)) {
            map.put("period", PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex());
            map.put("starttime", DateUtil.getDateStrByTimesamp(startTime));
            map.put("endtime", DateUtil.getDateStrByTimesamp(endTime));
        } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
            map.put("period", PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex());
            map.put("starttime", DateUtil.getDateStrByTimesamp(startTime));
            map.put("endtime", DateUtil.getDateStrByTimesamp(endTime));
            map.put("shalfday", shalfDay);
            map.put("ehalfday", ehalfDay);
        } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            map.put("period", PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex());
            map.put("starttime", DateUtil.getTimeStrByTimesamp4(startTime));
            map.put("endtime", DateUtil.getTimeStrByTimesamp4(endTime));
        } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
            map.put("period", PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex());
            map.put("starttime", DateUtil.getTimeStrByTimesamp4(startTime));
            map.put("endtime", DateUtil.getTimeStrByTimesamp4(endTime));
        }
        return map;
    }

    private String getColName(String colName) {
        if (colName.contains(".")) {
            String[] ss = colName.split("\\.");
            return ss[0] + " ->> '" + ss[1] + "'";
        }
        return colName;
    }

    /**
     * 员工替换班次后置处理
     *
     * @param corpId
     * @param belongId
     * @param addList
     * @param updList
     */
    @Transactional
    public void changeEmpShiftAfterTrigger(Long corpId, String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        List<UpdRowDto> valueAddList = addList.get("wa_emp_shift_change");
        List<UpdRowDto> valueUpdList = updList.get("wa_emp_shift_change");
        List<UpdRowDto> allList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            allList.addAll(valueAddList);
        }
        if (CollectionUtils.isNotEmpty(valueUpdList)) {
            allList.addAll(valueUpdList);
        }
        if (CollectionUtils.isEmpty(allList)) {
            log.info("changeEmpShiftAfterTrigger import data is empty");
            return;
        }
        // 本次导入数据的最小日期
        Long minDate = 0L;
        // 本次导入数据的最大日期
        Long maxDate = 0L;
        // 本次导入数据的员工ID集合
        List<Long> empidList = new ArrayList<>();
        for (UpdRowDto updRowDto : allList) {
            Long empid = null;
            Long workDate = null;
            for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                if ("empid".equals(saveItemDto.getItemCode())) {
                    empid = ConvertHelper.longConvert(saveItemDto.getItemValue());
                } else if ("work_date".equals(saveItemDto.getItemCode())) {
                    workDate = (Long) saveItemDto.getItemValue();
                }
            }
            if (empid != null && workDate != null) {
                if (empidList.indexOf(empid) < 0) {
                    empidList.add(empid);
                }
                if (minDate == 0 || minDate > workDate) {
                    minDate = workDate;
                }
                if (maxDate == 0 || maxDate < workDate) {
                    maxDate = workDate;
                }
            }
        }

        //本次导入的员工班次数据（草稿状态的数据）
        WaEmpShiftChangeExample example = new WaEmpShiftChangeExample();
        example.createCriteria().andBelongOrgIdEqualTo(belongId).andEmpidIn(empidList).andStatusEqualTo(0);
        List<WaEmpShiftChange> shiftChangeList = waEmpShiftChangeMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(shiftChangeList)) {
            log.info("changeEmpShiftAfterTrigger get shiftChangeList is empty");
            return;
        }
        // 查询员工班次替换历史记录
        Map<String, Integer> oldShiftChangeRecIdMap = new HashMap<>();
        WaEmpShiftChangeExample empShiftChangeExample = new WaEmpShiftChangeExample();
        empShiftChangeExample.createCriteria().andBelongOrgIdEqualTo(belongId).andEmpidIn(empidList).
                andStatusEqualTo(2).andWorkDateBetween(minDate, maxDate);
        List<WaEmpShiftChange> oldEmpShiftChangeList = waEmpShiftChangeMapper.selectByExample(empShiftChangeExample);
        if (CollectionUtils.isNotEmpty(oldEmpShiftChangeList)) {
            oldShiftChangeRecIdMap = oldEmpShiftChangeList.stream().collect(Collectors.toMap(o -> String.format("%s_%s", o.getEmpid(), o.getWorkDate()), WaEmpShiftChange::getRecId, (v1, v2) -> v1));
        }

        //查询员工当前排班
        Map<String, EmpShiftInfo> empShiftInfo = waCommonService.getEmpShiftInfoListMaps(belongId, empidList, minDate, maxDate);

        // 本次导入需要更新的数据集合
        List<WaEmpShiftChange> changeUpdList = new ArrayList<>();

        // 失效的替换班次记录
        List<Integer> invalidIdList = new ArrayList<>();

        Map<String, Integer> finalOldShiftChangeRecIdMap = oldShiftChangeRecIdMap;
        shiftChangeList.forEach(waEmpShiftChange -> {
            Long empid = waEmpShiftChange.getEmpid();
            Long workDate = waEmpShiftChange.getWorkDate();

            WaEmpShiftChange changeUpd = new WaEmpShiftChange();
            changeUpd.setRecId(waEmpShiftChange.getRecId());
            changeUpd.setStatus(2);
            changeUpd.setUpdtime(DateUtil.getCurrentTime(true));
            changeUpd.setUpduser(SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId());
            String key = String.format("%s_%s", empid, workDate);
            if (empShiftInfo.containsKey(key) && empShiftInfo.get(key) != null) {
                changeUpd.setOldShiftDefId(empShiftInfo.get(key).getShiftDefId());
            }
            if (finalOldShiftChangeRecIdMap.containsKey(key)) {
                invalidIdList.add(finalOldShiftChangeRecIdMap.get(key));
            }
            changeUpdList.add(changeUpd);
        });

        if (CollectionUtils.isNotEmpty(invalidIdList)) {
            WaEmpShiftChange updShift = new WaEmpShiftChange();
            updShift.setStatus(4);
            updShift.setUpdtime(DateUtil.getCurrentTime(true));
            updShift.setUpduser(SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId());
            WaEmpShiftChangeExample updExample = new WaEmpShiftChangeExample();
            updExample.createCriteria().andRecIdIn(invalidIdList);
            waEmpShiftChangeMapper.updateByExampleSelective(updShift, updExample);
        }
        if (CollectionUtils.isNotEmpty(changeUpdList)) {
            importService.fastUpdList(WaEmpShiftChange.class, "recId", changeUpdList);
        }
    }

    /**
     * 更新员工排班结束时间
     *
     * @param addList
     * @param updList
     */
    @Transactional
    public void updateWaEmpShiftEndTime(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        List<UpdRowDto> valueAddList = addList.get("wa_emp_shift");
        List<UpdRowDto> valueUpdList = updList.get("wa_emp_shift");
        List<UpdRowDto> allList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            allList.addAll(valueAddList);
        }
        if (CollectionUtils.isNotEmpty(valueUpdList)) {
            allList.addAll(valueUpdList);
        }
        if (CollectionUtils.isNotEmpty(allList)) {
            for (UpdRowDto updRowDto : allList) {
                Long startTime = null;
                Long endTime = null;
                Long empid = null;
                for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                    if (saveItemDto.getItemCode().equals("empid")) {
                        empid = ConvertHelper.longConvert(saveItemDto.getItemValue());
                    } else if (saveItemDto.getItemCode().equals("start_time")) {
                        startTime = (Long) saveItemDto.getItemValue();
                    } else if (saveItemDto.getItemCode().equals("end_time")) {
                        endTime = (Long) saveItemDto.getItemValue();
                    }
                }
                if (startTime != null && endTime != null) {
                    //1、查找比此次开始日期大的排班分配数据，如果有，则取最近一条的开始日期减一天作为本次的结束日期
                    WaEmpShiftExample empShiftExample = new WaEmpShiftExample();
                    empShiftExample.createCriteria().andBelongOrgidEqualTo(belongId)
                            .andEmpidEqualTo(empid).andStartTimeGreaterThan(startTime);
                    empShiftExample.setOrderByClause("start_time");
                    List<WaEmpShift> empShiftList = waEmpShiftMapper.selectByExample(empShiftExample);
                    if (CollectionUtils.isNotEmpty(empShiftList)) {
                        WaEmpShift empShift = empShiftList.get(0);
                        if (null != empShift.getStartTime()) {
                            Calendar cal = Calendar.getInstance();
                            cal.setTimeInMillis(empShift.getStartTime() * 1000);
                            cal.add(Calendar.DATE, -1);

                            WaEmpShift record = new WaEmpShift();
                            record.setEndTime(cal.getTimeInMillis() / 1000);
                            waEmpShiftMapper.updateByExampleSelective(record, empShiftExample);
                        }
                    }
                    //2、查找比此次开始日期小的排班分配数据，如果有，则把最近一条的结束日期改为本次开始日期减一天
                    WaEmpShiftExample empShiftExample1 = new WaEmpShiftExample();
                    empShiftExample1.createCriteria().andBelongOrgidEqualTo(belongId)
                            .andEmpidEqualTo(empid).andStartTimeLessThan(startTime);
                    empShiftExample1.setOrderByClause("start_time desc");
                    List<WaEmpShift> empShiftList1 = waEmpShiftMapper.selectByExample(empShiftExample1);
                    if (CollectionUtils.isNotEmpty(empShiftList1)) {
                        WaEmpShift empShift = empShiftList1.get(0);
                        if (null != empShift.getStartTime()) {
                            Calendar cal = Calendar.getInstance();
                            cal.setTimeInMillis(startTime * 1000);
                            cal.add(Calendar.DATE, -1);

                            empShift.setEndTime(cal.getTimeInMillis() / 1000);
                            waEmpShiftMapper.updateByPrimaryKeySelective(empShift);
                        }
                    }
                }
            }
        }
    }

    /**
     * 员工排班（工作日历）导入
     *
     * @param addList
     * @param updList
     * @throws Exception
     */
    @Transactional
    public void updateEmpShift(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) throws Exception {
        log.info("开始执行员工日历导入后置方法，参数[belongId:{},addList:{},updList:{}]", belongId, JSON.toJSONString(addList), JSON.toJSONString(updList));
        Long userId = SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId();
        List<UpdRowDto> allEmpShiftList = this.getAllEmpShiftList(addList, updList);
        if (CollectionUtils.isEmpty(allEmpShiftList)) {
            log.info("*************接入的员工排班（工作日历）数据为空*************");
            return;
        }
        //员工id
        List<Long> empIds = new ArrayList<Long>();
        //获取导入的员工排班（工作日历）数据
        List<WaEmpShift> importEmpShifts = this.getImportEmpShiftList(allEmpShiftList, empIds);
        if (CollectionUtils.isEmpty(importEmpShifts) || CollectionUtils.isEmpty(empIds)) {
            log.info("*************接入的员工排班（工作日历）数据为空*************");
            return;
        }
        //查询导入员工的涉及到的所有排班
        WaEmpShiftExample example = new WaEmpShiftExample();
        example.createCriteria().andBelongOrgidEqualTo(belongId).andEmpidIn(empIds);
        List<WaEmpShift> empShiftList = waEmpShiftMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(empShiftList)) {
            Map<Long, List<WaEmpShift>> empShiftListMap = empShiftList.stream().collect(Collectors.groupingBy(WaEmpShift::getEmpid));
            List<WaEmpShift> updEmpShiftList = new ArrayList<>();
            Long currentTime = DateUtil.getCurrentTime(true);
            for (WaEmpShift importShift : importEmpShifts) {
                Optional<WaEmpShift> empShiftOpt = empShiftListMap.get(importShift.getEmpid()).stream().filter(s -> !(
                        s.getEmpid().equals(importShift.getEmpid())
                                && s.getWorkCalendarId().equals(importShift.getWorkCalendarId())
                                && s.getStartTime().equals(importShift.getStartTime())
                                && importShift.getEndTime().equals(DateUtil.getOnlyDate(new Date(s.getEndTime() * 1000)))))
                        .max(Comparator.comparing(WaEmpShift::getStartTime));
                if (empShiftOpt.isPresent()) {
                    WaEmpShift empShift = empShiftOpt.get();
                    Long endDate = DateUtil.getOnlyDate(new Date(empShift.getEndTime() * 1000));
                    if (importShift.getStartTime() > empShift.getStartTime() && importShift.getStartTime() <= endDate && importShift.getEndTime() >= endDate) {
                        empShift.setEndTime(DateUtil.addDate(importShift.getStartTime() * 1000, -1) + 86399);
                        empShift.setUpduser(userId);
                        empShift.setUpdtime(currentTime);
                        updEmpShiftList.add(empShift);
                    }
                }
                importShift.setEndTime(DateUtil.getOnlyDate(new Date(importShift.getEndTime() * 1000)) + 86399);
                importShift.setUpduser(userId);
                importShift.setUpdtime(currentTime);
                updEmpShiftList.add(importShift);
            }
            log.info("员工日历导入数据，参数[belongId:{},updEmpShiftList:{}]", belongId, JSON.toJSONString(updEmpShiftList));
            if (CollectionUtils.isNotEmpty(updEmpShiftList)) {
                importService.fastUpdList(WaEmpShift.class, "empShiftId", updEmpShiftList);
            }
        }
        log.info("结束执行员工日历导入后置方法");
    }

    /**
     * 获取导入的员工排班（工作日历）
     *
     * @param allEmpShiftList
     */
    private List<WaEmpShift> getImportEmpShiftList(List<UpdRowDto> allEmpShiftList, List<Long> empIds) {
        List<WaEmpShift> importEmpShiftList = new ArrayList<>();
        if (CollectionUtils.isEmpty(allEmpShiftList)) {
            return importEmpShiftList;
        }
        for (UpdRowDto updRowDto : allEmpShiftList) {
            Long empId = null;
            Long startTime = null;
            Long endTime = null;
            Integer workCalendarId = null;
            for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                //员工工号
                if ("empid".equals(saveItemDto.getItemCode())) {
                    empId = ConvertHelper.longConvert(saveItemDto.getItemValue());
                }
                //有效期开始时间
                if ("start_time".equals(saveItemDto.getItemCode())) {
                    startTime = this.getLongValue(saveItemDto.getItemValue());
                }
                //有效期结束时间
                if ("end_time".equals(saveItemDto.getItemCode())) {
                    endTime = this.getLongValue(saveItemDto.getItemValue());
                }
                //员工日历
                if ("work_calendar_id".equals(saveItemDto.getItemCode())) {
                    workCalendarId = ConvertHelper.intConvert(saveItemDto.getItemValue());
                }
            }
            if (null == empId || null == workCalendarId || null == startTime || null == endTime) {
                continue;
            }
            if (!empIds.contains(empId)) {
                empIds.add(empId);
            }
            WaEmpShift importObj = new WaEmpShift();
            importObj.setStartTime(startTime);
            importObj.setEndTime(endTime);
            importObj.setEmpid(empId);
            importObj.setWorkCalendarId(workCalendarId);
            importEmpShiftList.add(importObj);
        }
        return importEmpShiftList;
    }

    private Long getLongValue(Object dataTime) {
        Long longValue = null;
        if (dataTime instanceof BigDecimal) {
            longValue = ((BigDecimal) dataTime).longValue();
        } else if (dataTime instanceof Integer) {
            longValue = ((Integer) dataTime).longValue();
        } else {
            longValue = (Long) dataTime;
        }
        return longValue;
    }

    private List<UpdRowDto> getAllEmpShiftList(Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        List<UpdRowDto> allEmpShiftList = new ArrayList<>();
        List<UpdRowDto> valueAddList = addList.get("wa_emp_shift");
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            allEmpShiftList.addAll(valueAddList);
        }
//        List<UpdRowDto> valueUpdList = updList.get("wa_emp_shift");
//        if (CollectionUtils.isNotEmpty(valueUpdList)) {
//            allEmpShiftList.addAll(valueUpdList);
//        }
        return allEmpShiftList;
    }

    /**
     * 员工考勤方案定界处理
     *
     * @param belongId
     * @param addList
     * @param updList
     */
    @Transactional
    public void updateWaEmpGroup(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        log.info("开始执行员工考勤方案后置处理,参数[belongId:{},addList:{},updList:{}]", belongId, FastjsonUtil.toJson(addList), FastjsonUtil.toJson(updList));
        Long userId = SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId();
        List<UpdRowDto> valueAddList = addList.get("wa_emp_group");
        if (CollectionUtils.isEmpty(valueAddList)) {
            log.info("*************接入的员工考勤方案数据为空*************");
            return;
        }
        //员工id
        List<Long> empIds = new ArrayList<Long>();
        List<WaEmpGroup> importWaEmpGroups = this.getImportWaEmpGroup(valueAddList, empIds);
        if (CollectionUtils.isEmpty(importWaEmpGroups) || CollectionUtils.isEmpty(empIds)) {
            log.info("*************接入的员工考勤方案数据为空*************");
            return;
        }
        //过滤不是当前租户的数据
        SysEmpInfoExample sysEmpInfo = new SysEmpInfoExample();
        sysEmpInfo.createCriteria().andEmpidIn(empIds).andBelongOrgIdEqualTo(belongId);
        List<SysEmpInfo> sysEmpInfoList = sysEmpInfoMapper.selectByExample(sysEmpInfo);
        List<Long> empIdList = sysEmpInfoList.stream().map(SysEmpInfo::getEmpid).collect(Collectors.toList());
        WaEmpGroupExample example = new WaEmpGroupExample();
        example.createCriteria().andEmpidIn(empIdList);
        List<WaEmpGroup> empGroupList = waEmpGroupMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(empGroupList)) {
            Map<Long, List<WaEmpGroup>> empGroupMap = empGroupList.stream().collect(Collectors.groupingBy(WaEmpGroup::getEmpid));
            List<WaEmpGroup> updEmpGroupList = new ArrayList<>();
            Long currentTime = DateUtil.getCurrentTime(true);
            for (WaEmpGroup importEmpGroup : importWaEmpGroups) {
                Optional<WaEmpGroup> optional = empGroupMap.get(importEmpGroup.getEmpid()).stream().filter(e -> !(
                        e.getEmpid().equals(importEmpGroup.getEmpid())
                                && e.getWaGroupId().equals(importEmpGroup.getWaGroupId())
                                && e.getStartTime().equals(importEmpGroup.getStartTime())
                                && importEmpGroup.getEndTime().equals(DateUtil.getOnlyDate(new Date(e.getEndTime() * 1000)))))
                        .max(Comparator.comparing(WaEmpGroup::getStartTime));
                if (optional.isPresent()) {
                    WaEmpGroup empGroup = optional.get();
                    Long endDate = DateUtil.getOnlyDate(new Date(empGroup.getEndTime() * 1000));
                    if (importEmpGroup.getStartTime() > empGroup.getStartTime() && importEmpGroup.getStartTime() <= endDate
                            && importEmpGroup.getEndTime() >= endDate) {
                        empGroup.setEndTime(DateUtil.addDate(importEmpGroup.getStartTime() * 1000, -1) + 86399);
                        empGroup.setUpduser(userId);
                        empGroup.setUpdtime(currentTime);
                        updEmpGroupList.add(empGroup);
                    }
                }
                importEmpGroup.setEndTime(DateUtil.getOnlyDate(new Date(importEmpGroup.getEndTime() * 1000)) + 86399);
                importEmpGroup.setUpduser(userId);
                importEmpGroup.setUpdtime(currentTime);
                updEmpGroupList.add(importEmpGroup);
            }
            if (CollectionUtils.isNotEmpty(updEmpGroupList)) {
                importService.fastUpdList(WaEmpGroup.class, "empGroupId", updEmpGroupList);
            }
        }
        log.info("执行员工考勤方案后置方法结束");
    }

    /**
     * 获取员工考勤方案导入数据
     *
     * @param allWaEmpGroup
     * @param empIds
     * @return
     */
    private List<WaEmpGroup> getImportWaEmpGroup(List<UpdRowDto> allWaEmpGroup, List<Long> empIds) {
        List<WaEmpGroup> importWaEmpGroup = new ArrayList<>();
        if (CollectionUtils.isEmpty(allWaEmpGroup)) {
            return importWaEmpGroup;
        }
        for (UpdRowDto updRowDto : allWaEmpGroup) {
            Long empId = null;
            Long startTime = null;
            Long endTime = null;
            Integer waGroupId = null;
            for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                //员工工号
                if ("empid".equals(saveItemDto.getItemCode())) {
                    empId = ConvertHelper.longConvert(saveItemDto.getItemValue());
                }
                //有效期开始时间
                if ("start_time".equals(saveItemDto.getItemCode())) {
                    startTime = (Long) saveItemDto.getItemValue();
                }
                //有效期结束时间
                if ("end_time".equals(saveItemDto.getItemCode())) {
                    endTime = (Long) saveItemDto.getItemValue();
                }
                //员工考勤方案
                if ("wa_group_id".equals(saveItemDto.getItemCode())) {
                    waGroupId = ConvertHelper.intConvert(saveItemDto.getItemValue());
                }
            }
            if (null == empId || null == waGroupId || null == startTime || null == endTime) {
                continue;
            }
            if (!empIds.contains(empId)) {
                empIds.add(empId);
            }
            WaEmpGroup waEmpGroup = new WaEmpGroup();
            waEmpGroup.setStartTime(startTime);
            waEmpGroup.setEndTime(endTime);
            waEmpGroup.setEmpid(empId);
            waEmpGroup.setWaGroupId(waGroupId);
            importWaEmpGroup.add(waEmpGroup);
        }
        return importWaEmpGroup;
    }

    /**
     * 员工打卡方案导入
     *
     * @param addList
     * @param updList
     * @throws Exception
     */
    @Transactional
    public void updateEmpClockPlan(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) throws Exception {
        log.info("开始执行员工打卡方案导入后置方法，参数[belongId:{},addList:{},updList:{}]", belongId, JSON.toJSONString(addList), JSON.toJSONString(updList));
        Long userId = SessionHolder.getUserId() == null ? 0 : SessionHolder.getUserId();
        List<UpdRowDto> allImportList = addList.get("wa_plan_emp_rel");
        if (CollectionUtils.isEmpty(allImportList)) {
            log.info("*************接入的员工打卡方案数据为空*************");
            return;
        }
        //员工id
        List<Long> empIds = new ArrayList<>();
        //获取导入的员工打卡方案数据
        List<PlanEmpRel> importList = this.getImportEmpClockPlanList(belongId, allImportList, empIds);
        if (CollectionUtils.isEmpty(importList) || CollectionUtils.isEmpty(empIds)) {
            log.info("*************接入的员工打卡方案数据为空*************");
            return;
        }
        List<PlanEmpRel> updateList = new ArrayList<>();
        List<PlanEmpRel> insertList = new ArrayList<>();
        //查询导入员工的涉及到的所有打卡方案
        List<PlanEmpRel> list = waPlanEmpRelMapper.queryByEmpIds(empIds, belongId);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<Long, List<PlanEmpRel>> listMap = list.stream().collect(Collectors.groupingBy(PlanEmpRel::getEmpId));
            Long currentTime = DateUtil.getCurrentTime(true);
            for (PlanEmpRel importObj : importList) {
                List<PlanEmpRel> relList = listMap.get(importObj.getEmpId());
                Optional<PlanEmpRel> opt = relList.stream().max(Comparator.comparing(PlanEmpRel::getStartTime));
                if (opt.isPresent()) {
                    PlanEmpRel rel = opt.get();
                    Long endDate = DateUtil.getOnlyDate(new Date(rel.getEndTime() * 1000));
                    if (importObj.getStartTime().equals(rel.getStartTime())) {
                        rel.setEndTime(DateUtil.getOnlyDate(new Date(importObj.getEndTime() * 1000)) + 86399);
                        rel.setUpdater(userId);
                        rel.setUpdateTime(currentTime);
                        updateList.add(rel);
                    } else if (importObj.getStartTime() > rel.getStartTime() && importObj.getStartTime() <= endDate && importObj.getEndTime() >= endDate) {
                        rel.setEndTime(DateUtil.addDate(importObj.getStartTime() * 1000, -1) + 86399);
                        rel.setUpdater(userId);
                        rel.setUpdateTime(currentTime);
                        updateList.add(rel);
                        insertList.add(importObj);
                    } else if (importObj.getStartTime() > endDate && importObj.getEndTime() > endDate) {
                        importObj.setEndTime(DateUtil.getOnlyDate(new Date(importObj.getEndTime() * 1000)) + 86399);
                        insertList.add(importObj);
                    }
                } else {
                    importObj.setEndTime(DateUtil.getOnlyDate(new Date(importObj.getEndTime() * 1000)) + 86399);
                    insertList.add(importObj);
                }
            }
        } else {
            for (PlanEmpRel planEmpRel : importList) {
                planEmpRel.setEndTime(DateUtil.getOnlyDate(new Date(planEmpRel.getEndTime() * 1000)) + 86399);
            }
            insertList.addAll(importList);
        }
        log.info("员工打卡方案导入数据，参数[belongId:{}, insertList:{}]", belongId, JSON.toJSONString(insertList));
        if (CollectionUtils.isNotEmpty(insertList)) {
            this.insertList(insertList);
        }
        log.info("员工打卡方案导入数据，参数[belongId:{}, updateList:{}]", belongId, JSON.toJSONString(updateList));
        if (CollectionUtils.isNotEmpty(updateList)) {
            for (PlanEmpRel rel : updateList) {
                waPlanEmpRelMapper.updateByPrimaryKey(rel);
            }
        }
        log.info("结束执行员工打卡方案导入后置方法");
    }

    /**
     * 获取导入的员工打卡方案
     *
     * @param belongId
     * @param allList
     * @param empIds
     * @return
     */
    private List<PlanEmpRel> getImportEmpClockPlanList(String belongId, List<UpdRowDto> allList, List<Long> empIds) {
        List<PlanEmpRel> importList = new ArrayList<>();
        if (CollectionUtils.isEmpty(allList)) {
            return importList;
        }
        for (UpdRowDto updRowDto : allList) {
            Long empId = null;
            Long startTime = null;
            Long endTime = null;
            Long planId = null;
            for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                //员工工号
                if ("emp_id".equals(saveItemDto.getItemCode())) {
                    empId = (Long) saveItemDto.getItemValue();
                }
                //打卡方案
                if ("plan_id".equals(saveItemDto.getItemCode())) {
                    planId = (Long) saveItemDto.getItemValue();
                }
                //有效期结束时间
                if ("end_time".equals(saveItemDto.getItemCode())) {
                    endTime = this.getLongValue(saveItemDto.getItemValue());
                }
                //有效期开始时间
                if ("start_time".equals(saveItemDto.getItemCode())) {
                    startTime = this.getLongValue(saveItemDto.getItemValue());
                }
            }
            if (null == empId || null == planId || null == startTime || null == endTime) {
                continue;
            }
            if (!empIds.contains(empId)) {
                empIds.add(empId);
            }
            PlanEmpRel importObj = new PlanEmpRel();
            importObj.setId(snowflakeUtil.createId());
            importObj.setBelongOrgId(belongId);
            importObj.setCorpId(SessionHolder.getCorpId());
            importObj.setPlanId(planId);
            importObj.setStartTime(startTime);
            importObj.setEndTime(endTime);
            importObj.setEmpId(empId);
            importObj.setCreator(SessionHolder.getUserId());
            importObj.setUpdater(SessionHolder.getUserId());
            long currentTime = System.currentTimeMillis() / 1000;
            importObj.setCreateTime(currentTime);
            importObj.setUpdateTime(currentTime);
            importList.add(importObj);
        }
        return importList;
    }

    private void insertList(List<PlanEmpRel> models) {
        int size = models.size();
        if (size > 0) {
            int page = 500;
            if (size > page) {
                int length = (int) Math.ceil(size * 1.0 / page);
                for (int i = 0; i < length; i++) {
                    int end = (i + 1) * page < size ? (i + 1) * page : size;
                    final List<PlanEmpRel> subRows = models.subList(i * page, end);
                    if (CollectionUtils.isNotEmpty(subRows)) {
                        waPlanEmpRelMapper.saveBatch(subRows);
                    }
                }
            } else {
                waPlanEmpRelMapper.saveBatch(models);
            }
        }
    }

    /**
     * 企业微信接入打卡记录
     * 0考勤 1企业微信 2飞书 3钉钉
     */
    public List<Map> getWeChartRegList(List<Map> sourceResult, Integer sourceFromType) {
        log.info("getWeChartRegList start..");
        if (CollectionUtils.isEmpty(sourceResult)) {
            return new ArrayList<>();
        }
        List list = new ArrayList();
        sourceFromType = sourceFromType == null ? 0 : sourceFromType;
        for (Map map : sourceResult) {
            String exceptionType = (String) map.get("exception_type");
            String regType = (String) map.get("checkin_type");
            if (StringUtils.isNotBlank(regType)) {
                if (regType.indexOf("上班") > -1) {
                    map.put("register_type", 1);
                    map.put("type", 1);
                } else if (regType.indexOf("下班") > -1) {
                    map.put("register_type", 2);
                    map.put("type", 1);

                } else if (regType.indexOf("外出") > -1) {
                    map.put("register_type", 1);
                    map.put("type", 3);
                }
            }
            if (StringUtils.isNotBlank(exceptionType)) {
                if (exceptionType.indexOf("未打卡") > -1) {
                    list.add(map);
                }
                exceptionType = exceptionType.replaceAll("时间异常", "TIME_ERR").replaceAll("地点异常", "LOCAL_ERR").replaceAll("非常用设备", "DEVICE_ERR");
                map.put("result_desc", exceptionType);
                map.put("result_type", 2);

            } else {
                map.put("result_tyoe", 1);
            }
            if (map.containsKey("checkin_time")) {
                Long checkinTime = Long.valueOf(map.get("checkin_time").toString());
                if (checkinTime != null) {
                    Long oneDayTimestamps = Long.valueOf(60 * 60 * 24);
                    Long belongDate = checkinTime - (checkinTime + 60 * 60 * 8 * 1000) % oneDayTimestamps;
                    map.put("belong_date", belongDate);
                }
                map.put("empid", map.get("userid"));
                map.put("reg_addr", map.get("location_detail"));
                map.put("reason", map.get("notes"));
                map.put("reg_date_time", map.get("checkin_time"));
                map.put("mob_device_num", map.get("deviceid"));
                map.put("source_from_type", sourceFromType);
            }

        }
        sourceResult.removeAll(list);
        return sourceResult;
    }

    @Transactional
    public void saveRegisterRecordEmpMapToCache(Integer belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) {
        log.info("saveRegisterRecordEmpMapToCache start..");
        List<UpdRowDto> allList = importClockTriggerService.getImportDataListForReg(addList, updList);
        Map<String, List<Integer>> belongDateAndEmpList = new HashedMap();
        for (UpdRowDto updRowDto : allList) {
            Integer empid = null;
            Long reg_date_time = null;
            for (SaveItemDto saveItemDto : updRowDto.getRow()) {
                if (saveItemDto.getItemCode().equals("empid")) {
                    empid = ConvertHelper.intConvert(saveItemDto.getItemValue());
                }
                if (saveItemDto.getItemCode().equals("reg_date_time")) {
                    reg_date_time = ConvertHelper.longConvert(saveItemDto.getItemValue());
                }
            }
            if (empid == null || reg_date_time == null) {
                continue;
            }
            Long date = DateUtil.getDateLong(reg_date_time * 1000, "yyyy-MM-dd", true);
            if (belongDateAndEmpList.get(date.toString()) == null) {
                belongDateAndEmpList.put(date.toString(), new ArrayList<>());
            }
            belongDateAndEmpList.get(date.toString()).add(empid);
        }
        final String key = "syncDataInputRegisterRecordEmpMap_" + belongId;
        redisTemplate.delete(key);
        redisTemplate.opsForHash().putAll(key, belongDateAndEmpList);
    }

    /**
     * 钉钉考勤接入打卡记录
     *
     * @param sourceResult
     * @return
     */
    public List<Map> getDingTalkRegList(List<Map> sourceResult, String appKey, String belongId) {
        Map empMap = this.getDingDingUserEmpMap(belongId, appKey);

        if (CollectionUtils.isEmpty(sourceResult) || empMap == null) {
            log.info("-------------钉钉接入数据源或员工数据为空---------------");
            return new ArrayList<>();
        }

        List list = new ArrayList();
        for (Map map : sourceResult) {
            String userId = map.get("userId").toString();
            if (empMap.get(userId) == null) {
                log.info("userId:{} noMatchEmpInfo:{}", userId, map);
                continue;
            }
            String exceptionType = (String) map.get("timeResult");
            if (StringUtils.isNotBlank(exceptionType)) {
                if (exceptionType.indexOf("NotSigned") > -1) {
                    list.add(map);
                }
            }
            if (map.containsKey("userCheckTime")) {
                Long checkinTime = Long.valueOf(map.get("userCheckTime").toString()) / 1000L;
                if (checkinTime != null) {
                    Long oneDayTimestamps = 86400L;
                    Long belongDate = checkinTime - (checkinTime + 60 * 60 * 8 * 1000) % oneDayTimestamps;
                    map.put("belong_date", belongDate);
                }
                map.put("empid", empMap.get(userId));
                map.put("reg_addr", map.get("userAddress"));
                map.put("reason", map.get("outsideRemark"));
                map.put("reg_date_time", checkinTime);
                map.put("source_from_type", 3);
            }
            map.put("type", 1);
        }
        log.info("DingDingRemoveAttendAnceRecord:{}", list);
        sourceResult.removeAll(list);
        return sourceResult;
    }

    public Map<String, String> getDingDingUserEmpMap(String belongId, String appKey) {
        Map params = new HashMap<>();
        params.put("tenantId", belongId);
        params.put("userIdKey", appKey + "_DingUserId");
        Map<String, String> empMap = new HashMap<>();
        List<DingTalkUserIdDto> empDingTalkInfoList = importOtherMapper.getEmpDingTalkInfoList(params);
        if (CollectionUtils.isEmpty(empDingTalkInfoList)) {
            log.info("tenantId={},appKey={} DingUserIdMap is null", belongId, appKey);
            return empMap;
        }

        for (DingTalkUserIdDto dingTalkUserIdDto : empDingTalkInfoList) {
            String dingTalkUserId = dingTalkUserIdDto.getDingTalkUserId();
            if (StringUtils.isBlank(dingTalkUserId)) {
                continue;
            }
            empMap.put(dingTalkUserId, dingTalkUserIdDto.getWorkNo());
        }
        log.info("tenantId:{} dingTalkUserId:{}", belongId, empMap);
        return empMap;
    }
}