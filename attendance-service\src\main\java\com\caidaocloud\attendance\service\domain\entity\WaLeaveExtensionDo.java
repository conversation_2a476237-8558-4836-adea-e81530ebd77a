package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.service.domain.repository.IWaLeaveExtensionRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveExtension;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
@Slf4j
@Service
public class WaLeaveExtensionDo {
    private Long id;
    private String tenantId;
    private Long quotaId;
    private Long empId;
    private Integer leaveTypeId;
    private Long configId;
    private Float timeDuration;
    private Integer timeUnit;
    private Long startDate;
    private Long endDate;
    private Long originalEndDate;
    private Integer status;
    private Long lastApprovalTime;
    private String reason;
    private String revokeReason;
    private String fileName;
    private String fileId;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;

    private String orgName;
    private String quotaName;
    private String empName;
    private String workNo;
    private String workCity;
    private Long hireDate;
    private String employType;
    private String processCode;
    private String i18nRuleName;

    @Resource
    private IWaLeaveExtensionRepository waLeaveExtensionRepository;

    public WaLeaveExtensionDo selectByPrimaryKey(Long id) {
        Optional<WaLeaveExtension> opt = Optional.ofNullable(waLeaveExtensionRepository.selectByPrimaryKey(id));
        return opt.map(waLeaveExtension -> ObjectConverter.convert(waLeaveExtension, WaLeaveExtensionDo.class)).orElse(null);
    }

    public int save(WaLeaveExtensionDo model) {
        if (null == model) {
            return 0;
        }
        return waLeaveExtensionRepository.save(ObjectConverter.convert(model, WaLeaveExtension.class));
    }

    public int update(WaLeaveExtensionDo model) {
        if (null == model) {
            return 0;
        }
        return waLeaveExtensionRepository.update(ObjectConverter.convert(model, WaLeaveExtension.class));
    }

    public int delete(WaLeaveExtensionDo model) {
        if (null == model) {
            return 0;
        }
        return waLeaveExtensionRepository.delete(ObjectConverter.convert(model, WaLeaveExtension.class));
    }

    public PageList<WaLeaveExtensionDo> getEmpLeaveExtensionList(MyPageBounds myPageBounds, Map params) {
        return waLeaveExtensionRepository.getEmpLeaveExtensionList(myPageBounds, params);
    }

    @CDText(exp = {"employType" + TextAspect.DICT_E, "workCity" + TextAspect.PLACE}, classType = WaLeaveExtensionDo.class)
    public WaLeaveExtensionDo getById(String tenantId, Long id) {
        return waLeaveExtensionRepository.getById(tenantId, id);
    }

    public List<WaLeaveExtensionDo> getLeaveExtensionList(String tenantId, List<Long> configIds, List<Long> quotaIds, List<Integer> status) {
        List<WaLeaveExtension> models = waLeaveExtensionRepository.getLeaveExtensionList(tenantId, configIds, quotaIds, status);
        if (CollectionUtils.isEmpty(models)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(models, WaLeaveExtensionDo.class);
    }
}
