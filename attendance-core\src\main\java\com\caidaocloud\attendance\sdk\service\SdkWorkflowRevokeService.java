package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.sdk.dto.SdkWorkflowRevokeDTO;
import com.caidaocloud.attendance.sdk.feign.IWorkflowRevokeFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 撤销申请走工作流的撤销
 *
 * <AUTHOR>
 * @Date 2023/6/19
 */
@Slf4j
@Service
public class SdkWorkflowRevokeService {
    @Autowired
    private IWorkflowRevokeFeignClient workflowRevokeFeignClient;

    /**
     * 撤销工作流
     *
     * @param dto
     * @return
     */
    public Result<?> revoke(SdkWorkflowRevokeDTO dto) {
        return workflowRevokeFeignClient.revoke(dto);
    }
}
