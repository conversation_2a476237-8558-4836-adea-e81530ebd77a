package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTypeListReqDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/8
 */
public interface IWaTravelTypeRepository {
    int selectCountByTravelName(String tenantId, String travelTypeName, Long travelTypeId);

    int save(WaTravelTypeDo waTravelTypeDo);

    int update(WaTravelTypeDo waTravelTypeDo);

    int delete(Long travelTypeId, UserInfo userInfo);

    WaTravelTypeDo selectOneById(String tenantId, Long travelTypeId);

    PageResult<WaTravelTypeDo> getTravelTypePageResult(TravelTypeListReqDto dto);

    List<WaTravelTypeDo> getTravelTypeList(String tenantId);
}
