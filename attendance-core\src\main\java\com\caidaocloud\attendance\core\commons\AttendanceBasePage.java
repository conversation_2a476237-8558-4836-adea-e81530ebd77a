package com.caidaocloud.attendance.core.commons;

import com.caidao1.report.common.OpEnum;
import com.caidao1.report.dto.FilterBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceBasePage {
    private final String DOUBLE_QUOTATION_MARKS = "\"",
            SINGLE_QUOTATION_MARK = "'";
    private static Pattern SQL_PATTERN = Pattern.compile("^(.+)\\sAND\\s(.+)|(.+)\\sOR(.+)\\s|(.+)\\sUNION\\s(.+)|(.+)\\sSELECT\\s(.+)|(.+)\\sUPDATE\\s(.+)|(.+)\\sDELETE\\s(.+)|(.+)\\sINSERT\\s(.+)|(.+)\\s--\\s(.+)$");
    /**
     * 页码
     */
    private int pageNo = 1;
    /**
     * 每页显示条数
     */
    private int pageSize = 10;
    /**
     * 总条数
     */
    private int total;
    private boolean all = false;
    private List<FilterBean> filterList;
    private String filter;

    private String keywords;

    public String getFilter() {
        return getFilterV1();
    }

    /**
     * @return
     * <AUTHOR>
     * @date 2020-09-21
     */
    public String getFilterV1() {
        // 升级版修复 SQL 注入漏洞
        if (CollectionUtils.isEmpty(filterList)) {
            return "";
        }

        String filter = "";
        for (FilterBean filterBean : filterList) {
            String field = filterBean.getField();
            if (null == field) {
                continue;
            }

            if (field.contains(".")) {
                field = field.replace(".", ".\"") + DOUBLE_QUOTATION_MARKS;
            } else {
                field = DOUBLE_QUOTATION_MARKS + field + DOUBLE_QUOTATION_MARKS;
            }

            if (StringUtils.hasText(filterBean.getMin())) {
                if (null == filterBean.getOp()) {
                    continue;
                }

                filter = getOpMinFilter(filterBean, field, filter);

                continue;
            }

            OpEnum op = filterBean.getOp();
            if (null != op) {
                filter = getOpFilter(op, field, filter);
            }
        }

        Matcher matcher = SQL_PATTERN.matcher(filter);
        if (!matcher.find()) {
            // 未有注入漏洞的 SQL 过滤条件放行
            return filter;
        }

        return "";
    }

    private String getOpMinFilter(FilterBean filterBean, String field, String filter) {
        switch (filterBean.getOp()) {
            case lk:
                filter += " and upper(" + field + "::VARCHAR) like '%" + filterBean.getMin().toUpperCase()
                        .replace("'", "''").replace("\\", "\\\\") + "%'";
                break;
            case nl:
                filter += " and upper(" + field + "::VARCHAR) not like '%" + filterBean.getMin().toUpperCase()
                        .replace("'", "''").replace("\\", "\\\\") + "%'";
                break;
            case he:
                filter += " and upper(" + field + "::VARCHAR) like '" + filterBean.getMin().toUpperCase()
                        .replace("'", "''").replace("\\", "\\\\") + "%'";
                break;
            case nh:
                filter += " and upper(" + field + "::VARCHAR) not like '" + filterBean.getMin().toUpperCase()
                        .replace("'", "''").replace("\\", "\\\\") + "%'";
                break;
            case fe:
                filter += " and upper(" + field + "::VARCHAR) like '%" + filterBean.getMin().toUpperCase()
                        .replace("'", "''").replace("\\", "\\\\") + "'";
                break;
            case nf:
                filter += " and upper(" + field + "::VARCHAR) not like '%" + filterBean.getMin().toUpperCase()
                        .replace("'", "''").replace("\\", "\\\\") + "'";
                break;
            case eq:
                if (filterBean.getMin().contains(",")) {
                    filter += generateInCondtion(filterBean, field);
                } else if (" ".equals(filterBean.getMin())) {
                    filter += " and (" + field + "::VARCHAR = '' or " + field + " IS NULL)";
                } else {
                    filter += " and " + field + " = '" + filterBean.getMin().replace(SINGLE_QUOTATION_MARK, "''") + SINGLE_QUOTATION_MARK;
                }
                break;
            case ne:
                if (" ".equals(filterBean.getMin())) {
                    filter += " and (" + field + "::VARCHAR <> '' or " + field + " IS NOT NULL)";
                } else {
                    filter += " and " + field + " <> '" + filterBean.getMin().replace(SINGLE_QUOTATION_MARK, "''") + SINGLE_QUOTATION_MARK;
                }
                break;
            case gt:
                filter += " and " + field + " > '" + filterBean.getMin() + SINGLE_QUOTATION_MARK;
                break;
            case ge:
                filter += " and " + field + " >= '" + filterBean.getMin() + SINGLE_QUOTATION_MARK;
                break;
            case lt:
                filter += " and " + field + " < '" + filterBean.getMin() + SINGLE_QUOTATION_MARK;
                break;
            case le:
                filter += " and " + field + " <= '" + filterBean.getMin() + SINGLE_QUOTATION_MARK;
                break;
            case bt:
                filter += " and " + field + " >= '" + filterBean.getMin() + SINGLE_QUOTATION_MARK;
                if (StringUtils.hasText(filterBean.getMax())) {
                    filter += " and " + field + " <= '" + filterBean.getMax() + SINGLE_QUOTATION_MARK;
                }
                break;
            case nb:
                filter += " and (" + field + " <= " + filterBean.getMin();
                if (StringUtils.hasText(filterBean.getMax())) {
                    filter += " or " + field + " >= " + filterBean.getMax();
                }
                filter += ")";
                break;
            case in:
                filter += generateInCondtion(filterBean, field);
                break;
            case ni:
                filter += generateNotInCondtion(filterBean, field);
                break;
            case ce:
                filter += " and " + field + " >= '" + filterBean.getMin() + SINGLE_QUOTATION_MARK;
                if (StringUtils.hasText(filterBean.getMax())) {
                    filter += " and " + field + " <= '" + filterBean.getMax() + SINGLE_QUOTATION_MARK;
                }
                break;
            case nc:
                filter += " and " + field + " < '" + filterBean.getMin() + SINGLE_QUOTATION_MARK;
                if (StringUtils.hasText(filterBean.getMax())) {
                    filter += " or " + field + " > '" + filterBean.getMax() + SINGLE_QUOTATION_MARK;
                }
                break;
            case em:
                filter += " and (" + field + "::VARCHAR = '' or " + field + " IS NULL)";
                break;
            case nm:
                filter += " and (" + field + "::VARCHAR <> '' or " + field + " IS NOT NULL)";
                break;
            default:
        }

        return filter;
    }

    private String getOpFilter(OpEnum op, String field, String filter) {
        switch (op) {
            case lk:
                if (field.length() > 1) {
                    filter += " and (" + field + " like '% %')";
                }
                break;
            case nl:
                if (field.length() > 1) {
                    filter += " and (" + field + " not like '% %')";
                }
                break;
            case he:
                if (field.length() > 1) {
                    filter += " and (" + field + " like ' %')";
                }
                break;
            case nh:
                if (field.length() > 1) {
                    filter += " and (" + field + " not like ' %')";
                }
                break;
            case fe:
                if (field.length() > 1) {
                    filter += " and (" + field + " like '% ')";
                }
                break;
            case nf:
                if (field.length() > 1) {
                    filter += " and (" + field + " not like '% ')";
                }
                break;
            case eq:
                filter += " and (" + field + "::VARCHAR = '' or " + field + " IS NULL)";
                break;
            case ne:
                filter += " and (" + field + "::VARCHAR <> '' and " + field + " IS NOT NULL)";
                break;
            case em:
                filter += " and (" + field + "::VARCHAR = '' or " + field + " IS NULL)";
                break;
            case nm:
                filter += " and (" + field + "::VARCHAR <> '' or " + field + " IS NOT NULL)";
                break;
            default:
        }

        return filter;
    }

    private String generateNotInCondtion(FilterBean filterBean, String field) {
        String list = filterBean.getMin();
        list = list.replace(" ", ",").replace(SINGLE_QUOTATION_MARK, "''").replace(",", "','");
        return " and " + field + " not in ('" + list + "')";
    }

    private String generateInCondtion(FilterBean filterBean, String field) {
        String list = filterBean.getMin();
        list = list.replace(" ", ",").replace(SINGLE_QUOTATION_MARK, "''").replaceAll(",", "','");
        return " and " + field + " in ('" + list + "')";
    }
}
