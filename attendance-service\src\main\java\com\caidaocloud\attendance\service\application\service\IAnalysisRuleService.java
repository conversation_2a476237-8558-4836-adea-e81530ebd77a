package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.dto.AnalysisDetailDto;

/**
 * <AUTHOR>
 */
public interface IAnalysisRuleService {

    AttendancePageResult getAnalysisList(String belongId, AttendanceBasePage basePage);

    AnalysisDetailDto getParseGroupById(Integer id) throws Exception;

    void deleteRuleById(Integer id);

    AnalysisDetailDto getGroupInfo(Integer id);
}
