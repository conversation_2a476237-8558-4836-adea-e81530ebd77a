package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveSettingDo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/14
 */
public interface ILeaveSettingRepository {
    AttendancePageResult<WaLeaveSettingDo> getLeaveSettingPageList(AttendanceBasePage basePage, String belongOrgId, Integer leaveTypeId);

    List<Map> getLeaveQuotaGroupRuleList(String belongOrgId, Integer quotaSettingId);

    WaLeaveSettingDo getLeaveSettingById(Integer id);

    List<WaLeaveSettingDo> getLeaveSettingList(String belongOrgId,List<Integer> leaveTypeIds);
}
