package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.repository.ILeaveSettingRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/14
 */
@Slf4j
@Service
public class WaLeaveSettingDo {
    private Integer quotaSettingId;
    private Short leaveType;
    private Integer quotaPeriodType;
    private Long startDate;
    private Long endDate;
    private Boolean isCountInProbation;
    private Integer carryOverType;
    private Integer carryOverTimeNum;
    private Integer carryOverTimeUnit;
    private Integer maxCarryOverDay;
    private Integer noCarryOverHandle;
    private String belongOrgid;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private String rmk;
    private Integer leaveTypeId;
    private String quotaSettingName;
    private Integer quotaSortNo;
    private Boolean isEmpShow;
    private Boolean isTrialFreeze;
    private Boolean isTrialConvert;
    private Short freezingRules;
    private Object quotaSettingNameLang;
    private Boolean ifConvert;

    public WaLeaveSettingDo() {
    }

    public Integer getQuotaSettingId() {
        return this.quotaSettingId;
    }

    public void setQuotaSettingId(Integer quotaSettingId) {
        this.quotaSettingId = quotaSettingId;
    }

    public Short getLeaveType() {
        return this.leaveType;
    }

    public void setLeaveType(Short leaveType) {
        this.leaveType = leaveType;
    }

    public Integer getQuotaPeriodType() {
        return this.quotaPeriodType;
    }

    public void setQuotaPeriodType(Integer quotaPeriodType) {
        this.quotaPeriodType = quotaPeriodType;
    }

    public Long getStartDate() {
        return this.startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return this.endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public Boolean getIsCountInProbation() {
        return this.isCountInProbation;
    }

    public void setIsCountInProbation(Boolean isCountInProbation) {
        this.isCountInProbation = isCountInProbation;
    }

    public Integer getCarryOverType() {
        return this.carryOverType;
    }

    public void setCarryOverType(Integer carryOverType) {
        this.carryOverType = carryOverType;
    }

    public Integer getCarryOverTimeNum() {
        return this.carryOverTimeNum;
    }

    public void setCarryOverTimeNum(Integer carryOverTimeNum) {
        this.carryOverTimeNum = carryOverTimeNum;
    }

    public Integer getCarryOverTimeUnit() {
        return this.carryOverTimeUnit;
    }

    public void setCarryOverTimeUnit(Integer carryOverTimeUnit) {
        this.carryOverTimeUnit = carryOverTimeUnit;
    }

    public Integer getMaxCarryOverDay() {
        return this.maxCarryOverDay;
    }

    public void setMaxCarryOverDay(Integer maxCarryOverDay) {
        this.maxCarryOverDay = maxCarryOverDay;
    }

    public Integer getNoCarryOverHandle() {
        return this.noCarryOverHandle;
    }

    public void setNoCarryOverHandle(Integer noCarryOverHandle) {
        this.noCarryOverHandle = noCarryOverHandle;
    }

    public String getBelongOrgid() {
        return this.belongOrgid;
    }

    public void setBelongOrgid(String belongOrgid) {
        this.belongOrgid = belongOrgid;
    }

    public Long getCrtuser() {
        return this.crtuser;
    }

    public void setCrtuser(Long crtuser) {
        this.crtuser = crtuser;
    }

    public Long getCrttime() {
        return this.crttime;
    }

    public void setCrttime(Long crttime) {
        this.crttime = crttime;
    }

    public Long getUpduser() {
        return this.upduser;
    }

    public void setUpduser(Long upduser) {
        this.upduser = upduser;
    }

    public Long getUpdtime() {
        return this.updtime;
    }

    public void setUpdtime(Long updtime) {
        this.updtime = updtime;
    }

    public String getRmk() {
        return this.rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public Integer getLeaveTypeId() {
        return this.leaveTypeId;
    }

    public void setLeaveTypeId(Integer leaveTypeId) {
        this.leaveTypeId = leaveTypeId;
    }

    public String getQuotaSettingName() {
        return this.quotaSettingName;
    }

    public void setQuotaSettingName(String quotaSettingName) {
        this.quotaSettingName = quotaSettingName;
    }

    public Integer getQuotaSortNo() {
        return this.quotaSortNo;
    }

    public void setQuotaSortNo(Integer quotaSortNo) {
        this.quotaSortNo = quotaSortNo;
    }

    public Boolean getIsEmpShow() {
        return this.isEmpShow;
    }

    public void setIsEmpShow(Boolean isEmpShow) {
        this.isEmpShow = isEmpShow;
    }

    public Boolean getIsTrialFreeze() {
        return this.isTrialFreeze;
    }

    public void setIsTrialFreeze(Boolean isTrialFreeze) {
        this.isTrialFreeze = isTrialFreeze;
    }

    public Boolean getIsTrialConvert() {
        return this.isTrialConvert;
    }

    public void setIsTrialConvert(Boolean isTrialConvert) {
        this.isTrialConvert = isTrialConvert;
    }

    public Short getFreezingRules() {
        return this.freezingRules;
    }

    public void setFreezingRules(Short freezingRules) {
        this.freezingRules = freezingRules;
    }

    public Object getQuotaSettingNameLang() {
        return this.quotaSettingNameLang;
    }

    public void setQuotaSettingNameLang(Object quotaSettingNameLang) {
        this.quotaSettingNameLang = quotaSettingNameLang;
    }

    public Boolean getIfConvert() {
        return this.ifConvert;
    }

    public void setIfConvert(Boolean ifConvert) {
        this.ifConvert = ifConvert;
    }


    //业务方法&字段
    private String carryOverTypeName;
    private String quotaPeriodTypeName;

    public String getCarryOverTypeName() {
        return carryOverTypeName;
    }

    public void setCarryOverTypeName(String carryOverTypeName) {
        this.carryOverTypeName = carryOverTypeName;
    }

    public String getQuotaPeriodTypeName() {
        return quotaPeriodTypeName;
    }

    public void setQuotaPeriodTypeName(String quotaPeriodTypeName) {
        this.quotaPeriodTypeName = quotaPeriodTypeName;
    }

    @Autowired
    private ILeaveSettingRepository leaveSettingRepository;

    public AttendancePageResult<WaLeaveSettingDo> getLeaveSettingPageList(AttendanceBasePage basePage, String belongOrgId, Integer leaveTypeId) {
        return leaveSettingRepository.getLeaveSettingPageList(basePage, belongOrgId, leaveTypeId);
    }

    public List getLeaveQuotaGroupRuleList(String belongOrgId, Integer quotaSettingId) {
        return leaveSettingRepository.getLeaveQuotaGroupRuleList(belongOrgId, quotaSettingId);
    }

    public WaLeaveSettingDo getLeaveSettingById(Integer id){
        return leaveSettingRepository.getLeaveSettingById(id);
    }

    public List<WaLeaveSettingDo> getLeaveSettingList(String belongOrgId,List<Integer> leaveTypeIds){
        return leaveSettingRepository.getLeaveSettingList(belongOrgId,leaveTypeIds);
    }

}
