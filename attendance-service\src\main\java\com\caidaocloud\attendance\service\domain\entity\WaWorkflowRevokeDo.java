package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.domain.repository.IWorkflowRevokeRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaWorkflowRevoke;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
@Slf4j
@Service
public class WaWorkflowRevokeDo {
    private Long id;
    private String tenantId;
    private Long entityId;
    private String moduleName;
    private Integer status;
    private String reason;
    private Long lastApprovalTime;
    private String revokeReason;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;

    @Resource
    private IWorkflowRevokeRepository workflowRevokeRepository;

    public WaWorkflowRevokeDo selectByPrimaryKey(Long id) {
        Optional<WaWorkflowRevoke> opt = Optional.ofNullable(workflowRevokeRepository.selectByPrimaryKey(id));
        return opt.map(waWorkflowRevoke -> ObjectConverter.convert(waWorkflowRevoke, WaWorkflowRevokeDo.class)).orElse(null);
    }

    public int save(WaWorkflowRevokeDo workflowRevoke) {
        if (null == workflowRevoke) {
            return 0;
        }
        return workflowRevokeRepository.save(ObjectConverter.convert(workflowRevoke, WaWorkflowRevoke.class));
    }

    public int update(WaWorkflowRevokeDo workflowRevoke) {
        if (null == workflowRevoke) {
            return 0;
        }
        return workflowRevokeRepository.update(ObjectConverter.convert(workflowRevoke, WaWorkflowRevoke.class));
    }

    public PageList<Map> getOvertimeWorkflowRevokeList(MyPageBounds myPageBounds, Map param) {
        return workflowRevokeRepository.getOvertimeWorkflowRevokeList(myPageBounds, param);
    }

    public PageList<Map> getTravelWorkflowRevokeList(MyPageBounds myPageBounds, Map param) {
        return workflowRevokeRepository.getTravelWorkflowRevokeList(myPageBounds, param);
    }

    public List<WaWorkflowRevokeDo> getWorkflowRevokeList(String tenantId, Long entityId) {
        List<WaWorkflowRevoke> models = workflowRevokeRepository.getWorkflowRevokeList(tenantId, entityId);
        if (null == models || models.size() == 0) {
            return Lists.newArrayList();
        }
        return ObjectConverter.convertList(models, WaWorkflowRevokeDo.class);
    }

    public List<WaWorkflowRevokeDo> getWorkflowRevokeList(String tenantId, Long entityId, List<Integer> status, List<String> moduleNames) {
        return ObjectConverter.convertList(workflowRevokeRepository.getWorkflowRevokeList(tenantId, entityId, status, moduleNames), WaWorkflowRevokeDo.class);
    }

    public int delete(WaWorkflowRevokeDo workflowRevoke) {
        if (null == workflowRevoke) {
            return 0;
        }
        return workflowRevokeRepository.delete(ObjectConverter.convert(workflowRevoke, WaWorkflowRevoke.class));
    }
}
