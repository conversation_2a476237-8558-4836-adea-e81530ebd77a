package com.caidaocloud.attendance.service.application.service;

public interface ICommonService {
    void handleLeaveWorkflowEvent(Integer id, Long eventTime);

    void handleOvertimeWorkflowEvent(Integer id, Long eventTime);

    void handleBdkWorkflowEvent(Long id, Long eventTime);

    void handleTravelWorkflowEvent(Long id, Long eventTime);

    void handleShiftWorkflowEvent(Long id, Long eventTime);

    void handleLeaveCancelWorkflowEvent(Long id, Long eventTime);

    void handleCompensatoryWorkflowEvent(Long id, Long eventTime);

    void handleOvertimeRevokeWorkflowEvent(Long id, Long eventTime);

    void handleOvertimeAbolishWorkflowEvent(Long id, Long eventTime);

    void handleTravelRevokeWorkflowEvent(Long id, Long eventTime);

    void handleTravelAbolishWorkflowEvent(Long id, Long eventTime);

    void handleLeaveExtensionWorkflowEvent(Long id, Long eventTime);
}
