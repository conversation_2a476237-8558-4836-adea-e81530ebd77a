package com.caidaocloud.attendance.core.wa.dto.shift;

import com.caidao1.commons.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class EmpCalendarShiftForMonthDto {
    @ApiModelProperty("日期类型")
    private Integer dateType;
    @ApiModelProperty("日期时间戳")
    private Long workDateTimestamp;
    @ApiModelProperty("日期（格式yyyyMMdd）")
    private String workDate;
    @ApiModelProperty("班次日期类型名称")
    private String shiftDefName;
    @ApiModelProperty("班次ID")
    private Integer shiftDefId;
    @ApiModelProperty("班次编码")
    private String shiftDefCode;
    @ApiModelProperty("班次名称")
    private String shiftName;
    @ApiModelProperty("班次信息集合（一天多班次时使用）")
    private List<EmpCalendarShiftForMonthDto> shiftList;

    public static Map<String, Long> calCalendarTimePeriodByMonth(Integer searchMonth) throws ParseException {
        Date firstDay = new SimpleDateFormat("yyyyMMdd").parse(searchMonth + "01");
        int weekDay = DateUtil.getDayOfWeek(firstDay.getTime(), null) - 1;
        if (0 == weekDay) {
            weekDay = 7;
        }
        weekDay -= 1;
        int monthDays = DateUtil.getMonthActualMax(firstDay.getTime());
        int lastDays = 42 - monthDays - weekDay;
        Long firstTime = firstDay.getTime() / 1000 - 3600 * 24 * weekDay;
        Date lastDay = DateUtil.getLastDayOfMonth(firstDay);
        Long lastTime = lastDay.getTime() / 1000 + 3600 * 24 * lastDays;

        Map<String, Long> resultMap = new HashMap<>();
        resultMap.put("start", firstTime);
        resultMap.put("end", lastTime);
        return resultMap;
    }
}
