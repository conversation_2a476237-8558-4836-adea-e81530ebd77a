package com.caidaocloud.attendance.core.wa.dto;

import com.caidao1.ioc.util.IocUtil;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.enums.WaLeavePeriodTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 申请休假时校验时间重叠时构造查询校验参数DTO
 */
@Data
@Accessors(chain = true)
public class BuildTimeOverlapCheckParamDto {
    private Long empid;
    private Integer period;
    private long startDate;
    private long endDate;
    private String startTimeStr;
    private String endTimeStr;
    private String shalfday;
    private String ehalfday;
    private WaShiftDef startDateShift;
    private WaShiftDef endDateShift;
    private String leaveTypeDefCode;
    private Long realStartTime;
    private Long realEndTime;

    /**
     * 计算休假时间（用于时间重叠校验）
     *
     * @return
     */
    @Deprecated
    public void calRealTime() {
        if (WaLeavePeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(this.period)
                || WaLeavePeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(this.period)) {
            this.setRealStartTime(this.startDate);
            this.setRealEndTime(this.endDate + 86399);
        } else if (WaLeavePeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(this.period)) {
            Long start = (Long) IocUtil.getDateValue(this.startTimeStr, "BIGINT");
            Long end = (Long) IocUtil.getDateValue(this.endTimeStr, "BIGINT");
            this.setRealStartTime(start);
            this.setRealEndTime(end);
        } else if (WaLeavePeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(this.period)) {
            calRealTimeForPeriodTypeNine();
        }
    }

    public void calRealTimeForPeriodTypeNine() {
        // 半天开始
        WaShiftDef startDateShift = this.startDateShift;
        WaShiftDef startShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(startDateShift);
        if ("P".equals(this.shalfday)) {
            if (startDateShift.getIsHalfdayTime() != null && startDateShift.getIsHalfdayTime()
                    && startDateShift.doGetRealHalfdayTime() > 0) {
                this.setRealStartTime(this.startDate + startDateShift.doGetRealHalfdayTime() * 60);
            } else if (startDateShift.getIsNoonRest() != null && startDateShift.getIsNoonRest()) {
                this.setRealStartTime(this.startDate + startDateShift.doGetRealNoonRestEnd() * 60);
            } else {
                this.setRealStartTime(this.startDate + startShiftWorkTime.doGetRealStartTime() * 60);
            }
        } else {
            this.setRealStartTime(this.startDate + startShiftWorkTime.doGetRealStartTime() * 60);
        }
        // 半天结束
        WaShiftDef endDateShift = this.endDateShift;
        WaShiftDef endShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(endDateShift);
        if ("A".equals(this.ehalfday)) {
            if (endDateShift.getIsHalfdayTime() != null && endDateShift.getIsHalfdayTime()
                    && endDateShift.doGetRealHalfdayTime() > 0) {
                this.setRealEndTime(this.endDate + endDateShift.doGetRealHalfdayTime() * 60);
            } else if (endDateShift.getIsNoonRest() != null && endDateShift.getIsNoonRest()) {
                this.setRealEndTime(this.endDate + endDateShift.doGetRealNoonRestStart() * 60);
            } else {
                this.setRealEndTime(this.endDate + endShiftWorkTime.doGetRealEndTime() * 60);
            }
        } else {
            if (CdWaShiftUtil.checkCrossNightV2(endDateShift, endDateShift.getDateType())) {
                this.setRealEndTime((this.endDate + 86400) + endShiftWorkTime.getEndTime() * 60);
            } else {
                this.setRealEndTime(this.endDate + endShiftWorkTime.getEndTime() * 60);
            }
        }
    }
}

