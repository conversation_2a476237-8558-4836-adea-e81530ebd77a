package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.repository.IEmpQuotaRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpQuotaDetail;
import com.caidaocloud.attendance.service.interfaces.dto.FixQuotaDto;
import com.caidaocloud.attendance.service.interfaces.dto.FixQuotaSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.QuotaDto;
import com.caidaocloud.dto.UserInfo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/4/19
 */
@Slf4j
@Data
@Service
public class WaEmpQuotaDo {
    private Integer empQuotaId;
    private Integer quotaSettingId;
    private Short periodYear;
    private Long startDate;
    private Long lastDate;
    private Long empid;
    private Float remainDay;
    private Float deductionDay;
    private Float quotaDay;
    private Float usedDay;
    private Long remainValidDate;
    private Float originalQuotaDay;
    private Float remainUsedDay;
    private Float adjustQuota;
    private Float nowQuota;
    private Long crttime;
    private Long crtuser;
    private Long updtime;
    private Long upduser;
    private Float fixUsedDay;
    private String remarks;
    private Float inTransitQuota;
    private Boolean ifCarryForward;
    private Long disCycleStart;
    private Long disCycleEnd;
    private Float validityDuration;
    private Integer validityUnit;
    private Integer ifAdvance;
    private String belongOrgId;
    private Integer originalEmpQuotaId;

    private String empName;
    private Long leaveTypeId;//假期类型
    private String leaveTypeName;//假期类型
    private Long hireDate;
    private Long terminationDate;
    private Long marriage;
    private String marriageName;
    private Long workplace;
    private String workplaceName;
    private String workno;

    private Integer acctTimeType;
    private Integer empStatus;
    private Long socialProvince;
    private Long socialCity;
    private Long configId;
    private String i18nLeaveTypeName;//假期类型

    @Autowired
    private IEmpQuotaRepository empQuotaRepository;
    @Resource
    private TextAspect textAspect;

    public int getEmpQuotaCountByYearAndType(QuotaDto dto) {
        return empQuotaRepository.getEmpQuotaCountByYearAndType(dto);
    }

    public List<Long> getGroupEmpList(Integer groupId, Short year, Long curDate, String datafilter) {
        return empQuotaRepository.getGroupEmpList(groupId, year, curDate, datafilter);
    }

    public PageList<WaEmpQuotaDo> getEmpFixQuotaList(FixQuotaSearchDto dto, String belongOrgId) {
        PageList<WaEmpQuotaDo> empFixQuotaList = empQuotaRepository.getEmpFixQuotaList(dto, belongOrgId);
        if (CollectionUtils.isNotEmpty(empFixQuotaList)) {
            for (WaEmpQuotaDo waEmpQuotaDo : empFixQuotaList) {
                if (waEmpQuotaDo.getWorkplace() != null) {
                    waEmpQuotaDo.setWorkplaceName(textAspect.getWorkPlaceText(waEmpQuotaDo.getWorkplace().toString(), belongOrgId));
                }
                if (waEmpQuotaDo.getMarriage() != null) {
                    waEmpQuotaDo.setMarriageName(textAspect.getMaritalEnumText(waEmpQuotaDo.getMarriage().toString(), belongOrgId));
                }
            }
        }
        return empFixQuotaList;
    }

    public void insertFixQuota(FixQuotaDto dto, UserInfo userInfo) {
        empQuotaRepository.insertFixQuota(dto, userInfo);
    }

    public void updateFixQuota(FixQuotaDto dto, UserInfo userInfo) {
        empQuotaRepository.updateFixQuota(dto, userInfo);
    }

    public WaEmpQuotaDo getEmpFixQuota(Long empQuotaId) {
        return empQuotaRepository.getEmpFixQuota(empQuotaId);
    }

    public List<WaEmpQuotaDo> getEmpFixQuotaByIds(String tenantId, List<Integer> empQuotaIds) {
        if (CollectionUtils.isEmpty(empQuotaIds)) {
            return new ArrayList<>();
        }
        return empQuotaRepository.getEmpFixQuotaByIds(tenantId, empQuotaIds);
    }

    public void deleteFixQuota(Long empQuotaId) {
        empQuotaRepository.deleteFixQuota(empQuotaId);
    }

    public void deleteFixQuotas(String tenantId, List<Integer> empQuotaIds) {
        empQuotaRepository.deleteFixQuotas(tenantId, empQuotaIds);
    }

    public List<Map> getCurrentlyEffectEmpQuotaList(String belongOrgId, Long curtime, Long empId) {
        return empQuotaRepository.getCurrentlyEffectEmpQuotaList(belongOrgId, curtime, empId);
    }

    public PageList<EmpQuotaDetail> getEmpQuotaDetailPageList(MyPageBounds pageBounds, String belongOrgId, Long curDate, Short year) {
        return empQuotaRepository.getEmpQuotaDetailPageList(pageBounds, belongOrgId, curDate, year);
    }

    public int getWaEmpQuotaCountByLeaveTypeId(String belongOrgId, Integer leaveTypeId) {
        return empQuotaRepository.getWaEmpQuotaCountByLeaveTypeId(belongOrgId, leaveTypeId);
    }

    public int getWaEmpCompensatoryQuotaCountByLeaveTypeId(String tenantId, Integer leaveTypeId) {
        return empQuotaRepository.getWaEmpCompensatoryQuotaCountByLeaveTypeId(tenantId, leaveTypeId);
    }

    public int updateQuotaDayByAnnualQuota(String belongOrgId, Long crossQuotaDate, Long updtime) {
        return empQuotaRepository.updateQuotaDayByAnnualQuota(belongOrgId, crossQuotaDate, updtime);
    }

    public AttendancePageResult<Map> getIssuedAnnuallyQuotaListByEffectiveTime(AttendanceBasePage basePage, String belongOrgId, Long startDate, Long endDate, Long empId) {
        return empQuotaRepository.getIssuedAnnuallyQuotaListByEffectiveTime(basePage, belongOrgId, startDate, endDate, empId);
    }

    public List<WaEmpQuotaDo> getEmpQuotaList(String tenantId, List<Long> quotaIds) {
        return empQuotaRepository.getEmpQuotaList(tenantId, quotaIds);
    }

    public List<EmpQuotaDetail> getNotExpiredEmpQuotaListByYear(String tenantId, Integer year, Long curDate) {
        return empQuotaRepository.getNotExpiredEmpQuotaListByYear(tenantId, year, curDate);
    }
}
