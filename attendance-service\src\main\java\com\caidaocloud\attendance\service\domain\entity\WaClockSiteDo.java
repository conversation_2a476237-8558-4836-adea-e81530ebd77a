package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.repository.IClockSiteRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Data
@Service
public class WaClockSiteDo {
    private Long id;

    private Long corpId;

    private String belongOrgId;

    private String siteName;

    private String address;

    private BigDecimal range;

    private BigDecimal lng;

    private BigDecimal lat;

    private Long creator;

    private Long createTime;

    private Long updater;

    private Long updateTime;

    @Autowired
    private IClockSiteRepository clockSiteRepository;

    public int save(WaClockSiteDo clockSiteDo) {
        return clockSiteRepository.save(clockSiteDo);
    }

    public int update(WaClockSiteDo clockSiteDo) {
        return clockSiteRepository.update(clockSiteDo);
    }

    public int deleteById(Long id) {
        return clockSiteRepository.deleteById(id);
    }

    public WaClockSiteDo getSiteById(Long id) {
        return clockSiteRepository.getById(id);
    }

    public AttendancePageResult<WaClockSiteDo> getPageList(AttendanceBasePage basePage, Long corpId, String belongOrgId) {
        return clockSiteRepository.getClockSitePageList(basePage, corpId, belongOrgId);
    }

    public List<WaClockSiteDo> getClockSiteListByIds(List<Long> ids) {
        return clockSiteRepository.getClockSiteListByIds(ids);
    }
}