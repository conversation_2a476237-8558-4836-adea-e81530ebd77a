package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.ILeaveTypeService;
import com.caidaocloud.attendance.service.application.service.ISobService;
import com.caidaocloud.attendance.service.application.service.ITravelCompensatoryService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.TravelCompensatoryItemDto;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.TravelCompensatoryReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTransferRulePeriod;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TravelCompensatoryServiceImpl implements ITravelCompensatoryService {
    @Resource
    private TravelCompensatoryDo travelCompensatoryDo;
    @Resource
    private EmpCompensatoryQuotaDo empCompensatoryQuotaDo;
    @Resource
    private LeaveQuotaConfigDo leaveQuotaConfigDo;
    @Autowired
    private ILeaveTypeService leaveTypeService;
    @Autowired
    private WaLeaveTypeDo leaveTypeDo;
    @Resource
    private ISobService sobService;
    @Autowired
    private SysEmpInfoDo sysEmpInfo;
    @Autowired
    private AttEmpGroupDo attEmpGroupDo;
    @Autowired
    private WaEmpTravelDaytimeDo waEmpTravelDaytimeDo;
    @Autowired
    private WaEmpTravelDo empTravelDo;
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    @Override
    public PageList<TravelCompensatoryItemDto> getTravelCompensatoryList(TravelCompensatoryReqDto dto, PageBean pageBean, UserInfo userInfo) {
        Map<String, Object> map = new HashMap<>();
        map.put("tenantId", userInfo.getTenantId());
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("orgid\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        map.put("filter", filter);
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            map.put("keywords", pageBean.getKeywords());
        }
        if (null != dto.getEmpId()) {
            map.put("empId", dto.getEmpId());
        }
        map.put("dataFilter", dto.getDataScope());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        PageList<TravelCompensatoryDo> pageList = travelCompensatoryDo.getTravelCompensatoryList(pageBounds, map);
        List<TravelCompensatoryItemDto> items = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(pageList)) {
            items = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(TravelCompensatoryItemDto.class);
            items.forEach(item -> {
                Integer quotaUint = item.getQuotaUnit();
                Float quotaDay = item.getQuotaDay();
                if (PreTimeUnitEnum.HOUR.getIndex().equals(quotaUint)) {
                    BigDecimal applyDurationB = new BigDecimal(String.valueOf(quotaDay)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    item.setQuotaDay(applyDurationB.floatValue());
                }
            });
        }
        return new PageList<>(items, pageList.getPaginator());
    }

    public List<WaSobDo> getAttendanceSobs(String tenantId, Long travelId, Long today) {
        if (null != travelId) {
            List<WaEmpTravelDaytimeDo> list = waEmpTravelDaytimeDo.getEmpTravelDaytimeByTravelId(Collections.singletonList(travelId));
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newArrayList();
            }
            Optional<Long> maxDateOpt = list.stream().map(WaEmpTravelDaytimeDo::getTravelDate).filter(Objects::nonNull).max(Long::compare);
            if (!maxDateOpt.isPresent()) {
                return Lists.newArrayList();
            }
            return sobService.getWaSobIdByDateRangeAndPeriodMonth(tenantId, maxDateOpt.get(), null, null);
        } else {
            return sobService.getWaSobIdByDateRangeAndPeriodMonth(tenantId, today, null, null);
        }
    }

    @Transactional
    @Override
    public void autoTravelToCompensatoryApproval(String tenantId, Long travelId, Long empId) throws Exception {
        if (null == tenantId) {
            return;
        }
        Long today = DateUtil.getOnlyDate();
        log.info("Start to execute autoTravelToCompensatory time:{}", DateUtil.getTimeStrByTimesamp(today));
        //查询当前生效的考勤周期
        List<WaSobDo> attendanceSobs = getAttendanceSobs(tenantId, travelId, today);
        if (CollectionUtils.isNotEmpty(attendanceSobs)) {
            Map<Integer, WaLeaveTypeDo> transferLeaveTypeMap = getTransferLeaveTypeGroupByLeaveTypeId(tenantId);
            if (transferLeaveTypeMap.isEmpty()) {
                log.info("Current tenant no leave type, tenantId:{}", tenantId);
                return;
            }
            //查询当前以及前一个周期
            List<WaSobDo> sobs = getSobs(tenantId, attendanceSobs);
            List<Integer> sobIds = sobs.stream().map(WaSobDo::getWaSobId).distinct().collect(Collectors.toList());
            //已转换记录
            List<TravelCompensatoryDo> transformedRecords = getTravelCompensatoryRecords(tenantId, sobIds);
            //已转换记录Map
            Map<String, TravelCompensatoryDo> transformedRecordMap = getTransformedRecordMap(transformedRecords);
            //已转换额度Map
            Map<Long, EmpCompensatoryQuotaDo> empCompensatoryMap = getEmpCompensatoryQuotaMap(transformedRecords);
            List<EmpCompensatoryQuotaDo> empCompensatoryQuotaList = Lists.newArrayList();
            List<EmpCompensatoryQuotaDo> updateEmpCompensatoryQuotaList = Lists.newArrayList();
            List<TravelCompensatoryDo> travelCompensatoryQuotaList = Lists.newArrayList();
            List<TravelCompensatoryDo> updateTravelCompensatoryQuotaList = Lists.newArrayList();
            List<WaEmpTravelDaytimeDo> allTravelDaytimeList = Lists.newArrayList();
            log.info("generateCompensatoryQuota 1");
            for (WaSobDo waSob : sobs) {
                Long startDate = waSob.getStartDate();
                Long endDate = waSob.getEndDate();
                //查询当前周期下的员工，查询周期内已分配考勤方案的员工
                List<Long> assignedEmpIds = getAssignedEmpIds(tenantId, startDate, endDate, waSob.getWaGroupId(), empId);
                //查询出差单据，按照出差类型分组
                Map<Long, List<WaEmpTravelDaytimeDo>> empTravelDaytimeMap = getTravelDaytimeList(tenantId, startDate, endDate, assignedEmpIds);
                if (empTravelDaytimeMap.isEmpty()) {
                    log.info("Current sob period no approval travel form,waSobId:{}", waSob.getWaSobId());
                    continue;
                }
                //额度规则匹配的员工
                Map<Long, List<Long>> leaveConfigMatchEmpMap = new HashMap<>();
                //假期规则下的额度规则
                Map<Integer, List<LeaveQuotaConfigDo>> leaveQuotaConfigMap = handleLeaveConfigs(tenantId, empTravelDaytimeMap, leaveConfigMatchEmpMap);
                Map<Integer, List<WaLeaveTypeDo>> leaveTypeGroupMap = new HashMap<>();
                log.info("generateCompensatoryQuota 2");
                for (Map.Entry<Long, List<WaEmpTravelDaytimeDo>> entry : empTravelDaytimeMap.entrySet()) {
                    List<WaEmpTravelDaytimeDo> empTravelDaytimeList = entry.getValue();
                    Optional<WaEmpTravelDaytimeDo> empTravelDaytimeOpt = empTravelDaytimeList.stream().findFirst();
                    if (!empTravelDaytimeOpt.isPresent()) {
                        continue;
                    }
                    //获取出差类型的配置
                    WaEmpTravelDaytimeDo travelModel = empTravelDaytimeOpt.get();
                    Integer overtimeRule = Optional.ofNullable(travelModel.getOvertimeRule()).orElse(2);
                    if (overtimeRule != 3) {
                        log.error("Travel to compensatory type not auto generate,tenantId:{},travelTypeId:{}", tenantId, travelModel.getTravelTypeId());
                        continue;
                    }
                    if (StringUtil.isBlank(travelModel.getAutoTransferRule())) {
                        log.error("Travel to compensatory type autoTransferRule blank,tenantId:{},travelTypeId:{}", tenantId, travelModel.getTravelTypeId());
                        continue;
                    }
                    List<TravelTransferRulePeriod> periods = JSON.parseArray(travelModel.getAutoTransferRule(), TravelTransferRulePeriod.class);
                    if (null == periods || periods.size() == 0) {
                        log.error("travel to compensatory type autoTransferRule blank,tenantId:{},travelTypeId:{}", tenantId, travelModel.getTravelTypeId());
                        continue;
                    }
                    //继按照出差类型分组后，再按照员工分组
                    Map<Long, List<WaEmpTravelDaytimeDo>> empTravelDaytimeGroupByEmpId = empTravelDaytimeList.stream().collect(Collectors.groupingBy(WaEmpTravelDaytimeDo::getEmpId));
                    for (Map.Entry<Long, List<WaEmpTravelDaytimeDo>> empEntry : empTravelDaytimeGroupByEmpId.entrySet()) {
                        List<WaEmpTravelDaytimeDo> travelDaytimeList = empEntry.getValue();
                        Optional<Long> maxDateOpt = travelDaytimeList.stream().map(WaEmpTravelDaytimeDo::getTravelDate).filter(Objects::nonNull).max(Long::compare);
                        if (!maxDateOpt.isPresent()) {
                            continue;
                        }
                        Long lastTravelDate = maxDateOpt.get();
                        //汇总求和周期内出差总时长
                        Float timeDuration = BigDecimal.valueOf(travelDaytimeList.stream().mapToDouble(WaEmpTravelDaytimeDo::getTimeDuration).sum()).floatValue();
                        TravelTransferRulePeriod transferRulePeriod = getTravelTransferRulePeriod(travelModel.getAcctTimeType(), timeDuration, periods);
                        if (null == transferRulePeriod) {
                            log.info("Travel to compensatory no match transfer rule,tenantId:{},travelTypeId:{}, empId:{}", tenantId, travelModel.getTravelTypeId(), empEntry.getKey());
                            continue;
                        }
                        //转换假期
                        Integer leaveTypeId = transferRulePeriod.getLeaveTypeId();
                        //转换时长
                        Double transferDuration = transferRulePeriod.getDuration();
                        if (transferDuration == null || leaveTypeId == null) {
                            continue;
                        }
                        //查询休假类型
                        Optional<WaLeaveTypeDo> leaveTypeOpt = Optional.ofNullable(transferLeaveTypeMap.get(leaveTypeId));
                        if (!leaveTypeOpt.isPresent()) {
                            log.info("Travel to compensatory leave type not exist");
                            continue;
                        }
                        WaLeaveTypeDo leaveType = leaveTypeOpt.get();
                        if (QuotaRestrictionTypeEnum.NO_LIMIT_QUOTA.getIndex().equals(leaveType.getQuotaRestrictionType())) {
                            log.info("Travel to compensatory leave type is not limit quota");
                            continue;
                        }
                        List<WaLeaveTypeDo> leaveTypes = Lists.newArrayList();
                        //查询考勤分组下的假期规则
                        if (!leaveTypeGroupMap.containsKey(waSob.getWaGroupId())) {
                            leaveTypes = leaveTypeDo.getLeaveTypeByGroupId(tenantId, waSob.getWaGroupId());
                            if (CollectionUtils.isNotEmpty(leaveTypes)) {
                                leaveTypeGroupMap.put(waSob.getWaGroupId(), leaveTypes);
                            }
                        } else {
                            leaveTypes = leaveTypeGroupMap.get(waSob.getWaGroupId());
                        }
                        if (CollectionUtils.isEmpty(leaveTypes) || leaveTypes.stream().noneMatch(l -> l.getLeaveTypeId().equals(leaveTypeId))) {
                            log.info("Travel to compensatory leave type not enable");
                            continue;
                        }
                        //查询配额配置信息
                        List<LeaveQuotaConfigDo> configDoList = leaveQuotaConfigMap.get(leaveTypeId);
                        if (CollectionUtils.isEmpty(configDoList)) {
                            log.info("Travel to compensatory no match leave quota config");
                            continue;
                        }
                        //筛选出差单所属日期有效期范围内的调休假额度规则(目前选取周期内最后一天的出差单日期匹配)
                        List<LeaveQuotaConfigDo> validLeaveQuotaConfig = configDoList.stream().filter(config -> config.getRuleStartDate() <= lastTravelDate && lastTravelDate < config.getRuleEndDate()).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(validLeaveQuotaConfig)) {
                            log.info("Travel to compensatory no match leave quota rule");
                            continue;
                        }
                        Optional<LeaveQuotaConfigDo> configOpt = Optional.ofNullable(matchLeaveQuotaConfig(empEntry.getKey(), validLeaveQuotaConfig, leaveConfigMatchEmpMap));
                        if (!configOpt.isPresent()) {
                            log.info("Travel to compensatory, the valid compensatory leave quota not match employee");
                            continue;
                        }
                        log.info("generateCompensatoryQuota start");
                        LeaveQuotaConfigDo config = configOpt.get();
                        Long calOnlyDate = DateUtil.getOnlyDate(new Date(endDate * 1000));
                        String key = String.format("%s_%s", waSob.getWaSobId(), empEntry.getKey());
                        //生成调休配额
                        EmpCompensatoryQuotaDo empCompensatoryQuota = genCompensatoryQuota(tenantId, transferDuration, empEntry.getKey(), config, leaveType, calOnlyDate);
                        if (null == empCompensatoryQuota) {
                            log.info("Travel to compensatory, the quota rule can not gen quota record");
                            continue;
                        }
                        TravelCompensatoryDo travelCompensatory = null;
                        if (transformedRecordMap.containsKey(key)) {
                            travelCompensatory = transformedRecordMap.get(key);
                            if (empCompensatoryMap.containsKey(travelCompensatory.getQuotaId())) {
                                EmpCompensatoryQuotaDo old = empCompensatoryMap.get(travelCompensatory.getQuotaId());
                                empCompensatoryQuota.setQuotaId(travelCompensatory.getQuotaId());
                                empCompensatoryQuota.setUsedDay(old.getUsedDay());
                                empCompensatoryQuota.setInTransitQuota(old.getInTransitQuota());
                                empCompensatoryQuota.setAdjustQuotaDay(old.getAdjustQuotaDay());
                                empCompensatoryQuota.setCreateTime(null);
                                updateEmpCompensatoryQuotaList.add(empCompensatoryQuota);
                                travelCompensatory.setQuotaDay(empCompensatoryQuota.getQuotaDay());
                                travelCompensatory.setQuotaUnit(empCompensatoryQuota.getQuotaUnit());
                                travelCompensatory.setUpdateTime(DateUtil.getCurrentTime(true));
                                updateTravelCompensatoryQuotaList.add(travelCompensatory);
                            }
                        } else {
                            empCompensatoryQuotaList.add(empCompensatoryQuota);
                            //生成转换记录
                            travelCompensatory = genTravelCompensatoryDo(waSob.getWaSobId(), empCompensatoryQuota);
                            travelCompensatoryQuotaList.add(travelCompensatory);
                        }
                        for (WaEmpTravelDaytimeDo empTravelDaytimeDo : empTravelDaytimeList) {
                            empTravelDaytimeDo.setEntityId(travelCompensatory.getId());
                        }
                        allTravelDaytimeList.addAll(empTravelDaytimeList);
                        log.info("generateCompensatoryQuota end");
                    }
                }
            }
            log.info("generateCompensatoryQuota 3");
            if (CollectionUtils.isNotEmpty(empCompensatoryQuotaList)) {
                // 新增调休配额
                empCompensatoryQuotaDo.save(empCompensatoryQuotaList);
            }
            if (CollectionUtils.isNotEmpty(updateEmpCompensatoryQuotaList)) {
                // 更新调休配额
                empCompensatoryQuotaDo.batchUpdate(updateEmpCompensatoryQuotaList);
            }
            if (CollectionUtils.isNotEmpty(travelCompensatoryQuotaList)) {
                // 新增调休转配额记录
                travelCompensatoryDo.batchSave(travelCompensatoryQuotaList);
            }
            if (CollectionUtils.isNotEmpty(updateTravelCompensatoryQuotaList)) {
                // 更新调休转配额记录
                travelCompensatoryDo.batchUpdate(updateTravelCompensatoryQuotaList);
            }
            if (CollectionUtils.isNotEmpty(allTravelDaytimeList)) {
                List<WaEmpTravelDaytimeDo> updateWaEmpTravelDaytime = allTravelDaytimeList.stream().filter(travel -> travel.getEntityId() != null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(updateWaEmpTravelDaytime)) {
                    // 更新出差详情
                    waEmpTravelDaytimeDo.batchUpdate(updateWaEmpTravelDaytime);
                }
            }
        }
        log.info("Execute autoTravelToCompensatoryApproval end time:{}", DateUtil.getTimeStrByTimesamp(System.currentTimeMillis() / 1000));
    }

    @Override
    public void generateCompensatoryQuota(Long travelId) throws Exception {
        log.info("manual approval passed autoTravelToCompensatory start");
        Optional<WaEmpTravelDo> empTravelOpt = Optional.ofNullable(empTravelDo.getWaEmpTravelByPrimaryKey(travelId));
        if (!empTravelOpt.isPresent()) {
            return;
        }
        WaEmpTravelDo empTravel = empTravelOpt.get();
        String tenantId = empTravel.getTenantId();
        Long empId = empTravel.getEmpId();
        autoTravelToCompensatoryApproval(tenantId, travelId, empId);
        log.info("manual approval passed autoTravelToCompensatory end");
    }

    /**
     * 调休配额过期，自动转付现
     *
     * @param tenantId 租户
     */
    @Override
    public void autoTravelToCompensatory(String tenantId) throws Exception {
        log.info("autoTravelToCompensatory start");
        autoTravelToCompensatoryApproval(tenantId, null, null);
        log.info("autoTravelToCompensatory end");
    }

    /**
     * 已调休额度Map
     *
     * @param transformedRecords
     * @return
     */
    public Map<Long, EmpCompensatoryQuotaDo> getEmpCompensatoryQuotaMap(List<TravelCompensatoryDo> transformedRecords) {
        if (CollectionUtils.isEmpty(transformedRecords)) {
            return new HashMap<>();
        }
        List<Long> quotaIds = transformedRecords.stream().map(TravelCompensatoryDo::getQuotaId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(quotaIds)) {
            return new HashMap<>();
        }
        List<EmpCompensatoryQuotaDo> list = empCompensatoryQuotaDo.getEmpCompensatoryQuotaList(quotaIds);
        return list.stream().collect(Collectors.toMap(EmpCompensatoryQuotaDo::getQuotaId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 出差转调休记录Map
     *
     * @param transformedRecords 出差转调休记录
     * @return
     */
    public Map<String, TravelCompensatoryDo> getTransformedRecordMap(List<TravelCompensatoryDo> transformedRecords) {
        if (CollectionUtils.isEmpty(transformedRecords)) {
            return new HashMap<>();
        }
        return transformedRecords.stream().collect(Collectors.toMap(record -> String.format("%s_%s", record.getSobId(), record.getEmpId()), Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 出差转调休记录
     *
     * @param tenantId 租户
     * @param sobIds   周期
     * @return
     */
    public List<TravelCompensatoryDo> getTravelCompensatoryRecords(String tenantId, List<Integer> sobIds) {
        if (CollectionUtils.isEmpty(sobIds)) {
            return Lists.newArrayList();
        }
        return travelCompensatoryDo.getTravelCompensatoryRecords(tenantId, sobIds);
    }

    /**
     * 出差转调休记录
     *
     * @param sobId                周期
     * @param empCompensatoryQuota 调休配额
     * @return
     */
    public TravelCompensatoryDo genTravelCompensatoryDo(Integer sobId, EmpCompensatoryQuotaDo empCompensatoryQuota) {
        TravelCompensatoryDo travelCompensatory = new TravelCompensatoryDo();
        travelCompensatory.setId(snowflakeUtil.createId());
        travelCompensatory.setEmpId(empCompensatoryQuota.getEmpId());
        travelCompensatory.setTenantId(empCompensatoryQuota.getTenantId());
        travelCompensatory.setSobId(sobId);
        travelCompensatory.setQuotaId(empCompensatoryQuota.getQuotaId());
        travelCompensatory.setQuotaDay(empCompensatoryQuota.getQuotaDay());
        travelCompensatory.setQuotaUnit(empCompensatoryQuota.getQuotaUnit());
        travelCompensatory.setCreateBy(0L);
        travelCompensatory.setCreateTime(DateUtil.getCurrentTime(true));
        travelCompensatory.setUpdateBy(0L);
        travelCompensatory.setUpdateTime(DateUtil.getCurrentTime(true));
        travelCompensatory.setDeleted(0);
        travelCompensatory.setStatus(1);
        return travelCompensatory;
    }

    /**
     * 额度规则匹配员工匹配验证
     *
     * @param empId                  员工
     * @param configDoList           额度规则
     * @param leaveConfigMatchEmpMap 额度规则匹配的运功
     * @return
     */
    public LeaveQuotaConfigDo matchLeaveQuotaConfig(Long empId, List<LeaveQuotaConfigDo> configDoList, Map<Long, List<Long>> leaveConfigMatchEmpMap) {
        if (null == configDoList || configDoList.size() == 0) {
            return null;
        }
        //根据有效期范围内的调休假额度规则的员工分组规则判断额度规则是否适用该出差单所属员工
        for (LeaveQuotaConfigDo quotaConfig : configDoList) {
            if (leaveConfigMatchEmpMap.containsKey(quotaConfig.getConfigId()) &&
                    leaveConfigMatchEmpMap.get(quotaConfig.getConfigId()).contains(empId)) {
                return quotaConfig;
            }
        }
        return null;
    }

    /**
     * 处理假期规则/额度规则配置
     *
     * @param tenantId               租户
     * @param empTravelDaytimeMap    出差单
     * @param leaveConfigMatchEmpMap 规则匹配的员工
     */
    private Map<Integer, List<LeaveQuotaConfigDo>> handleLeaveConfigs(String tenantId, Map<Long, List<WaEmpTravelDaytimeDo>> empTravelDaytimeMap, Map<Long, List<Long>> leaveConfigMatchEmpMap) {
        Map<Integer, List<LeaveQuotaConfigDo>> leaveConfigMap = new HashMap<>();
        if (empTravelDaytimeMap.isEmpty()) {
            return leaveConfigMap;
        }
        List<Integer> leaveTypeIds = Lists.newArrayList();
        for (Map.Entry<Long, List<WaEmpTravelDaytimeDo>> entry : empTravelDaytimeMap.entrySet()) {
            List<String> autoTransferRules = entry.getValue().stream().map(WaEmpTravelDaytimeDo::getAutoTransferRule).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            for (String autoTransferRule : autoTransferRules) {
                List<TravelTransferRulePeriod> periods = JSON.parseArray(autoTransferRule, TravelTransferRulePeriod.class);
                if (null == periods || periods.size() == 0) {
                    continue;
                }
                //转换假期
                leaveTypeIds.addAll(periods.stream().map(TravelTransferRulePeriod::getLeaveTypeId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isEmpty(leaveTypeIds)) {
            return leaveConfigMap;
        }
        leaveTypeIds = leaveTypeIds.stream().distinct().collect(Collectors.toList());
        List<LeaveQuotaConfigDo> configDoList = leaveQuotaConfigDo.getConfigListByIds(tenantId, leaveTypeIds);
        if (CollectionUtils.isEmpty(configDoList)) {
            return leaveConfigMap;
        }
        leaveConfigMap = configDoList.stream().collect(Collectors.groupingBy(LeaveQuotaConfigDo::getLeaveTypeId));
        List<String> businessKeys = configDoList.stream().map(c -> c.getConfigId().toString()).distinct().collect(Collectors.toList());
        Map<Long, EmployeeGroupDto> leaveQuotaConfigMap = leaveTypeService.getGroupRuleMap(tenantId, businessKeys);
        //根据有效期范围内的调休假额度规则的员工分组规则判断额度规则是否适用该出差单所属员工
        for (LeaveQuotaConfigDo quotaConfig : configDoList) {
            if (leaveQuotaConfigMap.containsKey(quotaConfig.getConfigId())) {
                EmployeeGroupDto groupDto = leaveQuotaConfigMap.get(quotaConfig.getConfigId());
                List<Long> empIds = sysEmpInfo.getEmpIdsByGroupExp(tenantId, null, null, null, null, groupDto.getGroupExp(), null);
                if (CollectionUtils.isNotEmpty(empIds)) {
                    leaveConfigMatchEmpMap.put(quotaConfig.getConfigId(), empIds);
                }
            }
        }
        return leaveConfigMap;
    }

    /**
     * 租户下的假期类型（规则）
     *
     * @param tenantId
     * @return
     */
    private Map<Integer, WaLeaveTypeDo> getTransferLeaveTypeGroupByLeaveTypeId(String tenantId) {
        List<WaLeaveTypeDo> leaveTypes = leaveTypeDo.getLeaveTypesByBelongOrgId(tenantId, null);
        return leaveTypes.stream().collect(Collectors.toMap(WaLeaveTypeDo::getLeaveTypeId, Function.identity(), (v1, v2) -> v1));
    }

    /**
     * 查询周期内审批通过的出差单据
     *
     * @param tenantId  租户
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param empIds    员工
     * @return
     */
    public Map<Long, List<WaEmpTravelDaytimeDo>> getTravelDaytimeList(String tenantId, Long startTime, Long endTime, List<Long> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return new HashMap<>();
        }
        List<WaEmpTravelDaytimeDo> travels = Lists.newArrayList();
        List<List<Long>> lists = ListTool.split(empIds, 300);
        for (List<Long> list : lists) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            travels.addAll(waEmpTravelDaytimeDo.getEmpTravelDaytimeList(tenantId, list, startTime, endTime, Collections.singletonList(2)));
        }
        return travels.stream().collect(Collectors.groupingBy(WaEmpTravelDaytimeDo::getTravelTypeId));
    }

    /**
     * 查询周期内已分配考勤方案的员工
     *
     * @param tenantId  租户
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param waGroupId 考勤方案
     * @return
     */
    public List<Long> getAssignedEmpIds(String tenantId, Long startDate, Long endDate, Integer waGroupId, Long empId) {
        AttEmpGroupDto dto = new AttEmpGroupDto();
        dto.setBelongOrgId(tenantId);
        dto.setStartTime(startDate);
        dto.setEndTime(endDate);
        dto.setWaGroupId(waGroupId);
        if (null != empId) {
            EmpInfoDTO empInfo = new EmpInfoDTO();
            empInfo.setEmpId(empId);
            dto.setEmpInfo(empInfo);
        }
        List<AttEmpGroupDo> list = attEmpGroupDo.getWaEmpGroupListByPeriod(dto);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(AttEmpGroupDo::getEmpId).distinct().collect(Collectors.toList());
    }

    /**
     * 汇总考勤周期
     *
     * @param attendanceSobs 考勤周期
     * @return
     * @throws Exception
     */
    public List<WaSobDo> getSobs(String tenantId, List<WaSobDo> attendanceSobs) throws Exception {
        List<WaSobDo> sobs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(attendanceSobs)) {
            for (WaSobDo sob : attendanceSobs) {
                List<Integer> sysPeriodMonths = new ArrayList<>();
                Integer sysPeriodMonth = sob.getSysPeriodMonth();
                sysPeriodMonths.add(sysPeriodMonth);
                Long lastMonth = DateUtil.addMonth(DateUtil.getOnlyDate(new Date(DateUtil.convertStringToDateTime(String.valueOf(sysPeriodMonth), "yyyyMM", Boolean.FALSE))), -1);
                Integer lastSysPeriodMonth = this.getPeriodMonth(lastMonth);
                sysPeriodMonths.add(lastSysPeriodMonth);
                sobs.addAll(sobService.getWaSobIdByDateRangeAndPeriodMonth(tenantId, null, sysPeriodMonths, sob.getWaGroupId()));
            }
        }
        return sobs.stream().sorted(Comparator.comparing(WaSobDo::getSysPeriodMonth)).collect(Collectors.toList());
    }

    /**
     * 获取年月
     *
     * @param time
     * @return
     */
    public Integer getPeriodMonth(long time) {
        return Integer.valueOf(DateUtil.parseDateToPattern(new Date(time * 1000), "yyyyMM"));
    }

    /**
     * 出差转换规则匹配
     *
     * @param timeUnit     单位：1天，2小时
     * @param timeDuration 时长
     * @param periods      转换规则
     * @return
     */
    public TravelTransferRulePeriod getTravelTransferRulePeriod(Integer timeUnit, Float timeDuration, List<TravelTransferRulePeriod> periods) {
        TravelTransferRulePeriod transferRulePeriod = null;
        if (null == periods || periods.size() == 0) {
            return transferRulePeriod;
        }
        for (TravelTransferRulePeriod period : periods) {
            Double start = period.getStart();
            Double end = period.getEnd();
            if (timeUnit == null || start == null || end == null) {
                continue;
            }
            if (PreTimeUnitEnum.HOUR.getIndex().equals(timeUnit)) {
                //单位为小时，进行时间转换
                start = BigDecimal.valueOf(start).multiply(BigDecimal.valueOf(60)).doubleValue();
                end = BigDecimal.valueOf(end).multiply(BigDecimal.valueOf(60)).doubleValue();
            }
            if (timeDuration >= start && timeDuration <= end) {
                //匹配到的调休转换规则
                transferRulePeriod = period;
                break;
            }
        }
        return transferRulePeriod;
    }

    /**
     * 根据调休额度规则生成调休配额
     *
     * @param tenantId         租户
     * @param transferDuration 转换时长
     * @param empId            员工
     * @param config           调休额度规则
     * @param leaveType        调休假期规则
     * @param calDate          计算日期
     * @return
     * @throws Exception
     */
    public EmpCompensatoryQuotaDo genCompensatoryQuota(String tenantId, Double transferDuration, Long empId, LeaveQuotaConfigDo config, WaLeaveTypeDo leaveType, Long calDate) throws Exception {
        //有效期类型
        Integer validityPeriodType = config.getValidityPeriodType();
        Integer validityStartType = config.getValidityStartType();
        Integer validityUnit = config.getValidityUnit();
        Float validityDuration = config.getValidityDuration();
        //1、固定有效期，2、当年失效，3、次年失效
        Integer invalidType = config.getInvalidType();
        //失效日期
        String invalidDate = config.getInvalidDate();
        //失效日期是否延长至失效月月底
        Boolean validityExtension = config.getValidityExtension();
        float duration = transferDuration.floatValue();
        Integer transferUnit = leaveType.getAcctTimeType();
        if (PreTimeUnitEnum.HOUR.getIndex().equals(transferUnit)) {
            //单位为小时，进行时间转换
            duration = BigDecimal.valueOf(transferDuration).multiply(BigDecimal.valueOf(60)).floatValue();
        }
        EmpCompensatoryQuotaDo compensatoryQuota = new EmpCompensatoryQuotaDo();
        compensatoryQuota.setConfigId(config.getConfigId());
        compensatoryQuota.setEmpId(empId);
        compensatoryQuota.setQuotaDay(duration);
        compensatoryQuota.setOriginalQuotaDay(duration);
        compensatoryQuota.setLeaveTypeId(leaveType.getLeaveTypeId());
        compensatoryQuota.setQuotaUnit(transferUnit);
        compensatoryQuota.setDeleted(0);
        compensatoryQuota.setStatus(ApprovalStatusEnum.PASSED.getIndex());
        compensatoryQuota.setTenantId(tenantId);
        if (ValidityPeriodTypeEnum.RESTRICTION.getIndex().equals(validityPeriodType)) {
            compensatoryQuota.setValidityPeriodType(validityStartType);
            //限制
            Long startDate = 1L;
            if (ValidityStartTypeEnum.NATURAL_YEAR.getIndex().equals(validityStartType)) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date(calDate * 1000));
                calendar.set(calendar.get(Calendar.YEAR), 0, 1, 0, 0, 0);
                startDate = calendar.getTime().getTime() / 1000;
            } else if (ValidityStartTypeEnum.OVERTIME_DATE.getIndex().equals(validityStartType)) {
                startDate = calDate;
            } else if (ValidityStartTypeEnum.OVERTIME_MONTH.getIndex().equals(validityStartType)) {
                startDate = DateUtilExt.getMonthBegin(calDate);
            }
            compensatoryQuota.setStartDate(startDate);
            Long lastDate = 0L;
            if (InvalidTypeEnum.FIXED.getIndex().equals(invalidType)) {
                if (validityUnit.equals(ValidityUnitEnum.DAY.getIndex())) {
                    int time = validityDuration.intValue() <= 0 ? 0 : validityDuration.intValue() - 1;
                    lastDate = DateUtilExt.getAddYearOrMonthOrDateEndTime(compensatoryQuota.getStartDate() * 1000, 0, 0, time);
                } else if (validityUnit.equals(ValidityUnitEnum.MONTH.getIndex())) {
                    lastDate = DateUtilExt.getAddYearOrMonthOrDateEndTime(compensatoryQuota.getStartDate() * 1000, 0, validityDuration.intValue(), -1);
                } else if (validityUnit.equals(ValidityUnitEnum.YEAR.getIndex())) {
                    lastDate = DateUtilExt.getAddYearOrMonthOrDateEndTime(compensatoryQuota.getStartDate() * 1000, validityDuration.intValue(), 0, -1);
                }
                if (null != validityExtension && validityExtension) {
                    lastDate = DateUtilExt.getMonthEnd(lastDate);
                }
            } else if (InvalidTypeEnum.CURRENT_YEAR.getIndex().equals(invalidType)) {
                lastDate = DateUtil.convertStringToDateTime(String.format("%s%s", DateUtilExt.getTimeYear(compensatoryQuota.getStartDate()), invalidDate), "yyyyMMdd", true) + 86399;
                //出差日期大于失效日期则不生成额度
                if (lastDate < calDate) {
                    return null;
                }
            } else {
                lastDate = DateUtil.convertStringToDateTime(String.format("%s%s", DateUtilExt.getTimeYear(compensatoryQuota.getStartDate()), invalidDate), "yyyyMMdd", true);
                lastDate = DateUtilExt.addYear(lastDate, 1) + 86399;
            }
            compensatoryQuota.setLastDate(lastDate);
            // 失效日期小于等生效日期则不生成额度
            if (lastDate <= startDate) {
                return null;
            }
        } else {
            compensatoryQuota.setValidityPeriodType(0);
            compensatoryQuota.setStartDate(1L);
            compensatoryQuota.setLastDate(253402271999L);
        }
        compensatoryQuota.setQuotaId(snowflakeUtil.createId());
        compensatoryQuota.setCreateBy(0L);
        compensatoryQuota.setCreateTime(DateUtil.getCurrentTime(true));
        compensatoryQuota.setUpdateBy(0L);
        compensatoryQuota.setUpdateTime(DateUtil.getCurrentTime(true));
        compensatoryQuota.setDataSource(DataSourceEnum.TRAVEL.name());
        if (compensatoryQuota.getUsedDay() == null) {
            compensatoryQuota.setUsedDay(0f);
        }
        if (compensatoryQuota.getInTransitQuota() == null) {
            compensatoryQuota.setInTransitQuota(0f);
        }
        if (compensatoryQuota.getAdjustQuotaDay() == null) {
            compensatoryQuota.setAdjustQuotaDay(0f);
        }
        return compensatoryQuota;
    }
}