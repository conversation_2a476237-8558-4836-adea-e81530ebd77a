package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IEmpCompensatoryQuotaRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo;
import com.caidaocloud.attendance.service.interfaces.dto.quota.CompensatoryQuotaSearchDto;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Service
public class EmpCompensatoryQuotaDo {
    private Long quotaId;
    private String tenantId;
    private String workno;
    private Long empId;
    private String empName;
    private Integer leaveTypeId;
    private String leaveTypeName;
    private Integer empQuotaId;
    private Integer overtimeDetailId;
    private Long overtimeDate;
    private Integer overtimeType;
    private Float overtimeDuration;
    private Integer overtimeUnit;
    private Float workingTime;
    private Integer quotaUnit;
    private Float quotaDay;
    private Float adjustQuotaDay;
    private Float usedDay;
    private Float inTransitQuota;
    private Integer validityPeriodType;
    private Long startDate;
    private Long lastDate;
    private Integer status;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private String remark;
    private Long configId;
    private String dataSource;
    private Float originalQuotaDay;
    private String i18nLeaveTypeName;
    private String entityId;

    @Autowired
    private IEmpCompensatoryQuotaRepository empCompensatoryQuotaRepository;

    public PageList<EmpCompensatoryQuotaDo> getEmpCompensatoryQuotaList(CompensatoryQuotaSearchDto dto, String tenantId) {
        return empCompensatoryQuotaRepository.getEmpCompensatoryQuotaList(dto, tenantId);
    }

    public List<EmpCompensatoryQuotaDo> getQuotaList(String tenantId, List<Integer> overtimeDetailIds) {
        return empCompensatoryQuotaRepository.getQuotaList(tenantId, overtimeDetailIds);
    }

    public List<EmpCompensatoryQuotaDo> getQuotaByDate(String tenantId, List<Long> empIds, Long startDate, Long endDate, String dataSource, List<Integer> status) {
        return empCompensatoryQuotaRepository.getQuotaListByDate(tenantId, empIds, startDate, endDate, dataSource, status);
    }

    public int save(List<EmpCompensatoryQuotaDo> list) {
        return empCompensatoryQuotaRepository.save(list);
    }

    public EmpCompensatoryQuotaDo getByQuotaId(Long quotaId) {
        return empCompensatoryQuotaRepository.getByQuotaId(quotaId);
    }

    public void delete(Long quotaId) {
        empCompensatoryQuotaRepository.delete(quotaId);
    }

    public void deleteByIds(String tenantId, List<Long> quotaIds) {
        empCompensatoryQuotaRepository.deleteByIds(tenantId, quotaIds);
    }

    public void batchUpdate(List<EmpCompensatoryQuotaDo> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<WaEmpCompensatoryQuotaPo> models = ObjectConverter.convertList(items, WaEmpCompensatoryQuotaPo.class);
        empCompensatoryQuotaRepository.batchUpdate(models);
    }

    public List<Map> queryEmpQuotaList(Map params) {
        return empCompensatoryQuotaRepository.queryEmpQuotaList(params);
    }

    public List<EmpCompensatoryQuotaDo> getQuotaListByIds(String tenantId, List<Long> quotaIds) {
        return ObjectConverter.convertList(empCompensatoryQuotaRepository.getQuotaListByIds(tenantId, quotaIds), EmpCompensatoryQuotaDo.class);
    }

    public int updateByEmpIdAndLeaveType(String tenantId, Long empId, Integer leaveTypeId, EmpCompensatoryQuotaDo quotaDo) {
        return empCompensatoryQuotaRepository.updateByEmpIdAndLeaveType(tenantId, empId, leaveTypeId, quotaDo);
    }

    public List<EmpCompensatoryQuotaDo> getQuotaListByEmpIdAndLeaveType(String tenantId, Long empId, Integer leaveTypeId) {
        List<WaEmpCompensatoryQuotaPo> list = empCompensatoryQuotaRepository.getQuotaListByEmpIdAndLeaveType(tenantId, empId, leaveTypeId);
        return ObjectConverter.convertList(list, EmpCompensatoryQuotaDo.class);
    }

    public int update(EmpCompensatoryQuotaDo quotaDo) {
        return empCompensatoryQuotaRepository.update(quotaDo);
    }

    public EmpCompensatoryQuotaDo getCompensatoryByDate(String tenantId, Long empId, Long overtimeDate) {
        WaEmpCompensatoryQuotaPo quota = empCompensatoryQuotaRepository.getByOverTimeDate(tenantId, empId, overtimeDate);
        if (quota != null) {
            return ObjectConverter.convert(quota, EmpCompensatoryQuotaDo.class);
        }
        return null;
    }

    public List<EmpCompensatoryQuotaDo> getEmpQuotas(EmpCompensatoryQuotaDo empCompensatoryQuota) {
        return empCompensatoryQuotaRepository.getEmpQuotas(empCompensatoryQuota);
    }

    public int save(EmpCompensatoryQuotaDo quota) {
        return empCompensatoryQuotaRepository.save(quota);
    }

    public int updateWaEmpCompensatoryQuota(Long userId, Long currentTime, List<Long> idList, Integer status) {
        return empCompensatoryQuotaRepository.updateWaEmpCompensatoryQuota(userId, currentTime, idList, status);
    }

    public List<EmpCompensatoryQuotaDo> getApplyCompensatoryQuotaList(String tenantId, List<Long> quotaIds, Integer status, Long currentTime) {
        return ObjectConverter.convertList(empCompensatoryQuotaRepository.getApplyCompensatoryQuotaList(tenantId, quotaIds, status, currentTime), EmpCompensatoryQuotaDo.class);
    }

    public List<EmpCompensatoryQuotaDo> getEmpCompensatoryQuotaList(List<Long> quotaIds) {
        List<WaEmpCompensatoryQuotaPo> models = empCompensatoryQuotaRepository.getEmpCompensatoryQuotaList(quotaIds);
        return ObjectConverter.convertList(models, EmpCompensatoryQuotaDo.class);
    }
}