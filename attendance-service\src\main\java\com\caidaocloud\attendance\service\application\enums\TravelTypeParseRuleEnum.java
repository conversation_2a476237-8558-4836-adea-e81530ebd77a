package com.caidaocloud.attendance.service.application.enums;

/**
 *  外勤分析规则：1 出差单 2 外勤打卡 3 出差单联动外勤打卡
 */
public enum TravelTypeParseRuleEnum {
    TRAVEL_BILL(1, "出差单"),
    TRAVEL_REG(2, "外勤打卡"),
    TRAVEL_BILL_REG(3, "出差单联动外勤打卡");

    private Integer index;
    private String name;

    TravelTypeParseRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (TravelTypeParseRuleEnum c : TravelTypeParseRuleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
