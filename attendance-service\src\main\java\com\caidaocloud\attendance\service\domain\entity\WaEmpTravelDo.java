package com.caidaocloud.attendance.service.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.domain.repository.IWaEmpTravelRepository;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Service
public class WaEmpTravelDo {
    private Long travelId;

    private String tenantId;

    private Long empId;

    private Long travelTypeId;

    private Long startTime;

    private Long endTime;

    private String shalfDay;

    private String ehalfDay;

    private Long shiftStartTime;

    private Long shiftEndTime;

    private Float timeDuration;

    private Short timeUnit;

    private Short periodType;

    private Long province;

    private Long city;

    private Integer county;

    private String travelMode;

    private Long lastApprovalTime;

    private Long lastEmpid;

    private String reason;

    private Integer status;

    private String revokeReason;

    private Integer revokeStatus;

    private String fileName;

    private String fileId;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    private Long quotaId;

    private String businessKey;

    private String extCustomCol;

    private Long batchTravelId;

    //其他字段
    private String workNo;

    private String empName;

    private String orgName;

    private String fullPath;

    private String workCity;

    private Long hireDate;

    private String travelType;

    private String statusName;

    private String employType;
    private Long realDate;
    private Long revokeId;
    private String processCode;
    private String i18nTravelTypeName;

    @TableField(exist = false)
    @Resource
    private IWaEmpTravelRepository waEmpTravelRepository;

    public PageResult<WaEmpTravelDo> getWaEmpTravelList(AttendanceBasePage basePage, Map params) {
        return waEmpTravelRepository.getEmpTravelPageList(basePage, params);
    }

    public PageResult<WaEmpTravelDo> getWaEmpTravelListOfPortal(QueryPageBean queryPageBean, Boolean ifBatch) {
        return waEmpTravelRepository.getEmpTravelPageListOfPortal(queryPageBean, ifBatch);
    }

    @CDText(exp = {"employType" + TextAspect.DICT_E, "workCity" + TextAspect.PLACE, "travelMode" + TextAspect.DICT_TRAVEL_MODE}, classType = WaEmpTravelDo.class)
    public WaEmpTravelDo getWaEmpTravelDetailById(Long id, Long corpId) {
        return waEmpTravelRepository.getWaEmpTravelById(id, corpId);
    }

    public WaEmpTravelDo getWaEmpTravelByPrimaryKey(Long id) {
        return waEmpTravelRepository.getWaEmpTravelByPrimaryKey(id);
    }

    public int save(WaEmpTravelDo empTravelDo) {
        return waEmpTravelRepository.save(empTravelDo);
    }

    public int update(WaEmpTravelDo empTravelDo) {
        return waEmpTravelRepository.update(empTravelDo);
    }

    public Integer checkEmpTravelTimeRepeat(Long empId, Long startTime, Long endTime) {
        return waEmpTravelRepository.checkEmpTravelTimeRepeat(empId, startTime, endTime);
    }

    public List<WaEmpTravelDo> getTravelInfoList(String belongOrgId, Long empId, Long dayTime, Long endTime) {
        return waEmpTravelRepository.getTravelList(belongOrgId, empId, dayTime, endTime);
    }

    public List<WaEmpTravelDo> getTravelInfoByTravelTypeId(String tenantId, Long travelTypeId) {
        return ObjectConverter.convertList(waEmpTravelRepository.getTravelInfoByTravelTypeId(tenantId, travelTypeId), WaEmpTravelDo.class);
    }

    public List<WaEmpTravelDo> getEmpTravelByEmpIds(List<Long> empIds, Long travelDateDate) {
        return waEmpTravelRepository.getEmpTravelByEmpIds(empIds, travelDateDate);
    }

    public int delete(Long travelId) {
        return waEmpTravelRepository.delete(travelId);
    }

    @CDText(exp = {"employType" + TextAspect.DICT_E, "workCity" + TextAspect.PLACE, "travelMode" + TextAspect.DICT_TRAVEL_MODE}, classType = WaEmpTravelDo.class)
    public WaEmpTravelDo getEmpTravelRevokeDetailById(String tenantId, Long id) {
        return waEmpTravelRepository.getEmpTravelRevokeById(tenantId, id);
    }

    public List<WaEmpTravelDo> listByBatchId(String tenantId, Long batchTravelId) {
        return waEmpTravelRepository.selectListByBatchId(tenantId, batchTravelId);
    }

    public int deleteByTravelIds(List<Long> travelIds) {
        return waEmpTravelRepository.deleteByTravelIds(travelIds);
    }

    public PageResult<WaEmpTravelDo> getWaEmpTravelListOfPortal(QueryPageBean queryPageBean, BusinessCodeEnum workflowEnum) {
        return waEmpTravelRepository.getEmpTravelPageListOfPortal(queryPageBean, workflowEnum);
    }

    public List<WaEmpTravelDo> getDurationOfTravel(List<Long> travelIdList) {
        return waEmpTravelRepository.getDurationOfTravel(travelIdList);
    }
}