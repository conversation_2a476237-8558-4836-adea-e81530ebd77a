package com.caidaocloud.attendance.service.application.dto.clock;

import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.core.wa.vo.MultiWorkTimeInfoSimpleVo;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClockListShiftDefDto {
    private String name;
    private List<ClockListShiftDefWorkTimeDto> workTimeDtoList;

    public static List<ClockListShiftDefDto> getList(List<MultiShiftSimpleVo> shiftSimpleVoList, Long nowDate,
                                                     Long belongDate, Long regDateTime, boolean ifExport) {
        MultiShiftSimpleVo shiftSimpleVo = shiftSimpleVoList.get(0);
        List<MultiWorkTimeInfoSimpleVo> multiWorkTimeList = shiftSimpleVo.getMultiWorkTimes();

        List<ClockListShiftDefWorkTimeDto> workTimeDtoList = new ArrayList<>();
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftSimpleVo.getDateType())
                && CollectionUtils.isNotEmpty(multiWorkTimeList)) {
            multiWorkTimeList.sort(Comparator.comparing(MultiWorkTimeInfoSimpleVo::getRealStartTime));
            int size = multiWorkTimeList.size();
            boolean belongFlag = false;
            for (int i = 0; i < multiWorkTimeList.size(); i++) {
                MultiWorkTimeInfoSimpleVo timeIt = multiWorkTimeList.get(i);
                String start = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getStartTime() * 60), "HH:mm", true);
                String end = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getEndTime() * 60), "HH:mm", true);
                if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getStartTimeBelong())) {
                    start = ShiftTimeBelongTypeEnum.getName(timeIt.getStartTimeBelong()) + start;
                }
                if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getEndTimeBelong())) {
                    end = ShiftTimeBelongTypeEnum.getName(timeIt.getEndTimeBelong()) + end;
                }
                boolean highlight = false;
                if (!belongFlag && !ifExport && size > 1 && null != belongDate && null != regDateTime) {
                    long offDutyEndTime = belongDate + timeIt.doGetRealOffDutyEndTime() * 60L;
                    if (i == size - 1) {
                        belongFlag = true;
                    } else {
                        belongFlag = regDateTime <= offDutyEndTime;
                    }
                    highlight = belongFlag;
                }
                workTimeDtoList.add(new ClockListShiftDefWorkTimeDto(String.format("%s~%s", start, end), highlight));
            }
        }
        return Lists.newArrayList(new ClockListShiftDefDto(shiftSimpleVo.getShiftDefName(), workTimeDtoList));
    }

    public static String getShiftTxtForExport(List<ClockListShiftDefDto> shiftDefList) {
        List<String> shiftTxtList = shiftDefList.stream().map(it -> {
            if (CollectionUtils.isNotEmpty(it.getWorkTimeDtoList())) {
                List<String> workTimeList = it.getWorkTimeDtoList().stream()
                        .map(ClockListShiftDefWorkTimeDto::getWorkTime).collect(Collectors.toList());
                return String.format("%s(%s)", it.getName(), StringUtils.join(workTimeList, "，"));
            } else {
                return it.getName();
            }
        }).collect(Collectors.toList());
        return StringUtils.join(shiftTxtList, "，");
    }
}
