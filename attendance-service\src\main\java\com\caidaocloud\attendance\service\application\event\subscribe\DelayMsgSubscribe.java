package com.caidaocloud.attendance.service.application.event.subscribe;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.attendance.service.application.event.publish.MessageParams;
import com.caidaocloud.attendance.service.application.service.msg.MessageService;
import com.caidaocloud.mq.rabbitmq.consumer.AbsMQConsumer;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class DelayMsgSubscribe extends AbsMQConsumer {

    @Resource
    private MessageService messageService;

    @Override
    public void process(String message) {
        log.info("DelayMsgSubscribe message={}", message);
        try {
            MessageParams msgInfo = JSON.parseObject(message, MessageParams.class);
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(msgInfo.getTenantId());
            userInfo.setEmpId(0L);
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            messageService.handleMsg(msgInfo);
        } catch (Exception ex) {
            log.error("An exception occurred in DelayMsgSubscribe message listening. Why:{}", ex.getMessage(), ex);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
}
