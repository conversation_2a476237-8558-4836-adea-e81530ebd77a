package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.sdk.dto.SdkCompensatoryCaseDTO;
import com.caidaocloud.attendance.sdk.dto.SdkCompensatoryRevokeDTO;
import com.caidaocloud.attendance.sdk.feign.ICompensatoryFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class SdkCompensatoryCaseService {
    @Autowired
    private ICompensatoryFeignClient compensatoryFeignClient;

    /**
     * 调休付现申请
     *
     * @param dto
     * @return
     */
    public Result<?> saveCompensatoryCaseApply(SdkCompensatoryCaseDTO dto) {
        return compensatoryFeignClient.saveCompensatoryCaseApply(dto);
    }

    /**
     * 撤销调休付现申请
     *
     * @param dto
     * @return
     */
    public Result<?> revokeCompensatoryCaseApply(SdkCompensatoryRevokeDTO dto) {
        return compensatoryFeignClient.revokeCompensatoryCaseApply(dto);
    }

    public Result<?> getEmpCompensatoryCaseList(Map<String, Object> parameter) {
        return compensatoryFeignClient.getEmpCompensatoryCaseList(parameter);
    }

    /**
     * 可申请调休配额
     * @return
     */
    public Result<?> myCompensatoryQuota() {
        return compensatoryFeignClient.myCompensatoryQuota();
    }
}