package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤异常调整明细DTO
 *
 * <AUTHOR>
 * @Date 2024/6/25
 */
@Data
public class AnalyseResultAdjustDto {
    @ApiModelProperty("考勤日期")
    private Long belongDate;

    @ApiModelProperty("迟到时长")
    private Float originalLateTime;

    @ApiModelProperty("早退时长")
    private Float originalEarlyTime;

    @ApiModelProperty("旷工时长")
    private Integer originalKgWorkTime;

    @ApiModelProperty("原签到ID")
    private Integer originalSigninId;

    @ApiModelProperty("原签退ID")
    private Integer originalSignoffId;

    @ApiModelProperty("原签到时间")
    private Long originalSigninTime;

    @ApiModelProperty("原签退时间")
    private Long originalSignoffTime;

    @ApiModelProperty("调整后的签到时间")
    private Long signinTime;

    @ApiModelProperty("调整后的签退时间")
    private Long signoffTime;

    @ApiModelProperty("事由")
    private String reason;
}
