package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.infrastructure.util.DateUtil;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.time.LocalDateTime;

/**
 * 考勤明细时间类型枚举
 * created by: FoAng
 * create time: 18/3/2024 4:30 下午
 */
public enum TimePeriodEnum {

    CUSTOM("自定义"),
    TODAY("今天"),
    YESTERDAY("昨天"),
    THIS_WEEK("本周"),
    LAST_WEEK("上周"),
    THIS_MONTH("本月"),
    LAST_MONTH("上月");

    private final String description;

    TimePeriodEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static ImmutablePair<Long, Long> parsePeriod(TimePeriodEnum periodEnum) {
        LocalDateTime todayTime = DateUtil.getTodayStartOfDay();
        if (periodEnum == CUSTOM) {
            return null;
        } else if (periodEnum == YESTERDAY) {
            return new ImmutablePair<>(DateUtil.toInstant(DateUtil.getYesterdayStartOfDay()),
                    DateUtil.toInstant(DateUtil.getYesterdayStartOfDay()));
        } else if (periodEnum == THIS_WEEK) {
            return new ImmutablePair<>(DateUtil.toInstant(DateUtil.getThisWeekStartOfWeek()),
                    DateUtil.toInstant(todayTime));
        } else if (periodEnum == LAST_WEEK) {
            return new ImmutablePair<>(DateUtil.toInstant(DateUtil.getLastWeekStartOfWeek()),
                    DateUtil.toInstant(DateUtil.getLastWeekEndOfWeek()));
        } else if (periodEnum == THIS_MONTH) {
            return new ImmutablePair<>(DateUtil.toInstant(DateUtil.getThisMonthStartOfMonth()),
                    DateUtil.toInstant(todayTime));
        } else if (periodEnum == LAST_MONTH) {
            return new ImmutablePair<>(DateUtil.toInstant(DateUtil.getLastMonthStartOfMonth()),
                    DateUtil.toInstant(DateUtil.getLastMonthEndOfMonth()));
        }
        return new ImmutablePair<>(DateUtil.toInstant(todayTime),
                DateUtil.toInstant(todayTime));
    }
}
