package com.caidaocloud.attendance.core.wa.dto;

import com.caidao1.wa.mybatis.model.WaAnalyze;

/**
 * Created by ken on 17/7/27.
 */
public class ShiftKyInfo {
    /**
     * 班次跨夜标识
     */
    private Boolean isKy = false;
    /**
     * 加班单跨夜需要用到的前一天的签到签退对象
     */
    private WaAnalyze preAnalyze = null;
    /**
     *  非工作日的跨夜
      */
    private Boolean isOffDayKy = false;

    public Boolean getKy() {
        return isKy;
    }

    public void setKy(Boolean ky) {
        isKy = ky;
    }

    public WaAnalyze getPreAnalyze() {
        return preAnalyze;
    }

    public void setPreAnalyze(WaAnalyze preAnalyze) {
        this.preAnalyze = preAnalyze;
    }

    public Boolean getOffDayKy() {
        return isOffDayKy;
    }

    public void setOffDayKy(Boolean offDayKy) {
        isOffDayKy = offDayKy;
    }
}
