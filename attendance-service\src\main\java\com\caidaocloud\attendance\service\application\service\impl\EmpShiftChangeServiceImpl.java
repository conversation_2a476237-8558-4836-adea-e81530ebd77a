package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.attendance.service.application.dto.EmpShiftChangeDto;
import com.caidaocloud.attendance.service.application.service.IEmpShiftChangeService;
import com.caidaocloud.attendance.service.domain.entity.EmpShiftChangeDo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: <PERSON>.Chen
 * @Date: 2021/12/1 15:49
 * @Description:
 **/
@Service
public class EmpShiftChangeServiceImpl implements IEmpShiftChangeService {

    @Autowired
    private EmpShiftChangeDo empShiftChangeDo;
    @Autowired
    private ISessionService sessionService;

    @Override
    public List<EmpShiftChangeDto> getEmpShiftChanges(List<Long> empIds, Long startDate, Long endDate, Integer status) {
        UserInfo userInfo = sessionService.getUserInfo();
        String tenantId = userInfo.getTenantId();
        return ObjectConverter.convertList(empShiftChangeDo.getEmpShiftChanges(tenantId, empIds, startDate, endDate, status), EmpShiftChangeDto.class);
    }

    @Override
    public void saveEmpShiftChanges(List<EmpShiftChangeDto> changes) {
        empShiftChangeDo.saveEmpShiftChanges(ObjectConverter.convertList(changes, EmpShiftChangeDo.class));
    }

    @Override
    public void updateEmpShiftChanges(List<EmpShiftChangeDto> changes) {
        empShiftChangeDo.updateEmpShiftChanges(ObjectConverter.convertList(changes, EmpShiftChangeDo.class));
    }
}
