package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import lombok.Data;

@Data
public class PaySyncWaMessage extends RabbitBaseMessage {
    public static PaySyncWaMessage bulidMsg(String msg, String exchange, String routingKey){
        PaySyncWaMessage message = new PaySyncWaMessage();
        message.setBody(msg);
        message.setExchange(exchange);
        message.setRoutingKey(routingKey);
        return message;
    }

}
