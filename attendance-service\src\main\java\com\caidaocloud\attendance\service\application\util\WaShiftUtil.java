package com.caidaocloud.attendance.service.application.util;

import com.caidaocloud.attendance.core.wa.dto.ShiftRestPeriods;

/**
 * 考勤班次工具类
 *
 * <AUTHOR>
 * @Date 2024/1/10
 */
public class WaShiftUtil {

    /**
     * 获取班次工作休息时间
     *
     * @param restStart
     * @param restEnd
     * @param shiftStart
     * @param shiftEnd
     * @return
     */
    public static ShiftRestPeriods getShiftWorkRestTime(Integer restStart, Integer restEnd, Integer shiftStart, Integer shiftEnd) {
        ShiftRestPeriods restPeriods = new ShiftRestPeriods();
        if (restEnd == null || restStart == null) {
            restPeriods.setNoonRestStart(0);
            restPeriods.setNoonRestEnd(0);
        } else {
            if (restStart > restEnd) {
                // 班次跨夜且休息时间跨夜
                restEnd = restEnd + 24 * 60;
            } else if (restStart < shiftStart && restStart <= shiftEnd) {
                // 班次跨夜且休息时间全在第二天
                restStart = restStart + 24 * 60;
                restEnd = restEnd + 24 * 60;
            }
            restPeriods.setNoonRestStart(restStart);
            restPeriods.setNoonRestEnd(restEnd);
        }
        return restPeriods;
    }
}
