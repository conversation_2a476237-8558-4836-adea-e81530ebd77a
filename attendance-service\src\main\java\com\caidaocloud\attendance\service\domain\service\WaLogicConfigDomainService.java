package com.caidaocloud.attendance.service.domain.service;

import com.caidaocloud.attendance.service.domain.entity.WaLogicConfigDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 考勤逻辑配置
 *
 * <AUTHOR>
 * @Date 2024/2/4
 */
@Slf4j
@Service
public class WaLogicConfigDomainService {
    @Autowired
    private WaLogicConfigDo waLogicConfigDo;

    public List<WaLogicConfigDo> getListByCodes(String tenantId, List<String> logicCodes) {
        return waLogicConfigDo.getListByCodes(tenantId, logicCodes);
    }
}
