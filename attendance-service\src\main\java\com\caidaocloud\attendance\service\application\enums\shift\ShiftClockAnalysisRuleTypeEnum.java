package com.caidaocloud.attendance.service.application.enums.shift;

/**
 * 班次设置-打卡分析规则-分析类型
 */
public enum ShiftClockAnalysisRuleTypeEnum {
    BY_OT("BY_OT", "按照加班时间"),
    BY_CLOCK_TIME("BY_CLOCK_TIME", "按照打卡时间");

    private String code;

    private String name;

    ShiftClockAnalysisRuleTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public static String getName(String code) {
        for (ShiftClockAnalysisRuleTypeEnum c : ShiftClockAnalysisRuleTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
