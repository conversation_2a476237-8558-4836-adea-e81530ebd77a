package com.caidaocloud.attendance.core.wa.service;

import com.caidao1.wa.dto.EmpCalendarInfoDto;
import com.caidaocloud.attendance.core.annoation.feign.WorkCalendarFeignClient;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpCalendarShiftQueryDto;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpRelCalendarQueryDto;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 假勤模块-工作日历
 *
 * <AUTHOR>
 * @Date 2025/2/14
 */
@Slf4j
@Service
public class WorkCalendarFeignService {
    @Autowired
    private WorkCalendarFeignClient workCalendarFeignClient;

    public List<EmpCalendarInfoDto> listEmpRelCalendar(ListEmpRelCalendarQueryDto queryDto) {
        Result<List<EmpCalendarInfoDto>> result = workCalendarFeignClient.listEmpRelCalendar(queryDto);
        if (null == result || !result.isSuccess()) {
            return Lists.newArrayList();
        }
        return result.getData();
    }

    public List<Map> getEmpCalendarShiftList(ListEmpCalendarShiftQueryDto queryDto) {
        Result<List<Map>> result = workCalendarFeignClient.getEmpCalendarShiftList(queryDto);
        if (null == result || !result.isSuccess()) {
            return Lists.newArrayList();
        }
        return result.getData();
    }

    public List<Map> getEmpCalendarChangeShiftList(ListEmpCalendarShiftQueryDto queryDto) {
        Result<List<Map>> result = workCalendarFeignClient.getEmpCalendarChangeShiftList(queryDto);
        if (null == result || !result.isSuccess()) {
            return Lists.newArrayList();
        }
        return result.getData();
    }
}
