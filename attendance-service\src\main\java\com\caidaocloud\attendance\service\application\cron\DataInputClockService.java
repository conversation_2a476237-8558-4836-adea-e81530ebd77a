package com.caidaocloud.attendance.service.application.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.caidao1.integrate.service.IntegratedService;
import com.caidaocloud.imports.service.application.interceptor.IntegrateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DataInputClockService {

    @Autowired
    private IntegratedService integratedService;

    @XxlJob("startDataInputJobHandler")
    public ReturnT<String> startDataInput() {
        String params = XxlJobHelper.getJobParam();
        JSONObject paramsObj = JSON.parseObject(params);
        Integer sysDataInputId = paramsObj.getInteger("sysDataInputId");
        XxlJobHelper.log("xxl-job startDataInput sysDataInputId: " + sysDataInputId);
        try {
            integratedService.syncDataInput(
                    sysDataInputId,
                    paramsObj.getLong("corpId"),
                    paramsObj.getString("belongId"),
                    true, null);
        } catch (Exception e) {
            XxlJobHelper.log("xxlJob startDataInput execute err:{}", e.getMessage(), e);
        }
        XxlJobHelper.log("xxl-job endDataInput :" + sysDataInputId);
        return ReturnT.SUCCESS;
    }

}