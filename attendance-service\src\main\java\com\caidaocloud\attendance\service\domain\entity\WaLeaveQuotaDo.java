package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.ILeaveQuotaRepository;
import com.caidaocloud.attendance.service.interfaces.dto.QuotaEmpGroupDto;
import com.caidaocloud.dto.UserInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/17
 */
@Slf4j
@Service
@Data
public class WaLeaveQuotaDo {
    private Integer quotaId;
    private Integer leaveType;
    private String belongOrgid;
    private Integer quotaVal;
    private Long crtuser;
    private Long crttime;
    private Integer quotaSettingId;
    private Integer empGroupId;
    private Object empGroupJsonb;


    @Autowired
    private ILeaveQuotaRepository leaveQuotaRepository;

    public List<WaLeaveQuotaDo> getLeaveQuotaList(String belongOrgid, Integer quotaSettingId) {
        return leaveQuotaRepository.getLeaveQuotaList(belongOrgid, quotaSettingId);
    }

    public int save(UserInfo userInfo, List<QuotaEmpGroupDto> empGroupList, Integer leaveType, Integer quotaSettingId) {
        return leaveQuotaRepository.save(userInfo, empGroupList, leaveType, quotaSettingId);
    }

    public int delete(String belongOrgid, Integer quotaSettingId) {
        return leaveQuotaRepository.delete(belongOrgid, quotaSettingId);
    }
}
