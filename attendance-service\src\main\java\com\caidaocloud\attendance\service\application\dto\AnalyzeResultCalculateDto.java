package com.caidaocloud.attendance.service.application.dto;

import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidao1.wa.mybatis.model.WaSob;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/6
 */
@Data
public class AnalyzeResultCalculateDto {
    private String belongid;
    private List<Long> empids;
    private Long startDate;
    private Long endDate;
    private Integer tmType;
    private WaParseGroup parseGroup;
    private WaSob waSob;

    private Long userId;
    private boolean job;
    private boolean includeInProgress;
}
