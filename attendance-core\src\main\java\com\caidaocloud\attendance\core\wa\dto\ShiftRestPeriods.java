package com.caidaocloud.attendance.core.wa.dto;


import com.caidaocloud.attendance.core.wa.dto.shift.OvertimeRestPeriodsDto;
import lombok.Data;

/**
 * 休息时间段
 */
@Data
public class ShiftRestPeriods extends OvertimeRestPeriodsDto {
    /************班次内休息时间************/
    /**
     * 休息开始时间
     */
    private Integer noonRestStart;
    /**
     * 休息结束时间
     */
    private Integer noonRestEnd;
    /**
     * 休息开始时间归属标记: 1 当日、2 次日
     */
    private Integer noonRestStartBelong;
    /**
     * 休息结束时间归属标记: 1 当日、2 次日
     */
    private Integer noonRestEndBelong;
}