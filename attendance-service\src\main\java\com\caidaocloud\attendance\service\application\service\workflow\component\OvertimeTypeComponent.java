package com.caidaocloud.attendance.service.application.service.workflow.component;

import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.workflow.annotation.WfComponentValueEnumDef;
import com.caidaocloud.workflow.dto.WfComponentValueDto;
import com.google.common.collect.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 注册加班类型枚举清单
 */
@Component
public class OvertimeTypeComponent extends WfComponentValueEnumDef {
    @NotNull
    @Override
    public List<WfComponentValueDto> enumList() {
        return Lists.newArrayList(
                new WfComponentValueDto(this.getName(DateTypeEnum.DATE_TYP_1.getName()), DateTypeEnum.DATE_TYP_1.getIndex().toString()),
                new WfComponentValueDto(this.getName(DateTypeEnum.DATE_TYP_2.getName()), DateTypeEnum.DATE_TYP_2.getIndex().toString()),
                new WfComponentValueDto(this.getName(DateTypeEnum.DATE_TYP_3.getName()), DateTypeEnum.DATE_TYP_3.getIndex().toString()),
                new WfComponentValueDto(this.getName(DateTypeEnum.DATE_TYP_4.getName()), DateTypeEnum.DATE_TYP_4.getIndex().toString()),
                new WfComponentValueDto(this.getName(DateTypeEnum.DATE_TYP_5.getName()), DateTypeEnum.DATE_TYP_5.getIndex().toString()));
    }

    private String getName(String name) {
        return String.format("%s%s", name, "加班");
    }
}
