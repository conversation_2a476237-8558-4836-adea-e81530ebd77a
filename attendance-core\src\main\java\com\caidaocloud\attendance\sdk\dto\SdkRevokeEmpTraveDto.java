package com.caidaocloud.attendance.sdk.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class SdkRevokeEmpTraveDto {
    private Long travelId;
    private String recokeReason;
    private String businessKey;

    public Long getTravelId() {
        if (null != travelId) {
            return travelId;
        }
        if (StringUtils.isEmpty(businessKey)) {
            return null;
        }
        if (businessKey.contains("_")) {
            String[] businessKeys = businessKey.split("_");
            return Long.valueOf(businessKeys[0]);
        }
        return Long.valueOf(businessKey);
    }
}
