package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.system.mybatis.model.SysCorpOrgExample;
import com.caidao1.wa.enums.LeaveCancelStatusEnum;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.application.dto.QuotaGenDto;
import com.caidaocloud.attendance.service.application.dto.msg.ClockMsgDto;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.service.application.enums.GenQuotaMode;
import com.caidaocloud.attendance.service.application.enums.LeaveJobTypeEnum;
import com.caidaocloud.attendance.service.application.enums.ParseGroupClockTypeEnum;
import com.caidaocloud.attendance.service.application.event.publish.DelayMsgPublish;
import com.caidaocloud.attendance.service.application.event.publish.MessageParams;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.attendance.service.interfaces.dto.AutoExeTaskClockInMsgDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/8/4
 */
@Slf4j
@Service
public class ClockTaskWorkService {
    @Autowired
    private WaGroupDo waGroupDo;
    @Resource
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private IQuotaService quotaService;
    @Autowired
    private IShiftService shiftService;
    @Autowired
    private INotifyConfigService notifyConfigService;
    @Resource
    private DelayMsgPublish delayMsgPublish;
    @Resource
    private WaRegisterRecordDo waRegisterRecordDo;
    @Resource
    private WaEmpLeaveDo waEmpLeaveDo;
    @Resource
    private ISobService sobService;
    @Autowired
    private WaEmpLeaveCancelService waEmpLeaveCancelService;
    @Autowired
    private ITravelCompensatoryService travelCompensatoryService;

    /**
     * 计算配额（配额类型：按年发放）
     *
     * @param belongOrgId
     * @throws Exception
     */
    public void autoGenEmpQuotaForIssuedAnnually(String belongOrgId) throws Exception {
        //查询所有公司数据
        List<SysCorpOrg> corpOrgList = getTenants(belongOrgId);
        if (CollectionUtils.isEmpty(corpOrgList)) {
            log.info("autoGenEmpQuotaForIssuedAnnually get org empty");
            return;
        }
        for (SysCorpOrg org : corpOrgList) {
            log.info("autoGenEmpQuotaForIssuedAnnually start， corpName：{} corpId：{} exeTime：{}", org.getShortname(), org.getOrgid(), System.currentTimeMillis());
            // 自动计算新入职员工和未生成配额的员工的假期配额（配额类型：按年发放）
            //查询公司下所有的考勤方案
            List<WaGroupDo> waGroupDos = waGroupDo.getWaGroupList(String.valueOf(org.getOrgid()));
            if (CollectionUtils.isEmpty(waGroupDos)) {
                log.info("autoGenEmpQuotaForIssuedAnnually belongOrgId = {} getWaGroupList is empty", org.getOrgid());
                continue;
            }
            //查询当前年份
            Calendar cal = Calendar.getInstance();
            int curYear = cal.get(Calendar.YEAR);
            //遍历考勤方案，生成员工假期配额
            for (WaGroupDo groupDo : waGroupDos) {
                QuotaGenDto genDto = new QuotaGenDto();
                genDto.setAll(false);
                genDto.setBelongId(groupDo.getBelongOrgid());
                genDto.setUserId(0L);
                genDto.setWaGroupId(groupDo.getWaGroupId());
                genDto.setYear((short) curYear);
                genDto.setType(LeaveJobTypeEnum.ANNUAL.getIndex());
                genDto.setGenQuotaMode(GenQuotaMode.NOT_GENERATED.getIndex());
                genDto.setCorpId(org.getCorpid());
                log.info("belongOrgId = {} ,waGroupName：{},params：{}", belongOrgId, groupDo.getWaGroupName(), JSONUtils.ObjectToJson(genDto));
                try {
                    quotaService.genEmpQuotaForIssuedAnnually(genDto);
                } catch (Exception e) {
                    log.info("waGroupName:{} genEmpQuotaForIssuedAnnually error msg：{}", groupDo.getWaGroupName(), e.getMessage(), e);
                }
            }
            try {
                // 更新跨额度规则的员工配额数据
                quotaService.autoCalEmpCrossQuota(String.valueOf(org.getOrgid()));
            } catch (Exception e) {
                log.error("autoCalEmpCrossQuota exe error,msg={}", e.getMessage(), e);
            }
            log.info("autoGenEmpQuotaForIssuedAnnually over， corpName：{} corpId：{} exeTime：{}", org.getShortname(), org.getOrgid(), System.currentTimeMillis());
        }
    }

    /**
     * 计算配额（配额类型：育儿假）
     *
     * @param belongOrgId
     */
    public void autoGenEmpQuotaForParRent(String belongOrgId) {
        log.info("autoGenEmpQuotaForParRent belongOrgId {} time {}", belongOrgId, System.currentTimeMillis());
        List<SysCorpOrg> corpOrgList = getTenants(belongOrgId);
        if (CollectionUtils.isEmpty(corpOrgList)) {
            log.info("autoGenEmpQuotaForParRent get org empty");
            return;
        }
        for (SysCorpOrg org : corpOrgList) {
            log.info("autoGenEmpQuotaForParRent start， corpName：{} corpId：{} exeTime：{}", org.getShortname(), org.getOrgid(), System.currentTimeMillis());
            List<WaGroupDo> waGroupDos = waGroupDo.getWaGroupList(belongOrgId);
            if (CollectionUtils.isEmpty(waGroupDos)) {
                log.info("autoGenEmpQuotaForParRent belongOrgId = {} getWaGroupList is empty", belongOrgId);
                continue;
            }
            //查询当前年份和前一年
            Calendar cal = Calendar.getInstance();
            int curYear = cal.get(Calendar.YEAR);
            int lastYear = curYear - 1;
            int[] years = {lastYear, curYear};
            //遍历考勤方案，生成员工假期配额
            for (WaGroupDo groupDo : waGroupDos) {
                QuotaGenDto genDto = new QuotaGenDto();
                for (int i = 0; i < years.length; i++) {
                    genDto.setAll(false);
                    genDto.setBelongId(groupDo.getBelongOrgid());
                    genDto.setUserId(0L);
                    genDto.setWaGroupId(groupDo.getWaGroupId());
                    genDto.setYear((short) years[i]);
                    genDto.setType(LeaveJobTypeEnum.PARENTING.getIndex());
                    genDto.setGenQuotaMode(GenQuotaMode.NOT_GENERATED.getIndex());
                    genDto.setCorpId(org.getCorpid());
                    log.info("belongOrgId = {} ,waGroupName：{},params：{}", belongOrgId, groupDo.getWaGroupName(), JSONUtils.ObjectToJson(genDto));
                    try {
                        quotaService.genEmpQuotaForIssuedAnnually(genDto);
                    } catch (Exception e) {
                        log.info("waGroupName:{} autoGenEmpQuotaForParRent error msg：{}", groupDo.getWaGroupName(), e.getMessage(), e);
                    }
                }
            }
        }
    }

    /**
     * 计算当前可用配额（配额类型：按年发放）
     */
    public void autoGenEmpNowQuotaForIssuedAnnually() {
        //查询所有公司数据
        List<SysCorpOrg> corpOrgList = getTenants(null);
        if (CollectionUtils.isEmpty(corpOrgList)) {
            log.info("autoGenEmpNowQuotaForIssuedAnnually get org empty");
            return;
        }
        for (SysCorpOrg org : corpOrgList) {
            log.info("autoGenEmpNowQuotaForIssuedAnnually start， corpName：{} corpId：{} exeTime：{}", org.getShortname(), org.getOrgid(), System.currentTimeMillis());
            try {
                quotaService.autoGenEmpNowQuotaForIssuedAnnuallyByOrg(String.valueOf(org.getOrgid()), null, null);
                log.info("autoGenEmpNowQuotaForIssuedAnnually over， corpName：{} corpId：{} exeTime：{}", org.getShortname(), org.getOrgid(), System.currentTimeMillis());
            } catch (Exception e) {
                log.error("autoGenEmpNowQuotaForIssuedAnnually exe error,msg={}", e.getMessage(), e);
            }
        }
    }

    public void autoGenEmpNowQuotaForIssuedAnnuallyByDate(String belongOrgId, Long date, Long empid) {
        quotaService.autoGenEmpNowQuotaForIssuedAnnuallyByOrg(belongOrgId, date, empid);
    }

    /**
     * 计算固定额度
     *
     * @param belongOrgId
     */
    public void autoGenEmpQuotaForFixedQuota(String belongOrgId) {
        log.info("autoGenEmpQuotaForFixedQuota belongOrgId {} time {}", belongOrgId, System.currentTimeMillis());
        List<SysCorpOrg> tenants = getTenants(belongOrgId);
        for (SysCorpOrg sysCorpOrg : tenants) {
            Long tenantId = sysCorpOrg.getOrgid();
            log.info("autoGenEmpQuotaForFixedQuota corpName {}, id {}", sysCorpOrg.getShortname(), tenantId);
            try {
                quotaService.genEmpQuotaForFixedQuota(String.valueOf(tenantId), 0L, "");
                log.info("autoGenEmpQuotaForFixedQuota tenantId {} success time {}", tenantId, System.currentTimeMillis());
            } catch (Exception e) {
                log.error("autoGenEmpQuotaForFixedQuota tenantId {} error msg={}", tenantId, e.getMessage(), e);
            }
        }
    }

    /**
     * 计算调休配额
     *
     * @param belongOrgId
     */
    public void autoGenEmpQuotaForCompensatoryLeave(String belongOrgId) throws Exception {
        Long today = DateUtil.getOnlyDate();
        log.info("Start to execute autoGenEmpQuotaForCompensatoryLeave: belongOrgId:{}, time:{}", belongOrgId, DateUtil.getTimeStrByTimesamp(today));
        List<WaSobDo> waSobs = sobService.getWaSobIdByDateRangeAndPeriodMonth(belongOrgId, today, null, null);
        log.info("Select waSobs:{}", JSONUtils.ObjectToJson(waSobs));
        if (CollectionUtils.isNotEmpty(waSobs)) {
            Map<String, List<WaSobDo>> sobMap = waSobs.stream().collect(Collectors.groupingBy(WaSobDo::getBelongOrgId));
            for (Map.Entry<String, List<WaSobDo>> m : sobMap.entrySet()) {
                if (CollectionUtils.isEmpty(m.getValue())) {
                    continue;
                }
                List<WaSobDo> sobs = Lists.newArrayList();
                belongOrgId = m.getKey();
                for (WaSobDo sob : m.getValue()) {
                    List<Integer> sysPeriodMonths = Lists.newArrayList();
                    Integer todaySysPeriodMonth = sob.getSysPeriodMonth();
                    sysPeriodMonths.add(todaySysPeriodMonth);
                    Long lastMonth = DateUtil.addMonth(DateUtil.getOnlyDate(new Date(DateUtil.convertStringToDateTime(String.valueOf(todaySysPeriodMonth), "yyyyMM", Boolean.FALSE))), -1);
                    Integer lastSysPeriodMonth = this.getPeriodMonth(lastMonth);
                    sysPeriodMonths.add(lastSysPeriodMonth);
                    sobs.addAll(sobService.getWaSobIdByDateRangeAndPeriodMonth(belongOrgId, null, sysPeriodMonths, sob.getWaGroupId()));
                }
                sobs = sobs.stream().sorted(Comparator.comparing(WaSobDo::getSysPeriodMonth)).collect(Collectors.toList());
                log.info("select sobs:{}", JSONUtils.ObjectToJson(sobs));
                for (WaSobDo waSob : sobs) {
                    log.info("waSob:{}", JSONUtils.ObjectToJson(waSob));
                    Integer waSobId = waSob.getWaSobId();
                    try {
                        quotaService.genEmpQuotaForCompensatoryLeave(belongOrgId, 0L, waSobId, null, "", waSob.getCorpid());
                        log.info("wa_sob_name={}, sob_id={}, success，time={}", waSob.getWaSobName(), waSobId, System.currentTimeMillis());
                    } catch (Exception e) {
                        log.info("wa_sob_name={}, sob_id={}, success，time={}", waSob.getWaSobName(), waSobId, System.currentTimeMillis());
                        log.error("error：{}", e.getMessage(), e);
                    }
                    log.info("automatic accounting sobId={},belongOrgId={}", waSob.getWaSobId(), belongOrgId);
                }
            }
        }
        log.info("Execute autoGenEmpQuotaForCompensatoryLeave end time:{}", DateUtil.getTimeStrByTimesamp(System.currentTimeMillis() / 1000));
    }

    private Integer getPeriodMonth(long time) {
        return Integer.valueOf(DateUtil.parseDateToPattern(new Date(time * 1000), "yyyyMM"));
    }

    /**
     * 按年发放的配额自动结转
     *
     * @param belongOrgId
     */
    public void autoCarryForwardQuota(String belongOrgId) {
        //查询所有公司数据
        List<SysCorpOrg> corpOrgList = getTenants(belongOrgId);
        if (CollectionUtils.isEmpty(corpOrgList)) {
            log.info("autoCarryForwardQuota get org empty");
            return;
        }
        for (SysCorpOrg org : corpOrgList) {
            log.info("autoCarryForwardQuota start， corpName：{} corpId：{} exeTime：{}", org.getShortname(), org.getOrgid(), System.currentTimeMillis());
            try {
                quotaService.autoCarryForwardQuota(String.valueOf(org.getOrgid()), 0L);
                log.info("autoCarryForwardQuota over， corpName：{} corpId：{} exeTime：{}", org.getShortname(), org.getOrgid(), System.currentTimeMillis());
            } catch (Exception e) {
                log.error("autoCarryForwardQuota exe error,msg={}", e.getMessage(), e);
            }
        }
    }

    /**
     * 员工销假提醒
     *
     * @param clockInMsgDto
     */
    public void synchronizeEmpLeaveCancelInfo(AutoExeTaskClockInMsgDto clockInMsgDto) {
        int pageNo = 1;
        int pageSize = 500;
        log.info("synchronizeEmpLeaveCancelInfo start~");
        synchronizeEmpLeaveCancelInfoByPage(pageNo, pageSize, clockInMsgDto);
        log.info("synchronizeEmpLeaveCancelInfo over~");
    }

    /**
     * 员工销假提醒 - 租户查询
     *
     * @param pageNo
     * @param pageSize
     * @param clockInMsgDto
     */
    private void synchronizeEmpLeaveCancelInfoByPage(int pageNo, int pageSize, AutoExeTaskClockInMsgDto clockInMsgDto) {
        List<NotifyConfigDo> notifyConfigList = new ArrayList<>();
        try {
            // 查询租户的消息设置
            notifyConfigList = notifyConfigService.getNotifyPageList(pageNo, pageSize);
            if (CollectionUtils.isNotEmpty(notifyConfigList)) {
                for (NotifyConfigDo notifyConfigDo : notifyConfigList) {
                    String tenantId = notifyConfigDo.getTenantId();
                    log.info("自动同步员工销假信息，参数:[tenantId:{}]", tenantId);
                    //销假提醒开关打开
                    if (notifyConfigDo.getLeaveCancelSwitch() == 1) {
                        if (notifyConfigDo.getLeaveCancelTime() != null && notifyConfigDo.getLeaveCancelFrequency() != null) {
                            pushEmpLeaveCancelInfo(tenantId, notifyConfigDo, clockInMsgDto);
                        }
                    }
                }
                if (notifyConfigList.size() < pageSize) {
                    return;
                }
                synchronizeEmpLeaveCancelInfoByPage(pageNo + 1, pageSize, clockInMsgDto);
            }
        } catch (Exception e) {
            log.error("自动同步员工销假提醒异常:{}", e.getMessage(), e);
            if (CollectionUtils.isNotEmpty(notifyConfigList)) {
                synchronizeEmpLeaveCancelInfoByPage(pageNo + 1, pageSize, clockInMsgDto);
            }
        }
    }

    public void autoLeaveCancel(String tenantId) {
        List<SysCorpOrg> tenants = getTenants(tenantId);
        if (CollectionUtils.isEmpty(tenants)) {
            log.info("autoLeaveCancel get tenant empty");
            return;
        }
        for (SysCorpOrg tenant : tenants) {
            log.info("autoLeaveCancel start，corpName：{} corpId：{} exeTime：{}", tenant.getShortname(), tenant.getOrgid(), System.currentTimeMillis());
            try {
                waEmpLeaveCancelService.autoLeaveCancel(String.valueOf(tenant.getOrgid()), tenant.getCorpid());
                log.info("autoLeaveCancel over， corpName：{} corpId：{} exeTime：{}", tenant.getShortname(), tenant.getOrgid(), System.currentTimeMillis());
            } catch (Exception e) {
                log.error("autoLeaveCancel exe error,msg={}", e.getMessage(), e);
            }
        }
    }

    private Integer getDifferenceDay(long start, long end) {
        if (start == end || (start <= 0 && end <= 0)) {
            return 0;
        }
        long betweenDays = (end - start) / (24 * 3600);
        return Integer.valueOf(String.valueOf(betweenDays));
    }

    /**
     * 给满足销假条件推送消息
     *
     * @param tenantId      租户
     * @param configDo      消息提醒配置
     * @param clockInMsgDto 提醒内容
     */
    private void pushEmpLeaveCancelInfo(String tenantId, NotifyConfigDo configDo, AutoExeTaskClockInMsgDto clockInMsgDto) {
        try {
            Integer leaveCancelTime = configDo.getLeaveCancelTime();
            Integer leaveCancelFrequency = configDo.getLeaveCancelFrequency();
            if (null == leaveCancelTime || null == leaveCancelFrequency) {
                return;
            }
            String leaveCancelContent = configDo.getLeaveCancelContent();
            //查询租户下审批通过，状态为未销假的单据
            List<WaEmpLeaveDo> list = waEmpLeaveDo.getEmpLeaveList(tenantId,
                    Collections.singletonList(ApprovalStatusEnum.PASSED.getIndex()),
                    Collections.singletonList(LeaveCancelStatusEnum.NO_LEAVE_CANCEL.getIndex()));
            //过滤满足条件的休假单 休假结束后n天推送,每隔m天推送一次
            for (WaEmpLeaveDo leaveDo : list) {
                Long currentDate = DateUtil.getOnlyDate();
                Long endTime = leaveDo.getEndTime();
                //Integer diff = DateUtilExt.getDifferenceDay(endTime, currentDate);
                Integer diff = getDifferenceDay(endTime, currentDate);
                if (diff < 0) {
                    continue;
                }
                if (diff < leaveCancelTime) {
                    continue;
                }
                if (leaveCancelFrequency != 0 && (diff % leaveCancelFrequency != 0)) {
                    continue;
                }
                // 指定推送员工
                if (clockInMsgDto != null && clockInMsgDto.getEmpId() != null) {
                    if (!leaveDo.getEmpid().equals(clockInMsgDto.getEmpId())) {
                        continue;
                    }
                }
                //组装消息体
                ClockMsgDto clockMsgDto = new ClockMsgDto();
                clockMsgDto.setTenantId(tenantId);
                clockMsgDto.setEmpIds(Collections.singletonList(leaveDo.getEmpid()));
                clockMsgDto.setContent(leaveCancelContent);
                clockMsgDto.setLeaveId(leaveDo.getLeaveId());
                clockMsgDto.setDelay(0); //销假提醒不需要延时
                //推送
                sendLeaveCancelMsg(clockMsgDto);
            }
        } catch (Exception e) {
            log.error("查询待发送的休假单异常:{}", e.getMessage(), e);
        }
    }

    /**
     * 同步员工打卡班次信息
     */
    public void synchronizeEmpClockShiftInfo(AutoExeTaskClockInMsgDto clockInMsgDto) {
        int pageNo = 1;
        int pageSize = 500;
        log.info("synchronizeEmpClockShiftInfo start");
        synchronizeEmpClockShiftInfoByPage(pageNo, pageSize, clockInMsgDto);
        log.info("synchronizeEmpClockShiftInfo over");
    }

    /**
     * 同步员工打卡信息,租户查询
     *
     * @param pageNo
     * @param pageSize
     */
    private void synchronizeEmpClockShiftInfoByPage(int pageNo, int pageSize, AutoExeTaskClockInMsgDto clockInMsgDto) {
        List<NotifyConfigDo> notifyConfigList = new ArrayList<>();
        try {
            // 查询租户的打卡设置
            notifyConfigList = notifyConfigService.getNotifyPageList(pageNo, pageSize);
            if (CollectionUtils.isNotEmpty(notifyConfigList)) {
                for (NotifyConfigDo notifyConfigDo : notifyConfigList) {
                    String tenantId = notifyConfigDo.getTenantId();
                    log.info("自动同步员工打卡班次信息，参数:[tenantId:{}]", tenantId);
                    if (notifyConfigDo.getClockNotifySwitch() == 1) {
                        // 查询租户下的班次信息
                        Long currentDate = DateUtil.getOnlyDate();
                        Long startDate = DateUtil.addDate(currentDate * 1000, 1);
                        Long endDate = startDate + 86399;
                        Map<Integer, List<EmpShiftInfo>> shiftInfoMap = new HashMap<>();
                        if (null != notifyConfigDo.getWorkTime() || null != notifyConfigDo.getOffWorkTime()) {
                            shiftInfoMap = shiftService.getEmpShiftInfoListMaps(startDate, endDate, tenantId);
                            log.info("Employee shift info:{}", JSON.toJSONString(shiftInfoMap));
                        }
                        if (shiftInfoMap.isEmpty()) {
                            continue;
                        }
                        if (null != notifyConfigDo.getWorkTime()) {
                            log.info("自动同步员工打卡班次信息，上班打卡参数:[tenantId:{}，workTime:{}]", tenantId, notifyConfigDo.getWorkTime());
                            synchronizeClockInfo(false, notifyConfigDo.getWorkTime(), notifyConfigDo.getWorkNotifyContent(), notifyConfigDo.getTenantId(), shiftInfoMap, clockInMsgDto);
                        }
                        if (null != notifyConfigDo.getOffWorkTime()) {
                            log.info("自动同步员工打卡班次信息，下班打卡参数:[tenantId:{}，workTime:{}]", tenantId, notifyConfigDo.getOffWorkTime());
                            synchronizeClockInfo(true, notifyConfigDo.getOffWorkTime(), notifyConfigDo.getOffWorkNotifyContent(), notifyConfigDo.getTenantId(), shiftInfoMap, clockInMsgDto);
                        }
                    }
                }
                if (notifyConfigList.size() < pageSize) {
                    return;
                }
                synchronizeEmpClockShiftInfoByPage(pageNo + 1, pageSize, clockInMsgDto);
            }
        } catch (Exception e) {
            log.error("自动同步员工打卡班次信息异常{}", e.getMessage(), e);
            if (CollectionUtils.isNotEmpty(notifyConfigList)) {
                synchronizeEmpClockShiftInfoByPage(pageNo + 1, pageSize, clockInMsgDto);
            }
        }
    }

    /**
     * 同步员工打卡信息，班次处理
     *
     * @param isOffWork
     * @param workTime
     * @param tenantId
     * @param shiftInfoMap
     */
    private void synchronizeClockInfo(boolean isOffWork, Integer workTime, String content, String tenantId, Map<Integer, List<EmpShiftInfo>> shiftInfoMap, AutoExeTaskClockInMsgDto clockInMsgDto) {
        for (Map.Entry<Integer, List<EmpShiftInfo>> entry : shiftInfoMap.entrySet()) {
            Integer shiftDefId = entry.getKey();
            List<EmpShiftInfo> empShiftInfos = entry.getValue();
            log.info("Employee shift infos [shiftDefId:{}, empShiftInfos:{}]", shiftDefId, JSON.toJSONString(empShiftInfos));
            Optional<EmpShiftInfo> optional = empShiftInfos.stream().findFirst();
            if (optional.isPresent()) {
                EmpShiftInfo empShiftInfo = optional.get();
                // 只在工作日进行消息提醒
                if (DateTypeEnum.DATE_TYP_1.getIndex().equals(empShiftInfo.getDateType())) {
                    // 计算延时时长
                    Long currentDate = DateUtil.getOnlyDate();
                    long currentTime = System.currentTimeMillis() / 1000;
                    // 上班时间
                    Integer startTime = empShiftInfo.getStartTime();
                    // 下班时间
                    Integer endTime = empShiftInfo.getEndTime();
                    boolean kyShift = CdWaShiftUtil.checkCrossNightV2(empShiftInfo, empShiftInfo.getDateType());
                    // 跨夜班需要加两天，上班时间大于下班时间，则为跨夜班
                    Long startDate = isOffWork && kyShift ? DateUtil.addDate(currentDate * 1000, 2) : DateUtil.addDate(currentDate * 1000, 1);
                    Integer time = isOffWork ? endTime : startTime;
                    Long endDate = startDate + (time - workTime) * 60;
                    Long delayTime = (endDate - currentTime) * 1000;
                    ClockMsgDto clockMsgDto = new ClockMsgDto();
                    clockMsgDto.setTenantId(tenantId);
                    if (clockInMsgDto != null && clockInMsgDto.getDelayTime() != null && clockInMsgDto.getDelayTime() > 0) {
                        // 指定延迟时长
                        clockMsgDto.setDelay(clockInMsgDto.getDelayTime().intValue());
                    } else {
                        clockMsgDto.setDelay(delayTime.intValue());
                    }
                    clockMsgDto.setShiftDefId(empShiftInfo.getShiftDefId());
                    clockMsgDto.setStartTime(empShiftInfo.getStartTime());
                    clockMsgDto.setEndTime(empShiftInfo.getEndTime());
                    String notifyContent = StringUtil.isNotBlank(content) ? content : String.format("%s %s打卡", getTimeStrByTimestamp(currentDate + time * 60), isOffWork ? "下班" : "上班");
                    clockMsgDto.setContent(notifyContent);
                    List<Long> empIds = empShiftInfos.stream().map(EmpShiftInfo::getEmpid).distinct().collect(Collectors.toList());
                    filterNoReminderEmpIdList(tenantId, empIds, DateUtil.addDate(currentDate * 1000, 1));
                    if (clockInMsgDto != null && clockInMsgDto.getEmpId() != null) {
                        // 指定推送员工
                        if (empIds.contains(clockInMsgDto.getEmpId())) {
                            empIds = new ArrayList<>(Collections.singletonList(clockInMsgDto.getEmpId()));
                        } else {
                            empIds = new ArrayList<>();
                        }
                    }
                    // 分批发送
                    if (CollectionUtils.isNotEmpty(empIds)) {
                        batchSynchronize(empIds, 0, 5000, clockMsgDto);
                    }
                }
            }
        }
    }

    /**
     * 过滤不需要提醒的员工
     *
     * @param tenantId
     * @param empIds
     * @param date
     */
    public void filterNoReminderEmpIdList(String tenantId, List<Long> empIds, Long date) {
        List<List<Long>> lists = ListTool.split(empIds, 1000);
        for (List<Long> list : lists) {
            // 查询员工指定日期对应的考勤分析出勤规则
            List<EmpParseGroup> empParseGroupList = waRegisterRecordDo.selectEmpParseGroupListByDate(tenantId, list, date);
            if (CollectionUtils.isNotEmpty(empParseGroupList)) {
                // 不打卡员工
                List<Long> filterEmpIdList = empParseGroupList.stream().filter(o -> o.getClockType() != null
                        && ParseGroupClockTypeEnum.SIGN_ZERO.getIndex().equals(o.getClockType())).map(EmpParseGroup::getEmpId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterEmpIdList)) {
                    empIds.removeAll(filterEmpIdList);
                }
            }
        }
    }

    /**
     * 同步员工打卡信息,时间处理
     *
     * @param ts
     * @return
     */
    private static String getTimeStrByTimestamp(Long ts) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        return sdf.format(new Date(ts * 1000L));
    }

    /**
     * 同步员工打卡信息,分批处理
     *
     * @param empIds
     * @param skip
     * @param limit
     * @param clockMsgDto
     */
    private void batchSynchronize(List<Long> empIds, long skip, long limit, ClockMsgDto clockMsgDto) {
        List<Long> items = empIds.stream().skip(skip).limit(limit).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            log.info("Rend clock remind message failed，employee is empty");
            return;
        }
        clockMsgDto.setEmpIds(items);
        // 批量插入数据的方法
        synchronize(clockMsgDto);
        // 递归每次limit条数据
        if (items.size() < limit) {
            return;
        }
        batchSynchronize(empIds, skip + limit, limit, clockMsgDto);
    }

    /**
     * 同步员工打卡信息,发送消息
     *
     * @param clockMsgDto
     */
    private void synchronize(ClockMsgDto clockMsgDto) {
        try {
            sendMsg(clockMsgDto);
        } catch (Exception e) {
            log.error("Rend clock remind message=[{}] err,{}", FastjsonUtil.toJson(clockMsgDto), e.getMessage(), e);
        }
    }

    /**
     * 同步员工打卡信息,发送消息
     *
     * @param clockMsgDto
     */
    private void sendMsg(ClockMsgDto clockMsgDto) {
        log.info("Start to send clock remind message");
        List<Long> receiver = new ArrayList<>(clockMsgDto.getEmpIds());
        MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                .type("msg")
                .subType("clock")
                .title("打卡提醒")
                .funcType("-1")
                .handlers(receiver)
                .content(clockMsgDto.getContent())
                .nType(NoticeType.ATTENDANCE_REGISTER_MSG).tenantId(clockMsgDto.getTenantId());
        MessageParams messageParams = builder.build();
        String jsonStr = JSON.toJSONString(messageParams);
        delayMsgPublish.publish(jsonStr, clockMsgDto.getDelay(), clockMsgDto.getTenantId());
        log.info("Success to send clock remind message, messageParams[{}]", FastjsonUtil.toJson(messageParams));
    }


    /**
     * 发送销假提醒消息
     *
     * @param clockMsgDto
     */
    private void sendLeaveCancelMsg(ClockMsgDto clockMsgDto) {
        log.info("Start to send leaveCancel message");
        List<Long> receiver = new ArrayList<>();
        receiver.add(clockMsgDto.getEmpIds().get(0));
        MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                .type("msg")
                .subType("leaveCancel")
                .title("销假提醒")
                .funcType("-1")
                .handlers(receiver).processId(String.valueOf(clockMsgDto.getLeaveId()))
                .content(clockMsgDto.getContent())
                .nType(NoticeType.ATTENDANCE_LEAVE_CANCEL_MSG).tenantId(clockMsgDto.getTenantId());
        MessageParams messageParams = builder.build();
        String jsonStr = JSON.toJSONString(messageParams);
        delayMsgPublish.publish(jsonStr, clockMsgDto.getDelay(), clockMsgDto.getTenantId());
        log.info("Success to send leaveCancel message, messageParams[{}]", FastjsonUtil.toJson(messageParams));
    }

    /**
     * 调休付现
     *
     * @param tenantId 租户
     */
    public void autoCompensatoryToCase(String tenantId) {
        List<SysCorpOrg> tenants = getTenants(tenantId);
        if (CollectionUtils.isEmpty(tenants)) {
            log.info("autoCompensatoryToCase get tenant empty");
            return;
        }
        /*for (SysCorpOrg tenant : tenants) {
            log.info("autoCompensatoryToCase start，corpName：{} corpId：{} exeTime：{}", tenant.getShortname(), tenant.getOrgid(), System.currentTimeMillis());
            try {
                empCompensatoryCaseService.autoCompensatoryToCase(String.valueOf(tenant.getOrgid()));
                log.info("autoCompensatoryToCase over， corpName：{} corpId：{} exeTime：{}", tenant.getShortname(), tenant.getOrgid(), System.currentTimeMillis());
            } catch (Exception e) {
                log.error("autoCompensatoryToCase exe error,msg={}", e.getMessage(), e);
            }
        }*/
    }

    /**
     * 出差转调休
     *
     * @param tenantId 租户
     */
    public void autoTravelToCompensatory(String tenantId) {
        List<SysCorpOrg> tenants = getTenants(tenantId);
        if (CollectionUtils.isEmpty(tenants)) {
            log.info("autoTravelToCompensatory get tenant empty");
            return;
        }
        for (SysCorpOrg tenant : tenants) {
            log.info("autoTravelToCompensatory start，corpName：{} corpId：{} exeTime：{}", tenant.getShortname(), tenant.getOrgid(), System.currentTimeMillis());
            try {
                travelCompensatoryService.autoTravelToCompensatory(String.valueOf(tenant.getOrgid()));
                log.info("autoTravelToCompensatory over， corpName：{} corpId：{} exeTime：{}", tenant.getShortname(), tenant.getOrgid(), System.currentTimeMillis());
            } catch (Exception e) {
                log.error("autoTravelToCompensatory exe error,msg={}", e.getMessage(), e);
            }
        }
    }

    public List<SysCorpOrg> getTenants(String tenantId) {
        SysCorpOrgExample example = new SysCorpOrgExample();
        SysCorpOrgExample.Criteria criteria = example.createCriteria();
        criteria.andOrgtype2EqualTo(1).andStatusEqualTo(1);
        if (tenantId != null) {
            criteria.andOrgidEqualTo(Long.valueOf(tenantId));
        }
        return sysCorpOrgMapper.selectByExample(example);
    }

    public void attendanceAbnormalJobRefresh(String belongOrgId) {
        List<SysCorpOrg> corpOrgList = getTenants(belongOrgId);
        if (CollectionUtils.isEmpty(corpOrgList)) {
            log.info("attendanceAbnormalJobRefresh get org empty");
            return;
        }
        for (SysCorpOrg org : corpOrgList) {
            String tenantId = String.valueOf(org.getOrgid());
            try {
                notifyConfigService.attendanceAbnormalJobRefresh(tenantId);
            } catch (Exception e) {
                log.error("attendanceAbnormalJobRefresh exe error,msg={}", e.getMessage(), e);
            }
            log.info("attendanceAbnormalJobRefresh over， corpName：{} corpId：{} exeTime：{}", org.getShortname(), tenantId, System.currentTimeMillis());
        }
    }

    public void attendanceDetailJobRefresh(String belongOrgId) {
        List<SysCorpOrg> corpOrgList = getTenants(belongOrgId);
        if (CollectionUtils.isEmpty(corpOrgList)) {
            log.info("attendanceDetailJobRefresh get org empty");
            return;
        }
        for (SysCorpOrg org : corpOrgList) {
            String tenantId = String.valueOf(org.getOrgid());
            try {
                notifyConfigService.attendanceDetailJobRefresh(tenantId);
            } catch (Exception e) {
                log.error("attendanceDetailJobRefresh exe error,msg={}", e.getMessage(), e);
            }
            log.info("attendanceDetailJobRefresh over， corpName：{} corpId：{} exeTime：{}", org.getShortname(), tenantId, System.currentTimeMillis());
        }
    }
}