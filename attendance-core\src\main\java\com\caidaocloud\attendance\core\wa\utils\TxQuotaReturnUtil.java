package com.caidaocloud.attendance.core.wa.utils;

import com.caidaocloud.attendance.core.wa.dto.MonthAmountBean;
import com.caidaocloud.attendance.core.wa.dto.PeriodAmountBean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class TxQuotaReturnUtil {

    public static void cancelAmount(List<MonthAmountBean> monthAmountList, Integer period, Float time) {
        BigDecimal cancelCnt = new BigDecimal(0);
        BigDecimal amount = new BigDecimal(time);

        List<MonthAmountBean> monthAmountList2 = getMonths(monthAmountList, period);
        int beginken = -1;
        for (int j = 0; j < monthAmountList2.size(); j++) {
            MonthAmountBean row2 = monthAmountList2.get(j);
            for (PeriodAmountBean prow : row2.getPeriodAmounts()) {
                if (prow.getPeriod().equals(period)) {
                    if (cancelCnt.floatValue() < amount.floatValue()) {
                        BigDecimal ken = new BigDecimal(Math.min(amount.subtract(cancelCnt).floatValue(),prow.getAmount()));
                        prow.setKen(ken.floatValue());
                        cancelCnt = cancelCnt.add(ken);
                        prow.setAmount(new BigDecimal(prow.getAmount()).subtract(ken).floatValue());
                        if (beginken < 0) beginken = monthAmountList.indexOf(row2);
                    }
                }
            }
        }

        for (int kenidx = beginken; kenidx < monthAmountList.size(); kenidx++) {
            MonthAmountBean row = monthAmountList.get(kenidx);
            for (int j = 0; j < row.getPeriodAmounts().size(); j++) {
                if (row.getPeriodAmounts().get(j).getKen() > 0) {
                    tianken(monthAmountList, row, row.getPeriodAmounts().get(j), row.getMonth());
                }
            }
        }
    }

    public static Float getRemain(MonthAmountBean monthAmount) {
        Float remain = monthAmount.getAmount();
        for (PeriodAmountBean row : monthAmount.getPeriodAmounts()) {
            remain = new BigDecimal(remain).subtract(new BigDecimal(row.getAmount())).floatValue();
        }
        return remain;
    }

    public static void tianken(List<MonthAmountBean> monthAmountList, MonthAmountBean ken, PeriodAmountBean pa, int begin) {
        boolean end = false;
        BigDecimal paKen = new BigDecimal(pa.getKen());
        for (int j = 0; j < monthAmountList.size(); j++) {
            MonthAmountBean row = monthAmountList.get(j);
            if (row.getMonth() > begin) {
                for (PeriodAmountBean prow : row.getPeriodAmounts()) {
                    BigDecimal pAmount = new BigDecimal(prow.getAmount());
                    BigDecimal pKen = new BigDecimal(prow.getKen());

                    if (prow.getAmount() > 0 && prow.getPeriod() >= ken.getMonth() && prow.getPeriod() <= ken.getMonth() + 2) {
                        for (PeriodAmountBean prow2 : ken.getPeriodAmounts()) {
                            if (prow2.getPeriod().equals(prow.getPeriod())) {
                                BigDecimal cnt = new BigDecimal(Math.min(pAmount.floatValue(), Math.min(pa.getKen(), getRemain(ken))));
                                BigDecimal pAmount2 = new BigDecimal(prow2.getAmount());

                                prow.setAmount(pAmount.subtract(cnt).floatValue());
                                prow.setKen(pKen.add(cnt).floatValue());

                                prow2.setAmount(pAmount2.add(cnt).floatValue());
                                pa.setKen(paKen.subtract(cnt).floatValue());
                                if (pa.getKen() == 0) {
                                    end = true;
                                }
                            }
                        }
                    }
                }
            }
            if (end) {
                break;
            }
        }
    }

    public static List<MonthAmountBean> getMonths(List<MonthAmountBean> monthAmountList, Integer period) {
        List<MonthAmountBean> monthAmountList2 = new ArrayList<>();
        for (int cnt = 0; cnt < monthAmountList.size(); cnt++) {
            MonthAmountBean row = monthAmountList.get(cnt);
            for (PeriodAmountBean prow : row.getPeriodAmounts()) {
                if (prow.getPeriod().equals(period)) {
                    monthAmountList2.add(row);
                }
            }
        }
        return monthAmountList2;
    }


    private void test() {
        List<MonthAmountBean> monthAmountList = new ArrayList() {{
            add(new MonthAmountBean(1, 18f, new ArrayList() {{
                add(new PeriodAmountBean(1, 6f));
                add(new PeriodAmountBean(2, 5f));
                add(new PeriodAmountBean(3, 7f));
            }},1));
            add(new MonthAmountBean(2, 11f, new ArrayList() {{
                add(new PeriodAmountBean(2, 2f));
                add(new PeriodAmountBean(3, 4f));
                add(new PeriodAmountBean(4, 5f));
            }},2));
            add(new MonthAmountBean(3, 8f, new ArrayList() {{
                add(new PeriodAmountBean(3, 4f));
                add(new PeriodAmountBean(4, 3f));
                add(new PeriodAmountBean(5, 1f));
            }},3));
        }};

        monthAmountList.stream().forEach(row -> {
            System.out.println(row.getMonth() + ":" + row.getAmount().intValue() + ":" + row.getPeriodAmounts());
        });

        cancelAmount(monthAmountList, 2, 5f);
        cancelAmount(monthAmountList, 2, 1f);

        System.out.println("---------------------------");

        monthAmountList.stream().forEach(row -> {
            System.out.println(row.getMonth() + ":" + row.getAmount().intValue() + ":" + row.getPeriodAmounts());
        });
    }

    public static void main(String[] args) {
        new TxQuotaReturnUtil().test();
    }

}
