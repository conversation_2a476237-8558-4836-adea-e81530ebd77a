package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaAnalyseResultAdjustDo;

import java.util.List;

/**
 * 考勤异常调整明细
 *
 * <AUTHOR>
 * @Date 2024/6/25
 */
public interface IWaAnalyseResultAdjustRepository {
    void updateById(WaAnalyseResultAdjustDo adjustDo);

    void insert(WaAnalyseResultAdjustDo adjustDo);

    WaAnalyseResultAdjustDo getById(Long adjustId);

    List<WaAnalyseResultAdjustDo> getListByBatchId(Long batchId);

    List<WaAnalyseResultAdjustDo> listByDate(Long empid, Long belongDate, List<Integer> statusList);

    List<WaAnalyseResultAdjustDo> listByDateRange(Long empid, Long startDate, Long endDate, List<Integer> statusList);

    void deleteById(Long id);

    void deleteByBatchId(Long batchId);
}
