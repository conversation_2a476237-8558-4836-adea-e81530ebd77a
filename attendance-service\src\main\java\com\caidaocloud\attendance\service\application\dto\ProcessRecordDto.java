package com.caidaocloud.attendance.service.application.dto;

import com.caidaocloud.attendance.core.workflow.dto.WfProcessRecordDto;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "流程记录")
public class ProcessRecordDto {
    private List<WfProcessRecordDto> leaveRecord = new ArrayList<>();
    private List<CancelProcessRecordDto> leaveCancelRecord = new ArrayList<>();
    private boolean autoLeaveCancel;
    private Long autoLeaveCancelTime;

    private List<WfProcessRecordDto> otRecord = new ArrayList<>();
    private List<List<WfProcessRecordDto>> otRevokeRecord = new ArrayList<>();
    private List<WfProcessRecordDto> travelRecord = new ArrayList<>();
    private List<List<WfProcessRecordDto>> travelRevokeRecord = new ArrayList<>();
}