package com.caidaocloud.attendance.core.annoation.aspect;

import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.dto.WorkplaceDto;
import com.caidaocloud.attendance.core.annoation.feign.BccServiceFeignClient;
import com.caidaocloud.attendance.core.annoation.feign.HrServiceFeignClient;
import com.caidaocloud.attendance.core.annoation.feign.PassMetadataFeign;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.vo.adapter.JobGradeDataOutVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SysParamDictDto;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.CodeSignature;
import org.postgresql.util.PGobject;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class TextAspect {
    // key = 前缀 + 租户ID + 数据ID
    private static String JOB_GRADE_CACHE_KEY = "job_grade_cache_%s";
    private static String WORK_PLACE_CACHE_KEY = "work_place_cache_%s";
    private static String EMPLOYEE_TYPE_DICT_MAP_KEY = "employee_type_dict_cache_%s";
    private static String EMP_STATUS_ENUM_MAP_KEY = "emp_status_enum_cache_%s";
    private static String EMP_MARITAL_ENUM_MAP_KEY = "emp_marital_status_enum_cache_%s";

    private static ThreadLocal<String> TENANT_ID = new ThreadLocal<>();

    // 用工类型字典
    public static final String DICT_E = ":employee";
    // 性别字典
    public static final String DICT_G = ":gender";
    // 工作地
    public static final String PLACE = ":place";
    // 员工状态枚举
    public static final String STATUS_ENUM = ":statusEnum";
    // 婚姻状态枚举
    public static final String MARITAL_ENUM = ":maritalEnum";
    // 出行方式字典
    public static final String DICT_TRAVEL_MODE = ":travelMode";
    // 职级
    public static final String JOB_GRADE = ":jobGrade";

    /**
     * 员工状态枚举 empStatus:empStatusName:esEnum
     */
    public static final String EMP_STATUS_ENUM_PROPERTY = "empStatus";
    /**
     * 婚姻状态枚举  maritalStatus:maritalStatusName:emEnum
     */
    public static final String EMP_MARITAL_STATUS_ENUM_PROPERTY = "maritalStatus";

    @Resource
    private BccServiceFeignClient bccServiceFeignClient;
    @Resource
    private PassMetadataFeign passMetadataFeign;
    @Resource
    private CacheService cacheService;
    @Resource
    private HrServiceFeignClient hrServiceFeignClient;

    private static String getField(String exp) {
        return exp.split(":")[0];
    }

    public void deleteCache(String key) {
        log.info("deleteCache key={}", key);
        cacheService.remove(key);
    }

    private static String getFieldAlies(String exp) {
        String[] expArray = exp.split(":");
        if (expArray.length == 2) {
            return expArray[0];
        } else if (expArray.length == 3) {
            return expArray[1];
        }
        return "";
    }

    private static String getConvertType(String exp) {
        String[] expArray = exp.split(":");
        if (expArray.length == 2) {
            return expArray[1];
        } else if (expArray.length == 3) {
            return expArray[2];
        }
        return "";
    }

    @Around("@annotation(cdText)")
    public Object cdText(ProceedingJoinPoint pjp, CDText cdText) {
        Object result = null;
        try {
            result = pjp.proceed();
            if (result != null) {
                Boolean mapClass = cdText.classType() == Map.class;
                setTenantId(pjp);
                Class<?> classType = cdText.classType();
                List<Map> list = new ArrayList<>();

                Boolean isCommonPageResult = result instanceof PageResult;
                Boolean isAttendancePageResult = result instanceof AttendancePageResult;
                Boolean isList = false;
                Boolean isPageList = result instanceof PageList;

                PageResult<Object> commonPageResult = isCommonPageResult ? ((PageResult) result) : null;
                AttendancePageResult<Object> attendancePageResult = isAttendancePageResult ? ((AttendancePageResult) result) : null;
                PageList<Object> pageList = isPageList ? (PageList<Object>) result : null;

                if (isCommonPageResult) {
                    if (mapClass) {
                        PageResult<Map> items = (PageResult<Map>) commonPageResult.getItems();
                        list.addAll(items.getItems());
                    } else {
                        list = FastjsonUtil.convertList(commonPageResult.getItems(), Map.class);
                    }
                } else if (isAttendancePageResult) {
                    if (mapClass) {
                        AttendancePageResult<Map> items = (AttendancePageResult<Map>) attendancePageResult.getItems();
                        list.addAll(items.getItems());
                    } else {
                        list = FastjsonUtil.convertList(attendancePageResult.getItems(), Map.class);
                    }
                } else if (isPageList) {
                    if (mapClass) {
                        PageList<Map> items = (PageList<Map>) result;
                        list.addAll(items);
                    } else {
                        list = FastjsonUtil.convertList(pageList, Map.class);
                    }
                } else if (result instanceof List) {
                    isList = true;
                    if (mapClass) {
                        list = (List<Map>) result;
                    } else {
                        list = FastjsonUtil.toList(FastjsonUtil.toJson(result), Map.class);
                    }
                } else {
                    list = new ArrayList<>(1);
                    if (mapClass) {
                        list.add((Map) result);
                    } else {
                        list.add(FastjsonUtil.convertObject(result, Map.class));
                    }
                }

                for (String ep : cdText.exp()) {
                    String field = getField(ep);
                    String fieldAliesName = getFieldAlies(ep);
                    String convertType = getConvertType(ep);
                    Locale locale = ResponseWrap.getLocale();
                    log.info("localeLanguage:{}", locale != null ? locale.getLanguage() : "");
                    for (Map map : list) {
                        if (map.get(field) != null) {
                            String dataId = map.get(field).toString();
                            switch (convertType) {
                                case "place":
                                    map.put(fieldAliesName, build2CityData(dataId));
                                    break;
                                case "jobGrade":
                                    map.put(fieldAliesName, build2JobGradeData(dataId));
                                    break;
                                case "statusEnum":
                                    map.put(fieldAliesName, build2EnumData(dataId, EMP_STATUS_ENUM_PROPERTY));
                                    break;
                                case "maritalEnum":
                                    map.put(fieldAliesName, build2EnumData(dataId, EMP_MARITAL_STATUS_ENUM_PROPERTY));
                                    break;
                                case "employee":
                                    map.put(fieldAliesName, getDictCache(dataId, locale));
                                    break;
                                case "gender":
                                    if ("0".equals(dataId)) {
                                        map.put(fieldAliesName, "全部");
                                    } else {
                                        map.put(fieldAliesName, getDictCache(dataId, locale));
                                    }
                                    break;
                                case "travelMode":
                                    map.put(fieldAliesName, build2DictDataArray(dataId, locale));
                                    break;
                                default:
                                    break;
                            }
                        } else if (convertType.equals(DICT_E)) {
                            map.put(fieldAliesName, "-");
                        }
                    }
                }

                if (isCommonPageResult) {
                    if (mapClass) {
                        result = new PageResult<>(list, commonPageResult.getPageNo(), commonPageResult.getPageSize(), commonPageResult.getTotal());
                    } else {
                        commonPageResult.setItems(FastjsonUtil.toList(FastjsonUtil.toJson(list), (Class<Object>) classType));
                    }
                } else if (isAttendancePageResult) {
                    if (mapClass) {
                        result = new PageResult<>(list, attendancePageResult.getPageNo(), attendancePageResult.getPageSize(), attendancePageResult.getTotal());
                    } else {
                        attendancePageResult.setItems(FastjsonUtil.toList(FastjsonUtil.toJson(list), (Class<Object>) classType));
                    }
                } else if (isPageList) {
                    pageList.clear();
                    for (Map map : list) {
                        if (mapClass) {
                            pageList.add(map);
                        } else {
                            pageList.add(FastjsonUtil.convertObject(map, classType));
                        }
                    }
                    result = pageList;
                } else if (isList) {
                    if (mapClass) {
                        result = list;
                    } else {
                        result = FastjsonUtil.toList(FastjsonUtil.toJson(list), classType);
                    }
                } else {
                    if (mapClass) {
                        result = list.get(0);
                    } else {
                        result = FastjsonUtil.toObject(FastjsonUtil.toJson(list.get(0)), classType);
                    }
                }
            }
        } catch (Throwable e) {
            log.error("cdText err:{}", e.getMessage(), e);
        } finally {
            TENANT_ID.remove();
        }
        return result;
    }

    public String build2CityData(String cityBid) {
        String cityName = getRedisValue(String.format(WORK_PLACE_CACHE_KEY, getTenantId()), cityBid);
        if (cityName == null) {
            Map<String, String> workPlace = flushWorkPlaceCache();
            return workPlace.getOrDefault(cityBid, cityBid);
        }
        return Optional.ofNullable(cityName).orElse(cityBid);
    }

    public String build2JobGradeData(String jobGradeId) {
        String jobGradeName = getRedisValue(jobGradeCacheKey(), jobGradeId);
        if (jobGradeName == null) {
            Map<String, String> jobGradeMap = flushJobGradeCache();
            return jobGradeMap.getOrDefault(jobGradeId, jobGradeId);
        }
        return Optional.ofNullable(jobGradeName).orElse(jobGradeId);
    }

    private Boolean isAsyncTask(){
        return Thread.currentThread().getName().contains("AsyncTask");
    }

    // 获取婚姻状态枚举文本值
    public String getMaritalEnumText(String emValue, String tenantId) {
        if (isAsyncTask()) {
            return getRedisValue(String.format(EMP_MARITAL_ENUM_MAP_KEY, tenantId), emValue);
        }
        tenantIfNullOrSet(tenantId);
        return build2EnumData(emValue, EMP_MARITAL_STATUS_ENUM_PROPERTY);
    }

    // 获取员工状态枚举文本值
    public String getEmpStatusEnumText(String emValue, String tenantId) {
        if (isAsyncTask()) {
            return getRedisValue(String.format(EMP_STATUS_ENUM_MAP_KEY, tenantId), emValue);
        }
        tenantIfNullOrSet(tenantId);
        return build2EnumData(emValue, EMP_STATUS_ENUM_PROPERTY);
    }

    // 获取工作地文本值
    public String getWorkPlaceText(String workplaceId, String tenantId) {
        if (isAsyncTask()) {
            return getRedisValue(String.format(WORK_PLACE_CACHE_KEY, tenantId), workplaceId);
        }
        tenantIfNullOrSet(tenantId);
        return build2CityData(workplaceId);
    }

    public String build2EnumData(String emValue, String enumProperty) {
        Object enumDisplay = null;
        String enumRedisKey = enumProperty.equals(EMP_STATUS_ENUM_PROPERTY) ? empStatusEnumCacheKey() : empMaritalStatusEnumCacheKey();
        enumDisplay = getRedisValue(enumRedisKey, emValue);
        if (enumDisplay == null) {
            Map<String, String> empStatusEnumMap = flushEnumCache(enumProperty);
            enumDisplay = empStatusEnumMap.getOrDefault(emValue, emValue);
        }
        return Optional.ofNullable(enumDisplay).orElse(emValue).toString();
    }

    public String getDictCache(String dictValue, Locale Locale) {
        String language = Locale != null ? Locale.getLanguage() : "zh_CN";
        SysParamDictDto sysParamDictDto = QueryInfoCache.getDict(String.format("DICT_%s", dictValue));
        if (null == sysParamDictDto) {
            log.error("DictCacheNotFound:{}", dictValue);
            return "-";
        }
        return getDictNameByLang(sysParamDictDto, language);
    }

    private String getDictNameByLang(SysParamDictDto paramDict, String language) {
        String dictChnName = paramDict.getDictChnName();
        if (null == paramDict.getDictNameLang()) {
            return dictChnName;
        }
        try {
            String dictLangJson = "{}";
            Object dictNameLangObj = paramDict.getDictNameLang();
            if (dictNameLangObj instanceof PGobject) {
                PGobject pgobject = (PGobject) paramDict.getDictNameLang();
                if (pgobject != null) {
                    dictLangJson = pgobject.getValue();
                }
            } else if (dictNameLangObj instanceof String) {
                dictLangJson = (String) paramDict.getDictNameLang();
            }
            if (com.caidao1.commons.utils.StringUtil.isNullOrTrimEmpty(dictLangJson)) {
                dictLangJson = "{}";
            }
            Map<String, String> langMap = new ObjectMapper().readValue(dictLangJson, Map.class);
            if (!MapUtils.isEmpty(langMap)) {
                switch (language) {
                    case "zh-CN":
                        return langMap.getOrDefault("cn", dictChnName);
                    case "en-US":
                        return langMap.getOrDefault("en", dictChnName);
                    case "ja-JP":
                        return langMap.getOrDefault("ja", dictChnName);
                    case "ko-KR":
                        return langMap.getOrDefault("ko", dictChnName);
                    default:
                        return langMap.getOrDefault(language, dictChnName);
                }
            }
        } catch (Exception e) {
            log.error("getDictNameByLang err,{}", e.getMessage(), e);
        }
        return dictChnName;
    }


    public String build2DictDataArray(String dictValues, Locale locale) {
        StringBuffer dictDisplayContent = new StringBuffer();
        for (String dictValue : dictValues.split(",")) {
            String dictDisplay = getDictCache(dictValue, locale);
            if (StringUtils.isNotBlank(dictDisplay.replace("-",""))) {
                dictDisplayContent.append(dictDisplay).append(",");
            }else {
                bccServiceFeignClient.refreshSystemDict();
            }
        }
        if (StringUtils.isNotBlank(dictDisplayContent)) {
            return dictDisplayContent.substring(0, dictDisplayContent.length() - 1);
        }
        return "-";
    }

    /**
     * 刷新工作地缓存
     */
    public Map<String, String> flushWorkPlaceCache() {
        boolean addTenant = ifSetSecurityUserInfo();
        try {
            Result<List<WorkplaceDto>> workPlace = hrServiceFeignClient.getWorkPlace();
            if (checkResult(workPlace)) {
                Map<String, String> workPlaceMap = workPlace.getData().stream().collect(Collectors.toMap(WorkplaceDto::getBid, WorkplaceDto::getName));
                mapToRedis(String.format(WORK_PLACE_CACHE_KEY, getTenantId()), workPlaceMap);
                return workPlaceMap;
            }
        } finally {
            if (addTenant) {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        }
        return new HashMap<>();
    }


    /**
     * 刷新职级缓存
     */
    public Map<String, String> flushJobGradeCache() {
        Result<List<JobGradeDataOutVo>> jobGrade = hrServiceFeignClient.getJobGrade();
        if (checkResult(jobGrade)) {
            Map<String, String> jobGradeMap = jobGrade.getData().stream().collect(Collectors.toMap(JobGradeDataOutVo::getBid, JobGradeDataOutVo::getJobGradeName));
            mapToRedis(jobGradeCacheKey(), jobGradeMap);
            return jobGradeMap;
        }
        return new HashMap<>();
    }

    <T> boolean checkResult(Result<List<T>> result) {
        return result != null && result.isSuccess() && result.getData() != null;
    }

    public void mapToRedis(String key, Map<String, String> map) {
        try {
            map.forEach((k, v) -> {
                log.info("valueToCache key:{} value:{}", k, v);
                cacheService.cacheValue(key + "_" + k, v, 60 * 60 * 12);
            });
        } catch (Exception e) {
            log.error("mapToRedis redisKey:{} err:{}", key, e.getMessage(), e);
        }
    }

    public String getRedisValue(String key, String dataId) {
        try {
            String value = cacheService.getValue(key + "_" + dataId);
            log.info("redisKey:{} value:{}", key + "_" + dataId, value);
            return value;
        } catch (Exception e) {
            log.error("getMapByRedis redisKey:{} err:{}", key, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 刷新枚举缓存并返回当前枚举值
     *
     * @param enumProperty
     * @return
     */
    public Map<String, String> flushEnumCache(String enumProperty) {
        // 访问pass 枚举
        String identifier = null;
        String cacheKey = null;
        switch (enumProperty) {
            case EMP_STATUS_ENUM_PROPERTY:
                cacheKey = empStatusEnumCacheKey();
                identifier = "entity.hr.EmpWorkInfo";
                break;
            case EMP_MARITAL_STATUS_ENUM_PROPERTY:
                cacheKey = empMaritalStatusEnumCacheKey();
                identifier = "entity.hr.EmpPrivateInfo";
                break;
        }
        Map<String, String> enumValueMap = new HashMap<>();
        Optional<MetadataPropertyVo> enumPropertyVo = getMetadataProperty(identifier, enumProperty);
        log.info("flushEnumCache enumProperty:{} metadataProperty:{}", enumProperty, FastjsonUtil.toJson(enumPropertyVo));
        if (enumPropertyVo == null || enumPropertyVo.get() == null) {
            return enumValueMap;
        }
        for (PropertyEnumDefDto enumDefDto : enumPropertyVo.get().getEnumDef()) {
            enumValueMap.put(enumDefDto.getValue(), enumDefDto.getDisplay());
        }
        mapToRedis(cacheKey, enumValueMap);
        return enumValueMap;
    }

    boolean ifSetSecurityUserInfo() {
        boolean addTenant = false;
        if (StringUtils.isEmpty(SecurityUserUtil.getSecurityUserInfo().getTenantId()) && TENANT_ID.get() != null) {
            SecurityUserInfo user = new SecurityUserInfo();
            user.setTenantId(TENANT_ID.get());
            addTenant = true;
            SecurityUserUtil.setSecurityUserInfo(user);
        }
        return addTenant;
    }

    public Optional<MetadataPropertyVo> getMetadataProperty(String identifier, String enumProperty) {
        boolean addTenant = ifSetSecurityUserInfo();
        try {
            MetadataVo metadata = null;
            Result result = passMetadataFeign.one(identifier);
            if (result.isSuccess()) {
                metadata = JsonEnhanceUtil.toObject(result.getData(), MetadataVo.class);
                return metadata.getStandardProperties().stream().
                        filter(metadataPropertyVo -> metadataPropertyVo.getProperty().equals(enumProperty)).findFirst();
            }
        } finally {
            if (addTenant) {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        }
        return null;
    }

    private String empStatusEnumCacheKey() {
        return String.format(EMP_STATUS_ENUM_MAP_KEY, getTenantId());
    }

    private String empMaritalStatusEnumCacheKey() {
        return String.format(EMP_MARITAL_ENUM_MAP_KEY, getTenantId());
    }

    private String jobGradeCacheKey() {
        return String.format(JOB_GRADE_CACHE_KEY, getTenantId());
    }

    private String getTenantId() {
        if (TENANT_ID.get() == null) {
            String tenantId = UserContext.getTenantId();
            if (null == tenantId) {
                return null;
            } else {
                TENANT_ID.set(tenantId);
            }
        }
        return TENANT_ID.get();
    }

    private void setTenantId(ProceedingJoinPoint pjp) {
        Object[] args = pjp.getArgs();
        if (null != args) {
            for (Object arg : args) {
                if (arg instanceof UserInfo) {
                    UserInfo userInfo = (UserInfo) arg;
                    if (userInfo == null) {
                        return;
                    }
                    TENANT_ID.set(userInfo.getTenantId());
                }
            }
            String[] paramNames = ((CodeSignature) pjp.getSignature()).getParameterNames();
            for (int i = 0; i < paramNames.length; i++) {
                String argName = paramNames[i];
                if ("tenantId".equals(argName) || "belongOrgId".equals(argName)) {
                    TENANT_ID.set(args[i].toString());
                }
                if ("corpId".equals(argName) || "corpid".equals(argName)) {
                    TENANT_ID.set(args[i].toString());
                }
            }
        }
    }

    public void tenantIfNullOrSet(String tenantId) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        if (stackTrace.length >= 3) {
            String callingMethod = stackTrace[2].getMethodName();
            log.info("Method called by {}", callingMethod);
        }
        log.info("tenantIfNullOrSetTenantId:{}", tenantId);
        if (TENANT_ID.get() == null && tenantId != null && !"null".equals(tenantId)) {
            TENANT_ID.set(tenantId);
        }
    }
}
