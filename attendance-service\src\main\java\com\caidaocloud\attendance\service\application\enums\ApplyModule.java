package com.caidaocloud.attendance.service.application.enums;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/12/24 17:59
 * @Description:
 **/
public enum ApplyModule {
    LEAVE("1", "休假"),
    OVERTIME("2", "加班"),
    GO_OUT("45", "出差"),
    PUNCH_IN("41", "补卡"),
    SHIFT_CHANGE("103","调班"),
    LEAVE_CANCEL("104","销假"),
    COMPENSATORY("105","调休付现"),
    OVERTIME_REVOKE("106", "加班撤销"),
    OVERTIME_ABOLISH("107", "加班废止"),
    TRAVEL_REVOKE("108", "出差撤销"),
    TRAVEL_ABOLISH("109", "出差废止"),
    LEAVE_EXTENSION("110", "假期延期");

    private String funcType;
    private String name;

    ApplyModule(String funcType, String name) {
        this.funcType = funcType;
        this.name = name;
    }

    public String getFuncType() {
        return funcType;
    }

    public void setFuncType(String funcType) {
        this.funcType = funcType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getFuncType(String name) {
        for (ApplyModule c : ApplyModule.values()) {
            if (c.name().equals(name)) {
                return c.getFuncType();
            }
        }
        return null;
    }
}
