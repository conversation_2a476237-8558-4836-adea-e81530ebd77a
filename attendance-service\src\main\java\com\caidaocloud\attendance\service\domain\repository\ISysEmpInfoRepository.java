package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.domain.entity.SysEmpInfoDo;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/6/24 18:56
 * @Description:
 **/
public interface ISysEmpInfoRepository {

    /**
     * 查询员工ID
     *
     * @param tenantId
     * @param empId
     * @param leaveId
     * @param leaveTypeId
     * @param corpId
     * @param sqlExp
     * @param nowTime
     * @param yearEndTime 司龄（至年底）计算使用参数
     * @return
     */
    List<Long> getGroupEmpIds(String tenantId, Long empId, Integer leaveId, Integer leaveTypeId, Long corpId, String sqlExp, Long nowTime, Long yearEndTime);


    List<Integer> getLeaveIdsByGroupExp(String tenantId, Long empId, Integer leaveId, Integer leaveTypeId, Long corpId, String sqlExp, Long nowTime, Long yearEndTime);

    /**
     * 查询员工ID
     *
     * @param corpId
     * @param sqlExp
     * @param nowTime
     * @param yearEndTime 司龄（至年底）计算使用参数
     * @return
     */
    List<Long> getGroupEmpIds(Long corpId, String sqlExp, Long nowTime, Long yearEndTime);

    List<Map> getEmpInfoListByGroupExp(String belongOrgId, String groupExp, String midDate, String firstDate, Integer gender);

    List<Map> getQuotaRuleEmpList(String belongOrgId, String groupExp, String midDate, String firstDate, Integer leaveTypeId, List<Integer> empids, Long gender, String dateStr);

    List<Long> getEmpIdListByWaGroup(String belongid, Long currentDate, Boolean isDefault, Integer waGroupId, Long startDate, Long endDate, String datafilter);

    PageList getEmpInfoPageListForCalendar(PageBean pageBean, String belongOrgId, String orgId,
                                           Long startDate, Long endDate, String keywords, String datafilter);

    List<SysEmpInfo> getEmpInfoByIds(String belongOrgId, List<Long> empIds);

    SysEmpInfo getEmpInfoById(String belongOrgId, Long empId);

    PageList<SysEmpInfoDo> getEmpInfoPageList(PageBean pageBean, String belongOrgId);

    List<SysEmpInfo> getEmpInfoByWorkNos(String belongOrgId, List<String> workNos);

    List<Long> getLeaderOrgEmpIds(String belongOrgId, Long leaderEmpId);

    List<Long> getEmpIdsByLeader(String belongOrgId, Long leaderEmpId);

    List<Long> getEmpIdsByCost(String belongOrgId, Long costId);

    PageList<SysEmpInfoDo> getEmpPageList(PageBean pageBean, String tenantId, String keywords, String dataFilter);

    List<EmpInfoDTO> getEmpDtoList(String tenantId, List<Long> empIds);

    List<Long> getEmpList(String tenantId, Long startDate, Long endDate, String keywords, List<Long> shiftGroupIds, List<Long> orgIds, String dataFilter);

    /**
     * 查询员工入职日期
     */
    List<Map> getEmpHireDateList(String tenantId, List<Long> empIds);
}
