package com.caidaocloud.attendance.service.infrastructure.feign.wfm;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


@Data
@ApiModel(value = "排班及工序状态查询dto")
@Accessors(chain = true)
public class SchedulingAndProcessStatusDto extends BasePage {
    private Boolean export = true;
    @ApiModelProperty("开始日期")
    private Long startTime;
    @ApiModelProperty("结束日期")
    private Long endTime;
    @ApiModelProperty("排班状态")
    private List<Integer> schedulingStatus;
    @ApiModelProperty("状态")
    private List<Integer> status;
    @ApiModelProperty("产品ID")
    private List<Long> productIds;
    @ApiModelProperty("工序名称")
    private String processName;
    @ApiModelProperty("叶片编号")
    private String bladeNumber;
    @ApiModelProperty("是否确认实际班次状态,0未确认、1已确认")
    private Integer actualShiftTimeStatus;
    @ApiModelProperty("是否确认工序时间状态,0未确认、1已确认")
    private Integer processTimeStatus;
}