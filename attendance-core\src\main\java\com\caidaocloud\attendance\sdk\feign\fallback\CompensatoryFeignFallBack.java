package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.sdk.dto.SdkCompensatoryCaseDTO;
import com.caidaocloud.attendance.sdk.dto.SdkCompensatoryRevokeDTO;
import com.caidaocloud.attendance.sdk.feign.ICompensatoryFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class CompensatoryFeignFallBack implements ICompensatoryFeignClient {
    @Override
    public Result<?> saveCompensatoryCaseApply(SdkCompensatoryCaseDTO dto) {
        return Result.fail();
    }

    @Override
    public Result<?> revokeCompensatoryCaseApply(SdkCompensatoryRevokeDTO dto) {
        return Result.fail();
    }

    @Override
    public Result<?> getEmpCompensatoryCaseList(Map<String, Object> parameter) {
        return Result.fail();
    }

    @Override
    public Result<?> myCompensatoryQuota() {
        return Result.fail();
    }
}
