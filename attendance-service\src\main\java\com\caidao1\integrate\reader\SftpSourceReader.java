package com.caidao1.integrate.reader;

import com.caidao1.commons.utils.SftpUtil;
import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.caidao1.integrate.util.GnuPG;
import com.caidao1.integrate.util.GzipUtil;
import com.caidao1.ioc.util.IocCsvReader;
import com.caidao1.integrate.util.IntegrateUtil;
import com.weibo.api.motan.core.extension.SpiMeta;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.StringReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpiMeta(name = "sftp")
public class SftpSourceReader implements SourceReader {

    private static final Logger logger = LoggerFactory.getLogger(SftpSourceReader.class);

    @Override
    public List<Map<String, Object>> getSourceResult(Long corpId, String belongId, SysDataInput dataInput, Map<String, Object> mapJson, Map exparams, List<String> logList, Map returnMap) throws Exception {
        List<Map<String, Object>> sourceResult = new ArrayList<Map<String, Object>>();

        SftpUtil client = new SftpUtil();
        client.setServer((String) mapJson.get("sftp.server"));
        client.setPort((Integer) mapJson.get("sftp.port"));
        client.setLogin((String) mapJson.get("sftp.login"));
        client.setPassword((String) mapJson.get("sftp.password"));
        // client.connect();

        Integer connectTimeout = (Integer) mapJson.get("sftp.connectTimeout");
        client.connect(null == connectTimeout ? 0 : connectTimeout.intValue());

        try {
            FileUtils.forceMkdir(new File((String) mapJson.get("sftp.destination")));
            String filter = (String) mapJson.getOrDefault("sftp.filter", "");
            //格式化支持
            if (mapJson.containsKey("sftp.filterParam")) {
                filter = String.format(filter, IntegrateUtil.formatExp((String) mapJson.get("sftp.filterParam"), "yyyyMMdd"));
            }
            List<String> files = client.lsFolder((String) mapJson.get("sftp.sources"), filter);
            if (CollectionUtils.isEmpty(files) && returnMap != null) {
                // 从 ftp 获取到 0 条数据
                returnMap.put("ftpFileEmpty", true);
            }
            for (String file : files) {
                client.retrieveFile((String) mapJson.get("sftp.sources") + File.separator + file, (String) mapJson.get("sftp.destination") + File.separator + file);

                boolean success = true;
                String readFile = mapJson.get("sftp.destination") + File.separator + file;
                if (mapJson.containsKey("encryption") && mapJson.containsKey("passphrase")) {
                    String enfile = mapJson.get("sftp.destination") + File.separator + file;
                    readFile = mapJson.get("sftp.destination") + File.separator + FilenameUtils.getName(enfile) + ".dec";
                    if ("gnupg".equalsIgnoreCase((String) mapJson.get("encryption"))) {
                        if (new File(readFile).exists()) FileUtils.forceDelete(new File(readFile));
                        success = GnuPG.decrypt(enfile, readFile, (String) mapJson.get("passphrase"));
                    }
                } else if ("targz".equalsIgnoreCase((String) mapJson.get("encryption"))) {
                    String enfile = mapJson.get("sftp.destination") + File.separator + file;
                    readFile = mapJson.get("sftp.destination") + File.separator + FilenameUtils.getBaseName(FilenameUtils.getBaseName(enfile));
                    success = GzipUtil.unCompressTarGz(enfile);
                }

                if (success) {
                    readFile(readFile, dataInput, mapJson, sourceResult, returnMap);
                }

                if (mapJson.containsKey("encryption") && mapJson.containsKey("passphrase") && (Boolean) mapJson.getOrDefault("delete_read", true)) {
                    FileUtils.forceDelete(new File(readFile));
                }
            }
        } catch (Exception e) {
            logger.error("", e);
        } finally {
            client.disconnect();
        }

        return sourceResult;
    }

    private void readFile(String readFile, SysDataInput dataInput, Map<String, Object> mapJson, List<Map<String, Object>> sourceResult, Map returnMap) throws FileNotFoundException {
        if (new File(readFile).isDirectory()) {
            for (File subFile : new File(readFile).listFiles()) {
                readFile(subFile.getAbsolutePath(), dataInput, mapJson, sourceResult, returnMap);
            }
            return;
        }

        try {
            IocCsvReader reader;
            boolean hasHeader = (boolean) mapJson.getOrDefault("file.hasHeader", true);
            if ("string".equals(mapJson.getOrDefault("file.read", "string"))) {
                String conent = FileUtils.readFileToString(new File(readFile)).replace("\n", "");
                reader = new IocCsvReader(new StringReader(conent), ((String) mapJson.get("file.delimiter")).charAt(0), hasHeader);
            } else {
                if(StringUtils.isNotBlank((CharSequence) mapJson.get("file.encoding"))){
                    reader = new IocCsvReader(new FileInputStream(readFile), (String) mapJson.get("file.encoding"),((String) mapJson.get("file.delimiter")).charAt(0), hasHeader);
                }else {
                    reader = new IocCsvReader(new FileInputStream(readFile), ((String) mapJson.get("file.delimiter")).charAt(0), hasHeader);
                }
            }

            List<Map<String, Object>> items = new ArrayList<>();
            reader.getRows().stream().forEach(row -> {
                Map<String, Object> srow = new HashMap<>();
                if (hasHeader) {
                    for (int i = 0; i < reader.getTitle().size(); i++) {
                        if (StringUtils.isNotEmpty(reader.getTitle().get(i))) {
                            srow.put(reader.getTitle().get(i).replace("\uFEFF", ""), row.get(i));
                        }
                    }
                } else {
                    for (int i = 0; i < row.size(); i++) {
                        srow.put(i + "", row.get(i));
                    }
                }
                items.add(srow);
            });
            items.stream().forEach(item -> {
                Map<String, Object> row = new HashMap<String, Object>();
                for (String field : dataInput.getSourceExp().split(",")) {
                    if (field.indexOf(":TIMESTAMP") != -1) {
                        String key = field.substring(field.lastIndexOf("#") + 1);
                        String dataFormat = field.substring(field.indexOf("(") + 1, field.indexOf(")"));
                        SimpleDateFormat sf = new SimpleDateFormat(dataFormat.split("#")[1]);
                        try {
                            Object object = item.get(dataFormat.split("#")[0]);
                            if (object != null) {
                                Long time = sf.parse((String) object).getTime() / 1000;
                                row.put(key, time);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else {
                        String[] meta = field.split("#");
                        if (meta.length > 1) {
                            row.put(meta[1], item.get(meta[0]));
                        } else {
                            row.put(field, item.get(field));
                        }
                    }
                }
//                System.out.println("添加了数据");
                sourceResult.add(row);
            });
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (returnMap != null) {
                    String fileName = readFile.substring(readFile.lastIndexOf(File.separator) + 1);
                    List<String> failFiles;
                    if (returnMap.containsKey("failFiles") && returnMap.get("failFiles") != null) {
                        failFiles = (List<String>) returnMap.get("failFiles");
                    } else {
                        failFiles = new ArrayList<>();
                    }
                    failFiles.add(fileName);
                    returnMap.put("failFiles", failFiles);
                }
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
    }
}