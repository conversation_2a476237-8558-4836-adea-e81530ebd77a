package com.caidaocloud.attendance.sdk.dto;

import com.caidaocloud.attendance.core.wa.enums.AppTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 加班申请
 **/
@Data
public class SdkOverApplySaveDTO {
    @ApiModelProperty("员工id")
    private Long empId;
    @ApiModelProperty("重新发起的id")
    private Integer waid;
    @ApiModelProperty("开始日期 日")
    private Integer startTime;
    @ApiModelProperty("结束日期 日")
    private Integer endTime;
    @ApiModelProperty("开始时间 分钟")
    private Integer stime;
    @ApiModelProperty("结束时间 分钟")
    private Integer etime;
    @ApiModelProperty("事由")
    private String reason;
    @ApiModelProperty("补偿类型")
    private Integer compensateType;
    @ApiModelProperty("附件")
    private String file;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("加班类型")
    private Integer overtimeTypeId;
    @ApiModelProperty("申请入口")
    private AppTypeEnum appType;
    @ApiModelProperty("加班日期（年月日unix时间戳，精确到秒）")
    private Long overtimeDate;
}
