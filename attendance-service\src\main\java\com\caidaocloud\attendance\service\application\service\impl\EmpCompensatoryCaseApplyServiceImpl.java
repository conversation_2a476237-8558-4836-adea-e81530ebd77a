package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaCompensatoryQuotaUseMapper;
import com.caidao1.wa.mybatis.model.WaCompensatoryQuotaUse;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.CompensatoryDataDto;
import com.caidaocloud.attendance.service.application.enums.PreTimeUnitEnum;
import com.caidaocloud.attendance.service.application.service.IEmpCompensatoryCaseApplyService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.application.service.user.MyCenterService;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryCaseApplyDo;
import com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryCaseDo;
import com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryQuotaDo;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.CompensatoryCaseItemDto;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.CompensatoryCaseReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.EmpCompensatoryCaseDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.CompensotaryDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.CompensatoryRevokeDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpCompensatoryCaseApplyServiceImpl implements IEmpCompensatoryCaseApplyService {
    @Resource
    private EmpCompensatoryCaseDo empCompensatoryCaseDo;
    @Resource
    private EmpCompensatoryCaseApplyDo empCompensatoryCaseApplyDo;
    @Resource
    private EmpCompensatoryQuotaDo empCompensatoryQuotaDo;
    @Autowired
    @Lazy
    private IWfService wfService;
    @Autowired
    private WorkflowCallBackService workflowCallBackService;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private ISessionService sessionService;
    @Resource
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaCompensatoryQuotaUseMapper waCompensatoryQuotaUseMapper;
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    @Autowired
    private MyCenterService myCenterService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Override
    public PageList<CompensatoryCaseItemDto> getEmpCompensatoryCaseList(CompensatoryCaseReqDto dto, PageBean pageBean, String tenantId) {
        Map<String, Object> map = new HashMap<>();
        map.put("tenantId", tenantId);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("orgid\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        map.put("filter", filter);
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            map.put("keywords", pageBean.getKeywords());
        }
        if (null != dto.getEmpId()) {
            map.put("empId", dto.getEmpId());
        }
        if (null != dto.getEmpIds()) {
            map.put("empIds", dto.getEmpIds());
        }
        if (null != dto.getStatus()) {
            map.put("status", dto.getStatus());
        }
        map.put("dataFilter", dto.getDataScope());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        PageList<EmpCompensatoryCaseApplyDo> pageList = empCompensatoryCaseApplyDo.getEmpCompensatoryCaseList(pageBounds, map);
        List<CompensatoryCaseItemDto> items = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(pageList)) {
            items = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(CompensatoryCaseItemDto.class);
            items.forEach(item -> {
                Integer timeUint = item.getTimeUnit();
                item.setTimeUnitName(PreTimeUnitEnum.getName(timeUint));
                Float applyDuration = Optional.ofNullable(item.getApplyDuration()).orElse(0f);
                Float validDuration = Optional.ofNullable(item.getValidDuration()).orElse(0f);
                item.setApplyDuration(applyDuration + validDuration);
                if (PreTimeUnitEnum.HOUR.getIndex().equals(timeUint)) {
                    BigDecimal applyDurationB = new BigDecimal(String.valueOf(item.getApplyDuration())).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    item.setApplyDuration(applyDurationB.floatValue());
                }
                item.setStatusName(ApprovalStatusEnum.getName(item.getStatus()));
                item.setBusinessKey(String.format("%s_%s", item.getId(), BusinessCodeEnum.COMPENSATORY.getCode()));
            });
        }
        return new PageList<>(items, pageList.getPaginator());
    }
    @Override
    public Result<String> saveApply(EmpCompensatoryCaseDto dto) throws Exception {
        CompensatoryDataDto dataDto = myCenterService.myCompensatoryQuota();
        return saveCompensatoryApply(dto, dataDto);
    }

    @Transactional
    public Result<String> saveCompensatoryApply(EmpCompensatoryCaseDto dto, CompensatoryDataDto dataDto) throws Exception {
        float validQuota = Optional.ofNullable(dataDto.getValidQuota()).orElse(0f);
        float inValidQuota = Optional.ofNullable(dataDto.getInValidQuota()).orElse(0f);
        if (validQuota == 0 && inValidQuota == 0) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_NOT_EXIST, "没有可申请的调休配额");
        }
        long currentTime = DateUtil.getCurrentTime(true);
        UserInfo userInfo = getUserInfo();
        String tenantId = userInfo.getTenantId();
        Long staffId = dto.getEmpId();
        //失效
        float applyDuration = Optional.ofNullable(dto.getApplyDuration()).orElse(0f);
        //生效中
        float validDuration = Optional.ofNullable(dto.getValidDuration()).orElse(0f);
        //构造调休付现申请实体
        EmpCompensatoryCaseApplyDo apply = new EmpCompensatoryCaseApplyDo();
        apply.setId(snowflakeUtil.createId());
        apply.setEmpId(staffId);
        apply.setTenantId(tenantId);
        apply.setApplyDuration(PreTimeUnitEnum.HOUR.getIndex().equals(dto.getTimeUnit()) ? applyDuration * 60 : applyDuration);
        apply.setValidDuration(PreTimeUnitEnum.HOUR.getIndex().equals(dto.getTimeUnit()) ? validDuration * 60 : validDuration);
        apply.setTimeUnit(dto.getTimeUnit());
        apply.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        apply.setNote(dto.getNote());
        apply.setDeleted(0);
        apply.setCreateBy(userInfo.getUserId());
        apply.setCreateTime(currentTime);
        List<EmpCompensatoryCaseDo> compensatoryList = Lists.newArrayList();
        if (applyDuration > 0) {
            if (inValidQuota < applyDuration) {
                return ResponseWrap.wrapResult(AttendanceCodes.APPLY_EXCEED_QUOTA, "");
            }
            //失效配额扣减
            compensatoryList.addAll(getEmpCompensatoryCaseList(applyDuration, dataDto.getInValidQuotaList(), apply.getId()));
        }
        if (validDuration > 0) {
            if (validQuota < validDuration) {
                return ResponseWrap.wrapResult(AttendanceCodes.APPLY_EXCEED_QUOTA, "");
            }
            //生效配额扣减
            compensatoryList.addAll(getEmpCompensatoryCaseList(validDuration, dataDto.getValidQuotaList(), apply.getId()));
        }
        if (CollectionUtils.isEmpty(compensatoryList)) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_NOT_EXIST, "");
        }
        //保存调休付现申请
        empCompensatoryCaseApplyDo.save(apply);
        //保存调休付现详情记录
        if (CollectionUtils.isNotEmpty(compensatoryList)) {
            empCompensatoryCaseDo.batchSave(compensatoryList);
        }
        //保存调休额度使用记录
        saveCompensatoryUseDetail(userInfo.getStaffId(), ApprovalStatusEnum.IN_APPROVAL.getIndex(), compensatoryList);
        // 检查流程是否已启用
        Result<Boolean> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.COMPENSATORY.getCode());
        if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
            return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, "");
        }
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(staffId);
        String businessKey = String.valueOf(apply.getId());
        String wfBusKey = String.format("%s_%s", businessKey, BusinessCodeEnum.COMPENSATORY.getCode());
        Boolean workflowEnabledResultData = checkWorkflowEnableResult.getData();
        if (!workflowEnabledResultData) {
            WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
            wfCallbackResultDto.setBusinessKey(wfBusKey);
            wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
            wfCallbackResultDto.setTenantId(tenantId);
            workflowCallBackService.compensatoryCallback(wfCallbackResultDto);
        } else {
            WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
            wfBeginWorkflowDto.setFuncCode(BusinessCodeEnum.COMPENSATORY.getCode());
            wfBeginWorkflowDto.setBusinessId(businessKey);
            wfBeginWorkflowDto.setApplicantId(empInfo.getEmpid().toString());
            wfBeginWorkflowDto.setApplicantName(empInfo.getEmpName());
            wfBeginWorkflowDto.setEventTime(currentTime * 1000);
            try {
                Result<?> result = wfRegisterFeign.begin(wfBeginWorkflowDto);
                List<Long> idList = compensatoryList.stream().map(EmpCompensatoryCaseDo::getId).collect(Collectors.toList());
                if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                    WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
                    wfCallbackResultDto.setBusinessKey(wfBusKey);
                    wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
                    wfCallbackResultDto.setTenantId(tenantId);
                    workflowCallBackService.compensatoryCallback(wfCallbackResultDto);
                } else {
                    empCompensatoryQuotaDo.updateWaEmpCompensatoryQuota(userInfo.getUserId(), currentTime * 1000, idList, apply.getStatus());
                }
            } catch (Exception e) {
                log.error("发起工作流失败{}", e.getMessage(), e);
                if (e instanceof CDException) {
                    if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_CONFIG_ERR, "").getMsg());
                    } else {
                        throw e;
                    }
                } else {
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, "").getMsg());
                }
            }
        }
        return Result.ok(wfBusKey);
    }

    private List<EmpCompensatoryCaseDo> getEmpCompensatoryCaseList(Float duration, List<CompensotaryDto> quotaList, Long applyId) {
        List<EmpCompensatoryCaseDo> compensatoryList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(quotaList)) {
            return compensatoryList;
        }
        quotaList = quotaList.stream().sorted(Comparator.comparing(CompensotaryDto::getLastDate)).collect(Collectors.toList());
        float applyDuration = duration;
        for (CompensotaryDto quota : quotaList) {
            Float leftDuration = quota.getLeftDay();
            if (leftDuration <= 0) {
                continue;
            }
            if (applyDuration > leftDuration) {
                applyDuration -= leftDuration;
                compensatoryList.add(getEmpCompensatoryCaseDo(getUserInfo(), quota, leftDuration, applyId));
            } else {
                compensatoryList.add(getEmpCompensatoryCaseDo(getUserInfo(), quota, duration, applyId));
                break;
            }
        }
        return compensatoryList;
    }

    private EmpCompensatoryCaseDo getEmpCompensatoryCaseDo(UserInfo userInfo, CompensotaryDto quota, Float applyDuration, Long applyId) {
        //构造调休付现明细实体
        EmpCompensatoryCaseDo model = new EmpCompensatoryCaseDo();
        model.setId(snowflakeUtil.createId());
        model.setApplyId(applyId);
        model.setTenantId(userInfo.getTenantId());
        model.setQuotaId(quota.getEmpQuotaId());
        model.setQuotaDay(PreTimeUnitEnum.HOUR.getIndex().equals(quota.getUnit()) ? quota.getQuotaDay() * 60 : quota.getQuotaDay());
        model.setQuotaUnit(quota.getUnit());
        model.setStartDate(quota.getStartDate());
        model.setEndDate(quota.getLastDate());
        model.setApplyDuration(PreTimeUnitEnum.HOUR.getIndex().equals(quota.getUnit()) ? applyDuration * 60 : applyDuration);
        model.setTimeUnit(quota.getUnit());
        model.setDeleted(0);
        model.setCreateBy(userInfo.getUserId());
        model.setCreateTime(DateUtil.getCurrentTime(true));
        return model;
    }

    public void saveCompensatoryUseDetail(Long empId, Integer status, List<EmpCompensatoryCaseDo> modelList) {
        if (CollectionUtils.isEmpty(modelList)) {
            return;
        }
        for (EmpCompensatoryCaseDo model : modelList) {
            getWaCompensatoryQuotaUse(empId, status, model);
        }
    }

    /**
     * 构造配额使用记录实体
     *
     * @param model 调休付现实体
     * @return
     */
    private void getWaCompensatoryQuotaUse(Long empId, Integer status, EmpCompensatoryCaseDo model) {
        WaCompensatoryQuotaUse waCompensatoryQuotaUse = new WaCompensatoryQuotaUse();
        Long currentTime = DateUtil.getCurrentTime(false);
        waCompensatoryQuotaUse.setUseId(snowflakeUtil.createId());
        waCompensatoryQuotaUse.setEmpId(empId);
        waCompensatoryQuotaUse.setApprovalStatus(status);
        waCompensatoryQuotaUse.setDeleted(0);
        waCompensatoryQuotaUse.setLeaveDate(DateUtil.getOnlyDate());
        waCompensatoryQuotaUse.setLeaveDaytimeId(model.getId());
        waCompensatoryQuotaUse.setQuotaId(model.getQuotaId());
        waCompensatoryQuotaUse.setTimeDuration(model.getApplyDuration());
        waCompensatoryQuotaUse.setCancelTimeDuration(0f);
        waCompensatoryQuotaUse.setCreateBy(model.getCreateBy());
        waCompensatoryQuotaUse.setCreateTime(currentTime);
        waCompensatoryQuotaUse.setUpdateBy(model.getCreateBy());
        waCompensatoryQuotaUse.setUpdateTime(currentTime);
        waCompensatoryQuotaUseMapper.insert(waCompensatoryQuotaUse);
    }

    /**
     * 撤销调休付现申请
     *
     * @param dto
     * @param userInfo
     * @return
     */
    @Transactional
    @Override
    public Result<Boolean> revokeCompensatoryApply(CompensatoryRevokeDto dto, UserInfo userInfo) {
        if (null == userInfo) {
            userInfo = getUserInfo();
        }
        String tenantId = userInfo.getTenantId();
        String[] businessKeys = dto.getBusinessKey().split("_");
        String businessId = businessKeys[0];
        Long id = Long.valueOf(businessId);
        // 判断是否是有效地付现申请单
        Optional<EmpCompensatoryCaseApplyDo> compensatoryCaseOpt = Optional.ofNullable(empCompensatoryCaseApplyDo.getDetailById(tenantId, id));
        if (!compensatoryCaseOpt.isPresent()) {
            return ResponseWrap.wrapResult(AttendanceCodes.APPLY_NOT_EXIST, Boolean.FALSE);
        }
        EmpCompensatoryCaseApplyDo model = compensatoryCaseOpt.get();
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(model.getStatus())) {
            return ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE);
        }
        // 只允许撤销审批中和审批通过的单据
        if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(model.getStatus()) && !ApprovalStatusEnum.PASSED.getIndex().equals(model.getStatus())) {
            return ResponseWrap.wrapResult(AttendanceCodes.APPLY_NOT_ALLOW_REVOKE, Boolean.FALSE);
        }
        // 查询配额
        List<EmpCompensatoryCaseDo> caseDetailList = empCompensatoryCaseDo.getEmpCompensatoryCase(tenantId, model.getId());
        if (CollectionUtils.isEmpty(caseDetailList)) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_NOT_EXIST, Boolean.FALSE);
        }
        List<Long> quotaIds = caseDetailList.stream().map(EmpCompensatoryCaseDo::getQuotaId).distinct().collect(Collectors.toList());
        List<EmpCompensatoryQuotaDo> quotaList = empCompensatoryQuotaDo.getEmpCompensatoryQuotaList(quotaIds);
        if (CollectionUtils.isEmpty(quotaList)) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_NOT_EXIST, Boolean.FALSE);
        }
        Long userId = userInfo.getUserId();
        Long current = DateUtil.getCurrentTime(true);
        //更新单据状态
        model.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
        model.setRevokeReason(dto.getRevokeReason());
        model.setUpdateBy(userId);
        model.setUpdateTime(current);
        //新工作流
        String businessKey = String.format("%s_%s", businessId, BusinessCodeEnum.COMPENSATORY.getCode());
        WfRevokeDto revokeDto = new WfRevokeDto();
        revokeDto.setBusinessKey(businessKey);
        Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
        if (null == result || !result.isSuccess()) {
            return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE);
        }
        empCompensatoryCaseApplyDo.update(model);
        //额度返还
        List<Long> idList = caseDetailList.stream().map(EmpCompensatoryCaseDo::getId).distinct().collect(Collectors.toList());
        QueryWrapper<WaCompensatoryQuotaUse> qw = new QueryWrapper<>();
        qw.in("leave_daytime_id", idList);
        WaCompensatoryQuotaUse waCompensatoryQuotaUse = new WaCompensatoryQuotaUse();
        waCompensatoryQuotaUse.setApprovalStatus(model.getStatus());
        waCompensatoryQuotaUse.setUpdateBy(userId);
        waCompensatoryQuotaUse.setUpdateTime(current * 1000);
        waCompensatoryQuotaUseMapper.update(waCompensatoryQuotaUse, qw);
        empCompensatoryQuotaDo.updateWaEmpCompensatoryQuota(userId, current * 1000, idList, model.getStatus());
        return Result.ok(Boolean.TRUE);
    }

    @Override
    public Map<Long, BigDecimal> getApprovedOfCompensatoryCase(List<Long> empIdList, Long startDate, Long endDate) {
        endDate = DateUtil.getOnlyDate(new Date(endDate * 1000)) + 86399;
        List<EmpCompensatoryCaseApplyDo> empCompensatoryCaseApplyDos = empCompensatoryCaseApplyDo.getApprovedOfCompensatoryCase(empIdList, startDate, endDate);
        Map<Long, BigDecimal> calMap = Maps.newHashMap();
        empCompensatoryCaseApplyDos.stream().collect(Collectors.groupingBy(EmpCompensatoryCaseApplyDo::getEmpId)).forEach((k, v) -> {
            for (EmpCompensatoryCaseApplyDo compensatoryCaseApplyDo : v) {
                var orDefault = calMap.getOrDefault(k, new BigDecimal(0));
                if (Objects.equals(compensatoryCaseApplyDo.getTimeUnit(), PreTimeUnitEnum.DAY.getIndex())) {
                    orDefault = orDefault.add(BigDecimal.valueOf((compensatoryCaseApplyDo.getApplyDuration() + compensatoryCaseApplyDo.getValidDuration()) * 8));
                } else if (Objects.equals(compensatoryCaseApplyDo.getTimeUnit(), PreTimeUnitEnum.HOUR.getIndex())) {
                    orDefault = orDefault.add(BigDecimal.valueOf(compensatoryCaseApplyDo.getApplyDuration() + compensatoryCaseApplyDo.getValidDuration()).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP));
                }
                calMap.put(k, orDefault);
            }
        });
        return calMap;
    }
}