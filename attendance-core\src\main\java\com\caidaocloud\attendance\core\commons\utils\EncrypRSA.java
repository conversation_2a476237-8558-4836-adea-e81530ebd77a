package com.caidaocloud.attendance.core.commons.utils;


import org.springframework.util.Base64Utils;

import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

public class EncrypRSA {

	private static final String PUBLIC_KEY ="publicKey";
	private static final String PRIVATE_KEY ="privateKey";

	private static final String KEY_ALGORITHM = "RSA";

	/**
	 * 加密
	 * @param publicKey
	 * @param srcBytes
	 * @return
	 * @throws NoSuchAlgorithmException
	 * @throws NoSuchPaddingException
	 * @throws InvalidKeyException
	 * @throws IllegalBlockSizeException
	 * @throws BadPaddingException
	 */
	public static byte[] encrypt(PublicKey publicKey, byte[] srcBytes) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException{
		if(publicKey!=null){
			//Cipher负责完成加密或解密工作，基于RSA
			Cipher cipher = Cipher.getInstance("RSA");
			//根据公钥，对Cipher对象进行初始化
			cipher.init(Cipher.ENCRYPT_MODE, publicKey);
			byte[] resultBytes = cipher.doFinal(srcBytes);
			return resultBytes;
		}
		return null;
	}
	
	/**
	 * 解密 
	 * @param privateKey
	 * @param srcBytes
	 * @return
	 * @throws NoSuchAlgorithmException
	 * @throws NoSuchPaddingException
	 * @throws InvalidKeyException
	 * @throws IllegalBlockSizeException
	 * @throws BadPaddingException
	 */
	public static byte[] decrypt(PrivateKey privateKey,byte[] srcBytes) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException{
		if(privateKey!=null){
			//Cipher负责完成加密或解密工作，基于RSA
			Cipher cipher = Cipher.getInstance("RSA");
			//根据公钥，对Cipher对象进行初始化
			cipher.init(Cipher.DECRYPT_MODE, privateKey);
			byte[] resultBytes = cipher.doFinal(srcBytes);
			return resultBytes;
		}
		return null;
	}

	/**
	 * 通过字符串生成私钥
	 * @param privateKeyString
	 * @return
	 */
	public static PrivateKey getPrivateKey(String privateKeyString){
		PrivateKey privateKey = null;
		byte[] decodeKey = Base64Utils.decodeFromString(privateKeyString); //将字符串Base64解码
		PKCS8EncodedKeySpec x509 = new PKCS8EncodedKeySpec(decodeKey);//创建x509证书封装类
		try {
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");//指定RSA
			privateKey = keyFactory.generatePrivate(x509);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (InvalidKeySpecException e) {
			e.printStackTrace();
		}
		return privateKey;
	}

	/**
	 * 通过字符串生成公钥
	 * @param publicKeyString
	 * @return
	 */
	public static PublicKey getPublicKey(String publicKeyString){
		PublicKey publicKey = null;
		byte[] keyBytes = Base64Utils.decodeFromString(publicKeyString); //将字符串Base64解码
		X509EncodedKeySpec x509 = new X509EncodedKeySpec(keyBytes);
		try {
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");//指定RSA
			publicKey = keyFactory.generatePublic(x509);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (InvalidKeySpecException e) {
			e.printStackTrace();
		}
		return publicKey;
	}

	public static Map<String,String> genKey() throws NoSuchAlgorithmException{
		Map<String,String> keyMap = new HashMap<String,String>();
		KeyPairGenerator keygen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
		SecureRandom random = new SecureRandom();
		// random.setSeed(keyInfo.getBytes());
		// 初始加密，512位已被破解，用1024位,最好用2048位
		keygen.initialize(1024, random);
		// 取得密钥对
		KeyPair kp = keygen.generateKeyPair();
		RSAPrivateKey privateKey = (RSAPrivateKey)kp.getPrivate();
		String privateKeyString = Base64Utils.encodeToString(privateKey.getEncoded());
		RSAPublicKey publicKey = (RSAPublicKey)kp.getPublic();
		String publicKeyString = Base64Utils.encodeToString(publicKey.getEncoded());
		keyMap.put(PUBLIC_KEY, publicKeyString);
		keyMap.put(PRIVATE_KEY, privateKeyString);
		return keyMap;
	}



	/**
	 * @param args
	 * @throws NoSuchAlgorithmException
	 * @throws BadPaddingException
	 * @throws IllegalBlockSizeException
	 * @throws NoSuchPaddingException
	 * @throws InvalidKeyException
	 */
	public static void main(String[] args) throws Exception{
		EncrypRSA rsa = new EncrypRSA();
		Map<String,String> keyMap = genKey();
//		PublicKey publicKey1 = getPublicKey(keyMap.get(PUBLIC_KEY));
//		PrivateKey privateKey1= getPrivateKey(keyMap.get(PRIVATE_KEY));
		System.out.println(keyMap.get(PUBLIC_KEY));
		System.out.println();
		System.out.println(keyMap.get(PRIVATE_KEY));
		PublicKey publicKey1 = getPublicKey(keyMap.get(PUBLIC_KEY));
		PrivateKey privateKey1= getPrivateKey(keyMap.get(PRIVATE_KEY));

		String info ="明文123456";
		//加密
		byte[] bytes1 = rsa.encrypt(publicKey1,info.getBytes("utf-8"));
		//解密
		bytes1 = rsa.decrypt(privateKey1,bytes1);

		System.out.println(new String(bytes1,"utf-8"));

//		java.security.Security.addProvider(
//				new org.bouncycastle.jce.provider.BouncyCastleProvider()
//		);
//		String msg = "{'asdf':232,'打开快递':'kkdkd打开时空的','22':232}";
//		//KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
//		KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
//		//初始化密钥对生成器，密钥大小为1024位
//		keyPairGen.initialize(1024);
//		//生成一个密钥对，保存在keyPair中
//		KeyPair keyPair = keyPairGen.generateKeyPair();
//		//得到私钥
//		RSAPrivateKey privateKey = (RSAPrivateKey)keyPair.getPrivate();
//		String prikey = Base64Utils.encodeToString(privateKey.getEncoded());
//		System.out.println(prikey);
//		System.out.println();
//		//得到公钥
//		RSAPublicKey publicKey = (RSAPublicKey)keyPair.getPublic();
//		String pubkey = Base64Utils.encodeToString(publicKey.getEncoded());
//		System.out.println(pubkey);
//
//		//用公钥加密
//		byte[] srcBytes = msg.getBytes();
//		byte[] resultBytes = rsa.encrypt(publicKey, srcBytes);
//
//		//用私钥解密
//		byte[] decBytes = rsa.decrypt(privateKey, resultBytes);
//
//		System.out.println("明文是:" + msg);
//		System.out.println("加密后是:" + new String(resultBytes));
//		String a = new BASE64Encoder().encode(resultBytes);
//		System.out.println("加密后是a:" + a);
//		System.out.println("加密后是:" + new String(resultBytes));
//		System.out.println("解密后是:" + new String(decBytes));
//		byte[] b = new BASE64Decoder().decodeBuffer(a);
//		System.out.println("解密后是b:" + new String(rsa.decrypt(privateKey, b)));

	}


}