package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/7/15 14:31
 * @Description:
 **/
public enum OtTypeEnum {
    WORKDAY(1, "工作日加班", AttendanceCodes.WORKDAY_OVERTIME),
    REST_DAY(2, "休息日加班", AttendanceCodes.REST_DAY_OVERTIME),
    HOLIDAY(3, "法定假日加班", AttendanceCodes.HOLIDAY_OVERTIME),
    SPECIAL(4, "特殊休日加班", AttendanceCodes.SPECIAL_REST_DAY_OVERTIME);
    private int index;
    private String desc;
    private Integer code;

    OtTypeEnum(int index, String desc, Integer code) {
        this.index = index;
        this.desc = desc;
        this.code = code;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public static String getDescByIndex(int index) {
        for (OtTypeEnum otType : OtTypeEnum.values()) {
            if (otType.getIndex() == index) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(otType.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return otType.getDesc();
            }
        }
        return "";
    }
}
