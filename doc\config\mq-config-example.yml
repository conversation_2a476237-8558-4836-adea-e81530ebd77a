# 考勤服务消息队列配置示例
# 将以下配置添加到 Nacos 配置中心

attendance:
  mq:
    # ============ 死信队列配置 ============
    # 是否启用死信队列功能
    # true: 启用死信队列保护，失败消息进入死信队列便于排查和恢复
    # false: 不启用死信队列，与现有队列兼容但失败消息可能丢失
    enableDLQ: true
    
    # ============ 重试配置 ============
    # 最大重试次数
    # 建议值: 1-5，过高可能影响系统性能
    # 0: 不重试，直接处理失败
    # 1: 重试1次（推荐用于生产环境）
    # 3: 重试3次（适合测试环境或网络不稳定场景）
    maxRetryCount: 3
    
    # ============ 失败处理策略 ============
    # 仅在 enableDLQ=false 时生效
    # ACK: 确认消息（丢弃消息，推荐用于生产环境避免消息积压）
    # NACK_REQUEUE: 拒绝并重新入队（可能导致无限循环，慎用）
    # NACK_DISCARD: 拒绝并丢弃（需要确保队列没有死信队列配置）
    failureStrategy: ACK
    
    # ============ 消费者配置 ============
    consumer:
      # 最小并发消费者数量
      # 建议根据服务器性能和业务量调整
      concurrentConsumers: 1
      
      # 最大并发消费者数量
      # 系统负载高时会自动扩展到此数量
      maxConcurrentConsumers: 3
      
      # 预取数量
      # 控制每个消费者一次性获取的消息数量
      # 1: 公平分发，适合处理时间差异大的场景
      # 5-10: 提高吞吐量，适合处理时间相对均匀的场景
      prefetchCount: 1
      
      # ============ 超时配置（重要！）============
      # 消费者超时时间（毫秒） - 防止长时间处理导致消息重复投递
      # 建议设置为预估业务处理时间的2-3倍
      # 30分钟 = 1800000ms, 60分钟 = 3600000ms
      consumerTimeout: 1800000
      
      # 消息接收超时时间（毫秒）
      # 消费者等待新消息的最大时间
      receiveTimeout: 60000
      
      # 是否自动启动消费者
      autoStartup: true
      
      # 任务执行器线程名前缀
      taskExecutorName: "attendance-mq-executor-"
      
      # ============ 超时自动ACK配置（新功能！）============
      # 是否启用超时自动ACK机制 - 解决长时间处理导致消息重复投递的问题
      enableTimeoutAutoAck: true
      
      # 安全ACK时间比例 - 在达到总超时时间的多少比例时自动ACK
      # 0.85 表示在85%超时时间时ACK，推荐范围：0.8-0.9
      timeoutAckRatio: 0.85
      
      # 是否在立即ACK后继续异步处理业务逻辑
      # true: ACK后继续处理，保证业务完整性
      # false: ACK后跳过处理，优先防止消息重复
      continueProcessingAfterTimeoutAck: true
      
      # 超时检测间隔（毫秒）
      # 检测线程多长时间检查一次是否接近超时
      timeoutCheckInterval: 1000
    
    # ============ 队列配置 ============
    queue:
      # 消息TTL（毫秒）
      # 0: 不设置TTL，消息永不过期
      # 300000: 5分钟后过期，适合实时性要求高的场景
      # 1800000: 30分钟后过期，适合一般业务场景
      messageTtl: 0
      
      # 是否自动删除队列
      # false: 不自动删除，保证消息安全（推荐）
      # true: 最后一个消费者断开时自动删除队列（谨慎使用）
      autoDelete: false

# ============ 不同场景推荐配置 ============

# 生产环境推荐配置（中等规模）：
# attendance:
#   mq:
#     enableDLQ: true
#     maxRetryCount: 3
#     failureStrategy: ACK
#     consumer:
#       concurrentConsumers: 2
#       maxConcurrentConsumers: 4
#       prefetchCount: 1
#       consumerTimeout: 1800000    # 30分钟
#       receiveTimeout: 60000
#       autoStartup: true
#       taskExecutorName: "attendance-clock-analysis-"
#     queue:
#       messageTtl: 0
#       autoDelete: false

# 大规模处理配置（500+员工，长时间范围）：
# attendance:
#   mq:
#     enableDLQ: true
#     maxRetryCount: 2              # 减少重试避免长时间阻塞
#     failureStrategy: ACK
#     consumer:
#       concurrentConsumers: 1      # 单线程避免数据库压力
#       maxConcurrentConsumers: 1
#       prefetchCount: 1
#       consumerTimeout: 7200000    # 2小时处理时间
#       receiveTimeout: 60000
#       autoStartup: true
#       taskExecutorName: "attendance-heavy-analysis-"
#       # 超时自动ACK配置
#       enableTimeoutAutoAck: true
#       timeoutAckRatio: 0.9        # 90%时ACK，更保守
#       continueProcessingAfterTimeoutAck: true
#       timeoutCheckInterval: 2000  # 2秒检查，减少开销
#     queue:
#       messageTtl: 0
#       autoDelete: false

# 小规模快速处理配置（< 100员工）：
# attendance:
#   mq:
#     enableDLQ: true
#     maxRetryCount: 3
#     failureStrategy: ACK
#     consumer:
#       concurrentConsumers: 2
#       maxConcurrentConsumers: 5
#       prefetchCount: 2
#       consumerTimeout: 300000     # 5分钟足够
#       receiveTimeout: 30000
#       autoStartup: true
#       taskExecutorName: "attendance-quick-analysis-"
#       # 超时自动ACK配置
#       enableTimeoutAutoAck: true
#       timeoutAckRatio: 0.8        # 80%时ACK，更激进
#       continueProcessingAfterTimeoutAck: true
#       timeoutCheckInterval: 500   # 500ms检查，更精确
#     queue:
#       messageTtl: 600000          # 10分钟过期
#       autoDelete: false

# 高可靠性配置（关键业务数据）：
# attendance:
#   mq:
#     enableDLQ: true
#     maxRetryCount: 2
#     failureStrategy: ACK
#     consumer:
#       concurrentConsumers: 1
#       maxConcurrentConsumers: 2
#       prefetchCount: 1
#       consumerTimeout: 3600000    # 60分钟
#       receiveTimeout: 60000
#       autoStartup: true
#       taskExecutorName: "attendance-reliable-analysis-"
#       # 超时自动ACK配置 - 高可靠性设置
#       enableTimeoutAutoAck: true
#       timeoutAckRatio: 0.9        # 90%时ACK，最大安全边际
#       continueProcessingAfterTimeoutAck: true  # 必须完成处理
#       timeoutCheckInterval: 500   # 精确检测
#     queue:
#       messageTtl: 0
#       autoDelete: false

# 高吞吐量配置（性能优先）：
# attendance:
#   mq:
#     enableDLQ: true
#     maxRetryCount: 1
#     failureStrategy: ACK
#     consumer:
#       concurrentConsumers: 3
#       maxConcurrentConsumers: 6
#       prefetchCount: 3
#       consumerTimeout: 900000     # 15分钟
#       receiveTimeout: 30000
#       autoStartup: true
#       taskExecutorName: "attendance-throughput-analysis-"
#       # 超时自动ACK配置 - 性能优先设置
#       enableTimeoutAutoAck: true
#       timeoutAckRatio: 0.8        # 80%时ACK，更激进
#       continueProcessingAfterTimeoutAck: false  # 性能优先，可跳过
#       timeoutCheckInterval: 2000  # 降低检测开销
#     queue:
#       messageTtl: 300000          # 5分钟过期
#       autoDelete: false

# 兼容现有队列配置（临时使用）：
# attendance:
#   mq:
#     enableDLQ: false            # 与现有队列兼容
#     maxRetryCount: 1
#     failureStrategy: ACK        # 确保不会丢失消息
#     consumer:
#       concurrentConsumers: 1
#       maxConcurrentConsumers: 3
#       prefetchCount: 1
#       consumerTimeout: 900000   # 15分钟
#       receiveTimeout: 60000
#       autoStartup: true
#       taskExecutorName: "attendance-compatible-analysis-"
#       # 兼容模式仍可使用超时自动ACK
#       enableTimeoutAutoAck: true
#       timeoutAckRatio: 0.85
#       continueProcessingAfterTimeoutAck: true
#       timeoutCheckInterval: 1000
#     queue:
#       messageTtl: 0
#       autoDelete: false

# ============ 超时自动ACK功能说明 ============
# 🎯 核心功能：解决长时间处理导致消息重复投递的问题
# - 在接近超时前自动ACK消息，避免RabbitMQ重新投递
# - 支持ACK后继续异步处理，兼顾可靠性和性能
# 
# 🔧 关键配置参数：
# - enableTimeoutAutoAck: 是否启用（默认true）
# - timeoutAckRatio: 何时ACK（0.85表示85%超时时间时ACK）
# - continueProcessingAfterTimeoutAck: ACK后是否继续处理（默认true）
# - timeoutCheckInterval: 检测间隔（默认1000ms）

# ============ 重要提醒 ============
# 1. consumerTimeout 是防止消息重复投递的关键配置
# 2. enableTimeoutAutoAck 提供了更优雅的超时处理解决方案
# 3. 建议根据实际业务处理时间设置，留出2-3倍安全边际
# 4. 大规模数据处理建议使用单线程避免数据库连接池耗尽
# 5. timeoutAckRatio建议设置为0.8-0.9之间
# 6. 定期监控处理时间并调整配置
# 7. 生产环境务必启用死信队列保护 