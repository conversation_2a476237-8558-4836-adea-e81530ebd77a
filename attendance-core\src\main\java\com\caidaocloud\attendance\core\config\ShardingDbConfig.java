package com.caidaocloud.attendance.core.config;

import com.caidao1.integrate.datasource.CusDataSource;
import com.caidaocloud.attendance.core.commons.quartz.JobBeanJobFactory;
import io.shardingjdbc.core.api.config.ShardingRuleConfiguration;
import io.shardingjdbc.core.api.config.strategy.HintShardingStrategyConfiguration;
import io.shardingjdbc.core.jdbc.core.datasource.ShardingDataSource;
import org.apache.commons.dbcp.BasicDataSource;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Configuration
@EnableTransactionManagement
@MapperScan(basePackages = {"com.caidao1.**.mapper"}, sqlSessionFactoryRef = "sqlSessionFactory")
@ConditionalOnProperty(name = "shardingjdbc.open", havingValue = "true")
public class ShardingDbConfig {

    @Bean(name = "cusDataSource")
    public CusDataSource cusDataSource() {
        return new CusDataSource();
    }

    @Bean(name = "cusParameterJdbcTemplate")
    public NamedParameterJdbcTemplate cusParameterJdbcTemplate() throws IOException {
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(cusDataSource());
        return jdbcTemplate;
    }

    @Bean(name = "quartzSf")
    public SchedulerFactoryBean schedulerFactoryBean() throws IOException, SQLException {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setJobFactory(new JobBeanJobFactory());
        factory.setApplicationContextSchedulerContextKey("applicationContextKey");
        factory.setQuartzProperties(quartzProperties());
        factory.setDataSource(shardingDataSource());
        factory.setAutoStartup(false);
        return factory;
    }

    @Bean
    public Properties quartzProperties() throws IOException {
        PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
        propertiesFactoryBean.setLocation(new ClassPathResource("/quartz2db.properties"));
        propertiesFactoryBean.afterPropertiesSet();
        return propertiesFactoryBean.getObject();
    }


    /**
     * 配置分库分表策略
     *
     * @return
     * @throws SQLException
     */
    @Primary
    @Bean(name = "shardingDataSource")
    public DataSource shardingDataSource() throws SQLException {
        ShardingRuleConfiguration shardingRuleConfig;
        shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.setDefaultDataSourceName("public");
        shardingRuleConfig.setDefaultDatabaseShardingStrategyConfig(new HintShardingStrategyConfiguration(DbShardingAlgorithm.class.getName()));
//        shardingRuleConfig.setDefaultTableShardingStrategyConfig(new HintShardingStrategyConfiguration(DbShardingAlgorithm.class.getName()));
        return new ShardingDataSource(shardingRuleConfig.build(createDataSourceMap()));
    }

    /**
     * 配置事务管理器
     *
     * @param shardingDataSource
     * @return
     */
    @Bean
    public PlatformTransactionManager transactitonManager(DataSource shardingDataSource) {
        return new DataSourceTransactionManager(shardingDataSource);
    }

//    @Bean
//    @Primary
//    public SqlSessionFactory sqlSessionFactory(DataSource shardingDataSource) throws Exception {
//        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
//        bean.setDataSource(shardingDataSource);
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:/com/caidao1/**/sqlmap/*.xml"));
//        return bean.getObject();
//    }

    private Map<String, DataSource> createDataSourceMap() {
        Map<String, DataSource> result = new HashMap<>();
        result.put("public", createDataSource("public"));
        result.put("dfzg", createDataSource("cdcore"));
        return result;
    }

    private DataSource createDataSource(final String dataSourceName) {
        BasicDataSource result = new BasicDataSource();
        result.setDriverClassName("org.postgresql.Driver");
//        result.setUrl(String.format("****************************************", dataSourceName));
//        result.setUsername("admin");
//        result.setPassword("AU3hO4wtXYcBkXBu");
        result.setUrl(String.format("************************************************************", dataSourceName));
        result.setUsername("admin");
        result.setPassword("AU3hO4wtXYcBkXBu");
        return result;
    }
}
