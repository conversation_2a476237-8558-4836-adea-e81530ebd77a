package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWaEmpLeaveCancelDaytimeRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Data
@Service
public class WaEmpLeaveCancelDaytimeDo {
    private Long leaveCancelDaytimeId;
    private Long leaveCancelId;
    private Long leaveCancelInfoId;
    private Long leaveCancelDate;
    private String shalfDay;
    private String ehalfDay;
    private Integer startTime;
    private Integer endTime;
    private Short periodType;
    private Short timeUnit;
    private Float timeDuration;
    private Integer dateType;
    private Long realDate;
    private Integer shiftDefId;
    private Float applyTimeDuration;
    private Float beforeAdjustTimeDuration;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private Long empid;

    @Autowired
    private IWaEmpLeaveCancelDaytimeRepository waEmpLeaveCancelDaytimeRepository;

    public void saveBatch(List<WaEmpLeaveCancelDaytimeDo> list){
        waEmpLeaveCancelDaytimeRepository.saveBatch(list);
    }

    public List<WaEmpLeaveCancelDaytimeDo> getByLeaveCancelIds(List<Long> cancelIds){
        return waEmpLeaveCancelDaytimeRepository.getByLeaveCancelIds(cancelIds);
    }

    public List<WaEmpLeaveCancelDaytimeDo> getByLeaveCancelId(Long cancelId) {
        return waEmpLeaveCancelDaytimeRepository.getByLeaveCancelId(cancelId);
    }

    public List<WaEmpLeaveCancelDaytimeDo> getLeaveCancelDaytimeList(String tenantId, Long startTime, Long endTime) {
        return waEmpLeaveCancelDaytimeRepository.getLeaveCancelDaytimeList(tenantId, startTime, endTime);
    }
}