package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.sdk.dto.SdkSaveBdkDTO;
import com.caidaocloud.attendance.sdk.feign.fallback.EmpClockInFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 员工打卡
 *
 * <AUTHOR>
 * @Date 2023/6/27
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = EmpClockInFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "empClockInFeignClient")
public interface IEmpClockInFeignClient {

    /**
     * 员工申请补卡
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/user/v1/punchIn")
    Result<?> saveBdkRegister(@RequestBody SdkSaveBdkDTO dto);
}
