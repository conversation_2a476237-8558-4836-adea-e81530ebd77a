package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.core.wa.dto.clock.ClockAnalyseDto;
import com.caidaocloud.attendance.sdk.feign.IClockFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class ClockFeignFallBack implements IClockFeignClient {
    @Override
    public Result<Boolean> analyseRegisterRecord(ClockAnalyseDto dto) {
        return Result.fail();
    }
}
