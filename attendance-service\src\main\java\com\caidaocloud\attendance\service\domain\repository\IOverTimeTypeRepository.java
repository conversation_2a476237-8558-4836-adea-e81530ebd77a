package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.wa.mybatis.model.WaOvertimeType;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>Chen
 * @Date: 2021/7/14 19:20
 * @Description:
 **/
public interface IOverTimeTypeRepository {

    void deleteOtTypeByIds(List<Integer> otIds);

    void deleteOtTypeById(Integer otId);

    List<WaOvertimeType> getOtTypes(String belongOrgId, Integer waGroupId, Long date, Boolean summary);

    List<WaOvertimeType> getOvertimeTypeList(String belongOrgId, Long empId, Long date, Integer dateType);

    List<WaOvertimeType> getOvertimeTypeByName(String belongOrgId, Integer waGroupId, String typeName, Integer overtimeTypeId);

    List<WaOvertimeType> getOvertimeTypeByIds(List<Integer> overtimeTypeIds);

    List<WaOvertimeType> getAllOtTypes(String belongOrgId, Integer status, Long curDate);

    List<WaOvertimeType> getOtTypeByNameAndTime(String belongOrgId, Long startDate, Long endDate, String typeName, Integer overtimeTypeId);

    List<WaOvertimeType> getOtTypeByRuleId(Long ruleId);

    List<WaOvertimeType> getOtTypeAnalyseRule(String tenantId, Integer waGroupId);

    List<WaOvertimeType> getOvertimeType(Long empId, Integer dateType, Long endDate);

    List<WaOvertimeType> getOtherTransferRuleOvertimeType(String tenantId);

    List<WaOvertimeType> getOtTypes(String tenantId, Integer overtimeTypeId, Integer dateType, Boolean defaultType, Long date);
}
