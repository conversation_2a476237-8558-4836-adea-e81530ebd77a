<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidao1.ioc.mybatis.mapper.ImportOtherMapper">

    <delete id="delTemplate">
        delete from sys_template_field where template_id = #{templateId}
    </delete>

    <select id="getTableId" resultType="String">
        select table_reg_id from  sys_field_reg where  field_reg_id =any (
            select regexp_split_to_table( field_reg_id, ',' )  from sys_template_field where  template_id = #{templateId}
        ) GROUP BY table_reg_id
    </select>

    <select id="getModelList" resultType="string">
        select  COALESCE(chn_name,'temNull') from  sys_field_reg  where  table_reg_id in (${tableRegId})
                                                                    and belong_org_id = '0'
        GROUP BY chn_name
    </select>

    <select id="getPathUrlFileName" resultType="string">
        select file_url from oss_file where id =#{id} and belong_org_id =#{belongOrgId,jdbcType=VARCHAR} limit 1
    </select>

    <select id="getFuncIdCheck" resultType="integer">
        select import_func_id from sys_import_template where template_id =#{templateId}
    </select>

    <select id="getDetailNameList" resultType="string">
        select payslip_name from pay_structure_detail s1 left join  pay_structure_main s2 on s1.struc_main_id = s2.struc_main_id
        where s2.belong_org_id =#{belongOrgId,jdbcType=VARCHAR} and param_mi =1 and param_r = 2
    </select>
    <select id="getEmpDingTalkInfoList" resultType="com.caidao1.ioc.dto.DingTalkUserIdDto">
        select workno as "workNo", REPLACE(cast(ext_custom_col ->> '${key}' as VARCHAR), '"', '') as "dingTalkUserId"
        from sys_emp_info
        where belong_org_id = #{tenantId}
    </select>
</mapper>