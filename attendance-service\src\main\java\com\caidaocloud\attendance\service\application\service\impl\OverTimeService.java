package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaOvertimeTypeMapper;
import com.caidao1.wa.mybatis.model.WaGroup;
import com.caidao1.wa.mybatis.model.WaOvertimeType;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.CompensateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.service.application.enums.LeaveStatusEnum;
import com.caidaocloud.attendance.service.application.enums.OverTimeTypeStatusEnum;
import com.caidaocloud.attendance.service.application.service.IGroupService;
import com.caidaocloud.attendance.service.application.service.IOverTimeService;
import com.caidaocloud.attendance.service.domain.entity.OverTimeTypeDo;
import com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.OverTimeTypeMapper;
import com.caidaocloud.attendance.service.interfaces.dto.OverTimeDto;
import com.caidaocloud.attendance.service.interfaces.dto.OverTimeTypeReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OverTimeInfoDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OverTimeService implements IOverTimeService {
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private OverTimeTypeMapper overTimeCheckMapper;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private OverTimeTypeDo overTimeTypeDo;
    @Autowired
    private IGroupService groupService;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaEmpOvertimeDo empOvertimeDo;
    @Resource
    private WaOvertimeTypeMapper waOvertimeTypeMapper;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    /**
     * 删除加班规则
     *
     * @param otIds
     */
    @Override
    public void deleteOtTypeByIds(List<Integer> otIds) {
        overTimeTypeDo.deleteOtTypeByIds(otIds);
    }

    @Override
    public List<Map> getOtTypeList(PageBean pageBean) {
        return waConfigService.getOtTypeList(pageBean);
    }

    @Override
    public Result<Boolean> saveOtType(OverTimeDto dto) {
        WaOvertimeType record = ObjectConverter.convert(dto, WaOvertimeType.class);
        if (null != dto.getI18nTypeName()) {
            record.setI18nTypeName(FastjsonUtil.toJson(dto.getI18nTypeName()));
        }
        UserInfo userInfo = this.getUserInfo();
        if (CollectionUtils.isNotEmpty(overTimeTypeDo.getOtTypeByNameAndTime(userInfo.getTenantId(), dto.getStartDate(), dto.getEndDate(), dto.getTypeName(), dto.getOvertimeTypeId()))) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE_NAME_EXISTS, Boolean.FALSE);
        }
        if (null != dto.getDefaultType() && dto.getDefaultType() && CollectionUtils.isNotEmpty(overTimeTypeDo.getOtTypes(userInfo.getTenantId(),
                dto.getOvertimeTypeId(), dto.getDateType(), dto.getDefaultType(), DateUtil.getCurrentTime(true)))) {
            return ResponseWrap.wrapResult(AttendanceCodes.DEFAULT_OVERTIME_TYPE_LIMIT, Boolean.FALSE);
        }
        waConfigService.saveOtType(record);
        return Result.ok(Boolean.TRUE);
    }

    @Override
    public OverTimeDto getOtType(Integer id) {
        WaOvertimeType overtimeType = waConfigService.getOtType(id);
        OverTimeDto dto = ObjectConverter.convert(overtimeType, OverTimeDto.class);
        if (StringUtils.isNotBlank(overtimeType.getI18nTypeName())) {
            dto.setI18nTypeName(FastjsonUtil.toObject(overtimeType.getI18nTypeName(), Map.class));
        } else if (StringUtils.isNotBlank(overtimeType.getTypeName())) {
            Map<String, String> i18nName = new HashMap<>();
            i18nName.put("default", overtimeType.getTypeName());
            dto.setI18nTypeName(i18nName);
        }
        return dto;
    }

    @Override
    public void deleteOtType(Integer id) {
        overTimeTypeDo.deleteById(id);
    }

    @Transactional
    @Override
    public void deleteGroupOtType(Integer groupId, Integer id) {
        WaOvertimeType type = waOvertimeTypeMapper.selectByPrimaryKey(id);
        LogRecordContext.putVariable("name", type.getTypeName());
        overTimeTypeDo.deleteById(id);

        //groupService.deleteGroupOtType(groupId, id);
    }

    @Override
    public List<OverTimeInfoDto> getOtTypes(Integer groupId, Integer status) {
        UserInfo userInfo = sessionService.getUserInfo();
        Long currentTime = DateUtil.getCurrentTime(true);
        List<OverTimeTypeDo> items = overTimeTypeDo.getAllOtTypes(userInfo.getTenantId(), status, currentTime);
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        List<OverTimeInfoDto> list = new ArrayList<>();
        items.forEach(item -> {
            OverTimeInfoDto dto = new OverTimeInfoDto();
            dto.setOvertimeTypeId(item.getOvertimeTypeId());
            if (item.getOvertimeType() != null) {
                dto.setOvertimeType(DateTypeEnum.getName(item.getOvertimeType()));
            }
            if (item.getCompensateType() != null) {
                dto.setCompensateType(CompensateTypeEnum.getDescByOrdinal(item.getCompensateType()));
            }
            if (item.getMinOvertimeUnit() != null) {
                dto.setMinOvertimeUnit(item.getMinOvertimeUnit());
            }
            dto.setMinApplyNum(item.getMinApplyNum());
            dto.setTypeName(LangParseUtil.getI18nLanguage(item.getI18nTypeName(), item.getTypeName()));
            dto.setStartDate(item.getStartDate());
            dto.setEndDate(item.getEndDate());
            Long startDate = item.getStartDate();
            Long endDate = item.getEndDate();
            if (currentTime < startDate) {
                dto.setStatus(OverTimeTypeStatusEnum.PENDING.getIndex());
            } else if (currentTime <= endDate) {
                dto.setStatus(OverTimeTypeStatusEnum.VALID.getIndex());
            } else {
                dto.setStatus(OverTimeTypeStatusEnum.INVALID.getIndex());
            }
            dto.setMaxValidTime(item.getMaxValidTime());
            dto.setEnableState(LeaveStatusEnum.UNENABLE.getIndex());
            list.add(dto);
        });
        //加班是否启用
        if (groupId != null) {
            Optional<WaGroup> optional = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(groupId));
            if (optional.isPresent()) {
                WaGroup waGroup = optional.get();
                if (waGroup.getOtTypeIds() != null) {
                    Integer[] ids = (Integer[]) waGroup.getOtTypeIds();
                    for (Integer id : ids) {
                        for (OverTimeInfoDto dto : list) {
                            if (dto.getOvertimeTypeId().equals(id)) {
                                dto.setEnableState(LeaveStatusEnum.ENABLE.getIndex());
                                break;
                            }
                        }
                    }
                }
            }
        }
        if (null != groupId) {
            return list.stream().sorted(Comparator.comparing(OverTimeInfoDto::getEnableState, Comparator.reverseOrder()).thenComparing(OverTimeInfoDto::getStartDate, Comparator.reverseOrder())).collect(Collectors.toList());
        }
        list.sort(Comparator.comparing(OverTimeInfoDto::getStartDate).reversed());
        return list;
    }

    /**
     * 判断加班类型是否被考勤方案引用
     *
     * @param belongId
     * @param id
     * @return
     */
    @Override
    public Integer getGroupOtRelCount(String belongId, Integer id) {
        return overTimeCheckMapper.getGroupOtRelCount(belongId, id);
    }

    /**
     * 判断休假类型是否被考勤方案引用
     *
     * @param belongId
     * @param id
     * @return
     */
    @Override
    public Integer getGroupLeaveRelCount(String belongId, Integer id) {
        return overTimeCheckMapper.getGroupLeaveRelCount(belongId, id);
    }

    @Override
    public Integer getRelBetweenTypeAndOt(Integer id) {
        return overTimeCheckMapper.countRelBetweenTypeAndOt(id);
    }

    @Override
    public Result<Boolean> enable(OverTimeTypeReqDto dto) {
        WaOvertimeType overtimeType = waConfigService.getOtType(dto.getOvertimeTypeId());
        groupService.updateGroupOvertimeType(dto.getWaGroupId(), dto.getOvertimeTypeId(), overtimeType.getDateType());
        return Result.ok(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> disable(OverTimeTypeReqDto dto) {
        groupService.deleteGroupOtType(dto.getWaGroupId(), dto.getOvertimeTypeId());
        return Result.ok(Boolean.TRUE);
    }

    @Override
    public Boolean checkOvertimeTypeUsed(Integer overtimeTypeId) {
        String tenantId = getUserInfo().getTenantId();
        return empOvertimeDo.checkOvertimeTypeUsed(tenantId, overtimeTypeId) > 0;
    }
}