package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.ISobRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.SobPo;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 考勤周期
 *
 * <AUTHOR>
 * @Date 2021/3/25
 */
@Slf4j
@Data
@Service
public class WaSobDo {
    private Integer waSobId;
    private Long corpid;
    private String belongOrgId;
    private String waSobName;
    private Integer sysPeriodMonth;
    private Integer waGroupId;
    private Long startDate;
    private Long endDate;
    private Long sobEndDate;
    private Long sobCloseDate;
    private Integer status;
    private Boolean isLock = Boolean.valueOf(false);
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;

    @Autowired
    private ISobRepository sobRepository;

    public int getSobCountByGroupId(Integer waGroupId, Long corpId, String belongOrgId) {
        return sobRepository.getSobCountByGroupId(waGroupId, corpId, belongOrgId);
    }

    public List<WaSobDo> getList(String belongOrgId, Integer sobId, String sobName, List<Integer> statusList) {
        return ObjectConverter.convertList(sobRepository.queryList(belongOrgId, sobId, sobName, statusList), WaSobDo.class);
    }

    public List<Long> getEmpIdListByGroup(String belongId, Long currentDate, Boolean isDefault, Integer waGroupId, Long startDate, Long endDate, String dataScope) {
        return sobRepository.getEmpIdListByGroup(belongId, currentDate, isDefault, waGroupId, startDate, endDate, dataScope);
    }

    public WaSobDo getSobByGroupIdAndPeriod(String belongOrgId, Integer groupId, Integer period) {
        SobPo sobPo = sobRepository.getSobByGroupIdAndPeriod(belongOrgId, groupId, period);
        if (null == sobPo) {
            return null;
        }
        return ObjectConverter.convert(sobPo, WaSobDo.class);
    }

    public List<WaSobDo> getWaSobIdByDateRangeAndPeriodMonth(String belongOrgId, Long dateTime, List<Integer> periodMonths, Integer groupId) {
        List<SobPo> list = sobRepository.getWaSobIdByDateRangeAndPeriodMonth(belongOrgId, dateTime, periodMonths, groupId);
        return ObjectConverter.convertList(list, WaSobDo.class);
    }

    public List<WaSobDo> getWaSobByEndDateAndTenantId(String tenantId, Long startDate, Long endDate) {
        List<SobPo> list = sobRepository.getWaSobByEndDateAndTenantId(tenantId, startDate, endDate);
        return ObjectConverter.convertList(list, WaSobDo.class);
    }

    public WaSobDo getById(String belongOrgId, Integer waSobId) {
        return sobRepository.getById(belongOrgId, waSobId);
    }
}
