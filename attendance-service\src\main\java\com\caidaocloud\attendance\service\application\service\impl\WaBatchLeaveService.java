package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.enums.LeaveStatusEnum;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaEmpLeaveMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveTypeMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidao1.wa.service.WaLeaveService;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.dto.WfAttachmentDto;
import com.caidaocloud.attendance.core.workflow.dto.WfBusinessDataDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.emp.EmpOrgPostInfo;
import com.caidaocloud.attendance.service.application.dto.leave.BatchLeaveDetailDto;
import com.caidaocloud.attendance.service.application.dto.leave.BatchLeaveSaveDto;
import com.caidaocloud.attendance.service.application.dto.leave.LeaveApplyResultDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.ILeaveApplyService;
import com.caidaocloud.attendance.service.application.service.ILeaveTypeService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.application.service.emp.WaEmpService;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryQuotaDo;
import com.caidaocloud.attendance.service.domain.entity.WaBatchLeaveDo;
import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveTimeDo;
import com.caidaocloud.attendance.service.domain.service.WaBatchLeaveDomainService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaBatchLeaveMapper;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveApplySaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveTypeDto;
import com.caidaocloud.attendance.service.interfaces.dto.RevokeEmpLeaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.common.WaKeyValue;
import com.caidaocloud.attendance.service.interfaces.dto.leave.BatchLeaveApplyDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.BatchLeaveApplyMultiTypeDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.BatchLeaveQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.RevokeBatchLeaveDto;
import com.caidaocloud.attendance.service.interfaces.vo.BatchLeaveApplyTimeMultiTypeVo;
import com.caidaocloud.attendance.service.interfaces.vo.LeaveApplyTimeVo;
import com.caidaocloud.attendance.service.interfaces.vo.leave.BatchLeavePageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.leave.LeaveDateInfoVo;
import com.caidaocloud.attendance.service.interfaces.vo.quota.EmpQuotaSummaryVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.SysConfigsEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.workflow.enums.WfAttachmentTypeEnum;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.*;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.caidaocloud.attendance.service.infrastructure.common.QuotaTimeFormat.formatFloat;

/**
 * 批量休假
 *
 * <AUTHOR>
 * @Date 2024/6/14
 */
@Slf4j
@Service
public class WaBatchLeaveService {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public static final String CACHE_KEY_FOR_BATCH_LEAVE_APPLY = "CACHE_KEY_FOR_BATCH_LEAVE_APPLY_";

    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaBatchLeaveDomainService waBatchLeaveDomainService;
    @Autowired
    private IWfService wfService;
    @Autowired
    private WorkflowCallBackService workflowCallBackService;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WaLeaveService waLeaveService;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private ILeaveTypeService leaveTypeService;
    @Autowired
    private ILeaveApplyService leaveApplyService;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaBatchLeaveMapper waBatchLeaveMapper;
    @Autowired
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private WaEmpLeaveTimeDo waEmpLeaveTimeDo;
    @Autowired
    private EmpCompensatoryQuotaDo empCompensatoryQuotaDo;
    @Autowired
    private WaEmpService waEmpService;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 获取休假日期列表
     *
     * @param empid
     * @param startDate
     * @param endDate
     * @param leaveTypeId
     * @return
     */
    public List<LeaveDateInfoVo> getLeaveDateList(Long empid, Long startDate, Long endDate, Integer leaveTypeId) throws Exception {
        if (startDate > endDate) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201921", WebUtil.getRequest()));
        }
        try {
            Long yearsEndTime = DateUtilExt.getYearsEndTime(startDate);
            if (endDate > yearsEndTime) {
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201923", WebUtil.getRequest()));
            }
        } catch (ParseException e) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201922", WebUtil.getRequest()));
        }
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
        if (empInfo == null) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, null).getMsg());
        }
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveTypeId);
        if (waLeaveType == null) {
            // 假期类型不存在
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201920", WebUtil.getRequest()));
        }
        Map<Integer, WaShiftDef> shiftMap = waCommonService.getCorpAllShiftDef(empInfo.getBelongOrgId());
        if (MapUtils.isEmpty(shiftMap)) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
        }
        int tmType = (empInfo.getTmType() == null || empInfo.getTmType() == 0) ? 1 : empInfo.getTmType();
        Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(empInfo.getBelongOrgId(),
                empid, tmType, startDate - 86400, endDate, empInfo.getWorktimeType(), true);
        if (MapUtils.isEmpty(pbMap)) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
        }
        List<LeaveDateInfoVo> dateList = new ArrayList<>();
        if (QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType())
                && QuotaTypeEnum.FIXED_QUOTA.getIndex().equals(waLeaveType.getQuotaType())) {
            // 固定额度假期类型
            LeaveDateInfoVo dateInfoVo = new LeaveDateInfoVo();
            dateInfoVo.setStartDate(startDate);
            dateInfoVo.setEndDate(endDate);
            dateInfoVo.setUnit(waLeaveType.getAcctTimeType());
            dateInfoVo.setUnitTxt(PreTimeUnitEnum.getName(dateInfoVo.getUnit()));
            // 计算休假时长
            LeaveApplySaveDto leaveApplySaveDto = new LeaveApplySaveDto();
            leaveApplySaveDto.setEmpid(empInfo.getEmpid());
            leaveApplySaveDto.setLeaveTypeId(waLeaveType.getLeaveTypeId());
            leaveApplySaveDto.setStartTime(startDate);
            leaveApplySaveDto.setEndTime(endDate);
            leaveApplySaveDto.setShowMin(0);
            leaveApplySaveDto.setShowDay(0);
            Result<LeaveApplyTimeVo> leaveApplyTimeVoResult = LeaveApplyTimeVo
                    .doWrapperGetLeaveTotalTime(waLeaveService.getLeaveTotalTime(leaveApplySaveDto), "");
            if (!leaveApplyTimeVoResult.isSuccess()) {
                throw new ServerException(leaveApplyTimeVoResult.getMsg());
            }
            LeaveApplyTimeVo applyTimeVo = leaveApplyTimeVoResult.getData();
            dateInfoVo.setDuration(applyTimeVo.getDurationValue());
            dateInfoVo.setLeaveTypeId(waLeaveType.getLeaveTypeId());
            dateInfoVo.setLeaveTypeName(waLeaveType.getLeaveName());
            dateList.add(dateInfoVo);
            return dateList;
        }
        Long eventDate = startDate;
        while (eventDate <= endDate) {
            if (!pbMap.containsKey(eventDate) || null == pbMap.get(eventDate)) {
                throw new ServerException(DateUtil.getDateStrByTimesamp(eventDate) + MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
            }
            WaWorktimeDetail worktimeDetail = pbMap.get(eventDate);
            if (!shiftMap.containsKey(worktimeDetail.getShiftDefId()) || null == shiftMap.get(worktimeDetail.getShiftDefId())) {
                throw new ServerException(DateUtil.getDateStrByTimesamp(eventDate) + MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
            }
            WaShiftDef shiftDef = shiftMap.get(worktimeDetail.getShiftDefId());
            if (DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType())) {
                worktimeDetail.setDateType(shiftDef.getDateType());
            }
            Integer dateType = worktimeDetail.getDateType();
            boolean allowApply = DateTypeEnum.DATE_TYP_1.getIndex().equals(dateType);
            if (!allowApply) {
                if ((DateTypeEnum.DATE_TYP_2.getIndex().equals(dateType) || DateTypeEnum.DATE_TYP_4.getIndex().equals(dateType))
                        && waLeaveType.getIsRestDay() != null && waLeaveType.getIsRestDay()) {
                    allowApply = true;
                }
                if ((DateTypeEnum.DATE_TYP_3.getIndex().equals(dateType) || DateTypeEnum.DATE_TYP_5.getIndex().equals(dateType))
                        && waLeaveType.getIsLegalHoliday() != null && waLeaveType.getIsLegalHoliday()) {
                    allowApply = true;
                }
            }
            if (allowApply) {
                LeaveDateInfoVo dateInfoVo = new LeaveDateInfoVo();
                dateInfoVo.setStartDate(eventDate);
                dateInfoVo.setEndDate(eventDate);
                dateInfoVo.setUnit(waLeaveType.getAcctTimeType());
                dateInfoVo.setUnitTxt(PreTimeUnitEnum.getName(dateInfoVo.getUnit()));
                if (waLeaveType.getAcctTimeType() == 1) {
                    dateInfoVo.setDuration(1d);
                } else {
                    if (shiftDef.getWorkTotalTime() != null && shiftDef.getWorkTotalTime() > 0) {
                        DecimalFormat f = new DecimalFormat("0.0");
                        f.setRoundingMode(RoundingMode.CEILING);//向正无穷方向舍入。向正最大方向靠拢。若是正数，舍入行为类似于ROUND_UP，若为负数，舍入行为类似于ROUND_DOWN。Math.round()方法就是使用的此模式。
                        double d = Double.parseDouble(String.valueOf(shiftDef.getWorkTotalTime())) / 60;
                        dateInfoVo.setDuration(Double.valueOf(f.format(d)));
                    }
                }
                dateInfoVo.setLeaveTypeId(waLeaveType.getLeaveTypeId());
                dateInfoVo.setLeaveTypeName(waLeaveType.getLeaveName());
                dateList.add(dateInfoVo);
            }
            eventDate = eventDate + 86400;
        }
        return dateList;
    }

    /**
     * 计算休假时长（仅支持选择一种假期类型申请）已过期，请使用支持假期类型支持多选的方法
     *
     * @param leaveApplyDto
     * @param lockKey
     * @return
     * @throws Exception
     */
    @Deprecated
    public LeaveApplyTimeVo calLeaveTime(BatchLeaveApplyDto leaveApplyDto, String lockKey) throws Exception {
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveApplyDto.getLeaveTypeId());
        if (waLeaveType == null) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201920", WebUtil.getRequest()));
        }
        BigDecimal totalTimeDuration = new BigDecimal(0);

        List<LeaveApplySaveDto> leaveList = leaveApplyDto.getLeaveList();
        for (LeaveApplySaveDto leaveApplySaveDto : leaveList) {
            leaveApplySaveDto.setEmpid(leaveApplyDto.getEmpid());
            leaveApplySaveDto.setLeaveTypeId(leaveApplyDto.getLeaveTypeId());
            Result<LeaveApplyTimeVo> leaveApplyTimeVoResult = LeaveApplyTimeVo
                    .doWrapperGetLeaveTotalTime(waLeaveService.getLeaveTotalTime(leaveApplySaveDto), lockKey);
            if (!leaveApplyTimeVoResult.isSuccess()) {
                throw new ServerException(leaveApplyTimeVoResult.getMsg());
            }
            LeaveApplyTimeVo applyTimeVo = leaveApplyTimeVoResult.getData();
            totalTimeDuration = totalTimeDuration.add(BigDecimal.valueOf(applyTimeVo.getOriginalDuration()));
        }

        LeaveApplyTimeVo leaveApplyTimeVo = new LeaveApplyTimeVo();
        leaveApplyTimeVo.setTimeUnit(waLeaveType.getAcctTimeType());
        leaveApplyTimeVo.setTimeUnitTxt(PreTimeUnitEnum.getName(leaveApplyTimeVo.getTimeUnit()));
        leaveApplyTimeVo.setOriginalDuration(totalTimeDuration.doubleValue());
        if (waLeaveType.getAcctTimeType() == 1) {
            leaveApplyTimeVo.setDurationValue(totalTimeDuration.doubleValue());
            leaveApplyTimeVo.setDuration(messageResource.getMessage("L005571", new Object[]{totalTimeDuration}, new Locale(SessionHolder.getLang())));
        } else {
            BigDecimal durationValue = totalTimeDuration.divide(BigDecimal.valueOf(60), 2, BigDecimal.ROUND_HALF_UP);
            leaveApplyTimeVo.setDurationValue(durationValue.doubleValue());
            String duration = totalTimeDuration.floatValue() % 60 > 0
                    ? messageResource.getMessage("L005572", new Object[]{totalTimeDuration.longValue() / 60, totalTimeDuration.longValue() % 60}, new Locale(SessionHolder.getLang()))
                    : messageResource.getMessage("L005573", new Object[]{totalTimeDuration.floatValue() / 60}, new Locale(SessionHolder.getLang()));
            leaveApplyTimeVo.setDuration(duration);
        }
        leaveApplyTimeVo.setSecretKey(null);
        return leaveApplyTimeVo;
    }

    /**
     * 计算休假时长（支持选择多种假期类型申请）
     *
     * @param leaveApplyDto
     * @param lockKey
     * @return
     * @throws Exception
     */
    public BatchLeaveApplyTimeMultiTypeVo calLeaveTimeForMultiType(BatchLeaveApplyMultiTypeDto leaveApplyDto, String lockKey) throws Exception {
        List<LeaveApplySaveDto> leaveList = leaveApplyDto.getLeaveList();
        if (CollectionUtils.isEmpty(leaveList)) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201912", WebUtil.getRequest()));
        }
        List<Integer> leaveTypeIdList = leaveList.stream().map(LeaveApplySaveDto::getLeaveTypeId).distinct().collect(Collectors.toList());
        WaLeaveTypeExample example = new WaLeaveTypeExample();
        WaLeaveTypeExample.Criteria criteria = example.createCriteria();
        criteria.andLeaveTypeIdIn(leaveTypeIdList);
        List<WaLeaveType> leaveTypeList = waLeaveTypeMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(leaveTypeList) || leaveTypeList.size() != leaveTypeIdList.size()) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201920", WebUtil.getRequest()));
        }
        Map<Integer, WaLeaveType> leaveTypeGroupMap = leaveTypeList.stream().collect(Collectors.toMap(WaLeaveType::getLeaveTypeId, Function.identity()));
        BigDecimal totalDay = new BigDecimal(0);
        BigDecimal totalMin = new BigDecimal(0);
        for (LeaveApplySaveDto leaveApplySaveDto : leaveList) {
            WaLeaveType waLeaveType = leaveTypeGroupMap.get(leaveApplySaveDto.getLeaveTypeId());
            if (null == waLeaveType) {
                continue;
            }
            Result<LeaveApplyTimeVo> leaveApplyTimeVoResult = LeaveApplyTimeVo
                    .doWrapperGetLeaveTotalTime(waLeaveService.getLeaveTotalTime(leaveApplySaveDto), lockKey);
            if (!leaveApplyTimeVoResult.isSuccess()) {
                throw new ServerException(leaveApplyTimeVoResult.getMsg());
            }
            LeaveApplyTimeVo applyTimeVo = leaveApplyTimeVoResult.getData();
            if (waLeaveType.getAcctTimeType() == 1) {
                totalDay = totalDay.add(BigDecimal.valueOf(applyTimeVo.getOriginalDuration()));
            } else {
                totalMin = totalMin.add(BigDecimal.valueOf(applyTimeVo.getOriginalDuration()));
            }
        }
        BatchLeaveApplyTimeMultiTypeVo leaveApplyTimeVo = new BatchLeaveApplyTimeMultiTypeVo();
        String dayTxt = "";
        if (totalDay.doubleValue() > 0) {
            dayTxt = messageResource.getMessage("L005571", new Object[]{totalDay}, new Locale(SessionHolder.getLang()));
        }
        String hourTxt = "";
        if (totalMin.doubleValue() > 0) {
            hourTxt = totalMin.floatValue() % 60 > 0
                    ? messageResource.getMessage("L005572", new Object[]{totalMin.longValue() / 60, totalMin.longValue() % 60}, new Locale(SessionHolder.getLang()))
                    : messageResource.getMessage("L005573", new Object[]{totalMin.floatValue() / 60}, new Locale(SessionHolder.getLang()));
        }
        if (StringUtils.isBlank(dayTxt)) {
            leaveApplyTimeVo.setDuration(hourTxt);
        } else if (StringUtils.isBlank(hourTxt)) {
            leaveApplyTimeVo.setDuration(dayTxt);
        } else {
            leaveApplyTimeVo.setDuration(String.format("%s,%s", dayTxt, hourTxt));
        }
        return leaveApplyTimeVo;
    }

    /**
     * 保存休假申请单（仅支持选择一种假期类型申请）已过期，请使用支持假期类型支持多选的方法
     *
     * @param leaveApplyDto
     * @return
     * @throws Exception
     */
    @Deprecated
    public Result saveLeave(BatchLeaveApplyDto leaveApplyDto) throws Exception {
        LeaveTypeDto leaveType = leaveTypeService.getLeaveTypeById(leaveApplyDto.getLeaveTypeId());
        if (null == leaveType) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        for (LeaveApplySaveDto leaveApplySaveDto : leaveApplyDto.getLeaveList()) {
            leaveApplySaveDto.setAcctTimeType(leaveType.getAcctTimeType());
            if (leaveType.getReasonMust() != null && leaveType.getReasonMust() && StringUtil.isBlank(leaveApplySaveDto.getReason())) {
                return ResponseWrap.wrapResult(AttendanceCodes.REASON_MUST, Boolean.FALSE);
            }
            Result<?> checkResult = LeaveApplySaveDto.checkLeaveApplyParams(leaveApplySaveDto, true, leaveType.getLeaveType() == 4);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
        }
        BatchLeaveSaveDto leaveSaveDto = new BatchLeaveSaveDto();
        leaveSaveDto.setEmpid(leaveApplyDto.getEmpid())
                .setEmergencyContact(leaveApplyDto.getEmergencyContact())
                .setFilePath(leaveApplyDto.getFilePath())
                .setFileName(leaveApplyDto.getFileName())
                .setLeaveList(leaveApplyDto.getLeaveList())
                .setLeaveTypeId(leaveType.getLeaveTypeId())
                .setAcctTimeType(leaveType.getAcctTimeType());
        return SpringUtil.getBean(WaBatchLeaveService.class).saveLeaveDetail(leaveSaveDto);
    }

    /**
     * 保存休假申请单（支持选择多种假期类型申请）
     *
     * @param leaveApplyDto
     * @return
     * @throws Exception
     */
    public Result saveLeaveForMultiType(BatchLeaveApplyMultiTypeDto leaveApplyDto) throws Exception {
        List<LeaveApplySaveDto> leaveList = leaveApplyDto.getLeaveList();
        if (CollectionUtils.isEmpty(leaveList)) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201912", WebUtil.getRequest()));
        }
        List<Integer> leaveTypeIdList = leaveList.stream().map(LeaveApplySaveDto::getLeaveTypeId).distinct().collect(Collectors.toList());
        List<LeaveTypeDto> leaveTypeList = leaveTypeService.getLeaveTypeListByIds(leaveTypeIdList);
        if (CollectionUtils.isEmpty(leaveTypeList)) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        Map<Integer, LeaveTypeDto> leaveTypeDtoMap = leaveTypeList.stream()
                .collect(Collectors.toMap(LeaveTypeDto::getLeaveTypeId, Function.identity()));
        for (LeaveApplySaveDto leaveApplySaveDto : leaveList) {
            LeaveTypeDto leaveType = leaveTypeDtoMap.get(leaveApplySaveDto.getLeaveTypeId());
            if (null == leaveType) {
                continue;
            }
            leaveApplySaveDto.setAcctTimeType(leaveType.getAcctTimeType());
            if (leaveType.getReasonMust() != null && leaveType.getReasonMust() && StringUtil.isBlank(leaveApplySaveDto.getReason())) {
                return ResponseWrap.wrapResult(AttendanceCodes.REASON_MUST, Boolean.FALSE);
            }
            Result<?> checkResult = LeaveApplySaveDto.checkLeaveApplyParams(leaveApplySaveDto, true, leaveType.getLeaveType() == 4);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
        }
        BatchLeaveSaveDto leaveSaveDto = new BatchLeaveSaveDto();
        leaveSaveDto.setEmpid(leaveApplyDto.getEmpid())
                .setEmergencyContact(leaveApplyDto.getEmergencyContact())
                .setFilePath(leaveApplyDto.getFilePath())
                .setFileName(leaveApplyDto.getFileName())
                .setLeaveList(leaveList)
                .setMultiLeaveTypeId(leaveApplyDto.getMultiLeaveTypeId());
        if (leaveTypeList.size() == 1) {
            LeaveTypeDto leaveType = leaveTypeList.get(0);
            leaveSaveDto.setLeaveTypeId(leaveType.getLeaveTypeId())
                    .setAcctTimeType(leaveType.getAcctTimeType());
        }
        return SpringUtil.getBean(WaBatchLeaveService.class).saveLeaveDetail(leaveSaveDto);
    }

    /**
     * 保存批量休假单
     *
     * @param leaveSaveDto
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<?> saveLeaveDetail(BatchLeaveSaveDto leaveSaveDto) throws Exception {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(leaveSaveDto.getEmpid());
        if (empInfo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, Boolean.FALSE);
        }
        // 检查批量流程是否已启用
        Result<Boolean> worflowCheckResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.BATCH_LEAVE.getCode());
        if (null == worflowCheckResult || !worflowCheckResult.isSuccess()) {
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
        }
        Boolean workflowEnabled = worflowCheckResult.getData();
        if (!workflowEnabled) {
            if (leaveApplyService.checkSwitchStatus(SysConfigsEnum.LEAVE_WORKFLOW_SWITCH.name(), empInfo.getBelongOrgId())) {
                return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null);
            }
        }
        leaveSaveDto.setWorkflowEnabled(workflowEnabled);

        // 保存休假单
        WaBatchLeaveDo waBatchLeave = saveApply(leaveSaveDto);

        // 缓存休假信息，以供流程发起使用
        Map<String, Object> cacheMap = new HashMap<>();
        cacheMap.put("leaveTypeId", waBatchLeave.getLeaveTypeId());
        cacheMap.put("timeUnit", waBatchLeave.getTimeUnit());
        cacheMap.put("timeDuration", waBatchLeave.getTimeDuration());
        redisTemplate.opsForValue().set(CACHE_KEY_FOR_BATCH_LEAVE_APPLY + waBatchLeave.getBatchId(),
                FastjsonUtil.toJsonStr(cacheMap), 120, TimeUnit.SECONDS);

        // 开启审批流
        String businessKey = String.valueOf(waBatchLeave.getBatchId());
        String wfBusKey = String.format("%s_%s", businessKey, BusinessCodeEnum.BATCH_LEAVE.getCode());
        if (!workflowEnabled) {
            WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
            wfCallbackResultDto.setBusinessKey(wfBusKey);
            wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
            wfCallbackResultDto.setTenantId(waBatchLeave.getTenantId());
            workflowCallBackService.saveBatchLeaveApproval(wfCallbackResultDto);
            return Result.ok(true);
        } else {
            WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
            wfBeginWorkflowDto.setFuncCode(BusinessCodeEnum.BATCH_LEAVE.getCode());
            wfBeginWorkflowDto.setBusinessId(businessKey);
            wfBeginWorkflowDto.setApplicantId(waBatchLeave.getEmpid().toString());
            wfBeginWorkflowDto.setApplicantName(empInfo.getEmpName());
            wfBeginWorkflowDto.setEventTime(waBatchLeave.getStartDate() * 1000);
            wfBeginWorkflowDto.setEventEndTime(waBatchLeave.getEndDate() * 1000);
            wfBeginWorkflowDto.setTimeSlot(waBatchLeave.getTimeSlot().replaceAll(" -> ", "~"));
            return startWorkflow(wfBeginWorkflowDto, waBatchLeave);
        }
    }

    public Result startWorkflow(WfBeginWorkflowDto wfBeginWorkflowDto, WaBatchLeaveDo waBatchLeave) {
        Result<?> result = null;
        try {
            result = wfRegisterFeign.begin(wfBeginWorkflowDto);
            log.debug("BatchLeave startWorkflow result={}", FastjsonUtil.toJsonStr(result));
        } catch (Exception e) {
            log.error("{}", e.getMessage(), e);
            if (e instanceof ServerException) {
                if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                    throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
                } else {
                    throw e;
                }
            } else {
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
            }
        }
        if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, null).getMsg());
        }
        return Result.ok(true);
    }

    @Transactional(rollbackFor = Exception.class)
    public WaBatchLeaveDo saveApply(BatchLeaveSaveDto leaveSaveDto) throws Exception {
        Long batchId = snowflakeUtil.createId();
        // 请假明细
        Long startTime = 0L;
        Long endTime = 0L;
        BigDecimal totalDay = new BigDecimal(0);
        BigDecimal totalMin = new BigDecimal(0);

        for (LeaveApplySaveDto leaveApplySaveDto : leaveSaveDto.getLeaveList()) {
            leaveApplySaveDto.setEmpid(leaveApplySaveDto.getEmpid());
            leaveApplySaveDto.setIfBtch(true);
            leaveApplySaveDto.setUpdateQuotaWhenBatch(leaveSaveDto.getWorkflowEnabled());
            leaveApplySaveDto.setBatchId(batchId);
            leaveApplySaveDto.setFileName(leaveSaveDto.getFileName());
            leaveApplySaveDto.setFile(leaveSaveDto.getFilePath());

            Result<?> leaveApplyResult = leaveApplyService.saveLeaveApply(leaveApplySaveDto, true);
            if (!leaveApplyResult.isSuccess()) {
                throw new ServerException(leaveApplyResult.getMsg());
            }
            LeaveApplyResultDto applyResultDto = (LeaveApplyResultDto) leaveApplyResult.getData();
            BigDecimal leaveTimeDuration = BigDecimal.valueOf(applyResultDto.getTotalTimeDuration());
            if (leaveApplySaveDto.getAcctTimeType() == 1) {
                totalDay = totalDay.add(leaveTimeDuration);
            } else {
                totalMin = totalMin.add(leaveTimeDuration);
            }
            if (startTime == 0 || startTime > leaveApplySaveDto.getStartTime()) {
                startTime = leaveApplySaveDto.getStartTime();
            }
            if (endTime < leaveApplySaveDto.getEndTime()) {
                endTime = leaveApplySaveDto.getEndTime();
            }
        }

        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(endTime * 1000));
        String startDateYmd = DateUtil.getDateStrByTimesamp(startDate);
        String endDateYmd = DateUtil.getDateStrByTimesamp(endDate);
        String timeSlot = startDateYmd + " -> " + endDateYmd;

        // 批量单据
        WaBatchLeaveDo waBatchLeave = new WaBatchLeaveDo();
        waBatchLeave.setBatchId(batchId);
        waBatchLeave.setEmpid(leaveSaveDto.getEmpid());
        waBatchLeave.setStartDate(startDate);
        waBatchLeave.setEndDate(endDate);
        waBatchLeave.setEmergencyContact(leaveSaveDto.getEmergencyContact());
        waBatchLeave.setFileName(leaveSaveDto.getFileName());
        waBatchLeave.setFilePath(leaveSaveDto.getFilePath());
        waBatchLeave.setTimeSlot(timeSlot);
        waBatchLeave.setTimeUnit(leaveSaveDto.getAcctTimeType());
        waBatchLeave.setLeaveTypeId(leaveSaveDto.getLeaveTypeId());
        waBatchLeave.setMultiLeaveTypeId(leaveSaveDto.getMultiLeaveTypeId());
        if (totalDay.floatValue() > 0 && totalMin.floatValue() > 0) {
            waBatchLeave.setTimeDuration(0f);
            BigDecimal totalHour = totalMin.divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
            waBatchLeave.setTimeDurationTxt(totalDay + ":" + PreTimeUnitEnum.DAY.getIndex()
                    + ","
                    + totalHour.floatValue() + ":" + PreTimeUnitEnum.HOUR.getIndex());
        } else {
            if (totalDay.floatValue() > 0) {
                float timeDuration = totalDay.floatValue();
                waBatchLeave.setTimeDuration(timeDuration);
                waBatchLeave.setTimeDurationTxt(timeDuration + ":" + PreTimeUnitEnum.DAY.getIndex());
            } else {
                float timeDuration = totalMin.floatValue();
                waBatchLeave.setTimeDuration(timeDuration);
                BigDecimal totalHour = totalMin.divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                waBatchLeave.setTimeDurationTxt(totalHour.floatValue() + ":" + PreTimeUnitEnum.HOUR.getIndex());
            }
        }
        waBatchLeave.setBusinessKey(String.format("%s_%s", batchId, BusinessCodeEnum.BATCH_LEAVE.getCode()));
        waBatchLeave.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        waBatchLeave.doInitCrtField();
        waBatchLeaveDomainService.save(waBatchLeave);
        return waBatchLeave;
    }

    /**
     * 分页列表查询
     *
     * @param pageBean
     * @param dto
     * @return
     */
    public PageList<BatchLeavePageListVo> getPageList(PageBean pageBean, BatchLeaveQueryDto dto, UserInfo userInfo) {
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("tenantId", userInfo.getTenantId());
        queryParam.put("keywords", pageBean.getKeywords());
        queryParam.put("datafilter", dto.getDataScope());
        queryParam.put("empid", dto.getEmpid());
        doParseFilterField(pageBean, queryParam);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "ei.orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
//        if (filter == null || !filter.contains("status")) {
//            filter = filter+"and \"status\" in ('1')" ;
//        }
        queryParam.put("filter", filter);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "crttime.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        PageList<Map> pageList = waBatchLeaveMapper.selectPageList(pageBounds, queryParam);
        if (CollectionUtils.isEmpty(pageList)) {
            return new PageList<>(Lists.newArrayList(), pageList.getPaginator());
        }
        List<Integer> leaveTypeIdList = new ArrayList<>();
        for (Map data : pageList) {
            Integer leaveTypeId = (Integer) data.get("leave_type_id");
            if (null != leaveTypeId) {
                leaveTypeIdList.add(leaveTypeId);
            }
            String multiLeaveTypeId = (String) data.get("multi_leave_type_id");
            if (StringUtils.isNotBlank(multiLeaveTypeId)) {
                List<Integer> leaveTypeIds = Arrays.stream(multiLeaveTypeId.split(","))
                        .map(Integer::parseInt).distinct().collect(Collectors.toList());
                leaveTypeIdList.addAll(leaveTypeIds);
            }
        }
        leaveTypeIdList = leaveTypeIdList.stream().distinct().collect(Collectors.toList());
        List<LeaveTypeDto> leaveTypeList = leaveTypeService.getLeaveTypeListByIds(leaveTypeIdList);
        Map<Integer, String> leaveTypeNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(leaveTypeList)) {
            leaveTypeList.forEach(leaveType -> {
                String leaveTypeName = LangParseUtil.getI18nLanguage(FastjsonUtil.toJson(leaveType.getI18nLeaveName()),
                        leaveType.getLeaveName());
                leaveTypeNameMap.put(leaveType.getLeaveTypeId(), leaveTypeName);
            });
        }

        List<BatchLeavePageListVo> listVos = pageList.stream().map(data -> {
            BatchLeavePageListVo listVo = new BatchLeavePageListVo();
            listVo.setBatchId((Long) data.get("batch_id"));
            listVo.setWorkno((String) data.get("workno"));
            listVo.setEmpName((String) data.get("emp_name"));
            listVo.setOrgName((String) data.get("orgName"));

            String multiLeaveTypeId = (String) data.get("multi_leave_type_id");
            if (StringUtils.isNotBlank(multiLeaveTypeId)) {
                List<Integer> leaveTypeIds = Arrays.stream(multiLeaveTypeId.split(","))
                        .map(Integer::parseInt).distinct().collect(Collectors.toList());
                List<String> leaveTypeTxtList = leaveTypeIds.stream()
                        .map(id -> Optional.ofNullable(leaveTypeNameMap.get(id)).orElse("")).collect(Collectors.toList());
                listVo.setLeaveTypeName(StringUtils.join(leaveTypeTxtList, ","));

                String timeDurationTxt = (String) data.get("time_duration_txt");
                if (StringUtils.isNotBlank(timeDurationTxt)) {
                    timeDurationTxt = timeDurationTxt.replaceAll(":" + PreTimeUnitEnum.DAY.getIndex(),
                            PreTimeUnitEnum.getName(PreTimeUnitEnum.DAY.getIndex()));
                    timeDurationTxt = timeDurationTxt.replaceAll(":" + PreTimeUnitEnum.HOUR.getIndex(),
                            PreTimeUnitEnum.getName(PreTimeUnitEnum.HOUR.getIndex()));
                    listVo.setTimeDuration(timeDurationTxt);
                }
            } else {
                Integer leaveTypeId = (Integer) data.get("leave_type_id");
                listVo.setLeaveTypeName(leaveTypeNameMap.get(leaveTypeId));

                /*listVo.setLeaveTypeName((String) data.get("leave_name"));
                if (null != data.get("i18n_leave_name")) {
                    String i18n = LangParseUtil.getI18nLanguage(data.get("i18n_leave_name").toString(), null);
                    if (StringUtil.isNotBlank(i18n)) {
                        listVo.setLeaveTypeName(i18n);
                    }
                }*/

                Integer timeUnit = (Integer) data.get("time_unit");
                Float timeDuration = Float.valueOf(data.get("time_duration").toString());
                if (timeUnit == 1) {
                    listVo.setTimeDuration(timeDuration + PreTimeUnitEnum.getName(timeUnit));
                } else {
                    BigDecimal v = new BigDecimal(String.valueOf(timeDuration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    listVo.setTimeDuration(v.floatValue() + PreTimeUnitEnum.getName(timeUnit));
                }
            }

            listVo.setTimeSlot((String) data.get("time_slot"));
            listVo.setCreateDate((Long) data.get("crttime"));
            Integer status = (Integer) data.get("status");
            listVo.setStatus(status);
            listVo.setStatusName(ApprovalStatusEnum.getName(status));
            listVo.setLastApprovalTime((Long) data.get("last_approval_time"));
            listVo.setBusinessKey((String) data.get("business_key"));
            return listVo;
        }).collect(Collectors.toList());
        return new PageList<>(listVos, pageList.getPaginator());
    }

    private void doParseFilterField(PageBean pageBean, Map<String, Object> queryParam) {
        if (CollectionUtils.isEmpty(pageBean.getFilterList())) {
            return;
        }
        Iterator<FilterBean> it = pageBean.getFilterList().iterator();
        while (it.hasNext()) {
            FilterBean filterBean = it.next();
            if ("eventtime".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    queryParam.put("startDate", Long.valueOf(filterBean.getMin()));
                }
                if (StringUtils.isNotBlank(filterBean.getMax())) {
                    queryParam.put("endDate", Long.valueOf(filterBean.getMax()));
                }
                it.remove();
            } else if ("crttime".equals(filterBean.getField()) || "last_approval_time".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    filterBean.setMin(Long.parseLong(filterBean.getMin()) * 1000 + "");
                }
                if (StringUtils.isNotBlank(filterBean.getMax())) {
                    filterBean.setMax(Long.parseLong(filterBean.getMax()) * 1000 + "");
                }
            }
        }
    }

    public void preHandleFilterField(PageBean pageBean) {
        if (CollectionUtils.isEmpty(pageBean.getFilterList())) {
            return;
        }
        Iterator<FilterBean> it = pageBean.getFilterList().iterator();
        while (it.hasNext()) {
            FilterBean filterBean = it.next();
            if ("eventtime".equals(filterBean.getField())
                    || "crttime".equals(filterBean.getField())
                    || "last_approval_time".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    filterBean.setMin(String.valueOf(Long.parseLong(filterBean.getMin()) / 1000));
                }
                if (StringUtils.isNotBlank(filterBean.getMax())) {
                    filterBean.setMax(String.valueOf(Long.parseLong(filterBean.getMax()) / 1000));
                }
            }
        }
    }

    /**
     * 撤销
     *
     * @param dto
     * @return
     * @throws Exception
     */
    public Result revoke(RevokeBatchLeaveDto dto) throws Exception {
        UserInfo userInfo = sessionService.getUserInfo();
        WaBatchLeaveDo waBatchLeave = waBatchLeaveDomainService.getById(dto.getBatchId());
        if (null == waBatchLeave) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TIME_NOT_EXIST, Boolean.FALSE);
        }
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(waBatchLeave.getStatus())) {
            return ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE);
        }
        if (waBatchLeave.getStatus().intValue() != LeaveStatusEnum.LEAVE_STATUS_1.value && waBatchLeave.getStatus().intValue() != LeaveStatusEnum.LEAVE_STATUS_2.value) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_REVOKE_NOT_ALLOW, Boolean.FALSE);
        }
        WaEmpLeaveExample empLeaveExample = new WaEmpLeaveExample();
        empLeaveExample.createCriteria().andBatchIdEqualTo(waBatchLeave.getBatchId())
                .andTenantIdEqualTo(waBatchLeave.getTenantId());
        List<WaEmpLeave> waEmpLeaveList = waEmpLeaveMapper.selectByExample(empLeaveExample);
        if (CollectionUtils.isEmpty(waEmpLeaveList)) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TIME_NOT_EXIST, Boolean.FALSE);
        }
        RevokeEmpLeaveDto leaveRevokeDto = new RevokeEmpLeaveDto();
        leaveRevokeDto.setRecokeReason(dto.getRevokeReason());
        for (WaEmpLeave waEmpLeave : waEmpLeaveList) {
            if (ApprovalStatusEnum.REVOKED.getIndex().equals(waEmpLeave.getStatus().intValue())) {
                continue;
            }
            leaveRevokeDto.setLeaveId(waEmpLeave.getLeaveId());
            leaveApplyService.revokeEmpLeave(leaveRevokeDto, userInfo, true, true);
        }
        // 保存撤销原因
        waBatchLeave.setRevokeReason(dto.getRevokeReason());
        waBatchLeave.setUpdateBy(UserContext.getUserId());
        waBatchLeave.setUpdateTime(DateUtil.getCurrentTime(true));
        waBatchLeaveDomainService.updateById(waBatchLeave);

        Integer status = waBatchLeave.getStatus();
        if (ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(status)) {
            WfRevokeDto revokeDto = new WfRevokeDto();
            revokeDto.setBusinessKey(waBatchLeave.getBusinessKey());
            Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
            if (null == result || !result.isSuccess()) {
                // 工作流撤销异常
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201919", WebUtil.getRequest()));
            }
        } else if (ApprovalStatusEnum.PASSED.getIndex().equals(status)) {
            WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
            wfCallbackResultDto.setBusinessKey(waBatchLeave.getBusinessKey());
            wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.REVOKE);
            wfCallbackResultDto.setTenantId(waBatchLeave.getTenantId());
            workflowCallBackService.saveBatchLeaveApproval(wfCallbackResultDto);
        }

        return Result.ok(Boolean.TRUE);
    }

    /**
     * 详情（审批页面使用）
     *
     * @param businessId
     * @param summary
     * @return
     */
    public WfResponseDto getWfDetail(Long businessId, boolean summary) {
        log.info("batchLeave getWfDetail businessId={}, summary={}", businessId, summary);
        WfResponseDto responseDto = new WfResponseDto();
        Optional<WaBatchLeaveDo> optional = Optional.ofNullable(waBatchLeaveDomainService.getById(businessId));
        if (!optional.isPresent()) {
            return responseDto;
        }
        WaBatchLeaveDo waBatchLeave = optional.get();

        boolean multiFlag = false;
        String leaveTypeTxt;
        Integer leaveTypeId = null;
        List<Integer> leaveTypeIdList = null;
        Map<Integer, String> leaveTypeNameMap = new HashMap<>();
        if (null != waBatchLeave.getLeaveTypeId()) {
            LeaveTypeDto leaveType = leaveTypeService.getLeaveTypeById(waBatchLeave.getLeaveTypeId());
            if (null == leaveType) {
                return responseDto;
            }
            leaveTypeId = leaveType.getLeaveTypeId();
            leaveTypeTxt = LangParseUtil.getI18nLanguage(FastjsonUtil.toJson(leaveType.getI18nLeaveName()),
                    leaveType.getLeaveName());
            leaveTypeNameMap.put(leaveTypeId, leaveTypeTxt);
        } else if (StringUtils.isNotBlank(waBatchLeave.getMultiLeaveTypeId())) {
            leaveTypeIdList = Arrays.stream(waBatchLeave.getMultiLeaveTypeId().split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            List<LeaveTypeDto> leaveTypeList = leaveTypeService.getLeaveTypeListByIds(leaveTypeIdList);
            if (CollectionUtils.isEmpty(leaveTypeList)) {
                return responseDto;
            }
            List<String> leaveTypeTxtList = new ArrayList<>();
            leaveTypeList.forEach(leaveType -> {
                String leaveTypeName = LangParseUtil.getI18nLanguage(FastjsonUtil.toJson(leaveType.getI18nLeaveName()),
                        leaveType.getLeaveName());
                leaveTypeTxtList.add(leaveTypeName);
                leaveTypeNameMap.put(leaveType.getLeaveTypeId(), leaveTypeName);
            });
            leaveTypeTxt = StringUtils.join(leaveTypeTxtList, ",");
            multiFlag = true;
        } else {
            return responseDto;
        }

        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(waBatchLeave.getEmpid());
        if (empInfo == null) {
            return responseDto;
        }

        EmpOrgPostInfo empOrgPostInfo = waEmpService.getEmpOrgPostInfo(String.valueOf(empInfo.getEmpid()));

        List<WfBusinessDataDetailDto> dataList = new ArrayList<>();
        dataList.add(new WfBusinessDataDetailDto("name", ResponseWrap.wrapResult(AttendanceCodes.EMP_NAME, null).getMsg(), empInfo.getEmpName(), null));
        dataList.add(new WfBusinessDataDetailDto("workno", ResponseWrap.wrapResult(AttendanceCodes.WORK_NO, null).getMsg(), empInfo.getWorkno(), null));
        dataList.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG, null).getMsg(), empOrgPostInfo.getOrg(), null));
        dataList.add(new WfBusinessDataDetailDto("post", ResponseWrap.wrapResult(AttendanceCodes.POSITION, null).getMsg(), empOrgPostInfo.getPost(), null));
        dataList.add(new WfBusinessDataDetailDto("job", ResponseWrap.wrapResult(AttendanceCodes.POST, null).getMsg(), empOrgPostInfo.getJob(), null));
        dataList.add(new WfBusinessDataDetailDto("jobGrade", ResponseWrap.wrapResult(AttendanceCodes.POSITION_LEVEL, null).getMsg(), empOrgPostInfo.getJobGrade(), null));
        dataList.add(new WfBusinessDataDetailDto("emergencyContact", ResponseWrap.wrapResult(AttendanceCodes.EMERGENCY_CONTACT, null).getMsg(), waBatchLeave.getEmergencyContact(), null));
        dataList.add(new WfBusinessDataDetailDto("leaveType", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE, null).getMsg(), leaveTypeTxt, null));
        dataList.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(waBatchLeave.getStartDate()), null));
        dataList.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(waBatchLeave.getEndDate()), null));
        if (!summary) {
            // 查询请假明细
            WaEmpLeaveExample empLeaveExample = new WaEmpLeaveExample();
            empLeaveExample.createCriteria().andBatchIdEqualTo(waBatchLeave.getBatchId())
                    .andTenantIdEqualTo(waBatchLeave.getTenantId());
            List<WaEmpLeave> waEmpLeaveList = waEmpLeaveMapper.selectByExample(empLeaveExample);
            if (CollectionUtils.isNotEmpty(waEmpLeaveList)) {
                List<Integer> leaveIds = waEmpLeaveList.stream().map(WaEmpLeave::getLeaveId).distinct().collect(Collectors.toList());
                List<WaEmpLeaveTimeDo> empLeaveTimeDoList = waEmpLeaveTimeDo.getListByIds(leaveIds);
                Map<Integer, WaEmpLeaveTimeDo> empLeaveTimeDoMap = empLeaveTimeDoList.stream()
                        .collect(Collectors.toMap(WaEmpLeaveTimeDo::getLeaveId, Function.identity(), (v1, v2) -> v2));
                Map<Integer, Short> leaveStatusMap = waEmpLeaveList.stream()
                        .collect(Collectors.toMap(WaEmpLeave::getLeaveId, WaEmpLeave::getStatus));

                List<BatchLeaveDetailDto> detailDtoList = waEmpLeaveList.stream().map(o -> {
                    WaEmpLeaveTimeDo empLeaveTimeDo = empLeaveTimeDoMap.get(o.getLeaveId());
                    Integer period = Integer.valueOf(empLeaveTimeDo.getPeriodType());
                    Long start = empLeaveTimeDo.getStartTime();
                    Long end = empLeaveTimeDo.getEndTime();

                    BatchLeaveDetailDto detailDto = new BatchLeaveDetailDto();
                    detailDto.setLeaveTypeId(o.getLeaveTypeId());
                    detailDto.setLeaveTypeName(leaveTypeNameMap.get(o.getLeaveTypeId()));
                    detailDto.setReason(o.getReason());
                    Short status = leaveStatusMap.get(o.getLeaveId());
                    detailDto.setStatus(status);
                    detailDto.setStatusTxt(ApprovalStatusEnum.getName(status.intValue()));
                    Float duration = empLeaveTimeDo.getTimeDuration();
                    if (empLeaveTimeDo.getTimeUnit() == 1) {//天
                        detailDto.setTimeDuration(duration + PreTimeUnitEnum.getName(empLeaveTimeDo.getTimeUnit()));
                    } else {//小时
                        BigDecimal totalMonthDecimal = (new BigDecimal(duration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                        detailDto.setTimeDuration(totalMonthDecimal.floatValue() + PreTimeUnitEnum.getName(empLeaveTimeDo.getTimeUnit()));
                    }
                    if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
                        detailDto.setStartDate(DateUtil.getDateStrByTimesamp(start));
                        detailDto.setEndDate(DateUtil.getDateStrByTimesamp(end));
                    } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
                        String startTime = DateUtil.getTimeStrByTimesamp4(start);
                        String endTime = DateUtil.getTimeStrByTimesamp4(end);
                        detailDto.setStartDate(startTime.substring(0, 10).trim());
                        detailDto.setEndDate(endTime.substring(0, 10).trim());
                        detailDto.setStartTime(startTime.substring(10).trim());
                        detailDto.setEndTime(endTime.substring(10).trim());
                    } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
                        detailDto.setStartDate(DateUtil.getDateStrByTimesamp(start));
                        detailDto.setEndDate(DateUtil.getDateStrByTimesamp(end));
                        detailDto.setStartTime(DayHalfTypeEnum.getDesc(empLeaveTimeDo.getShalfDay()));
                        detailDto.setEndTime(DayHalfTypeEnum.getDesc(empLeaveTimeDo.getEhalfDay()));
                    }
                    return detailDto;
                }).collect(Collectors.toList());

                dataList.add(new WfBusinessDataDetailDto("batchLeaveDetail",
                        ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TIME, null).getMsg(), detailDtoList, null));
            }
            // 查询配额
            if (multiFlag) {
                List<EmpQuotaSummaryVo> empQuotaList = this.getEmpQuotaList(empInfo.getEmpid(), leaveTypeIdList);
                dataList.add(new WfBusinessDataDetailDto("batchLeaveQuota",
                        ResponseWrap.wrapResult(AttendanceCodes.LEAVE_QUOTA, null).getMsg(), empQuotaList, null));
            } else {
                EmpQuotaSummaryVo empQuota = this.getEmpQuota(empInfo.getEmpid(), leaveTypeId);
                dataList.add(new WfBusinessDataDetailDto("batchLeaveQuota",
                        ResponseWrap.wrapResult(AttendanceCodes.LEAVE_QUOTA, null).getMsg(), empQuota, null));
            }
            // 附件字段
            if (StringUtils.isNotBlank(waBatchLeave.getFilePath())) {
                List<String> fileNameList = Arrays.stream(waBatchLeave.getFileName().split(",")).collect(Collectors.toList());
                List<String> filePathList = Arrays.stream(waBatchLeave.getFilePath().split(",")).collect(Collectors.toList());
                List<WfAttachmentDto> fileList = new ArrayList<>();
                for (int i = 0; i < filePathList.size(); i++) {
                    WfAttachmentDto attachmentDto = new WfAttachmentDto();
                    attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                    attachmentDto.setFileUrl(filePathList.get(i));
                    attachmentDto.setFileName(fileNameList.get(i));
                    fileList.add(attachmentDto);
                }
                dataList.add(new WfBusinessDataDetailDto("file", ResponseWrap.wrapResult(AttendanceCodes.ATTACHMENT, null).getMsg(), null, fileList));
            }
        }
        dataList.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), waBatchLeave.getProcessCode() == null ? "-" : waBatchLeave.getProcessCode(), null));
        responseDto.setDetailList(dataList);
        return responseDto;
    }

    /**
     * 详情（业务列表页面使用）
     *
     * @param businessId
     * @return
     */
    public WfDetailDto getWfDetailDto(Long businessId) {
        WfResponseDto wfResponseDto = getWfDetail(businessId, false);
        if (CollectionUtils.isEmpty(wfResponseDto.getDetailList())) {
            return new WfDetailDto();
        }
        List<String> fileNameList = Lists.newArrayList();
        List<String> filePathList = Lists.newArrayList();
        List<KeyValue> items = Lists.newArrayList();
        String orgFullPath = "";
        for (WfBusinessDataDetailDto detailDto : wfResponseDto.getDetailList()) {
            if (detailDto.getKey().equals("batchLeaveDetail") || detailDto.getKey().equals("batchLeaveQuota")) {
                items.add(new WaKeyValue(detailDto.getText(), detailDto.getValue(), detailDto.getKey()));
            } else if (detailDto.getKey().equals("file") && CollectionUtils.isNotEmpty(detailDto.getFileList())) {
                List<WfAttachmentDto> fileList = detailDto.getFileList();
                fileList.forEach(fileObj -> {
                    fileNameList.add(fileObj.getFileName());
                    filePathList.add(fileObj.getFileUrl());
                });
            } else {
                items.add(new KeyValue(detailDto.getText(), detailDto.getValue()));
                if (detailDto.getKey().equals("org") && null != detailDto.getValue()) {
                    orgFullPath = detailDto.getValue().toString();
                }
            }
        }
        WfDetailDto detailDto = new WfDetailDto();
        if (CollectionUtils.isNotEmpty(fileNameList)) {
            detailDto.setFiles(StringUtils.join(filePathList, ","));
            detailDto.setFileNames(StringUtils.join(fileNameList, ","));
        }
        detailDto.setFullPath(orgFullPath);
        detailDto.setItems(items);
        return detailDto;
    }

    /**
     * 查询员工单个假期配额 已过期，请使用支持假期类型支持多选的方法
     *
     * @param empid
     * @param leaveTypeId
     * @return
     */
    @Deprecated
    public EmpQuotaSummaryVo getEmpQuota(Long empid, Integer leaveTypeId) {
        Map params = new HashMap();
        params.put("empids", Lists.newArrayList(empid));
        params.put("today", DateUtil.getOnlyDate(new Date()));
        params.put("leaveTypeId", leaveTypeId);
        List<Map> quotaList = empCompensatoryQuotaDo.queryEmpQuotaList(params);
        if (CollectionUtils.isEmpty(quotaList)) {
            return null;
        }
        return parseQuotaMap(quotaList);
    }

    /**
     * 查询员工多个假期配额
     *
     * @param empid
     * @param leaveTypeIds
     * @return
     */
    public List<EmpQuotaSummaryVo> getEmpQuotaList(Long empid, List<Integer> leaveTypeIds) {
        Map params = new HashMap();
        params.put("empids", Lists.newArrayList(empid));
        params.put("today", DateUtil.getOnlyDate(new Date()));
        params.put("leaveTypeIds", leaveTypeIds);
        List<Map> empQuotaList = empCompensatoryQuotaDo.queryEmpQuotaList(params);
        if (CollectionUtils.isEmpty(empQuotaList)) {
            return Lists.newArrayList();
        }
        Map<Integer, List<Map>> leaveTypeQuotaMap = empQuotaList.stream().collect(Collectors.groupingBy(o -> Integer.valueOf(o.get("leave_type_id").toString())));
        List<EmpQuotaSummaryVo> empQuotaSummaryVoList = new ArrayList<>();
        leaveTypeQuotaMap.forEach((leaveTypeId, quotaList) -> {
            EmpQuotaSummaryVo empQuotaSummaryVo = parseQuotaMap(quotaList);
            empQuotaSummaryVo.setLeaveTypeId(leaveTypeId);
            Map quotaMap = quotaList.get(0);
            empQuotaSummaryVo.setLeaveTypeName((String) quotaMap.get("leave_name"));
            if (null != quotaMap.get("i18n_leave_name")) {
                String i18n = LangParseUtil.getI18nLanguage(quotaMap.get("i18n_leave_name").toString(), null);
                if (StringUtil.isNotBlank(i18n)) {
                    empQuotaSummaryVo.setLeaveTypeName(i18n);
                }
            }
            empQuotaSummaryVoList.add(empQuotaSummaryVo);
        });
        return empQuotaSummaryVoList;
    }

    /**
     * 员工假期配额解析计算
     *
     * @param quotaList
     * @return
     */
    private EmpQuotaSummaryVo parseQuotaMap(List<Map> quotaList) {
        // 本年配额、本年已用、本年剩余、申请中
        BigDecimal totalQuota = new BigDecimal(0);
        BigDecimal totalUsed = new BigDecimal(0);
        BigDecimal totalSurplus = new BigDecimal(0);
        BigDecimal totalInTransit = new BigDecimal(0);

        for (Map leaveMap : quotaList) {
            //本年额度
            BigDecimal quotaDay = BigDecimal.valueOf((Float) leaveMap.get("quota_day"));
            //本年已使用
            BigDecimal usedDay = BigDecimal.valueOf((Float) leaveMap.get("used_day"));
            //调整配额
            BigDecimal adjustQuota = BigDecimal.valueOf((Float) leaveMap.get("adjust_quota"));
            //调整已使用
            BigDecimal fixUsedDay = BigDecimal.valueOf((Float) leaveMap.get("fix_used_day"));
            //在途配额
            BigDecimal inTransitQuota = BigDecimal.valueOf((Float) leaveMap.get("in_transit_quota"));
            //发放类型
            Integer quotaType = (Integer) leaveMap.get("quota_type");
            if (QuotaTypeEnum.ISSUED_ANNUALLY.getIndex().equals(quotaType) || QuotaTypeEnum.PARENTAL_LEAVE.getIndex().equals(quotaType)) {
                totalQuota = totalQuota.add(quotaDay).add(adjustQuota);
                totalUsed = totalUsed.add(usedDay).add(fixUsedDay);
                totalInTransit = totalInTransit.add(inTransitQuota);

                BigDecimal surplus = quotaDay.add(adjustQuota).subtract(usedDay).subtract(fixUsedDay).subtract(inTransitQuota);
                totalSurplus = totalSurplus.add(surplus);
            } else if (QuotaTypeEnum.FIXED_QUOTA.getIndex().equals(quotaType)) {
                totalQuota = totalQuota.add(quotaDay).add(adjustQuota);
                totalUsed = totalUsed.add(usedDay).add(fixUsedDay);
                totalInTransit = totalInTransit.add(inTransitQuota);

                BigDecimal surplus = quotaDay.add(adjustQuota).subtract(usedDay).subtract(fixUsedDay).subtract(inTransitQuota);
                if (usedDay.compareTo(BigDecimal.ZERO) > 0 || inTransitQuota.compareTo(BigDecimal.ZERO) > 0) {
                    // 针对固定额度，只要有已使用，则变为 0
                    surplus = BigDecimal.ZERO;
                }

                totalSurplus = totalSurplus.add(surplus);
            } else if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(quotaType)) {
                totalQuota = totalQuota.add(quotaDay).add(adjustQuota);
                totalUsed = totalUsed.add(usedDay);
                totalInTransit = totalInTransit.add(inTransitQuota);

                BigDecimal surplus = quotaDay.add(adjustQuota).subtract(usedDay).subtract(inTransitQuota);
                totalSurplus = totalSurplus.add(surplus);
            }
        }
        Integer acctTimeType = (Integer) quotaList.get(0).get("acct_time_type");
        EmpQuotaSummaryVo empQuotaSummaryVo = new EmpQuotaSummaryVo();
        empQuotaSummaryVo.setTotalQuota(formatFloat(acctTimeType, totalQuota.floatValue(), 1));
        empQuotaSummaryVo.setTotalUsed(formatFloat(acctTimeType, totalUsed.floatValue(), 1));
        empQuotaSummaryVo.setTotalSurplus(formatFloat(acctTimeType, totalSurplus.floatValue(), 1));
        empQuotaSummaryVo.setTotalInTransit(formatFloat(acctTimeType, totalInTransit.floatValue(), 1));
        empQuotaSummaryVo.setUnit(acctTimeType);
        empQuotaSummaryVo.setUnitTxt(PreTimeUnitEnum.getName(acctTimeType));
        return empQuotaSummaryVo;
    }
}
