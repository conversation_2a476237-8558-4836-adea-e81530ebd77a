package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.system.mybatis.model.SysUnitCity;
import com.caidao1.wa.enums.LeaveCancelStatusEnum;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.dto.*;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.*;
import com.caidaocloud.attendance.service.application.dto.workflow.WfTaskApproveDTO;
import com.caidaocloud.attendance.service.application.dto.workflow.WfTaskUrgeDTO;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.feign.workflow.IWfOperateFeignClient;
import com.caidaocloud.attendance.service.application.service.IEmpInfoService;
import com.caidaocloud.attendance.service.application.service.IOverTimeService;
import com.caidaocloud.attendance.service.application.service.IOvertimeApplyService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.domain.service.WaEmpLeaveCancelDomainService;
import com.caidaocloud.attendance.service.domain.service.WaEmpLeaveCancelInfoDomainService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpInfoPoMapper;
import com.caidaocloud.attendance.service.interfaces.dto.OverTimeDto;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;
import com.caidaocloud.attendance.service.interfaces.dto.WfApprovalDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WfNoticeParameterItem;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WfTaskUrgeItem;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.DeleteStatusEnum;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.workflow.enums.WfAttachmentTypeEnum;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class WfService implements IWfService {
    @Autowired
    private WaEmpLeaveDo waEmpLeaveDo;
    @Autowired
    private WaEmpOvertimeDo waEmpOvertimeDo;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaRegisterRecordDo registerRecordDo;
    @Autowired
    private WaEmpTravelDo waEmpTravelDo;
    @Autowired
    private IOverTimeService overTimeService;
    @Autowired
    private WaShiftApplyRecordDo waShiftApplyRecordDo;
    @Autowired
    private IEmpInfoService empInfoService;
    @Autowired
    private WaRegisterRecordBdkDo registerRecordBdkDo;
    @Autowired
    private WaEmpLeaveCancelDomainService waEmpLeaveCancelDomainService;
    @Autowired
    private WaEmpLeaveCancelInfoDomainService waEmpLeaveCancelInfoDomainService;
    @Autowired
    private IWfOperateFeignClient wfOperateFeignClient;
    @Autowired
    private EmpCompensatoryCaseApplyDo empCompensatoryApplyDo;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private EmpInfoPoMapper empInfoPoMapper;
    @Autowired
    private WaWorkflowRevokeDo workflowRevokeDo;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private WaWorkflowRevokeDo waWorkflowRevokeDo;
    @Autowired
    private WaLeaveExtensionDo leaveExtensionDo;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;

    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    /**
     * 发起撤销的审批流程
     *
     * @param userInfo     员工信息
     * @param entityId     业务主键
     * @param revokeReason 撤销原因
     * @param workflowEnum 流程
     * @param eventTime    事件时间
     * @return
     */
    public Result<Boolean> revokeWorkflowBegin(UserInfo userInfo, Long entityId, String revokeReason, BusinessCodeEnum workflowEnum, Long eventTime) {
        //检查流程是否已启用
        Result<?> checkWorkflowEnableResult = checkWorkflowEnabled(workflowEnum.getCode());
        if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
            return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, Boolean.FALSE);
        }
        Boolean workflowEnabledResultData = (Boolean) checkWorkflowEnableResult.getData();
        if (!workflowEnabledResultData) {
            return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, Boolean.FALSE);
        }
        //保存撤销流程
        WaWorkflowRevokeDo workflowRevoke = getWorkflowRevoke(userInfo, entityId, revokeReason, workflowEnum);
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        // 发起新事务
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(def);
        try {
            //保存撤销流程
            workflowRevokeDo.save(workflowRevoke);
            // 提交新开事务
            platformTransactionManager.commit(transactionStatus);
            //发起工作流
            WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
            wfBeginWorkflowDto.setFuncCode(workflowEnum.getCode());
            wfBeginWorkflowDto.setBusinessId(workflowRevoke.getId().toString());
            wfBeginWorkflowDto.setApplicantId(userInfo.getStaffId().toString());
            wfBeginWorkflowDto.setApplicantName(userInfo.getEmpname());
            wfBeginWorkflowDto.setEventTime(DateUtil.getOnlyDate(new Date(eventTime * 1000)) * 1000);
            Result<?> result = wfRegisterFeign.begin(wfBeginWorkflowDto);
            if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                platformTransactionManager.rollback(transactionStatus);
                return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE);
            }
        } catch (Exception e) {
            // 回滚新开事务
            platformTransactionManager.rollback(transactionStatus);
            log.error("申请撤销/废止失败:{}", e.getMessage(), e);
            throw new CDException("申请撤销/废止失败");
        }
        return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_REVOKE_SUBMIT_SUCCESS, Boolean.TRUE);
    }

    public WaWorkflowRevokeDo getWorkflowRevoke(UserInfo userInfo, Long entityId, String revokeReason, BusinessCodeEnum workflowEnum) {
        WaWorkflowRevokeDo workflowRevoke = new WaWorkflowRevokeDo();
        workflowRevoke.setId(snowflakeUtil.createId());
        workflowRevoke.setTenantId(userInfo.getTenantId());
        workflowRevoke.setEntityId(entityId);
        workflowRevoke.setModuleName(workflowEnum.name());
        workflowRevoke.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        workflowRevoke.setReason(revokeReason);
        workflowRevoke.setDeleted(DeleteStatusEnum.UN_DELETED.getIndex());
        workflowRevoke.setCreateBy(userInfo.getUserId());
        workflowRevoke.setCreateTime(System.currentTimeMillis() / 1000);
        return workflowRevoke;
    }

    private UserInfo getUser() {
        UserInfo userInfo = sessionService.getUserInfo();
        PreCheck.preCheckArgument(userInfo == null, "员工不存在");
        return userInfo;
    }

    private Long getBusinessId(String businessKey) {
        if (StringUtils.isEmpty(businessKey)) {
            return null;
        }
        if (businessKey.contains("_")) {
            String[] businessKeys = businessKey.split("_");
            return Long.valueOf(businessKeys[0]);
        }
        return Long.valueOf(businessKey);
    }

    /**
     * 查看业务详情
     *
     * @param businessKey
     * @param nodeId
     * @param wfFuncType
     * @return
     */
    @Override
    public WfDetailDto getWfFuncDetail(String businessKey, String nodeId, Integer wfFuncType) {
        Long businessId = getBusinessId(businessKey);
        if (businessId == null) {
            return new WfDetailDto();
        }
        BusinessCodeEnum funcType = getBusinessCodeEnum(businessKey);
        if (funcType == null) {
            return new WfDetailDto();
        }
        UserInfo userInfo = getUser();
        switch (funcType) {
            case LEAVE:
                return getLeaveWfDetail(businessKey);
            case VACATION:
                return getLeaveCancelWfDetail(businessKey);
            case OVERTIME:
                return getOtDetail(ConvertHelper.longConvert(userInfo.getTenantId()), businessId);
            case REGISTER:
                return getCardReplacementDetail(ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), businessId);
            case TRAVEL:
                return getEmpTravelDetail(ConvertHelper.longConvert(userInfo.getTenantId()), businessId);
            case SHIFT:
                return getScDetail(ConvertHelper.longConvert(userInfo.getTenantId()), businessId);
            case COMPENSATORY:
                return getCompensatoryDetail(userInfo.getTenantId(), businessId);
            case BATCH_TRAVEL:
                return SpringUtil.getBean(WaBatchTravelService.class).getWfDetailDto(ConvertHelper.longConvert(userInfo.getTenantId()), businessId);
            case BATCH_LEAVE:
                return SpringUtil.getBean(WaBatchLeaveService.class).getWfDetailDto(businessId);
            case BATCH_OVERTIME:
                return SpringUtil.getBean(WaBatchOvertimeService.class).getWfDetailDto(businessId);
            case BATCH_ANALYSE_ADJUST:
                return SpringUtil.getBean(WaBatchAnalyseResultAdjustService.class).getWfDetailDto(businessId);
            case OVERTIME_REVOKE:
            case OVERTIME_ABOLISH:
                return getOvertimeRevokeDetail(userInfo.getTenantId(), businessId);
            case TRAVEL_REVOKE:
            case TRAVEL_ABOLISH:
                return getTravelRevokeDetail(userInfo.getTenantId(), businessId);
            case LEAVE_EXTENSION:
                return getLeaveExtensionDetail(userInfo.getTenantId(), businessId);
        }
        return new WfDetailDto();
    }

    /**
     * 假期延期申请详情
     *
     * @param tenantId   租户
     * @param businessId 业务主键
     * @return
     */
    private WfDetailDto getLeaveExtensionDetail(String tenantId, Long businessId) {
        WfDetailDto detailDto = new WfDetailDto();
        Optional<WaLeaveExtensionDo> optional = Optional.ofNullable(leaveExtensionDo.getById(tenantId, businessId));
        if (optional.isPresent()) {
            WaLeaveExtensionDo leaveExtension = optional.get();
            List<KeyValue> list = new ArrayList<>();
            //发起人、申请人、假期类型、开始时间、结束时间、申请时长、审批状态、申请事由
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(leaveExtension.getCreateBy())));
            if (StringUtils.isNotBlank(leaveExtension.getWorkNo()) && StringUtils.isNotBlank(leaveExtension.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), leaveExtension.getWorkNo() + "(" + leaveExtension.getEmpName() + ")"));
            } else if (StringUtils.isNotBlank(leaveExtension.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), leaveExtension.getEmpName()));
            } else if (StringUtils.isNotBlank(leaveExtension.getWorkNo())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), leaveExtension.getWorkNo()));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), Optional.ofNullable(leaveExtension.getEmployType()).orElse("-")));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), Optional.ofNullable(leaveExtension.getOrgName()).orElse("-")));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), Optional.ofNullable(leaveExtension.getWorkCity()).orElse("-")));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), leaveExtension.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(leaveExtension.getHireDate())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(leaveExtension.getI18nRuleName(), leaveExtension.getQuotaName())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_QUOTA, null).getMsg(), leaveExtension.getTimeDuration()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_QUOTA_UNIT, null).getMsg(), PreTimeUnitEnum.getName(leaveExtension.getTimeUnit())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_START_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(leaveExtension.getStartDate())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_END_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(leaveExtension.getEndDate())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(leaveExtension.getStatus())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), Optional.ofNullable(leaveExtension.getReason()).orElse("-")));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON, null).getMsg(), Optional.ofNullable(leaveExtension.getRevokeReason()).orElse("-")));
            detailDto.setItems(list);
            detailDto.setFiles(leaveExtension.getFileId());
            detailDto.setFileNames(leaveExtension.getFileName());
        }
        return detailDto;
    }

    /**
     * 加班撤销详情
     *
     * @param tenantId   租户
     * @param businessId 业务主键
     * @return
     */
    private WfDetailDto getOvertimeRevokeDetail(String tenantId, Long businessId) {
        WfDetailDto detailDto = new WfDetailDto();
        WaWorkflowRevokeDo workflowRevoke = getWaWorkflowRevoke(businessId);
        WaEmpOvertimeDo overtimeDo = waEmpOvertimeDo.getOtRevokeDetailById(tenantId, workflowRevoke.getId());
        if (overtimeDo != null) {
            List<KeyValue> list = new ArrayList<>();
            //发起人、申请人、开始时间、结束时间、申请时长、补偿类型、审批状态、申请事由
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(workflowRevoke.getCreateBy())));
            if (StringUtils.isNotBlank(overtimeDo.getWorkno()) && StringUtils.isNotBlank(overtimeDo.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getWorkno() + "(" + overtimeDo.getEmpName() + ")"));
            } else if (StringUtils.isNotBlank(overtimeDo.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getEmpName()));
            } else if (StringUtils.isNotBlank(overtimeDo.getWorkno())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getWorkno()));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), overtimeDo.getFullPath()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), overtimeDo.getWorkCity() == null ? "-" : overtimeDo.getWorkCity()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), overtimeDo.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(overtimeDo.getHireDate())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(overtimeDo.getStartTime())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(overtimeDo.getEndTime())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), overtimeDo.getOtDurationDesc()));
            if (overtimeDo.getCrttime() != null) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(overtimeDo.getCrttime())));
            }
            OverTimeDto overTimeDto = null;
            if (null != overtimeDo.getOvertimeTypeId()) {
                Optional<OverTimeDto> overTimeDtoOpt = Optional.ofNullable(overTimeService.getOtType(overtimeDo.getOvertimeTypeId()));
                if (overTimeDtoOpt.isPresent()) {
                    overTimeDto = overTimeDtoOpt.get();
                }
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE, null).getMsg(), overTimeDto == null ? "-" : LangParseUtil.getI18nLanguage(overTimeDto.getI18nTypeName(), overTimeDto.getTypeName())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATORY_TYPE, null).getMsg(), CompensateTypeEnum.getDescByOrdinal(overtimeDo.getCompensateType())));
            // 本周期内加班总时长
            String periodOtDuration = String.format("%s%s",
                    getEmpPeriodOtDuration(overtimeDo.getBelongOrgId(), overtimeDo.getEmpid(), overtimeDo.getStartTime()),
                    PreTimeUnitEnum.getName(2));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.PERIOD_OT_DURATION, null).getMsg(), periodOtDuration));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(overtimeDo.getStatus())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), overtimeDo.getReason() == null ? "-" : overtimeDo.getReason()));
            detailDto.setItems(list);
            detailDto.setFileNames(overtimeDo.getFileNames());
            detailDto.setFiles(overtimeDo.getFiles());
            detailDto.setFullPath(overtimeDo.getFullPath());
        }
        return detailDto;
    }

    /**
     * 出差撤销/废止详情
     *
     * @param tenantId   租户
     * @param businessId 业务主键
     * @return
     */
    private WfDetailDto getTravelRevokeDetail(String tenantId, Long businessId) {
        WfDetailDto detailDto = new WfDetailDto();
        WaWorkflowRevokeDo workflowRevoke = getWaWorkflowRevoke(businessId);
        Optional<WaEmpTravelDo> optional = Optional.ofNullable(waEmpTravelDo.getEmpTravelRevokeDetailById(tenantId, workflowRevoke.getId()));
        if (optional.isPresent()) {
            WaEmpTravelDo empTravel = optional.get();
            List<KeyValue> list = new ArrayList<>();
            //发起人、申请人、假期类型、开始时间、结束时间、申请时长、审批状态、申请事由
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(workflowRevoke.getCreateBy())));
            if (StringUtils.isNotBlank(empTravel.getWorkNo()) && StringUtils.isNotBlank(empTravel.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getWorkNo() + "(" + empTravel.getEmpName() + ")"));
            } else if (StringUtils.isNotBlank(empTravel.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getEmpName()));
            } else if (StringUtils.isNotBlank(empTravel.getWorkNo())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getWorkNo()));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), empTravel.getEmployType()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), empTravel.getFullPath()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), empTravel.getWorkCity() == null ? "-" : empTravel.getWorkCity()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), empTravel.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(empTravel.getHireDate())));
            Long startTime = empTravel.getStartTime();
            Long endTime = empTravel.getEndTime();
            Integer periodType = empTravel.getPeriodType() != null ? Integer.valueOf(empTravel.getPeriodType()) : null;
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime)));
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime)));
            } else {
                if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(startTime), DayHalfTypeEnum.getDesc(empTravel.getShalfDay()))));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(endTime), DayHalfTypeEnum.getDesc(empTravel.getEhalfDay()))));
                } else {
                    if (empTravel.getShiftStartTime() != null && empTravel.getShiftEndTime() != null) {
                        startTime = empTravel.getShiftStartTime();
                        endTime = empTravel.getShiftEndTime();
                    }
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime)));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime)));
                }
            }
            if (empTravel.getTimeUnit() == 1) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), empTravel.getTimeDuration() + TravelTypeUnitEnum.getName(empTravel.getTimeUnit())));
            } else {
                BigDecimal a = BigDecimal.valueOf(empTravel.getTimeDuration());
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), a.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + TravelTypeUnitEnum.getName(empTravel.getTimeUnit())));
            }
            if (empTravel.getCreateTime() != null) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(empTravel.getCreateTime())));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empTravel.getI18nTravelTypeName(), empTravel.getTravelType())));
            SysUnitCity provinceObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, empTravel.getProvince());
            SysUnitCity cityObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_CITY_KEY, empTravel.getCity());
            String province = "";
            String city = "";
            if (provinceObj != null) {
                province = provinceObj.getChnName();
            }
            if (cityObj != null) {
                city = cityObj.getChnName();
            }
            String location = province + " " + city;
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_PLACE, null).getMsg(), location));
            String travelMode = empTravel.getTravelMode();
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_MODE, null).getMsg(), travelMode));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(empTravel.getStatus())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), empTravel.getReason() == null ? "-" : empTravel.getReason()));
            detailDto.setItems(list);
            detailDto.setFiles(empTravel.getFileId());
            detailDto.setFileNames(empTravel.getFileName());
            detailDto.setFullPath(empTravel.getFullPath());
        }
        return detailDto;
    }

    private WaWorkflowRevokeDo getWaWorkflowRevoke(Long id) {
        return workflowRevokeDo.selectByPrimaryKey(id);
    }

    /**
     * 调休付现详情
     *
     * @param tenantId   租户
     * @param businessId 业务主键
     * @return
     */
    private WfDetailDto getCompensatoryDetail(String tenantId, Long businessId) {
        WfDetailDto detailDto = new WfDetailDto();
        EmpCompensatoryCaseApplyDo dto = empCompensatoryApplyDo.getDetailById(tenantId, businessId);
        List<KeyValue> list = new ArrayList<>();
        //发起人、申请人、调休额度、单位、生效日期、失效日期、申请付现额度、有效付现额度、申请时间、审批状态、审批通过日期、备注
        list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(dto.getCreateBy())));
        if (StringUtils.isNotBlank(dto.getWorkNo()) && StringUtils.isNotBlank(dto.getEmpName())) {
            list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), dto.getWorkNo() + "(" + dto.getEmpName() + ")"));
        } else if (StringUtils.isNotBlank(dto.getEmpName())) {
            list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), dto.getEmpName()));
        } else if (StringUtils.isNotBlank(dto.getWorkNo())) {
            list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), dto.getWorkNo()));
        }
        list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), Optional.ofNullable(dto.getFullPath()).orElse("-")));
        list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), Optional.ofNullable(dto.getEmployType()).orElse("-")));
        list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), Optional.ofNullable(dto.getWorkCity()).orElse("-")));
        list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), dto.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(dto.getHireDate())));
        Integer timeUnit = dto.getTimeUnit();
        Float applyDuration = Optional.ofNullable(dto.getApplyDuration()).orElse(0f);
        Float validDuration = Optional.ofNullable(dto.getValidDuration()).orElse(0f);
        if (PreTimeUnitEnum.HOUR.getIndex().equals(timeUnit)) {
            list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_INVALID_QUOTA, null).getMsg(), String.format("%s%s", BigDecimal.valueOf(applyDuration).divide(BigDecimal.valueOf(60), 1, RoundingMode.DOWN).floatValue(), PreTimeUnitEnum.getName(timeUnit))));
            list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_VALID_QUOTA, null).getMsg(), String.format("%s%s", BigDecimal.valueOf(validDuration).divide(BigDecimal.valueOf(60), 1, RoundingMode.DOWN).floatValue(), PreTimeUnitEnum.getName(timeUnit))));
            list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_QUOTA, null).getMsg(), String.format("%s%s", BigDecimal.valueOf(applyDuration + validDuration).divide(BigDecimal.valueOf(60), 1, RoundingMode.DOWN).floatValue(), PreTimeUnitEnum.getName(timeUnit))));
        } else {
            list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_INVALID_QUOTA, null).getMsg(), String.format("%s%s", applyDuration, PreTimeUnitEnum.getName(timeUnit))));
            list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_VALID_QUOTA, null).getMsg(), String.format("%s%s", validDuration, PreTimeUnitEnum.getName(timeUnit))));
            list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_QUOTA, null).getMsg(), String.format("%s%s", applyDuration + validDuration, PreTimeUnitEnum.getName(timeUnit))));
        }
        list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(dto.getCreateTime())));
        list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(dto.getStatus())));
        list.add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.NOTE, null).getMsg(), Optional.ofNullable(dto.getNote()).orElse("-")));
        detailDto.setItems(list);
        return detailDto;
    }

    private String getFuncCode(String businessKey) {
        if (!businessKey.contains("_")) {
            return null;
        }
        String[] businessKeys = businessKey.split("_");
        return businessKeys[1];
    }

//    private Integer getFuncType(String businessKey) {
//        if (!businessKey.contains("_")) {
//            return null;
//        }
//        String[] businessKeys = businessKey.split("_");
//        Integer wfFuncId = Integer.parseInt(businessKeys[1]);
//        //查询流程
//        WfFunc wfFunc = workflowService.getWfFuncById(wfFuncId);
//        if (wfFunc == null) {
//            return null;
//        }
//        return Integer.valueOf(wfFunc.getFuncType());
//    }

//    /**
//     * 根据工作流配置查询流程详情
//     *
//     * @param corpId
//     * @param businessKey
//     * @param nodeId
//     * @return
//     * @throws Exception
//     */
//    private WfDetailDto getWfDetail(Long corpId, String businessKey, String nodeId) throws Exception {
//        //查询流程明细
//        Map wfFuncDetail = workflowService.getWfFuncDetail(ConvertHelper.intConvert(corpId), businessKey, nodeId);
//        if (MapUtils.isEmpty(wfFuncDetail) || wfFuncDetail.get("data") == null
//                || CollectionUtils.isEmpty((Collection) wfFuncDetail.get("data"))) {
//            return null;
//        }
//        WfDetailDto detailDto = new WfDetailDto();
//        List<KeyValue> items = JSONArray.parseArray(JSON.toJSONString(wfFuncDetail.get("data")), KeyValue.class);
//        List<KeyValue> files = items.stream().filter(row -> "FILE_NAME".equals(row.getText())).collect(Collectors.toList());
//        List<KeyValue> list = items.stream().filter(row -> "FULL_PATH".equals(row.getText())).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(files)) {
//            detailDto.setFileNames((String) files.get(0).getValue());
//            detailDto.setFiles((String) wfFuncDetail.get("files"));
//        }
//        if (CollectionUtils.isNotEmpty(list)) {
//            detailDto.setFullPath((String) list.get(0).getValue());
//        }
//        items = items.stream().filter(row -> !"FILE_NAME".equals(row.getText())).collect(Collectors.toList());
//        items = items.stream().filter(row -> !"FULL_PATH".equals(row.getText())).collect(Collectors.toList());
//        boolean flag = items.stream().anyMatch(row -> "婚假".equals(row.getValue()));
//        if (!flag) {
//            items = items.stream().filter(row -> !"婚姻状况".equals(row.getText())).collect(Collectors.toList());
//        }
//        detailDto.setItems(items);
//        return detailDto;
//    }

    /**
     * 补卡详情
     *
     * @param corpId     集团
     * @param tenantId
     * @param businessId 业务主键
     * @return
     */
    private WfDetailDto getCardReplacementDetail(Long corpId, String tenantId, Long businessId) {
        WfDetailDto detailDto = new WfDetailDto();
        Optional<WaRegisterRecordBdkDo> optional = Optional.ofNullable(registerRecordBdkDo.getRegisterDetailById(corpId, businessId));
        if (optional.isPresent()) {
            List<KeyValue> list = new ArrayList<>();
            WaRegisterRecordBdkDo registerRecord = optional.get();
            //发起人、申请人、假期类型、开始时间、结束时间、申请时长、审批状态、申请事由
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(registerRecord.getCrtuser())));
            if (StringUtils.isNotBlank(registerRecord.getWorkno()) && StringUtils.isNotBlank(registerRecord.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), String.format("%s(%s)", registerRecord.getWorkno(), registerRecord.getEmpName())));
            } else if (StringUtils.isNotBlank(registerRecord.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), registerRecord.getEmpName()));
            } else {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), registerRecord.getWorkno()));
            }
            if (registerRecord.getCrttime() != null) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(registerRecord.getCrttime())));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), registerRecord.getFullPath()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), registerRecord.getWorkCity() == null ? "-" : registerRecord.getWorkCity()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), registerRecord.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(registerRecord.getHireDate())));
            Long belongDate = registerRecord.getBelongDate();

            // 班次
            List<Integer> shiftDefIdList = new ArrayList<>();
            if (null != registerRecord.getShiftDefIds() && !registerRecord.getShiftDefIds().isEmpty()) {
                String[] shiftDefIds = registerRecord.getShiftDefIds().split(",");
                shiftDefIdList = Stream.of(shiftDefIds).map(Integer::valueOf)
                        .collect(Collectors.toCollection(java.util.ArrayList::new));
            } else if (null != registerRecord.getShiftDefId()) {
                shiftDefIdList = com.google.common.collect.Lists.newArrayList(registerRecord.getShiftDefId());
            }
            EmpShiftRecordDto empShiftRecordDto = empInfoService.getShiftAndRecords(belongDate,
                    registerRecord.getEmpid(), tenantId, shiftDefIdList);
            if (StringUtil.isNotBlank(empShiftRecordDto.getShiftName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NAME, null).getMsg(), empShiftRecordDto.getShiftName()));
            }
            if (StringUtils.isNotBlank(empShiftRecordDto.getShiftTimePeriod())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_TIME_RANGE, null).getMsg(), empShiftRecordDto.getShiftTimePeriod()));
            }
            List<Long> records = empShiftRecordDto.getRecords();
            if (CollectionUtils.isNotEmpty(records)) {
                String retPattern = "HH:mm";
                String recordStr;
                if (records.size() > 4) {
                    String fst = DateUtil.convertDateTimeToStr(records.stream().min(Long::compare).orElse(0L), retPattern, true);
                    String sec = DateUtil.convertDateTimeToStr(records.stream().max(Long::compare).orElse(0L), retPattern, true);
                    recordStr = String.format("%s...%s", fst, sec);
                } else {
                    recordStr = records.stream().map(r -> DateUtil.convertDateTimeToStr(r, retPattern, true)).collect(Collectors.joining(","));
                }
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.REGISTER_RECORD, null).getMsg(), recordStr));
            } else {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.REGISTER_RECORD, null).getMsg(), ResponseWrap.wrapResult(AttendanceCodes.NOT_REGISTER_RECORD, null).getMsg()));
            }
            if (StringUtil.isNotBlank(registerRecord.getRegDateTimes())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.FILL_CARD_TIME, null).getMsg(), Arrays.stream(registerRecord.getRegDateTimes().split(",")).map(r -> DateUtil.getTimeStrByTimesamp(Long.valueOf(r))).collect(Collectors.joining(",<br/>"))));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(registerRecord.getApprovalStatus())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.FILL_CARD_REASON, null).getMsg(), registerRecord.getReason()));
            if (StringUtils.isNotBlank(registerRecord.getRevokeReason())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON, null).getMsg(), registerRecord.getRevokeReason()));
            }
            detailDto.setItems(list);
            detailDto.setFileNames(registerRecord.getFileNames());
            detailDto.setFiles(registerRecord.getFiles());
        }
        return detailDto;
    }

    /**
     * 出差详情
     *
     * @param corpId     集团
     * @param businessId 业务主键
     * @return
     */
    private WfDetailDto getEmpTravelDetail(Long corpId, Long businessId) {
        WfDetailDto detailDto = new WfDetailDto();
        Optional<WaEmpTravelDo> optional = Optional.ofNullable(waEmpTravelDo.getWaEmpTravelDetailById(businessId, corpId));
        if (optional.isPresent()) {
            WaEmpTravelDo empTravel = optional.get();
            List<KeyValue> list = new ArrayList<>();
            //发起人、申请人、假期类型、开始时间、结束时间、申请时长、审批状态、申请事由
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(empTravel.getCreateBy())));
            if (StringUtils.isNotBlank(empTravel.getWorkNo()) && StringUtils.isNotBlank(empTravel.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getWorkNo() + "(" + empTravel.getEmpName() + ")"));
            } else if (StringUtils.isNotBlank(empTravel.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getEmpName()));
            } else if (StringUtils.isNotBlank(empTravel.getWorkNo())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getWorkNo()));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), empTravel.getEmployType()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), empTravel.getFullPath()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), empTravel.getWorkCity() == null ? "-" : empTravel.getWorkCity()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), empTravel.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(empTravel.getHireDate())));
            Long startTime = empTravel.getStartTime();
            Long endTime = empTravel.getEndTime();
            Integer periodType = empTravel.getPeriodType() != null ? Integer.valueOf(empTravel.getPeriodType()) : null;
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime)));
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime)));
            } else {
                if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(startTime), DayHalfTypeEnum.getDesc(empTravel.getShalfDay()))));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(endTime), DayHalfTypeEnum.getDesc(empTravel.getEhalfDay()))));
                } else {
                    if (empTravel.getShiftStartTime() != null && empTravel.getShiftEndTime() != null) {
                        startTime = empTravel.getShiftStartTime();
                        endTime = empTravel.getShiftEndTime();
                    }
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime)));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime)));
                }
            }
            if (empTravel.getTimeUnit() == 1) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), empTravel.getTimeDuration() + TravelTypeUnitEnum.getName(empTravel.getTimeUnit())));
            } else {
                BigDecimal a = BigDecimal.valueOf(empTravel.getTimeDuration());
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), a.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + TravelTypeUnitEnum.getName(empTravel.getTimeUnit())));
            }
            if (empTravel.getCreateTime() != null) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(empTravel.getCreateTime())));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empTravel.getI18nTravelTypeName(), empTravel.getTravelType())));
            SysUnitCity proviceObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, empTravel.getProvince());
            SysUnitCity cityObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_CITY_KEY, empTravel.getCity());
            String province = "";
            String city = "";
            if (proviceObj != null) {
                province = proviceObj.getChnName();
            }
            if (cityObj != null) {
                city = cityObj.getChnName();
            }
            String location = province + " " + city;
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_PLACE, null).getMsg(), location));

            String travelMode = empTravel.getTravelMode();
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_MODE, null).getMsg(), travelMode));

            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(empTravel.getStatus())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), empTravel.getReason() == null ? "-" : empTravel.getReason()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), empTravel.getProcessCode() == null ? "-" : empTravel.getProcessCode()));
            detailDto.setItems(list);
            detailDto.setFiles(empTravel.getFileId());
            detailDto.setFileNames(empTravel.getFileName());
            detailDto.setFullPath(empTravel.getFullPath());
        }
        return detailDto;
    }

    /**
     * 查询休假记录申请明细
     *
     * @param businessKey
     * @return
     */
    public WfDetailDto getLeaveWfDetail(String businessKey) {
        UserInfo userInfo = getUser();
        Long businessId = getBusinessId(businessKey);
        // 查询休假申请单明细
        return getLtDetail(userInfo.getTenantId(), businessId);
    }

    private WfDetailDto getLtDetail(String tenantId, Long businessId) {
        WfDetailDto detailDto = new WfDetailDto();
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getLeaveDetailById(tenantId, businessId);
        if (empLeave != null) {
            List<KeyValue> list = new ArrayList<>();
            //发起人、申请人、假期类型、开始时间、结束时间、申请时长、审批状态、申请事由
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(empLeave.getCrtuser())));
            if (StringUtils.isNotBlank(empLeave.getWorkno()) && StringUtils.isNotBlank(empLeave.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeave.getWorkno() + "(" + empLeave.getEmpName() + ")"));
            } else if (StringUtils.isNotBlank(empLeave.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeave.getEmpName()));
            } else if (StringUtils.isNotBlank(empLeave.getWorkno())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeave.getWorkno()));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), empLeave.getFullPath()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), empLeave.getEmployType()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), empLeave.getWorkCity() == null ? "-" : empLeave.getWorkCity()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), empLeave.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(empLeave.getHireDate())));
            if (empLeave.getLeaveType() == 8) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.MARITAL_STATUS, null).getMsg(), empLeave.getMarriage() == null ? "-" : empLeave.getMarriage()));
            } else if (empLeave.getLeaveType() == 4) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NUMBER_OF_CHILD, null).getMsg(), empLeave.getChildNum() == null ? "-" : empLeave.getChildNum()));
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.TYPE_OF_MATERNITY_LEAVE, null).getMsg(), empLeave.getMaternityLeaveType() == null ? "-" :
                        MaternityLeaveTypeEnum.getNameByIndex(empLeave.getMaternityLeaveType())));
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.CHILD_BIRTHDAY, null).getMsg(), empLeave.getManufactureDate() == null ? "-" : DateUtil.getDateStrByTimesamp(empLeave.getManufactureDate())));
            }
            // 休假班次
            List<String> shiftDefNames;
            if (CollectionUtils.isNotEmpty(shiftDefNames = empLeave.getShiftDefNameList())) {
                list.add(getKeyValue(MessageHandler.getMessage("caidao.exception.error_202818", WebUtil.getRequest()),
                        StringUtils.join(shiftDefNames, "、")));
            }
            Long startTime = empLeave.getStartTime();
            Long endTime = empLeave.getEndTime();
            Integer periodType = empLeave.getPeriodType() != null ? Integer.valueOf(empLeave.getPeriodType()) : null;
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime)));
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime)));
            } else {
                if (empLeave.getShiftStartTime() != null && empLeave.getShiftEndTime() != null) {
                    startTime = empLeave.getShiftStartTime();
                    endTime = empLeave.getShiftEndTime();
                }
                if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    Long start = empLeave.getStartTime();
                    Long end = empLeave.getEndTime();
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeave.getShalfDay()))));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeave.getEhalfDay()))));
                } else {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime)));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime)));
                }
            }
            if (empLeave.getTimeUnit() == 1) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), empLeave.getTotalTimeDuration() + PreTimeUnitEnum.getName(empLeave.getTimeUnit())));
            } else {
                BigDecimal a = BigDecimal.valueOf(empLeave.getTotalTimeDuration());
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), a.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit())));
            }
            if (empLeave.getCrttime() != null) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(empLeave.getCrttime())));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empLeave.getI18nLeaveName(), empLeave.getLeaveName())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(empLeave.getStatus())));
            if (StringUtils.isNotBlank(empLeave.getRevokeReason())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON, null).getMsg(), empLeave.getRevokeReason()));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), empLeave.getReason() == null ? "-" : empLeave.getReason()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), empLeave.getProcessCode() == null ? "-" : empLeave.getProcessCode()));
            detailDto.setItems(list);
            detailDto.setFiles(empLeave.getFiles());
            detailDto.setFileNames(empLeave.getFileNames());
            detailDto.setFullPath(empLeave.getFullPath());
        }
        return detailDto;
    }

    /**
     * 加班明细
     *
     * @param corpId     集团
     * @param businessId 业务主键
     * @return
     */
    private WfDetailDto getOtDetail(Long corpId, Long businessId) {
        WfDetailDto detailDto = new WfDetailDto();
        WaEmpOvertimeDo overtimeDo = waEmpOvertimeDo.getOtDetailById(corpId, businessId);
        if (overtimeDo != null) {
            List<KeyValue> list = new ArrayList<>();
            //发起人、申请人、开始时间、结束时间、申请时长、补偿类型、审批状态、申请事由
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(overtimeDo.getCrtuser())));
            if (StringUtils.isNotBlank(overtimeDo.getWorkno()) && StringUtils.isNotBlank(overtimeDo.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getWorkno() + "(" + overtimeDo.getEmpName() + ")"));
            } else if (StringUtils.isNotBlank(overtimeDo.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getEmpName()));
            } else if (StringUtils.isNotBlank(overtimeDo.getWorkno())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getWorkno()));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), overtimeDo.getFullPath()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), overtimeDo.getWorkCity() == null ? "-" : overtimeDo.getWorkCity()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), overtimeDo.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(overtimeDo.getHireDate())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(overtimeDo.getStartTime())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(overtimeDo.getEndTime())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), overtimeDo.getOtDurationDesc()));
            if (overtimeDo.getCrttime() != null) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(overtimeDo.getCrttime())));
            }
            OverTimeDto overTimeDto = null;
            if (null != overtimeDo.getOvertimeTypeId()) {
                Optional<OverTimeDto> overTimeDtoOpt = Optional.ofNullable(overTimeService.getOtType(overtimeDo.getOvertimeTypeId()));
                if (overTimeDtoOpt.isPresent()) {
                    overTimeDto = overTimeDtoOpt.get();
                }
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE, null).getMsg(), overTimeDto == null ? "-" : LangParseUtil.getI18nLanguage(overTimeDto.getI18nTypeName(), overTimeDto.getTypeName())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATORY_TYPE, null).getMsg(), CompensateTypeEnum.getDescByOrdinal(overtimeDo.getCompensateType())));
            // 本周期内加班总时长
            String periodOtDuration = String.format("%s%s", getEmpPeriodOtDuration(overtimeDo.getBelongOrgId(), overtimeDo.getEmpid(), overtimeDo.getStartTime()), PreTimeUnitEnum.getName(2));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.PERIOD_OT_DURATION, null).getMsg(), periodOtDuration));

            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(overtimeDo.getStatus())));
            if (StringUtils.isNotBlank(overtimeDo.getRevokeReason())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON, null).getMsg(), overtimeDo.getRevokeReason()));
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), overtimeDo.getReason() == null ? "-" : overtimeDo.getReason()));
            detailDto.setItems(list);
            detailDto.setFileNames(overtimeDo.getFileNames());
            detailDto.setFiles(overtimeDo.getFiles());
            detailDto.setFullPath(overtimeDo.getFullPath());
        }
        return detailDto;
    }

    private BigDecimal getEmpPeriodOtDuration(String tenantId, Long empId, Long startDate) {
        return SpringUtil.getBean(IOvertimeApplyService.class).getEmpPeriodOtDuration(tenantId, empId, startDate);
    }

    /**
     * 调班明细
     *
     * @param corpId     集团
     * @param businessId 业务主键
     * @return
     */
    private WfDetailDto getScDetail(Long corpId, Long businessId) {
        WfDetailDto detailDto = new WfDetailDto();
        WaShiftApplyRecordDo shift = waShiftApplyRecordDo.getShiftApplyInfoById(businessId, corpId);
        if (shift != null) {
            List<KeyValue> list = new ArrayList<>();
            //发起人、申请人
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(shift.getCreateBy())));
            if (StringUtils.isNotBlank(shift.getWorkNo()) && StringUtils.isNotBlank(shift.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), shift.getWorkNo() + "(" + shift.getEmpName() + ")"));
            } else if (StringUtils.isNotBlank(shift.getEmpName())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), shift.getEmpName()));
            } else if (StringUtils.isNotBlank(shift.getWorkNo())) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), shift.getWorkNo()));
            }

            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), shift.getEmployType()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), shift.getFullPath()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), shift.getWorkCity() == null ? "-" : shift.getWorkCity()));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), shift.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(shift.getHireDate())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_CHANGE_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(shift.getWorkDate())));

            String os = "";
            String od = "";
            String ns = "";
            String nd = "";
            //原班次
            Long nowDate = DateUtil.getOnlyDate();
            if (shift.getOldStartTime() != null && shift.getOldStartTime() >= 0) {
                os = DateUtil.convertDateTimeToStr(nowDate + (shift.getOldStartTime() * 60), "HH:mm", true);
            }

            if (shift.getOldEndTime() != null && shift.getOldEndTime() >= 0) {
                od = DateUtil.convertDateTimeToStr(nowDate + (shift.getOldEndTime() * 60), "HH:mm", true);
            }
            String oldShiftTxt = shift.getOldShift();
            if (StringUtils.isNotBlank(os) && StringUtils.isNotBlank(od)) {
                oldShiftTxt = oldShiftTxt + " " + (os + "-" + od);
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINAL_SHIFT, null).getMsg(), oldShiftTxt));
            //打卡记录
            Long daytime = shift.getWorkDate();
            RegisterRecordRequestDto requestDto = new RegisterRecordRequestDto();
            requestDto.setPageNo(1);
            requestDto.setPageSize(10000);
            requestDto.setEndDate(daytime.intValue() + (24 * 60 * 60) - 1);
            requestDto.setStartDate(daytime.intValue());
            UserInfo user = getUser();
            requestDto.setBelongOrgId(user.getTenantId());
//            PageResult<RegisterRecordDto> registerRecord = registerRecordService.getRegisterRecordListByEmpId(requestDto);
            AttendancePageResult<WaRegisterRecordDo> registerRecord = registerRecordDo.getRegisterRecordPageListByEmpId(requestDto, shift.getEmpId());
            List<WaRegisterRecordDo> items = registerRecord.getItems();
            StringBuilder sb = new StringBuilder();

            if (items.size() > 4) {
                String fst = DateUtil.convertDateTimeToStr(items.get(0).getRegDateTime(), "HH:mm", true);
                String sec = DateUtil.convertDateTimeToStr(items.get(items.size() - 1).getRegDateTime(), "HH:mm", true);
                String regTime = String.format("%s,...,%s", fst, sec);
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.REGISTER_RECORD, null).getMsg(), regTime));
            } else {
                for (WaRegisterRecordDo dto : items) {
                    String reg = DateUtil.convertDateTimeToStr(dto.getRegDateTime(), "HH:mm", true);
                    sb = sb.append(reg).append("、");
                }
                if (StringUtils.isNotBlank(sb)) {
                    String regTime = sb.substring(0, sb.length() - 1);
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.REGISTER_RECORD, null).getMsg(), regTime));
                } else {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.REGISTER_RECORD, null).getMsg(), ResponseWrap.wrapResult(AttendanceCodes.NO_REGISTER, null).getMsg()));
                }
            }
            //新班次
            if (shift.getNewStartTime() != null && shift.getNewStartTime() >= 0) {
                ns = DateUtil.convertDateTimeToStr(nowDate + (shift.getNewStartTime() * 60), "HH:mm", true);
            }

            if (shift.getOldEndTime() != null && shift.getOldEndTime() >= 0) {
                nd = DateUtil.convertDateTimeToStr(nowDate + (shift.getNewEndTime() * 60), "HH:mm", true);
            }
            String newShiftTxt = shift.getNewShift();
            if (StringUtils.isNotBlank(os) && StringUtils.isNotBlank(od)) {
                newShiftTxt = newShiftTxt + " " + (ns + "-" + nd);
            }
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.CHANGED_SHIFT, null).getMsg(), newShiftTxt));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(shift.getCreateTime())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(shift.getStatus())));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), shift.getReason() == null ? "-" : shift.getReason()));

            detailDto.setItems(list);
            detailDto.setFiles(shift.getFileId());
            detailDto.setFileNames(shift.getFileName());
            detailDto.setFullPath(shift.getFullPath());
        }
        return detailDto;
    }

    /**
     * 查询销假记录申请明细
     *
     * @param businessKey
     * @return
     */
    public WfDetailDto getLeaveCancelWfDetail(String businessKey) {
        UserInfo userInfo = getUser();
        Long businessId = getBusinessId(businessKey);
        // 查询销假申请单明细
        return getLeaveCancelDetail(userInfo.getTenantId(), businessId);
    }

    /**
     * 查看销假单详情
     *
     * @param tenantId   租户
     * @param businessId 业务主键
     * @return
     */
    private WfDetailDto getLeaveCancelDetail(String tenantId, Long businessId) {
        WfDetailDto detailDto = new WfDetailDto();
        //发起人、申请人、员工类型、任职组织、工作地、入职日期、销假时间、申请时长、申请时间、假期类型、审批状态、申请事由
        WaEmpLeaveCancelDo empLeaveCancel = waEmpLeaveCancelDomainService.getInfoById(tenantId, businessId);
        if (empLeaveCancel == null) {
            return detailDto;
        }
        List<KeyValue> list = new ArrayList<>();
        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(empLeaveCancel.getCreateBy())));
        if (StringUtils.isNotBlank(empLeaveCancel.getWorkNo()) && StringUtils.isNotBlank(empLeaveCancel.getEmpName())) {
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeaveCancel.getWorkNo() + "(" + empLeaveCancel.getEmpName() + ")"));
        } else if (StringUtils.isNotBlank(empLeaveCancel.getEmpName())) {
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeaveCancel.getEmpName()));
        } else if (StringUtils.isNotBlank(empLeaveCancel.getWorkNo())) {
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeaveCancel.getWorkNo()));
        }
        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), empLeaveCancel.getEmployType()));
        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), empLeaveCancel.getFullPath()));
        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), empLeaveCancel.getWorkCity() == null ? "-" : empLeaveCancel.getWorkCity()));
        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), empLeaveCancel.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(empLeaveCancel.getHireDate())));
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getLeaveDetailById(tenantId, Long.valueOf(empLeaveCancel.getLeaveId()));
        //休假相关字段
        if (LeaveCancelTypeEnum.TIME_ADJUST.getIndex().equals(empLeaveCancel.getTypeId().intValue())) {
            WaEmpLeaveDo originalEmpLeave = waEmpLeaveDo.getLeaveDetailById(tenantId, Long.valueOf(empLeaveCancel.getOriginalLeaveId()));
            if (originalEmpLeave != null) {
                Long startTime = originalEmpLeave.getStartTime();
                Long endTime = originalEmpLeave.getEndTime();
                Integer periodType = originalEmpLeave.getPeriodType() != null ? Integer.valueOf(originalEmpLeave.getPeriodType()) : null;
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime)));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime)));
                } else {
                    if (originalEmpLeave.getShiftStartTime() != null && originalEmpLeave.getShiftEndTime() != null) {
                        startTime = originalEmpLeave.getShiftStartTime();
                        endTime = originalEmpLeave.getShiftEndTime();
                    }
                    if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                        Long start = originalEmpLeave.getStartTime();
                        Long end = originalEmpLeave.getEndTime();
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(originalEmpLeave.getShalfDay()))));
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(originalEmpLeave.getEhalfDay()))));
                    } else {
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime)));
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime)));
                    }
                }
                if (originalEmpLeave.getTimeUnit() == 1) {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_DURATION, null).getMsg(), originalEmpLeave.getTotalTimeDuration() + PreTimeUnitEnum.getName(originalEmpLeave.getTimeUnit())));
                    BigDecimal applyTime = BigDecimal.valueOf(originalEmpLeave.getTotalTimeDuration());
                    float cancelTime = originalEmpLeave.getCancelTimeDuration() == null ? 0 : originalEmpLeave.getCancelTimeDuration();
                    BigDecimal realTime = applyTime.subtract(BigDecimal.valueOf(cancelTime));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ACTUAL_DURATION, null).getMsg(), realTime.floatValue() + PreTimeUnitEnum.getName(originalEmpLeave.getTimeUnit())));
                } else {
                    BigDecimal applyTime = BigDecimal.valueOf(originalEmpLeave.getTotalTimeDuration());
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_DURATION, null).getMsg(), applyTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(originalEmpLeave.getTimeUnit())));
                    float cancelTime = originalEmpLeave.getCancelTimeDuration() == null ? 0 : originalEmpLeave.getCancelTimeDuration();
                    BigDecimal realTime = applyTime.subtract(BigDecimal.valueOf(cancelTime));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ACTUAL_DURATION, null).getMsg(), realTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(originalEmpLeave.getTimeUnit())));
                }
            }
            if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(Integer.valueOf(empLeaveCancel.getStatus()))
                    && !ApprovalStatusEnum.PASSED.getIndex().equals(Integer.valueOf(empLeaveCancel.getStatus()))) {
                String st = "";
                String et = "";
                String adjustTimeSlot = empLeaveCancel.getAdjustTimeSlot();
                if (StringUtil.isNotBlank(adjustTimeSlot)) {
                    String[] arr = adjustTimeSlot.split("~");
                    if (arr.length > 1) {
                        st = arr[0];
                        et = arr[1];
                    }
                }
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_START_TIME, null).getMsg(), st));
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_END_TIME, null).getMsg(), et));
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_APPLY_DURATION, null).getMsg(), empLeaveCancel.getAdjustDuration()));
            } else {
                Long startTime = empLeave.getStartTime();
                Long endTime = empLeave.getEndTime();
                Integer periodType = empLeave.getPeriodType() != null ? Integer.valueOf(empLeave.getPeriodType()) : null;
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_START_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime)));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_END_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime)));
                } else {
                    if (empLeave.getShiftStartTime() != null && empLeave.getShiftEndTime() != null) {
                        startTime = empLeave.getShiftStartTime();
                        endTime = empLeave.getShiftEndTime();
                    }
                    if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                        Long start = empLeave.getStartTime();
                        Long end = empLeave.getEndTime();
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_START_TIME, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeave.getShalfDay()))));
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_END_TIME, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeave.getEhalfDay()))));
                    } else {
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_START_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime)));
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_END_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime)));
                    }
                }
                if (empLeave.getTimeUnit() == 1) {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_APPLY_DURATION, null).getMsg(), empLeave.getTotalTimeDuration() + PreTimeUnitEnum.getName(empLeave.getTimeUnit())));
                } else {
                    BigDecimal applyTime = BigDecimal.valueOf(empLeave.getTotalTimeDuration());
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_APPLY_DURATION, null).getMsg(), applyTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit())));
                }
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empLeave.getI18nLeaveName(), empLeave.getLeaveName())));
            }
            // 休假班次
            List<String> shiftDefNames;
            if (Optional.ofNullable(empLeave).isPresent() && CollectionUtils.isNotEmpty(shiftDefNames = empLeave.getShiftDefNameList())) {
                list.add(getKeyValue(MessageHandler.getMessage("caidao.exception.error_202818", WebUtil.getRequest()), StringUtils.join(shiftDefNames, "、")));
            }
        } else {
            if (Optional.ofNullable(empLeave).isPresent()) {
                Long startTime = empLeave.getStartTime();
                Long endTime = empLeave.getEndTime();
                Integer periodType = empLeave.getPeriodType() != null ? Integer.valueOf(empLeave.getPeriodType()) : null;
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime)));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime)));
                } else {
                    if (empLeave.getShiftStartTime() != null && empLeave.getShiftEndTime() != null) {
                        startTime = empLeave.getShiftStartTime();
                        endTime = empLeave.getShiftEndTime();
                    }
                    if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                        Long start = empLeave.getStartTime();
                        Long end = empLeave.getEndTime();
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeave.getShalfDay()))));
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeave.getEhalfDay()))));
                    } else {
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime)));
                        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime)));
                    }
                }
                if (empLeave.getTimeUnit() == 1) {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_DURATION, null).getMsg(), empLeave.getTotalTimeDuration() + PreTimeUnitEnum.getName(empLeave.getTimeUnit())));
                    BigDecimal applyTime = BigDecimal.valueOf(empLeave.getTotalTimeDuration());
                    Float cancelTime = empLeave.getCancelTimeDuration() == null ? 0 : empLeave.getCancelTimeDuration();
                    BigDecimal realTime = applyTime.subtract(BigDecimal.valueOf(cancelTime));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ACTUAL_DURATION, null).getMsg(), realTime.floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit())));
                } else {
                    BigDecimal applyTime = BigDecimal.valueOf(empLeave.getTotalTimeDuration());
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_DURATION, null).getMsg(), applyTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit())));
                    Float cancelTime = empLeave.getCancelTimeDuration() == null ? 0 : empLeave.getCancelTimeDuration();
                    BigDecimal realTime = applyTime.subtract(BigDecimal.valueOf(cancelTime));
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ACTUAL_DURATION, null).getMsg(), realTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit())));
                }
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empLeave.getI18nLeaveName(), empLeave.getLeaveName())));
            }
        }
        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_TYPE, null).getMsg(), LeaveCancelTypeEnum.getName(empLeaveCancel.getTypeId())));
        if (LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex().equals(empLeaveCancel.getTypeId().intValue())) {
            // 销假时间
            List<WaEmpLeaveCancelInfoDo> leaveCancelInfoList = waEmpLeaveCancelInfoDomainService.getListByLeaveCancelId(new ArrayList<>(Collections.singletonList(empLeaveCancel.getLeaveCancelId())));
            if (CollectionUtils.isNotEmpty(leaveCancelInfoList)) {
                String shiftStr = leaveCancelInfoList.stream()
                        .map(o -> CollectionUtils.isNotEmpty(o.getShiftDefNameList(tenantId)) ? StringUtils.join(o.getShiftDefNameList(tenantId), "、") : "")
                        .filter(StringUtils::isNotEmpty).collect(Collectors.joining("<br/>"));
                if (StringUtils.isNotEmpty(shiftStr)) {
                    list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_SHIFT, null).getMsg(), shiftStr));
                }
                String timeStr = leaveCancelInfoList.stream().map(this::formatTimePeriod).collect(Collectors.joining("<br/>"));
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_TIME, null).getMsg(), timeStr));
            }

            if (empLeaveCancel.getTimeUnit() == 1) {
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), empLeaveCancel.getTimeDuration() + PreTimeUnitEnum.getName(empLeaveCancel.getTimeUnit())));
            } else {
                BigDecimal a = BigDecimal.valueOf(empLeaveCancel.getTimeDuration());
                list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), a.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeaveCancel.getTimeUnit())));
            }
        } else {
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_TIME, null).getMsg(), "-"));
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), "-"));
        }

        if (empLeaveCancel.getCreateTime() != null) {
            list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(empLeaveCancel.getCreateTime())));
        }
        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(empLeaveCancel.getStatus())));
        list.add(getKeyValue(ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), empLeaveCancel.getReason() == null ? "-" : empLeaveCancel.getReason()));
        detailDto.setItems(list);
        detailDto.setFiles(empLeaveCancel.getFileId());
        detailDto.setFileNames(empLeaveCancel.getFileName());
        detailDto.setFullPath(empLeaveCancel.getFullPath());
        return detailDto;
    }

    private String formatTimePeriod(WaEmpLeaveCancelInfoDo o) {
        Integer periodType = Integer.valueOf(o.getPeriodType());
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) ||
                PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
            return String.format("%s~%s", DateUtil.getDateStrByTimesamp(o.getStartTime()), DateUtil.getDateStrByTimesamp(o.getEndTime()));
        }
        if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
            return String.format("%s~%s",
                    String.format("%s%s",
                            DateUtil.getDateStrByTimesamp(o.getStartTime()),
                            DayHalfTypeEnum.getDesc(o.getShalfDay())),
                    String.format("%s%s",
                            DateUtil.getDateStrByTimesamp(o.getEndTime()),
                            DayHalfTypeEnum.getDesc(o.getEhalfDay())));
        }
        return String.format("%s~%s", DateUtil.getTimeStrByTimesamp4(o.getShiftStartTime()), DateUtil.getTimeStrByTimesamp4(o.getShiftEndTime()));
    }

    private KeyValue getKeyValue(String text, Object value) {
        return new KeyValue(text, value);
    }

    @Override
    public void handleWorkflowEvent(String businessKey, String choice, Long eventTime) {
        //Integer funcType = getFuncType(businessKey);
        String funcCode = getFuncCode(businessKey);
        FuncTypeEnum func = FuncTypeEnum.getFuncCodeEnum(funcCode);
        if (null != func) {
            String[] businessKeys = businessKey.split("_");
            func.handleEvent(businessKeys[0], eventTime);
        }
    }

    private BusinessCodeEnum getBusinessCodeEnum(String businessKey) {
        if (StringUtil.isBlank(businessKey) || !businessKey.contains("_")) {
            return null;
        }
        String[] businessKeys = businessKey.split("_");
        String code = businessKeys[1];
        for (BusinessCodeEnum one : BusinessCodeEnum.values()) {
            if (one.getCode().equals(code)) {
                return one;
            }
        }
        return null;
    }

    /**
     * 查看流程详情
     *
     * @param tenantId
     * @param businessKey
     * @param summary
     * @return
     */
    @Override
    public WfResponseDto getWfDetail(String tenantId, String businessKey, boolean summary) {
        log.info("WfService.getWfDetail start businessKey={}, summary={}", businessKey, summary);
        Long businessId = getBusinessId(businessKey);
        if (businessId == null) {
            log.info("WfService.getWfDetail businessId empty businessKey={}, summary={}", businessKey, summary);
            return null;
        }
        BusinessCodeEnum funcType = getBusinessCodeEnum(businessKey);
        if (funcType == null) {
            log.info("WfService.getWfDetail businessId funcType businessKey={}, summary={}", businessKey, summary);
            return null;
        }
        Long corpId = tenantId == null ? null : ConvertHelper.longConvert(tenantId);
        switch (funcType) {
            case LEAVE:
                WfResponseDto wfDetailDto = getLeaveDetail(tenantId, businessId);
                // 查询销假单明细
                List<LeaveCancelTimePeriodDto> cancelTimePeriodDtoList = waEmpLeaveCancelDomainService.getLeaveCancelTimePeriodList(tenantId, businessId.intValue());
                wfDetailDto.setLeaveCancelList(Lists.newArrayList());
                if (CollectionUtils.isNotEmpty(cancelTimePeriodDtoList)) {
                    List<LeaveTimeWfDetailDto> leaveCancelList = cancelTimePeriodDtoList.stream().map(o -> {
                        LeaveTimeWfDetailDto detailDto = new LeaveTimeWfDetailDto();
                        if (Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex()).equals(o.getTypeId())) {
                            //detailDto.setTimeSlot(DateUtil.getTimeStrByTimesamp4(o.getShiftStartTime()) + "~" + DateUtil.getTimeStrByTimesamp4(o.getShiftEndTime()));
                            Integer periodType = Integer.valueOf(o.getPeriodType());
                            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                                detailDto.setTimeSlot(String.format("%s~%s", DateUtil.getDateStrByTimesamp(o.getStartTime()), DateUtil.getDateStrByTimesamp(o.getEndTime())));
                            } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                                String startDateStr = String.format("%s%s", DateUtil.getDateStrByTimesamp(o.getStartTime()), DayHalfTypeEnum.getDesc(o.getShalfDay()));
                                String endDateStr = String.format("%s%s", DateUtil.getDateStrByTimesamp(o.getEndTime()), DayHalfTypeEnum.getDesc(o.getEhalfDay()));
                                detailDto.setTimeSlot(String.format("%s~%s", startDateStr, endDateStr));
                            } else {
                                detailDto.setTimeSlot(String.format("%s~%s", DateUtil.getTimeStrByTimesamp4(o.getShiftStartTime()), DateUtil.getTimeStrByTimesamp4(o.getShiftEndTime())));
                            }
                            Float timeDuration = o.getTimeDuration();
                            if (o.getTimeUnit().equals(LeaveTypeUnitEnum.HOUR.getIndex())) {
                                timeDuration = new BigDecimal(timeDuration).divide(new BigDecimal(60), 2, RoundingMode.DOWN).floatValue();
                            }
                            detailDto.setTimeDuration(timeDuration + TravelTypeUnitEnum.getName(o.getTimeUnit()));
                        }
                        detailDto.setTypeId(o.getTypeId());
                        detailDto.setFileId(o.getFileId());
                        detailDto.setFileName(o.getFileName());
                        detailDto.setReason(o.getReason());
                        detailDto.setStatus(o.getStatus());
                        detailDto.setStatusName(ApprovalStatusEnum.getName(Integer.valueOf(o.getStatus())));

                        //组装文件，方便前端处理
                        String fileStr = o.getFileId();
                        String fileNameStr = o.getFileName();
                        List<WfAttachmentDto> fileList = new ArrayList<>();
                        if (StringUtils.isNotBlank(fileStr) && StringUtils.isNotBlank(fileNameStr)) {
                            String[] files = fileStr.split(",");
                            String[] fileNames = fileNameStr.split(",");
                            for (int i = 0; i < files.length; i++) {
                                WfAttachmentDto attachmentDto = new WfAttachmentDto();
                                attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                                attachmentDto.setFileUrl(files[i]);
                                attachmentDto.setFileName(fileNames[i]);
                                fileList.add(attachmentDto);
                            }
                            detailDto.setFileList(fileList);
                        }
                        return detailDto;
                    }).collect(Collectors.toList());
                    wfDetailDto.setLeaveCancelList(leaveCancelList);
                }
                return wfDetailDto;
            case OVERTIME:
                return getOverTimeDetail(corpId, businessId);
            case TRAVEL:
                return getTravelDetail(corpId, businessId);
            case BATCH_TRAVEL:
                return SpringUtil.getBean(WaBatchTravelService.class).getWfDetail(corpId, businessId, summary);
            case BATCH_LEAVE:
                return SpringUtil.getBean(WaBatchLeaveService.class).getWfDetail(businessId, summary);
            case BATCH_OVERTIME:
                return SpringUtil.getBean(WaBatchOvertimeService.class).getWfDetail(businessId, summary);
            case BATCH_ANALYSE_ADJUST:
                return SpringUtil.getBean(WaBatchAnalyseResultAdjustService.class).getWfDetail(businessId, summary);
            case REGISTER:
                return getRegisterDetail(corpId, tenantId, businessId);
            case SHIFT:
                return getShiftChangeDetail(corpId, businessId);
            case VACATION:
                return getLeaveCancelDetailV1(tenantId, businessId);
            case COMPENSATORY:
                return getCompensatoryCaseDetail(tenantId, businessId);
            case OVERTIME_REVOKE:
            case OVERTIME_ABOLISH:
                return getOverTimeRevokeDetail(tenantId, businessId);
            case TRAVEL_REVOKE:
            case TRAVEL_ABOLISH:
                return getEmpTravelRevokeDetail(tenantId, businessId);
            case LEAVE_EXTENSION:
                return getEmpLeaveExtensionDetail(tenantId, businessId);
        }
        return new WfResponseDto();
    }

    private WfResponseDto getEmpLeaveExtensionDetail(String tenantId, Long businessId) {
        WfResponseDto detailDto = new WfResponseDto();
        Optional<WaLeaveExtensionDo> optional = Optional.ofNullable(leaveExtensionDo.getById(tenantId, businessId));
        if (optional.isPresent()) {
            WaLeaveExtensionDo leaveExtension = optional.get();
            List<WfBusinessDataDetailDto> list = new ArrayList<>();
            //发起人、申请人、假期类型、开始时间、结束时间、申请时长、审批状态、申请事由
            list.add(new WfBusinessDataDetailDto("originator", ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(leaveExtension.getCreateBy()), null));
            if (StringUtils.isNotBlank(leaveExtension.getWorkNo()) && StringUtils.isNotBlank(leaveExtension.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), leaveExtension.getWorkNo() + "(" + leaveExtension.getEmpName() + ")", null));
            } else if (StringUtils.isNotBlank(leaveExtension.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), leaveExtension.getEmpName(), null));
            } else if (StringUtils.isNotBlank(leaveExtension.getWorkNo())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), leaveExtension.getWorkNo(), null));
            }
            list.add(new WfBusinessDataDetailDto("empType", ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), Optional.ofNullable(leaveExtension.getEmployType()).orElse("-"), null));
            list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), Optional.ofNullable(leaveExtension.getOrgName()).orElse("-"), null));
            list.add(new WfBusinessDataDetailDto("workplace", ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), Optional.ofNullable(leaveExtension.getWorkCity()).orElse("-"), null));
            list.add(new WfBusinessDataDetailDto("hireDate", ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), leaveExtension.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(leaveExtension.getHireDate()), null));
            list.add(new WfBusinessDataDetailDto("quotaName", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(leaveExtension.getI18nRuleName(), leaveExtension.getQuotaName()), null));
            list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_QUOTA, null).getMsg(), Optional.ofNullable(leaveExtension.getTimeDuration()).orElse(0f), null));
            list.add(new WfBusinessDataDetailDto("unit", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_QUOTA_UNIT, null).getMsg(), PreTimeUnitEnum.getName(leaveExtension.getTimeUnit()), null));
            list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_START_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(leaveExtension.getStartDate()), null));
            list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_END_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(leaveExtension.getEndDate()), null));
            list.add(new WfBusinessDataDetailDto("approvalStatus", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(leaveExtension.getStatus()), null));
            list.add(new WfBusinessDataDetailDto("reason", ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), Optional.ofNullable(leaveExtension.getReason()).orElse("-"), null));
            list.add(new WfBusinessDataDetailDto("revokeReason", ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON, null).getMsg(), Optional.ofNullable(leaveExtension.getRevokeReason()).orElse("-"), null));
            list.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), leaveExtension.getProcessCode() == null ? "-" : leaveExtension.getProcessCode(), null));

            String fileStr = leaveExtension.getFileId();
            String fileNameStr = leaveExtension.getFileName();
            List<WfAttachmentDto> fileList = new ArrayList<>();
            if (StringUtils.isNotBlank(fileStr) && StringUtils.isNotBlank(fileNameStr)) {
                String[] files = fileStr.split(",");
                String[] fileNames = fileNameStr.split(",");
                for (int i = 0; i < files.length; i++) {
                    WfAttachmentDto attachmentDto = new WfAttachmentDto();
                    attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                    attachmentDto.setFileUrl(files[i]);
                    attachmentDto.setFileName(fileNames[i]);
                    fileList.add(attachmentDto);
                }
                list.add(new WfBusinessDataDetailDto("file", ResponseWrap.wrapResult(AttendanceCodes.ATTACHMENT, null).getMsg(), "", fileList));
            }
            detailDto.setDetailList(list);
        }
        return detailDto;
    }


    /**
     * 获取请假详情
     *
     * @param businessId
     * @return
     */
    private WfResponseDto getLeaveDetail(String tenantId, Long businessId) {
        WfResponseDto responseDto = new WfResponseDto();
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getLeaveDetailById(tenantId, businessId);
        if (empLeave != null) {
            List<WfBusinessDataDetailDto> list = new ArrayList<>();
            //发起人、申请人、假期类型、开始时间、结束时间、申请时长、审批状态、申请事由
            list.add(new WfBusinessDataDetailDto("originator", ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(empLeave.getCrtuser()), null));
            if (StringUtils.isNotBlank(empLeave.getWorkno()) && StringUtils.isNotBlank(empLeave.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeave.getWorkno() + "(" + empLeave.getEmpName() + ")", null));
            } else if (StringUtils.isNotBlank(empLeave.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeave.getEmpName(), null));
            } else if (StringUtils.isNotBlank(empLeave.getWorkno())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeave.getWorkno(), null));
            }
            list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), empLeave.getFullPath(), null));
            list.add(new WfBusinessDataDetailDto("empType", ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), empLeave.getEmployType(), null));
            list.add(new WfBusinessDataDetailDto("workplace", ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), empLeave.getWorkCity() == null ? "-" : empLeave.getWorkCity(), null));
            list.add(new WfBusinessDataDetailDto("hireDate", ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), empLeave.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(empLeave.getHireDate()), null));
            if (empLeave.getLeaveType() == 8) {
                list.add(new WfBusinessDataDetailDto("maritalStatus", ResponseWrap.wrapResult(AttendanceCodes.MARITAL_STATUS, null).getMsg(), empLeave.getMarriage() == null ? "-" : empLeave.getMarriage(), null));
            } else if (empLeave.getLeaveType() == 4) {
                list.add(new WfBusinessDataDetailDto("childNumber", ResponseWrap.wrapResult(AttendanceCodes.NUMBER_OF_CHILD, null).getMsg(), empLeave.getChildNum() == null ? "-" : empLeave.getChildNum(), null));
                list.add(new WfBusinessDataDetailDto("maternityLeave", ResponseWrap.wrapResult(AttendanceCodes.TYPE_OF_MATERNITY_LEAVE, null).getMsg(), empLeave.getMaternityLeaveType() == null ? "-" :
                        MaternityLeaveTypeEnum.getNameByIndex(empLeave.getMaternityLeaveType()), null));
                list.add(new WfBusinessDataDetailDto("childBirthday", ResponseWrap.wrapResult(AttendanceCodes.CHILD_BIRTHDAY, null).getMsg(), empLeave.getManufactureDate() == null ? "-" : DateUtil.getDateStrByTimesamp(empLeave.getManufactureDate()), null));
            }
            list.add(new WfBusinessDataDetailDto("revokeReason",
                    ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON, null).getMsg(),
                    StringUtils.defaultString(empLeave.getRevokeReason(), ""), null));
            // 休假班次
            List<String> shiftDefNames;
            if (CollectionUtils.isNotEmpty(shiftDefNames = empLeave.getShiftDefNameList())) {
                list.add(new WfBusinessDataDetailDto("vacationShift",
                        MessageHandler.getMessage("caidao.exception.error_202818", WebUtil.getRequest()),
                        StringUtils.join(shiftDefNames, "、"), null));
            }
            Long startTime = empLeave.getStartTime();
            Long endTime = empLeave.getEndTime();
            Integer periodType = empLeave.getPeriodType() != null ? Integer.valueOf(empLeave.getPeriodType()) : null;
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime), null));
                list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime), null));
            } else {
                if (empLeave.getShiftStartTime() != null && empLeave.getShiftEndTime() != null) {
                    startTime = empLeave.getShiftStartTime();
                    endTime = empLeave.getShiftEndTime();
                }
                if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    Long start = empLeave.getStartTime();
                    Long end = empLeave.getEndTime();
                    list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeave.getShalfDay())), null));
                    list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeave.getEhalfDay())), null));
                } else {
                    list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime), null));
                    list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime), null));
                }
            }
            if (empLeave.getTimeUnit() == 1) {
                list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), empLeave.getTotalTimeDuration() + PreTimeUnitEnum.getName(empLeave.getTimeUnit()), null));
                list.add(new WfBusinessDataDetailDto("actualDuration", ResponseWrap.wrapResult(AttendanceCodes.ACTUAL_DURATION, null).getMsg(), empLeave.getActualTimeDuration() + PreTimeUnitEnum.getName(empLeave.getTimeUnit()), null));
            } else {
                BigDecimal a = BigDecimal.valueOf(empLeave.getTotalTimeDuration());
                BigDecimal b = BigDecimal.valueOf(empLeave.getTotalTimeDuration());
                list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), a.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit()), null));
                list.add(new WfBusinessDataDetailDto("actualDuration", ResponseWrap.wrapResult(AttendanceCodes.ACTUAL_DURATION, null).getMsg(), b.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit()), null));
            }
            if (empLeave.getCrttime() != null) {
                list.add(new WfBusinessDataDetailDto("applyTime", ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(empLeave.getCrttime()), null));
            }
            list.add(new WfBusinessDataDetailDto("leaveType", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empLeave.getI18nLeaveName(), empLeave.getLeaveName()), null));
            list.add(new WfBusinessDataDetailDto("approvalStatus", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(empLeave.getStatus()), null));
            if (StringUtils.isNotBlank(empLeave.getRevokeReason())) {
                list.add(new WfBusinessDataDetailDto("revokeReason", ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON, null).getMsg(), empLeave.getRevokeReason(), null));
            }
            list.add(new WfBusinessDataDetailDto("reason", ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), empLeave.getReason(), null));
            list.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), empLeave.getProcessCode() == null ? "-" : empLeave.getProcessCode(), null));

            String fileStr = empLeave.getFiles();
            String fileNameStr = empLeave.getFileNames();
            List<WfAttachmentDto> fileList = new ArrayList<>();
            if (StringUtils.isNotBlank(fileStr) && StringUtils.isNotBlank(fileNameStr)) {
                String[] files = fileStr.split(",");
                String[] fileNames = fileNameStr.split(",");
                for (int i = 0; i < files.length; i++) {
                    WfAttachmentDto attachmentDto = new WfAttachmentDto();
                    attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                    attachmentDto.setFileUrl(files[i]);
                    attachmentDto.setFileName(fileNames[i]);
                    fileList.add(attachmentDto);
                }
                list.add(new WfBusinessDataDetailDto("file", ResponseWrap.wrapResult(AttendanceCodes.ATTACHMENT, null).getMsg(), "", fileList));
            }
            responseDto.setDetailList(list);
            if (StringUtil.isNotBlank(empLeave.getQuotaDetail())) {
                List<LeaveQuotaDetailDto> quotaList = JSON.parseArray(empLeave.getQuotaDetail(), LeaveQuotaDetailDto.class);
                if (CollectionUtils.isNotEmpty(quotaList)) {
                    List<Integer> leaveTypeIds = quotaList.stream().map(LeaveQuotaDetailDto::getLeaveTypeId).collect(Collectors.toList());
                    List<WaLeaveTypeDo> leaveTypes = waLeaveTypeDo.getLeaveTypesByIds(tenantId, leaveTypeIds);
                    Map<Integer, WaLeaveTypeDo> leaveTypeMap = leaveTypes.stream().collect(Collectors.toMap(WaLeaveTypeDo::getLeaveTypeId, Function.identity(), (v1, v2) -> v1));
                    List<QuotaDetailDto> quotas = Lists.newArrayList();
                    quotaList.forEach(quota -> {
                        if (leaveTypeMap.containsKey(quota.getLeaveTypeId())) {
                            WaLeaveTypeDo leaveType = leaveTypeMap.get(quota.getLeaveTypeId());
                            String durationTxt = String.format("%s%s", quota.getDuration().toString(), PreTimeUnitEnum.getName(leaveType.getAcctTimeType()));
                            quotas.add(new QuotaDetailDto(LangParseUtil.getI18nLanguage(leaveType.getI18nLeaveName(), leaveType.getLeaveName()), durationTxt));
                        }
                    });
                    responseDto.setQuotaList(quotas);
                }
            }
        }
        return responseDto;
    }

    /**
     * 加班单明细
     *
     * @param corpId
     * @param businessId
     * @return
     */
    private WfResponseDto getOverTimeDetail(Long corpId, Long businessId) {
        WfResponseDto responseDto = new WfResponseDto();
        WaEmpOvertimeDo overtimeDo = waEmpOvertimeDo.getOtDetailById(corpId, businessId);
        if (overtimeDo != null) {
            List<WfBusinessDataDetailDto> list = new ArrayList<>();
            //发起人、申请人、开始时间、结束时间、申请时长、补偿类型、审批状态、申请事由
            list.add(new WfBusinessDataDetailDto("originator", ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(overtimeDo.getCrtuser()), null));
            if (StringUtils.isNotBlank(overtimeDo.getWorkno()) && StringUtils.isNotBlank(overtimeDo.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getWorkno() + "(" + overtimeDo.getEmpName() + ")", null));
            } else if (StringUtils.isNotBlank(overtimeDo.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getEmpName(), null));
            } else if (StringUtils.isNotBlank(overtimeDo.getWorkno())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getWorkno(), null));
            }
            list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), overtimeDo.getFullPath(), null));
            list.add(new WfBusinessDataDetailDto("workplace", ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), overtimeDo.getWorkCity() == null ? "-" : overtimeDo.getWorkCity(), null));
            list.add(new WfBusinessDataDetailDto("hireDate", ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), overtimeDo.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(overtimeDo.getHireDate()), null));
            list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(overtimeDo.getStartTime()), null));
            list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(overtimeDo.getEndTime()), null));
            list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), overtimeDo.getOtDurationDesc(), null));
            if (overtimeDo.getCrttime() != null) {
                list.add(new WfBusinessDataDetailDto("applyTime", ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(overtimeDo.getCrttime()), null));
            }
            OverTimeDto overTimeDto = null;
            if (null != overtimeDo.getOvertimeTypeId()) {
                Optional<OverTimeDto> overTimeDtoOpt = Optional.ofNullable(overTimeService.getOtType(overtimeDo.getOvertimeTypeId()));
                if (overTimeDtoOpt.isPresent()) {
                    overTimeDto = overTimeDtoOpt.get();
                }
            }
            list.add(new WfBusinessDataDetailDto("overtimeType", ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE, null).getMsg(), overTimeDto == null ? "-" : LangParseUtil.getI18nLanguage(overTimeDto.getI18nTypeName(), overTimeDto.getTypeName()), null));
            list.add(new WfBusinessDataDetailDto("compensateType", ResponseWrap.wrapResult(AttendanceCodes.COMPENSATORY_TYPE, null).getMsg(), CompensateTypeEnum.getDescByOrdinal(overtimeDo.getCompensateType()), null));
            // 本周期内加班总时长
            String periodOtDuration = String.format("%s%s",
                    getEmpPeriodOtDuration(overtimeDo.getBelongOrgId(), overtimeDo.getEmpid(), overtimeDo.getStartTime()),
                    PreTimeUnitEnum.getName(2));
            list.add(new WfBusinessDataDetailDto("periodOtDuration", ResponseWrap.wrapResult(AttendanceCodes.PERIOD_OT_DURATION, null).getMsg(), periodOtDuration));
            list.add(new WfBusinessDataDetailDto("approvalStatus", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(overtimeDo.getStatus()), null));
            list.add(new WfBusinessDataDetailDto("revokeReason", ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON, null).getMsg(), StringUtils.defaultString(overtimeDo.getRevokeReason(), ""), null));
            list.add(new WfBusinessDataDetailDto("reason", ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), overtimeDo.getReason() == null ? "-" : overtimeDo.getReason(), null));
            list.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), overtimeDo.getProcessCode() == null ? "-" : overtimeDo.getProcessCode(), null));

            String fileStr = overtimeDo.getFiles();
            String fileNameStr = overtimeDo.getFileNames();
            List<WfAttachmentDto> fileList = new ArrayList<>();
            if (StringUtils.isNotBlank(fileStr) && StringUtils.isNotBlank(fileNameStr)) {
                String[] files = fileStr.split(",");
                String[] fileNames = fileNameStr.split(",");
                for (int i = 0; i < files.length; i++) {
                    WfAttachmentDto attachmentDto = new WfAttachmentDto();
                    attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                    attachmentDto.setFileUrl(files[i]);
                    attachmentDto.setFileName(fileNames[i]);
                    fileList.add(attachmentDto);
                }
                list.add(new WfBusinessDataDetailDto("file", ResponseWrap.wrapResult(AttendanceCodes.ATTACHMENT, null).getMsg(), "", fileList));
            }
            responseDto.setDetailList(list);
        }
        return responseDto;
    }

    /**
     * 出差单明细
     *
     * @param corpId
     * @param businessId
     * @return
     */
    private WfResponseDto getTravelDetail(Long corpId, Long businessId) {
        WfResponseDto responseDto = new WfResponseDto();
        Optional<WaEmpTravelDo> optional = Optional.ofNullable(waEmpTravelDo.getWaEmpTravelDetailById(businessId, corpId));
        if (optional.isPresent()) {
            WaEmpTravelDo empTravel = optional.get();
            List<WfBusinessDataDetailDto> list = new ArrayList<>();
            //发起人、申请人、假期类型、开始时间、结束时间、申请时长、审批状态、申请事由
            list.add(new WfBusinessDataDetailDto("originator", ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(empTravel.getCreateBy()), null));
            if (StringUtils.isNotBlank(empTravel.getWorkNo()) && StringUtils.isNotBlank(empTravel.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getWorkNo() + "(" + empTravel.getEmpName() + ")", null));
            } else if (StringUtils.isNotBlank(empTravel.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getEmpName(), null));
            } else if (StringUtils.isNotBlank(empTravel.getWorkNo())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getWorkNo(), null));
            }
            list.add(new WfBusinessDataDetailDto("empType", ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), empTravel.getEmployType(), null));
            list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), empTravel.getFullPath(), null));
            list.add(new WfBusinessDataDetailDto("workplace", ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), empTravel.getWorkCity() == null ? "-" : empTravel.getWorkCity(), null));
            list.add(new WfBusinessDataDetailDto("hireDate", ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), empTravel.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(empTravel.getHireDate()), null));

            Long startTime = empTravel.getStartTime();
            Long endTime = empTravel.getEndTime();
            Integer periodType = Integer.valueOf(empTravel.getPeriodType());
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime), null));
                list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime), null));
            } else {
                if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(startTime), DayHalfTypeEnum.getDesc(empTravel.getShalfDay())), null));
                    list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(endTime), DayHalfTypeEnum.getDesc(empTravel.getEhalfDay())), null));
                } else {
                    if (empTravel.getShiftStartTime() != null && empTravel.getShiftEndTime() != null) {
                        startTime = empTravel.getShiftStartTime();
                        endTime = empTravel.getShiftEndTime();
                    }
                    list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime), null));
                    list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime), null));
                }
            }
            if (empTravel.getTimeUnit() == 1) {
                list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), empTravel.getTimeDuration() + TravelTypeUnitEnum.getName(empTravel.getTimeUnit()), null));
            } else {
                BigDecimal a = BigDecimal.valueOf(empTravel.getTimeDuration());
                list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), a.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + TravelTypeUnitEnum.getName(empTravel.getTimeUnit()), null));
            }
            if (empTravel.getCreateTime() != null) {
                list.add(new WfBusinessDataDetailDto("applyTime", ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(empTravel.getCreateTime()), null));
            }
            list.add(new WfBusinessDataDetailDto("travelType", ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empTravel.getI18nTravelTypeName(), empTravel.getTravelType()), null));
            SysUnitCity proviceObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, empTravel.getProvince());
            SysUnitCity cityObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_CITY_KEY, empTravel.getCity());
            String province = "";
            String city = "";
            if (proviceObj != null) {
                province = proviceObj.getChnName();
            }
            if (cityObj != null) {
                city = cityObj.getChnName();
            }
            String location = province + " " + city;
            list.add(new WfBusinessDataDetailDto("site", ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_PLACE, null).getMsg(), location, null));
            String travelMode = empTravel.getTravelMode();
            list.add(new WfBusinessDataDetailDto("travelMode", ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_MODE, null).getMsg(), Optional.ofNullable(travelMode).orElse("-"), null));
            list.add(new WfBusinessDataDetailDto("approvalStatus", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(empTravel.getStatus()), null));
            list.add(new WfBusinessDataDetailDto("reason", ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), empTravel.getReason() == null ? "-" : empTravel.getReason(), null));
            list.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), empTravel.getProcessCode() == null ? "-" : empTravel.getProcessCode(), null));
            String fileStr = empTravel.getFileId();
            String fileNameStr = empTravel.getFileName();
            List<WfAttachmentDto> fileList = new ArrayList<>();
            if (StringUtils.isNotBlank(fileStr) && StringUtils.isNotBlank(fileNameStr)) {
                String[] files = fileStr.split(",");
                String[] fileNames = fileNameStr.split(",");
                for (int i = 0; i < files.length; i++) {
                    WfAttachmentDto attachmentDto = new WfAttachmentDto();
                    attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                    attachmentDto.setFileUrl(files[i]);
                    attachmentDto.setFileName(fileNames[i]);
                    fileList.add(attachmentDto);
                }
                list.add(new WfBusinessDataDetailDto("file", ResponseWrap.wrapResult(AttendanceCodes.ATTACHMENT, null).getMsg(), "", fileList));
            }
            responseDto.setDetailList(list);
        }
        return responseDto;
    }

    /**
     * 补卡明细
     *
     * @param corpId
     * @param tenantId
     * @param businessId
     * @return
     */
    private WfResponseDto getRegisterDetail(Long corpId, String tenantId, Long businessId) {
        WfResponseDto responseDto = new WfResponseDto();
        Optional<WaRegisterRecordBdkDo> optional = Optional.ofNullable(registerRecordBdkDo.getRegisterDetailById(corpId, businessId));
        if (optional.isPresent()) {
            List<WfBusinessDataDetailDto> list = new ArrayList<>();
            WaRegisterRecordBdkDo registerRecord = optional.get();
            //发起人、申请人、假期类型、开始时间、结束时间、申请时长、审批状态、申请事由
            list.add(new WfBusinessDataDetailDto("originator", ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(registerRecord.getCrtuser()), null));
            if (StringUtils.isNotBlank(registerRecord.getWorkno()) && StringUtils.isNotBlank(registerRecord.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), String.format("%s(%s)", registerRecord.getWorkno(), registerRecord.getEmpName()), null));
            } else if (StringUtils.isNotBlank(registerRecord.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), registerRecord.getEmpName(), null));
            } else {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), registerRecord.getWorkno(), null));
            }
            if (registerRecord.getCrttime() != null) {
                list.add(new WfBusinessDataDetailDto("applyTime", ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(registerRecord.getCrttime()), null));
            }
            list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), registerRecord.getFullPath(), null));
            list.add(new WfBusinessDataDetailDto("workplace", ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), registerRecord.getWorkCity() == null ? "-" : registerRecord.getWorkCity(), null));
            list.add(new WfBusinessDataDetailDto("hireDate", ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), registerRecord.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(registerRecord.getHireDate()), null));
            Long belongDate = registerRecord.getBelongDate();
            // 班次
            List<Integer> shiftDefIdList = new ArrayList<>();
            if (null != registerRecord.getShiftDefIds() && !registerRecord.getShiftDefIds().isEmpty()) {
                String[] shiftDefIds = registerRecord.getShiftDefIds().split(",");
                shiftDefIdList = Stream.of(shiftDefIds).map(Integer::valueOf)
                        .collect(Collectors.toCollection(java.util.ArrayList::new));
            } else if (null != registerRecord.getShiftDefId()) {
                shiftDefIdList = com.google.common.collect.Lists.newArrayList(registerRecord.getShiftDefId());
            }
            EmpShiftRecordDto empShiftRecordDto = empInfoService.getShiftAndRecords(belongDate,
                    registerRecord.getEmpid(), tenantId, shiftDefIdList);
            if (StringUtil.isNotBlank(empShiftRecordDto.getShiftName())) {
                list.add(new WfBusinessDataDetailDto("shiftName", ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NAME, null).getMsg(), empShiftRecordDto.getShiftName(), null));
            }
            if (StringUtils.isNotBlank(empShiftRecordDto.getShiftTimePeriod())) {
                list.add(new WfBusinessDataDetailDto("shiftTime", ResponseWrap.wrapResult(AttendanceCodes.SHIFT_TIME_RANGE, null).getMsg(), empShiftRecordDto.getShiftTimePeriod(), null));
            }
            List<Long> records = empShiftRecordDto.getRecords();
            if (CollectionUtils.isNotEmpty(records)) {
                String retPattern = "HH:mm";
                String recordStr;
                if (records.size() > 4) {
                    String fst = DateUtil.convertDateTimeToStr(records.stream().min(Long::compare).orElse(0L), retPattern, true);
                    String sec = DateUtil.convertDateTimeToStr(records.stream().max(Long::compare).orElse(0L), retPattern, true);
                    recordStr = String.format("%s...%s", fst, sec);
                } else {
                    recordStr = records.stream().map(r -> DateUtil.convertDateTimeToStr(r, retPattern, true)).collect(Collectors.joining(","));
                }
                list.add(new WfBusinessDataDetailDto("records", ResponseWrap.wrapResult(AttendanceCodes.REGISTER_RECORD, null).getMsg(), recordStr, null));
            } else {
                list.add(new WfBusinessDataDetailDto("records", ResponseWrap.wrapResult(AttendanceCodes.REGISTER_RECORD, null).getMsg(),
                        ResponseWrap.wrapResult(AttendanceCodes.NOT_REGISTER_RECORD, null).getMsg(), null));
            }
            if (StringUtil.isNotBlank(registerRecord.getRegDateTimes())) {
                list.add(new WfBusinessDataDetailDto("regDateTime", ResponseWrap.wrapResult(AttendanceCodes.FILL_CARD_TIME, null).getMsg(), Arrays.stream(registerRecord.getRegDateTimes().split(",")).map(r -> DateUtil.getTimeStrByTimesamp(Long.valueOf(r))).collect(Collectors.joining(",<br/>")), null));
            }
            list.add(new WfBusinessDataDetailDto("approvalStatus", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(registerRecord.getApprovalStatus()), null));
            list.add(new WfBusinessDataDetailDto("reason", ResponseWrap.wrapResult(AttendanceCodes.FILL_CARD_REASON, null).getMsg(), registerRecord.getReason(), null));
            String fileStr = registerRecord.getFiles();
            String fileNameStr = registerRecord.getFileNames();
            List<WfAttachmentDto> fileList = new ArrayList<>();
            if (StringUtils.isNotBlank(fileStr) && StringUtils.isNotBlank(fileNameStr)) {
                String[] files = fileStr.split(",");
                String[] fileNames = fileNameStr.split(",");
                for (int i = 0; i < files.length; i++) {
                    WfAttachmentDto attachmentDto = new WfAttachmentDto();
                    attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                    attachmentDto.setFileUrl(files[i]);
                    attachmentDto.setFileName(fileNames[i]);
                    fileList.add(attachmentDto);
                }
                list.add(new WfBusinessDataDetailDto("file", ResponseWrap.wrapResult(AttendanceCodes.ATTACHMENT, null).getMsg(), "", fileList));
            }
            responseDto.setDetailList(list);
        }
        return responseDto;
    }

    /**
     * 调班明细
     *
     * @param corpId     集团
     * @param businessId 业务id
     * @return
     */
    private WfResponseDto getShiftChangeDetail(Long corpId, Long businessId) {
        WfResponseDto responseDto = new WfResponseDto();
        WaShiftApplyRecordDo shift = waShiftApplyRecordDo.getShiftApplyInfoById(businessId, corpId);
        if (shift != null) {
            List<WfBusinessDataDetailDto> list = new ArrayList<>();
            //发起人
            list.add(new WfBusinessDataDetailDto("originator", ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(shift.getCreateBy()), null));
            //申请人
            if (StringUtils.isNotBlank(shift.getWorkNo()) && StringUtils.isNotBlank(shift.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), shift.getWorkNo() + "(" + shift.getEmpName() + ")", null));
            } else if (StringUtils.isNotBlank(shift.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), shift.getEmpName(), null));
            } else if (StringUtils.isNotBlank(shift.getWorkNo())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), shift.getWorkNo(), null));
            }
            if (shift.getHireDate() != null) {
                list.add(new WfBusinessDataDetailDto("hireDate", ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(shift.getHireDate()), null));
            }
            list.add(new WfBusinessDataDetailDto("empType", ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), shift.getEmployType(), null));
            list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), shift.getFullPath(), null));
            list.add(new WfBusinessDataDetailDto("workplace", ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), shift.getWorkCity() == null ? "-" : shift.getWorkCity(), null));
            list.add(new WfBusinessDataDetailDto("shiftChangeDate", ResponseWrap.wrapResult(AttendanceCodes.SHIFT_CHANGE_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(shift.getWorkDate()), null));

            String os = "";
            String od = "";
            String ns = "";
            String nd = "";
            //原班次
            Long nowDate = DateUtil.getOnlyDate();
            if (shift.getOldStartTime() != null && shift.getOldStartTime() >= 0) {
                os = DateUtil.convertDateTimeToStr(nowDate + (shift.getOldStartTime() * 60), "HH:mm", true);
            }

            if (shift.getOldEndTime() != null && shift.getOldEndTime() >= 0) {
                od = DateUtil.convertDateTimeToStr(nowDate + (shift.getOldEndTime() * 60), "HH:mm", true);
            }
            String oldShiftTxt = shift.getOldShift();
            if (StringUtils.isNotBlank(os) && StringUtils.isNotBlank(od)) {
                oldShiftTxt = oldShiftTxt + " " + (os + "-" + od);
            }
            list.add(new WfBusinessDataDetailDto("oldShift", ResponseWrap.wrapResult(AttendanceCodes.ORIGINAL_SHIFT, null).getMsg(), oldShiftTxt, null));
            //打卡记录
            Long daytime = shift.getWorkDate();
            RegisterRecordRequestDto requestDto = new RegisterRecordRequestDto();
            requestDto.setPageNo(1);
            requestDto.setPageSize(10000);
            requestDto.setEndDate(daytime.intValue() + (24 * 60 * 60) - 1);
            requestDto.setStartDate(daytime.intValue());
            UserInfo user = UserContext.preCheckUser();
            if (null == user) {
                user = getUser();
            }
            requestDto.setBelongOrgId(user.getTenantId());
            AttendancePageResult<WaRegisterRecordDo> registerRecord = registerRecordDo.getRegisterRecordPageListByEmpId(requestDto, shift.getEmpId());
            List<WaRegisterRecordDo> items = registerRecord.getItems();
            StringBuilder sb = new StringBuilder();

            if (items.size() > 4) {
                String fst = DateUtil.convertDateTimeToStr(items.get(0).getRegDateTime(), "HH:mm", true);
                String sec = DateUtil.convertDateTimeToStr(items.get(items.size() - 1).getRegDateTime(), "HH:mm", true);
                String regTime = String.format("%s,...,%s", fst, sec);
                list.add(new WfBusinessDataDetailDto("registerRecord", ResponseWrap.wrapResult(AttendanceCodes.REGISTER_RECORD, null).getMsg(), regTime, null));
            } else {
                for (WaRegisterRecordDo dto : items) {
                    String reg = DateUtil.convertDateTimeToStr(dto.getRegDateTime(), "HH:mm", true);
                    sb = sb.append(reg).append("、");
                }
                if (StringUtils.isNotBlank(sb)) {
                    String regTime = sb.substring(0, sb.length() - 1);
                    list.add(new WfBusinessDataDetailDto("registerRecord", ResponseWrap.wrapResult(AttendanceCodes.REGISTER_RECORD, null).getMsg(), regTime, null));
                } else {
                    list.add(new WfBusinessDataDetailDto("registerRecord", ResponseWrap.wrapResult(AttendanceCodes.REGISTER_RECORD, null).getMsg(),
                            ResponseWrap.wrapResult(AttendanceCodes.NO_REGISTER, null).getMsg(), null));
                }
            }
            //新班次
            if (shift.getNewStartTime() != null && shift.getNewStartTime() >= 0) {
                ns = DateUtil.convertDateTimeToStr(nowDate + (shift.getNewStartTime() * 60), "HH:mm", true);
            }

            if (shift.getOldEndTime() != null && shift.getOldEndTime() >= 0) {
                nd = DateUtil.convertDateTimeToStr(nowDate + (shift.getNewEndTime() * 60), "HH:mm", true);
            }
            String newShiftTxt = shift.getNewShift();
            if (StringUtils.isNotBlank(os) && StringUtils.isNotBlank(od)) {
                newShiftTxt = newShiftTxt + " " + (ns + "-" + nd);
            }
            list.add(new WfBusinessDataDetailDto("newShift", ResponseWrap.wrapResult(AttendanceCodes.CHANGED_SHIFT, null).getMsg(), newShiftTxt, null));
            list.add(new WfBusinessDataDetailDto("applyTime", ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(shift.getCreateTime()), null));
            list.add(new WfBusinessDataDetailDto("approvalStatus", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(shift.getStatus()), null));
            list.add(new WfBusinessDataDetailDto("reason", ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), shift.getReason() == null ? "-" : shift.getReason(), null));
            String fileStr = shift.getFileId();
            String fileNameStr = shift.getFileName();
            List<WfAttachmentDto> fileList = new ArrayList<>();
            if (StringUtils.isNotBlank(fileStr) && StringUtils.isNotBlank(fileNameStr)) {
                String[] files = fileStr.split(",");
                String[] fileNames = fileNameStr.split(",");
                for (int i = 0; i < files.length; i++) {
                    WfAttachmentDto attachmentDto = new WfAttachmentDto();
                    attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                    attachmentDto.setFileUrl(files[i]);
                    attachmentDto.setFileName(fileNames[i]);
                    fileList.add(attachmentDto);
                }
                list.add(new WfBusinessDataDetailDto("file", ResponseWrap.wrapResult(AttendanceCodes.ATTACHMENT, null).getMsg(), "", fileList));
            }
            responseDto.setDetailList(list);
        }
        return responseDto;
    }

    private String getInitiator(Long userId) {
        if (userId != null) {
            SysEmpInfo sysEmpInfo = empInfoPoMapper.selectByUserId(userId);
            if (sysEmpInfo != null) {
                return sysEmpInfo.getWorkno() + "(" + sysEmpInfo.getEmpName() + ")";
            }
        }
        return "管理员";
    }

    /**
     * 查看销假单详情
     *
     * @param tenantId   租户
     * @param businessId 业务id
     * @return
     */
    private WfResponseDto getLeaveCancelDetailV1(String tenantId, Long businessId) {
        WfResponseDto responseDto = new WfResponseDto();
        //发起人、申请人、员工类型、任职组织、工作地、入职日期、销假时间、申请时长、申请时间、假期类型、审批状态、申请事由
        WaEmpLeaveCancelDo empLeaveCancel = waEmpLeaveCancelDomainService.getInfoById(tenantId, businessId);
        if (empLeaveCancel == null) {
            return responseDto;
        }
        List<WfBusinessDataDetailDto> list = new ArrayList<>();
        list.add(new WfBusinessDataDetailDto("originator", ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(empLeaveCancel.getCreateBy()), null));
        if (StringUtils.isNotBlank(empLeaveCancel.getWorkNo()) && StringUtils.isNotBlank(empLeaveCancel.getEmpName())) {
            list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeaveCancel.getWorkNo() + "(" + empLeaveCancel.getEmpName() + ")", null));
        } else if (StringUtils.isNotBlank(empLeaveCancel.getEmpName())) {
            list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeaveCancel.getEmpName(), null));
        } else if (StringUtils.isNotBlank(empLeaveCancel.getWorkNo())) {
            list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empLeaveCancel.getWorkNo(), null));
        }
        list.add(new WfBusinessDataDetailDto("empType", ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), empLeaveCancel.getEmployType(), null));
        list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), empLeaveCancel.getFullPath(), null));
        list.add(new WfBusinessDataDetailDto("workplace", ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), empLeaveCancel.getWorkCity() == null ? "-" : empLeaveCancel.getWorkCity(), null));
        list.add(new WfBusinessDataDetailDto("hireDate", ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), empLeaveCancel.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(empLeaveCancel.getHireDate()), null));
        list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), StringUtils.defaultString(empLeaveCancel.getOrgName(), ""), null));
        //休假相关字段
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getLeaveDetailById(tenantId, Long.valueOf(empLeaveCancel.getLeaveId()));
        //休假相关字段
        if (LeaveCancelTypeEnum.TIME_ADJUST.getIndex().equals(empLeaveCancel.getTypeId().intValue())) {
            WaEmpLeaveDo originalEmpLeave = waEmpLeaveDo.getLeaveDetailById(tenantId, Long.valueOf(empLeaveCancel.getOriginalLeaveId()));
            if (originalEmpLeave != null) {
                Long startTime = originalEmpLeave.getStartTime();
                Long endTime = originalEmpLeave.getEndTime();
                Integer periodType = originalEmpLeave.getPeriodType() != null ? Integer.valueOf(originalEmpLeave.getPeriodType()) : null;
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime), null));
                    list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime), null));
                } else {
                    if (originalEmpLeave.getShiftStartTime() != null && originalEmpLeave.getShiftEndTime() != null) {
                        startTime = originalEmpLeave.getShiftStartTime();
                        endTime = originalEmpLeave.getShiftEndTime();
                    }
                    if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                        Long start = originalEmpLeave.getStartTime();
                        Long end = originalEmpLeave.getEndTime();
                        list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(originalEmpLeave.getShalfDay())), null));
                        list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(originalEmpLeave.getEhalfDay())), null));
                    } else {
                        list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime), null));
                        list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime), null));
                    }
                }
                if (originalEmpLeave.getTimeUnit() == 1) {
                    list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_DURATION, null).getMsg(), originalEmpLeave.getTotalTimeDuration() + PreTimeUnitEnum.getName(originalEmpLeave.getTimeUnit()), null));
                    BigDecimal applyTime = BigDecimal.valueOf(originalEmpLeave.getTotalTimeDuration());
                    float cancelTime = originalEmpLeave.getCancelTimeDuration() == null ? 0 : originalEmpLeave.getCancelTimeDuration();
                    BigDecimal realTime = applyTime.subtract(BigDecimal.valueOf(cancelTime));
                    list.add(new WfBusinessDataDetailDto("actualDuration", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ACTUAL_DURATION, null).getMsg(), realTime.floatValue() + PreTimeUnitEnum.getName(originalEmpLeave.getTimeUnit()), null));
                } else {
                    BigDecimal applyTime = BigDecimal.valueOf(originalEmpLeave.getTotalTimeDuration());
                    list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_DURATION, null).getMsg(), applyTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(originalEmpLeave.getTimeUnit()), null));
                    float cancelTime = originalEmpLeave.getCancelTimeDuration() == null ? 0 : originalEmpLeave.getCancelTimeDuration();
                    BigDecimal realTime = applyTime.subtract(BigDecimal.valueOf(cancelTime));
                    list.add(new WfBusinessDataDetailDto("actualDuration", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ACTUAL_DURATION, null).getMsg(), realTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(originalEmpLeave.getTimeUnit()), null));
                }
            }
            if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(Integer.valueOf(empLeaveCancel.getStatus()))
                    && !ApprovalStatusEnum.PASSED.getIndex().equals(Integer.valueOf(empLeaveCancel.getStatus()))) {
                String st = "";
                String et = "";
                String adjustTimeSlot = empLeaveCancel.getAdjustTimeSlot();
                if (StringUtil.isNotBlank(adjustTimeSlot)) {
                    String[] arr = adjustTimeSlot.split("~");
                    if (arr.length > 1) {
                        st = arr[0];
                        et = arr[1];
                    }
                }
                list.add(new WfBusinessDataDetailDto("newStartTime", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_START_TIME, null).getMsg(), st, null));
                list.add(new WfBusinessDataDetailDto("newEndTime", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_END_TIME, null).getMsg(), et, null));
                list.add(new WfBusinessDataDetailDto("newDuration", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_APPLY_DURATION, null).getMsg(), empLeaveCancel.getAdjustDuration(), null));
            } else if (empLeave != null) {
                Long startTime = empLeave.getStartTime();
                Long endTime = empLeave.getEndTime();
                Integer periodType = empLeave.getPeriodType() != null ? Integer.valueOf(empLeave.getPeriodType()) : null;
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    list.add(new WfBusinessDataDetailDto("newStartTime", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_START_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime), null));
                    list.add(new WfBusinessDataDetailDto("newEndTime", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_END_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime), null));
                } else {
                    if (empLeave.getShiftStartTime() != null && empLeave.getShiftEndTime() != null) {
                        startTime = empLeave.getShiftStartTime();
                        endTime = empLeave.getShiftEndTime();
                    }
                    if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                        Long start = empLeave.getStartTime();
                        Long end = empLeave.getEndTime();
                        list.add(new WfBusinessDataDetailDto("newStartTime", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_START_TIME, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeave.getShalfDay())), null));
                        list.add(new WfBusinessDataDetailDto("newEndTime", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_END_TIME, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeave.getEhalfDay())), null));
                    } else {
                        list.add(new WfBusinessDataDetailDto("newStartTime", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_START_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime), null));
                        list.add(new WfBusinessDataDetailDto("newEndTime", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_END_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime), null));
                    }
                }
                if (empLeave.getTimeUnit() == 1) {
                    list.add(new WfBusinessDataDetailDto("newDuration", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_APPLY_DURATION, null).getMsg(), empLeave.getTotalTimeDuration() + PreTimeUnitEnum.getName(empLeave.getTimeUnit()), null));
                } else {
                    BigDecimal applyTime = BigDecimal.valueOf(empLeave.getTotalTimeDuration());
                    list.add(new WfBusinessDataDetailDto("newDuration", ResponseWrap.wrapResult(AttendanceCodes.NEW_LEAVE_APPLY_DURATION, null).getMsg(), applyTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit()), null));
                }
                list.add(new WfBusinessDataDetailDto("leaveType", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empLeave.getI18nLeaveName(), empLeave.getLeaveName()), null));
            }
            // 休假班次
            List<String> shiftDefNames;
            if (Optional.ofNullable(empLeave).isPresent() && CollectionUtils.isNotEmpty(shiftDefNames = empLeave.getShiftDefNameList())) {
                list.add(new WfBusinessDataDetailDto("vacationShift",
                        MessageHandler.getMessage("caidao.exception.error_202818", WebUtil.getRequest()),
                        StringUtils.join(shiftDefNames, "、"), null));
            }
        } else {
            if (empLeave != null) {
                Long startTime = empLeave.getStartTime();
                Long endTime = empLeave.getEndTime();
                if (empLeave.getPeriodType() != null && (empLeave.getPeriodType() == 1 || empLeave.getPeriodType() == 4)) {
                    list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime), null));
                    list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime), null));
                } else {
                    if (empLeave.getShiftStartTime() != null && empLeave.getShiftEndTime() != null) {
                        startTime = empLeave.getShiftStartTime();
                        endTime = empLeave.getShiftEndTime();
                    }
                    list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_START_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime), null));
                    list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_END_TIME, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime), null));
                }
                if (empLeave.getTimeUnit() == 1) {
                    list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_DURATION, null).getMsg(), empLeave.getTotalTimeDuration() + PreTimeUnitEnum.getName(empLeave.getTimeUnit()), null));

                    BigDecimal applyTime = BigDecimal.valueOf(empLeave.getTotalTimeDuration());
                    Float cancelTime = empLeave.getCancelTimeDuration() == null ? 0 : empLeave.getCancelTimeDuration();
                    BigDecimal realTime = applyTime.subtract(BigDecimal.valueOf(cancelTime));
                    list.add(new WfBusinessDataDetailDto("actualDuration", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ACTUAL_DURATION, null).getMsg(), realTime.floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit()), null));
                } else {
                    BigDecimal applyTime = BigDecimal.valueOf(empLeave.getTotalTimeDuration());
                    list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_DURATION, null).getMsg(), applyTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit()), null));

                    Float cancelTime = empLeave.getCancelTimeDuration() == null ? 0 : empLeave.getCancelTimeDuration();
                    BigDecimal realTime = applyTime.subtract(BigDecimal.valueOf(cancelTime));
                    list.add(new WfBusinessDataDetailDto("actualDuration", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ACTUAL_DURATION, null).getMsg(), realTime.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeave.getTimeUnit()), null));
                }
                list.add(new WfBusinessDataDetailDto("leaveType", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empLeave.getI18nLeaveName(), empLeave.getLeaveName()), null));
            }
        }
        list.add(new WfBusinessDataDetailDto("leaveCancelType", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_TYPE, null).getMsg(), LeaveCancelTypeEnum.getName(empLeaveCancel.getTypeId()), null));
        if (LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex().equals(empLeaveCancel.getTypeId().intValue())) {
            // 销假时间
            List<WaEmpLeaveCancelInfoDo> leaveCancelInfoList = waEmpLeaveCancelInfoDomainService.getListByLeaveCancelId(Collections.singletonList(empLeaveCancel.getLeaveCancelId()));
            if (CollectionUtils.isNotEmpty(leaveCancelInfoList)) {
                String shiftStr = leaveCancelInfoList.stream()
                        .map(o -> CollectionUtils.isNotEmpty(o.getShiftDefNameList(tenantId)) ? StringUtils.join(o.getShiftDefNameList(tenantId), "、") : "")
                        .filter(StringUtils::isNotEmpty).collect(Collectors.joining("<br/>"));
                if (StringUtils.isNotEmpty(shiftStr)) {
                    list.add(new WfBusinessDataDetailDto("leaveCancelShift", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_SHIFT, null).getMsg(), shiftStr));
                }
                String timeStr = leaveCancelInfoList.stream().map(this::formatTimePeriod).collect(Collectors.joining("<br/>"));
                list.add(new WfBusinessDataDetailDto("leaveCancelTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_TIME, null).getMsg(), timeStr, null));
            }
            if (empLeaveCancel.getTimeUnit() == 1) {
                list.add(new WfBusinessDataDetailDto("leaveCancelDuration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), empLeaveCancel.getTimeDuration() + PreTimeUnitEnum.getName(empLeaveCancel.getTimeUnit()), null));
            } else {
                BigDecimal a = BigDecimal.valueOf(empLeaveCancel.getTimeDuration());
                list.add(new WfBusinessDataDetailDto("leaveCancelDuration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), a.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + PreTimeUnitEnum.getName(empLeaveCancel.getTimeUnit()), null));
            }
        } else {
            list.add(new WfBusinessDataDetailDto("leaveCancelTime", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CANCEL_TIME, null).getMsg(), "-", null));
            list.add(new WfBusinessDataDetailDto("leaveCancelDuration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), "-", null));
        }
        if (empLeaveCancel.getCreateTime() != null) {
            list.add(new WfBusinessDataDetailDto("applyTime", ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(empLeaveCancel.getCreateTime()), null));
        }
        list.add(new WfBusinessDataDetailDto("approvalStatus", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(empLeaveCancel.getStatus()), null));
        list.add(new WfBusinessDataDetailDto("reason", ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), empLeaveCancel.getReason() == null ? "-" : empLeaveCancel.getReason(), null));
        list.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), empLeaveCancel.getProcessCode() == null ? "-" : empLeaveCancel.getProcessCode(), null));
        String fileStr = empLeaveCancel.getFileId();
        String fileNameStr = empLeaveCancel.getFileName();
        List<WfAttachmentDto> fileList = new ArrayList<>();
        if (StringUtils.isNotBlank(fileStr) && StringUtils.isNotBlank(fileNameStr)) {
            String[] files = fileStr.split(",");
            String[] fileNames = fileNameStr.split(",");
            for (int i = 0; i < files.length; i++) {
                WfAttachmentDto attachmentDto = new WfAttachmentDto();
                attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                attachmentDto.setFileUrl(files[i]);
                attachmentDto.setFileName(fileNames[i]);
                fileList.add(attachmentDto);
            }
            list.add(new WfBusinessDataDetailDto("file", ResponseWrap.wrapResult(AttendanceCodes.ATTACHMENT, null).getMsg(), "", fileList));
        }
        responseDto.setDetailList(list);
        return responseDto;
    }

    /**
     * 查看调休复现详情
     *
     * @param tenantId   租户
     * @param businessId 业务id
     * @return
     */
    private WfResponseDto getCompensatoryCaseDetail(String tenantId, Long businessId) {
        WfResponseDto responseDto = new WfResponseDto();
        EmpCompensatoryCaseApplyDo dto = empCompensatoryApplyDo.getDetailById(tenantId, businessId);
        List<WfBusinessDataDetailDto> list = new ArrayList<>();
        //发起人、申请人、调休额度、单位、生效日期、失效日期、申请付现额度、有效付现额度、申请时间、审批状态、审批通过日期、备注
        list.add(new WfBusinessDataDetailDto("initiator", ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(dto.getCreateBy()), null));
        if (StringUtils.isNotBlank(dto.getWorkNo()) && StringUtils.isNotBlank(dto.getEmpName())) {
            list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), dto.getWorkNo() + "(" + dto.getEmpName() + ")", null));
        } else if (StringUtils.isNotBlank(dto.getEmpName())) {
            list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), dto.getEmpName(), null));
        } else if (StringUtils.isNotBlank(dto.getWorkNo())) {
            list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), dto.getWorkNo(), null));
        }
        list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), dto.getFullPath(), null));
        list.add(new WfBusinessDataDetailDto("empType", ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), dto.getEmployType(), null));
        list.add(new WfBusinessDataDetailDto("workplace", ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), Optional.ofNullable(dto.getWorkCity()).orElse("-"), null));
        list.add(new WfBusinessDataDetailDto("hireDate", ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), dto.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(dto.getHireDate()), null));
        Integer timeUnit = dto.getTimeUnit();
        Float applyDuration = Optional.ofNullable(dto.getApplyDuration()).orElse(0f);
        Float validDuration = Optional.ofNullable(dto.getValidDuration()).orElse(0f);
        if (PreTimeUnitEnum.HOUR.getIndex().equals(timeUnit)) {
            list.add(new WfBusinessDataDetailDto("applyDuration", ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_INVALID_QUOTA, null).getMsg(), String.format("%s%s", BigDecimal.valueOf(applyDuration).divide(BigDecimal.valueOf(60), 1, RoundingMode.DOWN).floatValue(), PreTimeUnitEnum.getName(timeUnit)), null));
            list.add(new WfBusinessDataDetailDto("validDuration", ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_VALID_QUOTA, null).getMsg(), String.format("%s%s", BigDecimal.valueOf(validDuration).divide(BigDecimal.valueOf(60), 1, RoundingMode.DOWN).floatValue(), PreTimeUnitEnum.getName(timeUnit)), null));
            list.add(new WfBusinessDataDetailDto("totalDuration", ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_QUOTA, null).getMsg(), String.format("%s%s", BigDecimal.valueOf(applyDuration + validDuration).divide(BigDecimal.valueOf(60), 1, RoundingMode.DOWN).floatValue(), PreTimeUnitEnum.getName(timeUnit)), null));
        } else {
            list.add(new WfBusinessDataDetailDto("applyDuration", ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_INVALID_QUOTA, null).getMsg(), String.format("%s%s", applyDuration, PreTimeUnitEnum.getName(timeUnit)), null));
            list.add(new WfBusinessDataDetailDto("validDuration", ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_VALID_QUOTA, null).getMsg(), String.format("%s%s", validDuration, PreTimeUnitEnum.getName(timeUnit)), null));
            list.add(new WfBusinessDataDetailDto("totalDuration", ResponseWrap.wrapResult(AttendanceCodes.COMPENSATOR_TO_CASH_QUOTA, null).getMsg(), String.format("%s%s", applyDuration + validDuration, PreTimeUnitEnum.getName(timeUnit)), null));
        }
        list.add(new WfBusinessDataDetailDto("applyTime", ResponseWrap.wrapResult(AttendanceCodes.APPLY_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(dto.getCreateTime()), null));
        list.add(new WfBusinessDataDetailDto("approvalStatus", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(dto.getStatus()), null));
        list.add(new WfBusinessDataDetailDto("note", ResponseWrap.wrapResult(AttendanceCodes.NOTE, null).getMsg(), Optional.ofNullable(dto.getNote()).orElse("-"), null));
        responseDto.setDetailList(list);
        return responseDto;
    }

    /**
     * 加班单撤销明细
     *
     * @param tenantId   租户
     * @param businessId 业务主键
     * @return
     */
    private WfResponseDto getOverTimeRevokeDetail(String tenantId, Long businessId) {
        WfResponseDto responseDto = new WfResponseDto();
        WaWorkflowRevokeDo workflowRevoke = getWaWorkflowRevoke(businessId);
        WaEmpOvertimeDo overtimeDo = waEmpOvertimeDo.getOtRevokeDetailById(tenantId, workflowRevoke.getId());
        if (overtimeDo != null) {
            List<WfBusinessDataDetailDto> list = new ArrayList<>();
            //发起人、申请人、开始时间、结束时间、申请时长、补偿类型、审批状态、申请事由
            list.add(new WfBusinessDataDetailDto("originator", ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(workflowRevoke.getCreateBy()), null));
            if (StringUtils.isNotBlank(overtimeDo.getWorkno()) && StringUtils.isNotBlank(overtimeDo.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getWorkno() + "(" + overtimeDo.getEmpName() + ")", null));
            } else if (StringUtils.isNotBlank(overtimeDo.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getEmpName(), null));
            } else if (StringUtils.isNotBlank(overtimeDo.getWorkno())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), overtimeDo.getWorkno(), null));
            }
            list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), overtimeDo.getFullPath(), null));
            list.add(new WfBusinessDataDetailDto("workplace", ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), overtimeDo.getWorkCity() == null ? "-" : overtimeDo.getWorkCity(), null));
            list.add(new WfBusinessDataDetailDto("hireDate", ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), overtimeDo.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(overtimeDo.getHireDate()), null));
            list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(overtimeDo.getStartTime()), null));
            list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(overtimeDo.getEndTime()), null));
            list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), overtimeDo.getOtDurationDesc(), null));
            if (overtimeDo.getCrttime() != null) {
                list.add(new WfBusinessDataDetailDto("applyTime", ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(overtimeDo.getCrttime()), null));
            }
            OverTimeDto overTimeDto = null;
            if (null != overtimeDo.getOvertimeTypeId()) {
                Optional<OverTimeDto> overTimeDtoOpt = Optional.ofNullable(overTimeService.getOtType(overtimeDo.getOvertimeTypeId()));
                if (overTimeDtoOpt.isPresent()) {
                    overTimeDto = overTimeDtoOpt.get();
                }
            }
            list.add(new WfBusinessDataDetailDto("overtimeType", ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE, null).getMsg(), overTimeDto == null ? "-" : LangParseUtil.getI18nLanguage(overTimeDto.getI18nTypeName(), overTimeDto.getTypeName()), null));
            list.add(new WfBusinessDataDetailDto("compensateType", ResponseWrap.wrapResult(AttendanceCodes.COMPENSATORY_TYPE, null).getMsg(), CompensateTypeEnum.getDescByOrdinal(overtimeDo.getCompensateType()), null));
            String periodOtDuration = String.format("%s%s",
                    getEmpPeriodOtDuration(overtimeDo.getBelongOrgId(), overtimeDo.getEmpid(), overtimeDo.getStartTime()),
                    PreTimeUnitEnum.getName(2));
            list.add(new WfBusinessDataDetailDto("periodOtDuration", ResponseWrap.wrapResult(AttendanceCodes.PERIOD_OT_DURATION, null).getMsg(), periodOtDuration));
            list.add(new WfBusinessDataDetailDto("approvalStatus", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(overtimeDo.getStatus()), null));
            list.add(new WfBusinessDataDetailDto("reason", ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), overtimeDo.getReason() == null ? "-" : overtimeDo.getReason(), null));
            list.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), overtimeDo.getProcessCode()));
            responseDto.setDetailList(list);
        }
        return responseDto;
    }

    /**
     * 出差单撤销明细
     *
     * @param tenantId   租户
     * @param businessId 流程主键
     * @return
     */
    private WfResponseDto getEmpTravelRevokeDetail(String tenantId, Long businessId) {
        WfResponseDto responseDto = new WfResponseDto();
        WaWorkflowRevokeDo workflowRevoke = getWaWorkflowRevoke(businessId);
        Optional<WaEmpTravelDo> optional = Optional.ofNullable(waEmpTravelDo.getEmpTravelRevokeDetailById(tenantId, workflowRevoke.getId()));
        if (optional.isPresent()) {
            WaEmpTravelDo empTravel = optional.get();
            List<WfBusinessDataDetailDto> list = new ArrayList<>();
            //发起人、申请人、假期类型、开始时间、结束时间、申请时长、审批状态、申请事由
            list.add(new WfBusinessDataDetailDto("originator", ResponseWrap.wrapResult(AttendanceCodes.ORIGINATOR, null).getMsg(), getInitiator(workflowRevoke.getCreateBy()), null));
            if (StringUtils.isNotBlank(empTravel.getWorkNo()) && StringUtils.isNotBlank(empTravel.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getWorkNo() + "(" + empTravel.getEmpName() + ")", null));
            } else if (StringUtils.isNotBlank(empTravel.getEmpName())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getEmpName(), null));
            } else if (StringUtils.isNotBlank(empTravel.getWorkNo())) {
                list.add(new WfBusinessDataDetailDto("applicant", ResponseWrap.wrapResult(AttendanceCodes.APPLICANT, null).getMsg(), empTravel.getWorkNo(), null));
            }
            list.add(new WfBusinessDataDetailDto("empType", ResponseWrap.wrapResult(AttendanceCodes.EMPLOYEE_TYPE, null).getMsg(), empTravel.getEmployType(), null));
            list.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG_POSITION, null).getMsg(), empTravel.getFullPath(), null));
            list.add(new WfBusinessDataDetailDto("workplace", ResponseWrap.wrapResult(AttendanceCodes.WORKPLACE, null).getMsg(), empTravel.getWorkCity() == null ? "-" : empTravel.getWorkCity(), null));
            list.add(new WfBusinessDataDetailDto("hireDate", ResponseWrap.wrapResult(AttendanceCodes.HIRE_DATE, null).getMsg(), empTravel.getHireDate() == null ? "-" : DateUtil.getDateStrByTimesamp(empTravel.getHireDate()), null));
            Long startTime = empTravel.getStartTime();
            Long endTime = empTravel.getEndTime();
            Integer periodType = Integer.valueOf(empTravel.getPeriodType());
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(startTime), null));
                list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(endTime), null));
            } else {
                if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(startTime), DayHalfTypeEnum.getDesc(empTravel.getShalfDay())), null));
                    list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), String.format("%s%s", DateUtil.getDateStrByTimesamp(endTime), DayHalfTypeEnum.getDesc(empTravel.getEhalfDay())), null));
                } else {
                    if (empTravel.getShiftStartTime() != null && empTravel.getShiftEndTime() != null) {
                        startTime = empTravel.getShiftStartTime();
                        endTime = empTravel.getShiftEndTime();
                    }
                    list.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(startTime), null));
                    list.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getTimeStrByTimesamp4(endTime), null));
                }
            }
            if (empTravel.getTimeUnit() == 1) {
                list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), empTravel.getTimeDuration() + TravelTypeUnitEnum.getName(empTravel.getTimeUnit()), null));
            } else {
                BigDecimal a = BigDecimal.valueOf(empTravel.getTimeDuration());
                list.add(new WfBusinessDataDetailDto("duration", ResponseWrap.wrapResult(AttendanceCodes.DURATION, null).getMsg(), a.divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + TravelTypeUnitEnum.getName(empTravel.getTimeUnit()), null));
            }
            if (empTravel.getCreateTime() != null) {
                list.add(new WfBusinessDataDetailDto("applyTime", ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION, null).getMsg(), DateUtil.getDateStrByTimesamp(empTravel.getCreateTime()), null));
            }
            list.add(new WfBusinessDataDetailDto("travelType", ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE, null).getMsg(), LangParseUtil.getI18nLanguage(empTravel.getI18nTravelTypeName(), empTravel.getTravelType()), null));
            SysUnitCity provinceObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, empTravel.getProvince());
            SysUnitCity cityObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_CITY_KEY, empTravel.getCity());
            String province = "";
            String city = "";
            if (provinceObj != null) {
                province = provinceObj.getChnName();
            }
            if (cityObj != null) {
                city = cityObj.getChnName();
            }
            String location = province + " " + city;
            list.add(new WfBusinessDataDetailDto("site", ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_PLACE, null).getMsg(), location, null));
            String travelMode = empTravel.getTravelMode();
            list.add(new WfBusinessDataDetailDto("travelMode", ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_MODE, null).getMsg(), Optional.ofNullable(travelMode).orElse("-"), null));
            list.add(new WfBusinessDataDetailDto("approvalStatus", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_STATUS, null).getMsg(), ApprovalStatusEnum.getName(empTravel.getStatus()), null));
            list.add(new WfBusinessDataDetailDto("reason", ResponseWrap.wrapResult(AttendanceCodes.APPLY_REASON, null).getMsg(), empTravel.getReason() == null ? "-" : empTravel.getReason(), null));
            list.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), empTravel.getProcessCode()));
            responseDto.setDetailList(list);
        }
        return responseDto;
    }

    /**
     * 工作流审批
     *
     * @param dto
     * @return
     */
    @Override
    public Result workflowApproval(WfApprovalDto dto) {
        log.info("workflowApproval params:{}", JSONUtils.ObjectToJson(dto));
        if (StringUtil.isNotBlank(dto.getBusinessKey())) {
            String funcCode = getFuncCode(dto.getBusinessKey());
            FuncTypeEnum funcType = FuncTypeEnum.getFuncCodeEnum(funcCode);
            if (null == funcType) {
                return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, Boolean.FALSE);
            }
            switch (funcType) {
                case OVERTIME:
                    Long otId = getBusinessId(dto.getBusinessKey());
                    if (CollectionUtils.isNotEmpty(waWorkflowRevokeDo.getWorkflowRevokeList(null, otId,
                            Collections.singletonList(ApprovalStatusEnum.IN_APPROVAL.getIndex()),
                            Collections.singletonList(BusinessCodeEnum.OVERTIME_REVOKE.name())))) {
                        //校验是否有审批中的撤销流程，已有审批中的撤销流程不可进行审批操作
                        return ResponseWrap.wrapResult(AttendanceCodes.DURING_REVOKING_OVERTIME_REVOKE_NOT_ALLOW, Boolean.FALSE);
                    }
                    break;
                case TRAVEL:
                    Long travelId = getBusinessId(dto.getBusinessKey());
                    if (CollectionUtils.isNotEmpty(waWorkflowRevokeDo.getWorkflowRevokeList(null, travelId,
                            Collections.singletonList(ApprovalStatusEnum.IN_APPROVAL.getIndex()),
                            Collections.singletonList(BusinessCodeEnum.TRAVEL_REVOKE.name())))) {
                        //校验是否有审批中的撤销流程，已有审批中的撤销流程不可进行审批操作
                        return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_REVOKING_NOT_ALLOWED_REVOKE, Boolean.FALSE);
                    }
                    break;
            }
        }
        WfTaskApproveDTO wfTaskApproveDTO = new WfTaskApproveDTO();
        wfTaskApproveDTO.setTaskId(dto.getTaskId());
        wfTaskApproveDTO.setChoice(dto.getChoice());
        wfTaskApproveDTO.setComment(dto.getComment());
        try {
            Result<?> result = wfOperateFeignClient.approveTask(wfTaskApproveDTO);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                return Result.fail("Approval Exception");
            }
        } catch (Exception e) {
            log.error("Approval Exception:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok();
    }

    @Override
    public Result<Boolean> checkWorkflowEnabled(String funCode) {
        return wfOperateFeignClient.checkDefEnabled(funCode);
    }

    @Async
    @Override
    public void workflowTaskUrge(List<WfTaskUrgeItem> items) {
        List<String> businessKeys = items.stream().map(WfTaskUrgeItem::getBusinessKey).distinct().collect(Collectors.toList());
        try {
            for (String businessKey : businessKeys) {
                Result<?> result = wfOperateFeignClient.urgeTask(new WfTaskUrgeDTO(businessKey, "URGED_EACH_ONE"));
                if (!result.isSuccess()) {
                    log.error("urging task has exception:businessKey:{}, code:{},msg:{}", businessKey, result.getCode(), result.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("urging task has exception:{}", e.getMessage(), e);
        }
    }

    @Override
    public Map<String, String> workflowParameter(WfNoticeParameterItem item) {
        String businessKey = item.getBusinessKey();
        BusinessCodeEnum funcType = this.getBusinessCodeEnum(businessKey);
        if (funcType == null) {
            return null;
        }
        WfResponseDto dto;
        if (BusinessCodeEnum.BATCH_TRAVEL == funcType) {
            Long corpId = UserContext.getTenantId() == null ? null : ConvertHelper.longConvert(UserContext.getTenantId());
            Long businessId = this.getBusinessId(businessKey);
            if (businessId == null) {
                return null;
            }
            List<WaEmpTravelDo> waEmpTravelDos = waEmpTravelDo.listByBatchId(String.valueOf(corpId), businessId);
            if (CollectionUtils.isEmpty(waEmpTravelDos)) {
                return null;
            }
            dto = this.getTravelDetail(corpId, waEmpTravelDos.get(0).getTravelId());
        } else {
            Optional<WfResponseDto> opt = Optional.ofNullable(this.getWfDetail(UserContext.getTenantId(), businessKey, false));
            if (!opt.isPresent()) {
                return null;
            }
            dto = opt.get();
        }
        List<WfBusinessDataDetailDto> detailList = dto.getDetailList();
        Map<String, Object> wfDetailMap = new HashMap<>();
        detailList.forEach(d -> wfDetailMap.put(d.getKey(), Optional.ofNullable(d.getValue()).orElse("")));
        List<String> variables = item.getVariables();
        return getLeaveMap(wfDetailMap, variables);
    }

    private Map<String, String> getLeaveMap(Map<String, Object> map, List<String> variables) {
        Map<String, String> resultMap = new HashMap<>();
        for (String variable : variables) {
            if (map.containsKey(variable)) {
                resultMap.put(variable, map.get(variable).toString());
            }
        }
        return resultMap;
    }

    @Override
    public ProcessRecordDto recordOfProcess(ProcessDto processDto) throws Exception {
        ProcessRecordDto processRecordDto = new ProcessRecordDto();
        String businessKey = processDto.getBusinessKey();
        Long businessId = getBusinessId(businessKey);
        if (businessId == null) {
            return processRecordDto;
        }
        String funcCode = getFuncCode(businessKey);
        if (funcCode == null) {
            return processRecordDto;
        }
        Result<List<WfProcessRecordDto>> result = wfOperateFeignClient.getRecord(businessKey);
        if (!result.isSuccess()) {
            return processRecordDto;
        }
        List<WfProcessRecordDto> list = result.getData();
        UserInfo userInfo = getUser();
        if (BusinessCodeEnum.OVERTIME.getCode().equals(funcCode)) {
            processRecordDto.setOtRecord(list);
            List<WaWorkflowRevokeDo> revokeList = workflowRevokeDo.getWorkflowRevokeList(userInfo.getTenantId(), businessId);
            if (CollectionUtils.isNotEmpty(revokeList)) {
                for (WaWorkflowRevokeDo revoke : revokeList) {
                    String revokeBusinessKey = String.format("%s_%s", revoke.getId(), Objects.requireNonNull(FuncTypeEnum.getFuncCodeByName(revoke.getModuleName())).getName());
                    Result<List<WfProcessRecordDto>> res = wfOperateFeignClient.getRecord(revokeBusinessKey);
                    if (res.isSuccess() && CollectionUtils.isNotEmpty(res.getData())) {
                        processRecordDto.getOtRevokeRecord().add(res.getData());
                    }
                }
            }
        } else if (BusinessCodeEnum.TRAVEL.getCode().equals(funcCode)) {
            processRecordDto.setTravelRecord(list);
            List<WaWorkflowRevokeDo> revokeList = workflowRevokeDo.getWorkflowRevokeList(userInfo.getTenantId(), businessId);
            if (CollectionUtils.isNotEmpty(revokeList)) {
                for (WaWorkflowRevokeDo revoke : revokeList) {
                    String revokeBusinessKey = String.format("%s_%s", revoke.getId(), Objects.requireNonNull(FuncTypeEnum.getFuncCodeByName(revoke.getModuleName())).getName());
                    Result<List<WfProcessRecordDto>> res = wfOperateFeignClient.getRecord(revokeBusinessKey);
                    if (res.isSuccess() && CollectionUtils.isNotEmpty(res.getData())) {
                        processRecordDto.getTravelRevokeRecord().add(res.getData());
                    }
                }
            }
        } else if (BusinessCodeEnum.LEAVE.getCode().equals(funcCode)) {
            processRecordDto.setLeaveRecord(list);
            WaEmpLeaveDo empLeave = waEmpLeaveDo.getLeaveDetailById(userInfo.getTenantId(), businessId);
            if (null == empLeave) {
                throw new Exception(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TIME_NOT_EXIST, null).getMsg());
            }
            List<WaEmpLeaveCancelDo> cancelList = waEmpLeaveCancelDomainService.getListByLeaveId(userInfo.getTenantId(), businessId.intValue());
            if (CollectionUtils.isNotEmpty(cancelList)) {
                for (WaEmpLeaveCancelDo cancel : cancelList) {
                    String cancelBusinessKey = String.format("%s_%s", cancel.getLeaveCancelId(), FuncTypeEnum.LEAVE_CANCEL.getName());
                    Result<List<WfProcessRecordDto>> cancelResult = wfOperateFeignClient.getRecord(cancelBusinessKey);
                    if (cancelResult.isSuccess() && CollectionUtils.isNotEmpty(cancelResult.getData())) {
                        Long typeId = cancel.getTypeId();
                        CancelProcessRecordDto cancelProcessRecordDto = new CancelProcessRecordDto();
                        cancelProcessRecordDto.setCancelType(typeId);
                        cancelProcessRecordDto.setLeaveCancelRecord(cancelResult.getData());
                        cancelProcessRecordDto.setReason(cancel.getReason());
                        if (LeaveCancelTypeEnum.TIME_ADJUST.getIndex().equals(typeId.intValue())) {
                            if (ApprovalStatusEnum.REVOKED.getIndex().equals(cancel.getStatus().intValue())) {
                                cancelProcessRecordDto.setLeaveTime(cancel.getAdjustTimeSlot());
                            } else {
                                cancelProcessRecordDto.setLeaveTime(getTimeSlot(empLeave));
                            }
                            WaEmpLeaveDo originalEmpLeave = waEmpLeaveDo.getLeaveDetailById(userInfo.getTenantId(), Long.valueOf(cancel.getOriginalLeaveId()));
                            if (null != originalEmpLeave) {
                                cancelProcessRecordDto.setOriginalLeaveTime(getTimeSlot(originalEmpLeave));
                            }
                        } else if (LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex().equals(typeId.intValue())) {
                            // 销假时间
                            List<WaEmpLeaveCancelInfoDo> leaveCancelInfoList = waEmpLeaveCancelInfoDomainService.getListByLeaveCancelId(Collections.singletonList(cancel.getLeaveCancelId()));
                            if (CollectionUtils.isNotEmpty(leaveCancelInfoList)) {
                                List<String> timeList = leaveCancelInfoList.stream().map(o -> {
                                    Integer periodType = Integer.valueOf(o.getPeriodType());
                                    if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                                        return String.format("%s-%s", DateUtil.getDateStrByTimesamp(o.getStartTime()), DateUtil.getDateStrByTimesamp(o.getEndTime()));
                                    } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                                        return String.format("%s-%s", String.format("%s%s", DateUtil.getDateStrByTimesamp(o.getStartTime()), DayHalfTypeEnum.getDesc(o.getShalfDay())),
                                                String.format("%s%s", DateUtil.getDateStrByTimesamp(o.getEndTime()), DayHalfTypeEnum.getDesc(o.getEhalfDay())));
                                    } else {
                                        return String.format("%s-%s", DateUtil.getTimeStrByTimesamp4(o.getShiftStartTime()), DateUtil.getTimeStrByTimesamp4(o.getShiftEndTime()));
                                    }
                                }).collect(Collectors.toList());
                                cancelProcessRecordDto.setLeaveCancelTime(StringUtils.join(timeList, ","));
                            }
                        }
                        processRecordDto.getLeaveCancelRecord().add(cancelProcessRecordDto);
                    }
                }
            } else if (empLeave.getLeaveStatus() != null && LeaveCancelStatusEnum.LEAVE_CANCEL_PASSED.getIndex().equals(empLeave.getLeaveStatus())) {
                processRecordDto.setAutoLeaveCancel(Boolean.TRUE);
                processRecordDto.setAutoLeaveCancelTime(empLeave.getUpdtime() != null ? empLeave.getUpdtime() * 1000 : null);
            }
        }
        return processRecordDto;
    }

    private String getTimeSlot(WaEmpLeaveDo empLeave) {
        String timeSlot;
        Long startTime = empLeave.getStartTime();
        Long endTime = empLeave.getEndTime();
        Integer periodType = empLeave.getPeriodType() != null ? Integer.valueOf(empLeave.getPeriodType()) : null;
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
            timeSlot = String.format("%s~%s", DateUtil.getDateStrByTimesamp(startTime), DateUtil.getDateStrByTimesamp(endTime));
        } else {
            if (empLeave.getShiftStartTime() != null && empLeave.getShiftEndTime() != null) {
                startTime = empLeave.getShiftStartTime();
                endTime = empLeave.getShiftEndTime();
            }
            if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                Long start = empLeave.getStartTime();
                Long end = empLeave.getEndTime();
                timeSlot = String.format("%s~%s", String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeave.getShalfDay())),
                        String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeave.getEhalfDay())));
            } else {
                timeSlot = String.format("%s~%s", DateUtil.getTimeStrByTimesamp4(startTime), DateUtil.getTimeStrByTimesamp4(endTime));
            }
        }
        return timeSlot;
    }
}