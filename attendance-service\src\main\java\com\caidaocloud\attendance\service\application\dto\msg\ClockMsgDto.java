package com.caidaocloud.attendance.service.application.dto.msg;

import lombok.Data;

import java.util.List;

/**
 * 日报消息字段：班次、签到时间、签退时间、考勤结果、出勤时长（对应系统：实出勤时长）
 */
@Data
public class ClockMsgDto {
    /**
     * 员工信息
     */
    private List<Long> empIds;

    /**
     * tenantId
     */
    private String tenantId;

    /**
     * 延迟时间
     */
    private Integer delay;

    /**
     * 上班时间
     */
    private Integer startTime;

    /**
     * 下班时间
     */
    private Integer endTime;

    /**
     * 班次id
     */
    private Integer shiftDefId;
    /**
     * 消息内容
     */
    private String content;

    /**
     * 休假单ID
     */
    private Integer leaveId;

}
