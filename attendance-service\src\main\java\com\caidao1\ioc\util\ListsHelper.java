package com.caidao1.ioc.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class ListsHelper {

    public static final String LIST_VALUE = "value";

    public static final String LIST_TEXT = "text";

    private static final String LIST_TEMPLATE = "template";

    /**
     * 转换Bean列表为Map列表
     *
     * @param <T>
     * @param beanList              是否需要请选择
     * @param labelValueBeanCreator LabelValueBean创建接口
     * @return
     */
    public static <T> Map<String, List<Map<String, Object>>> convertMapList(List<T> beanList, LabelValueBeanCreator<T> labelValueBeanCreator) {
        return convertMapList(beanList, labelValueBeanCreator, true, "");
    }

    /**
     * 转换Bean列表为Map列表
     *
     * @param <T>
     * @param beanList              是否需要请选择
     * @param labelValueBeanCreator LabelValueBean创建接口
     * @return
     */
    public static <T> Map<String, List<Map<String, Object>>> convertMapList(List<T> beanList, LabelValueBeanCreator<T> labelValueBeanCreator, boolean includeHeader) {
        return convertMapList(beanList, labelValueBeanCreator, includeHeader, "");
    }

    /**
     * 转换Bean列表为Map列表
     *
     * @param <T>
     * @param beanList              是否需要请选择
     * @param labelValueBeanCreator LabelValueBean创建接口
     * @return
     */
    public static <T> Map<String, List<Map<String, Object>>> convertMapList(List<T> beanList, LabelValueBeanCreator<T> labelValueBeanCreator, String header) {
        return convertMapList(beanList, labelValueBeanCreator, true, header);
    }

    /**
     * 转换Bean列表为Map列表
     *
     * @param <T>
     * @param beanList              是否需要请选择
     * @param labelValueBeanCreator LabelValueBean创建接口
     * @return
     */
    public static <T> Map<String, List<Map<String, Object>>> convertMapList(List<T> beanList, LabelValueBeanCreator<T> labelValueBeanCreator, boolean includeHeader, String headValue) {
        Map<String, List<Map<String, Object>>> returnMap = new HashMap<String, List<Map<String, Object>>>();
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        Map<String, Object> map = new HashMap<String, Object>();
        if (includeHeader) {
            map.put(LIST_VALUE, headValue);
            map.put(LIST_TEXT, "请选择");
            list.add(map);
        }
        list.addAll(convertBean2List(beanList, labelValueBeanCreator));
        returnMap.put("options", list);
        return returnMap;
    }

    /**
     * 转换Bean列表为Map列表
     *
     * @param <T>
     * @param beanList              是否需要请选择
     * @param labelValueBeanCreator LabelValueBean创建接口
     * @return
     */
    public static <T> List<Map<String, Object>> convertBean2List(List<T> beanList, LabelValueBeanCreator<T> labelValueBeanCreator) {
        Map<String, List<Map<String, Object>>> returnMap = new HashMap<String, List<Map<String, Object>>>();
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        Map<String, Object> map = new HashMap<String, Object>();
        for (T t : beanList) {
            map = new HashMap<String, Object>();
            map.put(LIST_VALUE, labelValueBeanCreator.getValue(t));
            map.put(LIST_TEXT, labelValueBeanCreator.getLabel(t));
            list.add(map);
        }
        return list;
    }

    /**
     * 转换Bean列表为Map列表
     *
     * @param <T>
     * @param beanList              是否需要请选择
     * @param labelValueBeanCreator LabelValueBean创建接口
     * @return
     */
    public static <T> Map<String, List<Map<String, Object>>> convert2GridList(List<T> beanList, GridComboCreator<T> labelValueBeanCreator) {
        Map<String, List<Map<String, Object>>> returnMap = new HashMap<String, List<Map<String, Object>>>();
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        Map<String, Object> map = new HashMap<String, Object>();
        for (T t : beanList) {
            map = new HashMap<String, Object>();
            map.put(LIST_VALUE, labelValueBeanCreator.getValue(t));
            map.put(LIST_TEXT, labelValueBeanCreator.getLabel(t));
            list.add(map);
        }
        returnMap.put("options", list);
        return returnMap;
    }

    public static interface LabelValueBeanCreator<T> {
        String getLabel(T t);

        Object getValue(T t);
    }

    public static interface GridComboCreator<T> {
        Map getLabel(T t);

        Object getValue(T t);
    }
}