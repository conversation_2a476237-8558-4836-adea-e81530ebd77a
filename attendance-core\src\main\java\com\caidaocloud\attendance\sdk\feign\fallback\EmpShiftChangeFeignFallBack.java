package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.sdk.dto.SdkApplyShiftDTO;
import com.caidaocloud.attendance.sdk.feign.IEmpShiftChangeFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

/**
 * 员工班次变更
 *
 * <AUTHOR>
 * @Date 2023/6/27
 */
@Component
public class EmpShiftChangeFeignFallBack implements IEmpShiftChangeFeignClient {
    @Override
    public Result<?> userApplyShiftChange(SdkApplyShiftDTO dto) {
        return Result.fail();
    }
}
