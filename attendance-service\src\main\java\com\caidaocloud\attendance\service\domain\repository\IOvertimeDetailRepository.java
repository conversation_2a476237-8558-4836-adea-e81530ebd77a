package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpOvertimeDetailPo;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/8/26 16:56
 * @Description:
 **/
public interface IOvertimeDetailRepository {

    List<EmpOvertimeDetailPo> getOvertimeDetails(Integer overtimeId);

    List<EmpOvertimeDetailPo> getOvertimeDetailList(Long empId, Long startTime, Long endTime, List<Integer> status);

    List<EmpOvertimeDetailPo> getEmpLeftDurationOvertimeDetail(String tenantId, String empIds, String overtimeTypeIds);
}
