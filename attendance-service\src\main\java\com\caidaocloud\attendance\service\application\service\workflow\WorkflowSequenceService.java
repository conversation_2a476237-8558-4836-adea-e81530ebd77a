package com.caidaocloud.attendance.service.application.service.workflow;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.wa.mybatis.mapper.WaEmpLeaveMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpOvertimeMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveTypeMapper;
import com.caidao1.wa.mybatis.mapper.WaRegisterRecordMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.service.application.dto.travel.BatchTravelDto;
import com.caidaocloud.attendance.service.application.enums.LeaveTypeUnitEnum;
import com.caidaocloud.attendance.service.application.enums.PreTimeUnitEnum;
import com.caidaocloud.attendance.service.application.service.impl.WaBatchLeaveService;
import com.caidaocloud.attendance.service.domain.entity.WaBatchLeaveDo;
import com.caidaocloud.attendance.service.domain.entity.WaBatchOvertimeDo;
import com.caidaocloud.attendance.service.domain.entity.WaBatchTravelDo;
import com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDo;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.*;
import com.caidaocloud.attendance.service.infrastructure.repository.po.*;
import com.caidaocloud.attendance.service.schedule.application.service.dto.EmpWorkInfo;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.MdDataQuery;
import com.caidaocloud.masterdata.entity.emp.EmpInfoEntity;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作流回调业务服务类
 */
@Slf4j
@Service
public class WorkflowSequenceService {

    @Resource
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Resource
    private WaEmpOvertimeMapper waEmpOvertimeMapper;
    @Resource
    private WaEmpTravelMapper waEmpTravelMapper;
    @Resource
    private WaEmpLeaveCancelMapper leaveCancelMapper;
    @Resource
    private WaLeaveTypeMapper leaveTypeMapper;
    @Resource
    private WaEmpCompensatoryCaseApplyMapper empCompensatoryCaseApplyMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private WaWorkflowRevokeMapper workflowRevokeMapper;
    @Resource
    private WaBatchTravelDo waBatchTravelDo;
    @Resource
    private WaEmpTravelDo waEmpTravelDo;
    @Resource
    private WaBatchLeaveDo waBatchLeaveDo;
    @Resource
    private WaBatchOvertimeDo waBatchOvertimeDo;
    @Resource
    private WaLeaveExtensionMapper waLeaveExtensionMapper;
    @Resource
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Resource
    private WaRegisterRecordBdkMapper waRegisterRecordBdkMapper;
    @Resource
    private WaSobService waSobService;
    private final static String EMP_WORK_INFO_IDENTIFIER = "entity.hr.EmpWorkInfo";

    /**
     * 休假序列流节点
     *
     * @param businessId 主键
     * @param code       code
     * @return String
     */
    public String getLeaveWorkflowSequenceDateConstraint(String businessId, String code) {
        log.info("LeaveWorkflowSequence start businessId={}", businessId);
        Integer leaveId = Integer.valueOf(businessId);

        String cacheValue = (String) redisTemplate.opsForValue().get("import_leave_id_" + businessId);
        log.info("LeaveWorkflowSequence cacheValue={}", cacheValue);

        String constraintData = null;
        switch (code) {
            case "LEAVE_TYPE":
                constraintData = getLeaveTypeDefIdString(leaveId);
                if (StringUtils.isEmpty(constraintData) && StringUtils.isNotBlank(cacheValue)) {
                    final String[] split = cacheValue.split(",");
                    constraintData = split[0];
                }
                break;
            case "LEAVE_TOTAL_DURATION":
                constraintData = getLeaveTotalTimeDurationString(leaveId);
                if (StringUtils.isEmpty(constraintData) && StringUtils.isNotBlank(cacheValue)) {
                    final String[] split = cacheValue.split(",");
                    int unit = Integer.parseInt(split[2]);
                    constraintData = unit == 1 ? split[1] : BigDecimal.valueOf(Float.parseFloat(split[1]) / 60).toString();
                }
                break;
            case "LEAVE_TIME_UNIT":
                constraintData = getLeaveTimeUnitString(leaveId);
                if (StringUtils.isEmpty(constraintData) && StringUtils.isNotBlank(cacheValue)) {
                    final String[] split = cacheValue.split(",");
                    constraintData = split[2];
                }
                break;
            default:
                break;
        }
        log.info("LeaveWorkflowSequence end businessId={}, constraintData={}", businessId, constraintData);

        return constraintData;
    }

    /**
     * 批量休假序列流节点
     *
     * @param businessId
     * @param code
     * @return
     */
    public String getBatchLeaveWorkflowSequenceDateConstraint(String businessId, String code) {
        Integer leaveTypeId = null;
        Integer timeUnit = null;
        Float timeDuration = null;

        String cacheValue = (String) redisTemplate.opsForValue().get(WaBatchLeaveService.CACHE_KEY_FOR_BATCH_LEAVE_APPLY + businessId);
        if (StringUtils.isNotBlank(cacheValue)) {
            log.debug("BatchLeaveWorkflowSequence cahceValue={}", cacheValue);
            Map<String, Object> cacheMap = FastjsonUtil.convertObject(cacheValue, Map.class);
            if (null != cacheMap.get("leaveTypeId")) {
                leaveTypeId = Integer.valueOf(cacheMap.get("leaveTypeId").toString());
            }
            if (null != cacheMap.get("timeUnit")) {
                timeUnit = Integer.valueOf(cacheMap.get("timeUnit").toString());
            }
            if (null != cacheMap.get("timeDuration")) {
                timeDuration = Float.valueOf(cacheMap.get("timeDuration").toString());
            }
        } else {
            WaBatchLeaveDo batchLeave = waBatchLeaveDo.getById(Long.valueOf(businessId));
            if (null != batchLeave) {
                leaveTypeId = batchLeave.getLeaveTypeId();
                timeUnit = batchLeave.getTimeUnit();
                timeDuration = batchLeave.getTimeDuration();
            }
        }
        // 以下条件均不支持假期类型多选场景
        String constraintData = null;
        switch (code) {
            case "BATCH_LEAVE_TYPE":
                if (null != leaveTypeId) {
                    Optional<WaLeaveType> opt = Optional.ofNullable(leaveTypeMapper.selectByPrimaryKey(leaveTypeId));
                    constraintData = opt.map(waLeaveType -> waLeaveType.getLeaveType().toString()).orElse("-1");
                }
                break;
            case "BATCH_LEAVE_TOTAL_DURATION":
                if (timeUnit != null && null != timeDuration) {
                    if (timeUnit == 1) {
                        constraintData = timeDuration.toString();
                    } else {
                        constraintData = BigDecimal.valueOf(timeDuration / 60).toString();
                    }
                }
                break;
            case "BATCH_LEAVE_TIME_UNIT":
                if (null != timeUnit) {
                    constraintData = timeUnit.toString();
                }
                break;
            default:
                break;
        }

        log.info("BatchLeaveWorkflowSequence return businessId={},constraintData={}", businessId, constraintData);
        return constraintData;
    }

    private WaEmpLeave getWaEmpLeave(Integer leaveId) {
        log.info("workflow seq waEmpLeave params, leaveId={}", leaveId);
        Optional<WaEmpLeave> opt = Optional.ofNullable(waEmpLeaveMapper.selectByPrimaryKey(leaveId));
        if (!opt.isPresent()) {
            log.error("workflow seq waEmpLeave empty leaveId={}", leaveId);
            return null;
        }
        log.info("workflow seq waEmpLeave={}", FastjsonUtil.toJsonStr(opt.get()));
        return opt.get();
    }

    /**
     * 假期规则
     *
     * @param leaveId 主键
     * @return String
     */
    private Integer getLeaveTypeId(Integer leaveId) {
        Optional<WaEmpLeave> opt = Optional.ofNullable(getWaEmpLeave(leaveId));
        return opt.map(WaEmpLeave::getLeaveTypeId).orElse(null);
    }

    /**
     * 休假类型
     *
     * @param leaveId 主键
     * @return String
     */
    private String getLeaveTypeDefIdString(Integer leaveId) {
        log.info("workflow seq waEmpLeave getLeaveTypeDefIdString, leaveId={}", leaveId);
        Integer leaveTypeId = getLeaveTypeId(leaveId);
        if (null == leaveTypeId) {
            return "-1";
        }
        Optional<WaLeaveType> opt = Optional.ofNullable(leaveTypeMapper.selectByPrimaryKey(leaveTypeId));
        return opt.map(waLeaveType -> waLeaveType.getLeaveType().toString()).orElse("-1");
    }

    /**
     * 总时长
     *
     * @param leaveId 主键
     * @return String
     */
    private String getLeaveTotalTimeDurationString(Integer leaveId) {
        log.info("workflow seq waEmpLeave getLeaveTotalTimeDurationString, leaveId={}", leaveId);
        Optional<WaEmpLeave> opt = Optional.ofNullable(getWaEmpLeave(leaveId));
        return opt.map(leave -> leave.getTimeUnit() == 1 ? leave.getTotalTimeDuration().toString() : BigDecimal.valueOf(leave.getTotalTimeDuration() / 60).toString()).orElse(null);
    }

    /**
     * 单位（小时、天）
     *
     * @param leaveId 主键
     * @return String
     */
    private String getLeaveTimeUnitString(Integer leaveId) {
        log.info("workflow seq waEmpLeave getLeaveTimeUnitString, leaveId={}", leaveId);
        Optional<WaEmpLeave> opt = Optional.ofNullable(getWaEmpLeave(leaveId));
        return opt.map(waEmpLeave -> waEmpLeave.getTimeUnit().toString()).orElse("-1");
    }

    /**
     * 加班序列流节点
     *
     * @param businessId 业务主键
     * @param code       code
     * @return String
     */
    public String getOvertimeWorkflowSequenceDateConstraint(String businessId, String code) {
        log.info("OvertimeWorkflowSequence start businessId={}", businessId);
        Integer overtimeId = Integer.valueOf(businessId);
        String constraintData = null;
        switch (code) {
            case "OVERTIME_TYPE":
                constraintData = getOvertimeIdString(overtimeId);
                break;
            case "COMPENSATE_TYPE":
                constraintData = getCompensateTypeString(overtimeId);
                break;
            case "OVERTIME_DURATION":
                constraintData = getOvertimeTimeDurationString(overtimeId);
                break;
            case "OVERTIME_UNIT":
                constraintData = PreTimeUnitEnum.HOUR.getIndex().toString();
                break;
            case "CYCLE_OVERTIME_DURATION":
                constraintData = getCycleOvertimeDurationString(overtimeId);
                break;
            case "OVERTIME_JOB_ATTRIBUTES":
                constraintData = getEmpWorkAttributesString(overtimeId);
                break;
            default:
                break;
        }
        log.info("OvertimeWorkflowSequence end businessId={}, constraintData={}", businessId, constraintData);
        return constraintData;
    }

    private String getEmpInfoProperties(String tenantId, Long empId, String propertyName) {
        DataFilter filter = DataFilter.eq("tenantId", tenantId).andEq("empId", empId.toString())
                .andNe("deleted", Boolean.TRUE.toString());
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            List<EmpWorkInfo> items = DataQuery.identifier(EMP_WORK_INFO_IDENTIFIER).decrypt().dept().specifyLanguage()
                    .queryInvisible().exp().limit(1, 1).filter(filter, EmpWorkInfo.class, System.currentTimeMillis())
                    .getItems();
            if (CollectionUtils.isNotEmpty(items)) {
                EmpWorkInfo empWorkInfo = items.get(0);
                if (null != empWorkInfo.getWorkAttribute()) {
                    return Optional.ofNullable(empWorkInfo.getWorkAttribute().getValue()).orElse("-1");
                }
            }
        } catch (Exception e) {
            log.error("Query {} exception:{}", EMP_WORK_INFO_IDENTIFIER, e.getMessage(), e);
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        return "-1";
    }

    private String getEmpWorkAttributesString(Integer overtimeId) {
        log.error("getEmpWorkAttributesString, overtimeId:{}", overtimeId);
        Optional<WaEmpOvertime> opt = Optional.ofNullable(waEmpOvertimeMapper.selectByPrimaryKey(overtimeId));
        if (!opt.isPresent()) {
            log.error("getEmpWorkAttributesString, overtime is null:{}", overtimeId);
            return "-1";
        }
        WaEmpOvertime overtime = opt.get();
        String tenantId = overtime.getTenantId();
        Long empId = overtime.getEmpid();
        return getEmpInfoProperties(tenantId, empId, "workAttribute");
    }

    private String getCycleOvertimeDurationString(Integer overtimeId) {
        Optional<WaEmpOvertime> opt = Optional.ofNullable(waEmpOvertimeMapper.selectByPrimaryKey(overtimeId));
        if (!opt.isPresent()) {
            return "-1";
        }
        WaEmpOvertime overtime = opt.get();
        String tenantId = overtime.getTenantId();
        Long empId = overtime.getEmpid();
        Long startDate = DateUtil.getOnlyDate(new Date(overtime.getStartTime() * 1000));
        Optional<WaSob> optional = Optional.ofNullable(waSobService.getWaSob(empId, startDate));
        if (!optional.isPresent()) {
            return "-1";
        }
        WaSob waSob = optional.get();
        Long sobStartDate = waSob.getStartDate();
        Long sobEndDate = waSob.getSobEndDate();
        WaEmpOvertimeExample overtimeExample = new WaEmpOvertimeExample();
        overtimeExample.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andEmpidEqualTo(empId)
                .andStatusIn(Arrays.asList((short) 1, (short) 2))
                .andStartTimeBetween(sobStartDate, sobEndDate);
        List<WaEmpOvertime> records = waEmpOvertimeMapper.selectByExample(overtimeExample);
        return BigDecimal.valueOf(records.stream().map(WaEmpOvertime::getOtDuration).filter(Objects::nonNull).reduce(0, Integer::sum) / 60).toPlainString();
    }

    /**
     * 批量加班序列流节点
     *
     * @param businessId 业务主键
     * @param code       code
     * @return String
     */
    public String getBatchOvertimeWorkflowSequenceDateConstraint(String businessId, String code) {
        WaBatchOvertimeDo batchOvertime = waBatchOvertimeDo.getById(Long.valueOf(businessId));
        if (null == batchOvertime) {
            log.info("BatchOvertimeWorkflowSequence Fail batchOvertime Empty businessId={}", businessId);
            return null;
        }
        String constraintData = null;
        switch (code) {
            case "BATCH_OVERTIME_DURATION":
                constraintData = BigDecimal.valueOf(batchOvertime.getTimeDuration() / 60).toString();
                break;
            case "BATCH_OVERTIME_UNIT":
                constraintData = PreTimeUnitEnum.HOUR.getIndex().toString();
                break;
            default:
                break;
        }
        return constraintData;
    }

    /**
     * 加班撤销序列流节点
     *
     * @param businessId 业务主键
     * @param code       code
     * @return String
     */
    public String getOvertimeRevokeWorkflowSequenceDateConstraint(String businessId, String code) {
        Long overtimeRevokeId = Long.valueOf(businessId);
        Optional<WaWorkflowRevoke> opt = Optional.ofNullable(workflowRevokeMapper.selectByPrimaryKey(overtimeRevokeId));
        return opt.map(waWorkflowRevoke -> getOvertimeWorkflowSequenceDateConstraint(String.valueOf(waWorkflowRevoke.getEntityId()), code)).orElse("-1");
    }

    /**
     * 加班类型
     *
     * @param overtimeId 主键
     * @return String
     */
    private String getOvertimeIdString(Integer overtimeId) {
        Optional<WaEmpOvertime> opt = Optional.ofNullable(waEmpOvertimeMapper.selectByPrimaryKey(overtimeId));
        return opt.map(waEmpOvertime -> waEmpOvertime.getDateType().toString()).orElse("-1");
    }

    /**
     * 补偿方式
     *
     * @param overtimeId 主键
     * @return String
     */
    private String getCompensateTypeString(Integer overtimeId) {
        Optional<WaEmpOvertime> opt = Optional.ofNullable(waEmpOvertimeMapper.selectByPrimaryKey(overtimeId));
        return opt.map(waEmpOvertime -> waEmpOvertime.getCompensateType().toString()).orElse("-1");
    }

    /**
     * 总时长
     *
     * @param overtimeId 主键
     * @return String
     */
    private String getOvertimeTimeDurationString(Integer overtimeId) {
        Optional<WaEmpOvertime> opt = Optional.ofNullable(waEmpOvertimeMapper.selectByPrimaryKey(overtimeId));
        return opt.map(waEmpOvertime -> BigDecimal.valueOf(waEmpOvertime.getOtDuration() / 60).toPlainString()).orElse("-1");
    }

    /**
     * 出差序列流节点
     *
     * @param businessId 业务主键
     * @param code       code
     * @return String
     */
    public String getTravelWorkflowSequenceDateConstraint(String businessId, String code) {
        log.info("WorkflowSequence start businessId={}", businessId);
        Long travelId = Long.valueOf(businessId);
        String constraintData = null;
        switch (code) {
            case "TRAVEL_TYPE":
                constraintData = getTravelType(travelId);
                break;
            case "TRAVEL_DURATION":
                constraintData = getTravelDuration(travelId);
                break;
            case "TRAVEL_UNIT":
                constraintData = getTravelUnit(travelId);
                break;
            default:
                break;
        }
        log.info("WorkflowSequence end businessId={},constraintData={}", businessId, constraintData);
        return constraintData;
    }

    /**
     * 出差撤销序列流节点
     *
     * @param businessId 业务主键
     * @param code       code
     * @return String
     */
    public String getTravelRevokeWorkflowSequenceDateConstraint(String businessId, String code) {
        Long travelRevokeId = Long.valueOf(businessId);
        Optional<WaWorkflowRevoke> opt = Optional.ofNullable(workflowRevokeMapper.selectByPrimaryKey(travelRevokeId));
        return opt.map(waWorkflowRevoke -> getTravelWorkflowSequenceDateConstraint(String.valueOf(waWorkflowRevoke.getEntityId()), code)).orElse("-1");
    }

    /**
     * 出差类型
     *
     * @param travelId 出差id
     * @return String
     */
    private String getTravelType(Long travelId) {
        Optional<WaEmpTravel> opt = Optional.ofNullable(waEmpTravelMapper.selectByPrimaryKey(travelId));
        return opt.map(waEmpLeaveCancel -> waEmpLeaveCancel.getTravelTypeId().toString()).orElse("-1");
    }

    /**
     * 总时长
     *
     * @param travelId 出差id
     * @return String
     */
    private String getTravelDuration(Long travelId) {
        Optional<WaEmpTravel> opt = Optional.ofNullable(waEmpTravelMapper.selectByPrimaryKey(travelId));
        return opt.map(waEmpTravel -> waEmpTravel.getTimeUnit() == 1 ? waEmpTravel.getTimeDuration().toString() : BigDecimal.valueOf(waEmpTravel.getTimeDuration() / 60).toString()).orElse(null);
    }

    /**
     * 单位（小时、天）
     *
     * @param travelId 出差id
     * @return String
     */
    private String getTravelUnit(Long travelId) {
        Optional<WaEmpTravel> opt = Optional.ofNullable(waEmpTravelMapper.selectByPrimaryKey(travelId));
        return opt.map(waEmpTravel -> waEmpTravel.getTimeUnit().toString()).orElse("-1");
    }

    /**
     * 批量出差序列流节点
     *
     * @param businessId 业务主键
     * @param code       code
     * @return String
     */
    public String getBatchTravelWorkflowSequenceDateConstraint(String businessId, String code) {
        Long travelId = Long.valueOf(businessId);
        WaBatchTravelDo batchTravel = waBatchTravelDo.getById(travelId);
        if (null == batchTravel) {
            log.info("BatchTravelWorkflowSequence Fail batchTravel Empty businessId={}", businessId);
            return null;
        }
        List<WaEmpTravelDo> empTravelDetailList = waEmpTravelDo.listByBatchId(batchTravel.getTenantId(), batchTravel.getBatchTravelId());
        if (CollectionUtils.isEmpty(empTravelDetailList)) {
            log.info("BatchTravelWorkflowSequence Fail empTravelDetailList Empty businessId={}", businessId);
            return null;
        }
        WaEmpTravelDo waEmpTravel = empTravelDetailList.get(0);
        String constraintData = null;
        switch (code) {
            case "BATCH_TRAVEL_TYPE":
                constraintData = null != waEmpTravel.getTravelTypeId() ? waEmpTravel.getTravelTypeId().toString() : "-1";
                break;
            case "BATCH_TRAVEL_DURATION":
                if (null != waEmpTravel.getTimeUnit() && null != waEmpTravel.getTimeDuration()) {
                    constraintData = waEmpTravel.getTimeUnit() == 1 ? waEmpTravel.getTimeDuration().toString()
                            : BigDecimal.valueOf(waEmpTravel.getTimeDuration() / 60).toString();
                }
                break;
            case "BATCH_TRAVEL_UNIT":
                constraintData = null != waEmpTravel.getTimeUnit() ? waEmpTravel.getTimeUnit().toString() : "-1";
                break;
            case "BATCH_TRAVEL_COST_CENTER":
                constraintData = handleBatchTravelLCostCenter(waEmpTravel);
                break;
            default:
                break;
        }
        return constraintData;
    }

    private String handleBatchTravelLCostCenter(WaEmpTravelDo empTravel) {
        if (empTravel == null || StringUtils.isBlank(empTravel.getExtCustomCol())) {
            return "false";
        }
        var travelDetail = FastjsonUtil.convertObject(empTravel.getExtCustomCol(), BatchTravelDto.class);
        val costCenterId = StringUtils.defaultString(travelDetail.getCostCenterId(), "0");
        var pageResult = MdDataQuery.identifier("entity.hr.EmpWorkInfo")
                .filter(DataFilter.eq("empId", empTravel.getEmpId() == null ? "0" : empTravel.getEmpId().toString())
                        .andEq("deleted", Boolean.FALSE.toString()), EmpInfoEntity.class);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return "false";
        }
        List<String> costCentersLit = pageResult.getItems().stream()
                .filter(e -> StringUtils.isNotBlank(e.getCostCenters()))
                .map(e -> e.getCostCenters())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(costCentersLit)) {
            return "false";
        }
        for (String costCenterStr : costCentersLit) {
            if ("\"[]\"".equals(costCenterStr)) {
                continue;
            }
            Map<String, Object> costCenterMap = FastjsonUtil.toObject(costCenterStr, Map.class);
            val costCenterId2 = Objects.isNull(costCenterMap.get("cost")) ? "" : String.valueOf(costCenterMap.get("cost"));
            if (costCenterMap.containsKey("cost") && costCenterId2.equals(costCenterId)) {
                return "true";
            }
        }
        return "false";
    }

    /**
     * 销假序列流节点
     *
     * @param businessId 业务主键
     * @param code       code
     * @return String
     */
    public String getLeaveCancelWorkflowSequenceDateConstraint(String businessId, String code) {
        Long leaveCancelId = Long.valueOf(businessId);
        String constraintData = null;
        switch (code) {
            case "LEAVE_CANCEL_TYPE":
                constraintData = getLeaveCancelType(leaveCancelId);
                break;
            case "LEAVE_CANCEL_DURATION":
                constraintData = getLeaveCancelDuration(leaveCancelId);
                break;
            case "LEAVE_TIME_DURATION":
                constraintData = getLeaveCancelLeaveDuration(leaveCancelId);
                break;
            case "LEAVE_CANCEL_UNIT":
                constraintData = getLeaveCancelUnit(leaveCancelId);
                break;
            case "CANCEL_LEAVE_TYPE":
                constraintData = getLeaveCancelLeaveType(leaveCancelId);
                break;
            default:
                break;
        }
        return constraintData;
    }

    /**
     * 销假类型
     *
     * @param leaveCancelId 销假id
     * @return String
     */
    private String getLeaveCancelType(Long leaveCancelId) {
        Optional<WaEmpLeaveCancel> opt = Optional.ofNullable(leaveCancelMapper.selectByPrimaryKey(leaveCancelId));
        return opt.map(waEmpLeaveCancel -> waEmpLeaveCancel.getTypeId().toString()).orElse("-1");
    }

    /**
     * 销假时长
     *
     * @param leaveCancelId 销假id
     * @return String
     */
    private String getLeaveCancelDuration(Long leaveCancelId) {
        Optional<WaEmpLeaveCancel> opt = Optional.ofNullable(leaveCancelMapper.selectByPrimaryKey(leaveCancelId));
        return opt.map(waEmpLeaveCancel -> waEmpLeaveCancel.getTimeUnit() == 1 ? waEmpLeaveCancel.getTimeDuration().toString() : BigDecimal.valueOf(waEmpLeaveCancel.getTimeDuration() / 60).toString()).orElse("-1");
    }

    /**
     * 休假时长
     *
     * @param leaveCancelId 销假id
     * @return String
     */
    private String getLeaveCancelLeaveDuration(Long leaveCancelId) {
        Optional<WaEmpLeaveCancel> opt = Optional.ofNullable(leaveCancelMapper.selectByPrimaryKey(leaveCancelId));
        if (!opt.isPresent()) {
            return null;
        }
        Integer leaveId = opt.get().getLeaveId();
        Optional<WaEmpLeave> leaveOpt = Optional.ofNullable(waEmpLeaveMapper.selectByPrimaryKey(leaveId));
        if (!leaveOpt.isPresent()) {
            return null;
        }
        WaEmpLeave empLeave = leaveOpt.get();
        Float cancelDuration = Optional.ofNullable(empLeave.getCancelTimeDuration()).orElse(0f);
        Float actualTimeDuration = empLeave.getTotalTimeDuration() - cancelDuration;
        if (LeaveTypeUnitEnum.HOUR.getIndex().equals(empLeave.getTimeUnit())) {
            MathContext mc = new MathContext(2, RoundingMode.HALF_DOWN);
            BigDecimal a = new BigDecimal(actualTimeDuration);
            BigDecimal b = a.divide(new BigDecimal(60), mc);
            return b.toString();
        } else {
            return actualTimeDuration.toString();
        }
    }

    /**
     * 单位（小时、天）
     *
     * @param leaveCancelId 销假id
     * @return String
     */
    private String getLeaveCancelUnit(Long leaveCancelId) {
        Optional<WaEmpLeaveCancel> opt = Optional.ofNullable(leaveCancelMapper.selectByPrimaryKey(leaveCancelId));
        return opt.map(waEmpLeaveCancel -> waEmpLeaveCancel.getTimeUnit().toString()).orElse("-1");
    }

    /**
     * 休假类型
     *
     * @param leaveCancelId 销假id
     * @return String
     */
    private String getLeaveCancelLeaveType(Long leaveCancelId) {
        Optional<WaEmpLeaveCancel> opt = Optional.ofNullable(leaveCancelMapper.selectByPrimaryKey(leaveCancelId));
        return opt.map(waEmpLeaveCancel -> waEmpLeaveCancel.getLeaveTypeId().toString()).orElse("-1");
    }

    /**
     * 调休付现序列流节点
     *
     * @param businessId 主键
     * @param code       code
     * @return String
     */
    public String getCompensatoryWorkflowSequenceDateConstraint(String businessId, String code) {
        Long id = Long.valueOf(businessId);
        String constraintData = null;
        switch (code) {
            case "COMPENSATORY_TOTAL_DURATION":
                constraintData = getCompensatoryTimeDurationString(id);
                break;
            case "COMPENSATORY_TIME_UNIT":
                constraintData = getCompensatoryTimeUnitString(id);
                break;
            default:
                break;
        }
        return constraintData;
    }

    /**
     * 总时长
     *
     * @param id 主键
     * @return String
     */
    private String getCompensatoryTimeDurationString(Long id) {
        Optional<WaEmpCompensatoryCaseApply> opt = Optional.ofNullable(empCompensatoryCaseApplyMapper.selectByPrimaryKey(id));
        return opt.map(model -> model.getTimeUnit() == 1 ? model.getApplyDuration().toString() : BigDecimal.valueOf(model.getApplyDuration() / 60).toString()).orElse("-1");
    }

    /**
     * 单位（小时、天）
     *
     * @param id 主键
     * @return String
     */
    private String getCompensatoryTimeUnitString(Long id) {
        Optional<WaEmpCompensatoryCaseApply> opt = Optional.ofNullable(empCompensatoryCaseApplyMapper.selectByPrimaryKey(id));
        return opt.map(model -> model.getTimeUnit().toString()).orElse("-1");
    }

    /**
     * 假期延期序列流节点
     *
     * @param businessId 主键
     * @param code       code
     * @return String
     */
    public String getLeaveExtensionWorkflowSequenceDateConstraint(String businessId, String code) {
        log.info("getLeaveExtensionWorkflowSequenceDateConstraint businessId={}, code={}", businessId, code);
        Long id = Long.valueOf(businessId);
        String cacheValue = (String) redisTemplate.opsForValue().get("import_leave_id_" + businessId);
        log.info("getLeaveExtensionWorkflowSequenceDateConstraint cacheValue={}", cacheValue);
        String constraintData = null;
        switch (code) {
            case "LEAVE_EXTENSION_TYPE":
                constraintData = getLeaveExtensionTypeString(id);
                break;
            case "LEAVE_EXTENSION_TOTAL_DURATION":
                constraintData = getLeaveExtensionTimeDurationString(id);
                break;
            case "LEAVE_EXTENSION_TIME_UNIT":
                constraintData = getLeaveExtensionTimeUnitString(id);
                break;
            default:
                break;
        }
        return constraintData;
    }

    /**
     * 假期延期假期类型
     *
     * @param id 主键
     * @return String
     */
    private String getLeaveExtensionTypeString(Long id) {
        Optional<WaLeaveExtension> opt = Optional.ofNullable(waLeaveExtensionMapper.selectByPrimaryKey(id));
        return opt.map(model -> model.getConfigId().toString()).orElse("-1");
    }

    /**
     * 假期延期延期额度
     *
     * @param id 主键
     * @return String
     */
    private String getLeaveExtensionTimeDurationString(Long id) {
        Optional<WaLeaveExtension> opt = Optional.ofNullable(waLeaveExtensionMapper.selectByPrimaryKey(id));
        return opt.map(model -> model.getTimeDuration().toString()).orElse("-1");
    }

    /**
     * 假期延期单位（小时、天）
     *
     * @param id 主键
     * @return String
     */
    private String getLeaveExtensionTimeUnitString(Long id) {
        Optional<WaLeaveExtension> opt = Optional.ofNullable(waLeaveExtensionMapper.selectByPrimaryKey(id));
        return opt.map(model -> model.getTimeUnit().toString()).orElse("-1");
    }

    public String registerRecordCardReplacementWorkflowSequenceDateConstraint(String businessId, String code) {
        log.info("registerRecordCardReplacementWorkflowSequenceDateConstraint businessId={}, code={}", businessId, code);
        Long id = Long.valueOf(businessId);
        String constraintData = null;
        if ("NUMBER_OF_CARD_REPLACEMENT".equals(code)) {
            constraintData = getCardReplacementNumberString(id);
        }
        return constraintData;
    }

    /**
     * 考勤周期内补卡次数
     *
     * @param id 主键
     * @return String
     */
    private String getCardReplacementNumberString(Long id) {
        Optional<WaRegisterRecordBdkPo> bdkOpt = Optional.ofNullable(waRegisterRecordBdkMapper.selectByPrimaryKey(id));
        if (!bdkOpt.isPresent()) {
            return "-1";
        }
        WaRegisterRecordBdkPo bdk = bdkOpt.get();
        String tenantId = bdk.getBelongOrgId();
        Long empId = bdk.getEmpid();
        Optional<WaSob> optional = Optional.ofNullable(waSobService.getWaSob(empId, bdk.getBelongDate()));
        if (!optional.isPresent()) {
            return "-1";
        }
        WaSob waSob = optional.get();
        Long sobStartDate = waSob.getStartDate();
        Long sobEndDate = waSob.getSobEndDate();
        WaRegisterRecordExample registerRecordExample = new WaRegisterRecordExample();
        registerRecordExample.createCriteria()
                .andBelongOrgIdEqualTo(tenantId)
                .andEmpidEqualTo(empId)
                .andTypeEqualTo(6)
                .andApprovalStatusIn(Arrays.asList(1, 2))
                .andBelongDateBetween(sobStartDate, sobEndDate);
        List<WaRegisterRecord> records = waRegisterRecordMapper.selectByExample(registerRecordExample);
        return String.valueOf(records.size());
    }
}