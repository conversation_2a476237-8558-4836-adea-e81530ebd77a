# 在 Nacos 配置中心设置以下配置来启用死信队列

attendance:
  mq:
    # 启用死信队列功能
    enableDLQ: true
    
    # 其他推荐配置
    maxRetryCount: 3
    failureStrategy: ACK
    consumer:
      concurrentConsumers: 2
      maxConcurrentConsumers: 5
      prefetchCount: 1
    queue:
      messageTtl: 0
      autoDelete: false

# 设置后重启应用，将会创建：
# 1. 主队列：attendance.clock.analyse.multinode.queue（带死信队列参数）
# 2. 死信交换机：attendance.clock.analyse.multinode.dlq.exchange
# 3. 死信队列：attendance.clock.analyse.multinode.dlq
# 4. 死信队列绑定
# 5. 死信队列监控消费者 