package com.caidaocloud.attendance.service.domain.entity;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.wa.mybatis.model.WaAnalyze;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.dto.EmpRegisterTimeDto;
import com.caidaocloud.attendance.service.domain.repository.IRegisterRecordRepository;
import com.caidaocloud.attendance.service.domain.repository.IWaAnalyzeRepository;
import com.caidaocloud.attendance.service.interfaces.dto.SummaryDetailDto;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.googlecode.totallylazy.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Service
public class WaAnalyzeDo {
    private Integer analyzeId;
    private String belongOrgId;
    private Long empid;
    private Integer signinId;
    private Integer signoffId;
    private Integer workTime;
    private Float lateTime;
    private Float earlyTime;
    private Integer leaveTime;
    private Long belongDate;
    private String errMsg;
    private Integer shiftDefId;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private Integer isExp;
    private Float actualWorkTime;
    private Integer isKg;
    private Object levelColumnJsonb;
    private Object otColumnJsob;
    private Integer kgWorkTime;
    private Long regSigninTime;
    private Long regSignoffTime;
    private Object originLevelColumnJsonb;
    private Object originOtColumnJsonb;
    private Integer registerTime;
    private Integer isShift;
    private Integer bdkCount;
    private Object extCustomColJson;
    private Integer holidayWorkTime;
    private Integer abnormalAppealTime;
    private Integer clockType;
    private Object travelColumnJsonb;
    private Object originTravelColumnJsonb;

    //wa_analyze 表外的其他业务字段
    private String workNo;
    private String shiftDefName;
    private String clockRule;
    private Integer startTime;
    private Integer endTime;
    private Integer onDutyStartTime;
    private Integer onDutyEndTime;
    private Integer offDutyStartTime;
    private Integer offDutyEndTime;
    private Integer regSignInTime;
    private Integer regSignOffTime;
    private Integer regSignInResult;
    private Integer regSignOffResult;
    private Integer makeUpSignTime;
    private Integer makeOffSignTime;

    @ApiModelProperty("加班时长（分钟）")
    private Integer otTime;
    @ApiModelProperty("休假次数")
    private Integer leaveCount;
    @ApiModelProperty("出差次数")
    private Integer travelCount;


    @Autowired
    private IRegisterRecordRepository registerRecordRepository;
    @Autowired
    private IWaAnalyzeRepository waAnalyzeRepository;

    public WaAnalyzeDo getAnalyzeDetailById(Integer analyzeId) {
        Map map = registerRecordRepository.getDayAnalyzeDetailById(analyzeId);
        if (null == map) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(map), WaAnalyzeDo.class);
    }

    public Map<String, Object> getSummaryCountData(String belongOrgId, String[] orgIds, Long startDate, Long endDate, String scope) {
        return waAnalyzeRepository.getStatisticsSummaryCount(belongOrgId, orgIds, startDate, endDate, scope);
    }

    public PageList<Map> getSummaryWorkList(SummaryDetailDto dto, Map<String, Object> params) {
        return waAnalyzeRepository.getSummaryWorkList(dto, params);
    }

    public List<Map> getSummaryWorkRate(Map<String, Object> params) {
        return waAnalyzeRepository.getSummaryWorkRate(params);
    }

    public List<Map> getSummaryTimeRate(Map<String, Object> params) {
        return waAnalyzeRepository.getSummaryTimeRate(params);
    }

    public List<Map> getSummaryLtRate(Map<String, Object> params) {
        return waAnalyzeRepository.getSummaryLtRate(params);
    }

    public PageList<Map> selectWaAnalyseListByWaGroup(MyPageBounds pageBounds, Map<String, Object> paramsMap) {
        return registerRecordRepository.selectWaAnalyseListByWaGroup(pageBounds, paramsMap);
    }

    public AttendancePageResult<WaAnalyzeDo> getWaAnalyzePageList(AttendanceBasePage basePage, String belongOrgId, List<Long> empIdList, Long startDate, Long endDate) {
        return waAnalyzeRepository.getWaAnalyzePageList(basePage, belongOrgId, empIdList, startDate, endDate);
    }

    public List<WaAnalyzeDo> getWaAnalyzeByRegisterRecordIds(String belongOrgId, List<Integer> registerRecordIds) {
        return waAnalyzeRepository.getWaAnalyzeByRegisterRecordIds(belongOrgId, registerRecordIds);
    }

    public List<WaAnalyzeDo> getWaAnaLyzeList(String belongOrgId,Long empId,Long startDate,Long endDate){
        return waAnalyzeRepository.getWaAnalyzeList(belongOrgId,empId,startDate,endDate);
    }

    public PageList<Map> selectWaAnalyseList(MyPageBounds pageBounds, Map<String, Object> paramsMap) {
        return registerRecordRepository.selectWaAnalyseList(pageBounds, paramsMap);
    }

    public List<Long> selectWaAbnormalAnalyseList(String tenantId, Long startDate, Long endDate, Integer analyzeResult, List<Integer> waGroupIds) {
        return registerRecordRepository.selectWaAbnormalAnalyseList(tenantId, startDate, endDate, analyzeResult, waGroupIds);
    }

    public AttendancePageResult<WaAnalyze> getAnalyseList(AttendanceBasePage basePage, Map<String, Object> paramsMap) {
        return registerRecordRepository.selectAnalyseList(basePage, paramsMap);
    }

    public Map<String, BigDecimal> groupRegisterTimeByEmps(String tenantId, long startTime, long endTime, List<String> empIds) {
        Map<String, BigDecimal> result = Maps.map();
        val registerTimes = waAnalyzeRepository.groupRegisterTimeByEmps(tenantId, startTime, endTime, empIds);
        registerTimes.forEach(it->{
            val empId = (Long)it.get("empid");
            val time = (Number)it.get("time");
            if(null == time){
                return;
            }
            result.put(empId.toString(), new BigDecimal(String.valueOf(time)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP));
        });
        empIds.forEach(empId->{
            if(!result.containsKey(empId)){
                result.put(empId, new BigDecimal(0));
            }
        });
        return result;
    }

    public List<EmpRegisterTimeDto> listRegisterTimeByEmps(String tenantId, long startTime, long endTime, List<String> empIds) {
        return waAnalyzeRepository.listRegisterTimeByEmps(tenantId, startTime, endTime, empIds);
    }
}
