package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.infrastructure.util.DateUtil;
import lombok.Getter;
import org.apache.commons.lang3.tuple.ImmutablePair;

/**
 * created by: FoAng
 * create time: 22/3/2024 10:47 上午
 */
@Getter
public enum TimeTypeEnum {

    DAY("日统计"),
    WEEK("周统计"),
    MONTH("月统计");

    final String label;

    TimeTypeEnum(String label) {
        this.label = label;
    }

    public static ImmutablePair<Long, Long> parsePeriod(TimeTypeEnum typeEnum, Long startDay, Long endDay) {
        Long todayInstant = DateUtil.toInstant(DateUtil.getTodayStartOfDay());
        Long pickDayInstant = startDay == null ? todayInstant : startDay;
        if (typeEnum == null || typeEnum == DAY || endDay == null) {
            return new ImmutablePair<>(pickDayInstant, pickDayInstant);
        } else {
            return new ImmutablePair<>(pickDayInstant, endDay);
        }
    }
}
