package com.caidaocloud.attendance.service.application.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/7/31
 */
@Data
public class OvertimeQuotaDto {
    private Long empid;
    private Integer overtimeDetailId;
    private Long startTime;
    private Long endTime;
    private Integer overtimeDuration;
    private Float realOvertimeDuration;
    private Long belongDate;
    private Integer dateType;
    private Integer compensateType;
    private Integer overtimeTypeId;

    private Long ruleId;
    private Integer leaveTypeId;
    private Float leftDuration;
    private Integer carriedForward;
    /**
     * 上次分析的有效加班时长
     */
    private Float relTimeDuration;
    /**
     * 是否零点拆分
     */
    private Boolean zeroSplitting;

    private Long realDate;
}
