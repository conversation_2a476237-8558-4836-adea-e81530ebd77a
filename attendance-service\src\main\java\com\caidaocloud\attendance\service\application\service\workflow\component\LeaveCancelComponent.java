package com.caidaocloud.attendance.service.application.service.workflow.component;

import com.caidaocloud.attendance.service.application.enums.LeaveCancelTypeEnum;
import com.caidaocloud.workflow.annotation.WfComponentValueEnumDef;
import com.caidaocloud.workflow.dto.WfComponentValueDto;
import com.google.common.collect.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 注册销假类型枚举清单
 */
@Component
public class LeaveCancelComponent extends WfComponentValueEnumDef {
    @NotNull
    @Override
    public List<WfComponentValueDto> enumList() {
        return Lists.newArrayList(
                new WfComponentValueDto(LeaveCancelTypeEnum.LEAVE_END.getName(), LeaveCancelTypeEnum.LEAVE_END.getIndex().toString()),
                new WfComponentValueDto(LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getName(), LeaveCancelTypeEnum.TIME_ADJUST_PARTIAL.getIndex().toString()),
                new WfComponentValueDto(LeaveCancelTypeEnum.LEAVE_CANCEL.getName(), LeaveCancelTypeEnum.LEAVE_CANCEL.getIndex().toString()),
                new WfComponentValueDto(LeaveCancelTypeEnum.TIME_ADJUST.getName(), LeaveCancelTypeEnum.TIME_ADJUST.getIndex().toString()));
    }
}
