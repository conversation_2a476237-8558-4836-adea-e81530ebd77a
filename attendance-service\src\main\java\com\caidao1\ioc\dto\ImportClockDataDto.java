package com.caidao1.ioc.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 考勤记录导入或接入的数据DTO
 */
@Data
@Accessors(chain = true)
public class ImportClockDataDto {
    /**
     * 本次导入的最小打卡日期
     */
    private Long startDate;

    /**
     * 本次导入的最晚打卡日期
     */
    private Long endDate;

    /**
     * 本次导入的员工ID集合
     */
    private List<Long> empIds;

    /**
     * 本次导入的员工打卡数据：key=员工ID，value=打卡日期集合
     */
    private Map<Long, List<Long>> empRelBelongDateMap;

    /**
     * 本次导入的员工打卡数据：item=empId + "_" + date
     */
    private List<String> empRelBelongDateList;

    /**
     * 本次导入的员工打卡数据：key=打卡日期，value=员工ID集合
     */
    private Map<Long, List<Long>> regDateRelEmpIdMap;
}
