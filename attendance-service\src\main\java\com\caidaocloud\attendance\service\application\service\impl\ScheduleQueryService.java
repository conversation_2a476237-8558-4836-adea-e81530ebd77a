package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.exception.CDException;
import com.caidao1.wa.dto.EmpCalendarInfoDto;
import com.caidao1.wa.mybatis.model.WaEmpLeave;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpRelCalendarQueryDto;
import com.caidaocloud.attendance.core.wa.enums.CalendarWorktimeTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.IScheduleQueryService;
import com.caidaocloud.attendance.service.application.service.IWorkCalendarService;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveDaytimeDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.schedule.application.service.schedule.IEmpScheduleService;
import com.caidaocloud.attendance.service.wfm.application.dto.EmpMultiShiftInfoDto;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 员工排班查询服务
 *
 * <AUTHOR>
 * @Date 2025/2/15
 */
@Slf4j
@Service
public class ScheduleQueryService implements IScheduleQueryService {
    @Autowired
    private IWorkCalendarService workCalendarService;
    @Autowired
    private IEmpScheduleService empScheduleService;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WaLeaveDaytimeDo waLeaveDaytimeDo;

    /**
     * 批量查询多个员工的排班信息
     *
     * @param belongOrgId
     * @param startDate
     * @param endDate
     * @param empIds
     * @return
     */
    @Override
    public List<WaShiftDo> getEmpCalendarShiftList(String belongOrgId, Long startDate, Long endDate, List<Long> empIds) {
        // 查询员工日历考勤类型
        ListEmpRelCalendarQueryDto calendarQueryDto = new ListEmpRelCalendarQueryDto();
        calendarQueryDto.setBelongOrgid(belongOrgId);
        calendarQueryDto.setEmpids(empIds);
        calendarQueryDto.setStartDate(startDate);
        calendarQueryDto.setEndDate(endDate);
        List<EmpCalendarInfoDto> empCalendarList = workCalendarService.getEmpCalendarList(calendarQueryDto);
        if (CollectionUtils.isEmpty(empCalendarList)) {// 固定班次
            return workCalendarService.getEmpCalendarShiftList(belongOrgId, startDate, endDate, empIds);
        }

        Map<Long, List<EmpCalendarInfoDto>> empCalendarListMap = empCalendarList.stream().collect(Collectors.groupingBy(EmpCalendarInfoDto::getEmpid));
        Map<Integer, List<EmpCalendarInfoDto>> calendarWorktimeTypeListMap = empCalendarList.stream()
                .collect(Collectors.groupingBy(it -> Optional.ofNullable(it.getWorktimeType())
                        .orElse(CalendarWorktimeTypeEnum.FIXED_SCHEDULE.getIndex())));
        List<Integer> worktimeTypeKeyList = calendarWorktimeTypeListMap.keySet().stream().distinct().collect(Collectors.toList());

        // 只存在一种排班制
        if (worktimeTypeKeyList.size() == 1) {
            if (CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(worktimeTypeKeyList.get(0))) {
                List<EmpMultiShiftInfoDto> scheduleEmpShiftDtoList = empScheduleService.getEmpShiftInfos(belongOrgId, startDate, endDate, empIds);
                return doConvertForSchedule(belongOrgId, scheduleEmpShiftDtoList);
            } else {// 固定班次
                return workCalendarService.getEmpCalendarShiftList(belongOrgId, startDate, endDate, empIds);
            }
        }

        // 同时存在多种排班制
        List<WaShiftDo> waShiftDoList = Lists.newArrayList();

        // 排班制员工
        List<EmpCalendarInfoDto> scheduleCalendarList = calendarWorktimeTypeListMap.get(CalendarWorktimeTypeEnum.SCHEDULE.getIndex());
        List<Long> scheduleEmpIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(scheduleCalendarList)) {
            scheduleEmpIdList.addAll(scheduleCalendarList.stream().map(EmpCalendarInfoDto::getEmpid).distinct()
                    .collect(Collectors.toList()));
        }
        Map<String, List<WaShiftDo>> scheduleShiftDoMap = getScheduleShiftDoMap(scheduleEmpIdList, belongOrgId, startDate, endDate);

        // 固定班次员工
        List<EmpCalendarInfoDto> fixedCalendarList = calendarWorktimeTypeListMap.get(CalendarWorktimeTypeEnum.FIXED_SCHEDULE.getIndex());
        List<Long> fixedEmpIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(fixedCalendarList)) {
            fixedEmpIdList.addAll(fixedCalendarList.stream().map(EmpCalendarInfoDto::getEmpid).distinct().collect(Collectors.toList()));
        }
        Map<String, WaShiftDo> fixedShiftDoMap = getFixedShiftDoMap(fixedEmpIdList, belongOrgId, startDate, endDate);

        // 剩余的其他员工（默认：固定班次）
        List<Long> otherEmpIdList = empIds.stream()
                .filter(empId -> !scheduleEmpIdList.contains(empId) && !fixedEmpIdList.contains(empId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(otherEmpIdList)) {
            List<WaShiftDo> otherShiftDoList = workCalendarService.getEmpCalendarShiftList(belongOrgId,
                    startDate, endDate, otherEmpIdList);
            waShiftDoList.addAll(otherShiftDoList);
        }

        List<Long> calEmpIdList = empIds.stream().filter(empId -> !otherEmpIdList.contains(empId)).collect(Collectors.toList());
        for (Long empId : calEmpIdList) {
            List<EmpCalendarInfoDto> calendarList = empCalendarListMap.get(empId);
            long tmpDate = startDate;
            while (tmpDate <= endDate) {
                String shiftKey = String.format("%s_%s", empId, tmpDate);

                long finalTmpDate = tmpDate;
                Optional<EmpCalendarInfoDto> calendarOpt = calendarList.stream()
                        .filter(it -> finalTmpDate >= it.getStartTime() && finalTmpDate <= it.getEndTime())
                        .findFirst();
                if (calendarOpt.isPresent()
                        && CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(calendarOpt.get().getWorktimeType())) {
                    if (null != scheduleShiftDoMap.get(shiftKey)) {
                        waShiftDoList.addAll(scheduleShiftDoMap.get(shiftKey));
                    }
                } else {
                    if (null != fixedShiftDoMap.get(shiftKey)) {
                        waShiftDoList.add(fixedShiftDoMap.get(shiftKey));
                    }
                }
                tmpDate = tmpDate + 86400;
            }
        }
        return waShiftDoList;
    }

    /**
     * 查询员工排班（排班制）
     *
     * @param scheduleEmpIdList
     * @param belongOrgId
     * @param startDate
     * @param endDate
     * @return
     */
    private Map<String, List<WaShiftDo>> getScheduleShiftDoMap(List<Long> scheduleEmpIdList, String belongOrgId, Long startDate, Long endDate) {
        if (CollectionUtils.isEmpty(scheduleEmpIdList)) {
            return new HashMap<>();
        }
        List<EmpMultiShiftInfoDto> scheduleEmpShiftDtoList = empScheduleService.getEmpShiftInfos(belongOrgId, startDate, endDate, scheduleEmpIdList);
        if (CollectionUtils.isEmpty(scheduleEmpShiftDtoList)) {
            return new HashMap<>();
        }
        List<WaShiftDo> scheduleShiftDoList = doConvertForSchedule(belongOrgId, scheduleEmpShiftDtoList);
        return scheduleShiftDoList.stream()
                .collect(Collectors.groupingBy(it -> String.format("%s_%s", it.getEmpid(), it.getWorkDate())));
    }

    /**
     * 查询员工排班（固定班次）
     *
     * @param fixedEmpIdList
     * @param belongOrgId
     * @param startDate
     * @param endDate
     * @return
     */
    private Map<String, WaShiftDo> getFixedShiftDoMap(List<Long> fixedEmpIdList, String belongOrgId, Long startDate, Long endDate) {
        if (CollectionUtils.isEmpty(fixedEmpIdList)) {
            return new HashMap<>();
        }
        List<WaShiftDo> fixedShiftDoList = workCalendarService.getEmpCalendarShiftList(belongOrgId,
                startDate, endDate, fixedEmpIdList);
        if (CollectionUtils.isEmpty(fixedShiftDoList)) {
            return new HashMap<>();
        }
        return fixedShiftDoList.stream()
                .collect(Collectors.toMap(it -> String.format("%s_%s", it.getEmpid(), it.getWorkDate()),
                        Function.identity(), (v1, v2) -> v1));
    }

    /**
     * 排班制员工班次转换
     *
     * @param belongOrgId
     * @param scheduleEmpShiftDtoList
     * @return
     */
    private List<WaShiftDo> doConvertForSchedule(String belongOrgId, List<EmpMultiShiftInfoDto> scheduleEmpShiftDtoList) {
        List<WaShiftDo> waShiftDoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(scheduleEmpShiftDtoList)) {
            return waShiftDoList;
        }
        Map<Integer, WaShiftDef> corpAllShiftDef = waCommonService.getCorpAllShiftDef(belongOrgId);
        if (MapUtils.isEmpty(corpAllShiftDef)) {
            return waShiftDoList;
        }
        for (EmpMultiShiftInfoDto shiftInfoDto : scheduleEmpShiftDtoList) {
            Integer shiftDefId = shiftInfoDto.getShiftDefId().intValue();
            WaShiftDef shiftDef;
            if (!corpAllShiftDef.containsKey(shiftDefId) || null == (shiftDef = corpAllShiftDef.get(shiftDefId))) {
                continue;
            }
            WaShiftDo shiftDo = ObjectConverter.convert(shiftDef, WaShiftDo.class);
            shiftDo.setEmpid(shiftInfoDto.getEmpId());
            shiftDo.setWorkDate(shiftInfoDto.getWorkDate());
            shiftDo.setDateType(shiftInfoDto.getDateType());
            waShiftDoList.add(shiftDo);
        }
        return waShiftDoList;
    }

    @Override
    public Map<String, WaShiftDo> getEmpCalendarShiftMap(String belongOrgId, Long startDate, Long endDate, List<Long> empIds) {
        Map<String, WaShiftDo> empShiftMap = new HashMap<>(16);
        List<WaShiftDo> shiftDoList = getEmpCalendarShiftList(belongOrgId, startDate, endDate, empIds);
        if (CollectionUtils.isEmpty(shiftDoList)) {
            return empShiftMap;
        }
        Map<String, List<WaShiftDo>> shiftDoListMap = shiftDoList.stream()
                .collect(Collectors.groupingBy(it -> String.format("%s_%s", it.getEmpid(), it.getWorkDate())));
        shiftDoListMap.forEach((shiftKey, shiftList) -> {
            WaShiftDo shiftDo = ObjectConverter.convert(shiftList.get(0), WaShiftDo.class);
            shiftDo.setShiftDoList(shiftList);
            empShiftMap.put(shiftKey, shiftDo);
        });
        return empShiftMap;
    }

    @Override
    public List<WaShiftDo> getEmpShiftForLeaveCancel(String tenantId, Integer leaveId, Long leaveCancelDate, Integer leaveCancelType) {
        WaEmpLeave empLeave = waCommonService.getWaEmpLeave(leaveId);
        if (!Optional.ofNullable(empLeave).isPresent()) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.APPLY_NOT_EXIST, null).getMsg());
        }
        switch (leaveCancelType) {
            case 2://取消部分休假（原时间调整）
                return getEmpShiftForLeaveCancel(UserContext.getTenantId(), leaveId, leaveCancelDate);
            case 5://调整时间（新增）
                return getEmpCalendarShiftList(tenantId, leaveCancelDate, leaveCancelDate, Lists.newArrayList(empLeave.getEmpid()));
            default:
                return Lists.newArrayList();
        }
    }

    private List<WaShiftDo> getEmpShiftForLeaveCancel(String tenantId, Integer leaveId, Long leaveCancelDate) {
        List<WaShiftDo> waShiftDoList = Lists.newArrayList();
        List<WaLeaveDaytimeDo> leaveDayTimeList = waLeaveDaytimeDo.getByDate(leaveId, leaveCancelDate);
        if (CollectionUtils.isEmpty(leaveDayTimeList)) {
            return waShiftDoList;
        }
        List<Integer> shiftDefIds = leaveDayTimeList.stream().map(WaLeaveDaytimeDo::getUseShiftDefId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shiftDefIds)) {
            return waShiftDoList;
        }
        Map<Integer, WaShiftDef> corpAllShiftDef = waCommonService.getCorpAllShiftDef(tenantId, shiftDefIds);
        for (Integer shiftDefId : shiftDefIds) {
            WaShiftDef shiftDef;
            if (!corpAllShiftDef.containsKey(shiftDefId) || null == (shiftDef = corpAllShiftDef.get(shiftDefId))) {
                continue;
            }
            waShiftDoList.add(ObjectConverter.convert(shiftDef, WaShiftDo.class));
        }
        return waShiftDoList;
    }
}
