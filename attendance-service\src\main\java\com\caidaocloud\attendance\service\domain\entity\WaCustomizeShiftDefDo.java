package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiOvertimeDto;
import com.caidaocloud.attendance.core.wa.dto.shift.RestPeriodDto;
import com.caidaocloud.attendance.service.domain.repository.IWaCustomizeShiftDefRepository;
import com.caidaocloud.attendance.service.interfaces.dto.shift.CustomizeShiftDefQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.multi.MultiWorkTimeDto;
import com.caidaocloud.dto.UserInfo;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Data
@Slf4j
@Service
public class WaCustomizeShiftDefDo {
    private Long shiftDefId;

    private String shiftDefCode;

    private String shiftDefName;

    private String i18nShiftDefName;

    private Integer dateType;

    private String colorMark;

    private Integer workTotalTime;

    private Integer restTotalTime;

    private String multiWorkTimes;

    private String oriMultiWorkTimes;

    private String restPeriods;

    private Long empId;

    private Long belongDate;

    private String businessProcess;

    private String businessProcessId;

    private String businessId;

    private String businessType;

    private Long shiftGroupId;

    private String belongModule;

    private String tenantId;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    /**
     * 关联的标准班次表wa_shift_def的ID
     */
    private Integer waShiftDefId;

    // 其他业务字段
    /**
     * 休息时间段DTOList
     */
    private List<RestPeriodDto> restPeriodDtoList;

    /**
     * 工作日多段上班时间DTOList
     */
    private List<MultiWorkTimeDto> multiWorkTimeDtoList;

    /**
     * 多段加班时间DTOList
     */
    private List<MultiOvertimeDto> multiOvertimeDtoList;

    @Autowired
    private IWaCustomizeShiftDefRepository waCustomizeShiftDefRepository;

    public void doInitCrtField() {
        UserInfo userInfo = UserContext.preCheckUser();
        this.setTenantId(userInfo.getTenantId());
        this.setDeleted(0);
        this.setCreateBy(userInfo.getUserId());
        this.setCreateTime(System.currentTimeMillis());
        this.setUpdateBy(this.getCreateBy());
        this.setUpdateTime(this.getCreateTime());
    }

    public WaCustomizeShiftDefDo getById(Long batchId) {
        return waCustomizeShiftDefRepository.getById(batchId);
    }

    public void updateById(WaCustomizeShiftDefDo updateData) {
        waCustomizeShiftDefRepository.updateById(updateData);
    }

    public void save(WaCustomizeShiftDefDo addData) {
        waCustomizeShiftDefRepository.insert(addData);
    }

    public void deleteById(Long batchId) {
        waCustomizeShiftDefRepository.deleteById(batchId);
    }

    public List<WaCustomizeShiftDefDo> getList(CustomizeShiftDefQueryDto queryDto) {
        return waCustomizeShiftDefRepository.selectList(queryDto);
    }

    public List<WaCustomizeShiftDefDo> getList(String tenantId, String belongModule, Long startDate, Long endDate) {
        return waCustomizeShiftDefRepository.selectList(tenantId, belongModule, startDate, endDate);
    }

    public List<WaCustomizeShiftDefDo> getListByIds(List<Long> shiftDefIds) {
        return waCustomizeShiftDefRepository.selectListByIds(shiftDefIds);
    }

    public List<WaCustomizeShiftDefDo> getListByRelShiftIds(List<Integer> waShiftDefIds) {
        return waCustomizeShiftDefRepository.selectListByRelShiftIds(waShiftDefIds);
    }

    public WaCustomizeShiftDefDo getByWaShiftDefId(Integer waShiftDefId) {
        List<WaCustomizeShiftDefDo> list = getListByRelShiftIds(Lists.newArrayList(waShiftDefId));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }
}