package com.caidaocloud.attendance.service.application.dto.msg;

import lombok.Data;

/**
 * 日报消息字段：班次、签到时间、签退时间、考勤结果、出勤时长（对应系统：实出勤时长）
 */
@Data
public class WaDailyMsgDto {
    /**
     * 员工信息
     */
    private Long empid;
    /**
     * 日报汇总日期
     */
    private String summaryDate;

    /**
     * 班次名称
     */
    private String shiftName;

    /**
     * tenantId
     */
    private String tenantId;

    /**
     * 签到时间
     */
    private Long checkInTime;

    /**
     * 一次卡没有签退时间
     */
    private Long checkOutTime;

    /**
     * 出勤结果
     */
    private String checkResult;

    /**
     * 出勤时长
     */
    private String checkHours;

    /**
     * 延迟时间
     */
    private Integer delay;

    /**
     * 日报时间
     */
    private Long nowDate;

    /**
     * 休假
     */
    private String empLeave;

    /**
     * 出差
     */
    private String empTravel;

    /**
     * 补卡
     */
    private String empFillClock;

    /**
     * 调班
     */
    private String empShiftChange;
}
