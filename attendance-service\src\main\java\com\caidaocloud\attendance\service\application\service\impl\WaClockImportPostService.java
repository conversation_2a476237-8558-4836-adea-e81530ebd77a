package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidaocloud.attendance.service.application.dto.clock.ClockRecordImportDto;
import com.caidaocloud.attendance.service.application.event.publish.WaClockRecordImportPublish;
import com.caidao1.ioc.mq.IWaMqPostProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ConditionalOnProperty(
    prefix = "caidaocloud.importpost",
    name = "wapost",
    havingValue = "true"
)
public class WaClockImportPostService implements IWaMqPostProcess {
    @Autowired
    private WaClockRecordImportPublish waClockRecordImportPublish;
    @Autowired
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Override
    public void syncRegisterRecordToMongo(Long corpId, String belongId) {
        log.info("syncRegisterRecordToMongo corpId={},belongId={}", corpId, belongId);

        if(null == corpId){
            SysCorpOrg sysCorpOrg = sysCorpOrgMapper.selectByPrimaryKey(Long.valueOf(belongId));
            if(null == sysCorpOrg || null == sysCorpOrg.getCorpid()){
                return;
            }

            corpId = sysCorpOrg.getCorpid();
        }

        ClockRecordImportDto crid = new ClockRecordImportDto();
        crid.setBelongOrgId(belongId);
        crid.setCorpId(corpId);
        waClockRecordImportPublish.publish(JSON.toJSONString(crid));
    }
}
