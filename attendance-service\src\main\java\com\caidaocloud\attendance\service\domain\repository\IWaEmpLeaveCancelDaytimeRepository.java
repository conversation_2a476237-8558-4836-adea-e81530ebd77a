package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveCancelDaytimeDo;

import java.util.List;

public interface IWaEmpLeaveCancelDaytimeRepository {

    void saveBatch(List<WaEmpLeaveCancelDaytimeDo> list);

    List<WaEmpLeaveCancelDaytimeDo> getByLeaveCancelId(Long leaveCancelId);

    List<WaEmpLeaveCancelDaytimeDo> getByLeaveCancelIds(List<Long> leaveCancelIds);

    List<WaEmpLeaveCancelDaytimeDo> getLeaveCancelDaytimeList(String tenantId, Long startTime, Long endTime);
}
