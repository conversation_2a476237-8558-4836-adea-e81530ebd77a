package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.AttEmpGroupDo;
import com.caidaocloud.attendance.service.interfaces.dto.common.VerifyResult;
import com.caidaocloud.attendance.service.interfaces.dto.group.*;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;

import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON>.Chen
 * @Date: 2021/7/9 10:48
 * @Description:
 **/
public interface IGroupService {
    Result<Boolean> saveGroup(GroupInfoDto dto);

    Result<Boolean> saveGroupInfo(PlanGroupInfoDto dto);

    AttendancePageResult<GroupDto> getGroupList(PageBean pageBean);

    GroupDetailDto getGroupInfo(Integer id) throws Exception;

    void deleteGroup(Integer id);

    List<Map> selectGroupOptions();

    List<VerifyResult> verifySelectedEmployees(Integer planId, List<Long> empIds);

    List<Map> getEmpGroupList(Integer planId, PageBean pageBean);

    void updateGroupLeaveType(Integer groupId, Integer leaveTypeId, String leaveTypeName);

    void updateGroupOvertimeType(Integer groupId, Integer otTypeId, Integer dateType);

    void deleteGroupLeaveType(Integer groupId, Integer leaveTypeId);

    void deleteGroupOtType(Integer groupId, Integer otTypeId);

    void saveGroupBaseInfo(GroupBaseInfoDto dto);

    void updateGroupParseGroupId(Integer groupId, Integer parseGroupId);

    AttendancePageResult<AttEmpGroupDto> getWaEmpGroupList(AttEmpGroupReqDto dto, UserInfo userInfo);

    AttEmpGroupDto getWaEmpGroupById(Integer id);

    void deleteWaEmpGroup(Integer id);

    Result<Boolean> saveWaEmpGroup(AttEmpGroupDto dto);

    void synchronizeEmpGroup(String belongOrgId, Long userId, Long corpId);

    void synchronizeEmpGroup();

    void updateWaGroupLeaveInfo(Integer waGroupId);

    void deleteWaEmpGroups(List<Integer> ids);

    /**
     * 获取员工指定时间内生效的考勤方案
     *
     * @param belongOrgId
     * @param empId
     * @param currentTime
     * @return
     */
    GroupDetailDto getEmpGroup(String belongOrgId, Long empId, Long currentTime);

    List<KeyValue> getConTractCompany(String tenantId);

    List<EmpGroupIdDto> getEmpGroupIdByEmpIds(String belongOrgId, List<Long> empIds, Long currentTime);

    List<AttEmpGroupDo> getEmpGroupIdByEmpIds(String tenantId, List<Long> empIds, Long startDate, Long endDate);
}
