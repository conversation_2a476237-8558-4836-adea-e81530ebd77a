package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.system.mybatis.mapper.SysConfigMapper;
import com.caidao1.system.mybatis.model.SysConfig;
import com.caidao1.system.mybatis.model.SysConfigExample;
import com.caidaocloud.attendance.service.application.dto.AdjustShiftConfigurationDto;
import com.caidaocloud.attendance.service.application.dto.SysConfigurationDto;
import com.caidaocloud.attendance.service.application.service.IConfigService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.ErrorCollectMapper;
import com.caidaocloud.attendance.service.interfaces.dto.common.ErrorCollectDto;
import com.caidaocloud.attendance.service.interfaces.dto.config.SysConfigReqDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.SysConfigsEnum;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: Aaron.Chen
 * @Date: 2022/3/21 9:43
 * @Description:
 **/
@Slf4j
@Service
public class ConfigServiceImpl implements IConfigService {

    @Resource
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private ISessionService sessionService;
    @Resource
    private ErrorCollectMapper errorCollectMapper;


    @Override
    public void updateConfig(SysConfigReqDto dto) {
        UserInfo userInfo = sessionService.getUserInfo();
        SysConfig sysConfig = new SysConfig();
        sysConfig.setUpdtime(System.currentTimeMillis() / 1000L);
        sysConfig.setUpduser(userInfo.getUserid());
        sysConfig.setConfigId(dto.getConfigId());
        sysConfig.setStatus(dto.getStatus());
        sysConfigMapper.updateByPrimaryKeySelective(sysConfig);
    }

    public void saveConfig(SysConfigsEnum configsEnum) {
        UserInfo userInfo = sessionService.getUserInfo();
        String tenantId = userInfo.getTenantId();
        SysConfig sysConfig = new SysConfig();
        sysConfig.setUpdtime(System.currentTimeMillis() / 1000L);
        sysConfig.setUpduser(userInfo.getUserid());
        sysConfig.setBelongOrgId(tenantId);
        sysConfig.setName(configsEnum.getDesc());
        sysConfig.setConfigCode(configsEnum.name());
        sysConfig.setStatus(configsEnum.getDefValue());
        sysConfig.setCrttime(System.currentTimeMillis() / 1000L);
        sysConfig.setCrtuser(userInfo.getUserid());
        sysConfigMapper.insertSelective(sysConfig);
    }

    @Override
    public AdjustShiftConfigurationDto getConfiguration(String configCode) {
        UserInfo userInfo = getUserInfo();
        String tenantId = userInfo.getTenantId();
        SysConfigExample sysConfigExample = new SysConfigExample();
        SysConfigExample.Criteria criteria = sysConfigExample.createCriteria();
        criteria.andBelongOrgIdEqualTo(tenantId).andConfigCodeEqualTo(configCode);
        List<SysConfig> list = sysConfigMapper.selectByExample(sysConfigExample);
        return this.getSysConfigurationDto(list);
    }

    private AdjustShiftConfigurationDto getSysConfigurationDto(List<SysConfig> list) {
        Optional<SysConfig> opt = list.stream().findFirst();
        if (opt.isPresent()) {
            SysConfig sysConfig = opt.get();
            return ObjectConverter.convert(sysConfig, AdjustShiftConfigurationDto.class);
        }
        return null;
    }

    @Override
    public List<SysConfigurationDto> getSysConfigs() {
        UserInfo userInfo = getUserInfo();
        String tenantId = userInfo.getTenantId();
        SysConfigExample sysConfigExample = new SysConfigExample();
        SysConfigExample.Criteria criteria = sysConfigExample.createCriteria();
        criteria.andBelongOrgIdEqualTo(tenantId).andConfigCodeIn(SysConfigsEnum.getNames());
        List<SysConfig> list = sysConfigMapper.selectByExample(sysConfigExample);
        if (CollectionUtils.isEmpty(list) || list.size() < SysConfigsEnum.getNames().size()) {
            List<String> names = list.stream().map(SysConfig::getConfigCode).collect(Collectors.toList());
            for (SysConfigsEnum conf : SysConfigsEnum.values()) {
                if (!names.contains(conf.name())) {
                    this.saveConfig(conf);
                }
            }
            list = sysConfigMapper.selectByExample(sysConfigExample);
        }
        return ObjectConverter.convertList(list, SysConfigurationDto.class);
    }

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Override
    public boolean checkSwitchStatus(String configCode) {
        AdjustShiftConfigurationDto dto = this.getConfiguration(configCode);
        return dto != null && dto.getStatus() == 1;
    }

    @Override
    public void collectError(ErrorCollectDto collect) {
        errorCollectMapper.insert(collect.getAppId(), collect.getAppName(),
                collect.getAppVersion(),
                collect.getDeviceId(), collect.getDeviceUa(), collect.getType(),
                collect.getDetail(), collect.getTime(), sessionService.getTenantId(),
                sessionService.getUserInfo().getUserId());
    }
}
