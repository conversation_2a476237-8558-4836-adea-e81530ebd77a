package com.caidao1.workflow.service;

import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.mobile.service.MobileV16Service;
import com.caidao1.wa.mybatis.mapper.EmpTravelMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpShiftApplyRecordMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpShiftChangeMapper;
import com.caidao1.wa.mybatis.model.WaEmpShiftChange;
import com.caidao1.wa.mybatis.model.WaEmpShiftChangeExample;
import com.caidao1.wa.mybatis.model.WaShiftApplyRecord;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.service.WaRegisterRecordService;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 请假／加班 工作流审批
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Deprecated
public class WorkFlowApprovalService implements /*RemoteWorkFlowApprovalService,*/ ScriptBindable {
    /*@Autowired
    private MobileV16Service mobileV16Service;
    @Autowired
    private WaRegisterRecordService waRegisterRecordService;
    @Autowired
    private EmpTravelMapper empTravelMapper;
    @Autowired
    private WaEmpShiftApplyRecordMapper waEmpShiftApplyRecordMapper;
    @Autowired
    private WaEmpShiftChangeMapper waEmpShiftChangeMapper;
    @Autowired
    private WaCommonService waCommonService;

    *//**
     * 出差申请审批走工作流
     *
     * @param businessKey 请假单Id
     * @return
     *//*
    public boolean saveTravelApprovalWorkFlow(Long businessKey, WfCallbackTriggerOperationEnum operationEnum) throws Exception {
        if (operationEnum == WfCallbackTriggerOperationEnum.REVOKE) {
            empTravelMapper.updateWaEmpTravelStatus(businessKey, ApprovalStatusEnum.REVOKED.getIndex(), System.currentTimeMillis() / 1000L);
            log.info("saveTravelApprovalWorkFlow.revoke");
            return true;
        }
        Integer status = null;
        if (operationEnum == WfCallbackTriggerOperationEnum.APPROVED) {
            log.info("operationEnum is APPROVED ~~");
            status = 2;
        } else if (operationEnum == WfCallbackTriggerOperationEnum.REFUSED) {
            log.info("operationEnum is REFUSED ~~");
            status = 3;
        }

        empTravelMapper.updateWaEmpTravelStatus(businessKey, status, System.currentTimeMillis() / 1000L);

        log.info("saveTravelApprovalWorkFlow.over");
        return true;
    }


    *//**
     * 调班申请审批走工作流
     *
     * @param businessKey 请假单Id
     * @return
     *//*
    public boolean saveShiftApprovalWorkFlow(Long businessKey, WfCallbackTriggerOperationEnum operationEnum) throws Exception {
        log.info("start saveShiftApprovalWorkFlow ~~");
        if (operationEnum == WfCallbackTriggerOperationEnum.REVOKE) {
            waEmpShiftApplyRecordMapper.updateShiftApplyStatus(businessKey, ApprovalStatusEnum.CANCELLATION.getIndex(), System.currentTimeMillis() / 1000L);
            log.info("saveShiftApprovalWorkFlow.revoke");
            return true;
        }
        Integer status = null;
        if (operationEnum == WfCallbackTriggerOperationEnum.APPROVED) {
            log.info("operationEnum is APPROVED~~");
            status = 2;
            updateEmpWorkCalendarShift(businessKey);
        } else if (operationEnum == WfCallbackTriggerOperationEnum.REFUSED) {
            log.info("operationEnum is REFUSED~~");
            status = 3;
        }

        waEmpShiftApplyRecordMapper.updateShiftApplyStatus(businessKey, status, System.currentTimeMillis() / 1000L);

        log.info("saveShiftApprovalWorkFlow.over~~");
        return true;
    }

    *//**
     * 调班审批通过后更新班次，并插入一条调整明细
     *
     * @param businessKey
     *//*
    public void updateEmpWorkCalendarShift(Long businessKey) {
        WaShiftApplyRecord waShiftApplyRecord = waEmpShiftApplyRecordMapper.selectByPrimaryKey(businessKey);
        if (waShiftApplyRecord != null) {
            Long workDate = waShiftApplyRecord.getWorkDate();
            Long empId = waShiftApplyRecord.getEmpId();
            String tenantId = waShiftApplyRecord.getTenantId();
            Integer newShiftDefId = waShiftApplyRecord.getNewShiftDefId();
            String reason = waShiftApplyRecord.getReason();
            Long createBy = waShiftApplyRecord.getCreateBy();
            Long updateBy = waShiftApplyRecord.getUpdateBy();
            WaEmpShiftChangeExample empShiftChangeExample = new WaEmpShiftChangeExample();
            empShiftChangeExample.createCriteria().andBelongOrgIdEqualTo(tenantId).andEmpidEqualTo(empId).
                    andWorkDateEqualTo(workDate).andStatusEqualTo(2).andWorkDateEqualTo(workDate);
            //查询老的排班数据
            Integer oldShiftId = null;
            List<WaEmpShiftChange> empShiftChangeList = waEmpShiftChangeMapper.selectByExample(empShiftChangeExample);
            if (CollectionUtils.isNotEmpty(empShiftChangeList)) {
                oldShiftId = empShiftChangeList.get(0).getNewShiftDefId();
            } else {
                Map<Long, WaShiftDef> shiftDefMap = waCommonService.getEmpWorkShift(tenantId, empId, null, workDate, workDate);
                if (MapUtils.isNotEmpty(shiftDefMap)) {
                    oldShiftId = shiftDefMap.get(workDate).getShiftDefId();
                }
            }
            //将之前做的调整失效
            WaEmpShiftChange shiftChange = new WaEmpShiftChange();
            shiftChange.setStatus(4);
            shiftChange.setUpdtime(DateUtil.getCurrentTime(true));
            shiftChange.setUpduser(createBy);
            waEmpShiftChangeMapper.updateByExampleSelective(shiftChange, empShiftChangeExample);
            //添加新的班次记录
            WaEmpShiftChange waEmpShiftChange = new WaEmpShiftChange();
            waEmpShiftChange.setBelongOrgId(tenantId);
            waEmpShiftChange.setStatus(2);
            waEmpShiftChange.setCrttime(DateUtil.getCurrentTime(true));
            waEmpShiftChange.setCrtuser(createBy);
            waEmpShiftChange.setUpduser(createBy);
            waEmpShiftChange.setUpdtime(DateUtil.getCurrentTime(true));
            waEmpShiftChange.setEmpid(empId);
            waEmpShiftChange.setNewShiftDefId(newShiftDefId);
            waEmpShiftChange.setOldShiftDefId(oldShiftId);
            waEmpShiftChange.setWorkDate(workDate);
            waEmpShiftChange.setRemark(reason);
            waEmpShiftChangeMapper.insertSelective(waEmpShiftChange);
        }

    }

    *//**
     * 加班审批走工作流
     *
     * @param businessKey 加班单Id
     * @param choice      yes:审批通过,no拒绝
     * @param comment     审批意见
     * @return
     *//*
    public boolean saveOverTimeApprovalWorkFlow(Integer businessKey, String choice, String comment) throws Exception {
        log.info("saveOverTimeApprovalWorkFlow," + businessKey + choice);
        //TODO 加班审批走工作流 Tony
        Integer overtimeId = businessKey;
        // 审批通过拒绝标识  choice yes no
        // comment  审批通过或拒绝意见
        mobileV16Service.saveWfOtApproval(businessKey, choice, comment);
        //成功需要返回true
        return true;
    }

    *//**
     * 预加班审批走工作流
     *
     * @param businessKey 加班单Id
     * @param choice      yes:审批通过,no拒绝
     * @param comment     审批意见
     * @return
     *//*
    public boolean savePreOverTimeApprovalWorkFlow(Integer businessKey, String choice, String comment) throws Exception {
        mobileV16Service.savePreOverTimeApprovalWorkFlow(businessKey, choice, comment);
        return true;
    }

    *//**
     * 请假审批走工作流
     *
     * @param businessKey 请假单Id
     * @param choice      yes:审批通过,no拒绝
     * @param comment     审批意见
     * @return
     *//*
    public boolean saveLeaveApprovalWorkFlow(Integer businessKey, String choice, String comment) throws Exception {
        log.info("saveLeaveApprovalWorkFlow," + businessKey + "," + choice);
        //TODO 请假审批走工作流 Tony
        Integer leaveId = businessKey;
        // 审批通过拒绝标识  choice yes no
        // comment  审批通过或拒绝意见
        mobileV16Service.saveWfLeaveApproval(businessKey, choice, comment);
        //成功需要返回true
        return true;
    }

    @Override
    public boolean saveEmployeeApprovalWorkFlow(Integer integer, String s, String s1) throws Exception {
        return false;
    }

    @Override
    public boolean savePositiveApproval(Integer integer, String s, String s1) throws Exception {
        return false;
    }

    @Override
    public boolean saveContractChangeApproval(Integer integer, String s, String s1) throws Exception {
        return false;
    }

    @Override
    public boolean savePostChangeApproval(Integer integer, String s, String s1) throws Exception {
        return false;
    }

    @Override
    public boolean finishPayRollChangeApproval(Integer integer, String s, String s1) throws Exception {
        return false;
    }

    @Override
    public boolean finishWorkTimeChangeApproval(Integer integer, String s, String s1) throws Exception {
        return false;
    }

    @Override
    public boolean demissionApproval(Integer integer, String s, String s1) throws Exception {
        return false;
    }

    *//**
     * 销假申请审批走工作流
     *
     * @param businessKey 销假单id
     * @return
     * @throws Exception
     *//*
    public boolean saveLeaveCancelApprovalWorkFlow(Long businessKey, WfCallbackTriggerOperationEnum operationEnum) throws Exception {
        log.info("saveLeaveCancelApprovalWorkFlow," + businessKey + "," + operationEnum);
//        mobileV16Service.saveWfLeaveCancelApproval(businessKey, operationEnum);
        return true;
    }


    *//**
     * 薪资审批
     *
     * @param businessKey
     * @param choice
     * @return
     *//*
    public boolean finishPayrollApproval(Integer businessKey, String choice) throws Exception {
        // comment  审批通过或拒绝意见
//        payConfigService.finishPayrollApproval(businessKey, choice);
        //成功需要返回true
        return true;
    }

    @Override
    public boolean finishPreRecruitApproval(Integer integer, String s) throws Exception {
        return false;
    }

    @Override
    public boolean finishContractSign(Integer integer, String s) throws Exception {
        return false;
    }


    *//**
     * 表单审批
     *
     * @param businessKey
     * @param choice
     * @return
     * @throws Exception
     *//*
    public boolean finishFormApproval(Integer businessKey, String choice) throws Exception {
//        uiService.updateEmpDataStatus(businessKey, WorkflowUtils.approveStatus(choice));
        //成功需要返回true
        return true;
    }

    *//**
     * 异动表单审批
     *
     * @param businessKey
     * @param choice
     * @return
     * @throws Exception
     *//*
    public boolean finishEmpChangeFormApproval(Integer businessKey, String choice) throws Exception {
//        uiService.updateEmpChangeData(businessKey, choice);
        //成功需要返回true
        return true;
    }

    @Override
    public boolean finishHroRecruitApproval(Integer integer, String s) throws Exception {
        return false;
    }

    @Override
    public boolean finishRegisterExpApproval(Integer integer, String s) throws Exception {
        return false;
    }

    *//**
     * 杰尼亚-简单智表-部门岗位异动工作流定制回掉
     *
     * @param businessKey
     * @param choice
     * @return
     * @throws Exception
     *//*
    public boolean finishEmpChangeFormApprovalByEmpOrgAndPost(Integer businessKey, String choice) throws Exception {
//        uiService.updateEmpChangeData(businessKey, choice);
        return true;
    }


    *//**
     * 补打卡审批完后的回调
     *
     * @param businessKey
     * @param choice
     * @return
     * @throws Exception
     *//*
    public boolean finishBdkApproval(Integer businessKey, String choice) throws Exception {
        waRegisterRecordService.finishBdkApproval(businessKey, choice);
        return true;
    }

    *//**
     * 考勤结果调整审批完后的回调
     *
     * @param businessKey
     * @param choice
     * @return
     * @throws Exception
     *//*
    public boolean finishWaResultConfirmDetailApproval(Integer businessKey, String choice) throws Exception {
        waRegisterRecordService.finishWaResultConfirmDetailApproval(businessKey, choice);
        return true;
    }*/

//    /**
//     * 撤销请假单据审批完后的回调
//     *
//     * @param businessKey
//     * @param choice
//     * @return
//     * @throws Exception
//     */
//    public boolean finishRevokeEmpLeaveApproval(Integer businessKey, String choice) throws Exception {
//        waLeaveService.finishRevokeEmpLeaveApproval(businessKey, choice);
//        return true;
//    }

}
