package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

import java.util.Objects;

public enum NotifyConfigTypeEnum {
    CLOCK(1, "上下班打卡提醒", AttendanceCodes.CLOCK),
    DAILY_REPORT(2, "考勤日报提醒", AttendanceCodes.DAILY_REPORT),
    WEEKLY_REPORT(3, "考勤周报提醒", AttendanceCodes.WEEKLY_REPORT),
    LEAVE_CANCEL(4, "销假提醒", AttendanceCodes.LEAVE_CANCEL),
    ABNORMAL_SUMMARY(5, "考勤异常汇总提醒", AttendanceCodes.ABNORMAL_SUMMARY),
    ATTENDANCE_DETAIL_PUSH(8, "考勤明细推送", AttendanceCodes.ATTENDANCE_DETAIL_PUSH);

    private Integer index;
    private Integer code;
    private String desc;

    NotifyConfigTypeEnum(Integer index, String desc, Integer code) {
        this.index = index;
        this.desc = desc;
        this.code = code;
    }

    public static NotifyConfigTypeEnum getNotifyConfigTypeByIndex(Integer index) {
        for (NotifyConfigTypeEnum value : NotifyConfigTypeEnum.values()) {
            if (Objects.equals(value.getIndex(), index)) {
                return value;
            }
        }
        return null;
    }

    public static String getName(int index) {
        for (NotifyConfigTypeEnum c : NotifyConfigTypeEnum.values()) {
            if (c.getIndex() == index) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return c.desc;
            }
        }
        return "";
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}