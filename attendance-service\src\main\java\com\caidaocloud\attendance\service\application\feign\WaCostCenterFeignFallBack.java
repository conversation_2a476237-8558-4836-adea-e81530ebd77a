package com.caidaocloud.attendance.service.application.feign;

import com.caidaocloud.attendance.service.application.dto.emp.CostCenterDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class WaCostCenterFeignFallBack implements WaCostCenterFeignClient {
    @Override
    public Result<CostCenterDto> getDetail(String bid, Long dateTime) {
        return Result.fail();
    }
}
