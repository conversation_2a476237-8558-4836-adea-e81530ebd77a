package com.caidaocloud.attendance.service.application.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 考勤服务过渡配置类
 * 用于处理从启用DLQ到禁用DLQ的平滑过渡
 */
@Slf4j
@Configuration
public class AttendanceTransitionConfig {

    @Autowired
    private AttendanceMqProperties mqProperties;

    @PostConstruct
    public void checkTransitionStatus() {
        log.info("=== 考勤消息队列创建状态检查 ===");
        log.info("配置值：attendance.mq.enableDLQ = {}", mqProperties.isEnableDLQ());

        if (!mqProperties.isEnableDLQ()) {
            log.info("【当前模式】：普通队列模式（无死信队列保护）");
            log.info("【将要创建的队列和交换机】：");
            log.info("CONFIG: 主交换机: attendance.clock.analyse.pc.fac.direct.exchange");
            log.info("CONFIG: 主队列: attendance.clock.analyse.multinode.queue（普通队列，无死信参数）");
            log.info("CONFIG: 主队列绑定");
            log.info("【不会创建的组件】：");
            log.info("DISABLED: 死信交换机: attendance.clock.analyse.multinode.dlq.exchange");
            log.info("DISABLED: 死信队列: attendance.clock.analyse.multinode.dlq");
            log.info("DISABLED: 死信队列绑定");
            log.info("DISABLED: 死信队列消费者");

            log.warn("WARN: 注意：失败消息将根据策略 '{}' 处理，可能会丢失！", mqProperties.getFailureStrategy());

            log.info("【如需启用死信队列保护】：");
            log.info("在 Nacos 配置中设置：attendance.mq.enableDLQ: true");
            log.info("然后重启应用即可创建完整的死信队列保护机制");

            // 如果存在现有队列冲突的处理建议
            log.info("【队列冲突处理】：");
            log.info("如果启动时报队列参数冲突错误，请：");
            log.info("1. 删除现有队列: attendance.clock.analyse.multinode.queue");
            log.info("2. 删除相关死信队列和交换机（如果存在）");
            log.info("3. 重启应用让新配置生效");

        } else {
            log.info("【当前模式】：死信队列保护模式");
            log.info("【将要创建的队列和交换机】：");
            log.info("CONFIG: 主交换机: attendance.clock.analyse.pc.fac.direct.exchange");
            log.info("CONFIG: 主队列: attendance.clock.analyse.multinode.queue（带死信队列参数）");
            log.info("CONFIG: 主队列绑定");
            log.info("CONFIG: 死信交换机: attendance.clock.analyse.multinode.dlq.exchange");
            log.info("CONFIG: 死信队列: attendance.clock.analyse.multinode.dlq");
            log.info("CONFIG: 死信队列绑定");
            log.info("CONFIG: 死信队列消费者");

            log.info("SUCCESS: 完整的死信队列保护机制已启用");
            log.info("失败消息将在重试 {} 次后进入死信队列，由死信队列消费者监控处理",
                    mqProperties.getMaxRetryCount());
        }

        log.info("================================");
    }
} 