package com.caidao1.integrate.util;

import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;

import java.io.*;

public class GzipUtil {

    /**
     * 解压tar.gz
     *
     * @param filepath
     * @throws IOException
     */
    public static boolean unCompressTarGz(String filepath) throws IOException {
        boolean success = true;
        File file = new File(filepath);
        String fileName = file.getName().substring(0, file.getName().lastIndexOf("."));
        String finalName = file.getParent() + File.separator + fileName;
        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(finalName));
             GzipCompressorInputStream gcis = new GzipCompressorInputStream(bis)) {
            byte[] buffer = new byte[1024];
            int read = -1;
            while ((read = gcis.read(buffer)) != -1) {
                bos.write(buffer, 0, read);
            }
        } catch (Exception e) {
            success = false;
            e.printStackTrace();
        }
        unCompressTar(finalName);
        return success;
    }

    /**
     * 解压tar
     *
     * @param finalName
     * @throws IOException
     */
    public static void unCompressTar(String finalName) throws IOException {
        File file = new File(finalName);
        String parentPath = file.getParent();
        try (TarArchiveInputStream tais = new TarArchiveInputStream(new FileInputStream(file))) {
            TarArchiveEntry tarEntry = null;
            while ((tarEntry = tais.getNextTarEntry()) != null) {
                String name = tarEntry.getName();
                File tarFile = new File(parentPath, name);
                if (!tarFile.getParentFile().exists()) {
                    tarFile.getParentFile().mkdirs();
                }

                if (tarEntry.isDirectory()) {
                    tarFile.mkdirs();
                } else {
                    try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(tarFile))) {
                        int read = -1;
                        byte[] buffer = new byte[1024];
                        while ((read = tais.read(buffer)) != -1) {
                            bos.write(buffer, 0, read);
                        }
                    }
                }
            }
        } finally {
            file.delete();
        }
    }

    public static void main(String[] args) {
        try {
            unCompressTarGz("/Users/<USER>/Downloads/20181229093318.tar.gz");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
