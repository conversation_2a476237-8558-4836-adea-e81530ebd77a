package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.domain.entity.WaSobDo;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/5/6 15:20
 * @Description:
 **/
public interface ISobService {

    boolean checkSobName(String belongOrgId, Integer sobId, String sobName, List<Integer> statusList);

    List<WaSobDo> getWaSobIdByDateRangeAndPeriodMonth(String belongOrgId, Long dateTime, List<Integer> periodMonths, Integer groupId);

    List<WaSobDo> getWaSobByEndDateAndTenantId(String tenantId, Long startDate, Long endDate);

    void autoGenerateAttendancePeriod();
}
