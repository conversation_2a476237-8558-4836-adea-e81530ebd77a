package com.caidaocloud.attendance.core.wa.vo;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.wa.enums.ShiftTimeBelongTypeBaseEnum;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiWorkTimeBaseDto;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.postgresql.util.PGobject;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class WaShiftDefVo {
    //wa_shift_def 表对应字段
    private Integer shiftDefId;

    private Integer dateType;

    private Boolean isNight;

    private String shiftDefName;

    private String shiftDefCode;

    private Integer startTime;

    private Integer endTime;

    private Boolean isNoonRest;

    private Integer noonRestStart;

    private Integer noonRestEnd;

    private Integer restTotalTime;

    private Integer workTotalTime;

    private Integer onDutyStartTime;

    private Integer onDutyEndTime;

    private Integer offDutyStartTime;

    private Integer offDutyEndTime;

    private Integer overtimeStartTime;

    private Integer overtimeEndTime;

    private String belongOrgid;

    private Long crtuser;

    private Long crttime;

    private Boolean isDefault;

    private Boolean isHalfdayTime;

    private Integer halfdayTime;

    private Boolean isFlexibleWork;

    private Integer flexibleOnDutyStartTime;

    private Integer flexibleOnDutyEndTime;

    private Integer flexibleOffDutyStartTime;

    private Integer flexibleOffDutyEndTime;

    private Integer flexibleWorkType;

    private Object restPeriods;

    private Object overtimeRestPeriods;

    private Boolean isAdjustWorkHour;

    private Object adjustWorkHourJson;

    private Boolean isSpecial;

    private Integer specialWorkTime;

    private Boolean isApplyOvertime;

    private Object multiCheckinTimes;

    private Object multiWorkTimes;

    private String restTimeDesc;

    private Long orgid;

    private Long effectStartTime;

    private Long effectEndTime;

    private Integer flexibleWorkRule;

    private BigDecimal flexibleWorkLate;

    private BigDecimal flexibleWorkEarly;

    private String flexibleOffWorkRule;

    private Integer flexibleShiftSwitch;

    private String clockTimeLimit;

    private String midwayClockTime;

    private Integer substituteShift;

    private String i18nShiftDefName;

    private Integer startTimeBelong;

    private Integer endTimeBelong;

    private Integer noonRestStartBelong;

    private Integer noonRestEndBelong;

    private Integer onDutyStartTimeBelong;

    private Integer onDutyEndTimeBelong;

    private Integer offDutyStartTimeBelong;

    private Integer offDutyEndTimeBelong;

    private Integer overtimeStartTimeBelong;

    private Integer overtimeEndTimeBelong;

    private Integer halfdayTimeBelong;

    private Integer halfdayType;

    private String multiOvertime;

    private String belongModule;

    private Boolean temporaryShift;

    //其他业务字段
    private String id;
    private Long corpid;
    private Long empid;
    private Long workDate;
    private Integer calendarDateType;
    /**
     * 班次集合，一天排多个班时会有多条数据
     */
    private List<WaShiftDefVo> shiftList;

    public List<WaShiftDefVo> doGetShiftList() {
        if (CollectionUtils.isNotEmpty(this.shiftList)) {
            this.shiftList.sort(Comparator.comparing(WaShiftDefVo::doGetRealStartTime));
            return this.shiftList;
        }
        return Lists.newArrayList(this);
    }

    public Integer doGetRealStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.startTimeBelong)) {
            return startTime + 1440;
        }
        return startTime;
    }

    public Integer doGetRealEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.endTimeBelong)) {
            return endTime + 1440;
        }
        return endTime;
    }

    public Integer doGetRealNoonRestStart() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.noonRestStartBelong)) {
            return noonRestStart + 1440;
        }
        return noonRestStart;
    }

    public Integer doGetRealNoonRestEnd() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.noonRestEndBelong)) {
            return noonRestEnd + 1440;
        }
        return noonRestEnd;
    }

    public Integer doGetRealOnDutyStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.onDutyStartTimeBelong)) {
            return onDutyStartTime + 1440;
        }
        return onDutyStartTime;
    }

    public Integer doGetRealOnDutyEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.onDutyEndTimeBelong)) {
            return onDutyEndTime + 1440;
        }
        return onDutyEndTime;
    }

    public Integer doGetRealOffDutyStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.offDutyStartTimeBelong)) {
            return offDutyStartTime + 1440;
        }
        return offDutyStartTime;
    }

    public Integer doGetRealOffDutyEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong)) {
            return offDutyEndTime + 1440;
        }
        return offDutyEndTime;
    }

    public Integer doGetRealOvertimeStartTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.overtimeStartTimeBelong)) {
            return overtimeStartTime + 1440;
        }
        return overtimeStartTime;
    }

    public Integer doGetRealOvertimeEndTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.overtimeEndTimeBelong)) {
            return overtimeEndTime + 1440;
        }
        return overtimeEndTime;
    }

    public Integer doGetRealHalfdayTime() {
        if (ShiftTimeBelongTypeBaseEnum.NEXT_DAY.getIndex().equals(this.halfdayTimeBelong)) {
            return halfdayTime + 1440;
        }
        return halfdayTime;
    }

    public boolean checkClockTimeCrossNight() {
        if (this.onDutyStartTime == null || this.offDutyEndTime == null) {
            return Boolean.FALSE;
        }
        if (this.onDutyStartTimeBelong != null && this.offDutyEndTimeBelong != null) {
            return ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(this.onDutyStartTimeBelong)
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong);
        } else {
            return this.offDutyEndTime <= this.onDutyStartTime;
        }
    }

    public boolean checkOffDutyTimeCrossNight() {
        if (this.offDutyEndTime == null || this.offDutyStartTime == null) {
            return Boolean.FALSE;
        }
        if (this.offDutyEndTimeBelong != null && this.offDutyStartTimeBelong != null) {
            return ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(this.offDutyStartTimeBelong)
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.offDutyEndTimeBelong);
        } else {
            return this.offDutyEndTime < this.offDutyStartTime;
        }
    }

    public static List<String> getShiftMultiWorkTimePeriodList(WaShiftDefVo shiftDo) {
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDo.getDateType())) {
            return Lists.newArrayList();
        }
        Long nowDate = DateUtil.getOnlyDate();
        if (null != shiftDo.getMultiWorkTimes()) {
            String multiWorkTimeJson = "";
            if (shiftDo.getMultiWorkTimes() instanceof PGobject) {
                PGobject pGobject = (PGobject) shiftDo.getMultiWorkTimes();
                multiWorkTimeJson = pGobject.getValue();
            } else {
                Map map = FastjsonUtil.convertObject(shiftDo.getMultiWorkTimes(), Map.class);
                multiWorkTimeJson = map.get("value").toString();
            }
            List<MultiWorkTimeBaseDto> multiWorkTimeDtoList = FastjsonUtil.toArrayList(multiWorkTimeJson, MultiWorkTimeBaseDto.class);
            multiWorkTimeDtoList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime));
            return multiWorkTimeDtoList.stream().map(timeIt -> {
                String start = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getStartTime() * 60), "HH:mm", true);
                String end = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getEndTime() * 60), "HH:mm", true);
                if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getStartTimeBelong())) {
                    start = ShiftTimeBelongTypeEnum.getName(timeIt.getStartTimeBelong()) + start;
                }
                if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getEndTimeBelong())) {
                    end = ShiftTimeBelongTypeEnum.getName(timeIt.getEndTimeBelong()) + end;
                }
                return String.format("%s-%s", start, end);
            }).collect(Collectors.toList());
        } else {
            String start = DateUtil.convertDateTimeToStr(nowDate + (shiftDo.getStartTime() * 60), "HH:mm", true);
            String end = DateUtil.convertDateTimeToStr(nowDate + (shiftDo.getEndTime() * 60), "HH:mm", true);
            if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shiftDo.getEndTimeBelong()) ||
                    CdWaShiftUtil.checkCrossNight(shiftDo.getStartTime(), shiftDo.getEndTime(), shiftDo.getDateType())) {
                end = ShiftTimeBelongTypeEnum.getName(ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex()) + end;
            }
            return Lists.newArrayList(String.format("%s-%s", start, end));
        }
    }
}
