package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.sdk.dto.SdkWorkflowCallBackHandleLeaveDto;
import com.caidaocloud.attendance.sdk.feign.IWorkflowCallBackFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SdkWorkflowCallBackService {
    @Autowired
    private IWorkflowCallBackFeignClient workflowCallBackFeignClient;

    /**
     * 撤销销假休假单处理
     *
     * @param workflowCallBackHandleLeaveDto
     * @return
     */
    public Result<?> handleLeave(SdkWorkflowCallBackHandleLeaveDto workflowCallBackHandleLeaveDto) {
        return workflowCallBackFeignClient.handleLeave(workflowCallBackHandleLeaveDto);
    }
}
