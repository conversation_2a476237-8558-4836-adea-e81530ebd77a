package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.wa.mybatis.model.WaAnalyze;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.domain.repository.IWaBatchAnalyseResultAdjustRepository;
import com.caidaocloud.dto.UserInfo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Data
@Slf4j
@Service
public class WaBatchAnalyseResultAdjustDo {
    private Long batchId;

    private Long empid;

    private Integer waSobId;

    private Long startDate;

    private Long endDate;

    private String filePath;

    private String fileName;

    private String businessKey;

    private String reason;

    private String extCustomCol;

    private Integer status;

    private Long lastApprovalTime;

    private Long lastEmpid;

    private String revokeReason;

    private Integer revokeStatus;

    private String tenantId;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    private String processCode;

    @Autowired
    private IWaBatchAnalyseResultAdjustRepository waBatchAnalyseResultAdjustRepository;

    public WaBatchAnalyseResultAdjustDo getById(Long batchId) {
        return waBatchAnalyseResultAdjustRepository.getById(batchId);
    }

    public void updateById(WaBatchAnalyseResultAdjustDo updateData) {
        waBatchAnalyseResultAdjustRepository.updateById(updateData);
    }

    public void save(WaBatchAnalyseResultAdjustDo addData) {
        waBatchAnalyseResultAdjustRepository.insert(addData);
    }

    public void deleteById(Long batchId) {
        waBatchAnalyseResultAdjustRepository.deleteById(batchId);
    }

    public List<WaAnalyze> getEmpWaAnalyzeList(String tenantId, Long empid, Long startDate, Long endDate, Boolean abnormal) {
        return waBatchAnalyseResultAdjustRepository.selectEmpWaAnalyzeList(tenantId, empid, startDate, endDate, abnormal);
    }

    public PageList<Map> getPageList(MyPageBounds pageBounds, Map params) {
        return waBatchAnalyseResultAdjustRepository.selectPageList(pageBounds, params);
    }

    public void doInitCrtField() {
        UserInfo userInfo = UserContext.preCheckUser();
        this.setTenantId(userInfo.getTenantId());
        this.setDeleted(0);
        this.setCreateBy(userInfo.getUserId());
        this.setCreateTime(System.currentTimeMillis());
        this.setUpdateBy(this.getCreateBy());
        this.setUpdateTime(this.getCreateTime());
    }
}