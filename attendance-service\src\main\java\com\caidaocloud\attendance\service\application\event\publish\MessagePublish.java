package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.attendance.service.application.dto.msg.NoticeMsgDTO;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class MessagePublish {
    @Resource
    private MqMessageProducer<RabbitBaseMessage> producer;
    private final static String EXCHANGE = "message.fac.direct.exchange";
    private final static String ROUTING_KEY = "routingKey.message.plain.msg";

    public void publishToMessageService(NoticeMsgDTO noticeMsgDTO) {
        if (noticeMsgDTO == null) {
            log.info("MessagePublish.publishToMessageService push to message center noticeMsgDTO is null");
            return;
        }
        String msg = FastjsonUtil.toJson(noticeMsgDTO);
        log.info("MessagePublish.publishToMessageService push to message center time:{},param:{}", System.currentTimeMillis(), msg);
        try {
            RabbitBaseMessage message = new RabbitBaseMessage();
            message.setBody(msg);
            message.setExchange(EXCHANGE);
            message.setRoutingKey(ROUTING_KEY);
            producer.publish(message);
        }catch (Exception e) {
            log.error("MessagePublish.publishToMessageService push to message center error!! reason:{}:", e.getMessage(), e);
            e.printStackTrace();
        }

    }
}
