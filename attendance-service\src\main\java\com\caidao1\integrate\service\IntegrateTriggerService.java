
package com.caidao1.integrate.service;

import com.alibaba.fastjson.JSONObject;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.service.StartupServiceImpl;
import com.caidao1.commons.utils.*;
import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.caidao1.ioc.dto.UpdRowDto;
import com.caidao1.ioc.mybatis.mapper.IocImportMapper;
import com.caidao1.ioc.service.ImportTriggerService;
import com.caidao1.pub.api.RemoteAsynSendMailService;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper2;
import com.caidao1.system.mybatis.mapper.SysEmailTemplateMapper;
import com.caidao1.system.mybatis.model.SysEmailTemplate;
import com.caidao1.wa.mybatis.mapper.WaShiftDefMapper;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidao1.wx.mybatis.mapper.WxCorpInfoMapper;
import com.caidao1.xss.test.cache.RedisService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.web.ResponseWrap;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import javax.validation.constraints.NotNull;
import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IntegrateTriggerService implements ScriptBindable {
    private static final Logger logger = LoggerFactory.getLogger(IntegrateTriggerService.class);
    @Autowired
    private WaShiftDefMapper shiftDefMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private SysCorpOrgMapper2 sysCorpOrgMapper2;
    @Autowired
    private HtmlMailUtil sendMailUtil;
    @Autowired
    private RemoteAsynSendMailService asynSendMailService;
    @Autowired
    private IocImportMapper iocImportMapper;
    @Autowired
    WxCorpInfoMapper wxCorpInfoMapper;
    @Autowired
    private SysEmailTemplateMapper sysEmailTemplateMapper;
    @Autowired
    private ImportTriggerService importTriggerService;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private StartupServiceImpl startupServiceImpl;

    /**
     * 发送邮件
     *
     * @param title
     * @param to
     * @param content
     * @param message
     * @throws Exception
     */
    public void sendEmail(String belongId, String title, String to, String content, String message) {
        try {
            sendMailUtil.setSubject(title);
            sendMailUtil.setTo(to);
            sendMailUtil.setContent(content.replaceAll("#message#", message));
            asynSendMailService.sendMail(sendMailUtil, belongId);
        } catch (Exception e) {
            logger.error("IntegrateTriggerService.sendEmail：异常，msg=" + e.getMessage());
        }
    }

    public SysEmailTemplate getEmailTemplate(String belongId, String typeCode) {
        Map map = new HashMap();
        map.put("belongOrgId", belongId);
        map.put("typeCode", typeCode);
        List<SysEmailTemplate> emailTemplateList = sysEmailTemplateMapper.getEmailTmp(map);
        if (CollectionUtils.isNotEmpty(emailTemplateList)) {
            return emailTemplateList.get(0);
        }
        return null;
    }

    public String getSendMailContent(Map params, String content) {
        if (StringUtils.isNotBlank(content)) {
            String accessResults = (String) params.get("accessResults");
            Integer totalNum = (Integer) params.get("totalNum");
            Integer successNum = (Integer) params.get("successNum");
            Integer failNum = (Integer) params.get("failNum");
            String excMsg = (String) params.get("excMsg");
            String failFileNames = (String) params.get("failFileNames");

            if (StringUtils.isNotBlank(accessResults)) {
                content = content.replace("#{accessResults}", accessResults);//接入结果状态
            }
            if (StringUtils.isNotBlank(failFileNames)) {
                content = content.replace("#{failFileNames}", failFileNames);//失败文件名称
            }
            if (StringUtils.isNotBlank(excMsg)) {
                content = content.replace("#{excMsg}", excMsg);//异常信息
            }
            if (totalNum != null) {
                content = content.replace("#{totalNum}", String.valueOf(totalNum));
            }
            if (successNum != null) {
                content = content.replace("#{successNum}", String.valueOf(successNum));//成功条数
            }
            if (failNum != null) {
                content = content.replace("#{failNum}", String.valueOf(failNum));//失败条数
            }
            content = content.replace("#{accessTime}", DateUtil.getCurTimeStamp());//接入时间
            return content;
        }
        return "";
    }


    public List<Map<String, Object>> covertStr2IntBeforeTrigger(String col, List<Map<String, Object>> sourceResult) throws Exception {
        for (Map<String, Object> row : sourceResult) {
            String val = (String) row.get(col);
            row.put(col, Integer.parseInt(val));
        }
        return sourceResult;
    }

    public List<Map<String, Object>> registerRecordBeforeTrigger(List<Map<String, Object>> sourceResult) throws Exception {
        List<Map<String, Object>> allList = new ArrayList<Map<String, Object>>();
        for (Map<String, Object> row : sourceResult) {
            String brushDate = (String) row.get("brush_date");
            row.put("belong_date", DateUtil.convertStringToDateTime(brushDate, "yyyy-MM-dd", true));

            String brushData = (String) row.get("brush_data");
            String[] timeList = brushData.split("\\s+");
            row.put("reg_date_time", DateUtil.convertStringToDateTime(brushDate + " " + timeList[0], "yyyy-MM-dd HH:mm", true));
            allList.add(row);
            if (timeList.length > 1) {
                Map<String, Object> newNow = new HashMap<String, Object>();
                newNow.putAll(row);
                row.put("reg_date_time", DateUtil.convertStringToDateTime(brushDate + " " + timeList[timeList.length - 1], "yyyy-MM-dd HH:mm", true));
                allList.add(newNow);
            }
        }
        return allList;
    }

    /**
     * 不存在/存在员工删除
     *
     * @param belongId
     * @param sourceResult
     * @param flag         true只保留修改 false只保留新增
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> deleteEmpBeforeTrigger(String belongId, List<Map<String, Object>> sourceResult, boolean flag) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        Jedis jedis = redisService.getResource();

        try {
            for (Map<String, Object> row : sourceResult) {
                String workno;
                if (row.containsKey("empid")) {
                    workno = (String) row.get("empid");
                } else {
                    workno = (String) row.get("workno");
                }
                String empInfo = jedis.get(BaseConst.EMP_ + belongId + "_" + workno);
                if (empInfo == null) {
                    if (!flag) {
                        result.add(row);
                    }
                } else {
                    if (flag) {
                        result.add(row);
                    }
                }
            }
        } finally {
            jedis.close();
        }
        return result;
    }

    /**
     * 诺士签到转加班
     *
     * @param sourceResult
     * @param plus         分钟
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> register2OtBeforeTrigger(String belongId, List<Map<String, Object>> sourceResult, Integer plus) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        Jedis jedis = RedisService.getResource();

        try {
            Map<Integer, WaShiftDef> empShift = new HashMap();
            for (Map<String, Object> row : sourceResult) {
                Long belong_date = (Long) row.get("belong_date");
                Long start_time = (Long) row.get("start_time");
                Long end_time = (Long) row.get("end_time");
                Integer shift_def_id = (Integer) row.get("shift_def_id");
                if (end_time > start_time) {
                    WaShiftDef shiftDef = null;
                    if (empShift.containsKey(shift_def_id)) {
                        shiftDef = empShift.get(shift_def_id);
                    } else {
                        shiftDef = shiftDefMapper.selectByPrimaryKey(shift_def_id);
                        empShift.put(shift_def_id, shiftDef);
                    }
                    if (shiftDef != null) {
                        //工作日
                        if (shiftDef.getDateType() == 1) {
                            //跨夜
                            Long endDate = belong_date;
                            if (CdWaShiftUtil.checkCrossNightV2(shiftDef, shiftDef.getDateType())) {
                                endDate = end_time - (end_time + 28800) % 86400;
                            }
                            if (end_time >= endDate + (shiftDef.getEndTime() + plus) * 60) {
                                row.put("start_time", endDate + shiftDef.getEndTime() * 60);
                                result.add(row);
                            }
                        } else {
                            result.add(row);
                        }
                    }
                }
            }
        } finally {
            jedis.close();
        }
        return result;
    }


    public void addErrorMsg(Map m, List<Map> checkList, String errorMsg) {
        Map checkRow = new HashMap();
        checkRow.putAll(m);
        checkRow.put("msg", errorMsg);
        checkList.add(checkRow);
    }


    /**
     * 数据港数据上传成功后需要在上传一个flag文件,
     * 文件格式是 文件名+ '.flag'
     * 文件内容是 文件名 大小
     */
    public void sftpSJGFlagUpload(Map mapJson, Map<String, Object> responseResult) throws Exception {
        System.out.println(mapJson.toString());
        SftpUtil client = new SftpUtil();
        client.setServer((String) mapJson.get("sftp.server"));
        client.setPort((Integer) mapJson.get("sftp.port"));
        client.setLogin((String) mapJson.get("sftp.login"));
        client.setPassword((String) mapJson.get("sftp.password"));
        client.connect();

        try {

            //本项目存储路径
            String resourcePath = (String) responseResult.get("resourcePath");

            String resourcePathFlag = (String) mapJson.get("resourcePath");
            //目标文件路径
            String targetFilePath = (String) mapJson.get("targetFilePath");

            String fileName = resourcePathFlag + ".flag";
            File f = new File(FileUtil.getWebRoot() + fileName);
            if (!f.getParentFile().exists()) {
                f.getParentFile().mkdirs();
            }
            BufferedWriter out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(FileUtil.getWebRoot() + fileName), "UTF-8"));

            if (StringUtils.isNotBlank(resourcePath) && StringUtils.isNotBlank(targetFilePath)) {
                Pattern pattern = Pattern.compile(":DATEFORMAT\\((.*)\\)", 1);
                Matcher matcher = pattern.matcher(targetFilePath);
                if (matcher.find()) {
                    SimpleDateFormat sd = new SimpleDateFormat(matcher.group(1));
                    targetFilePath = targetFilePath.replaceAll(":DATEFORMAT\\((.*)\\)", sd.format(new Date()));
                }
                String content = targetFilePath.substring(targetFilePath.lastIndexOf("/") + 1) + " " + (new File(resourcePath)).length();
                out.write(content, 0, content.length());
                out.flush();
                out.close();
                client.uploadFile(FileUtil.getWebRoot() + fileName, targetFilePath + ".flag");
            }

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            client.disconnect();
        }
    }

    public void updateEmpLeave(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) throws Exception {
        importTriggerService.updateEmpLeave(belongId, addList, updList);
    }

    /**
     *
     */
    public void updateRegisterRecord(String belongId, Map<String, List<UpdRowDto>> addList, Map<String, List<UpdRowDto>> updList) throws Exception {
        importTriggerService.updateRegisterRecord(belongId, addList, updList);

        List<UpdRowDto> valueAddList = addList.get("wa_register_record");
        List<UpdRowDto> valueUpdList = updList.get("wa_register_record");
        List<UpdRowDto> allList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(valueAddList)) {
            allList.addAll(valueAddList);
        }
        if (CollectionUtils.isNotEmpty(valueUpdList)) {
            allList.addAll(valueUpdList);
        }
        // 如果不为空 则进行数据分析
        if (CollectionUtils.isNotEmpty(allList)) {

        }
    }

    public Long getEmpIdByWorkNo(String workno, String belongId) {
        Jedis jedis = RedisService.getResource();
        try {
            String empInfo = jedis.get(BaseConst.EMP_ + belongId + "_" + workno);
            if (empInfo == null) {
            } else {
                return Long.parseLong(empInfo.split(",")[0]);
            }
        } finally {
            jedis.close();
        }
        return null;
    }

    /**
     * 进行数据字段对应关系的转换
     *
     * @param corpId
     * @param belongId
     * @param dataInput
     * @param resource
     * @param fkInfoMap
     * @return
     */
    @Transactional
    public List<Map> dataMappingTransform(Long corpId, String belongId, SysDataInput dataInput, List<Map> resource, @NotNull Map<String, List<Map>> fkInfoMap) {
        return dataMappingTransform(corpId, belongId, dataInput, resource, fkInfoMap, true);
    }

    /**
     * 进行数据字段对应关系的转换
     *
     * @param corpId
     * @param belongId
     * @param dataInput
     * @param resource
     * @param fkInfoMap
     * @param useDataInput
     * @return
     */
    @Transactional
    public List<Map> dataMappingTransform(Long corpId, String belongId, SysDataInput dataInput, List<Map> resource, @NotNull Map<String, List<Map>> fkInfoMap, boolean useDataInput) {
        List<Map> returnList = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(dataInput.getSourceExp()) && CollectionUtils.isNotEmpty(resource)) {
            logger.info("开始进行数据转换，数据总条数[{}]", resource.size());
            int size = 0;
            //进行数据转换处理
            try {
                List<Map<String, Object>> list = new ObjectMapper().readValue(dataInput.getSourceExp(), List.class);
                for (Map tabConfig : list) {
                    String tabName = (String) tabConfig.get("tabName");
                    List<Map> result = new ArrayList<>();
                    List<Map> fkInfoList = new ArrayList<>();
                    fkInfoMap.put(tabName, fkInfoList);
                    String condition = (String) tabConfig.get("condition");
                    List<Map<String, String>> uniques = (List<Map<String, String>>) tabConfig.get("uniqueKeys");
                    List<Map<String, Object>> cols = (List<Map<String, Object>>) tabConfig.get("columns");
                    List<String> orderFields = (List<String>) tabConfig.get("orderFields");
                    boolean useBelongId = Boolean.parseBoolean(tabConfig.getOrDefault("useBelongId", "true").toString());
                    for (Map data : resource) {
                        if (StringUtil.isNullOrEmpty(condition) || groovyScriptEngine.executeBoolean(replaceVariables(condition, data), new HashMap<>())) {
                            data.put("@corpId", corpId);
                            data.put("@belongId", belongId);
                            Map<String, Object> fkMap = new HashMap<>();
                            Map newData = columnNameAndValueTransform(tabName, uniques, fkMap, cols, data, useBelongId);
                            if (newData != null && !newData.isEmpty()) {
                                //进行数据合并操作
                                mergeInputData(result, newData, fkInfoList, fkMap, uniques, orderFields);
                            }
                        }
                    }
                    //进行自定义字段处理
                    List<String> rmKeys = extractExtColumn(result);
                    size += result.size();
                    Map tabMap = new HashMap();
                    tabMap.put("tabName", tabName);
                    tabMap.put("uniqueKeys", uniques);
                    tabMap.put("columns", cols);
                    tabMap.put("rows", result);
                    tabMap.put("useBelongId", useBelongId);
                    tabMap.put("rmKeys", rmKeys);
                    if (useDataInput) {
                        logger.info("开始接入表[{}]的数据，共[{}]条数据", tabName, result.size());
                        doDataInput(corpId, belongId, tabMap);
                        logger.info("接入表[{}]的数据完成，共[{}]条数据", tabName, result.size());
                    }
                    returnList.add(tabMap);
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            logger.info("转换前数据总条数[{}]，转换后数据总条数[{}]", resource.size(), size);
        }
        return returnList;
    }

    private void doDataInput(Long corpId, String belongId, @NotNull Map tabMap) {
        List<String> insertSqls = new ArrayList<>();
        List<String> updateSqls = new ArrayList<>();
        String tabName = (String) tabMap.get("tabName");
        List<Map<String, Object>> rows = (List<Map<String, Object>>) tabMap.get("rows");
        List<Map<String, Object>> cols = (List<Map<String, Object>>) tabMap.get("columns");
        List<Map<String, String>> uniques = (List<Map<String, String>>) tabMap.get("uniqueKeys");
        List<String> rmKeys = (List<String>) tabMap.get("rmKeys");
        boolean useBelongId = (boolean) tabMap.get("useBelongId");
        for (Map<String, Object> row : rows) {
            StringBuilder validateExistSql = new StringBuilder();
            validateExistSql.append("SELECT count(*) FROM ").append(tabName);
            StringBuilder uniqueBuilder = new StringBuilder();
            uniqueBuilder.append(" WHERE ");
            if (useBelongId) {
                uniqueBuilder.append("belong_org_id = ").append(belongId);
            }
            for (Map<String, String> uniqueMap : uniques) {
                if (!uniqueBuilder.toString().trim().endsWith("WHERE")) {
                    uniqueBuilder.append(" AND ");
                }
                String name = uniqueMap.get("name");
                uniqueBuilder.append(getDbColName(name)).append(" = ").append(getSimpleSqlValue(row.get(uniqueMap.get("name")), uniqueMap.get("type")));
            }
            Object count = iocImportMapper.queryFKBySql(validateExistSql.append(uniqueBuilder).toString());
            StringBuilder exeSql = new StringBuilder();
            if (ConvertHelper.intConvert(count) > 0) {
                exeSql.append("UPDATE ").append(tabName).append(" SET");
                for (String colName : row.keySet()) {
                    if (rmKeys.contains(colName)) {
                        continue;
                    }
                    exeSql.append(" ").append(colName).append(" = ").append(getSimpleSqlValue(row.get(colName), (String) getColInfo(colName, cols).get("type"))).append(",");
                }
                exeSql.deleteCharAt(exeSql.length() - 1).append(uniqueBuilder);
                updateSqls.add(exeSql.toString());
            } else {
                StringBuilder valBuilder = new StringBuilder();
                exeSql.append("INSERT INTO ").append(tabName).append("(");
                if (useBelongId) {
                    exeSql.append("belong_org_id,");
                    valBuilder.append(belongId).append(",");
                }
                for (String colName : row.keySet()) {
                    if (rmKeys.contains(colName)) {
                        continue;
                    }
                    exeSql.append(colName).append(",");
                    valBuilder.append(getSimpleSqlValue(row.get(colName), (String) getColInfo(colName, cols).get("type"))).append(",");
                }
                exeSql.deleteCharAt(exeSql.length() - 1).append(") SELECT ").append(valBuilder.deleteCharAt(valBuilder.length() - 1))
                        .append(" WHERE NOT EXISTS(SELECT 1 FROM ").append(tabName).append(uniqueBuilder).append(")");
                insertSqls.add(exeSql.toString());
            }
        }
        insertBysqls(insertSqls);
        updateBysqls(updateSqls);
    }

    private Map<String, Object> getColInfo(String colName, List<Map<String, Object>> cols) {
        for (Map map : cols) {
            if (colName.equals(map.get("name"))) {
                return map;
            }
        }
        return new HashMap<>();
    }

    private List<String> extractExtColumn(List<Map> result) {
        List<String> rmKeys = new ArrayList();
        for (int i = 0; i < result.size(); i++) {
            Map map = result.get(i);
            Map<String, Map<String, Object>> extMap = new HashMap<>();
            for (Object key : map.keySet()) {
                if (((String) key).startsWith("EXT_COL_")) {
                    if (!extMap.containsKey("ext_custom_col")) {
                        extMap.put("ext_custom_col", new HashMap<>());
                    }
                    extMap.get("ext_custom_col").put((String) key, map.get(key));
                    if (i == 0) {
                        rmKeys.add((String) key);
                    }
                } else if (((String) key).contains(".")) {
                    String[] ss = ((String) key).split("\\.");
                    if (!extMap.containsKey(ss[0])) {
                        extMap.put(ss[0], new HashMap<>());
                    }
                    extMap.get(ss[0]).put(ss[1], map.get(key));
                    if (i == 0) {
                        rmKeys.add((String) key);
                    }
                }
            }
            if (extMap != null && !extMap.isEmpty()) {
                try {
                    for (Map.Entry entry : extMap.entrySet()) {
                        map.put(entry.getKey(), JacksonJsonUtil.beanToJson(entry.getValue()));
                    }
                } catch (JsonProcessingException e) {
                    throw new RuntimeException("自定义字段数据转换JSON异常");
                }
            }
        }
        return rmKeys;
    }

    private void mergeInputData(List<Map> result, Map newData, List<Map> fkInfoList, Map<String, Object> fkMap, List<Map<String, String>> uniques, List<String> orderFields) {
        if (newData != null && !newData.isEmpty()) {
            if (CollectionUtils.isNotEmpty(result) && CollectionUtils.isNotEmpty(uniques)) {
                Map uniqueVal = null;
                for (Map map : result) {
                    boolean flag = false;
                    for (Map ukMap : uniques) {
                        if (newData.get(ukMap.get("name")).equals(map.get(ukMap.get("name")))) {
                            flag = true;
                        } else {
                            flag = false;
                            break;
                        }
                    }
                    if (flag) {
                        uniqueVal = map;
                        break;
                    }
                }
                if (uniqueVal != null) {
                    if (CollectionUtils.isNotEmpty(orderFields)) {
                        for (String name : orderFields) {
                            if (Long.valueOf(uniqueVal.getOrDefault(name, "0").toString()) < Long.valueOf(newData.getOrDefault(name, "0").toString())) {
                                for (Object key : newData.keySet()) {
                                    uniqueVal.put(key, newData.get(key));
                                }
                                //替换原来的外键
                                if (fkMap != null && !fkMap.isEmpty()) {
                                    boolean flag;
                                    int ix = 0;
                                    for (Map ofkMap : fkInfoList) {
                                        flag = false;
                                        List<Map> uniqueList = (List<Map>) ofkMap.get("uniqueList");
                                        List<Map> newUniqueList = (List<Map>) fkMap.get("uniqueList");
                                        for (int i = 0; i < uniqueList.size(); i++) {
                                            Map om = uniqueList.get(i);
                                            Map nm = newUniqueList.get(i);
                                            if (om.get("value").equals(nm.get("value"))) {
                                                flag = true;
                                            } else {
                                                flag = false;
                                                break;
                                            }
                                        }
                                        if (flag) {
                                            fkInfoList.remove(ix);
                                            fkInfoList.add(fkMap);
                                            break;
                                        }
                                        ix++;
                                    }
                                }
                                break;
                            }
                        }
                    }
                    return;
                }
            }
            result.add(newData);
            if (fkMap != null && !fkMap.isEmpty()) {
                fkInfoList.add(fkMap);
            }
        }
    }

    private Map columnNameAndValueTransform(String tabName, List<Map<String, String>> uniques, Map<String, Object> fkMap, List<Map<String, Object>> cols, Map data, boolean useBelongId) {
        Map newData = new HashMap();
        if (CollectionUtils.isNotEmpty(cols)) {
            logger.info("准备进行数据转换，转换数据[{}]", JSONUtils.ObjectToJson(data), JSONUtils.ObjectToJson(newData));
            for (Map<String, Object> colInfo : cols) {
                Object colVal = colInfo.get("value") == null ? null : replaceVariableExp(data, colInfo.get("value").toString());
                if ("1".equalsIgnoreCase(colInfo.getOrDefault("fkFlag", "").toString()) && CollectionUtils.isNotEmpty(uniques)) {
                    if (fkMap.get("useBelongId") == null) {
                        fkMap.put("useBelongId", useBelongId);
                    }
                    if (fkMap.get("uniqueList") == null) {
                        List<Map> uniqueList = new ArrayList<>();
                        for (Map uMap : uniques) {
                            Map uniqueMap = new HashMap();
                            uniqueMap.put("type", uMap.get("type"));
                            uniqueMap.put("name", uMap.get("name"));
                            uniqueMap.put("value", replaceVariableExp(data, uMap.get("value").toString()));
                            uniqueList.add(uniqueMap);
                        }
                        fkMap.put("uniqueList", uniqueList);
                    }

                    try {
                        Map newColInfo = (Map) JacksonJsonUtil.jsonToBean(JacksonJsonUtil.beanToJson(colInfo), Map.class);
                        newColInfo.put("value", colVal);
                        if (fkMap.get("fkList") == null) {
                            fkMap.put("fkList", new ArrayList<Map>());
                        }
                        ((List<Map>) fkMap.get("fkList")).add(newColInfo);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    if (colInfo.get("fkValue") != null) {
                        colVal = exeSimpleOperator(replaceVariables(colInfo.get("fkValue").toString(), data));
                    } else {
                        colVal = null;
                    }
                }
                if (!"1".equals(colInfo.getOrDefault("exclude", "").toString())) {
                    if (colVal == null) {
                        colVal = colInfo.get("defaultValue") == null ? null : replaceVariableExp(data, colInfo.get("defaultValue").toString());
                    }
                    newData.put(colInfo.get("name"), colVal);
                }
            }
            logger.info("有一条数据转换成功，转换前数据[{}]，转换后数据[{}]", JSONUtils.ObjectToJson(data), JSONUtils.ObjectToJson(newData));
        }
        return newData;
    }

    private Object replaceVariableExp(Map data, String valExp) {
        Matcher m = Pattern.compile("\\$((sql)|(date)|(timestamp)|(map))\\((.+?)\\)").matcher(valExp);

        StringBuilder builder = new StringBuilder();
        int start = 0;
        String exp;
        while (m.find()) {
            builder.append(valExp.substring(start, m.start()));
            start = m.end();
            exp = m.group(m.groupCount());
            if ("sql".equalsIgnoreCase(m.group(1))) {
                String sql = replaceVariables(exp, data);
                try {
                    builder.append(iocImportMapper.queryTextBySql(sql));
                } catch (Exception e) {
                    throw new RuntimeException("SQL[" + sql + "]查询报错", e);
                }
            } else if ("date".equalsIgnoreCase(m.group(1))) {
                String[] params = exp.split(",");
                Object dateStr = exeSimpleOperator(replaceVariables(params[0], data));
                builder.append(dateFormat((String) dateStr, params[1], params.length > 2 ? params[2] : null));
            } else if ("timestamp".equalsIgnoreCase(m.group(1))) {
                String[] params = exp.split(",");
                builder.append(timstampFormat(replaceVariables(params[0], data), params[1], params.length > 2 ? Boolean.valueOf(params[2]) : false));
            } else if ("map".equalsIgnoreCase(m.group(1))) {
                String[] params = exp.split(",");
                int len = params.length;
                String key = replaceVariables(params[len - 1], data);
                if (len > 1) {
                    String[] items;
                    Map map = new HashMap();
                    for (int i = 0; i < len - 1; i++) {
                        items = params[i].split(":");
                        map.put(items[0], items[1]);
                    }
                    builder.append(map.get(key));
                } else {
                    builder.append(key);
                }
            }
        }
        builder.append(replaceVariables(valExp.substring(start), data));

        return exeSimpleOperator(builder.toString());
    }

    private Object exeSimpleOperator(String str) {
        if (StringUtil.isNullOrEmpty(str)) {
            return null;
        }
        if (!str.contains(" + ") && !str.contains(" - ") && !str.contains(" * ") && !str.contains(" / ")) {
            return str;
        }
        Object result = str;
        try {
            result = groovyScriptEngine.executeObject(str, new HashMap<>());
        } catch (Exception e) {
            logger.warn("执行groovy脚本失败，执行内容：[{}]", str);
        }
        return result;
    }

    private String timstampFormat(String dateStr, String format, boolean isMills) {
        if (StringUtil.isNullOrEmpty(dateStr)) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            if (isMills) {
                return String.valueOf(sdf.parse(dateStr).getTime());
            } else {
                return String.valueOf(sdf.parse(dateStr).getTime() / 1000);
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private String dateFormat(String dateStr, String format, String format2) {
        if (StringUtil.isNullOrEmpty(dateStr)) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            Date date;
            if (!StringUtil.isNullOrEmpty(format2)) {
                date = new SimpleDateFormat(format2).parse(dateStr);
            } else {
                date = new Date(Long.valueOf(dateStr));
            }
            return sdf.format(date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private String replaceVariables(String str, Map data) {
        Matcher matcher = Pattern.compile("\\$\\{(.+?) \\}").matcher(str);
        StringBuilder builder = new StringBuilder();
        int start = 0;
        while (matcher.find()) {
            Object val = getMapOrListValue(data, matcher.group(1).trim());
            builder.append(str.substring(start, matcher.start())).append(val == null ? "" : val);
            start = matcher.end();
        }
        builder.append(str.substring(start));
        return builder.toString();
    }

    private Object getMapOrListValue(Object mapOrList, String keyStr) {
        String[] keys = keyStr.split("\\.");
        Object val = mapOrList;
        for (String key : keys) {
            if (val instanceof Map) {
                val = ((Map) val).get(key);
            } else if (val instanceof List) {
                int ix = Integer.parseInt(key);
                if (ix < ((List) val).size()) {
                    val = ((List) val).get(ix);
                } else {
                    val = null;
                }
            } else {
                val = null;
            }
        }
        if (val == mapOrList) {
            val = null;
        }
        return val;
    }

    @Transactional
    public void handlerFkInfo(Long corpId, String belongId, Map<String, List<Map>> fkInfoMap) {
        if (fkInfoMap != null && !fkInfoMap.isEmpty()) {
            List<String> sqls = new ArrayList<>();
            List<String> cbSqls = new ArrayList<>();
            for (String tabName : fkInfoMap.keySet()) {
                List<Map> list = fkInfoMap.get(tabName);
                if (CollectionUtils.isNotEmpty(list)) {
                    for (Map map : list) {
                        Map params = new HashMap();
                        params.put("@corpId", corpId);
                        params.put("@belongId", belongId);

                        List<Map> fkList = (List<Map>) map.get("fkList");
                        List<Map> uniqueList = (List<Map>) map.get("uniqueList");
                        boolean useBelongId = (boolean) map.get("useBelongId");

                        if (CollectionUtils.isEmpty(uniqueList) || CollectionUtils.isEmpty(fkList)) {
                            throw new RuntimeException("不能进行外键更新，不存在唯一键的相关条件或需要更新的字段");
                        }

                        logger.info("开始进行关联键更新操作，关联键信息[{}]", JSONUtils.ObjectToJson(map));

                        StringBuilder sb0 = new StringBuilder();
                        sb0.append(" WHERE ");
                        for (Map uMap : uniqueList) {
                            String name = (String) uMap.get("name");
                            sb0.append(getDbColName(name)).append(" = ").append(getSimpleSqlValue(uMap.get("value"), (String) uMap.get("type"))).append(" AND ");
                            params.put(uMap.get("name"), uMap.get("value"));
                        }
                        if (useBelongId) {
                            sb0.append("belong_org_id = ").append(belongId).append(" AND ");
                        }

                        StringBuilder sb2 = new StringBuilder();
                        StringBuilder sb3 = new StringBuilder();
                        sb2.append("UPDATE ").append(tabName).append(" SET ");
                        sb3.append(sb0.subSequence(0, sb0.length() - 5));

                        StringBuilder uSql = new StringBuilder();
                        for (Map fkMap : fkList) {
                            params.put("@value", fkMap.get("value"));
                            if (fkMap.get("value") != null && !"".equals(fkMap.get("value"))) {
                                uSql.delete(0, uSql.length());
                                String fkExp = replaceVariables(fkMap.get("fkExp").toString(), params);
                                uSql.append("UPDATE ").append(tabName).append(" SET ").append(generateSetDbExpValStr((String) fkMap.get("name"), fkExp))
                                        .append(" ").append(sb0).append("EXISTS(").append(fkExp).append(")");
                                ;
                                sqls.add(uSql.toString());
                            }

                            if (fkMap.get("callbackExp") != null) {
                                List<Map> cbList = (List<Map>) fkMap.get("callbackExp");
                                if (CollectionUtils.isNotEmpty(cbList)) {
                                    for (Map cbMap : cbList) {
                                        uSql.delete(0, uSql.length());
                                        String cbExp = replaceVariables(cbMap.get("exp").toString(), params);
                                        uSql.append("UPDATE ").append(tabName).append(" SET ").append(generateSetDbExpValStr((String) cbMap.get("name"), cbExp))
                                                .append(" ").append(sb0.subSequence(0, sb0.length() - 5));
                                        cbSqls.add(uSql.toString());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            updateBysqls(sqls);
            logger.info("进行关联键更新操作完成，共执行了[{}]条更新信息", sqls.size());
            updateBysqls(cbSqls);
            logger.info("执行回调操作完成，共执行了[{}]条回调信息", cbSqls.size());
        }
    }

    private String generateSetDbExpValStr(String name, String fkExp) {
        if (name.startsWith("EXT_COL_")) {
            return "ext_custom_col = jsonb_set(ext_custom_col::jsonb, '{".concat(name).concat("}', ('\"' || (").concat(fkExp)
                    .concat(") || '\"')::jsonb, true)");
        } else if (name.contains(".")) {
            String[] ss = name.split("\\.");
            return ss[0].concat(" = jsonb_set(").concat(ss[0]).concat("::jsonb, '{").concat(ss[1]).concat("}', ('\"' || (").concat(fkExp)
                    .concat(") || '\"')::jsonb, true)");
        }
        return name + " = (" + fkExp + ")";
    }

    private String getDbColName(String name) {
        if (name.startsWith("EXT_COL_")) {
            return "ext_custom_col ->> '" + name + "'";
        } else if (name.contains(".")) {
            String[] ss = name.split("\\.");
            return ss[0] + " ->> '" + ss[1] + "'";
        }
        return name;
    }

    private void insertBysqls(List<String> sqls) {
        int len = sqls.size();
        int size = 100;
        int count = (len + size - 1) / size;
        for (int i = 0; i < count; i++) {
            List<String> tempSqls = new ArrayList<>(size);
            int max = (i + 1) * size;
            max = max < len ? max : len;
            for (int j = i * size; j < max; j++) {
                tempSqls.add(sqls.get(j));
            }
            iocImportMapper.insertBySqls(tempSqls);
        }
    }

    private void updateBysqls(List<String> sqls) {
        int len = sqls.size();
        int size = 100;
        int count = (len + size - 1) / size;
        for (int i = 0; i < count; i++) {
            List<String> tempSqls = new ArrayList<>(size);
            int max = (i + 1) * size;
            max = max < len ? max : len;
            for (int j = i * size; j < max; j++) {
                tempSqls.add(sqls.get(j));
            }
            iocImportMapper.updateBySqls(tempSqls);
        }
    }

    private String getSimpleSqlValue(Object val, String type) {
        if (val == null || "".equals(val)) {
            return null;
        }
        if ("boolean".equalsIgnoreCase(type) && ("true".equals(val.toString()) || "false".equals(val.toString()))) {
            return val.toString();
        } else if ("int".equalsIgnoreCase(type) && StringUtil.isNum(val.toString())) {
            return val.toString();
        } else {
            return "'" + val + "'";
        }
    }

    /**
     * 刷新员工缓存
     *
     * @param belongId
     */
    @Transactional
    public void flashEmpInfoCache(String belongId) {
        if (belongId != null) {
            startupServiceImpl.initEmp(belongId);
        }
    }

    /**
     * 刷新部门全路径
     */
    @Transactional
    public void flashSysOrgPath(String belongId) {
        if (belongId != null) {
            startupServiceImpl.initSysOrgPath(belongId);
        }
    }

    /**
     * 更新部门层级数字
     *
     * @param belongId
     */
    @Transactional
    public void updateOrgLevelnum(String belongId) {
        sysCorpOrgMapper2.updateOrgLevelnum(belongId);
    }

    /**
     * 信托项目人员信息同步接口前置处理器
     */
    public List<Map<String, Object>> GDBankEmpInfoBeforeTrigger(SysDataInput dataInput, Long corpId, String belongId, List<Map<String, Object>> sourceResult) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceResult)) return result;

        sourceResult = sourceResult.stream().filter(e -> e.get("DBO.CUIWT_DEPTNAME_LINE(B01_AD_CODE)").toString().contains("光大兴陇信托有限责任公司/光大信托本部")).collect(Collectors.toList());
        sourceResult.stream().forEach(item -> {
            Map<String, Object> row = new HashMap<String, Object>();
            for (String field : dataInput.getSourceExp().split(",")) {
                if (field.indexOf(":TIMESTAMP") != -1) {
                    String key = field.substring(field.lastIndexOf("#") + 1);
                    String dataFormat = field.substring(field.indexOf("(") + 1, field.indexOf(")"));
                    SimpleDateFormat sf = new SimpleDateFormat(dataFormat.split("#")[1]);
                    try {
                        Object object = item.get(dataFormat.split("#")[0]);
                        if (object != null) {
                            Long time = sf.parse((String) object).getTime() / 1000;
                            row.put(key, time);
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }
                } else {
                    String[] meta = field.split("#");
                    if (meta.length > 1) {
                        Object value = item.get(meta[0]);
                        if ("orgid".equals(meta[1])) {
                            value = value.toString().substring(value.toString().indexOf("/") + 1);
                        }
                        row.put(meta[1], value);
                    } else {
                        row.put(field, item.get(field));
                    }
                }
            }
            System.out.println("添加了数据");
            result.add(row);
        });
        //return new ArrayList<>();
        return result;

    }

    /**
     * 信托项目用户信息同步接口前置处理器
     */
    public List<Map<String, Object>> GDBankUserInfoBeforeTrigger(SysDataInput dataInput, Long corpId, String belongId, List<Map<String, Object>> sourceResult) {
        if (CollectionUtils.isEmpty(sourceResult)) return new ArrayList<>();
        JSONObject source = JSONObject.parseObject(dataInput.getSourceConfig().toString());
        List<Map<String, Object>> result = new ArrayList<>();
        sourceResult.stream().forEach(item -> {
            if (null != item.get("LOGINOTHERNAME") && StringUtils.isNoneEmpty(item.get("LOGINOTHERNAME").toString())) {
                Map<String, Object> row = new HashMap<String, Object>();
                for (String field : dataInput.getSourceExp().split(",")) {
                    if (field.indexOf(":TIMESTAMP") != -1) {
                        String key = field.substring(field.lastIndexOf("#") + 1);
                        String dataFormat = field.substring(field.indexOf("(") + 1, field.indexOf(")"));
                        SimpleDateFormat sf = new SimpleDateFormat(dataFormat.split("#")[1]);
                        try {
                            Object object = item.get(dataFormat.split("#")[0]);
                            if (object != null) {
                                Long time = sf.parse((String) object).getTime() / 1000;
                                row.put(key, time);
                            }
                        } catch (Exception e) {
                            logger.error(e.getMessage(), e);
                        }
                    } else {
                        String[] meta = field.split("#");
                        if (meta.length > 1) {
                            Object value = item.get(meta[0]);
                            if ("orgid".equals(meta[1])) {
                                value = value.toString().substring(value.toString().indexOf("/") + 1);
                            }
                            row.put(meta[1], value);
                        } else {
                            row.put(field, item.get(field));
                        }
                    }
                }
                ;
                row.put("email", row.get("account") + "@ebtrust.com");
                row.put("roleids", StringUtils.defaultIfEmpty(source.getString("roleids"), "信托系统角色"));
                System.out.println("添加了数据");
                result.add(row);
            }

        });

        return result;

    }

    private String getTimeStr(float minNum) {
        float h = minNum / 60;
        float m = minNum % 60;
        String s = "";
        if (h > 0) {
            Float h1 = new Float(h);
            s = s + h1.intValue() + "点";
        }
        if (m > 0) {
            Float m1 = new Float(m);
            s = s + m1.intValue() + "分";
        }
        return s;
    }

    /**
     * 加班时间校验
     *
     * @param empShiftInfo
     * @param originOt
     * @param checkList
     * @return
     */
    public Boolean checkOverTime(Map<String, EmpShiftInfo> empShiftInfo, Map originOt, List<Map> checkList) {
        Long startTime = (Long) originOt.get("start_time");
        Long endTime = (Long) originOt.get("end_time");
        Long startDate = DateUtil.getDateLong(startTime * 1000, "yyyy-MM-dd", true);
        Long endDate = DateUtil.getDateLong(endTime * 1000, "yyyy-MM-dd", true);
        Long startMin = startTime - startDate;//加班开始时间点
        Long endMin = endTime - startDate;//加班结束时间点

        //排班校验
        EmpShiftInfo startShift = empShiftInfo.get(startDate);
        if (startShift == null) {
            this.addErrorMsg(originOt, checkList, DateUtil.getDateStrByTimesamp(startDate) + "没有排班记录，不能加班");
            return false;
        }
        EmpShiftInfo endShift = startShift;
        if (startDate < endDate) {
            endShift = empShiftInfo.get(endDate);
        }
        if (endShift == null) {
            this.addErrorMsg(originOt, checkList, DateUtil.getDateStrByTimesamp(endDate) + "没有排班记录，不能加班");
            return false;
        }

        //校验日工作计划是否设置加班时间
        Integer overtimeStartTimeMin = startShift.getOvertimeStartTime();
        Integer overtimeEndTimeMin = startShift.getOvertimeEndTime();
        if (overtimeStartTimeMin == null || overtimeEndTimeMin == null) {
            return true;
        }

        //判断加班开始时间是否在加班允许的时间范围内
        if (startShift.getOvertimeStartTime() < startShift.getOvertimeEndTime()) {
            //日工作计划中设置：加班结束时间大于加班开始时间 即 加班不允许跨夜
            if (endDate > startDate) {
                this.addErrorMsg(originOt, checkList, "加班不允许跨夜");
                return false;
            }
            if (startMin < startShift.getOvertimeStartTime() * 60) {
                this.addErrorMsg(originOt, checkList, "必须在" + getTimeStr(startShift.getOvertimeStartTime()) + "之后才能申请加班");
                return false;
            }
        } else if (startShift.getOvertimeStartTime() > startShift.getOvertimeEndTime()) {
            //日工作计划中设置：加班结束时间小于加班开始时间 即 加班单允许跨夜
            if (startMin > startShift.getOvertimeEndTime() * 60 && startMin < startShift.getOvertimeStartTime() * 60) {
                this.addErrorMsg(originOt, checkList, "必须在" + getTimeStr(startShift.getOvertimeEndTime()) + "之前或者" + getTimeStr(startShift.getOvertimeStartTime()) + "之后才能申请加班");
                return false;
            }
        }

        if (startDate < endDate) {
            //校验日工作计划是否设置加班时间
            if (endShift.getOvertimeStartTime() == null || endShift.getOvertimeEndTime() == null) {
                return true;
            }
            if (endShift.getDateType() == 1) {
                //日工作计划中加班结束时间大于下班开始时间
                if (endShift.getOvertimeEndTime() > endShift.getOffDutyStartTime()) {
                    this.addErrorMsg(originOt, checkList, "必须在" + getTimeStr(endShift.getOvertimeEndTime()) + "之后才能申请加班");
                    return false;
                }
            }
            //校验加班时间是否在：日工作计划设置的加班时间范围中
            long endSecondTime = endMin - 24 * 60 * 60;//本次加班结束时间
            if (endSecondTime > endShift.getOvertimeEndTime() * 60 && endShift.getOvertimeEndTime() > 0) {
                //本次加班结束时间大于日工作计划中设置的加班结束时间
                this.addErrorMsg(originOt, checkList, "第二天，必须在" + getTimeStr(endShift.getOvertimeEndTime()) + "之前才能申请加班");
                return false;
            }
            if (endShift.getOvertimeStartTime() > endShift.getOvertimeEndTime()) {
                //日工作计划中：加班结束时间小于加班开始时间
                //加班结束时间所在的日工作计划
                long secondDayOvertimeEndTime = endShift.getOvertimeEndTime() * 60;
                long endMinTemp = endTime - endDate;
                if (!(endMinTemp >= 0 && endMinTemp <= secondDayOvertimeEndTime)) {
                    this.addErrorMsg(originOt, checkList, "第二天，必须在" + getTimeStr(endShift.getOvertimeEndTime()) + "之前才能申请加班");
                    return false;
                }
            }
        } else {
            //判断加班结束时间是否在加班的允许范围内
            if (startShift.getOvertimeEndTime() > startShift.getOvertimeStartTime()) {
                //日工作计划中设置：加班结束时间大于加班开始时间
                if (endMin > startShift.getOvertimeEndTime() * 60) {
                    this.addErrorMsg(originOt, checkList, "必须在" + getTimeStr(startShift.getOvertimeEndTime()) + "之前才能申请加班");
                    return false;
                }
            } else if (startShift.getOvertimeStartTime() > startShift.getOvertimeEndTime()) {
                //日工作计划中设置：加班结束时间小于加班开始时间
                //校验加班时间范围是否跨越
                long overtimeStartTime = startShift.getOvertimeStartTime() * 60;
                long overtimeEndTime = startShift.getOvertimeEndTime() * 60;
                if (!(startMin >= 0 && endMin <= overtimeEndTime) && !(startMin >= overtimeStartTime && endMin < 86400)) {
                    this.addErrorMsg(originOt, checkList, "加班的时间段必须在0点-" + getTimeStr(startShift.getOvertimeEndTime()) + getTimeStr(startShift.getOvertimeStartTime()) + "-24点两段时间段内");
                    return false;
                }
            }
        }
        return true;
    }

    private static final String MXY_HOST = "https://open.moxueyuan.com/api/v1/";

    public void sendToMagicAcademyPlatform(Boolean all, String belongId, String corpId, String corpSecret) {
        String mxyToken = getMxyToken(corpId, corpSecret);
        if (mxyToken == null) {
            log.error("tokenIsNull");
            return;
        }
        Map params = new HashMap<>();
        params.put("corpId", Long.parseLong(belongId));
        params.put("token", mxyToken);
        Long timeInterval = (System.currentTimeMillis() / 1000) - (3600 * 25);
        if (!all) {
            params.put("crtTime", timeInterval);
        }
        orgToMxy(params);
        postToMxy(params);
        empToMxy(params);

        if (!all) {
            params.remove("crtTime");
            params.put("updTime", timeInterval);
            orgToMxy(params);
            postToMxy(params);
            empToMxy(params);
        }
    }

    public void orgToMxy(Map params) {
        String corpId = params.get("corpId").toString();
        params.put("status", 1);
        List<Map<String, Object>> list = sysCorpOrgMapper2.selectOrgByParams(params);
        for (Map<String, Object> map : list) {
            String forgId = map.get("forgId").toString();
            boolean oneLevel = corpId.equals(forgId) || "0".equals(forgId);
            JSONObject body = new JSONObject();
            body.put("id", map.get("orgId").toString());
            body.put("name", map.get("shortName").toString());
            body.put("parentid", oneLevel ? "0" : forgId);
            body.put("order", map.get("order").toString());
            String result = HttpUtil.doPost(MXY_HOST + String.format("contacts/department/%s?access_token=%s"
                    , params.containsKey("updtime") ? "create" : "create", params.get("token")), null, body);
            log.info("addOrgToMxy={}result={}", body, result);
        }
    }

    public void postToMxy(Map params) {
        List<Map<String, Object>> list = sysCorpOrgMapper2.selectOrgPostByParams(params);
        for (Map<String, Object> map : list) {
            JSONObject body = new JSONObject();
            body.put("postid", map.get("postId").toString());
            body.put("postname", map.get("orgPath") + " 岗位：" + map.get("postName").toString());
            body.put("order", map.get("order").toString());
            String result = HttpUtil.doPost(MXY_HOST + String.format("contacts/post/%s?access_token=%s",
                    params.containsKey("updtime") ? "create" : "create", params.get("token")), null, body);
            log.info("postAddToMxy={}result={}", body, result);
        }
    }

    public void empToMxy(Map params) {
        TextAspect textAspect = SpringUtils.getBean(TextAspect.class);

        List<Map<String, Object>> list = sysCorpOrgMapper2.selectEmpByParams(params);
        for (Map<String, Object> map : list) {
            if (map.get("gender") != null) {
                String genderText = textAspect.getDictCache(map.get("gender").toString(), ResponseWrap.getLocale());
                if ("男".equals(genderText)) {
                    map.put("gender", "1");
                }
                if ("女".equals(genderText)) {
                    map.put("gender", "2");
                }
            } else {
                map.put("gender", "0");
            }
            String result = HttpUtil.doPost(
                    String.format(MXY_HOST + "contacts/user/%s?access_token=%s",
                            params.containsKey("updtime") ? "create" : "create", params.get("token")), null, new JSONObject(map));
            log.info("empAddToMxy={}result={}", map, result);
        }
    }

    public static String getMxyToken(String corpId, String corpSecret) {
        try {
            String token = HttpUtil.doGet(MXY_HOST + String.format("connect/get-token?corpid=%s&corpsecret=%s", corpId, corpSecret));
            log.info("getMxyToken result:{}", token);
            if (token != null) {
                Map<String, Object> map = JSONUtils.toMap(token);
                if ("0".equals(map.get("errcode"))) {
                    return JSONUtils.toMap(map.get("results").toString()).get("access_token").toString();
                }
            }
        } catch (Exception e) {
            log.error("getMxyToken:{}", e.getMessage(), e);
        }
        return null;
    }
}
