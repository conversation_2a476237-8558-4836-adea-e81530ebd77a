package com.caidaocloud.attendance.core.wa.dto.shift;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询员工排班详情请求参数DTO
 *
 * <AUTHOR>
 * @Date 2025/2/14
 */
@Data
@ApiModel("查询员工排班详情请求参数DTO")
public class ListScheduleQueryDto {
    @ApiModelProperty("租户ID，可选")
    private String tenantId;
    @ApiModelProperty("开始日期")
    private Long startDate;
    @ApiModelProperty("结束日期")
    private Long endDate;
    @ApiModelProperty("员工ID")
    private List<Long> empIds;
}
