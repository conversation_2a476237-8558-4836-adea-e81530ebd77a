package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaPlanEmpRel;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpClockPlanRel;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaPlanEmpRelPo;
import com.caidaocloud.dto.EmpInfoKeyValue;

import java.util.List;

public interface IPlanEmpRelRepository {

    int saveBatch(List<WaPlanEmpRel> rel);

    void deleteByPlanId(Long planId, String belongOrgId);

    void deleteByIds(String tenantId, List<Long> ids);

    List<WaPlanEmpRel> getByPlanId(Long planId);

    List<EmpInfoKeyValue> getEmployeesByPlanId(String belongOrgId, Long planId);

    List<EmpInfoKeyValue> getEmployeesByPlanIds(String belongOrgId, List<Long> planIds);

    void updatePlanIdByParams(Long planId, Long userId, Long corpId, String belongOrgId, List<Long> empIds);

    EmpClockPlanRel getPlanEmpRelById(Long id, String belongOrgId);

    List<EmpClockPlanRel> getEmpClockPlanByPeriod(Long empId, Long id, String belongOrgId, Long startTime, Long endTime);

    void save(WaPlanEmpRelPo waPlanEmpRelPo);

    void update(WaPlanEmpRelPo waPlanEmpRelPo);

    AttendancePageResult<EmpClockPlanRel> getEmpClockPlanList(AttendanceBasePage basePage, String belongOrgId,
                                                              Long planId, String filter, String effectiveStatus);

    EmpClockPlanRel getPlanEmpRelByEmpIdAndPeriod(String tenantId, Long empId, Long time);

    List<WaPlanEmpRelPo> getByIds(String tenantId, List<Long> ids);
}
