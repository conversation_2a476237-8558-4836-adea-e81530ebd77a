package com.caidaocloud.attendance.core.config.annotation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.Formatter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2020-12-16
 */
public class CDDateTimeFormatter implements Formatter<Long> {
    private final static Logger LOGGER = LoggerFactory.getLogger(CDDateTimeFormatter.class);
    private String pattern;

    @Override
    public Long parse(String str, Locale locale) throws ParseException {
        if(null == str || "".equals(str)){
            return null;
        }

        if(null == pattern || pattern.isEmpty()){
            LOGGER.warn("@CDDateTimeFormat parse pattern is empty, default yyyy/MM/dd ");
            pattern = "yyyy/MM/dd";
        }

        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date soureDate = sdf.parse(str);
        return soureDate.getTime();
    }

    @Override
    public String print(Long soure, Locale locale) {
        if(null == soure){
            return "";
        }

        if(null == pattern || pattern.isEmpty()){
            LOGGER.warn("@CDDateTimeFormat print pattern is empty, default yyyy/MM/dd ");
            pattern = "yyyy/MM/dd";
        }

        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(new Date(Long.valueOf(soure)));
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }
}
