package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.sdk.dto.SdkLeaveExtensionApplyDTO;
import com.caidaocloud.attendance.sdk.dto.SdkLeaveExtensionQuotaDTO;
import com.caidaocloud.attendance.sdk.dto.SdkLeaveExtensionRevokeDTO;
import com.caidaocloud.attendance.sdk.feign.ILeaveExtensionFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

/**
 * 假期延期
 */
@Component
public class LeaveExtensionFeignFallBack implements ILeaveExtensionFeignClient {
    @Override
    public Result<?> applyLeaveExtension(SdkLeaveExtensionApplyDTO dto) {
        return Result.fail();
    }

    @Override
    public Result<?> revokeLeaveExtension(SdkLeaveExtensionRevokeDTO dto) {
        return Result.fail();
    }

    @Override
    public Result<?> getLeaveExtensionQuotaType() {
        return Result.fail();
    }

    @Override
    public Result<?> getLeaveExtensionQuotaList(SdkLeaveExtensionQuotaDTO dto) {
        return Result.fail();
    }
}
