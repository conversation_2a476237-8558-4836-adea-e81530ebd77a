package com.caidao1.integrate.entity.TieTong;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;

import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.channels.WritableByteChannel;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.*;

public class HttpUtil {
    private static Map<String, String> strToMap(String str) {
        Map<String, String> map = new HashMap();

        try {
            String[] params = str.split("&");
            String[] var3 = params;
            int var4 = params.length;

            for (int var5 = 0; var5 < var4; ++var5) {
                String param = var3[var5];
                String[] a = param.split("=");
                map.put(a[0], a[1]);
            }

            return map;
        } catch (Exception var8) {
            throw new RuntimeException(var8);
        }
    }

    private static Map<String, String> initialBasicHeader(String method, String path, Map<String, String> headers, Map<String, String> querys, Map<String, String> bodys, List<String> signHeaderPrefixList, String appKey, String appSecret) throws MalformedURLException {
        if (headers == null) {
            headers = new HashMap();
        }

        ((Map) headers).put("x-ca-timestamp", String.valueOf((new Date()).getTime()));
        ((Map) headers).put("x-ca-nonce", UUID.randomUUID().toString());
        ((Map) headers).put("x-ca-key", appKey);
        ((Map) headers).put("x-ca-signature", SignUtil.sign(appSecret, method, path, (Map) headers, querys, bodys, signHeaderPrefixList));
        return (Map) headers;
    }

    public static HttpClient wrapClient(String host) {
        HttpClient httpClient = new DefaultHttpClient();
        if (host.startsWith("https://")) {
            sslClient(httpClient);
        }

        return httpClient;
    }

    private static void sslClient(HttpClient httpClient) {
        try {
            SSLContext ctx = null;
            String jdkVersion = System.getProperty("java.specification.version");
            if (Double.parseDouble(jdkVersion) >= Constants.JDK_VERSION) {
                ctx = SSLContext.getInstance("TLSv1.2");
            } else {
                ctx = SSLContext.getInstance("TLS");
            }

            X509TrustManager tm = new X509TrustManager() {
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

                public void checkClientTrusted(X509Certificate[] xcs, String str) {
                }

                public void checkServerTrusted(X509Certificate[] xcs, String str) {
                }
            };
            ctx.init((KeyManager[]) null, new TrustManager[]{tm}, (SecureRandom) null);
            SSLSocketFactory ssf = new SSLSocketFactory(ctx);
            ssf.setHostnameVerifier(SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
            ClientConnectionManager ccm = httpClient.getConnectionManager();
            SchemeRegistry registry = ccm.getSchemeRegistry();
            registry.register(new Scheme("https", 443, ssf));
        } catch (KeyManagementException var7) {
            throw new RuntimeException(var7);
        } catch (NoSuchAlgorithmException var8) {
            throw new RuntimeException(var8);
        }
    }

    private static int getTimeout(int timeout) {
        return timeout == 0 ? Constants.DEFAULT_TIMEOUT : timeout;
    }

    public static String initUrl(String host, String path, Map<String, String> querys) throws UnsupportedEncodingException {
        StringBuilder sbUrl = new StringBuilder();
        sbUrl.append(host);
        if (!StringUtils.isBlank(path)) {
            sbUrl.append(path);
        }

        if (null != querys) {
            StringBuilder sbQuery = new StringBuilder();
            Iterator var5 = querys.entrySet().iterator();

            while (var5.hasNext()) {
                Map.Entry<String, String> query = (Map.Entry) var5.next();
                if (0 < sbQuery.length()) {
                    sbQuery.append("&");
                }

                if (StringUtils.isBlank((CharSequence) query.getKey()) && !StringUtils.isBlank((CharSequence) query.getValue())) {
                    sbQuery.append((String) query.getValue());
                }

                if (!StringUtils.isBlank((CharSequence) query.getKey())) {
                    sbQuery.append((String) query.getKey());
                    if (!StringUtils.isBlank((CharSequence) query.getValue())) {
                        sbQuery.append("=");
                        sbQuery.append(URLEncoder.encode((String) query.getValue(), "UTF-8"));
                    }
                }
            }

            if (0 < sbQuery.length()) {
                sbUrl.append("?").append(sbQuery);
            }
        }

        return sbUrl.toString();
    }

    private static Response convert(HttpResponse response) throws IOException {
        Response res = new Response();
        if (null != response) {
            res.setStatusCode(response.getStatusLine().getStatusCode());
            Header[] var2 = response.getAllHeaders();
            int var3 = var2.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                Header header = var2[var4];
                res.setHeader(header.getName(), MessageDigestUtil.iso88591ToUtf8(header.getValue()));
            }

            res.setContentType(res.getHeader("Content-Type"));
            res.setRequestId(res.getHeader("X-Ca-Request-Id"));
            res.setErrorMessage(res.getHeader("X-Ca-Error-Message"));
            if (response.getEntity() == null) {
                res.setBody((String) null);
            } else {
                res.setBody(readStreamAsStr(response.getEntity().getContent()));
            }
        } else {
            res.setStatusCode(500);
            res.setErrorMessage("No Response");
        }

        return res;
    }

    public static String readStreamAsStr(InputStream is) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        WritableByteChannel dest = Channels.newChannel(bos);
        ReadableByteChannel src = Channels.newChannel(is);
        ByteBuffer bb = ByteBuffer.allocate(4096);

        while (src.read(bb) != -1) {
            bb.flip();
            dest.write(bb);
            bb.clear();
        }

        src.close();
        dest.close();
        return new String(bos.toByteArray(), "UTF-8");
    }

    public static Response httpPost(String host, String path, int connectTimeout, Map<String, String> headers, Map<String, String> querys, String body, List<String> signHeaderPrefixList, String appKey, String appSecret) throws Exception {
        String contentType = (String) headers.get("Content-Type");
        if ("application/x-www-form-urlencoded;charset=UTF-8".equals(contentType)) {
            Map<String, String> paramMap = strToMap(body);
            String modelDatas = (String) paramMap.get("modelDatas");
            if (StringUtils.isNotBlank(modelDatas)) {
                paramMap.put("modelDatas", URLDecoder.decode(modelDatas));
            }

            headers = initialBasicHeader("POST", path, headers, querys, paramMap, signHeaderPrefixList, appKey, appSecret);
        } else {
            headers = initialBasicHeader("POST", path, headers, querys, (Map) null, signHeaderPrefixList, appKey, appSecret);
        }

        HttpClient httpClient = wrapClient(host);
        httpClient.getParams().setParameter("http.connection.timeout", getTimeout(connectTimeout));
        HttpPost post = new HttpPost(initUrl(host, path, querys));
        Iterator r = headers.entrySet().iterator();

        while (r.hasNext()) {
            Map.Entry<String, String> e = (Map.Entry) r.next();
            post.addHeader((String) e.getKey(), MessageDigestUtil.utf8ToIso88591((String) e.getValue()));
        }

        if (StringUtils.isNotBlank(body)) {
            post.setEntity(new StringEntity(body, "UTF-8"));
        }

        Response response = null;
        try {
            response = convert(httpClient.execute(post));
        } finally {
            if (httpClient != null) {
                httpClient.getConnectionManager().shutdown();
            }

        }

        return response;
    }
}
