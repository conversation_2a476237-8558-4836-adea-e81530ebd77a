package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.model.SysUnitCity;
import com.caidao1.wa.mybatis.mapper.WaEmpOvertimeMapper;
import com.caidao1.wa.mybatis.model.WaEmpOvertime;
import com.caidao1.wa.mybatis.model.WaOvertimeType;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.IOvertimeTransferRuleService;
import com.caidaocloud.attendance.service.application.service.ITravelTypeService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.application.service.IWorkflowRevokeService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkOvertimeMapper;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OvertimeTransferRuleDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.RevokeDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WorkflowRevokeReqDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkflowRevokeServiceImpl implements IWorkflowRevokeService {

    @Autowired
    private WaWorkflowRevokeDo workflowRevokeDo;
    @Autowired
    private WaSobService waSobService;
    @Autowired
    private WaEmpTravelDo empTravelDo;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Resource
    private WaEmpOvertimeMapper empOvertimeMapper;
    @Autowired
    private IOvertimeTransferRuleService overtimeTransferRuleService;
    @Autowired
    private OverTimeTypeDo overTimeTypeDo;
    @Resource
    private WorkOvertimeMapper workOvertimeMapper;
    @Autowired
    private EmpCompensatoryQuotaDo empCompensatoryQuotaDo;
    @Autowired
    private ITravelTypeService travelTypeService;
    @Autowired
    private IWfService wfService;

    private void changeParam(PageBean pageBean, Map map) {
        List<FilterBean> list = pageBean.getFilterList();
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator<FilterBean> it = list.iterator();
            while (it.hasNext()) {
                FilterBean filterBean = it.next();
                if ("overtime".equals(filterBean.getField())) {
                    if (StringUtils.isNotBlank(filterBean.getMin())) {
                        map.put("startDateTime", Long.valueOf(filterBean.getMin()));
                    }
                    if (StringUtils.isNotBlank(filterBean.getMax())) {
                        map.put("endDateTime", Long.valueOf(filterBean.getMax()));
                    }
                    it.remove();
                }
            }
        }
    }

    private Map timeDurationListToMap(List<Map> tdList) {
        if (null == tdList || tdList.isEmpty()) {
            return new HashMap();
        }
        return tdList.stream().collect(Collectors.groupingBy(obj -> obj.get("overtime_id")));
    }

    private Integer getUnit(OvertimeTransferRuleDto transferRule) {
        TransferRuleEnum transferRuleEnum = TransferRuleEnum.getTransferRuleEnum(transferRule.getTransferRule());
        if (null == transferRuleEnum) {
            return 2;
        }
        Map<String, Object> map = transferRuleEnum.calTimeDuration(0f, transferRule.getTransferPeriods(), transferRule.getTransferTime());
        return (Integer) map.get("unit");
    }

    @Override
    public PageList<Map> getOvertimeWorkflowRevokeList(WorkflowRevokeReqDto dto, PageBean pageBean, UserInfo userInfo) {
        Map<String, Object> map = new HashMap<String, Object>();
        changeParam(pageBean, map);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        map.put("filter", filter);
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            map.put("keywords", pageBean.getKeywords());
        }
        String tenantId = userInfo.getTenantId();
        map.put("belongOrgId", tenantId);
        map.put("dataFilter", dto.getDataScope());
        map.put("moduleName", BusinessCodeEnum.OVERTIME_REVOKE.name());
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(StringUtils.isBlank(pageBean.getOrder()) ? "start_time.desc,waid.desc" : pageBean.getOrder()));
        PageList<Map> pageList = workflowRevokeDo.getOvertimeWorkflowRevokeList(pageBounds, map);
        if (CollectionUtils.isEmpty(pageList)) {
            return pageList;
        }
        String otIds = pageList.stream().map(obj -> obj.get("waid").toString()).collect(Collectors.joining(","));
        List<Map> tdList = workOvertimeMapper.getTimeDurationByOtIds(otIds);
        Map tdMap = timeDurationListToMap(tdList);
        List<Integer> overtimeTypeIds = pageList.stream().filter(o -> o.get("overtime_type_id") != null).map(obj -> (Integer) obj.get("overtime_type_id")).distinct().collect(Collectors.toList());
        Map<Long, OvertimeTransferRuleDto> transferRuleMap = new HashMap<>();
        Map<Integer, String> overtimeTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(overtimeTypeIds)) {
            List<WaOvertimeType> overtimeTypes = overTimeTypeDo.getOvertimeTypeByIds(overtimeTypeIds);
            overtimeTypeMap = overtimeTypes.stream().collect(Collectors.toMap(WaOvertimeType::getOvertimeTypeId, WaOvertimeType::getTypeName));
            //转换规则
            List<Long> transferRuleIds = overtimeTypes.stream().map(WaOvertimeType::getRuleId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(transferRuleIds)) {
                List<OvertimeTransferRuleDto> transferRules = overtimeTransferRuleService.getOvertimeTransferRuleList(transferRuleIds);
                transferRuleMap = transferRules.stream().collect(Collectors.toMap(OvertimeTransferRuleDto::getRuleId, Function.identity(), (v1, v2) -> v1));
            }
        }
        List<Map> item = null;
        for (Map woMap : pageList) {
            Integer overtimeTypeId = (Integer) woMap.get("overtime_type_id");
            Long ruleId = (Long) woMap.get("rule_id");
            if (null != overtimeTypeId) {
                String typeName = overtimeTypeMap.get(overtimeTypeId);
                if (StringUtil.isNotBlank(typeName)) {
                    woMap.put("typeName", typeName);
                }
            }
            Integer timeUint = (Integer) woMap.get("time_unit");
            if (timeUint == null) {
                timeUint = 1;
            }
            Integer status = (Integer) woMap.get("status");
            Integer duration = (Integer) woMap.get("duration");
            if (duration == null) {
                duration = 0;
            }
            woMap.put("relTimeDuration", 0f);
            item = (List<Map>) tdMap.get(woMap.get("waid"));
            Float relTimeDuration = 0f;
            if (null != item && !item.isEmpty() && !StringUtil.isEmpty(item.get(0).get("rel_time_duration"))) {
                relTimeDuration = Float.valueOf(item.get(0).get("rel_time_duration").toString());
            }
            if (timeUint == 1) {
                woMap.put("relTimeDuration", relTimeDuration);
            } else {
                BigDecimal v = BigDecimal.valueOf(duration).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                woMap.put("duration", v.floatValue());
                BigDecimal v1 = BigDecimal.valueOf(relTimeDuration).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                woMap.put("relTimeDuration", v1.floatValue());
            }
            woMap.put("timeUnitName", PreTimeUnitEnum.getName(timeUint));
            Integer transferUint = Optional.ofNullable((Integer) woMap.get("transfer_unit")).orElse(0);
            if (transferUint == 0) {
                if (transferRuleMap.containsKey(ruleId)) {
                    OvertimeTransferRuleDto transferRule = transferRuleMap.get(ruleId);
                    transferUint = getUnit(transferRule);
                    woMap.put("transfer_unit", transferUint);
                }
            }
            woMap.put("statusName", ApprovalStatusEnum.getName(status));
            woMap.put("status", status);
            String key = "date_type";
            String keyName = "date_type_txt";
            if (woMap.containsKey(key)) {
                woMap.put(keyName, DateTypeEnum.getName(Integer.parseInt(woMap.get(key).toString())));
            }
            if (woMap.containsKey("compensate_type")) {
                woMap.put("compensate_type_txt", CompensateTypeEnum.getDescByOrdinal((Integer) woMap.get("compensate_type")));
            }
            woMap.put("businessKey", woMap.get("id") + "_" + BusinessCodeEnum.OVERTIME_REVOKE.getCode());
        }
        return pageList;
    }

    @Override
    public PageList<Map> getOvertimeWorkflowAbolishList(WorkflowRevokeReqDto dto, PageBean pageBean, UserInfo userInfo) {
        Map<String, Object> map = new HashMap<String, Object>();
        changeParam(pageBean, map);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        map.put("filter", filter);
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            map.put("keywords", pageBean.getKeywords());
        }
        String tenantId = userInfo.getTenantId();
        map.put("belongOrgId", tenantId);
        map.put("dataFilter", dto.getDataScope());
        map.put("moduleName", BusinessCodeEnum.OVERTIME_ABOLISH.name());
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(StringUtils.isBlank(pageBean.getOrder()) ? "start_time.desc,waid.desc" : pageBean.getOrder()));
        PageList<Map> pageList = workflowRevokeDo.getOvertimeWorkflowRevokeList(pageBounds, map);
        if (CollectionUtils.isEmpty(pageList)) {
            return pageList;
        }
        String otIds = pageList.stream().map(obj -> obj.get("waid").toString()).collect(Collectors.joining(","));
        List<Map> tdList = workOvertimeMapper.getTimeDurationByOtIds(otIds);
        Map tdMap = timeDurationListToMap(tdList);
        List<Integer> overtimeTypeIds = pageList.stream().filter(o -> o.get("overtime_type_id") != null).map(obj -> (Integer) obj.get("overtime_type_id")).distinct().collect(Collectors.toList());
        Map<Long, OvertimeTransferRuleDto> transferRuleMap = new HashMap<>();
        Map<Integer, String> overtimeTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(overtimeTypeIds)) {
            List<WaOvertimeType> overtimeTypes = overTimeTypeDo.getOvertimeTypeByIds(overtimeTypeIds);
            overtimeTypeMap = overtimeTypes.stream().collect(Collectors.toMap(WaOvertimeType::getOvertimeTypeId, WaOvertimeType::getTypeName));
            //转换规则
            List<Long> transferRuleIds = overtimeTypes.stream().map(WaOvertimeType::getRuleId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(transferRuleIds)) {
                List<OvertimeTransferRuleDto> transferRules = overtimeTransferRuleService.getOvertimeTransferRuleList(transferRuleIds);
                transferRuleMap = transferRules.stream().collect(Collectors.toMap(OvertimeTransferRuleDto::getRuleId, Function.identity(), (v1, v2) -> v1));
            }
        }
        List<Map> item = null;
        for (Map woMap : pageList) {
            Integer overtimeTypeId = (Integer) woMap.get("overtime_type_id");
            Long ruleId = (Long) woMap.get("rule_id");
            if (null != overtimeTypeId) {
                String typeName = overtimeTypeMap.get(overtimeTypeId);
                if (StringUtil.isNotBlank(typeName)) {
                    woMap.put("typeName", typeName);
                }
            }
            Integer timeUint = (Integer) woMap.get("time_unit");
            if (timeUint == null) {
                timeUint = 1;
            }
            Integer status = (Integer) woMap.get("status");
            Integer duration = (Integer) woMap.get("duration");
            if (duration == null) {
                duration = 0;
            }
            woMap.put("relTimeDuration", 0f);
            item = (List<Map>) tdMap.get(woMap.get("waid"));
            Float relTimeDuration = 0f;
            if (null != item && !item.isEmpty() && !StringUtil.isEmpty(item.get(0).get("rel_time_duration"))) {
                relTimeDuration = Float.valueOf(item.get(0).get("rel_time_duration").toString());
            }
            if (timeUint == 1) {
                woMap.put("relTimeDuration", relTimeDuration);
            } else {
                BigDecimal v = BigDecimal.valueOf(duration).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                woMap.put("duration", v.floatValue());
                BigDecimal v1 = BigDecimal.valueOf(relTimeDuration).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                woMap.put("relTimeDuration", v1.floatValue());
            }
            woMap.put("timeUnitName", PreTimeUnitEnum.getName(timeUint));
            Integer transferUint = Optional.ofNullable((Integer) woMap.get("transfer_unit")).orElse(0);
            if (transferUint == 0) {
                if (transferRuleMap.containsKey(ruleId)) {
                    OvertimeTransferRuleDto transferRule = transferRuleMap.get(ruleId);
                    transferUint = getUnit(transferRule);
                    woMap.put("transfer_unit", transferUint);
                }
            }
            woMap.put("statusName", ApprovalStatusEnum.getName(status));
            woMap.put("status", status);
            String key = "date_type";
            String keyName = "date_type_txt";
            if (woMap.containsKey(key)) {
                woMap.put(keyName, DateTypeEnum.getName(Integer.parseInt(woMap.get(key).toString())));
            }
            if (woMap.containsKey("compensate_type")) {
                woMap.put("compensate_type_txt", CompensateTypeEnum.getDescByOrdinal((Integer) woMap.get("compensate_type")));
            }
            woMap.put("businessKey", woMap.get("id") + "_" + BusinessCodeEnum.OVERTIME_ABOLISH.getCode());
        }
        return pageList;
    }

    @CDText(exp = {"travel_mode:travelModeName" + TextAspect.DICT_TRAVEL_MODE}, classType = Map.class)
    @Override
    public PageList<Map> getTravelWorkflowRevokeList(WorkflowRevokeReqDto dto, UserInfo userInfo) {
        Map map = new HashMap<>();
        PageBean pageBean = PageUtil.getPageBean(dto);
        List<FilterBean> list = pageBean.getFilterList();
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator<FilterBean> it = list.iterator();
            while (it.hasNext()) {
                FilterBean filterBean = it.next();
                if ("travel_time".equals(filterBean.getField())) {
                    if (StringUtils.isNotBlank(filterBean.getMin())) {
                        map.put("startDateTime", Long.valueOf(filterBean.getMin()));
                    }
                    if (StringUtils.isNotBlank(filterBean.getMax())) {
                        map.put("endDateTime", Long.valueOf(filterBean.getMax()));
                    }
                    it.remove();
                }
                if ("travel_mode".equals(filterBean.getField()) && StringUtil.isNotEmpty(filterBean.getMin())) {
                    map.put("travelMode", filterBean.getMin());
                    it.remove();
                }
            }
        }
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        map.put("filter", filter);
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            map.put("keywords", pageBean.getKeywords());
        }
        map.put("belongOrgId", userInfo.getTenantId());
        map.put("dataFilter", dto.getDataScope());
        map.put("moduleName", BusinessCodeEnum.TRAVEL_REVOKE.name());
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        PageList<Map> pageList = workflowRevokeDo.getTravelWorkflowRevokeList(pageBounds, map);
        return convert(pageList, BusinessCodeEnum.TRAVEL_REVOKE);
    }

    @CDText(exp = {"travel_mode:travelModeName" + TextAspect.DICT_TRAVEL_MODE}, classType = Map.class)
    @Override
    public PageList<Map> getTravelWorkflowAbolishList(WorkflowRevokeReqDto dto, UserInfo userInfo) {
        Map map = new HashMap<>();
        PageBean pageBean = PageUtil.getPageBean(dto);
        List<FilterBean> list = pageBean.getFilterList();
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator<FilterBean> it = list.iterator();
            while (it.hasNext()) {
                FilterBean filterBean = it.next();
                if ("travel_time".equals(filterBean.getField())) {
                    if (StringUtils.isNotBlank(filterBean.getMin())) {
                        map.put("startDateTime", Long.valueOf(filterBean.getMin()));
                    }
                    if (StringUtils.isNotBlank(filterBean.getMax())) {
                        map.put("endDateTime", Long.valueOf(filterBean.getMax()));
                    }
                    it.remove();
                }
                if ("travel_mode".equals(filterBean.getField()) && StringUtil.isNotEmpty(filterBean.getMin())) {
                    map.put("travelMode", filterBean.getMin());
                    it.remove();
                }
            }
        }
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        map.put("filter", filter);
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            map.put("keywords", pageBean.getKeywords());
        }
        map.put("belongOrgId", userInfo.getTenantId());
        map.put("dataFilter", dto.getDataScope());
        map.put("moduleName", BusinessCodeEnum.TRAVEL_ABOLISH.name());
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        PageList<Map> pageList = workflowRevokeDo.getTravelWorkflowRevokeList(pageBounds, map);
        return convert(pageList, BusinessCodeEnum.TRAVEL_ABOLISH);
    }

    private PageList<Map> convert(PageList<Map> pageList, BusinessCodeEnum workflowEnum) {
        if (CollectionUtils.isNotEmpty(pageList)) {
            for (Map item : pageList) {
                Integer timeUnit = Integer.valueOf(item.get("time_unit").toString());
                Float duration = new BigDecimal(item.get("time_duration").toString()).floatValue();
                item.put("timeUnitName", PreTimeUnitEnum.getName(timeUnit));
                if (timeUnit == 2) {
                    BigDecimal v = new BigDecimal(String.valueOf(duration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    item.put("time_duration", v.floatValue());
                }
                Integer status = (Integer) item.get("status");
                if (status != null) {
                    item.put("statusName", ApprovalStatusEnum.getName(status));
                }
                item.put("businessKey", String.format("%s_%s", item.get("id"), workflowEnum.getCode()));
                Integer periodType = item.get("period_type") != null ? Integer.valueOf(item.get("period_type").toString()) : null;
                Long shiftStartTime = (Long) item.get("shift_start_time");
                Long shiftEndTime = (Long) item.get("shift_end_time");
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    item.put("startDate", DateUtil.getDateStrByTimesamp(shiftStartTime));
                    item.put("endDate", DateUtil.getDateStrByTimesamp(shiftEndTime));
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    Long startTime = (Long) item.get("start_time");
                    Long endTime = (Long) item.get("end_time");
                    item.put("startDate", String.format("%s%s", DateUtil.getDateStrByTimesamp(startTime), DayHalfTypeEnum.getDesc(item.get("shalf_day").toString())));
                    item.put("endDate", String.format("%s%s", DateUtil.getDateStrByTimesamp(endTime), DayHalfTypeEnum.getDesc(item.get("ehalf_day").toString())));
                } else {
                    item.put("startDate", DateUtil.getTimeStrByTimesamp(shiftStartTime));
                    item.put("endDate", DateUtil.getTimeStrByTimesamp(shiftEndTime));
                }
                //地点
                SysUnitCity sysUnitCity = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_PROVINCE_KEY, Long.valueOf(item.get("province").toString()));
                SysUnitCity cityObj = CDCacheUtil.getSysUnitCity(RedisKeyDefine.SYS_CITY_KEY, Long.valueOf(item.get("city").toString()));
                if (sysUnitCity != null) {
                    item.put("provinceName", sysUnitCity.getChnName());
                }
                if (cityObj != null) {
                    item.put("cityName", cityObj.getChnName());
                }
                if (null != item.get("i18nTravelTypeName")) {
                    String i18n = LangParseUtil.getI18nLanguage(item.get("i18nTravelTypeName").toString(), null);
                    if (StringUtil.isNotBlank(i18n)) {
                        item.put("travelType", i18n);
                    }
                }
            }
        }
        return pageList;
    }

    /**
     * 撤销流程的撤销
     * @param dto
     * @param userInfo
     * @return
     * @throws Exception
     */
    @Override
    public Result<Boolean> revoke(RevokeDto dto, UserInfo userInfo) throws Exception {
        Optional<WaWorkflowRevokeDo> opt = Optional.ofNullable(workflowRevokeDo.selectByPrimaryKey(dto.getRevokeId()));
        if (!opt.isPresent()) {
            //流程单据不存在
            return ResponseWrap.wrapResult(AttendanceCodes.APPLY_NOT_EXIST, Boolean.FALSE);
        }
        WaWorkflowRevokeDo workflowRevoke = opt.get();
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(workflowRevoke.getStatus())) {
            return ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE);
        }
        if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(workflowRevoke.getStatus())) {
            // 审批中的可撤销
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_NOT_ALLOW, Boolean.FALSE);
        }
        //考勤截止日校验
        String msg = checkAttendanceDeadline(workflowRevoke);
        if (StringUtils.isNotBlank(msg)) {
            return Result.fail(msg);
        }
        if (workflowRevoke.getModuleName().equals(BusinessCodeEnum.TRAVEL_ABOLISH.getCode())) {
            //如果出差规则出差撤销流程支持废止，则需要进行流程发起
            Long entityId = workflowRevoke.getEntityId();
            Optional<WaEmpTravelDo> empTravelOpt = Optional.ofNullable(empTravelDo.getWaEmpTravelByPrimaryKey(entityId));
            if (!empTravelOpt.isPresent()) {
                return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TIME_NOT_EXIST, false);
            }
            WaEmpTravelDo empTravel = empTravelOpt.get();
            Optional<WaTravelTypeDo> travelTypeOpt = Optional.ofNullable(travelTypeService.selectOneById(empTravel.getTravelTypeId()));
            if (!travelTypeOpt.isPresent()) {
                return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE_NOT_EXIST,false);
            }
            if (ApprovalStatusEnum.PASSED.getIndex().equals(workflowRevoke.getStatus())) {
                //发起撤销的审批流程
                return wfService.revokeWorkflowBegin(userInfo, dto.getRevokeId(), dto.getRevokeReason(), BusinessCodeEnum.TRAVEL_ABOLISH, empTravel.getStartTime());
            }
        }
        return revokeRevokeWorkflow(workflowRevoke, userInfo, dto.getRevokeReason());
    }

    /**
     * 发起撤销流程的撤销
     * @param workflowRevoke
     * @param userInfo
     * @param revokeReason
     * @return
     */
    public Result<Boolean> revokeRevokeWorkflow(WaWorkflowRevokeDo workflowRevoke, UserInfo userInfo, String revokeReason) {
        //新工作流
        WfRevokeDto revokeDto = new WfRevokeDto();
        String businessKey = String.format("%s_%s", workflowRevoke.getId(), BusinessCodeEnum.getCodeByName(workflowRevoke.getModuleName()));
        revokeDto.setBusinessKey(businessKey);
        Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
        if (null == result || !result.isSuccess()) {
            return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE);
        }
        //更新单据状态
        workflowRevoke.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
        workflowRevoke.setRevokeReason(revokeReason);
        workflowRevoke.setUpdateBy(userInfo.getUserId());
        workflowRevoke.setUpdateTime(DateUtil.getCurrentTime(true));
        int row = workflowRevokeDo.update(workflowRevoke);
        if (row > 0 && BusinessCodeEnum.OVERTIME_REVOKE.name().equals(workflowRevoke.getModuleName())) {
            // 撤销加班的审批流，如有调休配额，则进行配额解冻
            Integer otId = workflowRevoke.getEntityId().intValue();
            WaEmpOvertime ot = empOvertimeMapper.selectByPrimaryKey(otId);
            if (null != ot) {
                Long overTimeDate = DateUtil.getOnlyDate(new Date(ot.getStartTime() * 1000));
                List<EmpCompensatoryQuotaDo> quotas = empCompensatoryQuotaDo.getQuotaByDate(userInfo.getTenantId(), Collections.singletonList(ot.getEmpid()), overTimeDate, overTimeDate, DataSourceEnum.AUTO.name(), Collections.singletonList(8));
                if (CollectionUtils.isNotEmpty(quotas)) {
                    quotas.forEach(quota -> quota.setStatus(2));
                    empCompensatoryQuotaDo.batchUpdate(quotas);
                }
            }
        }
        return Result.ok();
    }

    /**
     * 考勤截止日校验，超过考勤截止日不允许撤销
     *
     * @param workflowRevoke 撤销审批流
     * @return
     */
    private String checkAttendanceDeadline(WaWorkflowRevokeDo workflowRevoke) {
        String moduleName = workflowRevoke.getModuleName();
        Long entityId = workflowRevoke.getEntityId();
        Long empId = null;
        Long startTime = null;
        switch (moduleName) {
            case "OVERTIME_REVOKE":
                Integer otId = entityId.intValue();
                Optional<WaEmpOvertime> empOvertime = Optional.ofNullable(getEmpOvertime(otId));
                if (empOvertime.isPresent()) {
                    empId = empOvertime.get().getEmpid();
                    startTime = empOvertime.get().getStartTime();
                }
                break;
            case "TRAVEL_ABOLISH":
            case "TRAVEL_REVOKE":
                Long travelId = workflowRevoke.getEntityId();
                Optional<WaEmpTravelDo> empTravel = Optional.ofNullable(getEmpTravel(travelId));
                if (empTravel.isPresent()) {
                    empId = empTravel.get().getEmpId();
                    startTime = empTravel.get().getStartTime();
                }
                break;
            default:
                break;
        }
        if (empId == null || startTime == null) {
            return "";
        }
        //超过考勤截止日不允许撤销
        Long onlyDate = DateUtil.getOnlyDate();
        WaSob waSob = waSobService.getWaSob(empId, startTime);
        if (waSob != null) {
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            if (onlyDate > sobEndDate) {
                Integer sysPeriodMonth = waSob.getSysPeriodMonth();
                //return "申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员。";
                return String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]);
            }
        }
        return "";
    }

    /**
     * 查询加班业务数据
     *
     * @param otId 加班主键
     * @return
     */
    private WaEmpOvertime getEmpOvertime(Integer otId) {
        Optional<WaEmpOvertime> opt = Optional.ofNullable(empOvertimeMapper.selectByPrimaryKey(otId));
        return opt.orElse(null);
    }

    /**
     * 查询出差业务数据
     *
     * @param travelId 出差主键
     * @return
     */
    private WaEmpTravelDo getEmpTravel(Long travelId) {
        Optional<WaEmpTravelDo> opt = Optional.ofNullable(empTravelDo.getWaEmpTravelByPrimaryKey(travelId));
        return opt.orElse(null);
    }
}