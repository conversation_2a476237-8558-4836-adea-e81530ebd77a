package com.caidaocloud.attendance.service.application.service.workflow;

import com.caidao1.report.dto.PageBean;
import com.caidao1.workflow.api.RemoteWorkflowService;
import com.caidao1.workflow.common.WfConfigDto;
import com.caidao1.workflow.exception.WfNoUserException;
import com.caidao1.workflow.exception.WfTerminationException;
import com.caidao1.workflow.model.TaskExecutor;
import com.caidao1.workflow.model.WfEmpType;
import com.caidao1.workflow.mybatis.model.WfConfig;
import com.caidao1.workflow.mybatis.model.WfFunc;
import com.caidao1.workflow.mybatis.model.WfNodeConfig;
import com.caidao1.workflow.mybatis.model.WfTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 临时用于实现RemoteWorkflowService，后续会删掉
 */
@Service
public class WaWorkflowServiceImpl implements RemoteWorkflowService {
    @Override
    public List<Map> getWfTemplateList(Integer belongId, PageBean pageBean) {
        return null;
    }

    @Override
    public WfTemplate getWfTemplate(Integer id) {
        return null;
    }

    @Override
    public void deleteWfTemplate(Integer id) {

    }

    @Override
    public void publishWfTemplate(Integer id) throws Exception {

    }

    @Override
    public Integer saveWfTemplate(Integer belongId, Integer userId, WfTemplate record) throws Exception {
        return null;
    }

    @Override
    public List<Map> getWfConfigList(Integer belongId, Integer empId, PageBean pageBean) {
        return null;
    }

    @Override
    public Map getWfConfig(Integer id) throws Exception {
        return null;
    }

    @Override
    public void deleteWfConfig(Integer id) {

    }

    @Override
    public Integer saveWfConfig(Integer belongId, Integer userId, WfConfig config, WfConfigDto configDto) throws Exception {
        return null;
    }

    @Override
    public void deleteWfConfig(Integer belongId, Integer id) {

    }

    @Override
    public void deployWfConfig(Integer belongId, Integer id) {

    }

    @Override
    public void pendWfConfig(Integer userId, Integer id) {

    }

    @Override
    public Map getWfConfigByUrl(Integer belongId, String url) {
        return null;
    }

    @Override
    public Map getWfConfigByType(Integer belongId, Integer type) {
        return null;
    }

    @Override
    public boolean beginWorkflow(Integer corpId, Integer belongId, String businessKey, Integer funcType, Map<String, String> paramMap) throws Exception {
        return false;
    }

    @Override
    public boolean beginWorkflow(Integer corpId, Integer belongId, String businessKey, String path, Map<String, String> paramMap) throws Exception {
        return false;
    }

    @Override
    public void startWorkflow(Integer corpId, Integer belongId, Integer wfConfigId, Integer funcId, String key, Integer startEmp, String businessKey, Integer initiator, Map<String, Object> variables) throws Exception {

    }

    @Override
    public void startWorkflow(Integer corpId, Integer belongId, Integer wfConfigId, Integer funcId, String key, Integer startEmp, String businessKey, Integer initiator, Map<String, Object> variables, boolean openApi) throws Exception {

    }

    @Override
    public List<Map> getIdentityListByNode(Integer belongId, String businessKey, String nodeId) {
        return null;
    }

    @Override
    public List<Map> getAboutMeTaskList(PageBean pageBean, Integer userId, Integer empId, Integer belongId, String status, String keyword) {
        return null;
    }

    @Override
    public List<Map> getTodoList(PageBean pageBean, Integer userId, Integer empId, Integer belongId, String funcType, String keyword) {
        return null;
    }

    @Override
    public List<Map> getTodoList(PageBean pageBean, Integer belongId, String funcType, String keyword, String datafilter) {
        return null;
    }

    @Override
    public List getOwnerTaskList(Integer belongId, PageBean pageBean, Integer empId, String status, String funcType, String keyword) {
        return null;
    }

    @Override
    public List getOwnerTaskList(Integer belongId, PageBean pageBean, String status, String funcType, String keyword, String datafilter) {
        return null;
    }

    @Override
    public List getOwnerProcList(Integer belongId, PageBean pageBean, Integer empId, String status, String keyword) {
        return null;
    }

    @Override
    public List getUserTaskList(Integer belongId, PageBean pageBean, Integer empId, String status, String funcType, String keyWord) {
        return null;
    }

    @Override
    public List getEmpApprovaledList(Integer belongId, PageBean pageBean, Integer empId, String status, String keyWord) {
        return null;
    }

    @Override
    public void completeTaskList(Integer belongId, String[] taskIdList, String choice, String comment, String filePath, Integer agent, Integer assignee, Integer assigneeUser) throws Exception {

    }

    @Override
    public void completeTask(Integer belongId, String taskId, String choice, String comment, Integer agent, String filePath, Integer assignee, Integer assigneeUser, Integer nextAssignee, Integer addAssignee) throws Exception {

    }

    @Override
    public void completeTask(Integer belongId, String taskId, String choice, String comment, Integer agent, String filePath, Integer assignee, Integer assigneeUser, Integer nextAssignee, Integer addAssignee, boolean openApi) throws Exception {

    }

    @Override
    public void stopTask(String taskId, Integer userId) {

    }

    @Override
    public void stopTask(String taskId, Integer userId, boolean openApi) {

    }

    @Override
    public void stopTaskByBusinessKey(String businessKey, Integer userId) {

    }

    @Override
    public void stopTaskById(String id, Integer funcType, Integer belongid, Integer userId) {

    }

    @Override
    public WfFunc getFuncByProcInst(String processInstanceId) {
        return null;
    }

    @Override
    public WfNodeConfig getNodeConfigById(Integer belongId, Integer funcId, String nodeId) {
        return null;
    }

    @Override
    public List<Map> getUserListByExecutor(Integer belongId, TaskExecutor taskExecutor) {
        return null;
    }

    @Override
    public List<Map> traceWorkflow(Integer belongId, String businessKey) {
        return null;
    }

    @Override
    public List<Map> getWorkflowHisList(Integer belongId, String businessKey, String procInstId) {
        return null;
    }

    @Override
    public List<Map> getLastestWorkflowHisList(Integer belongId, String businessKey) {
        return null;
    }

    @Override
    public List<Map> getLastestWorkflowHisList(Integer belongId, String bKey, String funcType) {
        return null;
    }

    @Override
    public List<Map> traceWorkflow(Integer belongId, String businessKey, String workflowPath) throws Exception {
        return null;
    }

    @Override
    public List<Map> getNextApprovaler(Integer corpId, Integer belongId, String businessKey, String workflowPath) {
        return null;
    }

    @Override
    public Map getWfNotice(Integer id) {
        return null;
    }

    @Override
    public List<Map> getWfNoticeList(Integer belongId, Integer empid) {
        return null;
    }

    @Override
    public List<TaskExecutor> getTaskExecutorList(Integer corpId, Integer belongId, String businessKey, String nodeId, String nodeName, String startUser, Integer initiator, String executionId) throws WfNoUserException, WfTerminationException {
        return null;
    }

    @Override
    public List<TaskExecutor> getTaskExecutorList(Integer corpId, Integer belongId, String businessKey, String nodeId, String nodeName, String startUser, Integer initiator, String executionId, boolean ingoreTermination) throws WfNoUserException, WfTerminationException {
        return null;
    }

    @Override
    public void replaceTaskEmp(Integer belongId, Integer empid, Integer replace) {

    }

    @Override
    public List<Map> getWfFuncList(Integer belongId) {
        return null;
    }

    @Override
    public WfFunc getWfFuncById(Integer id) {
        return null;
    }

    @Override
    public WfFunc getWfFunc(Integer belongid, String funcType) {
        return null;
    }

    @Override
    public WfFunc getWfFuncByForm(Integer formId) {
        return null;
    }

    @Override
    public Integer saveWfFunc(Integer belongId, Integer userid, WfFunc record) throws Exception {
        return null;
    }

    @Override
    public void sendWrokflowEmail(List<Integer> empidList, String title, String message, Integer belongid, boolean byEmp, Map params) {

    }

    @Override
    public void noticeNodeUser(Integer corpId, Integer belongId, String businessKey, Integer startEmp, String nodeId, List<TaskExecutor> executors, String type) {

    }

    @Override
    public Map getWfFuncDetail(Integer corpId, String businessKey, String nodeId) throws Exception {
        return null;
    }

    @Override
    public List<Integer> getHistoryOwnerList(Integer belongId, String businessKey, String processInstanceId) {
        return null;
    }

    @Override
    public void revokeDataAndTask(String businessKey, Integer empid, Integer userId, boolean onlyself) throws Exception {

    }

    @Override
    public void revokeDataAndTask(String businessKey, Integer empid, Integer userId, boolean onlyself, boolean openApi) throws Exception {

    }

    @Override
    public void revokeDataAndTask(String businessKey, Integer empid, Integer userId) throws Exception {

    }

    @Override
    public void initTask() {

    }

    @Override
    public List<Map> getTaskNodeEmpList(Integer corpId, Integer belongId, String funcType, String nodeId, String startUser, Integer initiator, Integer identityKind) {
        return null;
    }

    @Override
    public List<Map> getNextTaskNodeEmpList(Integer corpId, Integer belongId, String funcType, String taskId, Integer assignee) {
        return null;
    }

    @Override
    public List<Map> getNodeNameList(Integer belongId, String funcType, Integer startEmp) {
        return null;
    }

    @Override
    public void assignNodeUser(Integer belongId, Integer businessKey, Integer startEmp, Integer funcId, String nodeUsers) throws Exception {

    }

    @Override
    public List<Map> getWfFuncList(Integer belongId, Integer empid, WfEmpType wfEmpType) {
        return null;
    }

    @Override
    public void setInApprovalStatus(String sys_emp_changes, String approval_status, String idName, Object idVal) {

    }

    @Override
    public Map<String, List<Map>> getNextApprovers(Integer corpId, Integer belongId, Integer assignee, String taskId, String funcType, String nodeId, Integer applicant, Integer initiator) {
        return null;
    }
}
