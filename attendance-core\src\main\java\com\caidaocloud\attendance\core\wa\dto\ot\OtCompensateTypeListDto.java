package com.caidaocloud.attendance.core.wa.dto.ot;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OtCompensateTypeListDto {
    @ApiModelProperty("补偿方式名称")
    private String text;
    @ApiModelProperty("补偿方式ID")
    private Object value;
    @ApiModelProperty("加班说明")
    private String description;
    private Integer compensateType;
}
