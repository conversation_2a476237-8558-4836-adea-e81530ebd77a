package com.caidaocloud.attendance.core.commons.utils;

import java.util.ArrayList;
import java.util.List;

public class ListPaginationUtils {
    public static <T> List<T> getPaginatedList(List<T> list, int pageNo, int pageSize) {
        if (list == null || list.isEmpty() || pageNo <= 0) {
            return new ArrayList<>(); // 返回空列表，避免异常
        }

        if (pageSize == -1) {
            return new ArrayList<>(list); // pageSize 为 -1 时，返回所有数据
        } else if (pageSize <= 0) {
            return new ArrayList<>();
        }

        int totalSize = list.size();
        int fromIndex = (pageNo - 1) * pageSize; // 计算起始索引
        if (fromIndex >= totalSize) {
            return new ArrayList<>(); // 页号超出范围，返回空列表
        }

        int toIndex = Math.min(fromIndex + pageSize, totalSize);
        return list.subList(fromIndex, toIndex);
    }
}

