package com.caidaocloud.attendance.core.wa.dto;

public class MobileEmpPrivacyBean {
	private String empid;
	private String nationality; //国籍
	private String passport; //护照号码
	private String visa_expire_date; //护照有效期
	private String nation; //民族
	private String card_no; //身份证号码
	private String birthdate; //出生日期
	private String huji_address; //户籍地址
	private String marriage; //婚姻状态
	private String contact_name; //紧急联系人
	private String contact_tel; //紧急联系人电话
	private String politics;//政治面貌
	
	
	public String getEmpid() {
		return empid;
	}
	public void setEmpid(String empid) {
		this.empid = empid;
	}
	public String getNationality() {
		return nationality;
	}
	public void setNationality(String nationality) {
		this.nationality = nationality;
	}
	public String getPassport() {
		return passport;
	}
	public void setPassport(String passport) {
		this.passport = passport;
	}
	public String getVisa_expire_date() {
		return visa_expire_date;
	}
	public void setVisa_expire_date(String visa_expire_date) {
		this.visa_expire_date = visa_expire_date;
	}
	public String getNation() {
		return nation;
	}
	public void setNation(String nation) {
		this.nation = nation;
	}
	public String getCard_no() {
		return card_no;
	}
	public void setCard_no(String card_no) {
		this.card_no = card_no;
	}
	public String getBirthdate() {
		return birthdate;
	}
	public void setBirthdate(String birthdate) {
		this.birthdate = birthdate;
	}
	public String getHuji_address() {
		return huji_address;
	}
	public void setHuji_address(String huji_address) {
		this.huji_address = huji_address;
	}
	public String getMarriage() {
		return marriage;
	}
	public void setMarriage(String marriage) {
		this.marriage = marriage;
	}
	public String getContact_name() {
		return contact_name;
	}
	public void setContact_name(String contact_name) {
		this.contact_name = contact_name;
	}
	public String getContact_tel() {
		return contact_tel;
	}
	public void setContact_tel(String contact_tel) {
		this.contact_tel = contact_tel;
	}
	public String getPolitics() {
		return politics;
	}
	public void setPolitics(String politics) {
		this.politics = politics;
	}
	
	
}
