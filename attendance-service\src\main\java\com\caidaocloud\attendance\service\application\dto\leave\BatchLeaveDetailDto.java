package com.caidaocloud.attendance.service.application.dto.leave;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量休假详情
 *
 * <AUTHOR>
 * @Date 2024/6/27
 */
@Data
public class BatchLeaveDetailDto {
    @ApiModelProperty("开始日期")
    private String startDate;
    @ApiModelProperty("结束日期")
    private String endDate;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("事由")
    private String reason;
    @ApiModelProperty("时长")
    private String timeDuration;
    @ApiModelProperty("审批状态")
    private Short status;
    @ApiModelProperty("审批状态名称")
    private String statusTxt;

    @ApiModelProperty("假期类型ID")
    private Integer leaveTypeId;
    @ApiModelProperty("假期类型名称")
    private String leaveTypeName;
}
