package com.caidaocloud.attendance.service.application.enums;

/**
 * 考勤方案：有效打卡取值规则
 */
public enum ParseGroupClockRuleEnum {
    SIGN_TIME_RANGE(1, "在班次打卡时段内存在打卡、补卡记录"),
    WORK_TIME_RANGE(2, "在班次上班时间-下班时间内存在打卡、补卡记录");

    private Integer index;

    private String name;

    ParseGroupClockRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ParseGroupClockRuleEnum c : ParseGroupClockRuleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
