package com.caidaocloud.attendance.core.annoation.feign;


import com.caidaocloud.web.Result;

/**
 * <AUTHOR>
 */
public class BccServiceFeignFallback implements BccServiceFeignClient {
    @Override
    public Result getEnableDictList(String typeCode, String belongModule) {
        return Result.fail();
    }

    @Override
    public Result<Boolean> refreshSystemDict() {
        return Result.fail();
    }
}
