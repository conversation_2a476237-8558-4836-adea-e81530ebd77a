package com.caidaocloud.attendance.core.config.annotation;

import org.springframework.context.support.EmbeddedValueResolutionSupport;
import org.springframework.format.AnnotationFormatterFactory;
import org.springframework.format.Parser;
import org.springframework.format.Printer;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020-12-16
 */
public class CDDateTimeFormatAnnotationFormatterFactory extends EmbeddedValueResolutionSupport implements AnnotationFormatterFactory<CDDateTimeFormat> {
    @Override
    public Set<Class<?>> getFieldTypes() {
        Set<Class<?>> set = new HashSet<Class<?>>();
        set.add(String.class);
        set.add(Long.class);
        return set;
    }

    @Override
    public Printer<?> getPrinter(CDDateTimeFormat cdDateTimeFormat, Class<?> aClass) {
        CDDateTimeFormatter dateTimeFormatter = new CDDateTimeFormatter();
        dateTimeFormatter.setPattern(cdDateTimeFormat.pattern());
        return dateTimeFormatter;
    }

    @Override
    public Parser<?> getParser(CDDateTimeFormat cdDateTimeFormat, Class<?> aClass) {
        CDDateTimeFormatter booleanFormatter = new CDDateTimeFormatter();
        booleanFormatter.setPattern(cdDateTimeFormat.pattern());
        return booleanFormatter;
    }
}
