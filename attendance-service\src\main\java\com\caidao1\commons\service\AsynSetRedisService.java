package com.caidao1.commons.service;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class AsynSetRedisService {
    private final static Logger LOGGER = LoggerFactory.getLogger(AsynSetRedisService.class);

    public void setEmpWorkTimeRedis(SysEmpInfo oldemp, SysEmpInfo empInfo) {
        String comma = ",";

        // 先删除旧的
        if (oldemp != null) {
            String oldkey = BaseConst.EMP_ + oldemp.getBelongOrgId() + "_" + oldemp.getWorkno();
            CDCacheUtil.delRedisKey(oldkey);
        }

        String workno = empInfo.getWorkno();
        if (StringUtil.isNullOrTrimEmpty(workno) && null != oldemp) {
            workno = oldemp.getWorkno();
        }

        Integer tmType = empInfo.getTmType();
        // 考勤类型 默认是1 正常考勤 2 门店考勤
        if (empInfo.getTmType() == null) {
            tmType = 1;
        }

        String site = StringUtils.trimToEmpty(empInfo.getSiteids());
        if (StringUtils.isNotBlank(site)) {
            site = site.replace(comma, "#");
        }

        String key = BaseConst.EMP_ + empInfo.getBelongOrgId() + "_" + workno;
        String value = empInfo.getEmpid() + comma + empInfo.getEmpName() + comma + tmType + comma + site;

        // 插入新的
        CDCacheUtil.setRedisKeyValue(key, value);
        LOGGER.info("set cache key = [{}] value = [{}]", key, value);
    }

    @Async
    public void setKey(String key, String value) {
        LOGGER.info("async set cache key start, key = [{}] value = [{}]", key, value);
        CDCacheUtil.setRedisKeyValue(key, value);
        LOGGER.info("async set cache key end, key = [{}] value = [{}]", key, value);
    }
}
