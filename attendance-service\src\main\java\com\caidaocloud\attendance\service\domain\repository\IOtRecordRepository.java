package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.dto.EmpOvertimeDto;
import com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/21
 */
public interface IOtRecordRepository {
    WaEmpOvertimeDo getOtDetailById(Long corpid, Long otId);

    List<WaEmpOvertimeDo> getEmpOvertimeListByYmdDate(Long empid, Long startDate, Long endDate);

    PageResult<WaEmpOvertimeDo> getEmpOvertimePageList(BasePage basePage, WaEmpOvertimeDo search);

    List<WaEmpOvertimeDo> getOverTimeList(Long empid, Long startDate, Long endDate);

    List<WaEmpOvertimeDo> getEmpOvertimeListByBelongDate(Long empId, Long startDate, Long endDate);

    PageResult<WaEmpOvertimeDo> getPageListOfPortal(QueryPageBean queryPageBean);

    Integer checkOvertimeTypeUsed(String tenantId, Integer overtimeTypeId);

    List<WaEmpOvertimeDo> getEmpOvertimes(String tenantId, Long empId, Long startDate, Long endDate, Integer overtimeTypeId);

    WaEmpOvertimeDo getOtRevokeDetailById(String tenantId, Long id);

    PageResult<WaEmpOvertimeDo> getRevokePageListOfPortal(QueryPageBean queryPageBean, BusinessCodeEnum workflowEnum);

    List<WaEmpOvertimeDo> getEmpOtCompensatoryList(String tenantId, Long quotaId);

    List<WaEmpOvertimeDo> selectListByBatchId(Long batchId);

    List<EmpOvertimeDto> listOvertimeByEmps(String tenantId, long startTime, long endTime, List<String> empIds);
}