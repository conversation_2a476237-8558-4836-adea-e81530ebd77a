package com.caidaocloud.attendance.core.config;

import com.alibaba.fastjson.parser.ParserConfig;
import com.caidao1.commons.converter.BaseViewEnumConverter;
import com.caidaocloud.attendance.core.commons.filter.MyXssFilter;
import com.caidaocloud.attendance.core.config.annotation.CDDateTimeFormatAnnotationFormatterFactory;
import com.caidaocloud.attendance.core.mobile.filter.MobUserAccessInterceptor;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.support.ErrorPageFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.view.InternalResourceViewResolver;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * SpringBoot 1.5.x 中的 WebMvcConfigurerAdapter 已过时
 * 这里把已过时的 WebMvcConfigurerAdapter 替换为 SpringBoot 2.x 中的 WebMvcConfigurer
 */
@Configuration
public class MvcConfig implements WebMvcConfigurer {
    @Autowired
    private MobUserAccessInterceptor mobUserAccessInterceptor;

    @Bean
    public ViewResolver viewResolver() {
        InternalResourceViewResolver viewResolver = new InternalResourceViewResolver();
        viewResolver.setPrefix("/WEB-INF/jsp/");
        viewResolver.setSuffix(".jsp");
        return viewResolver;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        ParserConfig.getGlobalInstance().setSafeMode(true);
        // 注册监控拦截器
        registry.addInterceptor(mobUserAccessInterceptor)
            .addPathPatterns(
                "/mobile/**",
                "/mobileLeave/**",
                "/mobileLeave2/**",
                "/mobilePersonnel/**",
                "/mobileAch/**",
                "/mobileToken/**",
                "/mobileV16/**",
                "/mobileV18/**",
                "/mobileV16Employee/**",
                "/mobileWorkflow/**",
                "/bonus/**",
                "/wechart/**")
            .excludePathPatterns("/mobile/auth/**");
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(0, new MappingJackson2HttpMessageConverter());
    }

    @Bean
    public ErrorPageFilter errorPageFilter() {
        return new ErrorPageFilter();
    }

    @Bean
    public FilterRegistrationBean disableSpringBootErrorFilter(ErrorPageFilter filter) {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(filter);
        filterRegistrationBean.setEnabled(false);
        return filterRegistrationBean;
    }

    /**
     * xss过滤拦截器
     */
    @Bean
    @ConditionalOnProperty(name = "xssfilter.open", havingValue = "true")
    public FilterRegistrationBean xssFilterRegistrationBean() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new MyXssFilter());
        filterRegistrationBean.setOrder(1);
        filterRegistrationBean.setEnabled(true);
        filterRegistrationBean.addUrlPatterns("/*");
        Map<String, String> initParameters = Maps.newHashMap();
        List<String> excludes = Arrays.asList("/login", "/logout", "/assets", "/html", "/pre", "/favicon.ico", "/img/*", "/js/*", "/css/*");
        initParameters.put("excludes", String.join(",", excludes));
        initParameters.put("isIncludeRichText", "true");
        filterRegistrationBean.setInitParameters(initParameters);
        return filterRegistrationBean;
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverterFactory(new BaseViewEnumConverter());
        registry.addFormatterForFieldAnnotation(new CDDateTimeFormatAnnotationFormatterFactory());
    }

}

