package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.sdk.dto.SdkCompensatoryCaseDTO;
import com.caidaocloud.attendance.sdk.dto.SdkCompensatoryRevokeDTO;
import com.caidaocloud.attendance.sdk.feign.fallback.CompensatoryFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = CompensatoryFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "compensatoryFeignClient")
public interface ICompensatoryFeignClient {
    /**
     * 调休付现申请
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/attendance/empCompensatory/v1/saveApply")
    Result<?> saveCompensatoryCaseApply(@RequestBody SdkCompensatoryCaseDTO dto);

    /**
     * 撤销调休付现申请
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/attendance/empCompensatory/v1/revoke")
    Result<?> revokeCompensatoryCaseApply(@RequestBody SdkCompensatoryRevokeDTO dto);

    @PostMapping("/api/attendance/user/v1/getCompensatoryApplyRecordList")
    Result<?> getEmpCompensatoryCaseList(Map<String, Object> parameter);

    /**
     * 可申请调休配额
     * @return
     */
    @GetMapping("/api/attendance/user/v1/myCompensatoryQuota")
    Result<?> myCompensatoryQuota();
}
