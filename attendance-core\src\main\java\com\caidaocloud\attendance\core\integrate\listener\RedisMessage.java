package com.caidaocloud.attendance.core.integrate.listener;

import java.io.Serializable;
import java.util.Map;

/**
 * Created by darren on 16/10/26.
 */
public class RedisMessage implements Serializable {

    private Long corpId;
    private String belongId;
    private String msg;
    private Integer id;
    private Map<String, Object> params;

    public Long getCorpId() {
        return corpId;
    }

    public void setCorpId(Long corpId) {
        this.corpId = corpId;
    }

    public String getBelongId() {
        return belongId;
    }

    public void setBelongId(String belongId) {
        this.belongId = belongId;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
}