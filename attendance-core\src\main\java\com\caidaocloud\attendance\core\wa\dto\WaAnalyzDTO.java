package com.caidaocloud.attendance.core.wa.dto;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.wa.mybatis.model.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class WaAnalyzDTO {

	private final static Logger log = LoggerFactory.getLogger(WaAnalyzDTO.class);
	//班次信息
	private Map<Integer, WaShiftDef> shiftDefMap;
	//公司所有班次信息
	private Map<Integer,WaShiftDef> corpShiftDefMap;
	private Integer clockType;
	// 每个员工所属的考勤分组对应的考勤分析分组
	private List<WaAnalyzInfo> empAnalyzInfos;
	// 默认考勤分组对应的考勤分析分组对象
	private WaAnalyzInfo defaultAnalyz;
	//每个员工所对应的考勤分组及考勤分析分析分组
	private Map<Long,List<WaAnalyzInfo>> empAnalyzMaps = new HashMap<>();

	private WaSob waSob;
	
	private String belongid;
	
	private Long corpid;

	private Long userId;
	
	private Map<Integer,WaLeaveType> ltMaps = new HashMap<>();
	private Map<Integer,WaOvertimeType> otMaps = new HashMap<>();

	private Map<String,EmpShiftInfo> empShift = new HashMap<>();

	private Map<String,EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();

    //异常申诉工时-缺工时补请数据
	private Map<String,EmpAbnormalWorkTime> empAbnormalWorkTimeMap;

	private List<WaAnalyze> waAnalyzeList;

    //实际加班时长
	private Map<String,Integer> relOtTimeDurationMap;

	private Map<Integer,Map<Integer,WaOvertimeType>> waGroupOtRuleMap;

    //员工外勤签到记录（有打卡地点的）
	private Map<String,List<WaRegisterRecord>> empOutRegMap;

	private Map<Long,SysEmpInfo> empInfoMap;

	// 打卡记录（记录一次卡&不打卡员工打卡记录）
	private Map<String, List<Map>> registerRecordMap;

	//一次卡员工打卡记录
	private Map<String, List<WaRegisterRecord>> signOnceRegisterRecordMap;

	//不打卡员工打卡记录
	private Map<String, List<WaRegisterRecord>> signZeroRegisterRecordMap;

	public Map<String, List<Map>> getRegisterRecordMap() {
		return registerRecordMap;
	}

	public void setRegisterRecordMap(Map<String, List<Map>> registerRecordMap) {
		this.registerRecordMap = registerRecordMap;
	}

	public Map<String, List<WaRegisterRecord>> getEmpOutRegMap() {
		return empOutRegMap;
	}

	public void setEmpOutRegMap(Map<String, List<WaRegisterRecord>> empOutRegMap) {
		this.empOutRegMap = empOutRegMap;
	}

    //缺少外勤打卡 or 外勤打卡地址异常 的请假数据
	private List<WaEmpLeave> invalidLeaves = null;
	public List<WaEmpLeave> getInvalidLeaves() {
		return invalidLeaves;
	}

	public void setInvalidLeaves(List<WaEmpLeave> invalidLeaves) {
		this.invalidLeaves = invalidLeaves;
	}

	public Map<String, Integer> getRelOtTimeDurationMap() {
		return relOtTimeDurationMap;
	}

	public void setRelOtTimeDurationMap(Map<String, Integer> relOtTimeDurationMap) {
		this.relOtTimeDurationMap = relOtTimeDurationMap;
	}

	public List<WaAnalyze> getWaAnalyzeList() {
		return waAnalyzeList;
	}

	// 请假记录
	@Getter
	private List<EmpLeaveInfo> empLeaveInfoList;

	private Map<String,List<EmpLeaveInfo>> empLeaveMap;

	// 获取员工的请假记录
	public List<EmpLeaveInfo> getEmpLeaveInfoByDateEmpId(Long empid,Long belongDate){
		if(empLeaveMap == null){
			return null;
		}
		return empLeaveMap.get(empid+"_"+belongDate);
	}

	/**
	 * @param empLeaves
	 */
	public void setEmpLeaveInfo(List<Map> empLeaves){
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

		empLeaveMap = new HashMap<>();

		List<EmpLeaveInfo> list = new ArrayList<>();
		EmpLeaveInfo lf = null;
		String key = null;
		String json = null;
		for (Map leavemap : empLeaves) {
			lf = new EmpLeaveInfo();
			try {
				json = objectMapper.writeValueAsString(leavemap);
				lf = objectMapper.readValue(json, new TypeReference<EmpLeaveInfo>() {});
				list.add(lf);
				// 按员工+日期归类
				Long belongdata = (lf.getLeave_date() - (lf.getLeave_date()+28800)%86400);
				key = lf.getEmpid()+"_"+belongdata;
				if(empLeaveMap.containsKey(key)){
					empLeaveMap.get(key).add(lf);
				}else{
					List<EmpLeaveInfo> lts = new ArrayList<>();
					lts.add(lf);
					empLeaveMap.put(key,lts);
				}
			} catch (Exception e) {
				log.error("setEmpLeaveInfo err,{}", e.getMessage(), e);
			}
		}
		if(CollectionUtils.isNotEmpty(list)) {
			this.empLeaveInfoList = list;
		}
	}

	public void setWaAnalyzeList(List<WaAnalyze> waAnalyzeList) {
		this.waAnalyzeList = waAnalyzeList;
	}

	public void setEmpShiftInfoByDateMap(Map<String, EmpShiftInfo> empShiftInfoByDateMap) {
		this.empShiftInfoByDateMap = empShiftInfoByDateMap;
	}

	/**
	 * @param empShift
	 */
	public void setEmpShift(Map<String, EmpShiftInfo> empShift) {
		this.empShift = empShift;
	}
	public Map<String,EmpShiftInfo> getEmpShift() {
		return this.empShift;
	}
	public EmpShiftInfo getEmpShift(Long empid,Integer shiftDefId,Long workDate) {
		String key = empid + "_" +shiftDefId + "_" + workDate;
		return this.empShift.get(key);
	}

	/**
	 *  传递empid和日期获得排班
	 * @param empid
	 * @param workDate
	 * @return
	 */
	public EmpShiftInfo getEmpShiftByDate(Long empid,Long workDate) {
		String key = empid + "_" + workDate;
		EmpShiftInfo shiftInfo = this.empShiftInfoByDateMap.get(key);
		return shiftInfo;
	}

	/**
	 *  传递empid和日期获得
	 * @param empid
	 * @return
	 */
	public WaAnalyze getEmpAnalzyeByDate(Long empid,Long belongData) {
		if(CollectionUtils.isNotEmpty(this.getWaAnalyzeList())){
			for(WaAnalyze analyze : this.getWaAnalyzeList()){
				if(analyze.getEmpid().toString().equals(empid.toString()) && analyze.getBelongDate().toString().equals(belongData)){
					return analyze;
				}
			}
		}
		return null;
	}

	public Map<Integer, WaLeaveType> getLtMaps() {
		return ltMaps;
	}
	public Map<Integer, WaOvertimeType> getOtMaps() {
		return otMaps;
	}
	public String getBelongid() {
		return belongid;
	}
	public void setBelongid(String belongid) {
		this.belongid = belongid;
	}
	public Long getCorpid() {
		return corpid;
	}
	public void setCorpid(Long corpid) {
		this.corpid = corpid;
	}
	public List<WaAnalyzInfo> getEmpAnalyzInfos() {
		return empAnalyzInfos;
	}
	public void setEmpAnalyzInfos(List<WaAnalyzInfo> empAnalyzInfos) {
		if(empAnalyzInfos != null && empAnalyzInfos.size() > 0){
			for (WaAnalyzInfo waAnalyzInfo : empAnalyzInfos) {
				Long empid = waAnalyzInfo.getEmpid();
				if(empAnalyzMaps.containsKey(empid)){
					empAnalyzMaps.get(empid).add(waAnalyzInfo);
				}else{
					List<WaAnalyzInfo> list = new ArrayList<WaAnalyzInfo>();
					list.add(waAnalyzInfo);
					empAnalyzMaps.put(empid, list);
				}
			}
		}
		this.empAnalyzInfos = empAnalyzInfos;
	}
	public WaAnalyzInfo getDefaultAnalyz() {
		return defaultAnalyz;
	}
	public void setDefaultAnalyz(WaAnalyzInfo defaultAnalyz) {
		this.defaultAnalyz = defaultAnalyz;
	}
	public Map<Integer, WaShiftDef> getShiftDefMap() {
		return shiftDefMap;
	}
	public void setShiftDefMap(Map<Integer, WaShiftDef> shiftDefMap) {
		this.shiftDefMap = shiftDefMap;
	}

	/**
	 * 根据员工id 获取员工所属的考勤分组对应的考勤分析分组如果没有对应的考勤分组，则取默认考勤分组
	 * @param empid
	 * @param date
	 * @return
	 */
	public WaAnalyzInfo getEmpWaAnalyz(Long empid,Long date){
		List<WaAnalyzInfo> infos =  empAnalyzMaps.get(empid);
		if(infos != null && infos.size() > 0){
			for (WaAnalyzInfo waAnalyzInfo : infos) {
				// 如果是按月统计时 date传null 默认取第一个考勤周期作为他的分析依据 当没有所属考勤分组时 取默认考勤分组的对应的考勤分析分组
				if(date == null) {
                    return waAnalyzInfo;
                }

				if(date >= waAnalyzInfo.getStart_time() && date <= waAnalyzInfo.getEnd_time()){
					if(waAnalyzInfo.getLv_parse()==null){
						waAnalyzInfo.setLv_parse(false);
					}
					if(waAnalyzInfo.getOt_parse()==null){
						waAnalyzInfo.setOt_parse(false);
					}
					log.debug("***************************************匹配到的考勤分析分组:empid="+empid+";date="+date);
					return waAnalyzInfo;
				}
			}
		}
		log.debug("***************************************获取到默认的考勤分析分组:"+defaultAnalyz);
		return defaultAnalyz;
	}

	public WaSob getWaSob() {
		return waSob;
	}
	public void setWaSob(WaSob waSob) {
		this.waSob = waSob;
	}

	public Map<String, EmpAbnormalWorkTime> getEmpAbnormalWorkTimeMap() {
		return empAbnormalWorkTimeMap;
	}

	public void setEmpAbnormalWorkTimeMap(Map<String, EmpAbnormalWorkTime> empAbnormalWorkTimeMap) {
		this.empAbnormalWorkTimeMap = empAbnormalWorkTimeMap;
	}

	public Map<Long, SysEmpInfo> getEmpInfoMap() {
		return empInfoMap;
	}

	public void setEmpInfoMap(Map<Long, SysEmpInfo> empInfoMap) {
		this.empInfoMap = empInfoMap;
	}

	public Map<Integer, WaShiftDef> getCorpShiftDefMap() {
		return corpShiftDefMap;
	}

	public void setCorpShiftDefMap(Map<Integer, WaShiftDef> corpShiftDefMap) {
		this.corpShiftDefMap = corpShiftDefMap;
	}

	public Integer getClockType() {
		return clockType;
	}

	public void setClockType(Integer clockType) {
		this.clockType = clockType;
	}

	public Map<Integer, Map<Integer, WaOvertimeType>> getWaGroupOtRuleMap() {
		return waGroupOtRuleMap;
	}

	public void setWaGroupOtRuleMap(Map<Integer, Map<Integer, WaOvertimeType>> waGroupOtRuleMap) {
		this.waGroupOtRuleMap = waGroupOtRuleMap;
	}

	public Map<String, List<WaRegisterRecord>> getSignOnceRegisterRecordMap() {
		return signOnceRegisterRecordMap;
	}

	public void setSignOnceRegisterRecordMap(Map<String, List<WaRegisterRecord>> signOnceRegisterRecordMap) {
		this.signOnceRegisterRecordMap = signOnceRegisterRecordMap;
	}

	public Map<String, List<WaRegisterRecord>> getSignZeroRegisterRecordMap() {
		return signZeroRegisterRecordMap;
	}

	public void setSignZeroRegisterRecordMap(Map<String, List<WaRegisterRecord>> signZeroRegisterRecordMap) {
		this.signZeroRegisterRecordMap = signZeroRegisterRecordMap;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
}
