package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.core.wa.dto.ot.GetOtTimeResultDto;
import com.caidaocloud.attendance.core.wa.dto.ot.OtCompensateTypeListDto;
import com.caidaocloud.attendance.sdk.dto.SdkOtRevokeDTO;
import com.caidaocloud.attendance.sdk.dto.SdkOverApplySaveDTO;
import com.caidaocloud.attendance.sdk.feign.IOvertimeFeignClient;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 加班
 *
 * <AUTHOR>
 * @Date 2023/6/19
 */
@Component
public class OvertimeFeignFallBack implements IOvertimeFeignClient {

    @Override
    public Result<GetOtTimeResultDto> getOtTotaltime(Long empId, Integer overtimeTypeId, Integer startTime, Integer endTime, Integer stime, Integer etime, Long overtimeDate) {
        return Result.fail();
    }

    @Override
    public Result<List<KeyValue>> getOvertimeDateList(Long empId, Integer startTime, Integer endTime, Integer stime, Integer etime) {
        return Result.fail();
    }

    @Override
    public Result<?> saveOverApply(SdkOverApplySaveDTO dto) {
        return Result.fail();
    }

    @Override
    public Result<?> revokeEmpOt(SdkOtRevokeDTO dto) {
        return Result.fail();
    }

    @Override
    public Result<?> getEmpOvertimeLeftDuration(Long empId) {
        return Result.fail();
    }

    @Override
    public Result<List<OtCompensateTypeListDto>> getCompensateTypeList(Long empid, Long start, Long end, Long overtimeDate) {
        return Result.fail();
    }

    @Override
    public Result<BigDecimal> getEmpPeriodOtDuration(Long empId, Long startDate) {
        return Result.fail();
    }
}
