package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaCompensatoryQuotaRecordDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaCompensatoryQuotaRecord;

import java.util.List;

public interface ICompensatoryQuotaRecordRepository {

    int save(WaCompensatoryQuotaRecord model);

    int update(WaCompensatoryQuotaRecord model);

    int save(List<WaCompensatoryQuotaRecord> models);

    int update(List<WaCompensatoryQuotaRecord> models);

    List<WaCompensatoryQuotaRecordDo> getWaCompensatoryQuotaRecord(String tenantId, List<Integer> detailIds, List<Integer> status);

    List<WaCompensatoryQuotaRecordDo> getCompensatoryQuotaRecord(String tenantId, List<Long> quotaIds, List<Integer> status);

    int deleteByQuotaIds(String tenantId, List<Long> quotaIds);
}
