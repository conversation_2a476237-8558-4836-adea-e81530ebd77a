package com.caidao1.integrate.entity.GaiYa;

import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.service.AsynExecListener;
import com.caidao1.commons.service.AsyncExecService;
import com.caidao1.integrate.mybatis.model.LogDataInput;
import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.caidao1.integrate.util.IntegrateUtil;
import com.caidao1.ioc.dto.ImportResult;
import com.caidao1.ioc.dto.ImportResultMessage;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.imports.service.Infrastructure.util.UserContext;
import com.caidaocloud.imports.service.application.service.ImportsService;
import com.caidaocloud.imports.service.interfaces.dto.CheckImportsDto;
import com.caidaocloud.imports.service.interfaces.dto.ImportFileDto;
import com.caidaocloud.imports.service.interfaces.dto.SaveImportDataDto;
import com.caidaocloud.oss.file.LocalMultipartFile;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;


import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.caidaocloud.imports.service.application.service.ioc.ImportDataService.SPREADSHEETML_SHEET;

/**
 * <AUTHOR>
 * 盖雅 打卡 和 请假 和 加班 数据接入
 */
@Slf4j
@Service
public class GyReadSource {
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private AsyncExecService asyncService;

    private static Map<String,String>  workNoMapping = new HashMap<>();
    static {
        workNoMapping.put("00029690","29690");
        workNoMapping.put("00078024","78024");
        workNoMapping.put("00071835","71835");
        workNoMapping.put("00069274","69274");
        workNoMapping.put("00052843","52843");
        workNoMapping.put("00042700","42700");
        workNoMapping.put("00029978","29978");
        workNoMapping.put("00027264","27264");
        workNoMapping.put("00012754","12754");
        workNoMapping.put("00010173","10173");
        workNoMapping.put("00010717","10717");
        workNoMapping.put("00010145","10145");
        workNoMapping.put("00042939","42939");
        workNoMapping.put("00043444","43444");
        workNoMapping.put("00033067","33067");
        workNoMapping.put("00013172","13172");
        workNoMapping.put("00012883","12883");
        workNoMapping.put("00012799","12799");
        workNoMapping.put("00012760","12760");
        workNoMapping.put("00012739","12739");
    }

    /**
     * 生成 apiKey
     * 具体加密方式：【appId 反序+timestamp+Secret 反序】》转字节数组》hash sha256 加密》ToBase64String
     */
    public String doApiKey(String appId, String secret, Long timestamp) {
        String reversedAppId = new StringBuilder(appId).reverse().toString();
        String reversedSecret = new StringBuilder(secret).reverse().toString();
        String m = reversedAppId + timestamp + reversedSecret;
        try {
            byte[] inputBytes = m.getBytes(StandardCharsets.UTF_8);
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(inputBytes);
            return Base64.getEncoder().encodeToString(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("不支持 SHA-256 算法:{}", e.getMessage(), e);
        }
        return null;
    }

    public String doToken(String getTokenApiUrl, String appId, String secret, Long timestamp) throws Exception {
        String apiKey = doApiKey(appId, secret, timestamp);
        if (apiKey == null) {
            log.error("生成 apiKey 失败");
            return null;
        }
        HttpResponse response = null;
        try (CloseableHttpClient http = HttpClients.createDefault()) {
            HttpPost httppost = new HttpPost(getTokenApiUrl);
            httppost.setHeader("appId", appId);
            httppost.setHeader("apiKey", apiKey);
            httppost.setHeader("timestamp", timestamp.toString());
            response = http.execute(httppost);
            if (null != response && response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String content = EntityUtils.toString(response.getEntity(), "UTF-8");
                Map result = new ObjectMapper().readValue(content, Map.class);
                if (result != null && ((Boolean) result.get("result"))) {
                    return (String) result.get("data");
                }
            }
        }
        return null;
    }

//    public static void main(String[] args) {
//        System.out.println(IntegrateUtil.formatExp("今天", "yyyy-MM-dd"));
//        leaveTest();
//        otTest();
//    }

    public static void leaveTest() {
        Map apiConfig = new HashMap<>();
        apiConfig.put("appId", "Envision");
        apiConfig.put("secret", "dWPc7boJCI");
        apiConfig.put("getTokenUrl", "https://envisiontest.gaiaworkforce.com/api/common/v1.0/gettoken");
        apiConfig.put("readDataUrl", "https://envisiontest.gaiaworkforce.com/api/extend/v1.0/query");
        apiConfig.put("readDbParam", new HashMap() {{
            put("entity", "LEAVEDATA");
            put("queryField", "EMPLOYEEID,CLASSNAME,TIMECARDDATE,STARTDTM,ENDDTM,LIMIT_UNIT");
            put("sqlFilter", " and TIMECARDDATE = '今天' ");
            put("workNoList", "25170245");

        }});
        GyReadSource gyReadSource = new GyReadSource();
        List<Map<String, Object>> result = gyReadSource.getGyOtReadSourceResult(apiConfig, "1");
        System.out.println(FastjsonUtil.toJson(result));
    }

    public static void otTest() {
        Map apiConfig = new HashMap<>();
        apiConfig.put("appId", "Envision");
        apiConfig.put("secret", "dWPc7boJCI");
        apiConfig.put("getTokenUrl", "https://envisiontest.gaiaworkforce.com/api/common/v1.0/gettoken");
        apiConfig.put("readDataUrl", "https://envisiontest.gaiaworkforce.com/api/extend/v1.0/query");
        apiConfig.put("readDbParam", new HashMap() {{
            put("entity", "OTDATA");
            put("queryField", "EMPLOYEEID,CLASSNAME,TIMECARDDATE,STARTDTM,ENDDTM");
            put("sqlFilter", " and TIMECARDDATE = '今天' ");
//            put("workNoList", "25170245");
        }});
        GyReadSource gyReadSource = new GyReadSource();
        List<Map<String, Object>> result = gyReadSource.getGyOtReadSourceResult(apiConfig, "1");
        System.out.println(FastjsonUtil.toJson(result));
    }

    public List<Map<String, Object>> getGyOtReadSourceResult(Map apiConfig, String belongId) {
        try {
            Long time = System.currentTimeMillis() / 1000;
            String appId = (String) apiConfig.get("appId");
            String secret = (String) apiConfig.get("secret");
            String getTokenUrl = (String) apiConfig.get("getTokenUrl");
            String readOtListUrl = (String) apiConfig.get("readDataUrl");
            log.info("开始获取盖亚token");
            String token = doToken(getTokenUrl, appId, secret, time);
            log.error("盖亚token获取值:{}", token);
            if (token == null) {
                log.error("loadGaiYaTokenFailed");
                return null;
            }
            try (CloseableHttpClient http = HttpClients.createDefault()) {
                HttpPost httppost = new HttpPost(readOtListUrl);
                httppost.setHeader("appId", appId);
                httppost.setHeader("token", token);
                httppost.setHeader("Content-Type", "application/json");

                Map queryDbParam = (HashMap) apiConfig.get("readDbParam");
                Map<String, Object> body = new HashMap<>();
                String entity = queryDbParam.get("entity").toString();
                body.put("entity", entity);
                body.put("queryfield", Arrays.stream(queryDbParam.get("queryField").toString().split(",")).collect(Collectors.toList()));
                String sqlFilter = (String) queryDbParam.get("sqlFilter");
                sqlFilter = sqlFilter.replaceAll("昨天", IntegrateUtil.formatExp("昨天", "yyyy-MM-dd"));
                sqlFilter = sqlFilter.replaceAll("今天", IntegrateUtil.formatExp("今天", "yyyy-MM-dd"));
                if (queryDbParam.containsKey("workNoList")) {
                    List<String> workNoFilter = Arrays.stream(queryDbParam.get("workNoList").toString().split(",")).collect(Collectors.toList());
                    sqlFilter += " and EMPLOYEEID IN ('" + StringUtils.join(workNoFilter, "','") + "')";
                }
                body.put("filter", sqlFilter);

                ObjectMapper objectMapper = new ObjectMapper();
                String jsonBody = objectMapper.writeValueAsString(body);
                StringEntity stringEntity = new StringEntity(jsonBody, StandardCharsets.UTF_8);
                httppost.setEntity(stringEntity);
                HttpResponse response = http.execute(httppost);
                if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                    String content = EntityUtils.toString(response.getEntity(), "UTF-8");
                    Map result = new ObjectMapper().readValue(content, Map.class);
                    log.info("getGaiYaDataResult:{}", FastjsonUtil.toJson(result));
                    if (result != null && ((Boolean) result.get("result"))) {
                        List<Map<String, Object>> data = (List<Map<String, Object>>) result.get("data");
//                        for (Map<String, Object> datum : data) {
//                            if("PUNCHDATA".equals(entity)){
//                                String beginTime = datum.get("TIMECARDDATE").toString();
//                                datum.put("TIMECARDDATE", beginTime.replace("2024", "2025"));
//                                datum.put("EMPLOYEEID", "CI225780165" );
//                            }else {
//                                String beginTime = datum.get("STARTDTM").toString();
//                                String endTime = datum.get("ENDDTM").toString();
//                                datum.put("STARTDTM", beginTime.replace("2024", "2025"));
//                                datum.put("ENDDTM", endTime.replace("2024", "2025"));
//                            }
//                        }
                        if ("LEAVEDATA".equals(entity)) {
                            for (Map leaveData : data) {
                                // 休假单位 0：天 1：小时
                                String EMPLOYEEID = (String) leaveData.get("EMPLOYEEID");
                                String unit = (String) leaveData.get("LIMIT_UNIT");
                                String startTime = ((String) leaveData.get("STARTDTM"));
                                String endTime = (String) leaveData.get("ENDDTM");
                                if ("0".equals(unit)) {
                                    startTime = startTime.split("\\s+")[0];
                                    endTime = endTime.split("\\s+")[0];
                                } else if ("1".equals(unit)) {
                                    startTime += ":00";
                                    endTime += ":00";
                                }
                                leaveData.put("startTime", startTime);
                                leaveData.put("endTime", endTime);
//                                if ("25160186".equals(EMPLOYEEID)) {
//                                    leaveData.put("CLASSNAME", "婚假");
//                                    leaveData.put("EMPLOYEEID", "CI225780165");
//
//                                }  if ("25160496".equals(EMPLOYEEID)) {
//                                    leaveData.put("EMPLOYEEID", "CI3242375");
//
//                                }
                            }
                        }else if ("PUNCHDATA".equals(entity)) {
                            for (Map<String, Object> record : data) {
                                String dataFrom = record.get("DATAFROM").toString();
                                if ("2".equals(dataFrom)) {
                                    record.put("type", 6);
                                    record.put("approval_status", 2);
                                }else {
                                    record.put("type", 1);
                                    record.put("approval_status", null);
                                }
                                String ms  = record.get("TIMECARDTIME").toString();
                                if (ms.matches("\\d{2}:\\d{2}")) {
                                    ms += ":00";
                                }
                                record.put("regTime", record.get("TIMECARDDATE") + " " + ms);
                            }
                        }
                        log.info("盖亚源数据:{}", FastjsonUtil.toJson(data));
                        for (Map otOrLeaveData : data) {
                            String workNo = (String) otOrLeaveData.get("EMPLOYEEID");
                            if(workNoMapping.containsKey(workNo)){
                                workNo = workNoMapping.get(workNo);
                            }
                            otOrLeaveData.put("EMPLOYEEID", workNo);
                            String key = BaseConst.EMP_ + belongId + "_" + workNo;
                            String empCache = CDCacheUtil.getValue(key);
                            log.info("redisKey:{},res:{}", key, empCache);
                            if (StringUtils.isBlank(empCache)) {
                                otOrLeaveData.put("continue", true);
                                log.info("gaiYaWorkNoNotExists={},", workNo);
                            } else {
                                Long empId = Long.parseLong(String.valueOf(empCache.split(",")[0]).replace("\"", ""));
                                otOrLeaveData.put("empId", empId);
                                otOrLeaveData.put("name", empCache.split(",")[1]);
                                otOrLeaveData.put("status", 2);
                                otOrLeaveData.put("continue", false);
                            }
                        }
                        List<Map<String, Object>> filterList = data.stream().filter(map -> !((Boolean) map.get("continue"))).collect(Collectors.toList());
                        log.info("盖亚源数据工号过滤后:{}", FastjsonUtil.toJson(filterList));
                        return filterList;
                    } else {
                        log.error("readGaiYaOtListError:{}", result);
                    }
                }else {
                    log.error("readGaiYaDataListError:{}", response.getStatusLine().getStatusCode());
                }
            }
        } catch (Exception e) {
            log.error("readGaiYaOtListError:{}", e.getMessage(), e);
        }
        return null;
    }

    @Resource
    private OssService ossService;
    @Resource
    private ImportsService importsService;

    public void syncGyLeaveInput(SysDataInput dataInput, LogDataInput dataInputLog, Map<String, Object> apiConfig) {
        List<Map<String, Object>> gyOtReadSourceResult = getGyOtReadSourceResult(apiConfig, dataInput.getBelongOrgId());
        if (CollectionUtils.isNotEmpty(gyOtReadSourceResult)) {
            for (int i = 2; i <= gyOtReadSourceResult.size() + 2; i++) {
                gyOtReadSourceResult.get(i).put("excelRowNum", i);
            }
            dataInputLog.setSourceResult(FastjsonUtil.toJson(gyOtReadSourceResult));
            createExcel(gyOtReadSourceResult, "休假数据接入_" + System.currentTimeMillis() / 1000 , dataInput.getBelongOrgId(), dataInput, dataInputLog);
        }
    }

    public void createExcel(List<Map<String, Object>> data, String fileName, String belongId, SysDataInput dataInput, LogDataInput dataInputLog) {
        Workbook workbook = new SXSSFWorkbook();
        Sheet sheet = workbook.createSheet("请假数据");
        // 创建表头
        String[] headers = {"账号", "假期类型", "开始时间", "结束时间", "半天开始", "半天结束", "审批状态"};
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        DataFormat dateFormat = workbook.createDataFormat();
        short dateFormatCode = dateFormat.getFormat("yyyy-MM-dd HH:mm:ss");
        CellStyle dateCellStyle = workbook.createCellStyle();
        dateCellStyle.setDataFormat(dateFormatCode);

        int rowNum = 1;
        for (Map<String, Object> map : data) {
            Row row = sheet.createRow(rowNum++);

            // 账号
            String workNo = (String) map.get("EMPLOYEEID");
            Cell workNoCell = row.createCell(0);
            workNoCell.setCellValue(workNo);

            // 假期类型
            String leaveType = (String) map.get("CLASSNAME");
            Cell leaveTypeCell = row.createCell(1);
            leaveTypeCell.setCellValue(leaveType);

            // 开始时间
            String startTime = (String) map.get("startTime");
            Cell startTimeCell = row.createCell(2);
            if (startTime != null) {
                startTimeCell.setCellValue(startTime);
                startTimeCell.setCellStyle(dateCellStyle);
            }

            // 结束时间
            String endTime = (String) map.get("endTime");
            Cell endTimeCell = row.createCell(3);
            if (endTime != null) {
                endTimeCell.setCellValue(endTime);
                endTimeCell.setCellStyle(dateCellStyle);
            }

            // 半天开始
            Cell halfDayStartCell = row.createCell(4);
            halfDayStartCell.setCellValue("");

            // 半天结束
            Cell halfDayEndCell = row.createCell(5);
            halfDayEndCell.setCellValue("");

            // 审批状态
            Cell approvalStatusCell = row.createCell(6);
            approvalStatusCell.setCellValue("已通过");
        }

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream(1024)) {
            workbook.write(bos);
            bos.flush();
            LocalMultipartFile localMultipartFile = new LocalMultipartFile(fileName, bos.toByteArray(), SPREADSHEETML_SHEET);
            localMultipartFile.setOriginalFilename(fileName + ".xlsx");
            workbook.close();
            // 上次文件
            ImportFileDto importFileDto = new ImportFileDto();
            importFileDto.setResId(78);
            importFileDto.setFile(localMultipartFile);
            importFileDto.setBelongId(belongId);
            importFileDto.setBusArgs(new HashMap<>());
            ImportResult importResult = importsService.importFile(importFileDto);
            if (importResult.isResult() && importResult.getFuncId() != null && importResult.getTemplateId() != null) {
                // 校验文件
                long process = System.currentTimeMillis();
                CheckImportsDto checkImportsDto = new CheckImportsDto();
                checkImportsDto.setFuncId(importResult.getFuncId());
                checkImportsDto.setTemplateId(importResult.getTemplateId());
                checkImportsDto.setBusArgs(new HashMap<>());
                checkImportsDto.setProgress(process + "");
                checkImportsDto.setRtnFileName(importResult.getFileName());
                boolean b = importsService.checkImports(checkImportsDto);
                String checkData = cacheService.getValue("IMP_" + process);
                if (StringUtils.isNotEmpty(checkData)) {
                    log.info("盖亚休假数据列表:{}", data);
                    log.info("盖亚休假数据接入存在校验不通过数据_校验信息列表:{}", checkData);
                    List<Map> msgList = new ObjectMapper().readValue(checkData, List.class);
                    if (CollectionUtils.isNotEmpty(msgList)) {
                        dataInputLog.setCheckResult(checkData);
                        Set<Integer> removeRowNumIndexS = msgList.stream().map(map -> ((Integer) map.get("rowNum")) - 2).collect(Collectors.toSet());
                        List<Map<String,Object>> successRowList = new ArrayList<>();
                        for (int i = 0; i < data.size(); i++) {
                            if (!removeRowNumIndexS.contains(i)) {
                                log.info("盖亚休假数据校验通过的列表：{}", successRowList);
                                successRowList.add(data.get(i));
                                createExcel(successRowList, "休假数据接入_" + System.currentTimeMillis() / 1000, belongId, dataInput, dataInputLog);
                            }
                        }
                    }
                } else {
                    // 校验都通过直接导入
                    SaveImportDataDto query = new SaveImportDataDto();
                    query.setBelongOrgId(belongId);
                    query.setCorpId(Long.valueOf(belongId));
                    query.setUserId(-1L);
                    query.setEmpId(0L);
                    query.setTemplateId(importResult.getTemplateId());
                    query.setRtnFileName(importResult.getFileName());
                    query.setFuncId(importResult.getFuncId());
                    query.setProgress(process + "");

                    SecurityUserInfo securityUserInfo = new SecurityUserInfo();
                    securityUserInfo.setUserId(-1L);
                    securityUserInfo.setEmpId(0L);
                    securityUserInfo.setTenantId(belongId);
                    asyncService.execCallback(new AsynExecListener() {
                        @Override
                        public Map execute() throws Exception {
                            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
                            Map params = new HashMap();
                            ImportResultMessage importResultMessageF = new ImportResultMessage(-1, null);
                            try {
                                ImportResultMessage importResultMessageT = importsService.saveImportData(query);
                                params.put("result", importResultMessageT);
                                return params;
                            } catch (Exception e) {
                                log.error("v1SaveImportData err,{}", e.getMessage(), e);
                                importResultMessageF.setMessage(e.getMessage());
                                params.put("result", importResultMessageF);
                            } finally {
                                if (TransactionSynchronizationManager.isSynchronizationActive()) {
                                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                                        @Override
                                        public void afterCommit() {
                                            SecurityUserUtil.removeSecurityUserInfo();
                                        }
                                    });
                                } else {
                                    SecurityUserUtil.removeSecurityUserInfo();
                                }
                            }
                            return params;
                        }

                        @Override
                        public void callback(Map params) {
                            ImportResultMessage callAync = (ImportResultMessage) params.get("result");
                            log.info("Asynchronous execution returns body information:{}",
                                    null == callAync ? null : FastjsonUtil.toJson(callAync.toString()));
                            redisTemplate.opsForValue().set("importDataList_" + query.getProgress(), callAync, 35, TimeUnit.MINUTES);
                        }
                    });
                    while (true) {
                        Thread.sleep(2000);
                        // 获取导入进度
                        Object importProcess = redisTemplate.opsForValue().get("importData_" + process);
                        log.info("importData_" + process + ":导入中" + "当前进度:" + importProcess);
                        if (null != importProcess && BigDecimal.ONE.compareTo(new BigDecimal(importProcess.toString())) == 0) {
                            log.info("importData_" + process + ":导入完成");
                            break;
                        }
                    }
                }

            }
        } catch (Exception e) {
            log.error("importUploadExcelOssErr:{}", e.getMessage(), e);
        }
    }
}
