package com.caidaocloud.attendance.service.application.dto;

import com.caidaocloud.attendance.core.workflow.dto.WfProcessRecordDto;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "流程记录")
public class CancelProcessRecordDto {
    private Long cancelType;
    private String originalLeaveTime;
    private String leaveTime;
    private String leaveCancelTime;
    private String reason;
    private List<WfProcessRecordDto> leaveCancelRecord = new ArrayList<>();
}