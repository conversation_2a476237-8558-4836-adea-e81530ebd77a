package com.caidaocloud.attendance.service.application.service.workflow;

import com.caidaocloud.attendance.service.application.service.workflow.form.*;
import com.caidaocloud.workflow.annotation.WfFunction;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;
import org.springframework.stereotype.Service;

/**
 * 工作流注册
 */
@Service
public class WorkflowWaRegisterService {

    @WfFunction(name = "休假", code = "ATTENDANCE-LEAVE", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = LeaveApprovalFormDef.class)
    public void leave() {
    }

    @WfFunction(name = "批量休假", code = "ATTENDANCE-BATCH-LEAVE", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = BatchLeaveApprovalFormDef.class)
    public void batchLeave() {
    }

    @WfFunction(name = "批量加班", code = "ATTENDANCE-BATCH-OVERTIME", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = BatchOvertimeApprovalFormDef.class)
    public void batchOvertime() {
    }

    @WfFunction(name = "批量考勤异常申请", code = "ATTENDANCE-BATCH-ANALYSE-ADJUST", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = BatchAnalyseAdjustApprovalFormDef.class)
    public void batchAnalyseAdjust() {
    }

    @WfFunction(name = "加班", code = "ATTENDANCE-OVERTIME", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = OvertimeApprovalFormDef.class)
    public void overtime() {
    }

    @WfFunction(name = "出差", code = "ATTENDANCE-TRAVEL", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = TravelApprovalFormDef.class)
    public void travel() {
    }

    @WfFunction(name = "批量出差", code = "ATTENDANCE-BATCH-TRAVEL", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = BatchTravelApprovalFormDef.class)
    public void batchTravel() {
    }

    @WfFunction(name = "补卡", code = "ATTENDANCE-REGISTER", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = BdkApprovalFormDef.class)
    public void bdk() {
    }

    @WfFunction(name = "调班", code = "ATTENDANCE-SHIFT", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = ShiftChangeApprovalFormDef.class)
    public void shiftChange() {
    }

    @WfFunction(name = "销假", code = "ATTENDANCE-VACATION", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = LeaveCancelApprovalFormDef.class)
    public void leaveCancel() {
    }

    @WfFunction(name = "调休付现", code = "ATTENDANCE-COMPENSATORY", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = CompensatoryCaseFormDef.class)
    public void compensatoryToCase() {
    }

    @WfFunction(name = "加班撤销", code = "ATTENDANCE-OVERTIME-REVOKE", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = OvertimeRevokeApprovalFormDef.class)
    public void overtimeRevoke() {
    }

    @WfFunction(name = "加班废止", code = "ATTENDANCE-OVERTIME-ABOLISH", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = OvertimeAbolishApprovalFormDef.class)
    public void overtimeAbolish() {
    }

    @WfFunction(name = "出差撤销", code = "ATTENDANCE-TRAVEL-REVOKE", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = TravelRevokeApprovalFormDef.class)
    public void travelRevoke() {
    }

    @WfFunction(name = "出差废止", code = "ATTENDANCE-TRAVEL-ABOLISH", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = TravelAbolishApprovalFormDef.class)
    public void travelAbolish() {
    }

    @WfFunction(name = "假期延期", code = "ATTENDANCE-LEAVE-EXTENSION", pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/attendance/leaveapply/v1/getWfDetail", serviceId = "caidaocloud-attendance-service",
            redirectAddress = "", formDefClass = LeaveExtensionFormDef.class)
    public void leaveExtension() {
    }
}
