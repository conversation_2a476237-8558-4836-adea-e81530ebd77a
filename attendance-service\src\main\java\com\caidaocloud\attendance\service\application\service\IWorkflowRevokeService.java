package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.RevokeDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WorkflowRevokeReqDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.Map;

public interface IWorkflowRevokeService {
    Result<Boolean> revoke(RevokeDto dto, UserInfo userInfo) throws Exception;

    PageList<Map> getOvertimeWorkflowRevokeList(WorkflowRevokeReqDto dto, PageBean pageBean, UserInfo userInfo);

    PageList<Map> getOvertimeWorkflowAbolishList(WorkflowRevokeReqDto dto, PageBean pageBean, UserInfo userInfo);

    PageList<Map> getTravelWorkflowRevokeList(WorkflowRevokeReqDto dto, UserInfo userInfo);

    PageList<Map> getTravelWorkflowAbolishList(WorkflowRevokeReqDto dto, UserInfo userInfo);
}
