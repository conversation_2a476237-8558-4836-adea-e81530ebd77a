package com.caidaocloud.attendance.core.commons.utils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


public class ObjectUtil {

    public static <T> T getValue(Object obj, T defaultValue) {
        if (defaultValue == null && obj == null) {
            return null;
        }
        if (defaultValue == null) {
            if (obj instanceof BigDecimal) {
                obj = obj.toString();
            }
            if (obj instanceof Double) {
                obj = obj.toString();
            }
            if (obj instanceof Integer) {
                obj = obj.toString();
            }
            return (T) obj;
        }
        try {
            if (defaultValue.getClass().isArray() || List.class.isAssignableFrom(defaultValue.getClass())
                    || Map.class.isAssignableFrom(defaultValue.getClass())) {
                return obj == null ? defaultValue : (T) obj;
            } else if (String.class.isAssignableFrom(defaultValue.getClass())) {
                return obj == null ? defaultValue : (T) String.valueOf(obj);
            } else {
                if (obj instanceof BigDecimal) {
                    obj = obj.toString();
                }
                if (obj instanceof Double) {
                    obj = obj.toString();
                }
                String value = String.valueOf(obj);
                if (Integer.class.isAssignableFrom(defaultValue.getClass())
                        || int.class.isAssignableFrom(defaultValue.getClass())) {
                    return obj == null ? defaultValue : (T) Integer.valueOf(value.split("\\.")[0]);
                } else if (Long.class.isAssignableFrom(defaultValue.getClass())
                        || long.class.isAssignableFrom(defaultValue.getClass())) {
                    return obj == null ? defaultValue : (T) Long.valueOf(value.split("\\.")[0]);
                } else if (Float.class.isAssignableFrom(defaultValue.getClass())
                        || float.class.isAssignableFrom(defaultValue.getClass())) {
                    return obj == null ? defaultValue : (T) Float.valueOf(value);
                } else if (Double.class.isAssignableFrom(defaultValue.getClass())
                        || double.class.isAssignableFrom(defaultValue.getClass())) {
                    return obj == null ? defaultValue : (T) Double.valueOf(value);
                } else if (Boolean.class.isAssignableFrom(defaultValue.getClass())
                        || boolean.class.isAssignableFrom(defaultValue.getClass())) {
                    return obj == null ? defaultValue : (T) Boolean.valueOf(value);
                } else if (Short.class.isAssignableFrom(defaultValue.getClass())
                        || short.class.isAssignableFrom(defaultValue.getClass())) {
                    return obj == null ? defaultValue : (T) Short.valueOf(value.split("\\.")[0]);
                } else if (Byte.class.isAssignableFrom(defaultValue.getClass())
                        || byte.class.isAssignableFrom(defaultValue.getClass())) {
                    return obj == null ? defaultValue : (T) Byte.valueOf(value.split("\\.")[0]);
                } else if (Character.class.isAssignableFrom(defaultValue.getClass())
                        || char.class.isAssignableFrom(defaultValue.getClass())) {
                    return obj == null ? defaultValue : (T) obj;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return defaultValue;
    }
}
