package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.sdk.dto.SdkWorkflowRevokeDTO;
import com.caidaocloud.attendance.sdk.feign.IWorkflowRevokeFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class IWorkflowRevokeFeignFallback implements IWorkflowRevokeFeignClient {
    @Override
    public Result<?> revoke(SdkWorkflowRevokeDTO dto) {
        return Result.fail();
    }
}
