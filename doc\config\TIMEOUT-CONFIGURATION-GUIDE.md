# 消息队列超时配置指南

## 🎯 问题背景

`MultiNodeAnalyseClockSubscribe` 消费者执行 `clockSignService.doExeBatchAnalyse(dto)` 业务逻辑时，可能需要较长时间（几分钟到几十分钟），存在以下风险：

- **消息重复投递**：业务未完成时消息被认为处理失败
- **重复处理**：同一消息被多个消费者同时处理
- **资源浪费**：重复的数据库查询和计算

## 🔧 解决方案配置

### 1. 基础超时配置

在 Nacos 配置中心设置：

```yaml
attendance:
  mq:
    enableDLQ: true
    maxRetryCount: 2
    consumer:
      # 消费者超时时间 - 核心配置
      consumerTimeout: 1800000    # 30分钟 (毫秒)
      receiveTimeout: 60000       # 消息接收超时 60秒
      
      # 并发控制 - 避免过多并发加剧性能问题
      concurrentConsumers: 1      # 最小消费者数
      maxConcurrentConsumers: 2   # 最大消费者数
      prefetchCount: 1            # 预取消息数
      
      # 任务执行器配置
      autoStartup: true
      taskExecutorName: "attendance-clock-analysis-"
```

### 2. 不同场景的推荐配置

#### 小规模处理（< 100员工，< 30天）
```yaml
attendance:
  mq:
    consumer:
      consumerTimeout: 300000     # 5分钟
      concurrentConsumers: 2
      maxConcurrentConsumers: 3
```

#### 中等规模处理（100-500员工，30-90天）
```yaml
attendance:
  mq:
    consumer:
      consumerTimeout: 900000     # 15分钟
      concurrentConsumers: 1
      maxConcurrentConsumers: 2
```

#### 大规模处理（500+员工，90+天）
```yaml
attendance:
  mq:
    consumer:
      consumerTimeout: 3600000    # 60分钟
      concurrentConsumers: 1
      maxConcurrentConsumers: 1   # 单线程处理避免数据库压力
```

## 📊 超时时间计算公式

### 基础计算
```
预估处理时间 = 员工数 × 天数 × 每员工每天处理时间(约100-200ms)
超时时间 = 预估处理时间 × 安全系数(2-3倍)
```

### 实际示例
```
场景：500员工 × 30天
预估时间：500 × 30 × 150ms = 2,250,000ms ≈ 37.5分钟
推荐超时：37.5分钟 × 2.5 = 93.75分钟 ≈ 5,400,000ms
```

## 🚨 监控和告警机制

### 系统自动监控

消费者内置了处理时间监控：

- ⚠️ **50%超时警告**：处理时间超过超时时间的50%
- 🔔 **70%超时警告**：处理时间超过超时时间的70% 
- 🚨 **90%超时紧急告警**：处理时间超过超时时间的90%
- 💥 **120%超时错误**：处理时间超过超时时间的120%

### 日志监控关键词

在日志中搜索以下关键词进行监控：

```bash
# 处理时间预警
grep "⚠️.*close to consumer timeout" application.log

# 长时间处理检测
grep "Long-running message processing detected" application.log

# 超时临界告警
grep "🚨 CRITICAL.*nearly timeout" application.log

# 超时错误
grep "💥.*timeout exceeded" application.log
```

## 🔍 故障排查

### 常见问题和解决方案

#### 问题1：消息重复处理
**现象**：同一条消息被处理多次
**原因**：处理时间超过消费者超时时间
**解决**：增加 `consumerTimeout` 值

```yaml
attendance:
  mq:
    consumer:
      consumerTimeout: 3600000  # 从30分钟增加到60分钟
```

#### 问题2：消息积压
**现象**：队列中消息持续增长
**原因**：处理速度跟不上消息产生速度
**解决**：优化并发配置或业务逻辑

```yaml
attendance:
  mq:
    consumer:
      concurrentConsumers: 2          # 增加并发处理
      maxConcurrentConsumers: 4
      consumerTimeout: 900000         # 适当减少超时时间
```

#### 问题3：系统性能下降
**现象**：数据库连接池耗尽、CPU使用率高
**原因**：过多并发消费者
**解决**：减少并发数，增加单个任务的超时时间

```yaml
attendance:
  mq:
    consumer:
      concurrentConsumers: 1          # 减少并发
      maxConcurrentConsumers: 1
      consumerTimeout: 7200000        # 增加超时时间到2小时
```

## 🎯 性能优化建议

### 1. 业务层面优化

- **分批处理**：将大任务分解为多个小任务
- **缓存优化**：缓存常用数据减少数据库查询
- **异步处理**：非关键步骤使用异步处理
- **索引优化**：确保数据库查询使用合适的索引

### 2. 消息队列层面优化

- **消息分片**：按员工数或日期范围分片
- **优先级队列**：紧急任务使用高优先级队列
- **定时处理**：低峰期处理大批量任务

### 3. 系统层面优化

- **连接池配置**：增加数据库连接池大小
- **JVM参数调优**：调整堆内存和GC参数
- **资源监控**：监控CPU、内存、数据库连接使用情况

## 📋 配置检查清单

在部署前请检查：

- [ ] 根据业务规模设置合理的 `consumerTimeout`
- [ ] 配置适当的并发消费者数量
- [ ] 启用死信队列保护 (`enableDLQ: true`)
- [ ] 设置合理的重试次数
- [ ] 配置日志监控和告警
- [ ] 测试不同规模数据的处理时间
- [ ] 验证数据库连接池配置
- [ ] 检查系统资源限制

## 🔗 相关配置文件

- 配置属性：`AttendanceMqProperties.java`
- 容器工厂：`RabbitMQConfig.java`
- 消费者：`MultiNodeAnalyseClockSubscribe.java`
- 配置示例：`mq-config-example.yml`

## 💡 总结

- **核心原则**：超时时间应为预估处理时间的2-3倍
- **监控重要**：密切关注处理时间和告警信息
- **分批处理**：避免单个消息处理时间过长
- **资源平衡**：在处理速度和系统资源之间找平衡 