package com.caidaocloud.attendance.service.application.enums;

public enum FlexibleRuleEnum {

    FLEXBLE_ANALYZE(1, "按弹性区间分析"),
    SHIFT_ANALYZE(2, "按班次分析");


    private Integer index;

    private String name;

    FlexibleRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (FlexibleRuleEnum c : FlexibleRuleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
