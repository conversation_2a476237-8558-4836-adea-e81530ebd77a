package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.NotifyConfigDo;
import com.caidaocloud.dto.PageResult;

/**
 * <AUTHOR>
 * @Date 2021/9/16
 */
public interface INotifyConfigRepository {

    NotifyConfigDo selectById(String tenantId);

    int save(NotifyConfigDo notifyConfigDo);

    int update(NotifyConfigDo notifyConfigDo,String tenantId);

    PageResult<NotifyConfigDo> getPageList(int pageNo, int pageSize);
}