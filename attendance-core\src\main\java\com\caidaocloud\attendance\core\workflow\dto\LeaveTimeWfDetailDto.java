package com.caidaocloud.attendance.core.workflow.dto;

import com.caidaocloud.attendance.core.workflow.dto.WfAttachmentDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LeaveTimeWfDetailDto {
    @ApiModelProperty("时间")
    private String timeSlot;
    @ApiModelProperty("时长")
    private String timeDuration;
    @ApiModelProperty("审批状态")
    private Short status;
    @ApiModelProperty("审批状态文本")
    private String statusName;
    @ApiModelProperty("文件ID")
    private String fileId;
    @ApiModelProperty("文件名称")
    private String fileName;
    @ApiModelProperty("事由")
    private String reason;
    @ApiModelProperty("销假类型id")
    private Long typeId;
    @ApiModelProperty("文件")
    private List<WfAttachmentDto> fileList;
}
