package com.caidaocloud.attendance.service.application.shimz.service;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.wa.mybatis.model.WaGroup;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.service.application.dto.transfer.ChangeDefDto;
import com.caidaocloud.attendance.service.application.dto.transfer.TransferApplyDetailDto;
import com.caidaocloud.attendance.service.application.dto.transfer.TransferFieldDetailDto;
import com.caidaocloud.attendance.service.application.enums.DistributionCycleEnum;
import com.caidaocloud.attendance.service.application.enums.ValidityUnitEnum;
import com.caidaocloud.attendance.service.application.feign.IWaTransferFeignClient;
import com.caidaocloud.attendance.service.domain.entity.LeaveQuotaConfigDo;
import com.caidaocloud.attendance.service.domain.entity.QuotaGenRuleDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveSettingDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDo;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 清水配额生成
 *
 * <AUTHOR>
 * @Date 2024/5/24
 */
@Slf4j
@Service
public class ShimzQuotaService {
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;
    @Autowired
    private WaLeaveSettingDo waLeaveSettingDo;
    @Autowired
    private LeaveQuotaConfigDo leaveQuotaConfigDo;
    @Autowired
    private QuotaGenRuleDo quotaGenRuleDo;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private IWaTransferFeignClient waTransferFeignClient;

    /**
     * 员工部署异动后置处理器
     *
     * @return
     */
    public boolean empDeployChangeAfterTrigger(String businessKey, WfCallbackTriggerOperationEnum callbackType) throws Exception {
        if (WfCallbackTriggerOperationEnum.APPROVED != callbackType) {
            return Boolean.FALSE;
        }
        // 查询异动数据
        String applyId = StringUtils.substringBefore(businessKey, "_");
        Result<TransferApplyDetailDto> transferDetailResult = waTransferFeignClient.getTransferDetail(applyId, false);
        TransferApplyDetailDto transferApplyDetailDto = null;
        if (null == transferDetailResult || !transferDetailResult.isSuccess() || null == (transferApplyDetailDto = transferDetailResult.getData())) {
            return Boolean.FALSE;
        }

        // 查询异动配置
        Result<ChangeDefDto> changeDefInfoResult = waTransferFeignClient.getChangeDefInfo(transferApplyDetailDto.getDefId());
        ChangeDefDto changeDefDto = null;
        if (null == changeDefInfoResult || !changeDefInfoResult.isSuccess() || null == (changeDefDto = changeDefInfoResult.getData())) {
            return Boolean.FALSE;
        }

        // 检查是否是部署异动。只有部署异动才会生成移动休假配额
        if (!changeDefDto.getName().contains("部署异动")) {
            return Boolean.FALSE;
        }

        Long empId = Long.valueOf(transferApplyDetailDto.getEmp().getEmpId());
        Long effectDate = Long.valueOf(transferApplyDetailDto.getEffectiveDate());

        // 检查工作地是否变更，如有变更，则生成一条移动休假配额
        List<TransferFieldDetailDto> data = transferApplyDetailDto.getData();
        Optional<TransferFieldDetailDto> workplaceOpt = data.stream().filter(o -> "workplace".equals(o.getProperty())).findFirst();
        if (!workplaceOpt.isPresent()) {
            return Boolean.FALSE;
        }
        TransferFieldDetailDto workplaceChangeDto = workplaceOpt.get();
        String afterId = (String) workplaceChangeDto.getAfter();
        String beforeId = (String) workplaceChangeDto.getBefore();
        if (StringUtils.isBlank(afterId) || afterId.equals(beforeId)) {
            return Boolean.FALSE;
        }

        // 生成移动休假配额
        return genMigrateLeaveQuota(empId, effectDate);
    }

    /**
     * 生成移动休假配额
     *
     * @param empId      员工ID
     * @param effectDate 异动生效日期
     * @return
     */
    public boolean genMigrateLeaveQuota(Long empId, Long effectDate) throws Exception {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (null == empInfo) {
            return Boolean.FALSE;
        }
        WaGroup waGroup = waCommonService.getEmpBelongWaGroup(empId);
        if (null == waGroup) {
            return Boolean.FALSE;
        }
        // 查询员工所在考勤分组下设置的移动休假假期类型
        List<WaLeaveTypeDo> leaveTypes = waLeaveTypeDo.getLeaveTypeByGroupId(empInfo.getBelongOrgId(), waGroup.getWaGroupId());
        if (CollectionUtils.isEmpty(leaveTypes)) {
            return Boolean.FALSE;
        }
        Optional<WaLeaveTypeDo> leaveTypeDoOptional = leaveTypes.stream().filter(lt -> "MigrateLeave".equals(lt.getLeaveTypeDefCode())).findFirst();
        if (!leaveTypeDoOptional.isPresent()) {
            return Boolean.FALSE;
        }
        WaLeaveTypeDo leaveType = leaveTypeDoOptional.get();
        List<Integer> leaveTypeIds = Lists.newArrayList(leaveType.getLeaveTypeId());
        Integer quotaSettingId = null;
        List<WaLeaveSettingDo> leaveSettingDos = waLeaveSettingDo.getLeaveSettingList(empInfo.getBelongOrgId(), leaveTypeIds);
        if (CollectionUtils.isNotEmpty(leaveSettingDos)) {
            quotaSettingId = leaveSettingDos.get(0).getQuotaSettingId();
        }

        //查询配额配置信息
        List<LeaveQuotaConfigDo> configDoList = leaveQuotaConfigDo.getConfigListByIds(empInfo.getBelongOrgId(), leaveTypeIds);
        if (CollectionUtils.isEmpty(configDoList)) {
            return Boolean.FALSE;
        }

        //查询额度规则
        List<Long> ruleIds = configDoList.stream().map(LeaveQuotaConfigDo::getConfigId).collect(Collectors.toList());
        List<QuotaGenRuleDo> genRuleDoList = quotaGenRuleDo.getListByConfigIds(empInfo.getBelongOrgId(), ruleIds);
        if (CollectionUtils.isEmpty(genRuleDoList)) {
            return Boolean.FALSE;
        }
        LeaveQuotaConfigDo quotaConfigDo = configDoList.get(0);
        QuotaGenRuleDo ruleDo = genRuleDoList.get(0);

        // 生成配额
        com.caidaocloud.attendance.service.application.dto.WaEmpQuota empQuota = new com.caidaocloud.attendance.service.application.dto.WaEmpQuota();
        empQuota.setBelongOrgId(empInfo.getBelongOrgId());
        empQuota.setIfAdvance(quotaConfigDo.getIfAdvance());
        empQuota.setQuotaDay(Float.valueOf(ruleDo.getQuotaVal()));
        if (leaveType.getAcctTimeType() != null && 2 == leaveType.getAcctTimeType()) {
            empQuota.setQuotaDay(empQuota.getQuotaDay() * 60);
        }
        short year = DateUtilExt.getNowYear().shortValue();
        empQuota.setNowQuota(empQuota.getQuotaDay());
        empQuota.setOriginalQuotaDay(empQuota.getQuotaDay());
        empQuota.setEmpid(empId);
        empQuota.setPeriodYear(year);
        empQuota.setLeaveTypeId(quotaConfigDo.getLeaveTypeId());
        empQuota.setFamilyId(-1L);
        empQuota.setConfigId(ruleDo.getConfigId());
        empQuota.setHomeLeaveType(null);
        empQuota.setCrossQuotaDate(0L);
        empQuota.setAnnualQuota(0f);
        empQuota.setLastQuota(0);
        empQuota.setNextQuota(0);
        empQuota.setSecCrossQuotaDate(0L);
        empQuota.setThirdQuota(0);
        empQuota.setQuotaSettingId(quotaSettingId);

        // 配额有效期计算
        //发放周期：1 自然年、2 入职年、3 自定义周期
        Integer distributionCycle = quotaConfigDo.getDistributionCycle();
        Long disCycleStart = null;
        Long disCycleEnd = null;
        if (distributionCycle.equals(DistributionCycleEnum.NATURAL_YEAR.getIndex())) {
            //自然年
            Calendar calendar = Calendar.getInstance();
            calendar.set(year, 0, 1, 0, 0, 0);
            disCycleStart = calendar.getTime().getTime() / 1000;
            disCycleEnd = DateUtilExt.getYearsEndTime(disCycleStart);
        } else if (distributionCycle.equals(DistributionCycleEnum.CUSTOM_CYCLE.getIndex())) {
            //自定义周期
            Calendar startCal = Calendar.getInstance();
            startCal.setTimeInMillis(quotaConfigDo.getDisCycleStart() * 1000);
            startCal.set(Calendar.YEAR, year);
            disCycleStart = startCal.getTime().getTime() / 1000;
            disCycleEnd = DateUtilExt.addMonthDate(disCycleStart * 1000, 1, 0, -1);
        } else if (DistributionCycleEnum.ENTRY_YEAR.getIndex().equals(distributionCycle) && empInfo.getHireDate() != null) {
            disCycleStart = getCycleStart(year, empInfo.getHireDate());
            disCycleEnd = DateUtilExt.addMonthDate(disCycleStart * 1000, 12, -1);
        }
        Long startDate = DateUtilExt.addMonthDate(effectDate, -1, 0);
        empQuota.setStartDate(startDate);
        Float validityDuration = quotaConfigDo.getValidityDuration();
        Integer validityUnit = quotaConfigDo.getValidityUnit();
        Long validStartDate = empQuota.getStartDate();
        if (validityUnit.equals(ValidityUnitEnum.DAY.getIndex())) {
            int time = validityDuration.intValue();
            if (time <= 0) {
                time = 0;
            } else {
                time -= 1;
            }
            empQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(validStartDate * 1000, 0, 0, time));
        } else if (validityUnit.equals(ValidityUnitEnum.MONTH.getIndex())) {
            empQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(validStartDate * 1000, 0, validityDuration.intValue(), -1));
        } else if (validityUnit.equals(ValidityUnitEnum.YEAR.getIndex())) {
            empQuota.setLastDate(DateUtilExt.getAddYearOrMonthOrDateEndTime(validStartDate * 1000, validityDuration.intValue(), 0, -1));
        }
        empQuota.setDisCycleStart(disCycleStart);
        empQuota.setDisCycleEnd(disCycleEnd);
        empQuota.setValidityDuration(validityDuration);
        empQuota.setValidityUnit(validityUnit);
        empQuota.setDeductionDay(0f);
        empQuota.setUsedDay(0f);
        empQuota.setAdjustQuota(0f);
        empQuota.setRemainDay(0f);
        empQuota.setRemainUsedDay(0f);
        empQuota.setCrtuser(UserContext.getUserId());
        empQuota.setCrttime(System.currentTimeMillis() / 1000);
        empQuota.setUpduser(empQuota.getCrtuser());
        empQuota.setUpdtime(empQuota.getCrttime());
        List<com.caidaocloud.attendance.service.application.dto.WaEmpQuota> empQuotaAddList = new ArrayList<>();
        empQuotaAddList.add(empQuota);
        importService.fastInsertList(com.caidaocloud.attendance.service.application.dto.WaEmpQuota.class, "empQuotaId", empQuotaAddList);
        return Boolean.TRUE;
    }

    private long getCycleStart(Short year, Long hireDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(hireDate * 1000);
        Calendar cal1 = Calendar.getInstance();
        cal1.set(year, cal.get(Calendar.MONTH), cal.get(Calendar.DATE), 0, 0, 0);
        return cal1.getTime().getTime() / 1000;
    }
}
