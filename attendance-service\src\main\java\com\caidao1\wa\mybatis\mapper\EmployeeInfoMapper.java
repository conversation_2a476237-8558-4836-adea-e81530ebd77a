package com.caidao1.wa.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EmployeeInfoMapper extends BaseMapper<SysEmpInfo> {
    List<Integer> getGroupEmpIds(@Param("tenantId") String tenantId,
                                 @Param("empId") Long empId,
                                 @Param("leaveId") Integer leaveId,
                                 @Param("leaveTypeId") Integer leaveTypeId,
                                 @Param("groupExp") String groupExp,
                                 @Param("nowTime") Long nowTime,
                                 @Param("yearEndTime") Long yearEndTime);
}
