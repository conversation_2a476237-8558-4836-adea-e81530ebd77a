package com.caidaocloud.attendance.core.wa.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown=true)
public class MobileEmpContractInfoBean {

	private String emp_contract_id;
	private String contract_no;
	private String contract_name; //合同模板名称
	private String period_type; //合同类型  1、固定期限，0非固定期限
	private String start_time; //合同开始时间
	private String end_time; //合同截止时间
	private String contract_period; //合同有效期
	private String probation; //试用期
	private String positive_date;//转正日（没有试用期就不需要转正日）
	private String sign_contract_date; //签订日期
	private String sign_count;//签订次数
	
	public String getProbation() {
		return probation;
	}
	public void setProbation(String probation) {
		this.probation = probation;
	}
	public String getContract_no() {
		return contract_no;
	}
	public void setContract_no(String contract_no) {
		this.contract_no = contract_no;
	}
	public String getEmp_contract_id() {
		return emp_contract_id;
	}
	public void setEmp_contract_id(String emp_contract_id) {
		this.emp_contract_id = emp_contract_id;
	}
	
	public String getContract_name() {
		return contract_name;
	}
	public void setContract_name(String contract_name) {
		this.contract_name = contract_name;
	}
	public String getPeriod_type() {
		return period_type;
	}
	public void setPeriod_type(String period_type) {
		this.period_type = period_type;
	}
	public String getStart_time() {
		return start_time;
	}
	public void setStart_time(String start_time) {
		this.start_time = start_time;
	}
	public String getEnd_time() {
		return end_time;
	}
	public void setEnd_time(String end_time) {
		this.end_time = end_time;
	}
	public String getContract_period() {
		return contract_period;
	}
	public void setContract_period(String contract_period) {
		this.contract_period = contract_period;
	}
	public String getPositive_date() {
		return positive_date;
	}
	public void setPositive_date(String positive_date) {
		this.positive_date = positive_date;
	}
	public String getSign_contract_date() {
		return sign_contract_date;
	}
	public void setSign_contract_date(String sign_contract_date) {
		this.sign_contract_date = sign_contract_date;
	}
	public String getSign_count() {
		return sign_count;
	}
	public void setSign_count(String sign_count) {
		this.sign_count = sign_count;
	}
}
