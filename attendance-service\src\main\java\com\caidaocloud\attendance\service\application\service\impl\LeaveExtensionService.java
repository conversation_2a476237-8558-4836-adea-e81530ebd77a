package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.PreTimeUnitEnum;
import com.caidaocloud.attendance.service.application.enums.QuotaTypeEnum;
import com.caidaocloud.attendance.service.application.service.IGroupService;
import com.caidaocloud.attendance.service.application.service.ILeaveExtensionService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.group.GroupDetailDto;
import com.caidaocloud.attendance.service.interfaces.dto.leaveExtension.*;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.caidaocloud.attendance.service.infrastructure.common.QuotaTimeFormat.formatFloat;

@Slf4j
@Service
public class LeaveExtensionService implements ILeaveExtensionService {

    @Autowired
    private WaLeaveExtensionDo waLeaveExtensionDo;
    @Autowired
    private WaEmpQuotaDo empQuotaDo;
    @Autowired
    private EmpCompensatoryQuotaDo empCompensatoryQuotaDo;
    @Autowired
    private WorkflowCallBackService workflowCallBackService;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    @Lazy
    private IWfService wfService;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private LeaveQuotaConfigDo leaveQuotaConfigDo;
    @Autowired
    private WaLeaveTypeDo leaveTypeDo;
    @Resource
    private LeaveQuotaDo leaveQuotaDo;
    @Autowired
    private IGroupService groupService;
    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    @Transactional
    @Override
    public Result<String> applyLeaveExtension(LeaveExtensionApplyDto dto, UserInfo userInfo) {
        String tenantId = userInfo.getTenantId();
        if (null == dto.getEmpId()) {
            dto.setEmpId(userInfo.getStaffId());
        }
        List<LeaveExtensionApplyItem> items = dto.getItems();
        List<Long> quotaIds = items.stream().map(LeaveExtensionApplyItem::getQuotaId).collect(Collectors.toList());
        List<EmpCompensatoryQuotaDo> quotaList = Lists.newArrayList();
        quotaList.addAll(getEmpCompensatoryQuotas(dto.getEmpId(), quotaIds));
        quotaList.addAll(getEmpQuotas(tenantId, dto.getEmpId(), quotaIds));
        if (CollectionUtils.isEmpty(quotaList)) {//申请配额为无效数据
            return ResponseWrap.wrapResult(AttendanceCodes.EXTENSION_QUOTA_INVALID, null);
        }
        if (dto.getItems().size() != quotaList.size()) {//申请延期配额与实际配额数不匹配
            return ResponseWrap.wrapResult(AttendanceCodes.EXTENSION_QUOTA_NOT_MATCH, null);
        }
        Map<Long, EmpCompensatoryQuotaDo> quotaMap = quotaList.stream().collect(Collectors.toMap(EmpCompensatoryQuotaDo::getQuotaId, Function.identity(), (k1, k2) -> k2));
        List<WaLeaveExtensionDo> leaveExtensionList = Lists.newArrayList();
        for (LeaveExtensionApplyItem item : items) {
            EmpCompensatoryQuotaDo quota = quotaMap.get(item.getQuotaId());
            leaveExtensionList.add(getWaLeaveExtensionDo(dto, userInfo, item, quota));
        }
        long currentTime = DateUtil.getCurrentTime(true);
        String wfBusKey = null;
        for (WaLeaveExtensionDo leaveExtension : leaveExtensionList) {
            waLeaveExtensionDo.save(leaveExtension);
            Result<Boolean> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.LEAVE_EXTENSION.getCode());
            if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
                return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_CONFIG_ERR, null);
            }
            SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(leaveExtension.getEmpId());
            wfBusKey = String.format("%s_%s", leaveExtension.getId(), BusinessCodeEnum.LEAVE_EXTENSION.getCode());
            Boolean workflowEnabledResultData = checkWorkflowEnableResult.getData();
            if (!workflowEnabledResultData) {
                WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
                wfCallbackResultDto.setBusinessKey(wfBusKey);
                wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
                wfCallbackResultDto.setTenantId(tenantId);
                workflowCallBackService.saveWfLeaveExtensionApproval(wfCallbackResultDto);
            } else {
                WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
                wfBeginWorkflowDto.setFuncCode(BusinessCodeEnum.LEAVE_EXTENSION.getCode());
                wfBeginWorkflowDto.setBusinessId(leaveExtension.getId().toString());
                wfBeginWorkflowDto.setApplicantId(empInfo.getEmpid().toString());
                wfBeginWorkflowDto.setApplicantName(empInfo.getEmpName());
                wfBeginWorkflowDto.setEventTime(currentTime * 1000);
                try {
                    Result<?> result = wfRegisterFeign.begin(wfBeginWorkflowDto);
                    if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                        WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
                        wfCallbackResultDto.setBusinessKey(wfBusKey);
                        wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
                        wfCallbackResultDto.setTenantId(tenantId);
                        workflowCallBackService.saveWfLeaveExtensionApproval(wfCallbackResultDto);
                    }
                } catch (Exception e) {
                    log.error("发起工作流失败{}", e.getMessage(), e);
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, "").getMsg());
                }
            }
        }
        return Result.ok(wfBusKey);
    }

    private WaLeaveExtensionDo getWaLeaveExtensionDo(LeaveExtensionApplyDto dto, UserInfo userInfo, LeaveExtensionApplyItem item, EmpCompensatoryQuotaDo quota) {
        WaLeaveExtensionDo leaveExtension = new WaLeaveExtensionDo();
        leaveExtension.setId(snowflakeUtil.createId());
        leaveExtension.setTenantId(userInfo.getTenantId());
        leaveExtension.setQuotaId(item.getQuotaId());
        leaveExtension.setEmpId(dto.getEmpId());
        leaveExtension.setLeaveTypeId(quota.getLeaveTypeId());
        leaveExtension.setConfigId(dto.getConfigId());
        leaveExtension.setTimeDuration(item.getTimeDuration());
        leaveExtension.setTimeUnit(item.getTimeUnit());
        leaveExtension.setStartDate(item.getStartDate());
        leaveExtension.setEndDate(item.getEndDate());
        leaveExtension.setOriginalEndDate(quota.getLastDate());
        leaveExtension.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        leaveExtension.setReason(item.getReason());
        if (StringUtil.isNotBlank(dto.getFileName())) {
            leaveExtension.setFileName(dto.getFileName());
        }
        if (StringUtil.isNotBlank(dto.getFile())) {
            leaveExtension.setFileId(dto.getFile());
        }
        leaveExtension.setDeleted(0);
        leaveExtension.setCreateBy(userInfo.getUserId());
        leaveExtension.setCreateTime(DateUtil.getCurrentTime(true));
        return leaveExtension;
    }

    private List<EmpCompensatoryQuotaDo> getEmpCompensatoryQuotas(Long empId, List<Long> quotaIds) {
        List<EmpCompensatoryQuotaDo> list = empCompensatoryQuotaDo.getEmpCompensatoryQuotaList(quotaIds);
        return list.stream().filter(l -> l.getEmpId().equals(empId)).collect(Collectors.toList());
    }

    private List<EmpCompensatoryQuotaDo> getEmpQuotas(String tenantId, Long empId, List<Long> quotaIds) {
        List<WaEmpQuotaDo> list = empQuotaDo.getEmpQuotaList(tenantId, quotaIds);
        list = list.stream().filter(l -> l.getEmpid().equals(empId)).collect(Collectors.toList());
        List<EmpCompensatoryQuotaDo> quotaList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(list)) {
            return quotaList;
        }
        list.forEach(quota -> {
            EmpCompensatoryQuotaDo compensatoryQuotaDo = new EmpCompensatoryQuotaDo();
            compensatoryQuotaDo.setQuotaId(Long.valueOf(quota.getEmpQuotaId()));
            compensatoryQuotaDo.setStartDate(quota.getStartDate());
            compensatoryQuotaDo.setLastDate(quota.getLastDate());
            compensatoryQuotaDo.setLeaveTypeId(Integer.valueOf(quota.getLeaveTypeId().toString()));
            compensatoryQuotaDo.setConfigId(quota.getConfigId());
            quotaList.add(compensatoryQuotaDo);
        });
        return quotaList;
    }

    @Transactional
    @Override
    public Result<Boolean> revokeLeaveExtension(LeaveExtensionRevokeDto dto, UserInfo userInfo) {
        String[] businessKeys = dto.getBusinessKey().split("_");
        String businessId = businessKeys[0];
        Long id = Long.valueOf(businessId);
        // 判断是否是有效地假期延期申请单据
        Optional<WaLeaveExtensionDo> leaveExtensionOpt = Optional.ofNullable(waLeaveExtensionDo.selectByPrimaryKey(id));
        if (!leaveExtensionOpt.isPresent()) {
            return ResponseWrap.wrapResult(AttendanceCodes.APPLY_NOT_EXIST, Boolean.FALSE);
        }
        WaLeaveExtensionDo model = leaveExtensionOpt.get();
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(model.getStatus())) {
            return ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE);
        }
        // 只允许撤销审批中的单据
        if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(model.getStatus())) {
            return ResponseWrap.wrapResult(AttendanceCodes.NOT_ALLOW_REVOCATION, Boolean.FALSE);
        }
        Long userId = userInfo.getUserId();
        Long current = DateUtil.getCurrentTime(true);
        String businessKey = String.format("%s_%s", businessId, BusinessCodeEnum.LEAVE_EXTENSION.getCode());
        WfRevokeDto revokeDto = new WfRevokeDto();
        revokeDto.setBusinessKey(businessKey);
        Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
        if (null == result || !result.isSuccess()) {
            return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE);
        }
        //更新单据状态
        model.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
        model.setRevokeReason(dto.getRevokeReason());
        model.setUpdateBy(userId);
        model.setUpdateTime(current);
        waLeaveExtensionDo.update(model);
        return Result.ok(Boolean.TRUE);
    }

    @Override
    public PageList<LeaveExtensionDto> getEmpLeaveExtensionList(LeaveExtensionReqDto dto, UserInfo userInfo) {
        PageBean pageBean = PageUtil.getPageBean(dto);
        Map<String, Object> map = new HashMap<>();
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("orgid\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        map.put("filter", filter);
        map.put("tenantId", userInfo.getTenantId());
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            map.put("keywords", pageBean.getKeywords());
        }
        if (null != dto.getEmpId()) {
            map.put("empId", dto.getEmpId());
        }
        map.put("dataFilter", dto.getDataScope());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        PageList<WaLeaveExtensionDo> pageList = waLeaveExtensionDo.getEmpLeaveExtensionList(pageBounds, map);
        List<LeaveExtensionDto> items = org.apache.commons.compress.utils.Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(pageList)) {
            items = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(LeaveExtensionDto.class);
            items.forEach(item -> {
                Integer timeUint = item.getTimeUnit();
                item.setTimeUnitName(PreTimeUnitEnum.getName(timeUint));
                item.setStatusName(ApprovalStatusEnum.getName(item.getStatus()));
                item.setBusinessKey(String.format("%s_%s", item.getId(), BusinessCodeEnum.LEAVE_EXTENSION.getCode()));
                item.setQuotaName(LangParseUtil.getI18nLanguage(item.getI18nRuleName(), item.getQuotaName()));
            });
        }
        return new PageList<>(items, pageList.getPaginator());
    }

    @Override
    public List<KeyValue> getLeaveExtensionQuotaType() {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        List<LeaveQuotaConfigDo> models = leaveQuotaConfigDo.getConfigListByIds(userInfo.getTenantId(), null);
        if (CollectionUtils.isEmpty(models)) {
            return Lists.newArrayList();
        }
        GroupDetailDto groupDetail = groupService.getEmpGroup(userInfo.getTenantId(), userInfo.getEmpId(), DateUtil.getCurrentTime(true));
        if (null == groupDetail) {
            return Lists.newArrayList();
        }
        if (null == groupDetail.getLeaveTypeIds()) {
            return Lists.newArrayList();
        }
        Integer[] leaveTypeIdArr = (Integer[]) groupDetail.getLeaveTypeIds();
        if (leaveTypeIdArr == null || leaveTypeIdArr.length == 0) {
            return Lists.newArrayList();
        }
        List<Integer> leaveTypeIds = Arrays.stream(leaveTypeIdArr).collect(Collectors.toList());
        models = models.stream().filter(config -> config.getLeaveExtension() != null && config.getLeaveExtension() && leaveTypeIds.contains(config.getLeaveTypeId())).collect(Collectors.toList());
        List<KeyValue> list = Lists.newArrayList();
        models.forEach(config -> list.add(new KeyValue(LangParseUtil.getI18nLanguage(config.getI18nRuleName(), config.getRuleName()), config.getConfigId())));
        return list;
    }

    @Override
    public List<LeaveExtensionQuotaDto> getLeaveExtensionQuotaList(LeaveExtensionQuota dto, UserInfo userInfo) throws Exception {
        String tenantId = userInfo.getTenantId();
        Optional<LeaveQuotaConfigDo> leaveQuotaConfigOpt = Optional.ofNullable(leaveQuotaConfigDo.getConfigById(tenantId, dto.getConfigId()));
        List<LeaveExtensionQuotaDto> quotas = Lists.newArrayList();
        if (!leaveQuotaConfigOpt.isPresent()) {
            return quotas;
        }
        LeaveQuotaConfigDo leaveQuotaConfig = leaveQuotaConfigOpt.get();
        Integer leaveTypeId = leaveQuotaConfig.getLeaveTypeId();
        WaLeaveTypeDo leaveType = leaveTypeDo.selectById(leaveTypeId);
        Integer quotaType = leaveType.getQuotaType();
        Long onlyDate = DateUtil.getOnlyDate();
        if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(quotaType)) {//调休
            List<LeaveQuotaDo> compensatoryQuotaList = leaveQuotaDo.getCompensatoryQuotaList(userInfo.getTenantId(), userInfo.getStaffId(), onlyDate, leaveTypeId, dto.getConfigId(), Boolean.TRUE);
            quotas = handleCompensatoryList(compensatoryQuotaList);
        } else {//非调休
            List<LeaveQuotaDo> annualList = leaveQuotaDo.getAnnualLeaveList(userInfo.getTenantId(), userInfo.getStaffId(), leaveTypeId, dto.getConfigId(), quotaType, onlyDate, false);
            quotas = handleAnnualList(annualList);
        }
        if (CollectionUtils.isEmpty(quotas)) {
            return quotas;
        }
        List<Long> quotaIds = quotas.stream().map(LeaveExtensionQuotaDto::getEmpQuotaId).distinct().collect(Collectors.toList());
        List<Integer> status = Lists.newArrayList();
        status.add(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        status.add(ApprovalStatusEnum.PASSED.getIndex());
        List<WaLeaveExtensionDo> leaveExtensionList = waLeaveExtensionDo.getLeaveExtensionList(tenantId, Collections.singletonList(leaveQuotaConfig.getConfigId()), quotaIds, status);
        Map<Long, Long> countMap = leaveExtensionList.stream().collect(Collectors.groupingBy(WaLeaveExtensionDo::getQuotaId, Collectors.counting()));
        List<Long> inProgressLeaveExtensionIds = leaveExtensionList.stream().filter(le -> ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(le.getStatus())).map(WaLeaveExtensionDo::getQuotaId).collect(Collectors.toList());
        Integer extensionTime = leaveQuotaConfig.getExtensionTime();
        Integer extensionUnit = leaveQuotaConfig.getExtensionUnit();
        Integer maxExtension = leaveQuotaConfig.getMaxExtension();
        List<LeaveExtensionQuotaDto> list = Lists.newArrayList();
        for (LeaveExtensionQuotaDto quota : quotas) {
            if (inProgressLeaveExtensionIds.contains(quota.getEmpQuotaId())) {
                continue;
            }
            if (countMap.containsKey(quota.getEmpQuotaId()) && countMap.get(quota.getEmpQuotaId()) >= maxExtension) {
                continue;
            }
            long lastDate = quota.getLastDate();
            long startDate = DateUtil.getOnlyDate(new Date(lastDate * 1000)) + 86400;
            if (extensionUnit == 1) {
                lastDate = DateUtil.addDate(lastDate * 1000, extensionTime);
            } else {
                lastDate = DateUtil.addMonth(lastDate, extensionTime);
            }
            quota.setExtensionStartDate(startDate);
            quota.setExtensionEndDate(lastDate);
            list.add(quota);
        }
        return list;
    }

    public List<LeaveExtensionQuotaDto> handleAnnualList(List<LeaveQuotaDo> quotaList) {
        List<LeaveExtensionQuotaDto> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(quotaList)) {
            return list;
        }
        for (LeaveQuotaDo quotaDo : quotaList) {
            //单位
            Integer unit = quotaDo.getUnit();
            //本年额度
            BigDecimal sum = BigDecimal.valueOf(quotaDo.getQuotaDay() == null ? 0f : quotaDo.getQuotaDay());
            quotaDo.setQuotaDay(formatFloat(unit, sum.floatValue(), 1));
            //当前额度
            BigDecimal nowQuotaDay = BigDecimal.valueOf(quotaDo.getNowQuotaDay() == null ? 0f : quotaDo.getNowQuotaDay());
            quotaDo.setNowQuotaDay(formatFloat(unit, nowQuotaDay.floatValue(), 1));
            //调整配额
            BigDecimal adjustDay = BigDecimal.valueOf(quotaDo.getAdjustQuota() == null ? 0f : quotaDo.getAdjustQuota());
            quotaDo.setAdjustQuota(formatFloat(unit, adjustDay.floatValue(), 1));
            //已使用
            BigDecimal used = BigDecimal.valueOf(quotaDo.getUsedDay() == null ? 0f : quotaDo.getUsedDay());
            quotaDo.setUsedDay(formatFloat(unit, used.floatValue(), 1));
            //调整本年已用
            BigDecimal fixUsedDay = BigDecimal.valueOf(quotaDo.getFixUsedDay() == null ? 0f : quotaDo.getFixUsedDay());
            quotaDo.setFixUsedDay(formatFloat(unit, fixUsedDay.floatValue(), 1));
            //流程中
            BigDecimal inTransit = BigDecimal.valueOf(quotaDo.getInTransitQuota() == null ? 0f : quotaDo.getInTransitQuota());
            quotaDo.setInTransitQuota(formatFloat(unit, inTransit.floatValue(), 1));
            //总配额
            BigDecimal total = sum.add(adjustDay);
            //已用 = 已使用 + 流程中 + 调整本年已用
            BigDecimal leaveUsedDay = used.add(inTransit).add(fixUsedDay);
            //计算本年可用配额
            quotaDo.calAvailableYear();
            //本年余额
            BigDecimal leftDay = BigDecimal.valueOf(quotaDo.getAvailabQuotaDay()).add(adjustDay).subtract(leaveUsedDay);
            leftDay = leftDay.floatValue() < 0 ? BigDecimal.ZERO : leftDay;
            quotaDo.setLeftDay(formatFloat(unit, leftDay.floatValue(), 1));
            // 预支年假 = 本年已用 + 流程中 - 当前额度，老逻辑
            // 已预支年假 = 调整本年已用 + 本年已用 + 流程中 - 当前额度 - 调整额度，新逻辑
            BigDecimal advanceDay = used.add(fixUsedDay).add(inTransit).subtract(nowQuotaDay).subtract(adjustDay);
            if (advanceDay.compareTo(BigDecimal.ZERO) < 0) {
                quotaDo.setAdvanceDay(0f);
            } else {
                quotaDo.setAdvanceDay(advanceDay.floatValue());
            }
            //当前余额
            float currentQuota = nowQuotaDay.add(adjustDay).subtract(leaveUsedDay).floatValue();
            quotaDo.setCurrentQuota(formatFloat(unit, currentQuota < 0 ? 0f : currentQuota, 1));
        }
        return ObjectConverter.convertList(quotaList, LeaveExtensionQuotaDto.class);
    }

    public List<LeaveExtensionQuotaDto> handleCompensatoryList(List<LeaveQuotaDo> quotaList) {
        List<LeaveExtensionQuotaDto> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(quotaList)) {
            return list;
        }
        for (LeaveQuotaDo quotaDo : quotaList) {
            //单位
            Integer unit = quotaDo.getUnit();
            //总额度
            BigDecimal sum = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getQuotaDay()).orElse(0f));
            //已使用
            BigDecimal time = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getUsedDay()).orElse(0f));
            //流程中
            BigDecimal inTransit = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getInTransitQuota()).orElse(0f));
            //调整额度
            BigDecimal adjustQuota = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getAdjustQuota()).orElse(0f));
            //调整已使用
            BigDecimal fixUsedDay = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getFixUsedDay()).orElse(0f));
            //当前额度
            BigDecimal nowQuota = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getNowQuotaDay()).orElse(0f));
            if (PreTimeUnitEnum.HOUR.getIndex().equals(unit)) {
                //当前配额
                sum = sum.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                //已使用
                time = time.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                //流程中
                inTransit = inTransit.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                //调整额度
                adjustQuota = adjustQuota.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                //调整已使用
                fixUsedDay = fixUsedDay.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                //当前额度
                nowQuota = nowQuota.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
            }
            quotaDo.setQuotaDay(sum.floatValue());
            quotaDo.setUsedDay(time.floatValue());
            quotaDo.setInTransitQuota(inTransit.floatValue());
            quotaDo.setAdjustQuota(adjustQuota.floatValue());
            //已用 = 已使用+流程中+调整已使用
            BigDecimal used = time.add(inTransit).add(fixUsedDay);
            //本年余额 = 总额度 - 已用
            BigDecimal left = sum.add(adjustQuota).subtract(used);
            quotaDo.setLeftDay(left.floatValue() < 0 ? 0f : left.floatValue());
            float currentQuota = nowQuota.add(adjustQuota).subtract(used).subtract(fixUsedDay).floatValue();
            quotaDo.setCurrentQuota(currentQuota < 0 ? 0f : currentQuota);
        }
        return ObjectConverter.convertList(quotaList, LeaveExtensionQuotaDto.class);
    }
}
