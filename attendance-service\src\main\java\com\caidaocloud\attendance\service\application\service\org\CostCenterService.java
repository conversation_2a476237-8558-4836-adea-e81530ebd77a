package com.caidaocloud.attendance.service.application.service.org;

import com.caidaocloud.attendance.service.application.dto.emp.CostCenterDto;
import com.caidaocloud.attendance.service.application.feign.WaCostCenterFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CostCenterService {
    @Autowired
    private WaCostCenterFeignClient waCostCenterFeignClient;

    public CostCenterDto getCostCenter(String bid) {
        Result<CostCenterDto> result = waCostCenterFeignClient.getDetail(bid, System.currentTimeMillis());
        CostCenterDto costCenterDto = null;
        if (null == result || !result.isSuccess() || null == (costCenterDto = result.getData())) {
            return costCenterDto;
        }

        return costCenterDto;
    }
}
