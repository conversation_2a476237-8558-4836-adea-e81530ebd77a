package com.caidaocloud.attendance.service.application.enums;

import java.math.BigDecimal;
import java.math.RoundingMode;

public enum OvertimeQuotaUnitEnum {
    MINUTE("按分钟加班") {
        @Override
        public BigDecimal getTime(BigDecimal workingTime, BigDecimal overtimeDuration, float minOvertimeUnit) {
            return overtimeDuration;
        }
    },
    HALF_HOUR("按半小时加班") {
        @Override
        public BigDecimal getTime(BigDecimal workingTime, BigDecimal overtimeDuration, float minOvertimeUnit) {
            return overtimeDuration.divide(new BigDecimal(30),0, RoundingMode.DOWN).multiply(new BigDecimal(30));
        }
    },
    HOUR("按小时加班") {
        @Override
        public BigDecimal getTime(BigDecimal workingTime, BigDecimal overtimeDuration, float minOvertimeUnit) {
            return overtimeDuration.divide(new BigDecimal(String.valueOf(60 * minOvertimeUnit)),0, RoundingMode.DOWN)
                    .multiply(new BigDecimal(String.valueOf(minOvertimeUnit)))
                    .multiply(new BigDecimal(60));
        }
    },
    HALF_DAY("按半天加班") {
        @Override
        public BigDecimal getTime(BigDecimal workingTime, BigDecimal overtimeDuration, float minOvertimeUnit) {
            workingTime = workingTime.multiply(new BigDecimal(60));
            return overtimeDuration.divide(workingTime, 1, RoundingMode.DOWN).
                    divide(new BigDecimal("0.5"), 0, RoundingMode.DOWN).multiply(new BigDecimal("0.5"));
        }
    },
    DAY("按天加班") {
        @Override
        public BigDecimal getTime(BigDecimal workingTime, BigDecimal overtimeDuration, float minOvertimeUnit) {
            workingTime = workingTime.multiply(new BigDecimal(60));
            return overtimeDuration.divide(new BigDecimal(String.valueOf(60 * minOvertimeUnit)), 0, RoundingMode.DOWN)
                    .multiply(new BigDecimal(String.valueOf(60 * minOvertimeUnit)))
                    .divide(workingTime, 2, RoundingMode.DOWN);
        }
    };
    private String desc;

    OvertimeQuotaUnitEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByOrdinal(int ordinal) {
        for (OvertimeQuotaUnitEnum item : OvertimeQuotaUnitEnum.values()) {
            if (item.ordinal() == ordinal) {
                return item.getDesc();
            }
        }
        return "";
    }

    public abstract BigDecimal getTime(BigDecimal workingTime, BigDecimal overtimeDuration, float minOvertimeUnit);

    public static OvertimeQuotaUnitEnum getByOrdinal(int ordinal) {
        for (OvertimeQuotaUnitEnum item : OvertimeQuotaUnitEnum.values()) {
            if (item.ordinal() == ordinal) {
                return item;
            }
        }
        return OvertimeQuotaUnitEnum.MINUTE;
    }
}
