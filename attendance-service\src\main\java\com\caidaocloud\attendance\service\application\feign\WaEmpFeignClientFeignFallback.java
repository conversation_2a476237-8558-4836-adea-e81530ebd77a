package com.caidaocloud.attendance.service.application.feign;

import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class WaEmpFeignClientFeignFallback implements WaEmpFeginClient {
    @Override
    public Result<EmpWorkInfoVo> getEmpWorkInfo(String empId, Long dataTime) {
        return Result.fail();
    }
}
