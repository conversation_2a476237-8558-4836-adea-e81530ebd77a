package com.caidaocloud.attendance.service.application.enums;

public enum  ShowExpEnum {

    SHOW(1, "展示异常处理"),
    NOT_SHOW(0, "不展示异常处理");
    private Integer index;
    private String name;

    ShowExpEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ShowExpEnum c : ShowExpEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
