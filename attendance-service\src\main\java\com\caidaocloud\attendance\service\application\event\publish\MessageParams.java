package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.message.sdk.enums.NoticeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageParams {
    private String type;
    private String funcType;
    private String title;
    private String processId;
    private String processName;
    private String taskInstId;
    private String taskName;
    private String content;
    private Long applicant;
    private List<Long> handlers;
    private List<KeyValue> customForms;
    private List<String> filePaths;
    private List<String> fileNames;
    private String subType;
    private String taskId;
    private String empThirdId;
    private String corpKey;
    /**
     * 企微跳转页面
     */
    private String linkPage;
    /**
     * 是否发送消息
     */
    private Boolean msgNotice;
    /**
     * 是否发送email
     */
    private Boolean emailNotice;
    /*private List<ApproveRecord> approvalRecords;*/
    /**
     * workItemIds
     */
    private List<String> workItemIds;
    private NoticeType nType;
    private String tenantId;
}
