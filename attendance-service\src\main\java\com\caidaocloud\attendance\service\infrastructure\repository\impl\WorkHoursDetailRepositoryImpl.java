package com.caidaocloud.attendance.service.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.attendance.service.domain.entity.WorkHoursDetailDo;
import com.caidaocloud.attendance.service.domain.repository.IWorkHoursDetailRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkHoursDetailMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WorkHoursDetailPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class WorkHoursDetailRepositoryImpl implements IWorkHoursDetailRepository {

    @Autowired
    private WorkHoursDetailMapper workHoursDetailMapper;

    @Override
    public void delete(String tenantId, List<Long> empIds, Long startDate, Long endDate) {
        if(empIds.isEmpty()){
            return;
        }
        workHoursDetailMapper.delete(new QueryWrapper<WorkHoursDetailPo>()
                .lambda().eq(WorkHoursDetailPo::getTenantId, tenantId)
                .in(WorkHoursDetailPo::getEmpId, empIds)
                .ge(WorkHoursDetailPo::getDate, startDate)
                .le(WorkHoursDetailPo::getDate, endDate));
    }

    @Override
    public void addAll(String tenantId, List<WorkHoursDetailDo> hours) {
        WorkHoursDetailPo.fromDo(tenantId, hours).forEach(it->{
            workHoursDetailMapper.insert(it);
        });
    }

    @Override
    public List<WorkHoursDetailDo> list(String tenantId, List<Long> empIds, Long startDate, Long endDate) {
        return workHoursDetailMapper.selectList(new QueryWrapper<WorkHoursDetailPo>()
                .lambda().eq(WorkHoursDetailPo::getTenantId, tenantId)
                .in(WorkHoursDetailPo::getEmpId, empIds)
                .ge(WorkHoursDetailPo::getDate, startDate)
                .le(WorkHoursDetailPo::getDate, endDate)).stream().map(it->{
            WorkHoursDetailDo hoursDetail = new WorkHoursDetailDo();
            hoursDetail.setEmpId(it.getEmpId());
            hoursDetail.setWaGroupId(it.getWaGroupId());
            hoursDetail.setDate(it.getDate());
            hoursDetail.setStdWorkTime(new BigDecimal(it.getStdWorkTimeSt()));
            hoursDetail.setNightShiftDays(new BigDecimal(it.getNightShiftDaysSt()));
            return hoursDetail;
        }).collect(Collectors.toList());
    }
}
