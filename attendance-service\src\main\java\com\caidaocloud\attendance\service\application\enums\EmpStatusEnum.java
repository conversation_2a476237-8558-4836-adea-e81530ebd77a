package com.caidaocloud.attendance.service.application.enums;

public enum EmpStatusEnum {
    ON_THE_JOB(0, "在职"),
    QUIT(1, "离职"),
    ON_TRIAL(2, "试用期");

    private Integer index;
    private String name;

    EmpStatusEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (EmpStatusEnum c : EmpStatusEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return "-";
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
