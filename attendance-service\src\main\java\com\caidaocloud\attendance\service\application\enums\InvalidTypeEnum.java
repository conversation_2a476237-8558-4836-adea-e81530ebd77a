package com.caidaocloud.attendance.service.application.enums;

/**
 * create by Aaron 20220914
 */
public enum InvalidTypeEnum {
    FIXED(1, "固定有效期"), CURRENT_YEAR(2, "当年失效"), NEXT_YEAR(3, "次年失效");
    private Integer index;
    private String desc;

    InvalidTypeEnum(Integer index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getName(int index) {
        for (InvalidTypeEnum c : InvalidTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.desc;
            }
        }
        return null;
    }

    public static InvalidTypeEnum getInvalidType(int index) {
        for (InvalidTypeEnum c : InvalidTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c;
            }
        }
        return null;
    }
}
