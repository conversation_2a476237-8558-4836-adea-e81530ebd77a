package com.caidao1.integrate.entity.TieTong;



public class Client {
    public Client() {
    }

    public static Response execute(Request request) throws Exception {
        request.getHeaders().put("Hik-Request-ID", "artemis&artemis-http-client&1.1.6");
        switch (request.getMethod()) {
            case POST_STRING:
                return HttpUtil.httpPost(request.getHost(), request.getPath(), request.getTimeout(), request.getHeaders(), request.getQuerys(), request.getStringBody(), request.getSignHeaderPrefixList(), request.getAppKey(), request.getAppSecret());
        }
        return null;
    }
}
