package com.caidaocloud.attendance.service.application.event.subscribe;

import com.caidaocloud.attendance.service.application.event.constant.MqConstant;
import com.caidaocloud.attendance.service.application.event.consumer.WorkFlowCallBackDelayConsumer;
import com.caidaocloud.mq.rabbitmq.ConsumerGenerate;
import com.caidaocloud.mq.rabbitmq.consumer.DynamicConsumer;
import com.caidaocloud.mq.rabbitmq.factory.ExchangeType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 工作流回调-消费者
 */
@Slf4j
@Component
public class WorkFlowCallBackDelaySubscribe {
    @Resource
    private ConsumerGenerate consumerGenerate;
    @Resource
    private WorkFlowCallBackDelayConsumer workFlowCallBackDelayConsumer;

    @PostConstruct
    public void initDynamicQueue() {
        try {
            DynamicConsumer dynamicConsumer = consumerGenerate.genConsumer(ExchangeType.DELAY,
                    workFlowCallBackDelayConsumer,
                    MqConstant.EXCHANGE_WFCALLBACK,
                    MqConstant.QUEUE_WFCALLBACK,
                    MqConstant.ROUTING_KEY_WFCALLBACK, false, true, true);
            dynamicConsumer.start();
        } catch (Exception e) {
            log.error("WorkFlowCallBackDelaySubscribe initDynamicQueue error msg={}", e.getMessage(), e);
        }
    }
}
