package com.caidaocloud.attendance.core.wa.dto;

import com.caidao1.wa.mybatis.model.WaOvertimeType;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 *  记录从db取出来的员工对应的考勤分组 分析分组信息
 * <AUTHOR>
 *
 */
@Data
public class WaAnalyzInfo {
	public WaAnalyzInfo() {
	}

	private Long empid;
	private Long start_time;
	private Long end_time; 
	private Integer wa_group_id;
	private Integer cyle_month;
	private Integer cyle_startdate;
	private Double late_cycle;
	private Integer late_unit;
	private Double late_count;
	private Double early_cycle;
	private Integer early_unit;
	private Double early_count;
	private Double absent_limit;
	//加班记录联动考勤分析
	private Boolean ot_parse;
	//休假记录联动考勤分析 true 会拿着休假时长抵扣迟到早退时长，反之不会抵扣
	private Boolean lv_parse;
	private Integer register_miss;
	private Boolean is_analyze_late_early;
	private Boolean is_flexible_working;
	/**
	 * 是否开启加班单日汇总分析
	 */
	private Boolean ot_sum_parse;
	private Integer abnormalType;//异常类型：1早退加迟到，2迟到或早退
//	@JsonProperty("absent_condition_jsonb")
	private List<AbsenceAnalyzeRule> absentConditionRules;
	private Boolean is_outer_sign;
//	@JsonProperty("ot_pase_jsonb")
	private List<OtAnalyzeRule> otParseRules;

	private Map<Integer, WaOvertimeType> otMaps;

	private Integer clock_type;
	private String clock_rule;

	/**
	 * 外勤分析规则：1 出差单 2 外勤打卡 3 出差单联动外勤打卡
	 */
	private Integer outParseRule;

	/**
	 * 弹性分析开关：1 关闭 2 开启
	 */
	private Integer flexibleWorkSwitch;

	/**
	 * 弹性分析规则：1 按弹性区间分析 2 按班次分析
	 */
	private Integer flexibleWorkType;
	private String allowedDateType;
	/**
	 * 外勤联动班次打卡
	 */
	private Boolean fieldClockLinkShift;

	private Integer minLateTime;
	private Short minLateTimeUnit;
	private Integer minEarlyTime;
	private Short minEarlyTimeUnit;
	private Boolean leaveExemptionSwitch;


	private Integer lateTailProcessing;
	private Integer lateTailDecimals;
	private Integer earlyTailProcessing;
	private Integer earlyTailDecimals;

	/**
	 * 考勤方案新增字段当月计薪的休假数据
	 */
	private Integer leaveStatus;

	/**
	 * 统计转换类型：1、按阶梯统计，2、按比例统计，默认值1
	 */
	private Integer statisticType;

	/**
	 * 迟到早退转旷工按比例转换时长，默认1:1转换
	 */
	private Integer convertTime;

	/**
	 * 迟到早退转旷工按比例转换比例，默认1:1转换
	 */
	private Integer convertScale;

	/**
	 * 迟到早退转旷工上限，超过上限转旷工，默认1:1转换
	 */
	private Integer convertTimeLimit;

	/**
	 * 迟到早退按比例转换迟到早退，默认1:1转换
	 */
	private Integer expConvertScale;

	/**
	 * 迟到早退按比例转换旷工比例(旷工值)，默认1:1转换
	 */
	private Integer convertKgScale;

	/**
	 * 是否允许选择加班归属日期: true 允许 false 不允许
	 */
	private Boolean selectOvertimeDate;
}
