package com.caidaocloud.attendance.core.wa.dto;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.*;

@Data
public class WorkTimeCalendar {
    private Long empid;
    private Integer id;
    private String code;
    private Integer date;
//    private Integer day;
    //1、工作日，2休息日，3法定假日，4请假"
    private Integer type;
    private String name;

    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        //允许没有定义的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //允许使用未带引号的字段名
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //允许使用单引号
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
    }
    public static List<WorkTimeCalendar> convertWorkTimeJsonToObject(Long empid,String worktimejsonb,Integer month) {
        List<WorkTimeCalendar> list = new ArrayList<>();
        if(StringUtils.isNotBlank(worktimejsonb)){
            Map<String,Object> worktimes = null;
            try {
                worktimes = objectMapper.readValue(worktimejsonb, new TypeReference<Map<String,Object>>() {});
            } catch (IOException e) {
                e.printStackTrace();
            }
            for (int i = 1; i <= 31 ; i++) {
                String ymd = month + String.format("%02d", i);
                if(!worktimes.containsKey(ymd.concat("_date"))){
                    continue;
                }
                Integer id = (Integer)worktimes.get(ymd.concat("_id"));
                Integer type = (Integer)worktimes.get(ymd.concat("_type"));
                String name = (String)worktimes.get(ymd.concat("_name"));
                String code = (String)worktimes.get(ymd.concat("_code"));
                WorkTimeCalendar c = new WorkTimeCalendar();
                c.setEmpid(empid);c.setId(id);c.setDate(Integer.parseInt(ymd));c.setType(type);c.setName(name);c.setCode(code);
                list.add(c);
            }
        }
        return  list;
    }

    public static String convertObjectToWorktimeJson(List<WorkTimeCalendar> calendars) throws JsonProcessingException {
        if(CollectionUtils.isNotEmpty(calendars)){
            Map<String,Object> json = new LinkedHashMap<>();
            for (WorkTimeCalendar calendar : calendars){
                json.put(calendar.getDate()+"_id",calendar.getId());
                json.put(calendar.getDate()+"_date",calendar.getDate());
                json.put(calendar.getDate()+"_code",calendar.getCode());
                json.put(calendar.getDate()+"_type",calendar.getType());
                json.put(calendar.getDate()+"_name",calendar.getName());
            }
            return objectMapper.writeValueAsString(json);
        }
        return null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkTimeCalendar that = (WorkTimeCalendar) o;
        return empid.equals(that.empid) &&
                date.equals(that.date);
    }

    @Override
    public int hashCode() {
        return Objects.hash(empid, date);
    }
//     "20191014_date": "排班日期20191014",
//     "20191014_code": "班次代码",
//     "20191014_id": "班次ID",
//     "20191014_day": "天 14",
//     "20191014_name": "班次名称",
//     "20191014_type": "
}
