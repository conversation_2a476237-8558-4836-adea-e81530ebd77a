package com.caidaocloud.attendance.core.commons.utils;

import com.baidu.aip.ocr.AipOcr;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.util.Pair;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class OcrUtil {

    //设置APPID/AK/SK
    public static final String APP_ID = "14602281";
    public static final String API_KEY = "GtRVS2zSgSds5e6VZRva2acT";
    public static final String SECRET_KEY = "0uVQuS6UccpVKK4Efmi25tvq2T5FL4TG";
    private static AipOcr client;

    static {
        client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);
    }

    public static List<Pair> scanIdCard(String image) throws Exception {
        // 初始化一个AipOcr
//        AipOcr client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);

        // 可选：设置网络连接参数
//        client.setConnectionTimeoutInMillis(2000);
//        client.setSocketTimeoutInMillis(60000);

        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<String, String>();
        options.put("detect_direction", "true");
        options.put("detect_risk", "false");

        String idCardSide = "front";

        // 参数为本地图片路径
//        String image = "/Users/<USER>/Documents/WechatIMG371.jpeg";
        JSONObject idInfo = client.idcard(image, idCardSide, options);
//        System.out.println(res.toString(2));

        List<Pair> fields = new ArrayList<>();
        JSONObject words_result = idInfo.getJSONObject("words_result");
        fields.add(Pair.of("empName", getInfoString(words_result, "姓名")));
        String gender = getInfoString(words_result, "性别");
        if (gender.equals("男")) {
            gender = "67:" + gender;
        } else {
            gender = "66:" + gender;
        }
        fields.add(Pair.of("gender", gender));

        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String birthdate = getInfoString(words_result, "出生");
        if (StringUtils.isNotBlank(birthdate)) {
            Date date = format.parse(birthdate);
            fields.add(Pair.of("birthDate", date.getTime() / 1000L));
        }
        fields.add(Pair.of("homeAddress", getInfoString(words_result, "住址")));
        fields.add(Pair.of("idCardNo", getInfoString(words_result, "公民身份号码")));
        return fields;
    }

    private static String getInfoString(JSONObject words_result, String key) {
        if (words_result.has(key)) {
            return words_result.getJSONObject(key).getString("words");
        }
        return "";
    }

    private static String getJSONObjectValue(JSONObject result, String key) {
        if (result.has(key)) {
            Object rt = result.get(key);
            return rt.toString();
        }
        return "";
    }

    public static List<Pair> scanBankCard(String image) throws Exception {
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<String, String>();
        // 参数为本地图片路径
        JSONObject idInfo = client.bankcard(image, options);
        System.out.println(idInfo.toString(2));

        List<Pair> fields = new ArrayList<>();
        JSONObject words_result = idInfo.getJSONObject("result");

        fields.add(Pair.of("bank_card_number", getJSONObjectValue(words_result, "bank_card_number")));

        fields.add(Pair.of("bank_name", getJSONObjectValue(words_result, "bank_name")));
        fields.add(Pair.of("bank_card_type", getJSONObjectValue(words_result, "bank_card_type")));
        return fields;
    }

    public static void main(String[] args) {
        String image = "/IMG_20160811_153529.jpg";

        try {
            List<Pair> list = OcrUtil.scanBankCard(image);
            list.forEach(System.out::println);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
