package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.wa.dto.EmpLeaveInfo;
import com.caidaocloud.attendance.service.domain.repository.IWaEmpLeaveRepository;
import com.caidaocloud.dto.PageResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 休假每日明细
 */
@Slf4j
@Data
@Service
public class WaLeaveDaytimeDo {
    private Integer leaveDaytimeId;

    private Integer leaveTimeId;

    private Integer leaveId;

    private Long leaveDate;

    private String shalfDay;

    private String ehalfDay;

    private Integer startTime;

    private Integer endTime;

    private Short periodType;

    private Float timeDuration;

    private Short timeUnit;

    private Integer dateType;

    private Long realDate;

    private Integer shiftDefId;

    private Float applyTimeDuration;

    private Float beforeAdjustTimeDuration;

    private Float cancelTimeDuration;

    private Integer useShiftDefId;

    //wa_leave_daytime表结构之外的字段
    private Long shiftStartTime;
    private Long shiftEndTime;
    private String leaveTypeCode;
    private String leaveName;
    private Integer status;
    private Long applyTime;
    private String i18nLeaveName;
    private Long empid;
    private Long createTime;

    @Autowired
    private IWaEmpLeaveRepository waEmpLeaveRepository;

    public List<WaLeaveDaytimeDo> getEmpLeaveDaytimeList(Long empid, Long daytime, List<Integer> statusList) {
        return waEmpLeaveRepository.getEmpLeaveDaytimeList(empid, daytime, statusList);
    }

    public List<WaLeaveDaytimeDo> getEmpLeaveDay(Long empId, Long startDate, Long endDate, List<Integer> statusList) {
        return waEmpLeaveRepository.getEmpLeaveDay(empId, startDate, endDate, statusList);
    }

    public List<WaLeaveDaytimeDo> getByDate(Integer leaveId, Long date) {
        return waEmpLeaveRepository.getLeaveDayTimeByDay(leaveId, date);
    }

    public int update(WaLeaveDaytimeDo daytimeDo) {
        return waEmpLeaveRepository.updateDayTime(daytimeDo);
    }

    public PageResult<WaLeaveDaytimeDo> getLeaveDayTimePage(AttendanceBasePage basePage,
                                                            Long startTime, Long endTime,
                                                            List<Integer> statusList) {
        return waEmpLeaveRepository.selectDayTimePage(basePage, startTime, endTime, statusList);
    }

    public List<EmpLeaveInfo> getEmpLeaveDayTimeList(String tenantId, List<Long> empIdList, Long startDate, Long endDate) {
        return waEmpLeaveRepository.selectEmpLeaveDayTimeList(tenantId, empIdList, startDate, endDate);
    }
}
