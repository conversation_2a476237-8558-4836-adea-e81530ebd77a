package com.caidaocloud.attendance.core.annoation.feign;

import com.caidao1.wa.dto.EmpCalendarInfoDto;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpCalendarShiftQueryDto;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpRelCalendarQueryDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 工作日历
 *
 * <AUTHOR>
 * @Date 2025/2/14
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = WorkCalendarFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "workCalendarFeignClient")
public interface WorkCalendarFeignClient {

    /**
     * 查询员工所匹配的工作日历
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/api/attendance/workcalendar/v1/listEmpRelCalendar")
    Result<List<EmpCalendarInfoDto>> listEmpRelCalendar(@RequestBody ListEmpRelCalendarQueryDto queryDto);

    /**
     * 员工考勤日历
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/api/attendance/workcalendar/v1/getEmpCalendarShiftList")
    Result<List<Map>> getEmpCalendarShiftList(@RequestBody ListEmpCalendarShiftQueryDto queryDto);

    /**
     * 查询员工日历班次调整
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/api/attendance/workcalendar/v1/listEmpChangeShift")
    Result<List<Map>> getEmpCalendarChangeShiftList(@RequestBody ListEmpCalendarShiftQueryDto queryDto);
}
