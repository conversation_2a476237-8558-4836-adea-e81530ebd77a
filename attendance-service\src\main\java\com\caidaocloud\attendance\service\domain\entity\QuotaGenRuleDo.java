package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IQuotaGenRuleRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Data
@Service
public class QuotaGenRuleDo {
    private Long quotaRuleId;

    private String tenantId;

    private Integer leaveTypeId;

    private Long configId;

    private String conditionExp;

    private String conditionNote;

    private Integer quotaVal;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    @Autowired
    private IQuotaGenRuleRepository quotaGenRuleRepository;

    public int save(QuotaGenRuleDo quotaGenRuleDo) {
        return quotaGenRuleRepository.save(quotaGenRuleDo);
    }

    public int delete(String tenantId, Long configId) {
        return quotaGenRuleRepository.delete(tenantId, configId);
    }

    public List<QuotaGenRuleDo> getListByConfigId(String tenantId, Long configId){
        return quotaGenRuleRepository.getListByConfigId(tenantId,configId);
    }

    public List<QuotaGenRuleDo> getListByConfigIds(String tenantId, List<Long> configIds){
        return quotaGenRuleRepository.getListByConfigIds(tenantId,configIds);
    }
}