package com.caidaocloud.attendance.service.application.dto;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/18
 */
@Data
public class StatisticsReportReqDataDto extends AttendanceBasePage {
    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;
    @ApiModelProperty("员工ID")
    private Long empId;
    @ApiModelProperty(value = "报表类型： 1 周报 2 月报", required = true)
    private Integer rptType;
    @ApiModelProperty(value = "开始日期：周报：周一日期 、月报：每个月一号的日期", required = true)
    private Long startDate;

    private List<Long> empIdList;

    private Long endDate;

}
