package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.sdk.dto.SdkLeaveExtensionApplyDTO;
import com.caidaocloud.attendance.sdk.dto.SdkLeaveExtensionQuotaDTO;
import com.caidaocloud.attendance.sdk.dto.SdkLeaveExtensionRevokeDTO;
import com.caidaocloud.attendance.sdk.feign.ILeaveExtensionFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SdkLeaveExtensionService {
    @Autowired
    private ILeaveExtensionFeignClient leaveExtensionFeignClient;

    /**
     * 假期延期申请
     *
     * @param dto
     * @return
     */
    public Result<?> applyLeaveExtension(SdkLeaveExtensionApplyDTO dto) {
        return leaveExtensionFeignClient.applyLeaveExtension(dto);
    }

    /**
     * 假期延期申请撤销
     *
     * @param dto
     * @return
     */
    public Result<?> revokeLeaveExtension(SdkLeaveExtensionRevokeDTO dto) {
        return leaveExtensionFeignClient.revokeLeaveExtension(dto);
    }

    /**
     * 假期延期申请假期类型
     *
     * @return
     */
    public Result<?> getLeaveExtensionQuotaType() {
        return leaveExtensionFeignClient.getLeaveExtensionQuotaType();
    }

    /**
     * 获取假期延期申请额度
     *
     * @return
     */
    public Result<?> getLeaveExtensionQuotaList(SdkLeaveExtensionQuotaDTO dto) {
        return leaveExtensionFeignClient.getLeaveExtensionQuotaList(dto);
    }
}