package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量考勤异常调整详情明细
 */
@Data
public class BatchAnalyseResultAdjustDetailDto {
    @ApiModelProperty("考勤日期")
    private String belongDate;

    @ApiModelProperty("迟到时长")
    private Float originalLateTime;

    @ApiModelProperty("早退时长")
    private Float originalEarlyTime;

    @ApiModelProperty("旷工时长")
    private Integer originalKgWorkTime;

    @ApiModelProperty("原签到时间")
    private String originalSigninTime;

    @ApiModelProperty("原签退时间")
    private String originalSignoffTime;

    @ApiModelProperty("调整后的签到时间")
    private String signinTime;

    @ApiModelProperty("调整后的签退时间")
    private String signoffTime;

    @ApiModelProperty("事由")
    private String reason;
}