package com.caidaocloud.attendance.service.application.event.subscribe;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidaocloud.attendance.service.application.cron.ClockTaskService;
import com.caidaocloud.attendance.service.application.dto.clock.WaWorkCalendarDto;
import com.caidaocloud.attendance.service.infrastructure.util.DateUtil;
import com.caidaocloud.attendance.service.interfaces.dto.WorkCalendarReqDto;
import com.caidaocloud.attendance.service.schedule.application.service.shiftGroup.IWaEmpShiftGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
@RabbitListener(
        bindings = @QueueBinding(
                value = @Queue(value = "attendance.shiftgroup.queue", durable = "true"),
                exchange = @Exchange(value = "attendance.shiftgroup.fac.direct.exchange"),
                key = {"routingKey.shiftgroup"}
        )
)
public class ShiftGroupSubscribe {
    @Resource
    private IWaEmpShiftGroupService waEmpShiftGroupService;

    @RabbitHandler
    public void process(String message) {
        log.info("startShiftGroupSubscribe={}", message);
        try {
            Map<String, String> tenantMap = JSON.parseObject(message, Map.class);
            waEmpShiftGroupService.flushEmpShiftGroup(ConvertHelper.stringConvert(tenantMap.get("tenantId")), 0L);
        } catch (Exception ex) {
            log.error("shiftGroupSubscribeProcessErr,{}", ex.getMessage(), ex);
        }
        log.info("endShiftGroupSubscribe={}", message);
    }
}
