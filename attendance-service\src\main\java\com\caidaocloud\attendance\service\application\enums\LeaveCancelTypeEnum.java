package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

public enum LeaveCancelTypeEnum {

    LEAVE_END(1, "休假确认", AttendanceCodes.LEAVE_CANCEL_TYPE_CONFIRM),
    TIME_ADJUST_PARTIAL(2, "取消部分休假", AttendanceCodes.LEAVE_CANCEL_TYPE_PARTIAL),
    //FILE_ADD(3, "休假确认"),//原附件补充
    LEAVE_CANCEL(4, "取消休假", AttendanceCodes.LEAVE_CANCEL_TYPE_CANCEL),
    TIME_ADJUST(5, "调整时间", AttendanceCodes.LEAVE_CANCEL_TYPE_ADJUST_TIME);

    private Integer index;
    private String name;
    private Integer code;

    LeaveCancelTypeEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(int index) {
        for (LeaveCancelTypeEnum c : LeaveCancelTypeEnum.values()) {
            if (c.getIndex() == index) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return c.name;
            }
        }
        return "";
    }

    public static String getName(Long index) {
        for (LeaveCancelTypeEnum c : LeaveCancelTypeEnum.values()) {
            if (Long.valueOf(c.getIndex()).equals(index)) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return c.name;
            }
        }
        return "";
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
