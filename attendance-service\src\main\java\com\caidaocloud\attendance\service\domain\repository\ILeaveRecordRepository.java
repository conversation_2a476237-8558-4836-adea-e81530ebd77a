package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/21
 */
public interface ILeaveRecordRepository {

    WaEmpLeaveDo getLeaveDetailById(String tenantId, Long leaveId);

    WaEmpLeaveDo getById(Integer leaveId);

    AttendancePageResult<WaEmpLeaveDo> getWaEmpLeaveListForHaveQuota(AttendanceBasePage basePage, String belongOrgId, Integer leaveTypeId,
                                                                     Integer quotaType, Integer leaveId, Long empId);

    void update(WaEmpLeaveDo leaveDo);

    List<WaEmpLeaveDo> getEmpLeaveList(String belongOrgId, List<Integer> status, List<Integer> leaveStatus);

    Integer countLeave(Long empId, Long start, Long end);

    List<Map> selectEmpNonBrjLtList(Long empId, Long startDate, Long endDate);

    List<WaEmpLeaveDo> getWaEmpLeaveListByIds(List<Integer> leaveIds);

    List<WaEmpLeaveDo> getEmpLeaveByEmpIds(List<Long> empIds, Long leaveDate);

    List<WaEmpLeaveDo> getWaEmpLeaveListByLeaveDaytimeIds(List<Long> leaveDaytimeIds);
}
