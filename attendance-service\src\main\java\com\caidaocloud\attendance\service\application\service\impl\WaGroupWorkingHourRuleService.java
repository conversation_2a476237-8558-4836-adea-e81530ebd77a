package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.dto.WorkingHourRuleSaveDto;
import com.caidaocloud.attendance.service.application.enums.WorkingHourRuleWorkTypeEnum;
import com.caidaocloud.attendance.service.domain.entity.WaGroupWorkingHourRuleDo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class WaGroupWorkingHourRuleService {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1L, 1L);

    @Autowired
    private WaGroupWorkingHourRuleDo waGroupWorkingHourRuleDo;

    @Transactional(rollbackFor = Exception.class)
    public Long save(WorkingHourRuleSaveDto saveDto) {
        WaGroupWorkingHourRuleDo workingHourRuleDo = ObjectConverter.convert(saveDto, WaGroupWorkingHourRuleDo.class);
        if (!WorkingHourRuleWorkTypeEnum.COMP.getCode().equals(workingHourRuleDo.getWorkType())) {
            workingHourRuleDo.doClearCompField();
        }
        WaGroupWorkingHourRuleDo originalWorkingHourRuleDo = waGroupWorkingHourRuleDo.getByWaGroupId(UserContext.getTenantId(), saveDto.getWaGroupId());
        if (null != originalWorkingHourRuleDo) {
            workingHourRuleDo.setRuleId(originalWorkingHourRuleDo.getRuleId());
            workingHourRuleDo.setUpdateBy(UserContext.getUserId());
            workingHourRuleDo.setUpdateTime(DateUtil.getCurrentTime(true));
            waGroupWorkingHourRuleDo.updateById(workingHourRuleDo);
        } else {
            workingHourRuleDo.doInitCrtField();
            workingHourRuleDo.setRuleId(snowflakeUtil.createId());
            waGroupWorkingHourRuleDo.save(workingHourRuleDo);
        }
        return workingHourRuleDo.getRuleId();
    }

    public WaGroupWorkingHourRuleDo getByWaGroupId(String tenantId, Integer waGroupId) {
        return waGroupWorkingHourRuleDo.getByWaGroupId(tenantId, waGroupId);
    }

    public int deleteByWaGroupId(String tenantId, Integer waGroupId) {
        WaGroupWorkingHourRuleDo workingHourRuleDo = getByWaGroupId(tenantId, waGroupId);
        if (null == workingHourRuleDo) {
            return 0;
        }
        waGroupWorkingHourRuleDo.deleteById(workingHourRuleDo.getRuleId());
        return 1;
    }
}
