package com.caidaocloud.attendance.service.application.service.msg;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.BaseHeaderDto;
import com.caidaocloud.attendance.service.application.dto.StatisticsReportReqDataDto;
import com.caidaocloud.attendance.service.application.dto.WaAnalyzeStatisticsReportDto;
import com.caidaocloud.attendance.service.application.dto.msg.ClockMsgDto;
import com.caidaocloud.attendance.service.application.dto.msg.WaDailyMsgDto;
import com.caidaocloud.attendance.service.application.dto.msg.WaWeeklyMsgDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.event.publish.DelayMsgPublish;
import com.caidaocloud.attendance.service.application.event.publish.MessageParams;
import com.caidaocloud.attendance.service.application.service.INotifyConfigService;
import com.caidaocloud.attendance.service.application.service.ISobService;
import com.caidaocloud.attendance.service.application.service.IStatisticsService;
import com.caidaocloud.attendance.service.application.service.impl.StatisticsReportService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.AnalysisRuleMapper;
import com.caidaocloud.attendance.service.infrastructure.util.FileUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.MonthAnalysePageDto;
import com.caidaocloud.attendance.service.interfaces.dto.NotifyConfigDto;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordDto;
import com.caidaocloud.attendance.service.interfaces.vo.AttendanceDetailTimeDto;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.paas.common.dto.DynamicPageDto;
import com.caidaocloud.hrpaas.paas.common.feign.DynamicFeignClient;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.oss.dto.UploadResult;
import com.caidaocloud.oss.file.LocalMultipartFile;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class DailyWeeklyMsgService {
    private final static String MINUS_SIGN_SEPARATOR = "-";
    private final static int BATCH_PAGE_SIZE = 500;
    @Resource
    private DelayMsgPublish delayMsgPublish;
    @Resource
    private INotifyConfigService notifyConfigService;
    @Resource
    private AnalysisRuleMapper analysisRuleMapper;
    @Resource
    @Lazy
    private StatisticsReportService statisticsReportService;
    @Resource
    private WaEmpLeaveDo empLeaveDo;
    @Resource
    private WaEmpTravelDo empTravelDo;
    @Resource
    private WaRegisterRecordDo registerRecordDo;
    @Resource
    private WaShiftApplyRecordDo shiftChangeDo;
    @Resource
    private WaShiftDo shiftDo;
    @Autowired
    private WaAnalyzeDo waAnalyzeDo;
    @Resource
    private ISobService sobService;
    @Value("${caidaocloud.msg.abnormalUrl:}")
    private String abnormalUrl;
    @Autowired
    private OssService ossService;
    @Autowired
    private WaGroupDo waGroupDo;
    @Autowired
    @Lazy
    private IStatisticsService statisticsService;
    @Resource
    private DynamicFeignClient dynamicFeignClient;
    private final static int MAX_COLUMN_WIDTH = 65280;

    public void sendDailyTtlMsg() {
        int pageNo = 1;
        List<NotifyConfigDo> list = notifyConfigService.getNotifyPageList(pageNo, BATCH_PAGE_SIZE);
        long nowDate = DateUtil.getOnlyDate(new Date()) - 24 * 60 * 60;
        doDailyMsg(pageNo, list, nowDate);
    }

    public void sendDailyTtlMsg(String tenantId, String date) {
        doDailyMsg(1, getNotifyConfigList(tenantId), getMsgDate(date));
    }

    public void manualSendDailyTtlMsg(String tenantId, String date, boolean dailyNotifyAbnormal) {
        Long nowDate = getMsgDate(date);
        List data = analysisRuleMapper.selectDailyInfo(tenantId, nowDate, 0);
        sendDailyMsg(tenantId, 0, data, nowDate, 0, dailyNotifyAbnormal);
    }

    private List<NotifyConfigDo> getNotifyConfigList(String tenantId) {
        NotifyConfigDto ncd = notifyConfigService.getNotifyConfig(tenantId);
        if (null == ncd) {
            return new ArrayList();
        }

        NotifyConfigDo configDo = ObjectConverter.convert(ncd, NotifyConfigDo.class);
        configDo.setTenantId(tenantId);
        List<NotifyConfigDo> list = Arrays.asList(configDo);
        return list;
    }

    private long getMsgDate(String date) {
        long nowDate = DateUtil.getOnlyDate(new Date());
        if (StringUtil.isNotEmpty(date)) {
            nowDate = DateUtil.convertStringToDateTime(date, DateUtil.default_date_Pattern, true);
        }
        return nowDate;
    }

    public void sendWeeklyTtlMsg(String tenantId, String date) {
        doWeeklyMsg(1, getNotifyConfigList(tenantId), getMsgDate(date));
    }

    public void sendWeeklyTtlMsg(String tenantId) {
        if (null == tenantId) {
            return;
        }
        int pageNo = 1;
        List<NotifyConfigDo> list = notifyConfigService.getNotifyPageList(pageNo, BATCH_PAGE_SIZE);
        list = list.stream().filter(l -> tenantId.equals(l.getTenantId())).collect(Collectors.toList());
        long nowDate = DateUtil.getOnlyDate(new Date());
        doWeeklyMsg(pageNo, list, nowDate);
    }

    private void doDailyMsg(int pageNo, List<NotifyConfigDo> list, long nowDate) {
        if (null == list || list.isEmpty()) {
            return;
        }
        Long currentDate = DateUtil.getOnlyDate();
        long currentTime = System.currentTimeMillis() / 1000;
        for (NotifyConfigDo notifyConfig : list) {
            if (null == notifyConfig.getDailyNotifySwitch()
                    || notifyConfig.getDailyNotifySwitch().equals(0)) {
                // 日报通知开关关闭
                continue;
            }

            if (null == notifyConfig.getDailyNotifyRule() || null == notifyConfig.getDailyNotifyTime()) {
                // 日报规则未配置，则不进行通知
                // 日报通知时间未配置，也不进行通知
                continue;
            }

            int delay = 0;
            if (notifyConfig.getDailyNotifyRule().equals(0)) {
                // 当日推送
                //delay = notifyConfig.getDailyNotifyTime() * 60;
                delay += 24 * 60 * 60 + notifyConfig.getDailyNotifyTime() * 60;
            } else if (notifyConfig.getDailyNotifyRule().equals(1)) {
                // 次日推送
                delay += 24 * 60 * 60 + notifyConfig.getDailyNotifyTime() * 60;
                //delay = notifyConfig.getDailyNotifyTime() * 60;
            }
            Long delayTime = (currentDate + delay - currentTime) * 1000;
            List data = analysisRuleMapper.selectDailyInfo(notifyConfig.getTenantId(), nowDate, 0);
            boolean dailyNotifyAbnormal = notifyConfig.getDailyNotifyAbnormal() != null && notifyConfig.getDailyNotifyAbnormal() == 1;
            // 发送日报消息
            sendDailyMsg(notifyConfig.getTenantId(), delayTime.intValue(), data, nowDate, 0, dailyNotifyAbnormal);
        }
        if (list.size() < BATCH_PAGE_SIZE) {
            return;
        }

        pageNo++;
        list = notifyConfigService.getNotifyPageList(pageNo, BATCH_PAGE_SIZE);
        doDailyMsg(pageNo, list, nowDate);
    }

    private void sendDailyMsg(String tenantId, Integer delay, List<Map> list, Long date, int pageNo, boolean dailyNotifyAbnormal) {
        if (null == list || list.isEmpty()) {
            return;
        }

        List<Long> empIds = list.stream().map(l -> (Long) l.get("empid")).distinct().collect(Collectors.toList());
        //休假
        List<WaEmpLeaveDo> allEmpLeaves = empLeaveDo.getEmpLeaveByEmpIds(empIds, date);
        Map<Long, List<WaEmpLeaveDo>> empLeaveMap = allEmpLeaves.stream().collect(Collectors.groupingBy(WaEmpLeaveDo::getEmpid));
        //出差
        List<WaEmpTravelDo> allEmpTravels = empTravelDo.getEmpTravelByEmpIds(empIds, date);
        Map<Long, List<WaEmpTravelDo>> empTravelMap = allEmpTravels.stream().collect(Collectors.groupingBy(WaEmpTravelDo::getEmpId));
        //补卡
        List<WaRegisterRecordDo> allEmpRegisterRecords = registerRecordDo.getEmpBdkRegisterList(tenantId, empIds, date);
        Map<Long, List<WaRegisterRecordDo>> empRegisterRecordMap = allEmpRegisterRecords.stream().collect(Collectors.groupingBy(WaRegisterRecordDo::getEmpid));
        //调班
        List<WaShiftApplyRecordDo> allEmpShiftChanges = shiftChangeDo.getEmpShiftChangeApplyList(tenantId, empIds, date, date);
        Map<Long, List<WaShiftApplyRecordDo>> empShiftChangeMap = allEmpShiftChanges.stream().collect(Collectors.groupingBy(WaShiftApplyRecordDo::getEmpId));
        //获取班次
        List<Integer> shiftIds = Stream.concat(allEmpShiftChanges.stream().map(WaShiftApplyRecordDo::getOldShiftDefId).filter(Objects::nonNull),
                allEmpShiftChanges.stream().map(WaShiftApplyRecordDo::getNewShiftDefId).filter(Objects::nonNull)).distinct().collect(Collectors.toList());
        List<WaShiftDo> shifts = shiftDo.getWaShiftDefList(tenantId, shiftIds);
        Map<Integer, WaShiftDo> shiftMap = shifts.stream().collect(Collectors.toMap(WaShiftDo::getShiftDefId, Function.identity(), (k1, k2) -> k2));
        Map map = null;
        String checkResult = "";
        int size = list.size();
        try {
            for (int i = 0; i < size; i++) {
                map = list.get(i);
                WaDailyMsgDto waDailyMsgDto = new WaDailyMsgDto();
                waDailyMsgDto.setDelay(delay);
                waDailyMsgDto.setEmpid((Long) map.get("empid"));
                waDailyMsgDto.setTenantId(tenantId);
                waDailyMsgDto.setSummaryDate(DateUtil.getDateStrByTimesamp(date));
                waDailyMsgDto.setShiftName(String.format("%s-%s", getShiftTime(date, (Integer) map.get("start_time")), getShiftTime(date, (Integer) map.get("end_time"))));
                waDailyMsgDto.setCheckHours(doCheckHours(map));
                waDailyMsgDto.setCheckInTime((Long) map.get("mindatetime"));
                waDailyMsgDto.setCheckOutTime((Long) map.get("maxdatetime"));
                waDailyMsgDto.setNowDate(date);
                checkResult = "";
                //是否考勤异常
                boolean isAttendanceErr = false;
                if (StringUtil.isNotEmpty(map.get("late_time")) && !Objects.equals(0f, map.get("late_time"))) {
                    isAttendanceErr = true;
                    checkResult += String.format("迟到%s分钟;", map.get("late_time"));
                }
                if (StringUtil.isNotEmpty(map.get("early_time")) && !Objects.equals(0f, map.get("early_time"))) {
                    isAttendanceErr = true;
                    checkResult += String.format("早退%s分钟;", map.get("early_time"));
                }
                if (StringUtil.isNotEmpty(map.get("kg_work_time")) && !Objects.equals(0, map.get("kg_work_time"))) {
                    isAttendanceErr = true;
                    checkResult += String.format("旷工%s分钟;", map.get("kg_work_time"));
                }
                if (dailyNotifyAbnormal && !isAttendanceErr) {
                    continue;
                }
                if (StringUtil.isNotEmpty(checkResult)) {
                    checkResult = checkResult.substring(0, checkResult.length() - 1);
                } else {
                    checkResult = "正常";
                }
                Long empId = waDailyMsgDto.getEmpid();
                //员工休假申请
                if (empLeaveMap.containsKey(empId)) {
                    List<WaEmpLeaveDo> empLeaves = empLeaveMap.get(empId);
                    String leaveMsg = empLeaves.stream().map(l -> {
                        String timeUnitName = PreTimeUnitEnum.getName(l.getTimeUnit());
                        String approvalName = ApprovalStatusEnum.getName(l.getStatus());
                        Float duration = l.getTimeUnit() == 1 ? l.getTotalTimeDuration() : BigDecimal.valueOf(l.getTotalTimeDuration() / 60).setScale(2, RoundingMode.HALF_UP).floatValue();
                        return String.format("%s%s%s（%s）", l.getLeaveName(), duration, timeUnitName, approvalName);
                    }).collect(Collectors.joining(","));
                    waDailyMsgDto.setEmpLeave(leaveMsg);
                }
                //员工出差申请
                if (empTravelMap.containsKey(empId)) {
                    List<WaEmpTravelDo> empTravels = empTravelMap.get(empId);
                    String travelMsg = empTravels.stream().map(t -> {
                        String timeUnitName = PreTimeUnitEnum.getName(t.getTimeUnit());
                        String approvalName = ApprovalStatusEnum.getName(t.getStatus());
                        Float duration = t.getTimeUnit() == 1 ? t.getTimeDuration() : BigDecimal.valueOf(t.getTimeDuration() / 60).setScale(2, RoundingMode.HALF_UP).floatValue();
                        return String.format("%s%s%s（%s）", t.getTravelType(), duration, timeUnitName, approvalName);
                    }).collect(Collectors.joining(","));
                    waDailyMsgDto.setEmpTravel(travelMsg);
                }
                //员工补卡申请
                if (empRegisterRecordMap.containsKey(empId)) {
                    List<WaRegisterRecordDo> empRegisterRecords = empRegisterRecordMap.get(empId);
                    String registerRecordMsg = empRegisterRecords.stream().map(r -> {
                        String approvalName = ApprovalStatusEnum.getName(r.getApprovalStatus());
                        String timeClock = DateUtil.convertDateTimeToStr(r.getRegDateTime(), "HH:mm", true);
                        return String.format("%s（%s）", timeClock, approvalName);
                    }).collect(Collectors.joining(","));
                    waDailyMsgDto.setEmpFillClock(registerRecordMsg);
                }
                //员工调班申请
                if (empShiftChangeMap.containsKey(empId)) {
                    List<WaShiftApplyRecordDo> empShiftChanges = empShiftChangeMap.get(empId);
                    String shiftChangeMsg = empShiftChanges.stream().map(s -> {
                        String approvalName = ApprovalStatusEnum.getName(s.getStatus());
                        String oldShiftName = "";
                        if (null != s.getOldShiftDefId()) {
                            oldShiftName = shiftMap.containsKey(s.getOldShiftDefId()) ? shiftMap.get(s.getOldShiftDefId()).getShiftDefName() : "";
                        }
                        String newShiftName = shiftMap.containsKey(s.getNewShiftDefId()) ? shiftMap.get(s.getNewShiftDefId()).getShiftDefName() : "";
                        return String.format("%s-%s（%s）", oldShiftName, newShiftName, approvalName);
                    }).collect(Collectors.joining(","));
                    waDailyMsgDto.setEmpShiftChange(shiftChangeMsg);
                }
                waDailyMsgDto.setCheckResult(checkResult);
                pushDailyMsg(waDailyMsgDto);
            }
        } catch (Exception e) {
            log.error("sendDailyMsg err,{}", e.getMessage(), e);
        }

        if (list.size() < 2000) {
            return;
        }

        pageNo++;
        list = analysisRuleMapper.selectDailyInfo(tenantId, date, pageNo * 2000);
        sendDailyMsg(tenantId, delay, list, date, pageNo, dailyNotifyAbnormal);
    }

    private String getShiftTime(Long date, Integer time) {
        if (null == time) {
            return "";
        }

        return DateUtil.convertDateTimeToStr(date + time * 60, "HH:mm", true);
    }

    private String minuteToHour(Integer num) {
        if (null == num || num.equals(0)) {
            return MINUS_SIGN_SEPARATOR;
        }

        return String.format(ResponseWrap.wrapResult(AttendanceCodes.UNIT_HOUR, null).getMsg(), BigDecimal.valueOf(num).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
    }

    private String minuteToHour(Float num) {
        if (null == num || num.equals(0f)) {
            return MINUS_SIGN_SEPARATOR;
        }

        return String.format(ResponseWrap.wrapResult(AttendanceCodes.UNIT_HOUR, null).getMsg(), BigDecimal.valueOf(num).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toPlainString());
    }

    private String doCheckHours(Map map) {
        if (null == map) {
            return MINUS_SIGN_SEPARATOR;
        }
        Float actualWorkTime = (Float) map.getOrDefault("actual_work_time", 0f);
        return minuteToHour(actualWorkTime);
    }

    private long getMonDay(long dateTime, boolean isUnix) {
        if (isUnix) {
            dateTime *= 1000;
        }
        Calendar calendar= Calendar.getInstance();
        calendar.setTime(new Date(dateTime));
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return isUnix ? calendar.getTimeInMillis() / 1000 : calendar.getTimeInMillis();
    }

    private void doWeeklyMsg(int pageNo, List<NotifyConfigDo> list, long nowDate) {
        if (null == list || list.isEmpty()) {
            return;
        }
        /*int delay = 210 * 60 * 1000;
        long rptStartDate = nowDate - 7 * 24 * 60 * 60;*/
        StatisticsReportReqDataDto statisticsReportReqDataDto = null;
        for (NotifyConfigDo notifyConfig : list) {
            if (null == notifyConfig.getWeeklyNotifySwitch() || notifyConfig.getWeeklyNotifySwitch().equals(0)) {
                // 周报通知开关关闭
                continue;
            }
            int delay = 0;
            Integer notifyType = notifyConfig.getWeeklyNotifyType();
            if (null == notifyType) {
                continue;
            }
            long rptStartDate = getMonDay(nowDate, true);
            if (2 == notifyType) {
                rptStartDate -= 7 * 24 * 60 * 60;
            }
            statisticsReportReqDataDto = new StatisticsReportReqDataDto();
            statisticsReportReqDataDto.setTenantId(notifyConfig.getTenantId());
            statisticsReportReqDataDto.setStartDate(rptStartDate);
            statisticsReportReqDataDto.setPageNo(1);
            statisticsReportReqDataDto.setRptType(1);
            statisticsReportReqDataDto.setPageSize(2000);
            AttendancePageResult<WaAnalyzeStatisticsReportDto> data = statisticsReportService.getAnalyzeStatisticsReportPageList(statisticsReportReqDataDto);
            if (null == data) {
                continue;
            }
            // 发送周报消息
            sendWeeklyMsg(notifyConfig.getTenantId(), delay, data.getItems(), rptStartDate, statisticsReportReqDataDto.getPageNo());
        }
        if (list.size() < 500) {
            return;
        }
        pageNo++;
        list = notifyConfigService.getNotifyPageList(pageNo, 500);
        doWeeklyMsg(pageNo, list, nowDate);
    }

    private void sendWeeklyMsg(String tenantId, Integer delay, List<WaAnalyzeStatisticsReportDto> list, Long date, int pageNo) {
        if (null == list || list.isEmpty()) {
            return;
        }
        WaAnalyzeStatisticsReportDto wasrd = null;
        long weeklyDate = date;//date - 7 * 24 * 60 * 60;
        int size = list.size();
        try {
            for (int i = 0; i < size; i++) {
                wasrd = list.get(i);
                WaWeeklyMsgDto waWeeklyMsgDto = new WaWeeklyMsgDto();
                waWeeklyMsgDto.setDelay(delay);
                waWeeklyMsgDto.setEmpid(wasrd.getEmpId());
                waWeeklyMsgDto.setTenantId(tenantId);
                waWeeklyMsgDto.setSummaryDate(weeklySummaryDate(date));
                waWeeklyMsgDto.setShiftWorkingHours(minuteToHour(wasrd.getWorkTime()));
                waWeeklyMsgDto.setCheckHours(minuteToHour(wasrd.getActualWorkTime()));
                waWeeklyMsgDto.setLateTime(minuteToHour(wasrd.getLateTime()));
                waWeeklyMsgDto.setEarlyLeaveHours(minuteToHour(wasrd.getEarlyTime()));
                waWeeklyMsgDto.setMinerHours(minuteToHour(wasrd.getKgWorkTime()));
                waWeeklyMsgDto.setOvertimeHours(minuteToHour(wasrd.getOtTime()));
                waWeeklyMsgDto.setVacationCount(null == wasrd.getLeaveCount()
                        ? MINUS_SIGN_SEPARATOR : wasrd.getLeaveCount().toString());
                waWeeklyMsgDto.setCheckMmissTimes(null == wasrd.getBdkCount()
                        ? MINUS_SIGN_SEPARATOR : wasrd.getBdkCount().toString());
                waWeeklyMsgDto.setWeeklyDate(weeklyDate);
                pushWeeklyMsg(waWeeklyMsgDto);
            }
        } catch (Exception e) {
            log.error("sendWeeklyMsg err,{}", e.getMessage(), e);
        }

        if (list.size() < 2000) {
            return;
        }

        pageNo++;

        StatisticsReportReqDataDto wtatisticsReportReqDataDto = new StatisticsReportReqDataDto();
        wtatisticsReportReqDataDto.setRptType(1);
        wtatisticsReportReqDataDto.setTenantId(tenantId);
        wtatisticsReportReqDataDto.setStartDate(date);
        wtatisticsReportReqDataDto.setPageNo(pageNo * 2000);
        wtatisticsReportReqDataDto.setPageSize(2000);
        AttendancePageResult<WaAnalyzeStatisticsReportDto> data =
                statisticsReportService.getAnalyzeStatisticsReportPageList(wtatisticsReportReqDataDto);
        if (null == data) {
            return;
        }

        sendWeeklyMsg(tenantId, delay, data.getItems(), date, pageNo);
    }

    public void pushDailyMsg(List<WaDailyMsgDto> waDailyMsgDtos) {
        if (null == waDailyMsgDtos || waDailyMsgDtos.isEmpty()) {
            return;
        }

        waDailyMsgDtos.forEach(waDailyMsgDto -> pushDailyMsg(waDailyMsgDto));
    }

    public void pushDailyMsg(WaDailyMsgDto waDailyMsgDto) {
        try {
            sendDailyMsg(waDailyMsgDto);
        } catch (Exception e) {
            log.error("send Daily message=[{}] err,{}", FastjsonUtil.toJson(waDailyMsgDto), e.getMessage(), e);
        }
    }

    public void pushWeeklyMsg(List<WaWeeklyMsgDto> waWeeklyMsgDtos) {
        if (null == waWeeklyMsgDtos || waWeeklyMsgDtos.isEmpty()) {
            return;
        }

        waWeeklyMsgDtos.forEach(waWeeklyMsgDto -> pushWeeklyMsg(waWeeklyMsgDto));
    }

    public void pushWeeklyMsg(WaWeeklyMsgDto waWeeklyMsgDto) {
        try {
            sendWeekyMsg(waWeeklyMsgDto);
        } catch (Exception e) {
            log.error("send Weekly message=[{}] err,{}", FastjsonUtil.toJson(waWeeklyMsgDto), e.getMessage(), e);
        }
    }

    private void sendDailyMsg(WaDailyMsgDto waDailyMsgDto) {
        log.info("Start to send Daily message");
        List<Long> receiver = new ArrayList<>();
        receiver.add(Long.parseLong(waDailyMsgDto.getEmpid().toString()));
        MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                .type("msg")
                .subType("daily")
                .title(String.format("%s考勤日报", waDailyMsgDto.getSummaryDate()))
                .funcType("2")
                .handlers(receiver).processId(String.valueOf(waDailyMsgDto.getNowDate()))
                .customForms(getDailyFormCustoms(waDailyMsgDto))
                .nType(NoticeType.ATTENDANCE_DAILY_REPORT_MSG).tenantId(waDailyMsgDto.getTenantId());
        MessageParams messageParams = builder.build();
        String jsonStr = FastjsonUtil.toJson(messageParams);
        delayMsgPublish.publish(jsonStr, waDailyMsgDto.getDelay(), waDailyMsgDto.getTenantId());
        log.info("Success to send Daily message, messageParams[{}], x-delay = [{}]",
                jsonStr, waDailyMsgDto.getDelay());
    }

    /**
     * 日报消息模版
     *
     * @param waDailyMsgDto
     * @return
     */
    private List<KeyValue> getDailyFormCustoms(WaDailyMsgDto waDailyMsgDto) {
        List<KeyValue> list = new ArrayList<>();
        list.add(new KeyValue("班次", waDailyMsgDto.getShiftName()));
        list.add(new KeyValue("签到时间", Objects.equals(waDailyMsgDto.getCheckInTime(), null) ? MINUS_SIGN_SEPARATOR : DateUtil.getTimeStrByTimesamp4(waDailyMsgDto.getCheckInTime())));

        list.add(new KeyValue("签退时间", Objects.equals(waDailyMsgDto.getCheckInTime(), waDailyMsgDto.getCheckOutTime()) ?
                MINUS_SIGN_SEPARATOR : DateUtil.getTimeStrByTimesamp4(waDailyMsgDto.getCheckOutTime())));

        list.add(new KeyValue("出勤结果", waDailyMsgDto.getCheckResult()));
        list.add(new KeyValue("出勤时长", waDailyMsgDto.getCheckHours()));
        if (StringUtil.isNotBlank(waDailyMsgDto.getEmpLeave())) {
            list.add(new KeyValue("休假申请", waDailyMsgDto.getEmpLeave()));
        }
        if (StringUtil.isNotBlank(waDailyMsgDto.getEmpTravel())) {
            list.add(new KeyValue("出差申请", waDailyMsgDto.getEmpTravel()));
        }
        if (StringUtil.isNotBlank(waDailyMsgDto.getEmpFillClock())) {
            list.add(new KeyValue("补卡申请", waDailyMsgDto.getEmpFillClock()));
        }
        if (StringUtil.isNotBlank(waDailyMsgDto.getEmpShiftChange())) {
            list.add(new KeyValue("调班申请", waDailyMsgDto.getEmpShiftChange()));
        }
        return list;
    }

    private void sendWeekyMsg(WaWeeklyMsgDto waWeeklyMsgDto) {
        log.info("Start to send weekly message");
        List<Long> receiver = new ArrayList<>();
        receiver.add(waWeeklyMsgDto.getEmpid());
        MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                .type("msg")
                .subType("weekly")
                .title(waWeeklyMsgDto.getSummaryDate())
                .funcType("2")
                .processId(String.valueOf(waWeeklyMsgDto.getWeeklyDate()))
                .handlers(receiver)
                .customForms(getWeeklyFormCustoms(waWeeklyMsgDto))
                .nType(NoticeType.ATTENDANCE_WEEKLY_REPORT_MSG).tenantId(waWeeklyMsgDto.getTenantId());
        MessageParams messageParams = builder.build();
        String jsonStr = FastjsonUtil.toJson(messageParams);
        delayMsgPublish.publish(jsonStr, waWeeklyMsgDto.getDelay(), waWeeklyMsgDto.getTenantId());
        log.info("Success to send weekly message, messageParams[{}], x-delay = [{}]",
                jsonStr, waWeeklyMsgDto.getDelay());
    }

    /**
     * 周报消息模版
     *
     * @param waWeeklyMsgDto
     * @return
     */
    private List<KeyValue> getWeeklyFormCustoms(WaWeeklyMsgDto waWeeklyMsgDto) {
        List<KeyValue> list = new ArrayList<>();
        list.add(new KeyValue("排班工时", waWeeklyMsgDto.getShiftWorkingHours()));
        list.add(new KeyValue("出勤时长", waWeeklyMsgDto.getCheckHours()));
        list.add(new KeyValue("迟到时长", waWeeklyMsgDto.getLateTime()));
        list.add(new KeyValue("早退时长", waWeeklyMsgDto.getEarlyLeaveHours()));
        list.add(new KeyValue("补卡次数", waWeeklyMsgDto.getCheckMmissTimes()));
        list.add(new KeyValue("旷工时长", waWeeklyMsgDto.getMinerHours()));
        list.add(new KeyValue("加班时长", waWeeklyMsgDto.getOvertimeHours()));

        list.add(new KeyValue("休假次数", waWeeklyMsgDto.getVacationCount()));

        return list;
    }

    private String weeklySummaryDate(Long date) {
        return String.format("<%s 至 %s>考勤周报",
                DateUtil.convertDateTimeToStr(date, "MM-dd", true),
                DateUtil.convertDateTimeToStr(date + 6 * 24 * 60 * 60, "MM-dd", true)
        );
    }

    public void attendanceAbnormalReminder(String tenantId) {
        if (null == tenantId) {
            return;
        }
        int pageNo = 1;
        List<NotifyConfigDo> list = notifyConfigService.getNotifyPageList(pageNo, BATCH_PAGE_SIZE);
        list = list.stream().filter(l -> tenantId.equals(l.getTenantId())).collect(Collectors.toList());
        long nowDate = DateUtil.getOnlyDate(new Date());
        doAttendanceAbnormalMsg(pageNo, list, nowDate);
    }

    public void doAttendanceAbnormalMsg(int pageNo, List<NotifyConfigDo> list, long nowDate) {
        if (null == list || list.isEmpty()) {
            return;
        }
        for (NotifyConfigDo notifyConfig : list) {
            if (null == notifyConfig.getAbnormalSwitch() || notifyConfig.getAbnormalSwitch().equals(0)) {
                // 考勤异常汇总提醒开关关闭
                continue;
            }
            Integer abnormalType = notifyConfig.getAbnormalType();
            if (null == abnormalType) {
                continue;
            }
            long startDate = nowDate;
            long endDate = nowDate + 86399;
            if (1 == abnormalType) {
                List<WaSobDo> waSobs = sobService.getWaSobIdByDateRangeAndPeriodMonth(notifyConfig.getTenantId(), nowDate, null, null);
                if (CollectionUtils.isEmpty(waSobs)) {
                    continue;
                }
                for (WaSobDo waSob : waSobs) {
                    if (waSob.getStartDate() == null || waSob.getEndDate() == null) {
                        continue;
                    }
                    startDate = waSob.getStartDate();
                    endDate = waSob.getEndDate();
                    // 发送考勤异常提醒消息
                    sendAttendanceAbnormalMsg(notifyConfig.getTenantId(), startDate, endDate, notifyConfig.getAbnormalMsg(), Collections.singletonList(waSob.getWaGroupId()));
                }
            } else if (2 == abnormalType) {
                startDate -= 7 * 24 * 60 * 60;
                // 发送考勤异常提醒消息
                sendAttendanceAbnormalMsg(notifyConfig.getTenantId(), startDate, endDate, notifyConfig.getAbnormalMsg(), null);
            } else {
                startDate -= 14 * 24 * 60 * 60;
                // 发送考勤异常提醒消息
                sendAttendanceAbnormalMsg(notifyConfig.getTenantId(), startDate, endDate, notifyConfig.getAbnormalMsg(), null);
            }
        }
        if (list.size() < 500) {
            return;
        }
        pageNo++;
        list = notifyConfigService.getNotifyPageList(pageNo, 500);
        doAttendanceAbnormalMsg(pageNo, list, nowDate);
    }

    public void sendAttendanceAbnormalMsg(String tenantId, Long startDate, Long endDate, String abnormalMsg, List<Integer> waGroupIds) {
        List<Long> empIds = waAnalyzeDo.selectWaAbnormalAnalyseList(tenantId, startDate, endDate, 1, waGroupIds);
        if (null == empIds || empIds.isEmpty()) {
            return;
        }
        empIds = empIds.stream().distinct().collect(Collectors.toList());
        log.info("Start to send attendance abnormal message");
        for (Long empId : empIds) {
            List<Long> receiver = Lists.newArrayList();
            receiver.add(empId);
            MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                    .type("msg")
                    .subType("attendanceAbnormal")
                    .title("考勤异常汇总提醒")
                    .funcType("102")
                    .processId(String.format(abnormalUrl + "?empId=%s&startDate=%s&endDate=%s", empId, startDate, endDate))
                    .handlers(receiver)
                    .content(abnormalMsg)
                    .emailNotice(true).tenantId(tenantId)
                    .nType(NoticeType.ATTENDANCE_ABNORMAL_REMINDER_MSG);
            MessageParams messageParams = builder.build();
            String jsonStr = FastjsonUtil.toJson(messageParams);
            delayMsgPublish.publish(jsonStr, 0, tenantId);
        }
        log.info("Success to send attendance abnormal message, messageParams[{}], ", FastjsonUtil.toJson(empIds));
    }

    public void attendanceDetailReminder(String tenantId, Integer type, Integer day, Integer time, Integer waGroupId) {
        if (null == tenantId) {
            return;
        }
        if (null == type || null == day || null == time || null == waGroupId) {
            log.error("attendanceDetailReminder setting is null");
            return;
        }
        int pageNo = 1;
        List<NotifyConfigDo> list = notifyConfigService.getNotifyPageList(pageNo, BATCH_PAGE_SIZE);
        list = list.stream().filter(l -> tenantId.equals(l.getTenantId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            log.error("monthReportReminder setting is empty");
            return;
        }
        NotifyConfigDo notifyConfig = list.get(0);
        doAttendanceDetailMsg(type, day, time, waGroupId, notifyConfig);
    }

    public void doAttendanceDetailMsg(Integer type, Integer day, Integer time, Integer waGroupId, NotifyConfigDo notifyConfig) {
        try {
            String tenantId = notifyConfig.getTenantId();
            if (log.isDebugEnabled()) {
                log.debug("doAttendanceDetailMsg，Params:[notifyConfigDo:{}]", FastjsonUtil.toJsonStr(notifyConfig));
            }
            if (notifyConfig.getAttendanceDetailSwitch() == 1) {
                String setting = notifyConfig.getAttendanceDetailTime();
                if (!StringUtil.isNotBlank(setting)) {
                    log.error("doAttendanceDetailMsg setting is empty, tenantId:{}", tenantId);
                    return;
                }
                List<AttendanceDetailTimeDto> items = JSON.parseArray(setting, AttendanceDetailTimeDto.class);
                if (CollectionUtils.isEmpty(items)) {
                    log.error("doAttendanceDetailMsg setting is empty, tenantId:{}", tenantId);
                    return;
                }
                WaGroupDo group = waGroupDo.getById(waGroupId);
                if (null == group) {
                    log.error("doAttendanceDetailMsg setting group is null,tenantId：{},type:{},day:{},time:{},waGroupId:{}", tenantId, type, day, time, waGroupId);
                    return;
                }
                AttendanceDetailTimeDto timeSet = items.stream().filter(s -> type.equals(s.getType()) && day.equals(s.getDay()) && time.equals(s.getTime())&& waGroupId.equals(s.getWaGroupId())).findFirst().orElse(null);
                if (null == timeSet) {
                    log.error("doAttendanceDetailMsg setting not match,tenantId：{},type:{},day:{},time:{},waGroupId:{}", tenantId, type, day, time, waGroupId);
                    return;
                }
                long startDate = 0L;
                long endDate = 0L;
                //startType、endType:1当月，2上月
                Integer startType = Optional.ofNullable(timeSet.getStartType()).orElse(1);
                Integer startDay = Optional.ofNullable(timeSet.getStartDay()).orElse(0);
                Integer endType = Optional.ofNullable(timeSet.getEndType()).orElse(1);
                Integer endDay = Optional.ofNullable(timeSet.getEndDay()).orElse(0);
                if (startDay <= 0 || endDay <= 0) {
                    log.error("doAttendanceDetailMsg date setting is null,tenantId：{},type:{},day:{},time:{},waGroupId:{}", tenantId, type, day, time, waGroupId);
                    return;
                }
                startDate = getDateTime(startDay, startType);
                endDate = getDateTime(endDay, endType);
                if (startDate == 0 || endDate == 0 || endDate < startDay) {
                    log.error("doAttendanceDetailMsg date setting error,tenantId：{},type:{},day:{},time:{},waGroupId:{}", tenantId, type, day, time, waGroupId);
                    return;
                }
                String startDateStr = DateUtil.getDateStrByTimesamp(startDate);
                String endDateStr = DateUtil.getDateStrByTimesamp(endDate);
                String groupName = group.getWaGroupName();
                UserInfo userInfo = getUserInfo(Integer.valueOf(tenantId), tenantId);
                SecurityUserInfo securityUserInfo = new SecurityUserInfo();
                securityUserInfo.setUserId(userInfo.getUserId());
                securityUserInfo.setTenantId(userInfo.getTenantId());
                securityUserInfo.setEmpId(userInfo.getEmpid() != null ? userInfo.getEmpid().longValue() : 0L);
                SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
                MonthAnalysePageDto requestDto = new MonthAnalysePageDto();
                requestDto.setGroupId(waGroupId);
                requestDto.setStartDate(startDate);
                requestDto.setEndDate(endDate);
                requestDto.setPageNo(0);
                PageBean pageBean = PageUtil.getPageBean(requestDto);
                DynamicPageDto mouthAnalyseListForDynamic = statisticsService.getRegisterStatisticsAdvancedForDynamic(requestDto, pageBean, userInfo);
                List<Map> list = mouthAnalyseListForDynamic.getPageData().getItems();
                if (CollectionUtils.isEmpty(list)) {
                    log.error("doAttendanceDetailMsg no data,tenantId：{},type:{},day:{},time:{},waGroupId:{}", tenantId, type, day, time, waGroupId);
                    return;
                }
                List<BaseHeaderDto> head = statisticsService.getMonthAdvanceHeadersByDynamicForExport(requestDto.getGroupId(), userInfo, new ImmutablePair<>(requestDto.getStartDate(), requestDto.getEndDate()));
                List<String> monthlyAdvanced = initTimeStampColumn("MOUTHLYADVANCET");
                list.forEach(data -> {
                    for (String timeCol : monthlyAdvanced) {
                        if (data.containsKey(timeCol) && data.get(timeCol) != null) {
                            data.put(timeCol, DateUtil.getDateStrByTimesamp((Long.parseLong((String) data.get(timeCol))) / 1000));
                        }
                    }
                    if (data.containsKey("status") && data.get("status") != null) {
                        Integer status = (Integer) data.get("status");
                        data.put("status", ClockResultEnum.getName(status));
                    }
                    convertSummaryData(head, data);
                });
                //组装消息体
                ClockMsgDto clockMsgDto = new ClockMsgDto();
                clockMsgDto.setTenantId(tenantId);
                clockMsgDto.setContent(String.format(ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_DETAIL_CONTENT, null).getMsg(), groupName, startDateStr, endDateStr));
                clockMsgDto.setDelay(0); //考勤明细推送不需要延时
                String sheetName = ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_DETAIL, null).getMsg();
                String fileName = String.format("%s-%s%s%s", startDateStr, endDateStr, groupName, sheetName);
                UploadResult uploadResult = saveAndUploadFile(fileName, sheetName, head, list, userInfo);
                if (null == uploadResult) {
                    return;
                }
                //推送
                sendAttendanceDetailMsg(clockMsgDto, Collections.singletonList(uploadResult.getId().toString()), Collections.singletonList(uploadResult.getFileName()));
            }
        } catch (Exception e) {
            log.error("doAttendanceDetailMsg Exception Msg:{}", e.getMessage(), e);
        }
    }

    private Long getDateTime(Integer day, Integer dateType) throws ParseException {
        long dateTime = DateUtil.getOnlyDate(DateUtil.getFirstDayOfMonth(new Date(DateUtil.getOnlyDate() * 1000)));
        if (2 == dateType) {
            dateTime = DateUtil.addMonth(dateTime, -1);
        }
        int curMonthMaxDay = DateUtil.getMonthActualMax(dateTime * 1000);
        if (day > curMonthMaxDay) {
            return DateUtil.getOnlyDate(DateUtil.getLastDayOfMonth(new Date(dateTime * 1000)));
        } else {
            return DateUtil.addDate(dateTime * 1000, day - 1);
        }
    }

    private void convertSummaryData(List<BaseHeaderDto> headKey, Map<String, Object> dataMap) {
        if (dataMap != null && !dataMap.isEmpty()) {
            headKey.forEach(it -> {
                final String itemKey = it.getId();
                Pattern summaryPattern = Pattern.compile("^summary_\\w+$");
                Matcher matcher = summaryPattern.matcher(itemKey);
                if (matcher.find()) {
                    Object itemValue = dataMap.getOrDefault(itemKey, null);
                    if (itemValue == null || StringUtils.isEmpty(Objects.toString(itemValue)) || "approved_bdk".equals(itemValue.toString())) {
                        dataMap.put(itemKey, "✅");
                        if ("approved_bdk".equals(String.valueOf(itemValue))) {
                            dataMap.put(itemKey, "✅\n" + CalendarStatusEnum.getName(CalendarStatusEnum.PATCH.getIndex()));
                        }
                    } else {
                        List<RegisterRecordDto> recordDtos = (List<RegisterRecordDto>) dataMap.getOrDefault(itemKey.replace("summary", "record"), null);
                        if (CollectionUtils.isNotEmpty(recordDtos)) {
                            String summaryTxt = Objects.toString(dataMap.getOrDefault(itemKey, ""));
                            StringBuilder builder = new StringBuilder(summaryTxt.replace(",", "\n"));
                            recordDtos.forEach(o1 -> {
                                builder.append("\n");
                                String registerTypeName = Optional.ofNullable(o1.getRegisterTypeName()).orElseGet(() -> o1.getRegisterType() != null ? o1.getRegisterType() == 1 ? "签到" : "签退" : "打卡");
                                builder.append(registerTypeName).append(":").append(DateUtil.convertDateTimeToStr(o1.getRegDateTime(), "HH:mm:ss", true));
                            });
                            dataMap.put(itemKey, builder.toString());
                        }
                    }
                }
            });
        }
    }

    private List<String> initTimeStampColumn(String code) {
        val userDynamicSet = dynamicFeignClient.userDynamicTableLoad(code).getData();
        List<String> timeCols = new ArrayList<>();
        List<Map> columns = userDynamicSet.getColumns();
        for (Map column : columns) {
            Map<String, Object> props = (Map<String, Object>) column.get("props");
            if (props.get("dataType") != null && props.get("dataType").equals("Timestamp")) {
                timeCols.add(String.valueOf(props.get("dataIndex")));
            }
        }
        return timeCols;
    }

    /**
     * 上传文件
     * @param heads 表头
     * @param list  数据
     * @param userInfo  用户信息
     * @return
     */
    private UploadResult saveAndUploadFile(String fileName, String sheetName, List<BaseHeaderDto> heads, List<Map> list, UserInfo userInfo) {
        if (CollectionUtils.isEmpty(heads) && CollectionUtils.isEmpty(list)) {
            return null;
        }
        UploadResult uploadResult = null;
        String fullFileName = String.format("%s.%s", fileName, "xlsx");
        try {
            MultipartFile file = genExcelAndConvertToMultipartFile(heads, list, fullFileName, sheetName);
            String bucketName = String.format("%s-%s", userInfo.getCorpid(), userInfo.getTenantId());
            uploadResult = ossService.upload(bucketName, file);
        } catch (IOException ie) {
            log.error("文件生成失败：{}", ie.getMessage(), ie);
        } catch (Exception ex) {
            log.error("文件上传失败：{}", ex.getMessage(), ex);
        }
        return uploadResult;
    }

    /**
     * 生成excel文件
     * @param heads  表头
     * @param list   数据
     * @param fullFileName 文件全路径名称
     * @param sheetName   文件名称
     * @return
     * @throws IOException
     */
    public synchronized MultipartFile genExcelAndConvertToMultipartFile( List<BaseHeaderDto> heads, List<Map> list, String fullFileName, String sheetName) throws IOException {
        if (CollectionUtils.isEmpty(heads) && CollectionUtils.isEmpty(list)) {
            return null;
        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        LocalMultipartFile file = null;
        try {
            // 创建工作薄对象
            Workbook wb = new SXSSFWorkbook();
            // 创建休假工作表对象
            if (CollectionUtils.isNotEmpty(list)) {
                setFileSheet(wb, wb.createSheet(sheetName), heads, list);
            }
            // 文档输出
            wb.write(os);
            byte[] b = os.toByteArray();
            file = new LocalMultipartFile(fullFileName, b, "application/vnd.ms-excel;charset=UTF-8");
            file.setOriginalFilename(fullFileName);
        } catch (Exception ex) {
            log.error("生成文件失败:{}", ex.getMessage(), ex);
            throw new IOException(ex.getMessage(), ex);
        } finally {
            try {
                os.flush();
                os.close();
                os = null;
            } catch (IOException e) {
                log.error("close, {}", e.getMessage(), e);
            }
        }
        return file;
    }

    /**
     * 设置sheet数据
     * @param wb
     * @param sheet
     * @param heads
     * @param dataList
     */
    private void setFileSheet(Workbook wb, Sheet sheet, List<BaseHeaderDto> heads, List<Map> dataList) {
        // 生成表的表头
        int starRowIndex = 0;
        int startColIndex = 0;
        Row headRow = sheet.createRow(starRowIndex);
        for (int i = startColIndex; i < heads.size(); i++) {
            BaseHeaderDto head = heads.get(i);
            Cell cell = headRow.createCell(i);
            cell.setCellStyle(FileUtil.getHeaderCellStyle(wb));
            cell.setCellValue(head.getValue());
        }
        ((SXSSFSheet)sheet).trackAllColumnsForAutoSizing();
        // 设置为根据内容自动调整列宽
        for (int i = 0; i < heads.size(); i++) {
            sheet.autoSizeColumn(i);
            int width = sheet.getColumnWidth(i) * 15 / 10;
            int columnWidth = Math.min(width, MAX_COLUMN_WIDTH);
            sheet.setColumnWidth(i, columnWidth);
        }
        // 创建工作表的行
        CellStyle cellStyle = FileUtil.getCellStyle(wb, BorderStyle.THIN, IndexedColors.BLACK.getIndex(), (short) 0, FillPatternType.SOLID_FOREGROUND, HorizontalAlignment.CENTER, (short) 12);
        for (int i = 0; i < dataList.size(); i++) {
            Row row = sheet.createRow(i + 1);
            for (int j = startColIndex; j < heads.size(); j++) {
                Map map = dataList.get(i);
                BaseHeaderDto head = heads.get(j);
                Cell cell = row.createCell(j);
                cell.setCellStyle(cellStyle);
                String cellValue = map.get(head.getId()) == null ? "" : map.get(head.getId()).toString();
                cell.setCellValue(cellValue);
                cellStyle.setWrapText(true);
            }
            //解决设置单元格宽度的漏设
            if (i % 100 == 0 && i > 0) {
                ((SXSSFSheet)sheet).trackAllColumnsForAutoSizing();
                // 必须在单元格设值以后进行
                // 设置为根据内容自动调整列宽
                for (int k = 0; k < heads.size(); k++) {
                    sheet.autoSizeColumn(k);
                    int width = sheet.getColumnWidth(k) * 15 / 10;
                    int columnWidth = Math.min(width, MAX_COLUMN_WIDTH);
                    sheet.setColumnWidth(k, columnWidth);
                }
            }
        }
        ((SXSSFSheet)sheet).trackAllColumnsForAutoSizing();
        // 必须在单元格设值以后进行
        // 设置为根据内容自动调整列宽
        for (int i = 0; i < heads.size(); i++) {
            sheet.autoSizeColumn(i);
            int width = sheet.getColumnWidth(i) * 15 / 10;
            int columnWidth = Math.min(width, MAX_COLUMN_WIDTH);
            sheet.setColumnWidth(i, columnWidth);
        }
    }

    private UserInfo getUserInfo(Integer corpId, String tenantId) {
        UserInfo userInfo = new UserInfo();
        userInfo.setCorpid(corpId);
        userInfo.setTenantId(tenantId);
        userInfo.setUserid(0);
        return userInfo;
    }

    public void sendAttendanceDetailMsg(ClockMsgDto clockMsgDto, List<String> fileIds, List<String> fileNames) {
        if (CollectionUtils.isEmpty(fileIds)) {
            return;
        }
        log.info("Start to send attendance detail message");
        MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                .type("msg")
                .subType("attendanceDetail")
                .title("考勤明细推送")
                .funcType("105")
                .content(clockMsgDto.getContent())
                .filePaths(fileIds)
                .fileNames(fileNames)
                .emailNotice(true).tenantId(clockMsgDto.getTenantId())
                .nType(NoticeType.ATTENDANCE_DETAIL_REMINDER_MSG);
        MessageParams messageParams = builder.build();
        String jsonStr = FastjsonUtil.toJson(messageParams);
        delayMsgPublish.publish(jsonStr, clockMsgDto.getDelay(), clockMsgDto.getTenantId());
        log.info("Success to send attendance detail message, messageParams[{}], ", FastjsonUtil.toJson(clockMsgDto));
    }
}
