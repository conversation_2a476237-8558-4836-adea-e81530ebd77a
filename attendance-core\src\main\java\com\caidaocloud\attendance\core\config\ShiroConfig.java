package com.caidaocloud.attendance.core.config;

import com.caidaocloud.attendance.core.shiro.CaidaoInvalidRequestFilter;
import com.caidaocloud.attendance.core.shiro.URLPermissionsFilter;
import com.caidaocloud.attendance.core.shiro.credentials.MultiRealmAuthenticator;
import com.caidaocloud.attendance.core.shiro.credentials.RetryLimitHashedCredentialsMatcher;
import com.caidaocloud.attendance.core.shiro.realm.SimpleFormAuthenticationFilter;
import com.caidaocloud.attendance.core.shiro.realm.UserRealm;
import com.caidaocloud.attendance.core.shiro.zt.SystemAuthorizingRealm;
import org.apache.shiro.authc.Authenticator;
import org.apache.shiro.authc.pam.AtLeastOneSuccessfulStrategy;
import org.apache.shiro.codec.Base64;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.CookieRememberMeManager;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.session.mgt.ServletContainerSessionManager;
import org.crazycake.shiro.IRedisManager;
import org.crazycake.shiro.RedisCacheManager;
import org.springframework.beans.factory.config.MethodInvokingFactoryBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.servlet.DispatcherType;
import javax.servlet.Filter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Configuration
@ConditionalOnProperty(name = "shiro.active", havingValue = "local")
public class ShiroConfig {

    /**
     * FilterRegistrationBean
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean filterRegistrationBean() {
        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
        filterRegistration.setFilter(new DelegatingFilterProxy("shiroFilter"));
        filterRegistration.setEnabled(true);
        filterRegistration.addUrlPatterns("/*");
        filterRegistration.setDispatcherTypes(DispatcherType.REQUEST);
        return filterRegistration;
    }

    /**
     * @return
     * @see ShiroFilterFactoryBean
     */
    @Bean(name = "shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(DefaultWebSecurityManager securityManager) {
        ShiroFilterFactoryBean bean = new ShiroFilterFactoryBean();
        bean.setSecurityManager(securityManager);
        bean.setLoginUrl("/login");
        bean.setSuccessUrl("/");

        Map<String, Filter> filters = new HashMap<>();
        filters.put("authc", formAuthenticationFilter());
        filters.put("perms", urlPermissionsFilter());
        filters.put("invalidRequest", invalidRequestFilter());

        bean.setFilters(filters);
        Map<String, String> chains = new LinkedHashMap<>();
        chains.put("/", "authc");
        chains.put("/login", "authc");
        chains.put("/version", "anon");
        chains.put("/logout", "anon");
        chains.put("/v2Login.html", "anon");

        //swagger
        chains.put("/swagger-ui.html", "anon");
        chains.put("/v2/api-docs/**", "anon");
        chains.put("/swagger-resources/**", "anon");
        chains.put("/webjars/**", "anon");

        chains.put("/connect/**", "anon");

        chains.put("/assets/**", "anon");
        chains.put("/error/**", "anon");
        chains.put("/reg/**", "anon");
        chains.put("/mobile/**", "anon");
        chains.put("/open/api/**", "anon");
        chains.put("/iconnect/**", "anon");
        chains.put("/mobileLeave/**", "anon");
        chains.put("/mobileLeave2/**", "anon");
        chains.put("/mobileAch/**", "anon");
        chains.put("/mobileReport/**", "anon");
        chains.put("/zp/**", "anon");
        chains.put("/wechart/**", "anon");
        chains.put("/wechartZp/**", "anon");
        chains.put("/wxchart/**", "anon");
        chains.put("/mobileV16/**", "anon");
        chains.put("/mobileV18/**", "anon");
        chains.put("/gdEmployee/**", "anon");
        chains.put("/mobileToken/**", "anon");
        chains.put("/mobileV16Employee/**", "anon");
        chains.put("/mobileWorkflow/**", "anon");
        chains.put("/mobilePersonnel/**", "anon");
        chains.put("/mobileManager/**", "anon");
        chains.put("/mobile/home/<USER>", "anon");
        chains.put("/assets/**", "anon");
        chains.put("/html/iconnect/**", "anon");
        chains.put("/pubCorp/**", "anon");
        chains.put("/download/**", "anon");
        chains.put("/upload/**", "anon");
        chains.put("/corpReg.html", "anon");
        chains.put("/regResult.html", "anon");
        chains.put("/authorize/**", "anon");
        chains.put("/resetpwd.html", "anon");
        chains.put("/pre/**", "anon");
        chains.put("/preRecruit/**", "anon");
        chains.put("/upload/**", "anon");
        chains.put("/share-page/**", "anon");
        chains.put("/home/<USER>/**", "anon");
        chains.put("/ws/**", "anon");
        chains.put("/tuniu/**", "anon");
        chains.put("/app/**", "anon");
        chains.put("/user/oauth/**", "anon");
        chains.put("/workflow/completeEmailTask/**", "anon");
        chains.put("/ueditor/**", "anon");
        chains.put("/caidao/bestsign/**", "anon");
        chains.put("/caidao/esign/**", "anon");
        chains.put("/bonus/**", "anon");
        chains.put("/yd/yearSalary/**", "anon");
        chains.put("/kr/report/**", "anon");
        chains.put("/open/**", "anon");

        chains.put("/shopSet/**", "anon");
        chains.put("/commoditySet/**", "anon");
        chains.put("/quotaSet/**", "anon");
        chains.put("/ruleSet/**", "anon");
        chains.put("/uniformApply/**", "anon");
        chains.put("/welfareApproval/**", "anon");
        chains.put("/rsp/report/**", "anon");
        chains.put("/api/**", "anon");
        chains.put("/workflow/common/**", "anon");
        chains.put("/workflow/business/detail", "anon");
        chains.put("/workflow/begin/after", "anon");
        chains.put("/**", "perms,user");
        bean.setFilterChainDefinitionMap(chains);
        return bean;
    }

    @Bean
    public URLPermissionsFilter urlPermissionsFilter() {
        return new URLPermissionsFilter();
    }

    @Bean
    public CaidaoInvalidRequestFilter invalidRequestFilter() {
        return new CaidaoInvalidRequestFilter();
    }

    @Bean
    public SimpleFormAuthenticationFilter formAuthenticationFilter() {
        SimpleFormAuthenticationFilter formAuthenticationFilter = new SimpleFormAuthenticationFilter();
        formAuthenticationFilter.setUsernameParam("username");
        formAuthenticationFilter.setPasswordParam("password");
        formAuthenticationFilter.setRememberMeParam("rememberMe");
        formAuthenticationFilter.setLoginUrl("/login");
        return formAuthenticationFilter;
    }

    @Bean
    public SimpleCookie rememberMeCookie() {
        SimpleCookie simpleCookie = new SimpleCookie("rememberMe");
        simpleCookie.setMaxAge(259200);
        return simpleCookie;
    }

    /**
     * cookie管理对象;
     *
     * @return
     */
    @Bean
    public CookieRememberMeManager rememberMeManager() {
        CookieRememberMeManager cookieRememberMeManager = new CookieRememberMeManager();
        cookieRememberMeManager.setCookie(rememberMeCookie());
        //rememberMe cookie加密的密钥 建议每个项目都不一样 默认AES算法 密钥长度(128 256 512 位)
        cookieRememberMeManager.setCipherKey(Base64.decode("1AvVhmFsLUs12KTA3Ksdfsfsasdfprsdag=="));
        return cookieRememberMeManager;
    }

    /**
     * @return
     * @see org.apache.shiro.mgt
     */
    @Bean(name = "securityManager")
    public DefaultWebSecurityManager securityManager(RedisCacheManager cacheManager, UserRealm userRealm,
                                                     ServletContainerSessionManager defaultWebSessionManager,
                                                     Authenticator authenticator) {
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        manager.setRealms(Arrays.asList(userRealm, systemAuthorizingRealm(cacheManager)));
        manager.setAuthenticator(authenticator);
        manager.setCacheManager(cacheManager);
        manager.setSessionManager(defaultWebSessionManager);
        manager.setRememberMeManager(rememberMeManager());
        return manager;
    }

    /**
     * 设置验证管理器
     */
    @Bean(name = "authenticator")
    public Authenticator authenticator(UserRealm userRealm, RedisCacheManager cacheManager) {
        //扩展父类原方法，捕获原始异常
        MultiRealmAuthenticator myAuthenticator = new MultiRealmAuthenticator();
        //设置两个Realm，一个用于用户登录验证和访问权限获取；一个用于jwt token的认证
        myAuthenticator.setRealms(Arrays.asList(userRealm, systemAuthorizingRealm(cacheManager)));
        //设置多个realm认证策略，一个成功即跳过其它的
        myAuthenticator.setAuthenticationStrategy(new AtLeastOneSuccessfulStrategy());
        return myAuthenticator;
    }

    @Bean
    public MethodInvokingFactoryBean methodInvokingFactoryBean(DefaultWebSecurityManager securityManager) {
        MethodInvokingFactoryBean factoryBean = new MethodInvokingFactoryBean();
        factoryBean.setStaticMethod("org.apache.shiro.SecurityUtils.setSecurityManager");
        factoryBean.setArguments(new Object[]{securityManager});
        return factoryBean;
    }

    /**
     * ServletContainerSessionManager
     *
     * @return
     */
    @Bean(name = "sessionManager")
    public ServletContainerSessionManager defaultWebSessionManager() {
        ServletContainerSessionManager sessionManager = new ServletContainerSessionManager();
        return sessionManager;
    }

    /**
     * @return
     * @see UserRealm --->AuthorizingRealm`
     */
    @Bean
    public UserRealm userRealm(RedisCacheManager cacheManager, RetryLimitHashedCredentialsMatcher credentialsMatcher) {
        UserRealm userRealm = new UserRealm();
        userRealm.setCacheManager(cacheManager);
        userRealm.setCredentialsMatcher(credentialsMatcher);
        return userRealm;
    }

    /**
     * @return
     * @see UserRealm --->AuthorizingRealm`
     */
    @Bean
    public SystemAuthorizingRealm systemAuthorizingRealm(RedisCacheManager cacheManager) {
        SystemAuthorizingRealm userRealm = new SystemAuthorizingRealm();
        userRealm.setCachingEnabled(true);
        userRealm.setAuthenticationCachingEnabled(true);
        userRealm.setAuthorizationCachingEnabled(true);
        userRealm.setAuthenticationCacheName("authenticationCache");
        userRealm.setAuthorizationCacheName("authorizationCache");
        userRealm.setCacheManager(cacheManager);
        return userRealm;
    }

    //配置自定义的密码比较器
    @Bean(name = "credentialsMatcher")
    public RetryLimitHashedCredentialsMatcher credentialsMatcher(RedisCacheManager cacheManager) {
        RetryLimitHashedCredentialsMatcher matcher = new RetryLimitHashedCredentialsMatcher(cacheManager);
        matcher.setHashAlgorithmName("md5");
        matcher.setHashIterations(2);
        matcher.setStoredCredentialsHexEncoded(true);
        return matcher;
    }

    @Bean
    public RedisCacheManager cacheManager(IRedisManager redisManager) {
        RedisCacheManager cacheManager = new RedisCacheManager();
        cacheManager.setRedisManager(redisManager);
        cacheManager.setKeyPrefix("shiro:cache:");
        return cacheManager;
    }
}