package com.caidaocloud.attendance.service.application.dto.msg;

import lombok.Data;

@Data
public class EmailMsgDTO {

    /**
     * 主题，标题
     */
    private String subject;

    /**
     * 内容
     */
    private String content;

    /**
     * 抄送
     */
    private String cc;

    /**
     * 收件人
     */
    private String to;

    /**
     * 多个附件用逗号分割
     * 附件地址
     */
    private String affix;

    /**
     * 指定附件对应的名称，多个用逗号分割同affix一一对应，如果为空或对应下标上的值为空，则取文件名作为名称
     * 附件名称
     */
    private String affixName;

}
