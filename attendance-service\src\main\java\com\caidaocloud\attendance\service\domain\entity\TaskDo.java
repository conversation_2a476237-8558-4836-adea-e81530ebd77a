package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.ITaskRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaTaskRecord;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: Aaron.Chen
 * @Date: 2021/4/16 11:25
 * @Description:
 **/
@Slf4j
@Data
@Service
public class TaskDo {

    private Long id;
    private String tenantId;
    private String type;
    private Integer progress;
    private String uploadFiles;
    private String status;
    private String reason;
    private String ext;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;

    @Autowired
    private ITaskRepository taskRepository;
    @Autowired
    private ISessionService sessionService;

    public void save(TaskDo task) {
        UserInfo userInfo = sessionService.getUserInfo();
        long currentTime = System.currentTimeMillis();
        task.setTenantId(userInfo.getTenantId());
        task.setCreateBy(userInfo.getUserId());
        task.setCreateTime(currentTime);
        task.setUpdateBy(userInfo.getUserId());
        task.setUpdateTime(currentTime);
        taskRepository.save(ObjectConverter.convert(task, WaTaskRecord.class));
    }

    public void update(TaskDo task, UserInfo user) {
        UserInfo userInfo = user == null ? sessionService.getUserInfo() : user;
        long currentTime = System.currentTimeMillis();
        task.setUpdateBy(userInfo.getUserId());
        task.setUpdateTime(currentTime);
        taskRepository.update(ObjectConverter.convert(task, WaTaskRecord.class));
    }

    public TaskDo getById(Long id) {
        WaTaskRecord task = taskRepository.selectById(id);
        if (null == task) {
            return null;
        }
        return ObjectConverter.convert(task, TaskDo.class);
    }
}
