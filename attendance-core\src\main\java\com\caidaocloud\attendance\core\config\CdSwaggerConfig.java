package com.caidaocloud.attendance.core.config;

import com.caidaocloud.attendance.core.config.properties.ConfigurationSwaggerProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import com.google.common.base.Optional;
import com.google.common.base.Predicate;
import com.google.common.base.Function;
import springfox.documentation.swagger2.configuration.Swagger2DocumentationConfiguration;

import java.util.List;

@Configuration
@ConditionalOnClass(Swagger2DocumentationConfiguration.class)
@EnableSwagger2
@ConditionalOnProperty(
        prefix = "caidao.swagger",
        name = {"open"},
        havingValue = "true"
)
public class CdSwaggerConfig {

    @Autowired
    private ConfigurationSwaggerProperties swaggerProperties;

    @Bean
    @ConditionalOnClass({Docket.class, ApiInfo.class, Contact.class})
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
            .apiInfo(apiInfo())
            .enable(swaggerProperties.isEnableSwagger())
            .select()
            //.apis(RequestHandlerSelectors.basePackage("com.caidao1"))
            .apis(basePackage(swaggerProperties.getBasePackage()))
            .paths(PathSelectors.any())
            .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
            .title("CAIDAO CLOUD RESTful APIs")
            .description("caidao open_cloud project")
            .termsOfServiceUrl("https://www.52emp.com/")
            .contact(new Contact("herman", "https://www.52emp.com/", "<EMAIL>"))
            .version("1.0")
            .build();
    }

    public static Predicate<RequestHandler> basePackage(final List<String> basePackage) {
        return input -> declaringClass(input).transform(handlerPackage(basePackage)).or(true);
    }

    private static Function<Class<?>, Boolean> handlerPackage(final List<String> basePackage)     {
        return input -> {
            if(null == basePackage || basePackage.isEmpty()){
                return false;
            }

            // 循环判断匹配
            for (String strPackage : basePackage) {
                boolean isMatch = input.getPackage().getName().startsWith(strPackage);
                if (isMatch) {
                    return true;
                }
            }
            return false;
        };
    }

    private static Optional<? extends Class<?>> declaringClass(RequestHandler input) {
        return Optional.fromNullable(input.getHandlerMethod().getMethod().getDeclaringClass());
    }

}