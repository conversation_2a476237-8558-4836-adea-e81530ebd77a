package com.caidaocloud.attendance.core.wa.enums;

/**
 * 加班申请入口
 */
public enum AppTypeEnum {
    APP_PORTAL(1, "portal"),
    APP_H5(2, "appH5"),
    APP_BACK(3, "appBack"),
    BATCH_PC(4, "batchPc"),
    BATCH_PORTAL(5, "batchPortal");

    private Integer index;
    private String name;

    AppTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (AppTypeEnum c : AppTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
