package com.caidao1.wa.enums;

public enum LeaveCancelTypeEnum {

    LEAVE_END(1, "休假确认"),
    TIME_ADJUST_PARTIAL(2, "取消部分休假"),
    //FILE_ADD(3, "休假确认"),//原附件补充
    LEAVE_CANCEL(4, "取消休假"),
    TIME_ADJUST(5, "调整时间");

    private Integer index;

    private String name;

    LeaveCancelTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (LeaveCancelTypeEnum c : LeaveCancelTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public static String getName(Long index) {
        for (LeaveCancelTypeEnum c : LeaveCancelTypeEnum.values()) {
            if (Long.valueOf(c.getIndex()).equals(index)) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
