package com.caidaocloud.attendance.service.application.event.subscribe;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.dto.transfer.WaTransferPostEventDto;
import com.caidaocloud.attendance.service.application.shimz.service.ShimzQuotaService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Service;

/**
 * 人事异动通知订阅
 *
 * <AUTHOR>
 * @Date 2024/5/24
 */
@Slf4j
@Service
public class TransferPostWaSubscribe {

    @RabbitHandler
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "caidaocloud.hr.transfer.post.waquota.queue", durable = "true"),
                    exchange = @Exchange(value = "caidaocloud.transfer.fac.direct.exchange"),
                    key = {"routingKey.caidaocloud.transfer.post"}
            )
    )
    public void transferPostWaHandler(String message) {
        log.info("transferPostWaHandler subscribe message={}", message);
        try {
            WaTransferPostEventDto eventDto = FastjsonUtil.toObject(message, WaTransferPostEventDto.class);
            UserContext.doInitSecurityUserInfo(eventDto.getTenantId(), null, null, null, null, null);
            WfCallbackResultDto callback = FastjsonUtil.toObject(eventDto.getWfCallback(), WfCallbackResultDto.class);
            SpringUtil.getBean(ShimzQuotaService.class).empDeployChangeAfterTrigger(callback.getBusinessKey(), callback.getCallbackType());
        } catch (Exception e) {
            log.error("transferPostWaHandler err, {}", e.getMessage(), e);
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }
}
