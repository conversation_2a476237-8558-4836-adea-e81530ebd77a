package com.caidaocloud.attendance.core.wa.service;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.ioc.mybatis.mapper.IocImportMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpLeaveMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveTypeValidateMapper;
import com.caidao1.wa.mybatis.mapper.WaMapper;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 休假公共服务
 *
 * <AUTHOR>
 * @Date 2023/6/16
 */
@Slf4j
@Service
public class WaLeaveCoreService {
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private WaLeaveTypeValidateMapper waLeaveTypeValidateMapper;
    @Autowired
    private IocImportMapper iocImportMapper;

    public List getLeaveTypesV1(Long empId, Boolean isEmpShow, Long gender, String leaveTypeDefCodeEq, String leaveTypeDefCodeNotEq, Boolean allowViewQuota) throws Exception {
        SysEmpInfo empInfo = this.sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (empInfo == null) {
            throw new CDException("员工不存在");
        }

        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
        if (gender == null) {
            gender = empInfo.getGender();
        }

        Map map = new HashMap();
        map.put("empId", empId);
        map.put("nowTime", System.currentTimeMillis() / 1000L);
        map.put("isEmpShow", isEmpShow);
        map.put("gender", gender);
        map.put("allowViewQuota", allowViewQuota);
        if (StringUtils.isNotBlank(leaveTypeDefCodeEq)) {
            map.put("leaveTypeDefCodeEq", leaveTypeDefCodeEq);
        }

        if (StringUtils.isNotBlank(leaveTypeDefCodeNotEq)) {
            map.put("leaveTypeDefCodeNotEq", leaveTypeDefCodeNotEq);
        }

        map.put("tmType", tmType);
        List<Map> leaveTypeList = this.waMapper.getLeaveType(map);
        this.getLeaveTypeQuotaV1(leaveTypeList, empId);
        return leaveTypeList;
    }

    /**
     * PCHR、PC门户、H5申请休假获取员工假期配额信息
     * CDC-746
     *
     * @param leaveTypeList
     * @param empId
     */
    public void getLeaveTypeQuotaV1(List<Map> leaveTypeList, Long empId) throws Exception {
        if (CollectionUtils.isEmpty(leaveTypeList)) {
            return;
        }
        Long curDate = DateUtil.getOnlyDate();
        //过滤掉额度类型为空的数据，额度类型为空可代表是不限额的假期类型，如事假
        List<Map> notNullQuotaTypeList = leaveTypeList.stream().filter(ltl -> null != ltl.get("quota_type")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notNullQuotaTypeList)) {
            return;
        }
        Map<Integer, List<Map>> quotaTypeCollect = notNullQuotaTypeList.stream().collect(Collectors.groupingBy(ltMap -> {
            Integer quotaType = (Integer) ltMap.get("quota_type");
            // 额度类型： 1 按年发放、2 调休、3 固定额度、4 育儿假;
            if (2 == quotaType) {
                return quotaType;
            }
            return 0;
        }));
        Map<Integer, Integer> quotaTypeMap = notNullQuotaTypeList.stream().collect(Collectors.toMap(m -> (Integer) m.get("leaveType"), m -> (Integer) m.get("quota_type"), (v1, v2) -> v2));
        List<Long> leaveTypeIdList = null;
        List<Map> quotaTypeList = null;
        Integer dbLeaveTypeId = null;
        Map<Integer, Float> quotaMap = new HashMap<>(2 * leaveTypeList.size());
        Map<Integer, Float> inTransitQuotaMap = new HashMap<>(2 * leaveTypeList.size());

        if (null != (quotaTypeList = quotaTypeCollect.get(2))) {
            // 调休
            leaveTypeIdList = getLeaveTypeIdListByGroup(quotaTypeList);
            quotaTypeList = waMapper.getWaEmpCompensatoryQuotaByEmpidAndQuotaType(empId, curDate, leaveTypeIdList);
            if (CollectionUtils.isNotEmpty(quotaTypeList)) {
                Float calByDay = null, calByHour = null;
                Integer quotaUnit = 0;
                Float quotaDay = null, usedDay = null, inTransitQuota = null, adjustQuota = null;
                for (Map waEmpCompensatoryQuotaMap : quotaTypeList) {
                    calByDay = null;
                    calByHour = null;
                    quotaUnit = (Integer) waEmpCompensatoryQuotaMap.getOrDefault("quota_unit", 0);
                    quotaDay = (Float) waEmpCompensatoryQuotaMap.get("quota_day");
                    usedDay = (Float) waEmpCompensatoryQuotaMap.get("used_day");
                    inTransitQuota = (Float) waEmpCompensatoryQuotaMap.get("in_transit_quota");
                    adjustQuota = (Float) waEmpCompensatoryQuotaMap.get("adjust_quota_day");
                    inTransitQuota = inTransitQuota == null ? 0f : inTransitQuota;
                    if (Objects.equals(quotaUnit, 1)) {
                        // 按天计算 quota_day - used_day - in_transit_quota
                        calByDay = (null == quotaDay ? 0f : quotaDay) + adjustQuota - (null == usedDay ? 0f : usedDay) - inTransitQuota;
                    } else if (Objects.equals(quotaUnit, 2)) {
                        // 按小时计算
                        calByHour = (null == quotaDay ? 0f : quotaDay) + adjustQuota - (null == usedDay ? 0f : usedDay) - inTransitQuota;
                    }
                    dbLeaveTypeId = (Integer) waEmpCompensatoryQuotaMap.get("leave_type_id");
                    if (null != calByDay) {
                        if (calByDay >= 0) {
                            quotaMap.put(dbLeaveTypeId, quotaMap.getOrDefault(dbLeaveTypeId, 0f) + calByDay);
                        }
                        inTransitQuotaMap.put(dbLeaveTypeId, inTransitQuotaMap.getOrDefault(dbLeaveTypeId, 0f) + inTransitQuota);
                        continue;
                    }
                    if (null != calByHour) {
                        inTransitQuotaMap.put(dbLeaveTypeId, inTransitQuotaMap.getOrDefault(dbLeaveTypeId, 0f) + inTransitQuota);
                        if (calByHour >= 0) {
                            quotaMap.put(dbLeaveTypeId, quotaMap.getOrDefault(dbLeaveTypeId, 0f) + calByHour);
                        }
                    }
                }
            }
        }
        if (null != (quotaTypeList = quotaTypeCollect.get(0))) {
            // 非 2 则表示按年发放 或 固定额度
            leaveTypeIdList = getLeaveTypeIdListByGroup(quotaTypeList);
            quotaTypeList = waMapper.getWaEmpQuotaByEmpidAndQuotaType(empId, curDate, leaveTypeIdList);
            if (CollectionUtils.isNotEmpty(quotaTypeList)) {
                Float calByDay = null, calByHour = null;
                Integer quotaUnit = 0, ifAdvance = null;
                Float quotaDay = null, usedDay = null, inTransitQuota = null, adjustQuota = null, fixUsedDay = null, nowQuota = null, remainDay = null;
                for (Map waEmpQuotaMap : quotaTypeList) {
                    calByDay = null;
                    calByHour = null;
                    quotaUnit = (Integer) waEmpQuotaMap.getOrDefault("acct_time_type", 0);
                    quotaDay = (Float) waEmpQuotaMap.get("quota_day");
                    usedDay = (Float) waEmpQuotaMap.get("used_day");
                    inTransitQuota = (Float) waEmpQuotaMap.get("in_transit_quota");
                    inTransitQuota = inTransitQuota == null ? 0f : inTransitQuota;
                    adjustQuota = (Float) waEmpQuotaMap.get("adjust_quota");
                    fixUsedDay = (Float) waEmpQuotaMap.get("fix_used_day");
                    ifAdvance = (Integer) waEmpQuotaMap.get("if_advance");
                    nowQuota = (Float) waEmpQuotaMap.get("now_quota");
                    remainDay = (Float) waEmpQuotaMap.get("remain_day");
                    if (null != ifAdvance && 0 == ifAdvance) {
                        // 可否预支：0 不可预支 、 1 可预支
                        // 如果不可预支，则取 nowQuota，否则取 quotaDay
                        quotaDay = nowQuota;
                    }
                    dbLeaveTypeId = (Integer) waEmpQuotaMap.get("leave_type_id");
                    log.info("dbLeaveTypeId:{},usedDay:{},quotaTypeMap.get(dbLeaveTypeId):{}", dbLeaveTypeId, usedDay, quotaTypeMap.get(dbLeaveTypeId));
                    boolean isInvalidFixedQuota = quotaTypeMap.containsKey(dbLeaveTypeId) && quotaTypeMap.get(dbLeaveTypeId) != null
                            && quotaTypeMap.get(dbLeaveTypeId).equals(3) && (usedDay > 0 || inTransitQuota > 0);
                    if (Objects.equals(quotaUnit, 1)) {
                        // 按天计算 quota_day - used_day - in_transit_quota + adjust_quota - fix_used_day
                        calByDay = isInvalidFixedQuota ? 0f : (null == quotaDay ? 0f : quotaDay) - (null == usedDay ? 0f : usedDay) - inTransitQuota
                                + (null == adjustQuota ? 0f : adjustQuota) - (null == fixUsedDay ? 0f : fixUsedDay) - (null == remainDay ? 0f : remainDay);
                    } else if (Objects.equals(quotaUnit, 2)) {
                        // 按小时计算 quota_day - used_day - in_transit_quota + adjust_quota - fix_used_day
                        calByHour = isInvalidFixedQuota ? 0f : (null == quotaDay ? 0f : quotaDay) - (null == usedDay ? 0f : usedDay) - inTransitQuota
                                + (null == adjustQuota ? 0f : adjustQuota) - (null == fixUsedDay ? 0f : fixUsedDay) - (null == remainDay ? 0f : remainDay);
                    }
                    if (null != calByDay) {
                        inTransitQuotaMap.put(dbLeaveTypeId, inTransitQuotaMap.getOrDefault(dbLeaveTypeId, 0f) + inTransitQuota);
                        if (calByDay >= 0) {
                            quotaMap.put(dbLeaveTypeId, quotaMap.getOrDefault(dbLeaveTypeId, 0f) + calByDay);
                        }
                        continue;
                    }
                    if (null != calByHour) {
                        if (calByHour >= 0) {
                            quotaMap.put(dbLeaveTypeId, quotaMap.getOrDefault(dbLeaveTypeId, 0f) + calByHour);
                        }
                        inTransitQuotaMap.put(dbLeaveTypeId, inTransitQuotaMap.getOrDefault(dbLeaveTypeId, 0f) + inTransitQuota);
                    }
                }
            }
        }
        for (Map map1 : leaveTypeList) {
            Integer leaveTypeId1 = (Integer) map1.get("leaveType");
            if (quotaMap.containsKey(leaveTypeId1)) {
                Integer acctTimeType = (Integer) map1.get("acctTimeType");
                Float surplus = quotaMap.getOrDefault(leaveTypeId1, 0f);
                Float inTransitQuota = inTransitQuotaMap.getOrDefault(leaveTypeId1, 0f);
                Map leaveQuotaMap = new HashMap();
                leaveQuotaMap.put("surplusQuota", timeStr(acctTimeType, surplus));
                leaveQuotaMap.put("inTransitQuota", timeStr(acctTimeType, inTransitQuota));
                map1.put("quotaInfo", leaveQuotaMap);
            }
        }
    }

    private List<Long> getLeaveTypeIdListByGroup(List<Map> quotaTypeList) {
        return quotaTypeList.stream().map(ltiMap -> {
            Object leaveTypeObj = ltiMap.get("leaveType");
            if (leaveTypeObj instanceof Integer) {
                return ((Integer) leaveTypeObj).longValue();
            } else {
                return (Long) leaveTypeObj;
            }
        }).collect(Collectors.toList());
    }

    private String timeStr(Integer timeUnit, Float timeDuration) {
        if (timeDuration == null) return "";
        NumberFormat nf = new DecimalFormat("#.##");
        if (timeUnit == 1) {
            return nf.format(timeDuration) + ResponseWrap.wrapResult(10031, null).getMsg();
        } else if (timeUnit == 2) {
            BigDecimal b = new BigDecimal(timeDuration / 60);
            return nf.format(b) + ResponseWrap.wrapResult(10032, null).getMsg();
        }
        return "";
    }

    public Map getEmpLastMaternityLeave(Long empid, String belongOrgId) {
        try {
            Map params = new HashMap();
            params.put("leaveType", 14);
            params.put("empid", empid);
            params.put("belongOrgId", belongOrgId);

            Long curDate = DateUtil.getOnlyDate();
            Long pre12MonthDate = DateUtilExt.addMonth(curDate, -12);
            params.put("startTime", pre12MonthDate);
            params.put("endTime", curDate + 86399);

            List<Map> list = waEmpLeaveMapper.getEmpLeavesByLeaveType(params);
            if (CollectionUtils.isNotEmpty(list)) {
                return list.get(0);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return new HashMap();
    }

    public List<Map> filterLeaveType(Long corpid, String belongOrgId, Long empId, List<Map> leaveTypes) {
        if (CollectionUtils.isEmpty(leaveTypes)) {
            return leaveTypes;
        }
        //难产假申请校验
        Map maternityLeave = getEmpLastMaternityLeave(empId, belongOrgId);
        if (MapUtils.isEmpty(maternityLeave)) {
            leaveTypes = leaveTypes.stream().filter(row -> Integer.valueOf(row.get("leaveTypeDef").toString()) != 16).collect(Collectors.toList());
        }
        //查询假期自定义校验规则
        Map params = new HashMap() {{
            put("corpid", corpid);
            put("belongOrgId", belongOrgId);
            put("targetType", "FILTER");
        }};
        Map ruleMap = new HashMap();
        List<Map> ruleList = waLeaveTypeValidateMapper.getRuleList(params);
        for (Map map : ruleList) {
            Integer[] idArray = (Integer[]) map.get("leaveTypeIds");
            if (idArray != null && idArray.length > 0) {
                Arrays.asList(idArray).stream().forEach(item -> ruleMap.put(item, map));
            }
        }
        if (!ruleMap.isEmpty()) {
            return leaveTypes.stream().filter(row -> checkRule((Map) ruleMap.get(row.get("leaveType")), empId, null)).collect(Collectors.toList());
        }
        return leaveTypes;
    }

    public boolean checkRule(Map rule, Long empId, Map paramMap) {
        if (rule != null) {
            String ruleType = (String) rule.get("ruleType");
            switch (ruleType) {
                case "SQL":
                    String sql = (String) rule.get("ruleExp");

                    String sqlExp = sql.replaceAll(":EMPID", String.valueOf(empId));
                    if (paramMap != null) {
                        String startDate = "";
                        String endDate = "";
                        String shalfday = "A";
                        String ehalfday = "P";
                        if (paramMap.get("startDate") != null) {
                            startDate = String.valueOf(paramMap.get("startDate"));
                        }
                        if (paramMap.get("endDate") != null) {
                            endDate = String.valueOf(paramMap.get("endDate"));
                        }
                        if (StringUtils.isNotBlank((CharSequence) paramMap.get("shalfday"))) {
                            shalfday = (String) paramMap.get("shalfday");
                        }
                        if (StringUtils.isNotBlank((CharSequence) paramMap.get("ehalfday"))) {
                            ehalfday = (String) paramMap.get("ehalfday");
                        }
                        sqlExp = sqlExp.replaceAll(":STARTDATE", startDate)
                                .replaceAll(":ENDDATE", endDate)
                                .replaceAll(":SHALFDAY", shalfday)
                                .replaceAll(":EHALFDAY", ehalfday);
                    }
                    try {
                        Object existFk = iocImportMapper.queryFKBySql(sqlExp);
                        if (existFk == null || ConvertHelper.intConvert(existFk) == -1) {
                            return false;
                        }
                    } catch (Exception e) {
                        log.debug("假期自定义校验-SQL校验失败，msg=" + e.getMessage());
                    }
                    break;
            }
        }
        return true;
    }

    public String checkLeaveType(Long corpid, String belongOrgId, Long empId, Integer leaveTypeId, Map paramMap) {
        try {
            //查询假期自定义校验规则
            Map params = new HashMap() {{
                put("corpid", corpid);
                put("belongOrgId", belongOrgId);
                put("targetType", "CHECH");
            }};

            Map ruleMap = new HashMap();
            List<Map> ruleList = waLeaveTypeValidateMapper.getRuleList(params);
            for (Map map : ruleList) {
                Integer[] idArray = (Integer[]) map.get("leaveTypeIds");
                if (idArray != null && idArray.length > 0) {
                    Arrays.asList(idArray).stream().forEach(item -> ruleMap.put(item, map));
                }
            }
            if (!ruleMap.isEmpty() && ruleMap.get(leaveTypeId) != null) {
                Map rule = (Map) ruleMap.get(leaveTypeId);
                if (!checkRule(rule, empId, paramMap)) {
                    return (String) rule.get("validateMsg");
                }
            }
        } catch (Exception e) {
            log.debug("假期自定义校验失败，msg=" + e.getMessage());
        }
        return "";
    }
}
