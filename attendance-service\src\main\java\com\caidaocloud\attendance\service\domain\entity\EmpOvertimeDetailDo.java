package com.caidaocloud.attendance.service.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.caidaocloud.attendance.service.domain.repository.IOvertimeDetailRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpOvertimeDetailPo;
import com.caidaocloud.util.ObjectConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Data
@Service
public class EmpOvertimeDetailDo {

    @ApiModelProperty("主键id")
    private Integer detailId;
    @ApiModelProperty("加班单id")
    private Integer overtimeId;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
    @ApiModelProperty("加班时长：分钟计算，精确到0.5小时")
    private Integer timeDuration;
    @ApiModelProperty("日期类型：1、工作日，2休息日，3法定假日,4特殊日期")
    private Short dateType;
    @ApiModelProperty("确认日期")
    private Long realDate;
    @ApiModelProperty("创建时间")
    private Long crttime;
    @ApiModelProperty("创建人")
    private Long crtuser;
    @ApiModelProperty("更新时间")
    private Long updtime;
    @ApiModelProperty("更新人")
    private Long upduser;
    @ApiModelProperty("实际加班时长：分钟计算")
    private Integer relTimeDuration;

    private Integer overtimeTypeId;
    private Float transferDuration;
    private Integer transferUnit;
    private Float leftDuration;
    private Integer carriedForward;

    private String workno;
    private String empName;
    private Long empid;

    @Autowired
    private IOvertimeDetailRepository overtimeDetailRepository;

    public List<EmpOvertimeDetailDo> getOvertimeDetails(Integer overtimeId) {
        List<EmpOvertimeDetailPo> list = overtimeDetailRepository.getOvertimeDetails(overtimeId);
        return ObjectConverter.convertList(list, EmpOvertimeDetailDo.class);
    }

    public List<EmpOvertimeDetailDo> getOvertimeDetailList(Long empId, Long startTime, Long endTime, List<Integer> status) {
        List<EmpOvertimeDetailPo> list = overtimeDetailRepository.getOvertimeDetailList(empId, startTime, endTime, status);
        return ObjectConverter.convertList(list, EmpOvertimeDetailDo.class);
    }

    public List<EmpOvertimeDetailDo> getEmpLeftDurationOvertimeDetail(String tenantId, String empIds, String overtimeTypeIds) {
        List<EmpOvertimeDetailPo> list = overtimeDetailRepository.getEmpLeftDurationOvertimeDetail(tenantId, empIds, overtimeTypeIds);
        return ObjectConverter.convertList(list, EmpOvertimeDetailDo.class);
    }
}
