package com.caidaocloud.attendance.core.wa.service;

import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaEmpGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaSobMapper;
import com.caidao1.wa.mybatis.model.WaEmpGroup;
import com.caidao1.wa.mybatis.model.WaEmpGroupExample;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidao1.wa.mybatis.model.WaSobExample;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class WaSobService {

	@Autowired
	private WaSobMapper waSobMapper;
	@Autowired
	private WaAttendanceConfigService waConfigService;
	@Autowired
	private WaEmpGroupMapper waEmpGroupMapper;

	public Boolean checkSobName(String belongid, Integer sobId,
								String sobName,List<Integer> statusList) {
		WaSobExample example = new WaSobExample();
		WaSobExample.Criteria criteria = example.createCriteria().andBelongOrgIdEqualTo(belongid).andWaSobNameEqualTo(sobName);
		if(CollectionUtils.isNotEmpty(statusList)){
			criteria.andStatusIn(statusList);
		}
    	if (sobId != null) {
    		criteria.andWaSobIdEqualTo(sobId);
    	}
    	return waSobMapper.countByExample(example) == 0;
	}
	@Transactional
	public int saveOrUpWaSob(WaSob sob,Long corpid, Long userid,String belongid) {
		int row=0 ;
		if(sob.getWaSobId() == null){
			sob.setCorpid(corpid);
			sob.setBelongOrgId(belongid);
			sob.setCrttime(DateUtil.getCurrentTime(true));
			sob.setCrtuser(userid);
			sob.setStatus(0);
			row = waSobMapper.insertSelective(sob);
		}else{
			sob.setUpdtime(DateUtil.getCurrentTime(true));
			sob.setUpduser(userid);
			row = waSobMapper.updateByPrimaryKeySelective(sob);
		}
		return row;
	}
	@Transactional
	public void delWaSobById(Integer sobId) {

		waSobMapper.deleteByPrimaryKey(sobId);
	}
	@Transactional
	public void closeWaSob(Integer sobId) throws Exception {
		WaSob sob  = waSobMapper.selectByPrimaryKey(sobId);
		sob.setStatus(1); // 0 未关闭 1 关闭
		sob.setSobCloseDate(DateUtil.getCurrentTime(true));
		waSobMapper.updateByPrimaryKeySelective(sob);
		//富士通定制逻辑----调休有效期逻辑,关闭帐套的时候，判断系统是否开启调休配额转付现
		String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + sob.getBelongOrgId() + RedisKeyDefine.IS_OPEN_TX_QUOTA_TO_CASH);
		if (isOpen != null && "1".equals(isOpen)) {
			waConfigService.txToCash(sob.getBelongOrgId(),sob.getWaGroupId(),sob.getStartDate(),sob.getEndDate());
		}
	}

	public List<Map> searchWaSobList(PageBean pageBean, Map<String, Object> paramsMap) {
		String sortString = pageBean.getOrder();
		MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(),Order.formString(sortString));
		paramsMap.put("filter", pageBean.getFilter());
		return waSobMapper.searchWaSobList(pageBounds,paramsMap);
	}

	/**
	 * 锁定薪资账套
	 *
	 * @param corpId
	 */
	@Transactional
	public void lockSob(Long corpId, Integer sobId) {
		WaSob waSob = new WaSob();
		waSob.setWaSobId(sobId);
		waSob.setIsLock(true);
		waSobMapper.updateByPrimaryKeySelective(waSob);
	}

	/**
	 * 解除锁定薪资账套
	 *
	 * @param sobId
	 */
	@Transactional
	public void unLockSob(Long corpId, Integer sobId) {
		WaSob waSob = new WaSob();
		waSob.setWaSobId(sobId);
		waSob.setIsLock(false);
		waSobMapper.updateByPrimaryKeySelective(waSob);
	}

	/**
	 * 是否超过考勤截止日
	 *
	 * @param empId
	 * @param startTime
	 * @return
	 */
	public WaSob getWaSob(Long empId, Long startTime) {
		WaEmpGroupExample example = new WaEmpGroupExample();
		example.createCriteria().andEmpidEqualTo(empId).
				andStartTimeLessThanOrEqualTo(startTime).
				andEndTimeGreaterThanOrEqualTo(startTime);
		List<WaEmpGroup> list = waEmpGroupMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(list)) {
			WaEmpGroup group = list.get(0);
			WaSobExample waSobExample = new WaSobExample();
			waSobExample.setOrderByClause("sob_end_date DESC");
			waSobExample.createCriteria().andWaGroupIdEqualTo(group.getWaGroupId())
					.andStartDateLessThanOrEqualTo(startTime)
					.andEndDateGreaterThanOrEqualTo(startTime);
			List<WaSob> waSobs = waSobMapper.selectByExample(waSobExample);
			if (CollectionUtils.isNotEmpty(waSobs)) {
				WaSob waSob = waSobs.get(0);
				return waSob;
			}
		}
		return null;
	}

    public List<Integer> listWaGroupIdByEmpId(Long empId) {
        WaEmpGroupExample example = new WaEmpGroupExample();
        example.createCriteria().andEmpidEqualTo(empId);
        List<WaEmpGroup> waEmpGroupList = waEmpGroupMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(waEmpGroupList)) {
            return null;
        }
        return waEmpGroupList.stream().map(WaEmpGroup::getWaGroupId).distinct().collect(Collectors.toList());
    }
}
