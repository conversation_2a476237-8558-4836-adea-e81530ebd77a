package com.caidao1.wa.enums;

/**
 * 发放周期
 */
public enum WaDistributionCycleEnum {
    NATURAL_YEAR(1, "自然年"),
    ENTRY_YEAR(2, "入职年"),
    CUSTOM_CYCLE(3, "自定义周期"),
    CHILD_YEAR(4, "子女出生年");

    private Integer index;
    private String name;

    WaDistributionCycleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (WaDistributionCycleEnum c : WaDistributionCycleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
