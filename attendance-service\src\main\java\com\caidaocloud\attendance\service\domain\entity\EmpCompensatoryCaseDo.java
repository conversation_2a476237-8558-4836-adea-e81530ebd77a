package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.domain.repository.IEmpCompensatoryCaseRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCase;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Service
public class EmpCompensatoryCaseDo {
    private Long id;
    private String tenantId;
    private Long applyId;
    private Long quotaId;
    private Integer quotaUnit;
    private Float quotaDay;
    private Long startDate;
    private Long endDate;
    private Float applyDuration;
    private Integer timeUnit;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private String workNo;
    private String empName;
    private String initiator;
    private String initiatorWorkNo;
    private String quotaName;

    @Autowired
    private IEmpCompensatoryCaseRepository empCompensatoryCaseRepository;

    public EmpCompensatoryCaseDo getDetailById(String tenantId, Long id) {
        return empCompensatoryCaseRepository.getDetailById(tenantId, id);
    }

    public void save(EmpCompensatoryCaseDo model) {
        if (null == model) {
            return;
        }
        empCompensatoryCaseRepository.save(ObjectConverter.convert(model, WaEmpCompensatoryCase.class));
    }

    public void update(EmpCompensatoryCaseDo model) {
        if (null == model) {
            return;
        }
        empCompensatoryCaseRepository.update(ObjectConverter.convert(model, WaEmpCompensatoryCase.class));
    }

    public PageList<EmpCompensatoryCaseDo> getEmpCompensatoryCaseList(MyPageBounds myPageBounds, Map params) {
        return empCompensatoryCaseRepository.getEmpCompensatoryCaseList(myPageBounds, params);
    }

    public void batchSave(List<EmpCompensatoryCaseDo> records) {
        if (null == records || records.size() <= 0) {
            return;
        }
        List<WaEmpCompensatoryCase> models = ObjectConverter.convertList(records, WaEmpCompensatoryCase.class);
        empCompensatoryCaseRepository.batchSave(models);
    }

    public List<EmpCompensatoryCaseDo> getApprovedOfCompensatoryCase(List<Long> empIdList, Long startDate, Long endDate) {
        if (CollectionUtils.isEmpty(empIdList)) {
            return Lists.newArrayList();
        }
        return empCompensatoryCaseRepository.getApprovedOfCompensatoryCase(empIdList, startDate, endDate);
    }

    public List<EmpCompensatoryCaseDo> getEmpCompensatoryCase(String tenantId, Long applyId) {
        return empCompensatoryCaseRepository.getEmpCompensatoryCase(tenantId, applyId);
    }
}
