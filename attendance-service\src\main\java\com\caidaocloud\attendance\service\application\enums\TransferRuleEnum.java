package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.dto.TransferRulePeriod;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.postgresql.util.PGobject;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum TransferRuleEnum {
    TO_SCALE(1, "按比例转换") {
        @Override
        public Map<String, Object> calTimeDuration(Float duration, Object periods, Float transferTime) {
            Map<String, Object> map = new HashMap<>();
            map.put("duration", 0);
            map.put("unit", 2);
            if (null != transferTime && transferTime > 0) {
                map.put("duration", BigDecimal.valueOf(duration * transferTime).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue());
            }
            return map;
        }
    },
    STEP_BY_DAY(2, "阶梯规则（天）") {
        @Override
        public Map<String, Object> calTimeDuration(Float duration, Object periods, Float transferTime) {
            Map<String, Object> map = new HashMap<>();
            map.put("duration", 0);
            map.put("unit", 1);
            if (duration > 0 && null != periods) {
                List<TransferRulePeriod> rules = getPeriods(periods);
                for (TransferRulePeriod rule : rules) {
                    if (duration >= rule.getStart() * 60 && duration < rule.getEnd() * 60) {
                        map.put("duration", rule.getDuration());
                        map.put("unit", rule.getUnit());
                        break;
                    }
                }
            }
            return map;
        }
    },
    STEP_BY_HOUR(3, "阶梯规则（小时）") {
        @Override
        public Map<String, Object> calTimeDuration(Float duration, Object periods, Float transferTime) {
            Map<String, Object> map = new HashMap<>();
            map.put("duration", 0);
            map.put("unit", 2);
            if (duration > 0 && null != periods) {
                List<TransferRulePeriod> rules = getPeriods(periods);
                for (TransferRulePeriod rule : rules) {
                    if (duration >= rule.getStart() * 60 && duration < rule.getEnd() * 60) {
                        map.put("duration", rule.getDuration());
                        map.put("unit", rule.getUnit());
                        break;
                    }
                }
            }
            return map;
        }
    },
    OTHER(4, "其他规则") {
        @Override
        public Map<String, Object> calTimeDuration(Float duration, Object periods, Float transferTime) {
            return new HashMap<>();
        }
    };

    private Integer index;
    private String desc;

    TransferRuleEnum(Integer index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public static List<TransferRulePeriod> getPeriods(Object transferPeriods) {
        try {
            if (null != transferPeriods) {
                PGobject pGobject = (PGobject) transferPeriods;
                List<TransferRulePeriod> periods = new ObjectMapper().readValue(pGobject.getValue(), new TypeReference<List<TransferRulePeriod>>() {
                });
                return periods;
            }
        } catch (Exception e) {
            e.getStackTrace();
        }
        return null;
    }

    public abstract Map<String, Object> calTimeDuration(Float duration, Object periods, Float transferTime);

    public static String getDescByIndex(int index) {
        for (TransferRuleEnum t : TransferRuleEnum.values()) {
            if (t.getIndex() == index) {
                return t.getDesc();
            }
        }
        return null;
    }

    public static TransferRuleEnum getTransferRuleEnum(Integer index) {
        for (TransferRuleEnum one : TransferRuleEnum.values()) {
            if (one.getIndex().equals(index)) {
                return one;
            }
        }
        return null;
    }


    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

