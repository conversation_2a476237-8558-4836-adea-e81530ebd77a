package com.caidaocloud.attendance.service.application.dto.overtime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量加班详情
 *
 * <AUTHOR>
 * @Date 2024/6/27
 */
@Data
public class BatchOvertimeDetailDto {
    @ApiModelProperty("加班日期")
    private String date;

    @ApiModelProperty("加班类型")
    private String overtimeType;

    @ApiModelProperty("加班开始时间")
    private String stime;

    @ApiModelProperty("加班结束时间")
    private String etime;

    @ApiModelProperty("申请加班时长")
    private String timeDuration;

    @ApiModelProperty("事由")
    private String reason;

    @ApiModelProperty("审批状态")
    private Short status;

    @ApiModelProperty("审批状态名称")
    private String statusTxt;
}
