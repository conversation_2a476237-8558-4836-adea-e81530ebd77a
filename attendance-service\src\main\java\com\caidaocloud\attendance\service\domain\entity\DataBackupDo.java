package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.JSONUtils;
import com.caidaocloud.attendance.service.domain.repository.IDataBackupRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaDataBackup;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.WebUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: Aaron.Chen
 * @Date: 2021/10/21 17:43
 * @Description:
 **/
@Slf4j
@Data
@Service
public class DataBackupDo {

    private Long id;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 功能模块
     */
    private String module;
    /**
     * 数据表库表名
     */
    private String table;
    /**
     * 备份数据
     */
    private String data;
    /**
     * api
     */
    private String api;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 集团ID
     */
    private Integer status;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private Long updateTime;

    @Autowired
    private IDataBackupRepository dataBackupRepository;
    @Autowired
    private ISessionService sessionService;
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public DataBackupDo() {
    }

    public DataBackupDo(String module, String table, Object params, UserInfo userInfo) {
        this.tenantId = userInfo.getTenantId();
        this.id = snowflakeUtil.createId();
        this.module = module;
        this.table = table;
        this.data = JSONUtils.ObjectToJson(params);
        this.api = WebUtil.getRequest().getRequestURI();
        this.ipAddress = WebUtil.getIP();
        this.status = 1;//1正常，2已还原
        long currentTime = System.currentTimeMillis() / 1000;
        this.createBy = userInfo.getUserId();
        this.createTime = currentTime;
        this.updateBy = userInfo.getUserId();
        this.updateTime = currentTime;
    }

    public void save(DataBackupDo dataBackupDo) {
        WaDataBackup dataBackup = ObjectConverter.convert(dataBackupDo, WaDataBackup.class);
        dataBackupRepository.save(dataBackup);
    }
}
