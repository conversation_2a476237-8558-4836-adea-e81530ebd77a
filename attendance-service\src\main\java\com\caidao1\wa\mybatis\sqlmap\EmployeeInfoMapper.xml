<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidao1.wa.mybatis.mapper.EmployeeInfoMapper">
    <resultMap id="BaseResultMap" type="com.caidao1.auth.mybatis.model.SysEmpInfo">
        <id column="empid" jdbcType="BIGINT" property="empid" />
        <result column="belong_org_id" jdbcType="VARCHAR" property="belongOrgId" />
        <result column="orgid" jdbcType="INTEGER" property="orgid" />
        <result column="post_id" jdbcType="INTEGER" property="postId" />
        <result column="emp_name" jdbcType="VARCHAR" property="empName" />
        <result column="eng_name" jdbcType="VARCHAR" property="engName" />
        <result column="gender" jdbcType="BIGINT" property="gender" />
        <result column="corpid" jdbcType="BIGINT" property="corpid" />
        <result column="payroll_stats" jdbcType="INTEGER" property="payrollStats" />
        <result column="stats" jdbcType="INTEGER" property="stats" />
        <result column="workno" jdbcType="VARCHAR" property="workno" />
        <result column="photo_url" jdbcType="VARCHAR" property="photoUrl" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="email" jdbcType="VARCHAR" property="email" />
        <result column="office_tel" jdbcType="VARCHAR" property="officeTel" />
        <result column="crtuser" jdbcType="BIGINT" property="crtuser" />
        <result column="crttime" jdbcType="BIGINT" property="crttime" />
        <result column="upduser" jdbcType="BIGINT" property="upduser" />
        <result column="updtime" jdbcType="BIGINT" property="updtime" />
        <result column="isexsit_account" jdbcType="BIT" property="isexsitAccount" />
        <result column="employ_type" jdbcType="BIGINT" property="employType" />
        <result column="hire_date" jdbcType="BIGINT" property="hireDate" />
        <result column="first_work_date" jdbcType="BIGINT" property="firstWorkDate" />
        <result column="fixed_num" jdbcType="VARCHAR" property="fixedNum" />
        <result column="job_grade" jdbcType="INTEGER" property="jobGrade" />
        <result column="ee_type" jdbcType="INTEGER" property="eeType" />
        <result column="job_title" jdbcType="INTEGER" property="jobTitle" />
        <result column="prodead_line" jdbcType="BIGINT" property="prodeadLine" />
        <result column="worktime_type" jdbcType="INTEGER" property="worktimeType" />
        <result column="work_type" jdbcType="INTEGER" property="workType" />
        <result column="termination_date" jdbcType="BIGINT" property="terminationDate" />
        <result column="termination_type" jdbcType="INTEGER" property="terminationType" />
        <result column="termination_reason" jdbcType="INTEGER" property="terminationReason" />
        <result column="termination_reason_desc" jdbcType="VARCHAR" property="terminationReasonDesc" />
        <result column="used_name" jdbcType="VARCHAR" property="usedName" />
        <result column="labor_dispatch" jdbcType="VARCHAR" property="laborDispatch" />
        <result column="tm_type" jdbcType="INTEGER" property="tmType" />
        <result column="cost_items" jdbcType="OTHER" property="costItems" />
        <result column="job_level" jdbcType="INTEGER" property="jobLevel" />
        <result column="ext_custom_col" jdbcType="OTHER" property="extCustomCol" />
        <result column="store_id" jdbcType="INTEGER" property="storeId" />
        <result column="friends" jdbcType="ARRAY" property="friends" />
        <result column="site_id" jdbcType="INTEGER" property="siteId" />
        <result column="belongcorp_id" jdbcType="INTEGER" property="belongcorpId" />
        <result column="leader_empid" jdbcType="BIGINT" property="leaderEmpid" />
        <result column="career_seq" jdbcType="INTEGER" property="careerSeq" />
        <result column="belong_field" jdbcType="INTEGER" property="belongField" />
        <result column="is_mgt" jdbcType="INTEGER" property="isMgt" />
        <result column="is_gj" jdbcType="INTEGER" property="isGj" />
        <result column="job_duty" jdbcType="VARCHAR" property="jobDuty" />
        <result column="referee_emp_id" jdbcType="INTEGER" property="refereeEmpId" />
        <result column="struct_id" jdbcType="INTEGER" property="structId" />
        <result column="siteids" jdbcType="VARCHAR" property="siteids" />
        <result column="orders" jdbcType="VARCHAR" property="orders" />
        <result column="job_age_deduct" jdbcType="REAL" property="jobAgeDeduct" />
        <result column="hire_type" jdbcType="INTEGER" property="hireType" />
        <result column="is_disability" jdbcType="INTEGER" property="isDisability" />
        <result column="is_martyrs_family" jdbcType="INTEGER" property="isMartyrsFamily" />
        <result column="is_lonely_elder" jdbcType="INTEGER" property="isLonelyElder" />
        <result column="disability_card_no" jdbcType="VARCHAR" property="disabilityCardNo" />
        <result column="martyrs_family_card_no" jdbcType="VARCHAR" property="martyrsFamilyCardNo" />
        <result column="first_hire_date" jdbcType="BIGINT" property="firstHireDate" />
        <result column="social_pay_months" jdbcType="INTEGER" property="socialPayMonths" />
    </resultMap>
    <select id="getGroupEmpIds" resultType="long">
        SELECT ei.empid
        FROM sys_emp_info ei
        LEFT JOIN sys_emp_privacy ep on ei.empid = ep.empid
        LEFT JOIN wa_emp_leave wel on wel.empid = ei.empid
        WHERE ei.belong_org_id=#{tenantId} and ei.deleted=0
        <if test="leaveId != null">
            and wel.leave_id=#{leaveId}
        </if>
        <if test="empId != null">
            and ei.empid=#{empId}
        </if>
        <if test="leaveTypeId != null">
            and wel.leave_type_id=#{leaveTypeId}
        </if>
        <if test="groupExp != null and groupExp != ''">
            AND (${groupExp})
        </if>
    </select>
</mapper>