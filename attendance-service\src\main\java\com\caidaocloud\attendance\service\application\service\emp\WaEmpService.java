package com.caidaocloud.attendance.service.application.service.emp;

import com.caidaocloud.attendance.service.application.dto.emp.EmpOrgPostInfo;
import com.caidaocloud.attendance.service.application.feign.WaEmpFeginClient;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 员工信息
 *
 * <AUTHOR>
 * @Date 2024/6/28
 */
@Slf4j
@Service
public class WaEmpService {
    @Autowired
    private WaEmpFeginClient waEmpFeginClient;

    public EmpWorkInfoVo getEmpWorkInfo(String empId) {
        Result<EmpWorkInfoVo> result = waEmpFeginClient.getEmpWorkInfo(empId, System.currentTimeMillis());
        EmpWorkInfoVo empWorkInfo = null;
        if (null == result || !result.isSuccess() || null == (empWorkInfo = result.getData())) {
            return empWorkInfo;
        }

        return empWorkInfo;
    }

    public EmpOrgPostInfo getEmpOrgPostInfo(String empId) {
        EmpWorkInfoVo empWorkInfo = getEmpWorkInfo(empId);
        if (null == empWorkInfo) {
            return new EmpOrgPostInfo();
        }
        EmpOrgPostInfo info = new EmpOrgPostInfo();
        info.setOrg(empWorkInfo.getOrganizeTxt());
        info.setPost(empWorkInfo.getPostTxt());
        info.setJob(empWorkInfo.getJobTxt());
        if (StringUtils.isNotBlank(empWorkInfo.getJobGradeTxt())) {
            info.setJobGrade(empWorkInfo.getJobGradeTxt());
        } else if (null != empWorkInfo.getJobGrade()) {
            info.setJobGrade(empWorkInfo.getJobGrade().getStartGradeName());
        }
        return info;
    }
}
