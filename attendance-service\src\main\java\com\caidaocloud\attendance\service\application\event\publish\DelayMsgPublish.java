package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DelayMsgPublish {
    @Resource
    private MqMessageProducer<BaseDelayMsg> producer;
    @Value("${wf.msg.exchange:wf.msg.fac.direct.exchange}")
    private String EXCHANGE;
    @Value("${wf.msg.routingKey:direct.routingKey}")
    private String ROUTING_KEY;
    @Value("${wf.msg.queue:direct.queue}")
    private String GEN_QUEUE;

    public void publish(String msg, Integer delay, String tenantId) {
        BaseDelayMsg message = new BaseDelayMsg();
        message.setBody(msg);
        message.setExchange(String.format("%s.delay.attendance.ttl", EXCHANGE));
        message.setRoutingKey(String.format("%s.delay.attendance.ttl.%s", ROUTING_KEY, tenantId));
        // 毫秒
        this.producer.convertAndSend(message, delay);
    }
}
