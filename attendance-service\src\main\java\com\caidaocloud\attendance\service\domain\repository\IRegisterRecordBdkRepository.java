package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordBdkDo;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordBdkPo;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;

import java.util.List;
import java.util.Map;

public interface IRegisterRecordBdkRepository {
    WaRegisterRecordBdkPo getWaRegisterRecordById(Long recordId);

    void updateRegisterRecordBdk(WaRegisterRecordBdkPo recordBdkPo);

    void save(WaRegisterRecordBdkPo recordBdkPo);

    Integer getEmpDkCountByType(Long empId, Integer type, Long startTime, Long endTime);

    AttendancePageResult<WaRegisterRecordBdkDo> getRegisterPageList(RegisterRecordRequestDto requestDto);

    List<WaRegisterRecordBdkDo> getEmpBdkRegisterList(String belongOrgId, List<Long> empIdList, Long startDate, Long endDate);

    Map getRegisterDetailById(Long corpId, Long registerId);

    List<WaRegisterRecordBdkPo> getEmpRegisterRecordList(Long empId, Long startDate, Long endDate, List<Integer> statusList);

    int delete(Long recordId);
}
