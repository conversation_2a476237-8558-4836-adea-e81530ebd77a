package com.caidao1.integrate.reader;

import com.caidao1.commons.utils.SpringUtils;
import com.caidao1.integrate.bean.DatabaseDefineBean;
import com.caidao1.integrate.datasource.CusDataSource;
import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.caidao1.ioc.mybatis.mapper.IocImportMapper;
import com.caidao1.ioc.util.IocUtil;
import com.weibo.api.motan.core.extension.SpiMeta;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpiMeta(name = "db")
@Component
public class DbSourceReader implements SourceReader {

    private CusDataSource cusDataSource;

    private NamedParameterJdbcTemplate cusJdbcTemplate;

    private IocImportMapper iocImportMapper;

    public List<Map<String, Object>> getSourceResult(Long corpId, String belongId, SysDataInput dataInput, Map<String, Object> mapJson, Map exparams, List<String> logList,Map returnMap) throws Exception {
        if (cusDataSource == null) cusDataSource = SpringUtils.getBean(CusDataSource.class);
        if (cusJdbcTemplate == null) cusJdbcTemplate = SpringUtils.getBean(NamedParameterJdbcTemplate.class);
        if (iocImportMapper == null) iocImportMapper = SpringUtils.getBean(IocImportMapper.class);

        List<Map<String, Object>> sourceResult = new ArrayList<Map<String, Object>>();
        String sql = IocUtil.formatExp(corpId, belongId, dataInput.getSourceExp(), dataInput.getWorkTime());
        if ("local".equals((String) mapJson.get("database.url"))) {
            sourceResult = iocImportMapper.queryKeyListBySql(sql);
        } else {
            DatabaseDefineBean databaseDefineBean = new DatabaseDefineBean((String) mapJson.get("database.username"), (String) mapJson.get("database.password"), (String) mapJson.get("database.url"), (String) mapJson.get("database.driver"));
            cusDataSource.setDatabaseDefineBean(databaseDefineBean);
            Map params = new HashMap();
            sourceResult = cusJdbcTemplate.queryForList(sql, params);
        }
        return sourceResult;
    }
}