package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IEmpTransitRepository;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.dto.PageResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Data
@Service
public class EmpTransitAppLyDo {

    private Long id;

    private Integer eventType;

    private String eventName;

    private Long applyTime;

    private String startTime;

    private String endTime;

    private String duration;

    private String businessKey;

    public void buildKey(){
        this.setBusinessKey(String.format("%s_%s",id,eventType));
    }

    @Resource
    private IEmpTransitRepository empTransitRepository;

    public PageResult<EmpTransitAppLyDo> getEmpTransitAppLyList(AttendanceBasePage basePage, Map params){
        return empTransitRepository.getEmpTransitAppLyList(basePage,params);
    }
}
