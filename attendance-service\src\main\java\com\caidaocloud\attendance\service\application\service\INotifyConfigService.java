package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.application.dto.NotifyConfigItemDto;
import com.caidaocloud.attendance.service.domain.entity.NotifyConfigDo;
import com.caidaocloud.attendance.service.interfaces.dto.NotifyConfigDto;
import com.caidaocloud.dto.UserInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/16
 */
public interface INotifyConfigService {

    NotifyConfigDto getNotifyConfig(String belongId);

    int saveNotifyConfig(NotifyConfigDto dto, UserInfo userInfo);

    List<NotifyConfigDo> getNotifyPageList(int pageNo, int PageSize);

    List<NotifyConfigItemDto> getNotifyConfigs(String tenantId);

    String updateNotifyConfigStatus(Integer num, Integer status, UserInfo userInfo);

    void attendanceAbnormalJobRefresh(String tenantId);

    void attendanceDetailJobRefresh(String tenantId);
}


