package com.caidao1.wa.enums;

public enum WaOtValidTimeCalTypeEnum {
    APPLY_TIME(1, "按审批（申请）时长"),
    APPLY_REG_INTERSECTION(2, "取加班时间段和打卡时间段的交集"),
    REG_TIME(3, "按打卡时长计算");

    private Integer index;
    private String name;

    WaOtValidTimeCalTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (WaOtValidTimeCalTypeEnum c : WaOtValidTimeCalTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
