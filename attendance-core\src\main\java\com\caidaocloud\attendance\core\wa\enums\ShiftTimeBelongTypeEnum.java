package com.caidaocloud.attendance.core.wa.enums;

import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

/**
 * 班次设置：时间归属类型
 */
public enum ShiftTimeBelongTypeEnum {
    TODAY(1, "当日", 202803),
    NEXT_DAY(2, "次日", 202804),
    PRE_DAY(3, "前日", 202833);

    private Integer index;
    private String name;
    // 多语言编码
    private Integer code;

    ShiftTimeBelongTypeEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(int index) {
        for (ShiftTimeBelongTypeEnum c : ShiftTimeBelongTypeEnum.values()) {
            if (c.getIndex() == index) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return c.name;
            }
        }
        return "";
    }

    public static Integer getIndexByName(String name) {
        for (ShiftTimeBelongTypeEnum c : ShiftTimeBelongTypeEnum.values()) {
            if (c.getName().equals(name)) {
                return c.index;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
