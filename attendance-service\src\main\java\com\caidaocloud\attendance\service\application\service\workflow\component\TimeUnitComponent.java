package com.caidaocloud.attendance.service.application.service.workflow.component;

import com.caidaocloud.attendance.service.application.enums.PreTimeUnitEnum;
import com.caidaocloud.workflow.annotation.WfComponentValueEnumDef;
import com.caidaocloud.workflow.dto.WfComponentValueDto;
import com.google.common.collect.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 注册时间单位
 */
@Component
public class TimeUnitComponent extends WfComponentValueEnumDef {
    @NotNull
    @Override
    public List<WfComponentValueDto> enumList() {
        return Lists.newArrayList(
                new WfComponentValueDto(PreTimeUnitEnum.DAY.getName(), PreTimeUnitEnum.DAY.getIndex().toString()),
                new WfComponentValueDto(PreTimeUnitEnum.HOUR.getName(), PreTimeUnitEnum.HOUR.getIndex().toString()));
    }
}
