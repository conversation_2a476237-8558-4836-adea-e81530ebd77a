package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.application.dto.EmpShiftChangeDto;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/12/1 15:49
 * @Description:
 **/
public interface IEmpShiftChangeService {

    List<EmpShiftChangeDto> getEmpShiftChanges(List<Long> empIds, Long startDate, Long endDate, Integer status);

    void saveEmpShiftChanges(List<EmpShiftChangeDto> changes);

    void updateEmpShiftChanges(List<EmpShiftChangeDto> changes);
}
