package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.AttEmpGroupDo;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpGroupPo;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupDto;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupReqDto;

import java.util.List;


public interface IEmpGroupRepository {

    AttendancePageResult<AttEmpGroupDo> getWaEmpGroupList(AttEmpGroupReqDto dto);

    List<AttEmpGroupDo> getWaEmpGroupListByPeriod(AttEmpGroupDto dto);

    AttEmpGroupDo getWaEmpGroupById(Integer empGroupId);

    void deleteWaEmpGroup(List<Integer> ids);

    void saveWaEmpGroup(WaEmpGroupPo waEmpGroupPo);

    void updateWaEmpGroup(WaEmpGroupPo waEmpGroupPo);

    List<WaEmpGroupPo> getWaEmpGroupByIds(List<Integer> empGroupIds);

    List<WaEmpGroupPo> getEmpGroup(String belongOrgId, Long empId, Long currentTime);

    List<WaEmpGroupPo> getEmpGroupByEmpIds(String belongOrgId, List<Long> empIds, Long currentTime);

    List<WaEmpGroupPo> getEmpGroupByEmpIds(String tenantId, List<Long> empIds, Long startDate, Long endDate);
}

