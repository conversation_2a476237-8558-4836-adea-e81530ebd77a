package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaGroupDo;

import java.util.List;

/**
 * 考勤方案
 *
 * <AUTHOR>
 * @Date 2021/8/3
 */
public interface IWaGroupRepository {

    List<WaGroupDo> getWaGroupList(String belongOrgId);

    WaGroupDo selectById(Integer waGroupId);

    void updateGroupExpCondition(Integer waGroupId, String groupExpCondition);
}
