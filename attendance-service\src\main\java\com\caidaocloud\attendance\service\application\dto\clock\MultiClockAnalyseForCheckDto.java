package com.caidaocloud.attendance.service.application.dto.clock;

import com.caidao1.wa.mybatis.model.WaShiftDef;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MultiClockAnalyseForCheckDto {
    private Long onDutyStartTime;
    private Long onDutyEndTime;
    private Long offDutyStartTime;
    private Long offDutyEndTime;
    private WaShiftDef shiftDef;
    private boolean ifMultiWorkTime;
}
