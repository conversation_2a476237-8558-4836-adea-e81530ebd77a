package com.caidao1.mobile.mybatis.mapper;

import java.util.List;
import java.util.Map;

/**
 * Created by Intellij IDEA
 *
 * <AUTHOR> Aaron
 * @Version : 1.0 2018/8/13 上午10:12
 */
public interface MobileV18EmployeeMapper {

    List<Map> searchSysColAuthConfig(Map param);

    List<Map> searchColCustomAuthConfig(Map param);

    List<Map> findEmpInfo(Map param);

    List<Map> findEmpPrivacyInfo(Map param);

    String getCountryNameById(Integer countryId);

    List<Integer> getTableExtColInfo(Map param);

    Map getExtColInfo(Map param);

    List<String> getSysColInfo(Map param);

    List<Map> selectFieldLabelByTableName(Map param);

    List<Map> selectEmployeeInfo(Map param);

    List<Map> searchSysCompletionInfo(Map param);

    Map selectDelBeforeInfo(Map param);

    List<Map> searchTableField(Map param);

    List<Map> searchAllFieldConfig(Map param);
    /**
     * 查询员工信息对应外键值的value
     * @param param
     * @return
     */
    Map searchForeignList(Map param);
}
