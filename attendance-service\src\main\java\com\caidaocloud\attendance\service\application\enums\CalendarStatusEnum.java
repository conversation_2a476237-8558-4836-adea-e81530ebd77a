package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

public enum CalendarStatusEnum {
    ABNORMAL(1,"异常", AttendanceCodes.ABNORMAL),
    NORMAL(2,"正常", AttendanceCodes.NORMAL),
    OVERTIME(3,"加班", AttendanceCodes.OVERTIME),
    VACATION(4,"休假", AttendanceCodes.LEAVE),
    TRAVEL(5,"出差", AttendanceCodes.TRAVEL),
    LATE(6,"迟到", AttendanceCodes.LATE),
    EARLY(7,"早退", AttendanceCodes.EARLY),
    ABSENT(8,"旷工", AttendanceCodes.ABSENT),
    PATCH(9,"补卡", AttendanceCodes.CARD_REPLACEMENT);

    private Integer index;
    private String name;
    private Integer code;

    CalendarStatusEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(int index) {
        for (CalendarStatusEnum c : CalendarStatusEnum.values()) {
            if (c.getIndex() == index) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return c.name;
            }
        }
        return "";
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
