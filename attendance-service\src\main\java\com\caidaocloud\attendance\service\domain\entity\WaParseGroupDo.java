package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWaParseGroupRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 考勤分析规则设置
 *
 * <AUTHOR>
 * @Date 2021/9/21
 */
@Data
@Slf4j
@Service
public class WaParseGroupDo {
    private Integer parseGroupId;
    private String parseGroupName;
    private String lateCycle;
    private BigDecimal lateCount;
    private Integer lateUnit;
    private String earlyCycle;
    private BigDecimal earlyCount;
    private Integer earlyUnit;
    private Integer parseType;
    private String rangeCycle;
    private BigDecimal rangeMin;
    private Integer rangeUnit;
    private BigDecimal absentLimit;
    private String belongOrgid;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private Boolean otParse;
    private Boolean lvParse;
    private Integer registerMiss;
    private Integer lateAllowNumber;
    private Integer lateAllowUnit;
    private Integer earlyAllowNumber;
    private Integer earlyAllowUnit;
    private Boolean ignoreLocationExp;
    private Boolean isAnalyzeLateEarly;
    private Object absentConditionJsonb;
    private Boolean isOuterSign;
    private Object otPaseJsonb;
    private Boolean isFlexibleWorking;
    private Boolean otSumParse;
    private Integer clockType;
    private String clockRule;
    private Integer outParseRule;
    private Integer flexibleWorkSwitch;
    private Integer flexibleWorkType;
    private String allowedDateType;
    private Integer abnormalType;
    private Boolean fieldClockLinkShift;
    private Integer minLateTime;
    private Short minLateTimeUnit;
    private Integer minEarlyTime;
    private Short minEarlyTimeUnit;
    private Boolean leaveExemptionSwitch;
    private Integer statisticType;
    private Integer convertTime;
    private Integer convertScale;
    private Integer convertTimeLimit;
    private Integer expConvertScale;
    private Integer convertKgScale;

    //其它表字段
    private Long empid;
    private Long startTime;
    private Long endTime;
    private Integer waGroupId;
    private Integer cyleStartdate;
    private Integer cyleMonth;

    @Autowired
    private IWaParseGroupRepository waParseGroupRepository;

    public List<WaParseGroupDo> getWaParseGroupList(String belongOrgId, List<Long> empIds, Long startDate, Long endDate) {
        return waParseGroupRepository.getWaParseGroupList(belongOrgId, empIds, startDate, endDate);
    }

    public WaParseGroupDo getById(Integer parseGroupId) {
        return waParseGroupRepository.selectById(parseGroupId);
    }
}
