package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.interfaces.dto.task.TaskDto;
import com.caidaocloud.dto.UserInfo;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/4/16 11:25
 * @Description:
 **/
public interface ITaskService {

    TaskDto save(TaskDto task);

    void update(TaskDto task, UserInfo userInfo);

    TaskDto getTaskById(Long id);
}
