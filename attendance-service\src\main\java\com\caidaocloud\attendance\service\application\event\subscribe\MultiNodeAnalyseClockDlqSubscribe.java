package com.caidaocloud.attendance.service.application.event.subscribe;

import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.service.application.config.AttendanceMqProperties;
import com.caidaocloud.attendance.service.application.config.MultiNodeClockAnalyseRabbitConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * 多节点打卡分析死信队列消费者
 * 用于监控和处理失败的消息
 * 仅在通过 Nacos 配置启用 DLQ 时生效：attendance.mq.enableDLQ=true
 */
@Slf4j
@Component
@ConditionalOnProperty(
    name = "attendance.mq.enableDLQ", 
    havingValue = "true", 
    matchIfMissing = false  // 只有明确设置为 true 时才启用
)
public class MultiNodeAnalyseClockDlqSubscribe {

    @Autowired
    private AttendanceMqProperties mqProperties;

    @PostConstruct
    public void init() {
        log.info("MultiNodeAnalyseClockDlqSubscribe initialized - enableDLQ: {}, maxRetryCount: {}", 
                mqProperties.isEnableDLQ(), mqProperties.getMaxRetryCount());
    }

    @RabbitHandler
    @RabbitListener(
        queues = MultiNodeClockAnalyseRabbitConfig.DLQ_QUEUE,
        containerFactory = "rabbitListenerContainerFactory"  // 使用默认的容器工厂，自动ACK即可
    )
    public void processDlqMessage(String messageBody, Message message) {
        log.error("============ 死信队列消息处理开始 ============");
        log.error("DLQ Message received at: {}", DateUtil.getCurrentTime(true));
        log.error("Message body: {}", messageBody);
        log.error("Current MQ config - enableDLQ: {}, maxRetryCount: {}, failureStrategy: {}", 
                mqProperties.isEnableDLQ(), mqProperties.getMaxRetryCount(), mqProperties.getFailureStrategy());
        
        // 获取消息属性
        Map<String, Object> headers = message.getMessageProperties().getHeaders();
        log.error("Message headers: {}", headers);
        
        // 记录原始队列信息
        String originalQueue = (String) headers.get("x-first-death-queue");
        String originalExchange = (String) headers.get("x-first-death-exchange");
        log.error("Original queue: {}, Original exchange: {}", originalQueue, originalExchange);
        
        // 记录死信详细信息
        Object deathHeader = headers.get("x-death");
        if (deathHeader instanceof java.util.List) {
            @SuppressWarnings("unchecked")
            java.util.List<Map<String, Object>> deaths = (java.util.List<Map<String, Object>>) deathHeader;
            for (int i = 0; i < deaths.size(); i++) {
                Map<String, Object> death = deaths.get(i);
                log.error("Death #{} - Queue: {}, Reason: {}, Count: {}, Exchange: {}, Routing-Key: {}, Time: {}", 
                        i + 1,
                        death.get("queue"), 
                        death.get("reason"), 
                        death.get("count"),
                        death.get("exchange"),
                        death.get("routing-keys"),
                        death.get("time"));
            }
        }
        
        try {
            handleFailedMessage(messageBody, headers);
        } catch (Exception e) {
            log.error("处理死信队列消息时发生异常: {}", e.getMessage(), e);
        }
        
        log.error("============ 死信队列消息处理结束 ============");
        // 注意：这里使用自动ACK，消息会被自动确认
        // 如果需要手动控制ACK，可以修改containerFactory并添加Channel参数
    }

    /**
     * 处理失败的消息
     * 可以根据业务需求实现具体的处理逻辑
     */
    private void handleFailedMessage(String messageBody, Map<String, Object> headers) {
        // 可以实现以下逻辑：
        // 1. 解析消息内容，提取关键信息
        // 2. 记录失败详情到数据库
        // 3. 发送告警通知（邮件、钉钉、短信等）
        // 4. 标记相关业务数据状态
        // 5. 人工介入处理标记
        
        log.warn("Failed message needs manual handling or business compensation:");
        log.warn("Message content: {}", messageBody);
        log.warn("MQ Configuration: enableDLQ={}, maxRetryCount={}, failureStrategy={}", 
                mqProperties.isEnableDLQ(), mqProperties.getMaxRetryCount(), mqProperties.getFailureStrategy());
        
        // 示例：解析消息获取关键业务信息
        try {
            // BatchClockAnalyseDto dto = FastjsonUtil.convertObject(messageBody, BatchClockAnalyseDto.class);
            // log.warn("Failed analysis - BelongOrgId: {}, StartDate: {}, EndDate: {}, EmpCount: {}", 
            //         dto.getBelongOrgId(), dto.getStartDate(), dto.getEndDate(), 
            //         dto.getEmpIds() != null ? dto.getEmpIds().size() : 0);
        } catch (Exception e) {
            log.error("Failed to parse DLQ message: {}", e.getMessage());
        }
        
        // TODO: 根据实际业务需求实现以下逻辑
        // 1. 发送告警通知
        // alertService.sendAlert("多节点打卡分析处理失败", messageBody, headers);
        
        // 2. 记录失败日志到数据库
        // failedMessageLogService.saveFailedMessage(messageBody, headers, "MULTI_NODE_CLOCK_ANALYSE");
        
        // 3. 发送钉钉/企业微信通知
        // dingTalkService.sendMessage("考勤分析失败告警", messageBody);
        
        // 4. 标记业务数据需要人工处理
        // businessService.markForManualProcessing(dto.getBelongOrgId(), dto.getStartDate(), dto.getEndDate());
    }
} 