package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.application.dto.AdjustShiftConfigurationDto;
import com.caidaocloud.attendance.service.application.dto.SysConfigurationDto;
import com.caidaocloud.attendance.service.interfaces.dto.common.ErrorCollectDto;
import com.caidaocloud.attendance.service.interfaces.dto.config.SysConfigReqDto;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>Chen
 * @Date: 2022/3/21 9:40
 * @Description:
 **/
public interface IConfigService {

    void updateConfig(SysConfigReqDto dto);

    AdjustShiftConfigurationDto getConfiguration(String configCode);

    List<SysConfigurationDto> getSysConfigs();

    boolean checkSwitchStatus(String configCode);

    void collectError(ErrorCollectDto collect);
}
