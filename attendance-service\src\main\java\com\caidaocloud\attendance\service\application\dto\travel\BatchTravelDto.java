package com.caidaocloud.attendance.service.application.dto.travel;

import com.caidaocloud.attendance.service.application.dto.emp.CostCenterDto;
import com.caidaocloud.attendance.service.application.service.org.CostCenterService;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelSaveDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 批量出差-数据保存DTO
 *
 * <AUTHOR>
 * @Date 2024/4/10
 */
@Data
public class BatchTravelDto {
    @ApiModelProperty("申请人员工ID")
    private Long empId;

    @ApiModelProperty("出差类型ID")
    private Long travelTypeId;

    @ApiModelProperty("出差地省ID")
    private Long province;

    @ApiModelProperty("出差地市ID")
    private Long city;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("访问对象")
    private String accessObject;

    @ApiModelProperty("目的")
    private String purpose;

    @ApiModelProperty("费用承担（成本中心ID）")
    private String costCenterId;

    @ApiModelProperty("费用承担（成本中心名称）")
    private String costCenterName;

    @ApiModelProperty("同行人员")
    private String peerPeople;

    @ApiModelProperty("不在时业务联络")
    private String businessContact;

    @ApiModelProperty("联系电话")
    private String tel;

    @ApiModelProperty("出差明细")
    private List<BatchTravelTimeDetailDto> detailList;

    public EmpTravelSaveDto doConvert() {
        EmpTravelSaveDto empTravelSaveDto = new EmpTravelSaveDto();
        empTravelSaveDto.setEmpId(this.empId);
        empTravelSaveDto.setTravelTypeId(this.travelTypeId);
        List<BatchTravelTimeDetailDto> detailList = this.detailList;
        // 出差状态 1 出发 2 中转 3 回归
        Optional<BatchTravelTimeDetailDto> start = detailList.stream().filter(o -> o.getTraveStatus() == 1).findFirst();
        empTravelSaveDto.setStartTime(start.map(BatchTravelTimeDetailDto::getTravelDate).orElse(null));
        Optional<BatchTravelTimeDetailDto> end = detailList.stream().filter(o -> o.getTraveStatus() == 3).findFirst();
        empTravelSaveDto.setEndTime(end.map(BatchTravelTimeDetailDto::getTravelDate).orElse(empTravelSaveDto.getStartTime()));
        empTravelSaveDto.setShowDay(0);
        empTravelSaveDto.setShowMin(0);
        List<Integer> travelModeList = detailList.stream().filter(o -> StringUtils.isNotBlank(o.getTransportation()))
                .map(o -> Integer.valueOf(o.getTransportation())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(travelModeList)) {
            empTravelSaveDto.setTravelMode(travelModeList.stream().toArray(Integer[]::new));
        }
        empTravelSaveDto.setProvince(this.province);
        empTravelSaveDto.setCity(this.city);
        empTravelSaveDto.setReason(this.remarks);
        return empTravelSaveDto;
    }

    public String getWaEmpTravelExtCustomCol() {
        Map<String, Object> extCustomCol = new HashMap<>();
        extCustomCol.put("accessObject", this.accessObject);
        extCustomCol.put("purpose", this.purpose);
        extCustomCol.put("costCenterId", this.costCenterId);
        CostCenterDto costCenterDto;
        if (StringUtils.isNotBlank(this.costCenterId)
                && null != (costCenterDto = SpringUtil.getBean(CostCenterService.class).getCostCenter(this.costCenterId))) {
            extCustomCol.put("costCenterName", costCenterDto.getCostCenterName());
        }
        extCustomCol.put("peerPeople", this.peerPeople);
        extCustomCol.put("businessContact", this.businessContact);
        extCustomCol.put("tel", this.tel);
        return FastjsonUtil.toJson(extCustomCol);
    }

    public Map<Long, String> getTravelDayTimeExtInfoMap() {
        Map<Long, String> timeMap = new HashMap<>();
        if (null == this.detailList || this.detailList.isEmpty()) {
            return timeMap;
        }
        Map<Long, List<BatchTravelTimeDetailDto>> groupMap = this.detailList.stream()
                .collect(Collectors.groupingBy(BatchTravelTimeDetailDto::getTravelDate));
        groupMap.forEach((date, list) -> timeMap.put(date, FastjsonUtil.toJsonStr(list)));
        return timeMap;
    }
}
