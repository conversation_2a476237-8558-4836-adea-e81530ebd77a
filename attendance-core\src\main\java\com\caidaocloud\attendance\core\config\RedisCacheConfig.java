package com.caidaocloud.attendance.core.config;


import com.caidaocloud.attendance.core.integrate.listener.EmailListerner;
import com.caidaocloud.attendance.core.integrate.listener.IntegrateMessageListener;
import com.caidaocloud.attendance.core.integrate.listener.SmsMessageListener;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.crazycake.shiro.IRedisManager;
import org.crazycake.shiro.RedisManager;
import org.crazycake.shiro.RedisSentinelManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.util.Pool;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by darren on 2017/5/22.
 */
@Configuration
@EnableRedisHttpSession
public class RedisCacheConfig {

    @Value("${spring.redis.host:localhost}")
    private String host;

    @Value("${spring.redis.port:6379}")
    private int port;

    @Value("${spring.redis.password:}")
    private String password;

    @Value("${spring.redis.timeout}")
    private int timeout;

    @Value("${spring.redis.pool.max-idle}")
    private int maxIdle;

    @Value("${spring.redis.pool.min-idle}")
    private int minIdle;

    @Value("${spring.redis.pool.max-active}")
    private int maxActive;

    @Value("${spring.redis.pool.max-wait}")
    private long maxWait;

    @Value("${spring.redis.database:0}")
    private int database;

    @Value("${spring.redis.sentinel.master:}")
    private String master;

    @Value("${spring.redis.sentinel.nodes:}")
    private String nodes;

    @Value("${quartz.autoStartup:false}")
    private boolean autoStartup;

    @Value("${quartz.tax.autoStartup:false}")
    private boolean taxAutoStartup;

    @Value("${spring.redis.ssl:false}")
    private boolean ssl;

    /**
     * SpringBoot 1.x 用法
     */
    /*@Bean
    public CacheManager redisCacheManager(RedisTemplate<?, ?> redisTemplate) {
        CacheManager cacheManager = new RedisCacheManager(redisTemplate);
        return cacheManager;
    }*/

    /**
     * SpringBoot 2.x 用法
     */
    @Bean
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofSeconds(3600));
        return RedisCacheManager.builder(RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory))
                .cacheDefaults(redisCacheConfiguration).build();
    }

    @Bean
    @ConditionalOnProperty(prefix = "caidao.redis", name = "template", havingValue = "true")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        /*RedisTemplate<String, String> redisTemplate = new RedisTemplate<String, String>();
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setConnectionFactory(factory);
        return redisTemplate;*/

        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 配置连接工厂
        template.setConnectionFactory(redisConnectionFactory);

        //使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值（默认使用JDK的序列化方式）
        Jackson2JsonRedisSerializer jacksonSeial = new Jackson2JsonRedisSerializer(Object.class);

        ObjectMapper om = new ObjectMapper();
        // 指定要序列化的域，field,get和set,以及修饰符范围，ANY是都有包括private和public
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        // 修复报java.lang.ClassCastException: java.util.LinkedHashMap cannot be cast to XXX
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
        om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        jacksonSeial.setObjectMapper(om);

        // 值采用json序列化
        template.setValueSerializer(jacksonSeial);
        //使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());

        // 设置hash key 和value序列化模式
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(jacksonSeial);
        template.afterPropertiesSet();

        return template;
    }

    @Bean
    public IRedisManager redisManager() {
        if (StringUtils.isNotEmpty(master)) {
            RedisSentinelManager redisManager = new RedisSentinelManager();
            redisManager.setHost(nodes);
            redisManager.setDatabase(database);
            redisManager.setMasterName(master);
            if (StringUtils.isNotEmpty(password)) {
                redisManager.setPassword(password);
            }
            redisManager.setTimeout(timeout);
            return redisManager;
        } else {
            RedisManager redisManager = new RedisManager();
            redisManager.setJedisPool((JedisPool) jedisPool());
            return redisManager;
        }
    }

    @Bean
    public Pool<Jedis> jedisPool() {
        if (StringUtils.isNotEmpty(master)) {
            Set<String> nodeSet = new HashSet<>();
            //获取到节点信息
            String[] nodeArray = nodes.split(",");
            //循环注入至Set中
            for (String node : nodeArray) {
                nodeSet.add(node);
            }
            //创建连接池对象
            JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
            jedisPoolConfig.setMaxIdle(maxIdle);
            jedisPoolConfig.setMaxTotal(maxActive);
            jedisPoolConfig.setMinIdle(minIdle);
            jedisPoolConfig.setMaxWaitMillis(maxWait);
            if (StringUtils.isNotEmpty(password)) {
                return new JedisSentinelPool(master, nodeSet, jedisPoolConfig, timeout, password, database);
            } else {
                return new JedisSentinelPool(master, nodeSet, jedisPoolConfig, timeout, null, database);
            }
        } else {
            JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
            jedisPoolConfig.setMaxIdle(maxIdle);
            jedisPoolConfig.setMaxTotal(maxActive);
            jedisPoolConfig.setMinIdle(minIdle);
            jedisPoolConfig.setMaxWaitMillis(maxWait);
            if (StringUtils.isNotEmpty(password)) {
                if (ssl) {
                    return new JedisPool(jedisPoolConfig, host, port, timeout, password, database, ssl);
                } else {
                    return new JedisPool(jedisPoolConfig, host, port, timeout, password, database);
                }
            } else {
                if (ssl) {
                    return new JedisPool(jedisPoolConfig, host, port, timeout, null, database, ssl);
                } else {
                    return new JedisPool(jedisPoolConfig, host, port, timeout, null, database);
                }
            }
        }
    }

    @Bean
    public MessageListener integrateMessageListener() {
        return new IntegrateMessageListener();
    }

    @Bean
    public MessageListenerAdapter integrateMessageListenerAdapter() {
        return new MessageListenerAdapter(integrateMessageListener());
    }


    @Bean
    public MessageListener smsMessageListener() {
        return new MessageListenerAdapter(new SmsMessageListener());
    }

    @Bean
    public MessageListenerAdapter smsMessageListenerAdapter() {
        return new MessageListenerAdapter(smsMessageListener());
    }

    @Bean
    public MessageListener emailMessageListener() {
        return new EmailListerner();
    }

    @Bean
    public MessageListenerAdapter emailMessageListenerAdapter() {
        return new MessageListenerAdapter(emailMessageListener());
    }



    @Bean
    public RedisMessageListenerContainer topicContainer(RedisConnectionFactory factory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(factory);
        ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
        threadPoolTaskScheduler.initialize();
        threadPoolTaskScheduler.setPoolSize(10);
        container.setTaskExecutor(threadPoolTaskScheduler);
        if(autoStartup) {
            container.addMessageListener(integrateMessageListenerAdapter(), new ChannelTopic("integrate:topic"));
            container.addMessageListener(smsMessageListenerAdapter(), new ChannelTopic("sms:topic"));
            container.addMessageListener(emailMessageListenerAdapter(), new ChannelTopic("email:topic"));
        }
        return container;
    }

}

