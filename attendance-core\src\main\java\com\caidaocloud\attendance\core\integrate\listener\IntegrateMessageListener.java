package com.caidaocloud.attendance.core.integrate.listener;

import com.caidaocloud.imports.service.application.interceptor.listener.RedisMessage;
import com.caidaocloud.imports.service.application.interceptor.IntegrateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Created by darren on 16/10/26.
 */
public class IntegrateMessageListener implements MessageListener {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IntegrateService integrateService;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        byte[] body = message.getBody();
        Object msg = redisTemplate.getValueSerializer().deserialize(body);

//        RedisMessage redisMessage = new RedisMessage();
//        Map<String, Object> params = new HashedMap();
//        params.put("id", redisMessage.getId());
        RedisMessage redisMessage = (RedisMessage) msg;
//        redisMessage.setParams(params);

        integrateService.handleOutputMessage(redisMessage);
    }
}