package com.caidaocloud.attendance.service.application.enums;

/**
 * 考勤自定义逻辑配置-所属业务枚举
 */
public enum CustomLogicBelongBusinessEnum {
    LEAVE("LEAVE", "休假"),
    OVERTIME("OVERTIME", "加班");

    private String code;
    private String name;

    CustomLogicBelongBusinessEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CustomLogicBelongBusinessEnum getByCode(String code) {
        for (CustomLogicBelongBusinessEnum c : CustomLogicBelongBusinessEnum.values()) {
            if (c.getCode().equals(code)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
