package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量考勤异常调整
 *
 * <AUTHOR>
 * @Date 2024/6/24
 */
@Data
public class BatchAnalyseResultAdjustDto {
    @ApiModelProperty("考勤周期ID")
    private Integer waSobId;

    @ApiModelProperty("员工ID")
    private Long empid;

    @ApiModelProperty("考勤调整明细")
    private List<AnalyseResultAdjustDto> adjustDetailList;

    @ApiModelProperty("附件地址")
    private String filePath;

    @ApiModelProperty("附件名称")
    private String fileName;
}
