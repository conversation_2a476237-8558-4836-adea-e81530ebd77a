package com.caidao1.ext.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.caidao1.commons.BaseConst;
import com.caidao1.ext.common.DynamicCommon;
import com.caidao1.ext.entity.SysDynamicColumns;
import com.caidao1.ext.mapper.SysDynamicColumnsMapper;
import com.caidao1.ext.service.ISysDynamicColumnsService;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.payroll.mybatis.mapper.FuncParamMapper;
import com.caidao1.payroll.mybatis.model.FuncParam;
import com.caidao1.payroll.mybatis.model.FuncParamExample;
import com.caidao1.wa.mybatis.mapper.WaLeaveTypeMapper;
import com.caidao1.wa.mybatis.mapper.WaShiftDefMapper;
import com.caidao1.wa.mybatis.model.WaLeaveType;
import com.caidao1.wa.mybatis.model.WaLeaveTypeExample;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaShiftDefExample;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-12
 */
@Service
@Primary
public class SysDynamicColumnsServiceImpl extends ServiceImpl<SysDynamicColumnsMapper, SysDynamicColumns> implements ISysDynamicColumnsService {

    @Autowired
    private FuncParamMapper funcParamMapper;
    @Autowired
    private WaLeaveTypeMapper leaveTypeMapper;

    @Autowired
    private WaShiftDefMapper waShiftDefMapper;

    @Override
    public List<SysDynamicColumns> findDynamicColumns(String belongid, String tableRegId) {
        QueryWrapper queryWrapper = new QueryWrapper<SysDynamicColumns>();
        queryWrapper.eq("belong_Org_Id", belongid);
        queryWrapper.eq("status", 1);
        queryWrapper.eq("table_reg_id", tableRegId);
        queryWrapper.orderByAsc("sortno");
        return this.list(queryWrapper);
    }

    @Override
    public List<SysDynamicColumns> findDynamicColumnsByTable(String tableRegId, String... belongid) {
        QueryWrapper queryWrapper = new QueryWrapper<SysDynamicColumns>();
        queryWrapper.in("belong_Org_Id", belongid);
        queryWrapper.eq("status", 1);
        queryWrapper.eq("table_reg_id", tableRegId);
        queryWrapper.orderByAsc("sortno,dynamic_Columns_Id ");
        return this.list(queryWrapper);
    }

    @Override
    public Map getFuncParam2List(String belongId, String tabelRegId) {
        FuncParamExample example = new FuncParamExample();
        example.createCriteria().andBelongOrgIdIn(Arrays.asList("0", belongId)).andFormKindIn(Arrays.asList(tabelRegId));
        example.setOrderByClause("param_type,func_param_id");
        List<FuncParam> paramList = funcParamMapper.selectByExample(example);
        Map<String, List<Map>> result = new LinkedHashMap<String, List<Map>>();
        List<Map> sdlist = new ArrayList<Map>();

        for (FuncParam funcParam : paramList) {
            Map map = new HashMap();
            map.put("text", funcParam.getParamName());
            map.put("exp", funcParam.getParamExp().replaceAll("@PARAMID", funcParam.getFuncParamId().toString()));
            if (result.containsKey(funcParam.getParamClassName())) {
                List<Map> list = result.get(funcParam.getParamClassName());
                list.add(map);
            } else {
                List<Map> list = new ArrayList<Map>();
                list.add(map);
                result.put(funcParam.getParamClassName(), list);
            }
        }

        //考勤相关字段
        List<Map> walist = result.get("考勤项目");
        if (walist == null) {
            walist = new ArrayList<Map>();
        }

        WaLeaveTypeExample leaveTypeExample = new WaLeaveTypeExample();
        leaveTypeExample.createCriteria().andBelongOrgidEqualTo(belongId);
        leaveTypeExample.setOrderByClause("leave_type_id");
        List<WaLeaveType> leaveTypeList = leaveTypeMapper.selectByExample(leaveTypeExample);
        if (CollectionUtils.isNotEmpty(leaveTypeList)) {
            for (WaLeaveType leaveType : leaveTypeList) {
                Map map = new HashMap();
                map.put("text", leaveType.getLeaveName());
                map.put("exp", "lt_" + leaveType.getLeaveTypeId() + "_key"); // 请假时长变量
                walist.add(map);
            }
        }
        List<Map> finalWalist = walist;
        BaseConst.WA_OT_COMPENSATE.forEach((key, value) -> {
            Map map = new HashMap();
            map.put("text", value);
            map.put("exp", "ot_" + key + "_key"); // 加班时长变量
            finalWalist.add(map);
        });

        result.put("考勤项目", finalWalist);


        WaShiftDefExample waexample = new WaShiftDefExample();
        waexample.createCriteria().andBelongOrgidEqualTo(belongId);
        waexample.setOrderByClause(" shift_Def_Id");
        List<Map> shiftlist = new ArrayList<>();
        List<WaShiftDef> shiftDefs = waShiftDefMapper.selectByExample(waexample);
        shiftDefs.forEach(shiftDef -> {
            Map map = new HashMap();
            map.put("text", shiftDef.getShiftDefName());
            map.put("exp", DynamicCommon.VAR_SHFIT_PRE_ + shiftDef.getShiftDefId()); // 班次变量
            shiftlist.add(map);
        });

        result.put("班次", shiftlist);


//        WaGroupExample wagroupExp = new WaGroupExample();
//        wagroupExp.createCriteria().andBelongOrgidEqualTo(belongId);
//        wagroupExp.setOrderByClause("wa_Group_Id");
//        List<Map> wagroups = new ArrayList<>();
//        List<WaGroup> waGroups =  waGroupMapper.selectByExample(wagroupExp);
//        waGroups.forEach(waGroup -> {
//            Map map = new HashMap();
//            map.put("text", waGroup.getWaGroupName().concat("-暂不支持"));
//            map.put("exp", DynamicCommon.VAR_WAGROUP_PRE_+ waGroup.getWaGroupId()); // 分组变量
//            wagroups.add(map);
//        });
//        result.put("考勤分组", wagroups);


        //自定义字段
//        SysColCustomExample colCustomExample = new SysColCustomExample();
//        colCustomExample.createCriteria().andBelongOrgIdEqualTo(belongId).andStatusEqualTo(1).andColTypeIn(Arrays.asList("input", "select", "calendar"));
//        List<SysColCustom> colCustomList = colCustomMapper.selectByExample(colCustomExample);
//        for (SysColCustom colCustom : colCustomList) {
//            Map map = new HashMap();
//            map.put("text", colCustom.getChnName());
//            if (colCustom.getTypeId() == null) {
//                if ("calendar".equals(colCustom.getColType())) {
//                    map.put("exp", "payEngineService.getExtColumnDateByEmpId(empid,\"" + colCustom.getTableRegId() + "_" + colCustom.getColCustomId() + "\")");
//                } else {
//                    map.put("exp", "payEngineService.getExtColumnByEmpId(empid,\"" + colCustom.getTableRegId() + "_" + colCustom.getColCustomId() + "\")");
//                }
//            } else {
//                map.put("exp", "payEngineService.getExtColumnByEmpId(empid,\"" + colCustom.getTableRegId() + "_" + colCustom.getColCustomId() + "_" + colCustom.getTypeId() + "\")");
//            }
//            sdlist.add(map);
//        }

//        if (sdlist.size() > 0) {
//            result.put("其他", sdlist);
//        }

        return result;
    }

    @Override
    public List<SysDynamicColumns> getDynamicFieldList(String pageIds) {
        List<SysDynamicColumns> result = new ArrayList<>();
        if (StringUtils.isBlank(pageIds)) {
            return result;
        }

        String[] ids = pageIds.split(",");
        for (String pId : ids) {
            result.addAll(findDynamicColumnsByTable(pId, SessionHolder.getBelongOrgId()));
        }

        return result;
    }

    @Override
    public List<String> getCusFieldListByPageIds(String pageIds) {
        // 处理动态列
        List<SysDynamicColumns> cols = getDynamicFieldList(pageIds);
        if (null == cols || cols.isEmpty()) {
            return null;
        }
        List<String> r2cList = new ArrayList<String>();
        for (SysDynamicColumns sdc : cols) {
            if (null == sdc || StringUtils.isBlank(sdc.getCustomFieldConfig())) {
                continue;
            }

            r2cList.add(sdc.getCustomFieldConfig());
        }
        return r2cList.isEmpty() ? null : r2cList;
    }
}
