<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidao1.mobile.mybatis.mapper.MobileV18EmployeeMapper">

    <select id="searchSysColAuthConfig" parameterType="map" resultType="map">
        SELECT sfr.field_code AS "fieldCode",sfr.is_ajax AS "isAjax",
          sfr.data_resource AS "dataResource",sfr.col_type AS "colType",sfc.is_must AS "isMust",
           CASE WHEN sfr.sys_lang_id IS NULL THEN sfr.chn_name ELSE sl.${lang} END AS "fieldLabel",
           sfc.is_modify AS "isModify",FALSE AS "isExtCol",data_reg AS "dataReg",data_format AS "dataFormat"
            FROM sys_field_reg sfr LEFT JOIN sys_field_config sfc
                ON sfr.field_reg_id = sfc.field_reg_id
                JOIN sys_table_reg str ON str.table_reg_id = sfr.table_reg_id
                  LEFT JOIN sys_lang sl ON sl.sys_lang_id = sfr.sys_lang_id
             WHERE str.table_name = #{empTable}
                    <if test="corpId != null">
                        AND sfc.corpid = #{corpId}
                    </if>
                    AND is_app_show = TRUE ORDER BY sort
    </select>

    <select id="searchAllFieldConfig" parameterType="map" resultType="map">
        SELECT sfr.field_code AS "fieldCode",CASE WHEN sfr.is_ajax NOTNULL THEN sfr.is_ajax ELSE FALSE END AS "isAjax",
        sfr.data_resource AS "dataResource",sfr.col_type AS "colType",sfc.is_must AS "isMust",
        CASE WHEN sfr.sys_lang_id IS NULL THEN sfr.chn_name ELSE sl.zh END AS "zh",
        CASE WHEN sfr.sys_lang_id IS NULL THEN sfr.chn_name ELSE sl.en END AS "en",
        CASE WHEN sfr.sys_lang_id IS NULL THEN sfr.chn_name ELSE sl.ja END AS "ja",
        CASE WHEN sfr.sys_lang_id IS NULL THEN sfr.chn_name ELSE sl.ko END AS "ko",
        sfc.is_modify AS "isModify",FALSE AS "isExtCol",data_reg AS "dataReg",data_format AS "dataFormat",
        sfc.corpid AS "belongOrgId",sfc.is_upload_file AS "isUploadFile",sfc.sort as "fsort"
        FROM sys_field_config sfc LEFT JOIN sys_field_reg sfr
        ON sfr.field_reg_id = sfc.field_reg_id
        JOIN sys_table_reg str ON str.table_reg_id = sfr.table_reg_id
        LEFT JOIN sys_lang sl ON sl.sys_lang_id = sfr.sys_lang_id
        WHERE str.table_name = #{empTable} and sfr.field_type = 'sys'
          <if test="corpId != null">
              AND sfc.corpid = #{corpId}
          </if>
        AND is_app_show = TRUE ORDER BY sort
    </select>

    <select id="searchSysCompletionInfo" parameterType="map" resultType="map">
        select table_name AS "tableName",
            array_to_string(Array(
                select sfr.field_code || ' is null' from
                  sys_field_reg sfr JOIN sys_field_config sfc ON sfr.field_reg_id = sfc.field_reg_id
                  WHERE sfr.is_config = TRUE AND sfc.is_must = TRUE AND sfc.is_show = TRUE
                  AND sfc.is_modify = TRUE AND sfr.table_reg_id = str.table_reg_id AND sfc.corpid = #{corpId}
            ),',') AS "fieldCode",(SELECT field_code FROM sys_field_reg WHERE table_reg_id = str.table_reg_id AND is_pk = TRUE limit 1) AS "pkCode",
          array_to_string(array(
            select 'EXT_COL_' || col_custom_id from sys_col_custom WHERE is_show = TRUE AND status = 1 AND is_modify = TRUE
            AND belong_org_id = #{belongOrgId,jdbcType=VARCHAR} AND table_reg_id = str.table_reg_id
          ),',') AS "extColCustom"
        from sys_table_reg str WHERE table_reg_id in (${tableRegId}) AND is_custom = TRUE
    </select>

    <select id="searchColCustomAuthConfig" parameterType="map" resultType="map">
        <!--SELECT 'EXT_COL_' || scc.col_custom_id AS "fieldCode",CASE scc.col_type WHEN 'select' then TRUE else FALSE END AS "isAjax",scc.belong_org_id AS "belongOrgId",-->
              <!--CASE scc.col_type WHEN 'select' then 'mobilePersonnel/getParmDictByTypeCode?typeCode=' || (select type_code from sys_parm_type WHERE type_id = scc.type_id) else '' END AS "dataResource",-->
              <!--scc.col_type AS "colType",scc.is_require AS "isMust",scc.chn_name AS "fieldLabel",scc.is_modify AS "isModify",TRUE AS "isExtCol",'' AS "dataReg",data_format AS "dataFormat",scc.col_sort as "fsort"-->
        <!--FROM sys_col_custom scc JOIN sys_table_reg str ON scc.table_reg_id = str.table_reg_id-->
        <!--WHERE str.table_name = #{empTable}-->
              <!--AND scc.status = 1 AND scc.is_show = TRUE-->
              <!--<if test="belongOrgId != null">-->
                  <!--AND scc.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}-->
              <!--</if>-->
        <!--ORDER BY scc.col_sort-->


        SELECT 'EXT_COL_' || scc.col_custom_id AS "fieldCode",CASE scc.col_type WHEN 'select' then TRUE else FALSE END AS "isAjax",scc.belong_org_id AS "belongOrgId",
        CASE scc.col_type WHEN 'select' then 'mobilePersonnel/getParmDictByTypeCode?typeCode=' || (select type_code from sys_parm_type WHERE type_id = scc.type_id) else '' END AS "dataResource",
        scc.col_type AS "colType",sfc.is_must AS "isMust",scc.chn_name AS "fieldLabel",sfc.is_modify AS "isModify",TRUE AS "isExtCol",'' AS "dataReg",scc.data_format AS "dataFormat",sfc.sort as "fsort"
        FROM sys_col_custom scc JOIN sys_table_reg str ON scc.table_reg_id = str.table_reg_id
        join sys_field_reg sfr on sfr.field_code = 'EXT_COL_' || scc.col_custom_id
        join sys_field_config sfc on sfc.field_reg_id = sfr.field_reg_id
        WHERE str.table_name = #{empTable}
        AND scc.status = 1 AND sfc.is_app_show = TRUE
        <if test="belongOrgId != null">
            AND scc.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        </if>

        ORDER BY sfc.sort
    </select>

    <select id="findEmpInfo" parameterType="map" resultType="map">
        select ext_custom_col AS "extCustomCol"
        <if test="pk != null">
            ,${pk}
        </if>
        <if test="colConfig != null">
            <foreach collection="colConfig" separator="," item="col" open=",">
                ${col.fieldCode}
            </foreach>
        </if>
        from ${empTable} WHERE empid = #{empid}
        <if test="empTable == 'sys_enter_training' || empTable == 'sys_emp_rewards'
                        || empTable == 'sys_emp_social' || empTable == 'emp_bank_info'">
            AND valid = 'Y'
        </if>
        <if test="pkId != null">
            AND ${pk} = #{pkId}
        </if>
    </select>
    <select id="findEmpPrivacyInfo" parameterType="map" resultType="map">
     select ext_custom_col AS "extCustomCol"
        <if test="pk != null">
            ,${pk}
        </if>
        <if test="colConfig != null">
            <foreach collection="colConfig" separator="," item="col" open=",">
                ${col.fieldCode}
            </foreach>
        </if>
         from (
            select b.*,a.gender,a.hire_date,a.mobile,a.email,a.emp_name,a.workno,a.used_name,a.eng_name
            from sys_emp_info a
            left join sys_emp_privacy b on a.empid = b.empid
        ) as t where empid = #{empid}
        <if test="pkId != null">
            AND ${pk} = #{pkId}
        </if>
    </select>

    <select id="getCountryNameById" parameterType="java.lang.Integer" resultType="java.lang.String">
        select chn_name
         from sys_unit_country WHERE country_id = #{countryId}
    </select>

    <select id="getTableExtColInfo" parameterType="map" resultType="java.lang.Integer">
        select col_custom_id AS "colCustomId"
          from sys_col_custom
          scc WHERE belong_org_id = #{belongOrgId,jdbcType=VARCHAR} AND table_reg_id = #{tableRegId}
    </select>

    <select id="getExtColInfo" parameterType="map" resultType="map">
        select ext_custom_col AS "extCustomCol" from ${tableName} WHERE ${pk} = #{pkId}
    </select>

    <select id="getSysColInfo" parameterType="map" resultType="java.lang.String">
        select scr.field_code AS "fieldCode"
          from sys_field_reg scr JOIN sys_field_config sfc ON sfc.field_reg_id = scr.field_reg_id
           WHERE scr.table_reg_id = #{tableRegId} AND sfc.corpid = #{corpId}
                  AND sfc.is_show = TRUE AND sfc.is_modify = TRUE
    </select>

    <select id="selectFieldLabelByTableName" parameterType="map" resultType="map">
        (select sfr.field_code AS "fieldCode",CASE WHEN sl.sys_lang_id IS NULL THEN field_name else sl.${lang} END AS "fieldLabel",sfr.col_type AS "colType",sfr.data_format AS "dataFormat"
          from sys_field_reg sfr
          JOIN sys_table_reg str ON sfr.table_reg_id = str.table_reg_id
          JOIN sys_field_config sfc ON sfr.field_reg_id = sfc.field_reg_id
          LEFT JOIN sys_lang sl ON sl.sys_lang_id = sfr.sys_lang_id
          WHERE str.table_name = #{tableName} AND sfc.is_app_show = TRUE and sfc.is_modify = TRUE
            AND sfc.corpid = #{corpId})
        UNION ALL
          select 'EXT_COL_' || scc.col_custom_id AS "fieldCode",scc.chn_name AS "fieldLabel",scc.col_type AS "colType",scc.data_format AS "dataFormat" from sys_col_custom scc
            JOIN sys_table_reg str ON str.table_reg_id = scc.table_reg_id
            WHERE str.table_name = #{tableName} AND scc.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
            AND scc.is_modify = TRUE AND scc.is_show = TRUE
    </select>

    <select id="selectEmployeeInfo" parameterType="map" resultType="map">
        <foreach collection="fieldList" item="item" separator="UNION ALL">
            select ${item.code} AS "fieldText",'input' AS "colType",'${item.showCode}' AS "fieldCode",t."fieldLabel" AS
            "fieldLabel",${item.isMust} AS "isMust",t."dataReg" AS "dataReg",
            t."colType" AS "colType",${item.isModify} AS "isModify"
            from sys_emp_info sei LEFT JOIN sys_emp_privacy sep ON sei.empid = sep.empid
            LEFT JOIN sys_corp_org sco ON sco.orgid = sei.orgid LEFT JOIN
            sys_org_position sop ON sop.post_id = sei.post_id LEFT JOIN
            (select field_code AS "fieldCode",CASE WHEN sl.sys_lang_id IS NULL THEN field_name else sl.${lang} END AS "fieldLabel", sfr.col_type AS "colType", sfr.data_reg AS
            "dataReg"
            from sys_field_reg sfr LEFT JOIN sys_lang sl ON sl.sys_lang_id = sfr.sys_lang_id
            WHERE table_reg_id IN (#{item.tableRegId})) t ON t."fieldCode" = #{item.codeValue}
            WHERE sei.empid = #{empid}
        </foreach>
    </select>

    <select id="selectDelBeforeInfo" parameterType="map" resultType="map">
        select ext_custom_col
              <foreach collection="fieldInfo" item="item" separator="," open=",">
                ${item.fieldCode}
              </foreach>
          from ${tableName} WHERE ${pk} = #{pkId}
    </select>

    <select id="searchTableField" parameterType="map" resultType="map">
        select field_code AS "fieldCode", chn_name AS "chnName"
        from sys_field_reg sfr join sys_table_reg str ON str.table_reg_id = sfr.table_reg_id
          where str.table_name = #{tableName} AND  str.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
    </select>

    <!--新增获取外键值对应的查询语句-->
    <select id="searchForeignList" parameterType="map" resultType = "map">
    select
        so.shortname  AS  "shortName",
        sei.emp_name   AS  "leaderEmpName",
        sp.chn_name   AS  "positionName",
        org.shortname    as "orgName",
        ei.isexsit_account  AS "isexsitAccount",
        ei.stats,
        ses.social_type AS "socialType",
        ses.insurance_type AS "insuranceType",
        ses.is_pay_social AS "isPaySocial",
        ses.hf_type AS "HfType",
        ses.is_pay_fund AS "isPayFund",
        si.store_name  as "storeName",
        ei.worktime_type as "worktimeType",
      (select string_agg(co.addr,',') from sys_corp_office co where co.site_id::VARCHAR = ANY (string_to_array(ei.siteids,',')))  as "siteids",
      (select string_agg (("costItems" ->> 'cost_name') || ':' || ( "costItems" ->> 'proportion_scale' ), ',' ) from ( select  jsonb_array_elements(esi.cost_items) as "costItems" from sys_emp_info esi where esi.empid = ei.empid and esi.cost_items is not null) t) as "costItems",
      privacy.tax_province as "taxProvince",
      privacy.tax_city as "taxCity",
      ses.social_province as "socialProvince",
      ses.social_city as "socialCity"
      from sys_emp_info ei
        left join sys_corp_org so on ei.belong_org_id = so.orgid
        left join sys_emp_info sei on sei.empid = ei.leader_empid and sei.deleted = 0
        left join sys_org_position sp on sp.post_id = ei.post_id
        left join sys_store_info si on ei.store_id = si.store_id
        left join sys_corp_org org on ei.orgid = org.orgid
        left join sys_emp_social ses on ses.empid = ei.empid
        left join sys_emp_privacy privacy on privacy.empid = ei.empid
        where ei.stats = 0 and ei.empid = #{empid} and ei.deleted = 0
    </select>
</mapper>