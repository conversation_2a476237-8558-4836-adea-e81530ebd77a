package com.caidaocloud.attendance.service.application.service.workflow;

import com.baomidou.mybatisplus.core.assist.ISqlRunner;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.workflow.dto.WfAfterHandleDto;
import com.caidaocloud.workflow.service.IProcessHandleService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class WorkflowProcessHandleServiceImpl implements IProcessHandleService {
    @Autowired
    private ISqlRunner sqlRunner;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void postHandleOfBeginProc(WfAfterHandleDto wfAfterHandleDto) {
        if (wfAfterHandleDto == null || StringUtils.isBlank(wfAfterHandleDto.getBusinessKey())) {
            log.info("parameters were illegal");
            return;
        }
        var split = wfAfterHandleDto.getBusinessKey().split("_");
        var businessId = split[0];
        var funCode = split[1];
        var businessCodeEnum = BusinessCodeEnum.getEnumByFunCode(funCode);
        if (businessCodeEnum == null) {
            log.info("businessCodeEnum was illegal, businessId={} funCode={}", businessId, funCode);
            return;
        }
        if (StringUtils.isBlank(businessCodeEnum.table) || StringUtils.isBlank(businessCodeEnum.bidField)) {
            log.info("businessCodeEnum table was empty, businessCodeEnum={} businessId={} funCode={}", businessCodeEnum, businessId, funCode);
            return;
        }
        sqlRunner.update(String.format("update %s set process_code = '%s' where %s = %s",
                businessCodeEnum.table, wfAfterHandleDto.getProcessCode(), businessCodeEnum.bidField, Long.valueOf(businessId)));
    }
}