package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.sdk.feign.fallback.AttWfFeignClientFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 假勤-业务流程接口
 *
 * <AUTHOR>
 * @Date 2024/8/5
 */
@FeignClient(
        value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = AttWfFeignClientFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "attWfFeignClient"
)
public interface AttWfFeignClient {
    /**
     * 查看流程详情
     *
     * @param businessKey
     * @return
     * @throws Exception
     */
    @GetMapping(value = "/api/attendance/wf/v1/getWfDetail")
    Result<WfResponseDto> getWfDetail(@RequestParam("businessKey") String businessKey) throws Exception;
}
