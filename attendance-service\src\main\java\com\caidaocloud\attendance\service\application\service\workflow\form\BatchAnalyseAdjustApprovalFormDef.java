package com.caidaocloud.attendance.service.application.service.workflow.form;

import com.caidaocloud.workflow.annotation.WfMetaFunFormDef;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.googlecode.totallylazy.Lists;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * 批量考勤异常申请
 */
public class BatchAnalyseAdjustApprovalFormDef extends WfMetaFunFormDef {
    @NotNull
    @Override
    public List<WfMetaFunFormFieldDto> formList() {
        return Lists.list(
                new WfMetaFunFormFieldDto("name", "姓名", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("workno", "工号", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("org", "组织", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("post", "岗位", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("job", "职务", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("jobGrade", "职级", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("waMonth", "考勤月", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("batchAnalyseAdjustDetail", "异常申请明细", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("file", "附件", WfFieldDataTypeEnum.Text));
    }
}
