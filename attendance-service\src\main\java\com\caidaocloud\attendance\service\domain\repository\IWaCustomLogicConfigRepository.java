package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaCustomLogicConfigDo;

/**
 * 考勤自定义逻辑配置表
 *
 * <AUTHOR>
 * @Date 2024/7/23
 */
public interface IWaCustomLogicConfigRepository {

    void updateById(WaCustomLogicConfigDo logicConfig);

    void insert(WaCustomLogicConfigDo logicConfig);

    WaCustomLogicConfigDo getById(Long configId);

    void deleteById(Long configId);

    WaCustomLogicConfigDo getByCode(String tenantId, String belongBusiness, String code);
}
