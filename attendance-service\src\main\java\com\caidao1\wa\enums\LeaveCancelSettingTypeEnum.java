package com.caidao1.wa.enums;

public enum LeaveCancelSettingTypeEnum {

    AUTOMATIC(1, "自动"),
    MANUAL(2, "手动");

    private Integer index;
    private String name;


    LeaveCancelSettingTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (LeaveCancelSettingTypeEnum c : LeaveCancelSettingTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
