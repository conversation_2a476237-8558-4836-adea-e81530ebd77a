package com.caidaocloud.attendance.core.system.service;

import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.VelocityEngineUtils;
import com.caidao1.ioc.util.IocFileReader;
import com.caidao1.ioc.util.IocReaderFactory;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.mapper.SysLangMapper;
import com.caidao1.system.mybatis.model.SysLang;
import com.caidao1.system.mybatis.model.SysLangExample;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.app.VelocityEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class LangService {

    private static final Logger LOG = LoggerFactory.getLogger(LangService.class);

    @Autowired
    private SysLangMapper sysLangMapper;

    @Autowired
    private VelocityEngine velocityEngine;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private MessageResource messageResource;

    @Value("${caidao.langfile.path:}")
    private String langFilePath;

    @Value("${caidao.langfile.write:true}")
    private Boolean writeLangFile;

    public String getEmpLang(Long empid) {
        String lang = (String) redisTemplate.opsForValue().get(RedisKeyDefine.LANG + empid);
        if (StringUtils.isEmpty(lang)) {
            lang = "zh";
        }
        return lang;
    }

    public String getLangText(String lang, String code) {
        String key = RedisKeyDefine.LANG + code;
        if (lang.equals("en")) {
            key += "_" + lang.toUpperCase();
        }
        String text = (String) redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(text)) {
            return text;
        }
        return code;
    }

    public void setLangText(List<Map> rows, String lang, String code) {
        for (Map row : rows) {
            String key = RedisKeyDefine.LANG + row.get(code);
            if (lang.equals("en")) {
                key += "_" + lang.toUpperCase();
            }
            String text = (String) redisTemplate.opsForValue().get(key);
            if (StringUtils.isNotEmpty(text)) {
                row.put(code, text);
            }
        }
    }

    @Transactional
    public void initLang(String rtnFileName) throws Exception {
        if (StringUtils.isEmpty(rtnFileName)) {
            rtnFileName = "/Users/<USER>/Documents/才到工作/产品设计/caidao 国际语言包2016-20160330.xlsx";
        }
        String fileType = rtnFileName.substring(rtnFileName.lastIndexOf(".") + 1, rtnFileName.length());
        IocFileReader reader = IocReaderFactory.getReader(fileType, new FileInputStream(rtnFileName));
        if (reader != null) {
            List<String> title = reader.getTitle();
            List<List<String>> rows = reader.getRows();
            for (List<String> row : rows) {
                String zh = row.get(0);
                String en = row.get(1);
                String code = null;
                if (row.size() > 2) {
                    code = row.get(2);
                }
                SysLang sysLang = new SysLang();
                if (row.size() > 2) {
                    sysLang.setCode(code);
                }
                sysLang.setZh(zh);
                sysLang.setEn(en);
                addLang(sysLang);
            }
        }
    }

    /**
     * 查询多语言
     *
     * @param pageBean
     * @return
     */
    public List<Map> getLangList(PageBean pageBean) {
        Map params = new HashMap();
        params.put("filter", pageBean.getFilter());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "code.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        return sysLangMapper.getLangList(pageBounds, params);
    }

    @Transactional
    public void saveLangRow(SysLang lang) {
        sysLangMapper.updateByPrimaryKeySelective(lang);
    }

    @Transactional
    public void addLang(SysLang lang) {
        SysLangExample example = new SysLangExample();
        example.createCriteria().andZhEqualTo(lang.getZh());
        if (sysLangMapper.countByExample(example) == 0) {
            System.out.println(lang.getZh());
            example = new SysLangExample();
            example.createCriteria();
            example.setOrderByClause("code desc");
            if (StringUtils.isEmpty(lang.getCode())) {
                List<SysLang> langs = sysLangMapper.selectByExample(example);
                lang.setCode(getLangCode(langs.get(0).getCode()));
            }
            sysLangMapper.insertSelective(lang);
        }
    }

    /**
     * 自动生成编码
     *
     * @return
     */
    public static String getLangCode(String max) {
        return "L" + StringUtils.leftPad((Integer.parseInt(max.substring(1)) + 1) + "", 6, "0");
    }

    /**
     * 刷新多语言
     *
     * @throws Exception
     */
    public void refreshLangJs() throws Exception {
        writeLangFile();
        messageResource.reload();
    }

    private void writeLangFile() throws Exception{
        if(null != writeLangFile && !writeLangFile){
            return;
        }

        Map<String, Object> params = new HashMap<String, Object>();
        SysLangExample example = new SysLangExample();
        example.setOrderByClause("code");
        List<SysLang> langList = sysLangMapper.selectByExample(example);
        params.put("LangList", langList);

        String sw = VelocityEngineUtils.mergeTemplateIntoString(this.velocityEngine, "vm/zh.vm", "UTF-8", params);

        File file = getLangFile("zh_CN.js");
        writeLangFile(file, sw, "zh_CN");

        for (String lang : new String[] { "en", "ja", "ko" }) {
            sw = VelocityEngineUtils.mergeTemplateIntoString(this.velocityEngine, "vm/" + lang + ".vm", "UTF-8", params);
            file = getLangFile(lang + ".js");
            writeLangFile(file, sw, lang);
        }
    }

    private File getLangFile(String fileName) throws Exception{
        File file = null;
        if(null != langFilePath && !"".equals(langFilePath)){
            String filePath = langFilePath + fileName;
            file = new File(filePath);
            if(!file.exists()){
                LOG.info("file.exists(), create mkdirs, filePath={}", filePath);
                Files.createDirectories(file.toPath());
                file.createNewFile();
            }
        } else {
            ClassPathResource classPathResource = new ClassPathResource("static/assets/js/lang/" + fileName);
            file = classPathResource.getFile();
        }
        return file;
    }

    private void writeLangFile(File file, String sw, String lang) throws Exception {
        BufferedReader reader = null;
        Writer writer = null;
        LOG.info("---start write {} lang file---", lang);
        try {
            reader = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(sw.getBytes("UTF-8")), "UTF-8"));
            writer = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");

            String line;
            while ((line = reader.readLine()) != null) {
                writer.write(line, 0, line.length());
                writer.flush();
            }
        }finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                }
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                }
            }
        }
        LOG.info("---write {} lang file end---", lang);
    }
}
