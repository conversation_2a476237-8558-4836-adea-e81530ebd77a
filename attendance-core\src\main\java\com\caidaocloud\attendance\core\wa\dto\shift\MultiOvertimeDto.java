package com.caidaocloud.attendance.core.wa.dto.shift;

import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("班次设置-多段加班时间设置信息DTO")
public class MultiOvertimeDto {
    @ApiModelProperty("开始时间(单位分钟),eg:780")
    private Integer overtimeStartTime;
    @ApiModelProperty("结束时间(单位分钟),eg:1110")
    private Integer overtimeEndTime;
    @ApiModelProperty("开始时间归属标记: 1 当日、2 次日")
    private Integer overtimeStartTimeBelong;
    @ApiModelProperty("结束时间归属标记: 1 当日、2 次日")
    private Integer overtimeEndTimeBelong;

    public boolean checkCrossNight() {
        if (ShiftTimeBelongTypeEnum.TODAY.getIndex().equals(overtimeStartTimeBelong)
                && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(overtimeEndTimeBelong)) {
            return Boolean.TRUE;
        }
        return overtimeStartTime > overtimeEndTime;
    }

    public Integer doGetRealOvertimeStartTime() {
        if (null == overtimeStartTime) {
            return 0;
        }
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.overtimeStartTimeBelong)) {
            return overtimeStartTime + 1440;
        }
        return overtimeStartTime;
    }

    public Integer doGetRealOvertimeEndTime() {
        if (null == overtimeEndTime) {
            return 0;
        }
        if (null != this.overtimeEndTimeBelong) {
            return ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.overtimeEndTimeBelong)
                    ? overtimeEndTime + 1440
                    : overtimeEndTime;
        }
        // 兼容假勤模块的一段班设置数据
        return overtimeStartTime > overtimeEndTime
                ? overtimeEndTime + 1440
                : overtimeEndTime;
    }
}
