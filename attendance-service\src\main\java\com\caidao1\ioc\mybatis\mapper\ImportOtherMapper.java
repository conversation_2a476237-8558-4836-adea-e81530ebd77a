package com.caidao1.ioc.mybatis.mapper;

import com.caidao1.ioc.dto.DingTalkUserIdDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.9527
 * User: zz.H
 * Date: 2021/3/24
 */
public interface ImportOtherMapper {

    //获取当前租户的对应模版

    //清除当前租户的对应模版
    int delTemplate(@Param("templateId")Integer templateId);

    //获取后台配置模版信息
    List<String> getTableId(@Param("templateId") Integer templateId);

    List<String> getModelList(@Param("tableRegId")String tableRegId);

    String getPathUrlFileName(@Param("id") Long id,@Param("belongOrgId")String belongOrgId);

    Integer getFuncIdCheck(@Param("templateId") Integer templateId);

    List<String> getDetailNameList(@Param("belongOrgId") String belongOrgId);

    List<DingTalkUserIdDto> getEmpDingTalkInfoList(Map params);
}
