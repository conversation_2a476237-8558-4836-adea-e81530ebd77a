package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaWorkRoundDo;
import com.caidaocloud.attendance.service.interfaces.dto.WorkRoundDto;
import com.caidaocloud.attendance.service.interfaces.dto.WorkRoundGridDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/4
 */
public interface IWorkRoundService {
    AttendancePageResult<WorkRoundGridDto> getWorkRoundPageList(PageBean pageBean);

    WorkRoundDto getRounfShiftInfoByWorkRoundId(String belongId, Integer workRoundId);

    Integer saveOrUpdateWorkRoundShift(WorkRoundDto workRoundDto) throws Exception;

    List<String> getWorktimeNameList(String belongOrgId, Integer id);

    Boolean checkWorkRoundIsUsed(Integer workRoundId);

    Integer deleteWorkRound(Integer id);

    List<WaWorkRoundDo> getWorkRoundListByShiftId(Integer shiftId);
}
