package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.domain.repository.IWaBatchOvertimeRepository;
import com.caidaocloud.dto.UserInfo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Data
@Slf4j
@Service
public class WaBatchOvertimeDo {
    private Long batchId;

    private Long empid;

    private Integer waSobId;

    private Long startTime;

    private Long endTime;

    private String filePath;

    private String fileName;

    private String timeSlot;

    private Float timeDuration;

    private Float validTimeDuration;

    private String businessKey;

    private String reason;

    private String extCustomCol;

    private Integer status;

    private Long lastApprovalTime;

    private Long lastEmpid;

    private String revokeReason;

    private Integer revokeStatus;

    private String tenantId;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    private String processCode;

    @Autowired
    private IWaBatchOvertimeRepository waBatchOvertimeRepository;

    public WaBatchOvertimeDo getById(Long batchId) {
        return waBatchOvertimeRepository.getById(batchId);
    }

    public void updateById(WaBatchOvertimeDo updateData) {
        waBatchOvertimeRepository.updateById(updateData);
    }

    public void save(WaBatchOvertimeDo addData) {
        waBatchOvertimeRepository.insert(addData);
    }

    public void deleteById(Long batchId) {
        waBatchOvertimeRepository.deleteById(batchId);
    }

    public PageList<Map> getPageList(MyPageBounds myPageBounds, Map params) {
        return waBatchOvertimeRepository.selectPageList(myPageBounds, params);
    }

    public List<Map> getOtStatisticList(String tenantId, Long empid, Long startTime, Long endTime,
                                                     List<Integer> statusList) {
        return waBatchOvertimeRepository.selectListGroupByCompensateType(tenantId, empid, startTime, endTime,
                statusList);
    }

    public void doInitCrtField() {
        UserInfo userInfo = UserContext.preCheckUser();
        this.setTenantId(userInfo.getTenantId());
        this.setDeleted(0);
        this.setCreateBy(userInfo.getUserId());
        this.setCreateTime(System.currentTimeMillis());
        this.setUpdateBy(this.getCreateBy());
        this.setUpdateTime(this.getCreateTime());
    }
}