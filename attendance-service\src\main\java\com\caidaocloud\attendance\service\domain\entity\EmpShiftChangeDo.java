package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IEmpShiftChangeRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftChangePo;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: Aaron.Chen
 * @Date: 2021/12/1 15:51
 * @Description:
 **/
@Slf4j
@Data
@Service
public class EmpShiftChangeDo {

    private Integer recId;
    private String belongOrgId;
    private Long empid;
    private Long workDate;
    private Integer oldShiftDefId;
    private Integer newShiftDefId;
    private String remark;
    private Integer status;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;

    @Autowired
    private IEmpShiftChangeRepository empShiftChangeRepository;

    public List<EmpShiftChangeDo> getEmpShiftChanges(String tenantId, List<Long> empIds, Long startDate, Long endDate, Integer status) {
        List<WaEmpShiftChangePo> list = empShiftChangeRepository.getEmpShiftChanges(tenantId, empIds, startDate, endDate, status);
        return ObjectConverter.convertList(list, EmpShiftChangeDo.class);
    }

    public void saveEmpShiftChanges(List<EmpShiftChangeDo> changes) {
        if (CollectionUtils.isNotEmpty(changes)) {
            empShiftChangeRepository.saveEmpShiftChanges(ObjectConverter.convertList(changes, WaEmpShiftChangePo.class));
        }
    }

    public void updateEmpShiftChanges(List<EmpShiftChangeDo> changes) {
        if (CollectionUtils.isNotEmpty(changes)) {
            empShiftChangeRepository.updateEmpShiftChanges(ObjectConverter.convertList(changes, WaEmpShiftChangePo.class));
        }
    }
}
