package com.caidaocloud.attendance.core.commons.utils;

import com.caidao1.report.dto.ExportFieldDto;
import com.caidao1.report.dto.PageBean;
import com.caidao1.report.dto.SumExportFieldDto;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/*
 * 单独实现一个 ReportUtil，解决 GridUtil 布局错乱问题
 */
public class ReportUtil {
    public static Map covertList2GridJson(ExportFieldDto exportFieldDto, PageBean pageBean) {
        List<Map> header = new ArrayList();

        String width;
        for(int i = 0; i < exportFieldDto.getFields().size(); ++i) {
            String field = (String)exportFieldDto.getFields().get(i);
            String title = (String)exportFieldDto.getTitles().get(i);
            Map col = new HashMap();
            if ("#cspan".equals(title)) {
                int cspan = ((Map)header.get(header.size() - 1)).get("cspan") != null ? (Integer)((Map)header.get(header.size() - 1)).get("cspan") + 1 : 2;
                ((Map)header.get(header.size() - 1)).put("cspan", cspan);
                ((Map)header.get(header.size() - 1)).put("align", "center");
                ((Map)header.get(header.size() - 1)).put("filter", "");
                ((Map)header.get(header.size() - 1)).put("sort", "");
                ((Map)header.get(header.size() - 1)).put("id", "");
            } else {
                col.put("id", field);
                col.put("value", title);
                if (CollectionUtils.isNotEmpty(exportFieldDto.getSorts()) && exportFieldDto.getSorts().size() > i) {
                    col.put("sort", exportFieldDto.getSorts().get(i));
                } else {
                    col.put("sort", true);
                }

                if (CollectionUtils.isNotEmpty(exportFieldDto.getEditTypes()) && exportFieldDto.getEditTypes().size() > i) {
                    col.put("editType", exportFieldDto.getEditTypes().get(i));
                }

                if (CollectionUtils.isNotEmpty(exportFieldDto.getTypes()) && exportFieldDto.getTypes().size() > i) {
                    col.put("type", exportFieldDto.getTypes().get(i));
                }

                if (CollectionUtils.isNotEmpty(exportFieldDto.getWidths()) && exportFieldDto.getWidths().size() > i) {
                    width = (String)exportFieldDto.getWidths().get(i);
                    col.put("width", StringUtils.isNotEmpty(width) ? width : title.length() * 20 + "px");
                } else {
                    col.put("width", title.length() * 22 + "px");
                }

                if (CollectionUtils.isNotEmpty(exportFieldDto.getFilters()) && exportFieldDto.getFilters().size() > i) {
                    col.put("filter", exportFieldDto.getFilters().get(i));
                }

                header.add(col);
            }
        }

        List<Map> attachHeader = new ArrayList();
        if (CollectionUtils.isNotEmpty(exportFieldDto.getAttachHeader())) {
            int rsIdx = 0;
            for(int i = 0; i < exportFieldDto.getFields().size(); ++i) {
                width = (String)exportFieldDto.getFields().get(i);
                String title = (String)exportFieldDto.getAttachHeader().get(i);
                Map col = new HashMap();
                if ("#rspan".equals(title)) {
                    int rspan = ((Map)header.get(rsIdx)).get("rspan") != null ? (Integer)((Map)header.get(rsIdx)).get("rspan") + 1 : 2;
                    ((Map)header.get(rsIdx)).put("rspan", rspan);
                    ((Map)header.get(rsIdx)).put("valign", "middle");
                    ++rsIdx;
                } else {
                    col.put("id", width);
                    col.put("value", title);
                    if (CollectionUtils.isNotEmpty(exportFieldDto.getFilters()) && exportFieldDto.getFilters().size() > i) {
                        if(null != exportFieldDto.getFilters().get(i)){
                            ++rsIdx;
                            col.put("filter", exportFieldDto.getFilters().get(i));
                        }else {
                            Map filter = new HashMap();
                            filter.put("type", "int");
                            col.put("filter", filter);
                        }
                    }

                    attachHeader.add(col);
                }
            }
        }

        Map result = covertList2GridJson(exportFieldDto.getRows(), header, pageBean, attachHeader);
        if (exportFieldDto.getTotal() > 0) {
            result.put("total_count", exportFieldDto.getTotal());
        }

        if (exportFieldDto instanceof SumExportFieldDto && ((SumExportFieldDto)exportFieldDto).getSumRow() != null) {
            result.put("sumRow", ((SumExportFieldDto)exportFieldDto).getSumRow());
        }

        return result;
    }

    public static Map covertList2GridJson(List<Map> list, List<Map> header, PageBean pageBean, List<Map> attachHeader) {
        Map gridMap = new HashMap();
        List<Map> gridList = new ArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator var6 = list.iterator();

            while(var6.hasNext()) {
                Map row = (Map)var6.next();
                gridList.add(row);
            }

            gridMap.put("rows", gridList);
            if (pageBean != null) {
                if (list instanceof PageList) {
                    PageList<Map> pageList = (PageList)list;
                    gridMap.put("total_count", pageList.getPaginator().getTotalCount());
                }

                gridMap.put("pos", pageBean.getPosStart());
            } else {
                gridMap.put("total_count", gridList.size());
            }
        } else {
            if (pageBean != null) {
                gridMap.put("pos", pageBean.getPosStart());
            }

            gridMap.put("total_count", 0);
            gridMap.put("rows", gridList);
        }

        if (pageBean != null) {
            if (pageBean.getPosStart() == 0 && StringUtils.isEmpty(pageBean.getFilter())) {
                gridMap.put("head", header);
                if (CollectionUtils.isNotEmpty(attachHeader)) {
                    gridMap.put("attachHead", attachHeader);
                }
            }
        } else {
            gridMap.put("head", header);
            if (CollectionUtils.isNotEmpty(attachHeader)) {
                gridMap.put("attachHead", attachHeader);
            }
        }

        return gridMap;
    }
}
