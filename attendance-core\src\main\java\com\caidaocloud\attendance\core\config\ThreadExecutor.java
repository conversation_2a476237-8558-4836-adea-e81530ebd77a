package com.caidaocloud.attendance.core.config;

import com.caidaocloud.excption.ServerException;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 自定义线程池
 *
 * <AUTHOR>
 * @date 2023/1/3
 **/
@Slf4j
@Component
public class ThreadExecutor {

    private int corePoolSize;

    private int maxPoolSize;

    private static ThreadPoolExecutor threadPool;

    @PostConstruct
    public void initThreadPool() {
        int processors = Runtime.getRuntime().availableProcessors();
        generateThreadPoolExecutor(processors, processors * 2);
    }


    public void generateThreadPoolExecutor(Integer corePoolSize, Integer maxPoolSize) {
        synchronized (ThreadExecutor.class) {
            if (threadPool == null) {
                this.corePoolSize = corePoolSize;
                this.maxPoolSize = maxPoolSize;
                init();
            }
        }
    }

    public static ThreadPoolExecutor getThreadPool() {
        if (threadPool == null) {
            throw new ServerException("the thread pool waas not initialized");
        }
        return threadPool;
    }

    private void init() {
        if (maxPoolSize < corePoolSize) {
            corePoolSize = maxPoolSize / 2;
        }

        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("ThreadPoolExector-%d").build();

        if (threadPool == null) {
            threadPool = new ThreadPoolExecutor(corePoolSize, maxPoolSize, 60, TimeUnit.SECONDS,
                    new LinkedBlockingQueue(1000), threadFactory, new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        }

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (threadPool != null && !threadPool.isShutdown()) {
                threadPool.shutdown();
                if (log.isDebugEnabled()) {
                    log.debug("[ThreadPoolExector] thread pool is shutdown now");
                }
            }
        }));
        log.info("init Thread Pool success");
    }

}
