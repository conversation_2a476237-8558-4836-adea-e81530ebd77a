package com.caidao1.integrate.entity.TieTong;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.integrate.entity.dto.DoorData;
import com.caidao1.integrate.entity.dto.DoorDetail;
import com.caidao1.integrate.entity.dto.DoorResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class DoorSourceInput {
    public List<DoorDetail> getDoorSourceResult(String startTime, String endTime, String filterWorkNo, String accessDateDuration,String excludedAddress) throws Exception {
        List<String> filterWorkNoList = new ArrayList<>();
        if (StringUtils.isNotBlank(filterWorkNo)) {
            filterWorkNoList.addAll(Arrays.stream(filterWorkNo.split(",")).distinct().collect(Collectors.toList()));
        }
        List<String> excludedAddressList = new ArrayList<>();
        if (StringUtils.isNotBlank(excludedAddress)) {
            excludedAddressList.addAll(Arrays.stream(excludedAddress.split(",")).distinct().collect(Collectors.toList()));
        }
        Long day = -1L;
        if (StringUtils.isNotBlank(accessDateDuration)) {
            day = Long.valueOf(accessDateDuration);
        }
        log.info("getDoorSourceResult filterWorkNoList:{}", filterWorkNoList);
        if (startTime == null && endTime == null) {
            Date date = new Date();
            com.caidaocloud.util.DateUtil.addDays(date, day);
            startTime = DateUtil.parseDateToPattern(date, "yyyy-MM-dd");
            endTime = DateUtil.parseDateToPattern(new Date(), "yyyy-MM-dd");
        }
        List<DoorDetail> doorDetailList = new ArrayList<>();
        ArtemisConfig artemisConfig = new ArtemisConfig("************:10443", "20045157", "JCHv7p2QyHzP28UGqQ3J");
        final String getSecurityApi = "/artemis/api/acs/v2/door/events";
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getSecurityApi);
            }
        };
        Map<String, Object> params = new HashMap<>();

        params.put("pageNo", "1");
        params.put("pageSize", "50");
        params.put("startTime", IsoTimeUtil.timeToIso(startTime + " 00:00:00"));
        params.put("endTime", IsoTimeUtil.timeToIso(endTime + " 23:59:59"));

        log.info("TieTongGetDoorSourceResult oneParam:{}", params);

        Constants.DEFAULT_TIMEOUT = 10000;
        String result = doPostStringArtemis(artemisConfig, path, JSON.toJSONString(params), null, null, "application/json");
        log.info("queryTieTongDoorResult:{}", result);
        DoorResultDto doorResultDto = JSON.parseObject(result, DoorResultDto.class);
        if ("0".equals(doorResultDto.getCode()) && "success".equals(doorResultDto.getMsg())) {
            DoorData data = doorResultDto.getData();
            List<DoorDetail> list = data.getList();
            list = filerRecord(filterWorkNoList, excludedAddressList, list);
            doorDetailList.addAll(list);
            Integer pageNo = data.getPageNo();
            while (++pageNo <= data.getTotalPage()) {
                log.info("queryTieTongDoorDataPageNo:{}", pageNo);
                params.put("pageNo", pageNo);
                DoorResultDto doorResultDtoNextPage = JSON.parseObject
                        (doPostStringArtemis(artemisConfig, path, JSON.toJSONString(params)
                                , null, null, "application/json"), DoorResultDto.class);
                if ("0".equals(doorResultDtoNextPage.getCode()) && "success".equals(doorResultDtoNextPage.getMsg())) {
                    List<DoorDetail> filterBefore = doorResultDtoNextPage.getData().getList().stream().filter(doorDetail -> doorDetail.getJobNo() != null).collect(Collectors.toList());
                    filterBefore = filerRecord(filterWorkNoList, excludedAddressList, filterBefore);
                    if(CollectionUtils.isNotEmpty(filterBefore)){
                        doorDetailList.addAll(filterBefore);
                    }
                } else {
                    break;
                }
            }
        }
        return doorDetailList;
    }

    private List<DoorDetail> filerRecord(List<String> filterWorkNoList, List<String> excludedAddressList, List<DoorDetail> filterBefore) {
        if (CollectionUtils.isNotEmpty(filterWorkNoList)) {
            filterBefore = filterBefore.stream().filter(t -> {return filterWorkNoList.contains(t.getJobNo());}).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(excludedAddressList)) {
            filterBefore = filterBefore.stream().filter(t -> {return excludedAddressList.contains(t.getDoorName()) ? false : true;}).collect(Collectors.toList());
        }
        return filterBefore;
    }

    public static String doPostStringArtemis(ArtemisConfig artemisConfig, Map<String, String> path, String body, Map<String, String> querys, String accept, String contentType) throws Exception {
        String httpSchema = (String) path.keySet().toArray()[0];
        if (httpSchema != null && !StringUtils.isEmpty(httpSchema)) {
            Map<String, String> headers = new HashMap();
            if (StringUtils.isNotBlank(accept)) {
                headers.put("Accept", accept);
            } else {
                headers.put("Accept", "*/*");
            }

            if (StringUtils.isNotBlank(contentType)) {
                headers.put("Content-Type", contentType);
            } else {
                headers.put("Content-Type", "application/text;charset=UTF-8");
            }
            Request request = new Request(Method.POST_STRING, httpSchema + artemisConfig.getHost(), (String) path.get(httpSchema), artemisConfig.getAppKey(), artemisConfig.getAppSecret(), Constants.DEFAULT_TIMEOUT);
            request.setHeaders(headers);
            request.setQuerys(querys);
            request.setStringBody(body);
            Response response = Client.execute(request);
            return getResponseResult(response);
        } else {
            throw new RuntimeException("http和https参数错误httpSchema: " + httpSchema);
        }
    }

    private static String getResponseResult(Response response) {
        String responseStr = null;
        int statusCode = response.getStatusCode();
        if (!String.valueOf(statusCode).startsWith("2") && !String.valueOf(statusCode).startsWith("3")) {
            responseStr = response.getBody();
        } else {
            responseStr = response.getBody();
        }
        return responseStr;
    }
}
