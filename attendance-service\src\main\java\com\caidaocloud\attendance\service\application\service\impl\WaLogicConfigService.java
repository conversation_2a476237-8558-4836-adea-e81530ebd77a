package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.attendance.service.domain.entity.WaLogicConfigDo;
import com.caidaocloud.attendance.service.domain.service.WaLogicConfigDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 考勤逻辑配置
 *
 * <AUTHOR>
 * @Date 2024/2/4
 */
@Slf4j
@Service
public class WaLogicConfigService {
    @Autowired
    private WaLogicConfigDomainService waLogicConfigDomainService;

    public List<WaLogicConfigDo> getListByCodes(String tenantId, List<String> logicCodes) {
        return waLogicConfigDomainService.getListByCodes(tenantId, logicCodes);
    }
}
