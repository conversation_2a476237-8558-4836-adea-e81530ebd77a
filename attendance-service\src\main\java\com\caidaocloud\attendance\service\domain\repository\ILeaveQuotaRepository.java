package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaLeaveQuotaDo;
import com.caidaocloud.attendance.service.interfaces.dto.QuotaEmpGroupDto;
import com.caidaocloud.dto.UserInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/17
 */
public interface ILeaveQuotaRepository {
    List<WaLeaveQuotaDo> getLeaveQuotaList(String belongOrgId, Integer quotaSettingId);

    int save(UserInfo userInfo, List<QuotaEmpGroupDto> empGroupList, Integer leaveType, Integer quotaSettingId);

    int delete(String belongOrgid, Integer quotaSettingId);
}
