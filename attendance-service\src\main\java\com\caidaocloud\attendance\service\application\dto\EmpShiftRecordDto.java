package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2022/3/31 21:03
 * @Description:
 **/
@Data
@NoArgsConstructor
public class EmpShiftRecordDto {
    @ApiModelProperty("当天排班信息")
    private List<EmpShiftBaseInfoForPortalDto> shifts;
    @ApiModelProperty("班次时段")
    private String shiftTimePeriod;
    @ApiModelProperty("班次名称")
    private String shiftName;
    @Deprecated
    @ApiModelProperty("上班时间（废弃）")
    private Integer startTime;
    @Deprecated
    @ApiModelProperty("下班时间（废弃）")
    private Integer endTime;
    @ApiModelProperty("打卡记录时间")
    private List<Long> records = new ArrayList<>();
    @ApiModelProperty("补卡条数每次")
    private Integer supplementNumber;
}
