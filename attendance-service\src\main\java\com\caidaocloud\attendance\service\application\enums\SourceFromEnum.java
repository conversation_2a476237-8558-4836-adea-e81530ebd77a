package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

public enum SourceFromEnum {
    ATTENDANCE(0, "考勤", AttendanceCodes.ATTENDANCE),
    WE(1, "企业微信", AttendanceCodes.WE),
    FS(2, "飞书", AttendanceCodes.FS),
    DD(3, "钉钉", AttendanceCodes.DD),
    MACHINE(4, "考勤机", AttendanceCodes.MACHINE),
    FACE(5, "人脸考勤", AttendanceCodes.FACE),
    SCANHOUR(6, "工时扫码", AttendanceCodes.SCANHOUR);
    private Integer index;
    private Integer code;
    private String name;

    SourceFromEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(int index) {
        for (SourceFromEnum c : SourceFromEnum.values()) {
            if (c.getIndex() == index) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return c.name;
            }
        }
        return "";
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
