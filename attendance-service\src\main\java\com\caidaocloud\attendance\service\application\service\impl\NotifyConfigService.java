package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.mns.common.utils.Lists;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.cron.XxlJobService;
import com.caidaocloud.attendance.service.application.dto.NotifyConfigItemDto;
import com.caidaocloud.attendance.service.application.dto.xxljob.XxlJobInfo;
import com.caidaocloud.attendance.service.application.dto.xxljob.XxlJobParams;
import com.caidaocloud.attendance.service.application.enums.NotifyConfigTypeEnum;
import com.caidaocloud.attendance.service.application.service.INotifyConfigService;
import com.caidaocloud.attendance.service.domain.entity.NotifyConfigDo;
import com.caidaocloud.attendance.service.interfaces.dto.NotifyConfigDto;
import com.caidaocloud.attendance.service.interfaces.vo.AbnormalTimeDto;
import com.caidaocloud.attendance.service.interfaces.vo.AttendanceDetailTimeDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2021/9/16
 */
@Slf4j
@Service
public class NotifyConfigService implements INotifyConfigService {

    @Resource
    private NotifyConfigDo notifyConfigDo;
    @Autowired
    private XxlJobService xxlJobService;
    @Autowired
    private ISessionService sessionService;

    private final static String WEEKLY_REPORT_XXL_JOB_NAME = "考勤周报定时任务";
    private final static String WEEKLY_REPORT_HANDLER = "weeklyClockJobHandler";

    private final static String ATTENDANCE_ABNORMAL_XXL_JOB_NAME = "考勤异常汇总提醒定时任务";
    private final static String ATTENDANCE_ABNORMAL_HANDLER = "attendanceAbnormalJobHandler";

    private final static String ATTENDANCE_DETAIL_XXL_JOB_NAME = "考勤明细提醒定时任务";
    private final static String ATTENDANCE_DETAIL_HANDLER = "attendanceDetailJobHandler";

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    @Override
    public NotifyConfigDto getNotifyConfig(String belongId) {
        NotifyConfigDo configDo = notifyConfigDo.getNotifyConfigById(belongId);
        if (null == configDo){
            return new NotifyConfigDto();
        }
        NotifyConfigDto dto = ObjectConverter.convert(configDo, NotifyConfigDto.class);
        if (StringUtil.isNotBlank(configDo.getAbnormalTime())) {
            dto.setAbnormalTime(JSON.parseArray(configDo.getAbnormalTime(), AbnormalTimeDto.class));
        } else {
            dto.setAbnormalTime(new ArrayList<>());
        }
        if (StringUtil.isNotBlank(configDo.getAttendanceDetailTime())) {
            dto.setAttendanceDetailTime(JSON.parseArray(configDo.getAttendanceDetailTime(), AttendanceDetailTimeDto.class));
        } else {
            dto.setAttendanceDetailTime(new ArrayList<>());
        }
        return dto;
    }

    @Override
    public int saveNotifyConfig(NotifyConfigDto dto, UserInfo userInfo) {
        long curTime = System.currentTimeMillis() / 1000;
        Long userId = userInfo.getUserId();
        NotifyConfigDo modifyConfigDo = notifyConfigDo.getNotifyConfigById(userInfo.getTenantId());
        NotifyConfigDo configDo = ObjectConverter.convert(dto, NotifyConfigDo.class);
        if (CollectionUtils.isNotEmpty(dto.getAbnormalTime())) {
            configDo.setAbnormalTime(JSONUtils.ObjectToJson(dto.getAbnormalTime()));
        } else {
            configDo.setAbnormalTime(JSONUtils.ObjectToJson(new ArrayList<>()));
        }
        if (CollectionUtils.isNotEmpty(dto.getAttendanceDetailTime())) {
            configDo.setAttendanceDetailTime(JSONUtils.ObjectToJson(dto.getAttendanceDetailTime()));
        } else {
            configDo.setAttendanceDetailTime(JSONUtils.ObjectToJson(new ArrayList<>()));
        }
        String tenantId = userInfo.getTenantId();
        if (modifyConfigDo == null) {
            //新增
            configDo.setCreateTime(curTime);
            configDo.setUpdateTime(curTime);
            configDo.setCreateBy(userId);
            configDo.setUpdateBy(userId);
            configDo.setTenantId(userInfo.getTenantId());
            configDo.setDeleted(0);
            updateXxlJobTask(configDo);
            configDo.setNotifyConfigId(snowflakeUtil.createId());
            if (CollectionUtils.isNotEmpty(dto.getAbnormalTime())) {
                saveAbnormalXxlJobTask(tenantId, dto);
            }
            if (CollectionUtils.isNotEmpty(dto.getAttendanceDetailTime())) {
                saveAttendanceDetailXxlJobTask(tenantId, dto);
            }
            return notifyConfigDo.save(configDo);
        } else {
            //修改
            configDo.setNotifyConfigId(modifyConfigDo.getNotifyConfigId());
            configDo.setTenantId(userInfo.getTenantId());
            configDo.setDeleted(0);
            configDo.setUpdateTime(curTime);
            configDo.setUpdateBy(userId);
            if (checkChanged(modifyConfigDo, configDo)) {
                updateXxlJobTask(configDo);
            }
            if (CollectionUtils.isNotEmpty(dto.getAbnormalTime())) {
                updateAbnormalXxlJobTask(tenantId, dto, modifyConfigDo);
            } else {
                deleteAbnormalXxlJobTask(tenantId, modifyConfigDo.getAbnormalTime());
            }
            if (CollectionUtils.isNotEmpty(dto.getAttendanceDetailTime())) {
                updateAttendanceDetailXxlJobTask(tenantId, dto, modifyConfigDo);
            } else {
                deleteAttendanceDetailXxlJobTask(tenantId, modifyConfigDo.getAttendanceDetailTime());
            }
            return notifyConfigDo.update(configDo, userInfo.getTenantId());
        }
    }

    /**
     * 删除xxl-job任务
     * @param tenantId 租户
     * @param abnormalTime 配置参数
     */
    private void deleteAbnormalXxlJobTask(String tenantId, String abnormalTime) {
        if (null != abnormalTime && !"[]".equals(abnormalTime)) {
            List<AbnormalTimeDto> list = JSON.parseArray(abnormalTime, AbnormalTimeDto.class);
            deleteAbnormalXxlJobTask(tenantId, list);
        }
    }

    /**
     * 删除xxl-job任务
     * @param tenantId 租户
     * @param list 配置参数
     */
    private void deleteAbnormalXxlJobTask(String tenantId, List<AbnormalTimeDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (AbnormalTimeDto abnormalTime : list) {
            try {
                Integer notifyType = abnormalTime.getType();
                Integer notifyDay = abnormalTime.getDay();
                Integer notifyTime = abnormalTime.getTime();
                XxlJobParams xxlJobParams = new XxlJobParams();
                xxlJobParams.setBelongOrgId(tenantId);
                String jobDesc = String.format("%s_%s_%s_%s", ATTENDANCE_ABNORMAL_XXL_JOB_NAME, notifyType, notifyDay, notifyTime);
                xxlJobService.deleteAttendanceXxlJob(tenantId, jobDesc, ATTENDANCE_ABNORMAL_HANDLER);
            } catch (Exception e) {
                log.error("deleteAbnormalXxlJobTask failed:{}", e.getMessage(), e);
            }
        }
    }

    /**
     * 更新xxl-job任务
     * @param tenantId 租户
     * @param dto 配置参数
     */
    private void updateAbnormalXxlJobTask(String tenantId, NotifyConfigDto dto, NotifyConfigDo modifyConfig) {
        deleteAbnormalXxlJobTask(tenantId, modifyConfig.getAbnormalTime());
        saveAbnormalXxlJobTask(tenantId, dto);
    }

    /**
     * 保存xxl-job任务
     * @param tenantId 租户
     * @param dto 配置参数
     */
    private void saveAbnormalXxlJobTask(String tenantId, NotifyConfigDto dto) {
        if (CollectionUtils.isEmpty(dto.getAbnormalTime())) {
            return;
        }
        saveAttendanceAbnormalJob(tenantId, dto.getAbnormalSwitch(), dto.getAbnormalTime());
    }

    /**
     * 保存考勤异常汇总定时任务
     * @param tenantId 租户
     * @param abnormalSwitch 开关
     * @param abnormalTimes 配置
     */
    public void saveAttendanceAbnormalJob(String tenantId, Integer abnormalSwitch, List<AbnormalTimeDto> abnormalTimes) {
        if (null == abnormalTimes || abnormalTimes.size() <= 0) {
            return;
        }
        for (AbnormalTimeDto abnormalTime : abnormalTimes) {
            Integer notifyType = abnormalTime.getType();
            Integer notifyDay = abnormalTime.getDay();
            Integer notifyTime = abnormalTime.getTime();
            if (notifyType == null || notifyDay == null || notifyTime == null) {
                continue;
            }
            int hour = notifyTime / 60;
            int minute = notifyTime % 60;
            int day = notifyDay;
            String cron = "";
            if (notifyType == 1) {
                int curMonthMaxDay = DateUtil.getMonthActualMax(DateUtil.getOnlyDate() * 1000);
                if (day > curMonthMaxDay) {
                    cron = String.format("0 %s %s L * ?", minute, hour);
                } else {
                    cron = String.format("0 %s %s %s * ?", minute, hour, day);
                }
            } else {
                cron = String.format("0 %s %s ? * %s", minute, hour, notifyDay);
            }
            try {
                XxlJobParams xxlJobParams = new XxlJobParams();
                xxlJobParams.setBelongOrgId(tenantId);
                boolean autoStart = abnormalSwitch == 1;
                String jobDesc = String.format("%s_%s_%s_%s", ATTENDANCE_ABNORMAL_XXL_JOB_NAME, notifyType, notifyDay, notifyTime);
                xxlJobService.addOrUpdateAttendanceXxlJob(jobDesc, cron, ATTENDANCE_ABNORMAL_HANDLER, xxlJobParams, autoStart);
            } catch (Exception e) {
                log.error("saveAbnormalXxlJobTask failed:{}", e.getMessage(), e);
            }
        }
    }

    private boolean checkChanged(NotifyConfigDo oldConfig, NotifyConfigDo newConfig) {
        if (null == newConfig.getWeeklyNotifyFrequency() || null == newConfig.getWeeklyNotifyTime()) {
            return false;
        }
        return !newConfig.getWeeklyNotifyFrequency().equals(oldConfig.getWeeklyNotifyFrequency()) || !newConfig.getWeeklyNotifyTime().equals(oldConfig.getWeeklyNotifyTime());
    }

    @Override
    public List<NotifyConfigDo> getNotifyPageList(int pageNo, int PageSize) {
        return notifyConfigDo.getNotifyPageList(pageNo, PageSize).getItems();
    }

    @Override
    public List<NotifyConfigItemDto> getNotifyConfigs(String tenantId) {
        List<NotifyConfigItemDto> list = new ArrayList<>();
        NotifyConfigDo config = notifyConfigDo.getNotifyConfigById(tenantId);
        if (null == config) {
            UserInfo userInfo = sessionService.getUserInfo();
            NotifyConfigDto newConfig = getNotifyConfigDo();
            saveNotifyConfig(newConfig, userInfo);
            config = notifyConfigDo.getNotifyConfigById(tenantId);
        }
        list.add(NotifyConfigItemDto.getNotifyConfigItemDto(NotifyConfigTypeEnum.CLOCK, config));
        list.add(NotifyConfigItemDto.getNotifyConfigItemDto(NotifyConfigTypeEnum.DAILY_REPORT, config));
        list.add(NotifyConfigItemDto.getNotifyConfigItemDto(NotifyConfigTypeEnum.WEEKLY_REPORT, config));
        list.add(NotifyConfigItemDto.getNotifyConfigItemDto(NotifyConfigTypeEnum.LEAVE_CANCEL, config));
        list.add(NotifyConfigItemDto.getNotifyConfigItemDto(NotifyConfigTypeEnum.ABNORMAL_SUMMARY, config));
        list.add(NotifyConfigItemDto.getNotifyConfigItemDto(NotifyConfigTypeEnum.ATTENDANCE_DETAIL_PUSH, config));
        return list;
    }

    private NotifyConfigDto getNotifyConfigDo() {
        String configJsonStr = "{\"clockNotifySwitch\":0,\"workTime\":0,\"workNotifyContent\":\"\",\"offWorkTime\":0,\"offWorkNotifyContent\":\"\",\"dailyNotifySwitch\":0,\"weeklyNotifySwitch\":0,\"leaveCancelSwitch\":0,\"dailyNotifyTime\":0,\"dailyNotifyRule\":0,\"dailyNotifyAbnormal\":0,\"weeklyNotifyFrequency\":2,\"weeklyNotifyTime\":0,\"weeklyNotifyType\":2,\"abnormalSwitch\":0,\"abnormalType\":10,\"abnormalTime\":[{\"type\":1,\"day\":1,\"time\":540}],\"abnormalMsg\":\"\",\"attendanceDetailSwitch\":0,\"attendanceDetailTime\":[]}";
        return JSON.parseObject(configJsonStr, NotifyConfigDto.class);
    }

    @Override
    public String updateNotifyConfigStatus(Integer num, Integer status, UserInfo userInfo) {
        if (null == num) {
            log.error("params is empty");
            return ResponseWrap.wrapResult(AttendanceCodes.NOTIFY_CONFIG_EMPTY, null).getMsg();
        }
        long curTime = DateUtil.getCurrentTime(true);
        String tenantId = userInfo.getTenantId();
        Long userId = Long.valueOf(userInfo.getUserid());
        NotifyConfigDo modifyConfig = notifyConfigDo.getNotifyConfigById(tenantId);
        if (null == modifyConfig) {
            log.error("config is not exist");
            return ResponseWrap.wrapResult(AttendanceCodes.NOTIFY_CONFIG_NOT_EXIST, null).getMsg();
        }
        modifyConfig.setNotifyConfigId(modifyConfig.getNotifyConfigId());
        modifyConfig.setTenantId(tenantId);
        modifyConfig.setDeleted(0);
        modifyConfig.setUpdateTime(curTime);
        modifyConfig.setUpdateBy(userId);
        NotifyConfigTypeEnum notifyConfigType = NotifyConfigTypeEnum.getNotifyConfigTypeByIndex(num);
        if (null == notifyConfigType) {
            log.error("notify config type is not exist");
            return ResponseWrap.wrapResult(AttendanceCodes.NOTIFY_CONFIG_TYPE_NOT_EXIST, null).getMsg();
        }
        boolean weeklyReportSet = true;
        boolean abnormalSummarySet = true;
        boolean detailSet = true;
        switch (notifyConfigType) {
            case CLOCK:
                modifyConfig.setClockNotifySwitch(status);
                break;
            case DAILY_REPORT:
                modifyConfig.setDailyNotifySwitch(status);
                break;
            case WEEKLY_REPORT:
                modifyConfig.setWeeklyNotifySwitch(status);
                weeklyReportSet = updateXxlJobTaskStatus(modifyConfig);
                break;
            case LEAVE_CANCEL:
                modifyConfig.setLeaveCancelSwitch(status);
                break;
            case ABNORMAL_SUMMARY:
                modifyConfig.setAbnormalSwitch(status);
                abnormalSummarySet = updateAbnormalXxlJobTaskStatus(modifyConfig);
                break;
            case ATTENDANCE_DETAIL_PUSH:
                modifyConfig.setAttendanceDetailSwitch(status);
                detailSet = updateAttendanceDetailXxlJobTaskStatus(modifyConfig);
                break;
        }
        if (!weeklyReportSet) {
            return ResponseWrap.wrapResult(AttendanceCodes.WEEKLY_NOTIFY_CONFIG_JOB_NOT_SET, null).getMsg();
        }
        if (!abnormalSummarySet) {
            return ResponseWrap.wrapResult(AttendanceCodes.ABNORMAL_NOTIFY_CONFIG_JOB_NOT_SET, null).getMsg();
        }
        if (!detailSet) {
            return ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_DETAIL_NOTIFY_CONFIG_JOB_NOT_SET, null).getMsg();
        }
        notifyConfigDo.update(modifyConfig, tenantId);
        return "";
    }

    /**
     * 更新xxl-job任务
     * @param modifyConfig 配置参数
     */
    private void updateXxlJobTask(NotifyConfigDo modifyConfig) {
        if (modifyConfig.getWeeklyNotifyTime() == null || modifyConfig.getWeeklyNotifyFrequency() == null) {
            return;
        }
        String week = modifyConfig.getWeeklyNotifyFrequency().toString();
        Integer notifyTime = modifyConfig.getWeeklyNotifyTime();
        int hour = notifyTime / 60;
        int minute = notifyTime % 60;
        String cron = String.format("0 %s %s ? * %s", minute, hour, week);
        try {
            XxlJobParams xxlJobParams = new XxlJobParams();
            xxlJobParams.setBelongOrgId(modifyConfig.getTenantId());
            boolean autoStart = modifyConfig.getWeeklyNotifySwitch() == 1;
            xxlJobService.addOrUpdateAttendanceXxlJob(WEEKLY_REPORT_XXL_JOB_NAME, cron, WEEKLY_REPORT_HANDLER, xxlJobParams, autoStart);
        } catch (Exception e) {
            log.error("updateXxlJobTask failed:{}", e.getMessage(), e);
        }
    }

    /**
     * 启用停用周报xxl-job
     * @param modifyConfig 配置参数
     * @return
     */
    private boolean updateXxlJobTaskStatus(NotifyConfigDo modifyConfig) {
        try {
            XxlJobParams xxlJobParams = new XxlJobParams();
            xxlJobParams.setBelongOrgId(modifyConfig.getTenantId());
            if (!xxlJobService.checkXxlJobExisted(WEEKLY_REPORT_XXL_JOB_NAME, xxlJobParams, WEEKLY_REPORT_HANDLER)) {
                //未配置xxl-job
                return false;
            }
            //已配置，获取任务参数
            Optional<XxlJobInfo> xxlJobInfoOpt = xxlJobService.getAttendanceXxlJobInfoByJobDesc(WEEKLY_REPORT_XXL_JOB_NAME + modifyConfig.getTenantId(), WEEKLY_REPORT_HANDLER);
            if (xxlJobInfoOpt != null && xxlJobInfoOpt.isPresent()) {
                XxlJobInfo xxlJobInfo = xxlJobInfoOpt.get();
                if (modifyConfig.getWeeklyNotifySwitch() == 1) {
                    //启用
                    return xxlJobService.startJobByXxlJobId(xxlJobInfo.getId());
                } else {
                    //停用
                    return xxlJobService.stopJobByXxlJob(xxlJobInfo.getId());
                }
            }
        } catch (Exception e) {
            log.error("updateXxlJobTaskStatus failed:{}", e.getMessage(), e);
        }
        return false;
    }

    /**
     * 启用停用考勤异常汇总提醒xxl-job
     * @param modifyConfig 配置参数
     * @return
     */
    private boolean updateAbnormalXxlJobTaskStatus(NotifyConfigDo modifyConfig) {
        if (null != modifyConfig.getAbnormalTime() && !"[]".equals(modifyConfig.getAbnormalTime())) {
            List<AbnormalTimeDto> list = JSON.parseArray(modifyConfig.getAbnormalTime(), AbnormalTimeDto.class);
            for (AbnormalTimeDto abnormalTime : list) {
                Integer notifyType = abnormalTime.getType();
                Integer notifyDay = abnormalTime.getDay();
                Integer notifyTime = abnormalTime.getTime();
                if (notifyType == null || notifyDay == null || notifyTime == null) {
                    continue;
                }
                try {
                    XxlJobParams xxlJobParams = new XxlJobParams();
                    xxlJobParams.setBelongOrgId(modifyConfig.getTenantId());
                    String jobDesc = String.format("%s_%s_%s_%s", ATTENDANCE_ABNORMAL_XXL_JOB_NAME, notifyType, notifyDay, notifyTime);
                    if (!xxlJobService.checkXxlJobExisted(jobDesc, xxlJobParams, ATTENDANCE_ABNORMAL_HANDLER)) {
                        continue;
                    }
                    //已配置，获取任务参数
                    Optional<XxlJobInfo> xxlJobInfoOpt = xxlJobService.getAttendanceXxlJobInfoByJobDesc(jobDesc + modifyConfig.getTenantId(), ATTENDANCE_ABNORMAL_HANDLER);
                    if (xxlJobInfoOpt != null && xxlJobInfoOpt.isPresent()) {
                        XxlJobInfo xxlJobInfo = xxlJobInfoOpt.get();
                        if (modifyConfig.getAbnormalSwitch() == 1) {
                            //启用
                            return xxlJobService.startJobByXxlJobId(xxlJobInfo.getId());
                        } else {
                            //停用
                            return xxlJobService.stopJobByXxlJob(xxlJobInfo.getId());
                        }
                    }
                } catch (Exception e) {
                    log.error("updateAbnormalXxlJobTaskStatus failed:{}", e.getMessage(), e);
                }
            }
        }
        return false;
    }

    /**
     * 刷新考勤异常汇总定时任务
     * @param tenantId 租户
     */
    @Override
    public void attendanceAbnormalJobRefresh(String tenantId) {
        NotifyConfigDo modifyConfig = notifyConfigDo.getNotifyConfigById(tenantId);
        if (null == modifyConfig) {
            return;
        }
        if (null != modifyConfig.getAbnormalTime() && !"[]".equals(modifyConfig.getAbnormalTime())) {
            List<AbnormalTimeDto> abnormalTimes = JSON.parseArray(modifyConfig.getAbnormalTime(), AbnormalTimeDto.class);
            deleteAbnormalXxlJobTask(tenantId, modifyConfig.getAbnormalTime());
            saveAttendanceAbnormalJob(tenantId, modifyConfig.getAbnormalSwitch(), abnormalTimes);
        }
    }

    /**
     * 保存考勤明细xxl-job任务
     * @param tenantId 租户
     * @param dto 配置参数
     */
    private void saveAttendanceDetailXxlJobTask(String tenantId, NotifyConfigDto dto) {
        if (CollectionUtils.isEmpty(dto.getAttendanceDetailTime())) {
            return;
        }
        saveAttendanceDetailJob(tenantId, dto.getAttendanceDetailSwitch(), dto.getAttendanceDetailTime());
    }

    /**
     * 保存考勤明细定时任务
     * @param tenantId 租户
     * @param msgSwitch 开关
     * @param times 配置
     */
    public void saveAttendanceDetailJob(String tenantId, Integer msgSwitch, List<AttendanceDetailTimeDto> times) {
        if (null == times || times.size() <= 0) {
            return;
        }
        for (AttendanceDetailTimeDto time : times) {
            Integer notifyType = time.getType();
            Integer notifyDay = time.getDay();
            Integer notifyTime = time.getTime();
            Integer waGroupId = time.getWaGroupId();
            if (notifyType == null || notifyDay == null || notifyTime == null || waGroupId == null) {
                continue;
            }
            int hour = notifyTime / 60;
            int minute = notifyTime % 60;
            int day = notifyDay;
            String cron = "";
            if (notifyType == 1) {
                int curMonthMaxDay = DateUtil.getMonthActualMax(DateUtil.getOnlyDate() * 1000);
                if (day > curMonthMaxDay) {
                    cron = String.format("0 %s %s L * ?", minute, hour);
                } else {
                    cron = String.format("0 %s %s %s * ?", minute, hour, day);
                }
            } else {
                cron = String.format("0 %s %s ? * %s", minute, hour, notifyDay);
            }
            try {
                XxlJobParams xxlJobParams = new XxlJobParams();
                xxlJobParams.setBelongOrgId(tenantId);
                xxlJobParams.setType(notifyType);
                xxlJobParams.setDay(notifyDay);
                xxlJobParams.setTime(notifyTime);
                xxlJobParams.setWaGroupId(waGroupId);
                boolean autoStart = msgSwitch == 1;
                String jobDesc = String.format("%s_%s_%s_%s_%s", ATTENDANCE_DETAIL_XXL_JOB_NAME, notifyType, notifyDay, notifyTime, waGroupId);
                xxlJobService.addOrUpdateAttendanceXxlJob(jobDesc, cron, ATTENDANCE_DETAIL_HANDLER, xxlJobParams, autoStart);
            } catch (Exception e) {
                log.error("saveAttendanceDetailJob failed:{}", e.getMessage(), e);
            }
        }
    }

    /**
     * 更新考勤明细xxl-job任务
     * @param tenantId 租户
     * @param dto 配置参数
     */
    private void updateAttendanceDetailXxlJobTask(String tenantId, NotifyConfigDto dto, NotifyConfigDo modifyConfig) {
        deleteAttendanceDetailXxlJobTask(tenantId, modifyConfig.getAttendanceDetailTime());
        saveAttendanceDetailXxlJobTask(tenantId, dto);
    }

    /**
     * 删除考勤明细xxl-job任务
     * @param tenantId 租户
     * @param time 配置参数
     */
    private void deleteAttendanceDetailXxlJobTask(String tenantId, String time) {
        if (null != time && !"[]".equals(time)) {
            List<AttendanceDetailTimeDto> list = JSON.parseArray(time, AttendanceDetailTimeDto.class);
            deleteAttendanceDetailXxlJobTask(tenantId, list);
        }
    }

    /**
     * 删除考勤明细xxl-job任务
     * @param tenantId 租户
     * @param list 配置参数
     */
    private void deleteAttendanceDetailXxlJobTask(String tenantId, List<AttendanceDetailTimeDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (AttendanceDetailTimeDto time : list) {
            try {
                Integer notifyType = time.getType();
                Integer notifyDay = time.getDay();
                Integer notifyTime = time.getTime();
                Integer waGroupId = time.getWaGroupId();
                XxlJobParams xxlJobParams = new XxlJobParams();
                xxlJobParams.setBelongOrgId(tenantId);
                xxlJobParams.setType(notifyType);
                xxlJobParams.setDay(notifyDay);
                xxlJobParams.setTime(notifyTime);
                xxlJobParams.setWaGroupId(waGroupId);
                String jobDesc = String.format("%s_%s_%s_%s_%s", ATTENDANCE_DETAIL_XXL_JOB_NAME, notifyType, notifyDay, notifyTime, waGroupId);
                xxlJobService.deleteAttendanceXxlJob(tenantId, jobDesc, ATTENDANCE_DETAIL_HANDLER);
            } catch (Exception e) {
                log.error("deleteAttendanceDetailXxlJobTask failed:{}", e.getMessage(), e);
            }
        }
    }

    /**
     * 启用停用考勤明细提醒xxl-job
     * @param modifyConfig 配置参数
     * @return
     */
    private boolean updateAttendanceDetailXxlJobTaskStatus(NotifyConfigDo modifyConfig) {
        if (null != modifyConfig.getAttendanceDetailTime() && !"[]".equals(modifyConfig.getAttendanceDetailTime())) {
            List<AttendanceDetailTimeDto> list = JSON.parseArray(modifyConfig.getAttendanceDetailTime(), AttendanceDetailTimeDto.class);
            for (AttendanceDetailTimeDto time : list) {
                Integer waGroupId = time.getWaGroupId();
                Integer notifyType = time.getType();
                Integer notifyDay = time.getDay();
                Integer notifyTime = time.getTime();
                if (notifyType == null || notifyDay == null || notifyTime == null || waGroupId == null) {
                    continue;
                }
                try {
                    XxlJobParams xxlJobParams = new XxlJobParams();
                    xxlJobParams.setBelongOrgId(modifyConfig.getTenantId());
                    xxlJobParams.setType(notifyType);
                    xxlJobParams.setDay(notifyDay);
                    xxlJobParams.setTime(notifyTime);
                    xxlJobParams.setWaGroupId(waGroupId);
                    String jobDesc = String.format("%s_%s_%s_%s_%s", ATTENDANCE_DETAIL_XXL_JOB_NAME, notifyType, notifyDay, notifyTime, waGroupId);
                    if (!xxlJobService.checkXxlJobExisted(jobDesc, xxlJobParams, ATTENDANCE_DETAIL_HANDLER)) {
                        continue;
                    }
                    //已配置，获取任务参数
                    Optional<XxlJobInfo> xxlJobInfoOpt = xxlJobService.getAttendanceXxlJobInfoByJobDesc(jobDesc + modifyConfig.getTenantId(), ATTENDANCE_DETAIL_HANDLER);
                    if (xxlJobInfoOpt != null && xxlJobInfoOpt.isPresent()) {
                        XxlJobInfo xxlJobInfo = xxlJobInfoOpt.get();
                        if (modifyConfig.getAttendanceDetailSwitch() == 1) {
                            //启用
                            return xxlJobService.startJobByXxlJobId(xxlJobInfo.getId());
                        } else {
                            //停用
                            return xxlJobService.stopJobByXxlJob(xxlJobInfo.getId());
                        }
                    }
                } catch (Exception e) {
                    log.error("updateAttendanceDetailXxlJobTaskStatus failed:{}", e.getMessage(), e);
                }
            }
        }
        return false;
    }

    /**
     * 刷新考勤明细定时任务
     * @param tenantId 租户
     */
    @Override
    public void attendanceDetailJobRefresh(String tenantId) {
        NotifyConfigDo modifyConfig = notifyConfigDo.getNotifyConfigById(tenantId);
        if (null == modifyConfig) {
            return;
        }
        if (null != modifyConfig.getAttendanceDetailTime() && !"[]".equals(modifyConfig.getAttendanceDetailTime())) {
            List<AttendanceDetailTimeDto> times = JSON.parseArray(modifyConfig.getAttendanceDetailTime(), AttendanceDetailTimeDto.class);
            deleteAttendanceDetailXxlJobTask(tenantId, modifyConfig.getAttendanceDetailTime());
            saveAttendanceDetailJob(tenantId, modifyConfig.getAttendanceDetailSwitch(), times);
        }
    }
}
