package com.caidao1.integrate.reader;

import com.caidao1.commons.utils.SftpUtil;
import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.caidao1.integrate.util.GnuPG;
import com.caidao1.integrate.util.GzipUtil;
import com.caidao1.integrate.util.XmlParseUtil;
import com.caidao1.integrate.util.IntegrateUtil;
import com.weibo.api.motan.core.extension.SpiMeta;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SpiMeta(name = "xmlsftp")
public class SftpXmlSourceReader implements SourceReader {

    private static final Log logger = LogFactory.getLog(SftpXmlSourceReader.class);

    public List<Map<String, Object>> getSourceResult(Long corpId, String belongId, SysDataInput dataInput, Map<String, Object> mapJson, Map exparams, List<String> logList, Map returnMap) throws Exception {
        List<Map<String, Object>> sourceResult = new ArrayList<>();

        SftpUtil client = new SftpUtil();
        client.setServer((String) mapJson.get("sftp.server"));
        client.setPort((Integer) mapJson.get("sftp.port"));
        client.setLogin((String) mapJson.get("sftp.login"));
        client.setPassword((String) mapJson.get("sftp.password"));
        client.connect();

        try {
            FileUtils.forceMkdir(new File((String) mapJson.get("sftp.destination")));
            String filter = (String) mapJson.getOrDefault("sftp.filter", "");
            //格式化支持
            if (mapJson.containsKey("sftp.filterParam")) {
                filter = String.format(filter, IntegrateUtil.formatExp((String) mapJson.get("sftp.filterParam"), "yyyyMMdd"));
            }
            List<String> files = client.lsFolder((String) mapJson.get("sftp.sources"), filter);
            if (CollectionUtils.isEmpty(files) && returnMap != null) {
                returnMap.put("ftpFileEmpty", true);//从ftp获取到0条数据
            }
            logger.info("本次接入文件个数："+files.size()+"，文件名："+ files.toString());
            for (String file : files) {
                logger.info("开始下载文件："+file);
                client.retrieveFile((String) mapJson.get("sftp.sources") + File.separator + file, (String) mapJson.get("sftp.destination") + File.separator + file);
                logger.info("下载文件成功："+file);
                boolean success = true;
                String readFile = mapJson.get("sftp.destination") + File.separator + file;
                if (mapJson.containsKey("encryption") && mapJson.containsKey("passphrase")) {
                    String enfile = mapJson.get("sftp.destination") + File.separator + file;
                    readFile = mapJson.get("sftp.destination") + File.separator + FilenameUtils.getName(enfile) + ".dec";
                    if ("gnupg".equalsIgnoreCase((String) mapJson.get("encryption"))) {
                        if (new File(readFile).exists()) FileUtils.forceDelete(new File(readFile));
                        success = GnuPG.decrypt(enfile, readFile, (String) mapJson.get("passphrase"));
                    }
                } else if ("targz".equalsIgnoreCase((String) mapJson.get("encryption"))) {
                    String enfile = mapJson.get("sftp.destination") + File.separator + file;
                    readFile = mapJson.get("sftp.destination") + File.separator + FilenameUtils.getBaseName(FilenameUtils.getBaseName(enfile));
                    success = GzipUtil.unCompressTarGz(enfile);
                }

                if (success) {
                    logger.info("开始读取文件："+file);
                    readFile(readFile,sourceResult, returnMap);
                    logger.info("读取文件成功："+sourceResult.size());
                }

                if (mapJson.containsKey("encryption") && mapJson.containsKey("passphrase") && (Boolean) mapJson.getOrDefault("delete_read", true)) {
                    logger.info("开始删除文件。。。");
                    FileUtils.forceDelete(new File(readFile));
                }
            }
        } catch (Exception e) {
            logger.error("", e);
        } finally {
            client.disconnect();
        }
        logger.info("成功返回数据，size="+sourceResult.size());
        return sourceResult;
    }

    private void readFile(String readFile,List<Map<String, Object>> sourceResult, Map returnMap) throws FileNotFoundException {
        if (new File(readFile).isDirectory()) {
            for (File subFile : new File(readFile).listFiles()) {
                logger.debug("递归获取文件："+subFile.getName());
                readFile(subFile.getAbsolutePath(),sourceResult, returnMap);
            }
            return;
        }
        try {
            logger.debug("文件解析开始。。。："+readFile);
            Map data = XmlParseUtil.xmlTomap(new File(readFile));
            if(data != null){
                sourceResult.add(data);
            }
            logger.debug("文件解析结束。。。："+readFile);
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (returnMap != null) {
                    String fileName = readFile.substring(readFile.lastIndexOf(File.separator) + 1);
                    List<String> failFiles;
                    if (returnMap.containsKey("failFiles") && returnMap.get("failFiles") != null) {
                        failFiles = (List<String>) returnMap.get("failFiles");
                    } else {
                        failFiles = new ArrayList<>();
                    }
                    failFiles.add(fileName);
                    returnMap.put("failFiles", failFiles);
                }
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
    }
}