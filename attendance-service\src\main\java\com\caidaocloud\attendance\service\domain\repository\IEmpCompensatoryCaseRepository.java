package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryCaseDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCase;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

public interface IEmpCompensatoryCaseRepository {
    EmpCompensatoryCaseDo getDetailById(String tenantId, Long id);

    void save(WaEmpCompensatoryCase model);

    void update(WaEmpCompensatoryCase model);

    PageList<EmpCompensatoryCaseDo> getEmpCompensatoryCaseList(MyPageBounds myPageBounds, Map params);

    void batchSave(List<WaEmpCompensatoryCase> records);

    List<EmpCompensatoryCaseDo> getApprovedOfCompensatoryCase(List<Long> empIdList, Long startDate, Long endDate);

    List<EmpCompensatoryCaseDo> getEmpCompensatoryCase(String tenantId, Long applyId);
}
