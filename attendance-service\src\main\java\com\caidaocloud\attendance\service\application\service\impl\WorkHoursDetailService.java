package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.exception.CDException;
import com.caidao1.wa.mybatis.mapper.WaGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaSobMapper;
import com.caidao1.wa.mybatis.model.WaGroup;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.service.application.dto.EmpOvertimeDto;
import com.caidaocloud.attendance.service.application.dto.EmpRegisterTimeDto;
import com.caidaocloud.attendance.service.application.dto.WaStatisticsDto;
import com.caidaocloud.attendance.service.application.enums.CompStdCalcMethodEnum;
import com.caidaocloud.attendance.service.domain.entity.WaAnalyzeDo;
import com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo;
import com.caidaocloud.attendance.service.domain.entity.WaGroupWorkingHourRuleDo;
import com.caidaocloud.attendance.service.domain.entity.WorkHoursDetailDo;
import com.caidaocloud.attendance.service.interfaces.dto.workHour.HourlyWorkingHourEmpDto;
import com.caidaocloud.attendance.service.schedule.application.service.dto.EmpWorkInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.*;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class WorkHoursDetailService {

    @Autowired
    private WaSobMapper waSobMapper;

    @Autowired
    private WaGroupMapper waGroupMapper;

    @Autowired
    private WaGroupWorkingHourRuleService waGroupWorkingHourRuleService;

    @Autowired
    private EmpWorkingHoursSettlementService empWorkingHoursSettlementService;

    @Autowired
    private WaCommonService waCommonService;

    @Autowired
    private WaAnalyzeDo waAnalyzeDo;
    @Autowired
    private WaEmpOvertimeDo waEmpOvertimeDo;

    public void calcWorkHoursDetail(WaStatisticsDto waStatisticsDto, Integer sobId, String tenantId){
        WaSob waSob = waSobMapper.selectByPrimaryKey(sobId);
        WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waSob.getWaGroupId());
        Integer waGroupId = waGroup.getWaGroupId();
        val empIds = waStatisticsDto.getEmpList();
        if(empIds.isEmpty()){
            return;
        }
        // 获取工时规则
        WaGroupWorkingHourRuleDo rule = waGroupWorkingHourRuleService.getByWaGroupId(tenantId, waGroupId);
        if (rule == null) {
            log.info("未设置工时规则，waGroupId={}", waGroupId);
            throw new CDException("未设置工时规则");
        }
        List<EmpWorkInfo> empWorkInfos = empWorkingHoursSettlementService.getTenantEmpInfos(tenantId, empIds);
        // 按员工类型分类
        List<HourlyWorkingHourEmpDto> pieceworkEmpWorkInfos = empWorkingHoursSettlementService.getPieceworkEmpWorkInfos(empWorkInfos);
        List<HourlyWorkingHourEmpDto> nonPieceworkEmpWorkInfos = empWorkingHoursSettlementService.getNonPieceworkEmpWorkInfos(empWorkInfos);
        val hoursNonPiece = calc(nonPieceworkEmpWorkInfos, tenantId, waStatisticsDto, waGroupId, rule, true);
        val hoursPiece = calc(pieceworkEmpWorkInfos, tenantId, waStatisticsDto, waGroupId, rule, false);
        val hours = Sequences.sequence(hoursNonPiece).join(hoursPiece).toList();
        WorkHoursDetailDo.replaceAll(tenantId, empIds, waStatisticsDto.getStartDate(), waStatisticsDto.getEndDate(), hours);
    }

    public List<WorkHoursDetailDo> calc(List<HourlyWorkingHourEmpDto> empWorkInfos, String tenantId, WaStatisticsDto waStatisticsDto, Integer waGroupId, WaGroupWorkingHourRuleDo rule, boolean nonPiece){
        if(empWorkInfos.isEmpty()){
            return Lists.list();
        }
        val groupedEmps = empWorkInfos.stream()
                .collect(Collectors.groupingBy(HourlyWorkingHourEmpDto::getWorkingHourType));
        val workingHoursOfStandard = calcWorkHourOfStandard(tenantId, waStatisticsDto.getStartDate(), waStatisticsDto.getEndDate(),
                groupedEmps.getOrDefault(HourlyWorkingHourEmpDto.WorkingHourType.STANDARD, org.apache.commons.compress.utils.Lists.newArrayList())
                        .stream().map(HourlyWorkingHourEmpDto::getEmpId).collect(Collectors.toList()), String.valueOf(waGroupId));
        val workingHoursOfComprehensive = calcWorkHourOfComprehensive(tenantId, waStatisticsDto.getStartDate(), waStatisticsDto.getEndDate(), rule,
                groupedEmps.getOrDefault(HourlyWorkingHourEmpDto.WorkingHourType.COMPREHENSIVE, org.apache.commons.compress.utils.Lists.newArrayList())
                        .stream().map(HourlyWorkingHourEmpDto::getEmpId).collect(Collectors.toList()), String.valueOf(waGroupId));
        val result = Sequences.sequence(workingHoursOfStandard).join(workingHoursOfComprehensive).toList();

        List<EmpRegisterTimeDto> empRegisterList = waAnalyzeDo.listRegisterTimeByEmps(tenantId, waStatisticsDto.getStartDate(), waStatisticsDto.getEndDate(), empWorkInfos.stream().map(it->it.getEmpId()).collect(Collectors.toList()));

        empRegisterList.forEach(empRegister -> {
            val belongDate = empRegister.getBelongDate();
            val empId = empRegister.getEmpId();
            result.stream().filter(it->it.getDate().equals(belongDate) && it.getEmpId().equals(Long.valueOf(empId))).findFirst().ifPresent(workHoursDetail->{
                long shiftStartTime = empRegister.getShiftStartTime();
                long shiftEndTime = empRegister.getShiftEndTime();
                double registerTime = empRegister.getRegisterTime();
                if (shiftStartTime > shiftEndTime) {
                    shiftEndTime = shiftEndTime + 24 * 60;
                }
                val shiftTimeRange = (shiftEndTime < 1920 ? shiftEndTime : 1920) - (shiftStartTime > 1200 ? shiftStartTime : 1200);
                double time1 = 0;
                if (shiftTimeRange >= 240 && shiftTimeRange < 480) {
                    time1 = 0.5;
                } else if (shiftTimeRange >= 480) {
                    time1 = 1;
                }
                double time2 = 0;
                if (registerTime >= 240 && registerTime < 480) {
                    time2 = 0.5;
                } else if (registerTime >= 480) {
                    time2 = 1;
                }
                val time = Math.min(time1, time2);
                workHoursDetail.setNightShiftDays(workHoursDetail.getNightShiftDays().add(new BigDecimal(time)));
            });
        });
        if(nonPiece){
            List<EmpOvertimeDto> empOvertimeList = waEmpOvertimeDo.listOvertimeByEmps(tenantId, waStatisticsDto.getStartDate(), waStatisticsDto.getEndDate(), empWorkInfos.stream().map(it->it.getEmpId()).collect(Collectors.toList()));
            empOvertimeList.forEach(overtime -> {

                val belongDate = Instant.ofEpochSecond(overtime.getOvertimeStartTime())
                        .atZone(ZoneId.systemDefault()).withHour(0).withMinute(0)
                        .withSecond(0).withNano(0).toEpochSecond();
                val empId = overtime.getEmpId();
                result.stream().filter(it->it.getDate().equals(belongDate) && it.getEmpId().equals(Long.valueOf(empId)))
                        .findFirst().ifPresent(workHoursDetail->{
                    long otStartTime = overtime.getOvertimeStartTime();
                    long otEndTime = overtime.getOvertimeEndTime();
                    double relTime = overtime.getRelTimeDuration();

                    val compareEndTime = Instant.ofEpochSecond(otEndTime)
                            .atZone(ZoneId.systemDefault()).withHour(0).withMinute(0).withSecond(0).withNano(0).toEpochSecond() + 480*60;
                    val compareStartTime = Instant.ofEpochSecond(otStartTime)
                            .atZone(ZoneId.systemDefault()).withHour(0).withMinute(0).withSecond(0).withNano(0).toEpochSecond() + 1200*60;
                    val shiftTimeRange = (otEndTime < compareEndTime ? otEndTime : compareEndTime) - (otStartTime > compareStartTime ? otStartTime : compareStartTime);
                    double time1 = 0;
                    if (shiftTimeRange >= 240*60 && shiftTimeRange < 480*60) {
                        time1 = 0.5;
                    } else if (shiftTimeRange >= 480*60) {
                        time1 = 1;
                    }
                    double time2 = 0;
                    if (relTime >= 240 && relTime < 480) {
                        time2 = 0.5;
                    } else if (relTime >= 480) {
                        time2 = 1;
                    }
                    val time = Math.min(time1, time2);
                    workHoursDetail.setNightShiftDays(workHoursDetail.getNightShiftDays().add(new BigDecimal(time)));
                });
            });
        }
        return Sequences.sequence(workingHoursOfStandard).join(workingHoursOfComprehensive).toList();
    }



    public List<WorkHoursDetailDo> calcWorkHourOfStandard(String tenantId, long startTime, long endTime, List<String> empIds, String waGroupId) {
        if(empIds.isEmpty()){
            return Lists.list();
        }
        List<WorkHoursDetailDo> result = Lists.list();
        //标准工时:日历排班天数*8
        Map<Integer, WaShiftDef> corpShiftDefMap = waCommonService.getCorpAllShiftDef(tenantId);
        Map<String, Object> shiftMap = new HashMap<>();
        shiftMap.put("belongid", tenantId);
        shiftMap.put("startDate", startTime);
        shiftMap.put("endDate", endTime);
        shiftMap.put("anyEmpids", "'{" + StringUtils.join(empIds, ",").concat("}'"));
        Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();
        waCommonService.getEmpShiftInfoListMaps(shiftMap, empShiftInfoByDateMap, empIds.stream().map(Long::valueOf).collect(Collectors.toList()), corpShiftDefMap);
        empShiftInfoByDateMap.forEach((key, shiftInfo) -> {
            if(startTime>shiftInfo.getWorkDate() || endTime<shiftInfo.getWorkDate()){
                return;
            }
            if(shiftInfo.getDateType() != 1){
                return;
            }
            String empId = StringUtils.substringBefore(key, "_");
            String date = StringUtils.substringAfter(key, "_");
            WorkHoursDetailDo detail = new WorkHoursDetailDo();
            detail.setEmpId(Long.valueOf(empId));
            detail.setWaGroupId(waGroupId);
            detail.setDate(Long.valueOf(date));
            detail.setStdWorkTime(new BigDecimal(8));
            detail.setNightShiftDays(new BigDecimal(0));
            result.add(detail);
        });
        for(String empId : empIds){
            for(long time = startTime;time<=endTime;time=time+24*3600){
                val loopDate = time;
                val existed = result.stream().filter(it->it.getEmpId().equals(Long.valueOf(empId)) && it.getDate().equals(loopDate)).findFirst().isPresent();
                if(!existed){
                    WorkHoursDetailDo detail = new WorkHoursDetailDo();
                    detail.setEmpId(Long.valueOf(empId));
                    detail.setWaGroupId(waGroupId);
                    detail.setDate(time);
                    detail.setStdWorkTime(new BigDecimal(0));
                    detail.setNightShiftDays(new BigDecimal(0));
                    result.add(detail);
                }

            }
        }
        return result;
    }

    public List<WorkHoursDetailDo> calcWorkHourOfComprehensive(String tenantId, long startTime, long endTime, WaGroupWorkingHourRuleDo rule, List<String> empIds, String waGroupId) {
        if(empIds.isEmpty()){
            return Lists.list();
        }
        List<WorkHoursDetailDo> result = Lists.list();
        //标准工时:根据考勤方案 - 工时规则中配置的固定值还是工作日历去显示该周期的标准工时
        if (CompStdCalcMethodEnum.BY_FIX.getIndex().equals(rule.getCompStdCalcMethod())) {
            val fix = rule.getCompStdWorkingHours();
            for(String empId : empIds){
                for(long time = startTime;time<=endTime;time=time+24*3600){
                    WorkHoursDetailDo detail = new WorkHoursDetailDo();
                    detail.setEmpId(Long.valueOf(empId));
                    detail.setWaGroupId(waGroupId);
                    detail.setDate(time);
                    detail.setStdWorkTime(fix);
                    detail.setNightShiftDays(new BigDecimal(0));
                    result.add(detail);
                }
            }
            return result;
        } else {
            val compStdDateType = Arrays.stream(rule.getCompStdDateType().split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            Map<Integer, WaShiftDef> corpShiftDefMap = waCommonService.getCorpAllShiftDef(tenantId);
            Map<String, Object> shiftMap = new HashMap<>();
            shiftMap.put("belongid", tenantId);
            shiftMap.put("startDate", startTime);
            shiftMap.put("endDate", endTime);
            shiftMap.put("anyEmpids", "'{" + StringUtils.join(empIds, ",").concat("}'"));
            Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();
            waCommonService.getEmpShiftInfoListMaps(shiftMap, empShiftInfoByDateMap, empIds.stream().map(Long::valueOf).collect(Collectors.toList()), corpShiftDefMap);
            empShiftInfoByDateMap.forEach((key, shiftInfo) -> {
                if(startTime>shiftInfo.getWorkDate() || endTime<shiftInfo.getWorkDate()){
                    return;
                }
                if (compStdDateType.contains(shiftInfo.getDateType())) {
                    String empId = StringUtils.substringBefore(key, "_");
                    String date = StringUtils.substringAfter(key, "_");
                    WorkHoursDetailDo detail = new WorkHoursDetailDo();
                    detail.setEmpId(Long.valueOf(empId));
                    detail.setWaGroupId(waGroupId);
                    detail.setDate(Long.valueOf(date));
                    detail.setStdWorkTime(new BigDecimal(8));
                    detail.setNightShiftDays(new BigDecimal(0));
                    result.add(detail);
                }
            });
            for(String empId : empIds){
                for(long time = startTime;time<=endTime;time=time+24*3600){
                    val loopDate = time;
                    val existed = result.stream().filter(it->it.getEmpId().equals(Long.valueOf(empId)) && it.getDate().equals(loopDate)).findFirst().isPresent();
                    if(!existed){
                        WorkHoursDetailDo detail = new WorkHoursDetailDo();
                        detail.setEmpId(Long.valueOf(empId));
                        detail.setWaGroupId(waGroupId);
                        detail.setDate(time);
                        detail.setStdWorkTime(new BigDecimal(0));
                        detail.setNightShiftDays(new BigDecimal(0));
                        result.add(detail);
                    }

                }
            }
            return result;
        }
    }

    public Pair<Map<Long, BigDecimal>, Map<Long, BigDecimal>> list(List<String> empIdList, Long startDate, Long endDate) {
        Map<Long, BigDecimal> standard = Maps.map();
        Map<Long, BigDecimal> night = Maps.map();
        val tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        List<WorkHoursDetailDo> hours = WorkHoursDetailDo.list(tenantId, empIdList, startDate, endDate);
        hours.forEach(hour->{
            if(standard.containsKey(hour.getEmpId())){
                standard.put(hour.getEmpId(), standard.get(hour.getEmpId()).add(hour.getStdWorkTime()));
            }else{
                standard.put(hour.getEmpId(), hour.getStdWorkTime());
            }
            if(night.containsKey(hour.getEmpId())){
                night.put(hour.getEmpId(), night.get(hour.getEmpId()).add(hour.getNightShiftDays()));
            }else{
                night.put(hour.getEmpId(), hour.getNightShiftDays());
            }
        });
        return Pair.pair(standard, night);
    }
}
