package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.wa.mybatis.model.WaAnalyze;
import com.caidaocloud.attendance.service.domain.entity.WaBatchAnalyseResultAdjustDo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

/**
 * 批量考勤异常调整
 *
 * <AUTHOR>
 * @Date 2024/6/25
 */
public interface IWaBatchAnalyseResultAdjustRepository {
    void updateById(WaBatchAnalyseResultAdjustDo adjustDo);

    void insert(WaBatchAnalyseResultAdjustDo adjustDo);

    WaBatchAnalyseResultAdjustDo getById(Long batchId);

    void deleteById(Long batchId);

    /**
     * 查询员工考勤数据
     *
     * @param tenantId
     * @param empid
     * @param startDate
     * @param endDate
     * @param abnormal
     * @return
     */
    List<WaAnalyze> selectEmpWaAnalyzeList(String tenantId, Long empid, Long startDate, Long endDate, Boolean abnormal);

    /**
     * 批量考勤异常调整分页列表查询
     *
     * @param pageBounds
     * @param params
     * @return
     */
    PageList<Map> selectPageList(MyPageBounds pageBounds, Map params);
}
