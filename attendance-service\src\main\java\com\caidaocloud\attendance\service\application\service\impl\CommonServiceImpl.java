package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.wa.mybatis.mapper.WaEmpLeaveMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpOvertimeMapper;
import com.caidao1.wa.mybatis.mapper.WaRegisterRecordMapper;
import com.caidao1.wa.mybatis.model.WaEmpLeave;
import com.caidao1.wa.mybatis.model.WaEmpOvertime;
import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidao1.wa.mybatis.model.WaRegisterRecordExample;
import com.caidaocloud.attendance.service.application.service.ICommonService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.*;
import com.caidaocloud.attendance.service.infrastructure.repository.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class CommonServiceImpl implements ICommonService {

    @Resource
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Resource
    private WaEmpOvertimeMapper waEmpOvertimeMapper;
    @Resource
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Resource
    private WaRegisterRecordBdkMapper waRegisterRecordBdkMapper;
    @Resource
    private WaEmpTravelMapper waEmpTravelMapper;
    @Resource
    private WaShiftApplyRecordMapper waShiftApplyRecordMapper;
    @Resource
    private WaEmpLeaveCancelMapper waEmpLeaveCancelMapper;
    @Resource
    private WaEmpCompensatoryCaseApplyMapper empCompensatoryCaseApplyMapper;
    @Resource
    private WaWorkflowRevokeMapper workflowRevokeMapper;

    @Override
    public void handleLeaveWorkflowEvent(Integer id, Long eventTime) {
        WaEmpLeave leave = new WaEmpLeave();
        leave.setLeaveId(id);
        leave.setLastApprovalTime(eventTime);
        log.info("handleLeaveWorkflowEvent param:{}", JSONUtils.ObjectToJson(leave));
        waEmpLeaveMapper.updateByPrimaryKeySelective(leave);
    }

    @Override
    public void handleOvertimeWorkflowEvent(Integer id, Long eventTime) {
        WaEmpOvertime overtime = new WaEmpOvertime();
        overtime.setOtId(id);
        overtime.setLastApprovalTime(eventTime);
        log.info("handleOvertimeWorkflowEvent param:{}", JSONUtils.ObjectToJson(overtime));
        waEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
    }

    @Override
    public void handleBdkWorkflowEvent(Long id, Long eventTime) {
        WaRegisterRecordBdkPo bdkPo = new WaRegisterRecordBdkPo();
        bdkPo.setRecordId(id);
        bdkPo.setLastApprovalTime(eventTime);
        log.info("handleBdkWorkflowEvent param:{}", JSONUtils.ObjectToJson(bdkPo));
        waRegisterRecordBdkMapper.updateByPrimaryKeySelective(bdkPo);
        WaRegisterRecordExample registerRecordExample = new WaRegisterRecordExample();
        WaRegisterRecordExample.Criteria criteria = registerRecordExample.createCriteria();
        criteria.andBdkRecordIdEqualTo(id);
        List<WaRegisterRecord> records = waRegisterRecordMapper.selectByExample(registerRecordExample);
        if (CollectionUtils.isNotEmpty(records)) {
            for (WaRegisterRecord row : records) {
                WaRegisterRecord record = new WaRegisterRecord();
                record.setRecordId(row.getRecordId());
                record.setLastApprovalTime(eventTime);
                waRegisterRecordMapper.updateByPrimaryKeySelective(record);
            }
        }
    }

    @Override
    public void handleTravelWorkflowEvent(Long id, Long eventTime) {
        WaEmpTravel travel = new WaEmpTravel();
        travel.setTravelId(id);
        travel.setLastApprovalTime(eventTime);
        log.info("handleTravelWorkflowEvent param:{}", JSONUtils.ObjectToJson(travel));
        waEmpTravelMapper.updateByPrimaryKeySelective(travel);
    }

    @Override
    public void handleShiftWorkflowEvent(Long id, Long eventTime) {
        WaShiftApplyRecord shift = new WaShiftApplyRecord();
        shift.setRecId(id);
        shift.setUpdateTime(eventTime);
        log.info("handleShiftWorkflowEvent param:{}", JSONUtils.ObjectToJson(shift));
        waShiftApplyRecordMapper.updateByPrimaryKeySelective(shift);
    }

    @Override
    public void handleLeaveCancelWorkflowEvent(Long id, Long eventTime) {
        WaEmpLeaveCancel leaveCancel = new WaEmpLeaveCancel();
        leaveCancel.setLeaveCancelId(id);
        leaveCancel.setLastApprovalTime(eventTime);
        log.info("handleLeaveCancelWorkflowEvent param:{}", JSONUtils.ObjectToJson(leaveCancel));
        waEmpLeaveCancelMapper.updateByPrimaryKeySelective(leaveCancel);
    }

    @Override
    public void handleCompensatoryWorkflowEvent(Long id, Long eventTime) {
        WaEmpCompensatoryCaseApply empCompensatoryCaseApply = new WaEmpCompensatoryCaseApply();
        empCompensatoryCaseApply.setId(id);
        empCompensatoryCaseApply.setLastApprovalTime(eventTime);
        empCompensatoryCaseApplyMapper.updateByPrimaryKeySelective(empCompensatoryCaseApply);
    }

    @Override
    public void handleOvertimeRevokeWorkflowEvent(Long id, Long eventTime) {
        updateRevokeWorkflowEvent(id, eventTime);
    }

    @Override
    public void handleOvertimeAbolishWorkflowEvent(Long id, Long eventTime) {
        updateRevokeWorkflowEvent(id, eventTime);
    }

    @Override
    public void handleTravelRevokeWorkflowEvent(Long id, Long eventTime) {
        updateRevokeWorkflowEvent(id, eventTime);
    }

    @Override
    public void handleTravelAbolishWorkflowEvent(Long id, Long eventTime) {
        updateRevokeWorkflowEvent(id, eventTime);
    }

    private void updateRevokeWorkflowEvent(Long id, Long eventTime) {
        WaWorkflowRevoke workflowRevoke = new WaWorkflowRevoke();
        workflowRevoke.setId(id);
        workflowRevoke.setLastApprovalTime(eventTime);
        workflowRevokeMapper.updateByPrimaryKeySelective(workflowRevoke);
    }

    @Override
    public void handleLeaveExtensionWorkflowEvent(Long id, Long eventTime) {
        // todo
    }
}
