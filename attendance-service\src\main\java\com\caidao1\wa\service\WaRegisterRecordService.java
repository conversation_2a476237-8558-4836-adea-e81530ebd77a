package com.caidao1.wa.service;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.wa.mybatis.mapper.WaMapper;
import com.caidao1.wa.mybatis.mapper.WaRegisterRecordMapper;
import com.caidao1.wa.mybatis.mapper.WaResultConfirmMapper;
import com.caidao1.wa.mybatis.mapper.WaShiftDefMapper;
import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidao1.wa.mybatis.model.WaRegisterRecordExample;
import com.caidao1.wa.mybatis.model.WaResultConfirm;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.dto.MobileEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.record.core.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


@Service
@Slf4j
public class WaRegisterRecordService {
    @Autowired
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Autowired
    private WaShiftDefMapper waShiftDefMapper;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private WaResultConfirmMapper waResultConfirmMapper;
    @Autowired
    protected RedisTemplate redisTemplate;

    @Transactional
    public int updateRegTime(WaRegisterRecord record) throws Exception {
        WaRegisterRecord registerRecord = waRegisterRecordMapper.selectByPrimaryKey(record.getRecordId());
        if (registerRecord != null) {
            WaShiftDef shiftDef = waShiftDefMapper.selectByPrimaryKey(registerRecord.getShiftDefId());
            String result = registerRecord.getResultDesc();

            record.setEmpid(registerRecord.getEmpid());
            record.setRegisterType(registerRecord.getRegisterType());
            record.setBelongDate(registerRecord.getBelongDate());

            //打卡时间校验
            Boolean isError = checkTimeError(shiftDef, record);
            if (isError) {
                //异常
                if (StringUtils.isBlank(result)) {
                    result = MobileEnum.RegisterErrorType.TIME_ERR.toString();
                } else {
                    if (result.indexOf(MobileEnum.RegisterErrorType.TIME_ERR.toString()) == -1) {
                        result = result + "," + MobileEnum.RegisterErrorType.TIME_ERR.toString();
                    }
                }
                record.setResultDesc(result);
                record.setResultType(2);
            } else {
                if (StringUtils.isNotBlank(result)) {
                    result = result.replace(MobileEnum.RegisterErrorType.TIME_ERR.toString(), "");
                }
                record.setResultType(1);
                record.setResultDesc(result);
            }
            return waRegisterRecordMapper.updateByPrimaryKeySelective(record);
        }
        return 0;
    }

    public void finishBdkApproval(Integer businessKey, String choice) {
        Integer recordId = businessKey;
        if (recordId == null) {
            throw new CDException("PK is null");
        }
        if ("yes".equals(choice)) {
            WaRegisterRecord originRec = waRegisterRecordMapper.selectByPrimaryKey(recordId);
            if (originRec == null) {
                throw new CDException("DATA is null");
            }
            WaRegisterRecord registerRecordUpd = new WaRegisterRecord();
            registerRecordUpd.setRecordId(recordId);
            registerRecordUpd.setApprovalStatus(2); // 标识为审批通过
            registerRecordUpd.setLastApprovalTime(DateUtil.getCurrentTime(true));

            //打卡异常分析
            if (originRec.getShiftDefId() != null) {
                WaShiftDef shiftDef = waShiftDefMapper.selectByPrimaryKey(originRec.getShiftDefId());
                if (shiftDef != null) {
                    //设置打卡正常时间范围
                    if (originRec.getRegisterType() == 1 && shiftDef.getOnDutyStartTime() != null && shiftDef.getOnDutyEndTime() != null) {
                        registerRecordUpd.setNormalDate(DateUtil.convertMinuteToTime(shiftDef.doGetOnDutyStartTimeForView()) + "-" + DateUtil.convertMinuteToTime(shiftDef.getOnDutyEndTime()));
                    } else if (originRec.getRegisterType() == 2 && shiftDef.getOffDutyStartTime() != null && shiftDef.getOffDutyEndTime() != null) {
                        registerRecordUpd.setNormalDate(DateUtil.convertMinuteToTime(shiftDef.getOffDutyStartTime()) + "-" + DateUtil.convertMinuteToTime(shiftDef.getOffDutyEndTime()));
                    }
//                    Boolean isError = false;
//                    //判断调整时间后跟班次对比是还存在时间异常
//                    try {
//                        isError = mobileLeaveService.validateCheckInTime(shiftDef, originRec, SessionHolder.getLang());
//                    } catch (Exception e) {
//                        LOGGER.error(e.getMessage(),e);
//                    }
//                    if (isError) {
//                        registerRecordUpd.setResultType(2);
//                        registerRecordUpd.setResultDesc(MobileEnum.RegisterErrorType.TIME_ERR.toString());
//                    }
                }
            }
            registerRecordUpd.setUpdtime(DateUtil.getCurrentTime(true));
            waRegisterRecordMapper.updateByPrimaryKeySelective(registerRecordUpd);
        } else if ("no".equals(choice)) {
            WaRegisterRecord registerRecord = new WaRegisterRecord();
            registerRecord.setRecordId(recordId);
            registerRecord.setApprovalStatus(3); // 审批拒绝
            registerRecord.setLastApprovalTime(DateUtil.getCurrentTime(true));
            registerRecord.setUpdtime(DateUtil.getCurrentTime(true));

            waRegisterRecordMapper.updateByPrimaryKeySelective(registerRecord);
        } else if ("back".equals(choice)) {
            WaRegisterRecord registerRecord = new WaRegisterRecord();
            registerRecord.setRecordId(recordId);
            registerRecord.setApprovalStatus(5); // 已退回
            registerRecord.setLastApprovalTime(DateUtil.getCurrentTime(true));
            registerRecord.setUpdtime(DateUtil.getCurrentTime(true));
            waRegisterRecordMapper.updateByPrimaryKeySelective(registerRecord);
        }
    }

//    public Map getRegisterWorkFlowViews(String businessKey) throws Exception {
//        String startUrl = "mobileV18/saveBdkRegister";
//        Integer recordId = Integer.valueOf(businessKey.split("_")[0]);
//        Map map = waRegisterRecordMapper.getRegisterWorkFlowViews(recordId);
//
//        if (map != null && map.size() > 0) {
//            String key = "approval_status";
//            if (map.containsKey(key)) {
//                map.put(key + "_name", BaseConst.leave_OT_statusMaps.get(map.get(key)));
//            }
//            key = "ui_status";
//            if (map.containsKey(key)) {
//                map.put(key + "_name", BaseConst.leave_OT_statusMaps.get(map.get(key)));
//            }
//            key = "register_type";
//            if (map.containsKey(key)) {
//                Integer rtype = (Integer) map.get(key);
//                String text = "签到";
//                if (rtype == 2) {
//                    text = "签退";
//                }
//                map.put(key + "_name", text);
//            }
//            if (map.containsKey("form_data_id")) {
//                businessKey = businessKey.concat("_").concat("40");
//            }
//        }
//
//        //审批纪录
//        List approvalFlows = null;
//        String belongId = UserContext.getTenantId();
//        if (businessKey.indexOf("_") != -1) {
//            approvalFlows = workflowService.traceWorkflow(belongId, businessKey);
//        } else {
//            approvalFlows = workflowService.traceWorkflow(belongId, businessKey, startUrl);
//        }
//        map.put("approvalFlows", approvalFlows);
//        return map;
//    }

    /**
     * 考勤结果调整申请回掉
     *
     * @param businessKey
     * @param choice
     */
    public void finishWaResultConfirmDetailApproval(Integer businessKey, String choice) {
        if (businessKey == null) {
            throw new CDException("PK is null");
        }
        if (choice.equals("yes")) {
            WaResultConfirm updConfim = new WaResultConfirm();
            updConfim.setUpdtime(DateUtil.getCurrentTime(true));
            updConfim.setStatus(2);
            updConfim.setResultConfirmId(businessKey);
            waResultConfirmMapper.updateByPrimaryKeySelective(updConfim);
        } else if (choice.equals("no")) {
            WaResultConfirm updConfim = new WaResultConfirm();
            updConfim.setUpdtime(DateUtil.getCurrentTime(true));
            updConfim.setStatus(3);
            updConfim.setResultConfirmId(businessKey);
            waResultConfirmMapper.updateByPrimaryKeySelective(updConfim);
        } else if ("back".equals(choice)) {
            WaResultConfirm updConfim = new WaResultConfirm();
            updConfim.setUpdtime(DateUtil.getCurrentTime(true));
            updConfim.setStatus(5);
            updConfim.setResultConfirmId(businessKey);
            waResultConfirmMapper.updateByPrimaryKeySelective(updConfim);
        }
    }

    public boolean checkWaRegRecord(Long corpId, String belongOrgId, Integer recordId) {
        Map params = new HashMap();
        params.put("corpId", corpId);
        params.put("belongOrgId", belongOrgId);
        params.put("recordId", recordId);
        Map recMap = waMapper.getEmpRegRecordById(params);
        if (recMap != null && recMap.size() > 0) {
            //检查该条数据是否已纳入考勤分析
            params.put("empid", recMap.get("empid"));
            List<Map> list = waMapper.getWaAnalyzeByRecId(params);
            if (CollectionUtils.isNotEmpty(list)) {
                return false;
            }
        } else {
            throw new CDException(messageResource.getMessage("L005960", new Object[]{}, new Locale(SessionHolder.getLang())));
        }
        return true;
    }

    @Transactional
    public void delWaRegRecord(Long corpId, String belongOrgId, Integer recordId) {
        Map params = new HashMap();
        params.put("corpId", corpId);
        params.put("belongOrgId", belongOrgId);
        params.put("recordId", recordId);
        Map recMap = waMapper.getEmpRegRecordById(params);
        LogRecordContext.putVariable("empId", recMap.get("empid"));
        if (recMap != null && recMap.size() > 0) {
            waRegisterRecordMapper.deleteByPrimaryKey((Integer) recMap.get("recordId"));
        }

    }

    public static Long getMonthEnd(Long dateTimeMillis) {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date(dateTimeMillis));

        //设置为当月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND, 59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 999);
        // 获取本月最后一天的时间戳
        return c.getTimeInMillis() / 1000;
    }

    /**
     * 验证签到时间-判断调整时间后跟班次对比是还存在时间异常
     *
     * @param shiftDef
     * @param register
     * @return
     * @throws Exception
     */
    public boolean checkTimeError(WaShiftDef shiftDef, WaRegisterRecord register) {
        //签退：判断是否是跨夜签退，如果是在下班打卡范围内的跨夜签退，则自动标识归属日期在前一天
        //签到：如果签到时间大于签到结束时间 则说明上班迟到
        Integer registerType = register.getRegisterType();
        Long belongDate = register.getBelongDate();
        Long regTime = register.getRegDateTime();
        Long empid = register.getEmpid();

        if (registerType == 1) {
            //签到
            //判断是否弹性工作
            if (shiftDef.getIsFlexibleWork() != null && shiftDef.getIsFlexibleWork()) {
                Integer flexibleWorkType = shiftDef.getFlexibleWorkType();
                if (flexibleWorkType != null && flexibleWorkType == 1) {
                    //限定弹性打卡范围
                    Integer flexibleOnDutyStartTime = shiftDef.getFlexibleOnDutyStartTime();
                    Integer flexibleOnDutyEndTime = shiftDef.getFlexibleOnDutyEndTime();
                    if (flexibleOnDutyStartTime != null && flexibleOnDutyEndTime != null) {
                        Long flexibleStartTime = belongDate + (flexibleOnDutyStartTime * 60);
                        Long flexibleEndTime = belongDate + (flexibleOnDutyEndTime * 60);
                        if (regTime > flexibleStartTime && regTime <= flexibleEndTime) {
                            //签到时间在弹性范围内
                            return false;
                        }
                    }
                }
            }
            Long endTime = belongDate + (shiftDef.getOnDutyEndTime() * 60);
            if (regTime > endTime) {
                //签到时间大于签到结束时间
                return true;
            }
        } else if (registerType == 2) {
            // 签退 如果签退时间小于下班打卡开始时间，则说明是提前打卡
            //判断班次工作时间是否跨夜
            Long workbelongDate = belongDate;
            boolean isWorkKy = CdWaShiftUtil.checkCrossNightV2(shiftDef, shiftDef.getDateType());
            if (isWorkKy) {
                //工作时间跨夜
                workbelongDate = belongDate + 86400;
            }
            //下班开始时间
            Long onOffStart = workbelongDate + (shiftDef.getOffDutyStartTime() * 60);

            //判断是否弹性工作
            if (shiftDef.getIsFlexibleWork() != null && shiftDef.getIsFlexibleWork()) {
                //判断上班时间是在弹性范围内还是标准范围内(查询当天最早的签到时间)
                WaRegisterRecordExample exampleLast = new WaRegisterRecordExample();
                exampleLast.createCriteria().andEmpidEqualTo(empid).andBelongDateEqualTo(belongDate).andRegisterTypeEqualTo(1);
                exampleLast.setOrderByClause("reg_date_time asc");
                List<WaRegisterRecord> recordsLast = waRegisterRecordMapper.selectByExample(exampleLast);
                if (CollectionUtils.isNotEmpty(recordsLast)) {
                    WaRegisterRecord lastReg = recordsLast.get(0);
                    if (shiftDef.getFlexibleWorkType() != null && shiftDef.getFlexibleWorkType() == 1) {
                        Integer onDutyType = 0;//0 标准时间 1 弹性时间
                        Long signInTime = lastReg.getRegDateTime();//签到时间
                        Long flexibleOnDutyStartTime = belongDate + (shiftDef.getFlexibleOnDutyStartTime() * 60);
                        Long flexibleOnDutyEndTime = belongDate + (shiftDef.getFlexibleOnDutyEndTime() * 60);
                        if (signInTime > flexibleOnDutyStartTime && signInTime <= flexibleOnDutyEndTime) {
                            onDutyType = 1;
                        }
                        if (onDutyType == 1) {
                            //弹性下班
                            Long flexibleOffStartTime = workbelongDate + (shiftDef.getFlexibleOffDutyStartTime() * 60);//弹性下班开始时间
                            //弹性下班时间
                            if (regTime < flexibleOffStartTime) {
                                //早退
                                return true;
                            } else {
                                return false;
                            }
                        }
                    } else if (shiftDef.getFlexibleWorkType() != null && shiftDef.getFlexibleWorkType() == 2) {
                        //固定工时
                        Long minOffDutyTime = calculateMinOffDutyTime(shiftDef, belongDate, lastReg.getRegDateTime(), shiftDef.getWorkTotalTime());
                        if (regTime < minOffDutyTime || regTime < onOffStart) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                }
            }
            //标准时间签退判断
            if (regTime < onOffStart) {
                return true;
            }
        }
        return false;
    }

    /**
     * 弹性工作固定工作时常-计算下班最小时间
     *
     * @param workDate
     * @param regDateTime
     * @param fixedWorkingTime
     * @return
     */
    public Long calculateMinOffDutyTime(WaShiftDef shiftDef, Long workDate, Long regDateTime, Integer fixedWorkingTime) {
        if (shiftDef.getIsNoonRest()) {
            //中午休息
            Long noonRestStart = workDate + Long.valueOf(shiftDef.getNoonRestStart() * 60);
            Long noonRestEnd = workDate + Long.valueOf(shiftDef.getNoonRestEnd() * 60);
            if (regDateTime <= noonRestStart) {
                //早于中午休息开始时间，扣除整个中午休息的时间
                Integer noonTotalTime = shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                noonTotalTime = noonTotalTime < 0 ? 0 : noonTotalTime;
                fixedWorkingTime += noonTotalTime;
            } else if (regDateTime > noonRestStart && regDateTime <= noonRestEnd) {
                //在中午休息时间内，从中午休息结束时间点开始计算
                regDateTime = workDate + (shiftDef.getNoonRestEnd() * 60);
            }
        }
        Long minOffDutyTime = regDateTime + (fixedWorkingTime * 60);//最早下班时间
        return minOffDutyTime;
    }
}
