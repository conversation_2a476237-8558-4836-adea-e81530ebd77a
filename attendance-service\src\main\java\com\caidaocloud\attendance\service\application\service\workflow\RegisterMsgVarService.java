package com.caidaocloud.attendance.service.application.service.workflow;

import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.system.mybatis.model.SysCorpOrgExample;
import com.caidaocloud.attendance.service.application.enums.FuncTypeEnum;
import com.caidaocloud.hrpaas.core.metadata.enums.PropertyDataType;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfMetaNoticeVarDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程变量注册
 */
@Slf4j
@Service
public class RegisterMsgVarService {

    @Resource
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Value("${spring.application.name}")
    private String appName;
    @Value("${wf.msg.variable.callbackUrl:/api/attendance/wf/v1/notice/detail}")
    private String callbackUrl;

    public void registerWorkflowMsgVar(String tenantId) {
        SysCorpOrgExample example = new SysCorpOrgExample();
        SysCorpOrgExample.Criteria criteria = example.createCriteria();
        criteria.andOrgtype2EqualTo(1).andStatusEqualTo(1);
        List<Long> tenantIds = new ArrayList<>();
        if (StringUtils.isNotBlank(tenantId)) {
            tenantIds.add(Long.valueOf(tenantId));
        }
        if (CollectionUtils.isNotEmpty(tenantIds)) {
            criteria.andOrgidIn(tenantIds);
        }
        List<SysCorpOrg> items = sysCorpOrgMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(items)) {
            for (SysCorpOrg corp : items) {
                registerWorkflowMsgVariable(corp.getCorpid().toString());
            }
        }
    }

    /**
     * 注册工作流消息变量
     */
    private void registerWorkflowMsgVariable(String tenantId) {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        userInfo.setIsAdmin(true);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        try {
            // 1、请假
            registerLeaveWorkflowMsgVariable();
            // 2、加班
            registerOvertimeWorkflowMsgVariable();
            // 3、补卡
            registerBdkWorkflowMsgVariable();
            // 4、销假
            registerLeaveCancelWorkflowMsgVariable();
            // 5、出差
            registerTravelWorkflowMsgVariable();
            // 6、调班
            registerShiftChangeWorkflowMsgVariable();
            // 7、调休付现
            registerCompensatoryCaseWorkflowMsgVariable();
            // 8、批量出差
            registerBatchTravelWorkflowMsgVariable();
            // 9、加班撤销
            registerOvertimeRevokeWorkflowMsgVariable();
            // 10、加班废止
            registerOvertimeAbolishWorkflowMsgVariable();
            // 11、出差撤销
            registerTravelRevokeWorkflowMsgVariable();
            // 12、出差废止
            registerTravelAbolishWorkflowMsgVariable();
            // 13、批量休假
            registerBatchLeaveWorkflowMsgVariable();
            // 14、批量加班
            registerBatchOvertimeWorkflowMsgVariable();
            // 15、批量考勤异常申请
            registerBatchAnalyseAdjustWorkflowMsgVariable();
            // 16、假期延期
            registerLeaveExtensionWorkflowMsgVariable();
        } catch (Exception ex) {
            log.error("Registration Message Variable exception，异常信息：{}", ex.getMessage(), ex);
        }
    }

    /**
     * 1、休假
     */
    private void registerLeaveWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.LEAVE.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "假期名称", "leaveType"));
        list.add(singleNoticeParameter(funCode, "请假开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "请假结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "申请事由", "reason"));
        this.handleWorkflowVariableRegister(list);
    }

    /**
     * 2、加班
     */
    private void registerOvertimeWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.OVERTIME.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "加班类型", "overtimeType"));
        list.add(singleNoticeParameter(funCode, "加班开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "加班结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "申请事由", "reason"));
        this.handleWorkflowVariableRegister(list);
    }

    /**
     * 3、补卡
     */
    private void registerBdkWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.CARD_REPLACEMENT.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "补卡时间", "regDateTime"));
        list.add(singleNoticeParameter(funCode, "补卡事由", "reason"));
        this.handleWorkflowVariableRegister(list);
    }

    /**
     * 4、销假
     */
    private void registerLeaveCancelWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.LEAVE_CANCEL.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "休假开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "休假结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "休假申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "休假实际时长", "actualDuration"));
        list.add(singleNoticeParameter(funCode, "假期名称", "leaveType"));
        list.add(singleNoticeParameter(funCode, "销假类型", "leaveCancelType"));
        list.add(singleNoticeParameter(funCode, "销假时间", "leaveCancelTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "leaveCancelDuration"));
        this.handleWorkflowVariableRegister(list);
    }

    /**
     * 5、出差
     */
    private void registerTravelWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.TRAVEL.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "出差类型", "travelType"));
        list.add(singleNoticeParameter(funCode, "开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "出行地点", "site"));
        list.add(singleNoticeParameter(funCode, "出行方式", "travelMode"));
        this.handleWorkflowVariableRegister(list);
    }

    /**
     * 6、调班
     */
    private void registerShiftChangeWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.SHIFT.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "调班日期", "shiftChangeDate"));
        list.add(singleNoticeParameter(funCode, "原班次", "oldShift"));
        list.add(singleNoticeParameter(funCode, "打卡记录", "registerRecord"));
        list.add(singleNoticeParameter(funCode, "调整后班次", "newShift"));
        list.add(singleNoticeParameter(funCode, "申请事由", "reason"));
        handleWorkflowVariableRegister(list);
    }

    /**
     * 7、调休转付现
     */
    private void registerCompensatoryCaseWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.COMPENSATORY.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "申请付现额度", "applyDuration"));
        list.add(singleNoticeParameter(funCode, "单位", "transferUnit"));
        handleWorkflowVariableRegister(list);
    }

    /**
     * 8、加班撤销
     */
    private void registerOvertimeRevokeWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.OVERTIME_REVOKE.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "加班类型", "overtimeType"));
        list.add(singleNoticeParameter(funCode, "加班开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "加班结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "申请事由", "reason"));
        handleWorkflowVariableRegister(list);
    }

    /**
     * 加班废止
     */
    private void registerOvertimeAbolishWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.OVERTIME_ABOLISH.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "加班类型", "overtimeType"));
        list.add(singleNoticeParameter(funCode, "加班开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "加班结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "申请事由", "reason"));
        handleWorkflowVariableRegister(list);
    }

    /**
     * 9、出差撤销
     */
    private void registerTravelRevokeWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.TRAVEL_REVOKE.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "出差类型", "travelType"));
        list.add(singleNoticeParameter(funCode, "开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "出行地点", "site"));
        list.add(singleNoticeParameter(funCode, "出行方式", "travelMode"));
        handleWorkflowVariableRegister(list);
    }

    /**
     * 10、出差废止
     */
    private void registerTravelAbolishWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.TRAVEL_ABOLISH.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "出差类型", "travelType"));
        list.add(singleNoticeParameter(funCode, "开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "出行地点", "site"));
        list.add(singleNoticeParameter(funCode, "出行方式", "travelMode"));
        handleWorkflowVariableRegister(list);
    }

    /**
     * 8、批量出差
     */
    private void registerBatchTravelWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.BATCH_TRAVEL.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "出差类型", "travelType"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "出行地点", "site"));
        this.handleWorkflowVariableRegister(list);
    }

    /**
     * 批量休假
     */
    private void registerBatchLeaveWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.BATCH_LEAVE.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "假期名称", "leaveType"));
        list.add(singleNoticeParameter(funCode, "请假开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "请假结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        this.handleWorkflowVariableRegister(list);
    }

    /**
     * 批量加班
     */
    private void registerBatchOvertimeWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.BATCH_OVERTIME.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "加班开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "加班结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "原因", "reason"));
        this.handleWorkflowVariableRegister(list);
    }

    /**
     * 批量考勤异常申请
     */
    private void registerBatchAnalyseAdjustWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.BATCH_ANALYSE_ADJUST.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        this.handleWorkflowVariableRegister(list);
    }

    /**
     * 假期延期申请
     */
    private void registerLeaveExtensionWorkflowMsgVariable() {
        List<WfMetaNoticeVarDto> list = new ArrayList<>();
        String funCode = FuncTypeEnum.LEAVE_EXTENSION.getName();
        list.add(singleNoticeParameter(funCode, "申请人员", "applicant"));
        list.add(singleNoticeParameter(funCode, "申请日期", "applyTime"));
        list.add(singleNoticeParameter(funCode, "加班开始时间", "startTime"));
        list.add(singleNoticeParameter(funCode, "加班结束时间", "endTime"));
        list.add(singleNoticeParameter(funCode, "申请时长", "duration"));
        list.add(singleNoticeParameter(funCode, "申请事由", "reason"));
        this.handleWorkflowVariableRegister(list);
    }

    private void handleWorkflowVariableRegister(List<WfMetaNoticeVarDto> list) {
        try {
            Result<?> result = wfRegisterFeign.registerNoticeVar(list);
            if (!result.isSuccess()) {
                log.error("Register workflow variable fail:code:{}，msg:{}", result.getCode(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("Register workflow variable fail:msg:{}", e.getMessage(), e);
        }
    }

    private WfMetaNoticeVarDto singleNoticeParameter(String funCode, String name, String code) {
        WfMetaNoticeVarDto.WfMetaNoticeVarDtoBuilder builder = WfMetaNoticeVarDto.builder()
                .name(name)
                .code(code)
                .funCode(funCode)
                .type(PropertyDataType.String.name())
                .url(callbackUrl)
                .serviceName(appName);
        return builder.build();
    }
}
