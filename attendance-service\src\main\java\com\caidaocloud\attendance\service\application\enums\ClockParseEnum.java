package com.caidaocloud.attendance.service.application.enums;

public enum ClockParseEnum {

    ONE_TIME_CARD(1, "一次卡"),
    TWO_TIME_CARD(2, "二次卡"),
    NO_CARD(3, "不打卡");

    private Integer index;

    private String name;

    ClockParseEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ClockParseEnum c : ClockParseEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
