package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;

import java.util.List;
import java.util.Map;

/**
 * 员工排班查询服务
 *
 * <AUTHOR>
 * @Date 2025/2/15
 */
public interface IScheduleQueryService {
    /**
     * 批量查询多个员工的排班信息
     *
     * @param belongOrgId
     * @param startDate
     * @param endDate
     * @param empIds
     * @return
     */
    List<WaShiftDo> getEmpCalendarShiftList(String belongOrgId, Long startDate, Long endDate, List<Long> empIds);

    /**
     * 批量查询多个员工的排班信息
     *
     * @param belongOrgId
     * @param startDate
     * @param endDate
     * @param empIds
     * @return
     */
    Map<String, WaShiftDo> getEmpCalendarShiftMap(String belongOrgId, Long startDate, Long endDate, List<Long> empIds);

    /**
     * 申请部分销假时-根据休假日期查询班次信息
     *
     * @param tenantId
     * @param leaveId
     * @param leaveCancelDate
     * @param leaveCancelType
     * @return
     */
    List<WaShiftDo> getEmpShiftForLeaveCancel(String tenantId, Integer leaveId, Long leaveCancelDate, Integer leaveCancelType);
}
