package com.caidaocloud.attendance.service.application.event.subscribe;

import com.caidaocloud.msg.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EmpHireLeaveChangeSubscriber implements MessageHandler<EntityEvent> {
    //
    // @Autowired
    // private AllSubordinateService allSubordinateService;

    @Override
    public String topic() {
        return "OnboardingSubordinateChange";
    }

    @Override
    public void handle(EntityEvent message) throws Exception {
        try{
            allSubordinateService.onboardingEmpWorkInfoChange(message.getData());
        }catch (Exception e){
            log.error("更新全部下属异常，" + FastjsonUtil.toJson(message), e);
        }
    }
}
