package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.sdk.dto.SdkWorkflowRevokeDTO;
import com.caidaocloud.attendance.sdk.feign.fallback.TravelFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = TravelFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "workflowRevokeFeignClient")
public interface IWorkflowRevokeFeignClient {
    /**
     * 撤销工作流
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/workflowRevoke/v1/revoke")
    Result<?> revoke(@RequestBody SdkWorkflowRevokeDTO dto);
}
