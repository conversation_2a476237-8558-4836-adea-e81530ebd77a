package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWaGroupRepository;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 考勤方案
 *
 * <AUTHOR>
 * @Date 2021/8/3
 */
@Slf4j
@Data
@Service
public class WaGroupDo {
    private Integer waGroupId;
    private Integer overtimeGroupId;
    private String waGroupName;
    private Integer leaveGroupId;
    private String belongOrgid;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private Object otTypeIds;
    private Object leaveTypeIds;
    private String leaveTypeNames;
    private String otTypeNames;
    private Boolean isDefault;
    private Integer cyleType;
    private Integer cyleStartdate;
    private Integer parseGroupId;
    private Integer cyleMonth;
    private Integer modifyPeriodMonth;
    private Integer monthPhase;
    private Integer modifyMonthDay;
    private Boolean isSetOvertimeLimit;
    private Integer overtimeLimit;
    private Integer timescale;
    private Boolean isOpenOtlimitWarn;
    private Integer otToplimit;
    private Boolean isOpenPreOt;
    private Boolean singleOvertimeType;
    private BigDecimal workingTime;
    private Boolean reasonMust;
    private Boolean autoPeriod;
    private Integer periodCycleMonth;
    private Integer periodStartDate;
    private Boolean overtimeControl;
    private Boolean isOpenTimeControl;
    private Integer timeControlType;
    private Float controlTimeDuration;
    private Integer controlTimeUnit;
    private Integer calendarDataRange;
    private Integer leaveStatus;
    private Integer overtimeBelong;
    /**
     * 是否允许选择加班归属日期: true 允许 false 不允许
     */
    private Boolean selectOvertimeDate;

    @Autowired
    private IWaGroupRepository waGroupRepository;

    public List<WaGroupDo> getWaGroupList(String belongOrgId) {
        return waGroupRepository.getWaGroupList(belongOrgId);
    }

    public WaGroupDo getById(Integer waGroupId) {
        return waGroupRepository.selectById(waGroupId);
    }

    public void updateGroupExpCondition(Integer waGroupId, String groupExpCondition) {
        waGroupRepository.updateGroupExpCondition(waGroupId, groupExpCondition);
    }
}
