package com.caidaocloud.attendance.service.application.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/7/28
 */
@Data
public class QuotaGenDto {
    private String belongId;
    private Integer waGroupId;
    private Long empid;
    private Short year;
    // 是否生成全部：true 是（已生成的拿最新数据做覆盖）、false 否（只生成未生成过的数据，已生成的不做处理）
    private Boolean all;
    private Long userId;
    private String dataScope;
    private Integer type;

    /**
     * 1:生成新员工配额2:更新离职人员配额3:更新全员配额
     */
    private Integer genQuotaMode;

    private Long corpId;
}
