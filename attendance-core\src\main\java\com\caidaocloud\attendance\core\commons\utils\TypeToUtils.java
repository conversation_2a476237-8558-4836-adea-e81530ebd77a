package com.caidaocloud.attendance.core.commons.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.postgresql.util.PGobject;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * 类型转换工具类
 * <br>创建日期：2016年1月20日
 * <br><b>Copyright 2016 UTOUU All Rights Reserved</b>
 * <AUTHOR> ding peng
 * @since 1.0
 * @version 1.0
 */
public class TypeToUtils {
	/**
	 * 字符串转Map
	 * @since 1.0 
	 * @param object
	 * @return
	 * <br><b>作者： <AUTHOR> ding peng </b>
	 * <br>创建时间：2016年1月20日 下午4:40:48
	 */
	public static HashMap<String, Object> strToHashMap(Object object) {
		HashMap<String, Object> data = new HashMap<String, Object>();
		// 将json字符串转换成jsonObject
		JSONObject jsonObject = JSONObject.fromObject(object);
		Iterator it = jsonObject.keys();
		// 遍历jsonObject数据，添加到Map对象
		while (it.hasNext()) {
			String key = String.valueOf(it.next());
			data.put(key, jsonObject.get(key));
		}
		return data;
	}


	/**
	 * transBeanToString(Bean转字符串)
	 * @Title: transBeantoString
	 * @Description: TODO
	 * @param obj
	 * @return 传入参数
	 */
	public static String transBeanToString(Object obj){
		return JSONObject.fromObject(obj).toString();
	}
	
	/**
	 * json 转 List 集合
	 * @since 1.0 
	 * @param json
	 * @param clazz
	 * @return
	 * <br><b>作者： <AUTHOR> ding peng </b>
	 * <br>创建时间：2016年7月20日 下午12:26:24
	 */
	public static List jsonToList(String json , Class clazz){
		JSONArray jsonArray = JSONArray.fromObject(json);  
		return (List) JSONArray.toCollection(jsonArray,clazz);
	}
	
	/**
	 * 
	 * @since 1.0 
	 * @param list
	 * @return
	 * <br><b>作者： <AUTHOR>
	 * <br>创建时间：2015年11月10日 下午3:40:02
	 */
	@SuppressWarnings("rawtypes")
	public static String transListToString(List list){
		return JSONArray.fromObject(list).toString(); 
	}
	
	/**
	 * 
	 * @since 1.0 
	 * @param list
	 * @return
	 * <br><b>作者： <AUTHOR>
	 * <br>创建时间：2015年11月10日 下午3:40:02
	 */
	@SuppressWarnings("rawtypes")
	public static String transListToString(Object list){
		return JSONArray.fromObject(list).toString(); 
	}
	
	/**
	 * transStringToBean(字符串转Bean)
	 * @Title: transStringtoBean
	 * @Description: TODO
	 * @param json
	 * @param clazz
	 * @return 传入参数
	 */
	public static Object transStringToBean(String json , Class clazz){
		return net.sf.json.JSONObject.toBean(net.sf.json.JSONObject.fromObject(json), clazz);
	}

	/** 
     * 将一个 Map 对象转化为一个 JavaBean 
     * @param type 要转化的类型 
     * @param map 包含属性值的 map 
     * @return 转化出来的 JavaBean 对象 
     * @throws IntrospectionException 
     *             如果分析类属性失败 
     * @throws IllegalAccessException 
     *             如果实例化 JavaBean 失败 
     * @throws InstantiationException 
     *             如果实例化 JavaBean 失败 
     * @throws InvocationTargetException 
     *             如果调用属性的 setter 方法失败 
     */ 
    public static Object mapToBean(Class type, Map map) throws IntrospectionException, IllegalAccessException,
            InstantiationException, InvocationTargetException { 
        BeanInfo beanInfo = Introspector.getBeanInfo(type); // 获取类属性 
        Object obj = type.newInstance(); // 创建 JavaBean 对象 

        // 给 JavaBean 对象的属性赋值 
        PropertyDescriptor[] propertyDescriptors =  beanInfo.getPropertyDescriptors(); 
        for (int i = 0; i< propertyDescriptors.length; i++) { 
            PropertyDescriptor descriptor = propertyDescriptors[i]; 
            String propertyName = descriptor.getName(); 

            if (map.containsKey(propertyName)) { 
				try{
					Object value = map.get(propertyName);
					Object[] args = new Object[1];
					args[0] = value;
					descriptor.getWriteMethod().invoke(obj, args);
				}catch (Exception e){
					e.printStackTrace();
				}
            } 
        } 
        return obj; 
    } 

    /** 
     * 将一个 JavaBean 对象转化为一个  Map 
     * @param bean 要转化的JavaBean 对象 
     * @return 转化出来的  Map 对象 
     * @throws IntrospectionException 如果分析类属性失败 
     * @throws IllegalAccessException 如果实例化 JavaBean 失败 
     * @throws InvocationTargetException 如果调用属性的 setter 方法失败 
     */ 
    public static Map beanToMap(Object bean) 
            throws IntrospectionException, IllegalAccessException, InvocationTargetException { 
        Class type = bean.getClass(); 
        Map returnMap = new HashMap(); 
        BeanInfo beanInfo = Introspector.getBeanInfo(type); 

        PropertyDescriptor[] propertyDescriptors =  beanInfo.getPropertyDescriptors(); 
        for (int i = 0; i< propertyDescriptors.length; i++) { 
            PropertyDescriptor descriptor = propertyDescriptors[i]; 
            String propertyName = descriptor.getName(); 
            if (!propertyName.equals("class")) { 
                Method readMethod = descriptor.getReadMethod(); 
                Object result = readMethod.invoke(bean, new Object[0]); 
                if (result != null) { 
                    returnMap.put(propertyName, result); 
                } else { 
                    returnMap.put(propertyName, ""); 
                } 
            } 
        } 
        return returnMap; 
    }
    
    /**
     * 对象转String
     * @since 1.0 
     * @param obj
     * @return
     * <br><b>作者： <AUTHOR> ding peng </b>
     * <br>创建时间：2016年2月22日 下午1:45:56
     */
    public static String parseString(Object obj){
    	return obj == null || obj.equals("")? null : obj.toString();
    }
    
    /**
     * 对象转int
     * @since 1.0 
     * @param obj
     * @return
     * <br><b>作者： <AUTHOR> ding peng </b>
     * <br>创建时间：2016年2月22日 下午1:46:41
     */
    public static Integer parseInt(Object obj){
    	return obj == null ? null : new BigDecimal(obj.toString()).intValue();
    }
    
    /**
     * 将字母转换成数字,获取总数
     * @since 1.0 
     * @param input
     * <br><b>作者： <AUTHOR> ding peng </b>
     * <br>创建时间：2016年2月23日 上午9:01:40
     * @return 总数
     */
    public static int letterToNum(String input) {
    	int total = 0;
        for (byte b : input.getBytes()) {
        	total += b - 96;
        } 
        return total;
    }
    
    /**
	 * 使用java正则表达式去掉多余的.与0 
	 * @since 1.0 
	 * @param obj
	 * @return
	 * <br><b>作者： <AUTHOR> ding peng </b>
	 * <br>创建时间：2016年2月25日 下午5:48:16
	 */
    public static String subZeroAndDot(Object obj){
    	String content = obj.toString();
        if(content.indexOf(".") > 0){  
        	content = content.replaceAll("0+?$", "");//去掉多余的0  
            content = content.replaceAll("[.]$", "");//如最后一位是.则去掉  
        }  
        return content;  
    }

	public static Map convertObjToMap(Object obj){
		Map configMap = null;
		try {
			if(obj != null) {
				if (obj instanceof String) {
					String str = (String) obj;
					if (StringUtils.isNotEmpty(str)) {
						configMap = (new ObjectMapper()).readValue(str, Map.class);
					}
				} else if (obj instanceof Map) {
					configMap = (Map) obj;
				} else if (obj instanceof PGobject) {
					PGobject pgobject = (PGobject) obj;
					configMap = (new ObjectMapper()).readValue(pgobject.getValue(), Map.class);
				}
			}
		}catch (Exception e){
    		e.printStackTrace();
		}
		return configMap;
	}
}
