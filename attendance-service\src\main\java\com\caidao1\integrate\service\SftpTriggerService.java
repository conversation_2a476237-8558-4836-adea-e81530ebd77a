package com.caidao1.integrate.service;

import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.utils.EmailTmplateMessage;
import com.caidao1.commons.utils.HtmlMailUtil;
import com.caidao1.commons.utils.SftpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;
import java.util.Map;

@Service
public class SftpTriggerService implements ScriptBindable {
    private static final Logger logger = LoggerFactory.getLogger(SftpTriggerService.class);
    @Autowired
    private HtmlMailUtil sendMailUtil;

    /**
     * ftp 文件移动
     * @param mapJson
     * @throws Exception
     */
    public void sftpRenameFile(Map<String, Object> mapJson) throws Exception {
        SftpUtil client = new SftpUtil();
        client.setServer((String) mapJson.get("sftp.server"));
        client.setPort((Integer) mapJson.get("sftp.port"));
        client.setLogin((String) mapJson.get("sftp.login"));
        client.setPassword((String) mapJson.get("sftp.password"));
        client.connect();
        List<String> files = client.lsFolder((String) mapJson.get("sftp.sources"), (String) mapJson.getOrDefault("sftp.filter", ""));
        for (String file : files) {
            try {
                client.rename((String) mapJson.get("sftp.sources"),mapJson.get("sftp.sources") + File.separator + file,mapJson.get("sftp.sources.backup") + File.separator + file);
            } catch (Exception e) {
                try {
                    client.rename((String) mapJson.get("sftp.sources"),mapJson.get("sftp.sources") + File.separator + file,mapJson.get("sftp.sources.backup") + File.separator + file);
                } catch (Exception e2) {
                    try {
                        EmailTmplateMessage tmplateMessage = new EmailTmplateMessage();
                        tmplateMessage.setTo("<EMAIL>");
                        tmplateMessage.setCc("<EMAIL>,<EMAIL>,<EMAIL>");
                        tmplateMessage.setSubject("sftp移动文件报错");
                        StringWriter sw = new StringWriter();
                        PrintWriter pw = new PrintWriter(sw);
                        e.printStackTrace(pw);
                        tmplateMessage.setContent("<p font-size='20px'>sftp.server:" + client.getServer() + "</p><br/><br/>" + sw.toString() + "<br/> 文件名路径：" + file);
                        sendMailUtil.send2(tmplateMessage);
                    } catch (Exception e1) {
                        e1.printStackTrace();
                    }
                }
            }
        }
    }

}
