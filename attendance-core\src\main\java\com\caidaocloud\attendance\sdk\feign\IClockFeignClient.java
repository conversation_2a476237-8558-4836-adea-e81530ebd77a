package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.core.wa.dto.clock.ClockAnalyseDto;
import com.caidaocloud.attendance.sdk.feign.fallback.ClockFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 打卡
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = ClockFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "clockFeignClient")
public interface IClockFeignClient {
    /**
     * 打卡分析
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/attendance/register/v1/analyse")
    Result<Boolean> analyseRegisterRecord(@RequestBody ClockAnalyseDto dto);
}
