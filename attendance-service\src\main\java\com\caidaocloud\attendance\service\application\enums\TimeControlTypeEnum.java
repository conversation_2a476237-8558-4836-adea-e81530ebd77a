package com.caidaocloud.attendance.service.application.enums;

public enum TimeControlTypeEnum {
    ADVANCE(0, "提前"),
    DELAY(1, "延后");

    private Integer index;
    private String name;

    TimeControlTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (TimeControlTypeEnum c : TimeControlTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
