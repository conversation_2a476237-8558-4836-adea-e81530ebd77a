package com.caidaocloud.attendance.service.application.enums;

import com.caidao1.commons.utils.SpringUtils;
import com.caidaocloud.attendance.service.application.service.ICommonService;

/**
 * 流程应用
 *
 * @Author: <PERSON><PERSON>
 * @Date: 2021/6/10 10:46
 * @Description:
 **/
public enum FuncTypeEnum {
    LEAVE(1, "ATTENDANCE-LEAVE", "请假", "leave") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleLeaveWorkflowEvent(Integer.valueOf(key.toString()), eventTime);
        }
    },
    BATCH_LEAVE(105, "ATTENDANCE-BATCH-LEAVE", "批量休假", "batch-leave") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleTravelWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    BATCH_OVERTIME(211, "ATTENDANCE-BATCH-OVERTIME", "批量加班", "batch-overtime") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleTravelWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    BATCH_ANALYSE_ADJUST(212, "ATTENDANCE-BATCH-ANALYSE-ADJUST", "批量考勤异常申请", "batch-analyse-adjust") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleTravelWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    OVERTIME(2, "ATTENDANCE-OVERTIME", "加班", "overtime") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleOvertimeWorkflowEvent(Integer.valueOf(key.toString()), eventTime);
        }
    },
    CARD_REPLACEMENT(41, "ATTENDANCE-REGISTER", "补打卡", "bdk") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleBdkWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    TRAVEL(45, "ATTENDANCE-TRAVEL", "出差", "travel") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleTravelWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    BATCH_TRAVEL(106, "ATTENDANCE-BATCH-TRAVEL", "批量出差", "batch-travel") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleTravelWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    SHIFT(107, "ATTENDANCE-SHIFT", "调班", "shift") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleShiftWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    LEAVE_CANCEL(108, "ATTENDANCE-VACATION", "销假", "cancel") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleLeaveCancelWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    COMPENSATORY(109, "ATTENDANCE-COMPENSATORY", "调休付现", "compensatory") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleCompensatoryWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    OVERTIME_REVOKE(110, "ATTENDANCE-OVERTIME-REVOKE", "加班撤销", "overtime_revoke") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleOvertimeRevokeWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    OVERTIME_ABOLISH(111, "ATTENDANCE-OVERTIME-ABOLISH", "加班废止", "overtime_abolish") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleOvertimeAbolishWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    TRAVEL_REVOKE(112, "ATTENDANCE-TRAVEL-REVOKE", "出差撤销", "travel_revoke") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleTravelRevokeWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    TRAVEL_ABOLISH(113, "ATTENDANCE-TRAVEL-ABOLISH", "出差废止", "travel_abolish") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleTravelAbolishWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    LEAVE_EXTENSION(114, "ATTENDANCE-LEAVE-EXTENSION", "假期延期", "leave_extension") {
        @Override
        public void handleEvent(Object key, Long eventTime) {
            SpringUtils.getBean(ICommonService.class).handleLeaveExtensionWorkflowEvent(Long.valueOf(key.toString()), eventTime);
        }
    },
    GO_OUT(45, "ATTENDANCE-TRAVEL", "出差", "") {
        @Override
        public void handleEvent(Object key, Long eventTime) {

        }
    },
    PUNCH_IN(41, "ATTENDANCE-REGISTER", "补卡", "") {
        @Override
        public void handleEvent(Object key, Long eventTime) {

        }
    },
    SHIFT_CHANGE(103, "ATTENDANCE-SHIFT", "调班", "") {
        @Override
        public void handleEvent(Object key, Long eventTime) {

        }
    };

    private Integer index;
    private String name;
    private String desc;
    private String code;

    FuncTypeEnum(Integer index, String name, String desc, String code) {
        this.index = index;
        this.name = name;
        this.desc = desc;
        this.code = code;
    }

    public static FuncTypeEnum getFuncTypeEnum(Integer index) {
        for (FuncTypeEnum one : FuncTypeEnum.values()) {
            if (one.getIndex().equals(index)) {
                return one;
            }
        }
        return null;
    }

    public static FuncTypeEnum getFuncCodeEnum(String name) {
        for (FuncTypeEnum one : FuncTypeEnum.values()) {
            if (one.getName().equals(name)) {
                return one;
            }
        }
        return null;
    }

    public static FuncTypeEnum getFuncCodeByName(String name) {
        for (FuncTypeEnum one : FuncTypeEnum.values()) {
            if (one.name().equals(name)) {
                return one;
            }
        }
        return null;
    }

    public abstract void handleEvent(Object key, Long eventTime);

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
