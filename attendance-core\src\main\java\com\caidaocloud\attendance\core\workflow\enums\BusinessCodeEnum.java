package com.caidaocloud.attendance.core.workflow.enums;

import org.apache.commons.lang.StringUtils;

/**
 * 工作流流程应用
 */
public enum BusinessCodeEnum {
    LEAVE("ATTENDANCE-LEAVE", "请假", "wa_emp_leave", "leave_id","休假管理-休假记录"),
    OVERTIME("ATTENDANCE-OVERTIME", "加班", "wa_emp_overtime", "ot_id",""),
    REGISTER("ATTENDANCE-REGISTER", "补打卡", "wa_register_record_bdk", "record_id",""),
    TRAVEL("ATTENDANCE-TRAVEL", "出差", "wa_emp_travel", "travel_id","出差管理-出差记录"),
    BATCH_TRAVEL("ATTENDANCE-BATCH-TRAVEL", "批量出差", "wa_batch_travel", "batch_travel_id",""),
    BATCH_LEAVE("ATTENDANCE-BATCH-LEAVE", "批量休假", "wa_batch_leave", "batch_id",""),
    BATCH_OVERTIME("ATTENDANCE-BATCH-OVERTIME", "批量加班", "wa_batch_overtime", "batch_id",""),
    BATCH_ANALYSE_ADJUST("ATTENDANCE-BATCH-ANALYSE-ADJUST", "批量考勤异常申请", "wa_batch_analyse_result_adjust", "batch_id",""),
    SHIFT("ATTENDANCE-SHIFT", "调班", "wa_shift_apply_record", "rec_id","排班管理-调班记录"),
    VACATION("ATTENDANCE-VACATION", "销假", "wa_emp_leave_cancel", "leave_cancel_id",""),
    COMPENSATORY("ATTENDANCE-COMPENSATORY", "调休付现", "wa_emp_compensatory_case_apply", "id",""),
    OVERTIME_REVOKE("ATTENDANCE-OVERTIME-REVOKE", "加班撤销", "wa_workflow_revoke", "id",""),
    OVERTIME_ABOLISH("ATTENDANCE-OVERTIME-ABOLISH", "加班废止", "wa_workflow_revoke", "id",""),
    TRAVEL_REVOKE("ATTENDANCE-TRAVEL-REVOKE", "出差撤销", "wa_workflow_revoke", "id",""),
    TRAVEL_ABOLISH("ATTENDANCE-TRAVEL-ABOLISH", "出差废止", "wa_workflow_revoke", "id",""),
    LEAVE_EXTENSION("ATTENDANCE-LEAVE-EXTENSION", "假期延期", "wa_leave_extension", "id","");

    private String code;
    private String desc;
    public String table;
    public String bidField;
    public String menu;

    BusinessCodeEnum(String code, String desc, String table, String bidField, String menu) {
        this.code = code;
        this.desc = desc;
        this.table = table;
        this.bidField = bidField;
        this.menu = menu;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getMenu() {
        return menu;
    }

    public void setMenu(String menu) {
        this.menu = menu;
    }

    public static BusinessCodeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (BusinessCodeEnum businessCodeEnum : BusinessCodeEnum.values()) {
            if (code.equals(businessCodeEnum.getCode())) {
                return businessCodeEnum;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }
        for (BusinessCodeEnum businessCodeEnum : BusinessCodeEnum.values()) {
            if (businessCodeEnum.name().equals(name)) {
                return businessCodeEnum.getCode();
            }
        }
        return "";
    }

    public static BusinessCodeEnum getEnumByFunCode(String funCode) {
        if (StringUtils.isBlank(funCode)) {
            return null;
        }
        for (BusinessCodeEnum value : BusinessCodeEnum.values()) {
            if (funCode.equals(value.code)) {
                return value;
            }
        }
        return null;
    }
}
