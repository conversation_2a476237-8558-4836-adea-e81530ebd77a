package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.attendance.service.domain.entity.WorkHoursDetailDo;
import com.caidaocloud.attendance.service.domain.repository.IWorkHoursDetailRepository;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.val;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Data
@TableName(value = "wa_work_hours_detail")
public class WorkHoursDetailPo {

    @TableId
    private String id;

    private String tenantId;

    private String waGroupId;

    private Long empId;

    private Long date;

    /**
     * 标准工时，单位分钟
     */
    private String stdWorkTimeSt;

    /**
     * 夜班天数
     */
    private String nightShiftDaysSt;


    public static List<WorkHoursDetailPo> fromDo(String tenantId, List<WorkHoursDetailDo> hours) {
        return hours.stream().map(hour->{
            WorkHoursDetailPo po = new WorkHoursDetailPo();
            po.setId(SnowUtil.nextId());
            po.setDate(hour.getDate());
            po.setEmpId(hour.getEmpId());
            po.setTenantId(tenantId);
            po.setWaGroupId(hour.getWaGroupId());
            po.setStdWorkTimeSt(hour.getStdWorkTime().stripTrailingZeros().toPlainString());
            po.setNightShiftDaysSt(hour.getNightShiftDays().stripTrailingZeros().toPlainString());
            return po;
        }).collect(Collectors.toList());
    }
}
