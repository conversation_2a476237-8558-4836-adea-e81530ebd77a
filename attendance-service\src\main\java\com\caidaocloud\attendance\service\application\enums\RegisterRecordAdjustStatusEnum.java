package com.caidaocloud.attendance.service.application.enums;

/**
 * 打卡记录表-调整状态
 */
public enum RegisterRecordAdjustStatusEnum {
    UNADJUSTED(0, "未调整"),
    ADJUSTED(1, "已调整");

    private Integer index;
    private String name;

    RegisterRecordAdjustStatusEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (RegisterRecordAdjustStatusEnum c : RegisterRecordAdjustStatusEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return "-";
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
