package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.system.mybatis.mapper.SysWifiInfoMapper;
import com.caidao1.system.mybatis.model.SysWifiInfo;
import com.caidao1.system.mybatis.model.SysWifiInfoExample;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class WifiService {
    @Autowired
    private SysWifiInfoMapper sysWifiInfoMapper;

    public List<SysWifiInfo> getWifiListByIds(List<Integer> ids){
        if(CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }

        SysWifiInfoExample example = new SysWifiInfoExample();
        example.createCriteria().andWifiIdIn(ids);
        return sysWifiInfoMapper.selectByExample(example);
    }
}
