package com.caidaocloud.attendance.service.application.feign;

import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.dto.EmpGroup;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.masterdata.dto.EmpWorkInfoVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "${feign.rename.caidaocloud-masterdata-service:caidaocloud-masterdata-service}", fallback = MasterFeignFallBack.class, configuration = FeignConfiguration.class)
public interface MasterFeignClient {
    @GetMapping("/api/masterData/empGroup/v1/businessKey")
    Result<EmployeeGroupDto> getEmployeeGroup(@RequestParam("businessKey") String businessKey,
        @RequestParam("groupType") String groupType);

    @PostMapping("/api/masterData/empGroup/v1/businessKey")
    Result<EmployeeGroupDto> saveEmployeeGroup(@RequestBody EmployeeGroupDto empGroupDto);

    @PostMapping("/api/masterData/empGroup/v1/businessKey")
    Result<EmployeeGroupDto> saveOrUpdate(@RequestBody EmployeeGroupDto empGroupDto);

    @GetMapping("/api/masterData/empGroup/v1/detail")
    Result<EmployeeGroupDto> getEmployeeGroupDetail(@RequestParam("empGroupId") Long empGroupId);

    @DeleteMapping("/api/masterData/empGroup/v1/removeBusKey")
    Result removeBusKey(@RequestParam("businessKey") String businessKey, @RequestParam("groupType") String groupType);

    @PostMapping("/api/masterData/empGroup/v1/businessKeys")
    Result<List<EmployeeGroupDto>> detailByKeysAndType(@RequestBody EmpGroup empGroup);

    @GetMapping({"/api/masterdata/v1/web/metadata/property/enum"})
    Result<List<KeyValue>> fetchModelPropertyEnum(@RequestParam("identifier") String identifier, @RequestParam String property);

    @GetMapping("/api/masterdata/v2/emp/empInfo")
    Result<EmpWorkInfoVo> loadEmpInfo(@RequestParam("empId") String empId);
}
