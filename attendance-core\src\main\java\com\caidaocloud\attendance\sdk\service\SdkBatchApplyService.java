package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.sdk.dto.SdkRevokeBatchAnalyseResultAdjustDto;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeBatchLeaveDto;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeBatchOvertimeDto;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeEmpTraveDto;
import com.caidaocloud.attendance.sdk.feign.IBatchApplyFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 批量申请
 *
 * <AUTHOR>
 * @Date 2024/8/8
 */
@Slf4j
@Service
public class SdkBatchApplyService {
    @Autowired
    private IBatchApplyFeignClient batchApplyFeignClient;

    public Result<?> revokeBatchLeave(SdkRevokeBatchLeaveDto dto) {
        return batchApplyFeignClient.revokeBatchLeave(dto);
    }

    public Result<?> revokeBatchOt(SdkRevokeBatchOvertimeDto dto) {
        return batchApplyFeignClient.revokeBatchOt(dto);
    }

    public Result<?> revokeBatchTravel(SdkRevokeEmpTraveDto dto) {
        return batchApplyFeignClient.revokeBatchTravel(dto);
    }

    public Result<?> revokeAnalyseResultAdjust(SdkRevokeBatchAnalyseResultAdjustDto dto) {
        return batchApplyFeignClient.revokeAnalyseResultAdjust(dto);
    }
}
