package com.caidaocloud.attendance.core.mobile.filter;

import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.DesUtils;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.ReturnMessage;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.mobile.bean.PlatformEnum;
import com.caidao1.mobile.bean.SessionBean;
import com.caidaocloud.attendance.core.commons.service.TokenService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.PrintWriter;
import java.lang.reflect.Method;
import java.util.Locale;

@Component
public class MobUserAccessInterceptor extends HandlerInterceptorAdapter {

	private static TokenService tokenService;

	private static MessageResource messageResource;

	@Autowired
	public void setTokenService(TokenService service) {
		MobUserAccessInterceptor.tokenService = service;
	}

	@Autowired
	public void setMessageResource(MessageResource messageResource) {
		MobUserAccessInterceptor.messageResource = messageResource;
	}

	public static boolean verifyToken(SessionBean sessionBean, HttpServletRequest request,
									  HttpServletResponse response) throws Exception {
		response.setCharacterEncoding("UTF-8");
		response.setContentType("application/json; charset=utf-8");
		HttpSession session = request.getSession();
		ReturnMessage rt = new ReturnMessage(1,"success");
		String tokenId = request.getHeader("token_id");

		if(StringUtils.isBlank(tokenId)){
			rt.setStatus(-1);
			rt.setMessage(messageResource.getMessage("L005574",new Object[]{},new Locale(sessionBean.getLanguage())));
			PrintWriter out = response.getWriter();
			out.append(JSONUtils.ObjectToJson(rt));
			return false;
		}else{
			Long userid = tokenService.getUserId(request);
			Long empid = tokenService.getEmpId(request);
			if(userid ==  null){
				rt.setStatus(-9);
				rt.setMessage("Session Lost");
			}else if(empid == null){
				rt.setStatus(-1);
				rt.setMessage(messageResource.getMessage("L005575",new Object[]{},new Locale(sessionBean.getLanguage())));
			}
			if(rt.getStatus()==1) {
				return true;
			}else{
				String tokensid = request.getParameter("tokensid");
				if(!StringUtils.isBlank(tokensid)){
					try {
						//密钥 wechart_mobile 解密的时候保持一致
						DesUtils des = new DesUtils("wechart_mobile");
//						empInfo.getEmpid()+"_"+userInfo.getUserid()+"_"+empInfo.getCorpid()+"_"+belongOrgId+"_"+DateUtil.getCurrentTime(true);
						String str = des.decrypt(tokensid);
						String[] arr = str.split("_");
						session.setAttribute("empid", Integer.valueOf(arr[0]));
						session.setAttribute("userid",  Integer.valueOf(arr[1]));
						session.setAttribute("corpid", Integer.valueOf(arr[2]));
						session.setAttribute("belongid", Integer.valueOf(arr[3]));
						long date = Long.valueOf(arr[4]);
						long current  = DateUtil.getCurrentTime(true);
						long s = (current-date)/60/60/24;
						if(s>7){
							PrintWriter out = response.getWriter();
							rt.setMessage(messageResource.getMessage("L005576",new Object[]{},new Locale(sessionBean.getLanguage())));
							rt.setStatus(-1);
							out.append(JSONUtils.ObjectToJson(rt));
							return false;
						}
						return true;
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
				PrintWriter out = response.getWriter();
				out.append(JSONUtils.ObjectToJson(rt));
				return false;
			}
		}
	}

	@Override
	public boolean preHandle(HttpServletRequest request,
			HttpServletResponse response, Object handler) throws Exception  {
		HandlerMethod handlerMethod = (HandlerMethod) handler;
		Method method = handlerMethod.getMethod();
		SessionBean sessionBean = tokenService.getSessionBean(request);
		MobAccessRequired annotation = method.getAnnotation(MobAccessRequired.class);
		if (annotation != null) {
			Boolean flag = verifyToken(sessionBean, request, response);
			if(flag){
				// 正常返回才把sessionBean设置到RequestContextHolder中
				sessionBean.setPlatformType(PlatformEnum.MOBILE);
				SessionHolder.setSessionBean(sessionBean);
			}
			return flag;
		}
		return super.preHandle(request, response, handler);
	}

	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
		SessionHolder.reSetSessionBean();
	}
}
