package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.domain.entity.WaWorkRoundDo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/1
 */
public interface IWorkRoundRepository {
    WaWorkRoundDo selectById(Integer id);

    int save(WaWorkRoundDo workRoundDo);

    int updateById(WaWorkRoundDo workRoundDo);

    int deleteById(Integer id);

    List<WaWorkRoundDo> getWorkRoundListByIds(List<Integer> ids);

    int getRoundCountCountByName(String belongOrgId, Integer excludeId, String name);

    PageList<WaWorkRoundDo> getWorkRoundPageList(PageBean pageBean, String belongId);
}
