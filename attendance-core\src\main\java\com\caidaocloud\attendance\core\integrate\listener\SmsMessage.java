package com.caidaocloud.attendance.core.integrate.listener;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class SmsMessage implements Serializable {
	private String mobile;
	private String sign;
	private String templateCode;
	private Map<String, String> params = new HashMap<String, String>();
	private String content; 
	 
	 
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getSign() {
		return sign;
	}
	public void setSign(String sign) {
		this.sign = sign;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public String getTemplateCode() {
		return templateCode;
	}
	public void setTemplateCode(String templateCode) {
		this.templateCode = templateCode;
	}
	public Map<String, String> getParams() {
		return params;
	}
	public void setParams(Map<String, String> params) {
		this.params = params;
	}
	public void addParam(String key,String value) {
		if(params==null)params = new HashMap<String, String>();
		this.params.put(key, value);
	}
}
