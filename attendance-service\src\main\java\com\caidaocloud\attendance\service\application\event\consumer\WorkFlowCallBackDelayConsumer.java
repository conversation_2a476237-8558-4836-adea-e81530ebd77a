package com.caidaocloud.attendance.service.application.event.consumer;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.dto.workflow.WfCallbackResultExtDto;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.mq.rabbitmq.consumer.AbsMQConsumer;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 工作流回调
 */
@Component
@Slf4j
public class WorkFlowCallBackDelayConsumer extends AbsMQConsumer {
    @Autowired
    private WorkflowCallBackService workflowCallBackService;

    @Override
    public void process(String message) {
        log.info("WorkFlowCallBackDelayConsumer.process message={}", message);
        WfCallbackResultExtDto callbackResultDto = FastjsonUtil.toObject(message, WfCallbackResultExtDto.class);
        if (null == callbackResultDto) {
            log.warn("WorkFlowCallBackDelayConsumer.process fail messageDto empty");
            return;
        }
        try {
            UserInfo userInfo = callbackResultDto.getUserInfo();
            log.debug("WorkFlowCallBackDelayConsumer.process userInfo={}", FastjsonUtil.toJsonStr(userInfo));
            UserContext.doInitSecurityUserInfo(userInfo.getTenantId(),
                    null != userInfo.getUserId() ? userInfo.getUserId().toString() : null,
                    null != userInfo.getStaffId() ? userInfo.getStaffId().toString() : null,
                    null, null, null);
            workflowCallBackService.saveBatchLeaveApproval(callbackResultDto);
        } catch (Exception ex) {
            log.error("WorkFlowCallBackDelayConsumer.process fail msg={}", ex.getMessage(), ex);
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }
}
