package com.caidaocloud.attendance.service.application.service;

import com.caidao1.wa.dto.EmpCalendarInfoDto;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpRelCalendarQueryDto;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.EmpShiftInfoDto.EmpShiftInfoDto;
import com.caidaocloud.attendance.service.interfaces.dto.user.MyCalendarDateDto;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;

import java.util.List;
import java.util.Map;

/**
 * 工作日历
 *
 * <AUTHOR>
 * @Date 2021/3/4
 */
public interface IWorkCalendarService {
    String saveOrUpdateWorkCalendar(WaWorktimeDto waWorktimeDto, UserInfo userInfo) throws Exception;

    WaWorktimeDto getWorkCalendarById(Integer id, Boolean queryGroupExp);

    void deleteWorkCalendarById(Integer id);

    List<EmpShiftVerifyDto> verifySelectedEmployees(EmpShiftVerifyReqDto reqDto);

    AttendancePageResult<EmpShiftDto> getEmpShiftListByCalendarId(EmpShiftReqDto reqDto, UserInfo userInfo);

    List getWorkCalendarDetailList(Integer workCalendarId, String start, String end) throws Exception;

    List getEmpCalendarDetailListByYm(String belongOrgId, Long empId, String start, String end) throws Exception;

    AttendancePageResult getWaShiftList(WorkCalendarReqDto workCalendarReqDto);

    List<MyCalendarDateDto> getMyShopCalendar(Integer searchMonth);

    UserInfo checkSession();

    Result<Boolean> saveEmpShift(EmpShiftInfoDto dto);

    void deleteEmpShift(Integer empShiftId);

    EmpShiftDto getEmpShift(Integer empShiftId);

    List<KeyValue> getCalendarOptions();

    void synchronizeEmpShift(String belongOrgId, Long userId, Long corpId);

    boolean checkWorkCalendarIfAssigned(Integer workCalendarId);

    void synchronizeEmpShift();

    void deleteEmpShifts(List<Integer> ids);

    List<EmpCalendarInfoDto> getEmpCalendarList(ListEmpRelCalendarQueryDto queryDto);

    /**
     * 固定班次员工排班查询
     *
     * @param belongOrgId
     * @param startDate
     * @param endDate
     * @param empIds
     * @return
     */
    List<WaShiftDo> getEmpCalendarShiftList(String belongOrgId, Long startDate, Long endDate, List<Long> empIds);

    List<Map> getEmpCalendarShiftList(String belongOrgId, Long startDate, Long endDate, Long empId, String dataScope);

    List<Map> getChangeShiftDefListByEmpId(String belongOrgId, Long empId, Long startDate, Long endDate);
}
