package com.caidaocloud.attendance.service.application.dto.transfer;

import com.caidaocloud.hrpaas.core.metadata.enums.PropertyDataType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@Slf4j
public class TransferFieldDetailDto implements Serializable {

    @ApiModelProperty("字段类型")
    private String type;

    @ApiModelProperty("字段code")
    private String property;

    @ApiModelProperty("原始value")
    private Object before;

    @ApiModelProperty("原始文字")
    private String beforeTxt;

    @ApiModelProperty("异动value")
    private Object after;

    @ApiModelProperty("异动文字")
    private String afterTxt;

    @ApiModelProperty("是否启用异动")
    private boolean enable;

    @ApiModelProperty("数据类型")
    private PropertyDataType propertyDataType;

    public String fetchBeforeValue() {
        return fetchValue(before, beforeTxt);
    }

    public String fetchAfterValue() {
        return fetchValue(after, afterTxt);
    }

    private String fetchValue(Object value, String txt) {
        if (value == null) {
            return null;
        }
        if (propertyDataType == PropertyDataType.Dict || propertyDataType == PropertyDataType.Enum || propertyDataType == PropertyDataType.Timestamp) {
            return value.toString();
        }
        if (txt == null) {
            log.warn("Try fetch txt value but found null,value={}", value);
            return propertyDataType == PropertyDataType.String ? value.toString() : "";
        }
        return txt;
    }
}
