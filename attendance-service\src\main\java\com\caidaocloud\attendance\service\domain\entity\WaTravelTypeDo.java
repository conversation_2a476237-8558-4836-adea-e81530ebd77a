package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWaTravelTypeRepository;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTypeListReqDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Data
@Service
public class WaTravelTypeDo {
    private Long travelTypeId;

    private String tenantId;

    private String travelTypeName;

    private Integer acctTimeType;

    private Float roundTimeUnit;

    private Integer ifIncludeNonWorkday;

    private Integer ifUploadFile;

    private Integer ifWriteRemark;

    private String remark;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    private Integer overtimeRule;
    private String autoTransferRule;
    private Boolean revokeWorkflow;
    private Boolean revokePassed;
    private String revokeAllowStatus;
    private String i18nTravelTypeName;

    //表结构之外其他业务拓展字段
    private String acctTimeTypeTxt;

    private String ifIncludeNonWorkdayTxt;

    @Resource
    private IWaTravelTypeRepository waTravelTypeRepository;

    public int selectCountByTravelName(String tenantId, String travelTypeName, Long travelTypeId) {
        return waTravelTypeRepository.selectCountByTravelName(tenantId, travelTypeName, travelTypeId);
    }

    public int save(WaTravelTypeDo waTravelTypeDo) {
        return waTravelTypeRepository.save(waTravelTypeDo);
    }

    public int update(WaTravelTypeDo waTravelTypeDo) {
        return waTravelTypeRepository.update(waTravelTypeDo);
    }

    public int delete(Long travelTypeId, UserInfo userInfo) {
        return waTravelTypeRepository.delete(travelTypeId, userInfo);
    }

    public WaTravelTypeDo selectOneById(String tenantId, Long travelTypeId) {
        return waTravelTypeRepository.selectOneById(tenantId, travelTypeId);
    }

    public PageResult<WaTravelTypeDo> getTravelTypePageResult(TravelTypeListReqDto dto) {
        return waTravelTypeRepository.getTravelTypePageResult(dto);
    }

    public List<WaTravelTypeDo> getWaTravelTypeList(String tenantId) {
        return waTravelTypeRepository.getTravelTypeList(tenantId);
    }
}