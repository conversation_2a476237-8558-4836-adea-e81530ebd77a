package com.caidaocloud.attendance.core.wa.dto.shift;

import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import lombok.Data;

@Data
public class MultiShiftPeriodResult {
    private Boolean isAbsent;               // 是否缺卡
    private Long lateTime = 0L;             // 迟到秒数
    private Long earlyTime = 0L;            // 早退秒数
    private Integer workTime = 0;           // 应出勤时长
    private Long actualWorkTime = 0L;       // 实际工作时长
    private Long holidayWorkTime = 0L;      // 法定假日工作时长
    private Long registerTime = 0L;         // 实际出勤时长
    private Integer bdkCount = 0;           // 补打卡次数
    private EmpShiftInfo shift;             // 班次
    private Integer startTime;              // 上班时间
    private WaRegisterRecord clockIn;       // 迟到的打卡记录
}
