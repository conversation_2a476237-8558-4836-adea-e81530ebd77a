package com.caidaocloud.attendance.core.system.service;

import com.caidao1.system.api.RemoteHxService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class NoticeService {
    private Logger log = LoggerFactory.getLogger(NoticeService.class);
    @Autowired
    private RemoteHxService hxService;
    /**
     * 假期撤销 发送审批结果消息接口
     *
     * @param empIds
     * @param msg
     * @param title
     */
    public void sendHxMsg(List<Long> empIds, String msg, String title, Map<String, String> extMap) {
        log.info("环信消息推送开始");
        hxService.sendHxMsg(empIds, msg, title, extMap);
        log.info("环信消息推送结束");
    }

    /**
     * 发送审批消息接口
     *
     * @param empIds
     * @param msg
     * @param title
     */
    public void sendHxMsg3(List<Long> empIds, String msg, String title, Map<String, String> extMap) {
        hxService.sendHxMsg3(empIds, msg, title, extMap);
    }
}
