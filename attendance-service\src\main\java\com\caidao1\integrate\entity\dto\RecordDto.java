package com.caidao1.integrate.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RecordDto {
    private Long empId;
    @ApiModelProperty("员工工号")
    private String workNo;
    @ApiModelProperty("员工姓名")
    private String name;
    @ApiModelProperty("打卡时间")
    private String regTimeStr;
    private Long registerTime;
    @ApiModelProperty("设备号")
    private String deviceNumber;
    @ApiModelProperty("系统是否存在")
    private String systemExists;
    @ApiModelProperty("数据来源")
    private String sourceForm;
}
