package com.caidaocloud.attendance.core.wa.dto.shift;

import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("班次设置-加班时间范围内的多段休息时间段DTO")
public class OvertimeRestPeriodsDto {
    @ApiModelProperty("开始时间(单位分钟),eg:1080")
    private Integer overtimeRestStartTime;
    @ApiModelProperty("结束时间(单位分钟),eg:1110")
    private Integer overtimeRestEndTime;
    @ApiModelProperty("开始时间归属标记: 1 当日、2 次日")
    private Integer overtimeRestStartTimeBelong;
    @ApiModelProperty("结束时间归属标记: 1 当日、2 次日")
    private Integer overtimeRestEndTimeBelong;

    public Integer doGetRealOvertimeRestStartTime(List<MultiOvertimeDto> multiOvertimeList) {
        if (null == overtimeRestStartTime) {
            return 0;
        }
        if (null != this.overtimeRestStartTimeBelong) {
            return ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.overtimeRestStartTimeBelong)
                    ? overtimeRestStartTime + 1440
                    : overtimeRestStartTime;
        }
        // 兼容假勤模块的一段班设置数据
        if (multiOvertimeList.size() > 1) {
            for (MultiOvertimeDto multiOvertimeDto : multiOvertimeList) {
                if (null == multiOvertimeDto.getOvertimeStartTime() || null == multiOvertimeDto.getOvertimeEndTime()) {
                    continue;
                }
                if (multiOvertimeDto.checkCrossNight()) {// 加班区间跨夜
                    if (overtimeRestStartTime >= multiOvertimeDto.getOvertimeStartTime() && overtimeRestStartTime <= 1440) {
                        return overtimeRestStartTime;
                    } else if (overtimeRestStartTime >= 0 && overtimeRestStartTime <= multiOvertimeDto.getOvertimeEndTime()) {
                        return overtimeRestStartTime + 1440;
                    }
                } else if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(multiOvertimeDto.getOvertimeStartTimeBelong())
                        && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(multiOvertimeDto.getOvertimeEndTimeBelong())) {// 加班区间在第二天
                    if (overtimeRestStartTime >= multiOvertimeDto.getOvertimeStartTime()
                            && overtimeRestStartTime <= multiOvertimeDto.getOvertimeEndTime()) {
                        return overtimeRestStartTime + 1440;
                    }
                } else {
                    if (overtimeRestStartTime >= multiOvertimeDto.getOvertimeStartTime()
                            && overtimeRestStartTime <= multiOvertimeDto.getOvertimeEndTime()) {
                        return overtimeRestStartTime;
                    }
                }
            }
            return overtimeRestStartTime;
        } else {
            MultiOvertimeDto multiOvertimeDto = multiOvertimeList.get(0);
            if (null == multiOvertimeDto.getOvertimeStartTime() || null == multiOvertimeDto.getOvertimeEndTime()) {
                return overtimeRestStartTime;
            }
            if (multiOvertimeDto.checkCrossNight()) {// 加班区间跨夜
                return overtimeRestStartTime < multiOvertimeDto.getOvertimeStartTime()
                        ? overtimeRestStartTime + 1440
                        : overtimeRestStartTime;
            } else if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(multiOvertimeDto.getOvertimeStartTimeBelong())
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(multiOvertimeDto.getOvertimeEndTimeBelong())) {// 加班区间在第二天
                return overtimeRestStartTime + 1440;
            } else {
                return overtimeRestStartTime;
            }
        }
    }

    public Integer doGetRealOvertimeRestEndTime(List<MultiOvertimeDto> multiOvertimeList) {
        if (null == overtimeRestEndTime) {
            return 0;
        }
        if (null != this.overtimeRestEndTimeBelong) {
            return ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.overtimeRestEndTimeBelong)
                    ? overtimeRestEndTime + 1440
                    : overtimeRestEndTime;
        }
        // 兼容假勤模块的一段班设置数据
        if (multiOvertimeList.size() > 1) {
            for (MultiOvertimeDto multiOvertimeDto : multiOvertimeList) {
                if (null == multiOvertimeDto.getOvertimeStartTime() || null == multiOvertimeDto.getOvertimeEndTime()) {
                    continue;
                }
                if (multiOvertimeDto.checkCrossNight()) {// 加班区间跨夜
                    if (overtimeRestEndTime >= multiOvertimeDto.getOvertimeStartTime() && overtimeRestEndTime <= 1440) {
                        return overtimeRestEndTime;
                    } else if (overtimeRestEndTime >= 0 && overtimeRestEndTime <= multiOvertimeDto.getOvertimeEndTime()) {
                        return overtimeRestEndTime + 1440;
                    }
                } else if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(multiOvertimeDto.getOvertimeStartTimeBelong())
                        && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(multiOvertimeDto.getOvertimeEndTimeBelong())) {// 加班区间在第二天
                    if (overtimeRestEndTime >= multiOvertimeDto.getOvertimeStartTime()
                            && overtimeRestEndTime <= multiOvertimeDto.getOvertimeEndTime()) {
                        return overtimeRestEndTime + 1440;
                    }
                } else {
                    if (overtimeRestEndTime >= multiOvertimeDto.getOvertimeStartTime()
                            && overtimeRestEndTime <= multiOvertimeDto.getOvertimeEndTime()) {
                        return overtimeRestEndTime;
                    }
                }
            }
            return overtimeRestEndTime;
        } else {
            MultiOvertimeDto multiOvertimeDto = multiOvertimeList.get(0);
            if (null == multiOvertimeDto.getOvertimeStartTime() || null == multiOvertimeDto.getOvertimeEndTime()) {
                return overtimeRestEndTime;
            }
            if (multiOvertimeDto.checkCrossNight()) {// 加班区间跨夜
                return overtimeRestEndTime < multiOvertimeDto.getOvertimeStartTime()
                        ? overtimeRestEndTime + 1440
                        : overtimeRestEndTime;
            } else if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(multiOvertimeDto.getOvertimeStartTimeBelong())
                    && ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(multiOvertimeDto.getOvertimeEndTimeBelong())) {// 加班区间在第二天
                return overtimeRestEndTime + 1440;
            } else {
                return overtimeRestEndTime;
            }
        }
    }
}
