package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.application.dto.EmpLeaveQuotaDto;
import com.caidaocloud.attendance.service.application.dto.QuotaGenDto;
import com.caidaocloud.attendance.service.application.dto.ReDecEmpLeaveQuotaDto;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.interfaces.dto.FixQuotaDto;
import com.caidaocloud.attendance.service.interfaces.dto.FixQuotaSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.*;
import com.caidaocloud.attendance.service.interfaces.vo.EmpCompensatoryQuotaVo;
import com.caidaocloud.attendance.service.interfaces.vo.EmpFixQuotaVo;
import com.caidaocloud.attendance.service.wfm.domain.entity.WorkingHourAnalyzeDo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/4/19
 */
public interface IQuotaService {
    int getEmpQuotaCountByYearAndType(QuotaDto dto);

    void genEmpQuota(String belongId, Integer waGroupId, Long empId, Short year, boolean all,
                     boolean isCarryForward, boolean isReCalQuota, Long userId, String dataScope) throws Exception;

    List<Map> getQuotaList(PageBean pageBean, Integer year, Integer[] quotaSettingId, String belongOrgId, List<Integer> years, String dataScope);

    AttendancePageResult<EmpCompensatoryQuotaVo> getEmpCompensatoryQuotaList(CompensatoryQuotaSearchDto dto, UserInfo userInfo);

    List<String> getQuotaHeaders(String belongOrgId);

    List<Map> queryEmpQuotaList(QuotaEmpDto dto, PageBean pageBean, String belongOrgId);

    void genEmpQuotaForIssuedAnnually(QuotaGenDto genDto) throws Exception;

    void autoCarryForwardQuota(String belongOrgId, Long userId);

    void carryForwardQuota(QuotaCarryForwardDto genDto, UserInfo userInfo, String dataScope) throws Exception;

    AttendancePageResult<EmpFixQuotaVo> getEmpFixQuotaList(FixQuotaSearchDto dto, UserInfo userInfo);

    String saveOrUpdateFixQuota(FixQuotaDto dto);

    void deleteFixQuota(Long empQuotaId);

    void genEmpQuotaForFixedQuota(String belongOrgId, Long userId, String dataScope);

    void genEmpQuotaForCompensatoryLeave(String belongOrgId, Long userId, Integer waSobId, Long empId, String dataScope, Long corpId) throws Exception;

    void deleteCompensatoryQuota(Long quotaId);

    BigDecimal getConvertRate(Long start, Long end, Long calStart, Long calEnd);

    BigDecimal handleMantissa(String belongId, BigDecimal val, Integer unit, Integer rule);

    Float calNowQuota(EmpLeaveQuotaDto lq);

    /**
     * 更新跨额度规则的员工配额数据
     * @param belongOrgId
     */
    void autoCalEmpCrossQuota(String belongOrgId);

    void autoGenEmpNowQuotaForIssuedAnnuallyByOrg(String belongOrgId, Long date, Long empId);

    Result<String> deleteCompensatoryQuotas(List<Long> quotaIds);

    Result<String> deleteFixQuotas(List<Integer> empQuotaIds);

    void reDecEmpLeaveQuota(ReDecEmpLeaveQuotaDto reDecEmpLeaveQuotaDto);

    Result<Boolean> update(EmpCompensatoryQuotaDto dto);

    EmpCompensatoryQuotaDto getById(Long quotaId);

    Result<String> importEmpCompensatory(MultipartFile file);

    Result<Boolean> save(EmpCompensatoryQuotaDto dto);

    Result<String> importEmpCompensatoryQuota(MultipartFile file);

    Result<KeyValue> getCompensatoryLeaveUnit(Long empId);

    Workbook importExcelFile(MultipartFile file) throws Exception;

    void genEmpWorkingHourQuotaForCompensatoryLeave(String tenantId, Long userId, Long startDate, Long endDate, List<WorkingHourAnalyzeDo> analyzeList) throws ParseException;
}