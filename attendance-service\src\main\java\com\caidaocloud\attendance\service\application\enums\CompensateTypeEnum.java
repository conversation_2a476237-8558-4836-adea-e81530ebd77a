package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

/**
 * 加班补偿类型
 *
 * @Author: <PERSON><PERSON>
 * @Date: 2021/7/15 17:07
 * @Description:
 **/
public enum CompensateTypeEnum {
    WORK_FREE("不补偿", AttendanceCodes.OVERTIME_WORK_FREE),
    WORK_PAID("加班费", AttendanceCodes.OVERTIME_WORK_PAID),
    COMPENSATORY_LEAVE("调休", AttendanceCodes.OVERTIME_COMPENSATORY_LEAVE),
    PAY_IN_ADVANCE("已预付", AttendanceCodes.OVERTIME_PAY_IN_ADVANCE),
    CHOICE("其他", AttendanceCodes.OVERTIME_CHOICE);
    private String desc;
    private Integer code;

    CompensateTypeEnum(String desc, Integer code) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public static String getDescByOrdinal(int ordinal) {
        for (CompensateTypeEnum item : CompensateTypeEnum.values()) {
            if (item.ordinal() == ordinal) {
                String i18n = ResponseWrap.wrapResult(item.code, null).getMsg();
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return item.getDesc();
            }
        }
        return "";
    }
}
