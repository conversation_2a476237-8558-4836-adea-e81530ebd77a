package com.caidaocloud.attendance.service.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.utils.BeanUtil;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.XlsUtil;
import com.caidao1.integrate.service.IntegratedLogicService;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.dto.CheckMessage;
import com.caidao1.ioc.service.ImportCheckService;
import com.caidao1.ioc.util.IocUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.report.common.ExcelUtil;
import com.caidao1.system.mybatis.mapper.SysUnitCityMapper;
import com.caidao1.system.mybatis.model.SysUnitCity;
import com.caidao1.system.mybatis.model.SysUnitCityExample;
import com.caidao1.wa.mybatis.mapper.WaCheckMapper;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaWorktimeDetail;
import com.caidaocloud.attendance.core.annoation.dto.dict.DictKeyValue;
import com.caidaocloud.attendance.core.annoation.feign.BccServiceFeignClient;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.service.application.enums.DayHalfTypeEnum;
import com.caidaocloud.attendance.service.application.enums.PeriodTypeEnum;
import com.caidaocloud.attendance.service.application.service.ITravelCompensatoryService;
import com.caidaocloud.attendance.service.application.service.ITravelTypeService;
import com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDaytimeDo;
import com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDo;
import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravel;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravelDaytime;
import com.caidaocloud.attendance.service.infrastructure.util.FileUtil;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTypeListReqDto;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.DataInputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ImportEmpTravelService implements ScriptBindable {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    static Map<String, String> DAY_MAP = new HashMap(2) {{
        put("上半天", "A");
        put("下半天", "P");
    }};
    static ThreadLocal<Map<String, WaTravelTypeDo>> TRAVEL_TYPE_NAME_MAP = new ThreadLocal<>();
    static ThreadLocal<Map<Long, WaTravelTypeDo>> TRAVEL_TYPE_ID_MAP = new ThreadLocal<>();
    static ThreadLocal<Map<Integer, WaShiftDef>> CORP_ALL_SHIFT_MAP = new ThreadLocal<>();
    //省
    static ThreadLocal<Map<String, Long>> PROV_MAP = new ThreadLocal<>();
    //市
    static ThreadLocal<Map<String, Long>> CITY_MAP = new ThreadLocal<>();
    @Resource
    private RemoteImportService importService;
    @Resource
    private WaCheckMapper waCheckMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Resource
    private WaEmpTravelDo waEmpTravelDo;
    @Autowired
    private WaEmpTravelDaytimeDo waEmpTravelDaytimeDo;
    @Resource
    private ITravelTypeService travelTypeService;
    @Resource
    private ImportCheckService importCheckService;
    @Resource
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Resource
    private SysUnitCityMapper sysUnitCityMapper;
    @Resource
    private EmpTravelService empTravelService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private IntegratedLogicService integratedLogicService;
    @Autowired
    @Lazy
    private WfService wfService;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private ITravelCompensatoryService travelCompensatoryService;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Resource
    private BccServiceFeignClient bccServiceFeignClient;

    private void initHelpMap(String belongId) {
        TravelTypeListReqDto dto = new TravelTypeListReqDto();
        dto.setPageNo(1);
        dto.setPageSize(500);
        PageResult<WaTravelTypeDo> travelTypePageResult = travelTypeService.getTravelTypePageResult(dto);
        TRAVEL_TYPE_ID_MAP.set(travelTypePageResult.getItems().stream().collect(Collectors.toMap(WaTravelTypeDo::getTravelTypeId, t -> t)));
        TRAVEL_TYPE_NAME_MAP.set(travelTypePageResult.getItems().stream().collect(Collectors.toMap(WaTravelTypeDo::getTravelTypeName, t -> t)));
        CORP_ALL_SHIFT_MAP.set(waCommonService.getCorpAllShiftDef(belongId));
        //初始化省
        SysUnitCityExample example = new SysUnitCityExample();
        example.createCriteria().andTypeEqualTo(1).andCityPidEqualTo(0L);
        Map<String, Long> provMap = new HashMap<>();
        for (SysUnitCity sysUnitCity : sysUnitCityMapper.selectByExample(example)) {
            String key = sysUnitCity.getChnName();
            provMap.put(key, sysUnitCity.getCityId());
        }
        PROV_MAP.set(provMap);
        //初始化市
        SysUnitCityExample example2 = new SysUnitCityExample();
        example2.createCriteria().andTypeEqualTo(2);
        Map<String, Long> cityMap = new HashMap<>();
        for (SysUnitCity sysUnitCity : sysUnitCityMapper.selectByExample(example2)) {
            String key = sysUnitCity.getChnName() + "_" + sysUnitCity.getCityPid();
            cityMap.put(key, sysUnitCity.getCityId());
        }
        CITY_MAP.set(cityMap);
    }

    public boolean checkDate(String startTime) {
        String regular = "^((\\d{2}(([02468][048])|([13579][26]))[-|/]?((((0?[13578])|(1[02]))[-|/]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[-|/]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[-|/]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[-|/]?((((0?[13578])|(1[02]))[-|/]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[-|/]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[-|/]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s(((0?[0-9])|([1][0-9])|([2][0-4]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$";
        Pattern r = Pattern.compile(regular);
        Matcher a = r.matcher(startTime);
        if (!a.matches()) {
            return false;
        }
        return true;
    }

    public void addCheck(LinkedList<CheckMessage> checkMessageList, CheckMessage checkMessage) {
        checkMessageList.add(checkMessage);
    }

    @Transactional(rollbackFor = Exception.class)
    public void importEmpTravel(LinkedList<CheckMessage> checkMessageList, MultipartFile file, UserInfo userInfo) throws Exception {
        List<TravelAddDto> travelAddDtoList = new ArrayList<>();
        Boolean endApprovalFail = false;
        try {
            DataInputStream dataInputStream = new DataInputStream(file.getInputStream());
            file.getOriginalFilename();
            //Workbook wb = WorkbookFactory.create(dataInputStream);
            Workbook wb = FileUtil.createWorkbook(file.getOriginalFilename(), dataInputStream);
            if (null == wb) {
                addCheck(checkMessageList, new CheckMessage(0, 0, ResponseWrap.wrapResult(AttendanceCodes.PARSE_WORKBOOK_ERR, null).getMsg()));
            } else {
                Sheet sheet = wb.getSheetAt(0);
                if (null == sheet) {
                    addCheck(checkMessageList, new CheckMessage(0, 0, ResponseWrap.wrapResult(AttendanceCodes.PARSE_SHEET_ERR, null).getMsg()));
                } else {
                    int firstRowNum = sheet.getFirstRowNum();
                    int lastRowNum = sheet.getPhysicalNumberOfRows();
                    if (lastRowNum == firstRowNum || lastRowNum <= 0) {
                        addCheck(checkMessageList, new CheckMessage(0, 0, ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_EMPTY, null).getMsg()));
                    } else {
                        String belongId = userInfo.getTenantId();
                        initHelpMap(belongId);
                        integratedLogicService.initEmp(SessionHolder.getBelongOrgId());
                        Boolean flowRunning = false;
                        try {
                            Result<Boolean> flowDef = wfService.checkWorkflowEnabled(BusinessCodeEnum.TRAVEL.getCode());
                            if (null != flowDef && flowDef.isSuccess()) {
                                flowRunning = flowDef.getData();
                            }
                        } catch (Exception e) {
                            log.info("获取外出工作流配置失败:{}", e.getMessage(), e);
                        }
                        Result<List<DictKeyValue>> dictList = bccServiceFeignClient.getEnableDictList("TravelMode", null);
                        for (int i = 1; i <= lastRowNum; i++) {
                            Row row = sheet.getRow(i);
                            if (row != null) {
                                LinkedList<CheckMessage> rowCheckList = new LinkedList<>();
                                Cell workNoImport = row.getCell(0);
                                String workNo = null;
                                Long empId = null;
                                Integer tmType = null;
                                if (workNoImport != null) {
                                    workNoImport.setCellType(CellType.STRING);
                                    workNo = workNoImport.getStringCellValue().trim();
                                }
                                if (StringUtils.isBlank(workNo)) {
                                    continue;
                                } else {
                                    String empInfo = CDCacheUtil.getValue(BaseConst.EMP_ + belongId + "_" + workNo);
                                    if (empInfo == null) {
                                        addCheck(rowCheckList, new CheckMessage(i, 1, ResponseWrap.wrapResult(AttendanceCodes.WORK_NOT_EXIST, null).getMsg()));
                                    } else {
                                        String[] s = empInfo.split(",");
                                        empId = Long.valueOf(s[0]);
                                        tmType = Integer.valueOf(s[2]);
                                    }
                                }
                                if (empId != null) {
                                    WaTravelTypeDo travelTypeDo = null;
                                    Long travelTypeId = null;
                                    Long provId = null;
                                    Long cityId = null;
                                    List<Long> travelModelList = new ArrayList<>();
                                    // 0 账号 1出差类型 2.出差地点 3.出差方式 4.开始时间 5.结束时间 6.上半天 7.下半天 8备注 9 审批状态
                                    for (int colNum = 0; colNum < 9; colNum++) {
                                        Cell cell = row.getCell(colNum);
                                        String cellStringVal = XlsUtil.getCellStringVal(cell);
                                        if (StringUtils.isNotBlank(cellStringVal)) {
                                            if (colNum == 1) {
                                                String travelTypeName = row.getCell(1).getStringCellValue().trim();
                                                if (TRAVEL_TYPE_NAME_MAP.get().containsKey(travelTypeName)) {
                                                    travelTypeDo = TRAVEL_TYPE_NAME_MAP.get().get(travelTypeName);
                                                    travelTypeId = travelTypeDo.getTravelTypeId();
                                                } else {
                                                    this.addCheck(rowCheckList, new CheckMessage(i, 2, String.format(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE_ONE_NOT_EXIST, null).getMsg(), travelTypeName)));
                                                }
                                            } else if (colNum == 2) {
                                                String[] provinceCityArray = cell.getStringCellValue().trim().split("/");
                                                if (provinceCityArray.length != 2) {
                                                    //this.addCheck(rowCheckList, new CheckMessage(i, 3, "出差地点不符合 省,市"));
                                                    this.addCheck(rowCheckList, new CheckMessage(i, 3, ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_DESTINATION_ERR, null).getMsg()));
                                                } else {
                                                    String province = provinceCityArray[0];
                                                    provId = PROV_MAP.get().get(province);
                                                    if (provId == null) {
                                                        //this.addCheck(rowCheckList, new CheckMessage(i, 3, String.format("省-> %s 不存在", province)));
                                                        this.addCheck(rowCheckList, new CheckMessage(i, 3, String.format(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_PROVINCE_NOT_EXIST, null).getMsg(), province)));
                                                    } else {
                                                        String cityKey = provinceCityArray[1] + "_" + provId;
                                                        cityId = CITY_MAP.get().get(cityKey);
                                                        if (cityId == null) {
                                                            //this.addCheck(rowCheckList, new CheckMessage(i, 3, String.format("市 %s 不存在", cityKey)));
                                                            this.addCheck(rowCheckList, new CheckMessage(i, 3, String.format(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_CITY_NOT_EXIST, null).getMsg(), cityKey)));
                                                        }
                                                    }
                                                }
                                            } else if (colNum == 3) {
                                                String travelModelCell = cell.getStringCellValue().trim();
                                                for (String travelModelText : travelModelCell.split(",")) {
                                                    Long travelModelDict = null;
                                                    for (DictKeyValue dict : dictList.getData()) {
                                                        if (dict.getText().equals(travelModelText)) {
                                                            travelModelDict = Long.valueOf(dict.getValue());
                                                            break;
                                                        }
                                                    }
                                                    if (travelModelDict == null) {
                                                        //this.addCheck(rowCheckList, new CheckMessage(i, 4, String.format("出行方式 %s 不存在", travelModelText)));
                                                        this.addCheck(rowCheckList, new CheckMessage(i, 4, String.format(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_MODE_NOT_EXIST, null).getMsg(), travelModelText)));
                                                        break;
                                                    } else {
                                                        travelModelList.add(travelModelDict);
                                                    }
                                                }
                                            }
                                        } else {
                                            checkRequired(i, colNum, rowCheckList);
                                        }
                                    }
                                    if (CollectionUtils.isEmpty(rowCheckList)) {
                                        Boolean timeValidateOk = true;
                                        String startTimeStr = ExcelUtil.getCellFormatValue(row.getCell(4));
                                        String endTimeStr = ExcelUtil.getCellFormatValue(row.getCell(5));
                                        if (StringUtils.isBlank(startTimeStr)) {
                                            //this.addCheck(rowCheckList, new CheckMessage(i, 5, "开始时间不能为空"));
                                            this.addCheck(rowCheckList, new CheckMessage(i, 5, ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_START_TIME_NOT_EMPTY, null).getMsg()));
                                        } else {
                                            if (!checkDate(startTimeStr)) {
                                                timeValidateOk = false;
                                                //this.addCheck(rowCheckList, new CheckMessage(i, 5, "开始时间格式不正确"));
                                                this.addCheck(rowCheckList, new CheckMessage(i, 5, ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_START_TIME_FORMAT_ERR, null).getMsg()));
                                            }
                                        }
                                        if (StringUtils.isBlank(endTimeStr)) {
                                            //this.addCheck(rowCheckList, new CheckMessage(i, 6, "结束时间不能为空"));
                                            this.addCheck(rowCheckList, new CheckMessage(i, 6, ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_END_TIME_NOT_EMPTY, null).getMsg()));
                                        } else {
                                            if (!checkDate(endTimeStr)) {
                                                timeValidateOk = false;
                                                //this.addCheck(rowCheckList, new CheckMessage(i, 6, "结束时间格式不正确"));
                                                this.addCheck(rowCheckList, new CheckMessage(i, 6, ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_END_TIME_FORMAT_ERR, null).getMsg()));
                                            }
                                        }
                                        String shalfDay = null;
                                        if (row.getCell(6) != null) {
                                            shalfDay = row.getCell(6).getStringCellValue();
                                            if (!DAY_MAP.containsKey(shalfDay) && StringUtils.isNotBlank(shalfDay)) {
                                                //this.addCheck(rowCheckList, new CheckMessage(i, 7, String.format("半天开始 %s 不是系统配置的选项", shalfDay)));
                                                this.addCheck(rowCheckList, new CheckMessage(i, 7, String.format(ResponseWrap.wrapResult(AttendanceCodes.HALF_START_NOT_SYS_CONFIG, null).getMsg(), shalfDay)));
                                            } else {
                                                shalfDay = DAY_MAP.get(shalfDay);
                                            }
                                        }
                                        String ehalfDay = null;
                                        if (row.getCell(7) != null) {
                                            ehalfDay = row.getCell(7).getStringCellValue();
                                            if (!DAY_MAP.containsKey(ehalfDay) && StringUtils.isNotBlank(ehalfDay)) {
                                                //this.addCheck(rowCheckList, new CheckMessage(i, 7, String.format("半天结束 %s 不是系统配置的选项", ehalfDay)));
                                                this.addCheck(rowCheckList, new CheckMessage(i, 7, String.format(ResponseWrap.wrapResult(AttendanceCodes.HALF_END_NOT_SYS_CONFIG, null).getMsg(), ehalfDay)));
                                            } else {
                                                ehalfDay = DAY_MAP.get(ehalfDay);
                                            }
                                        }
                                        String remark = null;
                                        if (row.getCell(8) != null) {
                                            remark = row.getCell(8).getStringCellValue();
                                        }
                                        Integer approvalStatus = null;
                                        if (row.getCell(9) != null) {
                                            String approval = row.getCell(9).getStringCellValue();
                                            if ("审批中".equals(approval)) {
                                                approvalStatus = 1;
                                                if (!flowRunning) {
                                                    //this.addCheck(rowCheckList, new CheckMessage(i, 10, "未开启工作流，请修改导入模板"));
                                                    this.addCheck(rowCheckList, new CheckMessage(i, 10, ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_START, null).getMsg()));
                                                }
                                            } else if ("审批通过".equals(approval)) {
                                                approvalStatus = 2;
                                            } else {
                                                //this.addCheck(rowCheckList, new CheckMessage(i, 10, "只能导入 审批中,审批通过"));
                                                this.addCheck(rowCheckList, new CheckMessage(i, 10, ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_IMPORT_NOT_ALLOW, null).getMsg()));
                                            }
                                        } else {
                                            approvalStatus = 2;
                                        }
                                        if (travelTypeId != null && timeValidateOk) {
                                            if (!checkEmpTravelTimeUnit(travelTypeDo, shalfDay, ehalfDay, startTimeStr)) {
                                                // 1 校验时间
                                                //this.addCheck(rowCheckList, new CheckMessage(i, 2, "出差时间单位错误"));
                                                this.addCheck(rowCheckList, new CheckMessage(i, 2, ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_UNIT_ERR, null).getMsg()));
                                            } else {
                                                Long startTime = (Long) IocUtil.getDateValue(importCheckService.getTimeStr(startTimeStr), "DATE");
                                                Long endTime = (Long) IocUtil.getDateValue(importCheckService.getTimeStr(endTimeStr), "DATE");
                                                Long startTimeDateLong = startTime - (startTime + 8 * 3600) % 86400;
                                                Long endTimeDateLong = endTime - (endTime + 8 * 3600) % 86400;
                                                //  2 校验班次
                                                List<String> err = new ArrayList<>();
                                                Map<Long, WaWorktimeDetail> empShiftMap = checkShift(err, belongId, empId, tmType, startTimeDateLong, endTimeDateLong);
                                                if (err.size() > 0) {
                                                    this.addCheck(rowCheckList, new CheckMessage(i, 2, err.get(0)));
                                                } else {
                                                    // 3 校验请假时间重叠或者请假开始结束日期未非工作日
                                                    Short leavePeriod = importCheckService.getLeavePeriod(importCheckService.getTimeStr(startTimeStr), travelTypeDo.getAcctTimeType(), shalfDay, ehalfDay);
                                                    CheckDateRepeatDto dto = new CheckDateRepeatDto(
                                                            empId, leavePeriod,
                                                            startTimeStr, startTime, startTimeDateLong,
                                                            endTimeStr, endTime, endTimeDateLong,
                                                            shalfDay, ehalfDay);
                                                    CheckDateRepeatResultDto resultDto = checkDateRepeat(dto, empShiftMap);
                                                    if (resultDto.getCheckMsg() != null) {
                                                        this.addCheck(rowCheckList, new CheckMessage(i, 2, resultDto.getCheckMsg()));
                                                    } else {
                                                        if (rowCheckList.size() == 0) {
                                                            // 构建待新增的数据
                                                            buildData(i, travelAddDtoList, userInfo, empId,
                                                                    travelTypeDo, travelTypeId, provId, cityId,
                                                                    travelModelList, resultDto.getStartTimeStr(), resultDto.getEndTimeStr(),
                                                                    shalfDay, ehalfDay, remark, startTimeDateLong, endTimeDateLong,
                                                                    approvalStatus, empShiftMap, leavePeriod, dto, resultDto);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                if (CollectionUtils.isNotEmpty(rowCheckList)) {
                                    checkMessageList.addAll(rowCheckList);
                                }
                            }
                        }
                        if (checkMessageList.size() == 0) {
                            if (travelAddDtoList.size() > 0) {
                                Map<Long, SysEmpInfo> empInfoMap = getEmpMap(travelAddDtoList, belongId);
                                checkRowRepeat(checkMessageList, travelAddDtoList, empInfoMap);
                                if (checkMessageList.size() == 0) {
                                    List<WaEmpTravelDo> waEmpTravelDoList = travelAddDtoList.stream().map(TravelAddDto::getWaEmpTravelDo).collect(Collectors.toList());
                                    LinkedList<WaEmpTravelDaytimeDo> addWaEmpTravelDayTimeList = new LinkedList<>();
                                    for (TravelAddDto travelAddDto : travelAddDtoList) {
                                        addWaEmpTravelDayTimeList.addAll(travelAddDto.getWaEmpTravelDaytimeDoList());
                                    }
                                    DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                                    def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                                    TransactionStatus transactionStatus = platformTransactionManager.getTransaction(def);
                                    try {
                                        importService.fastInsertList(WaEmpTravel.class, null, ObjectConverter.convertList(waEmpTravelDoList, WaEmpTravel.class));
                                        importService.fastInsertList(WaEmpTravelDaytime.class, null, ObjectConverter.convertList(addWaEmpTravelDayTimeList, WaEmpTravelDaytime.class));
                                        platformTransactionManager.commit(transactionStatus);
                                    } catch (Exception e) {
                                        platformTransactionManager.rollback(transactionStatus);
                                        log.error("batchInsertEmpTravelError:{}", e.getMessage(), e);
                                        throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FAILED, null).getMsg());
                                    }
                                    // 审批通过需要出差转调休的 工作流发起也没问题后统一处理
                                    List<Long> quotaTravelIdList = new ArrayList<>();
                                    // 如果出现工作流发起异常则删除所有批量插入的记录
                                    List<Long> empTravelIdList = waEmpTravelDoList.stream().map(WaEmpTravelDo::getTravelId).collect(Collectors.toList());
                                    List<Long> empTravelDayTimeIdList = addWaEmpTravelDayTimeList.stream().map(WaEmpTravelDaytimeDo::getTravelDaytimeId).collect(Collectors.toList());
                                    for (TravelAddDto travelAddDto : travelAddDtoList) {
                                        WaEmpTravelDo waEmpTravel = travelAddDto.getWaEmpTravelDo();
                                        if (waEmpTravel.getStatus() == 1) {
                                            SysEmpInfo empInfo = empInfoMap.get(travelAddDto.getEmpId());
                                            boolean f = true;
                                            try {
                                                String businessKey = String.valueOf(waEmpTravel.getTravelId());
                                                WfBeginWorkflowDto workflowDto = new WfBeginWorkflowDto();
                                                workflowDto.setFuncCode(BusinessCodeEnum.TRAVEL.getCode());
                                                workflowDto.setBusinessId(businessKey);
                                                workflowDto.setApplicantId(empInfo.getEmpid().toString());
                                                workflowDto.setApplicantName(empInfo.getEmpName());
                                                workflowDto.setEventTime(travelAddDto.getStartDateLong() * 1000);
                                                workflowDto.setEventEndTime(travelAddDto.getEndDateLong() * 1000);
                                                workflowDto.setTimeSlot(getTimeSlot(waEmpTravel));
                                                Result<?> result = wfRegisterFeign.begin(workflowDto);
                                                if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                                                    log.error("empTravelImport start workflow failed:{}", FastjsonUtil.toJsonStr(result));
                                                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
                                                }
                                            } catch (Exception e) {
                                                deleteFailRow(empTravelIdList, empTravelDayTimeIdList);
                                                log.error("waEmpTravelImportBeginWorkflowErr:{}", e.getMessage(), e);
                                                f = false;
                                            }
                                            if (!f) {
                                                addCheck(checkMessageList, new CheckMessage(travelAddDto.getRow(), 1, ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_CONFIG_ERR_RESET, null).getMsg()));
                                            }
                                        } else {
                                            quotaTravelIdList.add(waEmpTravel.getTravelId());
                                        }
                                    }
                                    if (CollectionUtils.isNotEmpty(quotaTravelIdList) && checkMessageList.size() == 0) {
                                        try {
                                            for (Long travelId : quotaTravelIdList) {
                                                travelCompensatoryService.generateCompensatoryQuota(travelId);
                                            }
                                        } catch (Exception e) {
                                            deleteFailRow(empTravelIdList, empTravelDayTimeIdList);
                                            log.error("batchInsertEmpTravel generateCompensatoryQuota  Error:{}", e.getMessage(), e);
                                            endApprovalFail = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } finally {
            if (TRAVEL_TYPE_ID_MAP.get() != null) {
                TRAVEL_TYPE_ID_MAP.remove();
            }
            if (TRAVEL_TYPE_NAME_MAP.get() != null) {
                TRAVEL_TYPE_NAME_MAP.remove();
            }
            if (CORP_ALL_SHIFT_MAP.get() != null) {
                CORP_ALL_SHIFT_MAP.remove();
            }
            if (checkMessageList.size() > 0 || endApprovalFail) {
                log.info("importEmpTravel CheckList:{}", JSONUtils.ObjectToJson(checkMessageList));
                throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FAILED, null).getMsg());
            }
        }

    }

    public String getTimeSlot(WaEmpTravelDo empTravel) {
        Long shiftStartTime = empTravel.getShiftStartTime();
        Long shiftEndTime = empTravel.getShiftEndTime();
        Integer periodType = empTravel.getPeriodType().intValue();
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
            return String.format("%s~%s", DateUtil.getDateStrByTimesamp(shiftStartTime), DateUtil.getDateStrByTimesamp(shiftEndTime));
        } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
            return String.format("%s %s~%s %s", DateUtil.getDateStrByTimesamp(empTravel.getStartTime()), DayHalfTypeEnum.getDesc(empTravel.getShalfDay()), DateUtil.getDateStrByTimesamp(empTravel.getEndTime()), DayHalfTypeEnum.getDesc(empTravel.getEhalfDay()));
        } else {
            return String.format("%s~%s", DateUtil.getTimeStrByTimesamp(shiftStartTime), DateUtil.getTimeStrByTimesamp(shiftEndTime));
        }
    }

    private void deleteFailRow(List<Long> empTravelIdList, List<Long> empTravelDayTimeIdList) {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transaction = platformTransactionManager.getTransaction(definition);
        try {
            waEmpTravelDo.deleteByTravelIds(empTravelIdList);
            waEmpTravelDaytimeDo.deleteByTravelIds(empTravelDayTimeIdList);
            platformTransactionManager.commit(transaction);
        } catch (Exception deleteException) {
            platformTransactionManager.rollback(transaction);
        }
    }


    @NotNull
    private Map<Long, SysEmpInfo> getEmpMap(List<TravelAddDto> travelAddDtoList, String belongId) {
        List<Long> importEmpList = travelAddDtoList.stream().map(TravelAddDto::getEmpId).distinct().collect(Collectors.toList());
        QueryWrapper<SysEmpInfo> wrapper = new QueryWrapper<>();
        wrapper.in("empid", importEmpList);
        wrapper.in("belong_org_id", belongId);
        wrapper.select("empid,workno,emp_name,job_grade,ee_type,leader_empid,belong_org_id,corpid");
        Map<Long, SysEmpInfo> empInfoMap = sysEmpInfoMapper.selectList(wrapper).stream().collect(Collectors.toMap(SysEmpInfo::getEmpid, a -> a));
        return empInfoMap;
    }

    private void checkRowRepeat(LinkedList<CheckMessage> checkMessageList, List<TravelAddDto> travelAddDtoList, Map<Long, SysEmpInfo> empInfoMap) {
        Map<String, List<Integer>> repeatMap = new HashMap<>();
        for (TravelAddDto travelAddDto : travelAddDtoList) {
            WaEmpTravelDo empTravel = travelAddDto.getWaEmpTravelDo();
            String key = String.format("%s_%s_%s_%s_%s", empTravel.getEmpId(), empTravel.getStartTime(), empTravel.getEndTime(),
                    Optional.ofNullable(empTravel.getShalfDay()).orElse(""), Optional.ofNullable(empTravel.getEhalfDay()).orElse(""));
            if (repeatMap.get(key) == null) {
                repeatMap.put(key, new ArrayList<>());
            }
            repeatMap.get(key).add(travelAddDto.getRow());
            if (empTravel.getStatus() == 1) {
                genWorkFlowParams(empInfoMap, travelAddDto, empTravel);
            }
        }

        for (List<Integer> havingRow : repeatMap.values()) {
            if (havingRow.size() > 1) {
                for (int i = 0; i < havingRow.size(); i++) {
                    List<Integer> otherRow = new ArrayList<>(havingRow.size() - 1);
                    for (Integer row : havingRow) {
                        if (!row.toString().equals(havingRow.get(i).toString())) {
                            otherRow.add(row);
                        }
                    }
                    addCheck(checkMessageList, new CheckMessage(havingRow.get(i), 1,
                            String.format("第%s行与第%s行存在重复数据", havingRow.get(i), StringUtils.join(otherRow, ","))));
                }
            }
        }
    }

    private void genWorkFlowParams(Map<Long, SysEmpInfo> empInfoMap, TravelAddDto travelAddDto, WaEmpTravelDo empTravel) {
        WaTravelTypeDo travelTypeDo = travelAddDto.getWaTravelTypeDo();
        Map<String, String> map = BeanUtil.objectToMap(empTravel);
        map.put("businessKey", empTravel.getTravelId().toString());
        map.put("empid", empTravel.getEmpId().toString());
        map.put("travelTypeTxt", travelTypeDo.getTravelTypeName());
        map.put("travelType", empTravel.getTravelType());
        map.put("timeUnit", travelTypeDo.getAcctTimeType().toString());
        if (travelTypeDo.getAcctTimeType() == 2) {
            //小时
            MathContext mc = new MathContext(2, RoundingMode.HALF_DOWN);
            BigDecimal a = new BigDecimal(empTravel.getTimeDuration());
            BigDecimal b = a.divide(new BigDecimal(60), mc);
            map.put("totalTime", b.toString());
        } else {
            map.put("totalTime", empTravel.getTimeDuration().toString());
        }
        SysEmpInfo empInfo = empInfoMap.get(travelAddDto.getEmpId());
        if (empInfo.getJobGrade() != null) {
            map.put("jobGrade", empInfo.getJobGrade().toString());
        }
        if (empInfo.getEeType() != null) {
            map.put("ee_type", empInfo.getEeType().toString());
        }
        if (empInfo.getLeaderEmpid() != null) {
            SysEmpInfo leaderEmp = sysEmpInfoMapper.selectByPrimaryKey(empInfo.getLeaderEmpid());
            if (leaderEmp != null) {
                Long leaderJobGrade = leaderEmp.getJobGrade();
                if (leaderJobGrade != null) {
                    map.put("leaderJobGrade", leaderJobGrade.toString());
                }
            }
        }

        map.put("eventTime", String.valueOf(travelAddDto.getStartDateLong()));
        map.put("eventEndTime", String.valueOf(travelAddDto.getEndDateLong()));
        travelAddDto.setPrams(map);
    }

    @Data
    class TravelAddDto {
        Long empId;
        Long startDateLong;
        Long endDateLong;
        WaTravelTypeDo waTravelTypeDo;
        Integer row;
        Map prams;
        WaEmpTravelDo waEmpTravelDo;
        List<WaEmpTravelDaytimeDo> waEmpTravelDaytimeDoList = new ArrayList<>();
    }

    public void saveErrorCache(LinkedList<CheckMessage> errorList, String progress) {
        try {
            // 删除提示信息缓存
            cacheService.remove("IMP_" + progress);
            if (errorList.size() > 0) {
                String value = new ObjectMapper().writeValueAsString(errorList);
                cacheService.cacheValue("IMP_" + progress, value);
                cacheService.updateExpire("IMP_" + progress, 720);
            }
        } catch (Exception e) {
            log.error("saveErrorCache err:{}", e.getMessage(), e);
        }
    }

    private void buildData(Integer row, List<TravelAddDto> travelAddDtoList, UserInfo userInfo, Long empId, WaTravelTypeDo travelTypeDo, Long travelTypeId, Long provId, Long cityId, List<Long> travelModelList, String startTimeStr, String endTimeStr, String shalfDay, String ehalfDay, String remark, Long startTimeDateLong, Long endTimeDateLong, Integer approvalStatus, Map<Long, WaWorktimeDetail> empShiftMap, Short leavePeriod, CheckDateRepeatDto dto, CheckDateRepeatResultDto resultDto) {
        WaWorktimeDetail worktimeDetail;
        WaShiftDef shiftDef;
        WaEmpTravelDaytimeDo dayTime;
        //计算请假总额
        BigDecimal totalTimeDuration = new BigDecimal(0);
        List<WaEmpTravelDaytimeDo> daytimeList = new ArrayList<>();
        boolean includeNonWorkday = travelTypeDo.getIfIncludeNonWorkday() != null && travelTypeDo.getIfIncludeNonWorkday() == 1;

        boolean flag = true;
        while (flag) {
            worktimeDetail = empShiftMap.get(startTimeDateLong);
            shiftDef = CORP_ALL_SHIFT_MAP.get().get(worktimeDetail.getShiftDefId());
            if (worktimeDetail.getDateType() == 4) {
                //公司特殊假日
                worktimeDetail.setDateType(shiftDef.getDateType());
            }
            // 非工作日出差申请替换班次
            if (worktimeDetail.getDateType() != 1 && includeNonWorkday && null != shiftDef.getSubstituteShift()
                    && null != CORP_ALL_SHIFT_MAP.get().get(shiftDef.getSubstituteShift())) {
                shiftDef = CORP_ALL_SHIFT_MAP.get().get(shiftDef.getSubstituteShift());
            }
            dayTime = new WaEmpTravelDaytimeDo();
            dayTime.setTravelDate(startTimeDateLong);
            dayTime.setRealDate(startTimeDateLong);
            dayTime.setPeriodType(leavePeriod);
            dayTime.setTimeUnit(travelTypeDo.getAcctTimeType().shortValue());
            dayTime.setShiftDefId(shiftDef.getShiftDefId());
            dayTime.setDateType(worktimeDetail.getDateType());
            dayTime.setTimeDuration(0f);

            boolean isCheckDateType = true;
            if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType()) &&
                    PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().shortValue() == leavePeriod) {
                //非工作日并且休的是小时假
                Long preEndDate = DateUtil.addDate(endTimeDateLong * 1000, -1);
                WaWorktimeDetail preEndDateWorkTimeDetail = empShiftMap.get(preEndDate);
                if (preEndDateWorkTimeDetail != null && preEndDateWorkTimeDetail.getDateType() == 1 &&
                        preEndDateWorkTimeDetail.getStartTime() > preEndDateWorkTimeDetail.getEndTime()) {
                    //班次是跨夜班
                    isCheckDateType = false;
                }
            }

            //包含非工作日
            if (DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType()) || includeNonWorkday || !isCheckDateType) {
                if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().shortValue() == leavePeriod) {
                    try {
//                        empTravelService.calLeaveTimeByPeriod3(travelTypeDo, startTimeDateLong, endTimeDateLong, startTimeStr, endTimeStr, empShiftMap, dayTime, CORP_ALL_SHIFT_MAP.get());
                        empTravelService.calLeaveTimeByPeriod3New(travelTypeDo, startTimeDateLong, endTimeDateLong, startTimeStr, endTimeStr, empShiftMap, dayTime, CORP_ALL_SHIFT_MAP.get());
                    } catch (Exception e) {
                        log.error("calLeaveTimeByPeriod3Err:{}", e.getMessage(), e);
                    }
                } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().shortValue() == leavePeriod) {
                    empTravelService.processLeaveByPeriod4(travelTypeDo, worktimeDetail, dayTime, CORP_ALL_SHIFT_MAP.get());
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().shortValue() == leavePeriod) {
                    empTravelService.processLeaveByPeriod9(travelTypeDo, worktimeDetail, dayTime, dto.getShalDay(), dto.getEnalDay(),
                            startTimeDateLong, DateUtil.getTimesampByDateStr2(startTimeStr),
                            DateUtil.getTimesampByDateStr2(endTimeStr));
                } else {
                    dayTime.setTimeDuration(1f);
                }
            }

            if (Objects.equals(startTimeDateLong, endTimeDateLong)) {
                flag = false;
            } else {
                startTimeDateLong = startTimeDateLong + 24 * 60 * 60;
            }
            totalTimeDuration = totalTimeDuration.add(new BigDecimal(dayTime.getTimeDuration()));
            daytimeList.add(dayTime);
        }
        // 构建保存数据
        WaEmpTravelDo empTravel = new WaEmpTravelDo();
        empTravel.setTravelId(snowflakeUtil.createId());
        empTravel.setTenantId(userInfo.getTenantId());
        empTravel.setEmpId(empId);
        empTravel.setTravelTypeId(travelTypeId);
        empTravel.setTravelMode(StringUtils.join(travelModelList, ","));
        empTravel.setProvince(provId);
        empTravel.setCity(cityId);
        empTravel.setPeriodType(leavePeriod);
        empTravel.setTimeUnit(travelTypeDo.getAcctTimeType().shortValue());
        empTravel.setReason(remark);
        Long userId = Long.valueOf(userInfo.getUserid());
        long curTime = System.currentTimeMillis() / 1000L;
        empTravel.setCreateBy(userId);
        empTravel.setCreateTime(curTime);
        empTravel.setUpdateBy(userId);
        empTravel.setUpdateTime(curTime);
        empTravel.setDeleted(0);
        empTravel.setStatus(approvalStatus);
        if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().shortValue() == leavePeriod) {
            empTravel.setStartTime(startTimeDateLong);
            empTravel.setEndTime(endTimeDateLong);
            empTravel.setShalfDay(shalfDay);
            empTravel.setEhalfDay(ehalfDay);
        } else {
            empTravel.setStartTime(resultDto.getStart());
            empTravel.setEndTime(resultDto.getEnd());
        }
        empTravel.setShiftStartTime(resultDto.getStart());
        empTravel.setShiftEndTime(resultDto.getEnd());
        empTravel.setTimeDuration(totalTimeDuration.floatValue());
        if (approvalStatus == 2) {
            empTravel.setLastApprovalTime(System.currentTimeMillis() / 1000);
        }

        TravelAddDto travelAddDto = new TravelAddDto();
        travelAddDto.setEmpId(empId);
        travelAddDto.setStartDateLong(startTimeDateLong);
        travelAddDto.setEndDateLong(endTimeDateLong);
        travelAddDto.setRow(row);
        travelAddDto.setWaEmpTravelDo(empTravel);
        travelAddDto.setWaTravelTypeDo(travelTypeDo);

        daytimeList.forEach(daytime -> {
            daytime.setTravelId(empTravel.getTravelId());
            daytime.setTravelDaytimeId(snowflakeUtil.createId());
            travelAddDto.getWaEmpTravelDaytimeDoList().add(daytime);
        });

        travelAddDtoList.add(travelAddDto);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class CheckDateRepeatDto {
        private Long empId;
        private short period;

        private String startTime;
        private Long start;
        private Long startLong;

        private String endTime;
        private Long end;
        private Long endLong;

        private String shalDay;
        private String enalDay;
    }

    private void checkRequired(int row, int colNum, LinkedList<CheckMessage> checkMessageList) {
        switch (colNum) {
            case 1:
                this.addCheck(checkMessageList, new CheckMessage(row, 2, "出差类型不能为空"));
                break;
            case 2:
                this.addCheck(checkMessageList, new CheckMessage(row, 3, "出差地点不能为空"));
                break;
            case 3:
                this.addCheck(checkMessageList, new CheckMessage(row, 4, "出行方式不能为空"));
                break;
        }
    }

    @Data
    private class CheckDateRepeatResultDto {
        private String checkMsg;
        private Long start;
        private Long end;
        private String startTimeStr;
        private String endTimeStr;
    }

    public CheckDateRepeatResultDto checkDateRepeat(CheckDateRepeatDto dto, Map<Long, WaWorktimeDetail> empShiftMap) {
        CheckDateRepeatResultDto resultDto = new CheckDateRepeatResultDto();
        short period = dto.getPeriod();
        Long startLong = dto.getStartLong();
        Long endLong = dto.getEndLong();
        // 校验出差日期是否重叠
        // 出差时间重叠校验
        Long start = null;
        Long end = null;
        String startTimeStr = "", endTimeStr = "";
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().shortValue() == period || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().shortValue() == period) {
            //整天
            start = startLong;
            end = endLong;
            if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().shortValue() == period) {
                startTimeStr = DateUtil.getTimeStrByTimesamp4(dto.getStart());
                endTimeStr = DateUtil.getTimeStrByTimesamp4(dto.getEnd());
            } else {
                startTimeStr = DateUtil.getDateStrByTimesamp(dto.getStart());
                endTimeStr = DateUtil.getDateStrByTimesamp(dto.getEnd());
            }
        } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().shortValue() == period) {
            //小时
            start = dto.getStart();
            end = dto.getEnd();

            startTimeStr = DateUtil.getTimeStrByTimesamp4(start);
            endTimeStr = DateUtil.getTimeStrByTimesamp4(end);
        } else {
            WaShiftDef startDateShift = CORP_ALL_SHIFT_MAP.get().get(empShiftMap.get(startLong).getShiftDefId());
            //半天
            start = startLong + startDateShift.getStartTime() * 60;

            String shalfday = dto.getShalDay();
            if ("P".equals(shalfday)) {
                if (startDateShift.getIsHalfdayTime() != null && startDateShift.getIsHalfdayTime() && startDateShift.getHalfdayTime() > 0) {
                    start = startLong + startDateShift.getHalfdayTime() * 60;
                } else if (startDateShift.getIsNoonRest() != null && startDateShift.getIsNoonRest()) {
                    start = startLong + startDateShift.getNoonRestEnd() * 60;
                }
            }

            WaShiftDef endDateShift = CORP_ALL_SHIFT_MAP.get().get(empShiftMap.get(endLong).getShiftDefId());
            end = endLong + endDateShift.getEndTime() * 60;

            String ehalfday = dto.getEnalDay();
            if ("A".equals(ehalfday)) {
                if (endDateShift.getIsHalfdayTime() != null && endDateShift.getIsHalfdayTime() && endDateShift.getHalfdayTime() > 0) {
                    end = endLong + endDateShift.getHalfdayTime() * 60;
                } else if (endDateShift.getIsNoonRest() != null && endDateShift.getIsNoonRest()) {
                    end = endLong + endDateShift.getNoonRestStart() * 60;
                }
            }
            startTimeStr = DateUtil.getDateStrByTimesamp(start);
            endTimeStr = DateUtil.getDateStrByTimesamp(end);
        }
        long checkEnd = end;
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().shortValue() == period || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().shortValue() == period) {
            checkEnd = end + 86399;
        }
        if (waEmpTravelDo.checkEmpTravelTimeRepeat(dto.getEmpId(), start, checkEnd) > 0) {
            resultDto.setCheckMsg("存在日期重叠的申请，不能提交");
        }
        Map<String, Object> params = new HashMap<>(3);
        params.put("empid", dto.getEmpId());
        params.put("start", start);
        params.put("end", checkEnd);
        if (waCheckMapper.checkLeaveRepeat(params) > 0) {
            resultDto.setCheckMsg("存在日期重叠的申请，不能提交");
        }
        resultDto.setStart(start);
        resultDto.setEnd(end);
        resultDto.setStartTimeStr(startTimeStr);
        resultDto.setEndTimeStr(endTimeStr);
        return resultDto;
    }

    public boolean checkEmpTravelTimeUnit(WaTravelTypeDo waTravelTypeDo, String shalfDay, String ehalfDay, String startTime) {
        // 出差时间单位错误
        String startTimeStr = importCheckService.getTimeStr(startTime);
        if (StringUtils.isBlank(startTimeStr)) {
            return false;
        }
        if (shalfDay == null || ehalfDay == null) {
            // 必须都不为空
            shalfDay = "";
            ehalfDay = "";
        }
        Short period = importCheckService.getLeavePeriod(startTimeStr, waTravelTypeDo.getAcctTimeType(), shalfDay, ehalfDay);
        if (period == null) {
            return false;
        }
        return true;
    }

    public Map<Long, WaWorktimeDetail> checkShift(List<String> err, String belongId, Long empId, Integer tmType, Long startTimeDateLong, Long endTimeDateLong) {
        QueryWrapper<SysEmpInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("empid", empId);
        wrapper.eq("belong_org_id", belongId);
        wrapper.select("worktime_type");
        SysEmpInfo empInfo = sysEmpInfoMapper.selectOne(wrapper);
        Integer workTimeType = empInfo == null ? null : empInfo.getWorktimeType();
        Map<Long, WaWorktimeDetail> empShiftMap = waCommonService.getEmpWaWorktimeDetail(belongId, empId,
                tmType, startTimeDateLong - 86400, endTimeDateLong, workTimeType, true);
        if (MapUtils.isEmpty(empShiftMap) || empShiftMap.size() == 0) {
            err.add("员工未排班");
            return empShiftMap;
        }
        long tmpDate = startTimeDateLong;
        while (tmpDate <= endTimeDateLong) {
            if (!empShiftMap.containsKey(tmpDate)) {
                //err.add("员工" + DateUtil.getDateStrByTimesamp(tmpDate) + "未排班");
                err.add(String.format(ResponseWrap.wrapResult(AttendanceCodes.EMP_DAY_NOT_SHIFT, "").getMsg(), DateUtil.getDateStrByTimesamp(tmpDate)));
                return empShiftMap;
            }
            tmpDate = tmpDate + 24 * 60 * 60;
        }
        return empShiftMap;
    }
}
