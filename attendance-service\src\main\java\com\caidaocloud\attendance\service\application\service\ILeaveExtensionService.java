package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.interfaces.dto.leaveExtension.*;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;

public interface ILeaveExtensionService {
    Result<String> applyLeaveExtension(LeaveExtensionApplyDto dto, UserInfo userInfo);

    Result<Boolean> revokeLeaveExtension(LeaveExtensionRevokeDto dto, UserInfo userInfo);

    PageList<LeaveExtensionDto> getEmpLeaveExtensionList(LeaveExtensionReqDto dto, UserInfo userInfo);

    List<KeyValue> getLeaveExtensionQuotaType();

    List<LeaveExtensionQuotaDto> getLeaveExtensionQuotaList(LeaveExtensionQuota dto, UserInfo userInfo) throws Exception;
}
