package com.caidaocloud.attendance.service.application.dto.travel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BatchTravelTimeDetailDto {
    @ApiModelProperty("出发回归日期")
    private Long actualTravelDate;

    @ApiModelProperty("出差日期")
    private Long travelDate;

    @ApiModelProperty("出行方式")
    private String transportation;

    @ApiModelProperty("航班号/车次")
    private String transportationNo;

    @ApiModelProperty("出行方式名称")
    private String transportationTxt;

    @ApiModelProperty("座位等级")
    private String seatClasses;

    @ApiModelProperty("出发地")
    private String departure;

    @ApiModelProperty("出发时间")
    private Long departureTime;

    @ApiModelProperty("到达地")
    private String destination;

    @ApiModelProperty("到达时间")
    private Long destinationTime;

    @ApiModelProperty("住宿")
    private String accommodation;

    @ApiModelProperty("出差状态 1 出发 2 中转 3 回归")
    private Integer traveStatus;

    @ApiModelProperty("出差状态名称")
    private String traveStatusTxt;
}