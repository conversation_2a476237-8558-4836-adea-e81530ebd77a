package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.core.wa.dto.MyLeaveTimeBaseDto;
import com.caidaocloud.attendance.sdk.dto.SdkLeaveApplySaveDTO;
import com.caidaocloud.attendance.sdk.dto.SdkMaternityLeaveRange;
import com.caidaocloud.attendance.sdk.feign.fallback.LeaveFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 休假
 *
 * <AUTHOR>
 * @Date 2023/6/19
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = LeaveFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "leaveFeignClient")
public interface ILeaveFeignClient {

    /**
     * 获取计算的申请时间
     *
     * @param leaveApplySaveDto
     * @return
     */
    @PostMapping(value = "/api/attendance/leaveapply/v1/getLeaveTotalTime")
    Result<?> getLeaveTotalTime(@RequestBody SdkLeaveApplySaveDTO leaveApplySaveDto);

    /**
     * 申请休假
     *
     * @param leaveApplySaveDto
     * @return
     */
    @PostMapping(value = "/api/attendance/leaveapply/v1/saveLeaveApply")
    Result<?> saveLeaveApply(@RequestBody SdkLeaveApplySaveDTO leaveApplySaveDto);

    /**
     * 获取计算的申请时间
     *
     * @param leaveApplySaveDto
     * @return
     */
    @PostMapping(value = "/api/attendance/leaveapply/v1/getUserLeaveTotalTime")
    Result<?> getUserLeaveTotalTime(@RequestBody SdkLeaveApplySaveDTO leaveApplySaveDto);

    /**
     * 获取产假可选时间范围
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/attendance/leaveapply/v1/getMaternityLeaveRange")
    Result<?> getMaternityLeaveRange(@RequestBody SdkMaternityLeaveRange dto);

    /**
     * 我的休假信息
     *
     * @param date  格式: 年月日时间戳
     * @param empId
     * @return
     */
    @GetMapping(value = "/api/attendance/user/v1/listEmpLeave")
    Result<List<MyLeaveTimeBaseDto>> listMyEmpLeave(@RequestParam("day") Long date,
                                                    @RequestParam(value = "empid", required = false) Long empId);
}
