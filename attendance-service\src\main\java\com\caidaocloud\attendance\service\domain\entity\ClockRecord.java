package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidaocloud.attendance.service.domain.repository.IClockRecordRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Data
@Service
public class ClockRecord {
    private Integer recordId;

    private Long empid;

    private Integer registerType;

    private String resultDesc;

    private Integer resultType;

    private String reason;

    private String regAddr;

    private Long regDateTime;

    private BigDecimal lng;

    private BigDecimal lat;

    private Long belongDate;

    private String mobDeviceNum;

    private Long crtuser;

    private Long crttime;

    private Long upduser;

    private Long updtime;

    private String normalAddr;

    private String normalDate;

    private Integer type;

    private String owRmk;

    private String picList;

    private Long hisRegTime;

    private Integer shiftDefId;

    private Boolean isDeviceError;

    private String oriMobDeviceNum;

    private Boolean isWorkflow;

    private Integer approvalStatus;

    private String approvalReason;

    private String filePath;

    private String revokeReason;

    private Long siteId;

    private String province;

    private String city;

    private Integer startTime;

    private Integer endTime;

    private Long corpid;

    private String belongOrgId;

    @Autowired
    private IClockRecordRepository clockRecordRepository;

    public List<WaRegisterRecord> getClockRecords(String tenantId, Long empId, Long belongDate, Integer type, Integer registerType, String sort) {
        return clockRecordRepository.selectClockRecords(empId, belongDate, type, registerType, tenantId, sort);
    }
}
