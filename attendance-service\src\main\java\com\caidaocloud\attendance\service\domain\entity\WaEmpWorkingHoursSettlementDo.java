package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.domain.repository.IEmpWorkingHoursSettlementRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpWorkingHoursSettlement;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 考勤统计-工时结算 DO
 */
@Data
@Slf4j
@Service
public class WaEmpWorkingHoursSettlementDo {
    /**
     * 主键ID
     */
    private Long settlementId;

    /**
     * 考勤月，格式：yyyymm，如：202507
     */
    private Integer attendanceMonth;

    /**
     * 周期开始日期，格式：yyyy-mm-dd 日期时间戳秒级
     */
    private Long periodStartDate;

    /**
     * 周期结束日期，格式：yyyy-mm-dd 日期时间戳秒级
     */
    private Long periodEndDate;

    /**
     * 员工ID
     */
    private Long empId;

    /**
     * 员工姓名
     */
    private String empName;

    /**
     * 员工工号
     */
    private String workno;

    /**
     * 组织ID
     */
    private Long organize;

    /**
     * 组织名称
     */
    private String organizeTxt;

    /**
     * 计薪工时制度，字典
     */
    private String salaryWorkHour;

    /**
     * 工时制，枚举
     */
    private String workHour;

    /**
     * 标准工时，单位分钟
     */
    private BigDecimal stdWorkTime;

    /**
     * 实际工时，单位分钟
     */
    private BigDecimal actualWorkTime;

    /**
     * 中班天数
     */
    private BigDecimal middleShiftDays;

    /**
     * 夜班天数
     */
    private BigDecimal nightShiftDays;

    /**
     * 本周期调休总计
     */
    private BigDecimal curPeriodCompensatorySum;

    /**
     * 上周期调休池
     */
    private BigDecimal lastPeriodCompensatory;

    /**
     * 本周期调休池
     */
    private BigDecimal curPeriodCompensatory;

    /**
     * 本月结薪
     */
    private BigDecimal curMonthSalarySettlement;

    /**
     * 备注
     */
    private String remark;

    private String tenantId;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    private Integer waGroupId;

    private String waGroupName;

    @Autowired
    private IEmpWorkingHoursSettlementRepository empWorkingHoursSettlementRepository;

    public void doInitCrtField() {
        UserInfo userInfo = UserContext.preCheckUser();
        this.setTenantId(userInfo.getTenantId());
        this.setDeleted(0);
        this.setCreateBy(userInfo.getUserId());
        this.setCreateTime(System.currentTimeMillis());
        this.setUpdateBy(this.getCreateBy());
        this.setUpdateTime(this.getCreateTime());
    }

    public WaEmpWorkingHoursSettlementDo getById(Long ruleId) {
        return empWorkingHoursSettlementRepository.getById(ruleId);
    }

    public void updateById(WaEmpWorkingHoursSettlementDo updateData) {
        empWorkingHoursSettlementRepository.updateById(updateData);
    }

    public void save(WaEmpWorkingHoursSettlementDo addData) {
        empWorkingHoursSettlementRepository.insert(addData);
    }

    public void deleteById(Long id) {
        empWorkingHoursSettlementRepository.deleteById(id);
    }

    public PageList<WaEmpWorkingHoursSettlementDo> getPageList(String tenantId, Integer attendanceMonth, Integer waGroupId, Long organize,
                                                               List<String> workHours, List<String> salaryWorkHours,
                                                               String keywords, String dataScope, MyPageBounds pageBounds) {
        return empWorkingHoursSettlementRepository.getPageList(tenantId, attendanceMonth, waGroupId, organize, workHours, salaryWorkHours, keywords, dataScope, pageBounds);
    }

    public int deleteEmpWorkingHourSettlementList(String tenantId, Integer attendanceMonth, Integer waGroupId, List<Long> empIds) {
        return empWorkingHoursSettlementRepository.deleteEmpWorkingHourSettlementList(tenantId, attendanceMonth, waGroupId, empIds);
    }

    public int saveEmpWorkingHourSettlementList(List<WaEmpWorkingHoursSettlementDo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        List<WaEmpWorkingHoursSettlement> models = ObjectConverter.convertList(list, WaEmpWorkingHoursSettlement.class);
        return empWorkingHoursSettlementRepository.saveEmpWorkingHourSettlementList(models);
    }
}