package com.caidaocloud.attendance.service.application.feign.workflow;

import com.caidaocloud.attendance.core.workflow.dto.WfProcessRecordDto;
import com.caidaocloud.attendance.service.application.dto.workflow.WfTaskApproveDTO;
import com.caidaocloud.attendance.service.application.dto.workflow.WfTaskBackDTO;
import com.caidaocloud.attendance.service.application.dto.workflow.WfTaskUrgeDTO;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class WfOperateFeignFallBack implements IWfOperateFeignClient {
    @Override
    public Result approveTask(WfTaskApproveDTO wfApproveTaskDTO) {
        return Result.fail();
    }

    @Override
    public Result backTask(WfTaskBackDTO wfTaskBackDTO) {
        return Result.fail();
    }

    @Override
    public Result<Boolean> checkDefEnabled(String funCode) {
        return Result.fail();
    }

    @Override
    public Result urgeTask(WfTaskUrgeDTO wfTaskUrgeDTO) {
        return Result.fail();
    }

    @Override
    public Result<List<WfProcessRecordDto>> getRecord(String businessKey) {
        return Result.fail();
    }
}
