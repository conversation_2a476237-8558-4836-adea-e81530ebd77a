package com.caidaocloud.attendance.service.application.feign;

import com.caidaocloud.attendance.service.application.dto.AuthRoleScopeFilterDetail;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AuthFeignFallBack implements IAuthFeignClient {
    @Override
    public Result<List<AuthRoleScopeFilterDetail>> getScopeBySubject(String identifier, Long subjectId) {
        return Result.fail();
    }
}
