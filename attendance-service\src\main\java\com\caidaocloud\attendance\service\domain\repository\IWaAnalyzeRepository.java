package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.dto.EmpRegisterTimeDto;
import com.caidaocloud.attendance.service.domain.entity.WaAnalyzeDo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/9/18
 */
public interface IWaAnalyzeRepository {

    Map<String, Object> getStatisticsSummaryCount(String belongOrgId, String[] orgIds, Long startDate, Long endDate, String scope);

    PageList<Map> getSummaryWorkList(AttendanceBasePage page, Map<String, Object> params);

    List<Map> getSummaryWorkRate(Map<String, Object> params);

    List<Map> getSummaryTimeRate(Map<String, Object> params);

    List<Map> getSummaryLtRate(Map<String, Object> params);

    AttendancePageResult<WaAnalyzeDo> getWaAnalyzePageList(AttendanceBasePage basePage, String belongOrgId, List<Long> empIdList, Long startDate, Long endDate);

    List<WaAnalyzeDo> getWaAnalyzeByRegisterRecordIds(String belongOrgId, List<Integer> registerRecordIds);

    List<WaAnalyzeDo> getWaAnalyzeList(String belongOrgId, Long empId, Long startDate, Long endDate);

    List<Map> groupRegisterTimeByEmps(String tenantId, long startTime, long endTime, List<String> empIds);

    List<EmpRegisterTimeDto> listRegisterTimeByEmps(String tenantId, long startTime, long endTime, List<String> empIds);
}
