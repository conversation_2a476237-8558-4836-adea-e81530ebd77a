package com.caidaocloud.attendance.service.application.service.user;

import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.domain.entity.AttendanceEmpGroupDo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class EmployeeGroupService {
    //    @Resource
//    private MasterFeignClient masterFeignClient;
    @Resource
    private AttendanceEmpGroupDo attendanceEmpGroupDo;
    @Resource
    private ISessionService sessionService;

//    public void saveOrUpdateById(EmployeeGroupDto employeeGroupDto){
//        Result result = masterFeignClient.saveEmployeeGroup(employeeGroupDto);
//        if(null == result || !result.isSuccess() || null == result.getData()){
//            throw new ServerException("保存使用范围分组失败");
//        }
//    }

    public void saveOrUpdate(EmployeeGroupDto empGroupDto) {
//        Result result = masterFeignClient.saveOrUpdate(employeeGroupDto);
//        if(null == result || !result.isSuccess() || null == result.getData()){
//            throw new ServerException("保存使用范围分组失败");
//        }

        AttendanceEmpGroupDo empGroup = ObjectConverter.convert(empGroupDto, AttendanceEmpGroupDo.class);
        val userInfo = sessionService.getUserInfo();
        AttendanceEmpGroupDo empGroupDb = attendanceEmpGroupDo.selectByKeyAndGroupType(empGroupDto.getBusinessKey(),
                empGroupDto.getGroupType(), null == userInfo ?
                        empGroupDto.getDefaultTenantId() : Long.valueOf(userInfo.getBelongOrgId()));
        if (null != empGroupDb.getEmpGroupId()) {
            attendanceEmpGroupDo.updateByBusKey(empGroup, empGroupDto.getDefaultUserId(), empGroupDto.getDefaultTenantId());
        } else {
            attendanceEmpGroupDo.insert(empGroup, empGroupDto.getDefaultUserId(), empGroupDto.getDefaultTenantId());
        }
    }

    public EmployeeGroupDto getEmployeeGroup(String businessKey, String groupType) {
//        Result<EmployeeGroupDto> result = masterFeignClient.getEmployeeGroup(businessKey, groupType);
//        if(null == result || !result.isSuccess() || null == result.getData()){
//            return null;
//        }
//
//        return result.getData();

        if (StringUtil.isBlank(businessKey) || StringUtil.isBlank(groupType)) {
            return null;
        }
        val userInfo = sessionService.getUserInfo();
        AttendanceEmpGroupDo empGroup = attendanceEmpGroupDo.selectByKeyAndGroupType(businessKey, groupType, Long.valueOf(userInfo.getBelongOrgId()));
        EmployeeGroupDto empGroupDto = ObjectConverter.convert(empGroup, EmployeeGroupDto.class);
        return empGroupDto;
    }

    public void removeBusKey(String businessKey, String groupType) {
//        Result result = masterFeignClient.removeBusKey(businessKey, groupType);
//        log.info("removeBusKey businessKey ={},groupType={}, result={}", businessKey, groupType, JSON.toJSONString(result));
//        if(null == result || !result.isSuccess()){
//            throw new ServerException("删除适用范围分组失败");
//        }
        if (StringUtil.isEmpty(businessKey) || StringUtil.isEmpty(groupType)) {
            throw new ServerException("删除适用范围分组失败");
        }
        val userInfo = sessionService.getUserInfo();
        AttendanceEmpGroupDo empGroup = attendanceEmpGroupDo.selectByKeyAndGroupType(businessKey, groupType, Long.valueOf(userInfo.getBelongOrgId()));
        if (null == empGroup) {
            return;
        }

        attendanceEmpGroupDo.deleteById(empGroup.getEmpGroupId());
    }

    public List<EmployeeGroupDto> getEmployeeGroups(List<String> businessKeys, String groupType, String belongOrgId) {
        if (CollectionUtils.isEmpty(businessKeys) || StringUtil.isBlank(groupType)) {
            return new ArrayList<>();
        }

        List<AttendanceEmpGroupDo> items = attendanceEmpGroupDo.selectByKeysAndGroupType(businessKeys, groupType, Long.valueOf(belongOrgId));
        return ObjectConverter.convertList(items, EmployeeGroupDto.class);
    }
}
