package com.caidaocloud.attendance.service.application.dto.msg;

import com.caidaocloud.attendance.service.application.dto.PhoneSimple;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 钉钉待办通知dto
 * @Date 2023/2/24 下午2:24
 * @Version 1.0
 **/
@Data
@Accessors(chain = true)
public class DingtalkToDoNoticeDto {

    /**
     * 第三方id 钉钉待办任务id
     */
    private String thirdId;

    /**
     * 钉钉待办任务与审批任务关联表的主键ID
     */
    private String dingCaidaoId;

    /**
     * 通知标题
     */
    private String subject;

    /**
     * 创建者的unionId
     */
    private String creatorId;


    /**
     * 待办备注描述，最大长度4096
     */
    private String description;


    /**
     * 截止时间，Unix时间戳，单位毫秒
     */
    private Long dueTime;

    /**
     * 执行者的unionId，最大数量1000
     */
    private List<String> executorIds;

    /**
     * 参与者的unionId，最大数量1000
     */
    private List<String> participantIds;

    /**
     * 详情页url跳转地址
     */
    private DetailUrl detailurl;

    /**
     * 用户手机
     */
    private List<PhoneSimple> mobiles;

    /**
     * 才到流程业务key
     */
    private String businessKey;

    /**
     * 才到流程任务id
     */
    private String taskId;


    /**
     * 详情页url跳转地址
     */
    @Data
    public static class DetailUrl {

        /**
         * APP端详情页url跳转地址
         */
        private String appUrl;


        /**
         * PC端详情页url跳转地址
         */
        private String pcUrl;
    }
}

 
    
    
    
    