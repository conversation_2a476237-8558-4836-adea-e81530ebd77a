package com.caidaocloud.attendance.service.application.enums;

/**
 * 加班规则：有效时长计算单位
 */
public enum OtRuleValidTimeCalUnitEnum {
    SECOND("SECOND", "秒"),
    MINUTE("MINUTE", "分钟");

    private String code;

    private String name;

    OtRuleValidTimeCalUnitEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public static String getName(String code) {
        for (OtRuleValidTimeCalUnitEnum c : OtRuleValidTimeCalUnitEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
