package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.service.domain.repository.IOvertimeTransferRuleRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaOvertimeTransferRulePo;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Data
@Service
public class OvertimeTransferRuleDo {

    private Long ruleId;
    private String tenantId;
    private String ruleName;
    private Integer compensateType;
    private Integer leaveTypeId;
    private Integer transferRule;
    private Object transferPeriods;
    private Float transferTime;
    private String note;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private Float timeDuration;

    @Autowired
    private IOvertimeTransferRuleRepository overtimeTransferRuleRepository;

    public List<OvertimeTransferRuleDo> getOvertimeTransferRules(String tenantId, Long ruleId, String ruleName) {
        List<WaOvertimeTransferRulePo> list = overtimeTransferRuleRepository.getOvertimeTransferRules(tenantId, ruleId, ruleName);
        return ObjectConverter.convertList(list, OvertimeTransferRuleDo.class);
    }

    public void save(OvertimeTransferRuleDo overtimeTransferRule) {
        if (null != overtimeTransferRule) {
            WaOvertimeTransferRulePo overtimeTransferRulePo = ObjectConverter.convert(overtimeTransferRule, WaOvertimeTransferRulePo.class);
            overtimeTransferRuleRepository.save(overtimeTransferRulePo);
        }
    }

    public void update(OvertimeTransferRuleDo overtimeTransferRule) {
        if (null != overtimeTransferRule) {
            WaOvertimeTransferRulePo overtimeTransferRulePo = ObjectConverter.convert(overtimeTransferRule, WaOvertimeTransferRulePo.class);
            overtimeTransferRuleRepository.update(overtimeTransferRulePo);
        }
    }

    public OvertimeTransferRuleDo getOvertimeTransferRule(Long ruleId) {
        WaOvertimeTransferRulePo overtimeTransferRule = overtimeTransferRuleRepository.getOvertimeTransferRule(ruleId);
        return ObjectConverter.convert(overtimeTransferRule, OvertimeTransferRuleDo.class);
    }

    public void delete(Long ruleId, Long userId) {
        WaOvertimeTransferRulePo po = new WaOvertimeTransferRulePo();
        po.setRuleId(ruleId);
        po.setDeleted(1);
        po.setUpdateBy(userId);
        po.setUpdateTime(DateUtil.getCurrentTime(true));
        overtimeTransferRuleRepository.delete(po);
    }

    public List<OvertimeTransferRuleDo> getOvertimeTransferRules(List<Long> ruleIds) {
        List<WaOvertimeTransferRulePo> list = overtimeTransferRuleRepository.getOvertimeTransferRules(ruleIds);
        return ObjectConverter.convertList(list, OvertimeTransferRuleDo.class);
    }
}

