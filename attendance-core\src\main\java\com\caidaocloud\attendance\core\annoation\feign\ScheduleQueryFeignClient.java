package com.caidaocloud.attendance.core.annoation.feign;

import com.caidaocloud.attendance.core.wa.dto.shift.ListScheduleQueryDto;
import com.caidaocloud.attendance.core.wa.vo.SdkListEmpShiftForLeaveVo;
import com.caidaocloud.attendance.core.wa.vo.WaShiftDefVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 员工排班查询
 *
 * <AUTHOR>
 * @Date 2025/2/15
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = ScheduleQueryFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "scheduleQueryFeignClient")
public interface ScheduleQueryFeignClient {

    @ApiOperation("查询员工排班列表")
    @PostMapping("/api/attendance/schedule/query/v1/list")
    Result<List<WaShiftDefVo>> getEmpCalendarShiftList(@RequestBody ListScheduleQueryDto queryDto);

    @ApiOperation("申请部分销假时-根据休假日期查询班次信息")
    @GetMapping("/api/attendance/schedule/query/v1/leaveCancel/list")
    Result<List<SdkListEmpShiftForLeaveVo>> getEmpShiftForLeaveCancel(@RequestParam("leaveId") Integer leaveId, @RequestParam("date") Long date, @RequestParam("cancelType") Integer cancelType);
}
