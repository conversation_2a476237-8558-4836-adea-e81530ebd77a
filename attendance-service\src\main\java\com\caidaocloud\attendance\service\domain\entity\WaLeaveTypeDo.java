package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.wa.mybatis.model.WaLeaveType;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.enums.LeaveUpperTypeEnum;
import com.caidaocloud.attendance.service.domain.repository.ILeaveTypeRepository;
import com.caidaocloud.attendance.service.interfaces.dto.EmpFixLeaveTypeDto;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveTypeReqDto;
import com.caidaocloud.util.ObjectConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 假期类型DO
 *
 * <AUTHOR>
 * @Date 2021/3/4
 */
@Slf4j
@Data
@Service
public class WaLeaveTypeDo {
    private Integer leaveTypeId;
    private String leaveName;
    private String leaveCode;
    private Integer leaveType;
    private Integer acctTimeType;
    private Float roundTimeUnit;
    private Boolean isCheckMinTime;
    private Float minLeaveTime;
    private Boolean isCheckMaxTime;
    private Float maxLeaveTime;
    private Boolean isRestDay;
    private Boolean isLegalHoliday;
    private Boolean isUploadFile;
    private Boolean isEmpShow;
    private Boolean isUsedInAdvance;
    private Boolean isUsedInProbation;
    private String belongOrgid;
    private String rmk;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private Integer quotaItType;
    private Integer quotaFour2fiveRule;
    private Integer useItType;
    private Integer useFour2fiveRule;
    private Float maxMonthTime;
    private Boolean isCheckMonthTime;
    private Long genderType;
    private Integer orders;
    private Boolean isSumShow;
    private Float minFileCheckTime;
    private Integer proSettingId;
    private Float quotaMax;
    private Boolean isCheckQuarterTime;
    private Float maxQuarterTime;
    private Boolean isCheckYearTime;
    private Float maxYearTime;
    private Object leaveNameLang;
    private Boolean isOpenTimeControl;
    private Integer timeControlType;
    private Float controlTimeDuration;
    private Object superiorQuotaSettingIds;
    private Boolean isCheckMonthNum;
    private Boolean isCheckQuarterNum;
    private Boolean isCheckYearNum;
    private Integer maxMonthNum;
    private Integer maxQuarterNum;
    private Integer maxYearNum;
    private Object timelinessControlJsonb;
    private Boolean isRetentionPriority;
    private Integer timescale;
    private Boolean isAdjustWorkHour;
    private Boolean isFileupdNotice;
    private String fileupdNoticeContent;
    private Boolean linkOutsideSign;
    private Boolean quotaCheck;
    private Integer probationMinTime;
    private Boolean isCalWorktime;
    private Boolean isNotAnalyze;
    private Boolean isWarnMsg;
    private String warnMsg;
    private Object warnMsgLang;
    private Boolean isWorkhourConvert;
    private Integer convertDuration;
    private String quotaSorts;
    private String icon;
    private Boolean continuousApplication;
    private Integer quotaRestrictionType;
    private Integer quotaType;
    private Float minMonthTime;
    private Float minYearTime;
    private Integer upperLevel;
    private Boolean reasonMust;
    private Integer leaveLimitSwitch;
    private Integer leaveLimitDays;
    private Integer leaveCancelType;
    private String leaveCancelRemark;
    private Integer controlTimeUnit;
    private String leaveArea;
    @ApiModelProperty("销假类型，多个逗号隔开")
    private String allowedCancelType;
    @ApiModelProperty("允许多次销假开关字段，默认true(开启)，false(关闭)")
    private Boolean allowedMultipleCancel;
    @ApiModelProperty("销假后补附件开关字段，默认false(关闭)，true(开启)")
    private Boolean enclosureLater;
    @ApiModelProperty("是否允许查看余额，默认true(允许)，false(不允许)")
    private Boolean allowViewQuota;
    private String cancelAttachmentRequired;
    private Integer cancelTime;
    private Integer cancelTimeType;
    private Integer cancelTimeDay;
    private Integer viewType;
    private Integer applyNumberType;
    private String upperLevelQuotaCheckSymbol;
    private Float upperLevelQuotaCheck;
    private Long upperLevelQuotaTypeId;
    private String i18nLeaveName;
    private String i18nLeaveTypeName;

    // 其他表字段
    private String acctTimeTypeTxt;
    private String isRestDaytxt;
    private String isLegalHolidayTxt;
    private String genderTypeTxt;
    private String leaveTypeName;
    private String leaveTypeDefCode;
    private Boolean displayQuotaDetail;
    private Boolean paidLeave;

    @Autowired
    private ILeaveTypeRepository leaveTypeRepository;

    public WaLeaveTypeDo selectById(Integer id) {
        return leaveTypeRepository.selectById(id);
    }

    public List<WaLeaveTypeDo> getListByIds(List<Integer> ids) {
        return leaveTypeRepository.selectListByIds(ids);
    }

    @CDText(exp = {"genderTypeTxt" + TextAspect.DICT_G}, classType = WaLeaveTypeDo.class)
    public AttendancePageResult<WaLeaveTypeDo> getWaLeaveTypePageList(LeaveTypeReqDto reqDto) {
        return leaveTypeRepository.getWaLeaveTypePageList(reqDto);
    }

    public int getLeaveTypeCountByCode(String belongOrgId, Integer excludeId, String code) {
        return leaveTypeRepository.getLeaveTypeCountByCode(belongOrgId, excludeId, code);
    }

    public List<WaLeaveTypeDo> getLeaveTypeByGroupId(String belongOrgId, Integer waGroupId) {
        return leaveTypeRepository.getLeaveTypeByGroupId(belongOrgId, waGroupId);
    }

    public List<WaLeaveTypeDo> getLeaveTypeBySobId(String belongOrgId, Integer sobId) {
        return leaveTypeRepository.getLeaveTypeBySobId(belongOrgId, sobId);
    }

    @CDText(exp = {"genderTypeTxt" + TextAspect.DICT_G}, classType = WaLeaveTypeDo.class)
    public List<WaLeaveTypeDo> getLeaveTypes(String belongOrgId, Integer restrictionType, Integer quotaType) {
        return leaveTypeRepository.getLeaveTypes(belongOrgId, restrictionType, quotaType);
    }

    public void deleteLeaveTypeByIds(String belongOrgId, List<Integer> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            leaveTypeRepository.deleteLeaveTypeByIds(belongOrgId, ids);
        }
    }

    public List<EmpFixLeaveTypeDto> getEmpFixLeaveTypes(Long empid, String belongOrgId) {
        return leaveTypeRepository.getEmpFixLeaveTypes(empid, belongOrgId);
    }

    public List<WaLeaveTypeDo> getLeaveTypesByIds(String belongOrgId, List<Integer> leaveTypeIds) {
        return leaveTypeRepository.getLeaveTypesByIds(belongOrgId, leaveTypeIds);
    }

    public WaLeaveTypeDo getLeaveTypeByType(String belongOrgid, Integer leaveType) {
        return leaveTypeRepository.getLeaveTypeByType(belongOrgid, leaveType);
    }

    public List<Map> getLeaveTypeList(String belongOrgId, Long empId, Long gender, Integer quotaType, Integer cityId) {
        return leaveTypeRepository.getLeaveTypeList(belongOrgId, empId, gender, quotaType, cityId);
    }

    public List<WaLeaveTypeDo> getLeaveTypesByBelongOrgId(String belongOrgId, Integer quotaType) {
        return leaveTypeRepository.getLeaveTypesByBelongOrgId(belongOrgId, quotaType);
    }

    public void update(WaLeaveTypeDo waLeaveType) {
        WaLeaveType model = ObjectConverter.convert(waLeaveType, WaLeaveType.class);
        leaveTypeRepository.update(model);
    }

    public void deleteLeaveTypeById(String belongOrgId, Integer leaveTypeId) {
        leaveTypeRepository.deleteLeaveTypeById(belongOrgId, leaveTypeId);
    }

    public void updateLeaveType(String leaveName, Integer leaveType, String belongOrgId) {
        leaveTypeRepository.updateLeaveType(leaveName, leaveType, belongOrgId);
    }

    public int getLeaveTypeCountByName(String belongOrgId, Integer excludeId, String name) {
        return leaveTypeRepository.getLeaveTypeCountByName(belongOrgId, excludeId, name);
    }

    public void updateBatch(List<WaLeaveTypeDo> list) {
        leaveTypeRepository.updateBatch(list);
    }

    /**
     * 申请休假时-上级假期余额校验：检查是否满足优先假期额度申请规则
     *
     * @param usableQuota
     * @param waLeaveType
     * @param upperLevelLeaveType
     * @param upperType
     * @return true 满足、false 不满足
     */
    public boolean doCheckUpperLevelQuota(double usableQuota, WaLeaveType waLeaveType, WaLeaveType upperLevelLeaveType,
                                          LeaveUpperTypeEnum upperType) {
        boolean ifCheckUpperLeavel = true;
        if (LeaveUpperTypeEnum.QUOTA_TYPE != upperType || waLeaveType.getUpperLevelQuotaCheck() == null
                || StringUtils.isBlank(waLeaveType.getUpperLevelQuotaCheckSymbol())) {
            return ifCheckUpperLeavel;
        }
        // 上级假期余额校验时长
        BigDecimal upperLevelQuotaCheck = BigDecimal.valueOf(waLeaveType.getUpperLevelQuotaCheck());
        if (upperLevelLeaveType.getAcctTimeType() == 2) {//小时
            upperLevelQuotaCheck = upperLevelQuotaCheck.multiply(new BigDecimal(60));
        }
        double upperLevelQuotaLimit = upperLevelQuotaCheck.doubleValue();

        // 上级假期余额校验符号：eq、gt、ge、lt、le
        String upperLevelQuotaCheckSymbol = waLeaveType.getUpperLevelQuotaCheckSymbol();
        switch (upperLevelQuotaCheckSymbol) {
            case "eq":
                ifCheckUpperLeavel = usableQuota == upperLevelQuotaLimit;
                break;
            case "gt":
                ifCheckUpperLeavel = usableQuota > upperLevelQuotaLimit;
                break;
            case "ge":
                ifCheckUpperLeavel = usableQuota >= upperLevelQuotaLimit;
                break;
            case "lt":
                ifCheckUpperLeavel = usableQuota < upperLevelQuotaLimit;
                break;
            case "le":
                ifCheckUpperLeavel = usableQuota <= upperLevelQuotaLimit;
                break;
            default:
                ifCheckUpperLeavel = usableQuota >= upperLevelQuotaLimit;
                break;
        }

        return ifCheckUpperLeavel;
    }

    public List<Map> getEmpLeaveDayTimeByEmpId(Map params) {
        return leaveTypeRepository.getEmpLeaveDayTimeByEmpId(params);
    }
}
