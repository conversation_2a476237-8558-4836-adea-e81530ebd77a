package com.caidaocloud.attendance.service.application.enums;

/**
 * 班次设置：弹性打卡规则
 */
public enum FlexbleWorkTypeEnum {
    LATE_TO_LATE(1, "晚到晚走,早到早走"),
    GO_LATE_LATE(2, "前一天晚走,第二天晚到");

    private Integer index;

    private String name;


    FlexbleWorkTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (FlexbleWorkTypeEnum c : FlexbleWorkTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
