package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.ICompensatoryQuotaRecordRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaCompensatoryQuotaRecord;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Data
@Service
public class WaCompensatoryQuotaRecordDo {
    private Long id;
    private String tenantId;
    private Long quotaId;
    private Integer detailId;
    private Float carryDuration;
    private Integer status;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;

    @Autowired
    private ICompensatoryQuotaRecordRepository compensatoryQuotaRecordRepository;

    public void save(WaCompensatoryQuotaRecordDo model) {
        if (null == model) {
            return;
        }
        compensatoryQuotaRecordRepository.save(ObjectConverter.convert(model, WaCompensatoryQuotaRecord.class));
    }

    public void update(WaCompensatoryQuotaRecordDo model) {
        if (null == model) {
            return;
        }
        compensatoryQuotaRecordRepository.update(ObjectConverter.convert(model, WaCompensatoryQuotaRecord.class));
    }

    public int save(List<WaCompensatoryQuotaRecordDo> models) {
        if (CollectionUtils.isEmpty(models)) {
            return 0;
        }
        return compensatoryQuotaRecordRepository.save(ObjectConverter.convertList(models, WaCompensatoryQuotaRecord.class));
    }

    public int update(List<WaCompensatoryQuotaRecordDo> models) {
        if (CollectionUtils.isEmpty(models)) {
            return 0;
        }
        return compensatoryQuotaRecordRepository.update(ObjectConverter.convertList(models, WaCompensatoryQuotaRecord.class));
    }

    public List<WaCompensatoryQuotaRecordDo> getWaCompensatoryQuotaRecord(String tenantId, List<Integer> detailIds, List<Integer> status) {
        return compensatoryQuotaRecordRepository.getWaCompensatoryQuotaRecord(tenantId, detailIds, status);
    }

    public List<WaCompensatoryQuotaRecordDo> getCompensatoryQuotaRecord(String tenantId, List<Long> quotaIds, List<Integer> status) {
        return compensatoryQuotaRecordRepository.getCompensatoryQuotaRecord(tenantId, quotaIds, status);
    }

    public int deleteByQuotaIds(String tenantId, List<Long> quotaIds) {
        if (null == quotaIds || quotaIds.size() ==0) {
            return 0;
        }
        return compensatoryQuotaRecordRepository.deleteByQuotaIds(tenantId, quotaIds);
    }
}
