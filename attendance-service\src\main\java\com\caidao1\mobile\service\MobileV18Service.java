package com.caidao1.mobile.service;

import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper2;
import com.caidao1.wa.mybatis.mapper.WaGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaMapper;
import com.caidao1.wa.mybatis.model.WaGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MobileV18Service {
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private WaMapper waMapper;

    /**
     * 根据时间计算当前时间所在的考勤周期
     *
     * @param dateTime
     * @param cyleStartdate
     * @return
     * @throws ParseException
     */
    public Map calculateCycleTime(Long dateTime, Integer cyleStartdate) throws ParseException {
        Long preMonthBegin = DateUtilExt.getMonth(dateTime, -1, 1);//上个月开始时间
        Long preMonthEnd = DateUtilExt.getMonthEnd(preMonthBegin);//上个月的结束时间
        Long curMonthBegin = DateUtilExt.getMonth(dateTime, 0, 1);//本月开始时间
        Long curMonthEnd = DateUtilExt.getMonthEnd(curMonthBegin);//本月结束时间
        Long curCycleBegin = null;//当前时间所在的考勤周期开始时间
        Long curCycleEnd = null;//当前时间所在的考勤周期结束时间
        if (cyleStartdate > 1) {
            Long addDayMin = Long.valueOf((cyleStartdate - 1) * 86400);
            preMonthBegin += addDayMin;
            preMonthEnd += addDayMin;
            curMonthBegin += addDayMin;
            curMonthEnd += addDayMin;
        }
        //加班的时间不允许跨考勤周期，校验加班时间是否跨考勤周期
        //当前时间的考勤周期
        if (dateTime >= preMonthBegin && dateTime <= preMonthEnd) {
            curCycleBegin = preMonthBegin;
            curCycleEnd = preMonthEnd;
        } else if (dateTime >= curMonthBegin && dateTime <= curMonthEnd) {
            curCycleBegin = curMonthBegin;
            curCycleEnd = curMonthEnd;
        } else {
            return null;
        }
        Map resultMap = new HashMap();
        resultMap.put("cycleBegin", curCycleBegin);
        resultMap.put("cycleEnd", curCycleEnd);
        return resultMap;
    }

    /**
     * 获取员工指定时间所在的考勤周期
     *
     * @param empid 员工ID
     * @param time  时间
     * @return
     * @throws ParseException
     */
    public Map getEmpCycleDate(Long empid, Long time) throws ParseException {
        //查询员工所在的考勤分组
        Map groupParams = new HashMap();
        groupParams.put("empid", empid);
        List<Map> listEmpWaGroup = waMapper.listEmpWaGroup(groupParams);
        if (CollectionUtils.isNotEmpty(listEmpWaGroup)) {
            //有考勤分组
            Integer waGroupId = (Integer) listEmpWaGroup.get(0).get("wa_group_id");
            //2、查询考勤分组信息
            WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waGroupId);
            if (waGroup != null) {
                //获取当前时间所在的考勤周期
                return this.calculateCycleTime(time, waGroup.getCyleStartdate());
            }
        }
        return null;
    }
}
