package com.caidaocloud.attendance.service.application.enums;

public enum  ConvertRuleEnum {

    NOW(1, "生成时折算"),
    AFTER(2, "到期时折算");

    private Integer index;

    private String name;

    ConvertRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ConvertRuleEnum c : ConvertRuleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
