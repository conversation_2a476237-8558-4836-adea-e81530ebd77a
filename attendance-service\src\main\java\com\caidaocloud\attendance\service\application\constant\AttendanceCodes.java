package com.caidaocloud.attendance.service.application.constant;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/8/11 18:51
 * @Description:
 **/
public class AttendanceCodes {
    /**
     * 有效期开始时间为空
     */
    public static final int START_DATE_EMPTY = 10009;

    /**
     * 10010
     * 有效期结束时间为空
     */
    public static final int END_DATE_EMPTY = 10010;

    /**
     * 10013
     * 开始时间不允许大于结束时间
     */
    public static final int STARTTIME_CANNOTBE_GE_ENDTIME = 10013;

    /**
     * 10014
     * 开始时间-结束时间不能相同
     */
    public static final int STARTTIME_CANNOTBE_EQ_ENDTIME = 10014;

    /**
     * 10016
     * 请填写事由
     */
    public static final int PLEASE_WRITE_REASON = 10015;

    /**
     * 10016
     * 员工不存在
     */
    public static final int EMP_NOT_EXIST = 10016;

    /**
     * 10017
     * 员工未排班
     */
    public static final int EMP_NOT_SHIFT = 10017;

    /**
     * 10018
     * 保存失败
     */
    public static final int SAVE_FAILED = 10018;

    /**
     * 10019
     * 查看详情失败
     */
    public static final int GET_DETAIL_FAILED = 10019;

    /**
     * 10020
     * 查看列表失败
     */
    public static final int GET_LIST_FAILED = 10020;

    /**
     * 10021
     * 已被引用，不允许删除
     */
    public static final int DELETE_NOT_ALLOW = 10021;

    /**
     * 10022
     * 删除失败
     */
    public static final int DELETE_FAILED = 10022;

    /**
     * 10023
     * 同步失败
     */
    public static final int SYNC_FAILED = 10023;

    /**
     * 10024
     * 用户信息查询失败
     */
    public static final int USER_INFO_EMPTY = 10024;

    /**
     * 撤销失败
     */
    public static final int REVOKE_FAILED = 10025;

    /**
     * 员工%s未排班
     */
    public static final int EMP_DAY_NOT_SHIFT = 10026;

    /**
     * 名称重复
     */
    public static final int NAME_REPEAT = 10027;

    /**
     * 名称为必填项
     */
    public static final int NAME_NOT_EMPTY = 10028;

    /**
     * 名称限定为%s个字符
     */
    public static final int NAME_CHARACTER_LENGTH_LIMITED = 10029;

    /**
     * 获取下拉列表失败
     */
    public static final int SELECT_OPTION_FAILED = 10030;
    /**
     * 天
     */
    public static final int DAY = 10031;
    /**
     * 小时
     */
    public static final int HOUR = 10032;
    /**
     * 分钟
     */
    public static final int MINUTE = 10033;
    /**
     * 秒
     */
    public static final int SECOND = 10034;
    /**
     * 上半天
     */
    public static final int A_HALF_DAY = 10035;
    /**
     * 下半天
     */
    public static final int P_HALF_DAY = 10036;

    /**
     * 考勤方案不存在
     */
    public static final int ATTENDANCE_PLAN_NOT_EXIST = 200000;

    /**
     * 最大加班时长为空
     */
    public static final int OUT_LIMIT_EMPTY = 200001;

    /**
     * 超出上限控制为空
     */
    public static final int OUT_LIMIT_CONTROL_EMPTY = 200002;

    /**
     * 最大加班时长不能超过999
     */
    public static final int OUT_LIMIT_TOO_BIG = 200003;

    /**
     * 请勿选择自己作为上级
     */
    public static final int UPPER_LEVEL_ERROR = 200200;

    /**
     * 设置的下限休假时长不允许超过设置的上限休假时长
     */
    public static final int LOWER_EXCEED_UPPER = 200201;

    /**
     * 休假类型已存在
     */
    public static final int LEAVE_TYPE_EXIST = 200202;

    /**
     * 休假类型不存在
     */
    public static final int LEAVE_TYPE_NOT_EXIST = 200203;

    /**
     * 200906
     * 该假期类型已生成配额，无法删除
     */
    public static final int LEAVE_TYPE_NOT_DETLETE_FOR_QUOTA_REL = 200204;

    /**
     * 适用范围为空
     */
    public static final int SCOPE_EMPTY = 200300;

    /**
     * 打卡方案名称为空
     */
    public static final int CLOCK_PLAN_NAME_EMPTY = 200301;

    /**
     * 打卡间隔
     */
    public static final int TIME_INTERVAL = 200302;

    /**
     * 打卡方式为空
     */
    public static final int CLOCK_WAY_EMPTY = 200303;

    /**
     * 打卡地点为空
     */
    public static final int SITE_EMPTY = 200304;

    /**
     * WiFi配置为空
     */
    public static final int WIFI_EMPTY = 200305;

    /**
     * 蓝牙配置为空
     */
    public static final int BLUETOOTH_EMPTY = 200306;

    /**
     * 时间间隔格式不正确
     */
    public static final int TIME_INTERVAL_FORMAT_ERROR = 200307;

    /**
     * 打卡方案为空
     */
    public static final int CLOCK_PLAN_EMPTY = 200308;

    /**
     * 打卡方案不存在
     */
    public static final int CLOCK_PLAN_NOT_EXIST = 200309;

    /**
     * 员工该时间内已存在打卡方案，请修改原方案时间！
     */
    public static final int CLOCK_PLAN_TIME_OVERLAP = 200310;

    /**
     * 员工打卡方案同步进程正在运行中，请稍候再尝试!
     */
    public static final int CLOCK_PLAN_SYNCHRONIZING = 200311;

    /**
     * 补卡说明内容超长
     */
    public static final int CLOCK_PLAN_DESCRIPTION_TOO_LONG = 200312;

    /**
     * 有效期结束时间小于开始时间
     */
    public static final int EMP_CLOCK_PLAN_PERIOD_ERROR = 200313;

    /**
     * 新增数据与系统数据冲突，仅支持对员工最新一条数据的结束时间进行定界以及新增数据
     */
    public static final int NEW_CLOCK_PLAN_TIME_OVERLAP = 200314;

    /**
     * 方案名称%s已存在
     */
    public static final int CLOCK_PLAN_NAME_REPEAT = 200315;

    /**
     * 当前方案的适用范围条件筛选后记录为空，请重新设置
     */
    public static final int CLOCK_PLAN_CONDITION_FILTER_EMPTY = 200316;

    /**
     * 同步员工打卡方案失败,请联系管理员
     */
    public static final int CLOCK_PLAN_SYNC_FAILED = 200317;

    /**
     * 地点简称为空
     */
    public static final int LOCATION_ABBREVIATED_EMPTY = 200318;

    /**
     * 详细地址为空
     */
    public static final int ADDRESS_EMPTY = 200319;

    /**
     * 经度或者纬度为空
     */
    public static final int LNG_LAT_EMPTY = 200320;

    /**
     * GPS范围为空
     */
    public static final int GPS_RANGE_EMPTY = 200321;

    /**
     * 打卡地点不存在
     */
    public static final int CLOCK_LOCATION_NOT_EXIST = 200322;

    /**
     * 已存在相同的bssid
     */
    public static final int ALREADY_EXIST_BSSID = 200323;

    /**
     * 已存在%s个相同的bssid
     */
    public static final int ALREADY_EXIST_SOME_BSSID = 200324;
    /**
     * 未设置打卡方案
     */
    public static final int NO_CHECK_IN_PLAN_SET_UP = 200325;
    /**
     * 补卡原因不得少于%s字
     */
    public static final int BDK_REASON_LESS_LIMIT = 200326;
    /**
     * 补打卡次数已经超过设置上限
     */
    public static final int BDK_NUM_EXCEEDED_UPPER_LIMIT = 200327;

    /**
     * 旷工规则填写的数值长度不允许超过六位（含小数位）
     */

    public static final int KG_VALUE_LENGTH_NOT_EXCEED_SIX_DIGITS = 200401;

    /**
     * 旷工规则填写的数值应不允许超过9999
     */
    public static final int KG_VALUE_NOT_EXCEED_9999 = 200402;

    /**
     * 旷工字段值存在空值
     */
    public static final int KG_VALUE_EXIST_NULL = 200403;

    /**
     * 工作日历已分配，不可删除
     */
    public static final int WORK_CALENDAR_ASSIGNED = 200700;
    /**
     * 工作日历名称不能为空
     */
    public static final int WORK_CALENDAR_NAME_EMPTY = 200701;
    /**
     * 工作日历：%s已存在
     */
    public static final int WORK_CALENDAR_EXISTED = 200702;
    /**
     * 已存在适用于全部员工日历【%s】
     */
    public static final int WORK_CALENDAR_MATCH_ALL_EXISTED = 200703;
    /**
     * 所选适用人员无匹配的员工，请重新选择
     */
    public static final int SELECTED_CONDITION_NO_MATCH_EMP = 200704;
    /**
     * 工作日历生成失败，请检查日历生成配置
     */
    public static final int WORK_CALENDAR_GEN_FAILED = 200705;

    /**
     * 配额不存在
     */
    public static final int EMP_QUOTA_NOT_EXIST = 200800;

    /**
     * 额度规则不存在
     */
    public static final int LEAVE_QUOTA_RULE_DOES_NOT_EXIST = 200801;

    /**
     * 未设置额度发放周期
     */
    public static final int QUOTA_ISSUING_CYCLE_IS_NOT_SET = 200802;

    /**
     * 员工没有维护入职日期
     */
    public static final int EMP_HIRE_DATE_ISNULL = 200803;

    /**
     * 发放周期计算失败
     */
    public static final int DATE_PARSE_ERROR = 200804;
    /**
     * 没有调休配额可使用
     */
    public static final int COMPENSATORY_QUOTA_NO_LEFT = 200805;
    /**
     * %s的调休配额不足
     */
    public static final int COMPENSATORY_QUOTA_NOT_ENOUGH = 200806;
    /**
     * 没有假期配额可使用
     */
    public static final int LEAVE_QUOTA_NO_LEFT = 200807;
    /**
     * %s的假期配额不足
     */
    public static final int LEAVE_QUOTA_NOT_ENOUGH = 200808;
    /**
     * 已提交相同假期类型的申请
     */
    public static final int SAME_LEAVE_APPLY_SUBMIT = 200809;
    /**
     * 请选择员工
     */
    public static final int NO_SELECTED_EMP = 200810;
    /**
     * 请填写年份
     */
    public static final int PERIOD_YEAR_EMPTY = 200811;
    /**
     * 请选择余额类型
     */
    public static final int BALANCE_TYPE_EMPTY = 200812;
    /**
     * 请设置生效日期
     */
    public static final int VALID_START_DATE_EMPTY = 200813;
    /**
     * 请填写本年额度
     */
    public static final int CURRENT_YEAR_QUOTA_EMPTY = 200814;
    /**
     * 请设置上年结转有效期至
     */
    public static final int LAST_YEAR_CARRY_TO_VALID_DATE_EMPTY = 200815;
    /**
     * 选择的余额类型不存在
     */
    public static final int SELECTED_QUOTA_TYPE_NOT_EXIST = 200816;
    /**
     * 员工%s年的%s已存在
     */
    public static final int EMP_ONE_YEAR_ONE_QUOTA_EXISTED = 200817;
    /**
     * 另一个配额生成进程正在运行中，请稍候再尝试
     */
    public static final int ANOTHER_QUOTA_GEN_PROCESS_RUNNING = 200818;
    /**
     * 配额生成失败，请检查假期配额生成规则
     */
    public static final int QUOTA_GEN_FAILED = 200819;
    /**
     * 另一个配额结转进程正在运行中，请稍候再尝试
     */
    public static final int ANOTHER_QUOTA_CARRY_TO_PROCESS_RUNNING = 200820;
    /**
     * 余额调整数据为空
     */
    public static final int ADJUST_QUOTA_EMPTY = 200821;
    /**
     * 本次成功删除%s条数据，失败%s条数据
     */
    public static final int DELETE_COMPENSATORY_REMINDER = 200822;
    /**
     * 调休明细不存在
     */
    public static final int COMPENSATORY_DETAIL_NOT_EXIST = 200823;
    /**
     * 该配额已被使用，不可删除
     */
    public static final int QUOTA_USED_DELETE_NOT_ALLOW = 200824;
    /**
     * 该员工已存在一条为%s至%s的数据。请检查后重新填写
     */
    public static final int QUOTA_ALREADY_EXIST = 200825;
    /**
     * 已存在该员工相同年份的假期额度
     */
    public static final int EMP_QUOTA_ALREADY_EXIST = 200826;
    /**
     * 额度已结转，不可进行编辑
     */
    public static final int EMP_QUOTA_ALREADY_CARRIED = 200827;
    /**
     * 留存有效期不能大于结转到配额有效期
     */
    public static final int EMP_QUOTA_RETAIN_VALIDATE_GREATER_THAN_TO = 200828;
    /**
     * 暂无可结转上年假期配额
     */
    public static final int EMP_QUOTA_RETAIN_SOURCE_NOT_EXIST = 200829;
    /**
     * 上年留存的额度不能小于已使用额度
     */
    public static final int EMP_QUOTA_RETAIN_LESS_THAN_USED = 200830;
    /**
     * 上年留存额度不能为空
     */
    public static final int RETAIN_QUOTA_EMPTY = 200831;
    /**
     * 上年留存额度必须为大于0
     */
    public static final int RETAIN_QUOTA_LESS_ZERO = 200832;
    /**
     * 上年留存有效期不能为空
     */
    public static final int RETAIN_VALIDATE_EMPTY = 200833;
    /**
     * 当前配额类型不支持结转
     */
    public static final int EMP_QUOTA_CARRY_NOT_ALLOW = 200834;
    /**
     * 列表已存在相同结转年份、相同来源假期额度的留存记录
     */
    public static final int EMP_QUOTA_CARRY_ALREADY_EXIST = 200835;
    /**
     * 第%s行假期名称为空，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_LEAVE_NAME_EMPTY = 200836;
    /**
     * 第%s行额度名称为空，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_QUOTA_NAME_EMPTY = 200837;
    /**
     * 第%s行年份为空，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_PERIOD_YEAR_EMPTY = 200838;
    /**
     * 第%s行年份格式不正确，必须为数字，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_PERIOD_YEAR_FORMAT_ERR = 200839;
    /**
     * 第%s行上年留存为空，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_QUOTA_EMPTY = 200840;
    /**
     * 第%s行上年留存格式不正确，必须为数字，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_QUOTA_FORMAT_ERR = 200841;
    /**
     * 第%s行留存有效期至为空，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_VALID_DATE_EMPTY = 200842;
    /**
     * 第%s行账号%s的员工所属考勤方案下无启用的%s假期，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_EMP_GROUP_LEAVE_EMPTY = 200843;
    /**
     * 第%s行额度名称%s不存在，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_QUOTA_NAME_ERR = 200844;
    /**
     * 第%s行配额类型不支持结转，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_QUOTA_TYPE_NOT_ALLOWED = 200845;
    /**
     * 第%s行暂无可结转%s年假期配额%s，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_QUOTA_CAN_NOT_CARRY = 200846;
    /**
     * 第%s行留存有效期大于结转到%s年的假期配额%s的有效期
     */
    public static final int IMPORT_QUOTA_RETAIN_VALIDATE_GREATER_THAN_TO = 200847;
    /**
     * 第%s行假期配额%s已存在%s年的留存记录，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_EMP_QUOTA_CARRY_ALREADY_EXIST = 200848;
    /**
     * 第%s行暂无可结转%s假期配额%s，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_EMP_QUOTA_SOURCE_NOT_EXIST = 200849;
    /**
     * 第%s行留存有效期至%s小于生效日期%s，请检查后重新上传
     */
    public static final int IMPORT_RETAIN_VALID_DATE_LESS_THAN_START_DATE = 200850;
    /**
     * %s,额度更新
     */
    public static final int QUOTA_UPDATE = 200851;
    /**
     * 员工为空
     */
    public static final int EMP_EMPTY = 200900;

    /**
     * 工作日历为空
     */
    public static final int EMP_CALENDAR_EMPTY = 200901;

    /**
     * 有效期开始时间为空
     */
    public static final int CALENDAR_START_DATE = 200902;

    /**
     * 有效期结束时间为空
     */
    public static final int CALENDAR_END_DATE = 200903;

    /**
     * 时间重叠
     */
    public static final int TIME_OVERLAP = 200904;

    /**
     * 不存在
     */
    public static final int NOT_EXIST = 200905;
    /**
     * 导入文件排班周期与表头日期不匹配，请检查后重新上传
     */
    public static final int IMPORT_FILE_NOT_MATCH = 200906;
    /**
     * 导入文件表头日期长度不匹配，请检查后重新上传
     */
    public static final int IMPORT_FILE_HEAD_LENGTH_NOT_MATCH = 200907;
    /**
     * %s单元格的班次%s不存在，请检查后重新上传
     */
    public static final int IMPORT_FILE_SHIFT_NOT_EXIST = 200908;
    /**
     * 第%s行班次为空，请检查后重新上传
     */
    public static final int IMPORT_FILE_SHIFT_EMPTY = 200909;
    /**
     * 第%s行账号为空，请检查后重新上传
     */
    public static final int IMPORT_FILE_WORK_NO_EMPTY = 200910;
    /**
     * 导入文件账号列为必填项，请检查后重新上传
     */
    public static final int IMPORT_FILE_WORK_NO_MUST = 200911;
    /**
     * 导入文件账号%s存在重复，请检查后重新上传
     */
    public static final int IMPORT_FILE_WORK_NO_REPEAT = 200912;
    /**
     * 请在导入文件的账号列填写正确的账号
     */
    public static final int IMPORT_FILE_WORK_NO_ERR = 200913;
    /**
     * 第%s行的账号%s不存在，请检查后重新上传
     */
    public static final int IMPORT_FILE_WORK_NO_NOT_EXIST = 200914;

    /**
     * 加班单据不存在
     */
    public static final int OVERTIME_NOT_EXIST = 201000;

    /**
     * 不允许撤销加班单
     */
    public static final int REVOKE_NOT_ALLOW = 201001;

    /**
     * 额度已被使用不可撤销
     */
    public static final int QUOTA_USED_REVOKE_NOT_ALLOW = 201002;

    /**
     * 201003
     * 当前日期不支持加班单据提交
     */
    public static final int NOT_SET_OT_TYPE = 201003;

    /**
     * 201004
     * 补偿类型不一致，请拆分加班单重新申请
     */
    public static final int COMPENSATE_TYPE_NOTEQ_RETRY = 201004;

    /**
     * 201005
     * 该加班单结转的调休额度已被2024/3/29申请的2024/4/1 上半天～2024/4/1 下半天的调休假使用，请先撤销调休假申请，再撤销该加班申请
     */
    public static final int COMPENSATE_QUOTA_USED = 201005;

    /**
     * 休假单据不存在
     */
    public static final int LEAVE_TIME_NOT_EXIST = 201100;

    /**
     * 不允许撤销休假单
     */
    public static final int LEAVE_REVOKE_NOT_ALLOW = 201101;

    /**
     * 撤销失败
     */
    public static final int LEAVE_REVOKE_FAILED = 201102;

    /**
     * 开始/结束时间为空
     */
    public static final int TIME_EMPTY = 201103;

    /**
     * 开始时间不能大于结束时间
     */
    public static final int START_TIME_GREATER_THAN_END = 201104;

    /**
     * 对不起，您没有请假时长，请重新选择
     */
    public static final int NO_LEAVE_TIME = 201105;

    /**
     * 开始时间-结束时间不能相同
     */
    public static final int START_TIME_EQUAL_END = 201106;

    /**
     * 休假类型必须为产假
     */
    public static final int LEAVE_TYPE_MUST_MATERNITY = 201108;

    /**
     * 子女个数为空
     */
    public static final int CHILD_NUM_EMPTY = 201109;

    /**
     * 开子女出生日期为空
     */
    public static final int CHILD_BIRTHDAY_EMPTY = 201110;

    /**
     * 产假类型为空
     */
    public static final int MATERNITY_TYPE_EMPTY = 201111;

    /**
     * 子女个数不正确
     */
    public static final int CHILD_NUM_ERROR = 201112;
    /**
     * 上级假期%s额度还未使用完，剩余%s天，请先使用%s假期
     */
    public static final int UPPER_LEAVE_DAY = 201113;
    /**
     * %s年%s假期还未使用完，剩余%s天，请先使用%s假期
     */
    public static final int LEAVE_DAY = 201114;
    /**
     * 上级假期%s额度还未使用完，剩余%s小时，请先使用%s假期
     */
    public static final int UPPER_LEAVE_HOUR = 201115;
    /**
     * %s年%s假期还未使用完，剩余%s小时，请先使用%s假期
     */
    public static final int LEAVE_HOUR = 201116;
    /**
     * 休假申请失败
     */
    public static final int LEAVE_APPLY_FAILED = 201117;
    /**
     * 获取计算的休假申请时间失败
     */
    public static final int LEAVE_APPLY_DURATION_FAILED = 201118;
    /**
     * 获取休假日期列表失败
     */
    public static final int LEAVE_DATE_LIST_FAILED = 201119;

    /**
     * 上级假期%s额度还未使用完，请先使用%s假期
     */
    public static final int UPPER_LEAVE_DAY_NO_BALANCE = 201120;
    /**
     * %s年%s假期额度还未使用完，请先使用%s假期
     */
    public static final int LEAVE_DAY_NO_BALANCE = 201121;
    /**
     * 上级假期%s额度还未使用完，请先使用%s假期
     */
    public static final int UPPER_LEAVE_HOUR_NO_BALANCE = 201122;
    /**
     * %s年%s假期额度还未使用完，请先使用%s假期
     */
    public static final int LEAVE_HOUR_NO_BALANCE = 201123;

    /**
     * 请输入撤销原因
     */
    public static final int REASON_EMPTY = 201200;

    /**
     * 签到记录不存在
     */
    public static final int RECORD_NOT_EXIST = 201201;

    /**
     * 只允许撤销补卡
     */
    public static final int CAN_NOT_REVOKE = 201202;

    /**
     * 仅允许撤销审批中和已通过的补卡
     */
    public static final int NOT_ALLOW_REVOKE = 201203;

    /**
     * 考勤明细不存在
     */
    public static final int ANALYZE_NOT_EXIST = 201300;
    /**
     * 已封存的考勤方案不能再次核算
     */
    public static final int SEALED_SOB_CAN_NOT_RECALCULATED = 201301;
    /**
     * 核算周期时间范围应在考勤周期内（%s~%s）
     */
    public static final int ACCOUNT_RANGE_BETWEEN = 201302;
    /**
     * %s(%s)%s的加班单未查到匹配的加班转换规则
     */
    public static final int OVERTIME_NOT_MATCH_TRANSFER_RULE = 201303;
    /**
     * 核算失败，请检查分析规则以及员工考勤数据
     */
    public static final int ANALYZE_FAILED = 201304;
    /**
     * 发送考勤结果消息提醒失败
     */
    public static final int SENDING_RESULT_FAILED = 201305;

    /**
     * 201400
     * 出差类型名称已存在
     */
    public static final int TRAVEL_TYPE_EXIST = 201400;

    /**
     * 201500
     * 出差单据不存在
     */
    public static final int TRAVEL_TIME_NOT_EXIST = 201500;

    /**
     * 201501
     * 出差类型不存在
     */
    public static final int TRAVEL_TYPE_NOT_EXIST = 201501;

    /**
     * 201502
     * 不允许撤销出差单
     */
    public static final int RAVEL_REVOKE_NOT_ALLOW = 201502;

    /**
     * 201503
     * 请勿频繁操作
     */
    public static final int DO_NOT_OPERATE_FREQUENTLY = 201503;

    /**
     * 201504
     * 未选择员工
     */
    public static final int NO_EMPLOYEE_SELECTED = 201504;

    /**
     * 201505
     * 请选择出差时间
     */
    public static final int PLEASE_SELECT_TRAVEL_TIME = 201505;

    /**
     * 201506
     * 出差申请失败
     */
    public static final int TRAVEL_APPLY_FAIL = 201506;

    /**
     * 201507
     * 请上传附件
     */
    public static final int PLEASE_UPLOAD_FILE = 201507;

    /**
     * 201508
     * 存在日期重叠的申请，不能提交
     */
    public static final int EVENT_TIME_OVERLAP = 201508;

    /**
     * 201509
     * 出差时长不能为0
     */
    public static final int TRAVEL_TIME_NOT_ZERO = 201509;

    /**
     * 201510
     * 该出差申请已结转调休，不可撤销
     */
    public static final int TRAVEL_TO_COMPENSATORY_NOT_ALLOWED_REVOKE = 201510;

    /**
     * 201511
     * 该出差申请已有审批中的撤销/废止流程，不可进行此操作
     */
    public static final int TRAVEL_REVOKING_NOT_ALLOWED_REVOKE = 201511;

    /**
     * 201512
     * 不符合申请条件，无法进行出差申请
     */
    public static final int NOT_MATCH_TRAVEL_RULE = 201512;

    /**
     * 申请出差撤销/废止失败
     */
    public static final int REVOKE_OR_ABOLISH_FAILED = 201513;

    /**
     * 撤销出差的原因不能为空
     */
    public static final int TRAVEL_REVOKE_REASON_MUST = 201514;

    /**
     * 请输入%s字以内的撤销原因
     */
    public static final int TRAVEL_REVOKE_REASON_NUM_LIMIT = 201515;

    /**
     * 申请出差失败
     */
    public static final int APPLY_TRAVEL_FAILED = 201516;

    /**
     * 出差类型 %s 不存在
     */
    public static final int TRAVEL_TYPE_ONE_NOT_EXIST = 201517;

    /**
     * 出差地点不符合 省,市
     */
    public static final int TRAVEL_DESTINATION_ERR = 201518;

    /**
     * 省-> %s 不存在
     */
    public static final int TRAVEL_PROVINCE_NOT_EXIST = 201519;

    /**
     * 市 %s 不存在
     */
    public static final int TRAVEL_CITY_NOT_EXIST = 201520;

    /**
     * 出行方式 %s 不存在
     */
    public static final int TRAVEL_MODE_NOT_EXIST = 201521;

    /**
     * 开始时间不能为空
     */
    public static final int TRAVEL_START_TIME_NOT_EMPTY = 201522;

    /**
     * 开始时间格式不正确
     */
    public static final int TRAVEL_START_TIME_FORMAT_ERR = 201523;

    /**
     * 结束时间不能为空
     */
    public static final int TRAVEL_END_TIME_NOT_EMPTY = 201524;

    /**
     * 结束时间格式不正确
     */
    public static final int TRAVEL_END_TIME_FORMAT_ERR = 201525;

    /**
     * 半天开始 %s 不是系统配置的选项
     */
    public static final int HALF_START_NOT_SYS_CONFIG = 201526;

    /**
     * 半天结束 %s 不是系统配置的选项
     */
    public static final int HALF_END_NOT_SYS_CONFIG = 201527;

    /**
     * 未开启工作流，请修改导入模板
     */
    public static final int WORKFLOW_NOT_START = 201528;

    /**
     * 只能导入审批中/审批通过的出差
     */
    public static final int TRAVEL_IMPORT_NOT_ALLOW = 201529;

    /**
     * 出差时间单位错误
     */
    public static final int TRAVEL_UNIT_ERR = 201530;

    /**
     * 201400
     * 日期已存在
     */
    public static final int DATE_EXIST = 201600;

    /**
     * 特殊日期：%s已存在
     */
    public static final int SPECIAL_DATE_EXIST = 201601;

    /**
     * 该特殊日期不存在
     */
    public static final int SPECIAL_DATE_NOT_EXIST = 201602;

    /**
     * 请先删除日期项
     */
    public static final int DELETE_AFTER_DELETE_DATE_ITEM = 201603;

    /**
     * %s已被使用，暂不能删除
     */
    public static final int SPECIAL_DATE_DELETE_NOT_ALLOWED = 201604;

    /**
     * 请选择特殊日期
     */
    public static final int SPECIAL_DATE_EMPTY = 201605;

    /**
     * 日期项不存在
     */
    public static final int SPECIAL_DATE_ITEM_NOT_EXIST = 201606;

    /**
     * 日期分组名称为空
     */
    public static final int DATE_GROUP_NAME_EMPTY = 201607;
    /**
     * 日期分组名称不能超过%s字
     */
    public static final int DATE_GROUP_NAME_LENGTH_LIMIT = 201608;
    /**
     * 特殊日期选项为空
     */
    public static final int SPECIAL_DATE_NOT_SELECTED = 201609;
    /**
     * 日期分组名称已存在
     */
    public static final int DATE_GROUP_NAME_EXISTED = 201610;
    /**
     * 日期分组不存在
     */
    public static final int DATE_GROUP_NOT_EXIST = 201611;

    /**
     * 工作流配置错误，请联系管理员
     */
    public static final int WORKFLOW_CONFIG_ERR = 201700;

    /**
     * 工作流启动异常，请检查流程配置
     */
    public static final int WORKFLOW_START_ERR = 201701;

    /**
     * 审批流程未设置，无法发起申请
     */
    public static final int WORKFLOW_NOT_SET = 201702;

    /**
     * 撤销审批提交成功
     */
    public static final int WORKFLOW_REVOKE_SUBMIT_SUCCESS = 201703;

    /**
     * 废止审批提交成功
     */
    public static final int WORKFLOW_ABOLISH_SUBMIT_SUCCESS = 201704;

    /**
     * 工作流配置错误，请修改工作流配置
     */
    public static final int WORKFLOW_CONFIG_ERR_RESET = 201705;
    /**
     * 审批失败
     */
    public static final int APPROVED_FAILED = 201706;
    /**
     * 校验工作流是否开启请求失败
     */
    public static final int CHECK_WORKFLOW_ENABLED_FAILED = 201707;
    /**
     * 催办失败
     */
    public static final int URGE_FAILED = 201708;
    /**
     * 查询流程记录失败
     */
    public static final int WORKFLOW_PROCESS_RECORD_FAILED = 201709;

    /**
     * 考勤方案时间重叠
     */
    public static final int WA_TIME_OVERLAP = 200004;

    /**
     * 自动定界数据冲突
     */
    public static final int WA_TIME_CONFLICT = 200005;

    /**
     * 201401
     * 该规则已被引用，不允许删除。
     */
    public static final int RULE_USED = 201401;

    /**
     * 提示考勤周期已封存，请重新选择
     */
    public static final int WA_SOB_ISLOCK = 200500;

    /**
     * 考勤周期不存在
     */
    public static final int WA_SOB_NOT_EXIST = 200501;
    /**
     * 未设置考勤周期
     */
    public static final int WA_CYCLE_NOT_SET = 200502;
    /**
     * 当前选择的考勤方案已设置了相同的所属周期，请重新设置
     */
    public static final int SOB_NAME_DUPLICATED = 200503;
    /**
     * 考勤周期封存失败
     */
    public static final int SOB_CLOSE_FAILED = 200504;
    /**
     * 锁定考勤周期失败
     */
    public static final int SOB_LOCKED_FAILED = 200505;
    /**
     * 解除锁定考勤周期失败
     */
    public static final int SOB_UNLOCKED_FAILED = 200506;

    /**
     * 考勤方案名称不能为空
     */
    public static final int WA_GROUP_NAME_NOT_NULL = 200006;

    /**
     * 考勤方案名称不能重复
     */
    public static final int WA_GROUP_NAME_EXIST = 200007;

    /**
     * 方案内已存在调休假期
     */
    public static final int GROUP_EXIST_COMPENSATORY_LEAVE = 200008;

    /**
     * 方案内不存在调休假期
     */
    public static final int GROUP_NOT_EXIST_COMPENSATORY_LEAVE = 200009;

    /**
     * 所选适用人员已存在于其他方案
     */
    public static final int SELECTED_APPLICABLE_ALREADY_OTHER_GROUP = 200011;

    /**
     * 同一个假期类型不能有两种单位
     */
    public static final int LEAVE_ACCTIMETYPE_EXIST = 200205;

    /**
     * 启用失败
     */
    public static final int LEAVE_ENABLE_FAIL = 200206;

    /**
     * 停用失败
     */
    public static final int LEAVE_UNENABLE_FAIL = 200207;

    /**
     * 假期名称请限定在20字以内
     */
    public static final int LEAVE_NAME_TOO_LONG = 200208;

    /**
     * 假期编码请限定在20字以内
     */
    public static final int LEAVE_CODE_TOO_LONG = 200209;

    /**
     * 假期编码已存在，请重新填写
     */
    public static final int LEAVE_CODE_EXIST = 200210;

    /**
     * 假期名称已存在，请重新填写
     */
    public static final int LEAVE_NAME_EXIST = 200211;

    /**
     * 入职天数必填 Enrollment days are required
     */
    public static final int ENROLLMENT_DAYS_REQUIRED = 200212;

    /**
     * 规则配置错误
     */
    public static final int RULE_DEFINED_ERROR = 200213;

    /**
     * 上级假期配置不正确，请修改
     */
    public static final int LEAVE_PRIORITY_ORDER_ERROR = 200214;
    /**
     * 该假期类型已被使用，无法删除
     */
    public static final int LEAVE_TYPE_CAN_NOT_DELETE = 200215;
    /**
     * 额度规则名称重复
     */
    public static final int QUOTA_RULE_NAME_REPEAT = 200216;
    /**
     * 探亲假规则重复
     */
    public static final int FAMILY_VISIT_RULE_REPEAT = 200217;
    /**
     * 必须配置探亲事由
     */
    public static final int FAMILY_VISIT_REASON_MUST = 200218;
    /**
     * 获取假期类型code失败
     */
    public static final int LEAVE_CODE_FAILED = 200219;

    /**
     * 请输入正确的补卡时间规则！
     */
    public static final int TIME_RULE_ERROR = 200600;

    /**
     * 跨夜的加班休息时间需要拆分设置！
     */
    public static final int SHIFT_OVERTIME_ACROSS_NIGHT = 200601;

    /**
     * 班次不存在！
     */
    public static final int SHIFT_NOT_EXIST = 200602;

    /**
     * 班次名称重复！
     */
    public static final int SHIFT_NAME_EXIST = 200603;

    /**
     * 休息时间段超出上下班时间，请重新选择
     */
    public static final int SHIFT_REST_EXCEEDS_COMMUTING = 200604;

    /**
     * 加班时间段未设置
     */
    public static final int OVERTIME_PERIOD_NOT_SET = 200605;

    /**
     * 跨夜班班次不支持弹性
     */
    public static final int CROSS_NIGHT_SHIFT_NOT_FLEXIBILITY = 200606;
    /**
     * 班次编码：%s 重复
     */
    public static final int SHIFT_CODE_DUPLICATED = 200607;
    /**
     * 当前班次已分配，不可进行删除操作
     */
    public static final int SHIFT_DELETE_NOT_ALLOW = 200608;
    /**
     * 日期类型不能为空
     */
    public static final int DATE_TYPE_IS_NULL = 200609;
    /**
     * 班次名称不能为空
     */
    public static final int SHIFT_NAME_IS_NULL = 200610;
    /**
     * 班次名称不能超过100个字符
     */
    public static final int SHIFT_NAME_IS_TOO_LONG = 200611;
    /**
     * 班次编码不能为空
     */
    public static final int SHIFT_CODE_IS_NULL = 200612;
    /**
     * 班次编码不能超过%s个字符
     */
    public static final int SHIFT_CODE_IS_TOO_LONG = 200613;
    /**
     * %s日班次%s与%s日班次%s时间重叠，请重新选择
     */
    public static final int SHIFT_TIME_OVERLAP_BETWEEN = 200614;
    /**
     * 班次为空
     */
    public static final int SHIFT_EMPTY = 200615;
    /**
     * 班次时间重叠，请重新选择
     */
    public static final int SHIFT_TIME_OVERLAP = 200616;

    /**
     * 200100
     * 存在时间重叠的申请，不能提交
     */
    public static final int TIME_OVERAPPLY = 200100;

    /**
     * 200101
     * 加班类型名称为空
     */
    public static final int OVERTIME_TYPE_NAME_EMPTY = 200101;

    /**
     * 200102
     * 加班类型名称已存在
     */
    public static final int OVERTIME_TYPE_NAME_EXISTS = 200102;

    /**
     * 200103
     * 规则已被引用，无法删除
     */
    public static final int OVERTIME_TYPE_USED = 200103;

    /**
     * 200104
     * 加班类型名称超长
     */
    public static final int OVERTIME_TYPE_NAME_TOO_LONG = 200104;

    /**
     * 200105
     * 加班规则不存在
     */
    public static final int OVERTIME_TYPE_NOT_EXIST = 200105;

    /**
     * 200106
     * 超过最小加班单位最大值24
     */
    public static final int EXCEEDS_MIN_OVERTIME_MAXIMUM = 200106;

    /**
     * 200107
     * 转换规则名称为空
     */
    public static final int OVERTIME_TRANSFER_NAME_EMPTY = 200107;

    /**
     * 200108
     * 转换规则名称已存在
     */
    public static final int OVERTIME_TRANSFER_NAME_EXISTED = 200108;

    /**
     * 200109
     * 转换规则名称超长
     */
    public static final int OVERTIME_TRANSFER_NAME_TOO_LONG = 200109;

    /**
     * 200110
     * 补偿方式为空
     */
    public static final int OVERTIME_TRANSFER_COMPENSATE_TYPE_EMPTY = 200110;

    /**
     * 200111
     * 调休假为空
     */
    public static final int OVERTIME_TRANSFER_LEAVE_EMPTY = 200111;

    /**
     * 200112
     * 备注内容超长
     */
    public static final int OVERTIME_TRANSFER_NOTE_TOO_LONG = 200112;

    /**
     * 200113
     * 转换规则不存在
     */
    public static final int OVERTIME_TRANSFER_RULE_NOT_EXIST = 200113;

    /**
     * 200114
     * 加班转换规则已被引用，不可进行删除
     */
    public static final int OVERTIME_TRANSFER_RULE_REFERENCED = 200114;

    /**
     * 200115
     * 每个日期类型只能存在一个默认生效中的加班类型
     */
    public static final int DEFAULT_OVERTIME_TYPE_LIMIT = 200115;

    /**
     * 220303
     * 申请时间超过加班时效性，不允许申请
     */
    public static final int OVERTIME_TIMELINESS = 220303;

    /**
     * 220304
     * 请上传附件
     */
    public static final int PLEASE_UPLOAD_ATTACHMENTS = 220304;

    /**
     * 201900
     * 月份为空
     */
    public static final int MONTH_EMPTY = 201900;

    /**
     * 休假原因为必填
     */
    public static final int REASON_MUST = 201107;

    /**
     * 加班原因为必填
     */
    public static final int OVERTIME_REASON_MUST = 201008;

    /**
     * 加班原因超长
     */
    public static final int OVERTIME_REASON_TOO_LONG = 201009;
    /**
     * 加班类型为空
     */
    public static final int OVERTIME_TYPE_EMPTY = 201010;

    /**
     * 201011
     * 同一天仅可申请一条加班单
     */
    public static final int ONE_OVERTIME_APPLY_CONTROL = 201011;

    /**
     * 201012
     * 加班时长不得低于xx小时
     */
    public static final int OVERTIME_LESS_THAN = 201012;

    /**
     * 201013
     * 加班时长不得超过xx小时
     */
    public static final int OVERTIME_GREATER_THAN = 201013;

    /**
     * 201014
     * 加班时长最小单位为xx小时，即加班时长只能是xx的倍数
     */
    public static final int OVERTIME_MUST_MULTIPLES = 201014;

    /**
     * 201015
     * 出差期间，不能提交加班申请
     */
    public static final int DURING_OUT_OVERTIME_APPLY_NOT_ALLOW = 201015;

    /**
     * 201016
     * 该加班申请已有审批中的撤销/废止流程，不可进行此操作
     */
    public static final int DURING_REVOKING_OVERTIME_REVOKE_NOT_ALLOW = 201016;
    /**
     * 获取加班时长失败
     */
    public static final int GET_OVERTIME_DURATION_FAILED = 201017;
    /**
     * 获取加班转调休记录详情列表失败
     */
    public static final int GET_OVERTIME_TO_COMPENSATORY_DETAIL_FAILED = 201018;
    /**
     * 加班申请失败
     */
    public static final int APPLY_OVERTIME_FAILED = 201019;
    /**
     * 申请加班撤销/废止失败
     */
    public static final int OVERTIME_REVOKE_OR_ABOLISH_FAILED = 201020;
    /**
     * 该加班单结转的调休额度已被%s的调休假使用，请先撤销调休假申请，再撤销该加班申请
     */
    public static final int OVERTIME_CARRIED_COMPENSATORY_REVOKE_NOT_ALLOWED = 201021;
    /**
     * 该加班单结转的调休额度已被申请付现，不可进行撤销
     */
    public static final int OVERTIME_CARRIED_CASH_REVOKE_NOT_ALLOWED = 201022;
    /**
     * 考勤加班申请结果提醒
     */
    public static final int OVERTIME_MSG_REMINDER = 201023;
    /**
     * 请选择补偿方式
     */
    public static final int NOT_CHOOSE_COMPENSATORY_TYPE = 201024;

    /**
     * 补打卡日期不能大于当前时间
     */
    public static final int PUNCH_IN_DATE_GE_CURRENT = 201204;

    /**
     * 您没有补卡申请权限
     */
    public static final int NO_PERMISSION_CARD_REPLACEMENT = 201205;

    /**
     * 补卡原因为必填
     */
    public static final int PUNCH_IN_REASON_MUST = 201206;

    /**
     * 补卡时间不在允许时间范围
     */
    public static final int TIME_NOT_ALLOWED = 201207;

    /**
     * 已超过最晚调整时限，无法进行此步操作
     */
    public static final int TIMEOUT_NOT_ALLOWED = 201208;

    /**
     * 无法申请多天补卡，请重新选择
     */
    public static final int APPLY_MULTI_DAY_NOT_ALLOWED = 201209;
    /**
     * 补卡条数数已超上限
     */
    public static final int NUMBER_EXCEEDED_UPPER_LIMIT = 201210;

    /**
     * uuid不能为空
     */
    public static final int UUID_CAN_NOT_EMPTY = 201211;

    /**
     * major cannot be empty
     */
    public static final int MAJOR_CAN_NOT_EMPTY = 201212;

    /**
     * majorName cannot be empty
     */
    public static final int MAJOR_NAME_CAN_NOT_EMPTY = 201213;

    /**
     * 存在数值字段值超出上限
     */
    public static final int EXIST_NUMBER_VALUE_EXCEEDS_UPPERLIMIT = 201800;

    /**
     * 该数据已被撤销
     */
    public static final int HAVE_REVOKE_NOT_ALLOW_REVOKE = 201801;

    /**
     * 只允许撤销审批中和审批通过的申请
     */
    public static final int APPLY_NOT_ALLOW_REVOKE = 201802;

    /**
     * 申请单据不存在
     */
    public static final int APPLY_NOT_EXIST = 201803;

    /**
     * 撤销原因不能为空
     */
    public static final int REVOKE_REASON_EMPTY = 201804;

    /**
     * 额度已过期
     */
    public static final int QUOTA_EXPIRED = 201805;

    /**
     * 申请时间已超过%s月考勤截止日%s月%s日，请联系管理员
     */
    public static final int EXCEEDED_ATTENDANCE_DEADLINE = 201806;

    /**
     * %s最小单位是%s天
     */
    public static final int MIN_UNIT_DAY = 201807;

    /**
     * %s天
     */
    public static final int UNIT_DAY = 201808;

    /**
     * %s最小单位是%s小时
     */
    public static final int MIN_UNIT_HOUR = 201809;

    /**
     * %s小时%s分钟
     */
    public static final int UNIT_HOUR_AND_MINUTE = 201810;

    /**
     * %s小时
     */
    public static final int UNIT_HOUR = 201811;

    /**
     * 解析导入文件的Workbook错误，请检查后重新上传
     */
    public static final int PARSE_WORKBOOK_ERR = 201812;
    /**
     * 解析导入文件的Sheet错误，请检查后重新上传
     */
    public static final int PARSE_SHEET_ERR = 201813;
    /**
     * 导入文件为空，请检查后重新上传
     */
    public static final int IMPORT_FILE_EMPTY = 201814;
    /**
     * 账号不存在
     */
    public static final int WORK_NOT_EXIST = 201815;

    /**
     * 导入失败
     */
    public static final int IMPORT_FAILED = 201816;

    /**
     * 同步操作正在进行中，请稍后再重新尝试
     */
    public static final int SYNC_IN_PROGRESS = 201817;
    /**
     * 请导入模板文件
     */
    public static final int IMPORT_TEMPLATE_FILE = 201818;
    /**
     * 第%s行加班日期为空，请检查后重新上传
     */
    public static final int IMPORT_FILE_OVERTIME_DATE_EMPTY = 201819;
    /**
     * 第%s行加班日期格式不正确，请检查后重新上传
     */
    public static final int IMPORT_FILE_OVERTIME_DATE_FORMAT_ERR = 201820;
    /**
     * 第%s行调整额度为空，请检查后重新上传
     */
    public static final int IMPORT_FILE_ADJUST_QUOTA_EMPTY = 201821;
    /**
     * 第%s行调整额度格式不正确，必须为数字，请检查后重新上传
     */
    public static final int IMPORT_FILE_ADJUST_QUOTA_FORMAT_FORMAT_ERR = 201822;
    /**
     * 导入文件数据%s存在重复，请检查后重新上传
     */
    public static final int IMPORT_FILE_DATA_REPEAT = 201823;
    /**
     * 加班日期为%s,员工号为%s的调休额度不存在，请检查后重新上传
     */
    public static final int IMPORT_FILE_COMPENSATORY_NOT_EXIST = 201824;
    /**
     * 第%s行账号%s的员工当前无有效的考勤方案，请检查后重新上传
     */
    public static final int EMP_NO_VALID_GROUP = 201825;
    /**
     * 第%s行账号%s的员工所属考勤方案下无调休假，请检查后重新上传
     */
    public static final int EMP_GROUP_NO_COMPENSATORY_LEAVE = 201826;
    /**
     * 第%s行调休额度为空，请检查后重新上传
     */
    public static final int IMPORT_COMPENSATORY_QUOTA_EMPTY = 201827;
    /**
     * 第%s行调休额度格式不正确，必须为数字，请检查后重新上传
     */
    public static final int IMPORT_COMPENSATORY_QUOTA_FORMAT_ERR = 201828;
    /**
     * 第%s行生效日期为空，请检查后重新上传
     */
    public static final int IMPORT_START_DATE_EMPTY = 201829;
    /**
     * 第%s行生效日期格式不正确，请检查后重新上传
     */
    public static final int IMPORT_START_DATE_FORMAT_ERR = 201830;
    /**
     * 第%s行失效日期为空，请检查后重新上传
     */
    public static final int IMPORT_END_DATE_EMPTY = 201831;
    /**
     * 第%s行失效日期格式不正确，请检查后重新上传
     */
    public static final int IMPORT_END_DATE_FORMAT_ERR = 201832;
    /**
     * 名称请限定在%s字以内
     */
    public static final int NAME_LENGTH_LIMIT = 201833;
    /**
     * 备注请限定在%s字以内
     */
    public static final int NOTE_LENGTH_LIMIT = 201834;
    /**
     * 进度ID不存在
     */
    public static final int PROCESS_NOT_EXIST = 201835;
    /**
     * 另一个同步进程正在运行中，请稍后再尝试
     */
    public static final int ANOTHER_SYNCHRONIZATION_PROCESS_RUNNING = 201836;
    /**
     * 工作日
     */
    public static final int WORK_DAY = 201837;
    /**
     * 休息日
     */
    public static final int REST_DAY = 201838;
    /**
     * 法定假日
     */
    public static final int HOLIDAY = 201839;
    /**
     * 特殊休日
     */
    public static final int SPECIAL_REST_DAY = 201840;
    /**
     * 法定休日
     */
    public static final int LAW_REST_DAY = 201841;
    /**
     * 异常
     */
    public static final int ABNORMAL = 201842;
    /**
     * 正常
     */
    public static final int NORMAL = 201843;
    /**
     * 加班
     */
    public static final int OVERTIME = 201844;
    /**
     * 休假
     */
    public static final int LEAVE = 201845;
    /**
     * 出差
     */
    public static final int TRAVEL = 201846;
    /**
     * 迟到
     */
    public static final int LATE = 201847;
    /**
     * 早退
     */
    public static final int EARLY = 201848;
    /**
     * 旷工
     */
    public static final int ABSENT = 201849;
    /**
     * 补卡
     */
    public static final int CARD_REPLACEMENT = 201850;
    /**
     * 上午
     */
    public static final int AM = 201851;
    /**
     * 下午
     */
    public static final int PM = 201852;
    /**
     * 暂存
     */
    public static final int DRAFT = 201853;
    /**
     * 审批中
     */
    public static final int IN_APPROVAL = 201854;
    /**
     * 已通过
     */
    public static final int PASSED = 201855;
    /**
     * 已拒绝
     */
    public static final int REJECTED = 201856;
    /**
     * 已作废
     */
    public static final int CANCELLATION = 201857;
    /**
     * 已退回
     */
    public static final int RETURNED = 201858;
    /**
     * 撤销中
     */
    public static final int REVOKING = 201859;
    /**
     * 已撤销
     */
    public static final int REVOKED = 201860;
    /**
     * 顺产
     */
    public static final int EASY_LABOUR = 201861;
    /**
     * 难产
     */
    public static final int DIFFICULT_LABOUR = 201862;
    /**
     * 销假中
     */
    public static final int RETURNING_LEAVE = 201863;
    /**
     * 已销假
     */
    public static final int ABANDONED_LEAVE = 201864;
    /**
     * 未销假
     */
    public static final int UNRELEASED_LEAVE = 201865;
    /**
     * 系统生成
     */
    public static final int SYS_GENERATED = 201866;
    /**
     * 新增/导入
     */
    public static final int MANUAL = 201867;
    /**
     * 出差自动结转
     */
    public static final int TRAVEL_AUTO_CARRY = 201868;
    /**
     * 不补偿
     */
    public static final int OVERTIME_WORK_FREE = 201869;
    /**
     * 加班费
     */
    public static final int OVERTIME_WORK_PAID = 201870;
    /**
     * 调休
     */
    public static final int OVERTIME_COMPENSATORY_LEAVE = 201871;
    /**
     * 已预付
     */
    public static final int OVERTIME_PAY_IN_ADVANCE = 201872;
    /**
     * 其他
     */
    public static final int OVERTIME_CHOICE = 201873;
    /**
     * 已失效
     */
    public static final int INVALID = 201874;
    /**
     * 待生效
     */
    public static final int PENDING = 201875;
    /**
     * 生效中
     */
    public static final int VALID = 201876;

    /**
     * 发起人
     */
    public static final int ORIGINATOR = 201877;
    /**
     * 申请人
     */
    public static final int APPLICANT = 201878;
    /**
     * 任职组织
     */
    public static final int ORG_POSITION = 201879;
    /**
     * 员工类型
     */
    public static final int EMPLOYEE_TYPE = 201880;
    /**
     * 工作地
     */
    public static final int WORKPLACE = 201881;
    /**
     * 入职日期
     */
    public static final int HIRE_DATE = 201882;
    /**
     * 撤销原因
     */
    public static final int REVOKE_REASON = 201883;
    /**
     * 开始时间
     */
    public static final int START_DATE = 201884;
    /**
     * 结束时间
     */
    public static final int END_DATE = 201885;
    /**
     * 申请时长
     */
    public static final int DURATION = 201886;
    /**
     * 实际时长
     */
    public static final int ACTUAL_DURATION = 201887;
    /**
     * 申请时间
     */
    public static final int APPLY_DURATION = 201888;
    /**
     * 假期类型
     */
    public static final int LEAVE_TYPE = 201889;
    /**
     * 审批状态
     */
    public static final int APPROVAL_STATUS = 201890;
    /**
     * 休假时间
     */
    public static final int LEAVE_TIME = 201891;
    /**
     * 申请事由
     */
    public static final int APPLY_REASON = 201892;
    /**
     * 审批编号
     */
    public static final int APPROVAL_CODE = 201893;
    /**
     * 附件
     */
    public static final int ATTACHMENT = 201894;
    /**
     * 加班类型
     */
    public static final int OVERTIME_TYPE = 201895;
    /**
     * 补偿方式
     */
    public static final int COMPENSATORY_TYPE = 201896;
    /**
     * 出差类型
     */
    public static final int TRAVEL_TYPE = 201897;
    /**
     * 出行地点
     */
    public static final int TRAVEL_PLACE = 201898;
    /**
     * 出行方式
     */
    public static final int TRAVEL_MODE = 201899;
    /**
     * 职务
     */
    public static final int POST = 2018100;
    /**
     * 工号
     */
    public static final int WORK_NO = 2018101;
    /**
     * 姓名
     */
    public static final int EMP_NAME = 2018102;
    /**
     * 组织
     */
    public static final int ORG = 2018103;
    /**
     * 岗位
     */
    public static final int POSITION = 2018104;
    /**
     * 职级
     */
    public static final int POSITION_LEVEL = 2018105;
    /**
     * 紧急联系人
     */
    public static final int EMERGENCY_CONTACT = 2018106;
    /**
     * 假期配额
     */
    public static final int LEAVE_QUOTA = 2018107;
    /**
     * 考勤月
     */
    public static final int ATTENDANCE_MONTH = 2018108;
    /**
     * 加班明细
     */
    public static final int OVERTIME_DETAIL = 2018109;
    /**
     * 加班统计
     */
    public static final int OVERTIME_STATISTIC = 2018110;
    /**
     * 异常申请明细
     */
    public static final int ABNORMAL_APPLY_DETAIL = 2018111;
    /**
     * 部署
     */
    public static final int DEPLOY = 2018112;
    /**
     * 出差类型
     */
    public static final int BUSINESS_TRIP_TYPE = 2018113;
    /**
     * 访问地
     */
    public static final int VISIT_LOCATION = 2018114;
    /**
     * 访问对象
     */
    public static final int VISIT_OBJECT = 2018115;
    /**
     * 目的
     */
    public static final int OBJECTIVE = 2018116;
    /**
     * 费用承担
     */
    public static final int COST_BEARING = 2018117;
    /**
     * 联系电话
     */
    public static final int CONTACT_NUMBER = 2018118;
    /**
     * 同行人员
     */
    public static final int COLLEAGUES = 2018119;
    /**
     * 不在时业务联络
     */
    public static final int BUSINESS_CONTACT_DURING_ABSENCE = 2018120;
    /**
     * 备注
     */
    public static final int NOTE = 2018121;
    /**
     * 出差时间
     */
    public static final int BUSINESS_TRIP_DATE = 2018122;
    /**
     * 班次名称
     */
    public static final int SHIFT_NAME = 2018123;
    /**
     * 班次时段
     */
    public static final int SHIFT_TIME_RANGE = 2018124;
    /**
     * 打卡记录
     */
    public static final int REGISTER_RECORD = 2018125;
    /**
     * 暂无记录
     */
    public static final int NOT_REGISTER_RECORD = 2018126;
    /**
     * 补卡时间的
     */
    public static final int FILL_CARD_TIME = 2018127;
    /**
     * 补卡事由
     */
    public static final int FILL_CARD_REASON = 2018128;
    /**
     * 调班日期
     */
    public static final int SHIFT_CHANGE_DATE = 2018129;
    /**
     * 原班次
     */
    public static final int ORIGINAL_SHIFT = 2018130;
    /**
     * 未打卡
     */
    public static final int NO_REGISTER = 2018131;
    /**
     * 调整后班次
     */
    public static final int CHANGED_SHIFT = 2018132;
    /**
     * 休假开始时间
     */
    public static final int LEAVE_START_TIME = 2018133;
    /**
     * 休假结束时间
     */
    public static final int LEAVE_END_TIME = 2018134;
    /**
     * 休假申请时长
     */
    public static final int LEAVE_APPLY_DURATION = 2018135;
    /**
     * 休假实际时长
     */
    public static final int LEAVE_ACTUAL_DURATION = 2018136;
    /**
     * 新休假开始时间
     */
    public static final int NEW_LEAVE_START_TIME = 2018137;
    /**
     * 新休假结束时间
     */
    public static final int NEW_LEAVE_END_TIME = 2018138;
    /**
     * 新休假申请时长
     */
    public static final int NEW_LEAVE_APPLY_DURATION = 2018139;
    /**
     * 销假类型
     */
    public static final int LEAVE_CANCEL_TYPE = 2018140;
    /**
     * 销假时间
     */
    public static final int LEAVE_CANCEL_TIME = 2018141;
    /**
     * 付现失效额度
     */
    public static final int COMPENSATOR_TO_CASH_INVALID_QUOTA = 2018142;
    /**
     * 付现生效中额度
     */
    public static final int COMPENSATOR_TO_CASH_VALID_QUOTA = 2018143;
    /**
     * 申请付现额度
     */
    public static final int COMPENSATOR_TO_CASH_QUOTA = 2018144;
    /**
     * 延期假期类型
     */
    public static final int LEAVE_EXTENSION_TYPE = 2018145;
    /**
     * 延期额度
     */
    public static final int LEAVE_EXTENSION_QUOTA = 2018146;
    /**
     * 额度单位
     */
    public static final int LEAVE_EXTENSION_QUOTA_UNIT = 2018147;
    /**
     * 延期开始时间
     */
    public static final int LEAVE_EXTENSION_START_TIME = 2018148;
    /**
     * 延期结束时间
     */
    public static final int LEAVE_EXTENSION_END_TIME = 2018149;
    /**
     * 申请日期
     */
    public static final int APPLY_DATE = 2018150;
    /**
     * 婚姻状况
     */
    public static final int MARITAL_STATUS = 2018151;
    /**
     * 子女个数
     */
    public static final int NUMBER_OF_CHILD = 2018152;
    /**
     * 产假类型
     */
    public static final int TYPE_OF_MATERNITY_LEAVE = 2018153;
    /**
     * 子女出生日期
     */
    public static final int CHILD_BIRTHDAY = 2018154;
    /**
     * 月
     */
    public static final int MONTH = 2018155;
    /**
     * 年
     */
    public static final int YEAR = 2018156;
    /**
     * 不可预支
     */
    public static final int NO_ADVANCE = 2018157;
    /**
     * 可预支
     */
    public static final int AVALIBLE_ADVANCE = 2018158;
    /**
     * 生效
     */
    public static final int TAKE_EFFECT = 2018159;
    /**
     * 失效
     */
    public static final int LOSS_EFFECT = 2018160;
    /**
     * 撤销失效
     */
    public static final int REVOKE_LOSS_EFFECT = 2018161;
    /**
     * 冻结
     */
    public static final int FROZEN = 2018162;
    /**
     * 工作日加班
     */
    public static final int WORKDAY_OVERTIME = 2018163;
    /**
     * 休息日加班
     */
    public static final int REST_DAY_OVERTIME = 2018164;
    /**
     * 法定假日加班
     */
    public static final int HOLIDAY_OVERTIME = 2018165;
    /**
     * 特殊休日加班
     */
    public static final int SPECIAL_REST_DAY_OVERTIME = 2018166;
    /**
     * 出发
     */
    public static final int SET_OUT = 2018167;
    /**
     * 中转
     */
    public static final int TRANSFER = 2018168;
    /**
     * 回归
     */
    public static final int REGRESSION = 2018169;
    /**
     * 是
     */
    public static final int YES = 2018170;
    /**
     * 否
     */
    public static final int NO = 2018171;
    /**
     * 签到
     */
    public static final int SIGN_IN = 2018172;
    /**
     * 签退
     */
    public static final int SIGN_OUT = 2018173;
    /**
     * 外勤
     */
    public static final int SIGN_OUT_WORKER = 2018174;
    /**
     * 一次卡
     */
    public static final int SIGN_ONCE = 2018175;
    /**
     * 二次卡
     */
    public static final int SIGN_TWICE = 2018176;
    /**
     * 不打卡
     */
    public static final int SIGN_ZERO = 2018177;
    /**
     * 限额
     */
    public static final int LIMIT_QUOTA = 2018178;
    /**
     * 不限额
     */
    public static final int NO_LIMIT_QUOTA = 2018179;
    /**
     * 考勤
     */
    public static final int ATTENDANCE = 2018180;
    /**
     * 企业微信
     */
    public static final int WE = 2018181;
    /**
     * 飞书
     */
    public static final int FS = 2018182;
    /**
     * 钉钉
     */
    public static final int DD = 2018183;
    /**
     * 考勤机
     */
    public static final int MACHINE = 2018184;
    /**
     * 人脸考勤
     */
    public static final int FACE = 2018185;
    /**
     * 迟到%s
     */
    public static final int LATE_TIME = 2018186;
    /**
     * 迟到调整%s
     */
    public static final int LATE_ADJUST_TIME = 2018187;
    /**
     * 早退%s
     */
    public static final int EARLY_TIME = 2018188;
    /**
     * 早退调整%s
     */
    public static final int EARLY_ADJUST_TIME = 2018189;
    /**
     * 旷工%s
     */
    public static final int KG_TIME = 2018190;
    /**
     * 请假(%s)%s
     */
    public static final int HOLIDAY_TIME = 2018191;
    /**
     * 出差(%s)%s
     */
    public static final int TRAVEL_TIME = 2018192;
    /**
     * 加班(%s)%s
     */
    public static final int OVER_TIME_TIME = 2018193;
    /**
     * 停用
     */
    public static final int DISABLE = 2018194;
    /**
     * 启用
     */
    public static final int ENABLE = 2018195;
    /**
     * 上下班打卡提醒
     */
    public static final int CLOCK = 2018196;
    /**
     * 考勤日报提醒
     */
    public static final int DAILY_REPORT = 2018197;
    /**
     * 考勤周报提醒
     */
    public static final int WEEKLY_REPORT = 2018198;
    /**
     * 销假提醒
     */
    public static final int LEAVE_CANCEL = 2018199;
    /**
     * 考勤异常汇总提醒
     */
    public static final int ABNORMAL_SUMMARY = 2018201;
    /**
     * 探亲事由
     */
    public static final int HOME_LEAVE_REASON = 2018202;
    /**
     * 探望父母
     */
    public static final int VISITING_PARENT = 2018203;
    /**
     * 探望配偶
     */
    public static final int VISITING_SPOUSE = 2018204;
    /**
     * 当前假期配额
     */
    public static final int CURRENT_LEAVE_QUOTA = 2018205;
    /**
     * 考勤明细推送
     */
    public static final int ATTENDANCE_DETAIL_PUSH = 2018206;
    /**
     * 销假班次
     */
    public static final int LEAVE_CANCEL_SHIFT = 2018207;
    /**
     * 本周期内加班总时长
     */
    public static final int PERIOD_OT_DURATION = 2018208;

    /**
     * 工时自动结转
     */
    public static final int WORKING_HOUR_AUTO_CARRY = 2018209;

    /**
     * 请选择调班日期
     */
    public static final int NO_SHIFT_ADJUSTMENT_DATE = 201901;

    /**
     * 请选择班次
     */
    public static final int NO_SELECT_SHIFT = 201902;

    /**
     * 您当天已存在一条审批中的调班，请选择其他日期
     */
    public static final int EXISTING_IN_APPROVAL = 201903;

    /**
     * 调班申请失败
     */
    public static final int SHIFT_APPLY_FAIL = 201904;

    /**
     * 调班单据不存在
     */
    public static final int SHIFT_APPLY_NOT_EXIST = 201905;
    /**
     * 排班名称已存在，请重新填写
     */
    public static final int SHIFT_NAME_DUPLICATED = 201936;
    /**
     * 已排班，不允许删除
     */
    public static final int SCHEDULED_DELETE_NOT_ALLOW = 201937;
    /**
     * 员工不可申请此假期
     */
    public static final int NOT_ALLOW_APPLY = 201938;
    /**
     * 首次工作日
     */
    public static final int FIRST_WORK_DATE = 201939;


    /**************销假**************/
    /**
     * 休假单id为空
     */
    public static final int LEAVE_ID_IS_NULL = 202000;

    /**
     * 请输入销假时间
     */
    public static final int LEAVE_CANCEL_TIME_MUST = 202001;

    /**
     * 存在销假时间段非法
     */
    public static final int LEAVE_CANCEL_ILLEGAL = 202002;

    /**
     * 审批通过的休假单才允许进行销假
     */
    public static final int LEAVE_CANCEL_NOT_ALLOWED = 202003;

    /**
     * 休假单已销假
     */
    public static final int LEAVE_ALREADY_CANCEL = 202004;

    /**
     * 申请销假时间段超出休假时间段，请重新选择！
     */
    public static final int TIME_OUT_OF_RANGE = 202005;

    /**
     * 申请时间段已重复，请重新选择！
     */
    public static final int TIME_REPETITION = 202006;

    /**
     * 已存在销假单，不允许撤销
     */
    public static final int LEAVE_CANCEL_EXIST = 202007;

    /**
     * 休息日不能销假
     */
    public static final int REST_DAY_NOT_LEAVE_CANCEL = 202008;

    /**
     * 法定节假日不能销假
     */
    public static final int LEGAL_DAY_NOT_LEAVE_CANCEL = 202009;

    /**
     * 特殊休日不能销假
     */
    public static final int SPECIAL_DAY_NOT_LEAVE_CANCEL = 202010;

    /**
     * 不允许撤销销假单
     */
    public static final int NOT_ALLOW_REVOKE_LEAVE_CANCEL = 202011;
    /**
     * 请选择销假类型
     */
    public static final int PLEASE_SELECT_LEAVE_CANCEL_TYPE = 202012;
    /**
     * 计算销假时长失败
     */
    public static final int CAL_LEAVE_CANCEL_TIME_FAILED = 202013;
    /**
     * 休假确认
     */
    public static final int LEAVE_CANCEL_TYPE_CONFIRM = 202014;
    /**
     * 取消部分休假
     */
    public static final int LEAVE_CANCEL_TYPE_PARTIAL = 202015;
    /**
     * 取消休假
     */
    public static final int LEAVE_CANCEL_TYPE_CANCEL = 202016;
    /**
     * 调整时间
     */
    public static final int LEAVE_CANCEL_TYPE_ADJUST_TIME = 202017;

    /**
     * 消息配置参数为空
     */
    public static final int NOTIFY_CONFIG_EMPTY = 202100;

    /**
     * 消息配置不存在
     */
    public static final int NOTIFY_CONFIG_NOT_EXIST = 202101;

    /**
     * 消息配置类型不存在
     */
    public static final int NOTIFY_CONFIG_TYPE_NOT_EXIST = 202102;

    /**
     * 请检查并重新保存周报消息配置
     */
    public static final int WEEKLY_NOTIFY_CONFIG_JOB_NOT_SET = 202103;

    /**
     * 请检查并重新保存考勤异常汇总提醒配置
     */
    public static final int ABNORMAL_NOTIFY_CONFIG_JOB_NOT_SET = 202104;
    /**
     * 请检查并重新保存考勤明细提醒配置
     */
    public static final int ATTENDANCE_DETAIL_NOTIFY_CONFIG_JOB_NOT_SET = 202105;

    /**
     * 考勤明细
     */
    public static final int ATTENDANCE_DETAIL = 202106;
    /**
     * 附件为方案%s%s~%s范围内的考勤明细情况，请查收
     */
    public static final int ATTENDANCE_DETAIL_CONTENT = 202107;

    /**
     * 申请付现额度不得超过调休配额
     */
    public static final int APPLY_EXCEED_QUOTA = 202200;

    /**
     * 调休额度规则不存在
     */
    public static final int LEAVE_QUOTA_NOT_EXISTS = 202201;

    /**
     * 当前调休额度不允许申请转付现
     */
    public static final int LEAVE_QUOTA_NOT_ALLOWED_APPLY = 202202;

    /**
     * 申请付现额度为空
     */
    public static final int APPLY_DURATION_EMPTY = 202203;

    /**
     * 查询可申请调休配额失败
     */
    public static final int QUERY_APPLY_COMPENSATORY_TO_CASH_FAILED = 202204;

    /**
     * 执行接入接口失败
     */
    public static final int DATA_INPUT_INTERFACE_EXECUTE_FAILED = 202300;
    /**
     * 启用需配置触发器
     */
    public static final int ENABLE_NEED_TRIGGER = 202303;
    /**
     * 启动接入接口失败
     */
    public static final int ENABLE_DATA_INPUT_INTERFACE_FAILED = 202304;
    /**
     * 停止接入接口失败
     */
    public static final int STOP_DATA_INPUT_INTERFACE_FAILED = 202305;
    /**
     * 删除接入接口失败
     */
    public static final int DELETE_DATA_INPUT_INTERFACE_FAILED = 202306;
    /**
     * 执行输出接口失败
     */
    public static final int DATA_OUTPUT_INTERFACE_EXECUTE_FAILED = 202307;
    /**
     * 启动输出接口失败
     */
    public static final int ENABLE_DATA_OUTPUT_INTERFACE_FAILED = 202308;
    /**
     * 停止输出接口失败
     */
    public static final int STOP_DATA_OUTPUT_INTERFACE_FAILED = 202309;
    /**
     * 删除输出接口失败
     */
    public static final int DELETE_DATA_OUTPUT_INTERFACE_FAILED = 202310;
    /**
     * 清除接入接口日志失败
     */
    public static final int CLEAR_DATA_INPUT_LOG_FAILED = 202311;
    /**
     * 清除输出接口日志失败
     */
    public static final int CLEAR_DATA_OUTPUT_LOG_FAILED = 202312;

    /**
     * 有效打卡时间段配置错误！
     */
    public static final int CLOCK_TIME_RULE_ERROR = 202301;

    /**
     * 请输入撤销原因100字以内
     */
    public static final int REVOKE_REASON_LIMIT100 = 202302;

    /**
     * 202400
     * 延期配额为空
     */
    public static final int EXTENSION_QUOTA_EMPTY = 202400;

    /**
     * 202401
     * 申请的延期配额为无效数据
     */
    public static final int EXTENSION_QUOTA_INVALID = 202401;

    /**
     * 202402
     * 申请延期配额与实际配额数不匹配
     */
    public static final int EXTENSION_QUOTA_NOT_MATCH = 202402;

    /**
     * 只允许撤销审批中的申请
     */
    public static final int NOT_ALLOW_REVOCATION = 202403;

    /**
     * 假期延期申请提交失败
     */
    public static final int SAVE_LEAVE_EXTENSION_FAILED = 202404;

    /**
     * 获取可申请延期的假期配额失败
     */
    public static final int LEAVE_EXTENSION_QUOTA_FAILED = 202405;

    /**
     * 【%s】已使用，不允许删除
     */
    public static final int STATISTIC_SETTING_DELETE_NOT_ALLOW = 202500;
    /**
     * 【%s】已存在
     */
    public static final int STATISTIC_SETTING_DUPLICATED = 202501;
    /**
     * %s已提交异常申请
     */
    public static final int RESULT_ADJUST_APPLY_SUBMITTED = 202600;

    /**
     * 固定班次
     */
    public static final int FIXED_SCHEDULE = 202700;

    /**
     * 排班制
     */
    public static final int SCHEDULE = 202710;

    /**
     * 自由打卡
     */
    public static final int FREE_SCHEDULE = 202720;


    /**
     * 班组编码不能重复
     */
    public static final int SHIFT_GROUP_CODE_CHECK = 203081;

    /**
     * 班组名称不能重复
     */
    public static final int SHIFT_GROUP_NAME_CHECK = 203082;
    /**
     * 班组不存在
     */
    public static final int SHIFT_GROUP_NOT_EXISTS = 203083;

    /**
     * 当前员工在当前班组中存在日期重叠的记录
     */
    public static final int EMP_AT_SHIFT_GROUP_DATE_REPEAT = 203084;

    /**
     * 请选择正确的日期范围
     */
    public static final int DATE_CHECKED = 203085;

    /**
     * 当日
     */
    public static final int TODAY = 202803;

    /**
     * 次日
     */
    public static final int NEXT_DAY = 202804;

    /**
     * 你申请的补卡时间存在时间重叠
     */
    public static final int DUPLICATE_REG_DATE_TIMES = 202829;

    /**
     * 工时扫码
     */
    public static final int SCANHOUR = 203087;

    /**
     * 实际出勤总时长%s
     */
    public static final int REGISTER_TIME_TOTAL = 2218186;
}
