package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.attendance.service.application.service.ITenantService;
import com.caidaocloud.attendance.service.domain.entity.TenantDo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TenantServiceImpl implements ITenantService {

    @Autowired
    private TenantDo tenantDo;

    @Override
    public void initTenantTable(String tenantId) {
        if (null == tenantId) {
            return;
        }
        createEmpScheduleTable(tenantId);
        createEmpScheduleDraftTable(tenantId);
        createWfmWorkingHourAnalyzeTable(tenantId);
    }

    /**
     * 排班表
     * @param tenantId
     */
    private void createEmpScheduleTable(String tenantId) {
        String stringBuffer = "create table if not exists wa_emp_schedule_detail_" + tenantId +
                " (" +
                "schedule_detail_id bigint not null primary key," +
                "entity_id bigint, tenant_id varchar(50) not null, emp_id bigint, work_no varchar(50)," +
                "emp_name varchar(50),year smallint,month smallint,work_time text,deleted integer default 0," +
                "creator bigint not null,create_time bigint not null,updater bigint,update_time bigint" +
                ");" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".schedule_detail_id is '主键';" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".entity_id is '冗余字段，如关联班组信息';" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".tenant_id is '租户';" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".year is '所属年份';" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".month is '所属月份';" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".work_time is '[{\"date\":1735660800,\"shift_id\":1234,\"叶片编号\":1234,\"产品名称\":1234,\"批次编号\":1234,\"计薪工序\"}]';" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".deleted is '删除状态：0正常，1已删除';" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".creator is '创建人者：user_id';" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".create_time is '创建时间';" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".updater is '更新者：user_id';" +
                "comment on column wa_emp_schedule_detail_" + tenantId + ".update_time is '更新时间';";
        tenantDo.initTable(stringBuffer);
    }

    /**
     * 排班草稿表
     * @param tenantId
     */
    private void createEmpScheduleDraftTable(String tenantId) {
        String stringBuffer = "create table if not exists wa_emp_schedule_draft_detail_" + tenantId +
                " (" +
                "schedule_detail_id bigint not null primary key," +
                "entity_id bigint, tenant_id varchar(50) not null, emp_id bigint, work_no varchar(50)," +
                "emp_name varchar(50),year smallint,month smallint,work_time text,old_work_time text,deleted integer default 0," +
                "creator bigint not null,create_time bigint not null,updater bigint,update_time bigint" +
                ");" +
                "comment on column wa_emp_schedule_draft_detail_" + tenantId + ".schedule_detail_id is '主键';" +
                "comment on column wa_emp_schedule_draft_detail_" + tenantId + ".entity_id is '冗余字段，如关联班组信息';" +
                "comment on column wa_emp_schedule_draft_detail_" + tenantId + ".tenant_id is '租户';" +
                "comment on column wa_emp_schedule_draft_detail_" + tenantId + ".year is '所属年份';" +
                "comment on column wa_emp_schedule_draft_detail_" + tenantId + ".month is '所属月份';" +
                "comment on column wa_emp_schedule_draft_detail_" + tenantId + ".deleted is '删除状态：0正常，1已删除';" +
                "comment on column wa_emp_schedule_draft_detail_" + tenantId + ".creator is '创建人者：user_id';" +
                "comment on column wa_emp_schedule_draft_detail_" + tenantId + ".create_time is '创建时间';" +
                "comment on column wa_emp_schedule_draft_detail_" + tenantId + ".updater is '更新者：user_id';" +
                "comment on column wa_emp_schedule_draft_detail_" + tenantId + ".update_time is '更新时间';";
        tenantDo.initTable(stringBuffer);
    }

    /**
     * 工时分析记录表
     * @param tenantId
     */
    private void createWfmWorkingHourAnalyzeTable(String tenantId) {
        String stringBuffer = "create table if not exists wfm_working_hour_analyze_" + tenantId +
                " (" +
                "analyze_id bigint  not null primary key," +
                "tenant_id varchar(50) not null,belong_date bigint not null, emp_id bigint, shift_id bigint, order_id bigint,order_num varchar(100), " +
                "product_id bigint, product_name varchar(100), process_id bigint,process_name varchar(100),process_code varchar(100), " +
                "work_time integer default 0, actual_work_time numeric(20, 2) default 0,holiday_actual_work_time numeric(20, 2) default 0, effect_work_time numeric(20, 2) default 0, " +
                "abnormal_work_time numeric(20, 2) default 0, abnormal_content text, reg_date_time text, " +
                "sign_in_id bigint, sign_off_id bigint, reg_start_time bigint, reg_end_time bigint," +
                "deleted integer default 0,creator bigint not null,create_time bigint not null,updater bigint,update_time bigint, " +
                "process_work_time numeric(20, 2) default 0, effective_scan_time varchar(60), process_time varchar(60),work_day_actual_work_time numeric(20, 2) default 0, " +
                " completed_quantity integer default 0, actual_completed_quantity numeric(10, 2) default 0, completed_time bigint, stored boolean default false, " +
                " stored_time bigint, process_type varchar(60), triple_product_stored boolean default false, stored_quantity integer default 0, notes varchar(100)," +
                "work_day_ot_duration numeric(20, 2) default 0,rest_day_ot_duration numeric(20, 2) default 0);" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".analyze_id is '主键';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".tenant_id is '租户';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".belong_date is '排班日期';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".emp_id is '员工';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".shift_id is '员工排班';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".order_id is '订单';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".order_num is '订单编号';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".product_id is '产品';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".product_name is '产品名称';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".process_id is '工序';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".process_code is '工序编号';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".process_name is '工序名称';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".work_time is '排班工时';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".actual_work_time is '实际工时';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".holiday_actual_work_time is '法定假日实际工时';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".effect_work_time is '有效工时';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".abnormal_work_time is '异常工时';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".abnormal_content is '异常内容，包括异常类型以及时长';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".reg_date_time is '扫码时间';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".sign_in_id is '扫码开始主键';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".sign_off_id is '扫码结束主键';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".reg_start_time is '扫码开始时间';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".reg_end_time is '扫码结束时间';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".deleted is '删除状态：0正常，1已删除';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".creator is '创建人者：user_id';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".create_time is '创建时间';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".updater is '更新者：user_id';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".update_time is '更新时间';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".process_work_time is '工序工时';" +

                "comment on column wfm_working_hour_analyze_" + tenantId + ".process_time is '工序开始结束时间';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".work_day_actual_work_time is '工作日实际工时';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".completed_quantity is '完成数量';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".actual_completed_quantity is '实际完成数量';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".completed_time is '完成时间';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".stored is '是否已入库';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".stored_time is '入库时间';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".process_type is '工序类型';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".triple_product_stored is '三倍产量是否入库';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".stored_quantity is '入库数量';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".notes is '备注';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".work_day_ot_duration is '工作日加班时长';" +
                "comment on column wfm_working_hour_analyze_" + tenantId + ".rest_day_ot_duration is '休息日加班时长';";
        tenantDo.initTable(stringBuffer);
    }
}
