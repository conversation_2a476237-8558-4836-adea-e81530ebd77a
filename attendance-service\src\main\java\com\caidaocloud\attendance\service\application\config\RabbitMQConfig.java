package com.caidaocloud.attendance.service.application.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * RabbitMQ 配置类
 * 用于配置消息确认模式等
 */
@Slf4j
@Configuration
public class RabbitMQConfig {

    @Autowired
    private AttendanceMqProperties mqProperties;

    /**
     * 配置手动ACK的监听器容器工厂
     * 针对长时间运行的消息处理进行优化
     */
    @Bean("manualAckContainerFactory")
    public SimpleRabbitListenerContainerFactory manualAckContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);

        // 设置为手动确认模式
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);

        // 从配置类获取消费者并发配置
        factory.setConcurrentConsumers(mqProperties.getConsumer().getConcurrentConsumers());
        factory.setMaxConcurrentConsumers(mqProperties.getConsumer().getMaxConcurrentConsumers());

        // 设置预取数量，控制每个消费者一次性获取的消息数量
        factory.setPrefetchCount(mqProperties.getConsumer().getPrefetchCount());

        // 设置默认重试次数为0，通过业务逻辑控制重试
        factory.setDefaultRequeueRejected(false);

        // ===== 长时间运行优化配置 =====

        // 设置接收超时时间
        factory.setReceiveTimeout(mqProperties.getConsumer().getReceiveTimeout());

        // 设置是否自动启动
        factory.setAutoStartup(mqProperties.getConsumer().isAutoStartup());

        // 配置任务执行器以提供更好的线程管理
        factory.setTaskExecutor(createTaskExecutor());

        // 设置错误处理器（可选）
        factory.setErrorHandler(t -> {
            // 这里不会处理业务异常，只处理框架级别的异常
            log.error("RabbitMQ container error: {}", t.getMessage(), t);
        });

        // 关闭消费者超时检查（允许长时间处理）
        // 注意：这意味着我们依赖应用程序级别的超时控制
        factory.setIdleEventInterval(mqProperties.getConsumer().getConsumerTimeout());

        return factory;
    }

    /**
     * 创建专用的任务执行器（用于RabbitMQ消费者）
     */
    private ThreadPoolTaskExecutor createTaskExecutor() {
        ThreadPoolConfig.ThreadPoolProperties config = ThreadPoolConfig.createRabbitMQConsumerConfig(mqProperties);
        return ThreadPoolConfig.createThreadPool(config);
    }

    /**
     * 创建异步处理专用线程池
     */
    @Bean("asyncProcessingExecutor")
    public ThreadPoolTaskExecutor asyncProcessingExecutor() {
        ThreadPoolConfig.ThreadPoolProperties config = ThreadPoolConfig.createAsyncProcessingConfig(mqProperties);
        return ThreadPoolConfig.createThreadPool(config);
    }

    /**
     * 创建超时检测专用线程池
     */
    @Bean("timeoutCheckExecutor")
    public ThreadPoolTaskExecutor timeoutCheckExecutor() {
        ThreadPoolConfig.ThreadPoolProperties config = ThreadPoolConfig.createTimeoutCheckConfig(mqProperties);
        return ThreadPoolConfig.createThreadPool(config);
    }

    /**
     * 创建启动任务专用线程池
     */
    @Bean("startupTaskExecutor")
    public ThreadPoolTaskExecutor startupTaskExecutor() {
        ThreadPoolConfig.ThreadPoolProperties config = ThreadPoolConfig.createStartupTaskConfig();
        return ThreadPoolConfig.createThreadPool(config);
    }
} 