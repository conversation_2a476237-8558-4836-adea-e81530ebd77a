package com.caidaocloud.attendance.core.commons.handler;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.params.ExcelForEachParams;
import cn.afterturn.easypoi.excel.export.styler.IExcelExportStyler;
import org.apache.poi.ss.usermodel.*;


/**
 * <AUTHOR>
 */
public class ExcelStyleHandler implements IExcelExportStyler {

    private Workbook workbook;

    public ExcelStyleHandler(Workbook workbook) {
        this.workbook = workbook;
    }

    public static void autoWidth(Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(0);
        for (Row row : sheet) {
            short lastCellNum = row.getLastCellNum();
            for (int i = 0; i < lastCellNum; i++) {
                Cell cell = row.getCell(i);
                if (cell != null) {
                    int columnWidth = sheet.getColumnWidth(i) / 256;
                    if (cell.getCellType() == CellType.STRING) {
                        int length = cell.getStringCellValue().getBytes().length;
                        if (columnWidth < length) {
                            columnWidth = length;
                            int colWidth = (columnWidth > 255 ? 255 : columnWidth) * 256;
                            sheet.setColumnWidth(i, colWidth);
                        }
                    }
                }
            }
        }
    }

    @Override
    public CellStyle getHeaderStyle(short i) {
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.index);

        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 11);
        headerFont.setFontName("微软雅黑");
        headerFont.setCharSet(Font.DEFAULT_CHARSET);
        headerStyle.setFont(headerFont);
        return headerStyle;
    }

    @Override
    public CellStyle getTitleStyle(short i) {
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titleStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.index);

        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 11);
        titleFont.setFontName("微软雅黑");
        titleFont.setCharSet(Font.DEFAULT_CHARSET);
        titleStyle.setFont(titleFont);

        return titleStyle;
    }

    @Override
    public CellStyle getStyles(boolean isTemplate, ExcelExportEntity excelExportEntity) {
        CellStyle cellStyle = isTemplate ? getTemplateStyles(isTemplate, null) : workbook.createCellStyle();
        // 设置单元格样式，例如字体、颜色、对齐方式等
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName("微软雅黑");
        font.setCharSet(Font.DEFAULT_CHARSET);
        cellStyle.setFont(font);

        return cellStyle;
    }

    @Override
    public CellStyle getStyles(Cell cell, int i, ExcelExportEntity excelExportEntity, Object obj, Object value) {
        CellStyle cellStyle = cell.getRow().getSheet().getWorkbook().createCellStyle();
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName("微软雅黑");
        font.setCharSet(Font.DEFAULT_CHARSET);
        cellStyle.setFont(font);
        cell.getSheet().autoSizeColumn(cell.getColumnIndex());
        return cellStyle;
    }

    @Override
    public CellStyle getTemplateStyles(boolean isTemplate, ExcelForEachParams excelForEachParams) {
        CellStyle templateStyle = isTemplate ? workbook.createCellStyle() : null;
        // 设置模板样式，例如字体、颜色、对齐方式等
        templateStyle.setBorderBottom(BorderStyle.THIN);
        templateStyle.setBorderLeft(BorderStyle.THIN);
        templateStyle.setBorderRight(BorderStyle.THIN);
        templateStyle.setBorderTop(BorderStyle.THIN);
        templateStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        templateStyle.setAlignment(HorizontalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName("微软雅黑");
        font.setCharSet(Font.DEFAULT_CHARSET);
        templateStyle.setFont(font);

        return templateStyle;
    }
}