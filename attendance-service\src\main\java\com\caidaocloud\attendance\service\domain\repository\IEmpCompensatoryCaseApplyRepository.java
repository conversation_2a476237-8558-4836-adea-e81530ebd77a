package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryCaseApplyDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCaseApply;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

public interface IEmpCompensatoryCaseApplyRepository {
    EmpCompensatoryCaseApplyDo getDetailById(String tenantId, Long id);

    void save(WaEmpCompensatoryCaseApply model);

    void update(WaEmpCompensatoryCaseApply model);

    PageList<EmpCompensatoryCaseApplyDo> getEmpCompensatoryCaseList(MyPageBounds myPageBounds, Map params);

    void batchSave(List<WaEmpCompensatoryCaseApply> records);

    List<EmpCompensatoryCaseApplyDo> getApprovedOfCompensatoryCase(List<Long> empIdList, Long startDate, Long endDate);

    List<EmpCompensatoryCaseApplyDo> getCompensatoryCaseList(String tenantId, List<Long> ids, List<Integer> status);
}
