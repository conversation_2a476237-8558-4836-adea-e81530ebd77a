package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.QuotaGenRuleDo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/27
 */
public interface IQuotaGenRuleRepository {
    int save(QuotaGenRuleDo quotaGenRuleDo);

    int delete(String tenantId, Long configId);

    List<QuotaGenRuleDo> getListByConfigId(String tenantId, Long configId);

    List<QuotaGenRuleDo> getListByConfigIds(String tenantId, List<Long> configIds);
}
