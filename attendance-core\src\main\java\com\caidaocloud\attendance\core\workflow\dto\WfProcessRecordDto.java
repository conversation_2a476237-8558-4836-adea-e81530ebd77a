package com.caidaocloud.attendance.core.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "流程记录")
public class WfProcessRecordDto {
    private String businessKey;
    private String taskId;
    @ApiModelProperty("身份")
    private String identity;
    @ApiModelProperty("处理人")
    private String handler;
    @ApiModelProperty("处理动作")
    private String operate;
    @ApiModelProperty("处理动作code")
    private String operateCode;
    @ApiModelProperty("处理时间")
    private Long endTime;
    private String comment;
}