package com.caidaocloud.attendance.service.application.event.subscribe;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.attendance.service.infrastructure.util.DateUtil;
import com.caidaocloud.attendance.service.application.cron.ClockTaskService;
import com.caidaocloud.attendance.service.application.dto.clock.WaWorkCalendarDto;
import com.caidaocloud.attendance.service.interfaces.dto.WorkCalendarReqDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@RabbitListener(
        bindings = @QueueBinding(
                value = @Queue(value = "attendance.workcalendar.queue", durable = "true"),
                exchange = @Exchange(value = "attendance.workcalendar.fac.direct.exchange"),
                key = {"routingKey.workcalendar"}
        )
)
public class WorkCalendarSubscribe {
    @Resource
    private ClockTaskService clockTaskService;

    @RabbitHandler
    public void process(String message) {
        log.info("start WorkCalendarMessage={}", message);
        try {
            // WorkCalendarMessage msgInfo = JSON.parseObject(message, WorkCalendarMessage.class);
            WaWorkCalendarDto waWorkCalendarDto = JSON.parseObject(message, WaWorkCalendarDto.class);
            clockTaskService.batchDeleteShift(waWorkCalendarDto.getBelongOrgid(), waWorkCalendarDto.getCorpid().toString());


            WorkCalendarReqDto workCalendarReqDto = new WorkCalendarReqDto();
            workCalendarReqDto.setStartDate(DateUtil.getNextDate(-1));
            workCalendarReqDto.setEndDate(DateUtil.getEndDate());
            workCalendarReqDto.setBelongOrgId(waWorkCalendarDto.getBelongOrgid());
            workCalendarReqDto.setCorpId(waWorkCalendarDto.getCorpid());
            clockTaskService.batchSaveShift(workCalendarReqDto);
        } catch (Exception ex) {
            log.error("WorkCalendarSubscribe process err,{}", ex.getMessage(), ex);
        }
        log.info("end WorkCalendarMessage={}", message);
    }
}
