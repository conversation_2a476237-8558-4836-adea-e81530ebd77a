package com.caidao1.mobile.service;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.mybatis.model.SysEmpInfoExample;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.RedisCache;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.enums.CompensateTypeEnum;
import com.caidao1.commons.enums.LeaveStatusEnum;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.utils.*;
import com.caidao1.employee.mybatis.mapper.EmpInfoMapper;
import com.caidao1.ioc.util.IocUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.mobile.bean.SessionBean;
import com.caidao1.payroll.common.PayEngineUtil;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.mapper.SysCorpNoticeMapper;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.mapper.SysEmpCollectMapper;
import com.caidao1.system.mybatis.mapper.SysUserCorpMapper;
import com.caidao1.system.mybatis.model.*;
import com.caidao1.wa.dto.EmpQuoDto;
import com.caidao1.wa.dto.EmpQuotaDTO;
import com.caidao1.wa.dto.UsableCompensatoryQuotaDto;
import com.caidao1.wa.enums.LeaveCancelSettingTypeEnum;
import com.caidao1.wa.enums.LeaveCancelStatusEnum;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidao1.xss.test.cache.RedisService;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.commons.utils.TimeRangeCheckUtil;
import com.caidaocloud.attendance.core.wa.dto.*;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.WaLeavePeriodTypeEnum;
import com.caidaocloud.attendance.core.wa.service.RemoteSmartWorkTimeService;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.HomeLeaveType;
import com.caidaocloud.attendance.service.application.enums.OvertimeBelongType;
import com.caidaocloud.attendance.service.application.enums.QuotaRestrictionTypeEnum;
import com.caidaocloud.attendance.service.application.enums.QuotaTypeEnum;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.QuotaMapper;
import com.caidaocloud.em.HalfDayTypeEnum;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.web.ResponseWrap;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * MobileV16Service 改造之前的备份，备份日期20250210
 * TODO 此类由于实现了ScriptBindable，因此不清楚哪里有使用的，因此先保留一段时间，后面确定没有使用的地方就删除即可,
 * TODO 此类不做代码维护，有使用的方法请将方法迁移至MobileV16Service类中进行改造
 */
@Slf4j
@Service
@Deprecated
public class MobileV16ServiceBackUp implements ScriptBindable {
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private SysCorpNoticeMapper sysCorpNoticeMapper;
    @Autowired
    private SysEmpCollectMapper sysEmpCollectMapper;
    @Autowired
    private WaApprovalChartMapper waApprovalChartMapper;
    @Autowired
    private WaWorktimeDetailMapper waWorktimeDetailMapper;
    @Autowired
    private WaShiftDefMapper waShiftDefMapper;
    @Autowired
    private WaEmpOvertimeMapper waEmpOvertimeMapper;
    @Autowired
    private WaEmpOvertimeDetailMapper waEmpOvertimeDetailMapper;
    @Autowired
    private WaApprovalMapper waApprovalMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private WaLeaveFileMapper waLeaveFileMapper;
    @Autowired
    private WaEmpLeaveTimeMapper waEmpLeaveTimeMapper;
    @Autowired
    private WaLeaveDaytimeMapper waLeaveDaytimeMapper;
    @Autowired
    private WaLeaveSettingMapper waLeaveSettingMapper;
    @Autowired
    private WaEmpQuotaMapper waEmpQuotaMapper;
    @Autowired
    private WaApprovalFlowMapper waApprovalFlowMapper;
    @Autowired
    private WaOvertimeTypeMapper waOvertimeTypeMapper;
    @Autowired
    private WaHolidayCalendarMapper waHolidayCalendarMapper;
    @Autowired
    private EmpInfoMapper empInfoMapper;
    @Autowired
    private SysUserCorpMapper sysUserCorpMapper;
    @Autowired
    private WaStoreTimeMapper waStoreTimeMapper;
    @Autowired
    private WaConfigMapper waConfigMapper;
    @Autowired
    private MessageResource messageResource;
    /**
     * todo 申请休假，小时假时长计算新逻辑开启开关：(后面测试稳定后，去掉此开关)
     */
    @Value("${caidao.applyleave.calhourtime.opennew:true}")
    private Boolean applyleaveCalHourTimeOpenNew;
    /**
     * todo 申请休假，时间重叠校验逻辑开关：(后面测试稳定后，去掉此开关)
     */
    @Value("${caidao.applyleave.timeoverlap.opennew:true}")
    private Boolean applyleaveTimeoverlapOpenNew;
    @Autowired
    private WaEmpGroupMapper waEmpGroupMapper;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private WaCheckMapper waCheckMapper;
    @Autowired
    private WaOvertimeFileMapper waOvertimeFileMapper;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private WaEmpQuotaDetailMapper waEmpQuotaDetailMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private RemoteSmartWorkTimeService remoteSmartWorkTimeService;
    @Autowired
    private PreEmpOvertimeMapper preEmpOvertimeMapper;
    @Autowired
    private WaWorktimeMapper waWorktimeMapper;
    @Autowired
    private EmpCompensatoryQuotaMapper waEmpCompensatoryQuotaMapper;
    @Autowired
    private WaCompensatoryQuotaUseMapper waCompensatoryQuotaUseMapper;
    @Autowired
    private WaLeaveQuotaUseMapper waLeaveQuotaUseMapper;
    @Autowired
    private WaSobService waSobService;
    @Autowired
    private EmpTravelMapper empTravelMapper;
    @Autowired
    private WaLeaveTypeDefMapper waLeaveTypeDefMapper;

    @Autowired
    private QuotaMapper quotaMapper;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public List findEmpCollect(Map<String, Object> params) {
        // TODO Auto-generated method stub
        return sysEmpCollectMapper.findEmpCollect(params);
    }

    public Map getEmpInfo(Long empid) {
        return sysEmpInfoMapper.getMobileEmpInfo(empid);
    }

    /**
     * @param empid 员工ID
     * @param type  1请假，2加班，3离职
     * @return
     */
    public List getApprovalEmpList(Long empid, Integer type) {
        List<Map> list = new ArrayList();

        WaApprovalChartExample example = new WaApprovalChartExample();
        WaApprovalChartExample.Criteria criteria = example.createCriteria();
        criteria.andTrancationTypeEqualTo(type.shortValue());
        Map map = new HashMap();
        map.put("applyEmpid", empid);
        map.put("type", type);
        List<Map> list1 = waApprovalChartMapper.getApprovalList1(map);
        if (list1.size() > 0) {
            list.add(list1.get(0));
            List<Map> list2 = waApprovalChartMapper.getApprovalList2(map);
            if (list2.size() > 0) {
                list.add(list2.get(0));
            }
            return list;
        }
        map.put("type", 0);
        list1 = waApprovalChartMapper.getApprovalList1(map);
        if (list1.size() > 0) {
            list.add(list1.get(0));
            List<Map> list2 = waApprovalChartMapper.getApprovalList2(map);
            if (list2.size() > 0) {
                list.add(list2.get(0));
            }
        }
        return list;

    }

    public Map getOtCycleDate(Integer cyleStartdate, Long startTime, Long endTime) throws Exception {
        Map startCycle = this.calculateCycleTime(startTime, cyleStartdate);
        Map endCycle = this.calculateCycleTime(endTime, cyleStartdate);

        if (startCycle != null && endCycle != null) {
            Long cycleBegin = (Long) startCycle.get("cycleBegin");//考勤周期开始时间
            Long cycleEnd = (Long) startCycle.get("cycleEnd");//考勤周期结束时间

            Long cycleBegin1 = (Long) endCycle.get("cycleBegin");
            Long cycleEnd1 = (Long) endCycle.get("cycleEnd");

            if (cycleBegin.equals(cycleBegin1) && cycleEnd.equals(cycleEnd1)) {
                Map result = new HashMap();
                result.put("cycleBegin", cycleBegin);
                result.put("cycleEnd", cycleEnd);
                return result;
            } else {
                return null;
            }
        }
        return null;
    }

    /**
     * 校验加班上限
     *
     * @throws ParseException
     */

    public Map validateWaGroupOvertimeLimit(Integer waGroupId, Long empId, Long startTime, Long endTime, Long totalOtTime) throws Exception {
        Map rtnMap = new HashMap();
        rtnMap.put("status", 1);

        if (waGroupId != null) {
            //校验加班上限
            WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waGroupId);
            if (waGroup != null) {
                Integer cyleStartdate = waGroup.getCyleStartdate();//考勤周期开始日
                Integer overtimeLimitH = waGroup.getOvertimeLimit();//加班上限小时数
                Boolean isSetOvertimeLimit = waGroup.getIsSetOvertimeLimit();
                Boolean isOpenOtlimitWarn = waGroup.getIsOpenOtlimitWarn();
                Integer otToplimitH = waGroup.getOtToplimit();//加班上限小时数

                //加班上限
                if ((isSetOvertimeLimit != null && isSetOvertimeLimit && overtimeLimitH != null) || (isOpenOtlimitWarn != null && isOpenOtlimitWarn && otToplimitH != null)) {
                    Map cycleMap = getOtCycleDate(cyleStartdate, startTime, endTime);
                    if (cycleMap != null) {
                        //查询当前用户在考勤周期内总的加班时间
                        Map params = new HashMap();
                        params.put("empid", empId);
                        params.put("starttime", cycleMap.get("cycleBegin"));
                        params.put("endtime", cycleMap.get("cycleEnd"));
                        Long totalDuration = waEmpOvertimeMapper.getEmpOverTimeTotalDuration(params);
                        totalDuration = totalDuration == null ? 0L : totalDuration;

                        if (isSetOvertimeLimit != null && isSetOvertimeLimit && overtimeLimitH != null) {
                            //剩余可用的加班时常（判断是否已经超过考勤周期的加班上限，如果超出了就不允许申请加班，反之可以）
                            Long overtimeLimitMin = Long.valueOf(overtimeLimitH * 60);//加班上限分钟
                            Long surplusDuration = overtimeLimitMin - totalDuration;
                            if (surplusDuration > 0) {
                                if (totalOtTime.intValue() > surplusDuration.intValue()) {
                                    //剩余可用加班时间为" + surplusDuration / 60 + "小时" + surplusDuration % 60 + "分钟，本次申请加班的时间超出啦可用额度！
                                    rtnMap.put("status", "-1");
                                    rtnMap.put("message", messageResource.getMessage("L006224", new Object[]{(surplusDuration / 60), (surplusDuration % 60)}, new Locale(SessionHolder.getLang())));
                                    return rtnMap;
                                }
                            } else {
                                //本月加班上限" + overtimeLimitH + "小时，已申请" + totalDuration.intValue() / 60 + "小时" + totalDuration % 60 + "分钟" + "，剩余0小时0分钟！
                                rtnMap.put("status", "-1");
                                rtnMap.put("message", messageResource.getMessage("L006223", new Object[]{overtimeLimitH, (totalDuration.intValue() / 60), (totalDuration % 60)}, new Locale(SessionHolder.getLang())));
                                return rtnMap;
                            }
                        } else {//加班上限提示校验
                            Long otToplimitMin = Long.valueOf(otToplimitH * 60);//加班上限分钟
                            Long surplusDuration = otToplimitMin - totalDuration - totalOtTime;
                            if (surplusDuration <= 0) {//本月申请加班时长已超过{0}小时
                                rtnMap.put("status", "2");
                                rtnMap.put("message", messageResource.getMessage("L006226", new Object[]{otToplimitH}, new Locale(SessionHolder.getLang())));
                                return rtnMap;
                            }
                        }
                    } else {//加班不允许跨考勤周期
                        rtnMap.put("status", "-1");
                        rtnMap.put("message", messageResource.getMessage("L006225", new Object[]{}, new Locale(SessionHolder.getLang())));
                        return rtnMap;
                    }
                }
            }
        }
        return rtnMap;
    }

    /**
     * 根据时间计算当前时间所在的考勤周期
     *
     * @param dateTime
     * @param cyleStartdate
     * @return
     * @throws ParseException
     */
    public Map calculateCycleTime(Long dateTime, Integer cyleStartdate) throws ParseException {
        Long preMonthBegin = DateUtilExt.getMonth(dateTime, -1, 1);//上个月开始时间
        Long preMonthEnd = DateUtilExt.getMonthEnd(preMonthBegin);//上个月的结束时间
        Long curMonthBegin = DateUtilExt.getMonth(dateTime, 0, 1);//本月开始时间
        Long curMonthEnd = DateUtilExt.getMonthEnd(curMonthBegin);//本月结束时间
        Long curCycleBegin = null;//当前时间所在的考勤周期开始时间
        Long curCycleEnd = null;//当前时间所在的考勤周期结束时间
        if (cyleStartdate > 1) {
            Long addDayMin = Long.valueOf((cyleStartdate - 1) * 86400);
            preMonthBegin += addDayMin;
            preMonthEnd += addDayMin;
            curMonthBegin += addDayMin;
            curMonthEnd += addDayMin;
        }
        //加班的时间不允许跨考勤周期，校验加班时间是否跨考勤周期
        //当前时间的考勤周期
        if (dateTime >= preMonthBegin && dateTime <= preMonthEnd) {
            curCycleBegin = preMonthBegin;
            curCycleEnd = preMonthEnd;
        } else if (dateTime >= curMonthBegin && dateTime <= curMonthEnd) {
            curCycleBegin = curMonthBegin;
            curCycleEnd = curMonthEnd;
        } else {
            return null;
        }
        Map resultMap = new HashMap();
        resultMap.put("cycleBegin", curCycleBegin);
        resultMap.put("cycleEnd", curCycleEnd);
        return resultMap;
    }

    /**
     * 检查加班时间是否包含上班时间
     *
     * @param workTimeDetail
     * @param preWorkTimeDetail
     * @param waShiftDef
     * @param preShiftDef
     * @param overTimeStart
     * @param overTimeEnd
     * @param overtimeDate
     * @return
     */
    public boolean checkOverTime(WaWorktimeDetail workTimeDetail, WaWorktimeDetail preWorkTimeDetail, WaShiftDef waShiftDef, WaShiftDef preShiftDef,
                                 long overTimeStart, long overTimeEnd, Long overtimeDate) {
        long overtimeStartTime = overtimeDate + overTimeStart;
        long overtimeEndTime = overtimeDate + overTimeEnd;

        // 当前一天为跨夜班时，检查加班时间是否在前一天上班时间段内
        if (null != preWorkTimeDetail && preWorkTimeDetail.getDateType() == 1
                && CdWaShiftUtil.checkCrossNight(preShiftDef.getStartTime(), preShiftDef.getEndTime(), preShiftDef.getDateType())) {
            boolean inWorkTime = checkIfInWorkTime(preWorkTimeDetail, preShiftDef, overtimeStartTime, overtimeEndTime);
            if (inWorkTime) {
                // 如果在上班时间内，检查是否在上班时间内的休息时间段内
                return checkRestTime(preWorkTimeDetail, preShiftDef, overtimeStartTime, overtimeEndTime);
            }
        }

        // 检查加班时间是否在当天上班时间段内
        if (null != workTimeDetail && workTimeDetail.getDateType() == 1) {
            boolean inWorkTime = checkIfInWorkTime(workTimeDetail, waShiftDef, overtimeStartTime, overtimeEndTime);
            if (inWorkTime) {
                // 如果在上班时间内，检查是否在上班时间内的休息时间段内
                return checkRestTime(workTimeDetail, waShiftDef, overtimeStartTime, overtimeEndTime);
            }
        }

        return Boolean.TRUE;
    }

    /**
     * 校验加班时间是否不在上班时间段内
     *
     * @param workTimeDetail
     * @param shiftDef
     * @param overtimeStartTime
     * @param overtimeEndTime
     * @return
     */
    private boolean checkIfInWorkTime(WaWorktimeDetail workTimeDetail, WaShiftDef shiftDef, Long overtimeStartTime, Long overtimeEndTime) {
        long startTimeClockMin = shiftDef.getStartTime();
        long endTimeClockMin = shiftDef.getEndTime() == 0 ? 1440L : shiftDef.getEndTime();
        if (CdWaShiftUtil.checkCrossNight(startTimeClockMin, endTimeClockMin, shiftDef.getDateType())) {// 跨夜班
            endTimeClockMin = endTimeClockMin + 1440L;
        }

        long startTime = workTimeDetail.getWorkDate() + startTimeClockMin * 60L;
        long endTime = workTimeDetail.getWorkDate() + endTimeClockMin * 60L;

        // true 加班时间和上班时间有交集，false 没交集
        return overtimeStartTime < endTime && overtimeEndTime > startTime;
    }

    /**
     * 校验加班时间是否在工作时间范围内的休息时间段中
     *
     * @param workTimeDetail
     * @param shiftDef
     * @param overtimeStartTime
     * @param overtimeEndTime
     * @return
     */
    private boolean checkRestTime(WaWorktimeDetail workTimeDetail, WaShiftDef shiftDef, Long overtimeStartTime, Long overtimeEndTime) {
        // 是否在班次休息时间段内， true 在，false 不在
        boolean ifInRestTime = false;
        //获取上班期间休息的时间
        if (shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) {
            List<Map> restList = new ArrayList<>();
            //定义半天的休息时间段
            Integer noonRestStart = shiftDef.getNoonRestStart();
            Integer nooRestEnd = shiftDef.getNoonRestEnd();
            if (noonRestStart != null && nooRestEnd != null) {
                Map noonRestMap = new LinkedHashMap();
                noonRestMap.put("start", noonRestStart);
                noonRestMap.put("end", nooRestEnd);
                restList.add(noonRestMap);
            }
            //标准的休息时间段
            List<Map> noonRestPeriods = null;
            try {
                noonRestPeriods = (List) JacksonJsonUtil.jsonToBean(shiftDef.getRestPeriods().toString(), List.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (CollectionUtils.isNotEmpty(noonRestPeriods)) {
                for (Map map : noonRestPeriods) {
                    map.put("start", map.get("noonRestStart"));
                    map.put("end", map.get("noonRestEnd"));
                }
                restList.addAll(noonRestPeriods);
            }
            if (CollectionUtils.isNotEmpty(restList)) {
                //检验加班时间是否在上班休息时间范围之间
                for (Map map : restList) {
                    Integer restStart = (Integer) map.get("start");
                    Integer restEnd = (Integer) map.get("end");

                    ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(restStart, restEnd, shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType());
                    restStart = restPeriod.getNoonRestStart();
                    restEnd = restPeriod.getNoonRestEnd();

                    long restStartTime = workTimeDetail.getWorkDate() + restStart * 60L;
                    long restEndTime = workTimeDetail.getWorkDate() + restEnd * 60L;

                    if (overtimeStartTime >= restStartTime && overtimeEndTime <= restEndTime) {
                        ifInRestTime = true;
                        break;
                    }
                }
            }
        }
        return ifInRestTime;
    }

    /**
     * 校验加班时间，获取加班总时长
     *
     * @param sessionBean
     * @param empId       员工ID
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    public Map getOtTotaltime(SessionBean sessionBean, Long empId, Long startTime, Long endTime) throws Exception {
        Map rtnMap = new HashMap();
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();

        if (tmType == 0) {
            tmType = 1;
        } else if (tmType == 2) {//门店考勤人员
            Jedis jedis = RedisService.getResource();
            String appStoreEnable = jedis.get(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + sessionBean.getBelongid() + "_APP_STORE_Enable");
            try {
                if (appStoreEnable == null || (!appStoreEnable.equals("1"))) {
                    rtnMap.put("status", -1);
//                    rtnMap.put("message", "不支持门店考勤人员加班！");
                    rtnMap.put("message", messageResource.getMessage("L005712", new Object[]{}, new Locale(sessionBean.getLanguage())));
                    return rtnMap;
                }
            } catch (RuntimeException e) {
                jedis.close();
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }

        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));//加班开始日期
        long endDate = DateUtil.getOnlyDate(new Date(endTime * 1000));//加班结束日期
        long preDate = startDate - 1440L * 60;//加班开始日期的前一天
        long startTimeMin = startTime;
        long endTimeMin = endTime;
        long startMin = startTimeMin - startDate;//加班开始时间点
        long endMin = endTimeMin - startDate;//加班结束时间点
        long jg = endTimeMin - startTimeMin;//加班时间长度

        if (endTimeMin < startTimeMin) {
            rtnMap.put("status", -1);
            rtnMap.put("message", messageResource.getMessage("L005691", new Object[]{}, new Locale(sessionBean.getLanguage())));
            return rtnMap;
        }
        boolean isSecondDay = false;
        if (endDate > startDate) {//说明加班到第二天
            if (endTimeMin > startTimeMin + 24 * 60 * 60) {
                rtnMap.put("status", -1);
                rtnMap.put("message", messageResource.getMessage("L005692", new Object[]{}, new Locale(sessionBean.getLanguage())));
                return rtnMap;
            }
            isSecondDay = true;
        }

        Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(sessionBean.getBelongid(),
                sessionBean.getEmpid(), tmType, preDate, endDate);

        long tmpDate = startDate;
        while (tmpDate <= endDate) {
            if (pbMap == null || pbMap.get(tmpDate) == null) {//没有排班记录
                rtnMap.put("status", -1);
                rtnMap.put("message", DateUtil.getDateStrByTimesamp(tmpDate) + messageResource.getMessage("L005713", new Object[]{}, new Locale(sessionBean.getLanguage())));
                return rtnMap;
            }
            tmpDate = tmpDate + 24 * 60 * 60;
        }
        Integer empWaGroupId = null;
        //申请加班开始日期排班
        WaWorktimeDetail workTimeDetail = pbMap.get(startDate);
        Integer firstDateType = workTimeDetail.getDateType();//工作日历日期类型
        //查询员工所在的考勤分组
        Map groupParams = new HashMap();
        groupParams.put("empid", empId);
        List<Map> listEmpWaGroup = waMapper.listEmpWaGroup(groupParams);
        if (CollectionUtils.isNotEmpty(listEmpWaGroup)) {
            //有考勤分组
            Map groupMap = listEmpWaGroup.get(0);
            empWaGroupId = (Integer) groupMap.get("wa_group_id");

            Integer[] dateTypes = waEmpGroupMapper.getDateTypeByGroupId(empWaGroupId);
            if (dateTypes != null && dateTypes.length > 0) {
                boolean flag = false;
                for (Integer dateTypeTmp : dateTypes) {
                    if (firstDateType.intValue() == dateTypeTmp.intValue()) {
                        flag = true;
                    }
                }
                if (!flag) {
                    rtnMap.put("status", -1);
                    rtnMap.put("message", DateUtil.parseDateToPattern(new Date(startTime * 1000), "yyyy-MM-dd") + messageResource.getMessage("L005714", new Object[]{}, new Locale(sessionBean.getLanguage())));
                    return rtnMap;
                }
            } else {
                rtnMap.put("status", -1);
                rtnMap.put("message", DateUtil.parseDateToPattern(new Date(startTime * 1000), "yyyy-MM-dd") + messageResource.getMessage("L005714", new Object[]{}, new Locale(sessionBean.getLanguage())));
                return rtnMap;
            }
        }
        WaShiftDef waShiftDef = waShiftDefMapper.selectByPrimaryKey(workTimeDetail.getShiftDefId());//加班开始日期的日工作计划
        //校验日工作计划是否设置加班时间
        if (waShiftDef.getOvertimeStartTime() == null || waShiftDef.getOvertimeEndTime() == null) {
            rtnMap.put("status", -1);
            rtnMap.put("message", messageResource.getMessage("L005777", new Object[]{}, new Locale(sessionBean.getLanguage())));
            return rtnMap;
        }
        //判断加班开始时间是否在加班允许的时间范围内
        if (waShiftDef.getOvertimeStartTime() < waShiftDef.getOvertimeEndTime()) {
            //日工作计划中设置：加班结束时间大于加班开始时间
            if (startMin < waShiftDef.getOvertimeStartTime() * 60) {
                rtnMap.put("status", -1);
                //rtnMap.put("message", "必须在" + getTimeStr(waShiftDef.getOvertimeStartTime()) + "之后才能申请加班");
                rtnMap.put("message", messageResource.getMessage("L005694", new Object[]{getTimeStr(waShiftDef.getOvertimeStartTime(), sessionBean)}, new Locale(sessionBean.getLanguage())));
                return rtnMap;
            }
            //加班不允许跨夜
            if (isSecondDay) {
                rtnMap.put("status", -1);
                rtnMap.put("message", messageResource.getMessage("L005776", new Object[]{}, new Locale(sessionBean.getLanguage())));
                return rtnMap;
            }
        } else if (waShiftDef.getOvertimeStartTime() > waShiftDef.getOvertimeEndTime()) {
            //日工作计划中设置：加班结束时间小于加班开始时间
            if (startMin > waShiftDef.getOvertimeEndTime() * 60 && startMin < waShiftDef.getOvertimeStartTime() * 60) {
                //不能申请加班的时间段
                rtnMap.put("status", -1);
                //rtnMap.put("message", "必须在" + getTimeStr(waShiftDef.getOvertimeEndTime()) + "之前或者" + getTimeStr(waShiftDef.getOvertimeStartTime()) + "之后才能申请加班");
                rtnMap.put("message", messageResource.getMessage("L005715", new Object[]{getTimeStr(waShiftDef.getOvertimeEndTime(), sessionBean), getTimeStr(waShiftDef.getOvertimeStartTime(), sessionBean)}, new Locale(sessionBean.getLanguage())));
                return rtnMap;
            }
        }

        WaWorktimeDetail preWorkTimeDetail = pbMap.get(preDate);
        WaShiftDef preShiftDef = null;
        if (preWorkTimeDetail != null) {
            preShiftDef = waShiftDefMapper.selectByPrimaryKey(preWorkTimeDetail.getShiftDefId());
        }

        // 检查加班开始时间是否包含上班时间
        boolean checkOverTimeResult = checkOverTime(workTimeDetail, preWorkTimeDetail, waShiftDef, preShiftDef,
                startMin, isSecondDay ? 86400L : endMin, startDate);
        if (!checkOverTimeResult) {// 申请加班时间不允许包含上班时间
            rtnMap.put("status", -1);
            rtnMap.put("message", messageResource.getMessage("L005843", new Object[]{}, new Locale(sessionBean.getLanguage())));
            return rtnMap;
        }

        if (isSecondDay) {
            //加班到了第2天,查看第2天是否有排班
            WaWorktimeDetail secondDayWorkTimeDetail = pbMap.get(endDate);
            WaShiftDef waShiftDef2 = waShiftDefMapper.selectByPrimaryKey(secondDayWorkTimeDetail.getShiftDefId());
            Integer secondDateType = secondDayWorkTimeDetail.getDateType();

            //校验日工作计划是否设置加班时间
            if (waShiftDef2.getOvertimeStartTime() == null || waShiftDef2.getOvertimeEndTime() == null) {
                rtnMap.put("status", -1);
                rtnMap.put("message", messageResource.getMessage("L005777", new Object[]{}, new Locale(sessionBean.getLanguage())));
                return rtnMap;
            }

            boolean isValidateWorkTime2 = false;
            if (secondDateType.intValue() == 1) {
                isValidateWorkTime2 = true;
            }
            if (isValidateWorkTime2) {
                // 检查加班结束时间是否包含上班时间
                checkOverTimeResult = checkOverTime(secondDayWorkTimeDetail, workTimeDetail, waShiftDef2, waShiftDef,
                        0L, endTimeMin - endDate, endDate);
                if (!checkOverTimeResult) {
                    rtnMap.put("status", -1);
                    rtnMap.put("message", messageResource.getMessage("L005843", new Object[]{}, new Locale(sessionBean.getLanguage())));
                    return rtnMap;
                }

                //日工作计划中加班结束时间大于下班开始时间
                if (waShiftDef2.getOvertimeEndTime() > waShiftDef2.getOffDutyStartTime()) {
                    rtnMap.put("status", -1);
//                    rtnMap.put("message", "必须在" + getTimeStr(waShiftDef2.getOvertimeEndTime()) + "之后才能申请加班");
                    rtnMap.put("message", messageResource.getMessage("L005694", new Object[]{getTimeStr(waShiftDef2.getOvertimeEndTime(), sessionBean)}, new Locale(sessionBean.getLanguage())));
                    return rtnMap;
                }
            }
            //校验加班时间是否在：日工作计划设置的加班时间范围中
            long endSecondTime = endMin - 24 * 60 * 60;//本次加班结束时间
            if (endSecondTime > waShiftDef2.getOvertimeEndTime() * 60 && waShiftDef2.getOvertimeEndTime() > 0) {
                //本次加班结束时间大于日工作计划中设置的加班结束时间
                rtnMap.put("status", -1);
//                    rtnMap.put("message", "第二天，必须在" + getTimeStr(waShiftDef2.getOvertimeEndTime()) + "之前才能申请加班");
                rtnMap.put("message", messageResource.getMessage("L005697", new Object[]{getTimeStr(waShiftDef2.getOvertimeEndTime(), sessionBean)}, new Locale(sessionBean.getLanguage())));
                return rtnMap;
            }

            if (waShiftDef2.getOvertimeStartTime() > waShiftDef2.getOvertimeEndTime()) {
                //日工作计划中：加班结束时间小于加班开始时间
                //加班结束时间所在的日工作计划
                long secondDayOvertimeEndTime = waShiftDef2.getOvertimeEndTime() * 60;
                long endMinTemp = endTimeMin - endDate;
                if (!(endMinTemp >= 0 && endMinTemp <= secondDayOvertimeEndTime)) {
                    rtnMap.put("status", -1);
                    rtnMap.put("message", messageResource.getMessage("L005697", new Object[]{getTimeStr(secondDayOvertimeEndTime, sessionBean)}, new Locale(sessionBean.getLanguage())));
                    return rtnMap;
                }
            }
            //有考勤分组
            if (CollectionUtils.isNotEmpty(listEmpWaGroup)) {
                Integer dateType = secondDayWorkTimeDetail.getDateType();
                Integer waGroupId = (Integer) listEmpWaGroup.get(0).get("wa_group_id");
                Integer[] dateTypes = waEmpGroupMapper.getDateTypeByGroupId(waGroupId);
                if (dateTypes != null && dateTypes.length > 0) {
                    boolean flag = false;
                    for (Integer dateTypeTmp : dateTypes) {
                        if (dateType.intValue() == dateTypeTmp.intValue()) {
                            flag = true;
                        }
                    }
                    if (!flag) {
                        rtnMap.put("status", -1);
//                        rtnMap.put("message", DateUtil.getDateStrByTimesamp(startTime) + "不允许加班");
                        rtnMap.put("message", DateUtil.getDateStrByTimesamp(startTime) + messageResource.getMessage("L005714", new Object[]{}, new Locale(sessionBean.getLanguage())));
                        return rtnMap;
                    }
                } else {
                    rtnMap.put("status", -1);
//                    rtnMap.put("message", DateUtil.getDateStrByTimesamp(startTime) + "不允许加班");
                    rtnMap.put("message", DateUtil.getDateStrByTimesamp(startTime) + messageResource.getMessage("L005714", new Object[]{}, new Locale(sessionBean.getLanguage())));
                    return rtnMap;
                }
            }
            //扣除第一天的休息时间段，截止时间是24点
            jg -= calRestTotalTime(sessionBean.getBelongid(), waShiftDef, startMin, 86400L, firstDateType);
            //扣除第二天的休息时间段，开始时间是0点
            jg -= calRestTotalTime(sessionBean.getBelongid(), waShiftDef2, 0L, endTimeMin - endDate, secondDateType);
        } else {//在同一天加班
            //判断加班结束时间是否在加班的允许范围内
            if (waShiftDef.getOvertimeEndTime() > waShiftDef.getOvertimeStartTime()) {
                //日工作计划中设置：加班结束时间大于加班开始时间
                if (endMin > (waShiftDef.getOvertimeEndTime().intValue() * 60)) {
                    rtnMap.put("status", -1);
                    //rtnMap.put("message", "必须在" + getTimeStr(waShiftDef.getOvertimeEndTime()) + "之前才能申请加班");
                    rtnMap.put("message", messageResource.getMessage("L005698", new Object[]{getTimeStr(waShiftDef.getOvertimeEndTime(), sessionBean)}, new Locale(sessionBean.getLanguage())));
                    return rtnMap;
                }
            } else if (waShiftDef.getOvertimeStartTime() > waShiftDef.getOvertimeEndTime()) {
                //日工作计划中设置：加班结束时间小于加班开始时间
                //校验加班时间范围是否跨越
                long overtimeStartTime = waShiftDef.getOvertimeStartTime() * 60;
                long overtimeEndTime = waShiftDef.getOvertimeEndTime() * 60;
                if (!(startMin >= 0 && endMin <= overtimeEndTime) && !(startMin >= overtimeStartTime && endMin < 86400)) {
                    rtnMap.put("status", -1);
                    //rtnMap.put("message", " 加班的时间段必须在0点-" + getTimeStr(waShiftDef.getOvertimeEndTime()) + "\n" +
                    //getTimeStr(waShiftDef.getOvertimeStartTime()) + "-24点两段时间段内");
                    rtnMap.put("message", messageResource.getMessage("L005716",
                            new Object[]{getTimeStr(waShiftDef.getOvertimeEndTime(), sessionBean),
                                    getTimeStr(waShiftDef.getOvertimeStartTime(), sessionBean)}, new Locale(sessionBean.getLanguage())));
                    return rtnMap;
                }
            }
            jg -= calRestTotalTime(sessionBean.getBelongid().toString(), waShiftDef, startMin, endMin, firstDateType);
        }

        //校验加班上限&加班上限提示
        String message = "";
        Map validateMap = validateWaGroupOvertimeLimit(empWaGroupId, empId, startTime, endTime, jg / 60);
        Integer validateStatus = Integer.valueOf(String.valueOf(validateMap.get("status")));
        if (validateStatus == -1) {
            rtnMap.put("status", -1);
            rtnMap.put("message", validateMap.get("message"));
            return rtnMap;
        } else if (validateStatus == 2) {
            message = "<font color='#EE2C2C'>" + validateMap.get("message") + "</font>";
        }
        if ((jg / 60) % 60 > 0) {
//            rtnMap.put("data", MessageFormat.format("{0}小时{1}分钟", (jg / 3600), (jg / 60) % 60));
//            rtnMap.put("data", (jg / 3600)+"小时"+((jg / 60) % 60)+"分钟");
            if (jg / 3600 > 0) {
                rtnMap.put("data", messageResource.getMessage("L005572", new Object[]{(jg / 3600), ((jg / 60) % 60)}, new Locale(sessionBean.getLanguage())));
            } else {
                rtnMap.put("data", messageResource.getMessage("L006821", new Object[]{((jg / 60) % 60)}, new Locale(sessionBean.getLanguage())));
            }
        } else {
//            rtnMap.put("data", (jg / 3600) + "小时");
            rtnMap.put("data", messageResource.getMessage("L005573", new Object[]{(jg / 3600)}, new Locale(sessionBean.getLanguage())));
        }
        rtnMap.put("status", 1);
        rtnMap.put("message", message);
        return rtnMap;
    }

    /**
     * 计算加班时间段内休息的时间
     *
     * @param belongOrgId 租户
     * @param startMin    加班开始时间点 单位 秒
     * @param endMin      加班结束时间点 单位 秒
     * @return
     * @throws Exception
     */
    public Long calRestTotalTime(String belongOrgId, WaShiftDef waShiftDef, Long startMin, Long endMin, Integer dateType) throws Exception {
        //计算扣除中午休息的时间
        Long restTotalTime = 0L;
        List<Map> restList = new ArrayList<>();

        //加班专属的休息时间段
        if (waShiftDef.getOvertimeRestPeriods() != null) {
            List<Map> overtimeRestPeriods = (List) JacksonJsonUtil.jsonToBean(waShiftDef.getOvertimeRestPeriods().toString(), List.class);
            if (CollectionUtils.isNotEmpty(overtimeRestPeriods)) {
                restList.addAll(overtimeRestPeriods);
            }
        }

        if (CollectionUtils.isNotEmpty(restList)) {
            for (Map periodMap : restList) {
                Integer restStart = (Integer) periodMap.get("overtimeRestStartTime");
                Integer restEnd = (Integer) periodMap.get("overtimeRestEndTime");
                ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(restStart, restEnd, waShiftDef.getStartTime(), waShiftDef.getEndTime(), waShiftDef.getDateType());
                restStart = restPeriod.getNoonRestStart();
                restEnd = restPeriod.getNoonRestEnd();
                Long restStartSec = (long) (restStart * 60);
                Long restEndSec = (long) (restEnd * 60);
                if (startMin <= restEndSec && endMin >= restStartSec) {
                    if (waShiftDef.getDateType() == 3 || waShiftDef.getDateType() == 5) {
                        String key = RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongOrgId + RedisKeyDefine.WA_HOLIDAY_IGNORE;
                        String isIgnore = CDCacheUtil.getValue(key);
                        if (isIgnore == null || "0".equals(isIgnore)) {
                            restTotalTime += Math.min(restEndSec, endMin) - Math.max(restStartSec, startMin);
                        }
                    } else {
                        restTotalTime += Math.min(restEndSec, endMin) - Math.max(restStartSec, startMin);
                    }
                }
            }
        }
        return restTotalTime;
    }

    /**
     * 请假时扣减多段休息的时间
     *
     * @param workTimeDetail
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    public Integer deductLeaveDayNooRest(WaWorktimeDetail workTimeDetail, Long startTime, Long endTime) throws Exception {
        //计算扣除中午休息的时间，多段
        long durationSec = endTime - startTime;
        if (workTimeDetail.getIsNoonRest() != null && workTimeDetail.getIsNoonRest()) {
            List<Map> restList = new ArrayList<>();
            //定义半天的休息时间段
            Integer restStart = workTimeDetail.getNoonRestStart();
            Integer restEnd = workTimeDetail.getNoonRestEnd();
            if (restStart != null && restEnd != null) {
                Map noonRestMap = new LinkedHashMap();
                noonRestMap.put("noonRestStart", restStart);
                noonRestMap.put("noonRestEnd", restEnd);
                restList.add(noonRestMap);
            }
            if (workTimeDetail.getRestPeriods() != null) {
                List<Map> restPeriods = (List) JacksonJsonUtil.jsonToBean(workTimeDetail.getRestPeriods().toString(), List.class);
                if (CollectionUtils.isNotEmpty(restPeriods)) {
                    restList.addAll(restPeriods);
                }
            }
            if (CollectionUtils.isNotEmpty(restList)) {
                for (Map periodMap : restList) {
                    Integer noonRestStartMi = (Integer) periodMap.get("noonRestStart");
                    Integer noonRestEndMi = (Integer) periodMap.get("noonRestEnd");

                    ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMi, noonRestEndMi, workTimeDetail.getStartTime(), workTimeDetail.getEndTime(), workTimeDetail.getDateType());
                    noonRestStartMi = restPeriod.getNoonRestStart();
                    noonRestEndMi = restPeriod.getNoonRestEnd();

                    long noonRestStartTime = workTimeDetail.getWorkDate() + noonRestStartMi * 60;
                    long noonRestEndTime = workTimeDetail.getWorkDate() + noonRestEndMi * 60;

                    if (startTime <= noonRestEndTime && endTime >= noonRestStartTime) {
                        durationSec -= Math.min(noonRestEndTime, endTime) - Math.max(noonRestStartTime, startTime);
                    }
                }
            }
        }

        return new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
    }

    //根据考勤分组校验
    public String checkOtDateType(Long empid, Integer dateType, Integer inv, Integer compensateType) {
        String result = "";
        Map<String, Object> params = new HashMap<>();
        params.put("empid", empid);
        params.put("dateType", dateType);
        List<Map> otDateTypeList = waCheckMapper.checkOtDateType(params);
        if (CollectionUtils.isEmpty(otDateTypeList)) {
//            result = "权限有限，请联系管理员!";
            result = messageResource.getMessage("L005717", new Object[]{}, new Locale(SessionHolder.getLang()));
        } else {
            // 校验加班最小时长 和 单位
            if (compensateType == null) {
                Float min_apply_num = (Float) otDateTypeList.get(0).get("min_apply_num");
                if (min_apply_num != null && inv < min_apply_num * 60) {
//                    result = "加班最小时长" + min_apply_num + "小时！";
                    result = messageResource.getMessage("L005718", new Object[]{min_apply_num}, new Locale(SessionHolder.getLang()));
                    return result;
                }
                Float min_unit = (Float) otDateTypeList.get(0).get("min_unit");
                if (min_unit != null && inv % (min_unit * 60) != 0) {
//                    result = "加班最小单位为" + min_unit + "小时！";
                    result = messageResource.getMessage("L005719", new Object[]{min_unit}, new Locale(SessionHolder.getLang()));
                    return result;
                }
            } else {
                for (Map row : otDateTypeList) {
                    if (row.get("compensate_type") != null && row.get("compensate_type").equals(compensateType)) {
                        Float min_apply_num = (Float) row.get("min_apply_num");
                        if (min_apply_num != null && inv < min_apply_num * 60) {
//                            result = "加班最小时长" + min_apply_num + "小时！";
                            result = messageResource.getMessage("L005720", new Object[]{min_apply_num}, new Locale(SessionHolder.getLang()));
                            return result;
                        }
                        Float min_unit = (Float) row.get("min_unit");
                        if (min_unit != null && inv % (min_unit * 60) != 0) {
//                            result = "加班最小单位为" + min_unit + "小时！";
                            result = messageResource.getMessage("L005719", new Object[]{min_unit}, new Locale(SessionHolder.getLang()));
                            return result;
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 校验加班时效性
     *
     * @param empid          员工ID
     * @param startTime
     * @param endTime
     * @param compensateType
     * @return
     */
    public Map validateOverTimePrescription(Long empid, long startTime, long endTime, Integer compensateType, Map<Long, WaWorktimeDetail> pbMap, String myFiles, Long jg, OvertimeApplyBean applyBean) {
        Map mapRtn = new HashMap();
        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
        WaWorktimeDetail startTimeWorkTimeDetail = pbMap.get(startDate);
        Integer startTimeDateType = startTimeWorkTimeDetail.getDateType();

        Map params = new HashMap();
        params.put("empid", empid);
        params.put("compensateType", compensateType);
        params.put("dateType", startTimeDateType);

        List<WaOvertimeType> overtimeTypeList = waOvertimeTypeMapper.getEmpOtTypeList(params);
        if (CollectionUtils.isNotEmpty(overtimeTypeList)) {
            WaOvertimeType overtimeType = overtimeTypeList.get(0);
            if (applyBean.getIsValidateTimeControl() != null && applyBean.getIsValidateTimeControl()) {
                //设置啦加班时效控制
                Integer timeControlType = applyBean.getTimeControlType();//-1 提前 1 延后
                Float controlTimeHour = applyBean.getControlTimeDuration();
                if (timeControlType != null && controlTimeHour != null) {
                    Long curDateTime = DateUtil.getCurrentTime(true);
                    Long lastApplyTime = 0L;
                    Float controlTimeSecond = controlTimeHour * 60 * 60;

                    //判断申请休假时间是否在假期申请有效时间范围内，不在有效范围内不允许申请
                    if (timeControlType.intValue() == -1) {
                        //假期时效性设置为提前
                        lastApplyTime = startTime - controlTimeSecond.intValue();
                    } else if (timeControlType.intValue() == 1) {
                        //假期时效性设置为延后
                        lastApplyTime = endTime + controlTimeSecond.intValue();
                    }
                    if (curDateTime > lastApplyTime) {
                        mapRtn.put("status", -1);
                        mapRtn.put("message", messageResource.getMessage("L005803", new Object[]{}, new Locale(SessionHolder.getLang())));
                        return mapRtn;
                    }
                }
                //加班上传附件校验
                if (overtimeType.getIsUploadFile() != null && overtimeType.getIsUploadFile() && StringUtils.isBlank(myFiles)) {
                    if (overtimeType.getMinFileCheckTime() == null) {
                        mapRtn.put("status", -1);
                        mapRtn.put("message", messageResource.getMessage("L005730", new Object[]{}, new Locale(SessionHolder.getLang())));
                        return mapRtn;
                    } else {
                        if (overtimeType.getMinFileCheckTime() * 60 <= jg.floatValue() / 60) {
                            mapRtn.put("status", -1);
                            mapRtn.put("message", messageResource.getMessage("L005730", new Object[]{}, new Locale(SessionHolder.getLang())));
                            return mapRtn;
                        }
                    }
                }
            }

        }
        mapRtn.put("status", 1);
        return mapRtn;
    }

    /**
     * 仅可申请一种加班类型/当天
     *
     * @param empid          员工ID
     * @param startTime
     * @param endTime
     * @param compensateType
     * @return
     */
    public String checkCompensateType(Long empid, Long startTime, Long endTime, Integer compensateType) {
        //4、仅可申请一种加班类型/当天
        //4.1 查询员工所在的考勤分组
        Map<String, Object> groupParams = new HashMap<>();
        groupParams.put("empid", empid);
        List<Map> listEmpWaGroup = waMapper.listEmpWaGroup(groupParams);
        if (CollectionUtils.isNotEmpty(listEmpWaGroup)) {
            Map groupMap = listEmpWaGroup.get(0);
            Boolean singleOvertimeType = (Boolean) groupMap.get("single_overtime_type");
            if (BooleanUtils.isTrue(singleOvertimeType)) {
                Long startDate = DateUtil.getTimesampByDateStr2(DateUtil.getDateStrByTimesamp(startTime));
                Long endDate = DateUtil.getTimesampByDateStr2(DateUtil.getDateStrByTimesamp(endTime));
                Map<String, Object> checkParams = new HashMap<>();
                checkParams.put("empid", empid);
                checkParams.put("startTime", startDate);
                checkParams.put("endTime", endDate + 86399);
                checkParams.put("compensateTypeIsNotEqualTo", compensateType);
                List<Map> compensateTypeMaplist = waCheckMapper.listEmpOvertimeCompensateTypes(checkParams);
                if (CollectionUtils.isNotEmpty(compensateTypeMaplist)) {
                    String startDateYmStr = DateUtil.getDateStrByTimesamp(startTime);
                    String endDateYmStr = DateUtil.getDateStrByTimesamp(endTime);
                    for (Map map : compensateTypeMaplist) {
                        String date = (String) map.get("date");
                        if (startDateYmStr.equals(date) || endDateYmStr.equals(date)) {
                            return messageResource.getMessage("L006826", new Object[]{}, new Locale(SessionHolder.getLang()));
                        }
                    }
                }
            }
        }
        return "";
    }

    public Map validatePreOverTimePrescription(Long empid, long startTime, long endTime, Integer compensateType,
                                               Map<Long, WaWorktimeDetail> pbMap, SessionBean sessionBean, String myFiles, Long jg) {
        Map mapRtn = new HashMap();
        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
        WaWorktimeDetail startTimeWorkTimeDetail = pbMap.get(startDate);
        Integer startTimeDateType = startTimeWorkTimeDetail.getDateType();

        Map params = new HashMap();
        params.put("empid", empid);
        params.put("compensateType", compensateType);
        params.put("dateType", startTimeDateType);

        List<WaOvertimeType> overtimeTypeList = waOvertimeTypeMapper.getEmpOtTypeList(params);
        if (CollectionUtils.isNotEmpty(overtimeTypeList)) {
            WaOvertimeType overtimeType = overtimeTypeList.get(0);
            if (overtimeType.getIsPreTimecontrol() != null && overtimeType.getIsPreTimecontrol()) {
                //设置啦加班时效控制
                Integer timeControlType = overtimeType.getPreControlType();//-1 提前 1 延后
                Float controlTimeHour = overtimeType.getPreControlDuration();

                if (timeControlType != null && controlTimeHour != null) {
                    Long curDateTime = DateUtil.getCurrentTime(true);
                    Long lastApplyTime = 0L;
                    Float controlTimeSecond = controlTimeHour * 60 * 60;

                    //判断申请休假时间是否在假期申请有效时间范围内，不在有效范围内不允许申请
                    if (timeControlType == -1) {
                        //假期时效性设置为提前
                        lastApplyTime = startTime - controlTimeSecond.intValue();
                    } else if (timeControlType == 1) {
                        //假期时效性设置为延后
                        lastApplyTime = startTime + controlTimeSecond.intValue();
                    }
                    if (curDateTime > lastApplyTime) {
                        mapRtn.put("status", -1);
                        mapRtn.put("message", messageResource.getMessage("L005803", new Object[]{}, new Locale(sessionBean.getLanguage())));
                        return mapRtn;
                    }
                }
                //加班上传附件校验
                if (overtimeType.getIsUploadFile() != null && overtimeType.getIsUploadFile() && StringUtils.isBlank(myFiles)) {
                    if (overtimeType.getMinFileCheckTime() == null) {
                        mapRtn.put("status", -1);
                        mapRtn.put("message", messageResource.getMessage("L005730", new Object[]{}, new Locale(sessionBean.getLanguage())));
                        return mapRtn;
                    } else {
                        if (overtimeType.getMinFileCheckTime() * 60 <= jg.floatValue() / 60) {
                            mapRtn.put("status", -1);
                            mapRtn.put("message", messageResource.getMessage("L005730", new Object[]{}, new Locale(sessionBean.getLanguage())));
                            return mapRtn;
                        }
                    }
                }
            }

        }
        mapRtn.put("status", 1);
        return mapRtn;
    }

    public void updatePreOtStatus(Integer preOtId, Integer status) {
        PreEmpOvertime overtime = new PreEmpOvertime();
        overtime.setPreOtId(preOtId);
        overtime.setUpdtime(DateUtil.getCurrentTime(true));
        overtime.setUseStatus(status);
        preEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
    }

    private String getTimeStr(float minNum, SessionBean sessionBean) {
        float h = minNum / 60;
        float m = minNum % 60;
        String s = "";
        if (h > 0) {
            Float h1 = new Float(h);
//            s = s + h1.intValue() + "点";
            s = messageResource.getMessage("L005699", new Object[]{s + h1.intValue()}, new Locale(sessionBean.getLanguage()));
        }
        if (m > 0) {
            Float m1 = new Float(m);
//            s = s + m1.intValue() + "分";
            s = messageResource.getMessage("L005700", new Object[]{s + m1.intValue()}, new Locale(sessionBean.getLanguage()));
        }
        return s;
    }

    public WaEmpOvertime getWaEmpOvertime(Integer otId) {

        return waEmpOvertimeMapper.selectByPrimaryKey(otId);
    }

    /**
     * 取得补偿方式
     *
     * @param empid 员工ID
     * @param start
     * @param end
     * @return
     * @throws Exception
     */
    public List getCompensateTypeList(Long empid, Long start, Long end, String lang, SessionBean sessionBean) throws Exception {
        start = DateUtil.getOnlyDate(new Date(start * 1000));
        end = DateUtil.getOnlyDate(new Date(end * 1000));

        List<Map> list = new ArrayList<>();
        Map dMap = new HashMap();
        dMap.put("empid", empid);
        dMap.put("workDate", start);

        //根据员工的考勤类型查询工作日历
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
        if (empInfo != null) {
            if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(empInfo.getBelongOrgId())) {//智能排班
                WaShiftDef startShift = remoteSmartWorkTimeService.getEmployeeShiftByDate(empInfo.getBelongOrgId(), empInfo.getEmpid(), start);
                if (startShift != null) {
                    list = waConfigMapper.getCompensateTypeList(empid, startShift.getDateType());
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (Map row : list) {
                            row.put("text", CompensateTypeEnum.getDesc((Integer) row.get("value"), lang));
                            row.put("description", row.get("description") == null ? "" : String.valueOf(row.get("description")));
                        }
                    }
                }
                if (!start.equals(end) && (end + 28800) % 86400 != 0) {
                    WaShiftDef endShift = remoteSmartWorkTimeService.getEmployeeShiftByDate(empInfo.getBelongOrgId(), empInfo.getEmpid(), start);
                    if (endShift != null) {
                        List<Map> list2 = waConfigMapper.getCompensateTypeList(empid, endShift.getDateType());
                        if (CollectionUtils.isEmpty(list2)) {
                            throw new Exception(messageResource.getMessage("L005722", new Object[]{}, new Locale(sessionBean.getLanguage())));
                        } else {
                            if (list2.size() != list.size()) {
                                throw new Exception(messageResource.getMessage("L005722", new Object[]{}, new Locale(sessionBean.getLanguage())));
                            } else {
                                for (int i = 0; i < list2.size(); i++) {
                                    if (!list2.get(i).get("value").equals(list.get(i).get("value"))) {
                                        throw new Exception(messageResource.getMessage("L005722", new Object[]{}, new Locale(sessionBean.getLanguage())));
                                    }
                                }
                            }
                        }
                    }
                }
            } else {//旧的获取班次逻辑
                if (empInfo.getTmType() != null) {
                    int tmType = empInfo.getTmType().intValue();
                    if (tmType == 0) {
                        tmType = 1;
                    }
                    dMap.put("tmType", tmType);
                }

                List<WaWorktimeDetail> workTimeDetailList = waWorktimeDetailMapper.getEmpWorktimeDetail(dMap);
                if (CollectionUtils.isNotEmpty(workTimeDetailList)) {
                    WaWorktimeDetail detail = workTimeDetailList.get(0);//取得当天排班
                    list = waConfigMapper.getCompensateTypeList(empid, detail.getDateType());
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (Map row : list) {
                            row.put("text", CompensateTypeEnum.getDesc((Integer) row.get("value"), lang));
                            row.put("description", row.get("description") == null ? "" : String.valueOf(row.get("description")));
                        }
                    }
                }

                if (!start.equals(end) && (end + 28800) % 86400 != 0) {
                    dMap.put("workDate", end);
                    workTimeDetailList = waWorktimeDetailMapper.getEmpWorktimeDetail(dMap);
                    if (CollectionUtils.isNotEmpty(workTimeDetailList)) {
                        WaWorktimeDetail detail = workTimeDetailList.get(0);//取得当天排班
                        List<Map> list2 = waConfigMapper.getCompensateTypeList(empid, detail.getDateType());
                        if (CollectionUtils.isEmpty(list2)) {
//                    throw new Exception("补偿类型不一致，请拆分加班单重新申请");
                            throw new Exception(messageResource.getMessage("L005722", new Object[]{}, new Locale(sessionBean.getLanguage())));
                        } else {
                            if (list2.size() != list.size()) {
//                        throw new Exception("补偿类型不一致，请拆分加班单重新申请");
                                throw new Exception(messageResource.getMessage("L005722", new Object[]{}, new Locale(sessionBean.getLanguage())));
                            } else {
                                for (int i = 0; i < list2.size(); i++) {
                                    if (!list2.get(i).get("value").equals(list.get(i).get("value"))) {
//                                throw new Exception("补偿类型不一致，请拆分加班单重新申请");
                                        throw new Exception(messageResource.getMessage("L005722", new Object[]{}, new Locale(sessionBean.getLanguage())));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return list;
    }


    /**
     * 申请哺乳假校验
     *
     * @param waLeaveType
     * @param empid       员工ID
     * @param belongOrgId 租户
     * @param startDate
     * @param sessionBean
     * @return
     * @throws Exception
     */
    public String validateBRJ(WaLeaveType waLeaveType, Long empid, Long corpId, String belongOrgId, Long startDate, SessionBean sessionBean) throws Exception {
        //哺乳假申请校验，如果当天有申请除哺乳假之外的假，哺乳假撤销或者不能申请
        if (waLeaveType.getLeaveType() == 10) {
            //哺乳假
            //查询当天有没有申请过非哺乳假，有的话，那么当天不能申请哺乳假
            Map params = new HashMap();
            params.put("notEqLeaveType", 10);
            params.put("empid", empid);
            params.put("startDate", startDate);
            List<Map> listEmpLeaveData = waEmpLeaveMapper.listEmpLeaveData(params);
            if (CollectionUtils.isNotEmpty(listEmpLeaveData)) {
                return messageResource.getMessage("L005863", new Object[]{}, new Locale(sessionBean.getLanguage()));
            }
        } else {
            //非哺乳假
            //查询当天有没有申请哺乳假，有的话撤销
            Map params = new HashMap();
            params.put("eqLeaveType", 10);
            params.put("empid", empid);
            params.put("startDate", startDate);
            List<Map> listEmpLeaveData = waEmpLeaveMapper.listEmpLeaveData(params);
            if (CollectionUtils.isNotEmpty(listEmpLeaveData)) {
               /* for (Map leaveMap : listEmpLeaveData) {
                    //撤销假期
                    waLeaveService.revokeEmpLeave(Integer.parseInt(leaveMap.get("leave_id").toString()), corpId, belongOrgId,
                            messageResource.getMessage("L005864", new Object[]{}, new Locale(sessionBean.getLanguage())), empid, SessionHolder.getUserId(), null,null,sessionBean.getLanguage());
                }*/
            }
        }
        return "";
    }

    public Float getAdjustWorkTime(Integer startTime, Integer endTime, WaShiftDef shiftDef) {
        //是否调整半天工时，该逻辑只适用于申请的假时间单位是小时
        if (shiftDef != null && shiftDef.getIsAdjustWorkHour() != null && shiftDef.getIsAdjustWorkHour()) {
            //启动调整半天工时逻辑
            PGobject pGobject = convertToGobject(shiftDef.getAdjustWorkHourJson());
            if (pGobject != null) {
                Map<String, Object> workHourMap = JSONUtils.convertPGobjectToMap(pGobject);
                Integer amStartMin = (Integer) workHourMap.get("amStartTime");
                Integer amEndMin = (Integer) workHourMap.get("amEndTime");
                Integer pmStartMin = (Integer) workHourMap.get("pmStartTime");
                Integer pmEndMin = (Integer) workHourMap.get("pmEndTime");

                if (amStartMin.equals(startTime) && amEndMin.equals(endTime)) {
                    //上午
                    return Float.parseFloat(String.valueOf(workHourMap.get("amWorkHour"))) * 60;
                } else if (pmStartMin.equals(startTime) && pmEndMin.equals(endTime)) {
                    //下午
                    return Float.parseFloat(String.valueOf(workHourMap.get("pmWorkHour"))) * 60;
                }
            }
        }
        return null;
    }

    /**
     * 如果开启了门店考勤员工法定假日使用默认工作日历，则返回默认工作日历数据
     *
     * @param belongOrgId 租户
     * @return
     */
    public Map<Long, Integer> getDefaultWorkCalendarByTmType(String belongOrgId, Integer empTmType) {
        String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongOrgId + RedisKeyDefine.IS_OPEN_STOREEMP_VACATION_USE_DEFAULTCALENDAR);
        if (isOpen != null && "1".equals(isOpen) && Integer.valueOf(2).equals(empTmType)) {
            return getDefaultWorkCalendar(belongOrgId);
        }
        return new HashMap<>();
    }

    public Map<Long, Integer> getDefaultWorkCalendar(String belongOrgId) {
        Map<Long, Integer> map = new HashMap<>();//日期类型

        WaWorktimeExample example = new WaWorktimeExample();
        example.createCriteria().andBelongOrgidEqualTo(belongOrgId).andIsDefaultEqualTo(true);
        List<WaWorktime> worktimeList = waWorktimeMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(worktimeList)) {
            WaWorktime worktime = worktimeList.get(0);
            WaWorktimeDetailExample detailExample = new WaWorktimeDetailExample();
            detailExample.createCriteria().andWorkCalendarIdEqualTo(worktime.getWorkCalendarId());
            List<WaWorktimeDetail> detailList = waWorktimeDetailMapper.selectByExample(detailExample);
            if (CollectionUtils.isNotEmpty(detailList)) {
                detailList.forEach(row -> map.put(row.getWorkDate(), row.getDateType()));
            }
        }
        return map;
    }

    /**
     * 计算请假结束时间-产假 是适用于产假且单位为天，最小单位为一天
     *
     * @param leaveTypeId
     * @param leaveDay
     * @param startTime
     * @return
     */
    public Long getLeaveEndTime(String belongid, Long empid, Integer leaveTypeId, Integer leaveDay, Long startTime, String lang) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
        if (tmType == 0) {
            tmType = 1;
        } else if (tmType == 2) {//门店考勤人员
            Jedis jedis = RedisService.getResource();
            String appStoreEnable = jedis.get(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongid + "_APP_STORE_Enable");
            try {
                if (appStoreEnable == null || (!appStoreEnable.equals("1"))) {//不支持门店考勤人员请假
                    throw new CDException(messageResource.getMessage("L005723", new Object[]{}, new Locale(lang)));
                }
            } catch (RuntimeException e) {
                jedis.close();
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
        Long start = startTime;
        String errorMsg = "";
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveTypeId);
        if (waLeaveType != null) {
            Long end = DateUtils.addDays(new Date(start * 1000), leaveDay).getTime() / 1000;
            Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(belongid, empid, tmType, start, end, empInfo.getWorktimeType(), true);
            if (MapUtils.isEmpty(pbMap)) {
                errorMsg = messageResource.getMessage("L005739", new Object[]{DateUtil.getDateStrByTimesamp(startTime)}, new Locale(lang));
            } else {
                //CLOUD-8200 & 8414 查询公司默认工作日历
                Map<Long, Integer> defaultWorkCalendar = getDefaultWorkCalendarByTmType(belongid, tmType);

                WaWorktimeDetail startDateDetail;
                int day = leaveDay;
                boolean flag = true;
                while (flag) {
                    //查询员工排班
                    startDateDetail = pbMap.get(start);//取得当天排班
                    if (startDateDetail == null) {
                        Long end1 = DateUtils.addDays(new Date(end * 1000), 30).getTime() / 1000;
                        Map<Long, WaWorktimeDetail> pbMap1 = waCommonService.getEmpWaWorktimeDetail(belongid, empid, tmType, end, end1, empInfo.getWorktimeType(), true);
                        if (MapUtils.isNotEmpty(pbMap1)) {
                            pbMap.putAll(pbMap1);
                            startDateDetail = pbMap.get(start);//取得当天排班
                        }
                    }

                    if (startDateDetail == null) {
                        errorMsg = messageResource.getMessage("L005739", new Object[]{DateUtil.getDateStrByTimesamp(start)}, new Locale(lang));
                        flag = false;
                    } else {
                        Integer dateType = startDateDetail.getDateType();

                        if (Integer.valueOf(3).equals(defaultWorkCalendar.get(start))) {
                            dateType = defaultWorkCalendar.get(start);
                        }

                        if ((Integer.valueOf(2).equals(dateType) && BooleanUtils.isFalse(waLeaveType.getIsRestDay()))//日期类型为休息日，且休息日不连续计算
                                || (Integer.valueOf(3).equals(dateType) && BooleanUtils.isFalse(waLeaveType.getIsLegalHoliday()))) {//日期类型为法定假日，且法定假日不连续计算
                        } else {
                            day--;
                        }
                        if (day <= 0) {
                            flag = false;
                        } else {
                            start = start + 24 * 60 * 60;
                        }
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new CDException(errorMsg);
        }
        return start;
    }

    /**
     * 请假时长折算
     *
     * @param period
     * @param dayTime
     * @param waLeaveType
     * @param detailShift
     */
    public void convertLtTime(Integer period, WaLeaveDaytime dayTime, WaLeaveType waLeaveType, WaShiftDef detailShift) {
        //判断假期是否开启了工时折算 CLOUD-8257
        if (waLeaveType.getAcctTimeType() == 1 && BooleanUtils.isTrue(waLeaveType.getIsWorkhourConvert()) && waLeaveType.getConvertDuration() != null
                && waLeaveType.getConvertDuration() > 0) {
            Integer convertDuration = waLeaveType.getConvertDuration();
            if (period == 1 || period == 9) {
                if (dayTime.getTimeDuration() == 1) {//CLOUD-8257 请假时长按天折算
                    if (detailShift.getDateType() == 1 && detailShift.getWorkTotalTime() != null
                            && !convertDuration.equals(detailShift.getWorkTotalTime())) {
                        Integer workTime = detailShift.getWorkTotalTime();
                        BigDecimal a = new BigDecimal(workTime);
                        BigDecimal b = new BigDecimal(convertDuration);
                        Float convertDay = a.divide(b, 2, RoundingMode.DOWN).floatValue();
                        dayTime.setTimeDuration(convertDay);//实际申请时长，记录折算之后的时长
                        dayTime.setApplyTimeDuration(1f);//记录原始的申请时长
                    }
                }
            } else if (period == 3) {
                if (waLeaveType.getAcctTimeType() == 1) { //CLOUD-8450 工时折算，按照小时去申请单位为天的假期
                    BigDecimal a = BigDecimal.valueOf(dayTime.getTimeDuration());
                    BigDecimal b = new BigDecimal(convertDuration);
                    Float convertDay = a.divide(b, 2, RoundingMode.DOWN).floatValue();
                    dayTime.setTimeDuration(convertDay);
                    dayTime.setPeriodType((short) 9);
                    dayTime.setTimeUnit((short) 1);
                }
            }
        }
    }

    private Integer getHalfdayTime(WaShiftDef shiftDef, HalfDayTypeEnum halfday) {
        // 是否定义半天时间
        boolean isHalfdayDef = shiftDef.getIsHalfdayTime() != null
                && shiftDef.getIsHalfdayTime() && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() > 0;
        if (isHalfdayDef) {
            return shiftDef.getHalfdayTime();
        }

        // 是否定义午休时间
        boolean isNoonDef = shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()
                && null != shiftDef.getNoonRestStart() && null != shiftDef.getNoonRestEnd();
        if (isNoonDef) {
            return HalfDayTypeEnum.A == halfday ? shiftDef.getNoonRestStart() : shiftDef.getNoonRestEnd();
        }

        return null;
    }

    /**
     * 计算每日休假的实际休假时间段
     *
     * @param leaveDaytime
     * @param shiftDef
     * @return
     */
    public WaLeaveDaytimeExtDto calLeaveDayRealTimeSlot(WaLeaveDaytimeExtPo leaveDaytime, WaShiftDef shiftDef) {
        WaLeaveDaytimeExtDto leaveDaytimeExtDto = new WaLeaveDaytimeExtDto();
        leaveDaytimeExtDto.setCreateTime(leaveDaytime.getCreateTime());
        Integer periodType = Integer.valueOf(leaveDaytime.getPeriodType());
        // 跨夜
        boolean isKy = CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType());
        long leaveDate = leaveDaytime.getLeaveDate();
        long leaveTomorrowDate = DateUtil.addDate(leaveDaytime.getLeaveDate() * 1000, 1);
        if (WaLeavePeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType)) {
            // 整天
            leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (shiftDef.getStartTime() * 60));
            long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
            leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftDef.getEndTime() * 60));
        } else if (WaLeavePeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
            // 半天
            if ("A".equals(leaveDaytime.getShalfDay()) && "P".equals(leaveDaytime.getEhalfDay())) {
                // 一整天
                leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (shiftDef.getStartTime() * 60));
                long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
                leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftDef.getEndTime() * 60));
            } else if ("A".equals(leaveDaytime.getShalfDay()) && "A".equals(leaveDaytime.getEhalfDay())) {
                // 上半天
                leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (shiftDef.getStartTime() * 60));
                // 计算半天定义时间点
                Integer halfDayTime = getHalfdayTime(shiftDef, HalfDayTypeEnum.A);
                if (null != halfDayTime) {
                    boolean leaveKy = shiftDef.getStartTime() > halfDayTime;
                    long leaveEndDate = leaveKy ? leaveTomorrowDate : leaveDate;
                    leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (halfDayTime * 60));
                } else {
                    long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
                    leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftDef.getEndTime() * 60));
                }
            } else if ("P".equals(leaveDaytime.getShalfDay()) && "P".equals(leaveDaytime.getEhalfDay())) {
                // 下半天
                // 计算半天定义时间点
                Integer halfDayTime = getHalfdayTime(shiftDef, HalfDayTypeEnum.P);
                if (null != halfDayTime) {
                    boolean leaveKy = shiftDef.getStartTime() > halfDayTime;
                    long leaveStartDate = leaveKy ? leaveTomorrowDate : leaveDate;
                    leaveDaytimeExtDto.setLeaveStartTime(leaveStartDate + (halfDayTime * 60));

                    long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
                    leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftDef.getEndTime() * 60));
                } else {
                    leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (shiftDef.getStartTime() * 60));
                    long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
                    leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftDef.getEndTime() * 60));
                }
            }
        } else if (WaLeavePeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
            // 小时整天
            if (null != leaveDaytime.getStartTime() && null != leaveDaytime.getEndTime()) {
                leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (leaveDaytime.getStartTime() * 60));
                boolean leaveKy = leaveDaytime.getStartTime() > leaveDaytime.getEndTime();
                long leaveEndDate = leaveKy ? leaveTomorrowDate : leaveDate;
                leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (leaveDaytime.getEndTime() * 60));
            } else {
                leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (shiftDef.getStartTime() * 60));
                long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
                leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftDef.getEndTime() * 60));
            }
        } else if (WaLeavePeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(periodType)) {
            // 小时
            leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (leaveDaytime.getStartTime() * 60));
            boolean leaveKy = leaveDaytime.getStartTime() > leaveDaytime.getEndTime();
            long leaveEndDate = leaveKy ? leaveTomorrowDate : leaveDate;
            leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (leaveDaytime.getEndTime() * 60));
        }
        return leaveDaytimeExtDto;
    }

    public boolean checkTimeRangeOverlap(long start, long end, long start1, long end1) {
        if (end <= start1 || start >= end1) {
            return false;
        }
        return true;
    }

    /**
     * 每日休假时间重叠校验
     *
     * @param empId       员工ID
     * @param apply
     * @param shiftDef
     * @param waLeaveType
     * @return
     */
    public boolean checkLeaveDayTimeOverlap(Long empId, WaLeaveDaytime apply, WaShiftDef shiftDef, WaLeaveType waLeaveType) {
        // 1、先查询申请日期当天的休假数据，没有：允许申请，有：继续校验
        long startTime = apply.getLeaveDate();
        long endTime = startTime + 86399;
        WaLeaveTypeDef waLeaveTypeDef = waLeaveTypeDefMapper.selectByPrimaryKey(waLeaveType.getLeaveType());
        boolean isBrj = waLeaveTypeDef != null && "BRJ".equals(waLeaveTypeDef.getLeaveTypeDefCode()); // 哺乳假
        List<WaLeaveDaytimeExtPo> leaveDayTimes = waMapper.selectLeaveDaytimeList(empId, startTime, endTime, isBrj, null);
        if (CollectionUtils.isEmpty(leaveDayTimes)) {
            return false;
        }
        // 计算待申请单的实际休假开始、结束时间
        WaLeaveDaytimeExtPo waLeaveDaytimeExtPo = ObjectConverter.convert(apply, WaLeaveDaytimeExtPo.class);
        WaLeaveDaytimeExtDto applyExtDto = calLeaveDayRealTimeSlot(waLeaveDaytimeExtPo, shiftDef);
        // 计算已经申请的休假单的实际休假开始、结束时间
        List<WaLeaveDaytimeExtDto> ltExtDtoList = leaveDayTimes.stream()
                .map(ltDay -> calLeaveDayRealTimeSlot(ltDay, shiftDef)).collect(Collectors.toList());
        // 2、当天有休假单，检查休假单的时间是否和申请单的时间重叠，如不重叠：允许申请，重叠：继续校验
        List<WaLeaveDaytimeExtDto> overlapLeaveList = Lists.newArrayList();// 和当前申请单有交集的休假数据
        for (WaLeaveDaytimeExtDto ltExtDto : ltExtDtoList) {
            // 判断申请单是否和休假单的时间重叠
            boolean timeOverlap = checkTimeRangeOverlap(applyExtDto.getLeaveStartTime(), applyExtDto.getLeaveEndTime(),
                    ltExtDto.getLeaveStartTime(), ltExtDto.getLeaveEndTime());
            if (timeOverlap) {
                // 重叠
                overlapLeaveList.add(ltExtDto);
            }
        }
        if (CollectionUtils.isEmpty(overlapLeaveList)) {
            return false;
        }
        // 3、查询当天的销假数据，如：没有：不允许申请，如有：继续校验
        List<Integer> leaveIds = leaveDayTimes.stream().map(WaLeaveDaytimeExtPo::getLeaveId).collect(Collectors.toList());
        List<WaLeaveCancelDayTime> leaveCancelDayTimes = waMapper.selectLeaveCancelDaytimeList(empId, startTime, endTime, leaveIds);
        if (CollectionUtils.isEmpty(leaveCancelDayTimes)) {
            return true;
        }
        // 计算销假单实际的销假开始、结束时间
        List<WaLeaveDaytimeExtDto> leaveCancelDaytimeExtDtos = leaveCancelDayTimes.stream().map(leaveCancelDayTime -> {
            WaLeaveDaytimeExtPo waLeaveCancelDayTime = ObjectConverter.convert(leaveCancelDayTime, WaLeaveDaytimeExtPo.class);
            waLeaveCancelDayTime.setLeaveDate(leaveCancelDayTime.getLeaveCancelDate());
            WaLeaveDaytimeExtDto extDto = calLeaveDayRealTimeSlot(waLeaveCancelDayTime, shiftDef);
            extDto.setXj(true);
            return extDto;
        }).collect(Collectors.toList());
        List<WaLeaveDaytimeExtDto> allDaytimeExtDtoList = new ArrayList<>();
        allDaytimeExtDtoList.addAll(ltExtDtoList);
        allDaytimeExtDtoList.addAll(leaveCancelDaytimeExtDtos);
        allDaytimeExtDtoList.sort(Comparator.comparing(WaLeaveDaytimeExtDto::getCreateTime));
        // 根据休假单、销假单、待申请单 计算待申请单是否存在时间重叠，是否可请
        List<TimeRangeCheckUtil.ChangeData> leaveTimeRangeList = allDaytimeExtDtoList.stream()
                .map(lt -> TimeRangeCheckUtil.ChangeData.from(!lt.isXj(), lt.getLeaveStartTime(), lt.getLeaveEndTime() - 1))
                .collect(Collectors.toList());
        boolean ask = TimeRangeCheckUtil.ask(applyExtDto.getLeaveStartTime(), applyExtDto.getLeaveEndTime() - 1, leaveTimeRangeList);
        return !ask;
    }

    /**
     * 计算每日休假明细
     *
     * @param shiftMap
     * @param pbMap
     * @param map
     * @param startDate
     * @param endDate
     * @param startTimeStr
     * @param endTimeStr
     * @param period
     * @param waLeaveType
     * @param empInfo
     * @param sessionBean
     * @return
     * @throws Exception
     */
    public WaLeaveDaytime doBuildLeaveDaytime(Map<Integer, WaShiftDef> shiftMap, Map<Long, WaWorktimeDetail> pbMap,
                                              Map<String, Object> map, long startDate,
                                              long endDate, String startTimeStr, String endTimeStr,
                                              Integer period, WaLeaveType waLeaveType,
                                              SysEmpInfo empInfo, SessionBean sessionBean) throws Exception {
        WaWorktimeDetail startDateDetail = pbMap.get(startDate);//取得当天排班
        WaShiftDef startDateShift = shiftMap.get(startDateDetail.getShiftDefId());
        WaLeaveDaytime dayTime = new WaLeaveDaytime();
        dayTime.setLeaveDate(startDate);
        dayTime.setPeriodType(period.shortValue());
        if (period == 1 || period == 9) {//天
            dayTime.setTimeUnit((short) 1);
        } else if (period == 4 || period == 3) {//小时
            dayTime.setTimeUnit((short) 2);
        }
        dayTime.setDateType(startDateDetail.getDateType());
        boolean isCheckDateType = true;
        if (startDateDetail.getDateType() != 1 && period == 3) {//非工作日并且休的是小时假
            Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
            WaWorktimeDetail preEndDateWorkTimeDetail = pbMap.get(preEndDate);
            if (preEndDateWorkTimeDetail != null && preEndDateWorkTimeDetail.getDateType() == 1
                    && CdWaShiftUtil.checkCrossNight(preEndDateWorkTimeDetail.getStartTime(), preEndDateWorkTimeDetail.getEndTime(), preEndDateWorkTimeDetail.getDateType())) {//班次是跨夜班
                isCheckDateType = false;
            }
        }
        if (isCheckDateType && startDateDetail.getDateType() == 2 && (!waLeaveType.getIsRestDay())) {//日期类型为休息日，且休息日不连续计算
            dayTime.setTimeDuration(0f);
        } else if (isCheckDateType && (startDateDetail.getDateType() == 3 || startDateDetail.getDateType() == 5) && (!waLeaveType.getIsLegalHoliday())) {//日期类型为法定假日，且法定假日不连续计算
            dayTime.setTimeDuration(0f);
        } else if (isCheckDateType && startDateDetail.getDateType() == 4 && (!waLeaveType.getIsRestDay())) {//日期类型为特殊休日，且休息日不连续计算
            dayTime.setTimeDuration(0f);
        } else {
            if (period == 1) {// 时间单位为天的整天
                dayTime.setTimeDuration(1f);
            } else if (period == 3) {//3时间单位为小时的非整天。
                this.calLeaveTimeByPeriod3(waLeaveType, startDate, endDate, startTimeStr, endTimeStr, pbMap, dayTime, shiftMap);
                if (BooleanUtils.isTrue(waLeaveType.getIsAdjustWorkHour()) && shiftMap.get(startDateDetail.getShiftDefId()) != null) { //工时调整
                    Float timeDuration = this.getAdjustWorkTime(dayTime.getStartTime(), dayTime.getEndTime(), shiftMap.get(startDateDetail.getShiftDefId()));
                    if (timeDuration != null) {
                        dayTime.setBeforeAdjustTimeDuration(dayTime.getTimeDuration());//调整之前的数据
                        dayTime.setTimeDuration(timeDuration);//调整之后的数据
                    }
                }
            } else if (period == 4) {//时间单位为小时的整天
                //哺乳假
                WaLeaveTypeDef waLeaveTypeDef = waLeaveTypeDefMapper.selectByPrimaryKey(waLeaveType.getLeaveType());
                if ("BRJ".equals(waLeaveTypeDef.getLeaveTypeDefCode())) {
                    boolean isCount = true;
                    Map pram = new HashMap();
                    pram.put("start", startDate);
                    pram.put("end", startDate + 86399);
                    pram.put("empid", sessionBean.getEmpid());
                    List<Map> leaveRepeat = waCheckMapper.getLeaveRepeat(pram);
                    Float sumDuration = 0f;
                    for (Map leaveMap : leaveRepeat) {
                        Integer periodType = (Integer) leaveMap.get("period_type");
                        Float duration = (Float) leaveMap.get("time_duration");
                        Float cancelTimeDuration = (Float) leaveMap.get("cancelTimeDuration");
                        if (duration.equals(cancelTimeDuration)) {
                            continue;
                        }
                        //整天
                        if (periodType == 1 || periodType == 4) {
                            isCount = false;
                            break;
                        } else {
                            //半天
                            if (periodType == 9) {
                                if (duration < 1) {
                                    Integer workTotalTime = startDateDetail.getWorkTotalTime();
                                    int halfWorkTime = workTotalTime / 2; // 半天的时长等于班次上工作时长的二分之一
                                    String shalfDay = (String) leaveMap.get("shalf_day");
                                    String ehalfDay = (String) leaveMap.get("ehalf_day");
                                    //上半天时长
                                    if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(shalfDay) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(ehalfDay)) {
                                        halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, startDateShift, halfWorkTime);
                                        duration = Float.valueOf(halfWorkTime);
                                    }
                                    //下半天时长
                                    if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(shalfDay) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(ehalfDay)) {
                                        halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, startDateShift, halfWorkTime);
                                        duration = Float.valueOf(halfWorkTime);
                                    }
                                } else {
                                    isCount = false;
                                    break;
                                }
                            }
                            sumDuration = sumDuration + duration;
                        }
                    }
                    //如是休息日班次或者整天申请其他假已审批通过（请假时长=班次时长），那么这天请假时长=0 CAIDAOM-528
                    Boolean isSpecial = false;//特殊班次工时调整
                    if (startDateShift.getIsSpecial() != null && startDateShift.getIsSpecial() && startDateShift.getSpecialWorkTime() != null) {
                        isSpecial = true;
                    }
                    Float workTotalTime = Float.valueOf(startDateDetail.getWorkTotalTime());
                    if (startDateShift.getDateType() == 1) {
                        if (isSpecial) {
                            workTotalTime = startDateShift.getSpecialWorkTime().floatValue();
                        }
                    } else if (startDateShift.getDateType() == 2 && waLeaveType.getIsRestDay()) {//休息日连续计算
                        if (isSpecial) {
                            workTotalTime = startDateShift.getSpecialWorkTime().floatValue();
                        } else {
                            workTotalTime = 8 * 60f;
                        }
                    } else if ((startDateShift.getDateType() == 3 || startDateShift.getDateType() == 5) && waLeaveType.getIsLegalHoliday()) {//法定假日连续计算
                        if (isSpecial) {
                            workTotalTime = startDateShift.getSpecialWorkTime().floatValue();
                        } else {
                            workTotalTime = 8 * 60f;
                        }
                    } else if (startDateShift.getDateType() == 4 && waLeaveType.getIsRestDay()) {
                        if (isSpecial) {
                            workTotalTime = startDateShift.getSpecialWorkTime().floatValue();
                        } else {
                            workTotalTime = 8 * 60f;
                        }

                    }
                    if (sumDuration >= workTotalTime) {
                        isCount = false;
                    }
                    if (isCount) {
                        Double dailyDuration = (Double) map.get("dailyDuration") == null ? 0f : (Double) map.get("dailyDuration");
                        dailyDuration = dailyDuration * 60;
                        if (dailyDuration > workTotalTime) {
                            dayTime.setTimeDuration(workTotalTime);
                        } else {
                            dayTime.setTimeDuration(dailyDuration.floatValue());
                        }
                    } else {
                        dayTime.setTimeDuration(0f);
                    }
                } else {
                    //要看具体工作时间
                    this.processLeaveByPeriod4(waLeaveType, startDateDetail, dayTime, startDateShift);
                }
            } else if (period == 9) {//上半天，下半天，时间单位为天的非整天
                this.processLeaveByPeriod9(waLeaveType, startDateDetail, dayTime, map.get("shalfday").toString(), map.get("ehalfday").toString(), startDate, DateUtil.getTimesampByDateStr2(startTimeStr), DateUtil.getTimesampByDateStr2(endTimeStr));
            }
            //请假时长折算逻辑
            convertLtTime(period, dayTime, waLeaveType, startDateShift);
        }
        return dayTime;
    }

    /**
     * 请假配额校验或者扣减
     *
     * @param waLeaveType
     * @param allDaytimeList
     * @param empInfo
     * @param userId
     * @param onlyCheck
     * @param approvalStatus
     * @return
     */
    public String checkOrDecLeaveQuota(WaLeaveType waLeaveType, List<WaLeaveDaytime> allDaytimeList, SysEmpInfo empInfo,
                                       Long userId, boolean onlyCheck, Integer approvalStatus, String homeLeaveType, String marriageStatus) {
        if (CollectionUtils.isEmpty(allDaytimeList) || waLeaveType == null) {
            return "";
        }
        // 额度限制类型
        if (!QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType())) {
            return "";
        }
        // 额度类型
        Integer quotaType = waLeaveType.getQuotaType();
        if (quotaType == null) {
            if (waLeaveType.getLeaveType() == 3) {
                //假期类型为调休
                quotaType = QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex();
            } else {
                quotaType = QuotaTypeEnum.ISSUED_ANNUALLY.getIndex();
            }
        }
        if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(quotaType)) {
            return checkEmpCompensatoryQuota(userId, allDaytimeList, empInfo.getEmpid(), waLeaveType, onlyCheck, approvalStatus, false);
        } else {
            return checkEmpQuota(userId, allDaytimeList, empInfo.getEmpid(), waLeaveType, onlyCheck, approvalStatus, false, homeLeaveType, marriageStatus);
        }
    }

    public String checkEmpCompensatoryQuota(Long userId, List<WaLeaveDaytime> allDaytimeList, Long empid,
                                            WaLeaveType waLeaveType, boolean onlyCheck, Integer approvalStatus, Boolean isNegative) {
        if (CollectionUtils.isNotEmpty(allDaytimeList)) {
            allDaytimeList = allDaytimeList.stream().filter(o -> o.getTimeDuration() > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allDaytimeList)) {
                return "";
            }
            List<Long> tsList = allDaytimeList.stream().map(WaLeaveDaytime::getLeaveDate).sorted(Long::compareTo).collect(Collectors.toList());
            Long minDate = tsList.get(0);
            Long maxDate = tsList.get(tsList.size() - 1);
            List<UsableCompensatoryQuotaDto> usableCompensatoryQuotaDtos = waEmpCompensatoryQuotaMapper.selectUsableCompensatoryQuotaList(empid, waLeaveType.getLeaveTypeId(), minDate, maxDate);
            if (CollectionUtils.isEmpty(usableCompensatoryQuotaDtos)) {
                //return "没有调休配额可使用";
                return ResponseWrap.wrapResult(AttendanceCodes.COMPENSATORY_QUOTA_NO_LEFT, null).getMsg();
            }
            Float duration;
            Float usableDay;
            Float usedDay;
            Long leaveDate;
            for (WaLeaveDaytime waLeaveDaytime : allDaytimeList) {
                leaveDate = waLeaveDaytime.getLeaveDate();
                duration = waLeaveDaytime.getTimeDuration();
                for (UsableCompensatoryQuotaDto usableCompensatoryQuotaDto : usableCompensatoryQuotaDtos) {
                    if (usableCompensatoryQuotaDto.getStartDate() <= leaveDate && usableCompensatoryQuotaDto.getLastDate() >= leaveDate) {
                        usableDay = usableCompensatoryQuotaDto.getUsableDay();
                        if (usableDay <= 0) {
                            continue;
                        }
                        usedDay = usableCompensatoryQuotaDto.getUsedDay();
                        if (duration > usableDay) {
                            duration = duration - usableDay;
                            usedDay += usableDay;
                            usableCompensatoryQuotaDto.setUsableDay(0F);
                            usableCompensatoryQuotaDto.setUsedDay(usedDay);
                            saveCompensatoryDetail(onlyCheck, userId, waLeaveDaytime, usableDay, usableCompensatoryQuotaDto, approvalStatus);
                        } else {
                            usableCompensatoryQuotaDto.setUsableDay(usableDay - duration);
                            usedDay += duration;
                            usableCompensatoryQuotaDto.setUsedDay(usedDay);
                            saveCompensatoryDetail(onlyCheck, userId, waLeaveDaytime, duration, usableCompensatoryQuotaDto, approvalStatus);
                            duration = 0F;
                            break;
                        }
                    }
                }
                if (duration > 0) {
                    if (BooleanUtils.isTrue(isNegative)) {
                        if (!onlyCheck) {
                            UsableCompensatoryQuotaDto empQuoDto = usableCompensatoryQuotaDtos.get(usableCompensatoryQuotaDtos.size() - 1);
                            empQuoDto.setUsableDay(0F);
                            empQuoDto.setUsedDay(empQuoDto.getUsedDay() + duration);
                            saveCompensatoryDetail(onlyCheck, userId, waLeaveDaytime, duration, empQuoDto, approvalStatus);
                        }
                    } else {
                        //return DateUtil.getDateStrByTimesamp(leaveDate) + "的调休配额不足";
                        return String.format(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATORY_QUOTA_NOT_ENOUGH, null).getMsg(), DateUtil.getDateStrByTimesamp(leaveDate));
                    }
                }
            }
        }
        return "";
    }

    /**
     * 非调休配额扣减
     *
     * @param userId
     * @param allDaytimeList
     * @param empid          员工ID
     * @param waLeaveType
     * @param onlyCheck
     * @return
     */

    public String checkEmpQuota(Long userId, List<WaLeaveDaytime> allDaytimeList, Long empid, WaLeaveType waLeaveType,
                                boolean onlyCheck, Integer approvalStatus, Boolean isNegative) {
        return checkEmpQuota(userId, allDaytimeList, empid, waLeaveType, onlyCheck, approvalStatus, isNegative, null, null);
    }

    /**
     * 非调休配额额度使用检查
     *
     * @param userId
     * @param allDaytimeList
     * @param empid
     * @param waLeaveType
     * @param onlyCheck
     * @param approvalStatus
     * @param isNegative
     * @param homeLeaveType  探亲假
     * @param marriageStatus
     * @return
     */
    public String checkEmpQuota(Long userId, List<WaLeaveDaytime> allDaytimeList, Long empid, WaLeaveType waLeaveType,
                                boolean onlyCheck, Integer approvalStatus, Boolean isNegative, String homeLeaveType, String marriageStatus) {
        if (CollectionUtils.isNotEmpty(allDaytimeList)) {
            allDaytimeList = allDaytimeList.stream().filter(o -> o.getTimeDuration() > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allDaytimeList)) {
                return "";
            }
            List<Long> dateList = allDaytimeList.stream().map(WaLeaveDaytime::getLeaveDate).sorted(Long::compareTo).collect(Collectors.toList());
            Long minDate = dateList.get(0);
            Long maxDate = dateList.get(dateList.size() - 1);

            // 查询可用配额
            List<EmpQuoDto> empQuoDtoList = waMapper.selectUsableEmpQuotaList(empid, waLeaveType.getLeaveTypeId(), minDate, maxDate)
                    .stream().filter(quota -> !QuotaTypeEnum.FIXED_QUOTA.getIndex().equals(waLeaveType.getQuotaType())
                            || (Optional.ofNullable(quota.getUsedDay()).orElse(0f) <= 0
                            && Optional.ofNullable(quota.getInTransitQuota()).orElse(0f) <= 0))
                    .filter(it -> StringUtils.isEmpty(homeLeaveType) || it.getUsedDay() <= 0)
                    .collect(Collectors.toList());

            // 探亲假
            if (StringUtils.isNotEmpty(homeLeaveType) && CollectionUtils.isNotEmpty(empQuoDtoList)) {
                List<Integer> quotaIds = empQuoDtoList.stream().map(EmpQuoDto::getEmpQuotaId).collect(Collectors.toList());
                List<HomeLeaveType> types = null;
                if ("visiting_parents".equals(homeLeaveType)) {
                    if ("1".equals(marriageStatus)) {
                        types = Lists.newArrayList(
                                HomeLeaveType.MARRIED_VISIT_PARENTS,
                                HomeLeaveType.MARRIED_VISIT_BOTH,
                                HomeLeaveType.MARRIED_OR_NOT_VISIT_PARENTS
                        );
                    } else {
                        types = Lists.newArrayList(
                                HomeLeaveType.NOT_MARRIED_VISIT_PARENTS,
                                HomeLeaveType.MARRIED_OR_NOT_VISIT_PARENTS
                        );
                    }
                } else {
                    types = Lists.newArrayList(
                            HomeLeaveType.MARRIED_VISIT_BOTH,
                            HomeLeaveType.MARRIED_VISIT_SPOUSE
                    );
                }
                List<Integer> filterQuotaIds = quotaMapper.filterByHomeLeaveType(quotaIds, types);
                empQuoDtoList = empQuoDtoList.stream().filter(it ->
                        filterQuotaIds.contains(it.getEmpQuotaId())
                ).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(empQuoDtoList)) {
                    val filterQuotaIds2 = quotaMapper.filterNotInTransit(filterQuotaIds);
                    if(filterQuotaIds2.isEmpty()){
                        //return "已提交相同假期类型的申请";
                        return ResponseWrap.wrapResult(AttendanceCodes.SAME_LEAVE_APPLY_SUBMIT, null).getMsg();
                    }
                    empQuoDtoList = empQuoDtoList.stream().filter(it ->
                            filterQuotaIds2.contains(it.getEmpQuotaId())
                    ).collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isEmpty(empQuoDtoList)) {
                log.info("checkEmpQuota get EmpQuoDto is Empty");
                //return "没有假期配额可使用";
                return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_QUOTA_NO_LEFT, null).getMsg();
            }
            List<WaLeaveQuotaUse> useAddList = new ArrayList<>();
            Float duration;
            Float usableDay;
            Float usedDay;
            Long leaveDate;
            for (WaLeaveDaytime waLeaveDaytime : allDaytimeList) {
                duration = waLeaveDaytime.getTimeDuration();
                leaveDate = waLeaveDaytime.getLeaveDate();
                for (EmpQuoDto empQuoDto : empQuoDtoList) {
                    if (empQuoDto.getStartDate() <= leaveDate && empQuoDto.getLastDate() >= leaveDate) {
                        usableDay = empQuoDto.getUsableQuota();
                        usedDay = empQuoDto.getUsedDay();
                        if (usableDay <= 0) {
                            continue;
                        }
                        if (duration > usableDay) {
                            //额度不足
                            empQuoDto.setUsableQuota(0F);
                            duration = duration - usableDay;
                            usedDay += usableDay;
                            empQuoDto.setUsedDay(usedDay);
                            if (!onlyCheck) {
                                saveWaLeaveQuotaUse(userId, waLeaveDaytime, usableDay, empQuoDto, useAddList, approvalStatus);
                            }
                        } else {
                            empQuoDto.setUsableQuota(usableDay - duration);
                            usedDay += duration;
                            empQuoDto.setUsedDay(usedDay);
                            if (!onlyCheck) {
                                saveWaLeaveQuotaUse(userId, waLeaveDaytime, duration, empQuoDto, useAddList, approvalStatus);
                            }
                            duration = 0F;
                            break;
                        }
                    }
                }
                if (duration > 0) {
                    if (BooleanUtils.isTrue(isNegative)) {
                        if (!onlyCheck) {
                            EmpQuoDto empQuoDto = empQuoDtoList.get(empQuoDtoList.size() - 1);
                            empQuoDto.setUsableQuota(0F);
                            empQuoDto.setUsedDay(empQuoDto.getUsedDay() + duration);
                            saveWaLeaveQuotaUse(userId, waLeaveDaytime, duration, empQuoDto, useAddList, approvalStatus);
                        }
                    } else {
                        //return DateUtil.getDateStrByTimesamp(leaveDate) + "的假期配额不足";
                        return String.format(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_QUOTA_NOT_ENOUGH, null).getMsg(), DateUtil.getDateStrByTimesamp(leaveDate));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(useAddList)) {
                waLeaveQuotaUseMapper.insertBatch(useAddList);
            } else {
                log.info("checkEmpQuota insertBatch useAddList is Empty");
            }
        }
        return "";
    }

    private void saveWaLeaveQuotaUse(Long userId, WaLeaveDaytime waLeaveDaytime, Float duration, EmpQuoDto empQuoDto,
                                     List<WaLeaveQuotaUse> useAddList, Integer approvalStatus) {
        Long currentTime = DateUtil.getCurrentTime(false);
        WaLeaveQuotaUse quotaUse = new WaLeaveQuotaUse();
        quotaUse.setUseId(snowflakeUtil.createId());
        quotaUse.setEmpId(empQuoDto.getEmpId());
        quotaUse.setApprovalStatus(approvalStatus);
        quotaUse.setDeleted(0);
        quotaUse.setLeaveDate(waLeaveDaytime.getLeaveDate());
        quotaUse.setLeaveDaytimeId(waLeaveDaytime.getLeaveDaytimeId());
        quotaUse.setEmpQuotaId(empQuoDto.getEmpQuotaId());
        quotaUse.setTimeDuration(duration);
        quotaUse.setCreateBy(Long.valueOf(userId.toString()));
        quotaUse.setCreateTime(currentTime);
        quotaUse.setUpdateBy(Long.valueOf(userId.toString()));
        quotaUse.setUpdateTime(currentTime);
        quotaUse.setCancelTimeDuration(0f);
        useAddList.add(quotaUse);
    }

    private void saveCompensatoryDetail(boolean onlyCheck, Long userId, WaLeaveDaytime waLeaveDaytime, Float duration,
                                        UsableCompensatoryQuotaDto usableCompensatoryQuotaDto, Integer approvalStatus) {
        if (!onlyCheck) {
            WaCompensatoryQuotaUse waCompensatoryQuotaUse = new WaCompensatoryQuotaUse();

            Long currentTime = DateUtil.getCurrentTime(false);
            waCompensatoryQuotaUse.setUseId(snowflakeUtil.createId());
            waCompensatoryQuotaUse.setEmpId(usableCompensatoryQuotaDto.getEmpId());
            waCompensatoryQuotaUse.setApprovalStatus(approvalStatus);
            waCompensatoryQuotaUse.setDeleted(0);
            waCompensatoryQuotaUse.setLeaveDate(waLeaveDaytime.getLeaveDate());
            waCompensatoryQuotaUse.setLeaveDaytimeId(ConvertHelper.longConvert(waLeaveDaytime.getLeaveDaytimeId()));
            waCompensatoryQuotaUse.setQuotaId(usableCompensatoryQuotaDto.getQuotaId());
            waCompensatoryQuotaUse.setTimeDuration(duration);
            waCompensatoryQuotaUse.setCancelTimeDuration(0f);
            waCompensatoryQuotaUse.setCreateBy(Long.valueOf(userId));
            waCompensatoryQuotaUse.setCreateTime(currentTime);
            waCompensatoryQuotaUse.setUpdateBy(Long.valueOf(userId));
            waCompensatoryQuotaUse.setUpdateTime(currentTime);

            waCompensatoryQuotaUseMapper.insert(waCompensatoryQuotaUse);
        }
    }

    /**
     * 处理年假额度
     *
     * @param quoList1          法定年假
     * @param quoList2          全部年假
     * @param totalTimeDuration 请假时长
     * @param leaveId           请假单id
     * @param waLeaveType       假期类型
     * @param timeUnit          时间单位
     * @return
     */
    public String processQuota(SysEmpInfo empInfo, List<WaEmpQuota> quoList1, List<WaEmpQuota> quoList2, BigDecimal totalTimeDuration, Integer leaveId, WaLeaveType waLeaveType, int timeUnit, boolean isDel, WaLeaveSetting leaveSetting, SessionBean sessionBean) {
        WaEmpQuota empQuota = quoList1.get(0);//法定年假额度
        float remainDay = empQuota.getRemainDay();
        float quotaDay = empQuota.getQuotaDay();

        if (empQuota.getDeductionDay() == null) {
            empQuota.setDeductionDay(0f);
        }
        float deductionDay = empQuota.getDeductionDay();
        float usedDay = empQuota.getUsedDay();
        BigDecimal totalDecFd;

        Integer userItType = waLeaveType.getUseItType();
        boolean isZs = false;//是否参与折算
        BigDecimal zsDateNum = new BigDecimal(0);
        if (userItType != null && userItType.intValue() == 1) {//按天折算
            float fdOriginalQuota = empQuota.getOriginalQuotaDay();//法定原始配额
            float flOriginalQuota = 0f;//福利原始配额
            if (quoList2 != null && quoList2.size() > 0) {
                flOriginalQuota = quoList2.get(0).getOriginalQuotaDay();
            }
            BigDecimal tmpOriginalQuota = (new BigDecimal(fdOriginalQuota)).add(new BigDecimal(flOriginalQuota));
            //取得该人最大请假额度
            if (leaveSetting.getQuotaPeriodType().intValue() == 1) {//1自然年才有折算意义,2为合同年，无意义
                long quotaStartDate = leaveSetting.getStartDate();//配额开始日
                long hireDate = empInfo.getHireDate();
                if (hireDate > quotaStartDate) {//以hireDate为准
                    quotaStartDate = hireDate;
                }
                zsDateNum = this.getZsyz(quotaStartDate, tmpOriginalQuota, waLeaveType);
//				zsDateNum = zsDateNum.multiply(new BigDecimal(24*60));//额度为天，但
                isZs = true;
            }
        }
        if (waLeaveType.getAcctTimeType().intValue() == 2) {
            totalTimeDuration = totalTimeDuration.divide(new BigDecimal(60), 1, BigDecimal.ROUND_HALF_UP);
        }
        if (isZs) {
            if (zsDateNum.compareTo(totalTimeDuration) == -1) {
//                return "配额不够";
                return messageResource.getMessage("L005737", new Object[]{}, new Locale(sessionBean.getLanguage()));
            }
        }
        //法定可用额度
        totalDecFd = new BigDecimal(remainDay).add(new BigDecimal(quotaDay)).subtract(new BigDecimal(deductionDay)).subtract(new BigDecimal(usedDay));

        /**
         * 2种可能：1法定可用额度>请假额度。2：没有全部额度，且年假可预支。
         */
        if (totalDecFd.floatValue() >= totalTimeDuration.floatValue()) {//法定年假剩余大于需要使用的额度
            //法定年假增加使用值，全部年假也增加使用值
            //法定年假增加使用值
            WaEmpQuota empQuotaFd = new WaEmpQuota();
            BigDecimal usedFd = new BigDecimal(usedDay).add(totalTimeDuration);
            empQuotaFd.setUsedDay(usedFd.floatValue());
            empQuotaFd.setEmpQuotaId(empQuota.getEmpQuotaId());
            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuotaFd);
//		if(quoList2.size()>0){//需要扣除
//			WaEmpQuota empQuotaCorp = quoList2.get(0);//取得公司年假额度
//			WaEmpQuota empQuota3 = new WaEmpQuota();
//			BigDecimal empQuotaQB = new BigDecimal(empQuotaCorp.getUsedDay());
//			empQuota3.setUsedDay(empQuotaQB.add(totalTimeDuration).floatValue());
//			empQuota3.setEmpQuotaId(empQuotaCorp.getEmpQuotaId());
//			waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota3);
//		}
            return "";
        } else {//法定额度不够
            if (quoList2.size() < 1 && (!waLeaveType.getIsUsedInAdvance())) {//无公司年假配额&&不可预支
                if (isDel) {
                    deleteLeave(leaveId);
                } else {//修改状态为0
                    WaEmpLeave leaveTmp = new WaEmpLeave();
                    leaveTmp.setLeaveId(leaveId);
                    leaveTmp.setStatus((short) 0);
                    leaveTmp.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
                    leaveTmp.setUpdtime(System.currentTimeMillis() / 1000);
                    waEmpLeaveMapper.updateByPrimaryKeySelective(leaveTmp);
                }
//                return "配额不够";
                return messageResource.getMessage("L005737", new Object[]{}, new Locale(sessionBean.getLanguage()));
            }
            //需要判断法定年假剩余+公司年假剩余设法足够
            WaEmpQuota empQuotaCorp = quoList2.get(0);//取得公司年假额度
            BigDecimal usedCorp = new BigDecimal(empQuotaCorp.getUsedDay());
            BigDecimal flDecCorp, totalDecCorp;
            //总额度可用值
            flDecCorp = new BigDecimal(empQuotaCorp.getRemainDay()).add(new BigDecimal(empQuotaCorp.getQuotaDay())).subtract(new BigDecimal(empQuotaCorp.getDeductionDay())).subtract(new BigDecimal(empQuotaCorp.getUsedDay()));
            totalDecCorp = totalDecFd.add(flDecCorp);
            if (totalDecCorp.floatValue() < totalTimeDuration.floatValue() && (!waLeaveType.getIsUsedInAdvance())) {
                if (isDel) {
                    deleteLeave(leaveId);
                } else {//修改状态为0
                    WaEmpLeave leaveTmp = new WaEmpLeave();
                    leaveTmp.setLeaveId(leaveId);
                    leaveTmp.setStatus((short) 0);
                    leaveTmp.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
                    leaveTmp.setUpdtime(System.currentTimeMillis() / 1000);
                    waEmpLeaveMapper.updateByPrimaryKeySelective(leaveTmp);
                }
//                return "配额不够";
                return messageResource.getMessage("L005737", new Object[]{}, new Locale(sessionBean.getLanguage()));
            }

            //先扣除所有的法定额度
            if (totalDecFd.floatValue() > 0) {
                WaEmpQuota empQuota2 = new WaEmpQuota();
                BigDecimal usedDec = new BigDecimal(usedDay).add(totalDecFd);//额度变0
                empQuota2.setUsedDay(usedDec.floatValue());
                empQuota2.setEmpQuotaId(empQuota.getEmpQuotaId());
                waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota2);
            }
            //再扣除福利额度
            //福利额度需要扣除数为：
            BigDecimal fl2 = totalTimeDuration.subtract(totalDecFd);//福利额度需要扣除的额度
            WaEmpQuota empQuota3 = new WaEmpQuota();
            empQuota3.setUsedDay((usedCorp.add(fl2)).floatValue());
            empQuota3.setEmpQuotaId(empQuotaCorp.getEmpQuotaId());
            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota3);

            return "";
        }

    }

    /**
     * 日期类型校验
     *
     * @param waLeaveType
     * @param sdetail
     * @param edetail
     * @param startTimeStr
     * @param endTimeStr
     * @param startDate
     * @param endDate
     * @return
     */
    public String checkLeaveDateType(WaLeaveType waLeaveType, WaWorktimeDetail sdetail, WaWorktimeDetail edetail, String startTimeStr, String endTimeStr, Long startDate, Long endDate) {
        if (startDate.equals(endDate)) {
            if (sdetail.getDateType() == 2 && (waLeaveType.getIsRestDay() == null || !waLeaveType.getIsRestDay())) {
                return "休息日不能请假";
            } else if (sdetail.getDateType() == 3 && (waLeaveType.getIsLegalHoliday() == null || !waLeaveType.getIsLegalHoliday())) {
                return "法定节假日不能请假";
            } else if (sdetail.getDateType() == 5 && (waLeaveType.getIsLegalHoliday() == null || !waLeaveType.getIsLegalHoliday())) {
                return "法定休日不能请假";
            } else if (sdetail.getDateType() == 4 && (waLeaveType.getIsRestDay() == null || !waLeaveType.getIsRestDay())) {
                return "特殊休日不能请假";
            }
        } else {
            if (sdetail.getDateType() == 2 && (waLeaveType.getIsRestDay() == null || !waLeaveType.getIsRestDay())) {
                //{0}为休息日，不能为请假开始日
                return messageResource.getMessage("L005740", new Object[]{startTimeStr}, new Locale(SessionHolder.getLang()));
            } else if (sdetail.getDateType() == 3 && (waLeaveType.getIsLegalHoliday() == null || !waLeaveType.getIsLegalHoliday())) {
                //{0}为法定假日，不能为请假开始日
                return messageResource.getMessage("L005741", new Object[]{startTimeStr}, new Locale(SessionHolder.getLang()));
            } else if (sdetail.getDateType() == 5 && (waLeaveType.getIsLegalHoliday() == null || !waLeaveType.getIsLegalHoliday())) {
                return startTimeStr + "为法定休日，不能为请假开始日";
            } else if (sdetail.getDateType() == 4 && (waLeaveType.getIsRestDay() == null || !waLeaveType.getIsRestDay())) {
                return startTimeStr + "为特殊休日，不能为请假开始日";
            }
        }
        return "";
    }

    public Map checkLeave(Long empId, Integer period, String startTimeStr, String endTimeStr, Map<Long, WaWorktimeDetail> pbMap, Map<String, Object> leaveMap, WaLeaveType waLeaveType, SessionBean sessionBean) {
        Map<String, Object> mapRtn = new HashMap<>();
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (empInfo == null) {
            mapRtn.put("status", -1);
            mapRtn.put("message", messageResource.getMessage("L006835", new Object[]{}, new Locale(sessionBean.getLanguage())));
            return mapRtn;
        }
        // 休假校验
        return this.checkLeave(empInfo, period, startTimeStr, endTimeStr, pbMap, leaveMap, waLeaveType, sessionBean);
    }

    /**
     * 休假校验
     *
     * @param empInfo
     * @param period
     * @param startTimeStr
     * @param endTimeStr
     * @param pbMap
     * @param leaveMap
     * @param waLeaveType
     * @param sessionBean
     * @return
     */
    public Map checkLeave(SysEmpInfo empInfo, Integer period, String startTimeStr, String endTimeStr,
                          Map<Long, WaWorktimeDetail> pbMap, Map<String, Object> leaveMap,
                          WaLeaveType waLeaveType, SessionBean sessionBean) {
        long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);
        Long empid = empInfo.getEmpid();
        Map<String, Object> mapRtn = new HashMap<>();
        // 排班检查
        long tmpDate = startDate;
        while (tmpDate <= endDate) {
            if (pbMap == null || pbMap.get(tmpDate) == null) {//没有排班记录
                mapRtn.put("status", -1);
                mapRtn.put("message", messageResource.getMessage("L005739", new Object[]{DateUtil.getDateStrByTimesamp(tmpDate)}, new Locale(sessionBean.getLanguage())));
                return mapRtn;
            }
            tmpDate = tmpDate + 24 * 60 * 60;
        }
        // 查询休假开始、结束日期班次日历信息
        WaWorktimeDetail sdetail = pbMap.get(startDate);
        WaWorktimeDetail edetail = pbMap.get(endDate);
        WaShiftDef waShiftDef = waShiftDefMapper.selectByPrimaryKey(sdetail.getShiftDefId());
        WaShiftDef waShiftDef2;
        if (sdetail.getShiftDefId().equals(edetail.getShiftDefId())) {
            waShiftDef2 = waShiftDef;
        } else {
            waShiftDef2 = waShiftDefMapper.selectByPrimaryKey(edetail.getShiftDefId());
        }
        if (sdetail.getDateType() == 4) {
            sdetail.setDateType(waShiftDef.getDateType());
        }
        if (edetail.getDateType() == 4) {
            edetail.setDateType(waShiftDef2.getDateType());
        }

        //日期类型校验
        if (edetail.getDateType() != 1 && period == 3) {
            //非工作日并且休的是小时假，需要先判断前一日是否未跨夜班，如果不是，则继续校验日期类型
            Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
            WaWorktimeDetail preEndDateWorkTimeDetail = pbMap.get(preEndDate);
            if (!(preEndDateWorkTimeDetail != null && preEndDateWorkTimeDetail.getDateType() == 1
                    && CdWaShiftUtil.checkCrossNight(preEndDateWorkTimeDetail.getStartTime(), preEndDateWorkTimeDetail.getEndTime(), preEndDateWorkTimeDetail.getDateType()))) {//跨夜
                String errorMsg = checkLeaveDateType(waLeaveType, sdetail, edetail, startTimeStr, endTimeStr, startDate, endDate);
                if (StringUtils.isNotBlank(errorMsg)) {
                    mapRtn.put("status", -1);
                    mapRtn.put("message", errorMsg);
                    return mapRtn;
                }
            }
        } else {
            String errorMsg = checkLeaveDateType(waLeaveType, sdetail, edetail, startTimeStr, endTimeStr, startDate, endDate);
            if (StringUtils.isNotBlank(errorMsg)) {
                mapRtn.put("status", -1);
                mapRtn.put("message", errorMsg);
                return mapRtn;
            }
        }
        // 考勤帐套截止日判断
        WaSob waSob = waSobService.getWaSob(empid, startDate);
        Boolean isImport = leaveMap.get("isImport") == null ? Boolean.FALSE : (Boolean) leaveMap.get("isImport");
        if (waSob != null && !isImport) {
            Integer sysPeriodMonth = waSob.getSysPeriodMonth();
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            Long onlyDate = DateUtil.getOnlyDate();
            if (onlyDate > sobEndDate) {
                mapRtn.put("status", -1);
                //mapRtn.put("message", "申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员。");
                mapRtn.put("message", String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
                return mapRtn;
            }
        }
        //时间重叠校验
        String shalfday = null != leaveMap.get("shalfday") ? leaveMap.get("shalfday").toString() : "";
        String ehalfday = null != leaveMap.get("ehalfday") ? leaveMap.get("ehalfday").toString() : "";
        WaLeaveTypeDef waLeaveTypeDef = waLeaveTypeDefMapper.selectByPrimaryKey(waLeaveType.getLeaveType());
        BuildTimeOverlapCheckParamDto paramDto = new BuildTimeOverlapCheckParamDto();
        paramDto.setEmpid(empid).setPeriod(period).setStartDate(startDate).setEndDate(endDate)
                .setStartTimeStr(startTimeStr).setEndTimeStr(endTimeStr)
                .setShalfday(shalfday).setEhalfday(ehalfday).setStartDateShift(waShiftDef)
                .setEndDateShift(waShiftDef2).setLeaveTypeDefCode(waLeaveTypeDef.getLeaveTypeDefCode());
        Map<String, Object> params = doBuildTimeOverlapCheckParams(paramDto);
        // 非哺乳假：申请休假校验出差时间重复
        if (!"BRJ".equals(waLeaveTypeDef.getLeaveTypeDefCode())) {
            if (empTravelMapper.checkEmpTravelTimeRepeat(empid, Long.valueOf(params.get("start").toString()), Long.valueOf(params.get("end").toString())) > 0) {
                mapRtn.put("status", -1);
                // 存在日期重叠的申请，不能提交
                mapRtn.put("message", messageResource.getMessage("L005744", new Object[]{}, new Locale(sessionBean.getLanguage())));
                return mapRtn;
            }
        }
        // 检查待申请休假时长是否和系统已存在的休假单存在日期重叠-老的逻辑
        if (!applyleaveTimeoverlapOpenNew) {
            // todo 新逻辑测试稳定后，删除老逻辑,移除开关：applyleaveTimeoverlapOpenNew
            if (waCheckMapper.checkLeaveRepeat(params) > 0) {
                //查询销假单
                List<Map> list = waMapper.checkLcRepeat(params);
                boolean flag = true;
                Long leaveStart = (Long) params.get("start");
                Long leaveEnd = (Long) params.get("end");
                if (period == 1 || period == 4) {
                    leaveStart = startDate + waShiftDef.getStartTime() * 60;
                    leaveEnd = endDate + waShiftDef2.getEndTime() * 60;
                }
                for (Map map : list) {
                    Long start = (Long) map.get("shift_start_time");
                    Long end = (Long) map.get("shift_end_time");
                    if (leaveStart >= start && leaveEnd <= end) {
                        flag = false;
                    }
                }
                if (flag) {
                    mapRtn.put("status", -1);
                    mapRtn.put("message", messageResource.getMessage("L005744", new Object[]{}, new Locale(sessionBean.getLanguage())));
                    return mapRtn;
                }
            }
        } else if (isImport) {
            // 导入/接入 校验逻辑
            // 新版时间重叠校验逻辑
            boolean timeOverlap = false;
            try {
                //查询公司全部班次
                Map<Integer, WaShiftDef> shiftMap = waCommonService.getCorpAllShiftDef(sessionBean.getBelongid());
                timeOverlap = checkLeaveTimeOverlap(pbMap, shiftMap, leaveMap, waLeaveType, sessionBean, empInfo, period);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (timeOverlap) {
                // 存在日期重叠的申请，不能提交
                mapRtn.put("status", -1);
                mapRtn.put("message", messageResource.getMessage("L005744", new Object[]{}, new Locale(sessionBean.getLanguage())));
                return mapRtn;
            }
        }
        return mapRtn;
    }

    /**
     * 休假时间重叠校验
     *
     * @param pbMap
     * @param shiftMap
     * @param map
     * @param waLeaveType
     * @param sessionBean
     * @param empInfo
     * @return
     * @throws Exception
     */
    public boolean checkLeaveTimeOverlap(Map<Long, WaWorktimeDetail> pbMap, Map<Integer, WaShiftDef> shiftMap,
                                         Map<String, Object> map, WaLeaveType waLeaveType, SessionBean sessionBean,
                                         SysEmpInfo empInfo, Integer period) throws Exception {
        String startTimeStr = (String) map.get("starttime");
        String endTimeStr = (String) map.get("endtime");
        long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);//yyyy-MM-dd
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);//yyyy-MM-dd

        // 开始校验
        WaWorktimeDetail startDateDetail;
        WaShiftDef startDateShift;
        long tmpDate = startDate;
        while (tmpDate <= endDate) {
            startDateDetail = pbMap.get(startDate);
            startDateShift = shiftMap.get(startDateDetail.getShiftDefId());
            // 计算每日休假明细
            WaLeaveDaytime dayTime = doBuildLeaveDaytime(shiftMap, pbMap, map, tmpDate, endDate, startTimeStr, endTimeStr,
                    period, waLeaveType, empInfo, sessionBean);
            // 每日休假时间重叠校验
            boolean timeOverlap = checkLeaveDayTimeOverlap(empInfo.getEmpid(), dayTime, startDateShift, waLeaveType);
            if (timeOverlap) {
                return true;
            }
            tmpDate = tmpDate + 24 * 60 * 60;
        }
        return false;
    }

    private Map<String, Object> doBuildTimeOverlapCheckParams(BuildTimeOverlapCheckParamDto paramDto) {
        Long empid = paramDto.getEmpid();
        Integer period = paramDto.getPeriod();
        long startDate = paramDto.getStartDate();
        long endDate = paramDto.getEndDate();
        String startTimeStr = paramDto.getStartTimeStr();
        String endTimeStr = paramDto.getEndTimeStr();
        String shalfday = paramDto.getShalfday();
        String ehalfday = paramDto.getEhalfday();
        WaShiftDef waShiftDef = paramDto.getStartDateShift();
        WaShiftDef waShiftDef2 = paramDto.getEndDateShift();
        String leaveTypeDefCode = paramDto.getLeaveTypeDefCode();
        Map<String, Object> params = new HashMap<>();
        params.put("empid", empid);
        if (period == 1 || period == 4) {//整天
            params.put("start", startDate);
            params.put("end", endDate + 86399);
        } else if (period == 3) {
            //排班校验 如果请假 小时单位，日历是整天的，那么永远取不到排班信息
            Long start = (Long) IocUtil.getDateValue(startTimeStr, "BIGINT");
            Long end = (Long) IocUtil.getDateValue(endTimeStr, "BIGINT");
            params.put("start", start);
            params.put("end", end);
        } else if (period == 9) {
            //String shalfday = leaveMap.get("shalfday").toString();
            if ("P".equals(shalfday)) {
                if (waShiftDef.getIsHalfdayTime() != null && waShiftDef.getIsHalfdayTime() && waShiftDef.getHalfdayTime() > 0) {
                    params.put("start", startDate + waShiftDef.getHalfdayTime() * 60);
                } else if (waShiftDef.getIsNoonRest() != null && waShiftDef.getIsNoonRest()) {
                    params.put("start", startDate + waShiftDef.getNoonRestEnd() * 60);
                } else {
                    params.put("start", startDate + waShiftDef.getStartTime() * 60);
                }
            } else {
                params.put("start", startDate + waShiftDef.getStartTime() * 60);
            }
            //String ehalfday = leaveMap.get("ehalfday").toString();
            if ("A".equals(ehalfday)) {
                if (waShiftDef2.getIsHalfdayTime() != null && waShiftDef2.getIsHalfdayTime() && waShiftDef2.getHalfdayTime() > 0) {
                    params.put("end", endDate + waShiftDef2.getHalfdayTime() * 60);
                } else if (waShiftDef2.getIsNoonRest() != null && waShiftDef2.getIsNoonRest()) {
                    params.put("end", endDate + waShiftDef2.getNoonRestStart() * 60);
                } else {
                    params.put("end", endDate + waShiftDef2.getEndTime() * 60);
                }
            } else {
                //跨夜
                if (CdWaShiftUtil.checkCrossNight(waShiftDef2.getStartTime(), waShiftDef2.getEndTime(), waShiftDef2.getDateType())) {
                    endDate += 1440 * 60;
                }
                params.put("end", endDate + waShiftDef2.getEndTime() * 60);
            }
        }
        if (StringUtils.isNotBlank(leaveTypeDefCode) && "BRJ".equals(leaveTypeDefCode)) {
            params.put("leaveTypeCode", leaveTypeDefCode);
        }
        return params;
    }

    public String checkMaxMinLeave(WaLeaveType waLeaveType, BigDecimal totalTimeDuration, SessionBean sessionBean) {
        if (waLeaveType.getIsCheckMaxTime() && waLeaveType.getMaxLeaveTime() != null && waLeaveType.getMaxLeaveTime() > 0) {
            if (waLeaveType.getAcctTimeType() == 2) {//小时
                BigDecimal tmpMaxTime = new BigDecimal(waLeaveType.getMaxLeaveTime());
                tmpMaxTime = tmpMaxTime.multiply(new BigDecimal(60));
                if (totalTimeDuration.floatValue() > tmpMaxTime.floatValue()) {//超过最大值
//                    return "超过最大请假值：" + waLeaveType.getMaxLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType());
                    return messageResource.getMessage("L005745", new Object[]{waLeaveType.getMaxLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType())}, new Locale(sessionBean.getLanguage()));
                }
            } else {//按天
                if (totalTimeDuration.floatValue() > waLeaveType.getMaxLeaveTime()) {//超过最大值
//                    return "超过最大请假值：" + waLeaveType.getMaxLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType());
                    return messageResource.getMessage("L005745", new Object[]{waLeaveType.getMaxLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType())}, new Locale(sessionBean.getLanguage()));
                }
            }
        }
        if (waLeaveType.getIsCheckMinTime() && waLeaveType.getMinLeaveTime() != null && waLeaveType.getMinLeaveTime() > 0) {
            if (waLeaveType.getAcctTimeType() == 2) {//小时
                BigDecimal tmpMinTime = new BigDecimal(waLeaveType.getMinLeaveTime());
                tmpMinTime = tmpMinTime.multiply(new BigDecimal(60));
                if (totalTimeDuration.floatValue() < tmpMinTime.floatValue()) {//超过最大值
//                    return "必须最少请假：" + waLeaveType.getMinLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType());
                    return messageResource.getMessage("L005746", new Object[]{waLeaveType.getMinLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType())}, new Locale(sessionBean.getLanguage()));
                }
            } else {//按天
                if (totalTimeDuration.floatValue() < waLeaveType.getMinLeaveTime()) {//超过最大值
//                    return "必须最少请假：" + waLeaveType.getMinLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType());
                    return messageResource.getMessage("L005746", new Object[]{waLeaveType.getMinLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType())}, new Locale(sessionBean.getLanguage()));
                }
            }

        }
        return "";
    }

    public String processLeaveByPeriod3(WaLeaveType waLeaveType, long startDate, long endDate, String startTimeStr, String endTimeStr,
                                        WaWorktimeDetail detail, WaLeaveDaytime dayTime, WaShiftDef shiftDef) throws Exception {
        //小时非整天
        Integer tmpS = 0, tmpE = 0;
        long leaveStartTime = 0;
        long leaveEndTime = 0;
        if (startDate == endDate) {//最后一天
            String ehm = endTimeStr.substring(10).trim();//如2015-09-16 09:15  会变成09:15
            String[] ehmArr = ehm.split(":");
            Integer endShm = Integer.parseInt(ehmArr[0]) * 60 + Integer.parseInt(ehmArr[1]);

            if (startDate == DateUtil.getTimesampByDateStr2(startTimeStr)) {//也是第一天，最后一天
                String shm = startTimeStr.substring(10).trim();//如2015-09-16 09:15  会变成09:15
                String[] shmArr = shm.split(":");
                Integer startShm = Integer.parseInt(shmArr[0]) * 60 + Integer.parseInt(shmArr[1]);

                if (detail.getStartTime() > startShm) {
                    tmpS = detail.getStartTime();
                } else {
                    tmpS = startShm;
                }

                if (detail.getEndTime() < endShm) {
                    tmpE = detail.getEndTime();
                } else {
                    tmpE = endShm;
                }

                leaveStartTime = startDate + tmpS * 60;
                leaveEndTime = startDate + tmpE * 60;
            } else {//是最后一天，但不是第一天
                tmpS = detail.getStartTime();
                if (detail.getEndTime() < endShm) {
                    tmpE = detail.getEndTime();
                } else {
                    tmpE = endShm;
                }

                leaveStartTime = endDate + tmpS * 60;
                leaveEndTime = endDate + tmpE * 60;
            }
            if (detail.getIsNoonRest() != null && detail.getIsNoonRest()) {
                Integer timeDuration = deductLeaveDayNooRest(detail, leaveStartTime, leaveEndTime);
                dayTime.setTimeDuration(new Float(timeDuration));
            } else {
                dayTime.setTimeDuration(new Float(tmpE.intValue() - tmpS.intValue()));
            }
            dayTime.setStartTime(tmpS);
            dayTime.setEndTime(tmpE);
        } else if (startDate == DateUtil.getTimesampByDateStr2(startTimeStr)) {//也是第一天，不是最后一天
            String shm = startTimeStr.substring(10).trim();//如2015-09-16 09:15  会变成09:15
            String[] shmArr = shm.split(":");
            Integer startShm = Integer.parseInt(shmArr[0]) * 60 + Integer.parseInt(shmArr[1]);
            tmpS = detail.getStartTime();
            tmpE = detail.getEndTime();
            if (startShm < detail.getStartTime()) {
                dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
            } else if (startShm > detail.getEndTime()) {
                dayTime.setTimeDuration(0f);
            } else {//说明在上班中间请假
                tmpS = startShm;

                leaveStartTime = startDate + tmpS * 60;
                leaveEndTime = startDate + tmpE * 60;

                if (detail.getIsNoonRest()) {
                    Integer timeDuration = deductLeaveDayNooRest(detail, leaveStartTime, leaveEndTime);
                    dayTime.setTimeDuration(new Float(timeDuration));
                } else {
                    dayTime.setTimeDuration(new Float(tmpE.intValue() - tmpS.intValue()));
                }
            }
            dayTime.setStartTime(tmpS);
            dayTime.setEndTime(tmpE);
        } else {//是中间一天
            //加上特殊日期
            //不是特殊日期，算排班
            if (detail.getDateType() == 1) {//是工作日
                dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
                dayTime.setStartTime(detail.getStartTime());
                dayTime.setEndTime(detail.getEndTime());
            } else if (detail.getDateType() == 2 && waLeaveType.getIsRestDay()) {//是休息日连续计算
                dayTime.setTimeDuration(8 * 60f);
                dayTime.setStartTime(0);
                dayTime.setEndTime(1440);
            } else if ((detail.getDateType() == 3 || detail.getDateType() == 5) && waLeaveType.getIsLegalHoliday()) {//是法定假日连续计算
                dayTime.setTimeDuration(8 * 60f);
                dayTime.setStartTime(0);
                dayTime.setEndTime(1440);
            } else if (detail.getDateType() == 4 && waLeaveType.getIsRestDay()) {//是休息日连续计算
                dayTime.setTimeDuration(8 * 60f);
                dayTime.setStartTime(0);
                dayTime.setEndTime(1440);
            }

        }
        if (dayTime.getTimeDuration() == null || dayTime.getTimeDuration().floatValue() < 0) {
            dayTime.setTimeDuration(0f);
        }
        //特殊班次工作时长
        if (shiftDef != null && shiftDef.getIsSpecial() != null && shiftDef.getIsSpecial() && shiftDef.getSpecialWorkTime() != null
                && dayTime.getTimeDuration().equals(Float.valueOf(detail.getWorkTotalTime()))) {
            dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
        }
        return "";
    }

    public Integer getLeaveDateHour(Long time) {
        String s = DateUtil.convertDateTimeToStr(time, "HH:mm", true);
        if (StringUtils.isNotBlank(s)) {
            String[] shmArr = s.split(":");
            Integer startShm = Integer.parseInt(shmArr[0]) * 60 + Integer.parseInt(shmArr[1]);
            return startShm;
        }
        return 0;
    }

    /**
     * 小时（过期，请使用新的逻辑方法：calLeaveTimeByPeriod3New）
     * @param waLeaveType
     * @param startDate
     * @param endDate
     * @param startTimeStr
     * @param endTimeStr
     * @param pbMap
     * @param dayTime
     * @param shiftMap
     * @return
     * @throws Exception
     */
    @Deprecated
    public String calLeaveTimeByPeriod3(WaLeaveType waLeaveType, long startDate, long endDate,
                                        String startTimeStr, String endTimeStr, Map<Long, WaWorktimeDetail> pbMap,
                                        WaLeaveDaytime dayTime, Map<Integer, WaShiftDef> shiftMap) throws Exception {
        if(applyleaveCalHourTimeOpenNew){
            // 启用新的计算逻辑
            return calLeaveTimeByPeriod3New(waLeaveType, startDate, endDate, startTimeStr, endTimeStr, pbMap, dayTime, shiftMap);
        }
        long minDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long maxDate = DateUtil.getTimesampByDateStr2(endTimeStr);
        long firstTime = DateUtil.getTimesampByStr2(startTimeStr);
        long lastTime = DateUtil.getTimesampByStr2(endTimeStr);

        WaWorktimeDetail detail = pbMap.get(startDate);//取得当天排班
        WaShiftDef shiftDef = shiftMap.get(detail.getShiftDefId());

        long workStime = startDate + detail.getStartTime() * 60;//上班时间
        long workEndDate = startDate;
        boolean isky = false;
        if (CdWaShiftUtil.checkCrossNight(detail.getStartTime(), detail.getEndTime(), detail.getDateType())) {//跨夜
            workEndDate += 86400;
            isky = true;
        }
        long workEtime = workEndDate + detail.getEndTime() * 60;//下班时间

        if (startDate == endDate && minDate == maxDate) {//请一天假，第一天也是最后一天
            Integer tmpS = getLeaveDateHour(firstTime);
            Integer tmpE = getLeaveDateHour(lastTime);

            if (tmpE < detail.getStartTime() || detail.getDateType() != 1) {//请假结束时间小于当天上班开始时间 或者 当天非工作日
                Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
                WaWorktimeDetail preEndDateWorkTimeDetail = pbMap.get(preEndDate);
                if (preEndDateWorkTimeDetail != null && preEndDateWorkTimeDetail.getDateType() == 1 &&
                        CdWaShiftUtil.checkCrossNight(preEndDateWorkTimeDetail.getStartTime(), preEndDateWorkTimeDetail.getEndTime(), preEndDateWorkTimeDetail.getDateType())
                        && tmpS < preEndDateWorkTimeDetail.getEndTime()) {

                    long leaveStartTime = firstTime;
                    long leaveEndTime = lastTime;
                    if (tmpE > preEndDateWorkTimeDetail.getEndTime()) {
                        tmpE = preEndDateWorkTimeDetail.getEndTime();
                        leaveEndTime = maxDate + tmpE * 60;
                    }

                    //前一天班次为跨夜班
                    if (preEndDateWorkTimeDetail.getIsNoonRest() != null && preEndDateWorkTimeDetail.getIsNoonRest()) {
                        Integer timeDuration = deductLeaveDayNooRest(preEndDateWorkTimeDetail, leaveStartTime, leaveEndTime);
                        dayTime.setTimeDuration(new Float(timeDuration));
                    } else {
                        dayTime.setTimeDuration((float) (tmpE - tmpS));
                    }
                } else {
                    dayTime.setTimeDuration(0f);
                }
            } else {
                long stime = Math.max(workStime, firstTime);
                long etime = Math.min(workEtime, lastTime);

                tmpS = getLeaveDateHour(stime);
                tmpE = getLeaveDateHour(etime);

                if (detail.getIsNoonRest() != null && detail.getIsNoonRest()) {
                    Integer timeDuration = deductLeaveDayNooRest(detail, stime, etime);
                    dayTime.setTimeDuration(new Float(timeDuration));
                } else {
                    dayTime.setTimeDuration((float) (tmpE - tmpS));
                }
            }
            dayTime.setStartTime(tmpS);
            dayTime.setEndTime(tmpE);
        } else {//请多天假
            if (startDate == endDate) {//最后一天
                Integer tmpS = getLeaveDateHour(firstTime);
                Integer tmpE = getLeaveDateHour(lastTime);

                if (tmpE < detail.getStartTime() || detail.getDateType() != 1) {//请假结束时间小于当天上班开始时间 或者 当天非工作日
                    Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
                    WaWorktimeDetail preEndDateWorkTimeDetail = pbMap.get(preEndDate);
                    if (preEndDateWorkTimeDetail != null && preEndDateWorkTimeDetail.getDateType() == 1
                            && CdWaShiftUtil.checkCrossNight(preEndDateWorkTimeDetail.getStartTime(), preEndDateWorkTimeDetail.getEndTime(), preEndDateWorkTimeDetail.getDateType())) {
                        //前一天班次为跨夜班
                        tmpS = 0;
                        long leaveStartTime = maxDate;
                        long leaveEndTime = lastTime;
                        if (tmpE > preEndDateWorkTimeDetail.getEndTime()) {
                            tmpE = preEndDateWorkTimeDetail.getEndTime();
                            leaveEndTime = maxDate + tmpE * 60;
                        }
                        if (preEndDateWorkTimeDetail.getIsNoonRest() != null && preEndDateWorkTimeDetail.getIsNoonRest()) {
                            Integer timeDuration = deductLeaveDayNooRest(preEndDateWorkTimeDetail, leaveStartTime, leaveEndTime);
                            dayTime.setTimeDuration(new Float(timeDuration));
                        } else {
                            dayTime.setTimeDuration((float) (tmpE - tmpS));
                        }
                    } else {
                        dayTime.setTimeDuration(0f);
                    }
                } else {
                    long leaveStartTime;
                    long leaveEndTime;

                    if (isky) {
                        tmpS = 0;
                        tmpE = Math.min(this.getLeaveDateHour(lastTime), detail.getEndTime());

                        leaveStartTime = maxDate;
                        leaveEndTime = maxDate + tmpE * 60;
                    } else {
                        tmpS = detail.getStartTime();
                        long etime = Math.min(workEtime, lastTime);
                        tmpE = this.getLeaveDateHour(etime);

                        leaveStartTime = maxDate + tmpS * 60;
                        leaveEndTime = maxDate + tmpE * 60;
                    }

                    if (detail.getIsNoonRest() != null && detail.getIsNoonRest()) {
                        Integer timeDuration = deductLeaveDayNooRest(detail, leaveStartTime, leaveEndTime);
                        dayTime.setTimeDuration(new Float(timeDuration));
                    } else {
                        dayTime.setTimeDuration((float) (tmpE - tmpS));
                    }
                }
                dayTime.setStartTime(tmpS);
                dayTime.setEndTime(tmpE);
            } else if (startDate == minDate) {//第一天
                long stime = Math.max(workStime, firstTime);
                Integer tmpS = this.getLeaveDateHour(stime);
                Integer tmpE;
                if (isky) {
                    tmpE = 24 * 60;
                } else {
                    tmpE = detail.getEndTime();
                }

                long leaveStartTime = stime;
                long leaveEndTime = minDate + tmpE * 60;

                if (stime < workStime) {
                    dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
                } else if (stime > workEtime) {
                    dayTime.setTimeDuration(0f);
                } else {
                    if (detail.getIsNoonRest()) {
                        Integer timeDuration = deductLeaveDayNooRest(detail, leaveStartTime, leaveEndTime);
                        dayTime.setTimeDuration(new Float(timeDuration));
                    } else {
                        dayTime.setTimeDuration((float) (tmpE - tmpS));
                    }
                }
                dayTime.setStartTime(tmpS);
                dayTime.setEndTime(tmpE);
            } else {//是中间一天
                if (detail.getDateType() == 1) {
                    dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
                    dayTime.setStartTime(detail.getStartTime());
                    dayTime.setEndTime(detail.getEndTime());
                } else if (detail.getDateType() == 2 && waLeaveType.getIsRestDay()) {
                    dayTime.setTimeDuration(8 * 60f);
                    dayTime.setStartTime(0);
                    dayTime.setEndTime(1440);
                } else if ((detail.getDateType() == 3 || detail.getDateType() == 5) && waLeaveType.getIsLegalHoliday()) {
                    dayTime.setTimeDuration(8 * 60f);
                    dayTime.setStartTime(0);
                    dayTime.setEndTime(1440);
                } else if (detail.getDateType() == 4 && waLeaveType.getIsRestDay()) {
                    dayTime.setTimeDuration(8 * 60f);
                    dayTime.setStartTime(0);
                    dayTime.setEndTime(1440);
                }
            }
        }
        if (dayTime.getTimeDuration() == null || dayTime.getTimeDuration() < 0) {
            dayTime.setTimeDuration(0f);
        }
        //特殊班次工作时长
        if (shiftDef != null && shiftDef.getIsSpecial() != null && shiftDef.getIsSpecial() && shiftDef.getSpecialWorkTime() != null
                && dayTime.getTimeDuration().equals(Float.valueOf(detail.getWorkTotalTime()))) {
            dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
        }
        return "";
    }

    /**
     * 小时
     * @param waLeaveType
     * @param startDate
     * @param endDate
     * @param startTimeStr
     * @param endTimeStr
     * @param pbMap
     * @param dayTime
     * @param shiftMap
     * @return
     * @throws Exception
     */
    public String calLeaveTimeByPeriod3New(WaLeaveType waLeaveType, long startDate, long endDate,
                                        String startTimeStr, String endTimeStr, Map<Long, WaWorktimeDetail> pbMap,
                                        WaLeaveDaytime dayTime, Map<Integer, WaShiftDef> shiftMap) throws Exception {
        long minDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long maxDate = DateUtil.getTimesampByDateStr2(endTimeStr);
        long firstTime = DateUtil.getTimesampByStr2(startTimeStr);
        long lastTime = DateUtil.getTimesampByStr2(endTimeStr);

        WaWorktimeDetail detail = pbMap.get(startDate);//取得当天排班
        WaShiftDef shiftDef = shiftMap.get(detail.getShiftDefId());

        long workStime = startDate + detail.getStartTime() * 60;//上班时间
        long workEndDate = startDate;
        boolean isky = false;
        if (CdWaShiftUtil.checkCrossNight(detail.getStartTime(), detail.getEndTime(), detail.getDateType())) {//跨夜
            workEndDate += 86400;
            isky = true;
        }
        long workEtime = workEndDate + detail.getEndTime() * 60;//下班时间

        boolean includeNonWorkday = (detail.getDateType() == 2 && waLeaveType.getIsRestDay())
                || (detail.getDateType() == 3 && waLeaveType.getIsLegalHoliday())
                || (detail.getDateType() == 4 && waLeaveType.getIsRestDay())
                || (detail.getDateType() == 5 && waLeaveType.getIsLegalHoliday());

        if (startDate == endDate && minDate == maxDate) {//请一天假，第一天也是最后一天
            Integer tmpS = getLeaveDateHour(firstTime);
            Integer tmpE = getLeaveDateHour(lastTime);

            if (tmpE <= detail.getStartTime() || detail.getDateType() != 1) {//请假结束时间小于当天上班开始时间 或者 当天非工作日
                Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
                WaWorktimeDetail preEndDateWorkTimeDetail = pbMap.get(preEndDate);
                if (preEndDateWorkTimeDetail != null && preEndDateWorkTimeDetail.getDateType() == 1 &&
                        CdWaShiftUtil.checkCrossNight(preEndDateWorkTimeDetail.getStartTime(), preEndDateWorkTimeDetail.getEndTime(), preEndDateWorkTimeDetail.getDateType())
                        && tmpS < preEndDateWorkTimeDetail.getEndTime()) {

                    long leaveStartTime = firstTime;
                    long leaveEndTime = lastTime;
                    if (tmpE > preEndDateWorkTimeDetail.getEndTime()) {
                        tmpE = preEndDateWorkTimeDetail.getEndTime();
                        leaveEndTime = maxDate + tmpE * 60;
                    }

                    //前一天班次为跨夜班
                    if (preEndDateWorkTimeDetail.getIsNoonRest() != null && preEndDateWorkTimeDetail.getIsNoonRest()) {
                        Integer timeDuration = deductLeaveDayNooRest(preEndDateWorkTimeDetail, leaveStartTime, leaveEndTime);
                        dayTime.setTimeDuration(new Float(timeDuration));
                    } else {
                        dayTime.setTimeDuration((float) (tmpE - tmpS));
                    }
                } else {
                    dayTime.setTimeDuration(0f);
                }
            } else {
                long stime = Math.max(workStime, firstTime);
                long etime = Math.min(workEtime, lastTime);

                tmpS = getLeaveDateHour(stime);
                tmpE = getLeaveDateHour(etime);

                if (detail.getIsNoonRest() != null && detail.getIsNoonRest()) {
                    Integer timeDuration = deductLeaveDayNooRest(detail, stime, etime);
                    dayTime.setTimeDuration(new Float(timeDuration));
                } else {
                    dayTime.setTimeDuration((float) (tmpE - tmpS));
                }
            }
            dayTime.setStartTime(tmpS);
            dayTime.setEndTime(tmpE);
        } else {//请多天假
            //计算开始日期与结束日期相差的天数
            Integer dateDiff = DateUtilExt.getDifferenceDay(minDate, maxDate);
            if (startDate == endDate) {//最后一天
                // 前一天班次信息
                Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
                WaWorktimeDetail preEndDateWorkTimeDetail = pbMap.get(preEndDate);
                boolean preDateKy = preEndDateWorkTimeDetail != null && preEndDateWorkTimeDetail.getDateType() == 1
                        && CdWaShiftUtil.checkCrossNight(preEndDateWorkTimeDetail.getStartTime(), preEndDateWorkTimeDetail.getEndTime(), preEndDateWorkTimeDetail.getDateType());

                Integer tmpS;
                Integer tmpE = getLeaveDateHour(lastTime);

                if (detail.getDateType() != 1) {
                    if (preDateKy) {
                        if (tmpE <= preEndDateWorkTimeDetail.getEndTime()) {
                            tmpS = 0;
                            tmpE = 0;
                            dayTime.setTimeDuration(0f);
                        } else {
                            tmpS = preEndDateWorkTimeDetail.getEndTime();
                            if (includeNonWorkday) {
                                int timeDuration = tmpE - tmpS;
                                dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                            } else {
                                dayTime.setTimeDuration(0f);
                            }
                        }
                    } else {
                        tmpS = 0;
                        if (includeNonWorkday) {
                            int timeDuration = tmpE - tmpS;
                            dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                        } else {
                            tmpE = 0;
                            dayTime.setTimeDuration(0f);
                        }
                    }
                } else {
                    if(tmpE < detail.getStartTime()){
                        tmpS = 0;
                        tmpE = 0;
                        dayTime.setTimeDuration(0f);
                    } else {
                        long leaveStartTime;
                        long leaveEndTime;

                        if (isky) {
                            tmpS = getLeaveDateHour(workStime);
                            tmpE = Math.min(tmpE, 1440);

                            leaveStartTime = workStime;
                            leaveEndTime = maxDate + tmpE * 60;
                        } else {
                            tmpS = detail.getStartTime();
                            long etime = Math.min(workEtime, lastTime);
                            tmpE = this.getLeaveDateHour(etime);

                            leaveStartTime = maxDate + tmpS * 60;
                            leaveEndTime = maxDate + tmpE * 60;
                        }

                        if (detail.getIsNoonRest() != null && detail.getIsNoonRest()) {
                            Integer timeDuration = deductLeaveDayNooRest(detail, leaveStartTime, leaveEndTime);
                            dayTime.setTimeDuration(new Float(timeDuration));
                        } else {
                            dayTime.setTimeDuration((float) (tmpE - tmpS));
                        }
                    }
                }
                dayTime.setStartTime(tmpS);
                dayTime.setEndTime(tmpE);
            } else if (startDate == minDate) {//第一天
                Integer tmpS = this.getLeaveDateHour(firstTime);
                Integer tmpE = 24 * 60;

                if(detail.getDateType() != 1){
                    if(includeNonWorkday){
                        int timeDuration = tmpE - tmpS;
                        dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                    } else {
                        tmpS = 0;
                        tmpE = 0;
                        dayTime.setTimeDuration(0f);
                    }
                } else {
                    long stime = Math.max(workStime, firstTime);
                    tmpS = this.getLeaveDateHour(stime);

                    long leaveStartTime = stime;
                    long leaveEndTime;

                    if (isky) {
                        if (dateDiff > 1) {
                            // 申请日期天数>2天
                            tmpE = shiftDef.getEndTime();
                            leaveEndTime = minDate + 86400 + tmpE * 60;
                        } else {
                            // 申请日期天数=2天
                            tmpE = Math.min(this.getLeaveDateHour(lastTime), shiftDef.getEndTime());
                            leaveEndTime = minDate + 86400 + tmpE * 60;
                        }
                        tmpE = 24 * 60;
                    } else {
                        tmpE = detail.getEndTime();
                        leaveEndTime = minDate + tmpE * 60;
                    }

                    if (stime < workStime) {
                        // todo 此逻辑是从1.0延续过来的，暂不清楚什么场景下才会使用
                        dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
                    } else if (stime > workEtime) {
                        dayTime.setTimeDuration(0f);
                    } else {
                        if (detail.getIsNoonRest()) {
                            Integer timeDuration = deductLeaveDayNooRest(detail, leaveStartTime, leaveEndTime);
                            dayTime.setTimeDuration(new Float(timeDuration));
                        } else {
                            long durationSec = leaveEndTime - leaveStartTime;
                            int timeDuration = new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
                            dayTime.setTimeDuration((float) (timeDuration));
                        }
                    }
                }
                dayTime.setStartTime(tmpS);
                dayTime.setEndTime(tmpE);
            } else {//是中间一天
                if (detail.getDateType() == 1) {
                    if (startDate == (maxDate - 86400)) {
                        // 倒数第二天
                        if(isky){
                            Integer tmpE = getLeaveDateHour(lastTime);
                            if(tmpE >= detail.getEndTime()){
                                dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
                                dayTime.setStartTime(detail.getStartTime());
                                dayTime.setEndTime(detail.getEndTime());
                            } else {
                                long leaveStartTime = workStime;
                                long leaveEndTime = lastTime;
                                if (detail.getIsNoonRest()) {
                                    Integer timeDuration = deductLeaveDayNooRest(detail, leaveStartTime, leaveEndTime);
                                    dayTime.setTimeDuration(new Float(timeDuration));
                                } else {
                                    long durationSec = leaveEndTime - leaveStartTime;
                                    int timeDuration = new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
                                    dayTime.setTimeDuration((float) (timeDuration));
                                }
                                dayTime.setStartTime(detail.getStartTime());
                                dayTime.setEndTime(tmpE);
                            }
                        } else {
                            dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
                            dayTime.setStartTime(detail.getStartTime());
                            dayTime.setEndTime(detail.getEndTime());
                        }
                    } else {
                        dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
                        dayTime.setStartTime(detail.getStartTime());
                        dayTime.setEndTime(detail.getEndTime());
                    }
                } else if (includeNonWorkday) {
                    dayTime.setTimeDuration(8 * 60f);
                    dayTime.setStartTime(0);
                    dayTime.setEndTime(1440);
                }
            }
        }
        if (dayTime.getTimeDuration() == null || dayTime.getTimeDuration() < 0) {
            dayTime.setTimeDuration(0f);
        }
        //特殊班次工作时长
        if (shiftDef != null && shiftDef.getIsSpecial() != null && shiftDef.getIsSpecial() && shiftDef.getSpecialWorkTime() != null
                && dayTime.getTimeDuration().equals(Float.valueOf(detail.getWorkTotalTime()))) {
            dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
        }
        return "";
    }

    /**
     * 时间单位为小时的整天
     *
     * @param dayTime
     * @return
     */
    public String processLeaveByPeriod4(WaLeaveType waLeaveType, WaWorktimeDetail detail, WaLeaveDaytime dayTime, WaShiftDef shiftDef) {//为小时的整天
        Boolean isSpecial = false;//特殊班次工时调整
        if (shiftDef.getIsSpecial() != null && shiftDef.getIsSpecial() && shiftDef.getSpecialWorkTime() != null) {
            isSpecial = true;
        }

        //缺少特殊日期
        if (detail.getDateType() == 1) {
            if (isSpecial) {
                dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
            } else {
                dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
            }
            dayTime.setStartTime(detail.getStartTime());
            dayTime.setEndTime(detail.getEndTime());
        } else if (detail.getDateType() == 2 && waLeaveType.getIsRestDay()) {//是休息日连续计算
            if (isSpecial) {
                dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
            } else {
                dayTime.setTimeDuration(8 * 60f);
            }
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        } else if ((detail.getDateType() == 3 || detail.getDateType() == 5) && waLeaveType.getIsLegalHoliday()) {//是法定节假日连续计算
            if (isSpecial) {
                dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
            } else {
                dayTime.setTimeDuration(8 * 60f);
            }
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        } else if (detail.getDateType() == 4 && waLeaveType.getIsRestDay()) {//是休息日连续计算
            if (isSpecial) {
                dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
            } else {
                dayTime.setTimeDuration(8 * 60f);
            }
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        }

        return "";
    }

    /**
     * @param dayTime
     * @param shalfDay
     * @param ehalfDay
     * @return
     */
    public String processLeaveByPeriod9(WaLeaveType waLeaveType, WaWorktimeDetail detail, WaLeaveDaytime dayTime, String shalfDay, String ehalfDay, long nowDate, long startDate, long endDate) {
        if (nowDate == startDate && nowDate == endDate) {//第一天也是最后一天
            if (shalfDay.equals("P")) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("P");
                dayTime.setEhalfDay("P");
            } else if (shalfDay.equals("A") && ehalfDay.equals("A")) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("A");
            } else {
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(1f);
            }
        } else if (nowDate == startDate && nowDate < endDate) {//第一天不是最后一天
            if (shalfDay.equals("A")) {
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(1f);
            } else {
                dayTime.setShalfDay("P");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(0.5f);
            }
        } else if (nowDate > startDate && nowDate < endDate) {//中间一天
            if (detail.getDateType() == 1) {
                dayTime.setTimeDuration(1f);
            } else if (detail.getDateType() == 2 && waLeaveType.getIsRestDay()) {//是休息日连续计算
                dayTime.setTimeDuration(1f);
            } else if ((detail.getDateType() == 3 || detail.getDateType() == 5) && waLeaveType.getIsLegalHoliday()) {//是法定节假日连续计算
                dayTime.setTimeDuration(1f);
            } else if (detail.getDateType() == 4 && waLeaveType.getIsRestDay()) {
                dayTime.setTimeDuration(1f);
            }
            dayTime.setShalfDay("A");
            dayTime.setEhalfDay("P");
        } else if (nowDate > startDate && nowDate == endDate) {
            if (ehalfDay.equals("P")) {
                dayTime.setTimeDuration(1f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
            } else if (ehalfDay.equals("A")) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("A");
            }
        }

        return "";
    }

    @Transactional
    public void deleteLeave(Integer leaveId) {
        if (leaveId != null) {
            //删除请假信息
            WaLeaveDaytimeExample daytimeExample = new WaLeaveDaytimeExample();
            WaLeaveDaytimeExample.Criteria daytimeCriteria = daytimeExample.createCriteria();
            daytimeCriteria.andLeaveIdEqualTo(leaveId);
            waLeaveDaytimeMapper.deleteByExample(daytimeExample);

            WaEmpLeaveTimeExample leaveTimeExample = new WaEmpLeaveTimeExample();
            WaEmpLeaveTimeExample.Criteria leaveTimeCriteria = leaveTimeExample.createCriteria();
            leaveTimeCriteria.andLeaveIdEqualTo(leaveId);
            waEmpLeaveTimeMapper.deleteByExample(leaveTimeExample);

            WaLeaveFileExample fileExample = new WaLeaveFileExample();
            WaLeaveFileExample.Criteria fileCriteria = fileExample.createCriteria();
            fileCriteria.andLeaveIdEqualTo(leaveId);
            waLeaveFileMapper.deleteByExample(fileExample);
            waEmpLeaveMapper.deleteByPrimaryKey(leaveId);
        }
    }

    @Transactional
    public void deleteOt(Integer otId) {
        if (otId != null) {
            WaEmpOvertimeDetailExample example = new WaEmpOvertimeDetailExample();
            WaEmpOvertimeDetailExample.Criteria criteria = example.createCriteria();
            criteria.andOvertimeIdEqualTo(otId);
            waEmpOvertimeDetailMapper.deleteByExample(example);
            WaOvertimeFileExample fileExample = new WaOvertimeFileExample();
            fileExample.createCriteria().andOtIdEqualTo(otId);
            waOvertimeFileMapper.deleteByExample(fileExample);
            waEmpOvertimeMapper.deleteByPrimaryKey(otId);
        }
    }

    public List getApprovalList(Long empid, Integer approvalType, Integer applyType, Integer limit, Long lastCrttime) {
        if (approvalType.intValue() == 1) {//查看待审批
            Map mapParm = new HashMap();
//				mapParm.put("approvalType", 1);
            mapParm.put("empId1", empid);
            mapParm.put("empId2", empid);
            mapParm.put("empId3", empid);
            mapParm.put("empId4", empid);
            mapParm.put("empId5", empid);
            mapParm.put("leaveempId1", empid);
            mapParm.put("leaveempId2", empid);
            mapParm.put("leaveempId3", empid);
            mapParm.put("leaveempId4", empid);
            mapParm.put("leaveempId5", empid);
            mapParm.put("status", 1);
            mapParm.put("leavestatus", 1);
            if (lastCrttime != null && lastCrttime > 0) {
                mapParm.put("lastCrttime", lastCrttime);
            }
            if (limit == null || limit == 0) {
                limit = 10;
            }
            MyPageBounds pageBounds = new MyPageBounds(0,
                    limit, Order.formString("id.desc"));
            if (applyType == null || applyType.intValue() == 0) {//全部
                return waApprovalMapper.getProvalOrderList(pageBounds, mapParm);
            } else if (applyType == 1) {//请假
                return waApprovalMapper.getProvalLeaveList(pageBounds, mapParm);
            } else if (applyType == 2) {//加班
                return waApprovalMapper.getProvalOtList(pageBounds, mapParm);
            } else if (applyType == 3) {//离职
                return null;
            }
        } else if (approvalType.intValue() == 3 || approvalType.intValue() == 2) {//已批准
            if (applyType == null || applyType.intValue() == 0) {//全部
                Map mapParm = new HashMap();
                mapParm.put("empId1", empid);
                mapParm.put("leaveempId1", empid);
                mapParm.put("status", approvalType);
                mapParm.put("leavestatus", approvalType);
                MyPageBounds pageBounds = new MyPageBounds(0,
                        limit, Order.formString("id.desc"));
                return waApprovalMapper.getProvaledOrderList(pageBounds, mapParm);
            } else if (applyType == 1) {//请假
                Map mapParm = new HashMap();
                mapParm.put("leaveempId1", empid);
                mapParm.put("approvalType1", approvalType);
                MyPageBounds pageBounds = new MyPageBounds(0,
                        limit, Order.formString("id.desc"));
                return waApprovalMapper.getProvaledLeaveList(pageBounds, mapParm);
            } else if (applyType == 2) {//加班
                Map mapParm = new HashMap();
                mapParm.put("empId1", empid);
                mapParm.put("approvalType1", approvalType);
                MyPageBounds pageBounds = new MyPageBounds(0,
                        limit, Order.formString("id.desc"));
                return waApprovalMapper.getProvaledOtList(pageBounds, mapParm);
            } else if (applyType == 3) {//离职
                return null;
            }

        }

        return null;
    }

//    /**
//     * 审批人审批查询列表
//     *
//     * @param pageBean
//     * @param empId        员工ID
//     * @param belongId
//     * @param approvalType
//     * @return
//     */
//    public List<Map> getWfApprovalList(PageBean pageBean, Integer userId, Long empId, String belongId, Integer approvalType, String keyword, String funcType) {
//        if (approvalType.intValue() == 1) {
//            return workflowService.getTodoList(pageBean, userId, empId, belongId, funcType, keyword);
//        } else if (approvalType.intValue() == 2) {
//            return workflowService.getOwnerTaskList(belongId, pageBean, empId, "yes", funcType, keyword);
//        } else if (approvalType.intValue() == 3) {
//            return workflowService.getOwnerTaskList(belongId, pageBean, empId, "no", funcType, keyword);
//        } else if (approvalType.intValue() == 99) {
//            return workflowService.getOwnerTaskList(belongId, pageBean, empId, "yesno", funcType, keyword);
//        }
//        return null;
//    }

    /**
     * 单个审批加班单
     *
     * @param otId
     * @param choice
     * @param comment
     * @throws Exception
     */
    @Transactional
    public void saveWfOtApproval(Integer otId, String choice, String comment) throws Exception {
        if ("revoke".equals(choice)) {
            //超时作废
            invalidEmpOt(otId);
            log.info("saveWfOtApproval撤销回掉");
            return;
        }
        WaEmpOvertime ot = new WaEmpOvertime();
        ot.setOtId(otId);
        if ("yes".equals(choice)) {
            ot.setStatus((short) 2);
            WaEmpOvertime ot2 = waEmpOvertimeMapper.selectByPrimaryKey(otId);
            //加班转调休配额
            this.addOtQuota(ot2);
        } else if ("no".equals(choice)) {
            ot.setStatus((short) 3);
            this.updatePreOtStatus(ot.getPreOtId(), 0);
        } else if ("back".equals(choice)) {
            ot.setStatus((short) 5);  //已退回
            this.updatePreOtStatus(ot.getPreOtId(), 0);
        }
        ot.setLastApprovalTime(DateUtil.getCurrentTime(true));
        waEmpOvertimeMapper.updateByPrimaryKeySelective(ot);

        log.info("saveWfOtApproval over");
    }

    @Transactional
    public void invalidEmpOt(Integer otId) {
        log.info("start invalidEmpOt callback....");
        WaEmpOvertime ot = waEmpOvertimeMapper.selectByPrimaryKey(otId);
        if (ot != null) {
            //只有审批中的单据才有超时作废
            if (ot.getStatus().intValue() == LeaveStatusEnum.LEAVE_STATUS_1.value) {
                //更新为作废状态
                WaEmpOvertime overtime = new WaEmpOvertime();
                overtime.setOtId(otId);
                overtime.setStatus(ApprovalStatusEnum.CANCELLATION.getIndex().shortValue());
                overtime.setUpdtime(DateUtil.getCurrentTime(true));
                overtime.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
                waEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
            }
        }
        log.info("end invalidEmpOt callback....");
    }

    @Transactional
    public void savePreOverTimeApprovalWorkFlow(Integer otId, String choice, String comment) throws Exception {
        PreEmpOvertime ot = new PreEmpOvertime();
        ot.setPreOtId(otId);
        if ("yes".equals(choice)) {
            ot.setStatus((short) 2);
        } else if ("no".equals(choice)) {
            ot.setStatus((short) 3);
        } else if ("back".equals(choice)) {
            ot.setStatus((short) 5);  //已退回
        }
        ot.setUpdtime(DateUtil.getCurrentTime(true));
        preEmpOvertimeMapper.updateByPrimaryKeySelective(ot);
    }

    /**
     * 单个审批加班单
     *
     * @param approvalEmpid  审批人员empid
     * @param approvalStatus 同意／拒绝  1/2
     * @param opinion        审批意见
     * @throws Exception
     */
    @Transactional
    public void saveOvertimeApproval(Long approvalEmpid, Integer overtimeId, Integer approvalStatus, String opinion, String belongId, Integer userid, SessionBean sessionBean) throws Exception {
        WaEmpOvertime ot = waEmpOvertimeMapper.selectByPrimaryKey(overtimeId);
        Integer snum = ot.getApprovalNum() + 1;

        WaEmpOvertime overtime = new WaEmpOvertime();
        overtime.setOtId(overtimeId);
        overtime.setApprovalNum(snum);
        overtime.setLastEmpid(approvalEmpid);

        WaApprovalFlow flow = new WaApprovalFlow();
        flow.setApprovalEmpid(approvalEmpid);
        flow.setRelFormId(overtimeId);
        flow.setApprovalType((short) 2);

        flow.setApprovalSortno(snum.shortValue());
        flow.setApprovalOpinion(opinion);
        flow.setApprovalTime(System.currentTimeMillis() / 1000);

        if (approvalStatus == 2) {//审批没有通过
            overtime.setStatus((short) 3);
            waEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
            flow.setStatus((short) 3);
            waApprovalFlowMapper.insertSelective(flow);

        } else if (approvalStatus == 1) {//审批通过
            //判断还有没有下级一审批
            flow.setStatus((short) 2);
            waApprovalFlowMapper.insertSelective(flow);
            if (snum == 2) {
                overtime.setStatus((short) 2);
                waEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
            } else {//判断是否还有下一个人审核
                WaApprovalChartExample chartExample = new WaApprovalChartExample();
                WaApprovalChartExample.Criteria chartCriteria = chartExample.createCriteria();
                //chartCriteria.andApplyEmpidEqualTo(ot.getEmpid());
                chartCriteria.andTrancationTypeEqualTo((short) 2);
                chartCriteria.andFirstApprovalEmpidEqualTo(approvalEmpid);
                List<WaApprovalChart> chartList1 = waApprovalChartMapper.selectByExample(chartExample);
                if (chartList1.size() > 0) {
                    WaApprovalChart waApprovalChart = chartList1.get(0);
                    if (waApprovalChart.getSecondApprovalEmpid() != null && waApprovalChart.getSecondApprovalEmpid() > 0) {
                        overtime.setFirstEmpid(approvalEmpid);
                        overtime.setStatus((short) 1);
                        waEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
                    } else {
                        //如果没有
                        overtime.setStatus((short) 2);
//							overtime.setFirstEmpid(approvalEmpid);
                        waEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
                    }
                } else {//说明是通过全部审核流程下来的
                    WaApprovalChartExample chartExample2 = new WaApprovalChartExample();
                    WaApprovalChartExample.Criteria chartCriteria2 = chartExample2.createCriteria();
                    //chartCriteria2.andApplyEmpidEqualTo(ot.getEmpid());
                    chartCriteria2.andTrancationTypeEqualTo((short) 0);
                    chartCriteria2.andFirstApprovalEmpidEqualTo(approvalEmpid);
                    List<WaApprovalChart> chartList2 = waApprovalChartMapper.selectByExample(chartExample2);
                    if (chartList2.size() > 0) {
                        WaApprovalChart waApprovalChart = chartList2.get(0);
                        if (waApprovalChart.getSecondApprovalEmpid() != null && waApprovalChart.getSecondApprovalEmpid() > 0) {
                            overtime.setFirstEmpid(approvalEmpid);
                            overtime.setStatus((short) 1);
                            waEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
                        } else {
                            //如果没有
                            overtime.setStatus((short) 2);
//								overtime.setFirstEmpid(approvalEmpid);
                            waEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
                        }
                    } else {
//                        throw new CDException("数据错误");
                        throw new CDException(messageResource.getMessage("L005701", new Object[]{}, new Locale(sessionBean.getLanguage())));
                    }
                }
            }
            if (overtime.getStatus() == 2) {//加上休假额度
                //加入wa_leave_setting
                WaLeaveSettingExample settingExample = new WaLeaveSettingExample();
                WaLeaveSettingExample.Criteria settingCriteria = settingExample.createCriteria();
                settingCriteria.andLeaveTypeEqualTo((short) 4);
                settingCriteria.andBelongOrgidEqualTo(ConvertHelper.stringConvert(belongId));
                List<WaLeaveSetting> settingList = waLeaveSettingMapper.selectByExample(settingExample);
                WaLeaveSetting waLeaveSetting = null;
                if (settingList.size() > 0) {
                    waLeaveSetting = settingList.get(0);
                } else {
                    waLeaveSetting = new WaLeaveSetting();
                    waLeaveSetting.setBelongOrgid(ConvertHelper.stringConvert(belongId));
                    waLeaveSetting.setLeaveType((short) 4);
                    waLeaveSetting.setStartDate(0l);
                    waLeaveSetting.setEndDate(DateUtil.getTimesampByDateStr("9999.12.31"));
                    waLeaveSetting.setCarryOverType(1);
                    waLeaveSetting.setCarryOverTimeNum(0);
                    waLeaveSetting.setCarryOverTimeUnit(1);
                    waLeaveSetting.setQuotaPeriodType(3);
                    waLeaveSetting.setCrtuser(ConvertHelper.longConvert(userid));
                    waLeaveSetting.setCrttime(System.currentTimeMillis() / 1000);
                    waLeaveSettingMapper.insertSelective(waLeaveSetting);
                }
                WaEmpQuotaExample quotaExample = new WaEmpQuotaExample();
                WaEmpQuotaExample.Criteria quotaCriteria = quotaExample.createCriteria();
                quotaCriteria.andEmpidEqualTo(ot.getEmpid());
                quotaCriteria.andQuotaSettingIdEqualTo(waLeaveSetting.getQuotaSettingId());
                quotaCriteria.andStartDateLessThanOrEqualTo(System.currentTimeMillis() / 1000);
                quotaCriteria.andLastDateGreaterThanOrEqualTo(System.currentTimeMillis() / 1000);
                List<WaEmpQuota> empQuotaList = waEmpQuotaMapper.selectByExample(quotaExample);
                //取得比例
                BigDecimal otNum = this.getOtNumByOtid(overtimeId, ot.getEmpid());
                WaEmpQuota empQuota = null;
                if (empQuotaList.size() > 0) {
                    empQuota = empQuotaList.get(0);
                    BigDecimal quotaDay = new BigDecimal(empQuota.getQuotaDay());
                    quotaDay = quotaDay.add(otNum);
                    WaEmpQuota empQuota2 = new WaEmpQuota();
                    empQuota2.setEmpQuotaId(empQuota.getEmpQuotaId());
                    empQuota2.setQuotaDay(quotaDay.floatValue());
                    empQuota2.setNowQuota(new BigDecimal(empQuota.getNowQuota()).add(otNum).floatValue());
                    waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota2);
                } else {
                    empQuota = new WaEmpQuota();
                    empQuota.setQuotaSettingId(waLeaveSetting.getQuotaSettingId());
                    empQuota.setEmpid(ot.getEmpid());
                    empQuota.setStartDate(0L);
                    empQuota.setLastDate(DateUtil.getTimesampByDateStr("9999.12.31"));
                    empQuota.setRemainDay(0f);
                    empQuota.setDeductionDay(0f);
                    empQuota.setQuotaDay(otNum.floatValue());
                    empQuota.setNowQuota(otNum.floatValue());
                    empQuota.setUsedDay(0f);
                    empQuota.setPeriodYear((short) 1);
                    waEmpQuotaMapper.insertSelective(empQuota);
                }
            }

        } else {
//            throw new CDException("审批状态参数错误");
            throw new CDException(messageResource.getMessage("L005666", new Object[]{}, new Locale(sessionBean.getLanguage())));
        }

    }

    public BigDecimal getOtNumByOtid(Integer otId, Long empid) {
        Map<String, Object> params = new HashMap<>();
        params.put("empid", empid);
        params.put("compensateTypeList", new ArrayList<>(Arrays.asList(2, 4)));
        List<WaOvertimeType> typeList = waOvertimeTypeMapper.getEmpOtTypeList(params);
        if (CollectionUtils.isEmpty(typeList)) {
            return new BigDecimal(0);
        }
        Map<Integer, WaOvertimeType> typeMap = typeList.stream().collect(Collectors.toMap(WaOvertimeType::getDateType, Function.identity()));

        WaEmpOvertimeDetailExample example = new WaEmpOvertimeDetailExample();
        WaEmpOvertimeDetailExample.Criteria criteria = example.createCriteria();
        criteria.andOvertimeIdEqualTo(otId);
        List<WaEmpOvertimeDetail> detailList = waEmpOvertimeDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(detailList)) {
            return new BigDecimal(0);
        }
        BigDecimal totalTimeDuration = new BigDecimal(0);

        for (WaEmpOvertimeDetail detail : detailList) {
            Integer dateType = Integer.valueOf(detail.getDateType());

            WaOvertimeType overtimeType = typeMap.get(dateType);

            BigDecimal timeDuration = new BigDecimal(detail.getTimeDuration());
            if (overtimeType != null && overtimeType.getOffNum() != null && overtimeType.getOvertimeNum() != null) {
                totalTimeDuration = totalTimeDuration.add(timeDuration.multiply(new BigDecimal(overtimeType.getOffNum())).divide(new BigDecimal(overtimeType.getOvertimeNum()), 0, BigDecimal.ROUND_HALF_UP));
            }
        }
        return totalTimeDuration;
    }

    /**
     * 重新计算配额
     *
     * @param empid 员工ID
     * @throws Exception
     */
    @Transactional
    @Deprecated
    public void reCalLeaveQuota(Long empid, Integer year, Integer leaveTypeId, Integer leaveId) throws Exception {
        Map quotaCycleTime = waCommonService.getEmpQuotaCycleTime(leaveTypeId, empid, year);
        if (quotaCycleTime != null && !quotaCycleTime.isEmpty()) {
            Date endDate = (Date) quotaCycleTime.get("endDate");
            Date startDate = (Date) quotaCycleTime.get("startDate");
            List<WaEmpQuota> quotaList = this.getWaEmpQuota(leaveTypeId, empid, "asc");
            if (quotaList != null || quotaList.size() > 0) {
                //初始化使用配额
                for (WaEmpQuota empQuota : quotaList) {
                    empQuota.setRemainUsedDay(0f);
                    empQuota.setUsedDay(0f);
                    waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                }
                //查询请假数据
                WaEmpLeaveExample example = new WaEmpLeaveExample();
                example.createCriteria().andStatusEqualTo(LeaveStatusEnum.LEAVE_STATUS_2.value).
                        andLeaveIdNotEqualTo(leaveId).
                        andEmpidEqualTo(empid).
                        andLeaveTypeIdEqualTo(leaveTypeId).
                        andStartDateBetween(DateFormatUtils.format(startDate, "yyyy-MM-dd"),
                                DateFormatUtils.format(endDate, "yyyy-MM-dd"));
                List<WaEmpLeave> empLeaveList = waEmpLeaveMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(empLeaveList)) {
                    for (WaEmpLeave empLeave : empLeaveList) {
                        WaEmpLeaveTimeExample timeExample = new WaEmpLeaveTimeExample();
                        WaEmpLeaveTimeExample.Criteria timeCriteria = timeExample.createCriteria();
                        timeCriteria.andLeaveIdEqualTo(empLeave.getLeaveId());
                        timeExample.setOrderByClause("start_time desc");
                        List<WaEmpLeaveTime> timeList = waEmpLeaveTimeMapper.selectByExample(timeExample);
                        for (WaEmpLeaveTime leaveTime : timeList) {
                            this.processQuotaSigleNew(quotaList, BigDecimal.valueOf(leaveTime.getTimeDuration()), leaveTime.getStartTime(), leaveTime.getTimeUnit(), true);
                        }
                    }
                }
            }
        }
    }

    /**
     * 撤销销假单
     *
     * @param cancel
     * @param reason
     */
    /*public void revokeLeaveCancel(WaLeaveCancel cancel, String reason) {
        //只撤销审批中和审批通过的
        if (LeaveStatusEnum.LEAVE_STATUS_1.value.equals(cancel.getStatus())) {
            try {
                workflowService.stopTaskById(cancel.getLeaveCancelId().toString(), 104, cancel.getTenantId(), cancel.getCreateBy().intValue());
            } catch (Exception e) {
                log.error("type is leave cancel end revokeLeaveCancel stopTaskById error msg:" + e.getMessage());
            }
            //撤销审批消息数据
            Map wfConfig = workflowService.getWfConfigByType(cancel.getTenantId(), 104);
            if (wfConfig != null) {
                WfEmpNoticeExample empNoticeExample = new WfEmpNoticeExample();
                empNoticeExample.createCriteria().andBusinesskeyEqualTo(String.valueOf(cancel.getLeaveCancelId())).andWfFuncIdEqualTo((Integer) wfConfig.get("wf_func_id")).
                        andEmpidEqualTo(cancel.getEmpid());
                WfEmpNotice empNotice = new WfEmpNotice();
                empNotice.setStatus(3);//撤销
                empNotice.setUpduser(cancel.getCreateBy().intValue());
                empNotice.setUpdtime(DateUtil.getCurrentTime(Boolean.TRUE));
                empNoticeMapper.updateByExampleSelective(empNotice, empNoticeExample);
            }
            cancel.setStatus(ApprovalStatusEnum.REVOKED.getIndex().shortValue());
            cancel.setRevokeReason(reason);
            cancel.setUpdateTime(DateUtil.getCurrentTime(true));
            leaveCancelMapper.updateByPrimaryKeySelective(cancel);
        }

        if (LeaveStatusEnum.LEAVE_STATUS_2.value.equals(cancel.getStatus())) {
            cancel.setStatus(ApprovalStatusEnum.REVOKED.getIndex().shortValue());
            cancel.setRevokeReason(reason);
            cancel.setUpdateTime(DateUtil.getCurrentTime(true));
            leaveCancelMapper.updateByPrimaryKeySelective(cancel);

            WaEmpLeave updLeave = waEmpLeaveMapper.selectByPrimaryKey(cancel.getLeaveId());
            updLeave.setCancelTimeDuration(updLeave.getCancelTimeDuration() - cancel.getTimeDuration());
            waEmpLeaveMapper.updateByPrimaryKeySelective(updLeave);

            WaEmpLeaveTime leaveTime = null;
            WaEmpLeaveTimeExample timeExample = new WaEmpLeaveTimeExample();
            WaEmpLeaveTimeExample.Criteria empLeaveTimeCri = timeExample.createCriteria();
            empLeaveTimeCri.andLeaveIdEqualTo(updLeave.getLeaveId());
            List<WaEmpLeaveTime> leaveTimeList = waEmpLeaveTimeMapper.selectByExample(timeExample);
            if (CollectionUtils.isNotEmpty(leaveTimeList)) {
                leaveTime = leaveTimeList.get(0);
            }
            leaveTime.setCancelTimeDuration(leaveTime.getCancelTimeDuration() - cancel.getTimeDuration());
            waEmpLeaveTimeMapper.updateByPrimaryKeySelective(leaveTime);

            //额度重新扣减
            WaLeaveType leaveType = waLeaveTypeMapper.selectByPrimaryKey(updLeave.getLeaveTypeId());
            List<WaLeaveCancelDayTime> list = leaveCancelDayTimeMapper.selectListByCancelId(cancel.getLeaveCancelId());
            if (CollectionUtils.isNotEmpty(list)) {
                for (WaLeaveCancelDayTime daytime : list) {
                    WaLeaveDaytimeExample example = new WaLeaveDaytimeExample();
                    WaLeaveDaytimeExample.Criteria criteria = example.createCriteria();
                    criteria.andLeaveIdEqualTo(updLeave.getLeaveId());
                    criteria.andLeaveDateEqualTo(daytime.getLeaveCancelDate());
                    List<WaLeaveDaytime> daytimeList = leaveDaytimeMapper.selectByExample(example);
                    WaLeaveDaytime waLeaveDaytime = daytimeList.get(0);
                    waLeaveDaytime.setCancelTimeDuration(waLeaveDaytime.getCancelTimeDuration() - daytime.getTimeDuration());
                    leaveDaytimeMapper.updateByPrimaryKeySelective(waLeaveDaytime);

                    Float cancelTimeDuration = daytime.getTimeDuration();
                    if (leaveType.getQuotaRestrictionType() != null && leaveType.getQuotaRestrictionType() == 1) {
                        //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
                        Integer quotaType = leaveType.getQuotaType();
                        if (quotaType == 2) {
                            List<WaCompensatoryQuotaUse> quotaUseList = waCompensatoryQuotaUseMapper.selectOrderBy(updLeave.getEmpid(), waLeaveDaytime.getLeaveDaytimeId());
                            for (WaCompensatoryQuotaUse quotaUse : quotaUseList) {
                                Float oldCancelTimeDuration = quotaUse.getCancelTimeDuration();
                                if (cancelTimeDuration > oldCancelTimeDuration) {
                                    cancelTimeDuration = cancelTimeDuration - oldCancelTimeDuration;
                                    WaCompensatoryQuotaUse updUse = new WaCompensatoryQuotaUse();
                                    updUse.setUseId(quotaUse.getUseId());
                                    updUse.setCancelTimeDuration(0f);
                                    updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                    waCompensatoryQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                } else {
                                    WaCompensatoryQuotaUse updUse = new WaCompensatoryQuotaUse();
                                    updUse.setUseId(quotaUse.getUseId());
                                    updUse.setCancelTimeDuration(oldCancelTimeDuration - cancelTimeDuration);
                                    updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                    waCompensatoryQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                    break;
                                }
                            }
                            Long userId = SessionHolder.getUserId();
                            long updateTime = System.currentTimeMillis() / 1000;
                            List<Integer> idList = new ArrayList<>();
                            idList.add(waLeaveDaytime.getLeaveDaytimeId());
                            waEmpCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(ConvertHelper.longConvert(userId), updateTime, idList, ApprovalStatusEnum.PASSED.getIndex());
                        } else {
                            List<WaLeaveQuotaUse> quotaUseList = waLeaveQuotaUseMapper.selectOrderBy(updLeave.getEmpid(), waLeaveDaytime.getLeaveDaytimeId());
                            for (WaLeaveQuotaUse quotaUse : quotaUseList) {
                                Float oldCancelTimeDuration = quotaUse.getCancelTimeDuration();
                                if (cancelTimeDuration > oldCancelTimeDuration) {
                                    cancelTimeDuration = cancelTimeDuration - oldCancelTimeDuration;
                                    WaLeaveQuotaUse updUse = new WaLeaveQuotaUse();
                                    updUse.setCancelTimeDuration(0f);
                                    updUse.setUpdateBy(Long.valueOf(SessionHolder.getUserId()));
                                    updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                    waLeaveQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                } else {
                                    WaLeaveQuotaUse updUse = new WaLeaveQuotaUse();
                                    updUse.setUseId(quotaUse.getUseId());
                                    updUse.setCancelTimeDuration(oldCancelTimeDuration - cancelTimeDuration);
                                    updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                    waLeaveQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                    break;
                                }
                            }
                            Long userId = SessionHolder.getUserId();
                            long updateTime = System.currentTimeMillis() / 1000;
                            List<Integer> idList = new ArrayList<>();
                            idList.add(waLeaveDaytime.getLeaveDaytimeId());
                            waLeaveQuotaUseMapper.updateWaEmpQuota(ConvertHelper.longConvert(userId), updateTime, idList, ApprovalStatusEnum.PASSED.getIndex());
                        }
                    }
                }
            }
        }
    }*/

    /**
     * 撤销休假单
     *
     * @param cancel
     */
//    public void revokeLeave(WaLeaveCancel cancel) {
//        WaEmpLeave empLeave = waEmpLeaveMapper.selectByPrimaryKey(cancel.getLeaveId());
//        // 审批中的请假单需要走审批流的撤销
//        if (empLeave.getStatus().intValue() == LeaveStatusEnum.LEAVE_STATUS_1.value) {
//            //撤销审批流
//            try {
//                workflowService.stopTaskById(cancel.getLeaveId().toString(), BaseConst.WF_FUNC_TYPE_1, cancel.getTenantId().intValue(), cancel.getCreateBy().intValue());
//            } catch (Exception e) {
//                log.error("type is leave cancel end revokeEmpLeave stopTaskById error msg:" + e.getMessage());
//            }
//            /*//撤销审批消息数据
//            Map wfConfig = workflowService.getWfConfigByType(cancel.getTenantId().intValue(), BaseConst.WF_FUNC_TYPE_1);
//            if (wfConfig != null) {
//                WfEmpNoticeExample empNoticeExample = new WfEmpNoticeExample();
//                empNoticeExample.createCriteria().andBusinesskeyEqualTo(String.valueOf(cancel.getLeaveId())).andWfFuncIdEqualTo((Integer) wfConfig.get("wf_func_id")).
//                        andEmpidEqualTo(empLeave.getEmpid());
//                WfEmpNotice empNotice = new WfEmpNotice();
//                empNotice.setStatus(3);//撤销
//                empNotice.setUpduser(cancel.getCreateBy().intValue());
//                empNotice.setUpdtime(DateUtil.getCurrentTime(Boolean.TRUE));
//                empNoticeMapper.updateByExampleSelective(empNotice, empNoticeExample);
//            }*/
//        }
//        empLeave.setStatus(ApprovalStatusEnum.REVOKED.getIndex().shortValue());
//        empLeave.setLeaveStatus(LeaveCancelStatusEnum.LEAVE_CANCEL_PASSED.getIndex());
//        empLeave.setRevokeReason(cancel.getReason());
//        waEmpLeaveMapper.updateByPrimaryKeySelective(empLeave);
//
//        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(empLeave.getLeaveTypeId());
//        //如果假期类型为限额，则去更新配额使用明细表的数据状态和配额表的在途配额
//        if (waLeaveType.getQuotaRestrictionType() != null && waLeaveType.getQuotaRestrictionType() == 1) {
//            //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
//            Integer quotaType = waLeaveType.getQuotaType();
//            if (quotaType == null) {
//                //2假期类型为调休
//                quotaType = waLeaveType.getLeaveType() == 3 ? 2 : 1;
//            }
//            if (quotaType == 2) {
//                waCommonService.updateEmpCompensatoryQuotaApprovalResult(cancel.getCreateBy(), empLeave);
//            } else {
//                waCommonService.updateWaLeaveQuotaUse(cancel.getCreateBy(), empLeave);
//            }
//        }
//    }

    /**
     * 请假审批回掉
     *
     * @param leaveId
     * @param choice
     * @param comment
     * @throws Exception
     */
    @Transactional
    public void saveWfLeaveApproval(Integer leaveId, String choice, String comment) throws Exception {
        if ("revoke".equals(choice)) {
            //超时作废
            invalidEmpLeave(leaveId);
            return;
        }
        WaEmpLeave waEmpLeave = waEmpLeaveMapper.selectByPrimaryKey(leaveId);
        if (waEmpLeave == null) {
            log.warn("mobileV16Service.saveWfLeaveApproval异常，请假单据系统不存在，leaveId=" + leaveId);
            return;
        }
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(waEmpLeave.getLeaveTypeId());
        if (waLeaveType == null) {
            log.warn("mobileV16Service.saveWfLeaveApproval异常，假期类型系统不存在，leaveTypeId=" + waEmpLeave.getLeaveTypeId());
            return;
        }
        WaEmpLeave waEmpLeaveUpd = new WaEmpLeave();
        waEmpLeaveUpd.setLeaveId(leaveId);
        if ("yes".equals(choice)) {
            waEmpLeaveUpd.setStatus((short) 2);
            //更新realDate
            Long realDate = DateUtil.getOnlyDate();
            waLeaveDaytimeMapper.updateRealDate(waEmpLeaveUpd.getLeaveId(), realDate);
        } else if ("no".equals(choice)) {
            waEmpLeaveUpd.setStatus((short) 3);
        } else if ("back".equals(choice)) {
            // 已退回
            waEmpLeaveUpd.setStatus((short) 5);
        } else if ("revoke".equals(choice)) {
            // 已作废
            waEmpLeaveUpd.setStatus((short) 4);
        }
        //自动销假审批通过直接为休假完成
        if (LeaveCancelSettingTypeEnum.AUTOMATIC.getIndex().equals(waLeaveType.getLeaveCancelType())) {
            waEmpLeaveUpd.setLeaveStatus(LeaveCancelStatusEnum.LEAVE_CANCEL_PASSED.getIndex());
        }
        waEmpLeaveUpd.setLastApprovalTime(System.currentTimeMillis() / 1000);
        waEmpLeaveUpd.setUpdtime(System.currentTimeMillis() / 1000);
        waEmpLeaveUpd.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
        waEmpLeaveMapper.updateByPrimaryKeySelective(waEmpLeaveUpd);
        waEmpLeave.setStatus(waEmpLeaveUpd.getStatus());
        if (waLeaveType.getLeaveType() != 4) {
            if (waLeaveType.getQuotaRestrictionType() != null
                    && QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType())
                    && waEmpLeave.getStatus() != null) {
                //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
                Integer quotaType = waLeaveType.getQuotaType();
                if (quotaType == null) {
                    if (waLeaveType.getLeaveType() == 3) {
                        //假期类型为调休
                        quotaType = 2;
                    } else {
                        quotaType = 1;
                    }
                }
                if (quotaType == 2) {
                    waCommonService.updateEmpCompensatoryQuotaApprovalResult(0L, waEmpLeave);
                } else {
                    waCommonService.updateWaLeaveQuotaUse(0L, waEmpLeave);
                }
            }
        }
    }

    @Transactional
    public void invalidEmpLeave(Integer leaveId) {
        log.info("start invalidEmpLeave callback....");
        String belongOrgId = SessionHolder.getBelongOrgId();
        Long userId = SessionHolder.getUserId();
        WaEmpLeave empLeave = waEmpLeaveMapper.selectByPrimaryKey(leaveId);
        if (empLeave != null) {
            //审批中的单据才有超时作废
            if (empLeave.getStatus().intValue() == LeaveStatusEnum.LEAVE_STATUS_1.value) {
                WaEmpLeave leave = new WaEmpLeave();
                //更新为作废状态
                leave.setLeaveId(leaveId);
                leave.setStatus(ApprovalStatusEnum.CANCELLATION.getIndex().shortValue());
                leave.setUpdtime(DateUtil.getCurrentTime(true));
                waEmpLeaveMapper.updateByPrimaryKeySelective(leave);
                empLeave.setStatus(ApprovalStatusEnum.CANCELLATION.getIndex().shortValue());
                // 判断休假类型是否存在
                WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(empLeave.getLeaveTypeId());
                if (waLeaveType != null && waLeaveType.getLeaveType() != 4) {
                    //如果假期类型为限额，则去更新配额使用明细表的数据状态和配额表的在途配额
                    if (waLeaveType.getQuotaRestrictionType() != null && QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType())) {
                        //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
                        Integer quotaType = waLeaveType.getQuotaType();
                        if (quotaType == null) {
                            //2假期类型为调休
                            quotaType = waLeaveType.getLeaveType() == 3 ? 2 : 1;
                        }
                        if (quotaType == 2) {
                            waCommonService.updateEmpCompensatoryQuotaApprovalResult(ConvertHelper.longConvert(userId), empLeave);
                        } else {
                            waCommonService.updateWaLeaveQuotaUse(ConvertHelper.longConvert(userId), empLeave);
                        }
                    }
                }
            }
        }
        log.info("end invalidEmpLeave callback....");
    }

    /**
     * 单个审批请假单
     *
     * @param approvalEmpid  当前审批人empid
     * @param leaveId        请假单
     * @param approvalStatus 同意／拒绝  1/0
     * @param opinion
     */
    /*@Transactional
    public void saveLeaveApproval(Integer approvalEmpid, Integer leaveId, Integer approvalStatus, String opinion, SessionBean sessionBean) throws Exception {
        WaEmpLeave waEmpLeaveP = waEmpLeaveMapper.selectByPrimaryKey(leaveId);
        WaEmpLeave waEmpLeave = new WaEmpLeave();
        waEmpLeave.setLeaveId(leaveId);
        Integer snum = waEmpLeaveP.getApprovalNum() + 1;
        waEmpLeave.setApprovalNum(snum.shortValue());
        waEmpLeave.setLastEmpid(approvalEmpid);

        WaApprovalFlow flow = new WaApprovalFlow();
        flow.setApprovalEmpid(approvalEmpid);
        flow.setRelFormId(leaveId);
        flow.setApprovalType((short) 1);

        flow.setApprovalSortno(snum.shortValue());
        flow.setApprovalOpinion(opinion);
        flow.setApprovalTime(System.currentTimeMillis() / 1000);

        if (approvalStatus == 2) {//审批没有通过
            waEmpLeave.setStatus((short) 3);
            waEmpLeave.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
            waEmpLeave.setUpdtime(System.currentTimeMillis() / 1000);
            waEmpLeaveMapper.updateByPrimaryKeySelective(waEmpLeave);
            flow.setStatus((short) 3);
            waApprovalFlowMapper.insertSelective(flow);
            //如果是年假，需要返回额度信息
            WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(waEmpLeaveP.getLeaveTypeId());
            BigDecimal usedDayNow = new BigDecimal(waEmpLeaveP.getTotalTimeDuration());
            if (waLeaveType.getLeaveType().intValue() == 1) {//是年假
                Map quoMap = new HashMap();
                quoMap.put("empId", waEmpLeaveP.getEmpid());
                quoMap.put("leaveType", 1);//法定年假
                quoMap.put("nowDate", System.currentTimeMillis() / 1000);
                List<WaEmpQuota> quoList1 = waEmpQuotaMapper.getEmpQuota(quoMap);
                quoMap.put("leaveType", 2);//福利年假
                List<WaEmpQuota> quoList2 = waEmpQuotaMapper.getEmpQuota(quoMap);
                //------------处理福利年假配额
                WaEmpQuota quota2 = quoList2.get(0);//福利年假额度
                WaEmpQuota quota2New = new WaEmpQuota();
                quota2New.setEmpQuotaId(quota2.getEmpQuotaId());
                BigDecimal usedDay2 = new BigDecimal(quota2.getUsedDay());
                if (quota2.getUsedDay().floatValue() > 0) {//说明请了福利年假，需要先抵扣福利年假
                    if (usedDay2.floatValue() >= usedDayNow.floatValue()) {//福利年假可以抵扣，不要抵扣法定年假
                        quota2New.setUsedDay(usedDay2.subtract(usedDayNow).floatValue());
                        waEmpQuotaMapper.updateByPrimaryKeySelective(quota2New);
                    } else {
                        quota2New.setUsedDay(0f);
                        waEmpQuotaMapper.updateByPrimaryKeySelective(quota2New);
                        WaEmpQuota quota1 = quoList1.get(0);//法定年假额度
                        BigDecimal usedDay1 = new BigDecimal(quota1.getUsedDay());
                        WaEmpQuota quota1New = new WaEmpQuota();
                        quota1New.setUsedDay(usedDay1.subtract(usedDayNow.subtract(usedDay2)).floatValue());
                        waEmpQuotaMapper.updateByPrimaryKeySelective(quota1New);
                    }
                } else {//说明没有请福利年假，只扣除了法定年假
                    WaEmpQuota quota1 = quoList1.get(0);//法定年假额度
                    BigDecimal usedDay1 = new BigDecimal(quota1.getUsedDay());

                    WaEmpQuota quota1New = new WaEmpQuota();
                    quota1New.setEmpQuotaId(quota1.getEmpQuotaId());
                    quota1New.setUsedDay(usedDay1.subtract(usedDayNow).floatValue());
                    waEmpQuotaMapper.updateByPrimaryKeySelective(quota1New);
                }
            } else if (waLeaveType.getLeaveType().intValue() == 3) {//调休
//                Map quoMap = new HashMap();
//                quoMap.put("empId", waEmpLeaveP.getEmpid());
//                quoMap.put("leaveType", 4);//调休
//                quoMap.put("nowDate", System.currentTimeMillis() / 1000);
//                List<WaEmpQuota> quoList3 = waEmpQuotaMapper.getEmpQuota(quoMap);
//                if (quoList3.size() > 0) {
//                    WaEmpQuota quota3 = quoList3.get(0);
//                    WaEmpQuota quota3New = new WaEmpQuota();
//                    quota3New.setEmpQuotaId(quota3.getEmpQuotaId());
//                    BigDecimal usedDay3 = new BigDecimal(quota3.getUsedDay());
//                    quota3New.setUsedDay(usedDay3.subtract(usedDayNow).floatValue());
//                    waEmpQuotaMapper.updateByPrimaryKeySelective(quota3New);
//                }
                //重新计算配额
                Integer year = Calendar.getInstance().get(Calendar.YEAR);
                //this.reCalLeaveQuota(waEmpLeaveP.getEmpid(), year, waEmpLeaveP.getLeaveTypeId(), waEmpLeaveP.getLeaveId());
            }

        } else if (approvalStatus == 1) {//审批通过
            //判断还有没有下级一审批
            flow.setStatus((short) 2);
            waApprovalFlowMapper.insertSelective(flow);
            if (snum == 2) {
                waEmpLeave.setStatus((short) 2);
                waEmpLeave.setLastApprovalTime(System.currentTimeMillis() / 1000);
                waEmpLeave.setUpdtime(System.currentTimeMillis() / 1000);
                waEmpLeave.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
                waEmpLeaveMapper.updateByPrimaryKeySelective(waEmpLeave);
            } else {//判断是否还有下一个人审核
                //如果有
                WaApprovalChartExample chartExample = new WaApprovalChartExample();
                WaApprovalChartExample.Criteria chartCriteria = chartExample.createCriteria();
                //chartCriteria.andApplyEmpidEqualTo(waEmpLeaveP.getEmpid());
                chartCriteria.andTrancationTypeEqualTo((short) 1);
                chartCriteria.andFirstApprovalEmpidEqualTo(approvalEmpid);
                List<WaApprovalChart> chartList1 = waApprovalChartMapper.selectByExample(chartExample);
                if (chartList1.size() > 0) {
                    WaApprovalChart waApprovalChart = chartList1.get(0);
                    if (waApprovalChart.getSecondApprovalEmpid() != null && waApprovalChart.getSecondApprovalEmpid() > 0) {
                        waEmpLeave.setStatus((short) 1);
                        waEmpLeave.setFirstEmpid(approvalEmpid);
                        waEmpLeave.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
                        waEmpLeave.setUpdtime(System.currentTimeMillis() / 1000);
                        waEmpLeaveMapper.updateByPrimaryKeySelective(waEmpLeave);
                    } else {
                        //如果没有
                        waEmpLeave.setStatus((short) 2);
                        waEmpLeave.setLastApprovalTime(System.currentTimeMillis() / 1000);
                        waEmpLeave.setFirstEmpid(approvalEmpid);
                        waEmpLeave.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
                        waEmpLeave.setUpdtime(System.currentTimeMillis() / 1000);
                        waEmpLeaveMapper.updateByPrimaryKeySelective(waEmpLeave);
                    }
                } else {//说明是通过全部审核流程下来的
                    WaApprovalChartExample chartExample2 = new WaApprovalChartExample();
                    WaApprovalChartExample.Criteria chartCriteria2 = chartExample2.createCriteria();
                    //chartCriteria2.andApplyEmpidEqualTo(waEmpLeaveP.getEmpid());
                    chartCriteria2.andFirstApprovalEmpidEqualTo(approvalEmpid);
                    chartCriteria2.andTrancationTypeEqualTo((short) 0);
                    List<WaApprovalChart> chartList2 = waApprovalChartMapper.selectByExample(chartExample2);
                    if (chartList2.size() > 0) {
                        WaApprovalChart waApprovalChart = chartList2.get(0);
                        if (waApprovalChart.getSecondApprovalEmpid() != null && waApprovalChart.getSecondApprovalEmpid() > 0) {
                            waEmpLeave.setFirstEmpid(approvalEmpid);
                            waEmpLeave.setStatus((short) 1);
                            waEmpLeave.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
                            waEmpLeave.setUpdtime(System.currentTimeMillis() / 1000);
                            waEmpLeaveMapper.updateByPrimaryKeySelective(waEmpLeave);
                        } else {
                            //如果没有
                            waEmpLeave.setStatus((short) 2);
                            waEmpLeave.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
                            waEmpLeave.setUpdtime(System.currentTimeMillis() / 1000);
                            waEmpLeave.setLastApprovalTime(System.currentTimeMillis() / 1000);
                            waEmpLeaveMapper.updateByPrimaryKeySelective(waEmpLeave);
                        }
                    } else {
//                        throw new CDException("数据错误");
                        throw new CDException(messageResource.getMessage("L005701", new Object[]{}, new Locale(sessionBean.getLanguage())));
                    }
                }
            }

        } else {
//            throw new CDException("审批状态参数错误");
            throw new CDException(messageResource.getMessage("L005666", new Object[]{}, new Locale(sessionBean.getLanguage())));
        }
    }*/

    /**
     * 配额信息
     *
     * @param empid    员工ID
     * @param belongId
     * @return
     */
    public List getRemainDays(Long empid, String belongId, SessionBean sessionBean) {
        Map mapParm = new HashMap();
        mapParm.put("empId", empid);
        mapParm.put("nowTime", DateUtil.getOnlyDate());
        List<Map> list = waEmpQuotaMapper.getQuotaByMobEmpid(mapParm);
        List listResult = new ArrayList();

        NumberFormat nf = new DecimalFormat("#.####");
        try {
            for (Map map : list) {
                Map map2 = new HashMap();
                BigDecimal quotaDay = new BigDecimal((Float) map.get("quota_day"));
                BigDecimal usedDay = new BigDecimal((Float) map.get("used_day"));
                BigDecimal adjustQuota = new BigDecimal(map.get("adjust_quota") == null ? 0 : (Float) map.get("adjust_quota"));
                BigDecimal adjustUsedDay = map.get("adjust_used_day") == null ? new BigDecimal(0) : new BigDecimal((Float) map.get("adjust_used_day"));
                BigDecimal fixUsedDay = new BigDecimal(map.get("fix_used_day") == null ? 0f : (Float) map.get("fix_used_day"));
                BigDecimal deductionDay = map.get("deduction_day") == null ? new BigDecimal(0) : new BigDecimal((Float) map.get("deduction_day"));
                Integer acctTimeType = (Integer) map.get("acct_time_type");
                Boolean isEmpShow = (Boolean) map.get("is_emp_show");

                usedDay = usedDay.add(fixUsedDay).add(adjustUsedDay);
                if (isEmpShow) {
                    map2.put("typename", map.get("quota_setting_name"));
                    if (map.get("remain_valid_date") == null) {
                        BigDecimal totaltime = quotaDay.add(adjustQuota).subtract(deductionDay);
                        BigDecimal usedtime = new BigDecimal(usedDay.floatValue());
                        BigDecimal remaintime = totaltime.subtract(usedtime);

                        if (acctTimeType.intValue() == 2) {
                            remaintime = remaintime.divide(new BigDecimal(60), 1, RoundingMode.HALF_DOWN);
                            totaltime = totaltime.divide(new BigDecimal(60), 1, RoundingMode.HALF_DOWN);
                            usedtime = usedtime.divide(new BigDecimal(60), 1, RoundingMode.HALF_DOWN);
                        }
                        map2.put("totaltime", nf.format(totaltime));
                        map2.put("remaintime", nf.format(remaintime));
                        map2.put("surplus", nf.format(usedtime));
                    } else {
                        BigDecimal remainDay = new BigDecimal((Float) map.get("remain_day"));
                        BigDecimal kyRemain = null;//可用留成数
                        if (map.get("remain_used_day") == null) {
                            kyRemain = remainDay.subtract(new BigDecimal(0));
                        } else {
                            kyRemain = remainDay.subtract(new BigDecimal((Float) map.get("remain_used_day")));
                        }
                        long remainValidDate = (Long) map.get("remain_valid_date");
                        Long invLc = (remainValidDate - DateUtil.getOnlyDate(new Date())) / (24 * 60 * 60);
                        if (invLc < 0) {
                            kyRemain = new BigDecimal(0);
                        }
                        BigDecimal totaltime = quotaDay.add(kyRemain).add(adjustQuota).subtract(deductionDay);
                        BigDecimal usedtime = new BigDecimal(usedDay.floatValue());
                        BigDecimal remaintime = totaltime.subtract(usedtime);

                        if (acctTimeType.intValue() == 2) {
                            remaintime = remaintime.divide(new BigDecimal(60), 1, RoundingMode.HALF_DOWN);
                            totaltime = totaltime.divide(new BigDecimal(60), 1, RoundingMode.HALF_DOWN);
                            usedtime = usedtime.divide(new BigDecimal(60), 1, RoundingMode.HALF_DOWN);
                        }

                        map2.put("totaltime", nf.format(totaltime));
                        map2.put("remaintime", nf.format(remaintime.floatValue()));
                        map2.put("surplus", nf.format(usedtime));
                    }

                    if (acctTimeType.intValue() == 1) {
                        map2.put("timeunit", messageResource.getMessage("L005571", new Object[]{""}, new Locale(sessionBean.getLanguage())));
                    }
                    if (acctTimeType.intValue() == 2) {
                        map2.put("timeunit", messageResource.getMessage("L005573", new Object[]{""}, new Locale(sessionBean.getLanguage())));
                    }
                    listResult.add(map2);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listResult;
    }

    private String timeFormat(Integer timeUnit, Float timeDuration) {
        if (timeDuration == null) return "";
        NumberFormat nf = new DecimalFormat("#.##");
        if (timeUnit == 1) {
            return nf.format(timeDuration);
        } else if (timeUnit == 2) {
            BigDecimal b = new BigDecimal(timeDuration / 60);
            return nf.format(b);
        }
        return "";
    }

    /**
     * 手机端获取全部假期配额
     *
     * @param empId       员工ID
     * @param belongId
     * @param sessionBean
     * @return
     */
    public List<Object> getAllRemainsDay(Long empId, String belongId, SessionBean sessionBean) {
        /**
         app配额明细字段逻辑：
         冻结规则为：本年配额冻结
         冻结额度：当前配额-本年已使用（含固定已使用）
         剩余额度：（留存配额-留存已使用-留存已失效）+（调整配额-调整已使用-调整已失效）等同于 现在的剩余配额-冻结额度

         冻结规则为：试用期冻结
         冻结配额：（留存配额-留存已使用-留存已失效）+ （当前配额-本年已使用（含固定已使用））+（调整配额-调整已使用-调整已失效）等同于 现在的剩余额度
         剩余配额：（留存配额-留存已使用-留存已失效）+ （当前配额-本年已使用（含固定已使用））+（调整配额-调整已使用-调整已失效）- 冻结配额 等同于 0
         **/
        Map<String, Object> mParams = new HashMap<>();
        mParams.put("empId", empId);
        List<Map> mQueryList = empInfoMapper.getAllQuotaMyMobileEmpId(mParams);
        if (CollectionUtils.isNotEmpty(mQueryList)) {
            return mQueryList.stream().filter(item -> {
                return item.containsKey("is_emp_show") && (Boolean) item.get("is_emp_show");
            }).map(mItemObject -> {
                Map<String, Object> mResultItem = new HashMap<>();
                // 上年留存
                BigDecimal remainDay = mItemObject.get("remain_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("remain_day");
                // 上年留存已经使用
                BigDecimal remainUsedDay = mItemObject.get("remain_used_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("remain_used_day");
                // 上年留存已经失效
                BigDecimal remainInvalidDay = mItemObject.get("remain_invalid") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("remain_invalid");
                // 当年配额
                BigDecimal quotaDay = mItemObject.get("quota_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("quota_day");
                // 当年配额已经使用，需要加上固定已经使用
                BigDecimal quotaUsedDay = mItemObject.get("used_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("used_day");
                // 当前配额已经失效
                BigDecimal quotaInvalidDay = mItemObject.get("invalidDay") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("invalidDay");
                // 固定已经使用
                BigDecimal fixUsedDay = mItemObject.get("fix_used_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("fix_used_day");
                // 休假单位时间类型
                Integer timeType = mItemObject.get("acct_time_type") == null ? 1 : (Integer) mItemObject.get("acct_time_type");
                // 是否在员工端显示
                Boolean isEmpShow = (Boolean) mItemObject.get("is_emp_show");
                // 调整配额
                BigDecimal adjustDay = mItemObject.get("adjust_quota") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("adjust_quota");
                // 调整配额已使用
                BigDecimal adjustUsedDay = mItemObject.get("adjust_used_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("adjust_used_day");
                // 调整配额已失效
                BigDecimal adjustInvalid = mItemObject.get("adjustInvalid") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("adjustInvalid");
                // 当前配额
                BigDecimal nowQuotaDay = mItemObject.get("now_quota") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("now_quota");
                // 冻结额度
                BigDecimal freezeDay = mItemObject.get("freezeDay") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("freezeDay");
                // 当前额度扣除单位类型
                // 额度抵扣
                BigDecimal deductionDay = mItemObject.get("deduction_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("deduction_day");
                mResultItem.put("remainDay", timeType == 2 ? remainDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : remainDay);
                mResultItem.put("remainUsed", timeType == 2 ? remainUsedDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : remainUsedDay);
                mResultItem.put("remainInvalid", timeType == 2 ? remainInvalidDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : remainInvalidDay);
                mResultItem.put("quotaDay", timeType == 2 ? quotaDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : quotaDay);
                mResultItem.put("quotaUsed", timeType == 2 ? quotaUsedDay.add(fixUsedDay).divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : quotaUsedDay.add(fixUsedDay));
                mResultItem.put("quotaInvalid", timeType == 2 ? quotaInvalidDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : quotaInvalidDay);
                mResultItem.put("quotaFreeze", timeType == 2 ? freezeDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : freezeDay);
                mResultItem.put("adjustDay", timeType == 2 ? adjustDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : adjustDay);
                mResultItem.put("adjustUsed", timeType == 2 ? adjustUsedDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : adjustUsedDay);
                mResultItem.put("adjustInvalid", timeType == 2 ? adjustInvalid.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : adjustInvalid);
                mResultItem.put("deductionDay", timeType == 2 ? deductionDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : deductionDay);
                mResultItem.put("nowQuotaDay", timeType == 2 ? nowQuotaDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : nowQuotaDay);
                // 计算当前剩余时间 使用当前配额计算
                BigDecimal nowRemain =
                        remainDay.subtract(remainUsedDay).subtract(remainInvalidDay).add(nowQuotaDay).add(adjustDay)
                                .subtract(quotaUsedDay).subtract(quotaInvalidDay).subtract(adjustUsedDay)
                                .subtract(adjustInvalid).subtract(fixUsedDay).subtract(deductionDay).subtract(freezeDay);
                // 当前剩余
                BigDecimal curRemain =
                        remainDay.subtract(remainUsedDay).subtract(remainInvalidDay).add(quotaDay).add(adjustDay)
                                .subtract(quotaUsedDay).subtract(quotaInvalidDay).subtract(adjustUsedDay)
                                .subtract(adjustInvalid).subtract(fixUsedDay).subtract(deductionDay).subtract(freezeDay);

                if (nowRemain.floatValue() < 0) {
                    nowRemain = new BigDecimal(0);
                }

                if (curRemain.floatValue() < 0) {
                    curRemain = new BigDecimal(0);
                }

                mResultItem.put("leftQuotaDay", timeType == 2 ? nowRemain.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : nowRemain);

                mResultItem.put("nowRemain", timeType == 2 ? nowRemain.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : nowRemain);

                mResultItem.put("curRemain", timeType == 2 ? curRemain.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : curRemain);

                mResultItem.put("timeUnit", messageResource.getMessage(timeType == 1 ? "L005571" : "L005573", new Object[]{
                                ""},
                        new Locale(sessionBean.getLanguage())));
                // 假期配额名称
                mResultItem.put("holidayName", mItemObject.get("quota_setting_name"));
                // 获取休假quotaId
                mResultItem.put("quotaId", mItemObject.get("quotaId"));
                // 假期配额配置Id
                mResultItem.put("quotaSettingId", mItemObject.get("quotaSettingId"));
                return mResultItem;
            }).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 获取假期配额调整列表
     *
     * @param belongId
     * @param quotaId
     * @return
     */
    public Map getAdjustQuotaList(String belongId, Integer quotaId, Integer quotaSettingId) {
        Map mResult = new HashMap();
        try {
            mResult.put("status", 1);
            HashMap<String, Object> params = new HashMap<>();
            params.put("quotaId", quotaId);
            params.put("quotaSettingId", quotaSettingId);
            List<Map> mAdjustList = empInfoMapper.getAdjustQuotaList(params);
            mAdjustList.forEach((map) -> {
                if (map.containsKey("timeType") && (Integer) map.get("timeType") == 2) {
                    // 2 表示分钟类型
                    BigDecimal quotaDay = (BigDecimal) map.get("quotaDay");
                    BigDecimal quotaUsed = (BigDecimal) map.get("quotaUsed");
                    BigDecimal quotaSurplus = (BigDecimal) map.get("quotaSurplus");
                    BigDecimal quotaInvalid = (BigDecimal) map.get("invalidQuota");
                    map.put("quotaDay", quotaDay.divide(new BigDecimal(60), 1, RoundingMode.HALF_DOWN));
                    map.put("quotaUsed", quotaUsed.divide(new BigDecimal(60), 1, RoundingMode.HALF_DOWN));
                    map.put("quotaSurplus", quotaSurplus.divide(new BigDecimal(60), 1, RoundingMode.HALF_DOWN));
                    map.put("invalidQuota", quotaInvalid.divide(new BigDecimal(60), 1, RoundingMode.HALF_DOWN));
                }
            });
            mResult.put("data", mAdjustList);
        } catch (Exception e) {
            e.printStackTrace();
            mResult.put("status", -1);
        }
        return mResult;
    }

    public List getHolidays(String belongId, Integer year, String lang) {
        if (year == null || year.intValue() == 0) {
            Calendar a = Calendar.getInstance();
            year = a.get(Calendar.YEAR);
        }
        Map map = new HashMap();
        map.put("belongOrgid", belongId);
        map.put("searchYear", year);
        Date date1 = DateUtil.getDateByTimeStr(year + ".01.01");
        Date date2 = DateUtil.getDateByTimeStr((year + 1) + ".01.01");
        map.put("startDate", date1.getTime() / 1000);
        map.put("endDate", date2.getTime() / 1000);
        List<Map> listMap = waHolidayCalendarMapper.getMobHoliday(map);
        if (listMap != null && !listMap.isEmpty()) {
            for (Map holidayMap : listMap) {
                if (holidayMap.get("calendaricon") != null) {
                    holidayMap.put("calendaricon", "assets/img/holiday_icon/" + holidayMap.get("calendaricon"));
                }
            }
        }
        return LangUtil.langlist(listMap, lang, "holidayname", "calendar_name_lang");
    }

    public PGobject convertToGobject(Object obj) {
        try {
            if (obj instanceof String) {
                PGobject jsonObject = new PGobject();
                jsonObject.setType("jsonb");
                jsonObject.setValue(obj.toString());
                return jsonObject;
            } else if (obj instanceof Map) {
                PGobject jsonObject = new PGobject();
                jsonObject.setType("jsonb");
                jsonObject.setValue(com.alibaba.fastjson.JSONObject.toJSON(obj).toString());
                return jsonObject;
            } else if (obj instanceof PGobject) {
                return (PGobject) obj;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<Map> getHolidayDetails(Integer year, Integer holidayid) {
        String searchYear = "";
        if (year == null || year.intValue() == 0) {
            Calendar a = Calendar.getInstance();
            searchYear = a.get(Calendar.YEAR) + "";
        } else {
            searchYear = year.toString();
        }
        Map map = new HashMap();
        map.put("holidayid", holidayid);
        map.put("searchYear", searchYear);
        return waHolidayCalendarMapper.getHolidayDetails(map);

    }

    public SysCorpNotice getNotice(Integer id) {
        return sysCorpNoticeMapper.selectByPrimaryKey(id);

    }

    public List getNoticeList(Integer belongOrgId, Long searchTimestamp, Long empid) {
        Map map = new HashMap();
        try {
            map.put("belongOrgId", belongOrgId);
            long nowDate = DateUtil.getOnlyDate(new Date());
            SysCorpNoticeExample example = new SysCorpNoticeExample();
            SysCorpNoticeExample.Criteria criteria = example.createCriteria();
            criteria.andBelongOrgIdEqualTo(belongOrgId);
            long searchDate = 0l;
            if (searchTimestamp == null || searchTimestamp.intValue() == 0) {
                searchDate = nowDate - 24 * 60 * 60 * 3;//3天前的0时开始。
            } else {
                searchDate = searchTimestamp.longValue();
            }
            criteria.andPublishTimeGreaterThan(searchDate);
            example.setOrderByClause("notice_id desc");
            List<SysCorpNotice> list = sysCorpNoticeMapper.selectByExample(example);
            for (SysCorpNotice sysCorpNotice : list) {
                sysCorpNotice.setNoticeDetail(null);
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    /**
     * @param id
     * @param type 1请假，2加班
     * @return
     */
    public List<Map> getApprovalFlow(Integer id, int type) {
        return waApprovalMapper.findEmpApprovalRecoard(id, type);
    }

//    public List<Map> getWfApprovalFlow(String businessKey, Integer belongOrgId, String procInstId) {
//        return workflowService.getWorkflowHisList(belongOrgId, businessKey, procInstId);
//    }

    private void saveSysUserCorp(String account, SysCorpIp corp) {
        Jedis jedis = RedisService.getResource();
        try {
            String corpUrl = jedis.get(RedisKeyDefine.SYS_USER_CORP_URL + "_" + account);
            if (StringUtils.isBlank(corpUrl)) {
                SysUserCorp userCorp = new SysUserCorp();
                userCorp.setAccount(account);
                userCorp.setCorpIpId(corp.getCorpIpId());
                sysUserCorpMapper.insertSelective(userCorp);
                jedis.set(RedisKeyDefine.SYS_USER_CORP_URL + "_" + account, corp.getCorpUrl());
            }

        } catch (RuntimeException e) {
            jedis.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            jedis.close();
        }

    }

    /**
     * 取得折算因子
     *
     * @return
     */
    public BigDecimal getZsyz(long startDate, BigDecimal quota, WaLeaveType leaveType) {
        long jg = (System.currentTimeMillis() / 1000 - startDate) / 24 * 60 * 60;
        BigDecimal bd = new BigDecimal(jg);
        bd = bd.multiply(quota).divide(new BigDecimal(360), 2, BigDecimal.ROUND_HALF_UP);
        if (leaveType.getUseFour2fiveRule().intValue() == 1) {//向上取整
            return PayEngineUtil.handleMantissa(bd, (short) 1, (short) (leaveType.getRoundTimeUnit().floatValue() == 0.5 ? 1 : 0));
        } else if (leaveType.getUseFour2fiveRule().intValue() == 2) {//向下取整
            return PayEngineUtil.handleMantissa(bd, (short) 2, (short) (leaveType.getRoundTimeUnit() == 0.5 ? 1 : 0));
        }
        if (leaveType.getUseFour2fiveRule().intValue() == 3) {//四舍五入
            return PayEngineUtil.handleMantissa(bd, (short) 3, (short) 0);
        }
        return PayEngineUtil.handleMantissa(bd, (short) 3, (short) 0);
    }

    /**
     * 取得某天的排班List
     *
     * @return
     */
    @RedisCache(expire = 60) // 查看排班缓存5分钟
    public List getShiftByDate(Long empId, Long date) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(empInfo.getBelongOrgId())) {
            List<Map> list = new ArrayList<>();
            //开启智能排班
            WaShiftDef shiftDef = remoteSmartWorkTimeService.getEmployeeShiftByDate(empInfo.getBelongOrgId(), empInfo.getEmpid(), date);
            if (shiftDef != null) {
                Map map = new HashMap();
                map.put("dateType", shiftDef.getDateType());
                map.put("workDate", date);
                map.put("shiftDefName", BaseConst.WA_DATE_TYPE.get(shiftDef.getDateType()));
                map.put("startTime", shiftDef.getStartTime());
                map.put("endTime", shiftDef.getEndTime());
                map.put("restTimeDesc", shiftDef.getRestTimeDesc());
                map.put("onDutyStartTime", shiftDef.getOnDutyStartTime());
                map.put("onDutyEndTime", shiftDef.getOnDutyEndTime());
                map.put("offDutyStartTime", shiftDef.getOffDutyStartTime());
                map.put("offDutyEndTime", shiftDef.getOffDutyEndTime());
                map.put("shiftCode", shiftDef.getShiftDefCode());
                map.put("shiftName", shiftDef.getShiftDefName());
                map.put("i18nShiftDefName", shiftDef.getI18nShiftDefName());
                map.put("isNoonRest", shiftDef.getIsNoonRest());
                map.put("noonRestStart", shiftDef.getNoonRestStart());
                map.put("noonRestEnd", shiftDef.getNoonRestEnd());
                map.put("workTotalTime", shiftDef.getWorkTotalTime());
                if (empInfo.getOrgid() != null) {
                    SysCorpOrg org = sysCorpOrgMapper.selectByPrimaryKey(empInfo.getOrgid());
                    if (org != null) {
                        map.put("addr", org.getOfficeAddr());//部门办公地点
                    }
                }
                list.add(map);
                return list;
            }
        } else {
            Integer tmType = empInfo.getTmType();
            Map map = new HashMap();
            map.put("empId", empId);
            map.put("searchDate", date);
            if (tmType != null && tmType == 2) {//门店考勤
                //date需要转为20160102
                try {
                    map.put("searchDate", Integer.parseInt(DateUtil.convertDateTimeToStr(date, "yyyyMMdd", true)));
                } catch (Exception e) {
                    e.printStackTrace();
                    map.put("searchDate", 0);
                }
                List<Map> shiftList = waStoreTimeMapper.getMobileStorePbByDate(map);
                //杰尼亚-因允许未排班也可以休假，休假时会默认排个系统默认班次（班次代码为：system-default-shift），系统默认班次为虚拟班次，日历上面不展示
                shiftList = shiftList.stream().filter(row -> {
                    String code = (String) row.get("shiftDefCode");
                    return !StringUtils.isNotBlank(code) || !code.equals(BaseConst.SYSTEM_DEFAULT_SHIFT);
                }).collect(Collectors.toList());

                return shiftList;
            } else {//正常考勤
                //班次调整查询
                Map<String, Integer> empChangeShiftMap = waCommonService.getEmpChangeShiftMapByTimeStamp(empInfo.getBelongOrgId(), new ArrayList<>(Collections.singletonList(empId)), date, date);
                //班次替换
                if (empChangeShiftMap.containsKey(empId + "_" + date)) {
                    Integer changeShiftId = empChangeShiftMap.get(empId + "_" + date);
                    WaShiftDef shiftDef = waShiftDefMapper.selectByPrimaryKey(changeShiftId);
                    if (shiftDef != null) {
                        List<Map> list = new ArrayList<>();
                        Map dateMap = new HashMap();
                        dateMap.put("dateType", shiftDef.getDateType());
                        dateMap.put("workDate", date);
                        dateMap.put("shiftDefName", BaseConst.WA_DATE_TYPE.get(shiftDef.getDateType()));
                        dateMap.put("startTime", shiftDef.getStartTime());
                        dateMap.put("endTime", shiftDef.getEndTime());
                        dateMap.put("restTimeDesc", shiftDef.getRestTimeDesc());
                        dateMap.put("onDutyStartTime", shiftDef.getOnDutyStartTime());
                        dateMap.put("onDutyEndTime", shiftDef.getOnDutyEndTime());
                        dateMap.put("offDutyStartTime", shiftDef.getOffDutyStartTime());
                        dateMap.put("offDutyEndTime", shiftDef.getOffDutyEndTime());
                        dateMap.put("shiftCode", shiftDef.getShiftDefCode());
                        dateMap.put("shiftName", shiftDef.getShiftDefName());
                        dateMap.put("i18nShiftDefName", shiftDef.getI18nShiftDefName());
                        dateMap.put("isNoonRest", shiftDef.getIsNoonRest());
                        dateMap.put("noonRestStart", shiftDef.getNoonRestStart());
                        dateMap.put("noonRestEnd", shiftDef.getNoonRestEnd());
                        dateMap.put("workTotalTime", shiftDef.getWorkTotalTime());
                        if (empInfo.getOrgid() != null) {
                            SysCorpOrg org = sysCorpOrgMapper.selectByPrimaryKey(empInfo.getOrgid());
                            if (org != null) {
                                dateMap.put("addr", org.getOfficeAddr());//部门办公地点
                            }
                        }
                        list.add(dateMap);
                        return list;
                    }
                } else {
                    return waConfigMapper.getMobilePbByDate(map);
                }
            }
        }
        return new ArrayList();
    }

    public Map<Long, WaWorktimeDetail> getPbByDates(Long empId, long startDate, long endDate) {
        Map map = new HashMap();
        map.put("empId", empId);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        List<WaWorktimeDetail> list = waWorktimeDetailMapper.getWaShiftDefByDates(map);
        Map<Long, WaWorktimeDetail> retunMap = new HashMap();
        for (WaWorktimeDetail detail : list) {
            retunMap.put(detail.getWorkDate(), detail);
        }
        return retunMap;
    }

    public Map<Long, WaWorktimeDetail> getStoreShiftDefByDates(Long empId, long startDate, long endDate) {
        Map map = new HashMap();
        map.put("empId", empId);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        List<WaWorktimeDetail> list = waWorktimeDetailMapper.getStoreShiftDefByDates(map);
        Map<Long, WaWorktimeDetail> retunMap = new HashMap();
        for (WaWorktimeDetail detail : list) {
            retunMap.put(detail.getWorkDate(), detail);
        }
        return retunMap;
    }

    public List getShiftByMonth(Long empId, Integer searchMonth) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (null == empInfo) {
            return new ArrayList();
        }
        Integer tmType = empInfo.getTmType();
        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(empInfo.getBelongOrgId())) {
            List<Map> mapList = new ArrayList<>();
            List<WorkTimeCalendar> list = remoteSmartWorkTimeService.getEmpWorkTimeCalendarByYm(empInfo.getEmpid(), searchMonth);
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(row -> {
                    Map map = new HashMap();
                    map.put("dateType", row.getType());
                    map.put("workDate", String.valueOf(row.getDate()));
                    map.put("shiftDefName", BaseConst.WA_DATE_TYPE.get(row.getType().intValue()));
                    mapList.add(map);
                });
                return mapList;
            }
        } else {
            if (tmType != null && tmType.intValue() == 2) {//门店考勤
                Map map = new HashMap();
                map.put("empId", empId);
                map.put("searchMonth", searchMonth);
                return waStoreTimeMapper.getMobileStorePbByMonth(map);

            } else {//正常考勤
                //指定某月的第一天，最后一天
                try {
                    SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
                    String time = searchMonth + "01";
                    Date firstDay = format.parse(time);
                    int weekDay = DateUtil.getDayOfWeek(firstDay.getTime(), null) - 1;
                    if (0 == weekDay) {
                        weekDay = 7;
                    }
                    weekDay -= 1;
                    int monthDays = DateUtil.getMonthActualMax(firstDay.getTime());
                    int lastDays = 42 - monthDays - weekDay;
                    Long firstTime = firstDay.getTime() / 1000 - 3600 * 24 * weekDay;
                    Date lastDay = DateUtil.getLastDayOfMonth(firstDay);
                    Long lastTime = lastDay.getTime() / 1000 + 3600 * 24 * lastDays;
                    Map map = new HashMap();
                    map.put("empId", empId);
                    map.put("start", firstTime);
                    map.put("end", lastTime);
                    return waConfigMapper.getMobilePbByMonth(map);
                } catch (Exception e) {
                    e.printStackTrace();
                    return null;
                }
            }
        }
        return new ArrayList();
    }

    public String processEmpQuota(Long empId, BigDecimal totalTimeDuration, WaLeaveType waLeaveType, long startTime, Integer leaveId) {
        List<WaEmpQuota> quotaList = this.getWaEmpQuota(waLeaveType.getLeaveTypeId(), empId, "asc", startTime);
        if (quotaList == null || quotaList.size() < 1) {
            deleteLeave(leaveId);
            return "没有额度信息，不能请假";
        }
        WaEmpQuota empQuotaTmp = quotaList.get(0);
        Map map = new HashMap();
        map.put("empId", empId);
        map.put("leaveTypeId", waLeaveType.getLeaveTypeId());
        map.put("firstDate", empQuotaTmp.getStartDate());
        map.put("lastDate", empQuotaTmp.getLastDate());
        Integer[] array = new Integer[1];
        array[0] = 1;
        map.put("array", array);
        map.put("leaveId", leaveId);
        Float frozeNum = waEmpLeaveMapper.getLeaveTypeTotal(map);
        frozeNum = (frozeNum == null ? 0f : frozeNum);
        totalTimeDuration = totalTimeDuration.add(new BigDecimal(frozeNum));
//			getLeaveFrozenTotal
        if (waLeaveType.getAcctTimeType().intValue() == 2) {//以小时为单位
            totalTimeDuration = totalTimeDuration.divide(new BigDecimal(60), 1, BigDecimal.ROUND_HALF_UP);
        }
//        BigDecimal totalTimeDuration2 = new BigDecimal(totalTimeDuration.floatValue());
        for (WaEmpQuota empQuota : quotaList) {
            if (totalTimeDuration.floatValue() == 0) {
                break;
            }
            long remainValidDate = empQuota.getRemainValidDate() == null ? 0l : empQuota.getRemainValidDate();

            BigDecimal remainDayBig = new BigDecimal(empQuota.getRemainDay());
            if (empQuota.getRemainUsedDay() == null) {
                empQuota.setRemainUsedDay(0f);
            }

            if (remainValidDate >= startTime) {//留成可以使用。
                BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(empQuota.getRemainUsedDay()));//可用留成数

                if (waLeaveType.getAcctTimeType().intValue() == 1) {//以天为单位，需要计算最大使用留成天数
                    Long invLc = (remainValidDate - startTime) / (24 * 60 * 60);
                    if (invLc >= 0) {
                        invLc = invLc + 1;
                    }
                    if (kyRemain.floatValue() > invLc.floatValue()) {
                        kyRemain = new BigDecimal(invLc);//最大使用留成天数
                    }
                }
                if (kyRemain.floatValue() > 0) {
                    if (kyRemain.compareTo(totalTimeDuration) >= 0) {//留成数大于需要扣除额额度数
                        totalTimeDuration = new BigDecimal(0);
                        break;
                    } else {//留成不够，扣除所有留成。
                        totalTimeDuration = totalTimeDuration.subtract(kyRemain);
                    }
                }
            }
            //留成处理完，下面是可用额度
            BigDecimal keQuota = new BigDecimal(empQuota.getQuotaDay()).subtract(new BigDecimal(empQuota.getDeductionDay())).subtract(new BigDecimal(empQuota.getUsedDay()));
            if (keQuota.compareTo(totalTimeDuration) >= 0) {
                totalTimeDuration = new BigDecimal(0);
                break;
            } else {//额度不够
                totalTimeDuration = totalTimeDuration.subtract(keQuota);
            }
        }
        if (totalTimeDuration.floatValue() > 0) {
            deleteLeave(leaveId);
            return "额度不够，不能请假";
        }
        return "";
    }

    public boolean isCountQuota(Integer leaveTypeId) {
        WaLeaveSettingExample example = new WaLeaveSettingExample();
        WaLeaveSettingExample.Criteria criteria = example.createCriteria();
        criteria.andLeaveTypeIdEqualTo(leaveTypeId);
        List list = waLeaveSettingMapper.selectByExample(example);
        return list != null && list.size() > 0;
    }

    public List<WaEmpQuota> getWaEmpQuota(Integer leaveTypeId, Long empId, String sortNo) {
        Map map = new HashMap();
        map.put("leaveTypeId", leaveTypeId);
        map.put("empId", empId);
        map.put("nowDate", System.currentTimeMillis() / 1000);
        map.put("sortNo", sortNo);
        return waEmpQuotaMapper.getEmpQuota2(map);
    }

    public List<WaEmpQuota> getWaEmpQuota(Integer leaveTypeId, Long empId, String sortNo, List<Integer> quotaSettingIds) {
        Map map = new HashMap();
        if (leaveTypeId != null) {
            map.put("leaveTypeId", leaveTypeId);
        }
        if (CollectionUtils.isNotEmpty(quotaSettingIds)) {
            map.put("quotaSettingIds", quotaSettingIds);
        }
        map.put("empId", empId);
        map.put("nowDate", System.currentTimeMillis() / 1000);
        map.put("sortNo", sortNo);
        return waEmpQuotaMapper.getEmpQuota2(map);
    }

    public List<WaEmpQuota> getWaEmpQuota(Integer leaveTypeId, Long empId, String sortNo, Long nowDate) {
        Map map = new HashMap();
        map.put("leaveTypeId", leaveTypeId);
        map.put("empId", empId);
        map.put("nowDate", nowDate);
        map.put("sortNo", sortNo);
        return waEmpQuotaMapper.getEmpQuota2(map);
    }

    /**
     * 检查员工假期留存配额
     *
     * @param waLeaveType
     * @param empId       员工ID
     * @param startTime
     * @param endTime
     * @return
     */
    public BigDecimal checkQuotaRemain(WaLeaveType waLeaveType, Long empId, long startTime, long endTime, List<Integer> quotaIds) {
        Integer leaveTypeId = waLeaveType.getLeaveTypeId();
        String belongOrgId = waLeaveType.getBelongOrgid();
        Integer acctTimeType = waLeaveType.getAcctTimeType();

        BigDecimal totalRemain = new BigDecimal(0);

        //员工假期配额
        List<WaEmpQuota> quotaList = this.getWaEmpQuota(leaveTypeId, empId, "asc");
        if (CollectionUtils.isNotEmpty(quotaList)) {
            BigDecimal frozeDuration = new BigDecimal(0);
            if (waLeaveType.getLeaveType() != 3) {
                WaEmpQuota empQuotaTmp = quotaList.get(0);
                Map map = new HashMap();
                map.put("empId", empId);
                map.put("leaveTypeId", leaveTypeId);
                map.put("firstDate", empQuotaTmp.getStartDate());
                map.put("lastDate", empQuotaTmp.getLastDate());
                map.put("array", new Integer[]{1});
                Float frozeNum = waEmpLeaveMapper.getLeaveTypeTotal(map);
                frozeNum = (frozeNum == null ? 0f : frozeNum);//冻结配额
                frozeDuration = new BigDecimal(frozeNum);
            }

            Float ratio = waCommonService.getConvertRatio(belongOrgId, empId);//计算试用期额度折算比

            //查询假期额度是否试用期折算
            Map leaveQuotaSetMap = new HashMap();
            for (WaEmpQuota empQuota : quotaList) {
                if (!leaveQuotaSetMap.containsKey(empQuota.getQuotaSettingId())) {
                    Boolean isTrialConvert = false;//试用期额度是否折算
                    WaLeaveSetting waLeaveSetting = waLeaveSettingMapper.selectByPrimaryKey(empQuota.getQuotaSettingId());
                    if (waLeaveSetting != null && waLeaveSetting.getIsTrialConvert() != null && waLeaveSetting.getIsTrialConvert()) {
                        //试用期折算
                        isTrialConvert = true;
                    }
                    waLeaveSetting.setIsTrialConvert(isTrialConvert);

                    leaveQuotaSetMap.put(empQuota.getQuotaSettingId(), waLeaveSetting);
                }
            }
            BigDecimal remainDuration = new BigDecimal(0);//留存剩余额度
            //留存
            for (WaEmpQuota empQuota : quotaList) {
                //校验有效期
                Long lastDate = empQuota.getLastDate();
                if (endTime > lastDate) {
                    continue;
                }

                long remainValidDate = empQuota.getRemainValidDate() == null ? 0L : empQuota.getRemainValidDate();
                BigDecimal remainDayBig = new BigDecimal(empQuota.getRemainDay());
                if (empQuota.getRemainUsedDay() == null) {
                    empQuota.setRemainUsedDay(0f);
                }
                if (empQuota.getDeductionDay() == null) {
                    empQuota.setDeductionDay(0f);
                }
                if (remainValidDate >= startTime) {//留成可以使用。
                    BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(empQuota.getRemainUsedDay()));//可用留成数
                    if (acctTimeType == 1) {//以天为单位，需要计算最大使用留成天数
                        Long invLc = (remainValidDate - startTime) / (24 * 60 * 60);
                        if (invLc >= 0) {
                            invLc = invLc + 1;
                        }
                        if (kyRemain.floatValue() > invLc.floatValue()) {
                            kyRemain = new BigDecimal(invLc);//最大使用留成天数
                        }
                    }
                    WaLeaveSetting setting = (WaLeaveSetting) leaveQuotaSetMap.get(empQuota.getQuotaSettingId());
                    Boolean isTrialConvert = setting.getIsTrialConvert();//试用期额度是否折算

                    //额度折算，向下取整
                    if (isTrialConvert && ratio != null) {
                        kyRemain = kyRemain.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                    }
                    if (kyRemain.floatValue() > 0) {
                        if (kyRemain.compareTo(frozeDuration) >= 0) {//留成数大于需要扣除额额度数
                            //留存剩余
                            BigDecimal remainSurplus = kyRemain.subtract(frozeDuration);
                            if (remainSurplus.floatValue() > 0) {
                                remainDuration = remainDuration.add(remainSurplus);
                            }
                            frozeDuration = new BigDecimal(0);
                        } else {//留成不够，扣除所有留成
                            frozeDuration = frozeDuration.subtract(kyRemain);
                        }
                        //优先假期特殊判断
                        if (CollectionUtils.isNotEmpty(quotaIds)) {
                            if (quotaIds.indexOf(empQuota.getQuotaSettingId()) != -1) {
                                totalRemain = totalRemain.add(kyRemain);
                            }
                        }
                    }
                }
            }
        }
        return totalRemain;
    }


    /**
     * 获取员工假期留存配额
     *
     * @param waLeaveType
     * @param empId       员工ID
     * @param startTime
     * @param endTime
     * @return
     */
    public BigDecimal getEmpLeaveRemainQuota(WaLeaveType waLeaveType, Long empId, long startTime, long endTime) {
        Integer leaveTypeId = waLeaveType.getLeaveTypeId();
        String belongOrgId = waLeaveType.getBelongOrgid();
        Integer acctTimeType = waLeaveType.getAcctTimeType();

        //员工假期配额
        List<WaEmpQuota> quotaList = this.getWaEmpQuota(leaveTypeId, empId, "asc");
        if (CollectionUtils.isNotEmpty(quotaList)) {
            BigDecimal frozeDuration = new BigDecimal(0);
            if (waLeaveType.getLeaveType() != 3) {
                WaEmpQuota empQuotaTmp = quotaList.get(0);
                Map map = new HashMap();
                map.put("empId", empId);
                map.put("leaveTypeId", leaveTypeId);
                map.put("firstDate", empQuotaTmp.getStartDate());
                map.put("lastDate", empQuotaTmp.getLastDate());
                map.put("array", new Integer[]{1});
                Float frozeNum = waEmpLeaveMapper.getLeaveTypeTotal(map);
                frozeNum = (frozeNum == null ? 0f : frozeNum);//冻结配额
                frozeDuration = new BigDecimal(frozeNum);
            }

            Float ratio = waCommonService.getConvertRatio(belongOrgId, empId);//计算试用期额度折算比

            //查询假期额度是否试用期折算
            Map leaveQuotaSetMap = new HashMap();
            for (WaEmpQuota empQuota : quotaList) {
                if (!leaveQuotaSetMap.containsKey(empQuota.getQuotaSettingId())) {
                    Boolean isTrialConvert = false;//试用期额度是否折算
                    WaLeaveSetting waLeaveSetting = waLeaveSettingMapper.selectByPrimaryKey(empQuota.getQuotaSettingId());
                    if (waLeaveSetting != null && waLeaveSetting.getIsTrialConvert() != null && waLeaveSetting.getIsTrialConvert()) {
                        //试用期折算
                        isTrialConvert = true;
                    }
                    waLeaveSetting.setIsTrialConvert(isTrialConvert);

                    leaveQuotaSetMap.put(empQuota.getQuotaSettingId(), waLeaveSetting);
                }
            }
            BigDecimal remainDuration = new BigDecimal(0);//留存剩余额度
            //留存
            for (WaEmpQuota empQuota : quotaList) {
                //校验有效期
                Long lastDate = empQuota.getLastDate();
                if (endTime > lastDate) {
                    continue;
                }

                long remainValidDate = empQuota.getRemainValidDate() == null ? 0L : empQuota.getRemainValidDate();
                BigDecimal remainDayBig = new BigDecimal(empQuota.getRemainDay());
                if (empQuota.getRemainUsedDay() == null) {
                    empQuota.setRemainUsedDay(0f);
                }
                if (empQuota.getDeductionDay() == null) {
                    empQuota.setDeductionDay(0f);
                }
                if (remainValidDate >= startTime) {//留成可以使用。
                    BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(empQuota.getRemainUsedDay()));//可用留成数
                    if (acctTimeType == 1) {//以天为单位，需要计算最大使用留成天数
                        Long invLc = (remainValidDate - startTime) / (24 * 60 * 60);
                        if (invLc >= 0) {
                            invLc = invLc + 1;
                        }
                        if (kyRemain.floatValue() > invLc.floatValue()) {
                            kyRemain = new BigDecimal(invLc);//最大使用留成天数
                        }
                    }
                    WaLeaveSetting setting = (WaLeaveSetting) leaveQuotaSetMap.get(empQuota.getQuotaSettingId());
                    Boolean isTrialConvert = setting.getIsTrialConvert();//试用期额度是否折算

                    //额度折算，向下取整
                    if (isTrialConvert && ratio != null) {
                        kyRemain = kyRemain.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                    }
                    if (kyRemain.floatValue() > 0) {
                        if (kyRemain.compareTo(frozeDuration) >= 0) {//留成数大于需要扣除额额度数
                            //留存剩余
                            BigDecimal remainSurplus = kyRemain.subtract(frozeDuration);
                            if (remainSurplus.floatValue() > 0) {
                                remainDuration = remainDuration.add(remainSurplus);
                            }
                            frozeDuration = new BigDecimal(0);
                        } else {//留成不够，扣除所有留成
                            frozeDuration = frozeDuration.subtract(kyRemain);
                        }
                    }
                }
            }
            return remainDuration;
        }
        return new BigDecimal(0);
    }

    /**
     * 根据请假时间获取可用的假期额度
     *
     * @param waLeaveType
     * @param empId       员工ID
     * @param startTime
     * @param endTime
     * @return
     */
    public BigDecimal getEmpLeaveQuotaByLeaveTime(WaLeaveType waLeaveType, Long empId, long startTime, long endTime) {
        Integer leaveTypeId = waLeaveType.getLeaveTypeId();
        String belongOrgId = waLeaveType.getBelongOrgid();
        Integer acctTimeType = waLeaveType.getAcctTimeType();

        //员工假期配额
        List<WaEmpQuota> quotaList = this.getWaEmpQuota(leaveTypeId, empId, "asc");
        if (CollectionUtils.isNotEmpty(quotaList)) {
            BigDecimal frozeDuration = new BigDecimal(0);
            if (waLeaveType.getLeaveType() != 3) {
                WaEmpQuota empQuotaTmp = quotaList.get(0);
                Map map = new HashMap();
                map.put("empId", empId);
                map.put("leaveTypeId", leaveTypeId);
                map.put("firstDate", empQuotaTmp.getStartDate());
                map.put("lastDate", empQuotaTmp.getLastDate());
                map.put("array", new Integer[]{1});
                Float frozeNum = waEmpLeaveMapper.getLeaveTypeTotal(map);
                frozeNum = (frozeNum == null ? 0f : frozeNum);//冻结配额
                frozeDuration = new BigDecimal(frozeNum);
            }
            Float ratio = waCommonService.getConvertRatio(belongOrgId, empId);//计算试用期额度折算比

            List<Integer> listEmpQuotaId = new ArrayList<>();
            List<Integer> listQuotaSettingId = new ArrayList<>();

            for (WaEmpQuota empQuota : quotaList) {
                if (listEmpQuotaId.indexOf(empQuota.getEmpQuotaId()) < 0) {
                    listEmpQuotaId.add(empQuota.getEmpQuotaId());
                }
                if (listQuotaSettingId.indexOf(empQuota.getQuotaSettingId()) < 0) {
                    listQuotaSettingId.add(empQuota.getQuotaSettingId());
                }
            }
            //查询假期额度是否试用期折算
            Map<Integer, WaLeaveSetting> leaveQuotaSetMap = new HashMap<>();
            WaLeaveSettingExample settingExample = new WaLeaveSettingExample();
            settingExample.createCriteria().andQuotaSettingIdIn(listQuotaSettingId);
            List<WaLeaveSetting> settingList = waLeaveSettingMapper.selectByExample(settingExample);
            settingList.stream().forEach(row -> {
                if (!leaveQuotaSetMap.containsKey(row.getQuotaSettingId())) {
                    if (row.getIsTrialConvert() == null) {//试用期额度是否折算
                        row.setIsTrialConvert(false);
                    }
                    if (row.getFreezingRules() == null) {
                        row.setFreezingRules((short) 0);
                    }
                    leaveQuotaSetMap.put(row.getQuotaSettingId(), row);
                }
            });

            BigDecimal remainDuration = new BigDecimal(0);//留存剩余额度
            //留存
            for (WaEmpQuota empQuota : quotaList) {
                //校验有效期
                Long lastDate = empQuota.getLastDate();
                if (endTime > lastDate) {
                    continue;
                }

                long remainValidDate = empQuota.getRemainValidDate() == null ? 0L : empQuota.getRemainValidDate();
                BigDecimal remainDayBig = new BigDecimal(empQuota.getRemainDay());
                if (empQuota.getRemainUsedDay() == null) {
                    empQuota.setRemainUsedDay(0f);
                }
                if (empQuota.getDeductionDay() == null) {
                    empQuota.setDeductionDay(0f);
                }
                if (remainValidDate >= startTime) {//留成可以使用。
                    BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(empQuota.getRemainUsedDay()));//可用留成数
                    if (acctTimeType == 1) {//以天为单位，需要计算最大使用留成天数
                        Long invLc = (remainValidDate - startTime) / (24 * 60 * 60);
                        if (invLc >= 0) {
                            invLc = invLc + 1;
                        }
                        if (kyRemain.floatValue() > invLc.floatValue()) {
                            kyRemain = new BigDecimal(invLc);//最大使用留成天数
                        }
                    }
                    WaLeaveSetting setting = leaveQuotaSetMap.get(empQuota.getQuotaSettingId());
                    Boolean isTrialConvert = setting.getIsTrialConvert();//试用期额度是否折算

                    //额度折算，向下取整
                    if (isTrialConvert && ratio != null) {
                        kyRemain = kyRemain.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                    }
                    if (kyRemain.floatValue() > 0) {
                        if (kyRemain.compareTo(frozeDuration) >= 0) {//留成数大于需要扣除额额度数
                            //留存剩余
                            BigDecimal remainSurplus = kyRemain.subtract(frozeDuration);
                            if (remainSurplus.floatValue() > 0) {
                                remainDuration = remainDuration.add(remainSurplus);
                            }
                            frozeDuration = new BigDecimal(0);
                        } else {//留成不够，扣除所有留成
                            frozeDuration = frozeDuration.subtract(kyRemain);
                        }
                    }
                }
            }
            BigDecimal totalKeQuota = new BigDecimal(remainDuration.floatValue());//总的可用额度

            //查询调整已使用
            Map<Integer, Float> adjustUsedMap = new HashMap<>();
            Map parm = new HashMap();
            parm.put("anyEmpQuotaIds", "'{" + StringUtils.join(listEmpQuotaId, ",") + "}'");
            List<Map> listEmpAdjustUsedDay = waMapper.listEmpAdjustUsedDay(parm);
            if (CollectionUtils.isNotEmpty(listEmpAdjustUsedDay)) {
                for (Map map : listEmpAdjustUsedDay) {
                    Integer empQuotaId = (Integer) map.get("empQuotaId");
                    Float userDay = (Float) map.get("adjustUsedDay");
                    adjustUsedMap.put(empQuotaId, userDay);
                }
            }
            //当前可用额度
            for (WaEmpQuota empQuota : quotaList) {
                BigDecimal keQuota = this.floatToBigDecimal(empQuota.getNowQuota()).
                        add(this.floatToBigDecimal(empQuota.getAdjustQuota())).
                        subtract(this.floatToBigDecimal(empQuota.getDeductionDay())).
                        subtract(this.floatToBigDecimal(empQuota.getUsedDay())).
                        subtract(this.floatToBigDecimal(empQuota.getFixUsedDay())).
                        subtract(this.floatToBigDecimal(adjustUsedMap.get(empQuota.getEmpQuotaId())));

                WaLeaveSetting setting = leaveQuotaSetMap.get(empQuota.getQuotaSettingId());
                if (setting.getFreezingRules() != 2) {//当年配额冻结
                    Boolean isTrialConvert = setting.getIsTrialConvert();//试用期额度是否折算
                    //额度折算，向下取整
                    if (isTrialConvert && ratio != null) {
                        keQuota = keQuota.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                    }
                    //扣减冻结配额
                    if (frozeDuration.floatValue() > 0) {
                        if (keQuota.compareTo(frozeDuration) >= 0) {
                            keQuota = keQuota.subtract(frozeDuration);
                            frozeDuration = new BigDecimal(0);
                        } else {
                            frozeDuration = frozeDuration.subtract(keQuota);
                            keQuota = new BigDecimal(0);
                        }
                    }
                    if (keQuota.floatValue() > 0) {
                        totalKeQuota = totalKeQuota.add(keQuota);
                    }
                }
            }
            return totalKeQuota;
        }
        return new BigDecimal(0);
    }

    public BigDecimal floatToBigDecimal(Float value) {
        if (value == null) return new BigDecimal(0);
        return new BigDecimal(value);
    }

    public String getTimeResultMsg(BigDecimal duration, Integer acctTimeType, String leaveTypeName, String lang, String msgCode) {
        if (duration != null && duration.floatValue() >= 0) {
            //可用额度还有剩余
            float surplusQuotaTime = duration.floatValue();
            //计算剩余#天#小时#分钟
            float day = 0f;
            float hour = 0f;
            float mi = 0f;
            if (acctTimeType == 1) {
                day = surplusQuotaTime;
            } else if (acctTimeType == 2) {
                BigDecimal b = new BigDecimal(surplusQuotaTime / 60);
                BigDecimal c = new BigDecimal(surplusQuotaTime % 60);
                if (c.intValue() > 0) {
                    hour = b.intValue();
                    mi = c.intValue();
                } else {
                    hour = b.intValue();
                }
            }
            if (StringUtils.isNotBlank(msgCode)) {
                return messageResource.getMessage(msgCode, new Object[]{leaveTypeName, day, hour, mi}, new Locale(lang));
            } else {
                if (acctTimeType == 1) {//天
                    //余额不足，当前可用{0}天
                    return messageResource.getMessage("L006847", new Object[]{day}, new Locale(lang));
                } else {//小时
                    //余额不足，当前可用{0}小时{1}分钟
                    if (mi > 0) {
                        return messageResource.getMessage("L006849", new Object[]{hour, mi}, new Locale(lang));
                    } else {
                        return messageResource.getMessage("L006848", new Object[]{hour}, new Locale(lang));
                    }
                }
            }
        } else {
            log.info("getTimeResultMsg:" + messageResource.getMessage("L005760", new Object[]{}, new Locale(lang)));
            return messageResource.getMessage("L005760", new Object[]{}, new Locale(lang));
        }
    }

    /**
     * 根据员工配额id查询配额详情
     *
     * @param corpId
     * @param belongOrgId 租户
     * @param empQuotaId
     * @param applyDate
     * @param minTime
     * @param maxTime
     * @return
     */
    public List<Map> getEmpQuotaDetailList(Long corpId, String belongOrgId, Integer empQuotaId, Long applyDate, Long minTime, Long maxTime, String sort, Long empId, Boolean isNegative) throws ParseException {
        Map detailParams = new HashMap();
        if (corpId != null) {
            detailParams.put("corpId", corpId);
        }
        detailParams.put("belongOrgId", belongOrgId);
        detailParams.put("empQuotaId", empQuotaId);
        if (applyDate != null) {
            detailParams.put("nowDate", applyDate);
        }
        detailParams.put("sort", sort);
        detailParams.put("startTime", minTime);
        detailParams.put("endTime", maxTime);
        PageBean pageBean = new PageBean(true);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        List<Map> list = waMapper.getEmpQuotaDetailList(pageBounds, detailParams);
        if (CollectionUtils.isEmpty(list) && isNegative) {
            //如果调休配额详情为空，自动创建请假时间对应考勤周期的配额详情
            list = new ArrayList<>();
            //1、根据加班事件时间（加班开始日期）查询加班所在的考勤周期
            Map params = new HashMap();
            params.put("belongOrgId", belongOrgId);
            params.put("ym", DateUtilExt.getTimeStrByPattern(minTime, "yyyyMM"));
            params.put("empId", empId);
            Map waCycle = waMapper.getEmpWaGroupCycle(params);
            if (waCycle != null) {
                //考勤周期开始结束日期
                Long cycleStartDate = Long.valueOf(String.valueOf(waCycle.get("startDate")));
                Long cycleEndDate = Long.valueOf(String.valueOf(waCycle.get("endDate")));
                //2、调休有效期开始日期=考勤周期开始日期，调休有效期结束日期=开始日期+有效期
                Long validityStartDate = cycleStartDate;
                Long validityEndDate = DateUtilExt.getMonthEndDay(DateUtilExt.addMonth(cycleStartDate, 2));
                //3、生成调休配额明细
                WaEmpQuotaDetail quotaDetail = new WaEmpQuotaDetail();
                quotaDetail.setBelongOrgId(belongOrgId);
                quotaDetail.setCorpId(corpId);
                quotaDetail.setCrttime(DateUtil.getCurrentTime(true));
                quotaDetail.setCrtuser(0L);
                quotaDetail.setEmpQuotaId(empQuotaId);
                quotaDetail.setStartDate(validityStartDate);
                quotaDetail.setEndDate(validityEndDate);
                quotaDetail.setWaStartDate(cycleStartDate);
                quotaDetail.setWaEndDate(cycleEndDate);
                quotaDetail.setInvalidQuota(0f);//过期配额
                quotaDetail.setQuotaDay(0f);//剩余配额
                quotaDetail.setSurplusQuota(0f);//总配额
                quotaDetail.setUsedDay(0f);
                quotaDetail.setStatus(1);
                waEmpQuotaDetailMapper.insertSelective(quotaDetail);
                JSONObject jsonObject = JSONObject.fromObject(quotaDetail);
                list.add(jsonObject);
            }
        }
        return list;
    }

    /**
     * 查询员工冻结配额
     *
     * @param leaveType
     * @param leaveTypeId
     * @param empId       员工ID
     * @param leaveId
     * @param firstDate
     * @param lastDate
     * @return
     */
    public BigDecimal getEmpLeaveTypeFrozeDuration(Integer leaveType, Integer leaveTypeId, Long empId, Integer leaveId, Long firstDate, Long lastDate) {
        BigDecimal frozeDuration = new BigDecimal(0);
        if (leaveType != 3) {
            Map map = new HashMap();
            map.put("empId", empId);
            map.put("leaveTypeId", leaveTypeId);
            map.put("firstDate", firstDate);
            map.put("lastDate", lastDate);
            map.put("array", new Integer[]{1});
            if (leaveId != null) {
                map.put("leaveId", leaveId);
            }
            Float frozeNum = waEmpLeaveMapper.getLeaveTypeTotal(map);
            frozeNum = (frozeNum == null ? 0f : frozeNum);//冻结配额
            frozeDuration = new BigDecimal(frozeNum);
        }
        return frozeDuration;
    }

    /**
     * 查询配额冻结的请假数据
     *
     * @param leaveType
     * @param leaveTypeId
     * @param empId          员工ID
     * @param excludeLeaveId
     * @param firstDate
     * @param lastDate
     * @return
     */
    public List<WaLeaveDaytime> listFrozeLeave(Integer leaveType, Integer leaveTypeId, Long empId, Integer excludeLeaveId, Long firstDate, Long lastDate) {
        if (leaveType != 3) {
            Map map = new HashMap();
            map.put("empId", empId);
            map.put("leaveTypeId", leaveTypeId);
            map.put("firstDate", firstDate);
            map.put("lastDate", lastDate);
            map.put("array", new Integer[]{1});
            if (excludeLeaveId != null) {
                map.put("leaveId", excludeLeaveId);
            }
            return waLeaveDaytimeMapper.listLeaveDaytime(map);
        }
        return null;
    }

    /**
     * 查询员工请假数据
     *
     * @param leaveTypeId
     * @param empId          员工ID
     * @param excludeLeaveId
     * @param firstDate
     * @param lastDate
     * @param status
     * @return
     */
    public List<WaLeaveDaytime> listWaEmpLeave(Integer leaveTypeId, Long empId, Integer excludeLeaveId, Long firstDate, Long lastDate, Integer[] status) {
        Map map = new HashMap();
        map.put("empId", empId);
        map.put("leaveTypeId", leaveTypeId);
        map.put("firstDate", firstDate);
        map.put("lastDate", lastDate);
        map.put("array", status);
        if (excludeLeaveId != null) {
            map.put("leaveId", excludeLeaveId);
        }
        return waLeaveDaytimeMapper.listLeaveDaytime(map);
    }

    /**
     * 检查员工请假配额是否足够
     *
     * @param daytimeList
     * @param empId       员工ID
     * @param waLeaveType
     * @param leaveId
     * @param lang
     * @return
     */
    @Deprecated
    public String checkEmpLeaveQuotaDetail(List<WaLeaveDaytime> daytimeList, Long empId, WaLeaveType waLeaveType, Integer leaveId, String lang, List<Integer> leaveYearList) {
        Integer leaveTypeId = waLeaveType.getLeaveTypeId();
        String belongOrgId = waLeaveType.getBelongOrgid();

        List<EmpQuotaDTO> quotaList = waCommonService.getEmpQuotaList(waLeaveType.getLeaveTypeId(), waLeaveType.getLeaveType(), empId, "asc", leaveYearList);
        if (CollectionUtils.isNotEmpty(quotaList)) {
            //查询所有配额的最早开始时间和最晚结束时间
            final Long[] minDate = {null};
            final Long[] maxDate = {null};
            quotaList.forEach(quota -> {
                if (minDate[0] == null || quota.getStartDate() < minDate[0]) {
                    minDate[0] = quota.getStartDate();
                }

                if (maxDate[0] == null || quota.getLastDate() > maxDate[0]) {
                    maxDate[0] = quota.getLastDate();
                }
            });

            List<WaLeaveDaytime> allDaytimeList = new ArrayList<>();//须扣减配额的请假数据
            if (waLeaveType.getLeaveType() != 3) {//如归假期类型为非调休，则去查待审核的休假单据，此部分休假配额需做冻结处理
                List<WaLeaveDaytime> frozeLeaveList = this.listFrozeLeave(waLeaveType.getLeaveType(), leaveTypeId, empId, leaveId, minDate[0], maxDate[0]);
                if (CollectionUtils.isNotEmpty(frozeLeaveList)) {
                    allDaytimeList.addAll(frozeLeaveList);
                }
            }
            allDaytimeList.addAll(daytimeList);

            if (StringUtils.isNotBlank(waLeaveType.getQuotaSorts())) {//按照设置的配额扣减顺序进行配额扣减
                List<EmpQuotaUseDTO> quotaUseDTOList = waCommonService.getEmpQuotaDetailOrderBySorts(belongOrgId, empId, quotaList, false, waLeaveType.getQuotaSorts());
                if (CollectionUtils.isNotEmpty(quotaUseDTOList)) {
                    final BigDecimal[] totalKyQuota = {new BigDecimal(0)};//总的可用额度
                    quotaUseDTOList.forEach(quotaDTO -> {
                        if (quotaDTO.getKeQuota() > 0) {
                            totalKyQuota[0] = totalKyQuota[0].add(new BigDecimal(quotaDTO.getKeQuota()));
                        }
                    });

                    //扣减配额
                    for (WaLeaveDaytime daytime : allDaytimeList) {
                        if (daytime.getTimeDuration() <= 0) {
                            continue;
                        }
                        Long leaveDate = daytime.getLeaveDate();
                        BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                        for (EmpQuotaUseDTO quotaDTO : quotaUseDTOList) {
                            if (dayDuration.floatValue() <= 0) {
                                break;
                            }
                            if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                BigDecimal surplusQuota = new BigDecimal(quotaDTO.getKeQuota());//剩余配额
                                if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {
                                    //额度足够
                                    surplusQuota = surplusQuota.subtract(dayDuration);
                                    dayDuration = new BigDecimal(0);
                                } else {
                                    //额度不够
                                    dayDuration = dayDuration.subtract(surplusQuota);
                                    surplusQuota = new BigDecimal(0);
                                }
                                quotaDTO.setKeQuota(surplusQuota.floatValue());//更新剩余额度
                            }
                        }
                        daytime.setTimeDuration(dayDuration.floatValue());
                    }
                    //检查配额是否足够
                    List<WaLeaveDaytime> surplusList = allDaytimeList.stream().filter(daytime -> daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(surplusList)) {
                        return this.getTimeResultMsg(totalKyQuota[0], waLeaveType.getAcctTimeType(), waLeaveType.getLeaveName(), lang, "L006852");
                    }
                }
            } else {//兼容旧的扣减逻辑
                //查询可用配额详情列表
                Map<String, List<EmpQuotaUseDTO>> quotaDetailMap = waCommonService.listWaEmpQuotaDetail(belongOrgId, empId, quotaList, false);
                if (CollectionUtils.isEmpty(quotaDetailMap.get("remainQuotas")) && CollectionUtils.isEmpty(quotaDetailMap.get("curYearQuotas"))) {//没有额度信息，不能请假
                    return messageResource.getMessage("L005760", new Object[]{}, new Locale(lang));
                }
                final BigDecimal[] totalKyQuota = {new BigDecimal(0)};

                //先扣留存
                List<EmpQuotaUseDTO> remainQuotas = quotaDetailMap.get("remainQuotas");
                if (CollectionUtils.isNotEmpty(remainQuotas)) {
                    remainQuotas.forEach(quotaDTO -> {
                        if (quotaDTO.getKeQuota() > 0) {
                            totalKyQuota[0] = totalKyQuota[0].add(new BigDecimal(quotaDTO.getKeQuota()));
                        }
                    });
                    for (WaLeaveDaytime daytime : allDaytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                            //开始扣减配额
                            for (EmpQuotaUseDTO quotaDTO : remainQuotas) {
                                if (dayDuration.floatValue() <= 0) {
                                    break;
                                }
                                if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                    BigDecimal surplusQuota = new BigDecimal(quotaDTO.getKeQuota());//剩余配额
                                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {//额度足够
                                        surplusQuota = surplusQuota.subtract(dayDuration);
                                        dayDuration = new BigDecimal(0);
                                    } else {//额度不够
                                        dayDuration = dayDuration.subtract(surplusQuota);
                                        surplusQuota = new BigDecimal(0);
                                    }
                                    quotaDTO.setKeQuota(surplusQuota.floatValue());//更新剩余额度
                                }
                            }
                            daytime.setTimeDuration(dayDuration.floatValue());
                        }
                    }
                }
                //再扣减本年和调整
                List<EmpQuotaUseDTO> curYearQuotas = quotaDetailMap.get("curYearQuotas");
                if (CollectionUtils.isNotEmpty(curYearQuotas)) {
                    curYearQuotas.forEach(quotaDTO -> {
                        if (quotaDTO.getKeQuota() > 0) {
                            totalKyQuota[0] = totalKyQuota[0].add(new BigDecimal(quotaDTO.getKeQuota()));
                        }
                    });
                    for (WaLeaveDaytime daytime : allDaytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                            for (EmpQuotaUseDTO quotaDTO : curYearQuotas) {
                                if (dayDuration.floatValue() <= 0) {
                                    break;
                                }
                                if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                    BigDecimal surplusQuota = new BigDecimal(quotaDTO.getKeQuota());//剩余配额
                                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {//额度足够
                                        surplusQuota = surplusQuota.subtract(dayDuration);
                                        dayDuration = new BigDecimal(0);
                                    } else {//额度不够
                                        dayDuration = dayDuration.subtract(surplusQuota);
                                        surplusQuota = new BigDecimal(0);
                                    }
                                    quotaDTO.setKeQuota(surplusQuota.floatValue());//更新剩余额度
                                }
                            }
                            daytime.setTimeDuration(dayDuration.floatValue());
                        }
                    }
                }

                //检查配额是否足够
                List<WaLeaveDaytime> surplusList = allDaytimeList.stream().filter(daytime -> daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(surplusList)) {
                    return this.getTimeResultMsg(totalKyQuota[0], waLeaveType.getAcctTimeType(), waLeaveType.getLeaveName(), lang, "L006852");
                }
            }
        } else {//没有额度信息，不能请假
            return messageResource.getMessage("L005760", new Object[]{}, new Locale(lang));
        }
        return "";
    }

    /**
     * 休假配额扣减
     *
     * @param daytimeList
     * @param empId       员工ID
     * @param belongOrgId 租户
     * @param leaveTypeId
     * @param leaveType
     * @param isNegative
     * @param lang
     * @return
     * @throws Exception
     */
    @Deprecated
    @Transactional
    public String deductionEmpQuotaDetail(List<WaLeaveDaytime> daytimeList, Long empId, String belongOrgId, Integer leaveTypeId,
                                          Integer leaveType, Boolean isNegative, String lang) throws Exception {
        if (CollectionUtils.isNotEmpty(daytimeList)) {
            //开启调休配额按照年份去生成维护
            String genQuotaByYear = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongOrgId + RedisKeyDefine.IF_GEN_TXQUOTA_BY_YEAR);
            //提取休假年份
            List<Integer> periodYears = new ArrayList<>();
            if (leaveType != 3 || "1".equals(genQuotaByYear)) {
                daytimeList.forEach(row -> {
                    try {
                        Integer leaveYear = DateUtilExt.getTimeYear(row.getLeaveDate());
                        if (periodYears.indexOf(leaveYear) == -1) {
                            periodYears.add(leaveYear);
                        }
                    } catch (ParseException e) {
                        log.error(e.getMessage(), e);
                    }
                });
            }
            //查询配额
            List<EmpQuotaDTO> quotaList = waCommonService.getEmpQuotaList(leaveTypeId, leaveType, empId, "asc", periodYears);
            //配额扣减
            waCommonService.deductLeaveQuota(quotaList, daytimeList, empId, belongOrgId, leaveTypeId, isNegative, lang, false);
            //重新计算次年留存配额
            waCommonService.updateRemainDay(ConvertHelper.longConvert(SessionHolder.getUserId()), belongOrgId, empId, leaveTypeId, leaveType, periodYears);
        }
        return "";
    }

    /**
     * 请假额度扣减校验
     *
     * @param empId       员工ID
     * @param leavetime
     * @param waLeaveType
     * @param startTime
     * @param leaveId
     * @return
     */
    @Deprecated
    public String processEmpQuotaNew(Long empId, BigDecimal leavetime, WaLeaveType waLeaveType,
                                     long startTime, long endTime, Integer leaveId, SessionBean sessionBean) {
        Integer leaveTypeId = waLeaveType.getLeaveTypeId();
        String belongOrgId = waLeaveType.getBelongOrgid();
        Integer acctTimeType = waLeaveType.getAcctTimeType();
        String leaveTypeName = waLeaveType.getLeaveName();

        //员工假期配额
        List<WaEmpQuota> quotaList = this.getWaEmpQuota(leaveTypeId, empId, "asc");
        if (CollectionUtils.isNotEmpty(quotaList)) {
            WaEmpQuota empQuotaTmp = quotaList.get(0);

            //冻结配额
            BigDecimal frozeDuration = getEmpLeaveTypeFrozeDuration(waLeaveType.getLeaveType(), leaveTypeId, empId, leaveId,
                    empQuotaTmp.getStartDate(), empQuotaTmp.getLastDate());

            BigDecimal leaveTimeDuration = new BigDecimal(leavetime.floatValue());//请假时长

            Float ratio = waCommonService.getConvertRatio(belongOrgId, empId);//计算试用期额度折算比

            //查询假期额度是否试用期折算
            Map<Integer, WaLeaveSetting> leaveQuotaSetMap = new HashMap<>();
            List<Integer> listQuotaSettingId = new ArrayList<>();
            quotaList.stream().forEach(row -> listQuotaSettingId.add(row.getQuotaSettingId()));

            WaLeaveSettingExample settingExample = new WaLeaveSettingExample();
            settingExample.createCriteria().andQuotaSettingIdIn(listQuotaSettingId);
            List<WaLeaveSetting> settingList = waLeaveSettingMapper.selectByExample(settingExample);
            settingList.stream().forEach(row -> {
                if (!leaveQuotaSetMap.containsKey(row.getQuotaSettingId())) {
                    if (row.getIsTrialConvert() == null) {
                        row.setIsTrialConvert(false);
                    }
                    if (row.getFreezingRules() == null) {
                        row.setFreezingRules((short) 0);
                    }
                    leaveQuotaSetMap.put(row.getQuotaSettingId(), row);
                }
            });

            BigDecimal remainDuration = new BigDecimal(0);//留存剩余额度
            //留存
            for (WaEmpQuota empQuota : quotaList) {
                //校验有效期
                Long lastDate = empQuota.getLastDate();
                if (endTime > lastDate) {
                    continue;
                }
                long remainValidDate = empQuota.getRemainValidDate() == null ? 0l : empQuota.getRemainValidDate();
                BigDecimal remainDayBig = new BigDecimal(empQuota.getRemainDay());
                if (empQuota.getRemainUsedDay() == null) {
                    empQuota.setRemainUsedDay(0f);
                }
                if (empQuota.getDeductionDay() == null) {
                    empQuota.setDeductionDay(0f);
                }
                if (remainValidDate >= startTime) {//留成可以使用。
                    BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(empQuota.getRemainUsedDay()));//可用留成数
                    if (acctTimeType == 1) {//以天为单位，需要计算最大使用留成天数
                        Long invLc = (remainValidDate - startTime) / (24 * 60 * 60);
                        if (invLc >= 0) {
                            invLc = invLc + 1;
                        }
                        if (kyRemain.floatValue() > invLc.floatValue()) {
                            kyRemain = new BigDecimal(invLc);//最大使用留成天数
                        }
                    }
                    WaLeaveSetting setting = leaveQuotaSetMap.get(empQuota.getQuotaSettingId());
                    Boolean isTrialConvert = setting.getIsTrialConvert();//试用期额度是否折算

                    //额度折算，向下取整
                    if (isTrialConvert && ratio != null) {
                        kyRemain = kyRemain.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                    }
                    if (kyRemain.floatValue() > 0) {
                        if (kyRemain.compareTo(frozeDuration) >= 0) {//留成数大于需要扣除额额度数
                            //留存剩余
                            BigDecimal remainSurplus = kyRemain.subtract(frozeDuration);
                            if (remainSurplus.floatValue() > 0) {
                                remainDuration = remainDuration.add(remainSurplus);
                            }
                            frozeDuration = new BigDecimal(0);
                        } else {//留成不够，扣除所有留成
                            frozeDuration = frozeDuration.subtract(kyRemain);
                        }
                    }
                }
            }
            BigDecimal totalTimeDuration = leaveTimeDuration.add(frozeDuration);
            BigDecimal totalKeQuota = new BigDecimal(0);//总的可用额度

            if (remainDuration.floatValue() > 0) {
                totalKeQuota = totalKeQuota.add(remainDuration);
            }

            //扣除留存
            if (remainDuration.compareTo(totalTimeDuration) >= 0) {//留成数大于需要扣除额额度数
                totalTimeDuration = new BigDecimal(0);
            } else {//留成不够，扣除所有留成
                totalTimeDuration = totalTimeDuration.subtract(remainDuration);
            }

            if (totalTimeDuration.floatValue() > 0) {
                //当前可用额度
                for (WaEmpQuota empQuota : quotaList) {
                    BigDecimal keQuota = new BigDecimal(empQuota.getNowQuota()).subtract(new BigDecimal(empQuota.getDeductionDay())).subtract(new BigDecimal(empQuota.getUsedDay()));

                    WaLeaveSetting setting = leaveQuotaSetMap.get(empQuota.getQuotaSettingId());
                    if (setting.getFreezingRules() != 2) {//当年配额冻结
                        Boolean isTrialConvert = setting.getIsTrialConvert();//试用期额度是否折算
                        //额度折算，向下取整
                        if (isTrialConvert && ratio != null) {
                            keQuota = keQuota.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                        }
                        if (keQuota.floatValue() > 0) {
                            totalKeQuota = totalKeQuota.add(keQuota);
                            if (keQuota.compareTo(totalTimeDuration) >= 0) {
                                totalTimeDuration = new BigDecimal(0);
                                break;
                            } else {//额度不够
                                totalTimeDuration = totalTimeDuration.subtract(keQuota);
                            }
                        }
                    }
                }
            }
            if (totalTimeDuration.floatValue() > 0) {
                //额度不够，不能请假
                return getTimeResultMsg(totalKeQuota, acctTimeType, leaveTypeName, sessionBean.getLanguage(), "L005926");
            }
        } else {
            //没有额度信息，不能请假
            return messageResource.getMessage("L005760", new Object[]{}, new Locale(sessionBean.getLanguage()));
        }
        return "";
    }

    /**
     * 请假额度扣减
     *
     * @param quotaList
     * @param leaveTime
     * @param startTime
     * @param timeUnit
     */
    @Transactional
    @Deprecated
    public void processQuotaSigleNew(List<WaEmpQuota> quotaList, BigDecimal leaveTime, long startTime, Integer timeUnit, Boolean isNegative) throws Exception {
        if (CollectionUtils.isNotEmpty(quotaList)) {
            BigDecimal totalLeaveTime = new BigDecimal(0);
            if (leaveTime != null) {
                totalLeaveTime = new BigDecimal(leaveTime.floatValue());
            }
            //扣除留存
            for (WaEmpQuota empQuota : quotaList) {
                if (totalLeaveTime.floatValue() <= 0) {
                    break;
                }
                //假期配额有效期
                Long lastDate = empQuota.getLastDate();
                if (startTime > lastDate) {
                    continue;
                }
                if (empQuota.getRemainUsedDay() == null) {
                    empQuota.setRemainUsedDay(0f);
                }
                //留存有效期
                long remainValidDate = empQuota.getRemainValidDate() == null ? 0l : empQuota.getRemainValidDate();
                //留存天数或者小时
                BigDecimal remainDayBig = new BigDecimal(empQuota.getRemainDay());
                if (remainValidDate >= startTime) {//留成可以使用。
                    BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(empQuota.getRemainUsedDay()));//可用留成数
                    if (timeUnit.intValue() == 1) {//以天为单位，需要计算最大使用留成天数
                        Long invLc = (remainValidDate - startTime) / (24 * 60 * 60);
                        if (invLc >= 0) {
                            invLc = invLc + 1;
                        }
                        if (kyRemain.floatValue() > invLc.floatValue()) {
                            kyRemain = new BigDecimal(invLc);//最大使用留成天数
                        }
                    }
                    if (kyRemain.floatValue() > 0) {
                        if (kyRemain.compareTo(totalLeaveTime) >= 0) {//留成数大于需要扣除额额度数
                            empQuota.setRemainUsedDay(((new BigDecimal(empQuota.getRemainUsedDay())).add(totalLeaveTime)).floatValue());
                            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                            return;
                        } else {//留成不够，扣除所有留成
                            BigDecimal usedRemain = (new BigDecimal(empQuota.getRemainUsedDay())).add(kyRemain);
                            empQuota.setRemainUsedDay(usedRemain.floatValue());
                            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                            totalLeaveTime = totalLeaveTime.subtract(kyRemain);
                        }
                    }
                }
            }
            if (totalLeaveTime.floatValue() > 0) {
                for (WaEmpQuota empQuota : quotaList) {
                    //留成处理完，下面是可用额度
                    Float deductionDay = empQuota.getDeductionDay();
                    if (deductionDay == null) deductionDay = 0f;
                    Float usedDay = empQuota.getUsedDay();
                    if (usedDay == null) usedDay = 0f;
                    BigDecimal usedDayDec = new BigDecimal(usedDay);
                    //剩余额度
                    BigDecimal keQuota = new BigDecimal(empQuota.getNowQuota()).subtract(new BigDecimal(deductionDay)).subtract(usedDayDec);
                    if (keQuota.floatValue() > 0) {
                        if (keQuota.compareTo(totalLeaveTime) >= 0) {
                            empQuota.setUsedDay((new BigDecimal(empQuota.getUsedDay()).add(totalLeaveTime)).floatValue());
                            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                            return;
                        } else {//额度不够
                            keQuota = keQuota.floatValue() < 0 ? new BigDecimal(0) : keQuota;
                            totalLeaveTime = totalLeaveTime.subtract(keQuota);
                            empQuota.setUsedDay((new BigDecimal(empQuota.getUsedDay()).add(keQuota)).floatValue());
                            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                        }
                    }
                }
            }
            if (totalLeaveTime.floatValue() > 0 && isNegative) {
                //额度不够，扣除掉第一个配额类型的额度
                WaEmpQuota firstEmpQuota = quotaList.get(0);
                firstEmpQuota.setUsedDay((new BigDecimal(firstEmpQuota.getUsedDay()).add(totalLeaveTime)).floatValue());
                waEmpQuotaMapper.updateByPrimaryKeySelective(firstEmpQuota);
            }
        }
    }

    private String timeStr(Integer timeUnit, float timeDuration, SessionBean sessionBean) {
        NumberFormat nf = new DecimalFormat("#.####");
        String dayMessqge = messageResource.getMessage("L002076", new Object[]{}, new Locale(sessionBean.getLanguage()));
        String HourMessqge = messageResource.getMessage("L002307", new Object[]{}, new Locale(sessionBean.getLanguage()));
        String MinMessqge = messageResource.getMessage("L000550", new Object[]{}, new Locale(sessionBean.getLanguage()));

        if (timeUnit == 1) {
            return nf.format(timeDuration) + dayMessqge;
        } else if (timeUnit == 2) {
            BigDecimal b = new BigDecimal(timeDuration / 60);
            BigDecimal c = new BigDecimal(timeDuration % 60);
            if (c.intValue() > 0) {
                return b.intValue() + HourMessqge + c.intValue() + MinMessqge;
            } else {
                return b.intValue() + HourMessqge;
            }
        }
        return "";
    }

    /**
     * 请假额度扣减校验
     *
     * @param empId             员工ID
     * @param LeavetimeDuration
     * @param waLeaveType
     * @param startTime
     * @param leaveId
     * @return
     */
    public String processEmpQuota(Long empId, BigDecimal LeavetimeDuration, WaLeaveType waLeaveType, long startTime, long endTime, Integer leaveId, SessionBean sessionBean) {
        List<WaEmpQuota> quotaList = this.getWaEmpQuota(waLeaveType.getLeaveTypeId(), empId, "asc");
        if (quotaList == null || quotaList.size() < 1) {
            deleteLeave(leaveId);
//            return "没有额度信息，不能请假";
            return messageResource.getMessage("L005760", new Object[]{}, new Locale(sessionBean.getLanguage()));
        }
        if (LeavetimeDuration == null || LeavetimeDuration.floatValue() == 0) {
            deleteLeave(leaveId);
//            return "请假时间为0";
            return messageResource.getMessage("L005761", new Object[]{}, new Locale(sessionBean.getLanguage()));
        }
        BigDecimal totalTimeDuration = new BigDecimal(LeavetimeDuration.floatValue());
        WaEmpQuota empQuotaTmp = quotaList.get(0);
        Map map = new HashMap();
        map.put("empId", empId);
        map.put("leaveTypeId", waLeaveType.getLeaveTypeId());
        map.put("firstDate", empQuotaTmp.getStartDate());
        map.put("lastDate", empQuotaTmp.getLastDate());
        map.put("array", new Integer[]{1});
        map.put("leaveId", leaveId);
        Float frozeNum = waEmpLeaveMapper.getLeaveTypeTotal(map);
        frozeNum = (frozeNum == null ? 0f : frozeNum);
        totalTimeDuration = totalTimeDuration.add(new BigDecimal(frozeNum));
        //计算试用期额度折算比
        Float ratio = waCommonService.getConvertRatio(waLeaveType.getBelongOrgid(), empId);

        Map<Integer, WaLeaveSetting> leaveQuotaSetMap = new HashMap<>();
        List<Integer> listQuotaSettingId = new ArrayList<>();
        quotaList.stream().forEach(row -> listQuotaSettingId.add(row.getQuotaSettingId()));

        WaLeaveSettingExample settingExample = new WaLeaveSettingExample();
        settingExample.createCriteria().andQuotaSettingIdIn(listQuotaSettingId);
        List<WaLeaveSetting> settingList = waLeaveSettingMapper.selectByExample(settingExample);
        settingList.stream().forEach(row -> {
            if (!leaveQuotaSetMap.containsKey(row.getQuotaSettingId())) {
                if (row.getIsTrialConvert() == null) {
                    row.setIsTrialConvert(false);
                }
                if (row.getFreezingRules() == null) {
                    row.setFreezingRules((short) 0);
                }
                leaveQuotaSetMap.put(row.getQuotaSettingId(), row);
            }
        });

        for (WaEmpQuota empQuota : quotaList) {
            WaLeaveSetting waLeaveSetting = leaveQuotaSetMap.get(empQuota.getQuotaSettingId());
            boolean isTrialConvert = waLeaveSetting.getIsTrialConvert();//试用期额度是否折算

            //校验有效期
            Long lastDate = empQuota.getLastDate();
            if (endTime > lastDate) {
                continue;
            }
            if (totalTimeDuration.floatValue() == 0) {
                break;
            }
            long remainValidDate = empQuota.getRemainValidDate() == null ? 0l : empQuota.getRemainValidDate();
            BigDecimal remainDayBig = new BigDecimal(empQuota.getRemainDay());
            if (empQuota.getRemainUsedDay() == null) {
                empQuota.setRemainUsedDay(0f);
            }
            if (empQuota.getDeductionDay() == null) {
                empQuota.setDeductionDay(0f);
            }
            if (remainValidDate >= startTime) {//留成可以使用。
                BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(empQuota.getRemainUsedDay()));//可用留成数
                if (waLeaveType.getAcctTimeType().intValue() == 1) {//以天为单位，需要计算最大使用留成天数
                    Long invLc = (remainValidDate - startTime) / (24 * 60 * 60);
                    if (invLc >= 0) {
                        invLc = invLc + 1;
                    }
                    if (kyRemain.floatValue() > invLc.floatValue()) {
                        kyRemain = new BigDecimal(invLc);//最大使用留成天数
                    }
                }
                //额度折算，向下取整
                if (isTrialConvert && ratio != null) {
                    kyRemain = kyRemain.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                }
                if (kyRemain.floatValue() > 0) {
                    if (kyRemain.compareTo(totalTimeDuration) >= 0) {//留成数大于需要扣除额额度数
                        totalTimeDuration = new BigDecimal(0);
                        break;
                    } else {//留成不够，扣除所有留成。
                        totalTimeDuration = totalTimeDuration.subtract(kyRemain);
                    }
                }
            }
            //留成处理完，下面是可用额度 请假配额的上限校验由本年配额改为当前剩余
            if (waLeaveSetting.getFreezingRules() != 2) {
                BigDecimal keQuota = new BigDecimal(empQuota.getNowQuota()).subtract(new BigDecimal(empQuota.getDeductionDay())).
                        subtract(new BigDecimal(empQuota.getUsedDay()));
                //额度折算，向下取整
                if (isTrialConvert && ratio != null) {
                    keQuota = keQuota.multiply(new BigDecimal(ratio)).setScale(0, BigDecimal.ROUND_DOWN);
                }
                if (keQuota.floatValue() > 0) {
                    if (keQuota.compareTo(totalTimeDuration) >= 0) {
                        totalTimeDuration = new BigDecimal(0);
                        break;
                    } else {//额度不够
                        totalTimeDuration = totalTimeDuration.subtract(keQuota);
                    }
                }
            }
        }
        if (totalTimeDuration.floatValue() > 0) {
            deleteLeave(leaveId);
//            return "额度不够，不能请假";
            return messageResource.getMessage("L005762", new Object[]{}, new Locale(sessionBean.getLanguage()));
        }
        return "";
    }

    /**
     * 请假额度扣减
     *
     * @param quotaList
     * @param LeavetimeDuration
     * @param startTime
     * @param timeUnit
     */
    @Transactional
    @Deprecated
    public void processQuotaSigle(List<WaEmpQuota> quotaList, BigDecimal LeavetimeDuration, long startTime, Integer timeUnit, Boolean isNegative) throws Exception {
        if (quotaList != null && !quotaList.isEmpty()) {
            BigDecimal totalTimeDuration2 = new BigDecimal(0);
            if (LeavetimeDuration != null) {
                totalTimeDuration2 = new BigDecimal(LeavetimeDuration.floatValue());
            }
            for (WaEmpQuota empQuota : quotaList) {
                if (totalTimeDuration2.floatValue() <= 0) {
                    break;
                }
                //假期配额有效期
                Long lastDate = empQuota.getLastDate();
                if (startTime > lastDate) {
                    continue;
                }
                if (empQuota.getRemainUsedDay() == null) {
                    empQuota.setRemainUsedDay(0f);
                }
                //留存有效期
                long remainValidDate = empQuota.getRemainValidDate() == null ? 0l : empQuota.getRemainValidDate();
                //留存天数或者小时
                BigDecimal remainDayBig = new BigDecimal(empQuota.getRemainDay());
                if (remainValidDate >= startTime) {//留成可以使用。
                    BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(empQuota.getRemainUsedDay()));//可用留成数
                    if (timeUnit.intValue() == 1) {//以天为单位，需要计算最大使用留成天数
                        Long invLc = (remainValidDate - startTime) / (24 * 60 * 60);
                        if (invLc >= 0) {
                            invLc = invLc + 1;
                        }
                        if (kyRemain.floatValue() > invLc.floatValue()) {
                            kyRemain = new BigDecimal(invLc);//最大使用留成天数
                        }
                    }
                    if (kyRemain.floatValue() > 0) {
                        if (kyRemain.compareTo(totalTimeDuration2) >= 0) {//留成数大于需要扣除额额度数
                            empQuota.setRemainUsedDay(((new BigDecimal(empQuota.getRemainUsedDay())).add(totalTimeDuration2)).floatValue());
                            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                            return;
                        } else {//留成不够，扣除所有留成
                            BigDecimal usedRemain = (new BigDecimal(empQuota.getRemainUsedDay())).add(kyRemain);
                            empQuota.setRemainUsedDay(usedRemain.floatValue());
                            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                            totalTimeDuration2 = totalTimeDuration2.subtract(kyRemain);
                        }
                    }
                }
                if (totalTimeDuration2.compareTo(BigDecimal.ZERO) == 1) {
                    //留成处理完，下面是可用额度
                    Float deductionDay = empQuota.getDeductionDay();
                    if (deductionDay == null) deductionDay = 0f;
                    Float usedDay = empQuota.getUsedDay();
                    if (usedDay == null) usedDay = 0f;
                    BigDecimal usedDayDec = new BigDecimal(usedDay);
                    //剩余额度
                    BigDecimal keQuota = new BigDecimal(empQuota.getNowQuota()).subtract(new BigDecimal(deductionDay)).subtract(usedDayDec);
                    if (keQuota.floatValue() > 0) {
                        if (keQuota.compareTo(totalTimeDuration2) >= 0) {
                            empQuota.setUsedDay((new BigDecimal(empQuota.getUsedDay()).add(totalTimeDuration2)).floatValue());
                            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                            return;
                        } else {//额度不够
                            keQuota = keQuota.floatValue() < 0 ? new BigDecimal(0) : keQuota;
                            totalTimeDuration2 = totalTimeDuration2.subtract(keQuota);
                            empQuota.setUsedDay((new BigDecimal(empQuota.getUsedDay()).add(keQuota)).floatValue());
                            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                        }
                    }
                }
            }

            //校验额度是否足够
            if (totalTimeDuration2.compareTo(BigDecimal.ZERO) == 1 && isNegative) {
                //额度不够，扣除掉第一个配额类型的额度
                WaEmpQuota firstEmpQuota = quotaList.get(0);
                firstEmpQuota.setUsedDay((new BigDecimal(firstEmpQuota.getUsedDay()).add(totalTimeDuration2)).floatValue());
                waEmpQuotaMapper.updateByPrimaryKeySelective(firstEmpQuota);
            }
        }
    }


    public void processQuotaSigle(List<WaEmpQuota> quotaList, BigDecimal totalTimeDuration2, long startTime, Integer timeUnit,
                                  Integer leaveId, Integer leaveTimeId) {
        List<WaLeaveDaytime> leaveDaylist = getLeaveDayMap(leaveId, leaveTimeId);
        BigDecimal kyLcLeaveNum = new BigDecimal(0f);
        boolean flag = true;
        for (WaEmpQuota empQuota : quotaList) {
            long remainValidDate = empQuota.getRemainValidDate() == null ? 0l : empQuota.getRemainValidDate();
            if (flag) {
                for (WaLeaveDaytime daytime : leaveDaylist) {
                    if (daytime.getTimeDuration().floatValue() > 0 && daytime.getLeaveDate().longValue() <= remainValidDate) {
                        kyLcLeaveNum = kyLcLeaveNum.add(new BigDecimal(daytime.getTimeDuration()));
                    }
                }
                flag = false;
            }
            BigDecimal remainDayBig = new BigDecimal(empQuota.getRemainDay());
            if (empQuota.getRemainUsedDay() == null) {
                empQuota.setRemainUsedDay(0f);
            }

            if (remainValidDate >= startTime) {//留成可以使用。
                BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(empQuota.getRemainUsedDay()));//可用留成数
                System.out.println("假期，" + leaveId + "配额类型：" + empQuota + "留存有效期：" + remainValidDate +
                        "------------------------------------------------->>初始可用留存天数" + kyRemain);
                if (timeUnit.intValue() == 1) {//以天为单位，需要计算最大使用留成天数
                    Long invLc = (remainValidDate - startTime) / (24 * 60 * 60);
                    if (invLc >= 0) {
                        invLc = invLc + 1;
                    }
                    if (kyRemain.floatValue() > invLc.floatValue()) {
                        kyRemain = new BigDecimal(invLc);//最大使用留成天数
                    }
                    if (kyRemain.floatValue() >= kyLcLeaveNum.floatValue()) {
                        kyRemain = kyLcLeaveNum;
                        kyLcLeaveNum = new BigDecimal(0f);
                    } else {
                        kyLcLeaveNum = kyLcLeaveNum.subtract(kyRemain);
                    }
                }
                System.out.println("假期，" + leaveId + "配额类型：" + empQuota + "留存有效期：" + remainValidDate +
                        "------------------------------------------------->>可用留存天数" + kyRemain);
                if (kyRemain.floatValue() > 0) {
                    if (kyRemain.compareTo(totalTimeDuration2) >= 0) {//留成数大于需要扣除额额度数
                        empQuota.setRemainUsedDay(((new BigDecimal(empQuota.getRemainUsedDay())).add(totalTimeDuration2)).floatValue());
                        waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                        totalTimeDuration2 = new BigDecimal(0);
                        return;
                    } else {//留成不够，扣除所有留成。
                        if (empQuota.getRemainUsedDay() == null) {
                            empQuota.setRemainUsedDay(0f);
                        }
                        BigDecimal usedRemain = (new BigDecimal(empQuota.getRemainUsedDay())).add(kyRemain);
                        empQuota.setRemainUsedDay(usedRemain.floatValue());
                        waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                        totalTimeDuration2 = totalTimeDuration2.subtract(kyRemain);
                    }
                }
            }
        }
        for (WaEmpQuota empQuota : quotaList) {
            //留成处理完，下面是可用额度
            Float deductionDay = empQuota.getDeductionDay();
            if (deductionDay == null) deductionDay = 0f;
            Float usedDay = empQuota.getUsedDay();
            if (usedDay == null) usedDay = 0f;
            BigDecimal keQuota = new BigDecimal(empQuota.getQuotaDay()).subtract(new BigDecimal(deductionDay)).subtract(new BigDecimal(usedDay));
            if (keQuota.compareTo(totalTimeDuration2) >= 0) {
                empQuota.setUsedDay((new BigDecimal(empQuota.getUsedDay()).add(totalTimeDuration2)).floatValue());
                waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                return;
            } else {//额度不够
                totalTimeDuration2 = totalTimeDuration2.subtract(keQuota);
                empQuota.setUsedDay((new BigDecimal(empQuota.getUsedDay()).add(keQuota)).floatValue());
                waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
            }
        }
    }

    public void processQuotaSigle(List<WaEmpQuota> quotaList, BigDecimal totalTimeDuration2, long startTime, Integer timeUnit) {
        for (WaEmpQuota empQuota : quotaList) {
            if (totalTimeDuration2.floatValue() == 0) {
                break;
            }
            long remainValidDate = empQuota.getRemainValidDate() == null ? 0l : empQuota.getRemainValidDate();
            BigDecimal remainDayBig = new BigDecimal(empQuota.getRemainDay());
            if (empQuota.getRemainUsedDay() == null) {
                empQuota.setRemainUsedDay(0f);
            }

            if (remainValidDate >= startTime) {//留成可以使用。
                BigDecimal kyRemain = remainDayBig.subtract(new BigDecimal(empQuota.getRemainUsedDay()));//可用留成数
                if (timeUnit.intValue() == 1) {//以天为单位，需要计算最大使用留成天数
                    Long invLc = (remainValidDate - startTime) / (24 * 60 * 60);
                    if (invLc >= 0) {
                        invLc = invLc + 1;
                    }
                    if (kyRemain.floatValue() > invLc.floatValue()) {
                        kyRemain = new BigDecimal(invLc);//最大使用留成天数
                    }
                }
                if (kyRemain.floatValue() > 0) {
                    if (kyRemain.compareTo(totalTimeDuration2) >= 0) {//留成数大于需要扣除额额度数

                        empQuota.setRemainUsedDay(((new BigDecimal(empQuota.getRemainUsedDay())).add(totalTimeDuration2)).floatValue());
//						empQuota.setEmpQuotaId(empQuota.getEmpQuotaId());
                        waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                        totalTimeDuration2 = new BigDecimal(0);
                        return;
                    } else {//留成不够，扣除所有留成。

                        if (empQuota.getRemainUsedDay() == null) {
                            empQuota.setRemainUsedDay(0f);
                        }
                        BigDecimal usedRemain = (new BigDecimal(empQuota.getRemainUsedDay())).add(kyRemain);
                        empQuota.setRemainUsedDay(usedRemain.floatValue());
//						empQuota.setEmpQuotaId(empQuota.getEmpQuotaId());
                        waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
                        totalTimeDuration2 = totalTimeDuration2.subtract(kyRemain);
                    }
                }
            }
            //留成处理完，下面是可用额度
            Float deductionDay = empQuota.getDeductionDay();
            if (deductionDay == null) deductionDay = 0f;
            Float usedDay = empQuota.getUsedDay();
            if (usedDay == null) usedDay = 0f;
            BigDecimal keQuota = new BigDecimal(empQuota.getQuotaDay()).subtract(new BigDecimal(deductionDay)).subtract(new BigDecimal(usedDay));
            if (keQuota.compareTo(totalTimeDuration2) >= 0) {

                empQuota.setUsedDay((new BigDecimal(empQuota.getUsedDay()).add(totalTimeDuration2)).floatValue());
//				empQuota.setEmpQuotaId(empQuota.getEmpQuotaId());
                waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
//				totalTimeDuration2= new BigDecimal(0);
                return;
            } else {//额度不够
                totalTimeDuration2 = totalTimeDuration2.subtract(keQuota);

                empQuota.setUsedDay((new BigDecimal(empQuota.getUsedDay()).add(keQuota)).floatValue());
//				empQuota2.setEmpQuotaId(empQuota.getEmpQuotaId());
                waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
            }
        }
    }

    private List<WaLeaveDaytime> getLeaveDayMap(Integer leaveId, Integer leaveTimeId) {
        WaLeaveDaytimeExample example = new WaLeaveDaytimeExample();
        WaLeaveDaytimeExample.Criteria criteria = example.createCriteria();
        if (leaveId != null) {
            criteria.andLeaveIdEqualTo(leaveId);
        }
        if (leaveTimeId != null) {
            criteria.andLeaveTimeIdEqualTo(leaveTimeId);
        }
        example.setOrderByClause("leave_date asc");
        List<WaLeaveDaytime> list = waLeaveDaytimeMapper.selectByExample(example);
        return list;
    }

    public boolean getRegesitEnable(String version) {
        Jedis jedis = RedisService.getResource();
        String urlAddr = "";
        try {
            String url = jedis.get("SYS_REG_ENABLE");
            if (StringUtils.isBlank(version)) {
                return false;
            } else if (StringUtils.isBlank(url)) {
                return false;
            } else return url.equals(version);
        } catch (RuntimeException e) {
            jedis.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            jedis.close();
        }
        return false;
    }

    public List<String> getLast2Months() {
        String[] last12Months = new String[12];
        List<String> list = new ArrayList<String>();
        Calendar cal = Calendar.getInstance();
        for (int i = 0; i < 2; i++) {
            if ((cal.get(Calendar.MONTH) + 1) - i < 1) {
                last12Months[11 - i] = cal.get(Calendar.YEAR) - 1
                        + fillZero((cal.get(Calendar.MONTH) + 1 - i + 12 * 1));
            } else {
                last12Months[11 - i] = cal.get(Calendar.YEAR) + fillZero((cal.get(Calendar.MONTH) + 1 - i));
            }
            list.add(last12Months[11 - i]);
        }
        Collections.reverse(list);
        return list;
    }

    public String fillZero(int i) {
        String str = "";
        if (i > 0 && i < 10) {
            str = "0" + i;
        } else {
            str = "" + i;
        }
        return str;

    }

    public void addWaEmpQuota(Integer proSettingId, Long empId, BigDecimal addQuota) {
        WaEmpQuotaExample example = new WaEmpQuotaExample();
        WaEmpQuotaExample.Criteria criteria = example.createCriteria();
        criteria.andEmpidEqualTo(empId);
        criteria.andQuotaSettingIdEqualTo(proSettingId);

        List<WaEmpQuota> empQuotaList = waEmpQuotaMapper.selectByExample(example);
        //取得比例
        WaEmpQuota empQuota = null;
        if (empQuotaList.size() > 0) {
            empQuota = empQuotaList.get(0);
            BigDecimal quotaDay = new BigDecimal(empQuota.getQuotaDay());
            BigDecimal nowQuotaDay = new BigDecimal(empQuota.getNowQuota());
            empQuota.setQuotaDay(quotaDay.add(addQuota).floatValue());
            empQuota.setNowQuota(nowQuotaDay.add(addQuota).floatValue());
            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
        } else {
            empQuota = new WaEmpQuota();
            empQuota.setQuotaSettingId(proSettingId);
            empQuota.setEmpid(empId);
            empQuota.setStartDate(0l);
            empQuota.setLastDate(DateUtil.getTimesampByDateStr("9999.12.31"));
            empQuota.setRemainDay(0f);
            empQuota.setDeductionDay(0f);
            empQuota.setQuotaDay(addQuota.floatValue());
            empQuota.setNowQuota(addQuota.floatValue());
            empQuota.setUsedDay(0f);
            empQuota.setPeriodYear((short) 1);
            waEmpQuotaMapper.insertSelective(empQuota);
        }
    }

    public boolean isContinueLeave(WaEmpLeave empLeave, BigDecimal num) {
//        String startDate = empLeave.getStartDate();
        Integer leaveType = empLeave.getLeaveTypeId();
//        BigDecimal numTmp = num.subtract(new BigDecimal(empLeave.getTotalTimeDuration()));//得到相差天数
        //查询倒退天数是否是这个假期
        long endDate = DateUtil.convertStringToDateTime(empLeave.getStartDate(), "yyyy-MM-dd", null);
        long startDate = endDate - 3600 * 24 * num.intValue();
        Map map = new HashMap();
        map.put("empId", empLeave.getEmpid());
        map.put("leaveTypeId", empLeave.getLeaveTypeId());
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        Float resultNum = waEmpLeaveMapper.getJgLeave(map);
        if (resultNum == null) {
            return false;
        } else return num.compareTo(new BigDecimal(resultNum)) == 0;
    }

    /**
     * 获取调休配额类型ID
     *
     * @param belongOrgId                  租户
     * @param empId                        员工ID
     * @param autoCreateQuotaSettingByYear 是否按照年份自动生成配额类型
     * @return
     */
    public Integer getTxQuotaSettingId(String belongOrgId, Long empId, Boolean autoCreateQuotaSettingByYear) {
        //查询员工所在考勤分组设置的调休类型，如果没有分配则自动生成一个配额类型
        Integer settingId = waConfigMapper.getRelaxLeaveSettingId(empId);
        if (settingId == null) {
            WaLeaveSetting waLeaveSetting = new WaLeaveSetting();
            waLeaveSetting.setCrtuser(1L);
            waLeaveSetting.setCrttime(System.currentTimeMillis() / 1000);
            waLeaveSetting.setBelongOrgid(belongOrgId);
            waLeaveSetting.setQuotaSettingName("调休");
            waLeaveSetting.setLeaveType((short) 4);
            waLeaveSetting.setLeaveTypeId(getOTLeaveTypeId(belongOrgId));
            //结转类型 1结转，2付现，3作废
            waLeaveSetting.setCarryOverType(1);
            //结转时长 如90天，填写90；3个月填写3
            waLeaveSetting.setCarryOverTimeNum(0);
            //结转时长单位类型 1天，2月,3年(如为年，则时长只能是1)
            waLeaveSetting.setCarryOverTimeUnit(1);

            if (BooleanUtils.isTrue(autoCreateQuotaSettingByYear)) {
                waLeaveSetting.setQuotaPeriodType(1);
                waLeaveSetting.setStartDate(101L);
                waLeaveSetting.setEndDate(1231L);
            } else {
                //配额有效期类型 1自然年，2合同年，3自定义
                waLeaveSetting.setQuotaPeriodType(3);
                waLeaveSetting.setStartDate(DateUtil.getOnlyDate());
                waLeaveSetting.setEndDate(DateUtil.getTimesampByDateStr("9999.12.31"));
            }
            waLeaveSettingMapper.insertSelective(waLeaveSetting);
            settingId = waLeaveSetting.getQuotaSettingId();
        }
        return settingId;
    }

    /**
     * 获取员工当前时间生效的调休配额
     *
     * @param empId     员工ID
     * @param settingId
     * @return
     */
    public List<WaEmpQuota> getEmpEffectiveTxQuotaList(Long empId, Integer settingId) {
        WaEmpQuotaExample quotaExample = new WaEmpQuotaExample();
        WaEmpQuotaExample.Criteria quotaCriteria = quotaExample.createCriteria();
        quotaCriteria.andEmpidEqualTo(empId);
        quotaCriteria.andQuotaSettingIdEqualTo(settingId);
        quotaCriteria.andStartDateLessThanOrEqualTo(DateUtil.getOnlyDate());
        quotaCriteria.andLastDateGreaterThanOrEqualTo(DateUtil.getOnlyDate());
        return waEmpQuotaMapper.selectByExample(quotaExample);
    }

    /**
     * 根据年份查询员工调休配额
     *
     * @param empId     员工ID
     * @param settingId
     * @return
     */
    public List<WaEmpQuota> getEmpTxQuotaListByYear(Long empId, Integer settingId, Short year) {
        WaEmpQuotaExample quotaExample = new WaEmpQuotaExample();
        WaEmpQuotaExample.Criteria quotaCriteria = quotaExample.createCriteria();
        quotaCriteria.andEmpidEqualTo(empId);
        quotaCriteria.andQuotaSettingIdEqualTo(settingId);
        quotaCriteria.andPeriodYearEqualTo(year);
        return waEmpQuotaMapper.selectByExample(quotaExample);
    }

    /**
     * 生成或者更新员工调休配额
     *
     * @param belongOrgId 租户
     * @param empId       员工ID
     * @param otNum
     * @return
     */
    public WaEmpQuota addOrUpdateEmpTxQuota(String belongOrgId, Long empId, BigDecimal otNum) {
        //获取调休配额类型ID
        Integer settingId = getTxQuotaSettingId(belongOrgId, empId, false);
        //获取员工当前时间生效的调休配额
        List<WaEmpQuota> empQuotaList = getEmpEffectiveTxQuotaList(empId, settingId);
        WaEmpQuota empQuota = null;
        if (CollectionUtils.isNotEmpty(empQuotaList)) {
            empQuota = empQuotaList.get(0);
            BigDecimal quotaDay = new BigDecimal(empQuota.getQuotaDay());
            quotaDay = quotaDay.add(otNum);
            WaEmpQuota empQuota2 = new WaEmpQuota();
            empQuota2.setEmpQuotaId(empQuota.getEmpQuotaId());
            empQuota2.setQuotaDay(quotaDay.floatValue());
            empQuota2.setNowQuota(new BigDecimal(empQuota.getNowQuota()).add(otNum).floatValue());
            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota2);
        } else {
            empQuota = new WaEmpQuota();
            empQuota.setQuotaSettingId(settingId);
            empQuota.setEmpid(empId);
            empQuota.setStartDate(0L);
            empQuota.setLastDate(DateUtil.getTimesampByDateStr("9999.12.31"));
            empQuota.setRemainDay(0f);
            empQuota.setDeductionDay(0f);
            empQuota.setQuotaDay(otNum.floatValue());
            empQuota.setNowQuota(otNum.floatValue());
            empQuota.setUsedDay(0f);
            empQuota.setPeriodYear((short) 1);
            waEmpQuotaMapper.insertSelective(empQuota);
        }
        return empQuota;
    }

    /**
     * 根据年份生成或者更新员工调休配额
     *
     * @param belongOrgId 租户
     * @param empId       员工ID
     * @param otNum
     * @return
     */
    public WaEmpQuota addOrUpdateEmpTxQuotaByYear(String belongOrgId, Long empId, BigDecimal otNum) {
        //获取调休配额类型ID
        Integer settingId = getTxQuotaSettingId(belongOrgId, empId, true);
        Short peringYear = DateUtilExt.getNowYear().shortValue();
        //获取员工调休配额
        List<WaEmpQuota> empQuotaList = getEmpTxQuotaListByYear(empId, settingId, peringYear);
        WaEmpQuota empQuota = null;
        if (CollectionUtils.isNotEmpty(empQuotaList)) {
            empQuota = empQuotaList.get(0);
            BigDecimal quotaDay = new BigDecimal(empQuota.getQuotaDay());
            quotaDay = quotaDay.add(otNum);
            WaEmpQuota empQuota2 = new WaEmpQuota();
            empQuota2.setEmpQuotaId(empQuota.getEmpQuotaId());
            empQuota2.setQuotaDay(quotaDay.floatValue());
            empQuota2.setNowQuota(new BigDecimal(empQuota.getNowQuota()).add(otNum).floatValue());
            waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota2);
        } else {
            empQuota = new WaEmpQuota();
            empQuota.setQuotaSettingId(settingId);
            empQuota.setEmpid(empId);
            empQuota.setRemainDay(0f);
            empQuota.setDeductionDay(0f);
            empQuota.setQuotaDay(otNum.floatValue());
            empQuota.setNowQuota(otNum.floatValue());
            empQuota.setUsedDay(0f);
            empQuota.setPeriodYear(peringYear);
            //计算配额有效期
            WaLeaveSetting leaveSetting = waLeaveSettingMapper.selectByPrimaryKey(settingId);
            setLeaveTypeQuotaCycle(leaveSetting, peringYear, empQuota);
            waEmpQuotaMapper.insertSelective(empQuota);
        }
        return empQuota;
    }


    /**
     * 设置配额有效期
     *
     * @param leaveSetting
     * @param year
     * @param empQuota
     * @throws ParseException
     */
    public void setLeaveTypeQuotaCycle(WaLeaveSetting leaveSetting, Short year, WaEmpQuota empQuota) {
        if (leaveSetting == null) {
            throw new CDException("未设置调休配额类型");
        }
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTimeInMillis(df.parse(df.format(cal.getTime())).getTime());
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        //周期类型1:自然年2:合同年
        if (leaveSetting.getQuotaPeriodType().equals(1)) {
            int month = leaveSetting.getStartDate().intValue() / 100;
            int day = leaveSetting.getStartDate().intValue() % 100;
            cal.set(year, month - 1, day);
            empQuota.setStartDate(cal.getTimeInMillis() / 1000);
            cal.set(month == 1 && day == 1 ? year : year + 1, leaveSetting.getEndDate().intValue() / 100 - 1, leaveSetting.getEndDate().intValue() % 100, 23, 59, 59);
            empQuota.setLastDate(cal.getTimeInMillis() / 1000);
        } else if (leaveSetting.getQuotaPeriodType().equals(2)) {
            SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empQuota.getEmpid());
            if (empInfo != null && empInfo.getHireDate() != null) {
                Calendar hireDate = Calendar.getInstance();
                hireDate.setTimeInMillis(empInfo.getHireDate() * 1000);
                if (hireDate.get(Calendar.MONTH) * 100 + hireDate.get(Calendar.DATE) > cal.get(Calendar.MONTH) * 100 + cal.get(Calendar.DATE)) {
                    empQuota.setPeriodYear((short) (year - 1));
                    cal.set(year - 1, hireDate.get(Calendar.MONTH), hireDate.get(Calendar.DATE));
                } else {
                    cal.set(year, hireDate.get(Calendar.MONTH), hireDate.get(Calendar.DATE));
                }
                empQuota.setStartDate(cal.getTimeInMillis() / 1000);
                empQuota.setLastDate(DateUtils.addYears(cal.getTime(), 1).getTime() / 1000 - 1);
            }
        }
    }

    /**
     * 加班转调休配额计算
     *
     * @param empId 员工ID
     * @param otNum
     * @return
     */
    @Transactional
    public WaEmpQuota calOtToTxQuota(String belongOrgId, Long empId, BigDecimal otNum) {
        //开启调休配额按照年份去生成维护
        String genQuotaByYear = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongOrgId + RedisKeyDefine.IF_GEN_TXQUOTA_BY_YEAR);
        if ("1".equals(genQuotaByYear)) {
            return addOrUpdateEmpTxQuotaByYear(belongOrgId, empId, otNum);
        } else {
            //不按照年份维护员工调休配额，一个人只有一条永远生效的调休配额
            return addOrUpdateEmpTxQuota(belongOrgId, empId, otNum);
        }
    }

    /**
     * 加班转调休配配额
     *
     * @param ot2
     */
    @Deprecated
    @Transactional
    public void addOtQuota(WaEmpOvertime ot2) throws Exception {
        //调休配额会通过核算去生成
        return;
//        if (ot2.getCompensateType() != null && ot2.getCompensateType() == 2) {
//            Integer applyEmpid = ot2.getEmpid();
//            SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(applyEmpid);
//            if (empInfo == null) {
//                throw new CDException("员工不存在");
//            }
//            //生成调休配额
//            this.addEmpTxQuota(empInfo.getCorpid(), empInfo.getBelongOrgId(), empInfo.getEmpid(), ot2.getOtId());
//        }
    }

    /**
     * 根据员工ID、加班时间类型、加班补偿类型 获取加班类型
     *
     * @param empId          员工ID
     * @param compensateType
     * @param dateType
     * @return
     */
    public WaOvertimeType getEmpOvertimeType(Long empId, Integer compensateType, Integer dateType) {
        Map params = new HashMap();
        params.put("empid", empId);
        params.put("compensateType", compensateType);
        params.put("dateType", dateType);

        List<WaOvertimeType> overtimeTypeList = waOvertimeTypeMapper.getEmpOtTypeList(params);
        if (CollectionUtils.isNotEmpty(overtimeTypeList)) {
            return overtimeTypeList.get(0);
        }
        return null;
    }

    public Integer getOTLeaveTypeId(String belongOrgId) {
        WaLeaveTypeExample example = new WaLeaveTypeExample();
        WaLeaveTypeExample.Criteria criteria = example.createCriteria();
        criteria.andBelongOrgidEqualTo(belongOrgId);
        criteria.andLeaveTypeEqualTo(3);
        List<WaLeaveType> list = waLeaveTypeMapper.selectByExample(example);
        if (list.isEmpty()) {
            return 0;
        } else {
            return list.get(0).getLeaveTypeId();
        }
    }

    public Integer getTimeScale(Long empid, Integer belongOrgId) {
        Map param = new HashMap();
        param.put("belongOrgId", belongOrgId);
        param.put("time", DateUtil.getOnlyDate());
        param.put("empid", empid);
        Integer timeScale = 15;
        List<Integer> timeScaleList = waMapper.getTimeScaleList(param);
        if (CollectionUtils.isNotEmpty(timeScaleList)) {
            Integer integer = timeScaleList.get(0);
            if (integer == null) {
                return timeScale;
            } else {
                timeScale = timeScaleList.get(0);
            }

        }
        return timeScale;
    }

    /**
     * 根据公司ID和假期类型ID查询假期类型数据
     *
     * @param belongOrgId 租户
     * @param leaveTypeId
     * @return
     */
    public WaLeaveType getWaLeaveTypeById(String belongOrgId, Integer leaveTypeId) {
        WaLeaveTypeExample leaveTypeExample = new WaLeaveTypeExample();
        leaveTypeExample.createCriteria().andBelongOrgidEqualTo(belongOrgId).andLeaveTypeIdEqualTo(leaveTypeId);
        List<WaLeaveType> leaveTypeList = waLeaveTypeMapper.selectByExample(leaveTypeExample);
        if (CollectionUtils.isNotEmpty(leaveTypeList)) {
            return leaveTypeList.get(0);
        }
        return null;
    }

    /**
     * 根据公司ID和员工id查询员工信息
     *
     * @param belongOrgId 租户
     * @param empId       员工ID
     * @return
     */
    public SysEmpInfo getSysEmpInfoById(String belongOrgId, Long empId) {
        SysEmpInfoExample example = new SysEmpInfoExample();
        example.createCriteria().andBelongOrgIdEqualTo(belongOrgId)
                .andEmpidEqualTo(empId).andDeletedEqualTo((short) 0);
        List<SysEmpInfo> empInfoList = sysEmpInfoMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(empInfoList)) {
            return empInfoList.get(0);
        }
        return null;
    }

    /**
     * 求出是上半天或下半天的时长
     *
     * @param halfDay
     * @param shiftDef
     * @return
     */
    public int getHalfTime(String halfDay, WaShiftDef shiftDef, int halfWorkTime) {
        // 如果定义了半天时间点，则按这个时间拆分上半天和下半天
        if (BooleanUtils.isTrue(shiftDef.getIsHalfdayTime()) && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() > 0) {
            if (halfDay.equals(BaseConst.LEAVE_HALF_SHALF_DAY)) {
                // 上半天(时间) = 定义的时间点 - 上班开始时间
                halfWorkTime = shiftDef.getHalfdayTime() - shiftDef.getStartTime();
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) && shiftDef.getHalfdayTime() >= shiftDef.getNoonRestStart()) {
                    if (shiftDef.getHalfdayTime() > shiftDef.getNoonRestEnd()) {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                    } else {
                        halfWorkTime -= shiftDef.getHalfdayTime() - shiftDef.getNoonRestStart();
                    }
                }
            } else if (halfDay.equals(BaseConst.LEAVE_HALF_EHALF_DAY)) {
                // 下半天(时间) = 下班截止时间 - 定义的时间点 【如果下班截止时间跨夜则设置为次日】
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {//跨夜
                    Integer nextDaytime = shiftDef.getEndTime() + (24 * 60);
                    halfWorkTime = nextDaytime - shiftDef.getHalfdayTime();
                } else {
                    halfWorkTime = shiftDef.getEndTime() - shiftDef.getHalfdayTime();
                }
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) && shiftDef.getHalfdayTime() < shiftDef.getNoonRestEnd()) {
                    if (shiftDef.getHalfdayTime() < shiftDef.getNoonRestStart()) {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                    } else {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getHalfdayTime();
                    }
                }
            }
        } else if (shiftDef.getIsNoonRest()) { // 或者按 中午休息时间来拆分
            if (halfDay.equals(BaseConst.LEAVE_HALF_SHALF_DAY)) {
                halfWorkTime = shiftDef.getNoonRestStart() - shiftDef.getStartTime();
            } else if (halfDay.equals(BaseConst.LEAVE_HALF_EHALF_DAY)) {
                halfWorkTime = shiftDef.getEndTime() - shiftDef.getNoonRestEnd();
            }
        } else {
            // 以上条件都没有满足，则按工作时长的一半进行拆分
        }
        return halfWorkTime;
    }

    /**
     * 检查跨夜加班是否归属至加班开始日期
     *
     * @param empId
     * @return
     */
    public boolean checkOvertimeBelong(Long empId) {
        Map groupParams = new HashMap<>();
        groupParams.put("empid", empId);
        List<Map> listEmpWaGroup = waMapper.listEmpWaGroup(groupParams);
        if (CollectionUtils.isNotEmpty(listEmpWaGroup)) {
            Map groupMap = listEmpWaGroup.get(0);
            Integer overtimeBelong = (Integer) groupMap.get("overtime_belong");
            return null != overtimeBelong && overtimeBelong.equals(OvertimeBelongType.OVERTIME_START_DATE.getIndex());
        }
        return false;
    }
}