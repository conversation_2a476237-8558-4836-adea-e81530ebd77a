package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.interfaces.dto.OverTimeDto;
import com.caidaocloud.attendance.service.interfaces.dto.OverTimeTypeReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OverTimeInfoDto;
import com.caidaocloud.web.Result;

import java.util.List;
import java.util.Map;

/**
 * @Author: Aaron.Chen
 * @Date: 2021/7/14 19:07
 * @Description:
 **/
public interface IOverTimeService {
    void deleteOtTypeByIds(List<Integer> otIds);

    List<Map> getOtTypeList(PageBean pageBean);

    Result<Boolean> saveOtType(OverTimeDto record);

    OverTimeDto getOtType(Integer id);
    
    void deleteOtType(Integer id);

    void deleteGroupOtType(Integer groupId, Integer id);

    List<OverTimeInfoDto> getOtTypes(Integer groupId, Integer status);

    Integer getGroupOtRelCount(String belongId, Integer id);

    Integer getGroupLeaveRelCount(String belongId, Integer id);

    Integer getRelBetweenTypeAndOt(Integer id);

    Result<Boolean> enable(OverTimeTypeReqDto dto);

    Result<Boolean> disable(OverTimeTypeReqDto dto);

    Boolean checkOvertimeTypeUsed(Integer overtimeTypeId);
}
