package com.caidaocloud.attendance.service.application.dto.clock;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 当日打卡分析上下文
 */
@Data
@Builder
public class TodayClockFilterContext {
    // 基础信息
    private List<WaRegisterRecordDo> regList;
    private Long empId;
    private Long clockDate;
    private Long nextDay;
    private ClockAnalyseDataCacheDto dataCacheDto;
    private Map<String, WaShiftDo> empShiftDoMap;

    // 当日信息
    private WaShiftDo todayShiftDo;
    private WaShiftDef todayShiftDef;
    private WaShiftDef todayShiftWorkTime;
    private boolean isTodayWorkday;
    private Long todayShiftStartTime;
    private Long todayShiftEndTime;
    private Long todayOffDutyEndTime;
    private Long todayOvertimeEndTime;
    private Long todayOvertimeClockEndTime;

    // 次日信息
    private WaShiftDo nextDayShiftDo;
    private WaShiftDef nextDayShiftDef;
    private WaShiftDef nextDayShiftWorkTime;
    private boolean isNextDayWorkday;
    private Long nextDayShiftStartTime;
    private Long nextDayShiftEndTime;
    private Long nextDayOnDutyStartTime;
    private Long nextDayOvertimeStartTime;
    private Long nextDayOvertimeClockStartTime;

    public static TodayClockFilterContext doBuild(FilterBelongTodayRegDto filterDto) {
        Long empId = filterDto.getEmpId();
        Long clockDate = filterDto.getClockDate();
        Long nextDay = DateUtil.addDate(clockDate * 1000, 1);
        ClockAnalyseDataCacheDto dataCacheDto = filterDto.getDataCacheDto();
        Map<String, WaShiftDo> empShiftDoMap = filterDto.getEmpShiftDoMap();

        // 当日班次信息
        WaShiftDo todayShiftDo = empShiftDoMap.get(empId + "_" + clockDate);
        WaShiftDef todayShiftDef = null;
        WaShiftDef todayShiftWorkTime = null;
        Long todayShiftStartTime = null;
        Long todayShiftEndTime = null;
        Long todayOffDutyEndTime = null;
        Long todayOvertimeEndTime = null;
        Long todayOvertimeClockEndTime = null;
        if (null != todayShiftDo) {
            todayShiftDef = todayShiftDo.doGetLastShiftDef();
            todayShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(todayShiftDef);

            todayShiftStartTime = clockDate + todayShiftWorkTime.doGetRealStartTime() * 60;
            todayShiftEndTime = CdWaShiftUtil.checkCrossNightV2(todayShiftWorkTime, todayShiftWorkTime.getDateType())
                    ? clockDate + (todayShiftWorkTime.getEndTime() + 1440) * 60L
                    : clockDate + todayShiftWorkTime.getEndTime() * 60L;

            if (null != todayShiftWorkTime.getOffDutyEndTime()) {
                todayOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(todayShiftWorkTime, todayShiftWorkTime.getDateType())
                        ? clockDate + (todayShiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                        : clockDate + todayShiftWorkTime.getOffDutyEndTime() * 60L;
            } else {
                todayOffDutyEndTime = clockDate + 86399;
            }

            todayOvertimeEndTime = dataCacheDto.doGetMaxOtEndTime(empId, clockDate);
            todayOvertimeClockEndTime = Optional.ofNullable(
                            ClockAnalyseCalDto.doGetShiftMaxOtEndTime(empId, clockDate, dataCacheDto, todayShiftDef))
                    .orElse(todayOvertimeEndTime);
        } else {
            todayOffDutyEndTime = clockDate + 86399;
        }

        // 次日班次信息
        WaShiftDo nextDayShiftDo = empShiftDoMap.get(empId + "_" + nextDay);
        WaShiftDef nextDayShiftDef = null;
        WaShiftDef nextDayShiftWorkTime = null;
        Long nextDayShiftStartTime = null;
        Long nextDayShiftEndTime = null;
        Long nextDayOnDutyStartTime = null;
        Long nextDayOvertimeStartTime = null;
        Long nextDayOvertimeClockStartTime = null;
        if (null != nextDayShiftDo) {
            nextDayShiftDef = nextDayShiftDo.doGetFirstShiftDef();
            nextDayShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(nextDayShiftDef);

            nextDayShiftStartTime = nextDay + nextDayShiftWorkTime.getStartTime() * 60L;
            nextDayShiftEndTime = CdWaShiftUtil.checkCrossNightV2(nextDayShiftWorkTime, nextDayShiftWorkTime.getDateType())
                    ? nextDay + (nextDayShiftWorkTime.getEndTime() + 1440) * 60L
                    : nextDay + nextDayShiftWorkTime.getEndTime() * 60L;

            nextDayOnDutyStartTime = ClockAnalyseCalDto.getNextDatFirstOnDutyStartTime(empId, clockDate, empShiftDoMap);

            nextDayOvertimeStartTime = dataCacheDto.doGetMinOtStartTime(empId, nextDay);
            nextDayOvertimeClockStartTime = Optional.ofNullable(
                            ClockAnalyseCalDto.doGetShiftMinOtStartTime(empId, nextDay, dataCacheDto, nextDayShiftDef))
                    .orElse(nextDayOvertimeStartTime);
        }

        return TodayClockFilterContext.builder()
                .regList(filterDto.getRegList())
                .empId(empId)
                .clockDate(clockDate)
                .nextDay(nextDay)
                .dataCacheDto(dataCacheDto)
                .empShiftDoMap(empShiftDoMap)

                // 当日信息
                .todayShiftDo(todayShiftDo)
                .todayShiftDef(todayShiftDef)
                .todayShiftWorkTime(todayShiftWorkTime)
                .isTodayWorkday(todayShiftDo != null && DateTypeEnum.DATE_TYP_1.getIndex().equals(todayShiftDo.getDateType()))
                .todayShiftStartTime(todayShiftStartTime)
                .todayShiftEndTime(todayShiftEndTime)
                .todayOffDutyEndTime(todayOffDutyEndTime)
                .todayOvertimeEndTime(todayOvertimeEndTime)
                .todayOvertimeClockEndTime(todayOvertimeClockEndTime)

                // 次日信息
                .nextDayShiftDo(nextDayShiftDo)
                .nextDayShiftDef(nextDayShiftDef)
                .nextDayShiftWorkTime(nextDayShiftWorkTime)
                .isNextDayWorkday(nextDayShiftDo != null && DateTypeEnum.DATE_TYP_1.getIndex().equals(nextDayShiftDo.getDateType()))
                .nextDayShiftStartTime(nextDayShiftStartTime)
                .nextDayShiftEndTime(nextDayShiftEndTime)
                .nextDayOnDutyStartTime(nextDayOnDutyStartTime)
                .nextDayOvertimeStartTime(nextDayOvertimeStartTime)
                .nextDayOvertimeClockStartTime(nextDayOvertimeClockStartTime)
                .build();
    }
}
