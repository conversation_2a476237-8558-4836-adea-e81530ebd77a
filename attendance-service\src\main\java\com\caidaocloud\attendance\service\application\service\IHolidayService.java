package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaCalendarGroup;
import com.caidao1.wa.mybatis.model.WaCalendarGroupRel;
import com.caidao1.wa.mybatis.model.WaHolidayCalendar;
import com.caidao1.wa.mybatis.model.WaLeaveCalendarDetail;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaWorktimeDo;
import com.caidaocloud.attendance.service.interfaces.dto.HolidayGroupDto;
import com.caidaocloud.web.Result;

import java.util.List;
import java.util.Map;

/**
 * 特殊日期
 *
 * <AUTHOR>
 * @Date 2021/4/12
 */
public interface IHolidayService {
    AttendancePageResult<HolidayGroupDto> getCalendarGroupPageList(PageBean pageBean);

    List<WaWorktimeDo> getWorktimeListByCalendarGroupId(String belongOrgId, Integer calendarGroupId);

    Result<Boolean> deleteHolidayGroupById(Integer id) throws Exception;

    int getCalendarGroupCountByName(String belongOrgId, Integer excludeCalendarGroupId, String groupName);

    int saveOrUpdateOtherDateType(WaHolidayCalendar holiday, String belongId, Long userId);

    int checkName(String belongOrgId, String name, Integer id);

    List getOtherDateList(String belongOrgId);

    WaHolidayCalendar getHolidayCalendarInfo(Integer holidayCalendarId, String belongOrgId);

    int getWaLeaveCalendarDetailCount(Integer holidayCalendarId);

    int delSetDateTypeById(Integer holidayCalendarId, String belongOrgId);

    int saveOtherDateDetailType(WaLeaveCalendarDetail holiday, String belongOrgId, Long userId) throws Exception;

    List<Map> getOtherDateTypeDetailList(PageBean pageBean, Integer holidayCalendarId);

    WaLeaveCalendarDetail getLeaveDetailByInfo(Integer detailId, String belongOrgId);

    int delSetDateDetailById(Integer calendarDetailId, String belongOrgId);

    List getDateGroupList(String belongOrgId);

    int saveCalendarGroup(WaCalendarGroup group, String belongOrgId, Long userId);

    int delCalendarGroupRelById(Integer groupRelId);

    int saveGroupRel(WaCalendarGroupRel group, String belongOrgId, Long userId);

    WaCalendarGroup getCalendarGroupById(Integer id, String belongOrgId);

    List<Map> getDateGroupReList(PageBean pageBean, Integer calendarGroupId, Integer groupRelId);

    WaCalendarGroupRel getCalendarGroupRelById(Integer id, String belongOrgId);
}
