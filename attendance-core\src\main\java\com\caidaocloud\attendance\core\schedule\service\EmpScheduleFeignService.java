package com.caidaocloud.attendance.core.schedule.service;

import com.caidaocloud.attendance.core.annoation.feign.EmpScheduleFeignClient;
import com.caidaocloud.attendance.core.schedule.dto.ListEmpScheduleQueryDto;
import com.caidaocloud.attendance.core.wa.vo.EmpMultiShiftInfoVo;
import com.caidaocloud.attendance.core.wa.vo.WorkingHourCalendarDateVo;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 员工排班
 *
 * <AUTHOR>
 * @Date 2025/2/14
 */
@Slf4j
@Service
public class EmpScheduleFeignService {
    @Autowired
    private EmpScheduleFeignClient empScheduleFeignClient;

    /**
     * 排班制员工班次查询
     *
     * @param queryDto
     * @return
     */
    public List<EmpMultiShiftInfoVo> listEmpRelCalendar(ListEmpScheduleQueryDto queryDto) {
        Result<List<EmpMultiShiftInfoVo>> result = empScheduleFeignClient.getEmpScheduleList(queryDto);
        if (null == result || !result.isSuccess()) {
            return Lists.newArrayList();
        }
        return result.getData();
    }

    /**
     * 查询员工工时日历
     *
     * @param empId
     * @param month
     * @return
     */
    public List<WorkingHourCalendarDateVo> getShiftByMonthByEmpId(Long empId, Integer month) {
        Result<List<WorkingHourCalendarDateVo>> result = empScheduleFeignClient.getShiftByMonthByEmpId(month, empId);
        if (null == result || !result.isSuccess()) {
            return Lists.newArrayList();
        }
        return result.getData();
    }
}
