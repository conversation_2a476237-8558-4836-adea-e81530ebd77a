package com.caidaocloud.attendance.core.wa.dto;

import com.caidao1.wa.mybatis.model.WaLeaveDaytimeExtPo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class EmpLeaveInfo {
    private Integer leave_id;
    private Long empid;
    private String time_slot;
    private Float total_time_duration;
    private Integer leave_type_id;
    private Long leave_date;
    private Integer start_time;
    private Integer end_time;
    private Float time_duration;
    private Short period_type;
    private Integer date_type;
    private Integer time_unit;
    private Long real_date;
    private String shalf_day;
    private String ehalf_day;
    private Integer province;
    private Integer city;
    private Integer leave_type;
    private Boolean link_outside_sign;
    private Float before_adjust_time_duration;
    private String leave_type_def_code;
    private Boolean is_rest_day;
    private Boolean is_legal_holiday;
    private EmpShiftInfo empShiftInfo;
    private Boolean isFlexibleWorking;
    private Long createTime;
    private Integer status;
    private Integer useShiftDefId;

    public static List<WaLeaveDaytimeExtPo> doConvert2ExtPo(List<EmpLeaveInfo> leaveInfos) {
        List<WaLeaveDaytimeExtPo> dayTimes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(leaveInfos)) {
            leaveInfos.forEach(l -> {
                WaLeaveDaytimeExtPo daytimeExt = new WaLeaveDaytimeExtPo();
                daytimeExt.setEmpid(l.getEmpid());
                daytimeExt.setLeaveId(l.getLeave_id());
                daytimeExt.setLeaveDate(l.getLeave_date());
                daytimeExt.setShalfDay(l.getShalf_day());
                daytimeExt.setEhalfDay(l.getEhalf_day());
                daytimeExt.setStartTime(l.getStart_time());
                daytimeExt.setEndTime(l.getEnd_time());
                daytimeExt.setPeriodType(l.getPeriod_type());
                daytimeExt.setTimeUnit(l.getTime_unit().shortValue());
                daytimeExt.setTimeDuration(l.getTime_duration());
                daytimeExt.setDateType(l.getDate_type());
                daytimeExt.setCreateTime(l.getCreateTime());
                daytimeExt.setUseShiftDefId(l.getUseShiftDefId());
                dayTimes.add(daytimeExt);
            });
        }
        return dayTimes;
    }
}
