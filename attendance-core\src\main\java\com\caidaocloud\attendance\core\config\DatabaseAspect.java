package com.caidaocloud.attendance.core.config;

import io.shardingjdbc.core.api.HintManager;
import io.shardingjdbc.core.hint.HintManagerHolder;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

@Aspect
@Component
@ConditionalOnBean(ShardingDbConfig.class)
public class DatabaseAspect {

    @Pointcut("execution(* com..mapper..*.*(..))")
    private void databasePointcut() {
    }

    /**
     * 这个方法设置了只进行分库路由，不会触发分表路由，若要进行分表路由，需另外实现
     *
     * @param pjp
     * @throws Throwable
     */
    @Before("databasePointcut()")
    public void baseDatabaseSelect(JoinPoint pjp) throws Throwable {
        if (!HintManagerHolder.isUseShardingHint()) {
            HintManager instance = HintManager.getInstance();
            instance.setDatabaseShardingValue("public");
        }
    }

}