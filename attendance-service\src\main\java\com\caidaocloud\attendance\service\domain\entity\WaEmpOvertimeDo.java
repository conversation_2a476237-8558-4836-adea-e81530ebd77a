package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.dto.EmpOvertimeDto;
import com.caidaocloud.attendance.service.domain.repository.IOtRecordRepository;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 员工加班
 *
 * <AUTHOR>
 * @Date 2021/3/22
 */
@Slf4j
@Data
@Service
public class WaEmpOvertimeDo {
    private Integer otId;
    private String otFormNo;
    private Long startTime;
    private Long endTime;
    private Long empid;
    private Integer otDuration;
    private String reason;
    private Long firstEmpid;
    private Integer approvalNum;
    private Short status;
    private Short dateType;
    private Long crtuser;
    private Long crttime;
    private Long lastEmpid;
    private Long lastApprovalTime;
    private Integer compensateType;
    private String revokeReason;
    private Long updtime;
    private Long upduser;
    private Integer forgid;
    private Integer optFlag;
    private Integer preOtId;
    private Integer overtimeTypeId;
    private Long batchId;
    private Long belongDate;

    // 表其他字段
    private String workno;
    private String empName;
    private String compensateTypeName;
    private String statusName;
    private String otDurationDesc;
    private String files;
    private String fileNames;
    private Long hireDate;
    private String workCity;
    private String orgName;
    private String fullPath;
    private Long applyTime;
    private String belongOrgId;
    private Integer wfFuncId;
    private Long endApplyTime;
    private Integer funcType;
    private String businessKey;
    private Float duration;
    private Long revokeId;
    private String overtimeTypeName;
    private Float carryDuration;
    private Integer detailId;
    private String processCode;
    private String i18nTypeName;
    private String dateTypeName;
    private Integer periodOtDuration;
    private Long realDate;

    @Autowired
    private IOtRecordRepository otRecordRepository;

    @CDText(exp = {"workCity" + TextAspect.PLACE}, classType = WaEmpOvertimeDo.class)
    public WaEmpOvertimeDo getOtDetailById(Long corpid, Long id) {
        return otRecordRepository.getOtDetailById(corpid, id);
    }

    public List<WaEmpOvertimeDo> getEmpOvertimeListByYmdDate(Long empId, Long startDate, Long endDate) {
        return otRecordRepository.getEmpOvertimeListByYmdDate(empId, startDate, endDate);
    }

    public PageResult<WaEmpOvertimeDo> getPageList(BasePage basePage, WaEmpOvertimeDo search) {
        return otRecordRepository.getEmpOvertimePageList(basePage, search);
    }

    public List<WaEmpOvertimeDo> getEmpOvertimeList(Long empid, Long startDate, Long endDate) {
        return otRecordRepository.getOverTimeList(empid, startDate, endDate);
    }

    public List<WaEmpOvertimeDo> getEmpOvertimeListByBelongDate(Long empId, Long startDate, Long endDate) {
        return otRecordRepository.getEmpOvertimeListByBelongDate(empId, startDate, endDate);
    }

    public PageResult<WaEmpOvertimeDo> getPageListOfPortal(QueryPageBean queryPageBean) {
        return otRecordRepository.getPageListOfPortal(queryPageBean);
    }

    public Integer checkOvertimeTypeUsed(String tenantId, Integer overtimeTypeId) {
        return otRecordRepository.checkOvertimeTypeUsed(tenantId, overtimeTypeId);
    }

    public List<WaEmpOvertimeDo> getEmpOvertimes(String tenantId, Long empId, Long startDate, Long endDate, Integer overtimeTypeId) {
        return otRecordRepository.getEmpOvertimes(tenantId, empId, startDate, endDate, overtimeTypeId);
    }

    @CDText(exp = {"workCity" + TextAspect.PLACE}, classType = WaEmpOvertimeDo.class)
    public WaEmpOvertimeDo getOtRevokeDetailById(String tenantId, Long id) {
        return otRecordRepository.getOtRevokeDetailById(tenantId, id);
    }

    public PageResult<WaEmpOvertimeDo> getRevokePageListOfPortal(QueryPageBean queryPageBean, BusinessCodeEnum workflowEnum) {
        return otRecordRepository.getRevokePageListOfPortal(queryPageBean, workflowEnum);
    }

    public List<WaEmpOvertimeDo> getEmpOtCompensatoryList(String tenantId, Long quotaId) {
        return otRecordRepository.getEmpOtCompensatoryList(tenantId, quotaId);
    }

    public List<WaEmpOvertimeDo> getListByBatchId(Long batchId) {
        return otRecordRepository.selectListByBatchId(batchId);
    }

    public List<EmpOvertimeDto> listOvertimeByEmps(String tenantId, long startTime, long endTime, List<String> empIds) {
        return otRecordRepository.listOvertimeByEmps(tenantId, startTime, endTime, empIds);
    }
}
