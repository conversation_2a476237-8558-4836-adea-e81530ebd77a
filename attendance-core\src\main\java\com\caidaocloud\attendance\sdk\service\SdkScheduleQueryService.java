package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.core.annoation.feign.ScheduleQueryFeignClient;
import com.caidaocloud.attendance.core.wa.dto.shift.ListScheduleQueryDto;
import com.caidaocloud.attendance.core.wa.vo.SdkListEmpShiftForLeaveVo;
import com.caidaocloud.attendance.core.wa.vo.WaShiftDefVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 员工排班查询
 *
 * <AUTHOR>
 * @Date 2025/2/15
 */
@Slf4j
@Service
public class SdkScheduleQueryService {
    @Autowired
    private ScheduleQueryFeignClient scheduleQueryFeignClient;

    public List<WaShiftDefVo> getEmpCalendarShiftList(ListScheduleQueryDto queryDto) {
        Result<List<WaShiftDefVo>> result = scheduleQueryFeignClient.getEmpCalendarShiftList(queryDto);
        if (null == result || !result.isSuccess()) {
            return Lists.newArrayList();
        }
        return result.getData();
    }

    public Map<String, WaShiftDefVo> getEmpCalendarShiftMap(String belongOrgId, Long startDate, Long endDate, List<Long> empIds) {
        Map<String, WaShiftDefVo> empShiftMap = new HashMap<>(16);
        ListScheduleQueryDto queryDto = new ListScheduleQueryDto();
        queryDto.setTenantId(belongOrgId);
        queryDto.setStartDate(startDate);
        queryDto.setEndDate(endDate);
        queryDto.setEmpIds(empIds);
        List<WaShiftDefVo> shiftDoList = getEmpCalendarShiftList(queryDto);
        if (CollectionUtils.isEmpty(shiftDoList)) {
            return empShiftMap;
        }
        Map<String, List<WaShiftDefVo>> shiftDoListMap = shiftDoList.stream()
                .collect(Collectors.groupingBy(it -> String.format("%s_%s", it.getEmpid(), it.getWorkDate())));
        shiftDoListMap.forEach((shiftKey, shiftList) -> {
            WaShiftDefVo shiftDo = ObjectConverter.convert(shiftList.get(0), WaShiftDefVo.class);
            shiftDo.setShiftList(shiftList);
            empShiftMap.put(shiftKey, shiftDo);
        });
        return empShiftMap;
    }

    public List<SdkListEmpShiftForLeaveVo> getEmpShiftForLeaveCancel(Integer leaveId, Long date, Integer cancelType) {
        Result<List<SdkListEmpShiftForLeaveVo>> result = scheduleQueryFeignClient.getEmpShiftForLeaveCancel(leaveId, date, cancelType);
        if (null == result || !result.isSuccess()) {
            return Lists.newArrayList();
        }
        return result.getData();
    }
}
