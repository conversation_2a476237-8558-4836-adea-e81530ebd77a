package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaEmpShiftDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpShiftPo;

import java.util.List;

/**
 * 员工日历档案
 *
 * <AUTHOR>
 * @Date 2021/3/4
 */
public interface IEmpShiftRepository {
    List<WaEmpShiftDo> getEmpShiftListByWorkCalendarId(String belongOrgId, Integer workCalendarId);

    int deleteByWorkCalendarId(Integer workCalendarId);

    List<WaEmpShiftDo> getEmpShiftInfoList(Long corpId, String belongOrgId, List<Long> empIds, Long startTime, Long endTime, Integer workCalendarId);

    AttendancePageResult<WaEmpShiftDo> getEmpShiftDetailListByCalendarId(AttendanceBasePage basePage,
                                                                         String belongOrgId, Integer workCalendarId,
                                                                         String filter, String effectiveStatus);

    void saveEmpShift(EmpShiftPo empShiftPo);

    List<EmpShiftPo> getEmpShiftByPeriod(Long empId, Integer empShiftId, String belongOrgId, Long startTime, Long endTime);

    void deleteEmpShifts(List<Integer> empShiftIds);

    WaEmpShiftDo getEmpShift(Integer empShiftId);

    void batchSave(List<EmpShiftPo> empShifts);

    List<EmpShiftPo> getEmpShiftByIds(List<Integer> empShiftIds);
}
