package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaOvertimeType;
import com.caidao1.wa.mybatis.model.WaWorktimeDetail;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.enums.AppTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.workflow.dto.WfAttachmentDto;
import com.caidaocloud.attendance.core.workflow.dto.WfBusinessDataDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.emp.EmpOrgPostInfo;
import com.caidaocloud.attendance.service.application.dto.overtime.BatchOvertimeDetailDto;
import com.caidaocloud.attendance.core.wa.dto.ot.GetOtTimeResultDto;
import com.caidaocloud.attendance.service.application.enums.CompensateTypeEnum;
import com.caidaocloud.attendance.service.application.enums.PreTimeUnitEnum;
import com.caidaocloud.attendance.service.application.service.IConfigService;
import com.caidaocloud.attendance.service.application.service.IOvertimeApplyService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.application.service.emp.WaEmpService;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.interfaces.dto.OverApplyRevokeDto;
import com.caidaocloud.attendance.service.interfaces.dto.OverApplySaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.common.WaKeyValue;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.BatchOvertimeApplyDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.BatchOvertimeQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.RevokeBatchOvertimeDto;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.BatchOvertimePageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeApplyTimeVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeDateVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeStatisticsVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.SysConfigsEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.workflow.enums.WfAttachmentTypeEnum;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量加班
 *
 * <AUTHOR>
 * @Date 2024/6/18
 */
@Slf4j
@Service
public class WaBatchOvertimeService {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    @Autowired
    private IWfService wfService;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private WorkflowCallBackService workflowCallBackService;
    @Autowired
    private IConfigService configService;
    @Autowired
    private IOvertimeApplyService overtimeApplyService;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private WaBatchOvertimeDo waBatchOvertimeDo;
    @Autowired
    private WaSobDo waSobDo;
    @Autowired
    private SysEmpInfoDo sysEmpInfoDo;
    @Autowired
    private WaEmpOvertimeDo waEmpOvertimeDo;
    @Autowired
    private OverTimeTypeDo overTimeTypeDo;
    @Autowired
    private WaEmpService waEmpService;

    /**
     * 加班统计
     *
     * @param empid
     * @param startDate
     * @param endDate
     * @return
     */
    public OvertimeStatisticsVo statistics(Long empid, Long startDate, Long endDate) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        endDate = endDate + 86399;
        List<Map> otList = waBatchOvertimeDo.getOtStatisticList(userInfo.getTenantId(), empid, startDate, endDate,
                Lists.newArrayList(ApprovalStatusEnum.IN_APPROVAL.getIndex(), ApprovalStatusEnum.PASSED.getIndex()));
        if (CollectionUtils.isEmpty(otList)) {
            return new OvertimeStatisticsVo();
        }
        OvertimeStatisticsVo overtimeStatisticsVo = new OvertimeStatisticsVo();
        for (Map map : otList) {
            Integer status = (Integer) map.get("status");
            Integer compensateType = (Integer) map.get("compensateType");
            int otDuration = null != map.get("otDuration") ? Integer.parseInt(String.valueOf(map.get("otDuration"))) : 0;
            if (ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(status)) {
                if (CompensateTypeEnum.WORK_PAID.ordinal() == compensateType) {
                    overtimeStatisticsVo.setApplyingWorkPaid((float) otDuration);
                } else if (CompensateTypeEnum.COMPENSATORY_LEAVE.ordinal() == compensateType) {
                    overtimeStatisticsVo.setApplyingCompensatoryLeave((float) otDuration);
                }
            } else {
                if (CompensateTypeEnum.WORK_PAID.ordinal() == compensateType) {
                    overtimeStatisticsVo.setAppliedWorkPaid((float) otDuration);
                } else if (CompensateTypeEnum.COMPENSATORY_LEAVE.ordinal() == compensateType) {
                    overtimeStatisticsVo.setAppliedCompensatoryLeave((float) otDuration);
                }
            }
        }

        // 汇总计算
        Float appliedWorkPaid = Optional.ofNullable(overtimeStatisticsVo.getAppliedWorkPaid()).orElse(0f);
        Float appliedCompensatoryLeave = Optional.ofNullable(overtimeStatisticsVo.getAppliedCompensatoryLeave()).orElse(0f);
        Float applyingWorkPaid = Optional.ofNullable(overtimeStatisticsVo.getApplyingWorkPaid()).orElse(0f);
        Float applyingCompensatoryLeave = Optional.ofNullable(overtimeStatisticsVo.getApplyingCompensatoryLeave()).orElse(0f);

        float workPaid = appliedWorkPaid + applyingWorkPaid;
        float compensatoryLeave = appliedCompensatoryLeave + applyingCompensatoryLeave;
        float totalDuration = workPaid + compensatoryLeave;

        // 分钟转小时
        overtimeStatisticsVo.setAppliedWorkPaid(BigDecimal.valueOf(appliedWorkPaid)
                .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue());

        overtimeStatisticsVo.setAppliedCompensatoryLeave(BigDecimal.valueOf(appliedCompensatoryLeave)
                .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue());

        overtimeStatisticsVo.setApplyingWorkPaid(BigDecimal.valueOf(applyingWorkPaid)
                .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue());

        overtimeStatisticsVo.setApplyingCompensatoryLeave(BigDecimal.valueOf(applyingCompensatoryLeave)
                .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue());

        overtimeStatisticsVo.setWorkPaid(BigDecimal.valueOf(workPaid)
                .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue());

        overtimeStatisticsVo.setCompensatoryLeave(BigDecimal.valueOf(compensatoryLeave)
                .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue());

        overtimeStatisticsVo.setTotalDuration(BigDecimal.valueOf(totalDuration)
                .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue());

        overtimeStatisticsVo.setUnit(2);
        overtimeStatisticsVo.setUnitTxt(PreTimeUnitEnum.getName(2));
        return overtimeStatisticsVo;
    }

    /**
     * 获取加班日期
     *
     * @param empid
     * @param startDate
     * @param endDate
     * @return
     */
    public List<OvertimeDateVo> listOtDate(Long empid, Long startDate, Long endDate) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(UserContext.getTenantId(), empid);
        if (empInfo == null) {
            return null;
        }
        // 查询员工排班
        int tmType = (empInfo.getTmType() == null || empInfo.getTmType() == 0) ? 1 : empInfo.getTmType();
        Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(empInfo.getBelongOrgId(),
                empid, tmType, startDate - 86400, endDate, empInfo.getWorktimeType(), true);
        if (MapUtils.isEmpty(pbMap)) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
        }
        List<OvertimeDateVo> otDateList = new ArrayList<>();
        Long eventDate = startDate;
        while (eventDate <= endDate) {
            WaWorktimeDetail detail = pbMap.get(eventDate);
            if (detail != null) {
                // 查询当天的加班类型
                List<WaOvertimeType> overtimeTypeList = overTimeTypeDo.getOvertimeTypeList(userInfo.getTenantId(), empid, eventDate, detail.getDateType());
                if (CollectionUtils.isNotEmpty(overtimeTypeList)) {
                    List<Map<String, Object>> overtimeTypeMapList = overtimeTypeList.stream().map(o -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("description", o.getDescription());
                        map.put("text", o.getTypeName());
                        map.put("value", o.getOvertimeTypeId());
                        map.put("compensateType", o.getCompensateType());
                        return map;
                    }).collect(Collectors.toList());

                    OvertimeDateVo overtimeDateVo = new OvertimeDateVo();
                    overtimeDateVo.setOtDate(eventDate);
                    overtimeDateVo.setOvertimeTypes(overtimeTypeMapList);
                    otDateList.add(overtimeDateVo);
                }
            }
            eventDate = eventDate + 86400;
        }
        return otDateList;
    }

    /**
     * 计算加班时长
     *
     * @param applyDto
     * @return
     */
    public OvertimeApplyTimeVo calTime(BatchOvertimeApplyDto applyDto) throws Exception {
        List<OverApplySaveDto> overtimeList = applyDto.getOvertimeList();
        if (CollectionUtils.isEmpty(overtimeList)) {
            // 请求参数异常
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201912", WebUtil.getRequest()));
        }
        BigDecimal totalTimeDuration = new BigDecimal(0);
        for (OverApplySaveDto overtimeSaveDto : overtimeList) {
            Result<GetOtTimeResultDto> getOtTimeResult = overtimeApplyService.getOtTotalTime(overtimeSaveDto);
            if (!getOtTimeResult.isSuccess()) {
                throw new ServerException(getOtTimeResult.getMsg());
            }
            GetOtTimeResultDto otTimeResultData = getOtTimeResult.getData();
            Double originalOtDuration = otTimeResultData.getOriginalOtDuration();
            totalTimeDuration = totalTimeDuration.add(BigDecimal.valueOf(originalOtDuration));
        }
        OvertimeApplyTimeVo overtimeApplyTimeVo = new OvertimeApplyTimeVo();
        overtimeApplyTimeVo.setOriginalOtDuration(totalTimeDuration.doubleValue());
        double duration = totalTimeDuration.doubleValue();
        if (duration % 60 > 0) {
            if (duration / 60 > 0) {
                overtimeApplyTimeVo.setOtDuration(messageResource.getMessage("L005572", new Object[]{duration / 60,
                                duration % 60},
                        new Locale(SessionHolder.getLang())));
            } else {
                overtimeApplyTimeVo.setOtDuration(messageResource.getMessage("L006821", new Object[]{duration % 60},
                        new Locale(SessionHolder.getLang())));
            }
        } else {
            overtimeApplyTimeVo.setOtDuration(messageResource.getMessage("L005573", new Object[]{duration / 60},
                    new Locale(SessionHolder.getLang())));
        }
        return overtimeApplyTimeVo;
    }

    /**
     * 保存加班申请
     *
     * @param applyDto
     * @param appType
     * @return
     * @throws Exception
     */
    public OvertimeApplyTimeVo save(BatchOvertimeApplyDto applyDto, AppTypeEnum appType) throws Exception {
        if (CollectionUtils.isEmpty(applyDto.getOvertimeList())) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201912", WebUtil.getRequest()));
        }
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(UserContext.getTenantId(), applyDto.getEmpid());
        if (empInfo == null) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, Boolean.FALSE).getMsg());
        }
        // 检查工作流
        Result<?> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.BATCH_OVERTIME.getCode());
        if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
        }
        Boolean workflowEnabledResultData = (Boolean) checkWorkflowEnableResult.getData();
        if (!workflowEnabledResultData) {
            if (configService.checkSwitchStatus(SysConfigsEnum.OVERTIME_WORKFLOW_SWITCH.name())) {
                throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
            }
        }
        // 保存单据
        WaBatchOvertimeDo waBatchOvertime = SpringUtil.getBean(WaBatchOvertimeService.class)
                .saveApply(applyDto, appType, empInfo);
        // 开启流程
        String wfBusKey = String.format("%s_%s", waBatchOvertime.getBatchId(), BusinessCodeEnum.BATCH_OVERTIME.getCode());
        if (!workflowEnabledResultData) {
            WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
            wfCallbackResultDto.setBusinessKey(wfBusKey);
            wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
            wfCallbackResultDto.setTenantId(empInfo.getBelongOrgId());
            workflowCallBackService.saveBatchOvertimeApproval(wfCallbackResultDto);
        } else {
            WfBeginWorkflowDto dto = new WfBeginWorkflowDto();
            dto.setFuncCode(BusinessCodeEnum.BATCH_OVERTIME.getCode());
            dto.setBusinessId(waBatchOvertime.getBatchId().toString());
            dto.setApplicantId(empInfo.getEmpid().toString());
            dto.setApplicantName(empInfo.getEmpName());
            dto.setEventTime(waBatchOvertime.getStartTime() * 1000);
            dto.setEventEndTime(waBatchOvertime.getEndTime() * 1000);
            dto.setTimeSlot(waBatchOvertime.getTimeSlot().replace(" -> ", "~"));
            startWorkflow(dto, waBatchOvertime.getTenantId());
        }
        OvertimeApplyTimeVo overtimeApplyTimeVo = new OvertimeApplyTimeVo();
        overtimeApplyTimeVo.setOriginalOtDuration(Double.valueOf(waBatchOvertime.getTimeDuration()));
        overtimeApplyTimeVo.setWfBusKey(wfBusKey);
        return overtimeApplyTimeVo;
    }

    @Transactional(rollbackFor = Exception.class)
    public WaBatchOvertimeDo saveApply(BatchOvertimeApplyDto applyDto, AppTypeEnum appType, SysEmpInfo empInfo) throws Exception {
        long startTime = 0L;
        long endTime = 0L;
        BigDecimal totalTimeDuration = new BigDecimal(0);
        Long batchId = snowflakeUtil.createId();
        for (OverApplySaveDto overtimeSaveDto : applyDto.getOvertimeList()) {
            overtimeSaveDto.setBatchId(batchId);
            Result<?> result = overtimeApplyService.saveOtData(overtimeSaveDto, appType);
            if (!result.isSuccess()) {
                throw new ServerException(result.getMsg());
            }
            OvertimeApplyTimeVo data = (OvertimeApplyTimeVo) result.getData();
            totalTimeDuration = totalTimeDuration.add(BigDecimal.valueOf(data.getOriginalOtDuration()));

            if (startTime == 0 || startTime > overtimeSaveDto.getStartTime()) {
                startTime = overtimeSaveDto.getStartTime().longValue();
            }
            if (endTime < overtimeSaveDto.getEndTime()) {
                endTime = overtimeSaveDto.getEndTime().longValue();
            }
        }

        long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
        long endDate = DateUtil.getOnlyDate(new Date(endTime * 1000));
        String startDateYmd = DateUtil.getDateStrByTimesamp(startDate);
        String endDateYmd = DateUtil.getDateStrByTimesamp(endDate);

        // 批量单据
        WaBatchOvertimeDo waBatchOvertime = new WaBatchOvertimeDo();
        waBatchOvertime.setBatchId(batchId);
        waBatchOvertime.setEmpid(empInfo.getEmpid());
        waBatchOvertime.setWaSobId(applyDto.getWaSobId());
        waBatchOvertime.setStartTime(startDate);
        waBatchOvertime.setEndTime(endDate);
        waBatchOvertime.setTimeSlot(startDateYmd + " -> " + endDateYmd);
        waBatchOvertime.setFileName(applyDto.getFileName());
        waBatchOvertime.setFilePath(applyDto.getFilePath());
        waBatchOvertime.setTimeDuration(totalTimeDuration.floatValue());
        waBatchOvertime.setBusinessKey(String.format("%s_%s", batchId, BusinessCodeEnum.BATCH_OVERTIME.getCode()));
        waBatchOvertime.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        waBatchOvertime.doInitCrtField();
        waBatchOvertimeDo.save(waBatchOvertime);
        return waBatchOvertime;
    }

    public void startWorkflow(WfBeginWorkflowDto dto, String tenantId) {
        Result<?> result = null;
        try {
            result = wfRegisterFeign.begin(dto);
            log.debug("BatchOt startWorkflow result={}", FastjsonUtil.toJsonStr(result));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            waBatchOvertimeDo.deleteById(Long.valueOf(dto.getBusinessId()));
            overtimeApplyService.deleteByBatchId(tenantId, Long.valueOf(dto.getBusinessId()));
            if (e instanceof ServerException) {
                if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                    throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
                } else {
                    throw e;
                }
            } else {
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
            }
        }
        if (null == result || !result.isSuccess() || null == result.getData()
                || StringUtils.isBlank(result.getData().toString())) {
            waBatchOvertimeDo.deleteById(Long.valueOf(dto.getBusinessId()));
            overtimeApplyService.deleteByBatchId(tenantId, Long.valueOf(dto.getBusinessId()));
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, null).getMsg());
        }
    }

    /**
     * 加班分页列表
     *
     * @param pageBean
     * @param dto
     * @return
     */
    public PageList<BatchOvertimePageListVo> getPageList(PageBean pageBean, BatchOvertimeQueryDto dto, UserInfo userInfo) {
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("tenantId", userInfo.getTenantId());
        queryParam.put("keywords", pageBean.getKeywords());
        queryParam.put("datafilter", dto.getDataScope());
        queryParam.put("empid", dto.getEmpid());
        doParseFilterField(pageBean, queryParam);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "ei.orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
//        if (filter == null || !filter.contains("status")) {
//            filter = filter+"and \"status\" in ('1')" ;
//        }
        queryParam.put("filter", filter);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "crttime.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        PageList<Map> pageList = waBatchOvertimeDo.getPageList(pageBounds, queryParam);
        if (CollectionUtils.isEmpty(pageList)) {
            return new PageList<>(Lists.newArrayList(), pageList.getPaginator());
        }
        List<BatchOvertimePageListVo> listVos = pageList.stream().map(data -> {
            BatchOvertimePageListVo listVo = new BatchOvertimePageListVo();
            listVo.setBatchId((Long) data.get("batch_id"));
            listVo.setWorkno((String) data.get("workno"));
            listVo.setEmpName((String) data.get("emp_name"));
            listVo.setOrgName((String) data.get("orgName"));

            Float timeDuration = Float.valueOf(data.get("time_duration").toString());
            BigDecimal v = new BigDecimal(String.valueOf(timeDuration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
            listVo.setTimeDuration(v.floatValue() + PreTimeUnitEnum.getName(2));

            Float validTimeDuration = Float.valueOf(data.get("valid_time_duration").toString());
            BigDecimal v1 = new BigDecimal(String.valueOf(validTimeDuration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
            listVo.setValidTimeDuration(v1.floatValue() + PreTimeUnitEnum.getName(2));

            listVo.setTimeSlot((String) data.get("time_slot"));
            listVo.setCreateDate((Long) data.get("crttime"));
            Integer status = (Integer) data.get("status");
            listVo.setStatus(status);
            listVo.setStatusName(ApprovalStatusEnum.getName(status));
            listVo.setLastApprovalTime((Long) data.get("last_approval_time"));
            listVo.setBusinessKey((String) data.get("business_key"));
            return listVo;
        }).collect(Collectors.toList());
        return new PageList<>(listVos, pageList.getPaginator());
    }

    private void doParseFilterField(PageBean pageBean, Map<String, Object> queryParam) {
        if (CollectionUtils.isEmpty(pageBean.getFilterList())) {
            return;
        }
        Iterator<FilterBean> it = pageBean.getFilterList().iterator();
        while (it.hasNext()) {
            FilterBean filterBean = it.next();
            if ("eventtime".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    queryParam.put("startTime", Long.valueOf(filterBean.getMin()));
                }
                if (StringUtils.isNotBlank(filterBean.getMax())) {
                    queryParam.put("endTime", Long.valueOf(filterBean.getMax()));
                }
                it.remove();
            } else if ("crttime".equals(filterBean.getField()) || "last_approval_time".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    filterBean.setMin(Long.parseLong(filterBean.getMin()) * 1000 + "");
                }
                if (StringUtils.isNotBlank(filterBean.getMax())) {
                    filterBean.setMax(Long.parseLong(filterBean.getMax()) * 1000 + "");
                }
            }
        }
    }

    public void preHandleFilterField(PageBean pageBean) {
        if (CollectionUtils.isEmpty(pageBean.getFilterList())) {
            return;
        }
        Iterator<FilterBean> it = pageBean.getFilterList().iterator();
        while (it.hasNext()) {
            FilterBean filterBean = it.next();
            if ("eventtime".equals(filterBean.getField())
                    || "crttime".equals(filterBean.getField())
                    || "last_approval_time".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    filterBean.setMin(String.valueOf(Long.parseLong(filterBean.getMin()) / 1000));
                }
                if (StringUtils.isNotBlank(filterBean.getMax())) {
                    filterBean.setMax(String.valueOf(Long.parseLong(filterBean.getMax()) / 1000));
                }
            }
        }
    }

    /**
     * 撤销
     *
     * @param dto
     * @return
     * @throws Exception
     */
    public void revoke(RevokeBatchOvertimeDto dto) throws Exception {
        WaBatchOvertimeDo waBatchOvertime = waBatchOvertimeDo.getById(dto.getBatchId());
        if (null == waBatchOvertime) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_NOT_EXIST, Boolean.FALSE).getMsg());
        }
        Integer status = waBatchOvertime.getStatus();
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(status)) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE).getMsg());
        }
        if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(status)) {
            // 只允许撤销审批中的数据
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201918", WebUtil.getRequest()));
        }

        SpringUtil.getBean(WaBatchOvertimeService.class).revokeDetail(waBatchOvertime, dto);

        // 撤销审批流
        WfRevokeDto revokeDto = new WfRevokeDto();
        revokeDto.setBusinessKey(waBatchOvertime.getBusinessKey());
        try {
            Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
            if (null == result || !result.isSuccess()) {
                // 工作流撤销异常
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201919", WebUtil.getRequest()));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201919", WebUtil.getRequest()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void revokeDetail(WaBatchOvertimeDo waBatchOvertime, RevokeBatchOvertimeDto dto) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();
        // 加班明细单据撤销
        List<WaEmpOvertimeDo> waEmpOvertimeList = waEmpOvertimeDo.getListByBatchId(waBatchOvertime.getBatchId());
        if (CollectionUtils.isEmpty(waEmpOvertimeList)) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_NOT_EXIST, Boolean.FALSE).getMsg());
        }
        OverApplyRevokeDto overApplyRevokeDto = new OverApplyRevokeDto();
        overApplyRevokeDto.setRecokeReason(dto.getRevokeReason());
        overApplyRevokeDto.setIfBatch(true);
        for (WaEmpOvertimeDo waEmpOvertime : waEmpOvertimeList) {
            if (ApprovalStatusEnum.REVOKED.getIndex().equals(waEmpOvertime.getStatus().intValue())) {
                continue;
            }
            overApplyRevokeDto.setWaid(waEmpOvertime.getOtId());
            Result<Boolean> result = overtimeApplyService.revokeEmpOt(overApplyRevokeDto, userInfo);
            if (!result.isSuccess()) {
                throw new ServerException(result.getMsg());
            }
        }

        // 保存撤销原因
        waBatchOvertime.setRevokeReason(dto.getRevokeReason());
        waBatchOvertime.setUpdateBy(UserContext.getUserId());
        waBatchOvertime.setUpdateTime(DateUtil.getCurrentTime(true));
        waBatchOvertimeDo.updateById(waBatchOvertime);
    }

    /**
     * 详情（审批页面使用）
     *
     * @param businessId
     * @param summary
     * @return
     */
    public WfResponseDto getWfDetail(Long businessId, boolean summary) {
        WfResponseDto responseDto = new WfResponseDto();
        Optional<WaBatchOvertimeDo> optional = Optional.ofNullable(waBatchOvertimeDo.getById(businessId));
        if (!optional.isPresent()) {
            return responseDto;
        }
        WaBatchOvertimeDo waBatchOvertime = optional.get();

        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(waBatchOvertime.getTenantId(), waBatchOvertime.getEmpid());
        if (empInfo == null) {
            return responseDto;
        }

        // 查询考勤周期
        WaSobDo waSob = null;
        if (null != waBatchOvertime.getWaSobId()) {
            waSob = waSobDo.getById(waBatchOvertime.getTenantId(), waBatchOvertime.getWaSobId());
        }

        EmpOrgPostInfo empOrgPostInfo = waEmpService.getEmpOrgPostInfo(String.valueOf(empInfo.getEmpid()));

        List<WfBusinessDataDetailDto> dataList = new ArrayList<>();
        dataList.add(new WfBusinessDataDetailDto("name", ResponseWrap.wrapResult(AttendanceCodes.EMP_NAME, null).getMsg(), empInfo.getEmpName(), null));
        dataList.add(new WfBusinessDataDetailDto("workno", ResponseWrap.wrapResult(AttendanceCodes.WORK_NO, null).getMsg(), empInfo.getWorkno(), null));
        dataList.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG, null).getMsg(), empOrgPostInfo.getOrg(), null));
        dataList.add(new WfBusinessDataDetailDto("post", ResponseWrap.wrapResult(AttendanceCodes.POSITION, null).getMsg(), empOrgPostInfo.getPost(), null));
        dataList.add(new WfBusinessDataDetailDto("job", ResponseWrap.wrapResult(AttendanceCodes.POST, null).getMsg(), empOrgPostInfo.getJob(), null));
        dataList.add(new WfBusinessDataDetailDto("jobGrade", ResponseWrap.wrapResult(AttendanceCodes.POSITION_LEVEL, null).getMsg(), empOrgPostInfo.getJobGrade(), null));
        dataList.add(new WfBusinessDataDetailDto("startTime", ResponseWrap.wrapResult(AttendanceCodes.START_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(waBatchOvertime.getStartTime()), null));
        dataList.add(new WfBusinessDataDetailDto("endTime", ResponseWrap.wrapResult(AttendanceCodes.END_DATE, null).getMsg(), DateUtil.getDateStrByTimesamp(waBatchOvertime.getEndTime()), null));
        dataList.add(new WfBusinessDataDetailDto("waMonth", ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_MONTH, null).getMsg(), null != waSob ? waSob.getWaSobName() : null, null));
        if (!summary) {
            // 查询加班明细
            float currentWorkPaid = 0f;
            float currentCompensatoryLeave = 0f;
            List<WaEmpOvertimeDo> waEmpOvertimeList = waEmpOvertimeDo.getListByBatchId(waBatchOvertime.getBatchId());
            if (CollectionUtils.isNotEmpty(waEmpOvertimeList)) {
                List<Integer> overtimeTypeIds = waEmpOvertimeList.stream().map(WaEmpOvertimeDo::getOvertimeTypeId).distinct().collect(Collectors.toList());
                List<WaOvertimeType> overtimeTypes = overTimeTypeDo.getOvertimeTypeByIds(overtimeTypeIds);
                Map<Integer, String> overtimeTypeMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(overtimeTypes)) {
                    overtimeTypeMap = overtimeTypes.stream().collect(Collectors.toMap(WaOvertimeType::getOvertimeTypeId, WaOvertimeType::getTypeName));
                }
                Map<Integer, String> finalOvertimeTypeMap = overtimeTypeMap;
                List<BatchOvertimeDetailDto> batchOvertimeList = waEmpOvertimeList.stream().map(o -> {
                    BatchOvertimeDetailDto detailDto = new BatchOvertimeDetailDto();
                    detailDto.setDate(DateUtil.getDateStrByTimesamp(o.getStartTime()));
                    detailDto.setOvertimeType(finalOvertimeTypeMap.get(o.getOvertimeTypeId()));
                    detailDto.setStime(DateUtil.convertDateTimeToStr(o.getStartTime(), "HH:mm", true));
                    detailDto.setEtime(DateUtil.convertDateTimeToStr(o.getEndTime(), "HH:mm", true));
                    detailDto.setTimeDuration(BigDecimal.valueOf(o.getOtDuration())
                            .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue() + PreTimeUnitEnum.getName(2));
                    detailDto.setReason(o.getReason());
                    detailDto.setStatus(o.getStatus());
                    detailDto.setStatusTxt(ApprovalStatusEnum.getName(o.getStatus().intValue()));
                    return detailDto;
                }).collect(Collectors.toList());
                dataList.add(new WfBusinessDataDetailDto("batchOvertimeDetail", ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_DETAIL, null).getMsg(), batchOvertimeList, null));

                Map<Integer, List<WaEmpOvertimeDo>> otGroup = waEmpOvertimeList.stream()
                        .collect(Collectors.groupingBy(WaEmpOvertimeDo::getCompensateType));
                List<WaEmpOvertimeDo> workPaidOtList = otGroup.get(CompensateTypeEnum.WORK_PAID.ordinal());
                if (CollectionUtils.isNotEmpty(workPaidOtList)) {
                    int totalOtTime = 0;
                    for (WaEmpOvertimeDo overtimeDo : workPaidOtList) {
                        totalOtTime = totalOtTime + overtimeDo.getOtDuration();
                    }
                    currentWorkPaid = BigDecimal.valueOf(totalOtTime)
                            .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue();
                }
                List<WaEmpOvertimeDo> compensatoryLeaveOtList = otGroup.get(CompensateTypeEnum.COMPENSATORY_LEAVE.ordinal());
                if (CollectionUtils.isNotEmpty(compensatoryLeaveOtList)) {
                    int totalOtTime = 0;
                    for (WaEmpOvertimeDo overtimeDo : compensatoryLeaveOtList) {
                        totalOtTime = totalOtTime + overtimeDo.getOtDuration();
                    }
                    currentCompensatoryLeave = BigDecimal.valueOf(totalOtTime)
                            .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue();
                }
            }

            if (null != waSob) {
                OvertimeStatisticsVo statistics = this.statistics(empInfo.getEmpid(), waSob.getStartDate(), waSob.getEndDate());
                statistics.setCurrentWorkPaid(currentWorkPaid);
                statistics.setCurrentCompensatoryLeave(currentCompensatoryLeave);
                Float currentTotalDuration = currentWorkPaid + currentCompensatoryLeave;
                statistics.setCurrentTotalDuration(currentTotalDuration);
                dataList.add(new WfBusinessDataDetailDto("batchOvertimeStatistics", ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_STATISTIC, null).getMsg(), statistics, null));
            }

            if (StringUtils.isNotBlank(waBatchOvertime.getFilePath())) {
                List<String> fileNameList = Arrays.stream(waBatchOvertime.getFileName().split(",")).collect(Collectors.toList());
                List<String> filePathList = Arrays.stream(waBatchOvertime.getFilePath().split(",")).collect(Collectors.toList());
                List<WfAttachmentDto> fileList = new ArrayList<>();
                for (int i = 0; i < filePathList.size(); i++) {
                    WfAttachmentDto attachmentDto = new WfAttachmentDto();
                    attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                    attachmentDto.setFileUrl(filePathList.get(i));
                    attachmentDto.setFileName(fileNameList.get(i));
                    fileList.add(attachmentDto);
                }
                dataList.add(new WfBusinessDataDetailDto("file", ResponseWrap.wrapResult(AttendanceCodes.ATTACHMENT, null).getMsg(), null, fileList));
            }
        }
        dataList.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), waBatchOvertime.getProcessCode() == null ? "-" : waBatchOvertime.getProcessCode(), null));
        responseDto.setDetailList(dataList);
        return responseDto;
    }

    /**
     * 详情（业务列表页面使用）
     *
     * @param businessId
     * @return
     */
    public WfDetailDto getWfDetailDto(Long businessId) {
        WfResponseDto wfResponseDto = getWfDetail(businessId, false);
        if (CollectionUtils.isEmpty(wfResponseDto.getDetailList())) {
            return new WfDetailDto();
        }
        List<String> fileNameList = Lists.newArrayList();
        List<String> filePathList = Lists.newArrayList();
        List<KeyValue> items = Lists.newArrayList();
        String orgFullPath = "";
        for (WfBusinessDataDetailDto o : wfResponseDto.getDetailList()) {
            if (o.getKey().equals("batchOvertimeDetail") || o.getKey().equals("batchOvertimeStatistics")) {
                items.add(new WaKeyValue(o.getText(), o.getValue(), o.getKey()));
            } else if (o.getKey().equals("file") && CollectionUtils.isNotEmpty(o.getFileList())) {
                List<WfAttachmentDto> fileList = o.getFileList();
                fileList.forEach(fileObj -> {
                    fileNameList.add(fileObj.getFileName());
                    filePathList.add(fileObj.getFileUrl());
                });
            } else {
                items.add(new KeyValue(o.getText(), o.getValue()));
                if (o.getKey().equals("org") && null != o.getValue()) {
                    orgFullPath = o.getValue().toString();
                }
            }
        }
        WfDetailDto detailDto = new WfDetailDto();
        if (CollectionUtils.isNotEmpty(fileNameList)) {
            detailDto.setFiles(StringUtils.join(filePathList, ","));
            detailDto.setFileNames(StringUtils.join(fileNameList, ","));
        }
        detailDto.setFullPath(orgFullPath);
        detailDto.setItems(items);
        return detailDto;
    }
}