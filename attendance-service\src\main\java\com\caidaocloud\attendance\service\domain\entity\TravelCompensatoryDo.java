package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.domain.repository.ITravelCompensatoryRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelTransferCompensatory;
import com.caidaocloud.util.ObjectConverter;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Service
public class TravelCompensatoryDo {

    private Long id;
    private String tenantId;
    private Long empId;
    private Long quotaId;
    private Integer sobId;
    private Integer quotaUnit;
    private Float quotaDay;
    private Integer status;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;

    private Integer periodMonth;
    private String empName;
    private String workNo;
    private String quotaName;

    @Autowired
    private ITravelCompensatoryRepository travelCompensatoryRepository;

    public PageList<TravelCompensatoryDo> getTravelCompensatoryList(MyPageBounds myPageBounds, Map params) {
        return travelCompensatoryRepository.getTravelCompensatoryList(myPageBounds, params);
    }

    public void batchSave(List<TravelCompensatoryDo> records) {
        if (null == records || records.size() <= 0) {
            return;
        }
        List<WaTravelTransferCompensatory> models = ObjectConverter.convertList(records, WaTravelTransferCompensatory.class);
        travelCompensatoryRepository.batchSave(models);
    }

    public TravelCompensatoryDo getDetailById(String tenantId, Long id) {
        return travelCompensatoryRepository.getDetailById(tenantId, id);
    }

    public List<TravelCompensatoryDo> getTravelCompensatoryRecords(String tenantId, List<Integer> sobIds) {
        return travelCompensatoryRepository.getTravelCompensatoryRecords(tenantId, sobIds);
    }

    public void batchUpdate(List<TravelCompensatoryDo> records) {
        if (null == records || records.size() <= 0) {
            return;
        }
        List<WaTravelTransferCompensatory> models = ObjectConverter.convertList(records, WaTravelTransferCompensatory.class);
        travelCompensatoryRepository.batchUpdate(models);
    }

    public void batchDelete(List<Long> quotaIds, Long userId) {
        if (null == quotaIds || quotaIds.size() <= 0) {
            return;
        }
        travelCompensatoryRepository.batchDelete(quotaIds, userId);
    }
}
