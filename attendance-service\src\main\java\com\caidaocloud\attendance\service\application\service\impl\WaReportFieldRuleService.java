package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.attendance.service.application.dto.WaCustomParseRuleDto;
import com.caidaocloud.attendance.service.domain.entity.WaReportFieldRuleDo;
import com.caidaocloud.attendance.service.domain.service.WaReportFieldRuleDomainService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 考勤报表字段规则
 *
 * <AUTHOR>
 * @Date 2024/2/20
 */
@Slf4j
@Service
public class WaReportFieldRuleService {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    @Autowired
    private WaReportFieldRuleDomainService waReportFieldRuleDomainService;
    @Autowired
    private ISessionService sessionService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Transactional
    public void save(Integer parseGroupId, WaCustomParseRuleDto customLatePaseRule,
                     WaCustomParseRuleDto customEarlyPaseRule, WaCustomParseRuleDto customAbsentPaseRule,
                     Boolean openCustomPaseRule) {
        UserInfo userInfo = getUserInfo();
        PreCheck.preCheckArgument(null == userInfo, "未登录，请先登录");
        List<WaReportFieldRuleDo> ruleList = waReportFieldRuleDomainService.selectList(userInfo.getTenantId(), parseGroupId);
        if (CollectionUtils.isNotEmpty(ruleList)) {
            waReportFieldRuleDomainService.delete(userInfo.getTenantId(), parseGroupId);
        }
        if (null == openCustomPaseRule || !openCustomPaseRule) {
            return;
        }
        WaReportFieldRuleDo fieldRuleDo = new WaReportFieldRuleDo();
        fieldRuleDo.setId(snowflakeUtil.createId());
        fieldRuleDo.setTenantId(userInfo.getTenantId());
        fieldRuleDo.setParseGroupId(parseGroupId);
        fieldRuleDo.setLatePaseRule(FastjsonUtil.toJson(customLatePaseRule));
        fieldRuleDo.setEarlyPaseRule(FastjsonUtil.toJson(customEarlyPaseRule));
        fieldRuleDo.setAbsentPaseRule(FastjsonUtil.toJson(customAbsentPaseRule));
        fieldRuleDo.setCreateBy(userInfo.getUserId());
        fieldRuleDo.setCreateTime(System.currentTimeMillis());
        fieldRuleDo.setUpdateBy(userInfo.getUserId());
        fieldRuleDo.setUpdateTime(System.currentTimeMillis());
        fieldRuleDo.setDeleted(0);
        waReportFieldRuleDomainService.save(fieldRuleDo);
    }
}
