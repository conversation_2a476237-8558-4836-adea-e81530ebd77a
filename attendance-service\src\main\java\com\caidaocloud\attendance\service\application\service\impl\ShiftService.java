package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.ioc.dto.CheckMessage;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaRegisterRecordMapper;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiOvertimeDto;
import com.caidaocloud.attendance.core.wa.dto.shift.OvertimeRestPeriodsDto;
import com.caidaocloud.attendance.core.wa.dto.shift.RestPeriodDto;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftBelongModuleEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.core.wa.vo.MultiWorkTimeInfoSimpleVo;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.IShiftService;
import com.caidaocloud.attendance.service.application.service.IWorkRoundService;
import com.caidaocloud.attendance.service.domain.entity.WaEmpShiftChangeDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.domain.entity.WaWorkRoundDo;
import com.caidaocloud.attendance.service.infrastructure.util.FileUtil;
import com.caidaocloud.attendance.service.interfaces.dto.shift.MidwayClockTimeDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftSaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.multi.MultiShiftSaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.multi.MultiWorkTimeDto;
import com.caidaocloud.attendance.service.interfaces.vo.shift.MultiShiftVo;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.CustShiftImportResult;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.DataInputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShiftService implements IShiftService {
    private static final int PAGE_SIZE = 500;
    @Autowired
    private WaShiftDo waShiftDo;
    @Autowired
    private WaCommonService waCommonService;
    @Resource
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Resource
    private WaEmpShiftChangeDo waEmpShiftChangeDo;
    @Resource
    private WaAttendanceConfigService waConfigService;
    @Resource
    private IWorkRoundService workRoundService;


    public static Map<String, Integer> dateTypeMap = Maps.newHashMap();

    static {
        dateTypeMap.put("工作日", 1);
        dateTypeMap.put("休息日", 2);
        dateTypeMap.put("法定假日", 3);
        dateTypeMap.put("特殊休日", 4);
        dateTypeMap.put("法定休日", 5);
    }

    public UserInfo getUserInfo() {
        return UserContext.preCheckUser();
    }

    @Override
    public AttendancePageResult<WaShiftDo> getPageList(ShiftPageDto shiftPageDto, UserInfo user) {
        UserInfo userInfo = user == null ? this.getUserInfo() : user;
        shiftPageDto.setBelongOrgId(userInfo.getTenantId());
        AttendancePageResult<WaShiftDo> pageResult = waShiftDo.getShiftDefList(shiftPageDto);
        pageResult.getItems()
                .forEach(item -> item.setShiftDefName(LangParseUtil.getI18nLanguage(item.getI18nShiftDefName(),
                        item.getShiftDefName())));
        return pageResult;
    }

    private MultiWorkTimeInfoSimpleVo getMultiWorkTimeVo(WaShiftDo shiftDo) {
        MultiWorkTimeInfoSimpleVo multiWorkTimeVo = new MultiWorkTimeInfoSimpleVo();
        multiWorkTimeVo.setStartTime(shiftDo.getStartTime());
        multiWorkTimeVo.setEndTime(shiftDo.getEndTime());
        multiWorkTimeVo.setWorkTotalTime(shiftDo.getWorkTotalTime());
        multiWorkTimeVo.setWorkType(1);
        boolean crossNightForShiftTime = CdWaShiftUtil.checkCrossNight(shiftDo.getStartTime(), shiftDo.getEndTime(), shiftDo.getDateType());
        multiWorkTimeVo.setStartTimeBelong(Optional.ofNullable(shiftDo.getStartTimeBelong()).orElse(ShiftTimeBelongTypeEnum.TODAY.getIndex()));
        multiWorkTimeVo.setEndTimeBelong(shiftDo.getEndTimeBelong());
        if (null == shiftDo.getEndTimeBelong()) {
            multiWorkTimeVo.setEndTimeBelong(crossNightForShiftTime ?
                    ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex() : ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
        multiWorkTimeVo.setRealStartTime(multiWorkTimeVo.doGetRealStartTime());
        multiWorkTimeVo.setRealEndTime(multiWorkTimeVo.doGetRealEndTime());
        multiWorkTimeVo.setOnDutyStartTime(shiftDo.getOnDutyStartTime());
        multiWorkTimeVo.setOnDutyEndTime(shiftDo.getOnDutyEndTime());
        multiWorkTimeVo.setOffDutyStartTime(shiftDo.getOffDutyStartTime());
        multiWorkTimeVo.setOffDutyEndTime(shiftDo.getOffDutyEndTime());
        multiWorkTimeVo.setOnDutyStartTimeBelong(Optional.ofNullable(shiftDo.getOnDutyStartTimeBelong()).orElse(ShiftTimeBelongTypeEnum.TODAY.getIndex()));
        if (null != shiftDo.getOnDutyEndTimeBelong()) {
            multiWorkTimeVo.setOnDutyEndTimeBelong(shiftDo.getOnDutyEndTimeBelong());
        } else {
            boolean crossNightForOndutyTime = null != shiftDo.getOnDutyStartTime()
                    && null != shiftDo.getOnDutyEndTime() && shiftDo.getOnDutyStartTime() >= shiftDo.getOnDutyEndTime();
            multiWorkTimeVo.setOnDutyEndTimeBelong(crossNightForOndutyTime ?
                    ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex() : ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
        if (null != shiftDo.getOffDutyStartTimeBelong()) {
            multiWorkTimeVo.setOffDutyStartTimeBelong(shiftDo.getOffDutyStartTimeBelong());
        } else {
            multiWorkTimeVo.setOffDutyStartTimeBelong(crossNightForShiftTime ?
                    ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex() : ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
        if (null != shiftDo.getOffDutyEndTimeBelong()) {
            multiWorkTimeVo.setOffDutyEndTimeBelong(shiftDo.getOffDutyEndTimeBelong());
        } else {
            boolean crossNightForOffDutyTime = null != shiftDo.getOffDutyStartTime()
                    && null != shiftDo.getOffDutyEndTime() && shiftDo.getOffDutyStartTime() >= shiftDo.getOffDutyEndTime();
            multiWorkTimeVo.setOffDutyEndTimeBelong((crossNightForShiftTime || crossNightForOffDutyTime)
                    ? ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex()
                    : ShiftTimeBelongTypeEnum.TODAY.getIndex());
        }
        return multiWorkTimeVo;
    }

    private List<MultiWorkTimeInfoSimpleVo> getMultiWorkTimeVoList(List<MultiWorkTimeDto> multiWorkTimeDtoList) {
        return multiWorkTimeDtoList.stream().map(timeIt -> {
            MultiWorkTimeInfoSimpleVo timeInfoVo = new MultiWorkTimeInfoSimpleVo();
            timeInfoVo.setStartTime(timeIt.getStartTime());
            timeInfoVo.setEndTime(timeIt.getEndTime());
            timeInfoVo.setRealStartTime(timeIt.doGetRealStartTime());
            timeInfoVo.setRealEndTime(timeIt.doGetRealEndTime());
            timeInfoVo.setWorkTotalTime(timeIt.getWorkTotalTime());
            timeInfoVo.setStartTimeBelong(timeIt.getStartTimeBelong());
            timeInfoVo.setEndTimeBelong(timeIt.getEndTimeBelong());
            timeInfoVo.setWorkType(1);
            timeInfoVo.setOnDutyStartTime(timeIt.getOnDutyStartTime());
            timeInfoVo.setOnDutyEndTime(timeIt.getOnDutyEndTime());
            timeInfoVo.setOffDutyStartTime(timeIt.getOffDutyStartTime());
            timeInfoVo.setOffDutyEndTime(timeIt.getOffDutyEndTime());
            timeInfoVo.setOnDutyStartTimeBelong(timeIt.getOnDutyStartTimeBelong());
            timeInfoVo.setOnDutyEndTimeBelong(timeIt.getOnDutyEndTimeBelong());
            timeInfoVo.setOffDutyStartTimeBelong(timeIt.getOffDutyStartTimeBelong());
            timeInfoVo.setOffDutyEndTimeBelong(timeIt.getOffDutyEndTimeBelong());
            return timeInfoVo;
        }).collect(Collectors.toList());
    }

    public void setShiftSummary(WaShiftDo shiftDo, MultiShiftSimpleVo simpleVo) {
        Long nowDate = DateUtil.getOnlyDate();
        String start = DateUtil.convertDateTimeToStr(nowDate + (shiftDo.getStartTime() * 60), "HH:mm", true);
        String end = DateUtil.convertDateTimeToStr(nowDate + (shiftDo.getEndTime() * 60), "HH:mm", true);
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(shiftDo.getEndTimeBelong()) ||
                CdWaShiftUtil.checkCrossNight(shiftDo.getStartTime(), shiftDo.getEndTime(), shiftDo.getDateType())) {
            end = ShiftTimeBelongTypeEnum.getName(ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex()) + end;
        }
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDo.getDateType())) {
            simpleVo.setSummary(String.format("%s（%s）", simpleVo.getShiftDefName(), String.format("%s~%s", start, end)));
        } else {
            simpleVo.setSummary(simpleVo.getShiftDefName());
        }
    }

    private void setShiftSummaryList(List<MultiWorkTimeDto> multiWorkTimeDtoList, MultiShiftSimpleVo simpleVo) {
        Long nowDate = DateUtil.getOnlyDate();
        List<String> timeTxtList = multiWorkTimeDtoList.stream().map(timeIt -> {
            String start = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getStartTime() * 60), "HH:mm", true);
            String end = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getEndTime() * 60), "HH:mm", true);
            if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getStartTimeBelong())) {
                start = ShiftTimeBelongTypeEnum.getName(timeIt.getStartTimeBelong()) + start;
            }
            if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getEndTimeBelong())) {
                end = ShiftTimeBelongTypeEnum.getName(timeIt.getEndTimeBelong()) + end;
            }
            return String.format("%s~%s", start, end);
        }).collect(Collectors.toList());

        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(simpleVo.getDateType())) {
            simpleVo.setSummary(String.format("%s（%s）", simpleVo.getShiftDefName(), StringUtils.join(timeTxtList, "、")));
        } else {
            simpleVo.setSummary(simpleVo.getShiftDefName());
        }
    }

    public void calShiftTimeList(List<MultiWorkTimeDto> multiWorkTimeDtoList, MultiShiftSimpleVo simpleVo) {
        if (multiWorkTimeDtoList.size() == 1) {
            MultiWorkTimeDto multiWorkTimeDto = multiWorkTimeDtoList.get(0);
            simpleVo.setStartTime(multiWorkTimeDto.getStartTime());
            simpleVo.setEndTime(multiWorkTimeDto.getEndTime());
            simpleVo.setStartTimeBelong(multiWorkTimeDto.getStartTimeBelong());
            simpleVo.setEndTimeBelong(multiWorkTimeDto.getEndTimeBelong());
            simpleVo.setIsNight(multiWorkTimeDto.getIsNight());
        } else {
            MultiWorkTimeDto minMultiWorkTimeDto = multiWorkTimeDtoList.get(0);
            MultiWorkTimeDto maxMultiWorkTimeDto = multiWorkTimeDtoList.get(multiWorkTimeDtoList.size() - 1);
            simpleVo.setStartTime(minMultiWorkTimeDto.getStartTime());
            simpleVo.setEndTime(maxMultiWorkTimeDto.getEndTime());
            simpleVo.setStartTimeBelong(minMultiWorkTimeDto.getStartTimeBelong());
            simpleVo.setEndTimeBelong(maxMultiWorkTimeDto.getEndTimeBelong());
            long kyCount = multiWorkTimeDtoList.stream().filter(workTimeIt -> workTimeIt.getIsNight() != null && workTimeIt.getIsNight()).count();
            simpleVo.setIsNight(kyCount > 0);
        }
    }

    private void calShiftTimeList(WaShiftDo shiftDo, MultiShiftSimpleVo simpleVo, MultiWorkTimeInfoSimpleVo multiWorkTimeVo) {
        simpleVo.setStartTime(shiftDo.getStartTime());
        simpleVo.setEndTime(shiftDo.getEndTime());
        simpleVo.setStartTimeBelong(multiWorkTimeVo.getStartTimeBelong());
        simpleVo.setEndTimeBelong(multiWorkTimeVo.getEndTimeBelong());
        simpleVo.setIsNight(Optional.ofNullable(shiftDo.getIsNight()).orElse(Boolean.FALSE));
        if (null == shiftDo.getEndTimeBelong()) {
            if (CdWaShiftUtil.checkCrossNight(shiftDo.getStartTime(), shiftDo.getEndTime(), shiftDo.getDateType())) {
                simpleVo.setIsNight(Boolean.TRUE);
            } else {
                simpleVo.setIsNight(Boolean.FALSE);
            }
        }
    }

    private List<MultiWorkTimeInfoSimpleVo> getRestTimeVoList(WaShiftDo shiftDo) {
        List<RestPeriodDto> restPeriods = WaShiftDo.doGetRestPeriodList(shiftDo);
        boolean isNoonRest = Optional.ofNullable(shiftDo.getIsNoonRest()).orElse(Boolean.FALSE);
        if (CollectionUtils.isNotEmpty(restPeriods) && isNoonRest) {
            return restPeriods.stream().map(restIt -> {
                MultiWorkTimeInfoSimpleVo timeInfoVo = new MultiWorkTimeInfoSimpleVo();
                timeInfoVo.setStartTime(restIt.getNoonRestStart());
                timeInfoVo.setEndTime(restIt.getNoonRestEnd());
                timeInfoVo.setRealStartTime(restIt.doGetRealNoonRestStart());
                timeInfoVo.setRealEndTime(restIt.doGetRealNoonRestEnd());
                timeInfoVo.setWorkTotalTime(restIt.doGetRestTotalTime());
                timeInfoVo.setStartTimeBelong(restIt.getNoonRestStartBelong());
                timeInfoVo.setEndTimeBelong(restIt.getNoonRestEndBelong());
                timeInfoVo.setWorkType(2);
                return timeInfoVo;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private List<MultiWorkTimeInfoSimpleVo> getOtTimeVoList(WaShiftDo shiftDo) {
        List<MultiOvertimeDto> multiOvertime = shiftDo.getMultiOvertimeList();
        if (CollectionUtils.isEmpty(multiOvertime)) {
            return Lists.newArrayList();
        }
        List<OvertimeRestPeriodsDto> overtimeRestPeriodList = shiftDo.getOvertimeRestPeriodList();
        return multiOvertime.stream().map(otIt -> {
            MultiWorkTimeInfoSimpleVo timeInfoVo = new MultiWorkTimeInfoSimpleVo();
            timeInfoVo.setStartTime(otIt.getOvertimeStartTime());
            timeInfoVo.setEndTime(otIt.getOvertimeEndTime());
            timeInfoVo.setRealStartTime(otIt.doGetRealOvertimeStartTime());
            timeInfoVo.setRealEndTime(otIt.doGetRealOvertimeEndTime());
            timeInfoVo.setWorkTotalTime(calOtTimeDuration(overtimeRestPeriodList, shiftDo, otIt));
            timeInfoVo.setStartTimeBelong(otIt.getOvertimeStartTimeBelong());
            timeInfoVo.setEndTimeBelong(otIt.getOvertimeEndTimeBelong());
            timeInfoVo.setWorkType(3);
            return timeInfoVo;
        }).collect(Collectors.toList());
    }

    public Integer calOtTimeDuration(List<OvertimeRestPeriodsDto> overtimeRestPeriodList, WaShiftDo shiftDo, MultiOvertimeDto overtimeDto) {
        Integer otStartTime = overtimeDto.doGetRealOvertimeStartTime();
        Integer otEndTime = overtimeDto.doGetRealOvertimeEndTime();
        if (null == otStartTime || null == otEndTime) {
            return 0;
        }
        int timeDuration = otEndTime - otStartTime;
        if (CollectionUtils.isEmpty(overtimeRestPeriodList)) {
            return Math.max(timeDuration, 0);
        }
        List<MultiOvertimeDto> multiOvertimeList = shiftDo.getMultiOvertimeList();
        for (OvertimeRestPeriodsDto restPeriodsDto : overtimeRestPeriodList) {
            Integer restStartTime = restPeriodsDto.doGetRealOvertimeRestStartTime(multiOvertimeList);
            Integer restEndTime = restPeriodsDto.doGetRealOvertimeRestEndTime(multiOvertimeList);
            if (restStartTime >= otEndTime || restEndTime <= otStartTime) {
                continue;
            }
            int overlapStart = Math.max(otStartTime, restStartTime);
            int overlapEnd = Math.min(otEndTime, restEndTime);
            int restTimeDuration = overlapEnd - overlapStart;
            if (restTimeDuration <= 0) {
                continue;
            }
            timeDuration -= restTimeDuration;
        }
        return Math.max(timeDuration, 0);
    }

    @Override
    public List<MultiShiftSimpleVo> convertDoToSimpleVo(List<WaShiftDo> shiftDoList) {
        return shiftDoList.stream().map(shiftDo -> {
            MultiShiftSimpleVo simpleVo = new MultiShiftSimpleVo();
            simpleVo.setShiftDefId(Long.valueOf(shiftDo.getShiftDefId()));
            simpleVo.setDateType(shiftDo.getDateType());
            simpleVo.setShiftDefName(LangParseUtil.getI18nLanguage(shiftDo.getI18nShiftDefName(), shiftDo.getShiftDefName()));
            simpleVo.setShiftDefCode(shiftDo.getShiftDefCode());
            simpleVo.setWorkTotalTime(shiftDo.getWorkTotalTime());
            simpleVo.setTemporary(Optional.ofNullable(shiftDo.getTemporaryShift()).orElse(Boolean.FALSE));
            simpleVo.setColorMark("");

            // 工作时间
            if (null != shiftDo.getMultiWorkTimes()) {
                List<MultiWorkTimeDto> multiWorkTimeDtoList = FastjsonUtil.toArrayList(((PGobject) shiftDo.getMultiWorkTimes()).getValue(), MultiWorkTimeDto.class);
                simpleVo.setMultiWorkTimes(getMultiWorkTimeVoList(multiWorkTimeDtoList));
                setShiftSummaryList(multiWorkTimeDtoList, simpleVo);
                calShiftTimeList(multiWorkTimeDtoList, simpleVo);
            } else {
                MultiWorkTimeInfoSimpleVo multiWorkTimeVo = getMultiWorkTimeVo(shiftDo);
                simpleVo.setMultiWorkTimes(Lists.newArrayList(multiWorkTimeVo));
                setShiftSummary(shiftDo, simpleVo);
                calShiftTimeList(shiftDo, simpleVo, multiWorkTimeVo);
            }

            // 班次时间（包含非工作时间）
            List<MultiWorkTimeInfoSimpleVo> oriMultiWorkTimeList = Lists.newArrayList();
            oriMultiWorkTimeList.addAll(simpleVo.getMultiWorkTimes());
            // 休息时间
            oriMultiWorkTimeList.addAll(getRestTimeVoList(shiftDo));
            // 加班时间
            oriMultiWorkTimeList.addAll(getOtTimeVoList(shiftDo));
            simpleVo.setOriMultiWorkTimes(oriMultiWorkTimeList);
            return simpleVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<MultiShiftSimpleVo> getStdList(ShiftBelongModuleEnum belongModule) {
        StopWatch st = new StopWatch();
        st.start("开始查询标准班次");
        log.info("std shift query start time={}", System.currentTimeMillis());

        UserInfo userInfo = UserContext.getAndCheckUser();

        ShiftPageDto pageQueryDto = new ShiftPageDto();
        pageQueryDto.setPageNo(1);
        pageQueryDto.setPageSize(PAGE_SIZE);
        pageQueryDto.setBelongModule(belongModule.getCode());
        AttendancePageResult<WaShiftDo> pageResult = getPageList(pageQueryDto, userInfo);
        if (null == pageResult || CollectionUtils.isEmpty(pageResult.getItems())) {
            return Lists.newArrayList();
        }
        List<WaShiftDo> firstPageDataList = pageResult.getItems();
        int totalCount = pageResult.getTotal();
        if (totalCount <= PAGE_SIZE) {
            return convertDoToSimpleVo(firstPageDataList);
        }
        List<WaShiftDo> dataList = Lists.newArrayList();
        dataList.addAll(firstPageDataList);
        //总页数
        int totalPage = (int) Math.ceil((double) totalCount / PAGE_SIZE);
        for (int pageNo = 2; pageNo <= totalPage; pageNo++) {
            pageQueryDto.setPageNo(pageNo);
            pageResult = getPageList(pageQueryDto, userInfo);
            if (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems())) {
                dataList.addAll(pageResult.getItems());
            }
        }

        st.stop();
        log.info("std shift query end time={}", System.currentTimeMillis());

        st.start("开始转换标准班次");
        log.info("std shift convert start time={}", System.currentTimeMillis());

        List<MultiShiftSimpleVo> voList = convertDoToSimpleVo(dataList);

        st.stop();
        log.info("std shift convert end time={}", System.currentTimeMillis());

        log.info("ShiftService getStdList time :{}", st.prettyPrint());
        return voList;
    }

    @Override
    public List<MultiShiftSimpleVo> getListByIds(List<Integer> shiftDefIds) {
        List<WaShiftDo> shiftDoList = waShiftDo.getListByIds(shiftDefIds);
        if (CollectionUtils.isEmpty(shiftDoList)) {
            return Lists.newArrayList();
        }
        return convertDoToSimpleVo(shiftDoList);
    }

    public void checkUniqueValue(ShiftSaveDto dto) {
        if (checkShiftDefReCode(dto.getShiftDefCode(), dto.getShiftDefId(), dto.getBelongModule())) {
            throw new ServerException(String.format(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_CODE_DUPLICATED, Boolean.FALSE).getMsg(), dto.getShiftDefCode()));
        }
        if (checkShiftDefReName(dto.getShiftDefName(), dto.getShiftDefId(), dto.getBelongModule())) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NAME_EXIST, Boolean.FALSE).getMsg());
        }
    }

    /**
     * 班次设置保存（一段班）
     *
     * @param dto
     */
    @Override
    @Transactional
    public Integer saveShiftDef(ShiftSaveDto dto) {
        dto.initField();
        dto.doInitOnDutyTime();
        dto.initOvertime();

        dto.preCheck();
        checkUniqueValue(dto);

        WaShiftDef shiftDef = dto.doConvert();
        LogRecordContext.putVariable("name", shiftDef.getShiftDefName());
        UserInfo userInfo = UserContext.getAndCheckUser();
        if (shiftDef.getShiftDefId() == null) {
            shiftDef.setBelongOrgid(userInfo.getTenantId());
            shiftDef.setCrtuser(userInfo.getUserId());
            shiftDef.setCrttime(DateUtil.getCurrentTime(true));
            LogRecordContext.putVariable("operate", "新增");
            return waShiftDo.saveOrUpdateWaShiftDef(shiftDef);
        } else {
            shiftDef.setCrtuser(userInfo.getUserId());
            shiftDef.setCrttime(DateUtil.getCurrentTime(true));
            LogRecordContext.putVariable("operate", "编辑");
            return waShiftDo.saveOrUpdateWaShiftDef(shiftDef);
        }
    }

    /**
     * 班次设置保存（多段班）
     *
     * @param dto
     */
    @Override
    @Transactional
    public Integer saveMultiShiftDef(MultiShiftSaveDto dto) {
        dto.initField();
        dto.doInitOnDutyTime();
        dto.initSingleShift();

        dto.checkMultiWorkTime();
        dto.checkMultiOvertime();
        if (CollectionUtils.isNotEmpty(dto.getMultiWorkTimes()) && dto.getMultiWorkTimes().size() == 1) {
            dto.preCheck();
        } else {
            dto.preCheckBase();
        }
        checkUniqueValue(dto);

        WaShiftDef shiftDef = dto.doConvert();
        LogRecordContext.putVariable("name", shiftDef.getShiftDefName());
        UserInfo userInfo = UserContext.getAndCheckUser();
        if (shiftDef.getShiftDefId() == null) {
            shiftDef.setBelongOrgid(userInfo.getTenantId());
            shiftDef.setCrtuser(userInfo.getUserId());
            shiftDef.setCrttime(DateUtil.getCurrentTime(true));
            LogRecordContext.putVariable("operate", "新增");
            waShiftDo.saveOrUpdateWaShiftDef(shiftDef);
        } else {
            shiftDef.setCrtuser(userInfo.getUserId());
            shiftDef.setCrttime(DateUtil.getCurrentTime(true));
            LogRecordContext.putVariable("operate", "编辑");
            waShiftDo.saveOrUpdateWaShiftDef(shiftDef);
        }
        return shiftDef.getShiftDefId();
    }

    @Override
    public MultiShiftVo getById(Integer id) {
        WaShiftDef shiftDef = waConfigService.getShiftDefObj(UserContext.getTenantId(), id);
        if (null == shiftDef) {
            return new MultiShiftVo();
        }
        WaShiftDo.doInitTimeBelong(shiftDef);
        MultiShiftVo.doInitOnDutyTimeForView(shiftDef);
        MultiShiftVo.doSetRestPeriods(shiftDef);
        MultiShiftVo.doSetMultiWorkTimes(shiftDef);
        MultiShiftVo.doSetMultiCheckinTimes(shiftDef);
        return MultiShiftVo.doConvertToMultiVo(shiftDef);
    }

    @Transactional
    @Override
    public void deleteById(Integer id) {
        List<WaWorkRoundDo> list = workRoundService.getWorkRoundListByShiftId(id);
        if (CollectionUtils.isNotEmpty(list)) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_DELETE_NOT_ALLOW, Boolean.FALSE).getMsg());
        }
        waConfigService.deleteShiftDef(id);
    }

    @Override
    public ItemsResult<KeyValue> selectShiftDefList(String belongModule) {
        List<Map> list = waConfigService.getShiftDefList(new PageBean(true), belongModule);
        if (CollectionUtils.isEmpty(list)) {
            return new ItemsResult<>();
        }
        List<KeyValue> items = list.stream().map(it -> {
            KeyValue keyValue = new KeyValue();
            keyValue.setValue(it.get("shift_def_id"));
            keyValue.setText((String) it.get("shift_def_name"));
            return keyValue;
        }).collect(Collectors.toList());
        return new ItemsResult<>(items);
    }

    @Override
    public boolean checkShiftDefReCode(String defCode, Integer shiftDefId, String belongModule) {
        UserInfo userInfo = getUserInfo();
        return waShiftDo.checkShiftDefReCode(userInfo.getTenantId(), defCode, shiftDefId, belongModule);
    }

    @Override
    public boolean checkShiftDefReName(String defName, Integer shiftDefId, String belongModule) {
        UserInfo userInfo = getUserInfo();
        return waShiftDo.checkShiftDefReName(userInfo.getTenantId(), defName, shiftDefId, belongModule);
    }

    @Override
    public Map<Integer, List<EmpShiftInfo>> getEmpShiftInfoListMaps(Long startDate, Long endDate, String tenantId) {
        List<EmpShiftInfo> allEmpShiftInfoList = new ArrayList<>();
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(tenantId);
        Map<String, Integer> empChangeShiftMap = waEmpShiftChangeDo.getEmpShiftChangeMap(tenantId, startDate, endDate);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("belongid", tenantId);
        paramsMap.put("startDate", startDate);
        paramsMap.put("endDate", endDate);
        List<Map> shiftInfos = waRegisterRecordMapper.getEmpShiftRecords2(paramsMap);
        if (CollectionUtils.isNotEmpty(shiftInfos)) {
            List<EmpShiftInfo> shifts = JSON.parseArray(JSON.toJSONString(shiftInfos), EmpShiftInfo.class);
            for (int i = 0; i < shifts.size(); i++) {
                EmpShiftInfo shift = shifts.get(i);
                Long empId = shift.getEmpid();
                Long workDate = shift.getWorkDate();
                //班次替换
                String changedShiftKey = String.format("%s_%s", empId, workDate);
                if (empChangeShiftMap.containsKey(changedShiftKey)) {
                    WaShiftDef changedShiftDef = shiftDefMap.get(empChangeShiftMap.get(changedShiftKey));
                    if (changedShiftDef != null) {
                        shift = ObjectConverter.convert(changedShiftDef, EmpShiftInfo.class);
                        shift.setEmpid(empId);
                        shift.setWorkDate(workDate);
                        shifts.set(i, shift);
                        empChangeShiftMap.remove(changedShiftKey);
                    }
                }
            }
            allEmpShiftInfoList.addAll(shifts);
        }
        if (MapUtils.isNotEmpty(empChangeShiftMap)) {
            for (Map.Entry<String, Integer> entry : empChangeShiftMap.entrySet()) {
                EmpShiftInfo shift = ObjectConverter.convert(shiftDefMap.get(entry.getValue()), EmpShiftInfo.class);
                String empIdAndWorkDateKey = entry.getKey();
                String[] keyArray = empIdAndWorkDateKey.split("_");
                shift.setEmpid(Long.valueOf(keyArray[0]));
                shift.setWorkDate(Long.valueOf(keyArray[1]));
                allEmpShiftInfoList.add(shift);
            }
        }
        return allEmpShiftInfoList.stream().collect(Collectors.groupingBy(EmpShiftInfo::getShiftDefId));
    }

    @Override
    public List<WaShiftDo> getListByStTime(String tenantId, ShiftBelongModuleEnum belongModule,
                                           Boolean temporaryShift, Integer startTime, Integer endTime,
                                           Integer startTimeBelong, Integer endTimeBelong) {
        return waShiftDo.getListByStTime(tenantId, belongModule, temporaryShift, startTime, endTime, startTimeBelong, endTimeBelong);
    }

    @Override
    public CustShiftImportResult importShift(MultipartFile file, UserInfo userInfo) {
        try {
            UserContext.doInitSecurityUserInfo(userInfo.getTenantId(),
                    null != userInfo.getUserId() ? userInfo.getUserId().toString() : null,
                    null != userInfo.getStaffId() ? userInfo.getStaffId().toString() : null,
                    null, null, null);

            return importShift(file);
        } catch (Exception e) {
            log.error("async import cust shift error: {}", e.getMessage(), e);
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        return new CustShiftImportResult();
    }


    /**
     * 班次-导入
     *
     * @param file 班次导入文件
     */
    public CustShiftImportResult importShift(MultipartFile file) {
        CustShiftImportResult result = new CustShiftImportResult();

        if (file == null || file.isEmpty()) {
            result.addError(new CheckMessage(0, -1, ResponseWrap.wrapResult(AttendanceCodes.IMPORT_TEMPLATE_FILE, null).getMsg()));
            return result;
        }

        Workbook wb = null;
        try {
            DataInputStream dataInputStream = new DataInputStream(file.getInputStream());
            wb = FileUtil.createWorkbook(file.getOriginalFilename(), dataInputStream);
            if (wb == null) {
                result.addError(new CheckMessage(0, -1, "文件损坏"));
                return result;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        if (null == wb) {
            result.addError(new CheckMessage(0, -1, "文件损坏"));
            return result;
        }

        Sheet sheet = wb.getSheetAt(0);
        int firstRowNum = sheet.getFirstRowNum();
        int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum <= firstRowNum) {
            result.addError(new CheckMessage(0, -1, "文件内容为空"));
            return result;
        }

        // 读取表头
        Row headerRow = sheet.getRow(firstRowNum);
        int cellCount = headerRow.getLastCellNum();
        List<String> headers = new ArrayList<>();
        Map<String, Integer> headerIndexMap = new HashMap<>();
        for (int i = 0; i < cellCount; i++) {
            Cell cell = headerRow.getCell(i);
            String cellValue = (cell != null) ? cell.getStringCellValue().trim() : "";
            headers.add(cellValue);
            if (StringUtils.isNotBlank(cellValue)) {
                headerIndexMap.put(cellValue, i);
            }
        }

        // 读取数据行
        List<String> successList = new ArrayList<>();
        List<String> failList = new ArrayList<>();
        for (int i = firstRowNum + 1; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                result.addError(new CheckMessage(i - 1, -1, "数据为空"));
                continue;
            }
            DataFormatter dataFormatter = new DataFormatter();
            Map<String, String> dataMap = new HashMap<>();
            for (int j = 0; j < cellCount; j++) {
                Cell cell = row.getCell(j);
                String key = headers.get(j);
                if (StringUtils.isBlank(key)) {
                    continue;
                }
                String value = (cell != null) ? dataFormatter.formatCellValue(cell) : "";
                dataMap.put(key, value);
            }

            if (dataMap.values().stream().noneMatch(StringUtils::isNotBlank)) {
                result.addError(new CheckMessage(row.getRowNum() - 1, -1, "数据为空"));
                continue;
            }

            List<String> mustFieldList = Lists.newArrayList("班次编码", "班次名称", "日期类型", "上班时间", "下班时间", "半天定义", "工作时长(小时)", "最早最晚上班时间", "最早最晚下班时间", "加班时间段");
            if (checkMustField(result, headerIndexMap, row, dataMap, mustFieldList)) {
                continue;
            }

            String shiftDefCode = dataMap.get("班次编码");
            String shiftDefName = dataMap.get("班次名称");
            String dateTypeName = dataMap.get("日期类型");

            Integer dateType = doGetDateType(dateTypeMap, dateTypeName);
            if (dateType == null) {
                result.addError(new CheckMessage(row.getRowNum() - 1, headerIndexMap.get("日期类型"), "日期类型不存在"));
                continue;
            }

            ShiftSaveDto dto = new ShiftSaveDto();
            dto.setShiftDefCode(shiftDefCode);
            dto.setShiftDefName(shiftDefName);
            dto.setDateType(dateType);
            dto.setBelongModule(ShiftBelongModuleEnum.ATTENDANCE.getCode());

            String startTimeStr = dataMap.get("上班时间");
            if (!isValidTimeFormat(startTimeStr)) {
                result.addError(new CheckMessage(row.getRowNum() - 1, headerIndexMap.get("上班时间"), "上班时间格式不正确，应为HH:mm格式"));
                continue;
            }
            dto.setStartTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(startTimeStr));

            String endTimeStr = dataMap.get("下班时间");
            if (!isValidTimeFormat(endTimeStr)) {
                result.addError(new CheckMessage(row.getRowNum() - 1, headerIndexMap.get("下班时间"), "下班时间格式不正确，应为HH:mm格式"));
                continue;
            }
            dto.setEndTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(endTimeStr));


            String halfdayTimeStr = dataMap.get("半天定义");
            if (!isValidTimeFormat(endTimeStr)) {
                result.addError(new CheckMessage(row.getRowNum() - 1, headerIndexMap.get("半天定义"), "半天定义格式不正确，应为HH:mm格式"));
                continue;
            }
            dto.setHalfdayTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(halfdayTimeStr));

            String workTimeStr = dataMap.get("工作时长(小时)");
            if (!isValidTimeFormat(endTimeStr)) {
                result.addError(new CheckMessage(row.getRowNum() - 1, headerIndexMap.get("工作时长(小时)"), "工作时长(小时)格式不正确，应为数字"));
                continue;
            }
            dto.setWorkTotalTime(new BigDecimal(workTimeStr).multiply(new BigDecimal(60)).intValue());


            String restTime = dataMap.get("休息时间段");
            if (StringUtils.isNotBlank(restTime)) {
                try {
                    if (restTime.contains(",")) {
                        String[] workTimes = restTime.split(",");
                        for (String itemWorkTime : workTimes) {
                            doRestTime(itemWorkTime.trim(), dto);
                        }
                    } else {
                        doRestTime(restTime.trim(), dto);
                    }
                } catch (Exception e) {
                    log.error("第{}行数据休息时间段填写错误, Error:{}", row.getRowNum() + 1, e.getMessage(), e);
                    result.addError(new CheckMessage(row.getRowNum() - 1, -1, "休息时间段填写错误"));
                    continue;
                }
            } else {
                dto.setIsNoonRest(false);
            }

            String onDutyTimeSlot = dataMap.get("最早最晚上班时间");
            try {
                doOnDutyTimeSlot(onDutyTimeSlot.trim(), dto);
            } catch (Exception e) {
                log.error("第{}行数据最早最晚上班时间填写错误, Error:{}", row.getRowNum() + 1, e.getMessage(), e);
                result.addError(new CheckMessage(row.getRowNum() - 1, -1, "最早最晚上班时间填写错误"));
                continue;
            }

            String midWayTimeSlot = dataMap.get("最早最晚中途打卡时间");
            if (StringUtils.isNotBlank(midWayTimeSlot)) {
                try {
                    doMidwayTimeSlot(midWayTimeSlot.trim(), dto);
                } catch (Exception e) {
                    log.error("第{}行数据最早最晚中途打卡时间填写错误, Error:{}", row.getRowNum() + 1, e.getMessage(), e);
                    result.addError(new CheckMessage(row.getRowNum() - 1, -1, "最早最晚中途打卡时间填写错误"));
                    continue;
                }
            }


            String offDutyTimeSlot = dataMap.get("最早最晚下班时间");
            try {
                doOffDutyTimeSlot(offDutyTimeSlot.trim(), dto);
            } catch (Exception e) {
                log.error("第{}行数据最早最晚下班时间填写错误, Error:{}", row.getRowNum() + 1, e.getMessage(), e);
                result.addError(new CheckMessage(row.getRowNum() - 1, -1, "最早最晚下班时间填写错误"));
                continue;
            }

            String overTime = dataMap.get("加班时间段");
            if (StringUtils.isNotBlank(overTime)) {
                try {
                    if (overTime.contains(",")) {
                        String[] workTimes = overTime.split(",");
                        for (String itemWorkTime : workTimes) {
                            doOverTime(itemWorkTime.trim(), dto);
                        }
                    } else {
                        doOverTime(overTime.trim(), dto);
                    }
                } catch (Exception e) {
                    log.error("第{}行数据加班时间段填写错误 , Error:{}", row.getRowNum() + 1, e.getMessage(), e);
                    result.addError(new CheckMessage(row.getRowNum() - 1, -1, "加班时间段填写错误"));
                    continue;
                }
            }

            String overRestTime = dataMap.get("加班休息时间段");
            if (StringUtils.isNotBlank(overRestTime)) {
                try {
                    if (overRestTime.contains(",")) {
                        String[] workTimes = overRestTime.split(",");
                        for (String itemWorkTime : workTimes) {
                            doOverRestTime(itemWorkTime.trim(), dto);
                        }
                    } else {
                        doOverRestTime(overRestTime.trim(), dto);
                    }
                } catch (Exception e) {
                    log.error("第{}行数据加班休息时间段填写错误 , Error:{}", row.getRowNum() + 1, e.getMessage(), e);
                    result.addError(new CheckMessage(row.getRowNum() - 1, -1, "加班时间段填写错误"));
                    continue;
                }
            }

            // 保存
            try {
                Integer waShiftDefId = saveShiftDef(dto);
                if (null != waShiftDefId) {
                    successList.add(dto.getShiftDefName());
                } else {
                    failList.add(dto.getShiftDefName());
                    result.addError(new CheckMessage(row.getRowNum() - 1, -1, "保存失败"));
                }
            } catch (Exception e) {
                log.error("第{}行数据保存失败:{}", row.getRowNum() + 1, e.getMessage(), e);
                failList.add(dto.getShiftDefName());
                if (e instanceof ServerException || e instanceof CDException) {
                    result.addError(new CheckMessage(row.getRowNum() - 1, -1, "保存失败，原因：" + e.getMessage()));
                } else {
                    result.addError(new CheckMessage(row.getRowNum() - 1, -1, "保存失败"));
                }
            }
        }
        result.setSuccessList(successList)
                .setFailList(failList);
        return result;
    }


    private void doRestTime(String workTime, ShiftSaveDto dto) {
        String timePattern = "^(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)-(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)$";
        if (!Pattern.matches(timePattern, workTime)) {
            throw new ServerException("格式不正确，应为'日期类型HH:mm-日期类型HH:mm'格式，日期类型只能是'当日'或'次日'，示例：当日09:00-当日18:00 或 当日20:00-次日05:00，实际输入: " + workTime);
        }

        String[] timeParts = workTime.split("-");
        if (timeParts.length != 2) {
            throw new ServerException("工时格式不正确，缺少'-'分隔符: " + workTime);
        }

        List<RestPeriodDto> restPeriodDtoList = Optional.ofNullable(dto.getRestPeriods()).orElse(new ArrayList<>());
        RestPeriodDto restPeriodDto = new RestPeriodDto();
        // 开始时间：当日09:00
        String startTimePart = timeParts[0].trim();
        if (!validateTimePartFormat(startTimePart)) {
            throw new ServerException("开始时间格式不正确: " + startTimePart);
        }
        String startDateType = startTimePart.substring(0, 2);
        String startTimeStr = startTimePart.substring(2);

        if (!isValidDateType(startDateType)) {
            throw new ServerException("开始时间日期类型不正确，只能是'当日'或'次日': " + startDateType);
        }

        if (!isValidTimeFormat(startTimeStr)) {
            throw new ServerException("开始时间格式不正确，应为HH:mm格式: " + startTimeStr);
        }

        restPeriodDto.setNoonRestStart(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(startTimeStr));
        restPeriodDto.setNoonRestEnd(ShiftTimeBelongTypeEnum.getIndexByName(startDateType));

        // 结束时间：次日00:30
        String endTimePart = timeParts[1].trim();
        if (!validateTimePartFormat(endTimePart)) {
            throw new ServerException("结束时间格式不正确: " + endTimePart);
        }
        String endDateType = endTimePart.substring(0, 2);
        String endTimeStr = endTimePart.substring(2);

        if (!isValidDateType(endDateType)) {
            throw new ServerException("结束时间日期类型不正确，只能是'当日'或'次日': " + endDateType);
        }

        if (!isValidTimeFormat(endTimeStr)) {
            throw new ServerException("结束时间格式不正确，应为HH:mm格式: " + endTimeStr);
        }

        restPeriodDto.setNoonRestEnd(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(endTimeStr));
        restPeriodDto.setNoonRestEndBelong(ShiftTimeBelongTypeEnum.getIndexByName(endDateType));
        restPeriodDtoList.add(restPeriodDto);
        dto.setIsNoonRest(true);
        dto.setRestPeriods(restPeriodDtoList);
    }

    private void doOverTime(String workTime, ShiftSaveDto dto) {
        String timePattern = "^(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)-(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)$";
        if (!Pattern.matches(timePattern, workTime)) {
            throw new ServerException("格式不正确，应为'日期类型HH:mm-日期类型HH:mm'格式，日期类型只能是'当日'或'次日'，示例：当日09:00-当日18:00 或 当日20:00-次日05:00，实际输入: " + workTime);
        }

        String[] timeParts = workTime.split("-");
        if (timeParts.length != 2) {
            throw new ServerException("格式不正确，缺少'-'分隔符: " + workTime);
        }

        List<MultiOvertimeDto> multiOvertimeDtoList = Optional.ofNullable(dto.getMultiOvertime()).orElse(new ArrayList<>());
        MultiOvertimeDto multiOvertimeDto = new MultiOvertimeDto();
        // 开始时间：当日09:00
        String startTimePart = timeParts[0].trim();
        if (!validateTimePartFormat(startTimePart)) {
            throw new ServerException("开始时间格式不正确: " + startTimePart);
        }
        String startDateType = startTimePart.substring(0, 2);
        String startTimeStr = startTimePart.substring(2);

        if (!isValidDateType(startDateType)) {
            throw new ServerException("开始时间日期类型不正确，只能是'当日'或'次日': " + startDateType);
        }

        if (!isValidTimeFormat(startTimeStr)) {
            throw new ServerException("开始时间格式不正确，应为HH:mm格式: " + startTimeStr);
        }

        multiOvertimeDto.setOvertimeStartTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(startTimeStr));
        multiOvertimeDto.setOvertimeStartTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(startDateType));

        String endTimePart = timeParts[1].trim();
        if (!validateTimePartFormat(endTimePart)) {
            throw new ServerException("结束时间格式不正确: " + endTimePart);
        }
        String endDateType = endTimePart.substring(0, 2);
        String endTimeStr = endTimePart.substring(2);

        if (!isValidDateType(endDateType)) {
            throw new ServerException("结束时间日期类型不正确，只能是'当日'或'次日': " + endDateType);
        }

        if (!isValidTimeFormat(endTimeStr)) {
            throw new ServerException("结束时间格式不正确，应为HH:mm格式: " + endTimeStr);
        }

        multiOvertimeDto.setOvertimeEndTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(endTimeStr));
        multiOvertimeDto.setOvertimeEndTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(endDateType));
        multiOvertimeDtoList.add(multiOvertimeDto);
        dto.setMultiOvertime(multiOvertimeDtoList);
    }

    private void doOverRestTime(String workTime, ShiftSaveDto dto) {
        String timePattern = "^(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)-(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)$";
        if (!Pattern.matches(timePattern, workTime)) {
            throw new ServerException("格式不正确，应为'日期类型HH:mm-日期类型HH:mm'格式，日期类型只能是'当日'或'次日'，示例：当日09:00-当日18:00 或 当日20:00-次日05:00，实际输入: " + workTime);
        }

        String[] timeParts = workTime.split("-");
        if (timeParts.length != 2) {
            throw new ServerException("格式不正确，缺少'-'分隔符: " + workTime);
        }

        List<OvertimeRestPeriodsDto> overtimeRestPeriodsDtoList = Optional.ofNullable(dto.getOvertimeRestPeriods()).orElse(new ArrayList<>());
        OvertimeRestPeriodsDto overtimeRestPeriodsDto = new OvertimeRestPeriodsDto();
        // 开始时间：当日09:00
        String startTimePart = timeParts[0].trim();
        if (!validateTimePartFormat(startTimePart)) {
            throw new ServerException("开始时间格式不正确: " + startTimePart);
        }
        String startDateType = startTimePart.substring(0, 2);
        String startTimeStr = startTimePart.substring(2);

        if (!isValidDateType(startDateType)) {
            throw new ServerException("开始时间日期类型不正确，只能是'当日'或'次日': " + startDateType);
        }

        if (!isValidTimeFormat(startTimeStr)) {
            throw new ServerException("开始时间格式不正确，应为HH:mm格式: " + startTimeStr);
        }

        overtimeRestPeriodsDto.setOvertimeRestStartTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(startTimeStr));
        overtimeRestPeriodsDto.setOvertimeRestStartTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(startDateType));

        String endTimePart = timeParts[1].trim();
        if (!validateTimePartFormat(endTimePart)) {
            throw new ServerException("结束时间格式不正确: " + endTimePart);
        }
        String endDateType = endTimePart.substring(0, 2);
        String endTimeStr = endTimePart.substring(2);

        if (!isValidDateType(endDateType)) {
            throw new ServerException("结束时间日期类型不正确，只能是'当日'或'次日': " + endDateType);
        }

        if (!isValidTimeFormat(endTimeStr)) {
            throw new ServerException("结束时间格式不正确，应为HH:mm格式: " + endTimeStr);
        }

        overtimeRestPeriodsDto.setOvertimeRestEndTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(endTimeStr));
        overtimeRestPeriodsDto.setOvertimeRestEndTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(endDateType));

        overtimeRestPeriodsDtoList.add(overtimeRestPeriodsDto);
        dto.setOvertimeRestPeriods(overtimeRestPeriodsDtoList);
    }

    private void doOnDutyTimeSlot(String workTime, ShiftSaveDto dto) {
        String timePattern = "^(前日|当日|次日)([01]\\d|2[0-3]):([0-5]\\d)-(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)$";
        if (!Pattern.matches(timePattern, workTime)) {
            throw new ServerException("最早最晚上班时间格式不正确,实际输入: " + workTime);
        }

        String[] timeParts = workTime.split("-");
        if (timeParts.length != 2) {
            throw new ServerException("工时格式不正确，缺少'-'分隔符: " + workTime);
        }

        // 开始时间：当日09:00
        String startTimePart = timeParts[0].trim();
        if (!validateTimePartBeginFormat(startTimePart)) {
            throw new ServerException("开始时间格式不正确: " + startTimePart);
        }
        String startDateType = startTimePart.substring(0, 2);
        String startTimeStr = startTimePart.substring(2);

        if (!isValidBeginDateType(startDateType)) {
            throw new ServerException("开始时间日期类型不正确，只能是'前日'、'当日'或'次日': " + startDateType);
        }

        if (!isValidTimeFormat(startTimeStr)) {
            throw new ServerException("开始时间格式不正确，应为HH:mm格式: " + startTimeStr);
        }

        dto.setOnDutyStartTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(startTimeStr));
        dto.setOnDutyStartTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(startDateType));

        // 结束时间：次日00:30
        String endTimePart = timeParts[1].trim();
        if (!validateTimePartFormat(endTimePart)) {
            throw new ServerException("结束时间格式不正确: " + endTimePart);
        }
        String endDateType = endTimePart.substring(0, 2);
        String endTimeStr = endTimePart.substring(2);

        if (!isValidDateType(endDateType)) {
            throw new ServerException("结束时间日期类型不正确，只能是'当日'或'次日': " + endDateType);
        }

        if (!isValidTimeFormat(endTimeStr)) {
            throw new ServerException("结束时间格式不正确，应为HH:mm格式: " + endTimeStr);
        }

        dto.setOnDutyEndTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(endTimeStr));
        dto.setOnDutyEndTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(endDateType));
    }

    private void doMidwayTimeSlot(String workTime, ShiftSaveDto dto) {
        String timePattern = "^(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)-(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)$";
        if (!Pattern.matches(timePattern, workTime)) {
            throw new ServerException("最早最晚中途打卡时间格式不正确,实际输入: " + workTime);
        }

        String[] timeParts = workTime.split("-");
        if (timeParts.length != 2) {
            throw new ServerException("工时格式不正确，缺少'-'分隔符: " + workTime);
        }

        List<MidwayClockTimeDto> midwayClockTimes = Optional.ofNullable(dto.getMidwayClockTimes()).orElse(new ArrayList<>());
        MidwayClockTimeDto midwayClockTimeDto = new MidwayClockTimeDto();
        // 开始时间：当日09:00
        String startTimePart = timeParts[0].trim();
        if (!validateTimePartFormat(startTimePart)) {
            throw new ServerException("开始时间格式不正确: " + startTimePart);
        }
        String startDateType = startTimePart.substring(0, 2);
        String startTimeStr = startTimePart.substring(2);

        if (!isValidBeginDateType(startDateType)) {
            throw new ServerException("开始时间日期类型不正确，只能是'前日','当日'或'次日': " + startDateType);
        }

        if (!isValidTimeFormat(startTimeStr)) {
            throw new ServerException("开始时间格式不正确，应为HH:mm格式: " + startTimeStr);
        }

        midwayClockTimeDto.setStartTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(startTimeStr));
        midwayClockTimeDto.setStartTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(startDateType));

        String endTimePart = timeParts[1].trim();
        if (!validateTimePartFormat(endTimePart)) {
            throw new ServerException("结束时间格式不正确: " + endTimePart);
        }
        String endDateType = endTimePart.substring(0, 2);
        String endTimeStr = endTimePart.substring(2);

        if (!isValidDateType(endDateType)) {
            throw new ServerException("结束时间日期类型不正确，只能是'当日'或'次日': " + endDateType);
        }

        if (!isValidTimeFormat(endTimeStr)) {
            throw new ServerException("结束时间格式不正确，应为HH:mm格式: " + endTimeStr);
        }

        midwayClockTimeDto.setEndTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(endTimeStr));
        midwayClockTimeDto.setEndTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(endDateType));
        midwayClockTimes.add(midwayClockTimeDto);
        dto.setMidwayClockTimes(midwayClockTimes);
    }

    private void doOffDutyTimeSlot(String workTime, ShiftSaveDto dto) {
        String timePattern = "^(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)-(当日|次日)([01]\\d|2[0-3]):([0-5]\\d)$";
        if (!Pattern.matches(timePattern, workTime)) {
            throw new ServerException("最早最晚上班时间格式不正确,实际输入: " + workTime);
        }

        String[] timeParts = workTime.split("-");
        if (timeParts.length != 2) {
            throw new ServerException("工时格式不正确，缺少'-'分隔符: " + workTime);
        }

        // 开始时间：当日09:00
        String startTimePart = timeParts[0].trim();
        if (!validateTimePartFormat(startTimePart)) {
            throw new ServerException("开始时间格式不正确: " + startTimePart);
        }
        String startDateType = startTimePart.substring(0, 2);
        String startTimeStr = startTimePart.substring(2);

        if (!isValidDateType(startDateType)) {
            throw new ServerException("开始时间日期类型不正确，只能是'当日'或'次日': " + startDateType);
        }

        if (!isValidTimeFormat(startTimeStr)) {
            throw new ServerException("开始时间格式不正确，应为HH:mm格式: " + startTimeStr);
        }

        dto.setOffDutyStartTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(startTimeStr));
        dto.setOffDutyStartTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(startDateType));

        String endTimePart = timeParts[1].trim();
        if (!validateTimePartFormat(endTimePart)) {
            throw new ServerException("结束时间格式不正确: " + endTimePart);
        }
        String endDateType = endTimePart.substring(0, 2);
        String endTimeStr = endTimePart.substring(2);

        if (!isValidDateType(endDateType)) {
            throw new ServerException("结束时间日期类型不正确，只能是'当日'或'次日': " + endDateType);
        }

        if (!isValidTimeFormat(endTimeStr)) {
            throw new ServerException("结束时间格式不正确，应为HH:mm格式: " + endTimeStr);
        }

        dto.setOffDutyEndTime(com.caidaocloud.attendance.service.infrastructure.util.DateUtil.timeStrToMinute(endTimeStr));
        dto.setOffDutyEndTimeBelong(ShiftTimeBelongTypeEnum.getIndexByName(endDateType));
    }

    private boolean validateTimePartFormat(String timePart) {
        if (StringUtils.isBlank(timePart) || timePart.length() != 7) {
            return false;
        }
        // 前两个字符必须是日期类型，后5个字符必须是HH:mm格式
        String dateType = timePart.substring(0, 2);
        String timeStr = timePart.substring(2);
        return isValidDateType(dateType) && isValidTimeFormat(timeStr);
    }

    private boolean validateTimePartBeginFormat(String timePart) {
        if (StringUtils.isBlank(timePart) || timePart.length() != 7) {
            return false;
        }
        // 前两个字符必须是日期类型，后5个字符必须是HH:mm格式
        String dateType = timePart.substring(0, 2);
        String timeStr = timePart.substring(2);
        return isValidBeginDateType(dateType) && isValidTimeFormat(timeStr);
    }

    private boolean isValidDateType(String dateType) {
        return "当日".equals(dateType) || "次日".equals(dateType);
    }

    private boolean isValidBeginDateType(String dateType) {
        return "当日".equals(dateType) || "次日".equals(dateType) || "前日".equals(dateType);
    }

    private boolean isValidTimeFormat(String timeStr) {
        if (StringUtils.isBlank(timeStr) || timeStr.length() != 5 || !timeStr.contains(":")) {
            return false;
        }
        String[] parts = timeStr.split(":");
        if (parts.length != 2) {
            return false;
        }
        try {
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);
            return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public boolean checkMustField(CustShiftImportResult result, Map<String, Integer> headerIndexMap, Row row, Map<String, String> dataMap, List<String> mustFieldList) {
        Boolean isError = false;
        for (String field : mustFieldList) {
            if (StringUtils.isBlank(dataMap.get(field))) {
                CheckMessage checkMessage = new CheckMessage(row.getRowNum() - 1, headerIndexMap.get(field), field + " 不能为空");
                result.addError(checkMessage);
                isError = true;
            }
        }
        return isError;
    }

    public Integer doGetDateType(Map<String, Integer> dateTypeMap, String rowData) {
        if (!dateTypeMap.containsKey(rowData)) {
            return null;
        }
        return dateTypeMap.get(rowData);
    }
}
