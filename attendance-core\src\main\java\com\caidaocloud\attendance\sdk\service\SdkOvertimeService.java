package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.core.wa.dto.ot.GetOtTimeResultDto;
import com.caidaocloud.attendance.core.wa.dto.ot.OtCompensateTypeListDto;
import com.caidaocloud.attendance.sdk.dto.SdkOtRevokeDTO;
import com.caidaocloud.attendance.sdk.dto.SdkOverApplySaveDTO;
import com.caidaocloud.attendance.sdk.feign.IOvertimeFeignClient;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 加班申请
 *
 * <AUTHOR>
 * @Date 2023/6/19
 */
@Slf4j
@Service
public class SdkOvertimeService {
    @Autowired
    private IOvertimeFeignClient overtimeFeignClient;

    /**
     * 获取加班时长
     *
     * @param empId
     * @param overtimeTypeId
     * @param startTime
     * @param endTime
     * @param stime
     * @param etime
     * @return
     */
    public Result<GetOtTimeResultDto> getOtTotaltime(Long empId, Integer overtimeTypeId, Integer startTime,
                                                     Integer endTime, Integer stime, Integer etime) {
        return overtimeFeignClient.getOtTotaltime(empId, overtimeTypeId, startTime, endTime, stime, etime, null);
    }

    /**
     * 获取加班时长
     *
     * @param empId
     * @param overtimeTypeId
     * @param startTime
     * @param endTime
     * @param stime
     * @param etime
     * @param overtimeDate   加班日期（年月日unix时间戳，精确到秒）
     * @return
     */
    public Result<GetOtTimeResultDto> getOtTotaltime(Long empId, Integer overtimeTypeId, Integer startTime,
                                                     Integer endTime, Integer stime, Integer etime, Long overtimeDate) {
        return overtimeFeignClient.getOtTotaltime(empId, overtimeTypeId, startTime, endTime, stime, etime, overtimeDate);
    }

    /**
     * 获取加班日期列表
     *
     * @param empId
     * @param startTime
     * @param endTime
     * @param stime
     * @param etime
     * @return
     */
    public Result<List<KeyValue>> getOvertimeDateList(Long empId, Integer startTime, Integer endTime, Integer stime, Integer etime) {
        return overtimeFeignClient.getOvertimeDateList(empId, startTime, endTime, stime, etime);
    }

    /**
     * 保存加班单
     *
     * @param dto
     * @return
     */
    public Result<?> saveOverApply(SdkOverApplySaveDTO dto) {
        return overtimeFeignClient.saveOverApply(dto);
    }

    /**
     * 撤销加班单
     *
     * @param dto
     * @return
     */
    public Result<?> revokeEmpOt(SdkOtRevokeDTO dto) {
        return overtimeFeignClient.revokeEmpOt(dto);
    }

    /**
     * 加班结余时长
     *
     * @param empId 员工
     * @return
     */
    public Result<?> getEmpOvertimeLeftDuration(Long empId) {
        return overtimeFeignClient.getEmpOvertimeLeftDuration(empId);
    }

    /**
     * 加班申请时：获取补偿方式
     *
     * @param empid
     * @param start
     * @param end
     * @return
     */
    public List<OtCompensateTypeListDto> getCompensateTypeList(Long empid, Long start, Long end) {
        Result<List<OtCompensateTypeListDto>> listResult = overtimeFeignClient.getCompensateTypeList(empid, start, end, null);
        List<OtCompensateTypeListDto> dataList = null;
        if (null == listResult || !listResult.isSuccess() || null == (dataList = listResult.getData())) {
            return Lists.newArrayList();
        }
        return dataList;
    }

    /**
     * 加班申请时：获取补偿方式
     *
     * @param empid
     * @param start
     * @param end
     * @param overtimeDate 加班日期（年月日unix时间戳，精确到秒）
     * @return
     */
    public List<OtCompensateTypeListDto> getCompensateTypeList(Long empid, Long start, Long end, Long overtimeDate) {
        Result<List<OtCompensateTypeListDto>> listResult = overtimeFeignClient.getCompensateTypeList(empid, start, end, overtimeDate);
        List<OtCompensateTypeListDto> dataList = null;
        if (null == listResult || !listResult.isSuccess() || null == (dataList = listResult.getData())) {
            return Lists.newArrayList();
        }
        return dataList;
    }

    public Result<?> getEmpPeriodOtDuration(Long empId, Long startDate) {
        return overtimeFeignClient.getEmpPeriodOtDuration(empId, startDate);
    }
}
