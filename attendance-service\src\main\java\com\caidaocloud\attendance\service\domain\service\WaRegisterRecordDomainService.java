package com.caidaocloud.attendance.service.domain.service;

import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordOfPortalDo;
import com.caidaocloud.attendance.service.domain.repository.IRegisterRecordRepository;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 补卡领域service
 *
 * <AUTHOR>
 * @date 2023/11/15
 **/
@Service
public class WaRegisterRecordDomainService {
    @Resource
    private IRegisterRecordRepository registerRecordRepository;

    /**
     * 获取补卡门户分页
     *
     * @param queryPageBean
     * @return
     */
    public PageResult<WaRegisterRecordOfPortalDo> getPageOfPortal(QueryPageBean queryPageBean) {
        return registerRecordRepository.getPageOfPortal(queryPageBean);
    }
}