package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.sdk.dto.SdkRevokeBatchAnalyseResultAdjustDto;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeBatchLeaveDto;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeBatchOvertimeDto;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeEmpTraveDto;
import com.caidaocloud.attendance.sdk.feign.fallback.BatchApplyFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 批量申请
 *
 * <AUTHOR>
 * @Date 2024/8/8
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = BatchApplyFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "batchApplyFeignClient")
public interface IBatchApplyFeignClient {

    /**
     * 撤销批量休假单
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/batch/leave/v1/revoke")
    Result<?> revokeBatchLeave(@RequestBody SdkRevokeBatchLeaveDto dto);

    /**
     * 撤销批量加班单据
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/batch/overtime/v1/revoke")
    Result<?> revokeBatchOt(@RequestBody SdkRevokeBatchOvertimeDto dto);

    /**
     * 撤销批量出差单
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/batch/travel/v1/revoke")
    Result<?> revokeBatchTravel(@RequestBody SdkRevokeEmpTraveDto dto);

    /**
     * 撤销批量考勤异常申请
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/attendance/batch/analyseResultAdjust/v1/revoke")
    Result<?> revokeAnalyseResultAdjust(@RequestBody SdkRevokeBatchAnalyseResultAdjustDto dto);

}
