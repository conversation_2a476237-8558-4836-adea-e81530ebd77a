package com.caidaocloud.attendance.sdk.feign.fallback;

import com.caidaocloud.attendance.sdk.dto.SdkLeaveCancelDTO;
import com.caidaocloud.attendance.sdk.feign.ILeaveCancelFeignClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

/**
 * 销假申请
 *
 * <AUTHOR>
 * @Date 2023/6/20
 */
@Component
public class LeaveCancelFeignFallBack implements ILeaveCancelFeignClient {
    @Override
    public Result<?> apply(SdkLeaveCancelDTO dto) {
        return Result.fail();
    }

    @Override
    public Result<?> getTime(SdkLeaveCancelDTO dto) {
        return Result.fail();
    }
}
