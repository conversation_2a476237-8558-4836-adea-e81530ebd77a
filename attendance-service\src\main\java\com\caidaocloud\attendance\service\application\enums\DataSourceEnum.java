package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

public enum DataSourceEnum {
    AUTO("系统生成", AttendanceCodes.SYS_GENERATED),
    MANUAL("新增/导入", AttendanceCodes.MANUAL),
    TRAVEL("出差自动结转", AttendanceCodes.TRAVEL_AUTO_CARRY),
    MAN_HOUR("工时自动结转", AttendanceCodes.WORKING_HOUR_AUTO_CARRY);
    private String desc;
    private Integer code;

    DataSourceEnum(String desc, Integer code) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public static String getDescByName(String name) {
        for (DataSourceEnum row : DataSourceEnum.values()) {
            if (row.name().equals(name)) {
                String i18n = ResponseWrap.wrapResult(row.code, null).getMsg();
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return row.getDesc();
            }
        }
        return "";
    }
}