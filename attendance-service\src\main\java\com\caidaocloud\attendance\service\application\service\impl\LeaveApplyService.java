package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.enums.LeaveStatusEnum;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.service.util.SessionBeanUtil;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.LangUtil;
import com.caidao1.commons.utils.StringUtil;
import com.caidao1.employee.mybatis.mapper.EmpInfoMapper;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.mobile.bean.SessionBean;
import com.caidao1.mobile.service.MobileV16Service;
import com.caidao1.report.common.OpEnum;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.mapper.SysConfigMapper;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysConfig;
import com.caidao1.system.mybatis.model.SysConfigExample;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.wa.enums.LeaveCancelStatusEnum;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidao1.wa.service.WaLeaveService;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.TimeRangeCheckUtil;
import com.caidaocloud.attendance.core.wa.dto.EmpLeaveInfo;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.WaLeaveDaytimeExtDto;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaLeaveCoreService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.dto.WfBusinessDataDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.FileBaseInfoDto;
import com.caidaocloud.attendance.service.application.dto.LeaveQuotaDetailDto;
import com.caidaocloud.attendance.service.application.dto.leave.LeaveApplyResultDto;
import com.caidaocloud.attendance.service.application.dto.msg.ClockMsgDto;
import com.caidaocloud.attendance.service.application.enums.DayHalfTypeEnum;
import com.caidaocloud.attendance.service.application.enums.HomeLeaveType;
import com.caidaocloud.attendance.service.application.enums.PeriodTypeEnum;
import com.caidaocloud.attendance.service.application.enums.PreTimeUnitEnum;
import com.caidaocloud.attendance.service.application.event.publish.AttendanceWorkflowMsgPublish;
import com.caidaocloud.attendance.service.application.event.publish.DelayMsgPublish;
import com.caidaocloud.attendance.service.application.event.publish.MessageParams;
import com.caidaocloud.attendance.service.application.feign.MasterFeignClient;
import com.caidaocloud.attendance.service.application.service.ILeaveApplyService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.application.service.user.MyCenterService;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.domain.service.WaEmpLeaveCancelDomainService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpLeaveMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.LeaveRecordMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.QuotaMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.UserLeaveApplyListPo;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PatternUtil;
import com.caidaocloud.attendance.service.infrastructure.util.QueryAdapterUtil;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.quota.LeaveQuotaDto;
import com.caidaocloud.attendance.service.interfaces.vo.HolidayQuotaVo;
import com.caidaocloud.attendance.service.interfaces.vo.KeyValueVo;
import com.caidaocloud.attendance.service.interfaces.vo.MaternityLeaveRangeVo;
import com.caidaocloud.attendance.service.interfaces.vo.UserLeaveApplyListVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.SysConfigsEnum;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Pair;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 休假申请
 *
 * <AUTHOR>
 * @Date 2021/3/19
 */
@Slf4j
@Service
public class LeaveApplyService implements ILeaveApplyService {
    private static final int PAGE_SIZE = 500;
    @Autowired
    private WaEmpLeaveTimeMapper waEmpLeaveTimeMapper;
    @Autowired
    private WaLeaveDaytimeMapper waLeaveDaytimeMapper;
    @Autowired
    private WaCheckMapper waCheckMapper;
    @Autowired
    private EmpCompensatoryQuotaMapper waEmpCompensatoryQuotaMapper;
    @Autowired
    private WaLeaveQuotaUseMapper waLeaveQuotaUseMapper;
    @Autowired
    private WaLeaveTypeDefMapper waLeaveTypeDefMapper;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private WaLeaveFileMapper waLeaveFileMapper;
    @Autowired
    private LeaveRecordMapper leaveRecordMapper;
    @Autowired
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private EmpInfoMapper empInfoMapper;
    @Autowired
    private WaLeaveCoreService waLeaveCoreService;
    @Autowired
    private WaLeaveService waLeaveService;
    @Autowired
    private MobileV16Service mobileV16Service;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;
    @Autowired
    private WaSobService waSobService;
    @Autowired
    private AttendanceWorkflowMsgPublish attendanceWorkflowMsgPublish;
    @Autowired
    private WaEmpLeaveDo waEmpLeaveDo;
    @Autowired
    private WaEmpLeaveCancelDomainService leaveCancelDomainService;
    @Autowired
    private WaEmpLeaveCancelService waEmpLeaveCancelService;
    @Autowired
    private NotifyConfigDo notifyConfigDo;
    @Autowired
    private DelayMsgPublish delayMsgPublish;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    @Lazy
    private IWfService wfService;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private WorkflowCallBackService workflowCallBackService;
    @Autowired
    private LeaveQuotaConfigDo leaveQuotaConfigDo;
    @Autowired
    private QuotaGenRuleDo quotaGenRuleDo;
    @Autowired
    private SysEmpInfoDo sysEmpInfo;
    @Autowired
    private EmpLeaveMapper empLeaveMapper;
    @Autowired
    private QuotaMapper quotaMapper;
    @Autowired
    private MasterFeignClient masterdataFeign;
    @Autowired
    private MyCenterService myCenterService;
    @Autowired
    private WaLeaveDaytimeDo waLeaveDaytimeDo;
    @Autowired
    private WaEmpLeaveCancelDaytimeDo waEmpLeaveCancelDaytimeDo;

    public UserInfo getUserInfo() {
        UserInfo userInfo = sessionService.getUserInfo();
        if (null != userInfo) {
            return userInfo;
        }
        SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        if (null == securityUserInfo) {
            return null;
        }
        userInfo = new UserInfo();
        userInfo.setTenantId(securityUserInfo.getTenantId());
        userInfo.setStaffId(securityUserInfo.getEmpId());
        userInfo.setUserId(securityUserInfo.getUserId());
        return userInfo;
    }

    @Override
    public Map getEmpLeaveById(Integer leaveId) {
        Map map = waLeaveService.getEmpLeaveById(SessionHolder.getBelongOrgId(), leaveId);
        WaLeaveFileExample example = new WaLeaveFileExample();
        example.createCriteria().andLeaveIdEqualTo(leaveId);
        List<WaLeaveFile> leaveFiles = waLeaveFileMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(leaveFiles)) {
            List<FileBaseInfoDto> fileBaseInfoDtoList = new ArrayList<>();
            leaveFiles.forEach(row -> {
                if (row.getUrl().contains(",")) {
                    String[] ids = row.getUrl().split(",");
                    String[] names = row.getFileName().split(",");
                    for (int i = 0; i < ids.length; i++) {
                        fileBaseInfoDtoList.add(new FileBaseInfoDto(ids[i], names[i]));
                    }
                } else {
                    fileBaseInfoDtoList.add(new FileBaseInfoDto(row.getUrl(), row.getFileName()));
                }
            });
            map.put("files", fileBaseInfoDtoList);
        }
        return map;
    }

    @Override
    public void saveLeaveAttachments(AttachmentDto dto) {
        String businessKey = dto.getBusinessKey();
        if (StringUtils.isNotBlank(businessKey)) {
            Integer leaveId = null;
            if (businessKey.contains("_")) {
                leaveId = Integer.valueOf(businessKey.split("_")[0]);
            } else {
                leaveId = Integer.valueOf(businessKey);
            }
            boolean uploadFile = StringUtils.isNotBlank(dto.getFile()) && StringUtils.isNotBlank(dto.getFileName());
            WaLeaveFileExample example = new WaLeaveFileExample();
            example.createCriteria().andLeaveIdEqualTo(leaveId);
            List<WaLeaveFile> fileList = waLeaveFileMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(fileList)) {
                if (uploadFile) {
                    WaLeaveFile leaveFile = new WaLeaveFile();
                    leaveFile.setLeaveId(leaveId);
                    leaveFile.setUrl(dto.getFile());
                    leaveFile.setFileName(dto.getFileName());
                    waLeaveFileMapper.insertSelective(leaveFile);
                }
            } else {
                WaLeaveFile oldLeaveFile = fileList.get(0);
                if (uploadFile) {
                    oldLeaveFile.setUrl(dto.getFile());
                    oldLeaveFile.setFileName(dto.getFileName());
                    waLeaveFileMapper.updateByPrimaryKeySelective(oldLeaveFile);
                } else {
                    waLeaveFileMapper.deleteByPrimaryKey(oldLeaveFile.getLeaveFileId());
                }
            }
        }
    }

    /**
     * 保存休假单
     *
     * @param saveDto
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveLeaveApply(LeaveApplySaveDto saveDto, boolean enableWorkflow) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();

        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(saveDto.getLeaveTypeId());
        if (waLeaveType == null) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, null).getMsg());
        }
        waLeaveType.setIsRestDay(Optional.ofNullable(waLeaveType.getIsRestDay()).orElse(false));
        waLeaveType.setIsLegalHoliday(Optional.ofNullable(waLeaveType.getIsLegalHoliday()).orElse(false));

        Result checkForMultiShiftResult = LeaveApplySaveDto.checkForMultiShift(saveDto, waLeaveType);
        if (!checkForMultiShiftResult.isSuccess()) {
            return checkForMultiShiftResult;
        }

        LeaveApplySaveDto.setLeavePeriod(saveDto, waLeaveType);
        saveDto.setLeaveTimeList(LeaveApplySaveDto.getTimeJsonList(saveDto));
        saveDto.setEnableWorkflow(enableWorkflow);

        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(saveDto.getEmpid());
        if (empInfo == null) {
            return Result.fail(messageResource.getMessage("L006835", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
        }
        int tmType = (empInfo.getTmType() == null || empInfo.getTmType() == 0) ? 1 : empInfo.getTmType();
        empInfo.setTmType(tmType);

        Map<Integer, WaShiftDef> shiftMap = waCommonService.getCorpAllShiftDef(userInfo.getTenantId());
        if (null == shiftMap) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, null).getMsg());
        }

        // 保存休假单据
        WaEmpLeave empLeave = doContractWaEmpLeave(saveDto, waLeaveType);
        if (empLeave.getLeaveId() != null) {//重新发起 1、删除旧的请假数据 2 、重新添加（但是leaveId不变）
            deleteLeave(empLeave.getLeaveId());
        }
        waEmpLeaveMapper.insertSelective(empLeave);
        empLeaveMapper.updateHomeLeaveInfo(saveDto.getHomeLeaveType(), saveDto.getMarriageStatus(), empLeave.getLeaveId());

        // 保存休假附件
        WaLeaveFile leaveFile = doContractWaLeaveFile(saveDto, empLeave);
        if (null != leaveFile) {
            waLeaveFileMapper.insertSelective(leaveFile);
        }

        String leaveStartDate = "";// 记录本次休假单据的开始时间，用于后续的校验
        BigDecimal totalTimeDuration = new BigDecimal(0);
        List<String> leaveTimeSlotList = Lists.newArrayList();
        List<WaLeaveDaytime> allDaytimeList = new ArrayList<>();

        // 计算并保存休假明细
        for (Map<String, Object> leaveMap : saveDto.getLeaveTimeList()) {
            String startTimeStr = (String) leaveMap.get("starttime");
            String endTimeStr = (String) leaveMap.get("endtime");
            long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
            long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);

            if ("".equals(leaveStartDate)) {
                leaveStartDate = startTimeStr;
            }
            leaveTimeSlotList.add(getLeaveTimeSlot(leaveMap));

            // 查询排班
            Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(userInfo.getTenantId(), saveDto.getEmpid(), tmType,
                    startDate - 86400, endDate, empInfo.getWorktimeType(), true);
            if (MapUtils.isEmpty(pbMap)) {
                return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, null).getMsg());
            }
            pbMap.forEach((date, worktimeDetail) -> {
                WaShiftDef shiftDef = shiftMap.get(worktimeDetail.getShiftDefId());
                if (DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType())) {
                    worktimeDetail.setDateType(shiftDef.getDateType());
                }
            });

            // 保存休假详情和每日明细
            WaEmpLeaveTime leaveTime = SpringUtil.getBean(LeaveApplyService.class)
                    .saveLeaveTime(saveDto, empLeave, leaveMap, pbMap, shiftMap, waLeaveType, allDaytimeList);
            BigDecimal leaveTimeTotal = BigDecimal.valueOf(leaveTime.getTimeDuration());
            totalTimeDuration = totalTimeDuration.add(leaveTimeTotal);

            // 后补附件开启不用校验附件是否必填
            Result<Boolean> checkLeaveFilesResult = checkLeaveFiles(saveDto, waLeaveType, leaveTimeTotal);
            if (!checkLeaveFilesResult.isSuccess()) {
                deleteLeave(empLeave.getLeaveId());
                return checkLeaveFilesResult;
            }
        }

        // 假期规则校验
        String validateRuleMsg = validateLeaveSetRule(waLeaveType, empLeave, leaveStartDate);
        if (!"".equals(validateRuleMsg)) {
            return Result.fail(validateRuleMsg);
        }

        //请假配额扣减
        //CAIDAOM-1710 铁通需求
        //产假固定额度不生成假期额度，则申请休假时，不做假期配额扣减
        if (waLeaveType.getLeaveType() != 4) {
            String errorMsg = mobileV16Service.checkOrDecLeaveQuota(waLeaveType, allDaytimeList, empInfo, userInfo.getUserId(), false,
                    Integer.valueOf(LeaveStatusEnum.LEAVE_STATUS_1.value), saveDto.getHomeLeaveType(), saveDto.getMarriageStatus());
            if (!"".equals(errorMsg)) {
                deleteLeave(empLeave.getLeaveId());
                return Result.fail(errorMsg);
            }
        }

        // 更新休假单据
        WaEmpLeave updateEmpLeave = new WaEmpLeave();
        updateEmpLeave.setLeaveId(empLeave.getLeaveId());
        updateEmpLeave.setTimeSlot(StringUtils.join(leaveTimeSlotList, ";"));
        updateEmpLeave.setTimeUnit(waLeaveType.getAcctTimeType());
        if (leaveStartDate.length() > 10) {
            leaveStartDate = leaveStartDate.substring(0, 10);
        }
        updateEmpLeave.setStartDate(leaveStartDate);
        updateEmpLeave.setTotalTimeDuration(totalTimeDuration.floatValue());
        updateEmpLeave.setUpdtime(System.currentTimeMillis() / 1000);
        updateEmpLeave.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
        waEmpLeaveMapper.updateByPrimaryKeySelective(updateEmpLeave);

        // 发起工作流
        String businessKey = String.valueOf(empLeave.getLeaveId());
        String wfBusKey = String.format("%s_%s", businessKey, BusinessCodeEnum.LEAVE.getCode());
        if (!saveDto.isIfBtch()) {
            Result<Boolean> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.LEAVE.getCode());
            if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
                return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null);
            }
            Boolean workflowEnabledResultData = checkWorkflowEnableResult.getData();
            if (!workflowEnabledResultData) {
                if (checkSwitchStatus(SysConfigsEnum.LEAVE_WORKFLOW_SWITCH.name(), userInfo.getTenantId())) {
                    return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null);
                }
                if (saveDto.getEnableWorkflow()) {
                    WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
                    wfCallbackResultDto.setBusinessKey(wfBusKey);
                    wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
                    wfCallbackResultDto.setTenantId(empInfo.getBelongOrgId());
                    workflowCallBackService.saveWfLeaveApproval(wfCallbackResultDto);
                } else {
                    updateEmpQuota(empLeave, waLeaveType, allDaytimeList, userInfo);
                }
            } else {
                long startDate = DateUtil.getOnlyDate(new Date(saveDto.getStartTime() * 1000));
                long endDate = DateUtil.getOnlyDate(new Date(saveDto.getEndTime() * 1000));
                WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
                wfBeginWorkflowDto.setFuncCode(BusinessCodeEnum.LEAVE.getCode());
                wfBeginWorkflowDto.setBusinessId(businessKey);
                wfBeginWorkflowDto.setApplicantId(saveDto.getEmpid().toString());
                wfBeginWorkflowDto.setApplicantName(empInfo.getEmpName());
                wfBeginWorkflowDto.setEventTime(startDate * 1000);
                wfBeginWorkflowDto.setEventEndTime(endDate * 1000);
                wfBeginWorkflowDto.setTimeSlot(getTimeSlot(empLeave.getLeaveId()));
                try {
                    if (saveDto.getEnableWorkflow()) {
                        Result<?> result = wfRegisterFeign.begin(wfBeginWorkflowDto);
                        if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                            if (checkSwitchStatus(SysConfigsEnum.LEAVE_WORKFLOW_SWITCH.name(), userInfo.getTenantId())) {
                                return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null);
                            }
                        }
                        updateEmpQuota(empLeave, waLeaveType, allDaytimeList, userInfo);
                    }
                } catch (Exception e) {
                    log.error("{}", e.getMessage(), e);
                    if (e instanceof CDException) {
                        if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_CONFIG_ERR, null).getMsg());
                        } else {
                            throw e;
                        }
                    } else {
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, null).getMsg());
                    }
                }
            }
        } else {
            if (saveDto.isUpdateQuotaWhenBatch()) {
                updateEmpQuota(empLeave, waLeaveType, allDaytimeList, userInfo);
            }
        }
        LeaveApplyResultDto applyResultDto = new LeaveApplyResultDto();
        applyResultDto.setBusinessKey(wfBusKey);
        applyResultDto.setLeaveId(empLeave.getLeaveId());
        applyResultDto.setTotalTimeDuration(totalTimeDuration.floatValue());
        return Result.ok(applyResultDto);
    }

    public String getLeaveTimeSlot(Map<String, Object> leaveMap) {
        Integer period = (Integer) leaveMap.get("period");
        String startTimeStr = (String) leaveMap.get("starttime");
        String endTimeStr = (String) leaveMap.get("endtime");
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)) {
            return startTimeStr + " -> " + endTimeStr;
        } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
            return startTimeStr + " -> " + endTimeStr;
        } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            return startTimeStr + " -> " + endTimeStr;
        } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
            String shalfday = leaveMap.get("shalfday").toString();
            String ehalfday = leaveMap.get("ehalfday").toString();
            return startTimeStr + BaseConst.LEAVE_HALF_MAPS.get(shalfday) + " -> "
                    + endTimeStr + BaseConst.LEAVE_HALF_MAPS.get(ehalfday);
        }
        return startTimeStr + " -> " + endTimeStr;
    }

    private Result<Boolean> checkLeaveFiles(LeaveApplySaveDto saveDto, WaLeaveType waLeaveType, BigDecimal leaveTimeTotal) {
        if (!waLeaveType.getEnclosureLater()) {
            if (waLeaveType.getIsUploadFile() && StringUtils.isBlank(saveDto.getMyFiles())
                    && StringUtils.isBlank(saveDto.getFile())) {
                if (waLeaveType.getMinFileCheckTime() == null) {
                    return Result.fail(messageResource.getMessage("L005730", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
                } else {
                    if (waLeaveType.getAcctTimeType() == 1) {
                        if (waLeaveType.getMinFileCheckTime() <= leaveTimeTotal.floatValue()) {
                            return Result.fail(messageResource.getMessage("L005730", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
                        }
                    } else if (waLeaveType.getAcctTimeType() == 2) {
                        if (waLeaveType.getMinFileCheckTime() <= leaveTimeTotal.floatValue() / 60) {
                            return Result.fail(messageResource.getMessage("L005730", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
                        }
                    }
                }
            }
        }
        return Result.ok(Boolean.TRUE);
    }

    public WaLeaveDaytime doBuildLeaveDaytime(WaEmpLeave empLeave, WaEmpLeaveTime leaveTime, WaLeaveType waLeaveType,
                                              Map<Long, WaWorktimeDetail> pbMap,
                                              Map<Integer, WaShiftDef> shiftMap,
                                              Map<String, Object> leaveMap,
                                              long leaveDate, Integer useShiftDefId) throws Exception {
        Integer period = (Integer) leaveMap.get("period");
        String startTimeStr = (String) leaveMap.get("starttime");
        String endTimeStr = (String) leaveMap.get("endtime");
        // 查询排班
        WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
        WaShiftDef shiftDef = shiftMap.get(Optional.ofNullable(useShiftDefId).orElse(worktimeDetail.getShiftDefId()));
        // TODO 检查休假时间是否属于当天的班次
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);
        boolean belongToday = checkLeaveTimeIfBelongToday(pbMap, shiftMap, worktimeDetail, period, endDate);
        // 构造每日明细数据对象
        WaLeaveDaytime dayTime = new WaLeaveDaytime();
        dayTime.setUseShiftDefId(shiftDef.getShiftDefId());
        dayTime.setLeaveId(empLeave.getLeaveId());
        dayTime.setLeaveTimeId(leaveTime.getLeaveTimeId());
        dayTime.setLeaveDate(leaveDate);
        dayTime.setPeriodType(period.shortValue());
        dayTime.setCancelTimeDuration(0f);
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period) || PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
            dayTime.setTimeUnit((short) 1);
        }
        if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period) || PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            dayTime.setTimeUnit((short) 2);
        }
        //检查排班是否是系统默认班次，如果是则证明员工未排班, 请假使用系统默认班次时需记录下默认班次ID，其他场景则不记录
        WaShiftDef defaultShift = waCommonService.getDefaultShiftDef(shiftMap);
        if (defaultShift != null && defaultShift.getShiftDefId().equals(shiftDef.getShiftDefId())) {
            dayTime.setShiftDefId(shiftDef.getShiftDefId());
        }
        dayTime.setDateType(worktimeDetail.getDateType());
        // 计算休假时长
        if (belongToday && DateTypeEnum.DATE_TYP_2.getIndex().equals(worktimeDetail.getDateType()) && !waLeaveType.getIsRestDay()) {
            dayTime.setTimeDuration(0f);
        } else if (belongToday && DateTypeEnum.DATE_TYP_3.getIndex().equals(worktimeDetail.getDateType()) && !waLeaveType.getIsLegalHoliday()) {
            dayTime.setTimeDuration(0f);
        } else if (belongToday && DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType()) && !waLeaveType.getIsRestDay()) {
            dayTime.setTimeDuration(0f);
        } else if (belongToday && DateTypeEnum.DATE_TYP_5.getIndex().equals(worktimeDetail.getDateType()) && !waLeaveType.getIsLegalHoliday()) {
            dayTime.setTimeDuration(0f);
        } else {
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)) {
                dayTime.setTimeDuration(1f);
            } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
                mobileV16Service.calLeaveTimeByPeriod3(startTimeStr, endTimeStr, dayTime, waLeaveType, pbMap, shiftMap);
                if (BooleanUtils.isTrue(waLeaveType.getIsAdjustWorkHour())) {
                    Float timeDuration = mobileV16Service.getAdjustWorkTime(dayTime.getStartTime(), dayTime.getEndTime(), shiftDef);
                    if (timeDuration != null) {
                        dayTime.setBeforeAdjustTimeDuration(dayTime.getTimeDuration());
                        dayTime.setTimeDuration(timeDuration);
                    }
                }
            } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
                WaLeaveTypeDef waLeaveTypeDef = waLeaveTypeDefMapper.selectByPrimaryKey(waLeaveType.getLeaveType());
                if ("BRJ".equals(waLeaveTypeDef.getLeaveTypeDefCode())) {
                    processLeaveByPeriod4ForBrj(empLeave.getEmpid(), leaveMap, waLeaveType, shiftDef, dayTime);
                } else {
                    mobileV16Service.processLeaveByPeriod4(waLeaveType, worktimeDetail, shiftDef, dayTime);
                }
            } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
                mobileV16Service.processLeaveByPeriod9(waLeaveType, worktimeDetail, dayTime,
                        leaveTime.getShalfDay(),
                        leaveTime.getEhalfDay(),
                        DateUtil.getTimesampByDateStr2(startTimeStr),
                        DateUtil.getTimesampByDateStr2(endTimeStr));
            }
            //请假时长折算逻辑
            mobileV16Service.convertLtTime(period, dayTime, waLeaveType, shiftDef);
        }
        return dayTime;
    }

    public boolean checkLeaveTimeIfBelongToday(Map<Long, WaWorktimeDetail> pbMap,
                                               Map<Integer, WaShiftDef> shiftMap,
                                               WaWorktimeDetail worktimeDetail,
                                               Integer period,
                                               long endDate) {
        boolean belongToday = true;
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType())
                && PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            // 当天非工作日并且休的是小时假时，需要判断前一天是否为跨夜班，如果前一天是跨夜班，说明当天的休假时间有可能属于前一天班次
            Long preLeaveDate = DateUtil.addDate(endDate * 1000, -1);
            WaWorktimeDetail preLeaveDateWorkTime = pbMap.get(preLeaveDate);
            // TODO 多班次时查找跨夜的那个班次
            WaShiftDef preLeaveDateShift = null != preLeaveDateWorkTime ? shiftMap.get(preLeaveDateWorkTime.getShiftDefId()) : null;
            if (preLeaveDateShift != null
                    && DateTypeEnum.DATE_TYP_1.getIndex().equals(preLeaveDateWorkTime.getDateType())
                    && CdWaShiftUtil.checkCrossNightV2(preLeaveDateShift, preLeaveDateWorkTime.getDateType())) {
                belongToday = false;
            }
        }
        return belongToday;
    }

    @Transactional
    public WaEmpLeaveTime saveLeaveTime(LeaveApplySaveDto saveDto, WaEmpLeave empLeave, Map<String, Object> leaveMap,
                                        Map<Long, WaWorktimeDetail> pbMap, Map<Integer, WaShiftDef> shiftMap,
                                        WaLeaveType waLeaveType, List<WaLeaveDaytime> allDaytimeList) throws Exception {
        Integer period = (Integer) leaveMap.get("period");
        String startTimeStr = (String) leaveMap.get("starttime");
        String endTimeStr = (String) leaveMap.get("endtime");
        long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);

        // 休假时间明细
        WaEmpLeaveTime leaveTime = doContractWaEmpLeaveTime(saveDto, empLeave, leaveMap, pbMap, shiftMap);
        waEmpLeaveTimeMapper.insertSelective(leaveTime);

        // 计算每日明细
        BigDecimal leaveTimeTotal = new BigDecimal(0);
        List<WaLeaveDaytime> daytimeList = new ArrayList<>();
        long leaveDate = startDate;
        while (leaveDate <= endDate) {
            WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
            // 查询当天休假班次
            List<Integer> shiftDefIdList = worktimeDetail.doGetShiftDefIdList();
            if (leaveDate == startDate && CollectionUtils.isNotEmpty(saveDto.getStartShifts())) {
                shiftDefIdList = saveDto.getStartShifts().stream().map(Long::intValue).collect(Collectors.toList());
            } else if (leaveDate == endDate && CollectionUtils.isNotEmpty(saveDto.getEndShifts())) {
                shiftDefIdList = saveDto.getEndShifts().stream().map(Long::intValue).collect(Collectors.toList());
            }
            // 计算每个休假班次的休假时长
            for (Integer shiftDefId : shiftDefIdList) {
                WaLeaveDaytime dayTime = doBuildLeaveDaytime(empLeave, leaveTime, waLeaveType,
                        pbMap, shiftMap, leaveMap, leaveDate, shiftDefId);
                leaveTimeTotal = leaveTimeTotal.add(BigDecimal.valueOf(dayTime.getTimeDuration()));
                daytimeList.add(dayTime);
            }
            leaveDate = leaveDate + 86400;
        }
        allDaytimeList.addAll(daytimeList);
        importService.fastInsertList(WaLeaveDaytime.class, "leaveDaytimeId", daytimeList);

        // 修改休假详情
        WaEmpLeaveTime updateLeaveTime = new WaEmpLeaveTime();
        updateLeaveTime.setTimeDuration(leaveTimeTotal.floatValue());
        updateLeaveTime.setLeaveTimeId(leaveTime.getLeaveTimeId());
        updateLeaveTime.setTimeUnit(waLeaveType.getAcctTimeType());
        if (waLeaveType.getAcctTimeType() == 1
                && PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {//工时折算
            updateLeaveTime.setPeriodType(PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().shortValue());
        }
        waEmpLeaveTimeMapper.updateByPrimaryKeySelective(updateLeaveTime);
        leaveTime.setTimeDuration(updateLeaveTime.getTimeDuration());
        return leaveTime;
    }

    private WaEmpLeaveTime doContractWaEmpLeaveTime(LeaveApplySaveDto saveDto,
                                                    WaEmpLeave empLeave,
                                                    Map<String, Object> leaveMap,
                                                    Map<Long, WaWorktimeDetail> pbMap,
                                                    Map<Integer, WaShiftDef> shiftMap) {
        Integer period = (Integer) leaveMap.get("period");
        String startTimeStr = (String) leaveMap.get("starttime");
        String endTimeStr = (String) leaveMap.get("endtime");
        long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);

        // 查询排班
        WaWorktimeDetail sdetail = pbMap.get(startDate);
        WaWorktimeDetail edetail = pbMap.get(endDate);

        // 构建休假详情
        WaEmpLeaveTime leaveTime = new WaEmpLeaveTime();
        leaveTime.setLeaveId(empLeave.getLeaveId());
        leaveTime.setPeriodType(period.shortValue());
        leaveTime.setCancelTimeDuration(0f);

        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)) {
            leaveTime.setStartTime(startDate);
            leaveTime.setEndTime(endDate);
            if (CollectionUtils.isNotEmpty(saveDto.getStartShifts())) {
                List<Integer> startUseShiftDefIdList = saveDto.getStartShifts()
                        .stream()
                        .map(Long::intValue)
                        .collect(Collectors.toList());
                leaveTime.setStartShift(FastjsonUtil.toJson(startUseShiftDefIdList));
            }
            if (CollectionUtils.isNotEmpty(saveDto.getEndShifts())) {
                List<Integer> endUseShiftDefIdList = saveDto.getEndShifts()
                        .stream()
                        .map(Long::intValue)
                        .collect(Collectors.toList());
                leaveTime.setEndShift(FastjsonUtil.toJson(endUseShiftDefIdList));
            }
        } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
            leaveTime.setStartTime(startDate);
            leaveTime.setEndTime(endDate);
        } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            leaveTime.setStartTime(DateUtil.convertStringToDateTime(startTimeStr, "yyyy-MM-dd HH:mm", true));
            leaveTime.setEndTime(DateUtil.convertStringToDateTime(endTimeStr, "yyyy-MM-dd HH:mm", true));
        }
        leaveTime.setShiftStartTime(leaveTime.getStartTime());
        leaveTime.setShiftEndTime(leaveTime.getEndTime());
        if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
            leaveTime.setStartTime(startDate);
            leaveTime.setEndTime(endDate);
            leaveTime.setShalfDay(leaveMap.get("shalfday").toString());
            leaveTime.setEhalfDay(leaveMap.get("ehalfday").toString());

            // 休假班次
            if (CollectionUtils.isNotEmpty(saveDto.getStartShifts())) {
                List<Integer> startUseShiftDefIdList = saveDto.getStartShifts()
                        .stream().map(Long::intValue).collect(Collectors.toList());
                leaveTime.setStartShift(FastjsonUtil.toJson(startUseShiftDefIdList));
            }
            if (CollectionUtils.isNotEmpty(saveDto.getEndShifts())) {
                List<Integer> endUseShiftDefIdList = saveDto.getEndShifts()
                        .stream().map(Long::intValue).collect(Collectors.toList());
                leaveTime.setEndShift(FastjsonUtil.toJson(endUseShiftDefIdList));
            }

            // 计算休假时间
            // 开始日期
            List<Integer> startShiftDefIdList = CollectionUtils.isNotEmpty(saveDto.getStartShifts())
                    ? saveDto.getStartShifts().stream().map(Long::intValue).collect(Collectors.toList())
                    : sdetail.doGetShiftDefIdList();
            Optional<WaEmpLeaveTime> startDateTime = startShiftDefIdList.stream().map(shiftDefId -> {
                WaShiftDef shiftDef = shiftMap.get(shiftDefId);
                return calLeaveShiftTimeForPeriodTypeNine(startDate, startDate,
                        shiftDef, shiftDef, leaveTime.getShalfDay(), leaveTime.getEhalfDay());
            }).min(Comparator.comparing(WaEmpLeaveTime::getShiftStartTime));
            startDateTime.ifPresent(waEmpLeaveTime -> leaveTime.setShiftStartTime(waEmpLeaveTime.getShiftStartTime()));
            // 结束日期
            List<Integer> endShiftDefIdList = CollectionUtils.isNotEmpty(saveDto.getEndShifts())
                    ? saveDto.getEndShifts().stream().map(Long::intValue).collect(Collectors.toList())
                    : edetail.doGetShiftDefIdList();
            Optional<WaEmpLeaveTime> endDateTime = endShiftDefIdList.stream().map(shiftDefId -> {
                WaShiftDef shiftDef = shiftMap.get(shiftDefId);
                return calLeaveShiftTimeForPeriodTypeNine(endDate, endDate,
                        shiftDef, shiftDef, leaveTime.getShalfDay(), leaveTime.getEhalfDay());
            }).max(Comparator.comparing(WaEmpLeaveTime::getShiftEndTime));
            endDateTime.ifPresent(waEmpLeaveTime -> leaveTime.setShiftEndTime(waEmpLeaveTime.getShiftEndTime()));
        }
        return leaveTime;
    }

    /**
     * 计算实际休假时间
     *
     * @param startDate
     * @param endDate
     * @param startDateShift
     * @param endDateShift
     * @param shalfday
     * @param ehalfday
     * @return
     */
    public WaEmpLeaveTime calLeaveShiftTimeForPeriodTypeNine(long startDate,
                                                             long endDate,
                                                             WaShiftDef startDateShift,
                                                             WaShiftDef endDateShift,
                                                             String shalfday,
                                                             String ehalfday) {
        WaShiftDef startShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(startDateShift);
        WaShiftDef endShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(endDateShift);

        WaEmpLeaveTime leaveTime = new WaEmpLeaveTime();
        // 半天开始
        if ("P".equals(shalfday)) {
            if (startDateShift.getIsHalfdayTime() != null && startDateShift.getIsHalfdayTime()
                    && startDateShift.getHalfdayTime() != null && startDateShift.getHalfdayTime() > 0) {
                leaveTime.setShiftStartTime(startDate + startDateShift.doGetRealHalfdayTime() * 60);
            } else if (startDateShift.getIsNoonRest() != null && startDateShift.getIsNoonRest()) {
                leaveTime.setShiftStartTime(startDate + startDateShift.doGetRealNoonRestEnd() * 60);
            } else {
                leaveTime.setShiftStartTime(startDate + startShiftWorkTime.getStartTime() * 60);
            }
        } else {
            leaveTime.setShiftStartTime(startDate + startShiftWorkTime.getStartTime() * 60);
        }

        // 半天结束
        if ("A".equals(ehalfday)) {
            if (endDateShift.getIsHalfdayTime() != null && endDateShift.getIsHalfdayTime()
                    && endDateShift.getHalfdayTime() != null && endDateShift.getHalfdayTime() > 0) {
                leaveTime.setShiftEndTime(endDate + endDateShift.doGetRealHalfdayTime() * 60);
            } else if (endDateShift.getIsNoonRest() != null && endDateShift.getIsNoonRest()) {
                leaveTime.setShiftEndTime(endDate + endDateShift.doGetRealNoonRestStart() * 60);
            } else {
                leaveTime.setShiftEndTime(endDate + endShiftWorkTime.getEndTime() * 60);
            }
        } else {
            long endTime = endDate;
            if (CdWaShiftUtil.checkCrossNightV2(endDateShift, endDateShift.getDateType())) {
                endTime = DateUtil.addDate(endTime * 1000, 1);
            }
            leaveTime.setShiftEndTime(endTime + endShiftWorkTime.getEndTime() * 60);
        }

        return leaveTime;
    }

    private WaEmpLeave doContractWaEmpLeave(LeaveApplySaveDto saveDto, WaLeaveType waLeaveType) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        WaEmpLeave empLeave = new WaEmpLeave();
        empLeave.setBatchId(saveDto.getBatchId());
        empLeave.setTenantId(userInfo.getTenantId());
        empLeave.setEmpid(saveDto.getEmpid());
        empLeave.setLeaveFormNo("");
        empLeave.setApprovalNum((short) 0);
        empLeave.setLeaveTypeId(saveDto.getLeaveTypeId());
        empLeave.setReason(saveDto.getReason());
        empLeave.setStatus((short) 1);
        empLeave.setProvince(saveDto.getProvince());
        empLeave.setCity(saveDto.getCity());
        empLeave.setCounty(saveDto.getCounty());
        empLeave.setCrtuser(userInfo.getUserId());
        empLeave.setCrttime(System.currentTimeMillis() / 1000);
        empLeave.setLeaveId(saveDto.getLeaveId());
        empLeave.setCancelTimeDuration(0f);
        empLeave.setLeaveStatus(LeaveCancelStatusEnum.NO_LEAVE_CANCEL.getIndex());
        empLeave.setDataSource("MANUAL");
        empLeave.setQuotaDetail(getQuotaDetail(userInfo.getTenantId(), saveDto.getEmpid(), saveDto.getStartTime()));
        if (4 == waLeaveType.getLeaveType()) {
            //产假设置子女个数，产假类型，子女出生日期
            empLeave.setChildNum(saveDto.getChildNum());
            empLeave.setMaternityLeaveType(saveDto.getMaternityLeaveType());
            empLeave.setManufactureDate(saveDto.getManufactureDate());
        }
        if (Integer.valueOf(14).equals(waLeaveType.getLeaveType()) || Integer.valueOf(16).equals(waLeaveType.getLeaveType())) {//产假
            empLeave.setExpectedDate(saveDto.getExpectedDate());
            empLeave.setManufactureDate(saveDto.getManufactureDate());
        }
        if (Integer.valueOf(15).equals(waLeaveType.getLeaveType())) {//系统婚假
            empLeave.setWeddingDate(saveDto.getWeddingDate());
        }
        return empLeave;
    }

    private WaLeaveFile doContractWaLeaveFile(LeaveApplySaveDto saveDto, WaEmpLeave empLeave) {
        WaLeaveFile leaveFile = null;
        if (StringUtils.isNotBlank(saveDto.getMyFiles())) {
            leaveFile = new WaLeaveFile();
            String[] fileArr = saveDto.getMyFiles().split(",");
            leaveFile.setLeaveId(empLeave.getLeaveId());
            leaveFile.setUrl(saveDto.getMyFiles());
            String s = "";
            for (String fileStr : fileArr) {
                if (s.length() > 0) s += ",";
                s += fileStr.substring(fileStr.lastIndexOf("/") + 1);
            }
            leaveFile.setFileName(s);
        } else if (StringUtils.isNotBlank(saveDto.getFile())) {
            leaveFile = new WaLeaveFile();
            leaveFile.setLeaveId(empLeave.getLeaveId());
            leaveFile.setUrl(saveDto.getFile());
            leaveFile.setFileName(saveDto.getFileName());
        }
        return leaveFile;
    }

    private void processLeaveByPeriod4ForBrj(Long empid,
                                             Map<String, Object> leaveTimeMap,
                                             WaLeaveType waLeaveType,
                                             WaShiftDef shiftDef,
                                             WaLeaveDaytime dayTime) {
        // 哺乳假
        boolean brjIfCalTimeDuration = true;
        float sumDurationForNonBbj = 0f;

        // 查询当天申请的非哺乳假
        Map<String, Object> pram = new HashMap<>();
        long leaveDate = dayTime.getLeaveDate();
        pram.put("start", leaveDate);
        pram.put("end", leaveDate + 86399);
        pram.put("empid", empid);
        List<Map> leaveRepeat = waCheckMapper.getLeaveRepeat(pram);
        for (Map leaveRepeatMap : leaveRepeat) {
            Integer periodType = (Integer) leaveRepeatMap.get("period_type");
            Float duration = (Float) leaveRepeatMap.get("time_duration");
            Float cancelTimeDuration = (Float) leaveRepeatMap.get("cancelTimeDuration");
            if (duration.equals(cancelTimeDuration)) {
                continue;
            }
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType)
                    || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                brjIfCalTimeDuration = false;
                break;
            } else {
                if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    if (duration < 1) {
                        Integer workTotalTime = shiftDef.getWorkTotalTime();
                        int halfWorkTime = workTotalTime / 2;
                        String shalfDay = (String) leaveRepeatMap.get("shalf_day");
                        String ehalfDay = (String) leaveRepeatMap.get("ehalf_day");
                        //上半天时长
                        if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(shalfDay) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(ehalfDay)) {
                            halfWorkTime = mobileV16Service.getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                            duration = Float.valueOf(halfWorkTime);
                        }
                        //下半天时长
                        if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(shalfDay) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(ehalfDay)) {
                            halfWorkTime = mobileV16Service.getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                            duration = Float.valueOf(halfWorkTime);
                        }
                    } else {
                        brjIfCalTimeDuration = false;
                        break;
                    }
                }
                sumDurationForNonBbj = sumDurationForNonBbj + duration;
            }
        }

        // 计算班次工作时长
        Float workTotalTime = getBrjWorkTotalTime(waLeaveType, shiftDef);
        if (sumDurationForNonBbj >= workTotalTime) {
            brjIfCalTimeDuration = false;
        }

        // 如是休息日班次或者整天申请其他假已审批通过（请假时长=班次时长），那么这天请假时长=0 CAIDAOM-528
        if (brjIfCalTimeDuration) {
            float dailyDuration = leaveTimeMap.get("dailyDuration") == null ? 0f : (Float) leaveTimeMap.get("dailyDuration");
            dailyDuration = dailyDuration * 60;
            if (dailyDuration > workTotalTime) {
                dayTime.setTimeDuration(workTotalTime);
            } else {
                dayTime.setTimeDuration(dailyDuration);
            }
        } else {
            dayTime.setTimeDuration(0f);
        }
    }

    private Float getBrjWorkTotalTime(WaLeaveType waLeaveType, WaShiftDef shiftDef) {
        // 特殊班次工时调整
        boolean isSpecial = Optional.ofNullable(shiftDef.getIsSpecial()).orElse(false)
                && shiftDef.getSpecialWorkTime() != null;
        Float workTotalTime = Float.valueOf(shiftDef.getWorkTotalTime());
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType())) {
            if (isSpecial) {
                workTotalTime = shiftDef.getSpecialWorkTime().floatValue();
            }
        } else if (DateTypeEnum.DATE_TYP_2.getIndex().equals(shiftDef.getDateType()) && waLeaveType.getIsRestDay()) {
            if (isSpecial) {
                workTotalTime = shiftDef.getSpecialWorkTime().floatValue();
            } else {
                workTotalTime = 8 * 60f;
            }
        } else if ((DateTypeEnum.DATE_TYP_3.getIndex().equals(shiftDef.getDateType())
                || DateTypeEnum.DATE_TYP_5.getIndex().equals(shiftDef.getDateType())) && waLeaveType.getIsLegalHoliday()) {
            if (isSpecial) {
                workTotalTime = shiftDef.getSpecialWorkTime().floatValue();
            } else {
                workTotalTime = 8 * 60f;
            }
        } else if (DateTypeEnum.DATE_TYP_4.getIndex().equals(shiftDef.getDateType()) && waLeaveType.getIsRestDay()) {
            if (isSpecial) {
                workTotalTime = shiftDef.getSpecialWorkTime().floatValue();
            } else {
                workTotalTime = 8 * 60f;
            }
        }
        return workTotalTime;
    }

    private String getQuotaDetail(String tenantId, Long empId, Long eventDate) {
        List<LeaveQuotaDetailDto> list = Lists.newArrayList();
        List<LeaveQuotaDto> leaveQuotaList = myCenterService.getLeaveQuotaList(tenantId, empId, eventDate);
        leaveQuotaList = leaveQuotaList.stream().filter(LeaveQuotaDto::getDisplayQuotaDetail).collect(Collectors.toList());
        leaveQuotaList.forEach(quota -> {
            list.add(new LeaveQuotaDetailDto(quota.getLeaveTypeId(), quota.getLeftDay()));
        });
        return JSON.toJSONString(list);
    }

    public String getTimeSlot(Integer leaveId) {
        WaEmpLeaveTimeExample example = new WaEmpLeaveTimeExample();
        WaEmpLeaveTimeExample.Criteria criteria = example.createCriteria();
        criteria.andLeaveIdEqualTo(leaveId);
        List<WaEmpLeaveTime> list = waEmpLeaveTimeMapper.selectByExample(example);
        WaEmpLeaveTime empLeaveTime = list.get(0);
        Long shiftStartTime = empLeaveTime.getShiftStartTime();
        Long shiftEndTime = empLeaveTime.getShiftEndTime();
        Integer periodType = empLeaveTime.getPeriodType().intValue();
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
            return String.format("%s~%s", DateUtil.getDateStrByTimesamp(shiftStartTime), DateUtil.getDateStrByTimesamp(shiftEndTime));
        } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
            Long start = empLeaveTime.getStartTime();
            Long end = empLeaveTime.getEndTime();
            return String.format("%s %s~%s %s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(empLeaveTime.getShalfDay()), DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(empLeaveTime.getEhalfDay()));
        } else {
            return String.format("%s~%s", DateUtil.getTimeStrByTimesamp(shiftStartTime), DateUtil.getTimeStrByTimesamp(shiftEndTime));
        }
    }

    public void updateEmpQuota(WaEmpLeave empLeave, WaLeaveType waLeaveType, List<WaLeaveDaytime> allDaytimeList, UserInfo userInfo) {
        if (waLeaveType.getLeaveType() == 4) {
            return;
        }
        //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
        Integer quotaType = waLeaveType.getQuotaType();
        if (quotaType == null) {
            quotaType = waLeaveType.getLeaveType() == 3 ? 2 : 1;
        }
        List<Integer> leaveDaytimeIdList = allDaytimeList.stream().map(WaLeaveDaytime::getLeaveDaytimeId).collect(Collectors.toList());
        if (quotaType == 2) {
            List<Long> ids = leaveDaytimeIdList.stream().map(Long::valueOf).collect(Collectors.toList());
            waEmpCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(userInfo.getUserId(), System.currentTimeMillis(), ids, Integer.valueOf(empLeave.getStatus()));
        } else {
            waLeaveQuotaUseMapper.updateWaEmpQuota(userInfo.getUserId(), System.currentTimeMillis(), leaveDaytimeIdList, Integer.valueOf(empLeave.getStatus()));
        }
    }

    @Transactional
    public void deleteLeave(Integer leaveId) {
        if (leaveId != null) {
            WaLeaveDaytimeExample daytimeExample = new WaLeaveDaytimeExample();
            WaLeaveDaytimeExample.Criteria daytimeCriteria = daytimeExample.createCriteria();
            daytimeCriteria.andLeaveIdEqualTo(leaveId);
            waLeaveDaytimeMapper.deleteByExample(daytimeExample);

            WaEmpLeaveTimeExample leaveTimeExample = new WaEmpLeaveTimeExample();
            WaEmpLeaveTimeExample.Criteria leaveTimeCriteria = leaveTimeExample.createCriteria();
            leaveTimeCriteria.andLeaveIdEqualTo(leaveId);
            waEmpLeaveTimeMapper.deleteByExample(leaveTimeExample);

            WaLeaveFileExample fileExample = new WaLeaveFileExample();
            WaLeaveFileExample.Criteria fileCriteria = fileExample.createCriteria();
            fileCriteria.andLeaveIdEqualTo(leaveId);
            waLeaveFileMapper.deleteByExample(fileExample);
            waEmpLeaveMapper.deleteByPrimaryKey(leaveId);
        }
    }

    @Transactional
    @Override
    public void deleteByBatchId(String tenantId, Long batchId) {
        WaEmpLeaveExample empLeaveExample = new WaEmpLeaveExample();
        empLeaveExample.createCriteria().andBatchIdEqualTo(batchId).andTenantIdEqualTo(tenantId);
        List<WaEmpLeave> waEmpLeaveList = waEmpLeaveMapper.selectByExample(empLeaveExample);
        if (CollectionUtils.isEmpty(waEmpLeaveList)) {
            return;
        }
        List<Integer> leaveIdList = waEmpLeaveList.stream().map(WaEmpLeave::getLeaveId).distinct().collect(Collectors.toList());
        WaLeaveDaytimeExample daytimeExample = new WaLeaveDaytimeExample();
        WaLeaveDaytimeExample.Criteria daytimeCriteria = daytimeExample.createCriteria();
        daytimeCriteria.andLeaveIdIn(leaveIdList);
        waLeaveDaytimeMapper.deleteByExample(daytimeExample);

        WaEmpLeaveTimeExample leaveTimeExample = new WaEmpLeaveTimeExample();
        WaEmpLeaveTimeExample.Criteria leaveTimeCriteria = leaveTimeExample.createCriteria();
        leaveTimeCriteria.andLeaveIdIn(leaveIdList);
        waEmpLeaveTimeMapper.deleteByExample(leaveTimeExample);

        WaLeaveFileExample fileExample = new WaLeaveFileExample();
        WaLeaveFileExample.Criteria fileCriteria = fileExample.createCriteria();
        fileCriteria.andLeaveIdIn(leaveIdList);
        waLeaveFileMapper.deleteByExample(fileExample);

        waEmpLeaveMapper.deleteByExample(empLeaveExample);
    }

    @Override
    public boolean checkSwitchStatus(String configCode, String tenantId) {
        SysConfigExample sysConfigExample = new SysConfigExample();
        SysConfigExample.Criteria criteria = sysConfigExample.createCriteria();
        criteria.andConfigCodeEqualTo(configCode).andBelongOrgIdEqualTo(tenantId);
        List<SysConfig> list = sysConfigMapper.selectByExample(sysConfigExample);
        Optional<SysConfig> opt = list.stream().findFirst();
        SysConfig dto = opt.orElse(null);
        return dto != null && dto.getStatus() == 1;
    }


    /**
     * 假期规则校验
     *
     * @param waLeaveType
     * @param empLeave
     * @param leaveStartDate
     * @return
     * @throws Exception
     */
    public String validateLeaveSetRule(WaLeaveType waLeaveType, WaEmpLeave empLeave, String leaveStartDate) throws Exception {
        if (waLeaveType.getIsCheckMonthTime() != null && waLeaveType.getIsCheckMonthTime()) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            //取得本月该假期请假总额
            float totalMonth = getLeaveTotal(empLeave.getEmpid(), waLeaveType.getLeaveTypeId(), sf.parse(leaveStartDate).getTime(), 1);
            if (waLeaveType.getAcctTimeType() == 1) {
                if (totalMonth > waLeaveType.getMaxMonthTime()) {
                    deleteLeave(empLeave.getLeaveId());
                    return messageResource.getMessage("L005731", new Object[]{totalMonth, waLeaveType.getMaxMonthTime()}, new Locale(SessionBeanUtil.getLanguage()));
                }
            } else if (waLeaveType.getAcctTimeType() == 2) {
                BigDecimal totalMonthDecimal = (new BigDecimal(totalMonth)).divide(new BigDecimal(60), 1, BigDecimal.ROUND_HALF_UP);
                totalMonthDecimal = totalMonthDecimal.setScale(1, BigDecimal.ROUND_HALF_UP);
                if (totalMonthDecimal.floatValue() > waLeaveType.getMaxMonthTime()) {
                    deleteLeave(empLeave.getLeaveId());
//                    return "您已超过当月最大请假数量。当月请假数量为：" + totalMonthDecimal.floatValue() + "小时，当月限额为：" + waLeaveType.getMaxMonthTime() + "小时";
                    return messageResource.getMessage("L005732", new Object[]{totalMonthDecimal.floatValue(), waLeaveType.getMaxMonthTime()}, new Locale(SessionBeanUtil.getLanguage()));
                }
            }
        }
        if (waLeaveType.getIsCheckQuarterTime() != null && waLeaveType.getIsCheckQuarterTime()) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            //取得本月该假期请假总额
            float totalMonth = this.getLeaveTotal(empLeave.getEmpid(), waLeaveType.getLeaveTypeId(), sf.parse(leaveStartDate).getTime(), 2);
            if (waLeaveType.getAcctTimeType() == 1) {
                if (totalMonth > waLeaveType.getMaxQuarterTime()) {
                    deleteLeave(empLeave.getLeaveId());
                    return messageResource.getMessage("L005733", new Object[]{totalMonth, waLeaveType.getMaxQuarterTime()}, new Locale(SessionBeanUtil.getLanguage()));
                }
            } else if (waLeaveType.getAcctTimeType() == 2) {//
                BigDecimal totalMonthDecimal = (new BigDecimal(totalMonth)).divide(new BigDecimal(60), 1, BigDecimal.ROUND_HALF_UP);
                totalMonthDecimal = totalMonthDecimal.setScale(1, BigDecimal.ROUND_HALF_UP);
                if (totalMonthDecimal.floatValue() > waLeaveType.getMaxQuarterTime()) {
                    deleteLeave(empLeave.getLeaveId());
//                    return "您已超过当季度最大请假数量。当季度请假数量为：" + totalMonthDecimal.floatValue() + "小时，当季度限额为：" + waLeaveType.getMaxQuarterTime() + "小时";
                    return messageResource.getMessage("L005734", new Object[]{totalMonthDecimal.floatValue(), waLeaveType.getMaxQuarterTime()}, new Locale(SessionBeanUtil.getLanguage()));
                }
            }
        }
        if (waLeaveType.getIsCheckYearTime() != null && waLeaveType.getIsCheckYearTime()) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            //取得本月该假期请假总额
            float totalMonth = getLeaveTotal(empLeave.getEmpid(), waLeaveType.getLeaveTypeId(), sf.parse(leaveStartDate).getTime(), 3);
            if (waLeaveType.getAcctTimeType() == 1) {
                if (totalMonth > waLeaveType.getMaxYearTime()) {
                    deleteLeave(empLeave.getLeaveId());
                    return messageResource.getMessage("L005735", new Object[]{totalMonth, waLeaveType.getMaxYearTime()}, new Locale(SessionBeanUtil.getLanguage()));
                }
            } else if (waLeaveType.getAcctTimeType() == 2) {//
                BigDecimal totalMonthDecimal = (new BigDecimal(totalMonth)).divide(new BigDecimal(60), 1, RoundingMode.HALF_UP);
                totalMonthDecimal = totalMonthDecimal.setScale(1, RoundingMode.HALF_UP);
                if (totalMonthDecimal.floatValue() > waLeaveType.getMaxYearTime()) {
                    deleteLeave(empLeave.getLeaveId());
                    return messageResource.getMessage("L005736", new Object[]{totalMonthDecimal.floatValue(), waLeaveType.getMaxYearTime()}, new Locale(SessionBeanUtil.getLanguage()));
                }
            }
        }
        //每月请假次数校验
        if (waLeaveType.getIsCheckMonthNum() != null && waLeaveType.getIsCheckMonthNum()) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            //取得本月该假期请假次数
            Integer totalMonthNum = this.getLeaveNum(empLeave.getEmpid(), waLeaveType.getLeaveTypeId(), sf.parse(leaveStartDate).getTime(), 1);
            if (totalMonthNum > waLeaveType.getMaxMonthNum()) {
                deleteLeave(empLeave.getLeaveId());
                //超过本月最大请假次数
                return messageResource.getMessage("L005818", new Object[]{totalMonthNum, waLeaveType.getMaxMonthNum()}, new Locale(SessionBeanUtil.getLanguage()));
            }
        }
        //每季请假次数校验
        if (waLeaveType.getIsCheckQuarterNum() != null && waLeaveType.getIsCheckQuarterNum()) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            //取得本月该假期请假总额
            Integer totalMonthNum = this.getLeaveNum(empLeave.getEmpid(), waLeaveType.getLeaveTypeId(), sf.parse(leaveStartDate).getTime(), 2);
            if (totalMonthNum > waLeaveType.getMaxQuarterNum()) {
                deleteLeave(empLeave.getLeaveId());
                return messageResource.getMessage("L005819", new Object[]{totalMonthNum, waLeaveType.getMaxQuarterNum()}, new Locale(SessionBeanUtil.getLanguage()));
            }
        }
        //每年请假次数校验
        if (waLeaveType.getIsCheckYearNum() != null && waLeaveType.getIsCheckYearNum()) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            //取得本月该假期请假总额
            Integer totalYearNum = this.getLeaveNum(empLeave.getEmpid(), waLeaveType.getLeaveTypeId(), sf.parse(leaveStartDate).getTime(), 3);
            if (totalYearNum > waLeaveType.getMaxYearNum()) {
                deleteLeave(empLeave.getLeaveId());
                return messageResource.getMessage("L005820", new Object[]{totalYearNum, waLeaveType.getMaxYearNum()}, new Locale(SessionBeanUtil.getLanguage()));
            }
        }
        return "";
    }

    /**
     * 获取员工请假时间
     *
     * @param empId       员工ID
     * @param leaveTypeId
     * @param startDate
     * @param type        1为月  2为季  3为年
     * @return
     * @throws Exception
     */
    public float getLeaveTotal(Long empId, Integer leaveTypeId, Long startDate, Integer type) throws Exception {
        Map timeMap = caldateTimeByDate(startDate, type);
        Map map = new HashMap();
        map.put("empId", empId);
        map.put("leaveTypeId", leaveTypeId);
        map.put("firstDate", timeMap.get("firstDate"));
        map.put("lastDate", timeMap.get("lastDate"));
        map.put("array", new Integer[]{1, 2});
        Float total = waEmpLeaveMapper.getLeaveTypeTotal(map);
        if (total == null) {
            return 0f;
        } else {
            return total;
        }
    }

    /**
     * 获取员工请假次数
     *
     * @param empId       员工ID
     * @param leaveTypeId
     * @param startDate
     * @param type        1为月  2为季  3为年
     * @return
     * @throws Exception
     */
    public Integer getLeaveNum(Long empId, Integer leaveTypeId, Long startDate, Integer type) throws Exception {
        //计算时间
        Map timeMap = caldateTimeByDate(startDate, type);
        Map map = new HashMap();
        map.put("empId", empId);
        map.put("leaveTypeId", leaveTypeId);
        map.put("firstDate", timeMap.get("firstDate"));
        map.put("lastDate", timeMap.get("lastDate"));
        map.put("array", new Integer[]{1, 2});
        return waEmpLeaveMapper.getEmpLeaveCount(map);
    }

    /**
     * 计算时间
     *
     * @param startDate
     * @param type
     * @return
     * @throws ParseException
     */
    public Map caldateTimeByDate(Long startDate, Integer type) throws ParseException {
        Map map = new HashMap();

        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        Date d = new Date(startDate);
        Calendar ca = Calendar.getInstance();

        long firstDate = 0l;
        long lastDate = 0l;

        if (type == 1) {
            ca.setTime(d);
            ca.add(Calendar.MONTH, 0);
            ca.set(Calendar.DAY_OF_MONTH, 1);
            firstDate = ca.getTime().getTime() / 1000;
            lastDate = DateUtil.getLastDayOfMonth(d).getTime() / 1000;
        } else if (type == 2) {
            ca.setTime(d);
            Integer nowMonth = ca.get(Calendar.MONTH) + 1;
            Integer nowQuarter = nowMonth / 3;
            String nowYear = ca.get(Calendar.YEAR) + "";
            firstDate = sf.parse(nowYear + ((nowQuarter * 3 + 1) < 10 ? "0" + (nowQuarter * 3 + 1) : (nowQuarter * 3 + 1)) + "01").getTime() / 1000;
            lastDate = DateUtil.getLastDayOfMonth(sf.parse(nowYear + ((nowQuarter * 3 + 3) < 10 ? "0" + (nowQuarter * 3 + 3) : (nowQuarter * 3 + 3)) + "01")).getTime() / 1000;
        } else if (type == 3) {
            ca.setTime(d);
            String nowYear = ca.get(Calendar.YEAR) + "";
            firstDate = sf.parse(nowYear + "0101").getTime() / 1000;
            lastDate = sf.parse(nowYear + "1231").getTime() / 1000;
        }

        map.put("firstDate", firstDate);
        map.put("lastDate", lastDate);
        return map;
    }

    @Override
    public List getLeaveApplyList(LeaveApplyDto dto, PageBean pageBean, UserInfo userInfo) {
        Integer[] filterStatus = dto.getFilterStatus() != null ? dto.getFilterStatus() :
                dto.getStatus() != null ? new Integer[]{dto.getStatus()} : null;
        return this.getLeaveList(userInfo.getTenantId(), pageBean, filterStatus, dto.getStartDate(), dto.getStartTime(), dto.getEndDate(), dto.getEndTime(), dto.getDataScope());
    }

    public List getLeaveApplyList(LeaveApplyDto dto, PageBean pageBean, UserInfo userInfo, boolean filterCancel) {
        Integer[] filterStatus = dto.getFilterStatus() != null ? dto.getFilterStatus() :
                dto.getStatus() != null ? new Integer[]{dto.getStatus()} : null;
        return this.getLeaveList(userInfo.getTenantId(), pageBean, filterStatus, dto.getStartDate(), dto.getStartTime(), dto.getEndDate(), dto.getEndTime(),
                dto.getDataScope(), true);
    }

    @Override
    public AttendancePageResult<UserLeaveApplyListVo> getUserLeaveApplyListOfPortal(QueryPageBean queryPageBean) {
        UserInfo userInfo = sessionService.getUserInfo();
        Object createTimeValue = null;
        Object leaveTypeIds = null;
        Object status = null;
        Object leaveStatus = null;
        if (CollectionUtils.isNotEmpty(queryPageBean.getFilterList())) {
            Iterator<com.caidaocloud.dto.FilterBean> iterator = queryPageBean.getFilterList().iterator();
            while (iterator.hasNext()) {
                com.caidaocloud.dto.FilterBean next = iterator.next();
                if ("crttime".equals(next.getProp())) {
                    createTimeValue = next.getValue();
                    iterator.remove();
                }
                if ("wlt.leave_type_id".equals(next.getProp())) {
                    leaveTypeIds = next.getValue();
                    iterator.remove();
                }
                if ("wel.status".equals(next.getProp())) {
                    status = next.getValue();
                    iterator.remove();
                }
                if ("wel.leave_status".equals(next.getProp())) {
                    leaveStatus = next.getValue();
                    iterator.remove();
                }
            }
        }
        QueryWrapper<UserLeaveApplyListPo> queryWrapper = QueryAdapterUtil.createQueryWrapper(queryPageBean);
        if (createTimeValue != null) {
            Long createTime = Long.valueOf(createTimeValue.toString());
            Calendar instance = Calendar.getInstance();
            instance.setTimeInMillis(createTime);
            instance.set(Calendar.HOUR_OF_DAY, 0);
            instance.set(Calendar.MINUTE, 0);
            instance.set(Calendar.SECOND, 0);
            instance.set(Calendar.MILLISECOND, 0);
            long startTime = instance.getTimeInMillis() / 1000;
            instance.set(Calendar.HOUR_OF_DAY, 23);
            instance.set(Calendar.MINUTE, 59);
            instance.set(Calendar.SECOND, 59);
            instance.set(Calendar.MILLISECOND, 0);
            long endTime = instance.getTimeInMillis() / 1000;
            queryWrapper.between("wel.crttime", startTime, endTime);
        }
        if (leaveTypeIds != null && StringUtils.isNotBlank(leaveTypeIds.toString())) {
            queryWrapper.in("wlt.leave_type_id", Splitter.on(",").splitToList(String.valueOf(leaveTypeIds))
                    .stream().map(Integer::valueOf).collect(Collectors.toList()));
        }
        if (status != null && StringUtils.isNotBlank(status.toString())) {
            queryWrapper.in("wel.status", Splitter.on(",").splitToList(String.valueOf(status))
                    .stream().map(Integer::valueOf).collect(Collectors.toList()));
        }
        if (leaveStatus != null && StringUtils.isNotBlank(leaveStatus.toString())) {
            queryWrapper.in("wel.leave_status", Splitter.on(",").splitToList(String.valueOf(leaveStatus))
                    .stream().map(Integer::valueOf).collect(Collectors.toList()));
        }
        Page page = new Page<UserLeaveApplyListPo>(queryPageBean.getPageNo(), queryPageBean.getPageSize());
        Long count = leaveRecordMapper.getCountLeaveList(page, userInfo.getStaffId(), queryWrapper);
        List<Map> pageList = leaveRecordMapper.getLeaveList(page, userInfo.getStaffId(), queryWrapper);
        List<UserLeaveApplyListVo> resultList = convertUserLeaveApplyListVo(pageList, userInfo, true);
        if (!CollectionUtils.isEmpty(resultList)) {
            for (UserLeaveApplyListVo userLeaveApplyListVo : resultList) {

            }
        }
        return new AttendancePageResult(resultList, queryPageBean.getPageNo(), queryPageBean.getPageSize(),
                count != null ? count.intValue() : 0);
    }

    @Override
    public AttendancePageResult<UserLeaveApplyListVo> getUserLeaveApplyList(UserLeaveApplySearchDto searchDto) {
        UserInfo userInfo = sessionService.getUserInfo();
        val pageBean = PageUtil.getPageBean(searchDto);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        val userLeaveList = leaveRecordMapper.getUserLeaveList(pageBounds, userInfo.getTenantId(),
                userInfo.getStaffId(), searchDto.getStatus(), null, searchDto.getLeaveName(), searchDto.getApplyStartTime(), searchDto.getApplyEndTime());
        List<UserLeaveApplyListVo> resultList = convertUserLeaveApplyListVo(userLeaveList, userInfo);
        return new AttendancePageResult<>(resultList, searchDto.getPageNo(), searchDto.getPageSize(), userLeaveList.getPaginator().getTotalCount());
    }

    private List<UserLeaveApplyListVo> convertUserLeaveApplyListVo(List<Map> list, UserInfo userInfo, boolean... isFlag) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<UserLeaveApplyListVo> resultList = list.stream().map(leave -> {
            val leaveVo = FastjsonUtil.toObject(FastjsonUtil.toJson(leave), UserLeaveApplyListVo.class);
            BeanUtils.copyProperties(leave, leaveVo);
            if (null != leave.get("i18nLeaveName")) {
                leaveVo.setLeaveName(LangParseUtil.getI18nLanguage(leave.get("i18nLeaveName").toString(), leaveVo.getLeaveName()));
            }
            leaveVo.setFuncType(BaseConst.WF_FUNC_TYPE_1);
            Integer timeUnit = (Integer) leave.get("time_unit");
            Float duration = (Float) leave.get("total_time_duration");
            Float cancelTimeDuration = (Float) leave.get("cancelTimeDuration");
            Float actualTimeDuration = (Float) leave.get("actualTimeDuration");
            if (timeUnit == null) {
                timeUnit = 1;
            }
            leaveVo.setTimeUnitName(PreTimeUnitEnum.getName(timeUnit));
            if (timeUnit == 1) {
                leaveVo.setDuration(duration);
                leaveVo.setCancelTimeDuration(cancelTimeDuration);
                leaveVo.setActualTimeDuration(actualTimeDuration);
            } else {
                BigDecimal v = new BigDecimal(duration).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                BigDecimal a = new BigDecimal(cancelTimeDuration).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                BigDecimal b = new BigDecimal(actualTimeDuration).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                leaveVo.setDuration(v.floatValue());
                leaveVo.setCancelTimeDuration(a.floatValue());
                leaveVo.setActualTimeDuration(b.floatValue());
            }
            if (ArrayUtils.isNotEmpty(isFlag) && isFlag[0]) {
                Integer periodType = (Integer) leave.get("period_type");
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType)
                        || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    String startTime = !Objects.isNull(leave.get("startTime")) ?
                            DateUtil.getDateStrByTimesamp(Long.valueOf(String.valueOf(leave.get("startTime")))) : "";
                    String endTime = !Objects.isNull(leave.get("endTime")) ?
                            DateUtil.getDateStrByTimesamp(Long.valueOf(String.valueOf(leave.get("endTime")))) : "";
                    leaveVo.setTimeSlot(String.format("%s-> %s", startTime, endTime));
                } else if (periodType == 9) {
                    String startTime = !Objects.isNull(leave.get("startTime")) ?
                            DateUtil.getDateStrByTimesamp(Long.valueOf(String.valueOf(leave.get("startTime")))) : "";
                    String endTime = !Objects.isNull(leave.get("endTime")) ?
                            DateUtil.getDateStrByTimesamp(Long.valueOf(String.valueOf(leave.get("endTime")))) : "";

                    String shalfDay = (String) leave.get("shalf_day");
                    String ehalfDay = (String) leave.get("ehalf_day");

                    startTime = String.format("%s%s", startTime, DayHalfTypeEnum.getDesc(shalfDay));
                    endTime = String.format("%s%s", endTime, DayHalfTypeEnum.getDesc(ehalfDay));

                    leaveVo.setTimeSlot(String.format("%s-> %s", startTime, endTime));
                } else {
                    String startTime = !Objects.isNull(leave.get("startTime")) ?
                            DateUtil.getTimeStrByTimesamp4(Long.valueOf(String.valueOf(leave.get("startTime")))) : "";
                    String endTime = !Objects.isNull(leave.get("endTime")) ?
                            DateUtil.getTimeStrByTimesamp4(Long.valueOf(String.valueOf(leave.get("endTime")))) : "";
                    leaveVo.setTimeSlot(String.format("%s-> %s", startTime, endTime));
                }
            } else {
                String timeSlot = (String) leave.get("timeSlot");
                if (StringUtils.isNotBlank(timeSlot)) {
                    leaveVo.setTimeSlot(timeSlot.replace("->", "~").replaceAll("A", DayHalfTypeEnum.getDesc("A")).replaceAll("P", DayHalfTypeEnum.getDesc("P")).replaceAll("/", "-"));
                }
            }
            List<WaEmpLeaveCancelDo> leaveCancelList = leaveCancelDomainService.getListByLeaveId(userInfo.getTenantId(), leaveVo.getLeaveId());
            Optional<Long> opt = leaveCancelList.stream().map(WaEmpLeaveCancelDo::getLastApprovalTime).filter(Objects::nonNull).max(Long::compare);
            if (opt.isPresent()) {
                leaveVo.setLeaveCancelTime(DateUtil.getTimeStrByTimesamp(opt.get()));
            } else {
                leaveVo.setLeaveCancelTime("-");
            }
            //是否允许多次销假
            boolean flag = true;
            //只要有审批中或审批中的销假单就算一次销假
            boolean isLeaveCancel = leaveCancelList.stream().anyMatch(e -> ApprovalStatusEnum.PASSED.getIndex().equals(Integer.valueOf(e.getStatus()))
                    || ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(Integer.valueOf(e.getStatus())));
            Boolean allowedMultipleCancel = (Boolean) leave.get("allowedMultipleCancel");
            if (!allowedMultipleCancel) {
                flag = !isLeaveCancel;
            }
            //如果是审批中的休假单，没有选择附件补充,则不允许销假
            leaveVo.setAllowManyLeaveCancel(flag);
            return leaveVo;
        }).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public AttendancePageResult getEmpList(EmpListDto dto) {
        UserInfo userInfo = getUserInfo();
        Long corpId = UserContext.getCorpId();
        if (dto.getBelongid() == null) {
            dto.setBelongid(userInfo.getTenantId());
        }
        Map<String, Object> map = new HashMap<>();
        if (dto.getCorpEmail() != null) {
            map.put("empName", dto.getCorpEmail().toUpperCase() + "%");
        }
        if (dto.getEmpName() != null) {
            map.put("empName", dto.getEmpName().toUpperCase() + "%");
        }
        // 只要有试用期日期的员工信息都显示出来
        map.put("workType", dto.getWorkType());
        if (dto.getTmType() != null) {
            map.put("tmType", dto.getTmType());
        }
        if (dto.getWorknoAndempname() != null) {
            map.put("worknoAndempname", dto.getWorknoAndempname() + '%');
        }
        // 默认只返回在职员工
        if (dto.getZzStatus() == null) {
            map.put("status", 0);
        } else {
            if (dto.getZzStatus() == 1) { // 等于1只查在职员工
                map.put("status", 0);
            } else if (dto.getZzStatus() == 2) { // 等于2只查离职原因
                map.put("status", 1);
            }
        }
        map.put("orgid", dto.getOrgid());

        map.put("datafilter", dto.getDataScope());

        PageBean pageBean = PageUtil.getPageBean(dto);
        List list = this.getEmployeeInfoList(pageBean, corpId, dto.getBelongid(), dto.getOrgid(), map, dto.getBelongorgid(), dto.getResId(), dto.getType());
        PageList pageList = (PageList) list;
        return new AttendancePageResult(pageList, pageBean.getPage(), pageBean.getCount());
    }

    private List getEmployeeInfoList(PageBean pageBean, Long corpId, String belongId, Long orgid,
                                     Map<String, Object> map, String belongorgid, Integer resId, String type) {
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(pageBean.getOrder()));
        if (type != null) {
            map.put("belongId", SessionHolder.getBelongOrgId());
            return empInfoMapper.getEmployeeInfoList(pageBounds, belongId, map);
        } else {
            if (orgid != null) {
                SysCorpOrg org = sysCorpOrgMapper.selectByPrimaryKey(orgid);
                corpId = org.getCorpid();
                belongId = org.getBelongOrgId();
            }
            String datafilter = (String) map.get("datafilter");

            if (StringUtils.isEmpty(belongorgid)) {
                if (StringUtil.isEmptyOrNull(datafilter)) {
                    map.put("datafilter", "AND emp.belong_org_id=" + belongId);
                }
            } else {
                if (StringUtil.isEmptyOrNull(datafilter)) {
                    map.put("datafilter", "AND emp.belong_org_id=" + belongorgid);
                }
            }
            return sysEmpInfoMapper.getEmployeeInfoList(pageBounds, corpId, belongId, map);
        }
    }

    public List getLeaveList(String belongId, PageBean pageBean, Integer[] filterStatus, Long startDate, Long startTime,
                             Long endDate, Long endTime, String dataScope) {
        return getLeaveList(belongId, pageBean, filterStatus, startDate, startTime, endDate, endTime, dataScope, false);
    }

    public List getLeaveList(String belongId, PageBean pageBean, Integer[] filterStatus, Long startDate, Long startTime,
                             Long endDate, Long endTime, String dataScope, boolean filterCancel) {
        if (pageBean != null && pageBean.getFilterList() != null) {
            pageBean = changeToday(pageBean);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("filterStatus", filterStatus);
        map.put("belongOrgId", belongId);
        map.put("filterCancel", filterCancel);
        if (startDate != null) {
            if (startTime != null) {
                startTime = startTime * 60;
            }
            if (endTime != null) {
                endTime = endTime * 60;
            }
            map.put("startDateTime", startDate + ((startTime == null) ? 0 : startTime));
            map.put("endDateTime", endDate + (endTime == null ? 0 : endTime));
        }
        changeParam(pageBean, map);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        map.put("filter", filter);
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            map.put("keywords", pageBean.getKeywords());
        }
        map.put("datafilter", dataScope);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "shift_start_time.desc,waid.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        PageList<Map> list = leaveRecordMapper.getLeaveRecordList(pageBounds, map);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Integer> leaveIds = list.stream().map(m -> (Integer) m.get("waid")).distinct().collect(Collectors.toList());
            List<WaEmpLeaveCancelDo> allLeaveCancelList = leaveCancelDomainService.getListByLeaveIds(belongId, leaveIds);
            Map<Integer, List<WaEmpLeaveCancelDo>> leaveCancelMap = allLeaveCancelList.stream().collect(Collectors.groupingBy(WaEmpLeaveCancelDo::getLeaveId));
            list.forEach(item -> {
                Integer timeUint = (Integer) item.get("time_unit");
                Float duration = (Float) item.get("duration");
                Float cancelTimeDuration = (Float) item.get("cancelTimeDuration");
                Float actualTimeDuration = (Float) item.get("actualTimeDuration");
                if (timeUint == null) {
                    timeUint = 1;
                }
                item.put("timeUnitName", PreTimeUnitEnum.getName(timeUint));
                if (timeUint == 2) {
                    BigDecimal v = new BigDecimal(String.valueOf(duration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    BigDecimal a = new BigDecimal(String.valueOf(cancelTimeDuration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    BigDecimal b = new BigDecimal(String.valueOf(actualTimeDuration)).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    item.put("duration", v.floatValue());
                    item.put("cancelTimeDuration", a.floatValue());
                    item.put("actualTimeDuration", b.floatValue());
                }
                Integer status = (Integer) item.get("status");
                if (status != null) {
                    item.put("statusName", ApprovalStatusEnum.getName(status));
                }
                Integer leaveId = (Integer) item.get("waidtype");
                item.put("funcType", BaseConst.WF_FUNC_TYPE_1);
                item.put("businessKey", leaveId + "_" + BusinessCodeEnum.LEAVE.getCode());
                String timeSlot = (String) item.get("timeSlot");
                if (StringUtils.isNotBlank(timeSlot)) {
                    item.put("timeSlot", timeSlot.replace("->", "~").replaceAll("A", DayHalfTypeEnum.getDesc("A")).replaceAll("P", DayHalfTypeEnum.getDesc("P")).replaceAll("/", "-"));
                }
                Long shiftStartTime = (Long) item.get("shift_start_time");
                Long shiftEndTime = (Long) item.get("shift_end_time");
                Integer periodType = (Integer) item.get("period_type");
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType) || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                    item.put("startDate", DateUtil.getDateStrByTimesamp(shiftStartTime));
                    item.put("endDate", DateUtil.getDateStrByTimesamp(shiftEndTime));
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    Long start = (Long) item.get("start_time");
                    Long end = (Long) item.get("end_time");
                    item.put("startDate", String.format("%s%s", DateUtil.getDateStrByTimesamp(start), DayHalfTypeEnum.getDesc(item.get("shalf_day").toString())));
                    item.put("endDate", String.format("%s%s", DateUtil.getDateStrByTimesamp(end), DayHalfTypeEnum.getDesc(item.get("ehalf_day").toString())));
                } else {
                    item.put("startDate", DateUtil.getTimeStrByTimesamp(shiftStartTime));
                    item.put("endDate", DateUtil.getTimeStrByTimesamp(shiftEndTime));
                }
                if (item.get("i18nLeaveName") != null) {
                    String i18n = LangParseUtil.getI18nLanguage(item.get("i18nLeaveName").toString(), null);
                    if (com.caidaocloud.util.StringUtil.isNotBlank(i18n)) {
                        item.put("wa_name", i18n);
                    }
                }
                //增加销假时间字段
                Integer waid = (Integer) item.get("waid");
                List<WaEmpLeaveCancelDo> leaveCancelList = leaveCancelMap.get(waid);
                if (CollectionUtils.isEmpty(leaveCancelList)) {
                    leaveCancelList = new ArrayList<>();
                }
                if (CollectionUtils.isNotEmpty(leaveCancelList)) {
                    Optional<Long> opt = leaveCancelList.stream().map(WaEmpLeaveCancelDo::getLastApprovalTime).filter(Objects::nonNull).max(Long::compare);
                    if (opt.isPresent()) {
                        item.put("leaveCancelTime", DateUtil.getTimeStrByTimesamp(opt.get()));
                    } else {
                        item.put("leaveCancelTime", "-");
                    }
                }
                boolean flag = true;
                //是否允许多次销假
                Boolean allowedMultipleCancel = (Boolean) item.get("allowedMultipleCancel");
                //只要有审批中或审批中的销假单就算一次销假
                boolean isLeaveCancel = leaveCancelList.stream().anyMatch(e -> ApprovalStatusEnum.PASSED.getIndex().equals(Integer.valueOf(e.getStatus()))
                        || ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(Integer.valueOf(e.getStatus())));
                if (!allowedMultipleCancel) {
                    flag = !isLeaveCancel;
                }
                //如果是审批中的休假单，没有选择附件补充,则不允许销假
                item.put("allowManyLeaveCancel", flag);
            });
        }
        return list;
    }

    @SuppressWarnings({"unchecked"})
    private PageBean changeToday(PageBean pageBean) {
        List<FilterBean> list = pageBean.getFilterList();
        for (int i = 0; i < list.size(); i++) {
            FilterBean fff = list.get(i);
            if (("crttime".equals(fff.getField()) || "last_approval_time".equals(fff.getField())) && OpEnum.eq == fff.getOp()) {
                fff.setOp(OpEnum.bt);
                fff.setMax(Long.valueOf(fff.getMin()) + 86400 + "");
                list.set(i, fff);
            }
        }
        return pageBean;
    }

    private void changeParam(PageBean pageBean, Map map) {
        List<FilterBean> list = pageBean.getFilterList();
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator<FilterBean> it = list.iterator();
            while (it.hasNext()) {
                FilterBean filterBean = it.next();
                if ("leaveTime".equals(filterBean.getField())) {
                    if (StringUtils.isNotBlank(filterBean.getMin())) {
                        map.put("startDateTime", Long.valueOf(filterBean.getMin()));
                    }
                    if (StringUtils.isNotBlank(filterBean.getMax())) {
                        map.put("endDateTime", Long.valueOf(filterBean.getMax()));
                    }
                    it.remove();
                }
            }
        }
    }

    @Override
    public List<Map> getUserLeaveTypes() throws Exception {
        val user = sessionService.getUserInfo();
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(user.getStaffId());
        if (empInfo == null) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, null).getMsg());
        }
        Integer cityId = null;
        List<Map> leaveTypeList = waLeaveTypeDo.getLeaveTypeList(empInfo.getBelongOrgId(), empInfo.getEmpid(), empInfo.getGender(), null, cityId);
        if (CollectionUtils.isNotEmpty(leaveTypeList)) {
            leaveTypeList = leaveTypeList.stream().filter(l -> (Boolean) l.get("isEmpShow")).collect(Collectors.toList());
        }
        //返回假期配额信息
        waLeaveCoreService.getLeaveTypeQuotaV1(leaveTypeList, empInfo.getEmpid());
        List<Map> list = LangUtil.langlist(waLeaveCoreService.filterLeaveType(Long.valueOf(user.getTenantId()), user.getTenantId(), user.getStaffId(), leaveTypeList), SessionHolder.getLang(),
                new String[]{"leaveName", "warnMsg"}, new String[]{"leave_name_lang", "warnMsgLang"});
        return list;
    }

    @Override
    public List<HolidayQuotaVo> getUserLeaveTypeQuotas() {
        val user = sessionService.getUserInfo();
        val empId = user.getStaffId();
        val belongId = user.getTenantId();
        SessionBean sessionBean = new SessionBean();
        sessionBean.setEmpid(empId);
        sessionBean.setCorpid(Long.valueOf(user.getTenantId()));
        sessionBean.setBelongid(belongId);
        sessionBean.setLanguage(SessionHolder.getLang());
        sessionBean.setUserid(user.getUserId());
        val remainList = mobileV16Service.getAllRemainsDay(empId, belongId, sessionBean);
        return remainList.stream().map(remain ->
                FastjsonUtil.toObject(FastjsonUtil.toJson(remain), HolidayQuotaVo.class)
        ).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> revokeEmpLeave(RevokeEmpLeaveDto dto, UserInfo userInfo, boolean isSendMsg, boolean callWorkflow) throws Exception {
        if (null == userInfo) {
            userInfo = getUserInfo();
        }
        WaEmpLeave empLeave = waEmpLeaveMapper.selectByPrimaryKey(dto.getLeaveId());
        if (empLeave == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TIME_NOT_EXIST, Boolean.FALSE);
        }
        LogRecordContext.putVariable("empId", empLeave.getEmpid());
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(empLeave.getLeaveTypeId());
        if (waLeaveType == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        LogRecordContext.putVariable("leaveName", waLeaveType.getLeaveName());
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(empLeave.getStatus().intValue())) {
            return ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE);
        }
        if (empLeave.getStatus().intValue() != LeaveStatusEnum.LEAVE_STATUS_1.value && empLeave.getStatus().intValue() != LeaveStatusEnum.LEAVE_STATUS_2.value) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_REVOKE_NOT_ALLOW, Boolean.FALSE);
        }
        //超过考勤截止日不允许撤销
        Long onlyDate = DateUtil.getOnlyDate();
        Long leaveDate = DateUtil.getTimesampByDateStr2(empLeave.getStartDate());
        WaSob waSob = waSobService.getWaSob(empLeave.getEmpid(), leaveDate);
        if (waSob != null) {
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            if (onlyDate > sobEndDate) {
                Integer sysPeriodMonth = waSob.getSysPeriodMonth();
                throw new CDException(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
            }
        }
        //先撤销销假单
        List<WaEmpLeaveCancelDo> list = leaveCancelDomainService.getListByLeaveId(userInfo.getTenantId(), empLeave.getLeaveId());
        List<WaEmpLeaveCancelDo> cancelList = list.stream().filter(e -> Objects.equals(e.getStatus(), LeaveStatusEnum.LEAVE_STATUS_1.value) || Objects.equals(e.getStatus(), LeaveStatusEnum.LEAVE_STATUS_2.value)).collect(Collectors.toList());
        for (WaEmpLeaveCancelDo cancelDo : cancelList) {
            RevokeLeaveCancelDto cancelDto = new RevokeLeaveCancelDto();
            cancelDto.setLeaveCancelId(cancelDo.getLeaveCancelId());
            cancelDto.setRevokeReason(dto.getRecokeReason());
            cancelDto.setCloseCheck(true);// 撤销时关闭校验逻辑
            waEmpLeaveCancelService.revokeCancelLeave(cancelDto);
        }
        //更新单据状态
        WaEmpLeave empLeaveRecord = new WaEmpLeave();
        empLeaveRecord.setLeaveId(empLeave.getLeaveId());
        empLeaveRecord.setStatus(ApprovalStatusEnum.REVOKED.getIndex().shortValue());
        empLeaveRecord.setRevokeReason(dto.getRecokeReason());
        empLeaveRecord.setUpduser(userInfo.getUserId());
        empLeaveRecord.setUpdtime(DateUtil.getCurrentTime(true));
        empLeaveRecord.setLeaveStatus(LeaveCancelStatusEnum.NO_LEAVE_CANCEL.getIndex());
        empLeave.setStatus(ApprovalStatusEnum.REVOKED.getIndex().shortValue());
        if (callWorkflow) {
            String businessKey = dto.getLeaveId() + "_" + BusinessCodeEnum.LEAVE.getCode();
            WfRevokeDto revokeDto = new WfRevokeDto();
            revokeDto.setBusinessKey(businessKey);
            Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
            if (null == result || !result.isSuccess()) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, Boolean.FALSE).getMsg());
            }
        }
        waEmpLeaveMapper.updateByPrimaryKeySelective(empLeaveRecord);
        if (waLeaveType.getLeaveType() != 4) {
            //如果假期类型为限额，则去更新配额使用明细表的数据状态和配额表的在途配额
            if (waLeaveType.getQuotaRestrictionType() != null && waLeaveType.getQuotaRestrictionType() == 1) {
                //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
                Integer quotaType = waLeaveType.getQuotaType();
                if (quotaType == null) {
                    //2假期类型为调休
                    quotaType = waLeaveType.getLeaveType() == 3 ? 2 : 1;
                }
                if (quotaType == 2) {
                    waCommonService.updateEmpCompensatoryQuotaApprovalResult(userInfo.getUserId(), empLeave);
                } else {
                    waCommonService.updateWaLeaveQuotaUse(userInfo.getUserId(), empLeave);
                }
            }
        }
        if (isSendMsg && empLeave.getStatus().intValue() == LeaveStatusEnum.LEAVE_STATUS_2.value) {
            this.sendMsg(userInfo.getTenantId(), empLeave, waLeaveType);
        }

        return Result.ok(Boolean.TRUE);
    }

    private void sendMsg(String tenantId, WaEmpLeave empLeave, WaLeaveType waLeaveType) {
        log.info("Start to send revoke message");
        List<Long> receiver = new ArrayList<>();
        receiver.add(empLeave.getEmpid());
        MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                .type("msg")
                .subType("revoke")
                .title("休假审批结果通知")
                .funcType("1")
                .handlers(receiver)
                .customForms(getFormCustoms(tenantId, empLeave.getLeaveId(), waLeaveType.getLeaveName()));
        MessageParams messageParams = builder.build();
        attendanceWorkflowMsgPublish.publish(JSON.toJSONString(messageParams), Long.valueOf(tenantId));
        log.info("Success to send revoke message, messageParams[{}]", FastjsonUtil.toJson(messageParams));
    }

    private List<KeyValue> getFormCustoms(String tenantId, Integer leaveId, String leaveTypeName) {
        WaEmpLeaveDo empLeave = waEmpLeaveDo.getLeaveDetailById(tenantId, Long.valueOf(leaveId));
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empLeave.getEmpid());
        List<KeyValue> list = new ArrayList<>();
        list.add(new KeyValue("申请人", empInfo.getWorkno().concat("(").concat(empInfo.getEmpName().concat(")"))));
        list.add(new KeyValue("员工类型", empLeave.getEmployType()));
        list.add(new KeyValue("假期类型", leaveTypeName));
        list.add(new KeyValue("开始时间", DateUtil.getTimeStrByTimesamp4(empLeave.getStartTime())));
        list.add(new KeyValue("结束时间", DateUtil.getTimeStrByTimesamp4(empLeave.getEndTime())));
        if (empLeave.getTimeUnit() == 1) {
            list.add(new KeyValue("申请时长", empLeave.getTotalTimeDuration() + "天"));
        } else {
            list.add(new KeyValue("申请时长", BigDecimal.valueOf(empLeave.getTotalTimeDuration()).divide(new BigDecimal(60), 1, RoundingMode.DOWN).floatValue() + "小时"));
        }
        list.add(new KeyValue("申请时间", DateUtil.getDateStrByTimesamp(empLeave.getCrttime())));
        return list;
    }

    @Override
    public List<Map> getLeaveTypeList(Long empId, Integer quotaType) throws Exception {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (empInfo == null) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, null).getMsg());
        }
        Integer cityId = null;
        List<Map> leaveTypeList = waLeaveTypeDo.getLeaveTypeList(empInfo.getBelongOrgId(), empId, empInfo.getGender(), quotaType, cityId);
        waLeaveCoreService.getLeaveTypeQuotaV1(leaveTypeList, empId);
        return leaveTypeList;
    }

    @Override
    public List<KeyValueVo> getLeaveStatusList() {
        List<KeyValueVo> list = new ArrayList<>();
        for (LeaveCancelStatusEnum statusEnum : LeaveCancelStatusEnum.values()) {
            KeyValueVo keyValueVo = new KeyValueVo();
            keyValueVo.setText(LeaveCancelStatusEnum.getName(statusEnum.getIndex()));
            keyValueVo.setValue(String.valueOf(statusEnum.getIndex()));
            list.add(keyValueVo);
        }
        return list;
    }

    @Override
    public Boolean urge(List<Integer> leaveIds) {
        UserInfo userInfo = sessionService.getUserInfo();
        NotifyConfigDo configDo = notifyConfigDo.getNotifyConfigById(userInfo.getTenantId());
        String content = "";
        if (configDo != null) {
            content = configDo.getLeaveCancelContent();
        }
        if (CollectionUtils.isNotEmpty(leaveIds)) {
            StringBuilder builder = new StringBuilder();
            List<Map<String, String>> maps = empLeaveMapper.getEmpNamesByLeaveIds(leaveIds);
            maps.forEach(st -> builder.append(st.get("empName")).append("的").append(st.get("leaveName")).append("、"));
            if (builder.length() > 1) {
                builder.deleteCharAt(builder.length() - 1);
            }
            LogRecordContext.putVariable("content", builder.toString());
        }
        //查询未销假的休假单
        WaEmpLeaveExample example = new WaEmpLeaveExample();
        WaEmpLeaveExample.Criteria criteria = example.createCriteria();
        criteria.andLeaveIdIn(leaveIds);
        criteria.andStatusEqualTo(ApprovalStatusEnum.PASSED.getIndex().shortValue());
        criteria.andLeaveStatusEqualTo(LeaveCancelStatusEnum.NO_LEAVE_CANCEL.getIndex());
        List<WaEmpLeave> list = waEmpLeaveMapper.selectByExample(example);
        log.info("leaveCancel urge param{}", FastjsonUtil.toJson(list));
        for (WaEmpLeave waEmpLeave : list) {
            ClockMsgDto clockMsgDto = new ClockMsgDto();
            clockMsgDto.setTenantId(userInfo.getTenantId());
            clockMsgDto.setEmpIds(Arrays.asList(waEmpLeave.getEmpid()));
            clockMsgDto.setContent(content);
            clockMsgDto.setLeaveId(waEmpLeave.getLeaveId());
            clockMsgDto.setDelay(0);
            //推送
            sendLeaveCancelMsg(clockMsgDto);
        }
        log.info("leaveCancel urge end ~~");

        return true;
    }

    /**
     * 发送销假提醒消息
     *
     * @param clockMsgDto
     */
    private void sendLeaveCancelMsg(ClockMsgDto clockMsgDto) {
        log.info("Start to send leaveCancel message");
        List<Long> receiver = new ArrayList<>();
        receiver.add(clockMsgDto.getEmpIds().get(0));
        MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                .type("msg")
                .subType("leaveCancel")
                .title("销假提醒")
                .funcType("-1")
                .handlers(receiver).processId(String.valueOf(clockMsgDto.getLeaveId()))
                .content(clockMsgDto.getContent());
        MessageParams messageParams = builder.build();
        String jsonStr = JSON.toJSONString(messageParams);
        delayMsgPublish.publish(jsonStr, clockMsgDto.getDelay(), clockMsgDto.getTenantId());
        log.info("Success to send leaveCancel message, messageParams[{}]", FastjsonUtil.toJson(messageParams));
    }

    @Override
    public Result<MaternityLeaveRangeVo> getMaternityLeaveRange(MaternityLeaveRangeDto dto) {
        if (null == dto.getLeaveTypeId()) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, null);
        }
        Integer leaveTypeId = dto.getLeaveTypeId();
        Optional<WaLeaveType> leaveTypeOpt = Optional.ofNullable(waLeaveTypeMapper.selectByPrimaryKey(leaveTypeId));
        if (!leaveTypeOpt.isPresent()) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, null);
        }
        WaLeaveType leaveType = leaveTypeOpt.get();
        if (leaveType.getLeaveType() != 4) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_MUST_MATERNITY, null);
        }
        if (null == dto.getChildNum()) {
            return ResponseWrap.wrapResult(AttendanceCodes.CHILD_NUM_EMPTY, null);
        }
        if (dto.getChildNum() <= 0) {
            return ResponseWrap.wrapResult(AttendanceCodes.CHILD_NUM_ERROR, null);
        }
        if (null == dto.getMaternityLeaveType()) {
            return ResponseWrap.wrapResult(AttendanceCodes.MATERNITY_TYPE_EMPTY, null);
        }
        if (null == dto.getManufactureDate()) {
            return ResponseWrap.wrapResult(AttendanceCodes.CHILD_BIRTHDAY_EMPTY, null);
        }
        UserInfo userInfo = getUserInfo();
        Long birthday = dto.getManufactureDate();
        String tenantId = userInfo.getTenantId();
        //查询额度配置
        Optional<LeaveQuotaConfigDo> configOpt = Optional.ofNullable(leaveQuotaConfigDo.getById(tenantId, leaveTypeId));
        if (!configOpt.isPresent()) {
            return ResponseWrap.wrapResult(AttendanceCodes.RULE_DEFINED_ERROR, null);
        }
        LeaveQuotaConfigDo configDo = configOpt.get();
        //查询额度规则
        List<QuotaGenRuleDo> genRuleDoList = quotaGenRuleDo.getListByConfigIds(tenantId, Collections.singletonList(configDo.getConfigId()));
        if (CollectionUtils.isEmpty(genRuleDoList)) {
            return ResponseWrap.wrapResult(AttendanceCodes.RULE_DEFINED_ERROR, null);
        }
        //额度规则匹配申请员工，匹配到方能进行产假申请
        QuotaGenRuleDo quotaRule = null;
        for (QuotaGenRuleDo genRuleDo : genRuleDoList) {
            Integer childNum = null;
            String sqlExp = genRuleDo.getConditionExp();
            String childNumExp = PatternUtil.getStrFromRegex(sqlExp, "child_num", "\\s*(>=|>|<=|<|<>|=)\\s*'(\\d+(?:\\.\\d*)?|\\.\\d+)'");
            if (StringUtils.isNotBlank(childNumExp)) {
                String childNumStr = PatternUtil.getStrFromRegex(childNumExp, "", "(\\d+(?:\\.\\d*)?|\\.\\d+)");
                if (StringUtils.isNotBlank(childNumStr)) {
                    childNum = Integer.valueOf(childNumStr);
                }
            }
            Integer maternityType = null;
            String maternityLeaveTypeExp = PatternUtil.getStrFromRegex(sqlExp, "maternity_type", "\\s*(>=|>|<=|<|<>|=)\\s*'(\\d+(?:\\.\\d*)?|\\.\\d+)'");
            if (StringUtils.isNotBlank(maternityLeaveTypeExp)) {
                String maternityLeaveTypeStr = PatternUtil.getStrFromRegex(maternityLeaveTypeExp, "", "(\\d+(?:\\.\\d*)?|\\.\\d+)");
                if (StringUtils.isNotBlank(maternityLeaveTypeStr)) {
                    maternityType = Integer.valueOf(maternityLeaveTypeStr);
                }
            }
            if (!dto.getChildNum().equals(childNum) || !dto.getMaternityLeaveType().equals(maternityType)) {
                continue;
            }
            sqlExp = getSqlExp(sqlExp);
            if (StringUtils.isNotBlank(sqlExp)) {
                Long empId = dto.getEmpId();
                List<Long> applicableEmpList = sysEmpInfo.getEmpIdsByGroupExp(Long.valueOf(tenantId), sqlExp, null);
                if (applicableEmpList.contains(empId)) {
                    quotaRule = genRuleDo;
                    break;
                }
            } else {
                quotaRule = genRuleDo;
                break;
            }
        }
        if (!Optional.ofNullable(quotaRule).isPresent()) {
            //没有配置额度规则
            return ResponseWrap.wrapResult(AttendanceCodes.RULE_DEFINED_ERROR, null);
        }
        //配置额度
        Integer quotaVal = Optional.ofNullable(quotaRule.getQuotaVal()).orElse(0);
        //产假起始日期
        Long startDate = DateUtil.getOnlyDate(new Date(birthday * 1000));
        //产假结束日期
        Long endDate = DateUtil.addDate(startDate * 1000, quotaVal - 1);
        Optional<Integer> containPrenatalLeaveOpt = Optional.ofNullable(configDo.getContainPrenatalLeave());
        //包含产前假，增加15日，产假起始日期往前推15日
        if (containPrenatalLeaveOpt.isPresent() && containPrenatalLeaveOpt.get() == 1) {
            startDate = DateUtil.addDate(startDate * 1000, -15);
        }
        return Result.ok(new MaternityLeaveRangeVo(startDate, endDate));
    }

    private String getSqlExp(String sqlExp) {
        if (null == sqlExp) {
            return "";
        }
        if (sqlExp.contains("child_num")) {
            sqlExp = PatternUtil.strReplaceFromRegex(sqlExp, "child_num", "\\s*(>=|>|<=|<|<>|=)\\s*'(\\d+(?:\\.\\d*)?|\\.\\d+)'", "");
        }
        if (sqlExp.contains("maternity_type")) {
            sqlExp = PatternUtil.strReplaceFromRegex(sqlExp, "maternity_type", "\\s*(>=|>|<=|<|<>|=)\\s*'(\\d+(?:\\.\\d*)?|\\.\\d+)'", "");
        }
        sqlExp = sqlExp.trim();
        if (StringUtils.isNotBlank(sqlExp)) {
            while (sqlExp.startsWith("AND")) {
                sqlExp = sqlExp.substring(3).trim();
            }
        }
        if (StringUtils.isNotBlank(sqlExp)) {
            while (sqlExp.startsWith("OR")) {
                sqlExp = sqlExp.substring(2).trim();
            }
        }
        return sqlExp.trim();
    }

    @Override
    public Pair<Double, String> getHomeLeaveAvailableQuota(Long leaveTypeId, Long empId, String visitingReason, String marriage) {
        if (null == empId) {
            empId = sessionService.getUserInfo().getStaffId();
        }
        List<HomeLeaveType> types = null;
        if ("visiting_parents".equals(visitingReason)) {
            if ("1".equals(marriage)) {
                types = Lists.newArrayList(
                        HomeLeaveType.MARRIED_VISIT_PARENTS,
                        HomeLeaveType.MARRIED_VISIT_BOTH,
                        HomeLeaveType.MARRIED_OR_NOT_VISIT_PARENTS
                );
            } else {
                types = Lists.newArrayList(
                        HomeLeaveType.NOT_MARRIED_VISIT_PARENTS,
                        HomeLeaveType.MARRIED_OR_NOT_VISIT_PARENTS
                );
            }
        } else {
            types = Lists.newArrayList(
                    HomeLeaveType.MARRIED_VISIT_BOTH,
                    HomeLeaveType.MARRIED_VISIT_SPOUSE
            );
        }
        List<Map> quotas = quotaMapper.getAvailableHomeLeaveQuota(leaveTypeId, empId, types);
        if (quotas.isEmpty()) {
            return Pair.pair(0d, "");
        }
        Integer acctTimeType = (Integer) quotas.get(0).get("accttimetype");
        Double sum = quotas.stream().mapToDouble(it -> {
            Float quotaDay = (Float) it.get("quotaday");
            Float nowQuota = (Float) it.get("nowquota");
            Float adjustQuota = (Float) it.get("adjustquota");
            Integer ifAdvance = (Integer) it.get("ifadvance");
            if (ifAdvance == 1) {
                return new Double(quotaDay + adjustQuota);
            } else {
                return new Double(nowQuota + adjustQuota);
            }
        }).sum();
        return Pair.pair(sum, PreTimeUnitEnum.getName(acctTimeType));
    }

    @Override
    public Result<String> getHomeLeaveAvailableQuotaText(Long leaveTypeId, Long empId, String visitingReason, String marriage) {
        val quotaPair = getHomeLeaveAvailableQuota(leaveTypeId, empId, visitingReason, marriage);
        return Result.ok(quotaPair.first() + quotaPair.second());
    }

    private Long getBusinessId(String businessKey) {
        if (StringUtils.isEmpty(businessKey)) {
            return null;
        }
        if (businessKey.contains("_")) {
            String[] businessKeys = businessKey.split("_");
            return Long.valueOf(businessKeys[0]);
        }
        return Long.valueOf(businessKey);
    }

    @Override
    public void appendHomeLeaveInfo(String businessKey, WfDetailDto detailDto) {
        if (businessKey == null || !businessKey.contains("LEAVE")) {
            return;
        }
        val leaveApplyId = getBusinessId(businessKey);
        Map homeLeaveInfo = empLeaveMapper.getHomeLeaveTypeAndMarriageStatus(leaveApplyId);
        val marriageDefList = masterdataFeign
                .fetchModelPropertyEnum("entity.hr.EmpPrivateInfo", "maritalStatus")
                .getData();
        if (null != homeLeaveInfo && StringUtils.isNotEmpty((String) homeLeaveInfo.get("homeleavetype"))) {
            val homeLeaveType = (String) homeLeaveInfo.get("homeleavetype");
            val marriageStatus = (String) homeLeaveInfo.get("marriagestatus");
            detailDto.getItems().add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.HOME_LEAVE_REASON, null).getMsg(), homeLeaveType.equals("visiting_parents") ?
                    ResponseWrap.wrapResult(AttendanceCodes.VISITING_PARENT, null).getMsg() : ResponseWrap.wrapResult(AttendanceCodes.VISITING_SPOUSE, null).getMsg()));
            detailDto.getItems().add(new KeyValue(ResponseWrap.wrapResult(AttendanceCodes.MARITAL_STATUS, null).getMsg(), marriageDefList.stream()
                    .filter(it -> it.getValue().equals(marriageStatus))
                    .findFirst().get().getText()));
        }
    }

    private BusinessCodeEnum getBusinessCodeEnum(String businessKey) {
        if (com.caidaocloud.util.StringUtil.isBlank(businessKey) || !businessKey.contains("_")) {
            return null;
        }
        String[] businessKeys = businessKey.split("_");
        String code = businessKeys[1];
        for (BusinessCodeEnum one : BusinessCodeEnum.values()) {
            if (one.getCode().equals(code)) {
                return one;
            }
        }
        return null;
    }

    @Override
    public void appendHomeLeaveInfo(String businessKey, WfResponseDto detailDto) {
        BusinessCodeEnum funcType = this.getBusinessCodeEnum(businessKey);
        if (funcType == null) {
            return;
        }
        if (BusinessCodeEnum.LEAVE.equals(funcType)) {
            val leaveApplyId = getBusinessId(businessKey);
            Map homeLeaveInfo = empLeaveMapper.getHomeLeaveTypeAndMarriageStatus(leaveApplyId);
            val marriageDefList = masterdataFeign
                    .fetchModelPropertyEnum("entity.hr.EmpPrivateInfo", "maritalStatus")
                    .getData();
            if (null != homeLeaveInfo && StringUtils.isNotEmpty((String) homeLeaveInfo.get("homeleavetype"))) {
                val homeLeaveType = (String) homeLeaveInfo.get("homeleavetype");
                val marriageStatus = (String) homeLeaveInfo.get("marriagestatus");
                detailDto.getDetailList().add(new WfBusinessDataDetailDto("homeLeaveType", ResponseWrap.wrapResult(AttendanceCodes.HOME_LEAVE_REASON, null).getMsg(),
                        homeLeaveType.equals("visiting_parents") ?
                                ResponseWrap.wrapResult(AttendanceCodes.VISITING_PARENT, null).getMsg() : ResponseWrap.wrapResult(AttendanceCodes.VISITING_SPOUSE, null).getMsg(), null));
                detailDto.getDetailList().add(new WfBusinessDataDetailDto("marriageStatus", ResponseWrap.wrapResult(AttendanceCodes.MARITAL_STATUS, null).getMsg(), marriageDefList.stream()
                        .filter(it -> it.getValue().equals(marriageStatus))
                        .findFirst().get().getText(), null));
            }
        }
    }

    @Override
    public Long getHomeLeaveRange(Long leaveTypeId, Long empId, long startTime, String visitingReason, String marriage) {
        if (null == empId) {
            empId = sessionService.getUserInfo().getStaffId();
        }
        Double days = getHomeLeaveAvailableQuota(leaveTypeId, empId, visitingReason, marriage).getKey();
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveTypeId.intValue());
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(sessionService.getUserInfo().getTenantId(),
                empInfo.getEmpid(), empInfo.getTmType(), startTime, startTime + 50l * 24 * 3600, empInfo.getWorktimeType(), true);
        Long maxEnd = pbMap.keySet().stream().max(Comparator.comparing(it -> it)).orElse(0l);
        if (maxEnd <= 0) {
            return startTime;
        }
        double leftDays = days;
        for (long i = startTime; i <= maxEnd; i = i + 24 * 3600l) {
            if (pbMap.containsKey(i)) {
                WaWorktimeDetail detail = pbMap.get(i);
                if (waLeaveType.getIsRestDay() != true && (detail.getDateType() == 2 || detail.getDateType() == 4)) {
                    continue;
                }
                if (waLeaveType.getIsLegalHoliday() != true && (detail.getDateType() == 3 || detail.getDateType() == 5)) {
                    continue;
                }
                leftDays--;
                if (leftDays <= 0) {
                    return i;
                }
            }
        }
        return startTime;
    }

    @Override
    public List<WaEmpLeave> getListByBatchId(String tenantId, Long batchId) {
        WaEmpLeaveExample empLeaveExample = new WaEmpLeaveExample();
        empLeaveExample.createCriteria().andBatchIdEqualTo(batchId)
                .andTenantIdEqualTo(tenantId);
        return waEmpLeaveMapper.selectByExample(empLeaveExample);
    }

    @Override
    public List<WaLeaveDaytimeDo> getLeaveDayTimeList(Long date) {
        AttendanceBasePage basePage = new AttendanceBasePage();
        basePage.setPageNo(1);
        basePage.setPageSize(PAGE_SIZE);
        List<Integer> statusList = Lists.newArrayList(ApprovalStatusEnum.IN_APPROVAL.getIndex(), ApprovalStatusEnum.PASSED.getIndex());
        PageResult<WaLeaveDaytimeDo> pageResult = waLeaveDaytimeDo.getLeaveDayTimePage(basePage, date, date, statusList);
        if (null == pageResult || CollectionUtils.isEmpty(pageResult.getItems())) {
            return Lists.newArrayList();
        }
        List<WaLeaveDaytimeDo> firstPageDataList = pageResult.getItems();
        int totalCount = pageResult.getTotal();
        if (totalCount <= PAGE_SIZE) {
            return firstPageDataList;
        }
        List<WaLeaveDaytimeDo> dataList = Lists.newArrayList();
        dataList.addAll(firstPageDataList);
        //总页数
        int totalPage = (int) Math.ceil((double) totalCount / PAGE_SIZE);
        for (int pageNo = 2; pageNo <= totalPage; pageNo++) {
            basePage.setPageNo(pageNo);
            pageResult = waLeaveDaytimeDo.getLeaveDayTimePage(basePage, date, date, statusList);
            if (null != pageResult && CollectionUtils.isNotEmpty(pageResult.getItems())) {
                dataList.addAll(pageResult.getItems());
            }
        }
        return dataList;
    }

    @Override
    public List<Long> getLeaveEmpIdList(Long date) {
        // 查询休假数据
        List<WaLeaveDaytimeDo> leaveDayTimeList = getLeaveDayTimeList(date);
        if (CollectionUtils.isEmpty(leaveDayTimeList)) {
            return Lists.newArrayList();
        }
        List<Long> empIdList = leaveDayTimeList.stream().map(WaLeaveDaytimeDo::getEmpid)
                .distinct().collect(Collectors.toList());

        Long startDate = date;
        Long endDate = date + 86399;
        // 查询销假数据
        List<WaEmpLeaveCancelDaytimeDo> leaveCancelDaytimeList = waEmpLeaveCancelDaytimeDo.getLeaveCancelDaytimeList(UserContext.getTenantId(),
                startDate, endDate);
        if (CollectionUtils.isEmpty(leaveCancelDaytimeList)) {
            return empIdList;
        }

        // 查询班次信息
        UserInfo userInfo = UserContext.getAndCheckUser();
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(userInfo.getTenantId());

        // 查询员工排班
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("startDate", startDate);
        paramsMap.put("endDate", endDate);
        paramsMap.put("belongid", userInfo.getTenantId());
        paramsMap.put("anyEmpid", "'{" + StringUtils.join(empIdList, ",") + "}'");
        paramsMap.put("anyEmpids", "'{" + StringUtils.join(empIdList, ",") + "}'");
        paramsMap.put("ymstart", Integer.valueOf(DateUtil.parseDateToPattern(new Date(startDate * 1000), "yyyyMM")));
        paramsMap.put("ymend", Integer.valueOf(DateUtil.parseDateToPattern(new Date(endDate * 1000), "yyyyMM")));
        Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();
        waCommonService.getEmpShiftInfoListMaps(paramsMap, empShiftInfoByDateMap, empIdList, shiftDefMap);

        // 计算休假单的实际休假开始、结束时间
        List<WaLeaveDaytimeExtPo> leaveDaytimeExtPoList = ObjectConverter.convertList(leaveDayTimeList, WaLeaveDaytimeExtPo.class);
        List<WaLeaveDaytimeExtDto> ltExtDtoList = leaveDaytimeExtPoList.stream()
                .map(ltDay -> {
                    String shiftKey = String.format("%s_%s", ltDay.getEmpid(),
                            ltDay.getLeaveDate());
                    WaShiftDef shiftDef;
                    EmpShiftInfo empShiftInfo = empShiftInfoByDateMap.get(shiftKey);
                    if (null == empShiftInfo || null == (shiftDef = shiftDefMap.get(empShiftInfo.getShiftDefId()))) {
                        return null;
                    }
                    return mobileV16Service.calLeaveDayRealTimeSlot(ltDay, shiftDef);
                })
                .filter(it -> !Objects.isNull(it))
                .collect(Collectors.toList());

        //计算销假单实际的销假开始、结束时间
        List<WaLeaveDaytimeExtDto> leaveCancelDaytimeExtDtoList = leaveCancelDaytimeList.stream()
                .map(leaveCancelDayTime -> {
                    String shiftKey = String.format("%s_%s", leaveCancelDayTime.getEmpid(),
                            leaveCancelDayTime.getLeaveCancelDate());
                    WaShiftDef shiftDef;
                    EmpShiftInfo empShiftInfo = empShiftInfoByDateMap.get(shiftKey);
                    if (null == empShiftInfo || null == (shiftDef = shiftDefMap.get(empShiftInfo.getShiftDefId()))) {
                        return null;
                    }
                    WaLeaveDaytimeExtPo waLeaveCancelDayTime = ObjectConverter.convert(leaveCancelDayTime, WaLeaveDaytimeExtPo.class);
                    waLeaveCancelDayTime.setLeaveDate(leaveCancelDayTime.getLeaveCancelDate());
                    WaLeaveDaytimeExtDto extDto = mobileV16Service.calLeaveDayRealTimeSlot(waLeaveCancelDayTime, shiftDef);
                    extDto.setXj(true);
                    return extDto;
                })
                .filter(it -> !Objects.isNull(it))
                .collect(Collectors.toList());

        //组合休假以及销假数据
        List<WaLeaveDaytimeExtDto> allDaytimeExtDtoList = new ArrayList<>();
        allDaytimeExtDtoList.addAll(ltExtDtoList);
        allDaytimeExtDtoList.addAll(leaveCancelDaytimeExtDtoList);
        allDaytimeExtDtoList.sort(Comparator.comparing(WaLeaveDaytimeExtDto::getCreateTime));

        Map<Long, List<WaLeaveDaytimeExtDto>> empLeaveGroupMap = allDaytimeExtDtoList.stream()
                .collect(Collectors.groupingBy(WaLeaveDaytimeExtDto::getEmpId));

        return empIdList.stream().filter(empId -> {
            List<WaLeaveDaytimeExtDto> daytimeExtDtoList = empLeaveGroupMap.get(empId);
            daytimeExtDtoList.sort(Comparator.comparing(WaLeaveDaytimeExtDto::getCreateTime));

            //根据休假单、销假单 计算有效地休假时间段
            List<TimeRangeCheckUtil.ChangeData> leaveTimeRangeList = daytimeExtDtoList.stream()
                    .map(lt -> TimeRangeCheckUtil.ChangeData.from(!lt.isXj(), lt.getLeaveStartTime(), lt.getLeaveEndTime()))
                    .collect(Collectors.toList());

            //筛选有效地休假时间段
            List<Pair<Long, Long>> leaveTimes = TimeRangeCheckUtil.asks(leaveTimeRangeList);
            if (CollectionUtils.isEmpty(leaveTimes)) {
                return Boolean.FALSE;
            }

            leaveTimes = leaveTimes.stream().filter(leaveTime -> {
                long start = leaveTime.getKey();
                long end = leaveTime.getValue();
                long validLeaveDuration = end - start;
                return validLeaveDuration > 0;
            }).collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(leaveTimes);
        }).collect(Collectors.toList());
    }

    @Override
    public List<EmpLeaveInfo> getEmpLeaveDayTimeList(String tenantId, List<Long> empIdList, Long startDate, Long endDate) {
        return waLeaveDaytimeDo.getEmpLeaveDayTimeList(tenantId, empIdList, startDate, endDate);
    }

    @Override
    public List<WaLeaveDaytimeExtDto> getEmpLeaveDayTimeExtDtoList(String tenantId, List<Long> empIdList,
                                                                   Long startDate, Long endDate,
                                                                   Map<String, WaShiftDo> empShiftDoMap) {
        List<EmpLeaveInfo> empLeaveInfos = getEmpLeaveDayTimeList(tenantId, empIdList, startDate, endDate);
        if (CollectionUtils.isEmpty(empLeaveInfos)) {
            return Lists.newArrayList();
        }

        List<Integer> useShiftDefIds = empLeaveInfos.stream()
                .map(EmpLeaveInfo::getUseShiftDefId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, WaShiftDef> corpShiftDefMap = CollectionUtils.isEmpty(useShiftDefIds)
                ? waCommonService.getCorpAllShiftDef(tenantId, useShiftDefIds) : new HashMap<>();

        // 计算实际休假开始、结束时间
        List<WaLeaveDaytimeExtPo> leaveDayTimes = EmpLeaveInfo.doConvert2ExtPo(empLeaveInfos);
        return leaveDayTimes.stream()
                .map(ltDay -> {
                    WaShiftDef userShiftDef = getUserShiftDef(ltDay, corpShiftDefMap, empShiftDoMap);
                    return userShiftDef != null ? mobileV16Service.calLeaveDayRealTimeSlot(ltDay, userShiftDef) : null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private WaShiftDef getUserShiftDef(WaLeaveDaytimeExtPo ltDay,
                                       Map<Integer, WaShiftDef> corpShiftDefMap,
                                       Map<String, WaShiftDo> empShiftDoMap) {
        WaShiftDef shiftDef;
        if (ltDay.getUseShiftDefId() != null && MapUtils.isNotEmpty(corpShiftDefMap)
                && null != (shiftDef = corpShiftDefMap.get(ltDay.getUseShiftDefId()))) {
            return shiftDef;
        }

        WaShiftDo shiftDo;
        if (MapUtils.isNotEmpty(empShiftDoMap)
                && null != (shiftDo = empShiftDoMap.get(ltDay.getEmpid() + "_" + ltDay.getLeaveDate()))) {
            return ObjectConverter.convert(shiftDo, WaShiftDef.class);
        }

        return null;
    }
}