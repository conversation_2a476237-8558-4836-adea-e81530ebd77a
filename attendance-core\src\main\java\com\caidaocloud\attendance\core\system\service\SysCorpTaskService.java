package com.caidaocloud.attendance.core.system.service;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.system.mybatis.mapper.SysCorpTaskMapper;
import com.caidao1.system.mybatis.mapper.SysCorpTaskRelMapper;
import com.caidao1.system.mybatis.model.SysCorpTask;
import com.caidao1.system.mybatis.model.SysCorpTaskRel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysCorpTaskService {
	@Autowired
    private SysCorpTaskMapper sysCorpTaskMapper;
    @Autowired
    private SysCorpTaskRelMapper sysCorpTaskRelMapper;
    
	public void generateTask(Integer belongid){
		
		List<SysCorpTask> tasks = sysCorpTaskMapper.selectCorpTasks(belongid);
		if(tasks != null && tasks.size() > 0){
			for (SysCorpTask sysCorpTask : tasks) {
				SysCorpTaskRel  rel = new SysCorpTaskRel();
				rel.setBelongOrgId(belongid);
				rel.setCorpTaskId(sysCorpTask.getCorpTaskId());
				rel.setStatus(1);
				rel.setCrttime(DateUtil.getCurrentTime(true));
				sysCorpTaskRelMapper.insertSelective(rel);
			}
		}
	}
	
	public Integer delTaskRelById(Integer corpTaskRelId){
		return sysCorpTaskRelMapper.deleteByPrimaryKey(corpTaskRelId);
	}
}
