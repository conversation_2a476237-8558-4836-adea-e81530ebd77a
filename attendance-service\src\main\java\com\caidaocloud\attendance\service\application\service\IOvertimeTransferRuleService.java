package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.interfaces.dto.overtime.OvertimeTransferRuleDto;
import com.caidaocloud.dto.KeyValue;

import java.util.List;

public interface IOvertimeTransferRuleService {

    void saveOvertimeTransferRule(OvertimeTransferRuleDto dto);

    OvertimeTransferRuleDto getOvertimeTransferRule(Long ruleId);

    void deleteOvertimeTransferRule(Long ruleId);

    Boolean checkOvertimeTransferRuleName(OvertimeTransferRuleDto dto);

    List<OvertimeTransferRuleDto> getOvertimeTransferRuleList();

    List<KeyValue> getOvertimeTransferRuleList(Integer compensateType);

    List<OvertimeTransferRuleDto> getOvertimeTransferRuleList(List<Long> ruleIds);

    boolean checkTransferRuleReferenced(Long ruleId);
}
