package com.caidaocloud.attendance.core.commons.interceptor;

import com.caidao1.commons.dao.OperLog;
import com.caidao1.commons.utils.SystemUtil;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

public class CDInterceptor implements HandlerInterceptor {
//	@Autowired
//	private MongoTemplate mongoTemplate;
	@Override
	public boolean preHandle(HttpServletRequest request,
			HttpServletResponse response, Object handler) throws Exception {
		// TODO Auto-generated method stub
//		System.out.println("------------preHandle----------------1------");
		return true;
	}

	@Override
	public void postHandle(HttpServletRequest request,
			HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
		// TODO Auto-generated method stub
//		System.out.println("------------postHandle--------------2--------");
	}

	@Override
	public void afterCompletion(HttpServletRequest request,
			HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		HttpSession session  = request.getSession();
		// TODO Auto-generated method stub
//		System.out.println("------------afterCompletion-----------3-----------");
		String path = request.getRequestURI();
//		System.out.println("path============="+path);
		if(session.getAttribute("userid")!=null&&path.indexOf(".")<0){
			OperLog operLog = new OperLog();	
			String empName="";
			if(session.getAttribute("empname")!=null){
				empName = session.getAttribute("empname").toString();
			}
			String account="";
			if(session.getAttribute("account")!=null){
				account = session.getAttribute("account").toString();
			}
			operLog.setEmpName(empName);
			operLog.setAccount(account);
			operLog.setEmpId((Integer)session.getAttribute("empid"));
			operLog.setUserId((Integer)session.getAttribute("userid"));
			operLog.setCorpId((Integer)session.getAttribute("corpid"));
			operLog.setBelongOrgId((String)session.getAttribute("belongid"));
			operLog.setOperTime(System.currentTimeMillis()/1000);
			operLog.setIp(SystemUtil.getLocalIp(request));
			operLog.setPath(path);
			String agent = request.getHeader("User-Agent");
			operLog.setBrowser(agent);
//			mongoTemplate.save(operLog);
		}
//		operLog.
		
	}

}
