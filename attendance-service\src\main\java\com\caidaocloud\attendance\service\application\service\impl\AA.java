package com.caidaocloud.attendance.service.application.service.impl;

import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.text.SimpleDateFormat;
import java.util.Date;

public class AA {

    public static void main(String[] args) throws Exception {
        val reader = new BufferedReader(new FileReader("D:\\workspace\\caidao-attendance-service\\attendance-service\\src\\main\\java\\com\\caidaocloud\\attendance\\service\\application\\service\\impl\\a.txt"));
        while(true){
            val l = reader.readLine();
            if(StringUtils.isNotEmpty(l)){
                val a = l.split("\\t");
                System.out.print(a[0]);
                System.out.print("\t");
                System.out.print(a[1]);
                System.out.print("\t");
                System.out.print(new SimpleDateFormat("yyyyMMdd HH:mm:ss").format(new Date(Long.valueOf(a[2]))));
                System.out.print("\t");
                System.out.print(a[2]);
                System.out.println();
            }else{
                break;
            }
        }
    }
}
