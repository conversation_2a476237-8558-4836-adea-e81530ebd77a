package com.caidaocloud.attendance.service.application.enums;

public enum FlexibleEnum {

    CLOSE(1, "弹性分析开关关闭"),
    OPEN(2, "弹性分析开关开启");


    private Integer index;

    private String name;

    FlexibleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (FlexibleEnum c : FlexibleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
