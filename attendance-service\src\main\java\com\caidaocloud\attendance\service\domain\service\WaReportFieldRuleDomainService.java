package com.caidaocloud.attendance.service.domain.service;

import com.caidaocloud.attendance.service.domain.entity.WaReportFieldRuleDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 考勤报表字段规则
 *
 * <AUTHOR>
 * @Date 2024/2/5
 */
@Slf4j
@Service
public class WaReportFieldRuleDomainService {
    @Autowired
    private WaReportFieldRuleDo waReportFieldRuleDo;

    public List<WaReportFieldRuleDo> selectList(String tenantId, Integer parseGroupId) {
        return waReportFieldRuleDo.selectList(tenantId, parseGroupId);
    }

    public void save(WaReportFieldRuleDo reportFieldRule) {
        waReportFieldRuleDo.save(reportFieldRule);
    }

    public void update(WaReportFieldRuleDo reportFieldRule) {
        waReportFieldRuleDo.update(reportFieldRule);
    }

    public void delete(String tenantId, Integer parseGroupId) {
        waReportFieldRuleDo.delete(tenantId, parseGroupId);
    }
}
