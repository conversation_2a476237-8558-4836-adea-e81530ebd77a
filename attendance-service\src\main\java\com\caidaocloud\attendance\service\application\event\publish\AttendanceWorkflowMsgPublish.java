package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.attendance.service.application.event.publish.dto.AttendanceWorkflowMessageDto;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class AttendanceWorkflowMsgPublish {

    @Resource
    private MqMessageProducer<AttendanceWorkflowMessageDto> producer;

    @Value("${wf.msg.exchange:wf.msg.fac.direct.exchange}")
    private String exchange;

    @Value("${wf.msg.routingKey:direct.routingKey}")
    private String routingKey;

    public void publish(String msg, Long corpId) {
        AttendanceWorkflowMessageDto message = new AttendanceWorkflowMessageDto();
        message.setBody(msg);
        message.setExchange(exchange);
        message.setRoutingKey(routingKey + "." + corpId);
        producer.publish(message);
    }

}
