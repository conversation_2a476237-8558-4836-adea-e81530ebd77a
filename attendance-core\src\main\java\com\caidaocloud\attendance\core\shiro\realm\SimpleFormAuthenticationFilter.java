package com.caidaocloud.attendance.core.shiro.realm;

import com.caidao1.commons.utils.JSONUtils;
import com.caidaocloud.attendance.core.commons.utils.ObjectUtil;
import com.caidao1.commons.utils.ReturnMessage;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.codec.Base64;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.apache.shiro.web.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;

public class SimpleFormAuthenticationFilter extends FormAuthenticationFilter {
    private final static Logger LOGGER = LoggerFactory.getLogger(SimpleFormAuthenticationFilter.class);

    //服务器端登录成功/失败后重定向到的客户端地址
    private String redirectUrl;
    private String failureUrl;
    private String trialExpiresUrl;
//    @Autowired
//    private CaidaoShiroService caidaoShiroService;
//
//    @Autowired
//    private PasswordRuleService passwordRuleService;
//
//    @Autowired
//    private AdLoginService adLoginService;

    @Value("${enable_captcha_code:false}")
    private Boolean isValidateCaptchaCode;

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public void setFailureUrl(String failureUrl) {
        this.failureUrl = failureUrl;
    }

    public void setTrialExpiresUrl(String trialExpiresUrl) {
        this.trialExpiresUrl = trialExpiresUrl;
    }

    @Override
    protected AuthenticationToken createToken(String username, String password, ServletRequest request, ServletResponse response) {
        username = Base64.decodeToString(username);
        password = Base64.decodeToString(password);
        // 获取登录请求中用户输入的验证码
        String captchaCode = request.getParameter("captchaCode");
        //光大信托登陆逻辑
        String[] users = getSplitUserInfo(username);
        if (users == null || users.length < 2) {
            users = null;
        } else {
            String userName = ObjectUtil.getValue(users[1], "-");
            String gdxt = ObjectUtil.getValue(users[0], "-");
//            if ("GDXT".equals(gdxt)) {
//                adLoginService.authLogin(userName, password);
//            }
        }
        if(isValidateCaptchaCode){
//            return new CaptchaToken(username,password,captchaCode);
            return null;
        }else {
            return super.createToken(username, password, request, response);
        }
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        if (this.isLoginRequest(request, response)) {
            if (this.isLoginSubmission(request, response)) {
                return this.executeLogin(request, response);
            } else {
                if (isAjax(request)) {
                    response.setContentType("application/json; charset=utf-8");
                    PrintWriter out = response.getWriter();
                    ReturnMessage rt = new ReturnMessage(-9, "Session Lost");
                    out.append(JSONUtils.ObjectToJson(rt));
                    return false;
                }
                return true;
            }
        } else {
            saveRequestAndRedirectToLogin(request, response);
            return false;
        }
    }

    private boolean isAjax(ServletRequest request) {
        String header = ((HttpServletRequest) request).getHeader("X-Requested-With");
        if ("XMLHttpRequest".equalsIgnoreCase(header)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

//    @Override
//    protected boolean executeLogin(ServletRequest request, ServletResponse response) throws Exception {
//        AuthenticationToken token = createToken(request, response);
//        if (token == null) {
//            String msg = "createToken method implementation returned null. A valid non-null AuthenticationToken " +
//                    "must be created in order to execute a login attempt.";
//            throw new IllegalStateException(msg);
//        }
//        try {
//            Subject subject = getSubject(request, response);
//            // 先清除缓存记录
//            if(SecurityUtils.getSecurityManager() instanceof  CachingSecurityManager) {
//                CachingSecurityManager cachingSecurityManager = (CachingSecurityManager) SecurityUtils.getSecurityManager();
//                cachingSecurityManager.getCacheManager().getCache("authenticationCache").remove(token.getPrincipal());
//            }
//            subject.login(token);
//            return onLoginSuccess(token, subject, request, response);
//        } catch (AuthenticationException e) {
//            return onLoginFailure(token, e, request, response);
//        }
//
//    }

    @Override
    protected boolean onLoginSuccess(AuthenticationToken token, Subject subject, ServletRequest request,
                                     ServletResponse response) throws Exception {
        HttpServletRequest req = (HttpServletRequest) request;
        String account = req.getRemoteUser();
        account = new String(Base64.decode(account.getBytes()));
        // 根据账号名，查询账户信息
//        SysUserInfo userInfo = caidaoShiroService.getSysUserInfo(account);
        Boolean isTrialExpires = false;
//        if (userInfo != null) {
//            //把用户信息，设置到session里
//            caidaoShiroService.saveUserInfoToSession(req, userInfo, isTrialExpires);
//            // 登录成功清除登录失败次数
//            passwordRuleService.cleanErrorCountCache(userInfo.getUserid());
//        }
        // 需要清空
        if (isTrialExpires) {
            WebUtils.issueRedirect(request, response, trialExpiresUrl);
        } else {
            issueSuccessRedirect(request, response);
        }
        return false;
    }

    @Override
    protected boolean onLoginFailure(AuthenticationToken token, AuthenticationException ae, ServletRequest request, ServletResponse response) {
//        Subject subject = getSubject(request, response);
//        if (subject.isAuthenticated() || subject.isRemembered()) {
//            try {
//                //如果身份验证成功了 则也重定向到成功页面
//                issueSuccessRedirect(request, response);
//            } catch (Exception e11) {
//                e.printStackTrace();
//            }
//        } else {
//            try {
//                //登录失败时重定向到失败页面
//                //failureUrl = "https://www.52emp.com/open_idp/authorize?client_id=e140f0df-a0c3-4456-a68e-9b68887bb8d3&response_type=code&redirect_uri=http://localhost:9080/job_cloud/login/caidao";
//                WebUtils.issueRedirect(request, response, "/logout");
//                //WebUtils.redirectToSavedRequest(request, response, red);
//            } catch (IOException e1) {
//                e.printStackTrace();
//            }
//        }
        return super.onLoginFailure(token, ae, request, response);
    }

    public static String[] getSplitUserInfo(String account) {
        String[] userArr = null;
        if (account.lastIndexOf("-") != -1) {
            userArr = new String[2];
            userArr[0] = account.substring(0, account.lastIndexOf("-"));
            userArr[1] = account.substring(account.lastIndexOf("-") + 1);
        }
        return userArr;
    }
}
