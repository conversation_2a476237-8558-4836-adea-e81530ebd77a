package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.TravelCompensatoryItemDto;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.TravelCompensatoryReqDto;
import com.caidaocloud.dto.UserInfo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;

public interface ITravelCompensatoryService {

    PageList<TravelCompensatoryItemDto> getTravelCompensatoryList(TravelCompensatoryReqDto dto, PageBean pageBean, UserInfo userInfo);

    void autoTravelToCompensatory(String tenantId) throws Exception;

    void autoTravelToCompensatoryApproval(String tenantId, Long travelId, Long empId) throws Exception;

    void generateCompensatoryQuota(Long travelId) throws Exception;
}
