package com.caidaocloud.attendance.service.application.enums;

public enum SalaryWorkHourEnum {
    NO_PIECEWORK(0, "非计件"),
    PIECEWORK(1, "计件");

    private Integer index;
    private String name;

    public static String getName(int index) {
        for (SalaryWorkHourEnum c : SalaryWorkHourEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    SalaryWorkHourEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
