package com.caidaocloud.attendance.service.application.enums;

public enum WfFuncEnum {
    OVERTIME(1, "加班"),
    LEAVE(2, "请假"),
    REGISTER(41, "补卡"),
    TRAVEL(121, "出差"),
    SHIFT_CHANGE(278, "调班"),
    LEAVE_CANCEL(279, "销假");

    private Integer index;
    private String name;

    WfFuncEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (WfFuncEnum c : WfFuncEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
