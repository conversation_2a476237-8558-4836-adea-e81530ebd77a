package com.caidaocloud.attendance.service.application.enums;

/**
 * 考勤自定义逻辑配置-类型枚举
 */
public enum CustomLogicTypeEnum {
    SCRIPT("SCRIPT", "Java代码脚本"),
    SQL("SQL", "数据库脚本");

    private String code;
    private String name;

    CustomLogicTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CustomLogicTypeEnum getByCode(String code) {
        for (CustomLogicTypeEnum c : CustomLogicTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
