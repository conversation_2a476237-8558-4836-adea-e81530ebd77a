package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.interfaces.dto.WaLeaveTypeDefDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IWaLeaveTypeDefService {

    List<WaLeaveTypeDefDto> getWaLeaveTypeDefList();

    List<WaLeaveTypeDefDto> getWaLeaveTypeDefList(String tenantId);

    void delete(Integer id);

    void save(WaLeaveTypeDefDto dto);

    WaLeaveTypeDefDto getDetailList(Integer id);

    WaLeaveTypeDefDto getWaLeaveTypeDefByData(WaLeaveTypeDefDto dto);

    void syncWaLeaveTypeDefList();
}
