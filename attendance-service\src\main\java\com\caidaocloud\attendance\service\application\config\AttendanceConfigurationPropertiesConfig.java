package com.caidaocloud.attendance.service.application.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 考勤配置属性启用类
 * 确保 ConfigurationProperties 正常工作并提供配置验证
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(AttendanceMqProperties.class)
public class AttendanceConfigurationPropertiesConfig {

    @Autowired
    private AttendanceMqProperties mqProperties;

    @PostConstruct
    public void validateConfiguration() {
        log.info("============ 考勤消息队列配置信息 ============");
        log.info("启用死信队列: {}", mqProperties.isEnableDLQ());
        log.info("最大重试次数: {}", mqProperties.getMaxRetryCount());
        log.info("失败处理策略: {}", mqProperties.getFailureStrategy());
        log.info("消费者并发数: {}", mqProperties.getConsumer().getConcurrentConsumers());
        log.info("最大消费者并发数: {}", mqProperties.getConsumer().getMaxConcurrentConsumers());
        log.info("预取数量: {}", mqProperties.getConsumer().getPrefetchCount());
        log.info("消息TTL: {}", mqProperties.getQueue().getMessageTtl());
        log.info("自动删除队列: {}", mqProperties.getQueue().isAutoDelete());
        log.info("=============================================");

        // 验证配置的合理性
        validateMqConfiguration();
    }

    /**
     * 验证消息队列配置的合理性
     */
    private void validateMqConfiguration() {
        try {
            // 验证失败策略
            if (!mqProperties.isValidFailureStrategy()) {
                log.warn("Invalid failure strategy: {}. Using default: ACK", mqProperties.getFailureStrategy());
            }

            // 验证重试次数
            if (mqProperties.getMaxRetryCount() < 0) {
                log.warn("Invalid maxRetryCount: {}. Should be >= 0", mqProperties.getMaxRetryCount());
            } else if (mqProperties.getMaxRetryCount() > 10) {
                log.warn("MaxRetryCount is too high: {}. Consider reducing it for better performance",
                        mqProperties.getMaxRetryCount());
            }

            // 验证消费者配置
            if (mqProperties.getConsumer().getConcurrentConsumers() > mqProperties.getConsumer().getMaxConcurrentConsumers()) {
                log.warn("ConcurrentConsumers ({}) should not be greater than maxConcurrentConsumers ({})",
                        mqProperties.getConsumer().getConcurrentConsumers(),
                        mqProperties.getConsumer().getMaxConcurrentConsumers());
            }

            // 验证预取数量
            if (mqProperties.getConsumer().getPrefetchCount() > 50) {
                log.warn("PrefetchCount is high: {}. Consider reducing it for better load balancing",
                        mqProperties.getConsumer().getPrefetchCount());
            }

            // 死信队列配置验证 - 修复逻辑
            if (!mqProperties.isEnableDLQ()) {
                log.info("死信队列已禁用 - 失败消息将根据策略 '{}' 处理", mqProperties.getFailureStrategy());

                if (!"ACK".equalsIgnoreCase(mqProperties.getFailureStrategy())) {
                    log.warn("DLQ disabled but failure strategy is '{}'. Consider using 'ACK' for safer message handling",
                            mqProperties.getFailureStrategy());
                }
            }

            // TTL 配置验证 - 修复逻辑
            if (mqProperties.getQueue().getMessageTtl() > 0 && !mqProperties.isEnableDLQ()) {
                log.warn("Message TTL is set ({} ms) but DLQ is disabled. TTL will have no effect",
                        mqProperties.getQueue().getMessageTtl());
            }

            log.info("MQ configuration validation completed");

        } catch (Exception e) {
            log.error("Failed to validate MQ configuration: {}", e.getMessage(), e);
        }
    }
} 