package com.caidaocloud.attendance.service.application.enums;

import lombok.Getter;

@Getter
public enum AuthRoleScopeRestriction {
    MY_ORG_AND_BELONGINGS(ValueComponentEnum.NONE),
    MY_ORG(ValueComponentEnum.NONE),
    SELECTED_ORG(ValueComponentEnum.ORG),
    SELECTED_ORG_AND_BELONGINGS(ValueComponentEnum.ORG),
    // 按部门负责人查看
    SELECTED_LEADER(ValueComponentEnum.NONE),
    // 直接下级
    DIRECT_SUBORDINATE(ValueComponentEnum.NONE),
    // 未认证
    NO_AUTH(ValueComponentEnum.NONE),
    //查看本人
    MYSELF(ValueComponentEnum.NONE),
    //查看指定岗位
    SELECTED_POST(ValueComponentEnum.POST),
    //查看指定基准岗位
    SELECTED_BENCH_POST(ValueComponentEnum.BENCH_POST),
    //查看指定成本中心
    SELECTED_COST_CENTER(ValueComponentEnum.COST_CENTER),
    //指定工作地
    SELECTED_WORKPLACE(ValueComponentEnum.SELECTED_WORKPLACE),
    //指定HRBP
    SELECTED_HRBP(ValueComponentEnum.SELECTED_HRBP),
    //指定用工类型
    SELECTED_EMP_TYPE(ValueComponentEnum.SELECTED_EMP_TYPE)
    ;

    private ValueComponentEnum component;

    AuthRoleScopeRestriction(ValueComponentEnum component) {
        this.component = component;
    }
}
