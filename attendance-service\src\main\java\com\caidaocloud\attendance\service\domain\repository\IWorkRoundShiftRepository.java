package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaWorkRoundShiftDo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/1
 */
public interface IWorkRoundShiftRepository {
    List<WaWorkRoundShiftDo> selectListByWorkRoundId(Integer workRoundId);

    int deleteByWorkRoundId(Integer workRoundId);

    int save(WaWorkRoundShiftDo workRoundShiftDo);

    List<WaWorkRoundShiftDo> selectListByShiftDefId(Integer shiftDefId);

}
