package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.dto.PageResult;

import java.util.List;

public interface IAttendanceEmpGroupRepository<T> {

    T selectById(Long empGroupId);

    List<T> getList(T t);

    void insert(T t);

    void deleteById(Long empGroupId);

    void updateById(T t);

    PageResult<T> getByPage(long pageNo, long size, T t);

    T selectByKeyAndGroupType(String businessKey, String groupType, Long tenantId);

    void updateByBusKey(T t, Long defaultTenantId);

    List<T> selectByKeysAndGroupType(List<String> businessKeys, String groupType, Long tenantId);
}

