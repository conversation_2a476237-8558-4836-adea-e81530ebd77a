package com.caidaocloud.attendance.core.commons.service;

import com.caidao1.commons.session.AuthPageBean;
import com.caidao1.payroll.api.RemotePayConfigService;
import com.caidao1.payroll.common.PayCheckRuleDto;
import com.caidao1.payroll.common.PayPaybaseListDto;
import com.caidao1.payroll.common.PayStructureDetailDto;
import com.caidao1.payroll.mybatis.model.*;
import com.caidao1.report.dto.ExportFieldDto;
import com.caidao1.report.dto.PageBean;
import org.postgresql.util.PGobject;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class PayConfigService implements RemotePayConfigService {
    @Override
    public List<Map> getPayConstantList(Long corpId, String belongId, PageBean pageBean) {
        return null;
    }

    @Override
    public Integer savePayConstant(Long corpId, String belongId, PayConstant record) {
        return null;
    }

    @Override
    public void deletePayConstant(Integer id) {

    }

    @Override
    public List<Map> getPayBaseFixList(Long corpId, String belongId, boolean onlyNew, AuthPageBean pageBean) {
        return null;
    }

    @Override
    public Map<String, List<Map>> getAllPayBaseFixList(Long corpId) {
        return null;
    }

    @Override
    public Integer savePayPaybase(Long corpId, Long userId, PayPaybase record, boolean isSave) throws Exception {
        return null;
    }

    @Override
    public void changePayslip(Long corpId, Long userId, Long empid, Long starttime, PGobject pgobject) throws Exception {

    }

    @Override
    public void savePayPaybaseList(Long corpId, Long userId, PayPaybaseListDto payPaybaseListDto, boolean isSave) throws Exception {

    }

    @Override
    public void deletePayPaybase(Integer id) {

    }

    @Override
    public void deletePayPaybaseList(PayPaybaseListDto payPaybaseListDto, boolean isMonth, Integer paramr) throws Exception {

    }

    @Override
    public PayPaybase getPayPaybase(Integer id) {
        return null;
    }

    @Override
    public void deletePayBase(Integer id) {

    }

    @Override
    public ExportFieldDto getPayBaseMonthlyList(Long corpId, String belongId, AuthPageBean pageBean, Integer sysPeriodId) {
        return null;
    }

    @Override
    public ExportFieldDto getPayBaseMonthlyList(Long corpId, String belongId, AuthPageBean pageBean, Integer sysPeriodId, Integer paramr) {
        return null;
    }

    @Override
    public ExportFieldDto getPayBaseSiList(Long corpId, String belongId, AuthPageBean pageBean, Integer sysPeriodId) {
        return null;
    }

    @Override
    public List<Map> getPayItemTypeList(Long corpId, String belongId, PageBean pageBean) {
        return null;
    }

    @Override
    public Integer savePayItemType(Long corpId, String belongId, Long userId, PayItemType record) {
        return null;
    }

    @Override
    public void deletePayItemType(Integer id) {

    }

    @Override
    public List<Map> getPayStructureMainList(Long corpId, String belongId, String type, PageBean pageBean) {
        return null;
    }

    @Override
    public Integer savePayStrucMain(PayStructureMain record, Boolean initSocial, Long corpId, String belongId, Long userId) throws Exception {
        return null;
    }

    @Override
    public void deletePayStrucMain(String belongId, Integer id) {

    }

    @Override
    public PayStructureMain getPayStrucMain(Integer id) {
        return null;
    }

    @Override
    public Integer copyPayStructureMain(PayStructureMain record, String details, Long corpId, String belongId, Long userId) throws Exception {
        return null;
    }

    @Override
    public List<Map> getStrucDetailList(String belongId, PageBean pageBean, Integer strucMainId, Integer pid) {
        return null;
    }

    @Override
    public Map getFuncParamList(String belongId, Integer strucMainId) {
        return null;
    }

    @Override
    public PayStructureDetail getPayStructureDetail(Integer id) {
        return null;
    }

    @Override
    public Map getPayStrucDetail(Integer id) throws Exception {
        return null;
    }

    @Override
    public void deletePayStrucDetail(Integer id) throws Exception {

    }

    @Override
    public Integer savePayStrucDetail(Long userId, PayStructureDetail record, PayCheckRuleDto payCheckRuleDto) throws Exception {
        return null;
    }

    @Override
    public List<Map> getPaySobList(Long corpId, String belongId, PageBean pageBean) {
        return null;
    }

    @Override
    public Integer savePaySob(Long corpId, String belongId, Long userId, PaySob record, PayPeriod payPeriod) throws Exception {
        return null;
    }

    @Override
    public Map getPaySob(Integer id) throws Exception {
        return null;
    }

    @Override
    public void deletePaySob(Integer id) {

    }

    @Override
    public List<Map> getPaySobEmpList(PageBean pageBean, Integer sobId) {
        return null;
    }

    @Override
    public List<Map> getPaySobEmpCheckList(Long corpId, String belongId, AuthPageBean pageBean, Integer sobId) {
        return null;
    }

    @Override
    public void addPaySobEmp(Long userId, Integer sobId, String empIds) {

    }

    @Override
    public void deletePaySobEmp(Long corpId, Integer sobId, Integer id) {

    }

    @Override
    public void initPaySobEmpList(Long corpId, String belongId, Integer userId, Integer sobId, String datascope) {

    }

    @Override
    public void closePaySob(Long corpId, Integer sobId) throws Exception {

    }

    @Override
    public void closeSocialRun(Long corpId, Integer sobId) {

    }

    @Override
    public void lockPaySobCache(Long corpId, Integer sobId) {

    }

    @Override
    public void delPaySobCache(Long corpId, Integer sobId) {

    }

    @Override
    public void saveDetailRow(PayStructureDetailDto record) {

    }

    @Override
    public List<Map> getPayFuncList(Long corpId, String belongId, PageBean pageBean) {
        return null;
    }

    @Override
    public Integer savePayFunc(Long userId, PayFunc record) {
        return null;
    }

    @Override
    public PayFunc getPayFunc(Integer id) {
        return null;
    }

    @Override
    public void deletePayFunc(Integer id) {

    }

    @Override
    public Integer getPayStrucMainByEmp(Long corpId, String belongId, Long empid, Integer yearMonth) {
        return null;
    }

    @Override
    public void initAllSysPeriod(short start, short end) {

    }

    @Override
    public void initSysPeriod(Long corpid, String belongOrgId, short start, short end) {

    }

    @Override
    public List<Map> getEmpFixList(Long corpId, String belognId, Long empid, Integer strucMainId) {
        return null;
    }

    @Override
    public Map getEmpFixAmount(Long empid, Integer detailId) {
        return null;
    }

    @Override
    public List<PayStructureDetail> getStrucAllDetailList(Integer strucMainId) {
        return null;
    }

    @Override
    public Boolean startWorkFlow(Long corpId, String belongId, Long empid, Integer sobId) throws Exception {
        return null;
    }

    @Override
    public void finishPayrollApproval(Integer sobId, String choice) {

    }

    @Override
    public void getEmpGroup(Long corpId, String belongId, String workno, String type) {

    }

    @Override
    public Integer saveOrUpdFuncParam(Long belongId, Long userId, FuncParam record) {
        return null;
    }

    @Override
    public void deleteFuncParam(Integer id) {

    }

    @Override
    public List<Map> getSysFuncParamList(String belongId, PageBean pageBean) {
        return null;
    }
}
