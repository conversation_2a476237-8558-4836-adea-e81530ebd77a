package com.caidaocloud.attendance.service.application.dto.transfer;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TransferApplyDetailDto implements Serializable {

    @ApiModelProperty("主键Id")
    private String id;

    @ApiModelProperty("申请员工")
    private EmpSimple emp;

    @ApiModelProperty("异动配置ID")
    private String defId;

    @ApiModelProperty("异动类型Id")
    private String typeId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("挂载表单ID")
    private String formId;

    @ApiModelProperty("表单formValueId")
    private String formValueId;

    @ApiModelProperty("开启审批")
    private boolean approval;

    @ApiModelProperty("审批状态")
    private String approvalStatus;

    @ApiModelProperty("创建时间")
    private long createTime;

    @ApiModelProperty("更新时间")
    private long updateTime;

    @ApiModelProperty("异动数据")
    private List<TransferFieldDetailDto> data;

    @ApiModelProperty("工作流id")
    private String businessKey;

    @ApiModelProperty("生效日期")
    private String effectiveDate;
}
