package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ShiftGroupPublish {
    @Resource
    private MqMessageProducer<ShiftGroupMessage> producer;

    private final static String EXCHANGE = "attendance.shiftgroup.fac.direct.exchange";

    private final static String ROUTING_KEY = "routingKey.shiftgroup";

    public void publish(String msg) {
        ShiftGroupMessage message = new ShiftGroupMessage();
        message.setBody(msg);
        message.setExchange(EXCHANGE);
        message.setRoutingKey(ROUTING_KEY);
        producer.publish(message);
    }
}
