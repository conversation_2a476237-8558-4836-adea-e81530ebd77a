package com.caidaocloud.attendance.service.application.enums;

/**
 * 补打卡限制次数规则
 */
public enum BdkCountLimitRuleEnum {
    LIMIT_EMP(1, "仅限制员工申请"),
    LIMIT_EMP_AND_BATCH(2, "员工申请和考勤申请均限制");

    private Integer index;
    private String name;

    BdkCountLimitRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static BdkCountLimitRuleEnum getByValue(Integer value) {
        if (null == value) {
            return BdkCountLimitRuleEnum.LIMIT_EMP_AND_BATCH;
        }
        for (BdkCountLimitRuleEnum c : BdkCountLimitRuleEnum.values()) {
            if (c.getIndex().equals(value)) {
                return c;
            }
        }
        return BdkCountLimitRuleEnum.LIMIT_EMP_AND_BATCH;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
