package com.caidaocloud.attendance.service.application.config;

public class XxlJobClientConfigProperties {
    private static String loginUrl = "%s/login?userName=%s&password=%s";
    private static String jobInfoAddUrl = "%s/jobinfo/add";
    private static String jobInfoDeleteUrl = "%s/jobinfo/remove?id=%s";
    private static String jobInfoStartJobUrl = "%s/jobinfo/start?id=%s";
    private static String jobInfoStopJobUrl = "%s/jobinfo/stop?id=%s";
    private static String jobInfoUpdateUrl = "%s/jobinfo/update";
    private static String jobInfoPageListUrl = "%s/jobinfo/pageList?jobGroup=%s&triggerStatus=-1&executorHandler=%s";
    private static String jobGroupPageListUrl = "%s/jobgroup/pageList";

    private static String JOB_ADD_TEMPLATE = "jobGroup=%s&jobDesc=%s&author=Aaron&alarmEmail=<EMAIL>&scheduleType=CRON&scheduleConf=%s&cronGen_display=%s&glueType=BEAN&executorHandler=%s&executorParam=%s&executorRouteStrategy=FIRST&misfireStrategy=DO_NOTHING&executorBlockStrategy=SERIAL_EXECUTION&executorTimeout=0&executorFailRetryCount=0&glueRemark=GLUE代码初始化";
    private static String JOB_UPD_TEMPLATE = "id=%s&jobGroup=%s&jobDesc=%s&author=Aaron&alarmEmail=<EMAIL>&scheduleType=CRON&scheduleConf=%s&cronGen_display=%s&glueType=BEAN&executorHandler=%s&executorParam=%s&executorRouteStrategy=FIRST&misfireStrategy=DO_NOTHING&executorBlockStrategy=SERIAL_EXECUTION&executorTimeout=0&executorFailRetryCount=0&glueRemark=GLUE代码初始化";

    public static String getJobInfoAddParams(Integer jobGroupId, String jobDesc, String corn, String xxlJobHandler, String executorParam) {
        return String.format(JOB_ADD_TEMPLATE, jobGroupId, jobDesc, corn, corn, xxlJobHandler, executorParam);
    }

    public static String getJobInfoUpdParams(Integer xxlJobId, Integer jobGroupId, String jobDesc, String corn, String xxlJobHandler, String executorParam) {
        return String.format(JOB_UPD_TEMPLATE, xxlJobId, jobGroupId, jobDesc, corn, corn, xxlJobHandler, executorParam);
    }

    public static String getLoginUrl(String adminAddress, String name, String pwd) {
        return String.format(loginUrl, adminAddress, name, pwd);
    }

    public static String getJobInfoAddUrl(String adminAddress) {
        return String.format(jobInfoAddUrl, adminAddress);

    }

    public static String getJobInfoDeleteUrl(String adminAddress, Integer xxlJobId) {
        return String.format(jobInfoDeleteUrl, adminAddress, xxlJobId);

    }

    public static String getJobInfoStartJobUrl(String adminAddress, Integer xxlJobId) {
        return String.format(jobInfoStartJobUrl, adminAddress, xxlJobId);

    }

    public static String getJobInfoStopJobUrl(String adminAddress, Integer xxlJobId) {
        return String.format(jobInfoStopJobUrl, adminAddress, xxlJobId);

    }

    public static String getJobInfoUpdateUrl(String adminAddress) {
        return String.format(jobInfoUpdateUrl, adminAddress);

    }

    public static String getJobInfoPageListUrl(String adminAddress, Integer jobGroupId, String executorHandler) {
        return String.format(jobInfoPageListUrl, adminAddress, jobGroupId, executorHandler);
    }

    public static String getJobGroupPageListUrl(String adminAddress) {
        return String.format(jobGroupPageListUrl, adminAddress);

    }
}
