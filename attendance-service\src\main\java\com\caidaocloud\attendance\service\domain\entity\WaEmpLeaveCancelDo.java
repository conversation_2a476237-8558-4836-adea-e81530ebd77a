package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.service.domain.repository.IWaEmpLeaveCancelRepository;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.dto.PageResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Data
@Service
public class WaEmpLeaveCancelDo {
    private Long leaveCancelId;
    private Integer leaveId;
    private Integer leaveTypeId;
    private Long empid;
    private Long typeId;
    private String reason;
    private Integer timeUnit;
    private Float timeDuration;
    private Short periodType;
    private Short status;
    private Long lastApprovalTime;
    private Integer lastEmpid;
    private Integer revokeStatus;
    private String revokeReason;
    private String fileName;
    private String fileId;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private String tenantId;
    private Integer originalLeaveId;
    private String adjustTimeSlot;
    private String adjustDuration;

    //其他字段
    private String workNo;
    private String empName;
    private String orgName;
    private String fullPath;
    private String leaveTypeName;
    private String statusName;
    private String workCity;
    private Long hireDate;
    private String employType;
    private String processCode;
    private String i18nLeaveTypeName;

    @Autowired
    private IWaEmpLeaveCancelRepository waEmpLeaveCancelRepository;

    public PageResult<WaEmpLeaveCancelDo> pageList(AttendanceBasePage basePage, Map params) {
        return waEmpLeaveCancelRepository.selectPageList(basePage, params);
    }

    public void save(WaEmpLeaveCancelDo leaveCancelDo) {
        waEmpLeaveCancelRepository.save(leaveCancelDo);
    }

    public void update(WaEmpLeaveCancelDo leaveCancelDo) {
        waEmpLeaveCancelRepository.update(leaveCancelDo);
    }

    public List<WaEmpLeaveCancelDo> getListByLeaveId(String tenantId, Integer leaveId) {
        return waEmpLeaveCancelRepository.getListByLeaveId(tenantId, leaveId);
    }

    public WaEmpLeaveCancelDo getById(String tenantId, Long leaveCancelId) {
        return waEmpLeaveCancelRepository.getById(tenantId, leaveCancelId);
    }

    @CDText(exp = {"employType" + TextAspect.DICT_E, "workCity" + TextAspect.PLACE}, classType = WaEmpLeaveCancelDo.class)
    public WaEmpLeaveCancelDo getInfoById(String tenantId, Long leaveCancelId) {
        return waEmpLeaveCancelRepository.getInfoById(tenantId, leaveCancelId);
    }

    public List<WaEmpLeaveCancelDo> getListByLeaveIds(String tenantId, List<Integer> leaveIds) {
        return waEmpLeaveCancelRepository.getListByLeaveIds(tenantId, leaveIds);
    }
}