package com.caidaocloud.attendance.core.wa.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.wa.mybatis.mapper.WaShiftDefMapper;
import com.caidao1.wa.mybatis.mapper.WaWorkcalendarDetailMapper;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaWorkcalendarDetail;
import com.caidaocloud.attendance.core.wa.dto.WorkTimeCalendar;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class SmartWorkTimeService implements RemoteSmartWorkTimeService {

    @Autowired
    private WaShiftDefMapper waShiftDefMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private WaWorkcalendarDetailMapper waWorkcalendarDetailMapper;
    @Autowired
    private WaCommonService waCommonService;

    @Override
    public WaShiftDef getEmployeeShiftByDate(String tenantId, Long empId, Long workDate) {
        //班次调整查询
        Map<String, Integer> empChangeShiftMap = waCommonService.getEmpChangeShiftMapByTimeStamp(tenantId, new ArrayList<>(Arrays.asList(empId)), workDate, workDate);
        //班次替换
        if (empChangeShiftMap.containsKey(empId + "_" + workDate)) {
            Integer changeShiftId = empChangeShiftMap.get(empId + "_" + workDate);
            return waShiftDefMapper.selectByPrimaryKey(changeShiftId);
        }

        String ymd = DateUtil.convertDateTimeToStr(workDate, "yyyyMMdd", true);
        Integer ym = Integer.parseInt(ymd.substring(0, 6));

        WaShiftDef shiftDef = getRedisCacheShift(empId, ym, Integer.parseInt(ymd));
        if (shiftDef == null) {
            QueryWrapper<WaWorkcalendarDetail> workcalendarDetailQueryWrapper = new QueryWrapper<>();
            workcalendarDetailQueryWrapper.eq("empid", empId);
            workcalendarDetailQueryWrapper.eq("shift_month", ym);
            WaWorkcalendarDetail empCalendar = waWorkcalendarDetailMapper.selectOne(workcalendarDetailQueryWrapper);
            if (empCalendar != null) {
                Object worktimejsonb = empCalendar.getWorkTimeJsonb();

                if (worktimejsonb != null && worktimejsonb instanceof PGobject) {
                    worktimejsonb = ((PGobject) worktimejsonb).getValue();
                }
                if (worktimejsonb != null) {
                    redisTemplate.opsForValue().set(RedisKeyDefine.SHIFT_PLAN.concat(ym + "_").concat(empId.toString()), worktimejsonb);

                    return getShiftToWorkTimeJson(worktimejsonb, ym, Integer.parseInt(ymd));
                }
            }
            return null;
        }
        return shiftDef;
    }

    @Override
    public WaShiftDef getEmpRegisterShiftByDate(String tenantId, Long empId, Long workDate) {
        //班次调整查询
        Map<String, Integer> empChangeShiftMap = waCommonService.getEmpChangeShiftMapByTimeStamp(tenantId, new ArrayList<>(Arrays.asList(empId)), workDate, workDate);
        //班次替换
        if (empChangeShiftMap.containsKey(empId + "_" + workDate)) {
            Integer changeShiftId = empChangeShiftMap.get(empId + "_" + workDate);
            return waShiftDefMapper.selectByPrimaryKey(changeShiftId);
        }
        boolean enableSmartShift = validateEnableSmartShiftPlan(tenantId);
        if (enableSmartShift) {
            String ymd = DateUtil.convertDateTimeToStr(workDate, "yyyyMMdd", true);
            Integer ym = Integer.parseInt(ymd.substring(0, 6));

            WaShiftDef shiftDef = getRedisCacheShift(empId, ym, Integer.parseInt(ymd));
            if (shiftDef == null) {
                QueryWrapper<WaWorkcalendarDetail> workcalendarDetailQueryWrapper = new QueryWrapper<>();
                workcalendarDetailQueryWrapper.eq("empid", empId);
                workcalendarDetailQueryWrapper.eq("shift_month", ym);
                WaWorkcalendarDetail empCalendar = waWorkcalendarDetailMapper.selectOne(workcalendarDetailQueryWrapper);
                if (empCalendar != null) {
                    Object worktimejsonb = empCalendar.getWorkTimeJsonb();

                    if (worktimejsonb != null && worktimejsonb instanceof PGobject) {
                        worktimejsonb = ((PGobject) worktimejsonb).getValue();
                    }
                    if (worktimejsonb != null) {
                        redisTemplate.opsForValue().set(RedisKeyDefine.SHIFT_PLAN.concat(ym + "_").concat(empId.toString()), worktimejsonb);

                        return getShiftToWorkTimeJson(worktimejsonb, ym, Integer.parseInt(ymd));
                    }
                }
                return null;
            }
            return shiftDef;

        } else {
            //非门店考勤
            Map hMap = new HashMap();
            hMap.put("empId", empId);
            hMap.put("workDate", workDate);
            hMap.put("calendarDate", workDate);
            List<WaShiftDef> waShiftDefList = waShiftDefMapper.getWaShiftDef(hMap);
            if (CollectionUtils.isNotEmpty(waShiftDefList)) {
                return waShiftDefList.get(0);
            }
        }
        return null;
    }

    private WaShiftDef getShiftToWorkTimeJson(Object worktimejsonb, Integer ym, Integer ymd) {
        if (worktimejsonb == null) {
            return null;
        }
        List<WorkTimeCalendar> list = WorkTimeCalendar.convertWorkTimeJsonToObject(null, worktimejsonb.toString(), ym);
        if (CollectionUtils.isNotEmpty(list)) {
            for (WorkTimeCalendar calendar : list) {
                if (calendar.getDate().equals(ymd)) {
                    Integer shiftid = calendar.getId();
                    return waShiftDefMapper.selectByPrimaryKey(shiftid);
                }
            }
        }
        return null;
    }

    public WaShiftDef getRedisCacheShift(Long empId, Integer ym, Integer ymd) {
        String worktimejsonb = (String) redisTemplate.opsForValue().get(RedisKeyDefine.SHIFT_PLAN.concat(ym + "_").concat(empId.toString()));
        if (StringUtils.isBlank(worktimejsonb)) {
            return null;
        }

        return getShiftToWorkTimeJson(worktimejsonb, ym, ymd);
    }

    @Override
    public boolean validateEnableSmartShiftPlan(String tenantId) {
        String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + tenantId + "_ENABLE_SMART_SHIFT_PLAN");
        if (StringUtils.isNotBlank(isOpen) && "1".equals(isOpen)) {
            return true;
        }
        return false;
    }


    /**
     * 获取员工指定月的排班信息
     *
     * @param empId
     * @param ym
     * @return
     */
    @Override
    public List<WorkTimeCalendar> getEmpWorkTimeCalendarByMonth(Long empId, Integer ym) {
        String worktimejsonb = (String) redisTemplate.opsForValue().get(RedisKeyDefine.SHIFT_PLAN.concat(ym + "_").concat(empId.toString()));
        List<WorkTimeCalendar> list = WorkTimeCalendar.convertWorkTimeJsonToObject(empId, worktimejsonb.toString(), ym);
        return list;
    }

    @Override
    public List<WorkTimeCalendar> getEmpWorkTimeCalendarByYm(Long empId, Integer ym) {
        String worktimejsonbData = (String) redisTemplate.opsForValue().get(RedisKeyDefine.SHIFT_PLAN.concat(ym + "_").concat(empId.toString()));
        if (StringUtils.isNotBlank(worktimejsonbData)) {
            return WorkTimeCalendar.convertWorkTimeJsonToObject(empId, worktimejsonbData.toString(), ym);
        } else {
            QueryWrapper<WaWorkcalendarDetail> workcalendarDetailQueryWrapper = new QueryWrapper<>();
            workcalendarDetailQueryWrapper.eq("empid", empId);
            workcalendarDetailQueryWrapper.eq("shift_month", ym);
            WaWorkcalendarDetail empCalendar = waWorkcalendarDetailMapper.selectOne(workcalendarDetailQueryWrapper);
            if (empCalendar != null) {
                Object worktimejsonb = empCalendar.getWorkTimeJsonb();
                if (worktimejsonb != null && worktimejsonb instanceof PGobject) {
                    worktimejsonb = ((PGobject) worktimejsonb).getValue();
                }
                if (worktimejsonb != null) {
                    redisTemplate.opsForValue().set(RedisKeyDefine.SHIFT_PLAN.concat(ym + "_").concat(empId.toString()), worktimejsonb);
                    return WorkTimeCalendar.convertWorkTimeJsonToObject(empId, worktimejsonb.toString(), ym);
                }
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<WorkTimeCalendar> getEmployeeShiftList(String tenantId, List<Long> empIds, Long startDate, Long endDate) {
        Integer ymstart = Integer.valueOf(DateUtil.parseDateToPattern(new Date(startDate * 1000), "yyyyMM"));
        Integer ymend = Integer.valueOf(DateUtil.parseDateToPattern(new Date(endDate * 1000), "yyyyMM"));
        QueryWrapper<WaWorkcalendarDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("empid", empIds);
        queryWrapper.between("shift_month", ymstart, ymend);
        queryWrapper.eq("belong_org_id", tenantId);
        List<WaWorkcalendarDetail> empCalendars = waWorkcalendarDetailMapper.selectList(queryWrapper);
        List<WorkTimeCalendar> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empCalendars)) {
            for (WaWorkcalendarDetail ec : empCalendars) {
                Object worktimejsonb = ec.getWorkTimeJsonb();
                if (worktimejsonb != null && worktimejsonb instanceof PGobject) {
                    worktimejsonb = ((PGobject) worktimejsonb).getValue();
                }

                if (worktimejsonb != null) {
                    List<WorkTimeCalendar> calendarList = WorkTimeCalendar.convertWorkTimeJsonToObject(ec.getEmpid(), worktimejsonb.toString(), ec.getShiftMonth());
                    list.addAll(calendarList);
                }
            }
        }
        return list;
    }
}
