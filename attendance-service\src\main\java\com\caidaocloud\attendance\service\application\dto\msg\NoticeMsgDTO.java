package com.caidaocloud.attendance.service.application.dto.msg;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class NoticeMsgDTO {

    private String tenantId;

    private String eventEmpId;

    private Long userId;

    /**
     * 批量消息的批次id
     */
    private String mdc;

    /**
     * 发送途径：1,2,3,4
     * 3：app 通知
     * 2：系统通知
     * 0：短信通知
     * 1：邮件通知
     * <p>
     * 多个发送途径，英文逗号分割
     */
    private List<String> channel;

    /**
     * 邮件消息
     */
    private EmailMsgDTO emailMsg;

    /**
     * 短信消息
     */
    private MessageDTO message;

    /**
     * app消息
     */
    private List<AppMsgSenderDTO> appMsg;

    /**
     * 钉钉工作通知 消息
     */
    private DingtalkMsgDto dingTalkMsg;

    /**
     * 钉钉待办通知消息
     */
    private DingtalkToDoNoticeDto dingtalkToDoNoticeDto;

    // 企微消息
    private WxOpenNoticeDto wxNoticeMsg;

    /**
     * 业务数据ID
     */
    private String bid;

    /**
     * 消息来源
     */
    private String msgFrom;

    /**
     * 消息设置
     */
    private String msgConfig;

    /**
     * 更改钉钉待办任务状态的字段, 1:代表去message服务更新任务状态
     */
    private String dingStatus;

    /**
     * 自定义参数
     */
    private Map<String, Object> ext;
}
