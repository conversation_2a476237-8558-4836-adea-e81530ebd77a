package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.attendance.service.infrastructure.repository.mapper.AnalysisRuleMapper;
import com.caidaocloud.attendance.service.interfaces.dto.payroll.OtAmountSearchDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PayrollService {
    @Resource
    private AnalysisRuleMapper analysisRuleMapper;

    public List getAllOtAmount(OtAmountSearchDto searchDto){
        Map params = new HashMap();
        params.put("tmStartDay", searchDto.getTmStartDay());
        params.put("tmEndDay", searchDto.getTmEndDay());
        params.put("overtimeType", searchDto.getOvertimeType());
        params.put("compensateType", searchDto.getCompensateType());

        List<List> fixedGroupEmpids = fixedGroupList(searchDto.getEmpids(), 500);
        List<Map> response = new ArrayList<>(searchDto.getEmpids().size());
        List<Map> data = null;
        for (List empids : fixedGroupEmpids) {
            params.put("empids", empids);
            data = analysisRuleMapper.getAllOtAmount(params);
            if(null != data && !data.isEmpty()){
                response.addAll(data);
            }
        }

        return response;
    }

    public List getAllLeaveTypeAmount(OtAmountSearchDto searchDto){
        Map params = new HashMap();
        params.put("tmStartDay", searchDto.getTmStartDay());
        params.put("tmEndDay", searchDto.getTmEndDay());
        params.put("leaveTypeId", searchDto.getLeaveTypeId());

        List<List> fixedGroupEmpids = fixedGroupList(searchDto.getEmpids(), 500);
        List<Map> data = null;
        List<Map> response = new ArrayList<>(searchDto.getEmpids().size());
        for (List empids : fixedGroupEmpids) {
            params.put("empids", empids);
            data = analysisRuleMapper.getLeaveTypeAmountByEmpids(params);
            if(null != data && !data.isEmpty()){
                response.addAll(data);
            }
        }

        return response;
    }

    public List getAllEmpQuotaAmount(OtAmountSearchDto searchDto){
        Map params = new HashMap();
        params.put("tmEndDay", searchDto.getTmEndDay());
        params.put("leaveTypeId", searchDto.getLeaveTypeId());

        List<List> fixedGroupEmpids = fixedGroupList(searchDto.getEmpids(), 500);
        List<Map> data = null;
        List<Map> response = new ArrayList<>(searchDto.getEmpids().size());
        for (List empids : fixedGroupEmpids) {
            params.put("empids", empids);
            data = analysisRuleMapper.getAllEmpQuotaAmount(params);
            if(null != data && !data.isEmpty()){
                response.addAll(data);
            }
        }

        return response;
    }

    private List<List> fixedGroupList(List source, int n) {
        List<List> result = new ArrayList<>();
        int remainder = source.size() % n;
        int size = (source.size() / n);
        for (int i = 0; i < size; i++) {
            List subset = source.subList(i * n, (i + 1) * n);
            result.add(subset);
        }

        if (remainder > 0) {
            List subset = source.subList(size * n, size * n + remainder);
            result.add(subset);
        }
        return result;
    }

}
