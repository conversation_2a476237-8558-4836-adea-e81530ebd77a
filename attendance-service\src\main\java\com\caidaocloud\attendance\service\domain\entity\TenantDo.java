package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.ITenantRepository;
import com.caidaocloud.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Data
@Service
public class TenantDo {

    @Autowired
    private ITenantRepository tenantRepository;

    public void initTable(String initSQL) {
        if (!StringUtil.isNotBlank(initSQL)) {
            return;
        }
        tenantRepository.initTable(initSQL);
    }
}
