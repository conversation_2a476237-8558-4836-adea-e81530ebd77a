package com.caidaocloud.attendance.service.domain.entity;

import com.alibaba.fastjson.JSON;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.repository.IRegisterRecordRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 员工打卡记录
 *
 * <AUTHOR>
 * @Date 2021/3/23
 */
@Slf4j
@Data
@Service
public class WaRegisterRecordDo {
    private Integer recordId;
    private Long empid;
    private Integer registerType;
    private String resultDesc;
    private Integer resultType;
    private String reason;
    private String regAddr;
    private Long regDateTime;
    private BigDecimal lng;
    private BigDecimal lat;
    private Long belongDate;
    private String mobDeviceNum;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private String normalAddr;
    private String normalDate;
    private Integer type;
    private String owRmk;
    private String picList;
    private Long hisRegTime;
    private Integer shiftDefId;
    private Boolean isDeviceError;
    private String oriMobDeviceNum;
    private Boolean isWorkflow;
    private Integer approvalStatus;
    private String approvalReason;
    private String filePath;
    private String revokeReason;
    private Long siteId;
    private String province;
    private String city;
    private Integer startTime;
    private Integer endTime;
    private Long corpid;
    private String belongOrgId;
    private Long lastApprovalTime;
    /**
     * 记录是否有效: 0 无效 、1 有效
     */
    private Integer ifValid;
    /**
     * 打卡地点状态:0无效1有效
     */
    private Integer clockSiteStatus;
    private Long bdkRecordId;
    private Integer sourceFromType;
    private Integer dataType;
    /**
     * 调整状态：0 未调整， 1 已调整
     */
    private Short adjustStatus;

    // 其他表字段
    private String workno;
    private String empName;
    private Long orgid;
    private Long approvalTime;
    private String orgName;
    private String fullPath;
    @ApiModelProperty("员工类型")
    private Long empStyle;
    @ApiModelProperty("员工类型名称")
    private String empStyleName;
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("员工状态")
    private Integer empStatus;
    @ApiModelProperty("员工状态名称")
    private String empStatusName;
    @ApiModelProperty("用户名称")
    private String userName;
    private String registerTypeName;
    private String shortName;
    private String statusName;
    private String files;
    private String fileNames;
    private String workCity;

    @Autowired
    private IRegisterRecordRepository registerRecordRepository;

    public AttendancePageResult<WaRegisterRecordDo> getRegisterPageList(RegisterRecordRequestDto requestDto) {
        return registerRecordRepository.getRegisterPageList(requestDto);
    }

    @CDText(exp = {"workCity" + TextAspect.PLACE}, classType = WaRegisterRecordDo.class)
    public WaRegisterRecordDo getRegisterDetailById(Long corpId, Long registerId) {
        Map map = registerRecordRepository.getRegisterDetailById(corpId, registerId);
        if (null == map) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(map), WaRegisterRecordDo.class);
    }

    public List<WaRegisterRecordDo> getEmpRegisterRecordList(Long empId, Long startDate, Long endDate, List<Integer> typeList, List<Integer> statusList) {
        return registerRecordRepository.getEmpRegisterRecordList(empId, startDate, endDate, typeList, statusList);
    }

    public WaRegisterRecordDo getWaRegisterRecordDoById(Integer recordId) {
        return registerRecordRepository.getWaRegisterRecordDoById(recordId);
    }

    public int updateByPrimaryKeySelective(WaRegisterRecordDo waRegisterRecordDo) {
        return registerRecordRepository.updateByPrimaryKeySelective(waRegisterRecordDo);
    }

    public WaParseGroup selectAttendanceRuleByEmpidAndDate(String belongOrgId, Long empid, Long date) {
        return registerRecordRepository.selectAttendanceRuleByEmpidAndDate(belongOrgId, empid, date);
    }

    public List<WaRegisterRecordDo> getAllRecordListByBelongDate(String belongOrgId, Long empId, Long belongDate) {
        return registerRecordRepository.selectAllRecordListByBelongDate(belongOrgId, empId, belongDate);
    }

    public List<WaRegisterRecordDo> getAllRecordListByDateRange(String belongOrgId, List<Long> empIds, Long startDate, Long endDate) {
        return registerRecordRepository.getAllRecordListByDateRange(belongOrgId, empIds, startDate, endDate);
    }

    public List<WaRegisterRecordDo> selectRegListByIds(String belongOrgId, List<Integer> registerIds) {
        return registerRecordRepository.selectRegListByIds(belongOrgId, registerIds);
    }

    public AttendancePageResult<WaRegisterRecordDo> getRegisterRecordPageListByEmpId(RegisterRecordRequestDto requestDto, Long empId) {
        return registerRecordRepository.getRegisterPageListByEmpId(requestDto, empId);
    }

    public AttendancePageResult<WaRegisterRecordDo> getAllRegisterRecordPageList(AttendanceBasePage basePage, String belongOrgId, List<Long> empIds,
                                                                                 Long startDate, Long endDate, List<Integer> typeList, Integer ifValid, Integer clockSiteStatus, List<Integer> approvalStatusList) {
        return registerRecordRepository.getAllRegisterRecordPageList(basePage, belongOrgId, empIds, startDate, endDate, typeList, ifValid, clockSiteStatus, approvalStatusList);
    }

    public AttendancePageResult<WaRegisterRecordDo> getAllRegisterRecordPageListNonAttendanceAnalyze(AttendanceBasePage basePage, String belongOrgId, List<Long> empIds,
                                                                                 Long startDate, Long endDate, List<Integer> typeList, Integer ifValid, Integer clockSiteStatus, List<Integer> approvalStatusList) {
        return registerRecordRepository.getAllRegisterRecordPageListNonAttendanceAnalyze(basePage, belongOrgId, empIds, startDate, endDate, typeList, ifValid, clockSiteStatus, approvalStatusList);
    }

    /**
     * 根据日期查询员工工作打卡记录
     *
     * @param empid
     * @param daytime
     * @param includeOutReg 是否保存外勤打卡
     * @return
     */
    public List<Map> getEmpWorkTimeRecordByDay(Long empid, Long daytime, boolean includeOutReg) {
        return registerRecordRepository.getEmpWorkTimeRecordByDay(empid, daytime, includeOutReg);
    }

    public List<WaRegisterRecordDo> getEmpBdkRegisterList(String belongOrgId, List<Long> empIdList, Long startDate, Long endDate) {
        return registerRecordRepository.getEmpBdkRegisterList(belongOrgId, empIdList, startDate, endDate);
    }

    public List<WaRegisterRecordDo> getEmpBdkRegisterList(String belongOrgId, List<Long> empIdList, Long belongDate) {
        return registerRecordRepository.getEmpBdkRegisterList(belongOrgId, empIdList, belongDate);
    }

    public List<WaRegisterRecordDo> getRegisterRecordPageList(PageBean pageBean, String belongOrgId, Long startTime, Long endTime,
                                                              List<Long> empIds, List<Integer> types, Integer clockSiteStatus) {
        return registerRecordRepository.getRegisterRecordPageList(pageBean, belongOrgId, startTime, endTime, empIds, types,
                clockSiteStatus);
    }

    public AttendancePageResult<WaRegisterRecordDo> getPageList(AttendanceBasePage basePage,
                                                                String belongOrgId, Long startTime, Long endTime,
                                                                List<Long> empIds, List<Integer> types, Integer clockSiteStatus) {
        return registerRecordRepository.getPageList(basePage, belongOrgId, startTime, endTime, empIds, types,
                clockSiteStatus);
    }

    public List<Long> getRegEmpIdList(String belongOrgId, Long startTime, Long endTime) {
        return registerRecordRepository.selectRegEmpIdList(belongOrgId, startTime, endTime);
    }

    public List<EmpParseGroup> selectEmpParseGroupListByDate(String belongOrgId, List<Long> empIds, Long date) {
        return registerRecordRepository.selectEmpParseGroupListByDate(belongOrgId, empIds, date);
    }

    public List<EmpParseGroup> selectEmpParseGroupListByDateRange(String belongOrgId, List<Long> empIds, Long startDate, Long endDate) {
        return registerRecordRepository.selectEmpParseGroupListByDateRange(belongOrgId, empIds, startDate, endDate);
    }

    public int updateValidState(String belongOrgId, List<Integer> ids, Long userId, Integer ifValid) {
        return registerRecordRepository.updateValidStateByIds(belongOrgId, ids, userId, ifValid);
    }

    public void deleteByIds(String belongOrgId, List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        registerRecordRepository.deleteByIds(belongOrgId, ids);
    }

    public void updateClockSiteStatus(String belongOrgId, List<Integer> recordIds, Integer clockSiteStatus) {
        if (CollectionUtils.isEmpty(recordIds)) {
            return;
        }
        registerRecordRepository.updateClockSiteStatus(belongOrgId, recordIds, clockSiteStatus);
    }

    public List<WaRegisterRecordDo> getWaRegisterRecordByBdkId(String tenantId, Long recordId) {
        return registerRecordRepository.getWaRegisterRecordByBdkId(tenantId, recordId);
    }

    public void batchSave(List<WaRegisterRecordDo> recordDoLists) {
        registerRecordRepository.batchSave(recordDoLists);
    }
}
