package com.caidaocloud.attendance.service.application.config;

import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 多节点打卡分析队列的完整 RabbitMQ 配置
 * 支持通过 Nacos 配置控制是否启用死信队列功能
 */
@Configuration
public class MultiNodeClockAnalyseRabbitConfig {

    // 主要的队列和交换机名称
    public static final String MAIN_QUEUE = "attendance.clock.analyse.multinode.queue";
    public static final String MAIN_EXCHANGE = "attendance.clock.analyse.pc.fac.direct.exchange";
    public static final String MAIN_ROUTING_KEY = "routingKey.clock.analyse.multinode";

    // 死信队列和交换机名称
    public static final String DLQ_QUEUE = "attendance.clock.analyse.multinode.dlq";
    public static final String DLQ_EXCHANGE = "attendance.clock.analyse.multinode.dlq.exchange";
    public static final String DLQ_ROUTING_KEY = "routingKey.clock.analyse.multinode.dlq";

    @Autowired
    private AttendanceMqProperties mqProperties;

    /**
     * 声明主交换机
     */
    @Bean
    public DirectExchange mainExchange() {
        return new DirectExchange(MAIN_EXCHANGE, true, false);
    }

    /**
     * 声明死信交换机（仅在启用DLQ时创建）
     */
    @Bean
    @ConditionalOnProperty(name = "attendance.mq.enableDLQ", havingValue = "true")
    public DirectExchange dlqExchange() {
        return new DirectExchange(DLQ_EXCHANGE, true, false);
    }

    /**
     * 声明主队列
     * 根据配置决定是否添加死信队列参数
     */
    @Bean
    public Queue mainQueue() {
        QueueBuilder queueBuilder = QueueBuilder.durable(MAIN_QUEUE);
        
        if (mqProperties.isEnableDLQ()) {
            // 启用死信队列：添加死信交换机参数
            queueBuilder.withArgument("x-dead-letter-exchange", DLQ_EXCHANGE)
                        .withArgument("x-dead-letter-routing-key", DLQ_ROUTING_KEY);
            
            // 如果配置了消息TTL，添加TTL参数
            if (mqProperties.getQueue().getMessageTtl() > 0) {
                queueBuilder.withArgument("x-message-ttl", mqProperties.getQueue().getMessageTtl());
            }
        }
        
        // 设置自动删除参数
        if (mqProperties.getQueue().isAutoDelete()) {
            queueBuilder.autoDelete();
        }
        
        return queueBuilder.build();
    }

    /**
     * 声明死信队列（仅在启用DLQ时创建）
     */
    @Bean
    @ConditionalOnProperty(name = "attendance.mq.enableDLQ", havingValue = "true")
    public Queue dlqQueue() {
        QueueBuilder queueBuilder = QueueBuilder.durable(DLQ_QUEUE);
        
        // 死信队列通常不需要自动删除，保持数据安全
        return queueBuilder.build();
    }

    /**
     * 绑定主队列到主交换机
     */
    @Bean
    public Binding mainBinding() {
        return BindingBuilder.bind(mainQueue()).to(mainExchange()).with(MAIN_ROUTING_KEY);
    }

    /**
     * 绑定死信队列到死信交换机（仅在启用DLQ时创建）
     */
    @Bean
    @ConditionalOnProperty(name = "attendance.mq.enableDLQ", havingValue = "true")
    public Binding dlqBinding(Queue dlqQueue, DirectExchange dlqExchange) {
        return BindingBuilder.bind(dlqQueue).to(dlqExchange).with(DLQ_ROUTING_KEY);
    }
} 