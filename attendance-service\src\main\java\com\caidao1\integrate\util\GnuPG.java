package com.caidao1.integrate.util;

import java.io.*;

public class GnuPG {

    private static final String DecryptCommand = "echo '%s' | gpg --passphrase-fd 0 -o \"%s\" --batch -d \"%s\"";

    public static boolean decrypt(String file, String outfile, String passPhrase) {
        String fullCommand = String.format(DecryptCommand, passPhrase, outfile, file);
        System.out.println(fullCommand);
        try {
            String line = null;
            String[] cmdline = {"sh", "-c", fullCommand};
            Process proc = Runtime.getRuntime().exec(cmdline);
            InputStream stderr = proc.getErrorStream();
            InputStreamReader esr = new InputStreamReader(stderr);
            BufferedReader ebr = new BufferedReader(esr);
            while ((line = ebr.readLine()) != null)
                System.out.println(line);

            InputStream stdout = proc.getInputStream();
            InputStreamReader osr = new InputStreamReader(stdout);
            BufferedReader obr = new BufferedReader(osr);
            while ((line = obr.readLine()) != null)
                System.out.println(line);
            int exitVal = proc.waitFor();
        } catch (Exception io) {
            System.out.println("io Error" + io.getMessage());
            return false;
        }

        return true;
    }

    public static void main(String args[]) throws IOException {
        // use this to check:
        String folder = "/Users/<USER>/Documents/";

        GnuPG.decrypt(folder + "1.txt", folder + "2.txt", "china@payroll");

//        FileUtils.forceDelete(new File(folder + "1.txt"));
//        FileUtils.moveFile(new File(folder + "2.txt"), new File(folder + "1.txt"));
    }
}
