package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

/**
 * 假期类型时间单位类型枚举
 */
public enum LeaveTypeUnitEnum {
    DAY(1, "天", AttendanceCodes.DAY),
    HOUR(2, "小时", AttendanceCodes.HOUR);

    private Integer index;
    private String name;
    private Integer code;

    LeaveTypeUnitEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(int index) {
        for (LeaveTypeUnitEnum c : LeaveTypeUnitEnum.values()) {
            if (c.getIndex() == index) {
                String msg = "";
                try {
                    msg = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(msg)) {
                    return msg;
                }
                return c.name;
            }
        }
        return "";
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
