package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.LeaveQuotaConfigDo;

import java.util.List;
import java.util.Map;

/**
 * 假期设置-额度规则
 *
 * <AUTHOR>
 * @Date 2021/7/27
 */
public interface ILeaveQuotaConfigRepository {

    int save(LeaveQuotaConfigDo configDo);

    int update(LeaveQuotaConfigDo configDo);

    LeaveQuotaConfigDo getById(String tenantId, Integer leaveTypeId);

    List<LeaveQuotaConfigDo> getConfigListByIds(String tenantId, List<Integer> leaveTypeIds);

    List<Map> getEmpFixedQuotaConfigList(String belongOrgId, Long curDate, String datafilter);

    List<LeaveQuotaConfigDo> getByLeaveTypeId(String tenantId, Integer leaveTypeId);

    LeaveQuotaConfigDo getConfigById(String tenantId, Long configId);

    void delete(String tenantId, Long configId);

    void updateBatch(List<LeaveQuotaConfigDo> list);

    List<LeaveQuotaConfigDo> getLeaveQuotaConfigs(String tenantId, Integer leaveTypeId, Long configId, String ruleName);

    List<LeaveQuotaConfigDo> getLeaveQuotaConfigByIds(String tenantId, List<Long> configIds);

    List<Map> loadStatus(List<Long> configIds);

    void enableQuotaConfig(Long quotaConfigId);

    void disableQuotaConfig(Long quotaConfigId);

    List<LeaveQuotaConfigDo> selectListByTenantId(String tenantId);
}
