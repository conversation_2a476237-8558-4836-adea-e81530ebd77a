package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravel;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface IWaEmpTravelRepository {
    PageResult<WaEmpTravelDo> getEmpTravelPageList(AttendanceBasePage basePage, Map params);

    PageResult<WaEmpTravelDo> getEmpTravelPageListOfPortal(QueryPageBean queryPageBean, Boolean ifBatch);

    WaEmpTravelDo getWaEmpTravelById(Long id, Long corpId);

    WaEmpTravelDo getWaEmpTravelByPrimaryKey(Long id);

    int save(WaEmpTravelDo empTravelDo);

    int update(WaEmpTravelDo travelDo);

    Integer checkEmpTravelTimeRepeat(Long empId, Long startTime, Long endTime);

    List<WaEmpTravelDo> getTravelList(String belongOrgId, Long empId, Long dayTime, Long endTime);

    List<WaEmpTravel> getTravelInfoByTravelTypeId(String tenantId, Long travelTypeId);

    List<WaEmpTravelDo> getEmpTravelByEmpIds(List<Long> empIds, Long travelDate);

    int delete(Long travelId);

    WaEmpTravelDo getEmpTravelRevokeById(String tenantId, Long id);

    List<WaEmpTravelDo> selectListByBatchId(String tenantId, Long batchTravelId);

    int deleteByTravelIds(List<Long> travelIds);

    PageResult<WaEmpTravelDo> getEmpTravelPageListOfPortal(QueryPageBean queryPageBean, BusinessCodeEnum workflowEnum);

    List<WaEmpTravelDo> getDurationOfTravel(List<Long> travelIdList);
}
