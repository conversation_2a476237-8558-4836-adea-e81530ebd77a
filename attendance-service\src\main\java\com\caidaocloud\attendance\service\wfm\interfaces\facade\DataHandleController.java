package com.caidaocloud.attendance.service.wfm.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.ListTool;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.core.wa.vo.MultiWorkTimeInfoSimpleVo;
import com.caidaocloud.attendance.service.schedule.application.service.utils.DateParseUtil;
import com.caidaocloud.attendance.service.schedule.domain.entity.WaEmpScheduleDo;
import com.caidaocloud.attendance.service.schedule.domain.entity.WaEmpScheduleDraftDo;
import com.caidaocloud.attendance.service.schedule.interfaces.vo.schedule.EmpScheduleItemVo;
import com.caidaocloud.attendance.service.schedule.interfaces.vo.schedule.ScheduleItemVo;
import com.caidaocloud.attendance.service.wfm.application.dto.WfmScanCheckIn;
import com.caidaocloud.attendance.service.wfm.application.dto.WfmScanCheckInDto;
import com.caidaocloud.attendance.service.wfm.application.service.WfmShiftService;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.DataHandleDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RestController
@RequestMapping("/api/attendance/wfm/data/v1")
public class DataHandleController {

    private final static String WFM_REGISTER_RECORD_IDENTIFIER = "entity.wfm.ScanWorkHours";

    @ApiOperation("修改预排班")
    @PostMapping("/updatePreShift")
    public Result<Boolean> updatePreShift(@RequestBody DataHandleDto dto) {
        UserInfo userInfo = new UserInfo();
        userInfo.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        userInfo.setUserId(0L);
        String tenantId = userInfo.getTenantId();
        Long startDate = dto.getStartDate();
        Long endDate = dto.getEndDate();
        List<Long> empIds = dto.getEmpIds();
        try {
            Map<Short, List<Short>> yearAndMonthMap = getYearAndMonthMap(startDate, endDate, true);
            List<Short> yearList = Lists.newArrayList();
            List<Short> monthList = Lists.newArrayList();
            //员工排班
            for (Map.Entry<Short, List<Short>> entry : yearAndMonthMap.entrySet()) {
                yearList.add(entry.getKey());
                monthList.addAll(entry.getValue());
            }
            yearList = yearList.stream().distinct().collect(Collectors.toList());
            monthList = monthList.stream().distinct().collect(Collectors.toList());
            //查询租户下所有班次
            List<MultiShiftSimpleVo> defShifts = getDefShifts(tenantId, startDate, endDate);
            Map<Long, MultiShiftSimpleVo> defShiftMap = defShifts.stream().collect(Collectors.toMap(MultiShiftSimpleVo::getShiftDefId, Function.identity(), (v1, v2) -> v2));
            // 排班，草稿
            List<WaEmpScheduleDo> schedules = repository(WaEmpScheduleDo.class).getEmpSchedules(tenantId, yearList, monthList, empIds);
            List<WaEmpScheduleDo> updateSchedules = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(schedules)) {
                List<Long> empIdList = optimizeEmpIdList(schedules, empIds);
                //班次变更
                Map<String, Long> shiftChangeMap = repository(WfmShiftService.class).getShiftChangeMap(tenantId, startDate, endDate, empIdList);
                for (WaEmpScheduleDo schedule : schedules) {
                    Long empId = schedule.getEmpId();
                    List<EmpScheduleItemVo> dayShifts = getScheduleMonthVo(schedule.getWorkTime());
                    dayShifts = dayShifts.stream().filter(s -> s.getDate() >= startDate && s.getDate() <= endDate).collect(Collectors.toList());
                    for (EmpScheduleItemVo dayShift : dayShifts) {
                        List<ScheduleItemVo> shifts = dayShift.getSchedules();
                        Long workDate = dayShift.getDate();
                        // 无排班或只有一个班次不存在时间重叠问题
                        if (CollectionUtils.isEmpty(shifts) || shifts.size() <= 1) {
                            continue;
                        }
                        // 只有一个班次不存在时间重叠问题（排除一个班次多支叶片的情况）
                        List<Long> shiftIds = shifts.stream().map(ScheduleItemVo::getShiftId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                        if (shifts.size() <= 1) {
                            continue;
                        }
                        // 班次信息匹配
                        List<MultiShiftSimpleVo> perDayShifts = defShifts.stream().filter(shift -> shiftIds.contains(shift.getShiftDefId())).collect(Collectors.toList());
                        // 转换班次时间为时间戳
                        List<PeriodShiftResult> periodShiftResults = getActualShiftTime(workDate, perDayShifts);
                        // 当天存在时间重叠的预排班
                        List<PeriodShiftResult> overlapping = filterOverlappingShifts(periodShiftResults);
                        boolean isOverlapping = !CollectionUtils.isEmpty(overlapping);
                        for (ScheduleItemVo shift : shifts) {
                            Long shiftId = shift.getShiftId();
                            String processShiftKey = String.format("%s_%s_%s_%s_%s", empId, workDate, shift.getSalaryProcessId(), shift.getLeafNumberId(), shiftId);
                            if (shiftChangeMap.containsKey(processShiftKey)) {
                                shiftId = shiftChangeMap.get(processShiftKey);
                                shiftChangeMap.remove(processShiftKey);
                            }
                            if (!defShiftMap.containsKey(shiftId)) {
                                continue;
                            }

                            updateSchedules.add(null);
                        }
                    }
                }
            }
            List<WaEmpScheduleDraftDo> draftSchedules = repository(WaEmpScheduleDraftDo.class).getEmpSchedules(tenantId, yearList, monthList, empIds);
            if (CollectionUtils.isNotEmpty(draftSchedules)) {
                List<Long> empIdList = optimizeDraftEmpIdList(draftSchedules, empIds);
                //班次变更
                Map<String, Long> shiftChangeMap = repository(WfmShiftService.class).getShiftChangeMap(tenantId, startDate, endDate, empIdList);
            }
            return Result.ok(true);
        } catch (Exception e) {
            log.error("DataHandleController.updatePreShift execute exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, e.getMessage(), false);
        }
    }

    private List<WfmScanCheckInDto> getEmpRegisterRecordList(String tenantId, Long startDate, Long endDate, List<Long> empIds, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Collections.emptyList();
        }
        List<WfmScanCheckInDto> list = Lists.newArrayList();
        List<List<Long>> empIdList = ListTool.split(empIds, 500);
        for (List<Long> rows : empIdList) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            list.addAll(getEmpRegisterRecords(tenantId, startDate, endDate, rows, orderIds));
        }
        return list;
    }

    private List<WfmScanCheckInDto> getEmpRegisterRecords(String tenantId, Long startDate, Long endDate, List<Long> empIds, List<Long> orderIds) {
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId) //租户
                .andIn("checkInReissueStatus", Arrays.asList("notReissue", "approved"))
                .andGe("scheduleDate", startDate.toString()) //大于等于
                .andLe("scheduleDate", endDate.toString()) //小于等于
                .andNe("deleted", Boolean.TRUE.toString());
        if (CollectionUtils.isNotEmpty(empIds)) {
            dataFilter = dataFilter.andIn("employeeId", empIds.stream().map(String::valueOf).collect(Collectors.toList())); //员工
        }
        if (CollectionUtils.isNotEmpty(orderIds)) {
            dataFilter = dataFilter.andIn("actualOrderId", orderIds.stream().map(String::valueOf).collect(Collectors.toList())); //订单
        }
        List<WfmScanCheckInDto> items = Lists.newArrayList();
        try {
            List<WfmScanCheckIn> list = DataQuery.identifier(WFM_REGISTER_RECORD_IDENTIFIER)
                    .decrypt().specifyLanguage().queryInvisible().limit(-1, 1)
                    .filter(dataFilter, WfmScanCheckIn.class).getItems();
            if (CollectionUtils.isNotEmpty(list)) {
                for (WfmScanCheckIn row : list) {
                    WfmScanCheckInDto dto = ObjectConverter.convert(row, WfmScanCheckInDto.class);
                    dto.setRecordId(Long.valueOf(row.getBid()));
                    dto.setEmpId(Long.valueOf(row.getEmployeeId()));
                    dto.setShiftId(Long.valueOf(row.getActualShiftId()));
                    dto.setOrderId(Long.valueOf(row.getActualOrderId()));
                    dto.setProcessId(Long.valueOf(row.getActualProcessId()));
                    dto.setWorkDate(Long.parseLong(row.getScheduleDate()) / 1000);
                    dto.setRegDateTime(Long.parseLong(row.getCheckInTime()) / 1000);
                    items.add(dto);
                }
            }
        } catch (Exception e) {
            log.error("getEmpRegisterRecords exception:{}", e.getMessage(), e);
        }
        return items;
    }

    private List<PeriodShiftResult> getActualShiftTime(Long workDate, List<MultiShiftSimpleVo> perDayShifts) {
        List<PeriodShiftResult> periodShiftResults = Lists.newArrayList();
        for (MultiShiftSimpleVo perDayShift : perDayShifts) {
            periodShiftResults.add(new PeriodShiftResult(perDayShift.getShiftDefId(), getEarliestStartTime(perDayShift.getMultiWorkTimes(), workDate), getLatestEndTime(perDayShift.getMultiWorkTimes(), workDate)));
        }
        return periodShiftResults;
    }

    private static class PeriodShiftResult {
        Long shiftId;
        Long startTime;
        Long endTime;

        public PeriodShiftResult(Long shiftId, long startTime, long endTime) {
            this.shiftId = shiftId;
            this.startTime = startTime;
            this.endTime = endTime;
        }

        @Override
        public String toString() {
            return "PeriodShiftResult{shiftId=" + shiftId +
                    ", startTime=" + startTime +
                    ", endTime=" + endTime + "}";
        }
    }

    /**
     * 筛选出所有存在时间重叠的PeriodShiftResult对象
     * 适用于startTime和endTime为秒级时间戳且已处理跨夜的情况
     * @param periodShiftResults 原始班次列表
     * @return 存在重叠的对象列表（去重）
     */
    private List<PeriodShiftResult> filterOverlappingShifts(List<PeriodShiftResult> periodShiftResults) {
        if (periodShiftResults == null || periodShiftResults.size() < 2) {
            return new ArrayList<>(); // 少于2个对象不可能有重叠
        }
        Set<PeriodShiftResult> overlappingSet = new HashSet<>();
        // 双层循环比较每对对象
        for (int i = 0; i < periodShiftResults.size(); i++) {
            PeriodShiftResult a = periodShiftResults.get(i);
            for (int j = i + 1; j < periodShiftResults.size(); j++) {
                PeriodShiftResult b = periodShiftResults.get(j);
                // 判断两个时间区间是否重叠
                if (isOverlapping(a, b)) {
                    overlappingSet.add(a);
                    overlappingSet.add(b);
                }
            }
        }
        return new ArrayList<>(overlappingSet);
    }

    /**
     * 判断两个秒级时间戳区间是否重叠
     * 前提：startTime <= endTime（已处理跨夜）
     * @param a 第一个班次
     * @param b 第二个班次
     * @return 是否重叠
     */
    private boolean isOverlapping(PeriodShiftResult a, PeriodShiftResult b) {
        // 时间区间重叠的核心判断：a的开始时间在b结束之前，且a的结束时间在b开始之后
        return a.startTime < b.endTime && a.endTime > b.startTime;
    }

    /**
     * 获取最早上班时间
     *
     * @param workTimes 班次时间段
     * @param baseDate  日期
     * @return long
     */
    private long getEarliestStartTime(List<MultiWorkTimeInfoSimpleVo> workTimes, long baseDate) {
        return workTimes.stream().map(shift -> shift.getRealStartTime() * 60 + baseDate).min(Long::compare).orElse(0L);
    }

    /**
     * 获取最晚下班时间
     *
     * @param workTimes 班次时间段
     * @param baseDate  日期
     * @return long
     */
    private long getLatestEndTime(List<MultiWorkTimeInfoSimpleVo> workTimes, long baseDate) {
        return workTimes.stream().map(shift -> shift.getRealEndTime() * 60 + baseDate).max(Long::compare).orElse(0L);
    }

    private List<EmpScheduleItemVo> getScheduleMonthVo(String workTime) {
        List<EmpScheduleItemVo> shiftMonth = Lists.newArrayList();
        if (StringUtil.isNotBlank(workTime) && !"[]".equals(workTime)) {
            shiftMonth = JSON.parseArray(workTime, EmpScheduleItemVo.class);
        }
        return shiftMonth;
    }

    private List<Long> optimizeEmpIdList(List<WaEmpScheduleDo> empMonthShifts, List<Long> empIds) {
        // 从empMonthShifts提取去重的empId流
        Stream<Long> monthShiftEmpIds = empMonthShifts == null ? Stream.empty() : empMonthShifts.stream().map(WaEmpScheduleDo::getEmpId).distinct();
        // 合并empIds流（去重）
        Stream<Long> empIdsStream = empIds == null ? Stream.empty() : empIds.stream().distinct();
        // 合并两个流并去重后收集为列表
        return Stream.concat(monthShiftEmpIds, empIdsStream).distinct().collect(Collectors.toList());
    }

    private List<Long> optimizeDraftEmpIdList(List<WaEmpScheduleDraftDo> empMonthShifts, List<Long> empIds) {
        // 从empMonthShifts提取去重的empId流
        Stream<Long> monthShiftEmpIds = empMonthShifts == null ? Stream.empty() : empMonthShifts.stream().map(WaEmpScheduleDraftDo::getEmpId).distinct();
        // 合并empIds流（去重）
        Stream<Long> empIdsStream = empIds == null ? Stream.empty() : empIds.stream().distinct();
        // 合并两个流并去重后收集为列表
        return Stream.concat(monthShiftEmpIds, empIdsStream).distinct().collect(Collectors.toList());
    }

    private List<MultiShiftSimpleVo> getDefShifts(String tenantId, Long startDate, Long endDate) {
        List<MultiShiftSimpleVo> defShifts;
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            //查询租户下所有班次
            defShifts = repository(WfmShiftService.class).getAllShiftDefList(tenantId, startDate, endDate);
        } catch (Exception e) {
            return Lists.newArrayList();
        } finally {
            UserContext.removeSecurityUserInfo();
        }
        return defShifts;
    }

    private Map<Short, List<Short>> getYearAndMonthMap(Long startDate, Long endDate, Boolean isUnix) {
        //年月范围
        List<String> yearAndMonth = DateParseUtil.getMonthBetween(startDate, endDate, "yyyy-MM", isUnix);
        //解析排班年月按年分组
        return yearAndMonth.stream().collect(Collectors.groupingBy(row -> Short.valueOf(row.split("-")[0]), Collectors.mapping(row -> Short.valueOf(row.split("-")[1]), Collectors.toList())));
    }

    private static <T> T repository(Class<T> clazz) {
        return SpringUtil.getBean(clazz);
    }
}
