package com.caidaocloud.attendance.core.config;

//@Configuration
public class ErrorPageConfig {
    //@Bean
    /*public EmbeddedServletContainerCustomizer containerCustomizer() {
        return new EmbeddedServletContainerCustomizer() {
            @Override
            public void customize(ConfigurableEmbeddedServletContainer container) {
                ErrorPage error400Page = new ErrorPage(HttpStatus.BAD_REQUEST, "/mobile/404.html");
                ErrorPage error401Page = new ErrorPage(HttpStatus.UNAUTHORIZED, "/mobile/404.html");
                ErrorPage error404Page = new ErrorPage(HttpStatus.NOT_FOUND, "/mobile/404.html");
                ErrorPage error500Page = new ErrorPage(HttpStatus.INTERNAL_SERVER_ERROR, "/mobile/404.html");

                container.addErrorPages(error400Page, error401Page, error404Page, error500Page);
            }
        };
    }*/
}