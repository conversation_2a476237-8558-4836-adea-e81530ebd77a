package com.caidaocloud.attendance.service.domain.service;

import com.caidao1.wa.mybatis.mapper.WaWorktimeGroupMapper;
import com.caidao1.wa.mybatis.model.WaWorktimeGroup;
import com.caidao1.wa.mybatis.model.WaWorktimeGroupExample;
import com.caidaocloud.attendance.service.domain.entity.WaEmpShiftDo;
import com.caidaocloud.attendance.service.domain.entity.WaWorktimeDo;
import com.caidaocloud.attendance.service.interfaces.dto.WaWorktimeDto;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/3/4
 */
@Slf4j
@Service
public class WorkCalendarDomainService {
    @Autowired
    private WaWorktimeDo waWorktimeDo;
    @Autowired
    private WaEmpShiftDo waEmpShiftDo;
    @Autowired
    private WaWorktimeGroupMapper waWorktimeGroupMapper;

    public WaWorktimeDto getWorkCalendarById(String belongOrgId, Integer id) {
        WaWorktimeDo worktimeDo = waWorktimeDo.selectById(belongOrgId, id);
        if (null == worktimeDo) {
            return new WaWorktimeDto();
        }
        WaWorktimeDto worktimeDto = ObjectConverter.convert(worktimeDo, WaWorktimeDto.class);
        if (StringUtils.isNotBlank(worktimeDo.getI18nWorkCalendarName())) {
            worktimeDto.setI18nWorkCalendarName(FastjsonUtil.toObject(worktimeDo.getI18nWorkCalendarName(), Map.class));
        } else if (StringUtils.isNotBlank(worktimeDo.getWorkCalendarName())) {
            Map<String, String> i18nName = new HashMap<>();
            i18nName.put("default", worktimeDo.getWorkCalendarName());
            worktimeDto.setI18nWorkCalendarName(i18nName);
        }
        if (StringUtils.isNotBlank(worktimeDo.getGroupExpCondition())) {
            worktimeDto.setGroupExpCondition(FastjsonUtil.toObject(worktimeDo.getGroupExpCondition(), ConditionTree.class));
        }
        WaWorktimeGroupExample example = new WaWorktimeGroupExample();
        example.createCriteria().andWorkCalendarIdEqualTo(id);
        example.setOrderByClause("crttime desc");
        List<WaWorktimeGroup> waWorktimeGroups = waWorktimeGroupMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(waWorktimeGroups)) {
            waWorktimeGroups.sort(Comparator.comparing(WaWorktimeGroup::getCrttime));
            Collections.reverse(waWorktimeGroups);
            WaWorktimeGroup worktimeGroup = waWorktimeGroups.get(0);
            worktimeDto.setStartdate(worktimeGroup.getStartdate());
            worktimeDto.setEnddate(worktimeGroup.getEnddate());
        }

        List<WaEmpShiftDo> empShiftDos = waEmpShiftDo.getEmpShiftListByWorkCalendarId(belongOrgId, worktimeDo.getWorkCalendarId());
        if (CollectionUtils.isNotEmpty(empShiftDos)) {
            List<Long> empidList = empShiftDos.stream().map(WaEmpShiftDo::getEmpid).distinct().collect(Collectors.toList());
            List<String> empNameList = empShiftDos.stream().map(WaEmpShiftDo::getEmpName).distinct().collect(Collectors.toList());
            worktimeDto.setEmpIdArray(StringUtils.join(empidList, ","));
            worktimeDto.setEmpNameArray(StringUtils.join(empNameList, ","));
        }
        return worktimeDto;

    }
}