package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDefDo;

import java.util.List;

public interface IWaLeaveTypeDefRepository {

    List<WaLeaveTypeDefDo> getWaLeaveTypeDefList(String belongOrgid);

    void delete(Integer id, String belongOrgId);

    void save(WaLeaveTypeDefDo defDo, String belongOrgid, Long userId);

    WaLeaveTypeDefDo getDetailList(Integer id);

    WaLeaveTypeDefDo selectById(String belongOrgid, Integer leaveTypeDefId);

    WaLeaveTypeDefDo selectByCode(String belongOrgid, String leaveTypeDefCode);

    WaLeaveTypeDefDo getWaLeaveTypeDefByData(WaLeaveTypeDefDo defDo, String belongId);

    List<WaLeaveTypeDefDo> getAllLeaveTypeDefList(String tenantId, Boolean includeSystem);
}
