package com.caidaocloud.attendance.core.commons.utils.office;

import com.deepoove.poi.data.RenderData;

public class TabelCellRenderData implements RenderData {
    private Style style;
    private String text;
    private Integer width;
    private Integer height;
    private String code;

    public TabelCellRenderData() {
    }

    public TabelCellRenderData(String text, Integer width) {
        this.text = text;
        this.width = width;
    }

    public TabelCellRenderData(String text, Integer width, String code) {
        this.text = text;
        this.width = width;
        this.code = code;
    }

    public TabelCellRenderData(String text, Integer width, Integer height) {
        this.text = text;
        this.width = width;
        this.height = height;
    }

    public TabelCellRenderData(String text, Integer width, Integer height, String code) {
        this.text = text;
        this.width = width;
        this.height = height;
        this.code = code;
    }

    public TabelCellRenderData(String color, String text, Integer width) {
        this.style = new Style(color);
        this.text = text;
        this.width = width;
    }

    public TabelCellRenderData(String color, String text, Integer width, String code) {
        this.style = new Style(color);
        this.text = text;
        this.width = width;
        this.code = code;
    }



    public TabelCellRenderData(String color, String text, Integer width, Integer height) {
        this.style = new Style(color);
        this.text = text;
        this.width = width;
        this.height = height;
    }

    public TabelCellRenderData(String color, String text, Integer width, Integer height, String code) {
        this.style = new Style(color);
        this.text = text;
        this.width = width;
        this.height = height;
        this.code = code;
    }

    public Style getStyle() {
        return style;
    }

    public void setStyle(Style style) {
        this.style = style;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
