package com.caidaocloud.attendance.service.application.enums;

public enum GenQuotaMode {
    NOT_GENERATED(1, "生成新员工配额"),
    RESIGNED(2, "更新离职人员配额"),
    ALL(3, "更新全员配额");

    private Integer index;
    private String desc;

    GenQuotaMode(Integer index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByIndex(Integer index) {
        for (GenQuotaMode quotaMode : GenQuotaMode.values()) {
            if (quotaMode.getIndex() == index) {
                return quotaMode.getDesc();
            }
        }
        return "";
    }
}
