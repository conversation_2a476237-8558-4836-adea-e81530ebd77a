package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.sdk.dto.SdkLeaveCancelDTO;
import com.caidaocloud.attendance.sdk.feign.fallback.LeaveCancelFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 销假申请
 *
 * <AUTHOR>
 * @Date 2023/6/20
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = LeaveCancelFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "leaveCancelFeignClient")
public interface ILeaveCancelFeignClient {

    /**
     * 销假申请
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/attendance/leaveCancel/v1/apply")
    Result<?> apply(@RequestBody SdkLeaveCancelDTO dto);

    /**
     * 获取销假时长
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/attendance/leaveCancel/v1/getTime")
    Result<?> getTime(@RequestBody SdkLeaveCancelDTO dto);
}
