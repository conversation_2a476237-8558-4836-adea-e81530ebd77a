package com.caidaocloud.attendance.core.config;

import com.caidao1.commons.handler.CDAsyncExceptionHandler;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean(name = "taskExecutor")
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor threadPool = new ThreadPoolTaskExecutor();
        threadPool.setCorePoolSize(10);
        threadPool.setMaxPoolSize(20);
        threadPool.setQueueCapacity(100);
        threadPool.setKeepAliveSeconds(60);
        threadPool.setAllowCoreThreadTimeOut(true);
        threadPool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPool.setWaitForTasksToCompleteOnShutdown(true);
        threadPool.setAwaitTerminationSeconds(60 * 5); // 5分钟等待时间
        threadPool.setThreadNamePrefix("Default-Async-Task-");
        threadPool.initialize();
        return threadPool;
    }

    /**
     * IO密集型任务（如数据库查询、网络请求）：线程数可适当多些，利用等待时间
     */
    @Bean(name = "exportAsyncTaskExecutor")
    public Executor ioIntensiveExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        int corePoolSize = 10;
        executor.setCorePoolSize(corePoolSize);
        // 最大线程数：核心线程数 * 2（避免过多线程切换开销）
        executor.setMaxPoolSize(corePoolSize * 2);
        // 队列容量：IO任务等待时间长，队列可适当大些
        executor.setQueueCapacity(500);
        // 空闲线程存活时间：60秒（IO任务空闲概率高，及时回收）
        executor.setKeepAliveSeconds(60);
        // 允许核心线程超时回收
        executor.setAllowCoreThreadTimeOut(true);
        // 线程名称前缀（便于日志追踪）
        executor.setThreadNamePrefix("IO-Async-Task-");
        // 拒绝策略：队列满时，让提交任务的线程执行（避免任务丢失）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 优雅关闭：等待任务完成再关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 最大等待时间：5分钟（避免无限等待）
        executor.setAwaitTerminationSeconds(60 * 5);
        executor.initialize();
        return executor;
    }

    /**
     * CPU密集型任务（如复杂计算、数据处理）：线程数不宜过多，避免CPU切换开销
     */
    @Bean(name = "cpuIntensiveExecutor")
    public Executor cpuIntensiveExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数：等于CPU核心数（CPU密集型推荐，减少切换）
        int corePoolSize = 5;
        executor.setCorePoolSize(corePoolSize);
        // 最大线程数：核心线程数 + 1（CPU密集型不建议过大）
        executor.setMaxPoolSize(corePoolSize + 1);
        // 队列容量：CPU任务执行快，队列可小些
        executor.setQueueCapacity(100);
        // 空闲线程存活时间：30秒（CPU任务空闲概率低）
        executor.setKeepAliveSeconds(30);
        executor.setAllowCoreThreadTimeOut(false); // 核心线程不回收（减少重建开销）
        executor.setThreadNamePrefix("CPU-Async-Task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60 * 5);
        executor.initialize();
        return executor;
    }

    /**
     * 定时任务线程池：与普通任务隔离，避免定时任务被阻塞
     */
    @Bean(name = "scheduledTaskExecutor")
    public Executor scheduledTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5); // 定时任务数量通常较少
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(30);
        executor.setThreadNamePrefix("Scheduled-Async-Task-");
        // 定时任务拒绝策略：丢弃最旧任务（避免队列堆积）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60 * 10); // 定时任务可能执行较久，等待时间长些
        executor.initialize();
        return executor;
    }

    @Bean
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new CDAsyncExceptionHandler();
    }
}

