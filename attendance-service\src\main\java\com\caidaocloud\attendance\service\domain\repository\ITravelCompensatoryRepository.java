package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.domain.entity.TravelCompensatoryDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelTransferCompensatory;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

public interface ITravelCompensatoryRepository {

    void save(WaTravelTransferCompensatory model);

    void update(WaTravelTransferCompensatory model);

    PageList<TravelCompensatoryDo> getTravelCompensatoryList(MyPageBounds myPageBounds, Map params);

    void batchSave(List<WaTravelTransferCompensatory> records);

    TravelCompensatoryDo getDetailById(String tenantId, Long id);

    List<TravelCompensatoryDo> getTravelCompensatoryRecords(String tenantId, List<Integer> sobIds);

    void batchUpdate(List<WaTravelTransferCompensatory> records);

    void batchDelete(List<Long> quotaIds, Long userId);
}
