package com.caidaocloud.attendance.service.application.service.overtime;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.service.IOvertimeTransferRuleService;
import com.caidaocloud.attendance.service.domain.service.OvertimeTransferRuleDomainService;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OvertimeTransferRuleDto;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.context.LogRecordContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OvertimeTransferRuleServiceImpl implements IOvertimeTransferRuleService {

    @Autowired
    private OvertimeTransferRuleDomainService overtimeTransferRuleDomainService;

    @Override
    public void saveOvertimeTransferRule(OvertimeTransferRuleDto dto) {
        overtimeTransferRuleDomainService.saveOvertimeTransferRule(UserContext.preCheckUser(), dto);
    }

    @Override
    public OvertimeTransferRuleDto getOvertimeTransferRule(Long ruleId) {
        return overtimeTransferRuleDomainService.getOvertimeTransferRule(ruleId);
    }

    @Override
    public void deleteOvertimeTransferRule(Long ruleId) {
        UserInfo userInfo = UserContext.preCheckUser();
        Long userId = userInfo.getUserId();
        OvertimeTransferRuleDto rule = overtimeTransferRuleDomainService.getOvertimeTransferRule(ruleId);
        LogRecordContext.putVariable("name", rule.getRuleName());
        overtimeTransferRuleDomainService.delete(ruleId, userId);

    }

    @Override
    public Boolean checkOvertimeTransferRuleName(OvertimeTransferRuleDto dto) {
        UserInfo userInfo = UserContext.preCheckUser();
        String tenantId = userInfo.getTenantId();
        return overtimeTransferRuleDomainService.checkTransferRuleName(tenantId, dto.getRuleId(), dto.getRuleName());
    }

    @Override
    public List<OvertimeTransferRuleDto> getOvertimeTransferRuleList() {
        UserInfo userInfo = UserContext.preCheckUser();
        String tenantId = userInfo.getTenantId();
        return overtimeTransferRuleDomainService.getOvertimeTransferRules(tenantId);
    }

    @Override
    public List<KeyValue> getOvertimeTransferRuleList(Integer compensateType) {
        UserInfo userInfo = UserContext.preCheckUser();
        String tenantId = userInfo.getTenantId();
        List<OvertimeTransferRuleDto> list = overtimeTransferRuleDomainService.getOvertimeTransferRules(tenantId);
        List<KeyValue> options = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list = list.stream().filter(l -> l.getCompensateType().equals(compensateType)).collect(Collectors.toList());
            list.forEach(l -> {
                options.add(new KeyValue(l.getRuleName(), l.getRuleId()));
            });
        }
        return options;
    }

    @Override
    public List<OvertimeTransferRuleDto> getOvertimeTransferRuleList(List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return new ArrayList<>();
        }
        return overtimeTransferRuleDomainService.getOvertimeTransferRules(ruleIds);
    }

    @Override
    public boolean checkTransferRuleReferenced(Long ruleId) {
        return overtimeTransferRuleDomainService.checkTransferRuleReferenced(ruleId);
    }
}
