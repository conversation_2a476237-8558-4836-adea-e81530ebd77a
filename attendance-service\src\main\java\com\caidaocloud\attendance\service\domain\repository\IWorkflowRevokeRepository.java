package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaWorkflowRevoke;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

public interface IWorkflowRevokeRepository {
    WaWorkflowRevoke selectByPrimaryKey(Long id);

    int save(WaWorkflowRevoke model);

    int update(WaWorkflowRevoke model);

    PageList<Map> getOvertimeWorkflowRevokeList(MyPageBounds myPageBounds, Map param);

    PageList<Map> getTravelWorkflowRevokeList(MyPageBounds myPageBounds, Map param);

    List<WaWorkflowRevoke> getWorkflowRevokeList(String tenantId, Long entityId);

    List<WaWorkflowRevoke> getWorkflowRevokeList(String tenantId, Long entityId, List<Integer> status, List<String> moduleNames);

    int delete(WaWorkflowRevoke model);
}
