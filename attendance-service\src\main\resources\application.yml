spring:
 profiles:
  active: dev
#galaxy:
# distribute:
#  lock:
#   isOpen: false
feign:
 httpclient:
  enabled: false
aliyun:
 mns:
  open: false

nacos:
 com:
  alibaba:
   nacos:
    naming:
     cache:
      dir: /tmp

i18n:
 resource:
  path: i18n/attendance/message


msg:
 middleware:
  type: rabbitmq

rabbitmq:
 topics:
  - topic: EmpWorkInfoHireLeaveChange
    exchange: hrpaas.es.exchange
    routingKey: routingKey.hrpaas.es.msg.attendance.empWorkInfo.hireAndLeave
    queue: caidao.attendance.emp.hireleave.change
    exchangeType: DIRECT
    tenantIsolated: true
    consumersCount: 1