package com.caidaocloud.attendance.service.domain.service;

import com.caidaocloud.attendance.service.domain.entity.WaWorkRoundShiftDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/1
 */
@Slf4j
@Service
public class WorkRoundShiftDomainService {
    @Autowired
    private WaWorkRoundShiftDo waWorkRoundShiftDo;

    public List<WaWorkRoundShiftDo> selectListByWorkRoundId(Integer workRoundId) {
        return waWorkRoundShiftDo.selectListByWorkRoundId(workRoundId);
    }

    public int deleteByWorkRoundId(Integer workRoundId) {
        return waWorkRoundShiftDo.deleteByWorkRoundId(workRoundId);
    }
}
