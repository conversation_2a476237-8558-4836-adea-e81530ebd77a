package com.caidaocloud.attendance.core.wa.dto;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class LeaveQuotaDto {
    private Integer quotaId;

    private Integer leaveType;

    private BigDecimal quotaVal;

    private Integer quotaSettingId;

    private boolean isCross = false;

    private Map crossParams = new HashMap();

    public Integer getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Integer quotaId) {
        this.quotaId = quotaId;
    }

    public Integer getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(Integer leaveType) {
        this.leaveType = leaveType;
    }

    public BigDecimal getQuotaVal() {
        return quotaVal;
    }

    public void setQuotaVal(BigDecimal quotaVal) {
        this.quotaVal = quotaVal;
    }

    public Integer getQuotaSettingId() {
        return quotaSettingId;
    }

    public void setQuotaSettingId(Integer quotaSettingId) {
        this.quotaSettingId = quotaSettingId;
    }

    public boolean isCross() {
        return isCross;
    }

    public void setCross(boolean cross) {
        isCross = cross;
    }

    public Map getCrossParams() {
        return crossParams;
    }

    public void setCrossParams(Map crossParams) {
        this.crossParams = crossParams;
    }
}