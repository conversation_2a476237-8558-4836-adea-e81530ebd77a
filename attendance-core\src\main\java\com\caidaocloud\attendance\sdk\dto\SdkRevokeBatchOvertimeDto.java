package com.caidaocloud.attendance.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
@ApiModel("批量加班呐单撤销参数DTO")
public class SdkRevokeBatchOvertimeDto {
    @ApiModelProperty("主键ID")
    private Long batchId;
    @ApiModelProperty("流程业务主键")
    private String businessKey;
    @ApiModelProperty("撤销原因")
    private String revokeReason;

    public Long getBatchId() {
        if (null != batchId) {
            return batchId;
        }
        if (StringUtils.isEmpty(businessKey)) {
            return null;
        }
        if (businessKey.contains("_")) {
            String[] businessKeys = businessKey.split("_");
            return Long.valueOf(businessKeys[0]);
        }
        return Long.valueOf(businessKey);
    }
}
