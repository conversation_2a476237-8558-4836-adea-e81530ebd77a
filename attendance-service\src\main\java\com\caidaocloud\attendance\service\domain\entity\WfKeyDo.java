package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWfKeyRepository;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Data
@Service
public class WfKeyDo {

    private String procInstId;

    private String businessKey;

    @Autowired
    private IWfKeyRepository wfKeyRepository;

    public WfKeyDo getWfKey(String belongOrgId, Long empId, String id, Integer wfFuncId) {
        return wfKeyRepository.getWfKey(belongOrgId, empId, id, wfFuncId);
    }
}
