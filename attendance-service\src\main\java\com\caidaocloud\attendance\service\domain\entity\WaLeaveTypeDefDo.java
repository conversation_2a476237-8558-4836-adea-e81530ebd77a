package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWaLeaveTypeDefRepository;
import com.caidaocloud.attendance.service.interfaces.dto.WaLeaveTypeDefDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 假期定义类型DO
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Service
public class WaLeaveTypeDefDo {
    private Integer leaveTypeDefId;
    private String leaveTypeDefCode;
    private String leaveTypeDefName;
    private Object leaveTypeDefLang;
    private Short deleted;
    private Integer status;
    private String belongOrgid;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private String i18nLeaveTypeDefName;

    @Autowired
    private IWaLeaveTypeDefRepository waLeaveTypeDefRepository;

    public List getLeaveTypeDefList(String belongOrgId) {
        return waLeaveTypeDefRepository.getWaLeaveTypeDefList(belongOrgId);
    }

    public List getAllLeaveTypeDefList(String tenantId, Boolean includeSystem) {
        return waLeaveTypeDefRepository.getAllLeaveTypeDefList(tenantId, includeSystem);
    }

    public void delete(Integer id, String belongOrgId) {
        waLeaveTypeDefRepository.delete(id, belongOrgId);
    }

    public void save(WaLeaveTypeDefDto dto, String belongId, Long userId) {
        WaLeaveTypeDefDo leaveTypeDef = ObjectConverter.convert(dto, WaLeaveTypeDefDo.class);
        if (null != dto.getI18nLeaveTypeDefName()) {
            leaveTypeDef.setI18nLeaveTypeDefName(FastjsonUtil.toJson(dto.getI18nLeaveTypeDefName()));
        }
        waLeaveTypeDefRepository.save(leaveTypeDef, belongId, userId);
    }

    public WaLeaveTypeDefDto getDetailList(Integer id) {
        WaLeaveTypeDefDo waLeaveTypeDefDo = waLeaveTypeDefRepository.getDetailList(id);
        return waLeaveTypeDefDo == null ? null : ObjectConverter.convert(waLeaveTypeDefDo, WaLeaveTypeDefDto.class);
    }

    public WaLeaveTypeDefDo getById(String belongOrgid, Integer leaveTypeDefId) {
        return waLeaveTypeDefRepository.selectById(belongOrgid, leaveTypeDefId);
    }

    public WaLeaveTypeDefDo getByCode(String belongOrgid, String leaveTypeDefCode) {
        return waLeaveTypeDefRepository.selectByCode(belongOrgid, leaveTypeDefCode);
    }

    public WaLeaveTypeDefDto getWaLeaveTypeDefByData(WaLeaveTypeDefDto dto, String belongId) {
        WaLeaveTypeDefDo waLeaveTypeDefDo = waLeaveTypeDefRepository.getWaLeaveTypeDefByData(ObjectConverter.convert(dto, WaLeaveTypeDefDo.class), belongId);
        return waLeaveTypeDefDo == null ? null : ObjectConverter.convert(waLeaveTypeDefDo, WaLeaveTypeDefDto.class);
    }
}
