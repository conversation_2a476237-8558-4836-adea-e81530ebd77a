package com.caidaocloud.attendance.service.application.service.workflow;

import com.caidaocloud.attendance.service.application.dto.travel.BatchTravelDto;
import com.caidaocloud.attendance.service.domain.entity.WaBatchTravelDo;
import com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.workflow.dto.WfApproverDto;
import com.google.common.collect.Lists;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class WorkflowApproverService {
    @Resource
    private WaBatchTravelDo waBatchTravelDo;
    @Resource
    private WaEmpTravelDo waEmpTravelDo;

    public String getBatchTravelCostCenter(String businessKey) {
        if (StringUtils.isNotBlank(businessKey)) {
            return "";
        }
        val travelId = Long.valueOf(StringUtils.substringBefore(businessKey, "_"));
        val batchTravel = waBatchTravelDo.getById(travelId);
        if (null == batchTravel) {
            return "";
        }
        List<WaEmpTravelDo> empTravelDetailList = waEmpTravelDo.listByBatchId(batchTravel.getTenantId(), batchTravel.getBatchTravelId());
        if (CollectionUtils.isEmpty(empTravelDetailList)) {
            return "";
        }
        var empTravel = empTravelDetailList.get(0);
        if (empTravel == null || StringUtils.isBlank(empTravel.getExtCustomCol())) {
            return "false";
        }
        var travelDetail = FastjsonUtil.convertObject(empTravel.getExtCustomCol(), BatchTravelDto.class);
        val costCenterId = StringUtils.defaultString(travelDetail.getCostCenterId(), "0");
        PageResult<Map<String, String>> pageResult = DataQuery.identifier("entity.hr.CostCenter")
                .filterProperties(DataFilter.eq("bid", costCenterId), Lists.newArrayList("leaderEmpId", "leaderName"), System.currentTimeMillis());
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return "";
        }
        Map<String, String> leadEmpMap = pageResult.getItems().get(0);
        ArrayList<WfApproverDto> approverList = Lists.newArrayList(new WfApproverDto(leadEmpMap.getOrDefault("leader.empId", ""), leadEmpMap.getOrDefault("leader.name", ""), ""));
        return FastjsonUtil.toJson(approverList);
    }
}