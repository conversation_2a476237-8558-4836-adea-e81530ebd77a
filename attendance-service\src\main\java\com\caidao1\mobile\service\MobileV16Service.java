package com.caidao1.mobile.service;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.service.util.SessionBeanUtil;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.employee.mybatis.mapper.EmpInfoMapper;
import com.caidao1.ioc.util.IocUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.mobile.bean.SessionBean;
import com.caidao1.wa.dto.EmpQuoDto;
import com.caidao1.wa.dto.EmpQuotaDTO;
import com.caidao1.wa.dto.UsableCompensatoryQuotaDto;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidao1.xss.test.cache.RedisService;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.commons.utils.TimeRangeCheckUtil;
import com.caidaocloud.attendance.core.wa.dto.BuildTimeOverlapCheckParamDto;
import com.caidaocloud.attendance.core.wa.dto.EmpQuotaUseDTO;
import com.caidaocloud.attendance.core.wa.dto.ShiftRestPeriods;
import com.caidaocloud.attendance.core.wa.dto.WaLeaveDaytimeExtDto;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiWorkTimeBaseDto;
import com.caidaocloud.attendance.core.wa.dto.shift.RestPeriodDto;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.WaLeavePeriodTypeEnum;
import com.caidaocloud.attendance.core.wa.service.RemoteSmartWorkTimeService;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.QuotaMapper;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveApplySaveDto;
import com.caidaocloud.em.HalfDayTypeEnum;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MobileV16Service implements ScriptBindable {
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaWorktimeDetailMapper waWorktimeDetailMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private WaLeaveDaytimeMapper waLeaveDaytimeMapper;
    @Autowired
    private EmpInfoMapper empInfoMapper;
    @Autowired
    private WaStoreTimeMapper waStoreTimeMapper;
    @Autowired
    private WaConfigMapper waConfigMapper;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private WaCheckMapper waCheckMapper;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private RemoteSmartWorkTimeService remoteSmartWorkTimeService;
    @Autowired
    private WaWorktimeMapper waWorktimeMapper;
    @Autowired
    private EmpCompensatoryQuotaMapper waEmpCompensatoryQuotaMapper;
    @Autowired
    private WaCompensatoryQuotaUseMapper waCompensatoryQuotaUseMapper;
    @Autowired
    private WaLeaveQuotaUseMapper waLeaveQuotaUseMapper;
    @Autowired
    private WaSobService waSobService;
    @Autowired
    private EmpTravelMapper empTravelMapper;
    @Autowired
    private WaLeaveTypeDefMapper waLeaveTypeDefMapper;
    @Autowired
    private QuotaMapper quotaMapper;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    /**
     * 请假时扣减休息时间
     *
     * @param shiftDef
     * @param workTimeDetail
     * @param startTime
     * @param endTime
     * @return
     */
    public Integer deductLeaveDayNooRest(WaShiftDef shiftDef, WaWorktimeDetail workTimeDetail, Long startTime, Long endTime) {
        long durationSec = endTime - startTime;
        if (shiftDef.getIsNoonRest() == null || !shiftDef.getIsNoonRest()) {
            return new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
        }
        List<RestPeriodDto> allRestPeriodList = WaShiftDo.doGetRestPeriodList(shiftDef);
        if (CollectionUtils.isEmpty(allRestPeriodList)) {
            return new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
        }
        for (RestPeriodDto periodDto : allRestPeriodList) {
            ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTimeV2(periodDto, shiftDef, workTimeDetail.getDateType());
            Integer noonRestStartMi = restPeriod.getNoonRestStart();
            Integer noonRestEndMi = restPeriod.getNoonRestEnd();

            long noonRestStartTime = workTimeDetail.getWorkDate() + noonRestStartMi * 60;
            long noonRestEndTime = workTimeDetail.getWorkDate() + noonRestEndMi * 60;

            if (startTime <= noonRestEndTime && endTime >= noonRestStartTime) {
                durationSec -= Math.min(noonRestEndTime, endTime) - Math.max(noonRestStartTime, startTime);
            }
        }
        return new BigDecimal(durationSec).divideToIntegralValue(new BigDecimal(60)).intValue();
    }

    /**
     * 申请哺乳假校验(检查当天有没有申请过非哺乳假，有的话，那么当天不能申请哺乳假)
     *
     * @param waLeaveType
     * @param empid
     * @param leaveDate
     * @return
     */
    public String validateBRJ(WaLeaveType waLeaveType, Long empid, Long leaveDate) {
        if (waLeaveType.getLeaveType() != 10) {
            return "";
        }
        Map params = new HashMap();
        params.put("notEqLeaveType", 10);
        params.put("empid", empid);
        params.put("startDate", leaveDate);
        List<Map> listEmpLeaveData = waEmpLeaveMapper.listEmpLeaveData(params);
        if (CollectionUtils.isNotEmpty(listEmpLeaveData)) {
            return messageResource.getMessage("L005863", new Object[]{}, new Locale(SessionBeanUtil.getLanguage()));
        }
        return "";
    }

    /**
     * 申请哺乳假校验(检查当天有没有申请过非哺乳假，有的话，那么当天不能申请哺乳假)
     *
     * @param waLeaveType
     * @param empid
     * @param startDate
     * @param endDate
     * @return
     */
    public String validateBRJ(WaLeaveType waLeaveType, Long empid, Long startDate, Long endDate) {
        if (waLeaveType.getLeaveType() != 10) {
            return "";
        }
        long leaveDate = startDate;
        while (leaveDate <= endDate) {
            // 哺乳假申请校验
            String errBRJMsg = validateBRJ(waLeaveType, empid, leaveDate);
            if (!"".equals(errBRJMsg)) {
                return errBRJMsg;
            }
            leaveDate = leaveDate + 86400;
        }
        return "";
    }

    /**
     * 是否调整半天工时(该逻辑只适用于申请的假时间单位是小时)
     *
     * @param startTime
     * @param endTime
     * @param shiftDef
     * @return
     */
    public Float getAdjustWorkTime(Integer startTime, Integer endTime, WaShiftDef shiftDef) {
        if (shiftDef != null && shiftDef.getIsAdjustWorkHour() != null && shiftDef.getIsAdjustWorkHour()) {
            PGobject pGobject = convertToGobject(shiftDef.getAdjustWorkHourJson());
            if (pGobject != null) {
                Map<String, Object> workHourMap = JSONUtils.convertPGobjectToMap(pGobject);
                Integer amStartMin = (Integer) workHourMap.get("amStartTime");
                Integer amEndMin = (Integer) workHourMap.get("amEndTime");
                Integer pmStartMin = (Integer) workHourMap.get("pmStartTime");
                Integer pmEndMin = (Integer) workHourMap.get("pmEndTime");

                if (amStartMin.equals(startTime) && amEndMin.equals(endTime)) {
                    //上午
                    return Float.parseFloat(String.valueOf(workHourMap.get("amWorkHour"))) * 60;
                } else if (pmStartMin.equals(startTime) && pmEndMin.equals(endTime)) {
                    //下午
                    return Float.parseFloat(String.valueOf(workHourMap.get("pmWorkHour"))) * 60;
                }
            }
        }
        return null;
    }

    /**
     * 如果开启了门店考勤员工法定假日使用默认工作日历，则返回默认工作日历数据
     *
     * @param belongOrgId 租户
     * @return
     */
    public Map<Long, Integer> getDefaultWorkCalendarByTmType(String belongOrgId, Integer empTmType) {
        String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongOrgId + RedisKeyDefine.IS_OPEN_STOREEMP_VACATION_USE_DEFAULTCALENDAR);
        if (isOpen != null && "1".equals(isOpen) && Integer.valueOf(2).equals(empTmType)) {
            return getDefaultWorkCalendar(belongOrgId);
        }
        return new HashMap<>();
    }

    public Map<Long, Integer> getDefaultWorkCalendar(String belongOrgId) {
        Map<Long, Integer> map = new HashMap<>();//日期类型

        WaWorktimeExample example = new WaWorktimeExample();
        example.createCriteria().andBelongOrgidEqualTo(belongOrgId).andIsDefaultEqualTo(true);
        List<WaWorktime> worktimeList = waWorktimeMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(worktimeList)) {
            WaWorktime worktime = worktimeList.get(0);
            WaWorktimeDetailExample detailExample = new WaWorktimeDetailExample();
            detailExample.createCriteria().andWorkCalendarIdEqualTo(worktime.getWorkCalendarId());
            List<WaWorktimeDetail> detailList = waWorktimeDetailMapper.selectByExample(detailExample);
            if (CollectionUtils.isNotEmpty(detailList)) {
                detailList.forEach(row -> map.put(row.getWorkDate(), row.getDateType()));
            }
        }
        return map;
    }

    /**
     * 计算请假结束时间-产假 是适用于产假且单位为天，最小单位为一天
     *
     * @param leaveTypeId
     * @param leaveDay
     * @param startTime
     * @return
     */
    public Long getLeaveEndTime(String belongid, Long empid, Integer leaveTypeId, Integer leaveDay, Long startTime, String lang) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
        if (tmType == 0) {
            tmType = 1;
        } else if (tmType == 2) {//门店考勤人员
            Jedis jedis = RedisService.getResource();
            String appStoreEnable = jedis.get(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongid + "_APP_STORE_Enable");
            try {
                if (appStoreEnable == null || (!appStoreEnable.equals("1"))) {//不支持门店考勤人员请假
                    throw new CDException(messageResource.getMessage("L005723", new Object[]{}, new Locale(lang)));
                }
            } catch (RuntimeException e) {
                jedis.close();
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
        Long start = startTime;
        String errorMsg = "";
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveTypeId);
        if (waLeaveType != null) {
            Long end = DateUtils.addDays(new Date(start * 1000), leaveDay).getTime() / 1000;
            Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(belongid, empid, tmType, start, end, empInfo.getWorktimeType(), true);
            if (MapUtils.isEmpty(pbMap)) {
                errorMsg = messageResource.getMessage("L005739", new Object[]{DateUtil.getDateStrByTimesamp(startTime)}, new Locale(lang));
            } else {
                //CLOUD-8200 & 8414 查询公司默认工作日历
                Map<Long, Integer> defaultWorkCalendar = getDefaultWorkCalendarByTmType(belongid, tmType);

                WaWorktimeDetail startDateDetail;
                int day = leaveDay;
                boolean flag = true;
                while (flag) {
                    //查询员工排班
                    startDateDetail = pbMap.get(start);//取得当天排班
                    if (startDateDetail == null) {
                        Long end1 = DateUtils.addDays(new Date(end * 1000), 30).getTime() / 1000;
                        Map<Long, WaWorktimeDetail> pbMap1 = waCommonService.getEmpWaWorktimeDetail(belongid, empid, tmType, end, end1, empInfo.getWorktimeType(), true);
                        if (MapUtils.isNotEmpty(pbMap1)) {
                            pbMap.putAll(pbMap1);
                            startDateDetail = pbMap.get(start);//取得当天排班
                        }
                    }

                    if (startDateDetail == null) {
                        errorMsg = messageResource.getMessage("L005739", new Object[]{DateUtil.getDateStrByTimesamp(start)}, new Locale(lang));
                        flag = false;
                    } else {
                        Integer dateType = startDateDetail.getDateType();

                        if (Integer.valueOf(3).equals(defaultWorkCalendar.get(start))) {
                            dateType = defaultWorkCalendar.get(start);
                        }

                        if ((Integer.valueOf(2).equals(dateType) && BooleanUtils.isFalse(waLeaveType.getIsRestDay()))//日期类型为休息日，且休息日不连续计算
                                || (Integer.valueOf(3).equals(dateType) && BooleanUtils.isFalse(waLeaveType.getIsLegalHoliday()))) {//日期类型为法定假日，且法定假日不连续计算
                        } else {
                            day--;
                        }
                        if (day <= 0) {
                            flag = false;
                        } else {
                            start = start + 24 * 60 * 60;
                        }
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new CDException(errorMsg);
        }
        return start;
    }

    /**
     * TODO 请假时长折算
     *
     * @param period
     * @param dayTime
     * @param waLeaveType
     * @param shiftDef
     */
    public void convertLtTime(Integer period, WaLeaveDaytime dayTime, WaLeaveType waLeaveType, WaShiftDef shiftDef) {
        // 判断假期是否开启了工时折算 CLOUD-8257
        if (waLeaveType.getAcctTimeType() == 1
                && BooleanUtils.isTrue(waLeaveType.getIsWorkhourConvert())
                && waLeaveType.getConvertDuration() != null
                && waLeaveType.getConvertDuration() > 0) {
            Integer convertDuration = waLeaveType.getConvertDuration();
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)
                    || PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
                if (dayTime.getTimeDuration() == 1) {//CLOUD-8257 请假时长按天折算
                    if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType())
                            && shiftDef.getWorkTotalTime() != null
                            && !convertDuration.equals(shiftDef.getWorkTotalTime())) {
                        Integer workTime = shiftDef.getWorkTotalTime();
                        BigDecimal a = new BigDecimal(workTime);
                        BigDecimal b = new BigDecimal(convertDuration);
                        Float convertDay = a.divide(b, 2, RoundingMode.DOWN).floatValue();
                        dayTime.setTimeDuration(convertDay);//实际申请时长，记录折算之后的时长
                        dayTime.setApplyTimeDuration(1f);//记录原始申请时长
                    }
                }
            } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
                if (waLeaveType.getAcctTimeType() == 1) { //CLOUD-8450 工时折算，按照小时去申请单位为天的假期
                    BigDecimal a = BigDecimal.valueOf(dayTime.getTimeDuration());
                    BigDecimal b = new BigDecimal(convertDuration);
                    Float convertDay = a.divide(b, 2, RoundingMode.DOWN).floatValue();
                    dayTime.setTimeDuration(convertDay);
                    dayTime.setPeriodType(PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().shortValue());
                    dayTime.setTimeUnit((short) 1);
                }
            }
        }
    }

    private Integer getHalfdayTime(WaShiftDef shiftDef, HalfDayTypeEnum halfday) {
        // 是否定义半天时间
        boolean isHalfdayDef = shiftDef.getIsHalfdayTime() != null
                && shiftDef.getIsHalfdayTime() && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() > 0;
        if (isHalfdayDef) {
            return shiftDef.getHalfdayTime();
        }

        // 是否定义午休时间
        boolean isNoonDef = shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()
                && null != shiftDef.getNoonRestStart() && null != shiftDef.getNoonRestEnd();
        if (isNoonDef) {
            return HalfDayTypeEnum.A == halfday ? shiftDef.getNoonRestStart() : shiftDef.getNoonRestEnd();
        }

        return null;
    }

    /**
     * 计算每日休假的实际休假时间段
     *
     * @param leaveDaytime
     * @param shiftDef
     * @return
     */
    public WaLeaveDaytimeExtDto calLeaveDayRealTimeSlot(WaLeaveDaytimeExtPo leaveDaytime, WaShiftDef shiftDef) {
        WaLeaveDaytimeExtDto leaveDaytimeExtDto = new WaLeaveDaytimeExtDto();
        leaveDaytimeExtDto.setEmpId(leaveDaytime.getEmpid());
        leaveDaytimeExtDto.setCreateTime(leaveDaytime.getCreateTime());
        leaveDaytimeExtDto.setLeaveDate(leaveDaytime.getLeaveDate());
        Integer periodType = Integer.valueOf(leaveDaytime.getPeriodType());
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        // 跨夜
        boolean isKy = CdWaShiftUtil.checkCrossNightV2(shiftDef, shiftDef.getDateType());
        long leaveDate = leaveDaytime.getLeaveDate();
        long leaveTomorrowDate = DateUtil.addDate(leaveDaytime.getLeaveDate() * 1000, 1);
        if (WaLeavePeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType)) {
            // 整天
            leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (shiftWorkTime.getStartTime() * 60));
            long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
            leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftWorkTime.getEndTime() * 60));
        } else if (WaLeavePeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
            // 半天
            if ("A".equals(leaveDaytime.getShalfDay()) && "P".equals(leaveDaytime.getEhalfDay())) {
                // 一整天
                leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (shiftWorkTime.getStartTime() * 60));
                long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
                leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftWorkTime.getEndTime() * 60));
            } else if ("A".equals(leaveDaytime.getShalfDay()) && "A".equals(leaveDaytime.getEhalfDay())) {
                // 上半天
                leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (shiftWorkTime.getStartTime() * 60));
                // 计算半天定义时间点
                Integer halfDayTime = getHalfdayTime(shiftDef, HalfDayTypeEnum.A);
                if (null != halfDayTime) {
                    boolean leaveKy = shiftWorkTime.getStartTime() > halfDayTime;
                    long leaveEndDate = leaveKy ? leaveTomorrowDate : leaveDate;
                    leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (halfDayTime * 60));
                } else {
                    long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
                    leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftWorkTime.getEndTime() * 60));
                }
            } else if ("P".equals(leaveDaytime.getShalfDay()) && "P".equals(leaveDaytime.getEhalfDay())) {
                // 下半天
                // 计算半天定义时间点
                Integer halfDayTime = getHalfdayTime(shiftDef, HalfDayTypeEnum.P);
                if (null != halfDayTime) {
                    boolean leaveKy = shiftWorkTime.getStartTime() > halfDayTime;
                    long leaveStartDate = leaveKy ? leaveTomorrowDate : leaveDate;
                    leaveDaytimeExtDto.setLeaveStartTime(leaveStartDate + (halfDayTime * 60));

                    long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
                    leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftWorkTime.getEndTime() * 60));
                } else {
                    leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (shiftWorkTime.getStartTime() * 60));
                    long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
                    leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftWorkTime.getEndTime() * 60));
                }
            }
        } else if (WaLeavePeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
            // 小时整天
            if (null != leaveDaytime.getStartTime() && null != leaveDaytime.getEndTime()) {
                leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (leaveDaytime.getStartTime() * 60));
                boolean leaveKy = leaveDaytime.getStartTime() > leaveDaytime.getEndTime();
                long leaveEndDate = leaveKy ? leaveTomorrowDate : leaveDate;
                leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (leaveDaytime.getEndTime() * 60));
            } else {
                leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (shiftWorkTime.getStartTime() * 60));
                long leaveEndDate = isKy ? leaveTomorrowDate : leaveDate;
                leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (shiftWorkTime.getEndTime() * 60));
            }
        } else if (WaLeavePeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(periodType)) {
            // 小时
            leaveDaytimeExtDto.setLeaveStartTime(leaveDate + (leaveDaytime.getStartTime() * 60));
            boolean leaveKy = leaveDaytime.getStartTime() > leaveDaytime.getEndTime();
            long leaveEndDate = leaveKy ? leaveTomorrowDate : leaveDate;
            leaveDaytimeExtDto.setLeaveEndTime(leaveEndDate + (leaveDaytime.getEndTime() * 60));
        }
        return leaveDaytimeExtDto;
    }

    public boolean checkTimeRangeOverlap(long start, long end, long start1, long end1) {
        if (end <= start1 || start >= end1) {
            return false;
        }
        return true;
    }

    /**
     * 每日休假时间重叠校验
     *
     * @param empId       员工ID
     * @param apply
     * @param shiftDef
     * @param waLeaveType
     * @return
     */
    public boolean checkLeaveDayTimeOverlap(Long empId, WaLeaveDaytime apply,
                                            WaShiftDef shiftDef, WaLeaveType waLeaveType,
                                            Map<Integer, WaShiftDef> shiftMap) {
        // 1、先查询申请日期当天的休假数据，没有：允许申请，有：继续校验
        long startTime = apply.getLeaveDate();
        long endTime = startTime + 86399;
        WaLeaveTypeDef waLeaveTypeDef = waLeaveTypeDefMapper.selectByPrimaryKey(waLeaveType.getLeaveType());
        boolean isBrj = waLeaveTypeDef != null && "BRJ".equals(waLeaveTypeDef.getLeaveTypeDefCode()); // 哺乳假
        List<WaLeaveDaytimeExtPo> leaveDayTimes = waMapper.selectLeaveDaytimeList(empId, startTime, endTime, isBrj, null);
        if (CollectionUtils.isEmpty(leaveDayTimes)) {
            return false;
        }
        // 计算待申请单的实际休假开始、结束时间
        WaLeaveDaytimeExtPo waLeaveDaytimeExtPo = ObjectConverter.convert(apply, WaLeaveDaytimeExtPo.class);
        WaLeaveDaytimeExtDto applyExtDto = calLeaveDayRealTimeSlot(waLeaveDaytimeExtPo, shiftDef);
        // 计算已经申请的休假单的实际休假开始、结束时间
        List<WaLeaveDaytimeExtDto> ltExtDtoList = leaveDayTimes.stream()
                .map(ltDay -> {
                    WaShiftDef userShiftDef = shiftDef;
                    Integer useShiftDefId = ltDay.getUseShiftDefId();
                    if (null != useShiftDefId && null != shiftMap.get(useShiftDefId)) {
                        userShiftDef = shiftMap.get(useShiftDefId);
                    }
                    return calLeaveDayRealTimeSlot(ltDay, userShiftDef);
                })
                .collect(Collectors.toList());
        // 2、当天有休假单，检查休假单的时间是否和申请单的时间重叠，如不重叠：允许申请，重叠：继续校验
        List<WaLeaveDaytimeExtDto> overlapLeaveList = Lists.newArrayList();// 和当前申请单有交集的休假数据
        for (WaLeaveDaytimeExtDto ltExtDto : ltExtDtoList) {
            boolean timeOverlap = checkTimeRangeOverlap(applyExtDto.getLeaveStartTime(), applyExtDto.getLeaveEndTime(),
                    ltExtDto.getLeaveStartTime(), ltExtDto.getLeaveEndTime());
            if (timeOverlap) {
                overlapLeaveList.add(ltExtDto);
            }
        }
        if (CollectionUtils.isEmpty(overlapLeaveList)) {
            return false;
        }
        // 3、查询当天的销假数据，如：没有：不允许申请，如有：继续校验
        List<Integer> leaveIds = leaveDayTimes.stream().map(WaLeaveDaytimeExtPo::getLeaveId).collect(Collectors.toList());
        List<WaLeaveCancelDayTime> leaveCancelDayTimes = waMapper.selectLeaveCancelDaytimeList(empId, startTime, endTime, leaveIds);
        if (CollectionUtils.isEmpty(leaveCancelDayTimes)) {
            return true;
        }
        // 计算销假单实际的销假开始、结束时间
        List<WaLeaveDaytimeExtDto> leaveCancelDaytimeExtDtos = leaveCancelDayTimes.stream().map(leaveCancelDayTime -> {
            WaLeaveDaytimeExtPo waLeaveCancelDayTime = ObjectConverter.convert(leaveCancelDayTime, WaLeaveDaytimeExtPo.class);
            waLeaveCancelDayTime.setLeaveDate(leaveCancelDayTime.getLeaveCancelDate());
            WaShiftDef userShiftDef = shiftDef;
            Integer useShiftDefId = leaveCancelDayTime.getShiftDefId();
            if (null != useShiftDefId && null != shiftMap.get(useShiftDefId)) {
                userShiftDef = shiftMap.get(useShiftDefId);
            }
            WaLeaveDaytimeExtDto extDto = calLeaveDayRealTimeSlot(waLeaveCancelDayTime, userShiftDef);
            extDto.setXj(true);
            return extDto;
        }).collect(Collectors.toList());
        List<WaLeaveDaytimeExtDto> allDaytimeExtDtoList = new ArrayList<>();
        allDaytimeExtDtoList.addAll(ltExtDtoList);
        allDaytimeExtDtoList.addAll(leaveCancelDaytimeExtDtos);
        allDaytimeExtDtoList.sort(Comparator.comparing(WaLeaveDaytimeExtDto::getCreateTime));
        // 根据休假单、销假单、待申请单 计算待申请单是否存在时间重叠，是否可请
        List<TimeRangeCheckUtil.ChangeData> leaveTimeRangeList = allDaytimeExtDtoList.stream()
                .map(lt -> TimeRangeCheckUtil.ChangeData.from(!lt.isXj(), lt.getLeaveStartTime(), lt.getLeaveEndTime() - 1))
                .collect(Collectors.toList());
        boolean ask = TimeRangeCheckUtil.ask(applyExtDto.getLeaveStartTime(), applyExtDto.getLeaveEndTime() - 1, leaveTimeRangeList);
        return !ask;
    }

    public WaLeaveDaytime doBuildLeaveDaytime(Map<Integer, WaShiftDef> shiftMap,
                                              Map<Long, WaWorktimeDetail> pbMap,
                                              Map<String, Object> leaveTimeMap,
                                              long leaveDate,
                                              WaLeaveType waLeaveType,
                                              Long empid,
                                              Integer useShiftDefId) throws Exception {
        Integer period = (Integer) leaveTimeMap.get("period");
        String startTimeStr = (String) leaveTimeMap.get("starttime");
        String endTimeStr = (String) leaveTimeMap.get("endtime");

        // 查询排班
        WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
        WaShiftDef shiftDef = shiftMap.get(Optional.ofNullable(useShiftDefId).orElse(worktimeDetail.getShiftDefId()));

        // TODO 检查休假时间是否属于当天的班次
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);
        boolean belongToday = checkLeaveTimeIfBelongToday(shiftMap, pbMap, endDate, period, worktimeDetail);

        // 构造每日明细数据对象
        WaLeaveDaytime dayTime = new WaLeaveDaytime();
        dayTime.setUseShiftDefId(useShiftDefId);
        dayTime.setLeaveDate(leaveDate);
        dayTime.setPeriodType(period.shortValue());
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period) || PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
            dayTime.setTimeUnit((short) 1);
        } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period) || PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            dayTime.setTimeUnit((short) 2);
        }
        dayTime.setDateType(worktimeDetail.getDateType());
        // 计算休假时长
        if (belongToday && DateTypeEnum.DATE_TYP_2.getIndex().equals(worktimeDetail.getDateType())
                && (!waLeaveType.getIsRestDay())) {// 休息日不连续计算
            dayTime.setTimeDuration(0f);
        } else if (belongToday && (DateTypeEnum.DATE_TYP_3.getIndex().equals(worktimeDetail.getDateType())
                || DateTypeEnum.DATE_TYP_5.getIndex().equals(worktimeDetail.getDateType()))
                && (!waLeaveType.getIsLegalHoliday())) {// 定假日不连续计算
            dayTime.setTimeDuration(0f);
        } else if (belongToday && DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType())
                && (!waLeaveType.getIsRestDay())) { // 特殊休日不连续计算
            dayTime.setTimeDuration(0f);
        } else {
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)) {
                dayTime.setTimeDuration(1f);
            } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
                this.calLeaveTimeByPeriod3(startTimeStr, endTimeStr, dayTime, waLeaveType, pbMap, shiftMap);
                if (BooleanUtils.isTrue(waLeaveType.getIsAdjustWorkHour()) && shiftDef != null) {
                    Float timeDuration = this.getAdjustWorkTime(dayTime.getStartTime(), dayTime.getEndTime(), shiftDef);
                    if (timeDuration != null) {
                        dayTime.setBeforeAdjustTimeDuration(dayTime.getTimeDuration());
                        dayTime.setTimeDuration(timeDuration);
                    }
                }
            } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
                WaLeaveTypeDef waLeaveTypeDef = waLeaveTypeDefMapper.selectByPrimaryKey(waLeaveType.getLeaveType());
                if ("BRJ".equals(waLeaveTypeDef.getLeaveTypeDefCode())) {
                    processLeaveByPeriod4ForBrj(empid, leaveTimeMap, waLeaveType, shiftDef, dayTime);
                } else {
                    this.processLeaveByPeriod4(waLeaveType, worktimeDetail, shiftDef, dayTime);
                }
            } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
                this.processLeaveByPeriod9(waLeaveType, worktimeDetail, dayTime,
                        leaveTimeMap.get("shalfday").toString(),
                        leaveTimeMap.get("ehalfday").toString(),
                        DateUtil.getTimesampByDateStr2(startTimeStr),
                        DateUtil.getTimesampByDateStr2(endTimeStr));
            }
            // 请假时长折算
            convertLtTime(period, dayTime, waLeaveType, shiftDef);
        }
        return dayTime;
    }

    /**
     * 检查休假时间是否属于当天的班次
     *
     * @param shiftMap
     * @param pbMap
     * @param leaveDate
     * @param period
     * @param worktimeDetail
     * @return
     */
    private boolean checkLeaveTimeIfBelongToday(Map<Integer, WaShiftDef> shiftMap,
                                                Map<Long, WaWorktimeDetail> pbMap,
                                                long leaveDate, Integer period,
                                                WaWorktimeDetail worktimeDetail) {
        boolean belongToday = true;
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType())
                && PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            // 当天非工作日并且休的是小时假时，需要判断前一天是否为跨夜班，如果前一天是跨夜班，说明当天的休假时间有可能属于前一天班次
            Long preLeaveDate = DateUtil.addDate(leaveDate * 1000, -1);
            WaWorktimeDetail preLeaveDateWorkTime = pbMap.get(preLeaveDate);
            WaShiftDef preLeaveDateShift = null != preLeaveDateWorkTime ? shiftMap.get(preLeaveDateWorkTime.getShiftDefId()) : null;
            if (null != preLeaveDateShift
                    && DateTypeEnum.DATE_TYP_1.getIndex().equals(preLeaveDateWorkTime.getDateType())
                    && CdWaShiftUtil.checkCrossNightV2(preLeaveDateShift, preLeaveDateWorkTime.getDateType())) {
                belongToday = false;
            }
        }
        return belongToday;
    }

    private void processLeaveByPeriod4ForBrj(Long empid,
                                             Map<String, Object> leaveTimeMap,
                                             WaLeaveType waLeaveType,
                                             WaShiftDef shiftDef,
                                             WaLeaveDaytime dayTime) {
        // 哺乳假
        boolean brjIfCalTimeDuration = true;
        float sumDurationForNonBbj = 0f;

        // 查询当天申请的非哺乳假
        Map<String, Object> pram = new HashMap<>();
        long leaveDate = dayTime.getLeaveDate();
        pram.put("start", leaveDate);
        pram.put("end", leaveDate + 86399);
        pram.put("empid", empid);
        List<Map> leaveRepeat = waCheckMapper.getLeaveRepeat(pram);
        for (Map leaveRepeatMap : leaveRepeat) {
            Integer periodType = (Integer) leaveRepeatMap.get("period_type");
            Float duration = (Float) leaveRepeatMap.get("time_duration");
            Float cancelTimeDuration = (Float) leaveRepeatMap.get("cancelTimeDuration");
            if (duration.equals(cancelTimeDuration)) {
                continue;
            }
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType)
                    || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
                brjIfCalTimeDuration = false;
                break;
            } else {
                if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    if (duration < 1) {
                        Integer workTotalTime = shiftDef.getWorkTotalTime();
                        int halfWorkTime = workTotalTime / 2;
                        String shalfDay = (String) leaveRepeatMap.get("shalf_day");
                        String ehalfDay = (String) leaveRepeatMap.get("ehalf_day");
                        //上半天时长
                        if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(shalfDay) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(ehalfDay)) {
                            halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                            duration = Float.valueOf(halfWorkTime);
                        }
                        //下半天时长
                        if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(shalfDay) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(ehalfDay)) {
                            halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                            duration = Float.valueOf(halfWorkTime);
                        }
                    } else {
                        brjIfCalTimeDuration = false;
                        break;
                    }
                }
                sumDurationForNonBbj = sumDurationForNonBbj + duration;
            }
        }

        // 计算班次工作时长
        Float workTotalTime = getBrjWorkTotalTime(waLeaveType, shiftDef);
        if (sumDurationForNonBbj >= workTotalTime) {
            brjIfCalTimeDuration = false;
        }

        // 如是休息日班次或者整天申请其他假已审批通过（请假时长=班次时长），那么这天请假时长=0 CAIDAOM-528
        if (brjIfCalTimeDuration) {
            Double dailyDuration = leaveTimeMap.get("dailyDuration") == null ? 0d : (Double) leaveTimeMap.get("dailyDuration");
            dailyDuration = dailyDuration * 60;
            if (dailyDuration > workTotalTime) {
                dayTime.setTimeDuration(workTotalTime);
            } else {
                dayTime.setTimeDuration(dailyDuration.floatValue());
            }
        } else {
            dayTime.setTimeDuration(0f);
        }
    }

    private Float getBrjWorkTotalTime(WaLeaveType waLeaveType, WaShiftDef shiftDef) {
        // 特殊班次工时调整
        boolean isSpecial = Optional.ofNullable(shiftDef.getIsSpecial()).orElse(false)
                && shiftDef.getSpecialWorkTime() != null;
        Float workTotalTime = Float.valueOf(shiftDef.getWorkTotalTime());
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType())) {
            if (isSpecial) {
                workTotalTime = shiftDef.getSpecialWorkTime().floatValue();
            }
        } else if (DateTypeEnum.DATE_TYP_2.getIndex().equals(shiftDef.getDateType()) && waLeaveType.getIsRestDay()) {
            if (isSpecial) {
                workTotalTime = shiftDef.getSpecialWorkTime().floatValue();
            } else {
                workTotalTime = 8 * 60f;
            }
        } else if ((DateTypeEnum.DATE_TYP_3.getIndex().equals(shiftDef.getDateType())
                || DateTypeEnum.DATE_TYP_5.getIndex().equals(shiftDef.getDateType())) && waLeaveType.getIsLegalHoliday()) {
            if (isSpecial) {
                workTotalTime = shiftDef.getSpecialWorkTime().floatValue();
            } else {
                workTotalTime = 8 * 60f;
            }
        } else if (DateTypeEnum.DATE_TYP_4.getIndex().equals(shiftDef.getDateType()) && waLeaveType.getIsRestDay()) {
            if (isSpecial) {
                workTotalTime = shiftDef.getSpecialWorkTime().floatValue();
            } else {
                workTotalTime = 8 * 60f;
            }
        }
        return workTotalTime;
    }

    /**
     * 请假配额校验或者扣减
     *
     * @param waLeaveType
     * @param allDaytimeList
     * @param empInfo
     * @param userId
     * @param onlyCheck
     * @param approvalStatus
     * @return
     */
    public String checkOrDecLeaveQuota(WaLeaveType waLeaveType, List<WaLeaveDaytime> allDaytimeList, SysEmpInfo empInfo,
                                       Long userId, boolean onlyCheck, Integer approvalStatus, String homeLeaveType, String marriageStatus) {
        if (CollectionUtils.isEmpty(allDaytimeList) || waLeaveType == null) {
            return "";
        }
        if (!QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType())) {
            return "";
        }
        // 额度类型
        Integer quotaType = waLeaveType.getQuotaType();
        if (quotaType == null) {
            if (waLeaveType.getLeaveType() == 3) {
                //假期类型为调休
                quotaType = QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex();
            } else {
                quotaType = QuotaTypeEnum.ISSUED_ANNUALLY.getIndex();
            }
        }
        if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(quotaType)) {
            return checkEmpCompensatoryQuota(userId, allDaytimeList, empInfo.getEmpid(), waLeaveType, onlyCheck, approvalStatus, false);
        } else {
            return checkEmpQuota(userId, allDaytimeList, empInfo.getEmpid(), waLeaveType, onlyCheck, approvalStatus, false, homeLeaveType, marriageStatus);
        }
    }

    public String checkEmpCompensatoryQuota(Long userId, List<WaLeaveDaytime> allDaytimeList, Long empid,
                                            WaLeaveType waLeaveType, boolean onlyCheck, Integer approvalStatus, Boolean isNegative) {
        if (CollectionUtils.isNotEmpty(allDaytimeList)) {
            allDaytimeList = allDaytimeList.stream().filter(o -> o.getTimeDuration() > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allDaytimeList)) {
                return "";
            }
            List<Long> tsList = allDaytimeList.stream().map(WaLeaveDaytime::getLeaveDate).sorted(Long::compareTo).collect(Collectors.toList());
            Long minDate = tsList.get(0);
            Long maxDate = tsList.get(tsList.size() - 1);
            List<UsableCompensatoryQuotaDto> usableCompensatoryQuotaDtos = waEmpCompensatoryQuotaMapper.selectUsableCompensatoryQuotaList(empid, waLeaveType.getLeaveTypeId(), minDate, maxDate);
            if (CollectionUtils.isEmpty(usableCompensatoryQuotaDtos)) {
                //return "没有调休配额可使用";
                return ResponseWrap.wrapResult(AttendanceCodes.COMPENSATORY_QUOTA_NO_LEFT, null).getMsg();
            }
            Float duration;
            Float usableDay;
            Float usedDay;
            Long leaveDate;
            for (WaLeaveDaytime waLeaveDaytime : allDaytimeList) {
                leaveDate = waLeaveDaytime.getLeaveDate();
                duration = waLeaveDaytime.getTimeDuration();
                for (UsableCompensatoryQuotaDto usableCompensatoryQuotaDto : usableCompensatoryQuotaDtos) {
                    if (usableCompensatoryQuotaDto.getStartDate() <= leaveDate && usableCompensatoryQuotaDto.getLastDate() >= leaveDate) {
                        usableDay = usableCompensatoryQuotaDto.getUsableDay();
                        if (usableDay <= 0) {
                            continue;
                        }
                        usedDay = usableCompensatoryQuotaDto.getUsedDay();
                        if (duration > usableDay) {
                            duration = duration - usableDay;
                            usedDay += usableDay;
                            usableCompensatoryQuotaDto.setUsableDay(0F);
                            usableCompensatoryQuotaDto.setUsedDay(usedDay);
                            saveCompensatoryDetail(onlyCheck, userId, waLeaveDaytime, usableDay, usableCompensatoryQuotaDto, approvalStatus);
                        } else {
                            usableCompensatoryQuotaDto.setUsableDay(usableDay - duration);
                            usedDay += duration;
                            usableCompensatoryQuotaDto.setUsedDay(usedDay);
                            saveCompensatoryDetail(onlyCheck, userId, waLeaveDaytime, duration, usableCompensatoryQuotaDto, approvalStatus);
                            duration = 0F;
                            break;
                        }
                    }
                }
                if (duration > 0) {
                    if (BooleanUtils.isTrue(isNegative)) {
                        if (!onlyCheck) {
                            UsableCompensatoryQuotaDto empQuoDto = usableCompensatoryQuotaDtos.get(usableCompensatoryQuotaDtos.size() - 1);
                            empQuoDto.setUsableDay(0F);
                            empQuoDto.setUsedDay(empQuoDto.getUsedDay() + duration);
                            saveCompensatoryDetail(onlyCheck, userId, waLeaveDaytime, duration, empQuoDto, approvalStatus);
                        }
                    } else {
                        //return DateUtil.getDateStrByTimesamp(leaveDate) + "的调休配额不足";
                        return String.format(ResponseWrap.wrapResult(AttendanceCodes.COMPENSATORY_QUOTA_NOT_ENOUGH, null).getMsg(), DateUtil.getDateStrByTimesamp(leaveDate));
                    }
                }
            }
        }
        return "";
    }

    /**
     * 非调休配额扣减
     *
     * @param userId
     * @param allDaytimeList
     * @param empid          员工ID
     * @param waLeaveType
     * @param onlyCheck
     * @return
     */

    public String checkEmpQuota(Long userId, List<WaLeaveDaytime> allDaytimeList, Long empid, WaLeaveType waLeaveType,
                                boolean onlyCheck, Integer approvalStatus, Boolean isNegative) {
        return checkEmpQuota(userId, allDaytimeList, empid, waLeaveType, onlyCheck, approvalStatus, isNegative, null, null);
    }

    /**
     * 非调休配额额度使用检查
     *
     * @param userId
     * @param allDaytimeList
     * @param empid
     * @param waLeaveType
     * @param onlyCheck
     * @param approvalStatus
     * @param isNegative
     * @param homeLeaveType  探亲假
     * @param marriageStatus
     * @return
     */
    public String checkEmpQuota(Long userId, List<WaLeaveDaytime> allDaytimeList, Long empid, WaLeaveType waLeaveType,
                                boolean onlyCheck, Integer approvalStatus, Boolean isNegative, String homeLeaveType, String marriageStatus) {
        if (CollectionUtils.isNotEmpty(allDaytimeList)) {
            allDaytimeList = allDaytimeList.stream().filter(o -> o.getTimeDuration() > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allDaytimeList)) {
                return "";
            }
            List<Long> dateList = allDaytimeList.stream().map(WaLeaveDaytime::getLeaveDate).sorted(Long::compareTo).collect(Collectors.toList());
            Long minDate = dateList.get(0);
            Long maxDate = dateList.get(dateList.size() - 1);

            // 查询可用配额
            List<EmpQuoDto> empQuoDtoList = waMapper.selectUsableEmpQuotaList(empid, waLeaveType.getLeaveTypeId(), minDate, maxDate)
                    .stream().filter(quota -> !QuotaTypeEnum.FIXED_QUOTA.getIndex().equals(waLeaveType.getQuotaType())
                            || (Optional.ofNullable(quota.getUsedDay()).orElse(0f) <= 0
                            && Optional.ofNullable(quota.getInTransitQuota()).orElse(0f) <= 0))
                    .filter(it -> StringUtils.isEmpty(homeLeaveType) || it.getUsedDay() <= 0)
                    .collect(Collectors.toList());

            // 探亲假
            if (StringUtils.isNotEmpty(homeLeaveType) && CollectionUtils.isNotEmpty(empQuoDtoList)) {
                List<Integer> quotaIds = empQuoDtoList.stream().map(EmpQuoDto::getEmpQuotaId).collect(Collectors.toList());
                List<HomeLeaveType> types = null;
                if ("visiting_parents".equals(homeLeaveType)) {
                    if ("1".equals(marriageStatus)) {
                        types = Lists.newArrayList(
                                HomeLeaveType.MARRIED_VISIT_PARENTS,
                                HomeLeaveType.MARRIED_VISIT_BOTH,
                                HomeLeaveType.MARRIED_OR_NOT_VISIT_PARENTS
                        );
                    } else {
                        types = Lists.newArrayList(
                                HomeLeaveType.NOT_MARRIED_VISIT_PARENTS,
                                HomeLeaveType.MARRIED_OR_NOT_VISIT_PARENTS
                        );
                    }
                } else {
                    types = Lists.newArrayList(
                            HomeLeaveType.MARRIED_VISIT_BOTH,
                            HomeLeaveType.MARRIED_VISIT_SPOUSE
                    );
                }
                List<Integer> filterQuotaIds = quotaMapper.filterByHomeLeaveType(quotaIds, types);
                empQuoDtoList = empQuoDtoList.stream().filter(it ->
                        filterQuotaIds.contains(it.getEmpQuotaId())
                ).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(empQuoDtoList)) {
                    val filterQuotaIds2 = quotaMapper.filterNotInTransit(filterQuotaIds);
                    if (filterQuotaIds2.isEmpty()) {
                        //return "已提交相同假期类型的申请";
                        return ResponseWrap.wrapResult(AttendanceCodes.SAME_LEAVE_APPLY_SUBMIT, null).getMsg();
                    }
                    empQuoDtoList = empQuoDtoList.stream().filter(it ->
                            filterQuotaIds2.contains(it.getEmpQuotaId())
                    ).collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isEmpty(empQuoDtoList)) {
                log.info("checkEmpQuota get EmpQuoDto is Empty");
                return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_QUOTA_NO_LEFT, null).getMsg();
            }
            List<WaLeaveQuotaUse> useAddList = new ArrayList<>();
            Float duration;
            Float usableDay;
            Float usedDay;
            Long leaveDate;
            for (WaLeaveDaytime waLeaveDaytime : allDaytimeList) {
                duration = waLeaveDaytime.getTimeDuration();
                leaveDate = waLeaveDaytime.getLeaveDate();
                for (EmpQuoDto empQuoDto : empQuoDtoList) {
                    if (empQuoDto.getStartDate() <= leaveDate && empQuoDto.getLastDate() >= leaveDate) {
                        usableDay = empQuoDto.getUsableQuota();
                        usedDay = empQuoDto.getUsedDay();
                        if (usableDay <= 0) {
                            continue;
                        }
                        if (duration > usableDay) {
                            //额度不足
                            empQuoDto.setUsableQuota(0F);
                            duration = duration - usableDay;
                            usedDay += usableDay;
                            empQuoDto.setUsedDay(usedDay);
                            if (!onlyCheck) {
                                saveWaLeaveQuotaUse(userId, waLeaveDaytime, usableDay, empQuoDto, useAddList, approvalStatus);
                            }
                        } else {
                            empQuoDto.setUsableQuota(usableDay - duration);
                            usedDay += duration;
                            empQuoDto.setUsedDay(usedDay);
                            if (!onlyCheck) {
                                saveWaLeaveQuotaUse(userId, waLeaveDaytime, duration, empQuoDto, useAddList, approvalStatus);
                            }
                            duration = 0F;
                            break;
                        }
                    }
                }
                if (duration > 0) {
                    if (BooleanUtils.isTrue(isNegative)) {
                        if (!onlyCheck) {
                            EmpQuoDto empQuoDto = empQuoDtoList.get(empQuoDtoList.size() - 1);
                            empQuoDto.setUsableQuota(0F);
                            empQuoDto.setUsedDay(empQuoDto.getUsedDay() + duration);
                            saveWaLeaveQuotaUse(userId, waLeaveDaytime, duration, empQuoDto, useAddList, approvalStatus);
                        }
                    } else {
                        return String.format(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_QUOTA_NOT_ENOUGH, null).getMsg(), DateUtil.getDateStrByTimesamp(leaveDate));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(useAddList)) {
                waLeaveQuotaUseMapper.insertBatch(useAddList);
            } else {
                log.info("checkEmpQuota insertBatch useAddList is Empty");
            }
        }
        return "";
    }

    private void saveWaLeaveQuotaUse(Long userId, WaLeaveDaytime waLeaveDaytime, Float duration, EmpQuoDto empQuoDto,
                                     List<WaLeaveQuotaUse> useAddList, Integer approvalStatus) {
        Long currentTime = DateUtil.getCurrentTime(false);
        WaLeaveQuotaUse quotaUse = new WaLeaveQuotaUse();
        quotaUse.setUseId(snowflakeUtil.createId());
        quotaUse.setEmpId(empQuoDto.getEmpId());
        quotaUse.setApprovalStatus(approvalStatus);
        quotaUse.setDeleted(0);
        quotaUse.setLeaveDate(waLeaveDaytime.getLeaveDate());
        quotaUse.setLeaveDaytimeId(waLeaveDaytime.getLeaveDaytimeId());
        quotaUse.setEmpQuotaId(empQuoDto.getEmpQuotaId());
        quotaUse.setTimeDuration(duration);
        quotaUse.setCreateBy(Long.valueOf(userId.toString()));
        quotaUse.setCreateTime(currentTime);
        quotaUse.setUpdateBy(Long.valueOf(userId.toString()));
        quotaUse.setUpdateTime(currentTime);
        quotaUse.setCancelTimeDuration(0f);
        useAddList.add(quotaUse);
    }

    private void saveCompensatoryDetail(boolean onlyCheck, Long userId, WaLeaveDaytime waLeaveDaytime, Float duration,
                                        UsableCompensatoryQuotaDto usableCompensatoryQuotaDto, Integer approvalStatus) {
        if (!onlyCheck) {
            WaCompensatoryQuotaUse waCompensatoryQuotaUse = new WaCompensatoryQuotaUse();

            Long currentTime = DateUtil.getCurrentTime(false);
            waCompensatoryQuotaUse.setUseId(snowflakeUtil.createId());
            waCompensatoryQuotaUse.setEmpId(usableCompensatoryQuotaDto.getEmpId());
            waCompensatoryQuotaUse.setApprovalStatus(approvalStatus);
            waCompensatoryQuotaUse.setDeleted(0);
            waCompensatoryQuotaUse.setLeaveDate(waLeaveDaytime.getLeaveDate());
            waCompensatoryQuotaUse.setLeaveDaytimeId(ConvertHelper.longConvert(waLeaveDaytime.getLeaveDaytimeId()));
            waCompensatoryQuotaUse.setQuotaId(usableCompensatoryQuotaDto.getQuotaId());
            waCompensatoryQuotaUse.setTimeDuration(duration);
            waCompensatoryQuotaUse.setCancelTimeDuration(0f);
            waCompensatoryQuotaUse.setCreateBy(Long.valueOf(userId));
            waCompensatoryQuotaUse.setCreateTime(currentTime);
            waCompensatoryQuotaUse.setUpdateBy(Long.valueOf(userId));
            waCompensatoryQuotaUse.setUpdateTime(currentTime);

            waCompensatoryQuotaUseMapper.insert(waCompensatoryQuotaUse);
        }
    }

    /**
     * 日期类型校验
     *
     * @param waLeaveType
     * @param dateType
     * @param startTimeStr
     * @param startDate
     * @param endDate
     * @return
     */
    public String checkLeaveDateType(WaLeaveType waLeaveType, Integer dateType, String startTimeStr, Long startDate, Long endDate) {
        if (startDate.equals(endDate)) {
            if (DateTypeEnum.DATE_TYP_2.getIndex().equals(dateType) && (waLeaveType.getIsRestDay() == null || !waLeaveType.getIsRestDay())) {
                // 休息日不能请假
                return MessageHandler.getMessage("caidao.exception.error_202808", WebUtil.getRequest());
            } else if (DateTypeEnum.DATE_TYP_3.getIndex().equals(dateType) && (waLeaveType.getIsLegalHoliday() == null || !waLeaveType.getIsLegalHoliday())) {
                // 法定节假日不能请假
                return MessageHandler.getMessage("caidao.exception.error_202809", WebUtil.getRequest());
            } else if (DateTypeEnum.DATE_TYP_5.getIndex().equals(dateType) && (waLeaveType.getIsLegalHoliday() == null || !waLeaveType.getIsLegalHoliday())) {
                // 法定休日不能请假
                return MessageHandler.getMessage("caidao.exception.error_202810", WebUtil.getRequest());
            } else if (DateTypeEnum.DATE_TYP_4.getIndex().equals(dateType) && (waLeaveType.getIsRestDay() == null || !waLeaveType.getIsRestDay())) {
                // 特殊休日不能请假
                return MessageHandler.getMessage("caidao.exception.error_202811", WebUtil.getRequest());
            }
        } else {
            if (DateTypeEnum.DATE_TYP_2.getIndex().equals(dateType) && (waLeaveType.getIsRestDay() == null || !waLeaveType.getIsRestDay())) {
                // {0}为休息日，不能为请假开始日
                return messageResource.getMessage("L005740", new Object[]{startTimeStr}, new Locale(SessionHolder.getLang()));
            } else if (DateTypeEnum.DATE_TYP_3.getIndex().equals(dateType) && (waLeaveType.getIsLegalHoliday() == null || !waLeaveType.getIsLegalHoliday())) {
                // {0}为法定假日，不能为请假开始日
                return messageResource.getMessage("L005741", new Object[]{startTimeStr}, new Locale(SessionHolder.getLang()));
            } else if (DateTypeEnum.DATE_TYP_5.getIndex().equals(dateType) && (waLeaveType.getIsLegalHoliday() == null || !waLeaveType.getIsLegalHoliday())) {
                // %s为法定休日，不能为请假开始日
                return String.format(MessageHandler.getMessage("caidao.exception.error_202812", WebUtil.getRequest()), startTimeStr);
            } else if (DateTypeEnum.DATE_TYP_4.getIndex().equals(dateType) && (waLeaveType.getIsRestDay() == null || !waLeaveType.getIsRestDay())) {
                // %s为特殊休日，不能为请假开始日
                return String.format(MessageHandler.getMessage("caidao.exception.error_202813", WebUtil.getRequest()), startTimeStr);
            }
        }
        return "";
    }

    public Result<Boolean> checkLeave(SysEmpInfo empInfo,
                                      Map<Integer, WaShiftDef> shiftMap,
                                      Map<Long, WaWorktimeDetail> pbMap,
                                      Map<String, Object> leaveTimeMap,
                                      WaLeaveType waLeaveType,
                                      boolean isImport,
                                      LeaveApplySaveDto applySaveDto) {
        WaLeaveTypeDef waLeaveTypeDef = waLeaveTypeDefMapper.selectByPrimaryKey(waLeaveType.getLeaveType());
        if (null == waLeaveTypeDef) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, null).getMsg());
        }

        Integer period = (Integer) leaveTimeMap.get("period");
        String startTimeStr = (String) leaveTimeMap.get("starttime");
        String endTimeStr = (String) leaveTimeMap.get("endtime");
        long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);

        // 排班检查
        long leaveDate = startDate;
        while (leaveDate <= endDate) {
            if (pbMap == null || pbMap.get(leaveDate) == null) {// {0}没有排班记录，不能请假
                return Result.fail(messageResource.getMessage("L005739", new Object[]{DateUtil.getDateStrByTimesamp(leaveDate)}, new Locale(SessionBeanUtil.getLanguage())));
            }
            leaveDate = leaveDate + 86400;
        }

        WaWorktimeDetail sdetail = pbMap.get(startDate);
        WaWorktimeDetail edetail = pbMap.get(endDate);

        //日期类型校验
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(edetail.getDateType())
                && PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            // 非工作日并且休的是小时假，需要先判断结束日期前一日是否为跨夜班，如果不是，则继续校验日期类型，反之无需校验
            Long preEndDate = DateUtil.addDate(endDate * 1000, -1);
            WaWorktimeDetail preEndDateWorkTime = pbMap.get(preEndDate);
            boolean preEndDateIsNight = DateTypeEnum.DATE_TYP_1.getIndex().equals(preEndDateWorkTime.getDateType())
                    && CdWaShiftUtil.checkCrossNightV3(preEndDateWorkTime, shiftMap);
            if (!preEndDateIsNight) {
                String errorMsg = checkLeaveDateType(waLeaveType, sdetail.getDateType(), startTimeStr, startDate, endDate);
                if (StringUtils.isNotBlank(errorMsg)) {
                    return Result.fail(errorMsg);
                }
            }
        } else {
            String errorMsg = checkLeaveDateType(waLeaveType, sdetail.getDateType(), startTimeStr, startDate, endDate);
            if (StringUtils.isNotBlank(errorMsg)) {
                return Result.fail(errorMsg);
            }
        }

        // 考勤帐套截止日判断
        WaSob waSob = waSobService.getWaSob(empInfo.getEmpid(), startDate);
        if (waSob != null && !isImport) {
            Integer sysPeriodMonth = waSob.getSysPeriodMonth();
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            Long onlyDate = DateUtil.getOnlyDate();
            if (onlyDate > sobEndDate) {
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
            }
        }

        // 非哺乳假：申请休假校验出差时间重复
        Result<Boolean> travelTimeCheckResult = doCheckEmpTravelTime(empInfo, leaveTimeMap, waLeaveTypeDef, shiftMap,
                pbMap, applySaveDto);
        if (!travelTimeCheckResult.isSuccess()) {
            return travelTimeCheckResult;
        }

        // 导入/接入 日期重叠校验
        if (isImport) {
            boolean timeOverlap = false;
            try {
                timeOverlap = checkLeaveTimeOverlap(pbMap, shiftMap, leaveTimeMap, waLeaveType, empInfo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (timeOverlap) {
                return Result.fail(messageResource.getMessage("L005744", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
            }
        }
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 非哺乳假：申请休假校验出差时间重复
     *
     * @param empInfo
     * @param leaveTimeMap
     * @param waLeaveTypeDef
     * @param shiftMap
     * @param pbMap
     * @param applySaveDto
     * @return
     */
    private Result<Boolean> doCheckEmpTravelTime(SysEmpInfo empInfo,
                                                 Map<String, Object> leaveTimeMap,
                                                 WaLeaveTypeDef waLeaveTypeDef,
                                                 Map<Integer, WaShiftDef> shiftMap,
                                                 Map<Long, WaWorktimeDetail> pbMap,
                                                 LeaveApplySaveDto applySaveDto) {
        if ("BRJ".equals(waLeaveTypeDef.getLeaveTypeDefCode())) {
            return Result.ok(Boolean.TRUE);
        }
        Integer period = (Integer) leaveTimeMap.get("period");
        String startTimeStr = (String) leaveTimeMap.get("starttime");
        String endTimeStr = (String) leaveTimeMap.get("endtime");
        long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);

        if (WaLeavePeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)
                || WaLeavePeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
            long leaveDate = startDate;
            while (leaveDate <= endDate) {
                WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
                // 查询当天使用的班次集合
                List<Integer> shiftDefIdList = worktimeDetail.doGetShiftDefIdList();
                if (leaveDate == startDate && CollectionUtils.isNotEmpty(applySaveDto.getStartShifts())) {
                    shiftDefIdList = applySaveDto.getStartShifts().stream().map(Long::intValue).collect(Collectors.toList());
                } else if (leaveDate == endDate && CollectionUtils.isNotEmpty(applySaveDto.getEndShifts())) {
                    shiftDefIdList = applySaveDto.getEndShifts().stream().map(Long::intValue).collect(Collectors.toList());
                }
                // 遍历每个班次判断时间重叠
                for (Integer shiftDefId : shiftDefIdList) {
                    WaShiftDef shiftDef = shiftMap.get(shiftDefId);
                    WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
                    long start = leaveDate + shiftWorkTime.doGetRealStartTime() * 60;
                    long end = leaveDate + shiftWorkTime.doGetRealEndTime() * 60;
                    if (empTravelMapper.checkEmpTravelTimeRepeat(empInfo.getEmpid(), start, end) > 0) {
                        return Result.fail(messageResource.getMessage("L005744", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
                    }
                }
                leaveDate = leaveDate + 86400;
            }
        } else if (WaLeavePeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
            Long start = (Long) IocUtil.getDateValue(startTimeStr, "BIGINT");
            Long end = (Long) IocUtil.getDateValue(endTimeStr, "BIGINT");
            if (empTravelMapper.checkEmpTravelTimeRepeat(empInfo.getEmpid(), start, end) > 0) {
                return Result.fail(messageResource.getMessage("L005744", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
            }
        } else if (WaLeavePeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
            long leaveDate = startDate;
            while (leaveDate <= endDate) {
                WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
                // 查询当天使用的班次集合
                List<Integer> shiftDefIdList = worktimeDetail.doGetShiftDefIdList();
                if (leaveDate == startDate && CollectionUtils.isNotEmpty(applySaveDto.getStartShifts())) {
                    shiftDefIdList = applySaveDto.getStartShifts().stream().map(Long::intValue).collect(Collectors.toList());
                } else if (leaveDate == endDate && CollectionUtils.isNotEmpty(applySaveDto.getEndShifts())) {
                    shiftDefIdList = applySaveDto.getEndShifts().stream().map(Long::intValue).collect(Collectors.toList());
                }
                // 遍历每个班次判断时间重叠
                for (Integer shiftDefId : shiftDefIdList) {
                    WaShiftDef shiftDef = shiftMap.get(shiftDefId);
                    BuildTimeOverlapCheckParamDto checkDto = new BuildTimeOverlapCheckParamDto();
                    checkDto.setEmpid(empInfo.getEmpid()).setPeriod(period)
                            .setStartDate(startDate).setEndDate(endDate)
                            .setStartTimeStr(startTimeStr).setEndTimeStr(endTimeStr)
                            .setShalfday(leaveTimeMap.getOrDefault("shalfday", "").toString())
                            .setEhalfday(leaveTimeMap.getOrDefault("ehalfday", "").toString())
                            .setStartDateShift(shiftDef).setEndDateShift(shiftDef)
                            .setLeaveTypeDefCode(waLeaveTypeDef.getLeaveTypeDefCode());
                    checkDto.calRealTimeForPeriodTypeNine();
                    if (empTravelMapper.checkEmpTravelTimeRepeat(empInfo.getEmpid(), checkDto.getRealStartTime(),
                            checkDto.getRealEndTime()) > 0) {
                        return Result.fail(messageResource.getMessage("L005744", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
                    }
                }
                leaveDate = leaveDate + 86400;
            }
        }
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 休假时间重叠校验
     *
     * @param pbMap
     * @param shiftMap
     * @param leaveTimeMap
     * @param waLeaveType
     * @param empInfo
     * @return
     * @throws Exception
     */
    public boolean checkLeaveTimeOverlap(Map<Long, WaWorktimeDetail> pbMap,
                                         Map<Integer, WaShiftDef> shiftMap,
                                         Map<String, Object> leaveTimeMap,
                                         WaLeaveType waLeaveType,
                                         SysEmpInfo empInfo) throws Exception {
        String startTimeStr = (String) leaveTimeMap.get("starttime");
        String endTimeStr = (String) leaveTimeMap.get("endtime");
        long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);

        WaWorktimeDetail worktimeDetail;
        WaShiftDef shiftDef;
        long leaveDate = startDate;
        while (leaveDate <= endDate) {
            worktimeDetail = pbMap.get(leaveDate);
            for (Integer shiftDefId : worktimeDetail.doGetShiftDefIdList()) {
                shiftDef = shiftMap.get(shiftDefId);
                WaLeaveDaytime dayTime = doBuildLeaveDaytime(shiftMap, pbMap, leaveTimeMap, leaveDate,
                        waLeaveType, empInfo.getEmpid(), shiftDefId);
                boolean timeOverlap = checkLeaveDayTimeOverlap(empInfo.getEmpid(), dayTime, shiftDef, waLeaveType, shiftMap);
                if (timeOverlap) {
                    return true;
                }
            }
            leaveDate = leaveDate + 86400;
        }
        return false;
    }

    public String checkMaxMinLeave(WaLeaveType waLeaveType, BigDecimal totalTimeDuration, String language) {
        if (waLeaveType.getIsCheckMaxTime() && waLeaveType.getMaxLeaveTime() != null && waLeaveType.getMaxLeaveTime() > 0) {
            if (waLeaveType.getAcctTimeType() == 2) {//小时
                BigDecimal tmpMaxTime = new BigDecimal(waLeaveType.getMaxLeaveTime());
                tmpMaxTime = tmpMaxTime.multiply(new BigDecimal(60));
                if (totalTimeDuration.floatValue() > tmpMaxTime.floatValue()) {//超过最大值
//                    return "超过最大请假值：" + waLeaveType.getMaxLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType());
                    return messageResource.getMessage("L005745", new Object[]{waLeaveType.getMaxLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType())}, new Locale(language));
                }
            } else {//按天
                if (totalTimeDuration.floatValue() > waLeaveType.getMaxLeaveTime()) {//超过最大值
//                    return "超过最大请假值：" + waLeaveType.getMaxLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType());
                    return messageResource.getMessage("L005745", new Object[]{waLeaveType.getMaxLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType())}, new Locale(language));
                }
            }
        }
        if (waLeaveType.getIsCheckMinTime() && waLeaveType.getMinLeaveTime() != null && waLeaveType.getMinLeaveTime() > 0) {
            if (waLeaveType.getAcctTimeType() == 2) {//小时
                BigDecimal tmpMinTime = new BigDecimal(waLeaveType.getMinLeaveTime());
                tmpMinTime = tmpMinTime.multiply(new BigDecimal(60));
                if (totalTimeDuration.floatValue() < tmpMinTime.floatValue()) {//超过最大值
//                    return "必须最少请假：" + waLeaveType.getMinLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType());
                    return messageResource.getMessage("L005746", new Object[]{waLeaveType.getMinLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType())}, new Locale(language));
                }
            } else {//按天
                if (totalTimeDuration.floatValue() < waLeaveType.getMinLeaveTime()) {//超过最大值
//                    return "必须最少请假：" + waLeaveType.getMinLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType());
                    return messageResource.getMessage("L005746", new Object[]{waLeaveType.getMinLeaveTime() + BaseConst.ACCT_TIME_TYPE.get(waLeaveType.getAcctTimeType())}, new Locale(language));
                }
            }
        }
        return "";
    }

    public String processLeaveByPeriod3(WaLeaveType waLeaveType, long startDate, long endDate, String startTimeStr, String endTimeStr,
                                        WaWorktimeDetail detail, WaLeaveDaytime dayTime, WaShiftDef shiftDef) throws Exception {
        //小时非整天
        Integer tmpS = 0, tmpE = 0;
        long leaveStartTime = 0;
        long leaveEndTime = 0;
        if (startDate == endDate) {//最后一天
            String ehm = endTimeStr.substring(10).trim();//如2015-09-16 09:15  会变成09:15
            String[] ehmArr = ehm.split(":");
            Integer endShm = Integer.parseInt(ehmArr[0]) * 60 + Integer.parseInt(ehmArr[1]);

            if (startDate == DateUtil.getTimesampByDateStr2(startTimeStr)) {//也是第一天，最后一天
                String shm = startTimeStr.substring(10).trim();//如2015-09-16 09:15  会变成09:15
                String[] shmArr = shm.split(":");
                Integer startShm = Integer.parseInt(shmArr[0]) * 60 + Integer.parseInt(shmArr[1]);

                if (detail.getStartTime() > startShm) {
                    tmpS = detail.getStartTime();
                } else {
                    tmpS = startShm;
                }

                if (detail.getEndTime() < endShm) {
                    tmpE = detail.getEndTime();
                } else {
                    tmpE = endShm;
                }

                leaveStartTime = startDate + tmpS * 60;
                leaveEndTime = startDate + tmpE * 60;
            } else {//是最后一天，但不是第一天
                tmpS = detail.getStartTime();
                if (detail.getEndTime() < endShm) {
                    tmpE = detail.getEndTime();
                } else {
                    tmpE = endShm;
                }

                leaveStartTime = endDate + tmpS * 60;
                leaveEndTime = endDate + tmpE * 60;
            }
            if (detail.getIsNoonRest() != null && detail.getIsNoonRest()) {
                Integer timeDuration = deductLeaveDayNooRest(shiftDef, detail, leaveStartTime, leaveEndTime);
                dayTime.setTimeDuration(new Float(timeDuration));
            } else {
                dayTime.setTimeDuration(new Float(tmpE.intValue() - tmpS.intValue()));
            }
            dayTime.setStartTime(tmpS);
            dayTime.setEndTime(tmpE);
        } else if (startDate == DateUtil.getTimesampByDateStr2(startTimeStr)) {//也是第一天，不是最后一天
            String shm = startTimeStr.substring(10).trim();//如2015-09-16 09:15  会变成09:15
            String[] shmArr = shm.split(":");
            Integer startShm = Integer.parseInt(shmArr[0]) * 60 + Integer.parseInt(shmArr[1]);
            tmpS = detail.getStartTime();
            tmpE = detail.getEndTime();
            if (startShm < detail.getStartTime()) {
                dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
            } else if (startShm > detail.getEndTime()) {
                dayTime.setTimeDuration(0f);
            } else {//说明在上班中间请假
                tmpS = startShm;

                leaveStartTime = startDate + tmpS * 60;
                leaveEndTime = startDate + tmpE * 60;

                if (detail.getIsNoonRest()) {
                    Integer timeDuration = deductLeaveDayNooRest(shiftDef, detail, leaveStartTime, leaveEndTime);
                    dayTime.setTimeDuration(new Float(timeDuration));
                } else {
                    dayTime.setTimeDuration(new Float(tmpE.intValue() - tmpS.intValue()));
                }
            }
            dayTime.setStartTime(tmpS);
            dayTime.setEndTime(tmpE);
        } else {//是中间一天
            //加上特殊日期
            //不是特殊日期，算排班
            if (detail.getDateType() == 1) {//是工作日
                dayTime.setTimeDuration(detail.getWorkTotalTime().floatValue());
                dayTime.setStartTime(detail.getStartTime());
                dayTime.setEndTime(detail.getEndTime());
            } else if (detail.getDateType() == 2 && waLeaveType.getIsRestDay()) {//是休息日连续计算
                dayTime.setTimeDuration(8 * 60f);
                dayTime.setStartTime(0);
                dayTime.setEndTime(1440);
            } else if ((detail.getDateType() == 3 || detail.getDateType() == 5) && waLeaveType.getIsLegalHoliday()) {//是法定假日连续计算
                dayTime.setTimeDuration(8 * 60f);
                dayTime.setStartTime(0);
                dayTime.setEndTime(1440);
            } else if (detail.getDateType() == 4 && waLeaveType.getIsRestDay()) {//是休息日连续计算
                dayTime.setTimeDuration(8 * 60f);
                dayTime.setStartTime(0);
                dayTime.setEndTime(1440);
            }

        }
        if (dayTime.getTimeDuration() == null || dayTime.getTimeDuration().floatValue() < 0) {
            dayTime.setTimeDuration(0f);
        }
        //特殊班次工作时长
        if (shiftDef != null && shiftDef.getIsSpecial() != null && shiftDef.getIsSpecial() && shiftDef.getSpecialWorkTime() != null
                && dayTime.getTimeDuration().equals(Float.valueOf(detail.getWorkTotalTime()))) {
            dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
        }
        return "";
    }

    public Integer getLeaveDateHour(Long time) {
        String s = DateUtil.convertDateTimeToStr(time, "HH:mm", true);
        if (StringUtils.isNotBlank(s)) {
            String[] shmArr = s.split(":");
            return Integer.parseInt(shmArr[0]) * 60 + Integer.parseInt(shmArr[1]);
        }
        return 0;
    }

    /**
     * 小时
     *
     * @param startTimeStr
     * @param endTimeStr
     * @param dayTime
     * @param waLeaveType
     * @param pbMap
     * @param shiftMap
     * @return
     * @throws Exception
     */
    public String calLeaveTimeByPeriod3(String startTimeStr, String endTimeStr,
                                        WaLeaveDaytime dayTime, WaLeaveType waLeaveType,
                                        Map<Long, WaWorktimeDetail> pbMap, Map<Integer, WaShiftDef> shiftMap) throws Exception {
        long firstDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        long lastDate = DateUtil.getTimesampByDateStr2(endTimeStr);
        long firstTime = DateUtil.getTimesampByStr2(startTimeStr);
        long lastTime = DateUtil.getTimesampByStr2(endTimeStr);

        if (firstDate == lastDate) {//请一天假
            calLeaveTimeByPeriod3ForOneDay(dayTime, firstTime, lastTime, pbMap, shiftMap);
        } else {//请多天假
            calLeaveTimeByPeriod3ForMultiDay(dayTime, firstDate, lastDate, firstTime, lastTime, pbMap,
                    shiftMap, waLeaveType);
        }
        dayTime.setTimeDuration(Optional.ofNullable(dayTime.getTimeDuration()).filter(d -> d >= 0).orElse(0f));

        // 特殊班次工作时长
        long leaveDate = dayTime.getLeaveDate();
        WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
        WaShiftDef shiftDef = shiftMap.get(Optional.ofNullable(dayTime.getUseShiftDefId()).orElse(worktimeDetail.getShiftDefId()));
        if (shiftDef.getIsSpecial() != null && shiftDef.getIsSpecial() && shiftDef.getSpecialWorkTime() != null
                && dayTime.getTimeDuration().equals(Float.valueOf(worktimeDetail.getWorkTotalTime()))) {
            dayTime.setTimeDuration(shiftDef.getSpecialWorkTime().floatValue());
        }
        return "";
    }

    /**
     * 小时假（请多天假）
     *
     * @param dayTime
     * @param firstDate
     * @param lastDate
     * @param firstTime
     * @param lastTime
     * @param pbMap
     * @param shiftMap
     * @param waLeaveType
     * @throws Exception
     */
    private void calLeaveTimeByPeriod3ForMultiDay(WaLeaveDaytime dayTime, long firstDate, long lastDate,
                                                  long firstTime, long lastTime, Map<Long, WaWorktimeDetail> pbMap,
                                                  Map<Integer, WaShiftDef> shiftMap, WaLeaveType waLeaveType) throws Exception {
        long leaveDate = dayTime.getLeaveDate();
        WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
        WaShiftDef shiftDef = shiftMap.get(Optional.ofNullable(dayTime.getUseShiftDefId()).orElse(worktimeDetail.getShiftDefId()));

        boolean includeNonWorkday = (DateTypeEnum.DATE_TYP_2.getIndex().equals(worktimeDetail.getDateType()) && waLeaveType.getIsRestDay())
                || (DateTypeEnum.DATE_TYP_3.getIndex().equals(worktimeDetail.getDateType()) && waLeaveType.getIsLegalHoliday())
                || (DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType()) && waLeaveType.getIsRestDay())
                || (DateTypeEnum.DATE_TYP_5.getIndex().equals(worktimeDetail.getDateType()) && waLeaveType.getIsLegalHoliday());

        if (leaveDate == lastDate) {//最后一天
            calLeaveTimeByPeriod3ForLastDay(dayTime, lastDate, lastTime, pbMap, shiftMap, worktimeDetail,
                    includeNonWorkday, shiftDef);
        } else if (leaveDate == firstDate) {//第一天
            calLeaveTimeByPeriod3ForFirstDay(dayTime, firstDate, lastDate, firstTime, lastTime, worktimeDetail,
                    shiftDef, includeNonWorkday);
        } else {//是中间一天
            calLeaveTimeByPeriod3ForMiddleDay(dayTime, lastDate, lastTime, worktimeDetail, shiftDef, includeNonWorkday);
        }
    }

    private void calLeaveTimeByPeriod3ForMiddleDay(WaLeaveDaytime dayTime, long lastDate, long lastTime,
                                                   WaWorktimeDetail worktimeDetail, WaShiftDef shiftDef,
                                                   boolean includeNonWorkday) throws Exception {
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        boolean isky = CdWaShiftUtil.checkCrossNightV2(shiftDef, worktimeDetail.getDateType());
        long leaveDate = dayTime.getLeaveDate();
        long workStime = leaveDate + shiftWorkTime.getStartTime() * 60;

        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType())) {
            if (leaveDate == (lastDate - 86400) && isky) {
                // 倒数第二天并且跨夜
                Integer lastMin = getLeaveDateHour(lastTime);
                if (lastMin >= shiftWorkTime.getEndTime()) {
                    dayTime.setTimeDuration(shiftDef.getWorkTotalTime().floatValue());
                    dayTime.setStartTime(shiftWorkTime.getStartTime());
                    dayTime.setEndTime(shiftWorkTime.getEndTime());
                } else {
                    Integer timeDuration = deductLeaveDayNooRest(shiftDef, worktimeDetail, workStime, lastTime);
                    dayTime.setTimeDuration(new Float(timeDuration));
                    dayTime.setStartTime(shiftWorkTime.getStartTime());
                    dayTime.setEndTime(lastMin);
                }
            } else {
                dayTime.setTimeDuration(shiftDef.getWorkTotalTime().floatValue());
                dayTime.setStartTime(shiftWorkTime.getStartTime());
                dayTime.setEndTime(shiftWorkTime.getEndTime());
            }
        } else if (includeNonWorkday) {
            dayTime.setTimeDuration(8 * 60f);
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        }
    }

    private void calLeaveTimeByPeriod3ForFirstDay(WaLeaveDaytime dayTime, long firstDate, long lastDate,
                                                  long firstTime, long lastTime, WaWorktimeDetail worktimeDetail,
                                                  WaShiftDef shiftDef, boolean includeNonWorkday) throws Exception {
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        boolean isky = CdWaShiftUtil.checkCrossNightV2(shiftDef, worktimeDetail.getDateType());
        long leaveDate = dayTime.getLeaveDate();
        long workStime = leaveDate + shiftWorkTime.getStartTime() * 60;

        Integer startMin = this.getLeaveDateHour(firstTime);
        Integer endMin = 24 * 60;
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType())) {
            if (includeNonWorkday) {
                int timeDuration = endMin - startMin;
                dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
            } else {
                startMin = 0;
                endMin = 0;
                dayTime.setTimeDuration(0f);
            }
        } else {
            //计算开始日期与结束日期相差的天数
            Integer dateDiff = DateUtilExt.getDifferenceDay(firstDate, lastDate);
            long stime = Math.max(workStime, firstTime);
            startMin = this.getLeaveDateHour(stime);
            long etime;
            if (isky) {
                if (dateDiff > 1) {// 申请日期天数>2天
                    endMin = shiftWorkTime.getEndTime();
                    etime = leaveDate + 86400 + endMin * 60;
                } else {// 申请日期天数<2天
                    endMin = Math.min(this.getLeaveDateHour(lastTime), shiftWorkTime.getEndTime());
                    etime = leaveDate + 86400 + endMin * 60;
                }
                //endMin = 24 * 60;
            } else {
                endMin = shiftWorkTime.getEndTime();
                etime = leaveDate + endMin * 60;
            }
            List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
            if (!Objects.isNull(multiWorkTimeList) && multiWorkTimeList.size() > 1) {
                Integer timeDuration = 0;
                for (MultiWorkTimeBaseDto itemWorkTime : multiWorkTimeList) {
                    long itemWorkStime = leaveDate + itemWorkTime.doGetRealStartTime() * 60;
                    long itemWorkEtime = leaveDate + itemWorkTime.doGetRealEndTime() * 60;
                    if (stime >= itemWorkEtime || etime <= itemWorkStime) {
                        continue;
                    }
                    long itemStime = Math.max(itemWorkStime, stime);
                    long itemEtime = Math.min(itemWorkEtime, etime);
                    timeDuration += deductLeaveDayNooRest(shiftDef, worktimeDetail, itemStime, itemEtime);
                }
                dayTime.setTimeDuration(new Float(timeDuration));
            } else {
                Integer timeDuration = deductLeaveDayNooRest(shiftDef, worktimeDetail, stime, etime);
                dayTime.setTimeDuration(new Float(timeDuration));
            }
        }
        dayTime.setStartTime(startMin);
        dayTime.setEndTime(endMin);
    }

    private void calLeaveTimeByPeriod3ForLastDay(WaLeaveDaytime dayTime, long lastDate, long lastTime,
                                                 Map<Long, WaWorktimeDetail> pbMap, Map<Integer, WaShiftDef> shiftMap,
                                                 WaWorktimeDetail worktimeDetail, boolean includeNonWorkday,
                                                 WaShiftDef shiftDef) throws Exception {
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        boolean isky = CdWaShiftUtil.checkCrossNightV2(shiftDef, worktimeDetail.getDateType());
        long leaveDate = dayTime.getLeaveDate();
        long workStime = leaveDate + shiftWorkTime.getStartTime() * 60;
        long workEtime = isky ? (leaveDate + 86400 + shiftWorkTime.getEndTime() * 60) : (leaveDate + shiftWorkTime.getEndTime() * 60);

        Integer startMin = 0;
        Integer endMin = getLeaveDateHour(lastTime);
        if (!DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType())) {
            // 当天非工作日时，需要判断休假时间段是否在前一天的上班范围内
            Long preDate = DateUtil.addDate(lastDate * 1000, -1);
            WaWorktimeDetail preWorkTime = pbMap.get(preDate);
            if (DateTypeEnum.DATE_TYP_1.getIndex().equals(preWorkTime.getDateType())) {
                boolean ifBelongPreDay = false;
                for (Integer preShiftDefId : preWorkTime.doGetShiftDefIdList()) {
                    WaShiftDef preShiftDef = shiftMap.get(preShiftDefId);
                    if (null == preShiftDef
                            || !CdWaShiftUtil.checkCrossNightV2(preShiftDef, preWorkTime.getDateType())) {
                        continue;
                    }
                    ifBelongPreDay = true;
                    WaShiftDef preShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(preShiftDef);
                    if (endMin <= preShiftWorkTime.getEndTime()) {
                        startMin = 0;
                        endMin = 0;
                        dayTime.setTimeDuration(0f);
                    } else {
                        startMin = preShiftWorkTime.getEndTime();
                        if (includeNonWorkday) {
                            int timeDuration = endMin - startMin;
                            dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                        } else {
                            dayTime.setTimeDuration(0f);
                        }
                    }
                    break;
                }
                if (!ifBelongPreDay) {
                    startMin = 0;
                    if (includeNonWorkday) {
                        int timeDuration = endMin - startMin;
                        dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                    } else {
                        endMin = 0;
                        dayTime.setTimeDuration(0f);
                    }
                }
            } else {
                startMin = 0;
                if (includeNonWorkday) {
                    int timeDuration = endMin - startMin;
                    dayTime.setTimeDuration(Math.min((float) timeDuration, 480f));
                } else {
                    endMin = 0;
                    dayTime.setTimeDuration(0f);
                }
            }
        } else {
            if (endMin < shiftWorkTime.getStartTime()) {
                startMin = 0;
                endMin = 0;
                dayTime.setTimeDuration(0f);
            } else {
                long stime;
                long etime;
                if (isky) {
                    startMin = getLeaveDateHour(workStime);
                    endMin = Math.min(endMin, 1440);
                    stime = workStime;
                    etime = lastDate + endMin * 60;
                } else {
                    startMin = shiftWorkTime.getStartTime();
                    endMin = this.getLeaveDateHour(Math.min(workEtime, lastTime));
                    stime = lastDate + startMin * 60;
                    etime = lastDate + endMin * 60;
                }
                List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
                if (!Objects.isNull(multiWorkTimeList) && multiWorkTimeList.size() > 1) {
                    Integer timeDuration = 0;
                    for (MultiWorkTimeBaseDto itemWorkTime : multiWorkTimeList) {
                        long itemWorkStime = lastDate + itemWorkTime.doGetRealStartTime() * 60;
                        long itemWorkEtime = lastDate + itemWorkTime.doGetRealEndTime() * 60;
                        if (stime >= itemWorkEtime || etime <= itemWorkStime) {
                            continue;
                        }
                        long itemStime = Math.max(itemWorkStime, stime);
                        long itemEtime = Math.min(itemWorkEtime, etime);
                        timeDuration += deductLeaveDayNooRest(shiftDef, worktimeDetail, itemStime, itemEtime);
                    }
                    dayTime.setTimeDuration(new Float(timeDuration));
                } else {
                    Integer timeDuration = deductLeaveDayNooRest(shiftDef, worktimeDetail, stime, etime);
                    dayTime.setTimeDuration(new Float(timeDuration));
                }
            }
        }
        dayTime.setStartTime(startMin);
        dayTime.setEndTime(endMin);
    }

    /**
     * 小时假（请一天假）
     *
     * @param dayTime
     * @param firstTime 休假开始时间
     * @param lastTime  休假结束时间
     * @param pbMap
     * @param shiftMap
     */
    private void calLeaveTimeByPeriod3ForOneDay(WaLeaveDaytime dayTime,
                                                long firstTime, long lastTime,
                                                Map<Long, WaWorktimeDetail> pbMap,
                                                Map<Integer, WaShiftDef> shiftMap) {
        long leaveDate = dayTime.getLeaveDate();
        WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
        WaShiftDef shiftDef = shiftMap.get(Optional.ofNullable(dayTime.getUseShiftDefId()).orElse(worktimeDetail.getShiftDefId()));
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        long workStime = leaveDate + shiftWorkTime.getStartTime() * 60;

        Long preDate = DateUtil.addDate(leaveDate * 1000, -1);
        WaWorktimeDetail preWorkTime = pbMap.get(preDate);

        Integer startMin = getLeaveDateHour(firstTime);
        Integer endMin = getLeaveDateHour(lastTime);

        if (endMin <= shiftDef.getStartTime()
                || !DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType())) {
            calPreLeaveTimeByPeriod3ForOneDay(dayTime, firstTime, lastTime, shiftMap, preWorkTime);
            dayTime.setStartTime(startMin);
            dayTime.setEndTime(endMin);
        } else if (endMin > shiftDef.getStartTime() && startMin < shiftDef.getStartTime()
                && null != preWorkTime && DateTypeEnum.DATE_TYP_1.getIndex().equals(preWorkTime.getDateType())) {
            Integer useShiftDefId = dayTime.getUseShiftDefId();
            calPreLeaveTimeByPeriod3ForOneDay(dayTime, firstTime, workStime, shiftMap, preWorkTime);

            WaLeaveDaytime newDayTime = FastjsonUtil.convertObject(dayTime, WaLeaveDaytime.class);
            newDayTime.setUseShiftDefId(useShiftDefId);
            calTodayLeaveTimeByPeriod3ForOneDay(newDayTime, workStime, lastTime, worktimeDetail, shiftDef);

            dayTime.setUseShiftDefId(useShiftDefId);
            dayTime.setStartTime(startMin);
            dayTime.setEndTime(endMin);
            dayTime.setTimeDuration(BigDecimal.valueOf(dayTime.getTimeDuration())
                    .add(BigDecimal.valueOf(newDayTime.getTimeDuration())).floatValue());
        } else {
            calTodayLeaveTimeByPeriod3ForOneDay(dayTime, firstTime, lastTime, worktimeDetail, shiftDef);
        }
    }

    public void calPreLeaveTimeByPeriod3ForOneDay(WaLeaveDaytime dayTime,
                                                  long firstTime, long lastTime,
                                                  Map<Integer, WaShiftDef> shiftMap,
                                                  WaWorktimeDetail preWorkTime) {
        // 请假结束时间小于当天上班开始时间或当天非工作日时，需要判断休假时间段是否在前一天的上班范围内
        long leaveDate = dayTime.getLeaveDate();
        Long preDate = DateUtil.addDate(leaveDate * 1000, -1);

        if (null != preWorkTime && DateTypeEnum.DATE_TYP_1.getIndex().equals(preWorkTime.getDateType())) {
            Integer timeDuration = 0;
            Integer latestShiftDefId = preWorkTime.getShiftDefId();
            long latestShiftEndTime = 0;
            for (Integer preShiftDefId : preWorkTime.doGetShiftDefIdList()) {
                WaShiftDef preShiftDef = shiftMap.get(preShiftDefId);
                if (null == preShiftDef
                        || !CdWaShiftUtil.checkCrossNightV2(preShiftDef, preWorkTime.getDateType())) {
                    continue;
                }
                WaShiftDef preShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(preShiftDef);
                long preWorkStime = preDate + preShiftWorkTime.doGetRealStartTime() * 60;
                long preWorkEtime = leaveDate + preShiftWorkTime.getEndTime() * 60;
                if (firstTime >= preWorkEtime || lastTime <= preWorkStime) {
                    continue;
                }
                long stime = Math.max(preWorkStime, firstTime);
                long etime = Math.min(preWorkEtime, lastTime);

                if (latestShiftEndTime < preWorkEtime) {
                    latestShiftEndTime = preWorkEtime;
                    latestShiftDefId = preShiftDefId;
                }

                List<MultiWorkTimeBaseDto> preMultiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(preShiftDef);
                if (!Objects.isNull(preMultiWorkTimeList) && preMultiWorkTimeList.size() > 1) {
                    for (MultiWorkTimeBaseDto preItemWorkTime : preMultiWorkTimeList) {
                        long itemWorkStime = preDate + preItemWorkTime.doGetRealStartTime() * 60;
                        long itemWorkEtime = preDate + preItemWorkTime.doGetRealEndTime() * 60;
                        if (stime >= itemWorkEtime || etime <= itemWorkStime) {
                            continue;
                        }
                        long itemStime = Math.max(itemWorkStime, stime);
                        long itemEtime = Math.min(itemWorkEtime, etime);
                        timeDuration += deductLeaveDayNooRest(preShiftDef, preWorkTime, itemStime, itemEtime);
                    }
                } else {
                    timeDuration += deductLeaveDayNooRest(preShiftDef, preWorkTime, stime, etime);
                }
            }
            dayTime.setUseShiftDefId(latestShiftDefId);
            dayTime.setTimeDuration(new Float(timeDuration));
        } else {
            dayTime.setTimeDuration(0f);
        }
    }

    public void calTodayLeaveTimeByPeriod3ForOneDay(WaLeaveDaytime dayTime,
                                                    long firstTime, long lastTime,
                                                    WaWorktimeDetail worktimeDetail,
                                                    WaShiftDef shiftDef) {
        long leaveDate = dayTime.getLeaveDate();
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

        boolean isky = CdWaShiftUtil.checkCrossNightV2(shiftDef, worktimeDetail.getDateType());
        long workStime = leaveDate + shiftWorkTime.getStartTime() * 60;
        long workEtime = isky ? (leaveDate + 86400 + shiftWorkTime.getEndTime() * 60) : (leaveDate + shiftWorkTime.getEndTime() * 60);

        long stime = Math.max(workStime, firstTime);
        long etime = Math.min(workEtime, lastTime);

        Integer startMin = getLeaveDateHour(stime);
        Integer endMin = getLeaveDateHour(etime);

        List<MultiWorkTimeBaseDto> multiWorkTimeList = CdWaShiftUtil.getAllMultiWorkTimeList(shiftDef);
        if (!Objects.isNull(multiWorkTimeList) && multiWorkTimeList.size() > 1) {
            Integer timeDuration = 0;
            for (MultiWorkTimeBaseDto itemWorkTime : multiWorkTimeList) {
                long itemWorkStime = leaveDate + itemWorkTime.doGetRealStartTime() * 60;
                long itemWorkEtime = leaveDate + itemWorkTime.doGetRealEndTime() * 60;
                if (stime >= itemWorkEtime || etime <= itemWorkStime) {
                    continue;
                }
                long itemStime = Math.max(itemWorkStime, stime);
                long itemEtime = Math.min(itemWorkEtime, etime);
                timeDuration += deductLeaveDayNooRest(shiftDef, worktimeDetail, itemStime, itemEtime);
            }
            dayTime.setTimeDuration(new Float(timeDuration));
        } else {
            Integer timeDuration = deductLeaveDayNooRest(shiftDef, worktimeDetail, stime, etime);
            dayTime.setTimeDuration(new Float(timeDuration));
        }

        dayTime.setStartTime(startMin);
        dayTime.setEndTime(endMin);
    }

    /**
     * 时间单位为小时的整天
     *
     * @param waLeaveType
     * @param worktimeDetail
     * @param shiftDef
     * @param dayTime
     * @return
     */
    public String processLeaveByPeriod4(WaLeaveType waLeaveType, WaWorktimeDetail worktimeDetail,
                                        WaShiftDef shiftDef, WaLeaveDaytime dayTime) {
        WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
        // 特殊班次工时调整
        boolean isSpecial = Optional.ofNullable(shiftDef.getIsSpecial()).orElse(false) && shiftDef.getSpecialWorkTime() != null;
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType())) {
            dayTime.setTimeDuration(isSpecial
                    ? shiftDef.getSpecialWorkTime().floatValue()
                    : shiftDef.getWorkTotalTime().floatValue());
            dayTime.setStartTime(shiftWorkTime.getStartTime());
            dayTime.setEndTime(shiftWorkTime.getEndTime());
        } else if (DateTypeEnum.DATE_TYP_2.getIndex().equals(worktimeDetail.getDateType())
                && waLeaveType.getIsRestDay()) {
            dayTime.setTimeDuration(isSpecial
                    ? shiftDef.getSpecialWorkTime().floatValue()
                    : 8 * 60f);
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        } else if ((DateTypeEnum.DATE_TYP_3.getIndex().equals(worktimeDetail.getDateType())
                || DateTypeEnum.DATE_TYP_5.getIndex().equals(worktimeDetail.getDateType()))
                && waLeaveType.getIsLegalHoliday()) {
            dayTime.setTimeDuration(isSpecial
                    ? shiftDef.getSpecialWorkTime().floatValue()
                    : 8 * 60f);
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        } else if (DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType())
                && waLeaveType.getIsRestDay()) {
            dayTime.setTimeDuration(isSpecial
                    ? shiftDef.getSpecialWorkTime().floatValue()
                    : 8 * 60f);
            dayTime.setStartTime(0);
            dayTime.setEndTime(1440);
        }
        return "";
    }

    /**
     * 半天
     *
     * @param waLeaveType
     * @param worktimeDetail
     * @param dayTime
     * @param shalfDay
     * @param ehalfDay
     * @param firstDate
     * @param lastDate
     * @return
     */
    public String processLeaveByPeriod9(WaLeaveType waLeaveType,
                                        WaWorktimeDetail worktimeDetail,
                                        WaLeaveDaytime dayTime,
                                        String shalfDay, String ehalfDay,
                                        long firstDate, long lastDate) {
        long leaveDate = dayTime.getLeaveDate();
        if (firstDate == lastDate) {//第一天也是最后一天
            if (shalfDay.equals("P")) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("P");
                dayTime.setEhalfDay("P");
            } else if (shalfDay.equals("A") && ehalfDay.equals("A")) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("A");
            } else {
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(1f);
            }
        } else if (leaveDate == firstDate && leaveDate < lastDate) {//第一天不是最后一天
            if (shalfDay.equals("A")) {
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(1f);
            } else {
                dayTime.setShalfDay("P");
                dayTime.setEhalfDay("P");
                dayTime.setTimeDuration(0.5f);
            }
        } else if (leaveDate > firstDate && leaveDate < lastDate) {//中间一天
            if (DateTypeEnum.DATE_TYP_1.getIndex().equals(worktimeDetail.getDateType())) {
                dayTime.setTimeDuration(1f);
            } else if (DateTypeEnum.DATE_TYP_2.getIndex().equals(worktimeDetail.getDateType()) && waLeaveType.getIsRestDay()) {
                dayTime.setTimeDuration(1f);
            } else if ((DateTypeEnum.DATE_TYP_3.getIndex().equals(worktimeDetail.getDateType())
                    || DateTypeEnum.DATE_TYP_5.getIndex().equals(worktimeDetail.getDateType())) && waLeaveType.getIsLegalHoliday()) {
                dayTime.setTimeDuration(1f);
            } else if (DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType()) && waLeaveType.getIsRestDay()) {
                dayTime.setTimeDuration(1f);
            }
            dayTime.setShalfDay("A");
            dayTime.setEhalfDay("P");
        } else if (leaveDate > firstDate && leaveDate == lastDate) {
            if (ehalfDay.equals("P")) {
                dayTime.setTimeDuration(1f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("P");
            } else if (ehalfDay.equals("A")) {
                dayTime.setTimeDuration(0.5f);
                dayTime.setShalfDay("A");
                dayTime.setEhalfDay("A");
            }
        }
        return "";
    }

    /**
     * 手机端获取全部假期配额
     *
     * @param empId       员工ID
     * @param belongId
     * @param sessionBean
     * @return
     */
    public List<Object> getAllRemainsDay(Long empId, String belongId, SessionBean sessionBean) {
        /**
         app配额明细字段逻辑：
         冻结规则为：本年配额冻结
         冻结额度：当前配额-本年已使用（含固定已使用）
         剩余额度：（留存配额-留存已使用-留存已失效）+（调整配额-调整已使用-调整已失效）等同于 现在的剩余配额-冻结额度

         冻结规则为：试用期冻结
         冻结配额：（留存配额-留存已使用-留存已失效）+ （当前配额-本年已使用（含固定已使用））+（调整配额-调整已使用-调整已失效）等同于 现在的剩余额度
         剩余配额：（留存配额-留存已使用-留存已失效）+ （当前配额-本年已使用（含固定已使用））+（调整配额-调整已使用-调整已失效）- 冻结配额 等同于 0
         **/
        Map<String, Object> mParams = new HashMap<>();
        mParams.put("empId", empId);
        List<Map> mQueryList = empInfoMapper.getAllQuotaMyMobileEmpId(mParams);
        if (CollectionUtils.isNotEmpty(mQueryList)) {
            return mQueryList.stream().filter(item -> {
                return item.containsKey("is_emp_show") && (Boolean) item.get("is_emp_show");
            }).map(mItemObject -> {
                Map<String, Object> mResultItem = new HashMap<>();
                // 上年留存
                BigDecimal remainDay = mItemObject.get("remain_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("remain_day");
                // 上年留存已经使用
                BigDecimal remainUsedDay = mItemObject.get("remain_used_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("remain_used_day");
                // 上年留存已经失效
                BigDecimal remainInvalidDay = mItemObject.get("remain_invalid") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("remain_invalid");
                // 当年配额
                BigDecimal quotaDay = mItemObject.get("quota_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("quota_day");
                // 当年配额已经使用，需要加上固定已经使用
                BigDecimal quotaUsedDay = mItemObject.get("used_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("used_day");
                // 当前配额已经失效
                BigDecimal quotaInvalidDay = mItemObject.get("invalidDay") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("invalidDay");
                // 固定已经使用
                BigDecimal fixUsedDay = mItemObject.get("fix_used_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("fix_used_day");
                // 休假单位时间类型
                Integer timeType = mItemObject.get("acct_time_type") == null ? 1 : (Integer) mItemObject.get("acct_time_type");
                // 是否在员工端显示
                Boolean isEmpShow = (Boolean) mItemObject.get("is_emp_show");
                // 调整配额
                BigDecimal adjustDay = mItemObject.get("adjust_quota") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("adjust_quota");
                // 调整配额已使用
                BigDecimal adjustUsedDay = mItemObject.get("adjust_used_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("adjust_used_day");
                // 调整配额已失效
                BigDecimal adjustInvalid = mItemObject.get("adjustInvalid") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("adjustInvalid");
                // 当前配额
                BigDecimal nowQuotaDay = mItemObject.get("now_quota") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("now_quota");
                // 冻结额度
                BigDecimal freezeDay = mItemObject.get("freezeDay") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("freezeDay");
                // 当前额度扣除单位类型
                // 额度抵扣
                BigDecimal deductionDay = mItemObject.get("deduction_day") == null ? new BigDecimal(0) :
                        (BigDecimal) mItemObject.get("deduction_day");
                mResultItem.put("remainDay", timeType == 2 ? remainDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : remainDay);
                mResultItem.put("remainUsed", timeType == 2 ? remainUsedDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : remainUsedDay);
                mResultItem.put("remainInvalid", timeType == 2 ? remainInvalidDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : remainInvalidDay);
                mResultItem.put("quotaDay", timeType == 2 ? quotaDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : quotaDay);
                mResultItem.put("quotaUsed", timeType == 2 ? quotaUsedDay.add(fixUsedDay).divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : quotaUsedDay.add(fixUsedDay));
                mResultItem.put("quotaInvalid", timeType == 2 ? quotaInvalidDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : quotaInvalidDay);
                mResultItem.put("quotaFreeze", timeType == 2 ? freezeDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : freezeDay);
                mResultItem.put("adjustDay", timeType == 2 ? adjustDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : adjustDay);
                mResultItem.put("adjustUsed", timeType == 2 ? adjustUsedDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : adjustUsedDay);
                mResultItem.put("adjustInvalid", timeType == 2 ? adjustInvalid.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : adjustInvalid);
                mResultItem.put("deductionDay", timeType == 2 ? deductionDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : deductionDay);
                mResultItem.put("nowQuotaDay", timeType == 2 ? nowQuotaDay.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : nowQuotaDay);
                // 计算当前剩余时间 使用当前配额计算
                BigDecimal nowRemain =
                        remainDay.subtract(remainUsedDay).subtract(remainInvalidDay).add(nowQuotaDay).add(adjustDay)
                                .subtract(quotaUsedDay).subtract(quotaInvalidDay).subtract(adjustUsedDay)
                                .subtract(adjustInvalid).subtract(fixUsedDay).subtract(deductionDay).subtract(freezeDay);
                // 当前剩余
                BigDecimal curRemain =
                        remainDay.subtract(remainUsedDay).subtract(remainInvalidDay).add(quotaDay).add(adjustDay)
                                .subtract(quotaUsedDay).subtract(quotaInvalidDay).subtract(adjustUsedDay)
                                .subtract(adjustInvalid).subtract(fixUsedDay).subtract(deductionDay).subtract(freezeDay);

                if (nowRemain.floatValue() < 0) {
                    nowRemain = new BigDecimal(0);
                }

                if (curRemain.floatValue() < 0) {
                    curRemain = new BigDecimal(0);
                }

                mResultItem.put("leftQuotaDay", timeType == 2 ? nowRemain.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : nowRemain);

                mResultItem.put("nowRemain", timeType == 2 ? nowRemain.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : nowRemain);

                mResultItem.put("curRemain", timeType == 2 ? curRemain.divide(new BigDecimal(60), 1,
                        RoundingMode.HALF_DOWN) : curRemain);

                mResultItem.put("timeUnit", messageResource.getMessage(timeType == 1 ? "L005571" : "L005573", new Object[]{
                                ""},
                        new Locale(sessionBean.getLanguage())));
                // 假期配额名称
                mResultItem.put("holidayName", mItemObject.get("quota_setting_name"));
                // 获取休假quotaId
                mResultItem.put("quotaId", mItemObject.get("quotaId"));
                // 假期配额配置Id
                mResultItem.put("quotaSettingId", mItemObject.get("quotaSettingId"));
                return mResultItem;
            }).collect(Collectors.toList());
        }
        return null;
    }

    public PGobject convertToGobject(Object obj) {
        try {
            if (obj instanceof String) {
                PGobject jsonObject = new PGobject();
                jsonObject.setType("jsonb");
                jsonObject.setValue(obj.toString());
                return jsonObject;
            } else if (obj instanceof Map) {
                PGobject jsonObject = new PGobject();
                jsonObject.setType("jsonb");
                jsonObject.setValue(com.alibaba.fastjson.JSONObject.toJSON(obj).toString());
                return jsonObject;
            } else if (obj instanceof PGobject) {
                return (PGobject) obj;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String getTimeResultMsg(BigDecimal duration, Integer acctTimeType, String leaveTypeName, String lang, String msgCode) {
        if (duration != null && duration.floatValue() >= 0) {
            //可用额度还有剩余
            float surplusQuotaTime = duration.floatValue();
            //计算剩余#天#小时#分钟
            float day = 0f;
            float hour = 0f;
            float mi = 0f;
            if (acctTimeType == 1) {
                day = surplusQuotaTime;
            } else if (acctTimeType == 2) {
                BigDecimal b = new BigDecimal(surplusQuotaTime / 60);
                BigDecimal c = new BigDecimal(surplusQuotaTime % 60);
                if (c.intValue() > 0) {
                    hour = b.intValue();
                    mi = c.intValue();
                } else {
                    hour = b.intValue();
                }
            }
            if (StringUtils.isNotBlank(msgCode)) {
                return messageResource.getMessage(msgCode, new Object[]{leaveTypeName, day, hour, mi}, new Locale(lang));
            } else {
                if (acctTimeType == 1) {//天
                    //余额不足，当前可用{0}天
                    return messageResource.getMessage("L006847", new Object[]{day}, new Locale(lang));
                } else {//小时
                    //余额不足，当前可用{0}小时{1}分钟
                    if (mi > 0) {
                        return messageResource.getMessage("L006849", new Object[]{hour, mi}, new Locale(lang));
                    } else {
                        return messageResource.getMessage("L006848", new Object[]{hour}, new Locale(lang));
                    }
                }
            }
        } else {
            log.info("getTimeResultMsg:" + messageResource.getMessage("L005760", new Object[]{}, new Locale(lang)));
            return messageResource.getMessage("L005760", new Object[]{}, new Locale(lang));
        }
    }

    /**
     * 查询配额冻结的请假数据
     *
     * @param leaveType
     * @param leaveTypeId
     * @param empId          员工ID
     * @param excludeLeaveId
     * @param firstDate
     * @param lastDate
     * @return
     */
    public List<WaLeaveDaytime> listFrozeLeave(Integer leaveType, Integer leaveTypeId, Long empId, Integer excludeLeaveId, Long firstDate, Long lastDate) {
        if (leaveType != 3) {
            Map map = new HashMap();
            map.put("empId", empId);
            map.put("leaveTypeId", leaveTypeId);
            map.put("firstDate", firstDate);
            map.put("lastDate", lastDate);
            map.put("array", new Integer[]{1});
            if (excludeLeaveId != null) {
                map.put("leaveId", excludeLeaveId);
            }
            return waLeaveDaytimeMapper.listLeaveDaytime(map);
        }
        return null;
    }

    /**
     * 检查员工请假配额是否足够
     *
     * @param daytimeList
     * @param empId       员工ID
     * @param waLeaveType
     * @param leaveId
     * @param lang
     * @return
     */
    @Deprecated
    public String checkEmpLeaveQuotaDetail(List<WaLeaveDaytime> daytimeList, Long empId, WaLeaveType waLeaveType, Integer leaveId, String lang, List<Integer> leaveYearList) {
        Integer leaveTypeId = waLeaveType.getLeaveTypeId();
        String belongOrgId = waLeaveType.getBelongOrgid();

        List<EmpQuotaDTO> quotaList = waCommonService.getEmpQuotaList(waLeaveType.getLeaveTypeId(), waLeaveType.getLeaveType(), empId, "asc", leaveYearList);
        if (CollectionUtils.isNotEmpty(quotaList)) {
            //查询所有配额的最早开始时间和最晚结束时间
            final Long[] minDate = {null};
            final Long[] maxDate = {null};
            quotaList.forEach(quota -> {
                if (minDate[0] == null || quota.getStartDate() < minDate[0]) {
                    minDate[0] = quota.getStartDate();
                }

                if (maxDate[0] == null || quota.getLastDate() > maxDate[0]) {
                    maxDate[0] = quota.getLastDate();
                }
            });

            List<WaLeaveDaytime> allDaytimeList = new ArrayList<>();//须扣减配额的请假数据
            if (waLeaveType.getLeaveType() != 3) {//如归假期类型为非调休，则去查待审核的休假单据，此部分休假配额需做冻结处理
                List<WaLeaveDaytime> frozeLeaveList = this.listFrozeLeave(waLeaveType.getLeaveType(), leaveTypeId, empId, leaveId, minDate[0], maxDate[0]);
                if (CollectionUtils.isNotEmpty(frozeLeaveList)) {
                    allDaytimeList.addAll(frozeLeaveList);
                }
            }
            allDaytimeList.addAll(daytimeList);

            if (StringUtils.isNotBlank(waLeaveType.getQuotaSorts())) {//按照设置的配额扣减顺序进行配额扣减
                List<EmpQuotaUseDTO> quotaUseDTOList = waCommonService.getEmpQuotaDetailOrderBySorts(belongOrgId, empId, quotaList, false, waLeaveType.getQuotaSorts());
                if (CollectionUtils.isNotEmpty(quotaUseDTOList)) {
                    final BigDecimal[] totalKyQuota = {new BigDecimal(0)};//总的可用额度
                    quotaUseDTOList.forEach(quotaDTO -> {
                        if (quotaDTO.getKeQuota() > 0) {
                            totalKyQuota[0] = totalKyQuota[0].add(new BigDecimal(quotaDTO.getKeQuota()));
                        }
                    });

                    //扣减配额
                    for (WaLeaveDaytime daytime : allDaytimeList) {
                        if (daytime.getTimeDuration() <= 0) {
                            continue;
                        }
                        Long leaveDate = daytime.getLeaveDate();
                        BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                        for (EmpQuotaUseDTO quotaDTO : quotaUseDTOList) {
                            if (dayDuration.floatValue() <= 0) {
                                break;
                            }
                            if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                BigDecimal surplusQuota = new BigDecimal(quotaDTO.getKeQuota());//剩余配额
                                if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {
                                    //额度足够
                                    surplusQuota = surplusQuota.subtract(dayDuration);
                                    dayDuration = new BigDecimal(0);
                                } else {
                                    //额度不够
                                    dayDuration = dayDuration.subtract(surplusQuota);
                                    surplusQuota = new BigDecimal(0);
                                }
                                quotaDTO.setKeQuota(surplusQuota.floatValue());//更新剩余额度
                            }
                        }
                        daytime.setTimeDuration(dayDuration.floatValue());
                    }
                    //检查配额是否足够
                    List<WaLeaveDaytime> surplusList = allDaytimeList.stream().filter(daytime -> daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(surplusList)) {
                        return this.getTimeResultMsg(totalKyQuota[0], waLeaveType.getAcctTimeType(), waLeaveType.getLeaveName(), lang, "L006852");
                    }
                }
            } else {//兼容旧的扣减逻辑
                //查询可用配额详情列表
                Map<String, List<EmpQuotaUseDTO>> quotaDetailMap = waCommonService.listWaEmpQuotaDetail(belongOrgId, empId, quotaList, false);
                if (CollectionUtils.isEmpty(quotaDetailMap.get("remainQuotas")) && CollectionUtils.isEmpty(quotaDetailMap.get("curYearQuotas"))) {//没有额度信息，不能请假
                    return messageResource.getMessage("L005760", new Object[]{}, new Locale(lang));
                }
                final BigDecimal[] totalKyQuota = {new BigDecimal(0)};

                //先扣留存
                List<EmpQuotaUseDTO> remainQuotas = quotaDetailMap.get("remainQuotas");
                if (CollectionUtils.isNotEmpty(remainQuotas)) {
                    remainQuotas.forEach(quotaDTO -> {
                        if (quotaDTO.getKeQuota() > 0) {
                            totalKyQuota[0] = totalKyQuota[0].add(new BigDecimal(quotaDTO.getKeQuota()));
                        }
                    });
                    for (WaLeaveDaytime daytime : allDaytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                            //开始扣减配额
                            for (EmpQuotaUseDTO quotaDTO : remainQuotas) {
                                if (dayDuration.floatValue() <= 0) {
                                    break;
                                }
                                if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                    BigDecimal surplusQuota = new BigDecimal(quotaDTO.getKeQuota());//剩余配额
                                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {//额度足够
                                        surplusQuota = surplusQuota.subtract(dayDuration);
                                        dayDuration = new BigDecimal(0);
                                    } else {//额度不够
                                        dayDuration = dayDuration.subtract(surplusQuota);
                                        surplusQuota = new BigDecimal(0);
                                    }
                                    quotaDTO.setKeQuota(surplusQuota.floatValue());//更新剩余额度
                                }
                            }
                            daytime.setTimeDuration(dayDuration.floatValue());
                        }
                    }
                }
                //再扣减本年和调整
                List<EmpQuotaUseDTO> curYearQuotas = quotaDetailMap.get("curYearQuotas");
                if (CollectionUtils.isNotEmpty(curYearQuotas)) {
                    curYearQuotas.forEach(quotaDTO -> {
                        if (quotaDTO.getKeQuota() > 0) {
                            totalKyQuota[0] = totalKyQuota[0].add(new BigDecimal(quotaDTO.getKeQuota()));
                        }
                    });
                    for (WaLeaveDaytime daytime : allDaytimeList) {
                        if (daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0) {
                            Long leaveDate = daytime.getLeaveDate();
                            BigDecimal dayDuration = new BigDecimal(daytime.getTimeDuration());//请假时长
                            for (EmpQuotaUseDTO quotaDTO : curYearQuotas) {
                                if (dayDuration.floatValue() <= 0) {
                                    break;
                                }
                                if (quotaDTO.getKeQuota() > 0 && leaveDate >= quotaDTO.getStartDate() && leaveDate <= quotaDTO.getLastDate()) {
                                    BigDecimal surplusQuota = new BigDecimal(quotaDTO.getKeQuota());//剩余配额
                                    if (surplusQuota.subtract(dayDuration).floatValue() >= 0) {//额度足够
                                        surplusQuota = surplusQuota.subtract(dayDuration);
                                        dayDuration = new BigDecimal(0);
                                    } else {//额度不够
                                        dayDuration = dayDuration.subtract(surplusQuota);
                                        surplusQuota = new BigDecimal(0);
                                    }
                                    quotaDTO.setKeQuota(surplusQuota.floatValue());//更新剩余额度
                                }
                            }
                            daytime.setTimeDuration(dayDuration.floatValue());
                        }
                    }
                }

                //检查配额是否足够
                List<WaLeaveDaytime> surplusList = allDaytimeList.stream().filter(daytime -> daytime.getTimeDuration() != null && daytime.getTimeDuration() > 0).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(surplusList)) {
                    return this.getTimeResultMsg(totalKyQuota[0], waLeaveType.getAcctTimeType(), waLeaveType.getLeaveName(), lang, "L006852");
                }
            }
        } else {//没有额度信息，不能请假
            return messageResource.getMessage("L005760", new Object[]{}, new Locale(lang));
        }
        return "";
    }

    /**
     * 加班转调休配配额
     *
     * @param ot2
     */
    @Deprecated
    @Transactional
    public void addOtQuota(WaEmpOvertime ot2) throws Exception {
        //调休配额会通过核算去生成
        return;
    }

    /**
     * 求出是上半天或下半天的时长
     *
     * @param halfDay
     * @param shiftDef
     * @return
     */
    public int getHalfTime(String halfDay, WaShiftDef shiftDef, int halfWorkTime) {
        // 如果定义了半天时间点，则按这个时间拆分上半天和下半天
        if (BooleanUtils.isTrue(shiftDef.getIsHalfdayTime()) && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() > 0) {
            if (halfDay.equals(BaseConst.LEAVE_HALF_SHALF_DAY)) {
                // 上半天(时间) = 定义的时间点 - 上班开始时间
                halfWorkTime = shiftDef.getHalfdayTime() - shiftDef.getStartTime();
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) && shiftDef.getHalfdayTime() >= shiftDef.getNoonRestStart()) {
                    if (shiftDef.getHalfdayTime() > shiftDef.getNoonRestEnd()) {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                    } else {
                        halfWorkTime -= shiftDef.getHalfdayTime() - shiftDef.getNoonRestStart();
                    }
                }
            } else if (halfDay.equals(BaseConst.LEAVE_HALF_EHALF_DAY)) {
                // 下半天(时间) = 下班截止时间 - 定义的时间点 【如果下班截止时间跨夜则设置为次日】
                if (CdWaShiftUtil.checkCrossNightV2(shiftDef, shiftDef.getDateType())) {//跨夜
                    Integer nextDaytime = shiftDef.getEndTime() + (24 * 60);
                    halfWorkTime = nextDaytime - shiftDef.getHalfdayTime();
                } else {
                    halfWorkTime = shiftDef.getEndTime() - shiftDef.getHalfdayTime();
                }
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) && shiftDef.getHalfdayTime() < shiftDef.getNoonRestEnd()) {
                    if (shiftDef.getHalfdayTime() < shiftDef.getNoonRestStart()) {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                    } else {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getHalfdayTime();
                    }
                }
            }
        } else if (shiftDef.getIsNoonRest()) { // 或者按 中午休息时间来拆分
            if (halfDay.equals(BaseConst.LEAVE_HALF_SHALF_DAY)) {
                halfWorkTime = shiftDef.getNoonRestStart() - shiftDef.getStartTime();
            } else if (halfDay.equals(BaseConst.LEAVE_HALF_EHALF_DAY)) {
                halfWorkTime = shiftDef.getEndTime() - shiftDef.getNoonRestEnd();
            }
        } else {
            // 以上条件都没有满足，则按工作时长的一半进行拆分
        }
        return halfWorkTime;
    }

    /**
     * 检查跨夜加班是否归属至加班开始日期
     *
     * @param empId
     * @return
     */
    public boolean checkOvertimeBelong(Long empId) {
        Map groupParams = new HashMap<>();
        groupParams.put("empid", empId);
        List<Map> listEmpWaGroup = waMapper.listEmpWaGroup(groupParams);
        if (CollectionUtils.isNotEmpty(listEmpWaGroup)) {
            Map groupMap = listEmpWaGroup.get(0);
            Integer overtimeBelong = (Integer) groupMap.get("overtime_belong");
            return null != overtimeBelong && overtimeBelong.equals(OvertimeBelongType.OVERTIME_START_DATE.getIndex());
        }
        return false;
    }
}