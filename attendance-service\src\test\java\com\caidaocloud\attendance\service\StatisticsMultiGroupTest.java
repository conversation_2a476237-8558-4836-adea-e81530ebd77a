package com.caidaocloud.attendance.service;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.application.service.IStatisticsService;
import com.caidaocloud.attendance.service.interfaces.dto.MonthAnalysePageDtoV2;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.paas.common.dto.DynamicPageDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

/**
 * 多考勤分组ID接口测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class StatisticsMultiGroupTest {

    @Autowired
    private IStatisticsService statisticsService;

    /**
     * 测试多个考勤分组ID的动态列数据查询
     */
    @Test
    public void testGetRegisterStatisticsAdvancedForDynamicMultiGroup() {
        // 准备测试数据
        MonthAnalysePageDtoV2 dto = new MonthAnalysePageDtoV2();
        dto.setGroupId(Arrays.asList(1, 2, 3)); // 测试多个考勤分组ID
        dto.setStartDate(1672531200L); // 2023-01-01
        dto.setEndDate(1675123199L);   // 2023-01-31
        dto.setStatisticsType(0);
        
        PageBean pageBean = new PageBean();
        pageBean.setPageNo(1);
        pageBean.setPageSize(20);
        
        UserInfo userInfo = new UserInfo();
        userInfo.setTenantId("test_tenant");
        userInfo.setUserId(0L);
        
        try {
            // 调用新的多分组接口
            DynamicPageDto result = statisticsService.getRegisterStatisticsAdvancedForDynamicMultiGroup(dto, pageBean, userInfo);
            
            // 验证结果
            assert result != null : "结果不应为空";
            assert result.getPageData() != null : "分页数据不应为空";
            
            System.out.println("测试成功：获取到 " + result.getPageData().getTotal() + " 条记录");
            
        } catch (Exception e) {
            System.err.println("测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试多个考勤分组ID的表头查询
     */
    @Test
    public void testSearchMonthHeaderListForDynamicMultiGroup() {
        List<Integer> waGroupIds = Arrays.asList(1, 2, 3);
        
        UserInfo userInfo = new UserInfo();
        userInfo.setTenantId("test_tenant");
        userInfo.setUserId(0L);
        
        try {
            // 调用新的多分组表头接口
            List<KeyValue> result = statisticsService.searchMonthHeaderListForDynamicMultiGroup(waGroupIds, userInfo);
            
            // 验证结果
            assert result != null : "结果不应为空";
            
            System.out.println("测试成功：获取到 " + result.size() + " 个表头字段");
            
            // 打印表头信息
            for (KeyValue kv : result) {
                System.out.println("字段：" + kv.getValue() + " -> " + kv.getText());
            }
            
        } catch (Exception e) {
            System.err.println("测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试单个考勤分组与多个考勤分组的结果对比
     */
    @Test
    public void testCompareWithSingleGroup() {
        // 准备测试数据
        MonthAnalysePageDtoV2 multiGroupDto = new MonthAnalysePageDtoV2();
        multiGroupDto.setGroupId(Arrays.asList(1)); // 单个分组
        multiGroupDto.setStartDate(1672531200L);
        multiGroupDto.setEndDate(1675123199L);
        multiGroupDto.setStatisticsType(0);
        
        PageBean pageBean = new PageBean();
        pageBean.setPageNo(1);
        pageBean.setPageSize(20);
        
        UserInfo userInfo = new UserInfo();
        userInfo.setTenantId("test_tenant");
        userInfo.setUserId(0L);
        
        try {
            // 调用多分组接口（传入单个分组）
            DynamicPageDto multiGroupResult = statisticsService.getRegisterStatisticsAdvancedForDynamicMultiGroup(multiGroupDto, pageBean, userInfo);
            
            // 验证结果
            assert multiGroupResult != null : "多分组接口结果不应为空";
            
            System.out.println("单个分组测试成功：获取到 " + multiGroupResult.getPageData().getTotal() + " 条记录");
            
        } catch (Exception e) {
            System.err.println("单个分组测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试空分组列表的处理
     */
    @Test
    public void testEmptyGroupList() {
        MonthAnalysePageDtoV2 dto = new MonthAnalysePageDtoV2();
        dto.setGroupId(Arrays.asList()); // 空分组列表
        dto.setStartDate(1672531200L);
        dto.setEndDate(1675123199L);
        dto.setStatisticsType(0);
        
        PageBean pageBean = new PageBean();
        pageBean.setPageNo(1);
        pageBean.setPageSize(20);
        
        UserInfo userInfo = new UserInfo();
        userInfo.setTenantId("test_tenant");
        userInfo.setUserId(0L);
        
        try {
            // 调用多分组接口（传入空分组列表）
            DynamicPageDto result = statisticsService.getRegisterStatisticsAdvancedForDynamicMultiGroup(dto, pageBean, userInfo);
            
            // 验证结果应该为空或者有默认处理
            assert result != null : "结果不应为空";
            
            System.out.println("空分组列表测试成功");
            
        } catch (Exception e) {
            System.err.println("空分组列表测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
