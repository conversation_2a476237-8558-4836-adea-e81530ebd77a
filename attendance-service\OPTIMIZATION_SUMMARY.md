# 考勤统计服务批量查询优化总结

## 优化背景

在 `StatisticsService` 类中，原有的动态列数据查询逻辑存在性能问题：
- 在循环中为每个员工单独调用 `addDynamicColumnValue` 方法
- 每次调用都会执行一次 `DataQuery` 单条查询
- 当员工数量较多时，会产生大量的数据库查询，严重影响性能

## 优化方案

### 1. 核心思路
将原有的单条循环查询改为批量查询：
- 收集所有需要查询的员工ID
- 使用 `DataFilter.andIn()` 进行批量查询
- 将查询结果缓存到内存中
- 在循环中直接从缓存中获取数据

### 2. 新增方法

#### 2.1 `batchQueryDynamicColumnValues`
```java
private Map<String, Map<String, Object>> batchQueryDynamicColumnValues(
        PageList<Map<String, Object>> page,
        Map<String, List<MetadataPropertyDto>> mappedProperties,
        long time)
```
- 用于 `PageList` 类型数据的批量查询
- 收集所有员工ID并去重
- 分批查询（每批500个ID）避免IN条件过长

#### 2.2 `batchQueryDynamicColumnValuesForList`
```java
private Map<String, Map<String, Object>> batchQueryDynamicColumnValuesForList(
        List<Map<String, Object>> list,
        Map<String, List<MetadataPropertyDto>> mappedProperties,
        long time)
```
- 用于 `List` 类型数据的批量查询
- 功能与上述方法相同，适配不同的数据类型

#### 2.3 `batchQueryEntityData`
```java
private void batchQueryEntityData(Map<String, Map<String, Object>> result,
                                List<String> empIds,
                                String identifier,
                                Map<String, List<MetadataPropertyDto>> mappedProperties,
                                long time,
                                String empIdProp)
```
- 批量查询指定实体的数据
- 支持四种实体类型：
  - `entity.hr.EmpPrivateInfo`
  - `entity.hr.EmpWorkInfo`
  - `entity.hr.EmpWorkOverview`
  - `entity.hr.LastContract`

#### 2.4 `getEmpIdFromDataSimple`
```java
private String getEmpIdFromDataSimple(DataSimple dataSimple, String empIdProp)
```
- 从 `DataSimple` 对象中提取员工ID
- 处理不同实体的ID提取逻辑
- 特别处理 `LastContract` 的 `owner$empId` 情况

#### 2.5 `addBatchedDynamicColumnValue`
```java
private void addBatchedDynamicColumnValue(Map<String, Object> data, 
                                        String empId, 
                                        Map<String, Map<String, Object>> batchDynamicData)
```
- 将批量查询的结果添加到员工数据中
- 替代原有的单条查询逻辑

### 3. 优化的方法

以下方法中的单条查询逻辑已被优化为批量查询：

1. `getMouthAnalyseListForDynamic` - 月度汇总动态列表
2. `getDayAnalyseListForDynamic` - 日度分析动态列表  
3. `getAdvanceAnalyseListForDynamic` - 高级分析动态列表
4. `getRegisterStatisticsAdvancedForDynamic` - 考勤统计高级动态
5. `getRegisterStatisticsAdvancedForDynamicMultiGroup` - 多分组考勤统计高级动态

## 性能提升

### 优化前
- 查询次数：N个员工 × 4个实体 = 4N次查询
- 网络往返：4N次
- 数据库连接：4N次

### 优化后  
- 查询次数：4个实体 × ⌈N/500⌉批次 ≈ 4次查询（当N≤500时）
- 网络往返：4次
- 数据库连接：4次

### 性能提升比例
当员工数量为500时，查询次数从2000次减少到4次，性能提升约 **99.8%**

## 注意事项

1. **内存使用**：批量查询会将所有员工的动态列数据加载到内存中，需要注意内存使用情况
2. **分批处理**：使用500个ID为一批进行查询，避免IN条件过长导致的SQL性能问题
3. **错误处理**：添加了异常捕获和日志记录，确保单个实体查询失败不影响其他实体
4. **兼容性**：保持了原有的方法签名和返回结果格式，确保向后兼容

## 测试建议

1. **功能测试**：验证优化后的结果与原有逻辑结果一致
2. **性能测试**：对比优化前后的查询时间和数据库连接数
3. **压力测试**：测试大量员工数据下的内存使用情况
4. **边界测试**：测试空数据、单个员工、大量员工等边界情况
