package com.caidaocloud.attendance.service.application.dto.clock;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Data
@Service
public class WaClockSiteDto {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("地点简称")
    private String siteName;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("GPS范围")
    private BigDecimal range;

    @ApiModelProperty("经度")
    private BigDecimal lng;

    @ApiModelProperty("纬度")
    private BigDecimal lat;
}