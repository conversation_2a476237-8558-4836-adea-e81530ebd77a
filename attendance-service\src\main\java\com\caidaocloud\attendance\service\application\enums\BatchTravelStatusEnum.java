package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

/**
 * 批量出差状态
 */
public enum BatchTravelStatusEnum {
    DEPART(1, "出发", AttendanceCodes.SET_OUT),
    TRANSFER(2, "中转", AttendanceCodes.TRANSFER),
    REGRESS(3, "回归", AttendanceCodes.REGRESSION);

    private Integer index;
    private String name;
    private Integer code;

    BatchTravelStatusEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(int index) {
        for (BatchTravelStatusEnum c : BatchTravelStatusEnum.values()) {
            if (c.getIndex() == index) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
