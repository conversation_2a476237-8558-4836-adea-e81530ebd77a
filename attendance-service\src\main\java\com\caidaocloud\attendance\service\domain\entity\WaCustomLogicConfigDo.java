package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.domain.repository.IWaCustomLogicConfigRepository;
import com.caidaocloud.dto.UserInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 考勤自定义逻辑配置DO
 */
@Data
@Slf4j
@Service
public class WaCustomLogicConfigDo {
    private Long configId;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型 CustomLogicTypeEnum
     */
    private String logicType;

    /**
     * 表达式
     */
    private String logicExp;

    /**
     * 变量
     */
    private String logicVar;

    /**
     * 所属业务 CustomLogicBelongBusinessEnum
     */
    private String belongBusiness;

    /**
     * 状态：0 未启用 1 启用 2 停用
     */
    private Integer status;

    private String tenantId;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    @Autowired
    private IWaCustomLogicConfigRepository waCustomLogicConfigRepository;

    public WaCustomLogicConfigDo getById(Long configId) {
        return waCustomLogicConfigRepository.getById(configId);
    }

    public void updateById(WaCustomLogicConfigDo updateData) {
        waCustomLogicConfigRepository.updateById(updateData);
    }

    public void save(WaCustomLogicConfigDo addData) {
        waCustomLogicConfigRepository.insert(addData);
    }

    public void deleteById(Long configId) {
        waCustomLogicConfigRepository.deleteById(configId);
    }

    public WaCustomLogicConfigDo getByCode(String tenantId, String belongBusiness, String code) {
        return waCustomLogicConfigRepository.getByCode(tenantId, belongBusiness, code);
    }

    public void doInitCrtField() {
        UserInfo userInfo = UserContext.preCheckUser();
        this.setTenantId(userInfo.getTenantId());
        this.setDeleted(0);
        this.setCreateBy(userInfo.getUserId());
        this.setCreateTime(System.currentTimeMillis());
        this.setUpdateBy(this.getCreateBy());
        this.setUpdateTime(this.getCreateTime());
    }
}