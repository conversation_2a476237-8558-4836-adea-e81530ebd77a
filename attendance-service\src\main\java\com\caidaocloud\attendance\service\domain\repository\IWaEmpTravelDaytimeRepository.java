package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDaytimeDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpTravelDaytimeDetail;

import java.util.List;

public interface IWaEmpTravelDaytimeRepository {
    int save(WaEmpTravelDaytimeDo daytimeDo);

    List<WaEmpTravelDaytimeDo> getEmpTravelDaytimeDetailList(String tenantId, String anyEmpIds, Long startDate, Long endDate, List<Integer> approvalStatusList);

    int deleteByTravelId(List<Long> travelIds);

    List<WaEmpTravelDaytimeDo> getEmpTravelDaytimeList(String tenantId, List<Long> empIds, Long startDate, Long endDate, List<Integer> status);

    void batchUpdate(List<EmpTravelDaytimeDetail> records);

    List<WaEmpTravelDaytimeDo> getEmpTravelDaytimeByTravelId(List<Long> travelIds);

    List<WaEmpTravelDaytimeDo> getEmpTravelDaytimeList(String tenantId, List<Long> travelIds);

    List<WaEmpTravelDaytimeDo> selectListByTravelId(Long travelId);

    List<WaEmpTravelDaytimeDo> getTravelDayTimeList(String tenantId, Long empId, Long dayTime, Long endTime);

    List<WaEmpTravelDaytimeDo> selectTravelDaytimeList(String tenantId, Long empId, Long startDate,
                                                       Long endDate, List<Integer> status, List<Integer> periodTypes);
}
