package com.caidaocloud.attendance.core.schedule.service;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.RedisCache;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.wa.dto.EmpCalendarInfoDto;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaWorktimeDetail;
import com.caidaocloud.attendance.core.schedule.dto.ListEmpScheduleQueryDto;
import com.caidaocloud.attendance.core.wa.dto.CalendarEventDto;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.shift.EmpCalendarShiftForMonthDto;
import com.caidaocloud.attendance.core.wa.dto.shift.ListEmpRelCalendarQueryDto;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiWorkTimeBaseDto;
import com.caidaocloud.attendance.core.wa.enums.CalendarWorktimeTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.ShiftQueryCoreService;
import com.caidaocloud.attendance.core.wa.service.WorkCalendarFeignService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.wa.vo.EmpMultiShiftInfoVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 员工排班查询服务（支持假勤-固定班次日历类型&工时模块-排班制日历类型-排班查询）
 *
 * <AUTHOR>
 * @Date 2025/2/14
 */
@Slf4j
@Service
public class ScheduleQueryCoreService extends ShiftQueryCoreService {
    @Autowired
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WorkCalendarFeignService workCalendarFeignService;
    @Autowired
    private EmpScheduleFeignService empScheduleFeignService;

    /**
     * 查询员工排班
     *
     * @param belongid
     * @param empid
     * @param tmType
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public Map<Long, WaWorktimeDetail> getEmpWaWorktimeDetail(String belongid, Long empid, Integer tmType,
                                                              Long startDate, Long endDate) {
        // 查询员工日历考勤类型
        ListEmpRelCalendarQueryDto calendarQueryDto = new ListEmpRelCalendarQueryDto();
        calendarQueryDto.setBelongOrgid(belongid);
        calendarQueryDto.setEmpids(Lists.newArrayList(empid));
        calendarQueryDto.setStartDate(startDate);
        calendarQueryDto.setEndDate(endDate);
        List<EmpCalendarInfoDto> empCalendarList = workCalendarFeignService.listEmpRelCalendar(calendarQueryDto);
        if (CollectionUtils.isEmpty(empCalendarList)) {// 固定班次
            return super.getEmpWaWorktimeDetail(belongid, empid, tmType, startDate, endDate);
        }

        Map<Integer, List<EmpCalendarInfoDto>> calendarWorktimeTypeListMap = empCalendarList.stream()
                .collect(Collectors.groupingBy(it -> Optional.ofNullable(it.getWorktimeType())
                        .orElse(CalendarWorktimeTypeEnum.FIXED_SCHEDULE.getIndex())));
        List<Integer> worktimeTypeKeyList = calendarWorktimeTypeListMap
                .keySet().stream().distinct().collect(Collectors.toList());

        // 只存在一种排班制
        if (worktimeTypeKeyList.size() == 1) {
            if (CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(worktimeTypeKeyList.get(0))) {
                return getScheduleShiftMap(belongid, empid, startDate, endDate);
            } else {// 固定班次
                return super.getEmpWaWorktimeDetail(belongid, empid, tmType, startDate, endDate);
            }
        }

        // 同时存在多种排班制
        Map<Long, WaWorktimeDetail> scheduleShiftMap = getScheduleShiftMap(belongid, empid, startDate, endDate);
        Map<Long, WaWorktimeDetail> fixedShiftMap = super.getEmpWaWorktimeDetail(belongid, empid, tmType, startDate, endDate);
        Map<Long, WaWorktimeDetail> waWorktimeDetailMap = new HashMap<>();
        long tmpDate = startDate;
        while (tmpDate <= endDate) {
            long finalTmpDate = tmpDate;
            Optional<EmpCalendarInfoDto> calendarOpt = empCalendarList.stream()
                    .filter(it -> finalTmpDate >= it.getStartTime() && finalTmpDate <= it.getEndTime())
                    .findFirst();
            if (calendarOpt.isPresent()
                    && CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(calendarOpt.get().getWorktimeType())) {
                WaWorktimeDetail scheduleWorktimeDetail = scheduleShiftMap.get(tmpDate);
                if (null != scheduleWorktimeDetail) {
                    waWorktimeDetailMap.put(tmpDate, scheduleWorktimeDetail);
                }
            } else {
                WaWorktimeDetail fixedShift = fixedShiftMap.get(tmpDate);
                if (null != fixedShift) {
                    waWorktimeDetailMap.put(tmpDate, fixedShift);
                }
            }
            tmpDate = tmpDate + 86400;
        }
        return waWorktimeDetailMap;
    }

    /**
     * 查询员工排班
     *
     * @param belongid
     * @param empid
     * @param tmType
     * @param startDate
     * @param endDate
     * @param worktimeType
     * @param isAutoScheduling
     * @return
     */
    @Override
    public Map<Long, WaWorktimeDetail> getEmpWaWorktimeDetail(String belongid, Long empid, Integer tmType,
                                                              Long startDate, Long endDate, Integer worktimeType,
                                                              Boolean isAutoScheduling) {
        // 查询员工日历考勤类型
        ListEmpRelCalendarQueryDto calendarQueryDto = new ListEmpRelCalendarQueryDto();
        calendarQueryDto.setBelongOrgid(belongid);
        calendarQueryDto.setEmpids(Lists.newArrayList(empid));
        calendarQueryDto.setStartDate(startDate);
        calendarQueryDto.setEndDate(endDate);
        List<EmpCalendarInfoDto> empCalendarList = workCalendarFeignService.listEmpRelCalendar(calendarQueryDto);
        if (CollectionUtils.isEmpty(empCalendarList)) {// 固定班次
            return super.getEmpWaWorktimeDetail(belongid, empid, tmType, startDate, endDate, worktimeType, isAutoScheduling);
        }

        Map<Integer, List<EmpCalendarInfoDto>> calendarWorktimeTypeListMap = empCalendarList.stream()
                .collect(Collectors.groupingBy(it -> Optional.ofNullable(it.getWorktimeType())
                        .orElse(CalendarWorktimeTypeEnum.FIXED_SCHEDULE.getIndex())));
        List<Integer> worktimeTypeKeyList = calendarWorktimeTypeListMap
                .keySet().stream().distinct().collect(Collectors.toList());

        // 只存在一种排班制
        if (worktimeTypeKeyList.size() == 1) {
            if (CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(worktimeTypeKeyList.get(0))) {
                return getScheduleShiftMap(belongid, empid, startDate, endDate);
            } else {// 固定班次
                return super.getEmpWaWorktimeDetail(belongid, empid, tmType, startDate, endDate, worktimeType, isAutoScheduling);
            }
        }

        // 同时存在多种排班制
        Map<Long, WaWorktimeDetail> scheduleShiftMap = getScheduleShiftMap(belongid, empid, startDate, endDate);
        Map<Long, WaWorktimeDetail> fixedShiftMap = super.getEmpWaWorktimeDetail(belongid, empid, tmType, startDate, endDate, worktimeType, isAutoScheduling);
        Map<Long, WaWorktimeDetail> waWorktimeDetailMap = new HashMap<>();
        long tmpDate = startDate;
        while (tmpDate <= endDate) {
            long finalTmpDate = tmpDate;
            Optional<EmpCalendarInfoDto> calendarOpt = empCalendarList.stream()
                    .filter(it -> finalTmpDate >= it.getStartTime() && finalTmpDate <= it.getEndTime())
                    .findFirst();
            if (calendarOpt.isPresent()
                    && CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(calendarOpt.get().getWorktimeType())) {
                WaWorktimeDetail scheduleWorktimeDetail = scheduleShiftMap.get(tmpDate);
                if (null != scheduleWorktimeDetail) {
                    waWorktimeDetailMap.put(tmpDate, scheduleWorktimeDetail);
                }
            } else {
                WaWorktimeDetail fixedShift = fixedShiftMap.get(tmpDate);
                if (null != fixedShift) {
                    waWorktimeDetailMap.put(tmpDate, fixedShift);
                }
            }
            tmpDate = tmpDate + 86400;
        }
        return waWorktimeDetailMap;
    }

    /**
     * 排班制员工班次查询
     *
     * @param belongid
     * @param empid
     * @param startDate
     * @param endDate
     * @return
     */
    private Map<Long, WaWorktimeDetail> getScheduleShiftMap(String belongid, Long empid, Long startDate, Long endDate) {
        if (null == empid) {
            return new HashMap<>();
        }
        ListEmpScheduleQueryDto scheduleQueryDto = new ListEmpScheduleQueryDto();
        scheduleQueryDto.setTenantId(belongid);
        scheduleQueryDto.setEmpIds(Lists.newArrayList(empid));
        scheduleQueryDto.setStartDate(startDate);
        scheduleQueryDto.setEndDate(endDate);
        List<EmpMultiShiftInfoVo> empScheduleList = empScheduleFeignService.listEmpRelCalendar(scheduleQueryDto);
        if (CollectionUtils.isEmpty(empScheduleList)) {
            return new HashMap<>();
        }
        Map<Integer, WaShiftDef> corpAllShiftDef = this.getCorpAllShiftDef(belongid);
        if (MapUtils.isEmpty(corpAllShiftDef)) {
            return new HashMap<>();
        }
        List<WaWorktimeDetail> worktimeDetailList = empScheduleList.stream()
                .map(shiftInfoVo -> {
                    Integer shiftDefId = shiftInfoVo.getShiftDefId().intValue();
                    WaShiftDef shiftDef = corpAllShiftDef.get(shiftDefId);
                    if (shiftDef == null) {
                        return null;
                    }
                    WaWorktimeDetail detail = new WaWorktimeDetail();
                    BeanUtils.copyProperties(shiftDef, detail);
                    detail.setWorkDate(shiftInfoVo.getWorkDate());
                    detail.setDateType(shiftInfoVo.getDateType());
                    return detail;
                }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, List<WaWorktimeDetail>> listMap = worktimeDetailList.stream().collect(Collectors.groupingBy(WaWorktimeDetail::getWorkDate));
        return listMap.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> {
                    List<WaWorktimeDetail> waWorktimeDetailList = entry.getValue();
                    List<Integer> shiftDefIdList = waWorktimeDetailList.stream()
                            .map(WaWorktimeDetail::getShiftDefId).distinct().collect(Collectors.toList());
                    WaWorktimeDetail waWorktimeDetail = ObjectConverter.convert(waWorktimeDetailList.get(0), WaWorktimeDetail.class);
                    waWorktimeDetail.setShiftDefIds(StringUtils.join(shiftDefIdList, ","));
                    return waWorktimeDetail;
                },
                (v1, v2) -> v1
        ));
    }

    /**
     * 查询员工排班（考勤分析使用）
     *
     * @param paramsMap
     * @param empShiftInfoByDateMap
     * @param empIdList
     * @param corpShiftDefMap
     * @return
     */
    @Override
    public Map<String, EmpShiftInfo> getEmpShiftInfoListMaps(Map<String, Object> paramsMap,
                                                             Map<String, EmpShiftInfo> empShiftInfoByDateMap,
                                                             List<Long> empIdList,
                                                             Map<Integer, WaShiftDef> corpShiftDefMap) {
        String belongid = (String) paramsMap.get("belongid");
        Long startdate = (Long) paramsMap.get("startDate");
        Long startDate = startdate - 86400;
        Long endDate = (Long) paramsMap.get("endDate");

        Map<String, EmpShiftInfo> empShift = new HashMap<>();

        // 查询员工日历考勤类型
        ListEmpRelCalendarQueryDto calendarQueryDto = new ListEmpRelCalendarQueryDto();
        calendarQueryDto.setBelongOrgid(belongid);
        calendarQueryDto.setEmpids(empIdList);
        calendarQueryDto.setStartDate(startDate);
        calendarQueryDto.setEndDate(endDate);
        List<EmpCalendarInfoDto> empCalendarList = workCalendarFeignService.listEmpRelCalendar(calendarQueryDto);
        if (CollectionUtils.isEmpty(empCalendarList)) {// 固定班次
            return super.getEmpShiftInfoListMaps(paramsMap, empShiftInfoByDateMap, empIdList, corpShiftDefMap);
        }

        Map<Long, List<EmpCalendarInfoDto>> empCalendarListMap = empCalendarList.stream().collect(Collectors.groupingBy(EmpCalendarInfoDto::getEmpid));
        Map<Integer, List<EmpCalendarInfoDto>> calendarWorktimeTypeListMap = empCalendarList.stream()
                .collect(Collectors.groupingBy(it -> Optional.ofNullable(it.getWorktimeType())
                        .orElse(CalendarWorktimeTypeEnum.FIXED_SCHEDULE.getIndex())));
        List<Integer> worktimeTypeKeyList = calendarWorktimeTypeListMap.keySet().stream().distinct().collect(Collectors.toList());

        // 只存在一种排班制
        if (worktimeTypeKeyList.size() == 1) {
            if (CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(worktimeTypeKeyList.get(0))) {
                List<EmpShiftInfo> scheduleEmpShiftInfoList = getScheduleEmpShiftInfoList(belongid, empIdList, startDate, endDate);
                if (CollectionUtils.isEmpty(scheduleEmpShiftInfoList)) {
                    return new HashMap<>();
                }
                Map<String, List<EmpShiftInfo>> scheduleEmpShiftGroupMap = scheduleEmpShiftInfoList.stream()
                        .collect(Collectors.groupingBy(it -> String.format("%s_%s", it.getEmpid(), it.getWorkDate())));

                scheduleEmpShiftGroupMap.forEach((itKey, itList) -> {
                    EmpShiftInfo shift = ObjectConverter.convert(itList.get(0), EmpShiftInfo.class);
                    shift.setShiftDefList(itList);
                    String key = shift.getEmpid() + "_" + shift.getShiftDefId() + "_" + shift.getWorkDate();
                    empShift.put(key, shift);
                    empShiftInfoByDateMap.put(shift.getEmpid() + "_" + shift.getWorkDate(), shift);
                });
                return empShift;
            } else {// 固定班次
                return super.getEmpShiftInfoListMaps(paramsMap, empShiftInfoByDateMap, empIdList, corpShiftDefMap);
            }
        }

        // 同时存在多种排班制
        // 排班制员工
        List<EmpCalendarInfoDto> scheduleCalendarList = calendarWorktimeTypeListMap.get(CalendarWorktimeTypeEnum.SCHEDULE.getIndex());
        List<Long> scheduleEmpIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(scheduleCalendarList)) {
            scheduleEmpIdList.addAll(scheduleCalendarList.stream().map(EmpCalendarInfoDto::getEmpid).distinct()
                    .collect(Collectors.toList()));
        }
        Map<String, EmpShiftInfo> scheduleEmpShiftMap = new HashMap<>();
        List<EmpShiftInfo> scheduleEmpShiftList = getScheduleEmpShiftInfoList(belongid, scheduleEmpIdList, startDate, endDate);
        if (CollectionUtils.isNotEmpty(scheduleEmpShiftList)) {
            Map<String, List<EmpShiftInfo>> scheduleEmpShiftGroupMap = scheduleEmpShiftList.stream()
                    .collect(Collectors.groupingBy(it -> String.format("%s_%s", it.getEmpid(), it.getWorkDate())));
            scheduleEmpShiftGroupMap.forEach((itKey, itList) -> {
                EmpShiftInfo shift = ObjectConverter.convert(itList.get(0), EmpShiftInfo.class);
                shift.setShiftDefList(itList);
                scheduleEmpShiftMap.put(shift.getEmpid() + "_" + shift.getWorkDate(), shift);
            });
        }

        // 固定班次员工
        List<EmpCalendarInfoDto> fixedCalendarList = calendarWorktimeTypeListMap.get(CalendarWorktimeTypeEnum.FIXED_SCHEDULE.getIndex());
        List<Long> fixedEmpIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(fixedCalendarList)) {
            fixedEmpIdList.addAll(fixedCalendarList.stream().map(EmpCalendarInfoDto::getEmpid).distinct().collect(Collectors.toList()));
        }
        Map<String, EmpShiftInfo> fixedEmpShiftInfoByDateMap = new HashMap<>();
        super.getEmpShiftInfoListMaps(paramsMap, fixedEmpShiftInfoByDateMap, fixedEmpIdList, corpShiftDefMap);

        // 剩余的其他员工
        Map<String, EmpShiftInfo> otherEmpShiftInfoByDateMap = new HashMap<>();
        List<Long> otherEmpIdList = empIdList.stream()
                .filter(empId -> !scheduleEmpIdList.contains(empId) && !fixedEmpIdList.contains(empId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(otherEmpIdList)) {
            super.getEmpShiftInfoListMaps(paramsMap, otherEmpShiftInfoByDateMap, otherEmpIdList, corpShiftDefMap);
        }

        for (Long empId : empIdList) {
            List<EmpCalendarInfoDto> calendarList = empCalendarListMap.get(empId);
            if (CollectionUtils.isEmpty(calendarList)) {
                continue;
            }
            long tmpDate = startDate;
            while (tmpDate <= endDate) {
                String shiftKey = String.format("%s_%s", empId, tmpDate);

                long finalTmpDate = tmpDate;
                Optional<EmpCalendarInfoDto> calendarOpt = calendarList.stream()
                        .filter(it -> finalTmpDate >= it.getStartTime() && finalTmpDate <= it.getEndTime())
                        .findFirst();
                if (calendarOpt.isPresent()
                        && CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(calendarOpt.get().getWorktimeType())) {
                    if (null != scheduleEmpShiftMap.get(shiftKey)) {
                        EmpShiftInfo shift = scheduleEmpShiftMap.get(shiftKey);

                        String key = shift.getEmpid() + "_" + shift.getShiftDefId() + "_" + shift.getWorkDate();
                        empShift.put(key, shift);
                        empShiftInfoByDateMap.put(shift.getEmpid() + "_" + shift.getWorkDate(), shift);
                    }
                } else {
                    if (null != fixedEmpShiftInfoByDateMap.get(shiftKey)) {
                        EmpShiftInfo shift = fixedEmpShiftInfoByDateMap.get(shiftKey);

                        String key = shift.getEmpid() + "_" + shift.getShiftDefId() + "_" + shift.getWorkDate();
                        empShift.put(key, shift);
                        empShiftInfoByDateMap.put(shift.getEmpid() + "_" + shift.getWorkDate(), shift);
                    } else if (null != otherEmpShiftInfoByDateMap.get(shiftKey)) {
                        EmpShiftInfo shift = otherEmpShiftInfoByDateMap.get(shiftKey);

                        String key = shift.getEmpid() + "_" + shift.getShiftDefId() + "_" + shift.getWorkDate();
                        empShift.put(key, shift);
                        empShiftInfoByDateMap.put(shift.getEmpid() + "_" + shift.getWorkDate(), shift);
                    }
                }
                tmpDate = tmpDate + 86400;
            }
        }
        return empShift;
    }

    /**
     * 查询员工排班（排班制）
     *
     * @param belongid
     * @param empIdList
     * @param startDate
     * @param endDate
     * @return
     */
    private List<EmpShiftInfo> getScheduleEmpShiftInfoList(String belongid, List<Long> empIdList, Long startDate, Long endDate) {
        if (CollectionUtils.isEmpty(empIdList)) {
            return Lists.newArrayList();
        }
        ListEmpScheduleQueryDto scheduleQueryDto = new ListEmpScheduleQueryDto();
        scheduleQueryDto.setTenantId(belongid);
        scheduleQueryDto.setEmpIds(empIdList);
        scheduleQueryDto.setStartDate(startDate);
        scheduleQueryDto.setEndDate(endDate);
        List<EmpMultiShiftInfoVo> empScheduleList = empScheduleFeignService.listEmpRelCalendar(scheduleQueryDto);
        if (CollectionUtils.isEmpty(empScheduleList)) {
            return Lists.newArrayList();
        }
        Map<Integer, WaShiftDef> corpAllShiftDef = this.getCorpAllShiftDef(belongid);
        if (MapUtils.isEmpty(corpAllShiftDef)) {
            return Lists.newArrayList();
        }
        List<EmpShiftInfo> list = Lists.newArrayList();
        for (EmpMultiShiftInfoVo shiftInfoVo : empScheduleList) {
            Integer shiftDefId = shiftInfoVo.getShiftDefId().intValue();
            WaShiftDef shiftDef;
            if (!corpAllShiftDef.containsKey(shiftDefId) || null == (shiftDef = corpAllShiftDef.get(shiftDefId))) {
                continue;
            }
            EmpShiftInfo shift = new EmpShiftInfo();
            BeanUtils.copyProperties(shiftDef, shift);
            convertJsonValue(shiftDef, shift);
            shift.setEmpid(shiftInfoVo.getEmpId());
            shift.setWorkDate(shiftInfoVo.getWorkDate());
            shift.setDateType(shiftInfoVo.getDateType());
            list.add(shift);
        }
        return list;
    }

    /**
     * 员工端：指定日期查询员工某天的排班信息
     *
     * @param empId
     * @param date
     * @return
     */
    @Override
    @RedisCache(expire = 60)
    public List<Map> getShiftListForEmpPortal(Long empId, Long date) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (null == empInfo || StringUtils.isBlank(empInfo.getBelongOrgId())) {
            return Lists.newArrayList();
        }
        String tenantId = empInfo.getBelongOrgId();
        // 查询员工日历考勤类型
        ListEmpRelCalendarQueryDto calendarQueryDto = new ListEmpRelCalendarQueryDto();
        calendarQueryDto.setBelongOrgid(tenantId);
        calendarQueryDto.setEmpids(Lists.newArrayList(empId));
        calendarQueryDto.setStartDate(date);
        calendarQueryDto.setEndDate(date);
        List<EmpCalendarInfoDto> empCalendarList = workCalendarFeignService.listEmpRelCalendar(calendarQueryDto);
        if (CollectionUtils.isEmpty(empCalendarList)) {// 固定班次
            return super.getShiftListForEmpPortal(empId, date);
        }
        EmpCalendarInfoDto empCalendarInfoDto = empCalendarList.get(0);
        if (CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(empCalendarInfoDto.getWorktimeType())) {
            Map<Long, WaWorktimeDetail> scheduleShiftMap = getScheduleShiftMap(tenantId, empId, date, date);
            WaWorktimeDetail worktimeDetail;
            Map<Integer, WaShiftDef> shiftDefMap;
            if (MapUtils.isEmpty(scheduleShiftMap)
                    || null == (worktimeDetail = scheduleShiftMap.get(date))
                    || CollectionUtils.isEmpty(worktimeDetail.doGetShiftDefIdList())
                    || null == (shiftDefMap = getCorpAllShiftDef(tenantId))
                    || shiftDefMap.isEmpty()) {
                return Lists.newArrayList();
            }
            // 部门办公地点
            SysCorpOrg org = empInfo.getOrgid() != null ? sysCorpOrgMapper.selectByPrimaryKey(empInfo.getOrgid()) : null;
            String officeAddr = null != org ? org.getOfficeAddr() : "";
            return worktimeDetail.doGetShiftDefIdList().stream().map(shiftDefId -> {
                WaShiftDef shiftDef = shiftDefMap.get(shiftDefId);
                if (null == shiftDef) {
                    return null;
                }
                Map<String, Object> shiftMap = new HashMap<>();
                shiftMap.put("dateType", shiftDef.getDateType());
                shiftMap.put("workDate", date);
                shiftMap.put("shiftDefName", BaseConst.WA_DATE_TYPE.get(shiftDef.getDateType()));
                shiftMap.put("startTime", shiftDef.getStartTime());
                shiftMap.put("endTime", shiftDef.getEndTime());
                shiftMap.put("restTimeDesc", shiftDef.getRestTimeDesc());
                shiftMap.put("onDutyStartTime", shiftDef.getOnDutyStartTime());
                shiftMap.put("onDutyEndTime", shiftDef.getOnDutyEndTime());
                shiftMap.put("offDutyStartTime", shiftDef.getOffDutyStartTime());
                shiftMap.put("offDutyEndTime", shiftDef.getOffDutyEndTime());
                shiftMap.put("shiftCode", shiftDef.getShiftDefCode());
                shiftMap.put("shiftName", shiftDef.getShiftDefName());
                if (null != shiftDef.getI18nShiftDefName()) {
                    shiftMap.put("shiftName", LangParseUtil.getI18nLanguage(shiftDef.getI18nShiftDefName(), shiftDef.getShiftDefName()));
                }
                shiftMap.put("i18nShiftDefName", shiftDef.getI18nShiftDefName());
                shiftMap.put("isNoonRest", shiftDef.getIsNoonRest());
                shiftMap.put("noonRestStart", shiftDef.getNoonRestStart());
                shiftMap.put("noonRestEnd", shiftDef.getNoonRestEnd());
                shiftMap.put("workTotalTime", shiftDef.getWorkTotalTime());
                shiftMap.put("addr", officeAddr);
                shiftMap.put("shiftDefId", shiftDef.getShiftDefId());
                shiftMap.put("startTimeBelong", shiftDef.getStartTimeBelong());
                shiftMap.put("endTimeBelong", shiftDef.getEndTimeBelong());
                shiftMap.put("noonRestStartBelong", shiftDef.getNoonRestStartBelong());
                shiftMap.put("noonRestEndBelong", shiftDef.getNoonRestEndBelong());
                if (null != shiftDef.getMultiWorkTimes()) {
                    List<MultiWorkTimeBaseDto> multiWorkTimeDtoList = FastjsonUtil.toArrayList(((PGobject) shiftDef.getMultiWorkTimes()).getValue(), MultiWorkTimeBaseDto.class);
                    shiftMap.put("multiWorkTimes", FastjsonUtil.convertList(multiWorkTimeDtoList, Map.class));
                }
                return shiftMap;
            }).filter(it -> !Objects.isNull(it)).collect(Collectors.toList());
        } else {// 固定班次
            return super.getShiftListForEmpPortal(empId, date);
        }
    }

    @Override
    public List<EmpCalendarShiftForMonthDto> getEmpCalendarShiftByMonth(String tenantId,
                                                                        Long empId,
                                                                        Integer searchMonth,
                                                                        Long startDate,
                                                                        Long endDate) {
        if (null == startDate || null == endDate) {
            Map<String, Long> timePeriodByMonth;
            try {
                timePeriodByMonth = EmpCalendarShiftForMonthDto.calCalendarTimePeriodByMonth(searchMonth);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Lists.newArrayList();
            }
            if (MapUtils.isEmpty(timePeriodByMonth)) {
                return Lists.newArrayList();
            }
            startDate = timePeriodByMonth.get("start");
            endDate = timePeriodByMonth.get("end");
        }

        // 查询员工日历考勤类型
        ListEmpRelCalendarQueryDto calendarQueryDto = new ListEmpRelCalendarQueryDto();
        calendarQueryDto.setBelongOrgid(tenantId);
        calendarQueryDto.setEmpids(Lists.newArrayList(empId));
        calendarQueryDto.setStartDate(startDate);
        calendarQueryDto.setEndDate(endDate);
        List<EmpCalendarInfoDto> empCalendarList = workCalendarFeignService.listEmpRelCalendar(calendarQueryDto);
        if (CollectionUtils.isEmpty(empCalendarList)) {// 固定班次
            return super.getEmpCalendarShiftByMonth(tenantId, empId, searchMonth, startDate, endDate);
        }

        Map<Integer, List<EmpCalendarInfoDto>> calendarWorktimeTypeListMap = empCalendarList.stream()
                .collect(Collectors.groupingBy(it -> Optional.ofNullable(it.getWorktimeType())
                        .orElse(CalendarWorktimeTypeEnum.FIXED_SCHEDULE.getIndex())));
        List<Integer> worktimeTypeKeyList = calendarWorktimeTypeListMap
                .keySet().stream().distinct().collect(Collectors.toList());

        // 只存在一种排班制
        if (worktimeTypeKeyList.size() == 1) {
            if (CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(worktimeTypeKeyList.get(0))) {
                return getEmpCalendarShiftForSchedule(tenantId, empId, startDate, endDate);
            } else {// 固定班次
                return super.getEmpCalendarShiftByMonth(tenantId, empId, searchMonth, startDate, endDate);
            }
        }

        // 同时存在多种排班制
        List<EmpCalendarShiftForMonthDto> scheduleShiftDtoList = getEmpCalendarShiftForSchedule(tenantId, empId, startDate, endDate);
        Map<String, EmpCalendarShiftForMonthDto> scheduleShiftMap = scheduleShiftDtoList
                .stream().collect(Collectors.toMap(EmpCalendarShiftForMonthDto::getWorkDate, Function.identity(), (v1, v2) -> v1));

        List<EmpCalendarShiftForMonthDto> fixedShiftDtoList = super.getEmpCalendarShiftByMonth(tenantId, empId, searchMonth, startDate, endDate);
        Map<String, EmpCalendarShiftForMonthDto> fixedShiftMap = fixedShiftDtoList
                .stream().collect(Collectors.toMap(EmpCalendarShiftForMonthDto::getWorkDate, Function.identity(), (v1, v2) -> v1));

        List<EmpCalendarShiftForMonthDto> allShiftDeoList = Lists.newArrayList();
        long tmpDate = startDate;
        while (tmpDate <= endDate) {
            long finalTmpDate = tmpDate;
            Optional<EmpCalendarInfoDto> calendarOpt = empCalendarList.stream()
                    .filter(it -> finalTmpDate >= it.getStartTime() && finalTmpDate <= it.getEndTime())
                    .findFirst();
            String tmpDateYmd = DateUtil.parseDateToPattern(new Date(tmpDate * 1000), "yyyyMMdd");
            if (calendarOpt.isPresent()
                    && CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(calendarOpt.get().getWorktimeType())) {
                EmpCalendarShiftForMonthDto shiftForMonthDto = scheduleShiftMap.get(tmpDateYmd);
                if (null != shiftForMonthDto) {
                    allShiftDeoList.add(shiftForMonthDto);
                }
            } else {
                EmpCalendarShiftForMonthDto shiftForMonthDto = fixedShiftMap.get(tmpDateYmd);
                if (null != shiftForMonthDto) {
                    allShiftDeoList.add(shiftForMonthDto);
                }
            }
            tmpDate = tmpDate + 86400;
        }
        return allShiftDeoList;
    }

    private List<EmpCalendarShiftForMonthDto> getEmpCalendarShiftForSchedule(String belongid, Long empid, Long startDate, Long endDate) {
        if (null == empid) {
            return Lists.newArrayList();
        }
        ListEmpScheduleQueryDto scheduleQueryDto = new ListEmpScheduleQueryDto();
        scheduleQueryDto.setTenantId(belongid);
        scheduleQueryDto.setEmpIds(Lists.newArrayList(empid));
        scheduleQueryDto.setStartDate(startDate);
        scheduleQueryDto.setEndDate(endDate);
        List<EmpMultiShiftInfoVo> empScheduleList = empScheduleFeignService.listEmpRelCalendar(scheduleQueryDto);
        if (CollectionUtils.isEmpty(empScheduleList)) {
            return Lists.newArrayList();
        }
        Map<Integer, WaShiftDef> corpAllShiftDef = this.getCorpAllShiftDef(belongid);
        if (MapUtils.isEmpty(corpAllShiftDef)) {
            return Lists.newArrayList();
        }
        List<EmpCalendarShiftForMonthDto> empCalendarShiftDtoList = empScheduleList.stream()
                .map(shiftInfoVo -> {
                    Integer shiftDefId = shiftInfoVo.getShiftDefId().intValue();
                    WaShiftDef shiftDef = corpAllShiftDef.get(shiftDefId);
                    if (shiftDef == null) {
                        return null;
                    }
                    EmpCalendarShiftForMonthDto ec = new EmpCalendarShiftForMonthDto();
                    ec.setDateType(shiftInfoVo.getDateType());
                    ec.setWorkDateTimestamp(shiftInfoVo.getWorkDate());
                    ec.setWorkDate(DateUtil.parseDateToPattern(new Date(shiftInfoVo.getWorkDate() * 1000), "yyyyMMdd"));
                    ec.setShiftDefName(DateTypeEnum.getName(shiftInfoVo.getDateType()));
                    ec.setShiftDefId(shiftDefId);
                    ec.setShiftDefCode(shiftDef.getShiftDefCode());
                    ec.setShiftName(shiftDef.getShiftDefName());
                    if (null != shiftDef.getI18nShiftDefName()) {
                        ec.setShiftName(LangParseUtil.getI18nLanguage(shiftDef.getI18nShiftDefName(), shiftDef.getShiftDefName()));
                    }
                    return ec;
                }).filter(Objects::nonNull).collect(Collectors.toList());

        Map<String, List<EmpCalendarShiftForMonthDto>> listMap = empCalendarShiftDtoList.stream().collect(Collectors.groupingBy(EmpCalendarShiftForMonthDto::getWorkDate));
        List<EmpCalendarShiftForMonthDto> calendarDtoList = Lists.newArrayList();
        listMap.forEach((workDate, shiftList) -> {
            EmpCalendarShiftForMonthDto shiftForMonthDto = ObjectConverter.convert(shiftList.get(0), EmpCalendarShiftForMonthDto.class);
            shiftForMonthDto.setShiftList(shiftList);
            calendarDtoList.add(shiftForMonthDto);
        });
        return calendarDtoList;
    }

    /**
     * 考勤日历-班次查询
     *
     * @param belongOrgId
     * @param empId
     * @param start
     * @param end
     * @param dataScope
     * @return
     */
    @Override
    public List<CalendarEventDto> getEmpCalendarShiftListByYm(String belongOrgId, Long empId,
                                                              String start, String end, String dataScope) {
        Long startDate = DateUtil.convertStringToDateTime(start, "yyyy-MM-dd", Boolean.TRUE);
        Long endDate = DateUtil.convertStringToDateTime(end, "yyyy-MM-dd", Boolean.TRUE);


        // 查询员工日历考勤类型
        ListEmpRelCalendarQueryDto calendarQueryDto = new ListEmpRelCalendarQueryDto();
        calendarQueryDto.setBelongOrgid(belongOrgId);
        calendarQueryDto.setEmpids(Lists.newArrayList(empId));
        calendarQueryDto.setStartDate(startDate);
        calendarQueryDto.setEndDate(endDate);
        List<EmpCalendarInfoDto> empCalendarList = workCalendarFeignService.listEmpRelCalendar(calendarQueryDto);
        if (CollectionUtils.isEmpty(empCalendarList)) {// 固定班次
            return super.getEmpCalendarShiftListByYm(belongOrgId, empId, start, end, dataScope);
        }

        Map<Integer, List<EmpCalendarInfoDto>> calendarWorktimeTypeListMap = empCalendarList.stream()
                .collect(Collectors.groupingBy(it -> Optional.ofNullable(it.getWorktimeType()).orElse(CalendarWorktimeTypeEnum.FIXED_SCHEDULE.getIndex())));
        List<Integer> worktimeTypeKeyList = calendarWorktimeTypeListMap.keySet().stream().distinct().collect(Collectors.toList());

        // 只存在一种排班制
        if (worktimeTypeKeyList.size() == 1) {
            if (CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(worktimeTypeKeyList.get(0))) {
                List<EmpCalendarShiftForMonthDto> scheduleShiftDtoList = getEmpCalendarShiftForSchedule(belongOrgId, empId,
                        startDate, endDate);
                return scheduleShiftDtoList.stream().map(it -> {
                    CalendarEventDto event = new CalendarEventDto();
                    event.setId(it.getShiftDefId());
                    event.setTitle(it.getShiftDefCode() + " " + it.getShiftName());
                    event.setDateType(it.getDateType());
                    event.setStart(DateUtil.getDateStrByTimesamp(it.getWorkDateTimestamp()));
                    event.setStartTimestamp(it.getWorkDateTimestamp());
                    return event;
                }).collect(Collectors.toList());
            } else {// 固定班次
                return super.getEmpCalendarShiftListByYm(belongOrgId, empId, start, end, dataScope);
            }
        }

        // 同时存在多种排班制
        List<EmpCalendarShiftForMonthDto> scheduleShiftDtoList = getEmpCalendarShiftForSchedule(belongOrgId, empId,
                startDate, endDate);
        Map<Long, CalendarEventDto> scheduleShiftMap = scheduleShiftDtoList
                .stream().map(it -> {
                    CalendarEventDto event = new CalendarEventDto();
                    event.setId(it.getShiftDefId());
                    event.setTitle(it.getShiftDefCode() + " " + it.getShiftName());
                    event.setDateType(it.getDateType());
                    event.setStart(DateUtil.getDateStrByTimesamp(it.getWorkDateTimestamp()));
                    event.setStartTimestamp(it.getWorkDateTimestamp());
                    return event;
                }).collect(Collectors.toMap(CalendarEventDto::getStartTimestamp, Function.identity(), (v1, v2) -> v1));

        List<CalendarEventDto> fixedShiftDtoList = super.getEmpCalendarShiftListByYm(belongOrgId, empId, start, end, dataScope);
        Map<Long, CalendarEventDto> fixedShiftMap = fixedShiftDtoList
                .stream().collect(Collectors.toMap(CalendarEventDto::getStartTimestamp, Function.identity(), (v1, v2) -> v1));

        List<CalendarEventDto> allShiftDeoList = Lists.newArrayList();
        long tmpDate = startDate;
        while (tmpDate <= endDate) {
            long finalTmpDate = tmpDate;
            Optional<EmpCalendarInfoDto> calendarOpt = empCalendarList.stream()
                    .filter(it -> finalTmpDate >= it.getStartTime() && finalTmpDate <= it.getEndTime())
                    .findFirst();
            if (calendarOpt.isPresent()
                    && CalendarWorktimeTypeEnum.SCHEDULE.getIndex().equals(calendarOpt.get().getWorktimeType())) {
                CalendarEventDto calendarEventDto = scheduleShiftMap.get(tmpDate);
                if (null != calendarEventDto) {
                    allShiftDeoList.add(calendarEventDto);
                }
            } else {
                CalendarEventDto calendarEventDto = fixedShiftMap.get(tmpDate);
                if (null != calendarEventDto) {
                    allShiftDeoList.add(calendarEventDto);
                }
            }
            tmpDate = tmpDate + 86400;
        }
        return allShiftDeoList;
    }
}
