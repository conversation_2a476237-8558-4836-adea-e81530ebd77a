package com.caidaocloud.attendance.service.application.enums;

public enum CarryOverStartTypeEnum {
    //结转有效期开始日期类型：1 原配额生效日期 、2 原配额失效日期后一天、3 原配额失效日期
    ORIGINAL_START_DATE(1, "原配额生效日期"),
    ORIGINAL_LAST_DATE_AFTE(2, "原配额失效日期后一天"),
    ORIGINAL_LAST_DATE(3,"原配额失效日期");

    private Integer index;
    private String name;

    CarryOverStartTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (CarryOverStartTypeEnum c : CarryOverStartTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
