package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.wa.dto.EmpLeaveInfo;
import com.caidaocloud.attendance.service.domain.repository.IWaEmpLeaveRepository;
import com.caidaocloud.dto.PageResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Data
@Service
public class WaLeaveDetailDo {
    private Long leaveDate;
    private Double timeDuration;
    private Integer timeUnit;
    private Long empId;
    private Double cancelTimeDuration;
}
