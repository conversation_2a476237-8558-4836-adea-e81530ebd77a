package com.caidaocloud.attendance.core.annoation.feign;

import com.caidaocloud.attendance.core.schedule.dto.ListEmpScheduleQueryDto;
import com.caidaocloud.attendance.core.wa.vo.EmpMultiShiftInfoVo;
import com.caidaocloud.attendance.core.wa.vo.WorkingHourCalendarDateVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 员工排班
 *
 * <AUTHOR>
 * @Date 2025/2/14
 */
@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = EmpScheduleFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "empScheduleFeignClient")
public interface EmpScheduleFeignClient {

    /**
     * 排班制员工班次查询
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/api/attendance/empSchedule/v1/wfm/listEmpSchedule")
    Result<List<EmpMultiShiftInfoVo>> getEmpScheduleList(@RequestBody ListEmpScheduleQueryDto queryDto);

    /**
     * 查询员工工时日历
     *
     * @param searchMonth
     * @param empId
     * @return
     */
    @GetMapping(value = "/api/attendance/empSchedule/v1/wfm/getShiftByMonthByEmpId")
    Result<List<WorkingHourCalendarDateVo>> getShiftByMonthByEmpId(@RequestParam("searchMonth") Integer searchMonth, @RequestParam(value = "empId", required = false) Long empId);
}
