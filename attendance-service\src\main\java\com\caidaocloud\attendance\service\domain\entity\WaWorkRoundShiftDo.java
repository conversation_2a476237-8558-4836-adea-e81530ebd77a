package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWorkRoundShiftRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/1
 */
@Slf4j
@Data
@Service
public class WaWorkRoundShiftDo {
    private Integer roundShiftId;
    private Integer workRoundId;
    private Integer shiftDefId;
    private Integer roundNo;
    private Long crtuser;
    private Long crttime;

    @Autowired
    private IWorkRoundShiftRepository workRoundShiftRepository;

    public List<WaWorkRoundShiftDo> selectListByWorkRoundId(Integer workRoundId) {
        return workRoundShiftRepository.selectListByWorkRoundId(workRoundId);
    }

    public int deleteByWorkRoundId(Integer workRoundId) {
        return workRoundShiftRepository.deleteByWorkRoundId(workRoundId);
    }

    public int save(WaWorkRoundShiftDo workRoundShiftDo) {
        return workRoundShiftRepository.save(workRoundShiftDo);
    }

    public List<WaWorkRoundShiftDo> selectListByShiftDefId(Integer shiftDefId) {
        return workRoundShiftRepository.selectListByShiftDefId(shiftDefId);
    }

}
