package com.caidaocloud.attendance.service.application.service.log;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.domain.entity.SysEmpInfoDo;
import com.caidaocloud.record.core.service.IParseFunction;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class LogEmpInfoService implements IParseFunction {
    @Resource
    private SysEmpInfoDo sysEmpInfoDo;

    @Override
    public String functionName() {
        return "empName";
    }

    @Override
    public String apply(String value) {
        SysEmpInfo emp = sysEmpInfoDo.getEmpInfoById(UserContext.getTenantId(), Long.valueOf(value));
        return emp.getWorkno() + "(" + emp.getEmpName() + ")";
    }
}
