package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.sdk.dto.SdkApplyShiftDTO;
import com.caidaocloud.attendance.sdk.feign.IEmpShiftChangeFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 员工班次变更
 *
 * <AUTHOR>
 * @Date 2023/6/27
 */
@Slf4j
@Service
public class SdkEmpShiftChangeService {
    @Autowired
    private IEmpShiftChangeFeignClient empShiftChangeFeignClient;

    public Result<?> userApplyShiftChange(SdkApplyShiftDTO dto) {
        return empShiftChangeFeignClient.userApplyShiftChange(dto);
    }
}
