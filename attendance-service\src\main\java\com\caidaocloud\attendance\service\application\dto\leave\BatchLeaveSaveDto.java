package com.caidaocloud.attendance.service.application.dto.leave;

import com.caidaocloud.attendance.service.interfaces.dto.LeaveApplySaveDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class BatchLeaveSaveDto {
    @ApiModelProperty("员工id")
    private Long empid;
    @ApiModelProperty("紧急联系人")
    private String emergencyContact;
    @ApiModelProperty("附件地址")
    private String filePath;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("休假集合")
    private List<LeaveApplySaveDto> leaveList;
    @ApiModelProperty("是否开启工作流")
    private Boolean workflowEnabled;
    
    @ApiModelProperty("假期类型id-选择单个假期类型时使用")
    private Integer leaveTypeId;
    @ApiModelProperty("假期类型单位-选择单个假期类型时使用")
    private Integer acctTimeType;
    @ApiModelProperty("假期类型id--选择多个假期类型时使用")
    private String multiLeaveTypeId;
}
