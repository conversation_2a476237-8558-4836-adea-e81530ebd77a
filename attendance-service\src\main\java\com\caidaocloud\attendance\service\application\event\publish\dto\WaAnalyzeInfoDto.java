package com.caidaocloud.attendance.service.application.event.publish.dto;

import lombok.Data;

@Data
public class WaAnalyzeInfoDto {
    private Integer analyzeId;
    private String belongOrgId;
    private Long empid;
    private Integer signinId;
    private Integer signoffId;
    private Integer workTime;
    private Float lateTime;
    private Float earlyTime;
    private Integer leaveTime;
    private Long belongDate;
    private String errMsg;
    private Integer shiftDefId;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private Integer isExp;
    private Float actualWorkTime;
    private Integer isKg;
    private Object levelColumnJsonb;
    private Object otColumnJsob;
    private Integer kgWorkTime;
    private Long regSigninTime;
    private Long regSignoffTime;
    private Object originLevelColumnJsonb;
    private Object originOtColumnJsonb;
    private Integer registerTime;
    private Integer isShift;
    private Integer bdkCount;
    private Object extCustomColJson;
    private Integer holidayWorkTime;
    private Integer abnormalAppealTime;
    private Integer clockType;
    private Object travelColumnJsonb;
    private Object originTravelColumnJsonb;
    private String shiftDefName;
    private Integer dateType;
}
