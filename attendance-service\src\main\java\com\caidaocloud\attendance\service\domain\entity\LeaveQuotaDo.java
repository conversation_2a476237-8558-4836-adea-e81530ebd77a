package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.service.domain.repository.IEmpLeaveQuotaRepository;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveDto;
import com.caidaocloud.dto.BasePage;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Data
@Service
public class LeaveQuotaDo {

    private Long empQuotaId;

    private Integer leaveTypeId;

    private String leaveName;

    private Float quotaDay;

    private Float usedDay;

    private Float adjustQuota;

    private Float inTransitQuota;

    private Float fixUsedDay;

    private Integer quotaType;

    private Integer unit;

    private Long hireDate;

    private Integer periodYear;

    private Long startDate;

    private Long lastDate;

    private Float nowQuotaDay;

    private Integer ifAdvance;

    private Float availabQuotaDay;

    private Float advanceDay;

    private Float leftDay;

    private String workplace;

    private Integer overTimeType;

    private String overTimeTypeTxt;

    private Long overtimeDate;

    private String description;

    private Float currentQuota;

    private Long configId;
    private String dataSource;
    private String i18nLeaveName;
    private Boolean displayQuotaDetail;

    //上年结转
    private Float retainDay;
    //上年留存流程中
    private Float retainInTransitQuota;
    // 上年结转已用
    private Float retainUsedDay;
    // 上年结转有效期至
    private Long retainValidDate;

    private Long disCycleStart;
    private Long disCycleEnd;

    @Resource
    private IEmpLeaveQuotaRepository empLeaveQuotaRepository;


    /**
     * 计算本年可用
     * 本年可用 = 可预支 ？本年额度 ：当前额度
     */
    public void calAvailableYear() {
        if (null == this.ifAdvance) {
            // 0 不可预支
            this.setIfAdvance(0);
        }
        // 本年可用 = 可预支 ？本年额度 ：当前额度
        this.setAvailabQuotaDay(1 == this.ifAdvance ? this.quotaDay : this.nowQuotaDay);
    }


    /**
     * 假期余额
     *
     * @param belongOrgId
     * @param empId
     * @param curDate
     * @return
     */
    public List<LeaveQuotaDo> getLeaveQuotaList(String belongOrgId, Long empId, Long curDate) {
        return empLeaveQuotaRepository.getLeaveQuotaList(belongOrgId, empId, curDate);
    }

    /**
     * 年假明细
     *
     * @param belongOrgId
     * @param empId
     * @param leaveTypeId
     * @return
     */
    @CDText(exp = {"workplace" + TextAspect.PLACE}, classType = LeaveQuotaDo.class)
    public List<LeaveQuotaDo> getAnnualLeaveList(String belongOrgId, Long empId, Integer leaveTypeId, Long configId,
                                                 Integer quotaType, Long today, Boolean mergeCarry) {
        return empLeaveQuotaRepository.getAnnualLeaveList(belongOrgId, empId, leaveTypeId, configId, quotaType, today, mergeCarry);
    }


    public List<LeaveQuotaDo> getCompensatoryQuotaList(String belongOrgId, Long empId, Long curDate, Integer leaveTypeId, Long configId, Boolean onlyAutoDataSource) {
        return empLeaveQuotaRepository.getCompensatoryQuotaList(belongOrgId, empId, curDate, leaveTypeId, configId, onlyAutoDataSource);
    }

    public PageList<LeaveQuotaDo> getInvalidCompensatory(AttendanceBasePage page, String belongOrgId, Long empId, Long curDate) {
        return empLeaveQuotaRepository.getInvalidCompensatory(page, belongOrgId, empId, curDate);
    }

    public List<LeaveQuotaDo> getInvalidCompensatoryQuotaList(String belongOrgId, Long empId, Long curDate, Integer leaveTypeId, Boolean onlyAutoDataSource) {
        return empLeaveQuotaRepository.getInvalidCompensatoryQuotaList(belongOrgId, empId, curDate, leaveTypeId, onlyAutoDataSource);
    }

    public void calTotalQuota(AnnualLeaveDto dto) {
        dto.setQuotaDay(dto.getQuotaDay() + this.quotaDay);
        dto.setNowQuotaDay(dto.getNowQuotaDay() + this.nowQuotaDay);
        dto.setAdjustQuota(dto.getAdjustQuota() + this.adjustQuota);
        dto.setUsedDay(dto.getUsedDay() + this.usedDay);
        dto.setFixUsedDay(dto.getFixUsedDay() + this.fixUsedDay);
        dto.setInTransitQuota(dto.getInTransitQuota() + this.inTransitQuota);
        dto.setLeftDay(dto.getLeftDay() + this.leftDay);
        dto.setAvailabQuotaDay(dto.getAvailabQuotaDay() + this.availabQuotaDay);
        dto.setAdvanceDay(dto.getAdvanceDay() + this.advanceDay);
        dto.setCurrentQuota(dto.getCurrentQuota() + this.currentQuota);
        dto.setRetainDay(dto.getRetainDay() + this.retainDay);
        dto.setRetainInTransitQuota(dto.getRetainInTransitQuota() + this.retainInTransitQuota);
        dto.setRetainUsedDay(dto.getRetainUsedDay() + this.retainUsedDay);
    }
}
