package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.JSONUtils;
import com.caidaocloud.attendance.service.application.service.ITaskService;
import com.caidaocloud.attendance.service.domain.entity.TaskDo;
import com.caidaocloud.attendance.service.interfaces.dto.task.TaskDto;
import com.caidaocloud.attendance.service.interfaces.dto.task.UploadFileResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: Aaron.Chen
 * @Date: 2021/4/16 11:25
 * @Description:
 **/
@Service
public class TaskServiceImpl implements ITaskService {

    @Autowired
    private TaskDo taskDo;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    @Override
    public TaskDto save(TaskDto taskDto) {
        taskDto.setId(snowflakeUtil.createId());
        TaskDo task = ObjectConverter.convert(taskDto, TaskDo.class);
        if (CollectionUtils.isNotEmpty(taskDto.getUploadFiles())) {
            task.setUploadFiles(JSONUtils.ObjectToJson(taskDto.getUploadFiles()));
        }
        taskDo.save(task);
        return ObjectConverter.convert(task, TaskDto.class);
    }

    @Override
    public void update(TaskDto taskDto, UserInfo userInfo) {
        TaskDo task = ObjectConverter.convert(taskDto, TaskDo.class);
        if (CollectionUtils.isNotEmpty(taskDto.getUploadFiles())) {
            task.setUploadFiles(JSONUtils.ObjectToJson(taskDto.getUploadFiles()));
        }
        taskDo.update(task, userInfo);
    }

    @Override
    public TaskDto getTaskById(Long id) {
        TaskDo task = taskDo.getById(id);
        if (null == task) {
            return null;
        }
        TaskDto taskDto = ObjectConverter.convert(task, TaskDto.class);
        if (StringUtil.isNotBlank(task.getUploadFiles())) {
            taskDto.setUploadFiles(JSON.parseArray(task.getUploadFiles(), UploadFileResult.class));
        }
        return taskDto;
    }
}
