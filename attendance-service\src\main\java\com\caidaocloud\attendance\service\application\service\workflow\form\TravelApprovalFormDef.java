package com.caidaocloud.attendance.service.application.service.workflow.form;

import com.caidaocloud.workflow.annotation.WfMetaFunFormDef;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.googlecode.totallylazy.Lists;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class TravelApprovalFormDef extends WfMetaFunFormDef {
    @NotNull
    @Override
    public List<WfMetaFunFormFieldDto> formList() {
        return Lists.list(
                new WfMetaFunFormFieldDto("originator", "发起人", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("applicant", "申请人", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("empType", "员工类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("org", "任职组织", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("workplace", "工作地", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("hireDate", "入职日期", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("startTime", "开始时间", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("endTime", "结束时间", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("duration", "申请时长", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("applyTime", "申请时间", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("travelType", "出差类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("site", "出行地点", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("travelMode", "出行方式", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("approvalStatus", "审批状态", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("reason", "申请事由", WfFieldDataTypeEnum.Text));
    }
}
