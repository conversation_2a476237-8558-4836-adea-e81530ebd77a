package com.caidaocloud.attendance.service.application.feign;

import com.caidaocloud.attendance.service.application.dto.emp.CostCenterDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = "${feign.rename.caidaocloud-hr-service:caidaocloud-hr-service}",
        fallback = WaCostCenterFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "waCostCenterFeignClient"
)
public interface WaCostCenterFeignClient {

    @ApiOperation("查询成本中心信息")
    @GetMapping("/api/hr/cost/v1/detail")
    Result<CostCenterDto> getDetail(@RequestParam("bid") String bid,
                                    @RequestParam(value = "dateTime", required = false) Long dateTime);
}
