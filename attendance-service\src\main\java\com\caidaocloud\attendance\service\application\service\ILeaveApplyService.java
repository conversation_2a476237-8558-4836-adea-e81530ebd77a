package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaEmpLeave;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.dto.EmpLeaveInfo;
import com.caidaocloud.attendance.core.wa.dto.WaLeaveDaytimeExtDto;
import com.caidaocloud.attendance.core.workflow.dto.WfDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveDaytimeDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.vo.HolidayQuotaVo;
import com.caidaocloud.attendance.service.interfaces.vo.KeyValueVo;
import com.caidaocloud.attendance.service.interfaces.vo.MaternityLeaveRangeVo;
import com.caidaocloud.attendance.service.interfaces.vo.UserLeaveApplyListVo;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Pair;

import java.util.List;
import java.util.Map;

/**
 * 休假申请
 *
 * <AUTHOR>
 * @Date 2021/3/19
 */
public interface ILeaveApplyService {

    void deleteByBatchId(String tenantId, Long batchId);

    boolean checkSwitchStatus(String configCode, String tenantId);

    Map getEmpLeaveById(Integer leaveId);

    Result saveLeaveApply(LeaveApplySaveDto leaveApplySaveDto, boolean enableWorkflow) throws Exception;

    void saveLeaveAttachments(AttachmentDto dto);

    List getLeaveApplyList(LeaveApplyDto dto, PageBean pageBean, UserInfo userInfo);

    AttendancePageResult<UserLeaveApplyListVo> getUserLeaveApplyList(UserLeaveApplySearchDto searchDto);

    AttendancePageResult<UserLeaveApplyListVo> getUserLeaveApplyListOfPortal(QueryPageBean queryPageBean);

    AttendancePageResult getEmpList(EmpListDto dto);

    List<Map> getUserLeaveTypes() throws Exception;

    List<HolidayQuotaVo> getUserLeaveTypeQuotas();

    Result<Boolean> revokeEmpLeave(RevokeEmpLeaveDto dto, UserInfo userInfo, boolean isSendMsg, boolean callWorkflow) throws Exception;

    List<Map> getLeaveTypeList(Long empId, Integer quotaType) throws Exception;

    List<KeyValueVo> getLeaveStatusList();

    Boolean urge(List<Integer> leaveIds);

    Result<MaternityLeaveRangeVo> getMaternityLeaveRange(MaternityLeaveRangeDto dto);

    Result<String> getHomeLeaveAvailableQuotaText(Long leaveTypeId, Long empId, String visitingReason, String marriage);

    Pair<Double, String> getHomeLeaveAvailableQuota(Long leaveTypeId, Long empId, String visitingReason, String marriage);

    void appendHomeLeaveInfo(String businessKey, WfDetailDto detailDto);

    void appendHomeLeaveInfo(String businessKey, WfResponseDto detailDto);

    Long getHomeLeaveRange(Long leaveTypeId, Long empId, long startTime, String visitingReason, String marriage);

    List<WaEmpLeave> getListByBatchId(String tenantId, Long batchId);

    List<WaLeaveDaytimeDo> getLeaveDayTimeList(Long date);

    /**
     * 根据日期获取当天休假的员工ID
     *
     * @param date
     * @return
     */
    List<Long> getLeaveEmpIdList(Long date);

    List<EmpLeaveInfo> getEmpLeaveDayTimeList(String tenantId, List<Long> empIdList, Long startDate, Long endDate);

    List<WaLeaveDaytimeExtDto> getEmpLeaveDayTimeExtDtoList(String tenantId, List<Long> empIdList, Long startDate, Long endDate,
                                                            Map<String, WaShiftDo> empShiftDoMap);
}
