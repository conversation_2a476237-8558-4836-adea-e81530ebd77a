package com.caidaocloud.attendance.service.application.cron;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.SpringUtils;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.dto.clock.ClockAnalyseDto;
import com.caidaocloud.attendance.service.application.dto.clock.ClockAnalyseTaskParamDto;
import com.caidaocloud.attendance.service.application.event.publish.AnalyseClockRecordPublish;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.application.service.impl.CheckWorkAttendanceService;
import com.caidaocloud.attendance.service.application.service.impl.ClockTaskWorkService;
import com.caidaocloud.attendance.service.domain.entity.SysEmpInfoDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.infrastructure.util.DateUtil;
import com.caidaocloud.attendance.service.interfaces.dto.BatchClockAnalyseDto;
import com.caidaocloud.attendance.service.interfaces.dto.WorkCalendarReqDto;
import com.caidaocloud.attendance.service.schedule.application.service.shiftGroup.IPerformanceService;
import com.caidaocloud.attendance.service.schedule.application.service.shiftGroup.IWaEmpShiftGroupService;
import com.caidaocloud.attendance.service.schedule.application.service.shiftGroup.IWaShiftGroupService;
import com.caidaocloud.attendance.service.schedule.infrastructure.repository.po.WaShiftGroup;
import com.caidaocloud.util.FastjsonUtil;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClockTaskService {
    @Resource
    private IWorkCalendarService workCalendarService;
    @Resource
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Resource
    private WaShiftDo waShiftDo;
    @Resource
    private ClockTaskWorkService clockTaskWorkService;
    @Resource
    private CheckWorkAttendanceService checkWorkAttendanceService;
    @Resource
    private IGroupService groupService;
    @Resource
    private IClockPlanService clockPlanService;
    @Resource
    private AnalyseClockRecordPublish analyseClockRecordPublish;
    @Resource
    private SysEmpInfoDo sysEmpInfoDo;
    @Resource
    private ISobService sobService;
    @Resource
    private IWaEmpShiftGroupService waEmpShiftGroupService;
    @Resource
    private IWaShiftGroupService waShiftGroupService;
    @Resource
    private IPerformanceService performanceService;

    @XxlJob("carryForwardQuotaDataJobHandler")
    public ReturnT<String> autoCarryForwardQuota() {
        XxlJobHelper.log("XxlJob autoCarryForwardQuota start");
        log.info("定时任务【按年发放的配额自动结转】------------------------开始执行,time {}", System.currentTimeMillis());
        clockTaskWorkService.autoCarryForwardQuota(null);
        log.info("定时任务【按年发放的配额自动结转】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob autoCarryForwardQuota end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("calHolidayQuotaDataJobHandler")
    public ReturnT<String> calHolidayQuota() throws Exception {
        XxlJobHelper.log("XxlJob calHolidayQuota start");
        log.info("定时任务【计算配额】------------------------开始执行,time {}", System.currentTimeMillis());
        clockTaskWorkService.autoGenEmpQuotaForIssuedAnnually(null);
        log.info("定时任务【计算配额】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob calHolidayQuota end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("calParentQuotaDataJobHandler")
    public ReturnT<String> calParentQuota() throws Exception {
        XxlJobHelper.log("XxlJob calParentQuota start");
        log.info("定时任务【计算育儿假配额】------------------------开始执行,time {}", System.currentTimeMillis());
        clockTaskWorkService.autoGenEmpQuotaForParRent(null);
        log.info("定时任务【计算育儿假配额】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob calParentQuota end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("calEmpNowQuotaDataJobHandler")
    public ReturnT<String> calEmpNowQuota() {
        XxlJobHelper.log("XxlJob calEmpNowQuota start");
        log.info("定时任务【计算当前可用配额】----------------开始执行,time {}", System.currentTimeMillis());
        clockTaskWorkService.autoGenEmpNowQuotaForIssuedAnnually();
        log.info("定时任务【计算当前可用配额】----------------执行结束,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob calEmpNowQuota end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("calFixedQuotaDataJobHandle")
    public ReturnT<String> calEmpQuotaForFixedQuota() {
        XxlJobHelper.log("XxlJob calEmpQuotaForFixedQuota start");
        log.info("定时任务【计算固定额度】----------------开始执行,time {}", System.currentTimeMillis());
        clockTaskWorkService.autoGenEmpQuotaForFixedQuota(null);
        log.info("定时任务【计算固定额度】----------------执行结束,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob calEmpQuotaForFixedQuota end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("calCompensatoryLeaveQuotaDataJobHandle")
    public ReturnT<String> calEmpQuotaForCompensatoryLeave() throws Exception {
        XxlJobHelper.log("XxlJob calEmpQuotaForCompensatoryLeave start");
        log.info("定时任务【计算调休配额】----------------开始执行,time {}", System.currentTimeMillis());
        clockTaskWorkService.autoGenEmpQuotaForCompensatoryLeave(null);
        log.info("定时任务【计算调休配额】----------------执行结束,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob calEmpQuotaForCompensatoryLeave end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("analyzeRegisterDataJobHandler")
    public ReturnT<String> analyzeRegister() {
        XxlJobHelper.log("XxlJob analyzeRegister start");
        log.info("定时任务【考勤分析】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            /**
             * 取消 wa jar 中的同步代码 registerAnalyzeService.analyzeRegister()，
             * 新抽取部分代码到  checkWorkAttendanceService.analyzeRegister() 中
             */
            checkWorkAttendanceService.analyzeRegister();
        } catch (Exception e) {
            log.error("【考勤分析】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务 【考勤分析】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob analyzeRegister end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("synchronizeEmpShiftDataJobHandler")
    public ReturnT<String> synchronizeEmpShift() {
        XxlJobHelper.log("XxlJob synchronizeEmpShift start");
        log.info("定时任务【同步员工日历】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            workCalendarService.synchronizeEmpShift();
        } catch (Exception e) {
            log.error("【同步员工日历】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【同步员工日历】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob synchronizeEmpShift end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("synchronizeEmpGroupDataJobHandler")
    public ReturnT<String> synchronizeEmpGroup() {
        XxlJobHelper.log("XxlJob synchronizeEmpGroup start");
        log.info("定时任务【员工考勤方案】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            groupService.synchronizeEmpGroup();
        } catch (Exception e) {
            log.error("【同步员工考勤方案】定时任务执行异常，{}", e.getMessage(), e);
        }
        log.info("定时任务【同步员工考勤方案】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob synchronizeEmpGroup end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("synchronizeEmpClockPlanDataJobHandler")
    public ReturnT<String> synchronizeEmpClockPlan() {
        XxlJobHelper.log("XxlJob synchronizeEmpClockPlan start");
        log.info("定时任务【同步员工打卡方案】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            clockPlanService.synchronizeClockPlan();
        } catch (Exception e) {
            log.error("【同步员工打卡方案】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【同步员工打卡方案】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob synchronizeEmpClockPlan end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("autoGenerateAttendancePeriodJobHandler")
    public ReturnT<String> autoGenerateAttendancePeriod() {
        XxlJobHelper.log("XxlJob autoGenerateAttendancePeriod start");
        log.info("定时任务【自动生成考勤周期】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            sobService.autoGenerateAttendancePeriod();
        } catch (Exception e) {
            log.error("【自动生成考勤周期】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【自动生成考勤周期】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob autoGenerateAttendancePeriod end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("autoLeaveCancelJobHandler")
    public ReturnT<String> autoLeaveCancel() {
        XxlJobHelper.log("XxlJob autoLeaveCancel start");
        log.info("定时任务【自动销假】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            clockTaskWorkService.autoLeaveCancel(null);
        } catch (Exception e) {
            log.error("【自动销假】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【自动销假】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob autoLeaveCancel end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("autoCompensatoryToCaseJobHandler")
    public ReturnT<String> autoCompensatoryToCase() {
        XxlJobHelper.log("XxlJob autoCompensatoryToCase start");
        log.info("定时任务【调休付现】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            clockTaskWorkService.autoCompensatoryToCase(null);
        } catch (Exception e) {
            log.error("【调休付现】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【调休付现】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob autoCompensatoryToCase end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("attendanceAbnormalJobRefreshHandler")
    public ReturnT<String> attendanceAbnormalJobRefresh() {
        XxlJobHelper.log("XxlJob attendanceAbnormalJobRefresh start");
        log.info("定时任务【考勤异常汇总提醒任务刷新】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            clockTaskWorkService.attendanceAbnormalJobRefresh(null);
            ReturnT<String> r = attendanceDetailJobRefresh();
            if (r.getCode() == 500) {
                return ReturnT.FAIL;
            }
        } catch (Exception e) {
            log.error("【考勤异常汇总提醒任务刷新】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【考勤异常汇总提醒任务刷新】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob attendanceAbnormalJobRefresh end");
        return ReturnT.SUCCESS;
    }

    public ReturnT<String> attendanceDetailJobRefresh() {
        XxlJobHelper.log("XxlJob attendanceDetailJobRefresh start");
        log.info("定时任务【考勤异常汇总提醒任务刷新】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            clockTaskWorkService.attendanceDetailJobRefresh(null);
        } catch (Exception e) {
            log.error("【考勤明细任务刷新】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【考勤明细任务刷新】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob attendanceDetailJobRefresh end");
        return ReturnT.SUCCESS;
    }

    @XxlJob("autoTravelToCompensatoryJobHandler")
    public ReturnT<String> autoTravelToCompensatory() {
        XxlJobHelper.log("XxlJob autoTravelToCompensatory start");
        log.info("定时任务【出差转调休】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            clockTaskWorkService.autoTravelToCompensatory(null);
        } catch (Exception e) {
            log.error("【出差转调休】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【出差转调休】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob autoTravelToCompensatory end");
        return ReturnT.SUCCESS;
    }

    //  @Scheduled(cron = "0 0 2 * * ?")
    //  @Scheduled(cron = "* 0/2 * * * ?")
//    @XxlJob("workCalendarDataJobHandler")
    public void workCalendarToMongo() {
        log.info("workCalendarToMongo start ....");
        MyPageBounds myPageBounds = new MyPageBounds(0, 50);
        PageList<SysCorpOrg> pageList = sysCorpOrgMapper.getSysCorpOrgPageList(myPageBounds);
        if (null == pageList || pageList.isEmpty()) {
            return;
        }

        doWorkCalendar(myPageBounds, pageList);
        log.info("workCalendarToMongo end ....");
    }

    private void doWorkCalendar(MyPageBounds myPageBounds, PageList<SysCorpOrg> pageList) {
        WorkCalendarReqDto workCalendarReqDto = new WorkCalendarReqDto();
        workCalendarReqDto.setStartDate(DateUtil.getNextDate(-1));
        workCalendarReqDto.setEndDate(DateUtil.getEndDate());
        for (SysCorpOrg sysCorpOrg : pageList) {
            workCalendarReqDto.setBelongOrgId(String.valueOf(sysCorpOrg.getOrgid()));
            workCalendarReqDto.setCorpId(ConvertHelper.longConvert(sysCorpOrg.getCorpid()));

            batchDeleteShift(String.valueOf(sysCorpOrg.getOrgid()), sysCorpOrg.getCorpid().toString());

            workCalendarReqDto.setPageNo(1);
            batchSaveShift(workCalendarReqDto);
        }

        if (pageList.size() < 50) {
            return;
        }

        myPageBounds = new MyPageBounds(myPageBounds.getOffset() + 1, 50);
        pageList = sysCorpOrgMapper.getSysCorpOrgPageList(myPageBounds);
        if (null == pageList || pageList.isEmpty()) {
            return;
        }

        doWorkCalendar(myPageBounds, pageList);
    }

    public void batchSaveShift(WorkCalendarReqDto workCalendarReqDto) {
        workCalendarReqDto.setPageNo(workCalendarReqDto.getPageNo());
        workCalendarReqDto.setPageSize(300);
        AttendancePageResult pageResult = workCalendarService.getWaShiftList(workCalendarReqDto);
        List<WaShiftDo> list = null;
        if (null == pageResult || (list = pageResult.getItems()) == null || list.isEmpty()) {
            return;
        }

        waShiftDo.saveList(list, String.valueOf(workCalendarReqDto.getCorpId()));

        if (list.size() < 300) {
            return;
        }

        workCalendarReqDto.setPageNo(workCalendarReqDto.getPageNo() + 1);
        batchSaveShift(workCalendarReqDto);
    }

    public void batchDeleteShift(String belongOrgId, String corpId) {
        waShiftDo.deleteShift(belongOrgId, corpId);
    }


    /**
     * 每日打卡分析（分析当日打卡数据）
     */
    @XxlJob("pcAnalyseClockDataJobHandler")
    public ReturnT<String> analyseRegisterRecord() {
        XxlJobHelper.log("PC XxlJob pcAnalyseClockDataJobHandler start, time={}", System.currentTimeMillis());
        log.info("PC 定时任务【每日打卡分析】------------------------开始执行,time {}", System.currentTimeMillis());

        Long today = com.caidao1.commons.utils.DateUtil.getOnlyDate();
        ClockAnalyseTaskParamDto paramDto = new ClockAnalyseTaskParamDto();
        paramDto.setStartDate(today).setEndDate(today).setIfMultiDay(Boolean.TRUE);
        analyseRegisterRecordInfo(paramDto);

        log.info("PC  定时任务【每日打卡分析】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("PC XxlJob pcAnalyseClockDataJobHandler end, time={}", System.currentTimeMillis());
        return ReturnT.SUCCESS;
    }

    /**
     * 前日打卡分析（分析前日打卡数据）
     */
    @XxlJob("pcAnalysePreClockDataJobHandler")
    public ReturnT<String> analysePreRegisterRecord() {
        XxlJobHelper.log("PC XxlJob pcAnalysePreClockDataJobHandler start, time={}", System.currentTimeMillis());
        log.info("PC 定时任务【前日打卡分析】------------------------开始执行,time {}", System.currentTimeMillis());

        Long today = com.caidao1.commons.utils.DateUtil.getOnlyDate();
        Long yesterday = com.caidao1.commons.utils.DateUtil.addDate(today * 1000, -1);

        ClockAnalyseTaskParamDto paramDto = new ClockAnalyseTaskParamDto();
        paramDto.setStartDate(yesterday).setEndDate(yesterday).setIfMultiDay(Boolean.TRUE);
        analyseRegisterRecordInfo(paramDto);

        log.info("PC 定时任务【前日打卡分析】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("PC XxlJob pcAnalysePreClockDataJobHandler end, time={}", System.currentTimeMillis());
        return ReturnT.SUCCESS;
    }

    /**
     * 多日打卡分析（分析一段时间内的打卡数据）
     */
    @XxlJob("analyseMultiDayClockJobHandler")
    public ReturnT<String> analyseMultiDayClockJobHandler() {
        XxlJobHelper.log("XxlJob analyseMultiDayClockJobHandler start, time={}", System.currentTimeMillis());
        log.info("定时任务【多日打卡分析】------------------------开始执行,time {}", System.currentTimeMillis());

        // 任务参数
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("XxlJob analyseMultiDayClockJobHandler XXL-JOB 任务参数：{}", jobParam);
        log.info("XxlJob analyseMultiDayClockJobHandler XXL-JOB 任务参数：{}", jobParam);

        // 解析日期范围参数，默认为7天
        int dateRange = 7;
        if (StringUtils.isNotBlank(jobParam)) {
            try {
                dateRange = Integer.parseInt(jobParam.trim());
                XxlJobHelper.log("使用传入的日期范围参数：{} 天", dateRange);
            } catch (NumberFormatException e) {
                XxlJobHelper.log("日期范围参数解析失败，使用默认值7天。参数值：{}, 错误：{}", jobParam, e.getMessage());
                log.warn("日期范围参数解析失败，使用默认值7天。参数值：{}", jobParam, e);
            }
        }

        Long today = com.caidao1.commons.utils.DateUtil.getOnlyDate();
        Long startDay = com.caidao1.commons.utils.DateUtil.addDate(today * 1000, -dateRange);

        XxlJobHelper.log("实际执行参数：日期范围={} 天，开始日期={}，结束日期={}", dateRange, startDay, today);
        log.info("实际执行参数：日期范围={} 天，开始日期={}，结束日期={}", dateRange, startDay, today);

        ClockAnalyseTaskParamDto paramDto = new ClockAnalyseTaskParamDto();
        paramDto.setStartDate(startDay).setEndDate(today).setIfMultiDay(Boolean.TRUE);
        analyseRegisterRecordInfo(paramDto);

        log.info("定时任务【多日打卡分析】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob analyseMultiDayClockJobHandler end, time={}", System.currentTimeMillis());
        return ReturnT.SUCCESS;
    }

    /**
     * 手动执行打卡分析（支持全部租户）
     *
     * @param date
     * @param belongOrgId
     * @param empIdList
     */
    public void analyseRegisterRecordByDate(Long date, String belongOrgId, List<Long> empIdList) {
        log.info("analyseRegisterRecordByDate start date={}, belongOrgId={}, time={}", date, belongOrgId, System.currentTimeMillis());

        if (belongOrgId != null && CollectionUtils.isNotEmpty(empIdList)) {
            ClockAnalyseDto dto = new ClockAnalyseDto(belongOrgId, empIdList, date, null);
            analyseClockRecordPublish.publish(JSON.toJSONString(dto));
        } else {
            analyseRegisterRecordInfo(new ClockAnalyseTaskParamDto(belongOrgId, date));
        }

        log.info("analyseRegisterRecordByDate end date={}, belongOrgId={}, time={}", date, belongOrgId, System.currentTimeMillis());
    }

    /**
     * 手动执行打卡分析（支持全部租户）
     *
     * @param taskParamDto
     */
    public void analyseRegisterRecordInfo(ClockAnalyseTaskParamDto taskParamDto) {
        log.info("analyseRegisterRecordInfo start taskParamDtoLong={}, time={}", FastjsonUtil.toJsonStr(taskParamDto),
                System.currentTimeMillis());

        MyPageBounds myPageBounds = new MyPageBounds(0, RowBounds.NO_ROW_LIMIT);
        List<SysCorpOrg> orgList = sysCorpOrgMapper.getSysCorpOrgPageList(myPageBounds);
        if (CollectionUtils.isEmpty(orgList)) {
            log.info("analyseRegisterRecordInfo fail cause: orgList Empty");
            return;
        }
        if (null != taskParamDto.getBelongOrgId()) {
            orgList = orgList.stream().filter(it -> it.getOrgid().toString().equals(taskParamDto.getBelongOrgId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(orgList)) {
            log.info("analyseRegisterRecordInfo fail cause: after filter orgList Empty");
            return;
        }

        List<String> orgNames = orgList.stream().map(SysCorpOrg::getShortname).distinct().collect(Collectors.toList());
        log.info("analyseRegisterRecord get org={} ", FastjsonUtil.toJsonStr(orgNames));

        if (taskParamDto.isIfMultiDay()) {
            BatchClockAnalyseDto batchClockAnalyseDto = new BatchClockAnalyseDto();
            batchClockAnalyseDto.setStartDate(taskParamDto.getStartDate());
            batchClockAnalyseDto.setEndDate(taskParamDto.getEndDate());
            batchClockAnalyseDto.setMultinode(Boolean.TRUE);
            for (SysCorpOrg org : orgList) {
                log.info("start analyse MultiDay orgname={}, orgid={}", org.getShortname(), org.getOrgid());

                batchClockAnalyseDto.setBelongOrgId(String.valueOf(org.getOrgid()));
                SpringUtils.getBean(IClockSignService.class).analyseByDateRange(batchClockAnalyseDto);
            }
            log.info("analyseRegisterRecordInfo MultiDay end time={}", System.currentTimeMillis());
            return;
        }

        int count = 500;
        PageBean pageBean = new PageBean();
        pageBean.setPosStart(0);
        pageBean.setCount(count);

        for (SysCorpOrg org : orgList) {
            log.info("start analyse  {}", org.getShortname());

            pageBean.setPosStart(0);
            PageList<SysEmpInfoDo> empList = sysEmpInfoDo.getEmpList(pageBean, String.valueOf(org.getOrgid()));
            if (CollectionUtils.isEmpty(empList)) {
                log.info("analyse {} emp empty", org.getShortname());
                continue;
            }
            int total = empList.getPaginator().getTotalCount();
            int totalPage = (int) Math.ceil((double) total / count);

            log.info("analyse {} emp total={}, totalPage={}", org.getShortname(), total, totalPage);

            // 推送第一页分析数据
            log.info("start push first page emp, count={}, time={}", empList.size(), System.currentTimeMillis());
            handleEmpRecord(String.valueOf(org.getOrgid()), empList, taskParamDto.getDate());

            if (totalPage > 1) {
                for (int i = 2; i <= totalPage; i++) {
                    pageBean.setPosStart((i - 1) * count);
                    empList = sysEmpInfoDo.getEmpList(pageBean, String.valueOf(org.getOrgid()));

                    log.info("start push {} page emp, count={}, time={}", i, empList.size(), System.currentTimeMillis());
                    handleEmpRecord(String.valueOf(org.getOrgid()), empList, taskParamDto.getDate());
                }
            }
        }

        log.info("analyseRegisterRecordInfo end time={}", System.currentTimeMillis());
    }

    private void handleEmpRecord(String belongOrgId, PageList<SysEmpInfoDo> pageList, Long date) {
        List<Long> empIdList = pageList.stream().map(SysEmpInfoDo::getEmpid).collect(Collectors.toList());
        ClockAnalyseDto dto = new ClockAnalyseDto(belongOrgId, empIdList, date, null);
        analyseClockRecordPublish.publish(JSON.toJSONString(dto));
    }

    @XxlJob("autoShiftGroupEmpDataJobHandler")
    public ReturnT<String> autoShiftGroupEmpDataJobHandler() {
        XxlJobHelper.log("XxlJob autoShiftGroupEmpData start");
        log.info("定时任务【同步班组人员】------------------------开始执行,time {}", System.currentTimeMillis());
        try {
            List<WaShiftGroup> allTenantShiftGroupList = waShiftGroupService.selectAll(null);
            if (allTenantShiftGroupList.size() > 0) {
                allTenantShiftGroupList.stream().collect(Collectors.groupingBy(WaShiftGroup::getTenantId)).forEach((tenantId, waShiftGroupList) -> {
                    waEmpShiftGroupService.flushEmpShiftByListShiftGroup(waShiftGroupList, tenantId, 0L);
                    log.info("clockInitPerformanceEmpStart....");
                    performanceService.clockInitPeriodEmp(tenantId, "0");
                    log.info("clockInitPerformanceEmpEnd....");
                });
            }
        } catch (Exception e) {
            log.error("【同步班组人员】定时任务执行异常，{}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
        log.info("定时任务【同步班组人员】------------------------结束执行,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob autoShiftGroupEmpData end");
        return ReturnT.SUCCESS;
    }
}
