package com.caidao1.integrate.reader;

import com.caidao1.commons.mybatis.ReflectUtil;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.SpringUtils;
import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.caidao1.ioc.mybatis.mapper.IocImportMapper;
import com.caidao1.xss.test.cache.RedisService;
import com.caidao1.integrate.util.IntegrateUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.weibo.api.motan.core.extension.SpiMeta;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.endpoint.dynamic.DynamicClientFactory;
import org.apache.cxf.transport.http.HTTPConduit;
import org.apache.cxf.transports.http.configuration.HTTPClientPolicy;
import redis.clients.jedis.Jedis;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@SpiMeta(name = "ws")
public class WsSourceReader implements SourceReader {

    private static final Log logger = LogFactory.getLog(WsSourceReader.class);

    private RedisService redisService;

    private IocImportMapper iocImportMapper;

    public List<Map<String, Object>> getSourceResult(Long corpId, String belongId, SysDataInput dataInput, Map<String, Object> mapJson, Map exparams, List<String> logList,Map returnMap) throws Exception {
        if (redisService == null) redisService = SpringUtils.getBean(RedisService.class);
        if (iocImportMapper == null) iocImportMapper = SpringUtils.getBean(IocImportMapper.class);

        List<Map<String, Object>> sourceResult = new ArrayList<Map<String, Object>>();
        Map<String, String> eiExtCacheMap = null;
        Map<String, String> epExtCacheMap = null;

        DynamicClientFactory factory = DynamicClientFactory.newInstance();
        Client proxy = factory.createClient((String) mapJson.get("ws.wsdl"));
        HTTPConduit conduit = (HTTPConduit) proxy.getConduit();
        HTTPClientPolicy policy = new HTTPClientPolicy();
        policy.setConnectionTimeout(6000000);// 连接超时(毫秒)
        policy.setAllowChunking(false);// 取消块编码
        policy.setReceiveTimeout(6000000);// 响应超时(毫秒)
        conduit.setClient(policy);
        List<Map<String, Object>> wsParams = (List<Map<String, Object>>) mapJson.get("ws.params");
        Object result;
        if (CollectionUtils.isNotEmpty(wsParams)) {
            Object[] methodParams = new Object[wsParams.size()];
            int j = 0;
            for (Map<String, Object> param : wsParams) {
                if (param.containsKey("format")) {
                    methodParams[j++] = IntegrateUtil.formatExp((String) param.get("value"), (String) param.get("format"));
                } else {
                    methodParams[j++] = param.get("value");
                }
            }
            result = proxy.invoke((String) mapJson.get("ws.method"), methodParams)[0];
        } else {
            result = proxy.invoke((String) mapJson.get("ws.method"))[0];
        }

        if (result != null) {
            List itemList;
            String resultType = (String) mapJson.get("ws.resultType");
            if (resultType != null && resultType.equals("json")) {
                Map resMap = new ObjectMapper().readValue((String) result, Map.class);
                itemList = (List) resMap.get(mapJson.get("ws.item"));
            } else if (resultType.equals("json_arrays")) {
                itemList = new ObjectMapper().readValue((String) result, List.class);
            } else {
                itemList = ReflectUtil.getFieldValue(result, (String) mapJson.get("ws.item"));
            }

            if (CollectionUtils.isNotEmpty(itemList)) {
                Jedis jedis = redisService.getResource();

                try {
                    Map<String, Integer> empCacheMap = new HashMap<String, Integer>();
                    for (Object item : itemList) {
                        Map<String, Object> row = new HashMap<String, Object>();
                        try {
                            for (String field : dataInput.getSourceExp().split(",")) {
                                if (field.contains(":TIMESTAMP")) {
                                    Pattern pattern = Pattern.compile(":TIMESTAMP\\((.*)#(.*)\\)#(.*)", Pattern.CASE_INSENSITIVE);
                                    Matcher matcher = pattern.matcher(field);
                                    if (matcher.find()) {
                                        String code = matcher.group(1);
                                        String format = matcher.group(2);
                                        row.put(matcher.group(3), DateUtil.convertStringToDateTime((String) ReflectUtil.getItemValue(item, StringUtils.trim(code)), format, true));
                                    }
                                } else if (field.contains(":EMPID")) {
                                    Pattern pattern = Pattern.compile(":EMPID\\((.*)\\)#(.*)", Pattern.CASE_INSENSITIVE);
                                    Matcher matcher = pattern.matcher(field);
                                    if (matcher.find()) {
                                        String code = matcher.group(1);
                                        String key = ReflectUtil.getItemValue(item, StringUtils.trim(code));
                                        if (empCacheMap.size() == 0) {
                                            List<Map<String, Object>> empCacheList = iocImportMapper.queryKeyListBySql("select workno,empid from sys_emp_info where belong_org_id=" + belongId);
                                            if (CollectionUtils.isNotEmpty(empCacheList)) {
                                                for (Map cache : empCacheList) {
                                                    empCacheMap.put((String) cache.get("workno"), (Integer) cache.get("empid"));
                                                }
                                            }
                                        }
                                        if (empCacheMap.containsKey(key)) {
                                            row.put(matcher.group(2), empCacheMap.get(key));
                                        } else {
                                            throw new RuntimeException("公司：" + belongId + "工号：" + key + "不存在！");
                                        }
                                    }
                                } else if (field.contains(":EIEXT")) {
                                    Pattern pattern = Pattern.compile(":EIEXT\\((.*)#(.*)\\)#(.*)", Pattern.CASE_INSENSITIVE);
                                    Matcher matcher = pattern.matcher(field);
                                    if (matcher.find()) {
                                        String code = matcher.group(1);
                                        String extName = matcher.group(2);
                                        String key = ReflectUtil.getItemValue(item, StringUtils.trim(code));
                                        if (eiExtCacheMap == null) {
                                            eiExtCacheMap = new HashMap<>();
                                            List<Map<String, Object>> empCacheList = iocImportMapper.queryKeyListBySql("SELECT ext_custom_col ->> '" + extName + "' AS \"" + extName + "\", workno FROM sys_emp_info WHERE ext_custom_col ->> '" + extName + "' IS NOT NULL and belong_org_id=" + belongId);
                                            if (CollectionUtils.isNotEmpty(empCacheList)) {
                                                for (Map cache : empCacheList) {
                                                    eiExtCacheMap.put((String) cache.get(extName), (String) cache.get("workno"));
                                                }
                                            }
                                        }
                                        if (eiExtCacheMap.containsKey(key)) {
                                            row.put(matcher.group(3), eiExtCacheMap.get(key));
                                        } else {
                                            throw new RuntimeException("公司：" + belongId + "-" + extName + "：" + key + "不存在！");
                                        }
                                    }
                                } else if (field.contains(":EPEXT")) {
                                    Pattern pattern = Pattern.compile(":EPEXT\\((.*)#(.*)\\)#(.*)", Pattern.CASE_INSENSITIVE);
                                    Matcher matcher = pattern.matcher(field);
                                    if (matcher.find()) {
                                        String code = matcher.group(1);
                                        String extName = matcher.group(2);
                                        String key = ReflectUtil.getItemValue(item, StringUtils.trim(code));
                                        if (epExtCacheMap == null) {
                                            epExtCacheMap = new HashMap<>();
                                            List<Map<String, Object>> empCacheList = iocImportMapper.queryKeyListBySql("SELECT ep.ext_custom_col ->> '" + extName + "' AS \"" + extName + "\", ei.workno FROM sys_emp_privacy ep JOIN sys_emp_info ei ON ei.empid = ep.empid WHERE ep.ext_custom_col ->> '" + extName + "' IS NOT NULL and belong_org_id=" + belongId);
                                            if (CollectionUtils.isNotEmpty(empCacheList)) {
                                                for (Map cache : empCacheList) {
                                                    epExtCacheMap.put((String) cache.get(extName), (String) cache.get("workno"));
                                                }
                                            }
                                        }
                                        if (epExtCacheMap.containsKey(key)) {
                                            row.put(matcher.group(3), epExtCacheMap.get(key));
                                        } else {
                                            throw new RuntimeException("公司：" + belongId + "-" + extName + "：" + key + "不存在！");
                                        }
                                    }
                                } else if (field.contains(":MAP")) {
                                    Pattern pattern = Pattern.compile(":MAP\\((.*)#(.*)\\)#(.*)", Pattern.CASE_INSENSITIVE);
                                    Matcher matcher = pattern.matcher(field);
                                    if (matcher.find()) {
                                        String code = matcher.group(1);
                                        String[] mapList = matcher.group(2).split("_");
                                        Object key = ReflectUtil.getItemValue(item, StringUtils.trim(code));
                                        for (String map : mapList) {
                                            String[] range = map.split(":");
                                            if (range[0].equals(String.valueOf(key))) {
                                                row.put(matcher.group(3), range[1]);
                                                break;
                                            }
                                        }
                                    }
                                } else {
                                    String[] meta = field.split("#");
                                    if (meta.length > 1) {
                                        row.put(meta[1], ReflectUtil.getItemValue(item, StringUtils.trim(meta[0])));
                                    } else {
                                        row.put(field, ReflectUtil.getItemValue(item, StringUtils.trim(field)));
                                    }
                                }
                            }
                            sourceResult.add(row);
                        } catch (Exception e) {
                            logger.error(e.getMessage());
//                           System.out.println(e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    jedis.close();
                }
            }
        }
        return sourceResult;
    }
}