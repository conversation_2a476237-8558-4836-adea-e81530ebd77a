package com.caidaocloud.attendance.core.annoation.feign;

import com.caidaocloud.attendance.core.annoation.dto.dict.DictKeyValue;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.caidaocloud.constant.CommonConstant.*;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "${feign.rename.caidaocloud-business-config-center:caidaocloud-business-config-center}",
        fallback = BccServiceFeignFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "attendanceBccServiceFeignClient"
)
public interface BccServiceFeignClient {
    @GetMapping(value = "/api/bcc/dict/common/v1/dict/getEnableDictList")
    Result<List<DictKeyValue>> getEnableDictList(@RequestParam("typeCode") String typeCode,
                                                 @RequestParam(name = "belongModule", required = false) String belongModule);

    @PostMapping(value = "/api/bcc/dict/common/v1/dict/refreshSystemDict")
    Result<Boolean> refreshSystemDict();

}
