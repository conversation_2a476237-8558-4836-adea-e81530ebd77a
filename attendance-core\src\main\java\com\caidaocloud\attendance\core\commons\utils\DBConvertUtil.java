package com.caidaocloud.attendance.core.commons.utils;

import org.apache.commons.lang3.StringUtils;
import org.postgresql.jdbc.PgArray;

import java.sql.SQLException;

public class DBConvertUtil {

    public static String convertDBArray(Object object){
        if(object != null){
            if(object instanceof Integer[]){
                return StringUtils.join((Integer[])object,",");
            }else if(object instanceof String[]){
                return StringUtils.join((String[])object,",");
            }else if(object instanceof Double[]){
                return StringUtils.join((Double[])object,",");
            }else if(object instanceof Long[]){
                return StringUtils.join((Long[])object,",");
            }else if(object instanceof PgArray){
                PgArray pg = (PgArray)object;
                return pg.toString().replace("{", "").replace("}", "");
            }
        }
        return null;
    }

    public static void main (String[] args){

        try {
            PgArray pgArray = new PgArray(null,0,"empid");

        } catch (SQLException e) {
            e.printStackTrace();
        }

    }
}
