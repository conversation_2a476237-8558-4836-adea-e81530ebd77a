package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.employee.mybatis.model.EmpFamily;
import com.caidaocloud.attendance.service.domain.repository.IEmpFamilyInfoRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Slf4j
@Service
@Data
public class EmpFamilyInfoDo {

    @Autowired
    private IEmpFamilyInfoRepository empFamilyInfoRepository;

    public List<EmpFamily> getEmpFamilyList(Long empId, String belongOrgId, String groupExp) {
        return empFamilyInfoRepository.getFamilyInfo(empId, belongOrgId, groupExp);
    }
}
