package com.caidaocloud.attendance.service.infrastructure.feign.wfm;

import com.caidaocloud.dto.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


@Data
@ApiModel(value = "排班及工序状态vo")
@Accessors(chain = true)
public class SchedulingAndProcessStatusVo {
    PageResult<SchedulingAndProcessVo> pageResult;

    @Data
    @ApiModel(value = "排班及工序状态")
    @Accessors(chain = true)
    public static class SchedulingAndProcessVo {
        @ApiModelProperty("工序")
        private String processName;
        @ApiModelProperty("叶片编号")
        private String bladeNumber;
        @ApiModelProperty("排班日期")
        private Long schedulingTime;
        @ApiModelProperty("班次")
        private String shiftTime;
        @ApiModelProperty("排班状态")
        private int schedulingStatus;
        @ApiModelProperty("已扫码人数/排班人数")
        private String  attendanceCountAndScheduling;
        @ApiModelProperty("实际班次时间")
        private String actualShiftTime;
        @ApiModelProperty("实际班次/预排班")
        private BigDecimal actualDavideExpectedTime;
        private Long actualShiftStartTime;
        private Long actualShiftEndTime;

        @ApiModelProperty("工序开始时间")
        private Long processStartTime;
        @ApiModelProperty("工序结束时间")
        private Long processEndTime;
        @ApiModelProperty("实际班次状态")
        private int actualShiftTimeStatus;
        @ApiModelProperty("工序时间状态")
        private int processTimeStatus;
        @ApiModelProperty("状态")
        private int status;

        private int attendanceCoutn;//已扫码人数
        private int schedulingPersonnelCount;//排班人数
    }

}