package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

public enum MinOvertimeUnitType {
    MINUTE(1, "分钟", AttendanceCodes.MINUTE),
    HOUR(2, "小时", AttendanceCodes.HOUR);

    MinOvertimeUnitType(int index, String name, Integer code) {
        this.index = index;
        this.name = name;
        this.code = code;
    }

    private int index;
    private String name;
    private Integer code;

    public static MinOvertimeUnitType getByIndex(int index) {
        for (MinOvertimeUnitType c : MinOvertimeUnitType.values()) {
            if (c.getIndex() == index) {
                return c;
            }
        }
        return null;
    }

    public static String getName(int index) {
        for (MinOvertimeUnitType c : MinOvertimeUnitType.values()) {
            if (c.getIndex() == index) {
                String msg = "";
                try {
                    msg = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(msg)) {
                    return msg;
                }
                return c.name;
            }
        }
        return "";
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
