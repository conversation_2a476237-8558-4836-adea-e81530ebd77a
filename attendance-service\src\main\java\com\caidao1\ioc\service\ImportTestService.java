package com.caidao1.ioc.service;

import com.caidao1.commons.BaseConst;
import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.payroll.mybatis.mapper.PayEmpGroupMapper;
import com.caidao1.payroll.mybatis.model.PayEmpGroup;
import com.caidao1.payroll.mybatis.model.PayEmpGroupExample;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class ImportTestService implements ScriptBindable {

    public static ConcurrentHashMap<String, Long> empStrucList = new ConcurrentHashMap<String, Long>();
    @Autowired
    private PayEmpGroupMapper payEmpGroupMapper;


    /**
     * 校验是否有员工分组
     *
     * @param corpId
     * @param belongId
     * @param workno
     * @return
     */
    public boolean checkEmpPayStruc(Long corpId, String belongId, String workno) {
        return getPayStrucMainByEmp(corpId, belongId, workno) != null;
    }

    /**
     * @param workno
     * @return
     */
    public Long getPayStrucMainByEmp(Long corpId, String belongId, String workno) {
        String key = corpId + "_" + belongId + "_" + workno;
        if (empStrucList.size() == 0) {
            System.out.println("getPayStrucMainByEmp--------->" + key);
            PayEmpGroupExample example = new PayEmpGroupExample();
            example.createCriteria().andCorpidEqualTo(corpId).andBelongOrgIdEqualTo(belongId).andTypeEqualTo("payroll");
            List<PayEmpGroup> payEmpGroupList = payEmpGroupMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(payEmpGroupList)) {
                Calendar start = Calendar.getInstance();
                start.set(Calendar.DATE, 1);
                int endDay = start.getActualMaximum(Calendar.DATE);
                Calendar endCal = Calendar.getInstance();
                endCal.set(Calendar.DATE, endDay);
                for (PayEmpGroup payEmpGroup : payEmpGroupList) {
                    String groupExp = payEmpGroup.getGroupExp();
                    Map params = new HashMap();
                    params.put("corpId", corpId);
                    params.put("belongId", belongId);
                    params.put("groupExp", groupExp.replaceAll("1#(" + BaseConst.FIX_EXT_COLUMN + "\\d+)#.*?=", "ei.ext_custom_col ->> '$1'=").replaceAll("9#(" + BaseConst.FIX_EXT_COLUMN + "\\d+)#.*?=", "ep.ext_custom_col->>'$1'=").replaceAll("orgid='(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))"));
                }
            }
        }
        return empStrucList.get(key);
    }
}
