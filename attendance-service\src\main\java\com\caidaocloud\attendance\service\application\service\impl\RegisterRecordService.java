package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.SpringUtils;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaRegisterRecordMapper;
import com.caidao1.wa.mybatis.model.WaRegisterRecord;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.WfConstant;
import com.caidaocloud.attendance.service.application.cron.ClockTaskService;
import com.caidaocloud.attendance.service.application.dto.clock.ClockListShiftDefDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.event.publish.AttendanceWorkflowMsgPublish;
import com.caidaocloud.attendance.service.application.event.publish.MessageParams;
import com.caidaocloud.attendance.service.application.service.IRegisterRecordService;
import com.caidaocloud.attendance.service.application.service.IShiftService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaRegisterRecordBdkMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordBdkPo;
import com.caidaocloud.attendance.service.interfaces.dto.ClockInAdjustDto;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordDto;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;
import com.caidaocloud.attendance.service.interfaces.dto.WaAnalyzeDto;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 考勤打卡记录服务
 *
 * <AUTHOR>
 * @Date 2021/3/19
 */
@Slf4j
@Service
public class RegisterRecordService implements IRegisterRecordService {
    @Autowired
    private WaRegisterRecordDo waRegisterRecordDo;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaAnalyzeDo waAnalyzeDo;
    @Autowired
    private AttendanceWorkflowMsgPublish attendanceWorkflowMsgPublish;
    @Autowired
    private ClockTaskService clockTaskService;
    @Autowired
    private DataBackupDo dataBackupDo;
    @Autowired
    private WaSobService waSobService;
    @Autowired
    private WaRegisterRecordBdkMapper registerRecordBdkMapper;
    @Autowired
    private WaRegisterRecordBdkDo registerRecordBdkDo;
    @Autowired
    private WaRegisterRecordMapper registerRecordMapper;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private ClockRecord clockRecord;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private IShiftService shiftService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Override
    public AttendancePageResult<RegisterRecordDto> getRegisterRecordPageList(RegisterRecordRequestDto requestDto) {
        UserInfo userInfo = this.getUserInfo();
        return SpringUtils.getBean(RegisterRecordService.class).getRegisterRecordPageList(requestDto, userInfo);
    }

    @Override
    @Transactional(readOnly = true)
    @CDText(exp = {"empStyle:empStyleName" + TextAspect.DICT_E, "empStatus:empStatusName" + TextAspect.STATUS_ENUM}, classType = RegisterRecordDto.class)
    public AttendancePageResult<RegisterRecordDto> getRegisterRecordPageList(RegisterRecordRequestDto requestDto, UserInfo user) {
        AttendancePageResult<RegisterRecordDto> result = new AttendancePageResult<>();
        UserInfo userInfo = user != null ? user : this.getUserInfo();
        requestDto.setBelongOrgId(userInfo.getTenantId());
        requestDto.setCorpId(ConvertHelper.longConvert(userInfo.getTenantId()));
        requestDto.setTypes(requestDto.getTypes());

        AttendancePageResult<WaRegisterRecordDo> pageResult = waRegisterRecordDo.getRegisterPageList(requestDto);
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            List<WaRegisterRecordDo> items = pageResult.getItems();
            List<RegisterRecordDto> dtoList = ObjectConverter.convertList(items, RegisterRecordDto.class);
            Map<Integer, WaShiftDef> corpShiftDefMap = waCommonService.getCorpAllShiftDef(requestDto.getBelongOrgId());
            Long nowDate = DateUtil.getOnlyDate();

            dtoList.forEach(row -> {
                row.setFuncType(WfConstant.WF_FUNC_TYPE_41);
                row.setBusinessKey(String.format("%s_%s", row.getRecordId(), BusinessCodeEnum.REGISTER.getCode()));
                if (row.getRegisterType() != null) {
                    if (row.getRegisterType() == 1 || row.getRegisterType() == 2) {
                        row.setRegisterTypeName(ClockTypeEnum.getName(row.getRegisterType()));
                    } else if (row.getRegisterType() == 3) {
                        row.setRegisterTypeName("");
                    }
                }
                if (row.getType() != null) {
                    row.setTypeName(BaseConst.REGISTER_TYPE.get(row.getType()));
                    if (row.getType().equals(ClockWayEnum.FILLCLOCK.getIndex())) {
                        row.setBdkReason(row.getReason());
                        if (row.getApprovalStatus() != null) {
                            row.setApprovalStatusName(ApprovalStatusEnum.getName(row.getApprovalStatus()));
                        }
                    }
                }
                if (row.getResultType() != null) {
                    if (row.getResultType() == 1 || row.getResultType() == 2) {
                        row.setResultTypeName(ClockResultEnum.getName(row.getResultType()));
                    } else if (row.getResultType() == 0) {
                        row.setResultTypeName("");
                    } else {
                        row.setResultTypeName("OTHER");
                    }
                }
                if (StringUtils.isNotBlank(row.getNormalDate())) {
                    row.setNormalDate(row.getNormalDate().replace("-", "~"));
                }
                if (row.getSourceFromType() != null) {
                    row.setSourceFromTypeName(SourceFromEnum.getName(row.getSourceFromType()));
                }
                // 班次
                if (null != row.getShiftDefId() && null != corpShiftDefMap.get(row.getShiftDefId())) {
                    WaShiftDef shiftDef = corpShiftDefMap.get(row.getShiftDefId());
                    List<MultiShiftSimpleVo> shiftSimpleVoList = shiftService.convertDoToSimpleVo(Lists.newArrayList(ObjectConverter.convert(shiftDef, WaShiftDo.class)));
                    List<ClockListShiftDefDto> shiftDefDtoList = ClockListShiftDefDto.getList(shiftSimpleVoList, nowDate,
                            row.getBelongDate(), row.getRegDateTime(), requestDto.isIfExport());
                    row.setShiftDefList(shiftDefDtoList);
                }
            });
            result.setItems(dtoList);
            result.setPageNo(pageResult.getPageNo());
            result.setPageSize(pageResult.getPageSize());
            result.setTotal(pageResult.getTotal());
        }
        return result;
    }

    @Override
    public WaAnalyzeDto getAnalyzeDetailById(Integer analyzeId) {
        WaAnalyzeDo analyzeDetailById = waAnalyzeDo.getAnalyzeDetailById(analyzeId);
        if (null == analyzeDetailById) {
            return null;
        }
        WaAnalyzeDto waAnalyzeDto = ObjectConverter.convert(analyzeDetailById, WaAnalyzeDto.class);
        if (null != waAnalyzeDto) {
            if (waAnalyzeDto.getClockType() != null && waAnalyzeDto.getClockType() == 1) {//一次卡
                if (waAnalyzeDto.getClockRule() != null) {
                    JSONObject map = JSONObject.parseObject(waAnalyzeDto.getClockRule());
                    Object clockRule = map.get("clockRule");
                    if (clockRule != null && "1".equals(clockRule.toString())) {
                        waAnalyzeDto.setStartTime(waAnalyzeDto.getOnDutyStartTime());
                        waAnalyzeDto.setEndTime(waAnalyzeDto.getOffDutyEndTime());
                    }

                }
            }
            //查询打卡记录
            List<WaRegisterRecordDo> recordList = waRegisterRecordDo.getAllRecordListByBelongDate(waAnalyzeDto.getBelongOrgId(), waAnalyzeDto.getEmpid(), waAnalyzeDto.getBelongDate());
            if (CollectionUtils.isNotEmpty(recordList)) {
                waAnalyzeDto.setRegSignInTime(null);
                waAnalyzeDto.setRegSignOffTime(null);
                //补卡记录
                List<WaRegisterRecordDo> bdkRegList = recordList.stream().filter(r -> r.getRegisterType() != null && r.getType() == 6 && (r.getApprovalStatus() == null || r.getApprovalStatus() == 2)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(bdkRegList)) {
                    Map<Integer, List<WaRegisterRecordDo>> bdkRegTypeMap = bdkRegList.stream().collect(Collectors.groupingBy(WaRegisterRecordDo::getRegisterType));
                    if (ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(waAnalyzeDto.getClockType())) {
                        // 一次卡
                        List<WaRegisterRecordDo> clockOneBdkList = bdkRegTypeMap.get(4);
                        if (CollectionUtils.isNotEmpty(clockOneBdkList)) {
                            Optional<WaRegisterRecordDo> optional = clockOneBdkList.stream().min(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));
                            optional.ifPresent(waRegisterRecordDo -> waAnalyzeDto.setMakeUpSignTime(waRegisterRecordDo.getRegDateTime()));
                        }
                    } else {
                        // 两次卡
                        List<WaRegisterRecordDo> signInBdkList = bdkRegTypeMap.get(1);
                        if (CollectionUtils.isNotEmpty(signInBdkList)) {
                            Optional<WaRegisterRecordDo> optional = signInBdkList.stream().min(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));
                            optional.ifPresent(waRegisterRecordDo -> waAnalyzeDto.setMakeUpSignTime(waRegisterRecordDo.getRegDateTime()));
                        }
                        List<WaRegisterRecordDo> signOffBdkList = bdkRegTypeMap.get(2);
                        if (CollectionUtils.isNotEmpty(signOffBdkList)) {
                            Optional<WaRegisterRecordDo> optional = signOffBdkList.stream().max(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));
                            optional.ifPresent(waRegisterRecordDo -> waAnalyzeDto.setMakeOffSignTime(waRegisterRecordDo.getRegDateTime()));
                        }
                    }
                }
                //正常打卡记录
                List<WaRegisterRecordDo> normalRegList = recordList.stream().filter(r -> r.getType() != 6).sorted(Comparator.comparing(WaRegisterRecordDo::getRegDateTime)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(normalRegList)) {
                    if (ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(waAnalyzeDto.getClockType())) {
                        Optional<WaRegisterRecordDo> optional = normalRegList.stream().filter(r -> ClockResultEnum.NORMAL.getIndex().equals(r.getResultType())).min(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));
                        //optional.ifPresent(r -> waAnalyzeDto.setRegSignInTime(r.getRegDateTime()));
                        if (optional.isPresent()) {
                            waAnalyzeDto.setRegSignInTime(optional.get().getRegDateTime());
                        } else {
                            normalRegList.stream().findFirst().ifPresent(r -> waAnalyzeDto.setRegSignInTime(r.getRegDateTime()));
                        }
                    } else {
                        //waAnalyzeDto.setRegSignInTime(normalRegList.get(0).getRegDateTime());
                        //if (normalRegList.size() > 1) {
                        //    waAnalyzeDto.setRegSignOffTime(normalRegList.get(normalRegList.size() - 1).getRegDateTime());
                        //}
                        Map<Integer, List<WaRegisterRecordDo>> regTypeMap = normalRegList.stream().filter(r -> r.getType() != 3 && r.getRegisterType() != null).collect(Collectors.groupingBy(WaRegisterRecordDo::getRegisterType));
                        List<WaRegisterRecordDo> signInList = regTypeMap.get(1);
                        if (CollectionUtils.isNotEmpty(signInList)) {
                            Optional<WaRegisterRecordDo> optional = signInList.stream().min(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));
                            optional.ifPresent(waRegisterRecordDo -> waAnalyzeDto.setRegSignInTime(waRegisterRecordDo.getRegDateTime()));
                        }
                        List<WaRegisterRecordDo> signOffList = regTypeMap.get(2);
                        if (CollectionUtils.isNotEmpty(signOffList)) {
                            Optional<WaRegisterRecordDo> optional = signOffList.stream().max(Comparator.comparing(WaRegisterRecordDo::getRegDateTime));
                            optional.ifPresent(waRegisterRecordDo -> waAnalyzeDto.setRegSignOffTime(waRegisterRecordDo.getRegDateTime()));
                        }
                    }
                }
            }
            //打卡结果取考勤分析结果
            /**
             * 一次卡只有旷工，没有迟到早退，以是否旷工为准
             * 二次卡以迟到早退旷工综合分析
             */
            if (ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(waAnalyzeDto.getClockType())) {
                if (waAnalyzeDto.getKgWorkTime() > 0) {
                    waAnalyzeDto.setRegSignInResult(ClockResultEnum.ABNORMAL.getIndex());
                } else {
                    waAnalyzeDto.setRegSignInResult(ClockResultEnum.NORMAL.getIndex());
                }
            } else {
                //旷工签到签退都为异常
                if (waAnalyzeDto.getKgWorkTime() > 0) {
                    waAnalyzeDto.setRegSignInResult(ClockResultEnum.ABNORMAL.getIndex());
                    waAnalyzeDto.setRegSignOffResult(ClockResultEnum.ABNORMAL.getIndex());
                } else {
                    //迟到是签到异常
                    if (waAnalyzeDto.getLateTime() > 0) {
                        waAnalyzeDto.setRegSignInResult(ClockResultEnum.ABNORMAL.getIndex());
                    } else {
                        waAnalyzeDto.setRegSignInResult(ClockResultEnum.NORMAL.getIndex());
                    }
                    //早退是签退异常
                    if (waAnalyzeDto.getEarlyTime() > 0) {
                        waAnalyzeDto.setRegSignOffResult(ClockResultEnum.ABNORMAL.getIndex());
                    } else {
                        waAnalyzeDto.setRegSignOffResult(ClockResultEnum.NORMAL.getIndex());
                    }
                }

            }
        }
        return waAnalyzeDto;
    }

    @Override
    public AttendancePageResult<RegisterRecordDto> getRegisterRecordListByEmpId(RegisterRecordRequestDto requestDto, UserInfo userInfo) {
        requestDto.setBelongOrgId(userInfo.getTenantId());
        AttendancePageResult<RegisterRecordDto> dtoPageResult = new AttendancePageResult<>();
        requestDto.setQueryApprovalBdk(true);
        AttendancePageResult<WaRegisterRecordDo> pageResult = waRegisterRecordDo.getRegisterRecordPageListByEmpId(requestDto, userInfo.getStaffId());
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaRegisterRecordDo> doList = pageResult.getItems();
            List<RegisterRecordDto> dtoList = ObjectConverter.convertList(doList, RegisterRecordDto.class);
            dtoList.forEach(row -> {
                if (row.getType() != null) {
                    row.setTypeName(BaseConst.REGISTER_TYPE.get(row.getType()));
                    if (row.getType().equals(ClockWayEnum.FILLCLOCK.getIndex())) {
                        row.setBdkReason(row.getReason());
                        if (row.getApprovalStatus() != null) {
                            //row.setApprovalStatusName(BaseConst.CHANGE_APPROVAL_STATUS.get(row.getApprovalStatus()));
                            row.setApprovalStatusName(ApprovalStatusEnum.getName(row.getApprovalStatus()));
                        }
                    }
                }
            });
            //dtoList = dtoList.stream().filter(dto -> !(dto.getType().equals(ClockWayEnum.FILLCLOCK.getIndex()) && (dto.getApprovalStatus() == null || !ApprovalStatusEnum.PASSED.getIndex().equals(dto.getApprovalStatus())))).collect(Collectors.toList());
            dtoPageResult.setItems(dtoList);
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setTotal(pageResult.getTotal());
        }
        return dtoPageResult;
    }

    public List<RegisterRecordDto> getBdkListByDate(Long date, Long empid, String belongOrgId) {
        List<WaRegisterRecordDo> recordList = waRegisterRecordDo.getEmpRegisterRecordList(empid, date, date,
                new ArrayList<>(Arrays.asList(ClockWayEnum.FILLCLOCK.getIndex())), null);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(recordList)) {
            List<RegisterRecordDto> recordDtoList = ObjectConverter.convertList(recordList, RegisterRecordDto.class);
            //查询流程信息
            recordDtoList.forEach(record -> {
                record.setBusinessKey(String.format("%s_%s", record.getRecordId(), BusinessCodeEnum.REGISTER.getCode()));
                if (record.getApprovalStatus() != null) {
                    record.setApprovalStatusName(ApprovalStatusEnum.getName(record.getApprovalStatus()));
                }
                record.setApplyTime(record.getCrttime());
                record.setStartDate(DateUtil.getTimeStrByTimesamp(record.getRegDateTime()));
                record.setStatus(record.getApprovalStatus());
                record.setStatusName(record.getApprovalStatusName());
                record.setApplyName(CalendarStatusEnum.getName(CalendarStatusEnum.PATCH.getIndex()));
            });

            return recordDtoList;
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public Result<Boolean> revokeEmpReg(Long id, String revokeReason) {
        UserInfo userInfo = this.getUserInfo();
        if (StringUtils.isBlank(revokeReason)) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, Boolean.FALSE);
        }

        WaRegisterRecordBdkPo registerRecord = registerRecordBdkMapper.selectByPrimaryKey(id);
        if (registerRecord == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.RECORD_NOT_EXIST, Boolean.FALSE);
        }
        if (null != registerRecord.getType() && !ClockWayEnum.FILLCLOCK.getIndex().equals(registerRecord.getType())) {
            return ResponseWrap.wrapResult(AttendanceCodes.CAN_NOT_REVOKE, Boolean.FALSE);
        }
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(registerRecord.getApprovalStatus())) {
            return ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE);
        }
        if (null != registerRecord.getApprovalStatus()
                && !ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(registerRecord.getApprovalStatus())
                && !ApprovalStatusEnum.PASSED.getIndex().equals(registerRecord.getApprovalStatus())) {
            return ResponseWrap.wrapResult(AttendanceCodes.NOT_ALLOW_REVOKE, Boolean.FALSE);
        }

        // 撤销流程
        WfRevokeDto revokeDto = new WfRevokeDto();
        String businessKey = id + "_" + BusinessCodeEnum.REGISTER.getCode();
        revokeDto.setBusinessKey(businessKey);
        Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
        if (null == result || !result.isSuccess()) {
            // todo update-for-e 失败处理
        }

        // 更新补打卡申请单据
        WaRegisterRecordBdkPo bdkDoUpdate = new WaRegisterRecordBdkPo();
        bdkDoUpdate.setRecordId(registerRecord.getRecordId());
        bdkDoUpdate.setApprovalStatus(ApprovalStatusEnum.REVOKED.getIndex());
        bdkDoUpdate.setRevokeReason(revokeReason);
        // 撤销后，还原签到时间
        if (registerRecord.getHisRegTime() != null) {
            bdkDoUpdate.setRegDateTime(registerRecord.getHisRegTime());
            bdkDoUpdate.setHisRegTime(0L);
        }
        registerRecordBdkMapper.updateByPrimaryKeySelective(bdkDoUpdate);

        // 修改打卡记录
        List<WaRegisterRecordDo> registerRecords = waRegisterRecordDo.getWaRegisterRecordByBdkId(registerRecord.getBelongOrgId(), registerRecord.getRecordId());
        if (CollectionUtils.isNotEmpty(registerRecords)) {
            for (WaRegisterRecordDo record : registerRecords) {
                WaRegisterRecord register = new WaRegisterRecord();
                register.setRecordId(record.getRecordId());
                register.setApprovalStatus(ApprovalStatusEnum.REVOKED.getIndex());
                register.setRevokeReason(revokeReason);
                // 撤销后，还原签到时间
                if (record.getHisRegTime() != null) {
                    register.setRegDateTime(record.getHisRegTime());
                    register.setHisRegTime(0L);
                }
                registerRecordMapper.updateByPrimaryKeySelective(register);
            }
        }
        if (ApprovalStatusEnum.PASSED.getIndex().equals(registerRecord.getApprovalStatus())) {
            this.sendMsg(ConvertHelper.longConvert(userInfo.getTenantId()), registerRecord);
        }
        clockTaskService.analyseRegisterRecordByDate(registerRecord.getBelongDate(), registerRecord.getBelongOrgId(),
                Lists.newArrayList(registerRecord.getEmpid()));
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    private void sendMsg(Long corpId, WaRegisterRecordBdkPo record) {
        log.info("Start to send revoke message");
        List<Long> receiver = new ArrayList<>();
        receiver.add(record.getEmpid());
        MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                .type("msg")
                .subType("revoke")
                .title("考勤补卡申请审批结果通知")
                .funcType("41")
                .handlers(receiver)
                .customForms(getFormCustoms(corpId, record));
        MessageParams messageParams = builder.build();
//        workflowMsgPublish.publish(JSON.toJSONString(messageParams), corpId.intValue());
        attendanceWorkflowMsgPublish.publish(JSON.toJSONString(messageParams), corpId);
        log.info("Success to send revoke message, messageParams[{}]", FastjsonUtil.toJson(messageParams));
    }

    private List<KeyValue> getFormCustoms(Long corpId, WaRegisterRecordBdkPo record) {
        List<KeyValue> list = new ArrayList<>();
        //WaRegisterRecordDo registerRecord = waRegisterRecordDo.getRegisterDetailById(corpId, record.getRecordId());
        WaRegisterRecordBdkDo registerRecord = registerRecordBdkDo.getRegisterDetailById(Long.valueOf(corpId), record.getRecordId());
        list.add(new KeyValue("申请部门", registerRecord.getShortName()));
        list.add(new KeyValue("补卡时间", DateUtil.getTimeStrByTimesamp(registerRecord.getRegDateTime())));
        list.add(new KeyValue("申请事由", registerRecord.getReason()));
        return list;
    }

    @Override
    public void deleteOutworkRecords(List<Integer> ids) {
        UserInfo userInfo = getUserInfo();
        List<WaRegisterRecordDo> list = waRegisterRecordDo.selectRegListByIds(userInfo.getTenantId(), ids);
        waRegisterRecordDo.deleteByIds(userInfo.getTenantId(), ids);
        if (CollectionUtils.isNotEmpty(list)) {
            dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_OUTWORK.name(), ModuleTypeEnum.EMP_OUTWORK.getTable(), list, getUserInfo()));
        }
    }

    @Override
    public int getWaAnalyzeUseRegisterRecordCount(List<Integer> recordIds) {
        if (CollectionUtils.isEmpty(recordIds)) {
            return 0;
        }
        UserInfo userInfo = getUserInfo();
        List<WaAnalyzeDo> analyzeDoList = waAnalyzeDo.getWaAnalyzeByRegisterRecordIds(userInfo.getTenantId(), recordIds);
        if (CollectionUtils.isNotEmpty(analyzeDoList)) {
            List<Integer> useRegisterRecordIdList = new ArrayList<>();
            List<Integer> signinIds = analyzeDoList.stream().map(WaAnalyzeDo::getSigninId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(signinIds)) {
                useRegisterRecordIdList.addAll(signinIds);
            }
            List<Integer> signoffIds = analyzeDoList.stream().filter(o -> o.getSignoffId() != null && !useRegisterRecordIdList.contains(o.getSignoffId())).map(WaAnalyzeDo::getSignoffId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(signoffIds)) {
                useRegisterRecordIdList.addAll(signoffIds);
            }
            if (CollectionUtils.isNotEmpty(useRegisterRecordIdList)) {
                List<Integer> intersection = recordIds.stream().filter(useRegisterRecordIdList::contains).collect(Collectors.toList());
                return intersection.size();
            }
        }
        return 0;
    }

    @Override
    public int deleteRegisterRecordByIds(List<Integer> recordIds) {
        UserInfo userInfo = getUserInfo();
        LogRecordContext.putVariable("num", recordIds.size());
        List<WaRegisterRecordDo> list = waRegisterRecordDo.selectRegListByIds(userInfo.getTenantId(), recordIds);
        waRegisterRecordDo.deleteByIds(userInfo.getTenantId(), recordIds);
        if (CollectionUtils.isNotEmpty(list)) {
            dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_CLOCK_IN.name(), ModuleTypeEnum.EMP_CLOCK_IN.getTable(), list, getUserInfo()));
        }
        return 1;
    }

    @Override
    public void updateClockSiteStatus(List<Integer> recordIds, Integer clockSiteStatus) {
        if (CollectionUtils.isEmpty(recordIds)) {
            return;
        }
        UserInfo userInfo = getUserInfo();
        String belongOrgId = userInfo.getTenantId();
        List<WaRegisterRecordDo> list = waRegisterRecordDo.selectRegListByIds(belongOrgId, recordIds);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Long curTime = System.currentTimeMillis() / 1000L;
        for (WaRegisterRecordDo record : list) {
            WaSob waSob = waSobService.getWaSob(record.getEmpid(), record.getBelongDate());
            if (waSob != null && (waSob.getIsLock() || waSob.getSobEndDate() < curTime)) {
                throw new CDException();
            }
        }
        recordIds = list.stream().map(WaRegisterRecordDo::getRecordId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(recordIds)) {
            return;
        }
        waRegisterRecordDo.updateClockSiteStatus(belongOrgId, recordIds, clockSiteStatus);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void adjustClockIn(ClockInAdjustDto adjustDto) {
        if (null == adjustDto.getRecordId()) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201924", WebUtil.getRequest()));
        }
        UserInfo userInfo = UserContext.getAndCheckUser();
        List<WaRegisterRecordDo> recordDoList = waRegisterRecordDo.selectRegListByIds(userInfo.getTenantId(), Lists.newArrayList(adjustDto.getRecordId()));
        if (CollectionUtils.isEmpty(recordDoList)) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201927", WebUtil.getRequest()));
        }
        WaRegisterRecordDo registerRecordDo = recordDoList.get(0);
        LogRecordContext.putVariable("empId", registerRecordDo.getEmpid());

        Integer adjustClockSiteStatus = adjustDto.getClockSiteStatus();
        if (registerRecordDo.getClockSiteStatus().equals(adjustClockSiteStatus)) {
            throw new ServerException(registerRecordDo.getClockSiteStatus() == 1 ?
                    MessageHandler.getMessage("caidao.exception.error_201928", WebUtil.getRequest())
                    : MessageHandler.getMessage("caidao.exception.error_201929", WebUtil.getRequest()));
        }

        WaSob waSob = waSobService.getWaSob(registerRecordDo.getEmpid(), registerRecordDo.getBelongDate());
        if (waSob != null) {
            if (null != waSob.getStatus() && waSob.getStatus() == 1) {// 帐套已关闭
                throw new ServerException(messageResource.getMessage("L006000", new Object[]{}, new Locale(SessionHolder.getLang())));
            }
            Integer sysPeriodMonth = waSob.getSysPeriodMonth();
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            if (DateUtil.getOnlyDate() > sobEndDate) {
//                throw new ServerException("申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员");
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201930", WebUtil.getRequest())
                        + sysPeriodMonth
                        + MessageHandler.getMessage("caidao.exception.error_201931", WebUtil.getRequest())
                        + dateList[1]
                        + MessageHandler.getMessage("caidao.exception.error_201932", WebUtil.getRequest())
                        + dateList[2]
                        + MessageHandler.getMessage("caidao.exception.error_201933", WebUtil.getRequest())
                        + MessageHandler.getMessage("caidao.exception.error_201934", WebUtil.getRequest()));
            }
        }

        if (ValidStatusEnum.VALID.getIndex().equals(adjustClockSiteStatus)) {
            // 无效变有效
            LogRecordContext.putVariable("operate", "有效");
            WaRegisterRecordDo registerRecordDoUpd = new WaRegisterRecordDo();
            if (null != adjustDto.getClockInDate() && null != adjustDto.getClockInTime()) {
                registerRecordDoUpd.setHisRegTime(registerRecordDo.getRegDateTime());
                registerRecordDoUpd.setRegDateTime(adjustDto.getClockInDate() + adjustDto.getClockInTime());
                registerRecordDoUpd.setBelongDate(DateUtil.getOnlyDate(new Date(registerRecordDoUpd.getRegDateTime() * 1000)));
            }
            if (null != adjustDto.getBelongDate()) {
                registerRecordDoUpd.setBelongDate(adjustDto.getBelongDate());
                registerRecordDoUpd.setAdjustStatus(RegisterRecordAdjustStatusEnum.ADJUSTED.getIndex().shortValue());
            }
            registerRecordDoUpd.setRecordId(registerRecordDo.getRecordId());
            registerRecordDoUpd.setReason(adjustDto.getReason());
            registerRecordDoUpd.setClockSiteStatus(adjustClockSiteStatus);
            registerRecordDoUpd.setUpdtime(DateUtil.getCurrentTime(true));
            registerRecordDoUpd.setUpduser(userInfo.getUserId());
            waRegisterRecordDo.updateByPrimaryKeySelective(registerRecordDoUpd);
        } else if (ValidStatusEnum.INVALID.getIndex().equals(adjustClockSiteStatus)) {
            // 有效变无效
            LogRecordContext.putVariable("operate", "无效");
            WaRegisterRecordDo registerRecordDoUpd = new WaRegisterRecordDo();
            registerRecordDoUpd.setReason("");
            registerRecordDoUpd.setRecordId(registerRecordDo.getRecordId());
            registerRecordDoUpd.setClockSiteStatus(adjustClockSiteStatus);
            registerRecordDoUpd.setUpdtime(DateUtil.getCurrentTime(true));
            registerRecordDoUpd.setUpduser(userInfo.getUserId());
            waRegisterRecordDo.updateByPrimaryKeySelective(registerRecordDoUpd);
        }
    }

    @Override
    public List<WaRegisterRecord> listWaRegisterRecord(Long empId, Long belongDate, Integer type, Integer registerType, String sort) {
        return clockRecord.getClockRecords(UserContext.getTenantId(), empId, belongDate, type, registerType, sort);
    }

    @Override
    public List<WaRegisterRecordDo> getRegisterRecordPageList(PageBean pageBean, String belongOrgId, Long startTime, Long endTime,
                                                              List<Long> empIds, List<Integer> types, Integer clockSiteStatus) {
        return waRegisterRecordDo.getRegisterRecordPageList(pageBean, belongOrgId, startTime, endTime, empIds, types,
                clockSiteStatus);
    }

    @Override
    public AttendancePageResult<WaRegisterRecordDo> getPageList(AttendanceBasePage basePage, String belongOrgId,
                                                                Long startTime, Long endTime, List<Long> empIds,
                                                                List<Integer> types, Integer clockSiteStatus) {
        return waRegisterRecordDo.getPageList(basePage, belongOrgId, startTime, endTime, empIds, types,
                clockSiteStatus);
    }

    @Override
    public List<Long> getRegEmpIdList(String belongOrgId, Long startTime, Long endTime) {
        return waRegisterRecordDo.getRegEmpIdList(belongOrgId, startTime, endTime);
    }

    @Override
    public List<EmpParseGroup> selectEmpParseGroupListByDate(String belongOrgId, List<Long> empIds, Long date) {
        return waRegisterRecordDo.selectEmpParseGroupListByDate(belongOrgId, empIds, date);
    }

    @Override
    public List<EmpParseGroup> selectEmpParseGroupListByDateRange(String belongOrgId, List<Long> empIds, Long startDate, Long endDate) {
        return waRegisterRecordDo.selectEmpParseGroupListByDateRange(belongOrgId, empIds, startDate, endDate);
    }

    @Override
    public int updateValidState(String belongOrgId, List<Integer> ids, Long userId, Integer ifValid) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("updateValidState fail ids empty, time={}", System.currentTimeMillis());
            return 0;
        }
        return waRegisterRecordDo.updateValidState(belongOrgId, ids, userId, ifValid);
    }
}