package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.domain.repository.ILeaveRecordRepository;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 休假申请记录
 *
 * <AUTHOR>
 * @Date 2021/3/22
 */
@Slf4j
@Data
@Service
public class WaEmpLeaveDo {
    private Integer leaveId;
    private String leaveFormNo;
    private Integer leaveTypeId;
    private Long empid;
    private String reason;
    private Short status;
    private Long firstEmpid;
    private Short approvalNum;
    private String startDate;
    private String timeSlot;
    private Float totalTimeDuration;
    private Integer timeUnit;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private Long lastApprovalTime;
    private Integer lastEmpid;
    private String revokeReason;
    private String cancelFormNo;
    private Integer revokeStatus;
    private Integer province;
    private Integer city;
    private Integer county;
    private Integer forgid;
    private String emergencyContact;
    private Integer optFlag;
    private Boolean isInvalid;
    private String invalidReason;
    private Long weddingDate;
    private Long expectedDate;
    private Long manufactureDate;
    private String tenantId;

    //子女个数
    private Integer childNum;
    //产假类型：1、顺产，2、难产
    private Integer maternityLeaveType;

    //业务字段
    private String workno;
    private String empName;
    private String leaveName;
    private Long startTime;
    private Long endTime;
    private Long shiftStartTime;
    private Long shiftEndTime;
    private String shalfDay;
    private String ehalfDay;
    private Short periodType;
    private String statusName;
    private String files;
    private String fileNames;
    private Long hireDate;
    private String workCity;
    private String orgName;
    private String marriage;
    private Integer leaveType;
    private String fullPath;
    private String employType;
    private Float cancelTimeDuration;
    private Float actualTimeDuration;
    private Integer leaveStatus;
    private String leaveCancelRemark;
    private Long realDate;
    private String processCode;
    private String i18nLeaveName;
    private String quotaDetail;
    /**
     * 休假班次信息
     */
    private String vacationShift;
    /**
     * 休假开始日期使用的班次（格式为json数组字符串:[112,113]）
     */
    private String startShift;
    /**
     * 休假结束日期使用的班次（格式为json数组字符串:[112,113]）
     */
    private String endShift;

    @Autowired
    private ILeaveRecordRepository leaveRecordRepository;

    @CDText(exp = {"marriage" + TextAspect.MARITAL_ENUM, "employType" + TextAspect.DICT_E,
            "workCity" + TextAspect.PLACE}, classType = WaEmpLeaveDo.class)
    public WaEmpLeaveDo getLeaveDetailById(String tenantId, Long id) {
        return leaveRecordRepository.getLeaveDetailById(tenantId, id);
    }

    public WaEmpLeaveDo getById(Integer leaveId) {
        return leaveRecordRepository.getById(leaveId);
    }

    public AttendancePageResult<WaEmpLeaveDo> getWaEmpLeaveListForHaveQuota(AttendanceBasePage basePage, String belongOrgId, Integer leaveTypeId,
                                                                            Integer quotaType, Integer leaveId, Long empId) {
        return leaveRecordRepository.getWaEmpLeaveListForHaveQuota(basePage, belongOrgId, leaveTypeId, quotaType, leaveId, empId);
    }

    public void update(WaEmpLeaveDo leaveDo) {
        leaveRecordRepository.update(leaveDo);
    }

    public List<WaEmpLeaveDo> getEmpLeaveList(String belongOrgId, List<Integer> statusList, List<Integer> leaveStatusList) {
        return leaveRecordRepository.getEmpLeaveList(belongOrgId, statusList, leaveStatusList);
    }

    public Integer countLeave(Long empId, Long start, Long end) {
        return leaveRecordRepository.countLeave(empId, start, end);
    }

    public List<Map> getEmpNonBrjLtList(Long empId, Long startDate, Long endDate) {
        return leaveRecordRepository.selectEmpNonBrjLtList(empId, startDate, endDate);
    }

    public List<WaEmpLeaveDo> getWaEmpLeaveListByIds(List<Integer> leaveIds) {
        if (CollectionUtils.isEmpty(leaveIds)) {
            return new ArrayList<>();
        }
        return leaveRecordRepository.getWaEmpLeaveListByIds(leaveIds);
    }

    public List<WaEmpLeaveDo> getEmpLeaveByEmpIds(List<Long> empIds, Long leaveDate) {
        if (CollectionUtils.isEmpty(empIds)) {
            return new ArrayList<>();
        }
        return leaveRecordRepository.getEmpLeaveByEmpIds(empIds, leaveDate);
    }

    public List<WaEmpLeaveDo> getWaEmpLeaveListByLeaveDaytimeIds(List<Long> leaveDaytimeIds) {
        if (CollectionUtils.isEmpty(leaveDaytimeIds)) {
            return new ArrayList<>();
        }
        return leaveRecordRepository.getWaEmpLeaveListByLeaveDaytimeIds(leaveDaytimeIds);
    }

    public List<String> getShiftDefNameList() {
        if (StringUtils.isBlank(this.startShift) && StringUtils.isBlank(this.endShift)) {
            return null;
        }
        Set<Long> userShiftIdSet = new LinkedHashSet<>();
        if (StringUtils.isNotBlank(this.startShift)) {
            List<Long> startShiftList = FastjsonUtil.toArrayList(this.startShift, Long.class);
            userShiftIdSet.addAll(startShiftList);
        }
        if (StringUtils.isNotBlank(this.endShift)) {
            List<Long> endShiftList = FastjsonUtil.toArrayList(this.endShift, Long.class);
            userShiftIdSet.addAll(endShiftList);
        }
        List<Integer> userShiftIdList = new ArrayList<>(userShiftIdSet)
                .stream().map(Long::intValue).collect(Collectors.toList());
        Map<Integer, WaShiftDef> shiftDefMap = SpringUtil.getBean(WaCommonService.class)
                .getCorpAllShiftDef(this.tenantId, userShiftIdList);
        if (MapUtils.isEmpty(shiftDefMap)) {
            return null;
        }
        return shiftDefMap.values().stream()
                .map(it -> LangParseUtil.getI18nLanguage(it.getI18nShiftDefName(), it.getShiftDefName()))
                .collect(Collectors.toList());
    }
}
