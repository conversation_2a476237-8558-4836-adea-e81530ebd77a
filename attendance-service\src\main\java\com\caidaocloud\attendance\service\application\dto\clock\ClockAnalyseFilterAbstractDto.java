package com.caidaocloud.attendance.service.application.dto.clock;

import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public abstract class ClockAnalyseFilterAbstractDto {
    private Long empId;
    private Long clockDate;
    private List<WaRegisterRecordDo> regList;
    private ClockAnalyseDataCacheDto dataCacheDto;
    private Map<String, WaShiftDo> empShiftDoMap;
}
