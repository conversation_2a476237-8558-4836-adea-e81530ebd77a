package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WaSobDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.SobPo;

import java.util.List;

/**
 * 考勤周期
 *
 * <AUTHOR>
 * @Date 2021/3/25
 */
public interface ISobRepository {
    int getSobCountByGroupId(Integer waGroupId, Long corpId, String belongOrgId);

    List<SobPo> queryList(String belongOrgId, Integer sobId, String sobName, List<Integer> statusList);

    List<Long> getEmpIdListByGroup(String belongOrgId, Long currentDate, Boolean isDefault, Integer waGroupId, Long startDate, Long endDate, String dataScope);

    SobPo getSobByGroupIdAndPeriod(String belongOrgId, Integer groupId, Integer period);

    List<SobPo> getWaSobIdByDateRangeAndPeriodMonth(String belongOrgId, Long dateTime, List<Integer> periodMonths, Integer groupId);

    List<SobPo> getWaSobByEndDateAndTenantId(String tenantId, Long startDate, Long endDate);

    WaSobDo getById(String belongOrgId, Integer waSobId);
}
