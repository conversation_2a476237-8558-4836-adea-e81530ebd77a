package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.WorkHoursDetailDo;

import java.util.List;

public interface IWorkHoursDetailRepository {
    void delete(String tenantId, List<Long> empIds, Long startDate, Long endDate);

    void addAll(String tenantId, List<WorkHoursDetailDo> hours);

    List<WorkHoursDetailDo> list(String tenantId, List<Long> empIds, Long startDate, Long endDate);
}
