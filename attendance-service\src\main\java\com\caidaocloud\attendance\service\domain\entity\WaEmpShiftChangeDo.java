package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.wa.mybatis.model.WaEmpShiftChange;
import com.caidaocloud.attendance.service.domain.repository.IWaEmpShiftChangeRepository;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/4
 */
@Slf4j
@Data
@Service
public class WaEmpShiftChangeDo {
    private Integer recId;

    private String belongOrgId;

    private Long empid;

    private Long workDate;

    private Integer oldShiftDefId;

    private Integer newShiftDefId;

    private String remark;

    private Integer status;

    private Long crtuser;

    private Long crttime;

    private Long upduser;

    private Long updtime;

    @Autowired
    private IWaEmpShiftChangeRepository waEmpShiftChangeRepository;

    public List<WaEmpShiftChangeDo> getWaEmpShiftChangeList(String belongOrgId, Long startDate, Long endDate) {
        List<WaEmpShiftChange> list = waEmpShiftChangeRepository.getWaEmpShiftChangeList(belongOrgId, startDate, endDate);
        if (CollectionUtils.isNotEmpty(list)) {
            return ObjectConverter.convertList(list, WaEmpShiftChangeDo.class);
        }
        return new ArrayList<>();
    }

    public Map<String, Integer> getEmpShiftChangeMap(String belongOrgId, Long startDate, Long endDate) {
        List<WaEmpShiftChange> list = waEmpShiftChangeRepository.getWaEmpShiftChangeList(belongOrgId, startDate, endDate);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.toMap(o -> String.format("%s_%s", o.getEmpid(), o.getWorkDate()), WaEmpShiftChange::getNewShiftDefId, (v1, v2) -> v1));
        }
        return new HashMap<>();
    }

    public List<WaEmpShiftChangeDo> getWaEmpShiftChangeList(String belongOrgId, List<Long> empIds, Long startDate, Long endDate) {
        if (CollectionUtils.isEmpty(empIds)) {
            return new ArrayList<>();
        }
        List<WaEmpShiftChange> list = waEmpShiftChangeRepository.getWaEmpShiftChangeList(belongOrgId, empIds, startDate, endDate);
        if (CollectionUtils.isNotEmpty(list)) {
            return ObjectConverter.convertList(list, WaEmpShiftChangeDo.class);
        }
        return new ArrayList<>();
    }
}
