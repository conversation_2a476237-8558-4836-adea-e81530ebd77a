package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("销假请假单明细")
public class LeaveInfoDto {

    @ApiModelProperty("员工姓名")
    private String empName;
    @ApiModelProperty("员工工号")
    private String workNo;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("申请时长")
    private Float totalTimeDuration;
    @ApiModelProperty("实际时长")
    private Float actualDuration;
    @ApiModelProperty("假期类型")
    private String leaveName;
    @ApiModelProperty("单位：1 天 2 小时")
    private Integer unit;
    @ApiModelProperty("销假说明")
    private String remark;
}
