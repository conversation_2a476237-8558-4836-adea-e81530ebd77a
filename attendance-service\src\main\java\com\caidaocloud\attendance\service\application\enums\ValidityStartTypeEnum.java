package com.caidaocloud.attendance.service.application.enums;

/**
 * <AUTHOR>
 * @Date 2021/7/27
 */
public enum ValidityStartTypeEnum {
    NATURAL_YEAR(1, "当年1月1号"),
    OVERTIME_DATE(2, "加班开始日期"),
    OVERTIME_MONTH(3, "加班开始月");

    private Integer index;
    private String name;

    ValidityStartTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ValidityStartTypeEnum c : ValidityStartTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
