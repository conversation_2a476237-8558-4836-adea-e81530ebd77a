package com.caidaocloud.attendance.service.application.service.msg;

import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.system.mybatis.model.SysCorpOrgExample;
import com.caidaocloud.attendance.service.application.event.subscribe.DelayMsgSubscribe;
import com.caidaocloud.attendance.service.application.event.subscribe.HrPaasEntityMsgSubscribe;
import com.caidaocloud.attendance.service.application.event.subscribe.MsgSubscribe;
import com.caidaocloud.mq.rabbitmq.ConsumerGenerate;
import com.caidaocloud.mq.rabbitmq.consumer.DynamicConsumer;
import com.caidaocloud.mq.rabbitmq.factory.ExchangeType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GenerateConsumerService {
    @Resource
    private ConsumerGenerate consumerGenerate;
    @Resource
    private MsgSubscribe msgSubscribe;
    @Resource
    private DelayMsgSubscribe delayMsgSubscribe;
    @Resource
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Value("${wf.msg.exchange:wf.msg.fac.direct.exchange}")
    private String EXCHANGE;
    @Value("${wf.msg.routingKey:direct.routingKey}")
    private String GEN_ROUTING_KEY;
    @Value("${wf.msg.queue:direct.queue}")
    private String GEN_QUEUE;
    @Value("${mq.consumer.autoStart:false}")
    private Boolean hrpaasMsgListenerStart;
    @Value("${mq.consumer.entityType:}")
    private String hrpaasMsgListenerEntity;
    private static final String HR_PAAS_EXCHANGE = "hrpaas.es.exchange";
    private static final String HR_PAAS_GEN_ROUTING_KEY = "routingKey.hrpaas.es.msg.attendance";
    private static final String HR_PAAS_GEN_QUEUE = "caidaocloud.hrpaas.sync.es.msg.attendance";
    @Resource
    private HrPaasEntityMsgSubscribe hrPaasEntityMsgSubscribe;

    /**
     * autoDelete 如果为 true，
     * 则代表：最后一个消费者退订时被自动删除。
     * 此时不管队列中是否还存在消息，队列都会删除，因此要慎用 autoDelete = true
     */
    private static final boolean autoDelete = false;

    private static final boolean durable = true;

    private static final boolean autoAck = true;

    public void initGenerateConsumer() throws Exception {
        initGenerateConsumer("");
    }

    public void initGenerateConsumer(String tenantId) throws Exception {
        try {
            log.info("开始初始化消息消费者，请求参数[tenantId:{}]", tenantId);
            SysCorpOrgExample example = new SysCorpOrgExample();
            SysCorpOrgExample.Criteria criteria = example.createCriteria();
            criteria.andOrgtype2EqualTo(1).andStatusEqualTo(1);
            List<Long> tenantIds = new ArrayList<>();
            if (StringUtils.isNotBlank(tenantId)) {
                tenantIds.add(Long.valueOf(tenantId));
            }
            if (CollectionUtils.isNotEmpty(tenantIds)) {
                criteria.andOrgidIn(tenantIds);
            }
            List<SysCorpOrg> items = sysCorpOrgMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(items)) {
                for (SysCorpOrg corp : items) {
                    Long belongOrgId = corp.getOrgid();
                    String ROUTING_KEY = GEN_ROUTING_KEY + "." + belongOrgId;
                    String QUEUE = GEN_QUEUE + "." + belongOrgId;
                    log.info("开始创建队列：[ROUTING_KEY:{}, QUEUE:{}]", ROUTING_KEY, QUEUE);
                    DynamicConsumer dynamicConsumer = consumerGenerate.genConsumer(ExchangeType.DIRECT, msgSubscribe, EXCHANGE, QUEUE, ROUTING_KEY, autoDelete, durable, autoAck);
                    dynamicConsumer.start();
                    log.info("创建队列结束：[ROUTING_KEY:{}, QUEUE:{}]", ROUTING_KEY, QUEUE);
                    initDelayQueue(String.valueOf(belongOrgId));
                    if (hrpaasMsgListenerStart) {
                        initHrPaasEntityEvent(String.valueOf(belongOrgId));
                    }
                }
            }
        } catch (Exception ex) {
            log.error("初始化消息消费者失败，错误信息：{}", ex.getMessage(), ex);
            throw new Exception(ex);
        }
    }

    /**
     * 初始化延迟队列
     * 适用于：周报、日报、打卡提醒等延迟消息
     * @param tenantId
     * @throws Exception
     */
    private void initDelayQueue(String tenantId) throws Exception{
        log.info("Start creating queue[tenantId={}]", tenantId);
        DynamicConsumer dynamicConsumer = consumerGenerate.genConsumer(ExchangeType.DELAY,
            delayMsgSubscribe,
            String.format("%s.delay.attendance.ttl", EXCHANGE),
            String.format("%s.delay.attendance.ttl.%s", GEN_QUEUE, tenantId),
            String.format("%s.delay.attendance.ttl.%s", GEN_ROUTING_KEY, tenantId),
            autoDelete, durable, autoAck);
        dynamicConsumer.start();
        log.info("End creating queue[tenantId={}]", tenantId);
    }

    private void initHrPaasEntityEvent(String tenantId)  throws Exception {
        if (hrpaasMsgListenerEntity == null) {
            return;
        }
        log.info("Start creating queue[tenantId={}]", tenantId);
        List<String> entities = Arrays.stream(hrpaasMsgListenerEntity.split(",")).collect(Collectors.toList());
        for (String entity : entities) {
            DynamicConsumer dynamicConsumer = consumerGenerate.genConsumer(ExchangeType.DIRECT,
                    hrPaasEntityMsgSubscribe, HR_PAAS_EXCHANGE,
                    String.format("%s.%s.%s", HR_PAAS_GEN_QUEUE, entity, tenantId),
                    String.format("%s.%s.%s", HR_PAAS_GEN_ROUTING_KEY, entity, tenantId),
                    autoDelete, durable, autoAck);
            dynamicConsumer.start();
        }
        log.info("End creating queue[tenantId={}]", tenantId);
    }
}