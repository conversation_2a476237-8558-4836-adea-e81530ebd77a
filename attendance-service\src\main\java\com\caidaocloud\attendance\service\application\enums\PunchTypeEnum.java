package com.caidaocloud.attendance.service.application.enums;

/**
 * 加班规则-有效打卡类型 枚举
 */
public enum PunchTypeEnum {

    SAME(1, "同种打卡类型"),
    ALL(2, "所有打卡类型"),
    WORK(3, "正常上下班卡"),
    FIELD(4, "外勤卡");

    private Integer index;
    private String name;

    PunchTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (PunchTypeEnum c : PunchTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
