package com.caidaocloud.attendance.core.wa.service;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.utils.*;
import com.caidao1.employee.mybatis.mapper.EmpInfoMapper;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.payroll.common.PayEngineUtil;
import com.caidao1.payroll.mybatis.mapper.PayEmpGroupMapper;
import com.caidao1.payroll.mybatis.model.PayEmpGroup;
import com.caidao1.record.mybatis.mapper.SysEmpInfoRecordMapper;
import com.caidao1.record.mybatis.model.SysEmpInfoRecord;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.mapper.SysFuncMapper;
import com.caidao1.system.mybatis.model.SysFunc;
import com.caidao1.system.mybatis.model.SysFuncExample;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.auth.service.WaAuthPubService;
import com.caidaocloud.attendance.core.commons.utils.DBConvertUtil;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.commons.utils.ObjectUtil;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.LeaveQuotaDto;
import com.caidaocloud.attendance.core.wa.dto.OtAnalyzeRule;
import com.caidaocloud.attendance.core.wa.dto.WaAnalyzDTO;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.web.ResponseWrap;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.text.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class WaAttendanceConfigService {

    @Autowired
    private WaConfigMapper waConfigMapper;

    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;

    @Autowired
    private WaOvertimeTypeMapper waOvertimeTypeMapper;

    @Autowired
    private WaGroupMapper waGroupMapper;

    @Autowired
    private WaEmpQuotaMapper waEmpQuotaMapper;

    @Autowired
    private WaLeaveSettingMapper waLeaveSettingMapper;

    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;

    @Autowired
    private WaLeaveTypeGroupRelMapper waLeaveTypeGroupRelMapper;
    @Autowired
    private WaShiftDefMapper waShiftDefMapper;

    @Autowired
    private WaWorkRoundShiftMapper waWorkRoundShiftMapper;

    @Autowired
    private WaWorktimeMapper waWorktimeMapper;

    @Autowired
    private WaEmpShiftMapper waEmpShiftMapper;

    @Autowired
    private WaEmpGroupMapper waEmpGroupMapper;

    @Autowired
    private WaWorktimeGroupMapper waWorktimeGroupMapper;

    @Autowired
    private WaWorktimeDetailMapper waWorktimeDetailMapper;

    @Autowired
    private WaLeaveCalendarDetailMapper waLeaveCalendarDetailMapper;

    @Autowired
    private WaCalendarGroupRelMapper waCalendarGroupRelMapper;

    @Autowired
    private WaLeaveQuotaMapper waLeaveQuotaMapper;

    @Autowired
    private WaQuotaSegmentRelMapper waQuotaSegmentRelMapper;

    @Autowired
    private PayEmpGroupMapper empGroupMapper;

    @Autowired
    private WaLeaveSegmentMapper waLeaveSegmentMapper;

    @Autowired
    private WaParseGroupMapper parseGroupMapper;
    @Autowired
    private WaAuthPubService waAuthPubService;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private SysFuncMapper sysFuncMapper;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private WaLeaveTypeDefMapper waLeaveTypeDefMapper;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private EmpInfoMapper empInfoMapper;
    @Autowired
    private WaEmpQuotaDetailMapper waEmpQuotaDetailMapper;
    @Autowired
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Autowired
    private WaEmpQuotaUseMapper waEmpQuotaUseMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private SysEmpInfoRecordMapper sysEmpInfoRecordMapper;
    @Autowired
    private WaEmpInfoRecordMapper waEmpInfoRecordMapper;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private RemoteSmartWorkTimeService remoteSmartWorkTimeService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    private static ConcurrentHashMap<String, SysFunc> funcCache = new ConcurrentHashMap<String, SysFunc>();
    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        //允许没有定义的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //允许使用未带引号的字段名
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //允许使用单引号
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
    }

    /**
     * 查询考勤分析分组
     *
     * @param pageBean
     * @return
     */
    public List<Map> getWaGroupList(PageBean pageBean) {
        UserInfo userInfo = this.getUserInfo();
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("filter", pageBean.getFilter());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "updtime.desc,wa_group_id.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        List<Map> list = waConfigMapper.getWaGroupList2(pageBounds, params);
        if (list != null && list.size() > 0) {
            for (Map map : list) {
                if (map.containsKey("otTypeIds")) {
                    Object obj = map.get("otTypeIds");
                    if (obj != null) {
                        map.put("otTypeIds", DBConvertUtil.convertDBArray(obj));
                    }
                }
                if (map.containsKey("leaveTypeIds")) {
                    Object obj = map.get("leaveTypeIds");
                    if (obj != null) {
                        map.put("leaveTypeIds", DBConvertUtil.convertDBArray(obj));
                    }
                }
                if (map.get("i18n_wa_group_name") != null) {
                    String i18nName = (String) map.get("i18n_wa_group_name");
                    String i18n = LangParseUtil.getI18nLanguage(i18nName, null);
                    if (com.caidaocloud.util.StringUtil.isNotBlank(i18n)) {
                        map.put("wa_group_name", i18n);
                    }
                }
            }
        }
        return list;
    }

    /**
     * 取得考勤分析分组
     *
     * @param id
     */
    public WaGroup getWaGroup(Integer id) {
        return waGroupMapper.selectByPrimaryKey(id);
    }

    /**
     * 删除考勤分析分组
     *
     * @param id
     */
    @Transactional
    public void deleteWaGroup(Integer id) {
        waGroupMapper.deleteByPrimaryKey(id);
    }


    /**
     * 取得员工排班
     *
     * @param id
     */
    public WaEmpShift getEmpShift(Integer id) {
        return waEmpShiftMapper.selectByPrimaryKey(id);
    }

    /**
     * 查询工作日历
     *
     * @param pageBean
     * @return
     */
    public List<Map> getWorkCalendarList(PageBean pageBean) {
        Map params = new HashMap();
        params.put("belongId", getUserInfo().getTenantId());
        params.put("filter", pageBean.getFilter());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "update_time.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        return waConfigMapper.getWorkCalendarList(pageBounds, params);
    }


    /**
     * 生成日历明细
     *
     * @param record
     * @return
     */
    @Transactional
    public void genWorkCalendar(WaWorktimeGroup record, String belongOrgId, Long userId) throws Exception {
        record.setCrtuser(userId);
        record.setCrttime(DateUtil.getCurrentTime(true));
        waWorktimeGroupMapper.insertSelective(record);

        WaWorktime worktime = waWorktimeMapper.selectByPrimaryKey(record.getWorkCalendarId());
        if (null == worktime) {
            log.info("genWorkCalendar fail worktime empty");
            return;
        }

        if (null != worktime.getWorktimeType() && worktime.getWorktimeType() != 1) {
            // 考勤类型: 1 固定班次、2 排班制、3 自由打卡
            log.info("genWorkCalendar fail worktimeType ={} not allowed", worktime.getWorktimeType());
            return;
        }

        // 排班周期
        WaWorkRoundShiftExample example = new WaWorkRoundShiftExample();
        example.createCriteria().andWorkRoundIdEqualTo(worktime.getWorkRoundId());
        example.setOrderByClause("round_no");
        List<WaWorkRoundShift> workRoundShiftList = waWorkRoundShiftMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(workRoundShiftList)) {
            log.info("genWorkCalendar fail workRoundShift empty");
            return;
        }

        // 特殊日期
        List<WaLeaveCalendarDetail> leaveCalendarDetailList = waLeaveCalendarDetailMapper.selectByCalendarGroupId(worktime.getCalendarGroupId());
        Map<Long, WaLeaveCalendarDetail> otherMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(leaveCalendarDetailList)) {
            leaveCalendarDetailList.forEach(waLeaveCalendarDetail -> otherMap.put(waLeaveCalendarDetail.getCalendarDate(), waLeaveCalendarDetail));
        }

        // 班次
        Map<Integer, WaShiftDef> shiftMap = new HashMap<>();
        Map<Integer, WaShiftDef> dataTypeShiftMap = new HashMap<>();
        WaShiftDefExample shiftDefExample = new WaShiftDefExample();
        shiftDefExample.createCriteria().andBelongOrgidEqualTo(belongOrgId);
        List<WaShiftDef> allShiftDefList = waShiftDefMapper.selectByExample(shiftDefExample);
        if (CollectionUtils.isNotEmpty(allShiftDefList)) {
            allShiftDefList.forEach(waShiftDef -> {
                if (!dataTypeShiftMap.containsKey(waShiftDef.getDateType())) {
                    dataTypeShiftMap.put(waShiftDef.getDateType(), waShiftDef);
                }
                shiftMap.put(waShiftDef.getShiftDefId(), waShiftDef);
            });
        }

        // 老日历
        Map<Long, WaWorktimeDetail> originalWorktimeDetailMap = new HashMap<>();
        WaWorktimeDetailExample detailExample = new WaWorktimeDetailExample();
        detailExample.createCriteria().andWorkCalendarIdEqualTo(record.getWorkCalendarId()).andWorkDateBetween(record.getStartdate(), record.getEnddate());
        List<WaWorktimeDetail> originalList = waWorktimeDetailMapper.selectByExample(detailExample);
        if (CollectionUtils.isNotEmpty(originalList)) {
            originalList.forEach(row -> originalWorktimeDetailMap.put(row.getWorkDate(), row));
        }

        // 新日历
        List<WaWorktimeDetail> addList = new ArrayList<>();
        List<WaWorktimeDetail> updList = new ArrayList<>();

        long start = record.getStartdate();
        long end = record.getEnddate();
        int idx = 0;
        for (long date = start; date <= end; date += 24 * 60 * 60) {
            if (workRoundShiftList.get(idx) == null) {
                continue;
            }
            WaWorkRoundShift workRoundShift = workRoundShiftList.get(idx);
            if (!shiftMap.containsKey(workRoundShift.getShiftDefId())) {
                continue;
            }
            WaWorktimeDetail worktimeDetail = new WaWorktimeDetail();
            WaShiftDef shiftDef = shiftMap.get(workRoundShift.getShiftDefId());
            if (originalWorktimeDetailMap.containsKey(date)) {
                BeanUtils.copyProperties(worktimeDetail, originalWorktimeDetailMap.get(date));
            }
            //查询有无特殊日期
            if (otherMap.containsKey(date)) {
                Long specDate = otherMap.get(date).getSpecDate();
                if (specDate != null) {
                    WaWorktimeDetail specWorkTimeDetail = null;
                    if (originalWorktimeDetailMap.containsKey(specDate)) {
                        specWorkTimeDetail = originalWorktimeDetailMap.get(specDate);
                    } else {
                        detailExample = new WaWorktimeDetailExample();
                        detailExample.createCriteria().andWorkCalendarIdEqualTo(record.getWorkCalendarId()).andWorkDateEqualTo(otherMap.get(date).getSpecDate());
                        List<WaWorktimeDetail> specDetails = waWorktimeDetailMapper.selectByExample(detailExample);
                        if (CollectionUtils.isNotEmpty(specDetails)) {
                            specWorkTimeDetail = specDetails.get(0);
                            originalWorktimeDetailMap.put(specDate, specWorkTimeDetail);
                        }
                    }
                    if (specWorkTimeDetail != null) {
                        Integer detailId = worktimeDetail.getWorktimeDetailId();
                        BeanUtils.copyProperties(worktimeDetail, specWorkTimeDetail);
                        worktimeDetail.setWorktimeDetailId(detailId);
                    } else {
                        BeanUtils.copyProperties(worktimeDetail, shiftDef);
                    }
                    worktimeDetail.setDateType(1);
                } else {
                    //非工作日
                    WaShiftDef replaceShift;
                    Integer replaceShiftId = otherMap.get(date).getReplaceShift();
                    if (replaceShiftId != null && shiftMap.containsKey(replaceShiftId)) {
                        replaceShift = shiftMap.get(replaceShiftId);
                    } else {
                        replaceShift = shiftDef;
                    }
                    if (shiftDef.getDateType().equals(1)) {
                        BeanUtils.copyProperties(worktimeDetail, replaceShift);
                        worktimeDetail.setDateType(otherMap.get(date).getDateType());
                    } else {
                        if (replaceShiftId == null && dataTypeShiftMap.containsKey(shiftDef.getDateType())) {
                            replaceShift = dataTypeShiftMap.get(shiftDef.getDateType());
                        }
                        BeanUtils.copyProperties(worktimeDetail, replaceShift);
                        worktimeDetail.setDateType(otherMap.get(date).getDateType());
                    }
                }
            } else {
                BeanUtils.copyProperties(worktimeDetail, shiftDef);
            }
            if (worktimeDetail.getWorktimeDetailId() == null) {
                worktimeDetail.setWorkCalendarId(record.getWorkCalendarId());
                worktimeDetail.setWorktimeGroupId(record.getWorktimeGroupId());
                worktimeDetail.setWorkDate(date);

                worktimeDetail.setRestPeriods(null);
                worktimeDetail.setOvertimeRestPeriods(null);
                worktimeDetail.setCreateUser(userId);
                worktimeDetail.setCreateTime(DateUtil.getCurrentTime(true));
                worktimeDetail.setUpdateTime(DateUtil.getCurrentTime(true));
                worktimeDetail.setUpdateUser(userId);
                addList.add(worktimeDetail);
            } else {
                worktimeDetail.setWorkDate(date);
                worktimeDetail.setUpdateTime(DateUtil.getCurrentTime(true));
                worktimeDetail.setUpdateUser(userId);
                updList.add(worktimeDetail);
            }
            if (idx == workRoundShiftList.size() - 1) {
                idx = 0;
            } else {
                idx++;
            }
        }

        if (CollectionUtils.isNotEmpty(addList)) {
            importService.fastInsertList(WaWorktimeDetail.class, "worktimeDetailId", addList);
        }
        if (CollectionUtils.isNotEmpty(updList)) {
            importService.fastUpdList(WaWorktimeDetail.class, "worktimeDetailId", updList);
        }
    }

    /**
     * 保存工作日历
     *
     * @param leaveCalendarDetail
     * @return
     */
    @Transactional
    public void genSpecWorkCalendar(WaLeaveCalendarDetail leaveCalendarDetail) throws Exception {
        WaCalendarGroupRelExample calendarGroupRelExample = new WaCalendarGroupRelExample();
        calendarGroupRelExample.createCriteria().andHolidayCalendarIdEqualTo(leaveCalendarDetail.getHolidayCalendar());
        List<WaCalendarGroupRel> calendarGroupRelList = waCalendarGroupRelMapper.selectByExample(calendarGroupRelExample);
        if (CollectionUtils.isNotEmpty(calendarGroupRelList)) {
            List<Integer> groupIds = new ArrayList<>();
            for (WaCalendarGroupRel calendarGroupRel : calendarGroupRelList) {
                groupIds.add(calendarGroupRel.getCalendarGroupId());
            }
            WaWorktimeExample waWorktimeExample = new WaWorktimeExample();
            waWorktimeExample.createCriteria().andCalendarGroupIdIn(groupIds);
            List<WaWorktime> worktimeList = waWorktimeMapper.selectByExample(waWorktimeExample);
            for (WaWorktime worktime : worktimeList) {
                WaWorktimeDetailExample detailExample = new WaWorktimeDetailExample();
                detailExample.createCriteria().andWorkCalendarIdEqualTo(worktime.getWorkCalendarId()).andWorkDateEqualTo(leaveCalendarDetail.getCalendarDate());
                List<WaWorktimeDetail> details = waWorktimeDetailMapper.selectByExample(detailExample);
                if (CollectionUtils.isNotEmpty(details)) {
                    if (leaveCalendarDetail.getSpecDate() != null) {
                        detailExample = new WaWorktimeDetailExample();
                        detailExample.createCriteria().andWorkCalendarIdEqualTo(worktime.getWorkCalendarId()).andWorkDateEqualTo(leaveCalendarDetail.getSpecDate());
                        List<WaWorktimeDetail> specDetails = waWorktimeDetailMapper.selectByExample(detailExample);
                        if (CollectionUtils.isNotEmpty(specDetails)) {
                            WaWorktimeDetail worktimeDetail = new WaWorktimeDetail();
                            BeanUtils.copyProperties(worktimeDetail, specDetails.get(0));
                            worktimeDetail.setDateType(leaveCalendarDetail.getDateType());
                            worktimeDetail.setWorkDate(details.get(0).getWorkDate());
                            worktimeDetail.setWorktimeDetailId(details.get(0).getWorktimeDetailId());
                            waWorktimeDetailMapper.updateByPrimaryKeySelective(worktimeDetail);
                        }
                    } else {
                        //非工作日
                        Integer shiftDefId = -1;
                        if (leaveCalendarDetail.getReplaceShift() != null) {
                            shiftDefId = leaveCalendarDetail.getReplaceShift();
                        }
                        WaWorktimeDetail worktimeDetail = new WaWorktimeDetail();
                        worktimeDetail.setShiftDefId(shiftDefId);
                        worktimeDetail.setDateType(leaveCalendarDetail.getDateType());
                        worktimeDetail.setWorktimeDetailId(details.get(0).getWorktimeDetailId());
                        waWorktimeDetailMapper.updateByPrimaryKeySelective(worktimeDetail);
                    }
                }
            }
        }
    }


    /**
     * 查询日工作计划
     *
     * @param pageBean
     * @param belongModule 所属模块：attendance 假勤、wfm 工时 默认假勤
     * @return
     */
    public List<Map> getShiftDefList(PageBean pageBean, String belongModule) {
        String belongid = getUserInfo().getTenantId();
        Map params = new HashMap();
        params.put("belongId", belongid);
        params.put("belongModule", belongModule);
        params.put("filter", pageBean.getFilter());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "shift_def_id" : pageBean.getOrder();
        String str = waAuthPubService.getDataScope(31, "ei", "belong_orgid='" + belongid + "'");
        str = str.replace("belong_org_id", "belong_orgid");
        params.put("datafilter", str);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        return waConfigMapper.getShiftDefList(pageBounds, params);
    }


    /**
     * 取得日工作计划
     *
     * @param belongOrgId
     * @param id
     * @return
     */
    public WaShiftDef getShiftDefObj(String belongOrgId, Integer id) {
        Map params = new HashMap();
        params.put("shiftDefId", id);
        params.put("belongOrgId", belongOrgId);
        return waShiftDefMapper.getShiftDefObj(params);
    }


    /**
     * 删除日工作计划
     *
     * @param id
     */
    @Transactional
    public void deleteShiftDef(Integer id) {
        WaShiftDef shiftDef = waShiftDefMapper.selectByPrimaryKey(id);
        LogRecordContext.putVariable("name", shiftDef.getShiftDefName());
        waShiftDefMapper.deleteByPrimaryKey(id);
    }


    /**
     * 查询期间工作计划
     *
     * @param pageBean
     * @return
     */
    public List<Map> getWorkRoundList(PageBean pageBean) {
        Map params = new HashMap();
        params.put("belongId", getUserInfo().getTenantId());
        params.put("filter", pageBean.getFilter());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "work_round_id" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        List<Map> list = waConfigMapper.getWorkRoundList(pageBounds, params);
        list.forEach(l -> {
            if (null != l.get("i18n_round_name")) {
                String i18n = LangParseUtil.getI18nLanguage(l.get("i18n_round_name").toString(), null);
                if (!StringUtil.isEmptyOrNull(i18n)) {
                    l.put("round_name", i18n);
                }
            }
        });
        return list;
    }


    /**
     * 查询轮次
     *
     * @param pageBean
     * @return
     */
    public List<Map> getWorkRoundShiftList(PageBean pageBean, Integer workRoundId) {
        Map params = new HashMap();
        params.put("workRoundId", workRoundId);
        params.put("filter", pageBean.getFilter());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "round_no" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        List<Map> list = waConfigMapper.getWorkRoundShiftList(pageBounds, params);
        list.forEach(l -> {
            if (null != l.get("i18n_shift_def_name")) {
                String i18n = LangParseUtil.getI18nLanguage(l.get("i18n_shift_def_name").toString(), null);
                if (com.caidaocloud.util.StringUtil.isNotBlank(i18n)) {
                    l.put("shift_def_name", i18n);
                }
            }
        });
        return list;
    }

    /**
     * 查询休假类型
     *
     * @param pageBean
     * @return
     */
    public List<Map> getLeaveTypeList(PageBean pageBean, String lang) throws Exception {
        UserInfo userInfo = getUserInfo();
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("filter", pageBean.getFilter());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "orders" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        List<Map> list = waConfigMapper.getLeaveTypeList2(pageBounds, params);
        if (CollectionUtils.isNotEmpty(list)) {
            for (Map map : list) {
                String joinids = DBConvertUtil.convertDBArray(map.get("superior_quota_setting_ids"));
                if (joinids != null)
                    map.put("superior_quota_setting_ids", joinids);
            }
        }
        return LangUtil.langlist(list, lang, "leave_name", "leave_name_lang");
    }


    /**
     * 取得休假类型
     *
     * @param id
     */
    public WaLeaveType getLeaveType(Integer id) {
        return waLeaveTypeMapper.selectByPrimaryKey(id);
    }

    /**
     * 保存休假类型
     *
     * @param record
     * @return
     */
    @Transactional
    public Integer saveLeaveType(WaLeaveType record, String file) {
        UserInfo userInfo = getUserInfo();
        if (record.getLeaveTypeId() == null) {
            record.setBelongOrgid(userInfo.getTenantId());
            if (record.getIsCheckMaxTime() == null) {
                record.setIsCheckMaxTime(false);
            }
            if (record.getIsCheckMinTime() == null) {
                record.setIsCheckMinTime(false);
            }
            if (record.getIsCheckMonthTime() == null) {
                record.setIsCheckMonthTime(false);
            }
            if (record.getIsUploadFile() == null) {
                record.setIsUploadFile(false);
            }
            if (record.getIsUsedInAdvance() == null) {
                record.setIsUsedInAdvance(false);
            }
            if (record.getIsUsedInProbation() == null) {
                record.setIsUsedInProbation(false);
            }
            if (record.getIsRestDay() == null) {
                record.setIsRestDay(false);
            }
            if (record.getIsLegalHoliday() == null) {
                record.setIsLegalHoliday(false);
            }
            record.setIcon(file);
            if (record.getLeaveType() == null) {
                record.setLeaveType(9);
            }
            record.setCrtuser(userInfo.getUserId());
            record.setCrttime(DateUtil.getCurrentTime(true));
            waLeaveTypeMapper.insertSelective(record);
        } else {
            record.setUpduser(userInfo.getUserId());
            record.setIcon(file);
            record.setUpdtime(DateUtil.getCurrentTime(true));
            record.setAcctTimeType(null);// update by 20181017 假期类型单位不允许变更
            waLeaveTypeMapper.updateByPrimaryKeySelective(record);
        }
        return record.getLeaveTypeId();
    }

    /**
     * 查询加班类型
     *
     * @param pageBean
     * @return
     */
    public List<Map> getOtTypeList(PageBean pageBean) {
        UserInfo userInfo = getUserInfo();
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("filter", pageBean.getFilter());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "updtime.desc,overtime_type_id.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        List<Map> otTypeList = waMapper.getOtTypeList(pageBounds, params);
        if (CollectionUtils.isNotEmpty(otTypeList)) {
            for (Map ot : otTypeList) {
                if (ot.containsKey("compensate_type")) {
                    Integer compensateype = (Integer) ot.get("compensate_type");
                    String code = "";
                    if (compensateype == 0) {
                        code = "L000150";//不补偿
                    } else if (compensateype == 1) {
                        code = "L000566";//付现
                    } else if (compensateype == 2) {
                        code = "L002094";//调休
                    } else if (compensateype == 3) {
                        code = "L006066";//已预付
                    }
                    if (compensateype != 2) {
                        ot.keySet().removeIf(key -> key.equals("overtime_num") || key.equals("off_num"));
                    }
                    ot.put("compensate_type", messageResource.getMessage(code, new Object[]{}, new Locale(SessionHolder.getLang())));
                }
                ot.put("ot_type_name", ObjectUtil.getValue(ot.get("overtime_type"), "") + ot.get("compensate_type"));
            }
        }
        return otTypeList;
    }

    /**
     * 取得加班类型
     *
     * @param id
     */
    public WaOvertimeType getOtType(Integer id) {
        return waOvertimeTypeMapper.selectByPrimaryKey(id);
    }


    /**
     * 保存加班类型
     *
     * @param record
     * @return
     */
    @Transactional
    public Integer saveOtType(WaOvertimeType record) {
        UserInfo userInfo = getUserInfo();
        LogRecordContext.putVariable("name", record.getTypeName());
        if (record.getOvertimeTypeId() == null) {
            record.setBelongOrgid(userInfo.getTenantId());
            record.setCrtuser(userInfo.getUserId());
            record.setCrttime(DateUtil.getCurrentTime(true));
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));
            LogRecordContext.putVariable("operate", "新增");
            waOvertimeTypeMapper.insertSelective(record);
        } else {
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));
            LogRecordContext.putVariable("operate", "编辑");
            waOvertimeTypeMapper.updateByPrimaryKeyIncludeFieldSelective(record, new ArrayList<>(Arrays.asList("parseRule")));
        }
        return record.getOvertimeTypeId();
    }

    /**
     * 查询考勤分析分组
     *
     * @param pageBean
     * @return
     */
    public List<Map> getParseGroupList(PageBean pageBean) {
        UserInfo userInfo = getUserInfo();
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("filter", pageBean.getFilter());
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "updtime.desc,parse_group_id.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        return waConfigMapper.getParseGroupList(pageBounds, params);
    }

    /**
     * 取得考勤分析分组
     *
     * @param id
     */
    public WaParseGroup getParseGroup(Integer id) {
        return parseGroupMapper.selectByPrimaryKey(id);
    }

    /**
     * 删除考勤分析分组
     *
     * @param id
     */
    @Transactional
    public void deleteParseGroup(Integer id) {
        parseGroupMapper.deleteByPrimaryKey(id);
    }

    /**
     * 保存考勤分析分组
     *
     * @param record
     * @return
     */
    @Transactional
    public Integer saveParseGroup(WaParseGroup record) {
        record.setLeaveExemptionSwitch(record.getLeaveExemptionSwitch() != null && record.getLeaveExemptionSwitch());
        UserInfo userInfo = getUserInfo();
        if (record.getParseGroupId() == null) {
            record.setBelongOrgid(userInfo.getTenantId());
            record.setCrtuser(userInfo.getUserId());
            record.setCrttime(DateUtil.getCurrentTime(true));
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));
            parseGroupMapper.insertSelective(record);
        } else {
            record.setBelongOrgid(userInfo.getTenantId());
            record.setCrtuser(userInfo.getUserId());
            record.setCrttime(DateUtil.getCurrentTime(true));
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));
            //parseGroupMapper.updateByPrimaryKeySelective(record);
            parseGroupMapper.updateByPrimaryKey(record);
        }
        return record.getParseGroupId();
    }


    /**
     * 取得员工排班
     *
     * @param id
     */
    public WaEmpGroup getEmpGroup(Integer id) {
        return waEmpGroupMapper.selectByPrimaryKey(id);
    }


    /**
     * 查询假期配额
     *
     * @param pageBean
     * @return
     */
//    @Cache2Text(lang = "#pageBean.lang", value = {
//            RedisKeyDefine.SYS_PARM_DICT_KEY + ":status"
//    })
    @CDText(exp = {"status:status_txt" + TextAspect.STATUS_ENUM}, classType = Map.class)
    public List<Map> getEmpQuotaList(PageBean pageBean, Integer year, Integer[] quotaSettingId, String belongOrgId, List<Integer> years) {
        Long now = DateUtil.getCurrentTime(true);
        Map params = new HashMap();
        params.put("belongId", belongOrgId);
        params.put("curTime", DateUtil.getOnlyDate());
        params.put("curDate", DateUtil.getOnlyDate());
        if (year != null) {
            params.put("year", year);
        }
        if (StringUtils.isNotBlank(pageBean.getKeywords())) {
            params.put("keywords", pageBean.getKeywords());
        }
        if (quotaSettingId != null && quotaSettingId.length > 0) {
            params.put("quotaSettingIds", quotaSettingId);
        }
        params.put("filter", pageBean.getFilter());
        params.put("years", years);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "emp_quota_id.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));

        List<Map> empQuotaList = waMapper.listEmpQuotaByParams(pageBounds, params);
        for (Map row : empQuotaList) {
            Integer acctTimeType = (Integer) row.get("acct_time_type");
            BigDecimal quotaDay = row.get("quota_day") == null ? new BigDecimal(0) : new BigDecimal((Float) row.get("quota_day"));
            BigDecimal deductionDay = row.get("deduction_day") == null ? new BigDecimal(0) : new BigDecimal((Float) row.get("deduction_day"));
            BigDecimal adjustQuota = row.get("adjust_quota") == null ? new BigDecimal(0) : new BigDecimal((Float) row.get("adjust_quota"));
            BigDecimal adjustUsedDay = row.get("adjust_used_day") == null ? new BigDecimal(0) : new BigDecimal((Float) row.get("adjust_used_day"));
            BigDecimal usedDay = row.get("used_day") == null ? new BigDecimal(0) : new BigDecimal((Float) row.get("used_day"));
            BigDecimal nowQuota = row.get("now_quota") == null ? new BigDecimal(0) : new BigDecimal((Float) row.get("now_quota"));
            BigDecimal fixUsedDay = row.get("fix_used_day") == null ? new BigDecimal(0) : new BigDecimal((Float) row.get("fix_used_day"));
            //计算留存有效剩余额度
            BigDecimal remainKy = new BigDecimal(0);
            BigDecimal remain_day = row.get("remain_day") == null ? new BigDecimal(0) : new BigDecimal((Float) row.get("remain_day"));
            BigDecimal remain_used_day = row.get("remain_used_day") == null ? new BigDecimal(0) : new BigDecimal((Float) row.get("remain_used_day"));
            Long remain_valid_date = (Long) row.get("remain_valid_date");
            if (remain_valid_date != null) {
                if (remain_valid_date >= DateUtil.getOnlyDate()) {
                    remainKy = remain_day.subtract(remain_used_day);
                }
            }
            BigDecimal frozenDay = new BigDecimal((Float) row.get("frozen_day"));
            float used_day = usedDay.add(fixUsedDay).floatValue();
            float curRemain = quotaDay.add(adjustQuota).add(remainKy).subtract(deductionDay).subtract(new BigDecimal(used_day)).subtract(adjustUsedDay).subtract(frozenDay).floatValue();
            float now_remain = nowQuota.add(adjustQuota).add(remainKy).subtract(deductionDay).subtract(new BigDecimal(used_day)).subtract(adjustUsedDay).subtract(frozenDay).floatValue();

            BigDecimal currentYearTotalQuota = nowQuota.add(adjustQuota).add(remainKy);//本年享有
            BigDecimal usedTotalQuota = usedDay.add(fixUsedDay).add(adjustUsedDay).add(deductionDay);//已使用
            BigDecimal remainTotalQuota = currentYearTotalQuota.subtract(usedTotalQuota);//剩余
            Float inTransitQuota = 0f;//在途
            if (row.containsKey("inTransitQuota") && row.get("inTransitQuota") != null) {
                inTransitQuota = (Float) row.get("inTransitQuota");
            }
            if (acctTimeType == 1) {
                row.put("timeUnitName", ResponseWrap.wrapResult(10031, null).getMsg());
            } else {
                row.put("timeUnitName", ResponseWrap.wrapResult(10032, null).getMsg());
            }
            Integer carryRule = 1;//分钟转小时 进位规则 1 四舍五入
            timeFormat(acctTimeType, now_remain, row, "now_remain", carryRule);
            timeFormat(acctTimeType, (Float) row.get("quota_day"), row, "quota_day", carryRule);
            timeFormat(acctTimeType, used_day, row, "used_day", carryRule);
            timeFormat(acctTimeType, (Float) row.get("remain_day"), row, "remain_day", carryRule);
            timeFormat(acctTimeType, (Float) row.get("now_quota"), row, "now_quota", carryRule);
            timeFormat(acctTimeType, curRemain, row, "cur_remain", carryRule);
            timeFormat(acctTimeType, (Float) row.get("remain_used_day"), row, "remain_used_day", carryRule);
            timeFormat(acctTimeType, (Float) row.get("deduction_day"), row, "deduction_day", carryRule);
            timeFormat(acctTimeType, adjustQuota.floatValue(), row, "adjust_quota", carryRule);
            timeFormat(acctTimeType, adjustUsedDay.floatValue(), row, "adjust_used_day", carryRule);
            timeFormat(acctTimeType, currentYearTotalQuota.floatValue(), row, "currentYearTotalQuota", carryRule);
            timeFormat(acctTimeType, usedTotalQuota.floatValue(), row, "usedTotalQuota", carryRule);
            timeFormat(acctTimeType, remainTotalQuota.floatValue(), row, "remainTotalQuota", carryRule);
            timeFormat(acctTimeType, inTransitQuota, row, "inTransitQuota", carryRule);
            //冻结配额
            timeFormat(acctTimeType, frozenDay.floatValue(), row, "frozen_day", carryRule);
            /*row.put("now_remain", timeFormat(acctTimeType, now_remain));
            row.put("quota_day", timeFormat(acctTimeType, (Float) row.get("quota_day")));
            row.put("used_day", timeFormat(acctTimeType, used_day));
            row.put("remain_day", timeFormat(acctTimeType, (Float) row.get("remain_day")));
            row.put("now_quota", timeFormat(acctTimeType, (Float) row.get("now_quota")));
            row.put("cur_remain", timeFormat(acctTimeType, curRemain));
            row.put("remain_used_day", timeFormat(acctTimeType, (Float) row.get("remain_used_day")));
            row.put("deduction_day", timeFormat(acctTimeType, (Float) row.get("deduction_day")));
            row.put("adjust_quota", timeFormat(acctTimeType, adjustQuota.floatValue()));
            row.put("adjust_used_day", timeFormat(acctTimeType, adjustUsedDay.floatValue()));*/
        }

        return empQuotaList;
    }

    /**
     * 查询假期配额
     *
     * @param pageBean
     * @return
     */
    public List<Map> getEmpQuotaDetailList(PageBean pageBean, String belongOrgId, Integer empQuotaId, Integer type) {
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        Map params = new HashMap();
        params.put("belongOrgId", belongOrgId);
        params.put("empQuotaId", empQuotaId);
        params.put("sort", "\"startDate\" asc,\"endDate\" asc");
        params.put("quotaType", type);//配额详情类型 1配额详情 2 调整配额
        params.put("curDate", DateUtil.getOnlyDate());
        List<Map> list = waMapper.getEmpQuotaDetailList(pageBounds, params);
        if (CollectionUtils.isNotEmpty(list)) {
            for (Map row : list) {
                BigDecimal quotaDay = new BigDecimal((Float) row.get("quotaDay"));
                BigDecimal surplusQuota = new BigDecimal(row.get("surplusQuota") == null ? 0f : (Float) row.get("surplusQuota"));
                BigDecimal invalidQuota = new BigDecimal(row.get("invalidQuota") == null ? 0f : (Float) row.get("invalidQuota"));
                BigDecimal usedDay = new BigDecimal(row.get("usedDay") == null ? 0f : (Float) row.get("usedDay"));

                Integer acctTimeType = (Integer) row.get("acctTimeType");

                Integer formatType = 1;//分钟转小时 小数进位规则
                row.put("quotaDay", convertTimeToStr(acctTimeType, quotaDay.floatValue(), formatType));
                row.put("surplusQuota", convertTimeToStr(acctTimeType, surplusQuota.floatValue(), formatType));
                row.put("invalidQuota", convertTimeToStr(acctTimeType, invalidQuota.floatValue(), formatType));
                row.put("usedDay", convertTimeToStr(acctTimeType, usedDay.floatValue(), formatType));
            }
        }
        return list;
    }

    /**
     * 格式化时长
     *
     * @param timeUnit
     * @param timeDuration
     * @param formatType
     * @return
     */
    private String convertTimeToStr(Integer timeUnit, Float timeDuration, Integer formatType) {
        if (timeDuration == null) return "";
        if (timeUnit == 1) {
            NumberFormat nf = new DecimalFormat("#.##");
            return nf.format(timeDuration) + ResponseWrap.wrapResult(10031, null).getMsg();
        } else if (timeUnit == 2) {
            if (Integer.valueOf(1).equals(formatType)) {
                BigDecimal v = new BigDecimal(timeDuration).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP);
                return v.doubleValue() + ResponseWrap.wrapResult(10032, null).getMsg();
            } else {
                NumberFormat nf = new DecimalFormat("#.##");
                BigDecimal b = new BigDecimal(timeDuration / 60);
                return nf.format(b) + ResponseWrap.wrapResult(10032, null).getMsg();
            }
        }
        return "";
    }

    /**
     * @param timeUnit
     * @param timeDuration
     * @param map
     * @param key
     * @param carryRule    进位规则
     */
    private void timeFormat(Integer timeUnit, Float timeDuration, Map map, String key, Integer carryRule) {
        if (timeDuration == null) {
            return;
        }

        try {
            NumberFormat nf = new DecimalFormat("#.##");
            if (timeUnit == 1) {
                map.put(key, nf.parse(nf.format(timeDuration)));
                return;
            }

            if (timeUnit == 2) {
                if (carryRule != null && carryRule == 1) {//四舍五入
                    BigDecimal v = new BigDecimal(timeDuration).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP);
                    map.put(key, v.doubleValue());
                    return;
                } else {
                    BigDecimal b = new BigDecimal(timeDuration / 60);
                    map.put(key, nf.format(b));
                    return;
                }
            }
        } catch (Exception e) {
            log.error("timeFormat 转换异常，{}", e.getMessage(), e);
        }
        map.put(key, "");
    }


    /**
     * 计算当前假期配额
     *
     * @return
     */
    @Deprecated
    public void calEmpNowQuota(String belongId, Long empid, String date) {
        Long now = DateUtil.getCurrentTime(true);
        //指定日期计算当前配额
        Long dateTimeStamp = null;
        if (StringUtils.isNotEmpty(date)) {
            dateTimeStamp = DateUtil.getTimesampByDateStr2(date);
            now = dateTimeStamp;
        }
        Map params = new HashMap();
        params.put("curTime", now);
        params.put("empid", empid);
        if (belongId != null) {
            params.put("datafilter", "and ei.belong_org_id='" + belongId + "'");
        }
        PageBean pageBean = new PageBean(true);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());

        funcCache.clear();
        List<Map> empQuotaList = waConfigMapper.getEmpQuotaList(pageBounds, params);
        if (CollectionUtils.isNotEmpty(empQuotaList)) {
            //杰尼亚年假配额折算定制逻辑，把杰尼亚的年假配额单独拿出来进行汇总折算
            calJnyEmpALNowQuota(empQuotaList, empid, dateTimeStamp);

            //系统标准逻辑
            List<WaEmpQuota> empQuotaUpdList = new ArrayList<>();
            List<Integer> empQuotaIdList = new ArrayList<>();
            for (Map row : empQuotaList) {
                BigDecimal quotaDay = new BigDecimal((Float) row.get("quota_day"));
                BigDecimal nowQuota = new BigDecimal((Float) row.get("now_quota"));
                Integer useItType = (Integer) row.get("use_it_type");
                Integer useour2fiveRule = (Integer) row.get("use_four2five_rule");
                Integer acctTimeType = (Integer) row.get("acct_time_type");

                if (useItType != null && !useItType.equals(1)) {
                    Long hireDate = (Long) row.get("hire_date");
                    Long startDate = (Long) row.get("start_date");
                    Long lastDate = (Long) row.get("last_date");
                    if (hireDate != null && hireDate > startDate) {
                        startDate = hireDate;
                    }

                    quotaDay = quotaDay.multiply(getConvRate(startDate, lastDate, useItType, startDate, now));
                    if (acctTimeType != null && 2 == acctTimeType) {
                        quotaDay = quotaDay.divide(new BigDecimal(60));
                    }
                    quotaDay = handleMantissa(row.get("belong_org_id").toString(), quotaDay, acctTimeType, useour2fiveRule);
                }
                if (nowQuota.floatValue() != quotaDay.floatValue()) {
                    WaEmpQuota empQuota = new WaEmpQuota();
                    empQuota.setEmpQuotaId((Integer) row.get("emp_quota_id"));
                    empQuota.setNowQuota(quotaDay.floatValue());
                    empQuotaUpdList.add(empQuota);
                }
                if (empQuotaIdList.indexOf((Integer) row.get("emp_quota_id")) < 0) {
                    empQuotaIdList.add((Integer) row.get("emp_quota_id"));
                }
            }

            //计算并且更新当年调整
            waCommonService.updateEmpAdjustQuota(empid, empQuotaIdList);

            importService.fastUpdList(WaEmpQuota.class, "empQuotaId", empQuotaUpdList, Arrays.asList("nowQuota"));
        }
    }

    @Transactional
    public void calJnyEmpALNowQuota(List<Map> empQuotaList, Long empId, Long date) {
        List<Map> jnyEmpALQuota = new ArrayList<>();
        List<Integer> jnyEmpAlId = new ArrayList<>();
        empQuotaList.forEach(row -> {
            Integer leaveType = Integer.valueOf(row.get("leave_type").toString());
            if (leaveType == 1) {
                String isOpenALQuotaSumConversion = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + row.get("belong_org_id") + RedisKeyDefine.IS_OPEN_AL_QUOTA_SUMCONVERSION);
                if ("1".equals(isOpenALQuotaSumConversion)) {
                    jnyEmpALQuota.add(row);
                    jnyEmpAlId.add((Integer) row.get("emp_quota_id"));
                }
            }
        });
        empQuotaList.removeIf(row -> jnyEmpAlId.indexOf(row.get("emp_quota_id")) != -1);

        List<WaEmpQuota> empQuotaUpdList = new ArrayList<>();
        List<Integer> empQuotaIdList = new ArrayList<>();

        Map<Long, List<Map>> empAlQuotaMap = new HashMap<>();
        jnyEmpALQuota.forEach(lq -> {
            Long empid = ConvertHelper.longConvert(lq.get("empid"));
            List<Map> list = empAlQuotaMap.get(empid);
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(lq);
            empAlQuotaMap.put(empid, list);
        });
        if (MapUtils.isNotEmpty(empAlQuotaMap)) {
            empAlQuotaMap.forEach((empid, alQuotaList) -> {
                try {
                    //查找法定年假配额数据
                    Map legalQuota = null;//法定
                    Map welfareQuota = null;//福利
                    BigDecimal totalQuota = new BigDecimal(0);//法定+福利

                    for (Map waEmpQuota : alQuotaList) {
                        Integer leaveType = (Integer) waEmpQuota.get("quota_setting_type");
                        BigDecimal quotaDay = new BigDecimal((Float) waEmpQuota.get("quota_day"));
                        if (leaveType == 1) {
                            legalQuota = waEmpQuota;
                        } else if (leaveType == 2) {
                            welfareQuota = waEmpQuota;
                        }
                        totalQuota = totalQuota.add(quotaDay);
                    }

                    if (legalQuota != null && welfareQuota != null) {
                        //总配额先=法定+福利，汇总折算，只折算，不参与四舍五入规则[假期设置中的额度四舍五入规则]
                        Map totalMap = new HashMap();
                        totalMap.putAll(legalQuota);
                        totalMap.put("quota_day", totalQuota.floatValue());
                        WaEmpQuota totalNowQuota = getEmpConversionNowQuota(totalMap, date);

                        //法定单独折算，折算规则和额度四舍五入规则根据假期设置的来计算
                        WaEmpQuota legalQuotaUpd = getEmpConversionNowQuota(legalQuota, date);
                        empQuotaUpdList.add(legalQuotaUpd);
                        if (empQuotaIdList.indexOf(legalQuotaUpd.getEmpQuotaId()) < 0) {
                            empQuotaIdList.add(legalQuotaUpd.getEmpQuotaId());
                        }

                        //福利=法定+福利一起折算的值 - 法定折算的值
                        BigDecimal welfareQuotaDay = new BigDecimal(totalNowQuota.getNowQuota()).subtract(new BigDecimal(legalQuotaUpd.getNowQuota()));
                        //福利配额四舍五入规则--向下取整0.5
                        welfareQuotaDay = PayEngineUtil.handleMantissa(welfareQuotaDay.divide(new BigDecimal(0.5)), (short) 3, (short) 0).multiply(new BigDecimal(0.5));

                        WaEmpQuota welfareQuotaUpd = new WaEmpQuota();
                        welfareQuotaUpd.setEmpQuotaId((Integer) welfareQuota.get("emp_quota_id"));
                        welfareQuotaUpd.setNowQuota(welfareQuotaDay.floatValue());
                        empQuotaUpdList.add(welfareQuotaUpd);
                        if (empQuotaIdList.indexOf(welfareQuotaUpd.getEmpQuotaId()) < 0) {
                            empQuotaIdList.add(welfareQuotaUpd.getEmpQuotaId());
                        }
                    } else {
                        for (Map waEmpQuota : alQuotaList) {
                            //配额折算
                            WaEmpQuota empQuota = getEmpConversionNowQuota(waEmpQuota, date);
                            empQuotaUpdList.add(empQuota);
                            if (empQuotaIdList.indexOf(empQuota.getEmpQuotaId()) < 0) {
                                empQuotaIdList.add(empQuota.getEmpQuotaId());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(empQuotaIdList)) {
            //计算并且更新当年调整
            waCommonService.updateEmpAdjustQuota(empId, empQuotaIdList);
        }
        if (CollectionUtils.isNotEmpty(empQuotaUpdList)) {
            importService.fastUpdList(WaEmpQuota.class, "empQuotaId", empQuotaUpdList, Arrays.asList("nowQuota"));
        }
    }

    /**
     * 当前配额折算
     *
     * @param row
     * @param date 指定折算日期
     * @return
     */
    public WaEmpQuota getEmpConversionNowQuota(Map row, Long date) {
        Long now = DateUtil.getCurrentTime(true);
        if (date != null) {
            now = date;
        }
        BigDecimal quotaDay = new BigDecimal((Float) row.get("quota_day"));
        Integer useItType = (Integer) row.get("use_it_type");
        Integer useour2fiveRule = (Integer) row.get("use_four2five_rule");
        Integer acctTimeType = (Integer) row.get("acct_time_type");
        if (useItType != null && !useItType.equals(1)) {
            Long hireDate = (Long) row.get("hire_date");
            Long startDate = (Long) row.get("start_date");
            Long lastDate = (Long) row.get("last_date");
            if (hireDate != null && hireDate > startDate) {
                startDate = hireDate;
            }
            quotaDay = quotaDay.multiply(getConvRate(startDate, lastDate, useItType, startDate, now));
            if (acctTimeType != null && 2 == acctTimeType) {
                quotaDay = quotaDay.divide(new BigDecimal(60));
            }
            quotaDay = handleMantissa(row.get("belong_org_id").toString(), quotaDay, acctTimeType, useour2fiveRule);
        }
        WaEmpQuota empQuota = new WaEmpQuota();
        empQuota.setEmpQuotaId((Integer) row.get("emp_quota_id"));
        empQuota.setNowQuota(quotaDay.floatValue());
        return empQuota;
    }

    /**
     * 处理进位
     *
     * @param val
     * @param unit
     * @param rule
     * @return
     */
    public BigDecimal handleMantissa(String belongId, BigDecimal val, Integer unit, Integer rule) {
        //向上
        if (rule != null) {
            if (rule.equals(2)) {
                val = PayEngineUtil.handleMantissa(val, (short) 2, (short) 0);
            } else if (rule.equals(4)) {
                val = PayEngineUtil.handleMantissa(val.divide(new BigDecimal(0.5)), (short) 2, (short) 0).multiply(new BigDecimal(0.5));
            }
            //向下
            else if (rule.equals(3)) {
                val = PayEngineUtil.handleMantissa(val, (short) 3, (short) 0);
            } //向下
            else if (rule.equals(5)) {
                val = PayEngineUtil.handleMantissa(val.divide(new BigDecimal(0.5)), (short) 3, (short) 0).multiply(new BigDecimal(0.5));
            }
            //四舍五入
            else if (rule.equals(1)) {
                String func = getFuncExp(belongId, "HandleMantissa" + rule);
                if (StringUtils.isNotEmpty(func)) {
                    Map params = new HashMap();
                    params.put("value", val);
                    val = groovyScriptEngine.executeBigDecimal(func, params);
                } else {
                    val = PayEngineUtil.handleMantissa(val, (short) 1, (short) 0);
                }
            }
            //清水--取整规则，0.25 折算逻辑，即小于0.25计0，大于等于0.25小于0.75计0.5，大于等于0.75计1
            else if (rule.equals(6)) {
                if (val.floatValue() % 1 > 0) {
                    String valStr = String.valueOf(val.floatValue());
                    Integer valInt = Integer.valueOf(valStr.substring(0, valStr.lastIndexOf(".")));
                    Float valFloat = Float.valueOf("0" + valStr.substring(valStr.lastIndexOf(".")));
                    if (valFloat < 0.25) {
                        val = new BigDecimal(valInt);
                    } else if (valFloat >= 0.25 && valFloat < 0.75) {
                        val = new BigDecimal(valInt).add(new BigDecimal(0.5));
                    } else if (valFloat >= 0.75) {
                        val = new BigDecimal(valInt).add(new BigDecimal(1));
                    }
                }
            }
        }
        if (unit != null && 2 == unit) {
            val = val.multiply(new BigDecimal(60));
        }
        return val;
    }

    /**
     * 处理进位
     *
     * @param val
     * @param unit
     * @param rule
     * @return
     */
    public BigDecimal handleMantissa2(String belongId, BigDecimal val, Integer unit, Integer rule) {
        if (rule != null) {
            if (rule.equals(1)) {
                //向上取整1
                rule = 2;
            } else if (rule.equals(2)) {
                //向下取整1
                rule = 3;
            } else if (rule.equals(3)) {
                //四舍五入
                rule = 1;
            }
        }
        return handleMantissa(belongId, val, unit, rule);
    }

    public String getFuncExpForNoCache(String belongId, String code) {
        funcCache.clear();
        return getFuncExp(belongId, code);
    }

    /**
     * 取得函数逻辑配置
     *
     * @param belongId
     * @param code
     * @return
     */
    public String getFuncExp(String belongId, String code) {
        String script = "";
        SysFunc func = getFunc(belongId, code);
        if (func != null) {
            if (func.getLogicType().equals("script")) {
                script = func.getLogicExp();
            } else if (func.getLogicType().equals("sql")) {
                script = func.getLogicExp();
                if (StringUtils.isNotEmpty(func.getLogicParams())) {
                    script = MessageFormat.format(script, func.getLogicParams().split(","));
                }
            }
        }
        return script;
    }

    /**
     * 取得函数逻辑配置
     *
     * @param belongId
     * @param code
     * @return
     */
    public SysFunc getFunc(String belongId, String code) {
        SysFunc func = null;
        if (!funcCache.containsKey(belongId + code)) {
            SysFuncExample example = new SysFuncExample();
            example.createCriteria().andBelongOrgIdEqualTo(Integer.valueOf(belongId)).andCodeEqualTo(code).andStatusEqualTo(1);
            List<SysFunc> funcList = sysFuncMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(funcList)) {
                func = funcList.get(0);
                funcCache.put(belongId + code, func);
            }
        } else {
            func = funcCache.get(belongId + code);
        }
        return func;
    }

    /**
     * 执行函数逻辑
     *
     * @return
     */
    public BigDecimal execFunc(SysFunc func, BigDecimal val, Map param) {
        BigDecimal result = null;
        if (func.getLogicType().equals("script")) {
            String script = func.getLogicExp();
            if (StringUtils.isNotEmpty(script)) {
                Map params = new HashMap();
                params.put("value", val);
                if (param != null) {
                    params.putAll(param);
                }
                result = groovyScriptEngine.executeBigDecimal(script, params);
            }
        }
        return result;
    }

    /**
     * 处理折算
     *
     * @return
     */
    public BigDecimal getConvRate(Long start, Long end, Integer type, Long calStart, Long calEnd) {
        if (calEnd < calStart) {
            return new BigDecimal(0);
        }
        //向上
        BigDecimal val = new BigDecimal(1);
        if (type != null) {
            if (type == 2) {
                //按天
                return new BigDecimal(((calEnd - calStart) / 24 / 3600 + 1) / getTotalDays(start, end));
            } else if (type == 3) {
                //按月
                return new BigDecimal(getMonthDiff(calStart, calEnd) * 1.0 / getMonthDiff(start, end));
            } else if (type == 4) {
                //按季
                return new BigDecimal(getQuarterDiff(calStart, calEnd) * 1.0 / getQuarterDiff(start, end));
            } else if (type == 5) {
                //按半年
                BigDecimal rate = new BigDecimal(getMonthDiff(calStart, calEnd) * 1.0 / getMonthDiff(start, end));
                if (rate.floatValue() > 0.5) {
                    return new BigDecimal(1);
                } else {
                    return new BigDecimal(0.5);
                }
            }
        }
        return val;
    }

    /**
     * 处理折算
     *
     * @return
     */
    public BigDecimal getConvRateQuota(Long midDate, Short year, Long calStart, Long calEnd, Integer last, Integer next) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(midDate * 1000);
        cal.set(Calendar.YEAR, year);
        midDate = cal.getTimeInMillis() / 1000;
        BigDecimal half1 = new BigDecimal(((midDate - calStart) / 24 / 3600) * last / getTotalDays(calStart, calEnd));
        BigDecimal half2 = new BigDecimal(((calEnd - midDate) / 24 / 3600 + 1) * next / getTotalDays(calStart, calEnd));

        return half1.add(half2);
    }

    private int getMonthDiff(Long start, Long end) {
        //按季
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(start * 1000);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTimeInMillis(end * 1000);
        int diff = cal2.get(Calendar.MONTH) - cal.get(Calendar.MONTH);
        if (diff < 0) diff += 12;
        return diff + 1;
    }

    private int getQuarterDiff(Long start, Long end) {
        //按季
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(start * 1000);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTimeInMillis(end * 1000);
        int diff = cal2.get(Calendar.MONTH) / 3 - cal.get(Calendar.MONTH) / 3;
        if (diff < 0) diff += 4;
        return diff + 1;
    }

    /**
     * 取得假期配额
     *
     * @param id
     */
    public WaEmpQuota getEmpQuota(Integer id) {
        WaEmpQuota empQuota = waEmpQuotaMapper.selectByPrimaryKey(id);
        if (empQuota.getQuotaSettingId() != null) {
            WaLeaveSetting leaveSetting = waLeaveSettingMapper.selectByPrimaryKey(empQuota.getQuotaSettingId());
            WaLeaveType leaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveSetting.getLeaveTypeId());
            if (leaveType.getAcctTimeType() != null && 2 == leaveType.getAcctTimeType()) {
                if (empQuota.getAdjustQuota() == null) {
                    empQuota.setAdjustQuota(0f);
                }
                if (empQuota.getRemainDay() == null) {
                    empQuota.setRemainDay(0f);
                }
                if (empQuota.getDeductionDay() == null) {
                    empQuota.setDeductionDay(0f);
                }
                if (empQuota.getQuotaDay() == null) {
                    empQuota.setQuotaDay(0f);
                }
                if (empQuota.getUsedDay() == null) {
                    empQuota.setUsedDay(0f);
                }
                if (empQuota.getFixUsedDay() == null) {
                    empQuota.setFixUsedDay(0f);
                }
                empQuota.setAdjustQuota(empQuota.getAdjustQuota() / 60);
                empQuota.setQuotaDay(empQuota.getQuotaDay() / 60);
                empQuota.setRemainDay(empQuota.getRemainDay() / 60);
                empQuota.setUsedDay(empQuota.getUsedDay() / 60);
                empQuota.setDeductionDay(empQuota.getDeductionDay() / 60);
                empQuota.setFixUsedDay(empQuota.getFixUsedDay() / 60);
            }
            if (empQuota.getFixUsedDay() != null) {
                BigDecimal a = new BigDecimal(empQuota.getFixUsedDay());
                BigDecimal b = new BigDecimal(empQuota.getUsedDay() == null ? 0f : empQuota.getUsedDay());
                empQuota.setUsedDay(a.add(b).floatValue());
            }
        }
        return empQuota;
    }

    /**
     * 删除假期配额
     *
     * @param id
     */
    @Transactional
    public void deleteEmpQuota(String belongOrgId, Integer id) {
        WaEmpQuota waEmpQuota = waEmpQuotaMapper.selectByPrimaryKey(id);
        waEmpQuotaMapper.deleteByPrimaryKey(id);
        WaEmpQuotaDetailExample detailExample = new WaEmpQuotaDetailExample();
        detailExample.createCriteria().andEmpQuotaIdEqualTo(id).andBelongOrgIdEqualTo(belongOrgId);
        List<WaEmpQuotaDetail> quotaDetailList = waEmpQuotaDetailMapper.selectByExample(detailExample);
        if (CollectionUtils.isNotEmpty(quotaDetailList)) {
            List<Integer> idList = new ArrayList<>();
            for (WaEmpQuotaDetail detail : quotaDetailList) {
                idList.add(detail.getEmpQuotaDetailId());
            }
            WaEmpQuotaUseExample useExample = new WaEmpQuotaUseExample();
            useExample.createCriteria().andEmpQuotaDetailIdIn(idList);
            waEmpQuotaUseMapper.deleteByExample(useExample);
            waEmpQuotaDetailMapper.deleteByExample(detailExample);
        }
    }

    /**
     * 保存假期配额
     *
     * @param record
     * @return
     */
    @Transactional
    public Integer saveEmpQuota(String belongOrgId, WaEmpQuota record) {
        UserInfo userInfo = getUserInfo();
        String tenantId = userInfo.getTenantId();
        SysEmpInfo sysEmpInfo = sysEmpInfoMapper.selectByPrimaryKey(record.getEmpid());
        if (record.getRemainDay() == null) {
            record.setRemainDay(0f);
        }
        if (record.getDeductionDay() == null) {
            record.setDeductionDay(0f);
        }
        if (record.getQuotaDay() == null) {
            record.setQuotaDay(0f);
        }
        if (record.getUsedDay() == null) {
            record.setUsedDay(0f);
        }
        if (record.getEmpQuotaId() != null && record.getQuotaSettingId() == null) {
            record.setQuotaSettingId(getEmpQuota(record.getEmpQuotaId()).getQuotaSettingId());
        }
        WaLeaveSetting leaveSetting = waLeaveSettingMapper.selectByPrimaryKey(record.getQuotaSettingId());
        WaLeaveType leaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveSetting.getLeaveTypeId());
        if (leaveType.getAcctTimeType() != null && 2 == leaveType.getAcctTimeType()) {
            record.setQuotaDay(record.getQuotaDay() * 60);
            record.setRemainDay(record.getRemainDay() * 60);
            record.setUsedDay(record.getUsedDay() * 60);
            record.setDeductionDay(record.getDeductionDay() * 60);
        }
        if (record.getNowQuota() == null) {
            if (record.getQuotaDay() != null) {
                SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(record.getEmpid());
                //特殊的按月折算逻辑
                SysFunc convRateFunc = getFunc(tenantId, "ConvRate2");
                record.setNowQuota(calNowQuota(leaveType, leaveSetting, empInfo, record, tenantId, convRateFunc, null, true));
            } else {
                record.setNowQuota(0f);
            }
        }
        if (record.getEmpQuotaId() == null) {
            if (record.getUsedDay() != null) {
                record.setFixUsedDay(record.getUsedDay());
                record.setUsedDay(0f);
            }
            record.setCrtuser(userInfo.getUserId());
            record.setCrttime(DateUtil.getCurrentTime(true));
            waEmpQuotaMapper.insertSelective(record);
        } else {
            if (record.getUsedDay() != null) {
                BigDecimal totalUsedDay = new BigDecimal(record.getUsedDay());//本年已使用
                BigDecimal usedDayDec = new BigDecimal(0);//本年系统已使用
                //查询系统已使用
                Map params = new HashMap();
                params.put("belongOrgId", belongOrgId);
                params.put("empQuotaId", record.getEmpQuotaId());
                Map empQuotaMap = waMapper.getEmpQuotaById(params);
                if (empQuotaMap != null) {
                    Float usedDay = (Float) empQuotaMap.get("used_day");
                    if (usedDay != null && usedDay > 0) {
                        usedDayDec = new BigDecimal(usedDay);
                    }
                }
                BigDecimal fixUsedDay = totalUsedDay.subtract(usedDayDec);//本年固定已使用
                record.setFixUsedDay(fixUsedDay.floatValue() < 0 ? 0f : fixUsedDay.floatValue());
                record.setUsedDay(null);
            }
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));

            WaEmpQuota oldQuota = waEmpQuotaMapper.selectByPrimaryKey(record.getEmpQuotaId());
            if (record.getRemainValidDate() == null) {
                waMapper.updateEmpQuotaRemainValidDate(record.getEmpQuotaId());
            }
            waEmpQuotaMapper.updateByPrimaryKeySelective(record);
            //计算并且更新当年调整
            waCommonService.updateEmpAdjustQuota(record.getEmpid(), new ArrayList(Arrays.asList(record.getEmpQuotaId())));
            WaEmpQuota updateBeforeData = waEmpQuotaMapper.selectByPrimaryKey(record.getEmpQuotaId());
        }
        // 配置发生变化，移除缓存里的信息 在开放平台里的获取员工配额信息接口
        String key = DigestUtils.md5DigestAsHex("com.caidao1.api.service.impl.CaidaoOpenApiV18ServiceImpl.getEmployeeHolidayQuota".getBytes()).concat("#").concat(sysEmpInfo.getWorkno());
        redisTemplate.delete(key);
        return record.getEmpQuotaId();
    }

    /**
     * 查询假期配额类型
     *
     * @param empid
     * @return
     */
    public List<Map> getLeaveSettingByEmp(Long empid) {
        UserInfo userInfo = getUserInfo();
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("empid", empid);
        params.put("nowTime", DateUtil.getCurrentTime(true));
        return waConfigMapper.getLeaveSettingByEmp(params);
    }

    /**
     * 配额类型明细查询
     *
     * @param leaveTypeId
     * @returns
     */
    public List<Map> getLeaveQuotaItemList(String belongOrgId, Integer leaveTypeId) {
        List<Map> itemList = new ArrayList<>();

        Map params = new HashMap();
        params.put("belongId", belongOrgId);
        params.put("leaveTypeId", leaveTypeId);
        PageBean pageBean = new PageBean(true);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString("quota_setting_name"));
        List<Map> leaveSettingList = waConfigMapper.getLeaveSettingList(pageBounds, params);
        if (CollectionUtils.isNotEmpty(leaveSettingList)) {
            leaveSettingList.forEach(row -> {
                Integer quota_setting_id = (Integer) row.get("quota_setting_id");
                String quota_setting_name = (String) row.get("quota_setting_name");

                Map item = new HashMap();//本年
                item.put("name", quota_setting_name + "-" + messageResource.getMessage("L006231", new Object[]{}, new Locale(SessionHolder.getLang())));
                item.put("id", quota_setting_id + "_1");
                itemList.add(item);

                Map item1 = new HashMap();//留存
                item1.put("name", quota_setting_name + "-" + messageResource.getMessage("L006503", new Object[]{}, new Locale(SessionHolder.getLang())));
                item1.put("id", quota_setting_id + "_0");
                itemList.add(item1);
            });
        }
        return itemList;
    }


    /**
     * 保存假期配额
     *
     * @param record
     * @return
     */
    @Transactional
    public Integer saveLeaveSetting(WaLeaveSetting record) {
        if (record.getLeaveType() == null) {
            WaLeaveType leaveType = getLeaveType(record.getLeaveTypeId());
            //1法定年假，2福利年假,3病假,4调休
            //1、年假，2病假，3、调休，4、婚假，5、丧假，9、其它
            if (leaveType.getLeaveType() != null) {
                if (leaveType.getLeaveType() == 3) {
                    record.setLeaveType((short) 4);
                } else if (leaveType.getLeaveType() == 2) {
                    record.setLeaveType((short) 3);
                } else {
                    record.setLeaveType(Short.valueOf(String.valueOf(leaveType.getLeaveType())));
                }
            } else {
                record.setLeaveType((short) 9);
            }
        }
        if (record.getFreezingRules() != null && record.getFreezingRules() == 1) {
            //假期冻结规则：试用期冻结,兼容老字段
            record.setIsTrialFreeze(true);
        } else {
            record.setIsTrialFreeze(false);
        }
        UserInfo userInfo = getUserInfo();
        if (record.getQuotaSettingId() == null) {
            record.setBelongOrgid(userInfo.getTenantId());
            record.setCrtuser(userInfo.getUserId());
            record.setCrttime(DateUtil.getCurrentTime(true));
            waLeaveSettingMapper.insertSelective(record);
        } else {
            record.setBelongOrgid(userInfo.getTenantId());
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));
            waLeaveSettingMapper.updateByPrimaryKeySelective(record);
        }
        return record.getQuotaSettingId();
    }

    /**
     * 关联假期类型
     *
     * @param leaveGroupId
     * @param leaveTypeIds
     */
    @Transactional
    public void saveLeaveGroupType(Integer leaveGroupId, String leaveTypeIds) {
        WaLeaveTypeGroupRelExample example = new WaLeaveTypeGroupRelExample();
        example.createCriteria().andLeaveGroupIdEqualTo(leaveGroupId);
        waLeaveTypeGroupRelMapper.deleteByExample(example);
        if (StringUtils.isNotEmpty(leaveTypeIds)) {
            String[] leaveTypeList = leaveTypeIds.split(",");
            for (String leaveTypeId : leaveTypeList) {
                WaLeaveTypeGroupRel waLeaveTypeGroupRel = new WaLeaveTypeGroupRel();
                waLeaveTypeGroupRel.setLeaveGroupId(leaveGroupId);
                waLeaveTypeGroupRel.setLeaveTypeId(Integer.parseInt(leaveTypeId));
                waLeaveTypeGroupRelMapper.insertSelective(waLeaveTypeGroupRel);
            }
        }
    }


    /**
     * 生成假期配额
     *
     * @return
     */
    @Deprecated
    @Transactional
    public void autoGenEmpQuota() {
        try {
            Map params = new HashMap();
            Calendar cal = Calendar.getInstance();
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            cal.setTimeInMillis(df.parse(df.format(DateUtils.addDays(new Date(), -1))).getTime());
            params.put("filter_date", new SimpleDateFormat("MMdd").format(cal.getTime()));
            params.put("filter_year", cal.get(Calendar.YEAR));
            params.put("filter_day", Integer.parseInt(new SimpleDateFormat("Mdd").format(new Date())));
            List<Map> autoGenEmpList = waConfigMapper.getAutoGenEmpList(params);
            Map<String, List<Long>> belongEmpMap = new HashMap<String, List<Long>>();
            if (CollectionUtils.isNotEmpty(autoGenEmpList)) {
                for (Map sysEmpInfo : autoGenEmpList) {
                    String key = sysEmpInfo.get("belong_org_id") + "_" + sysEmpInfo.get("wa_group_id");
                    if (belongEmpMap.containsKey(key)) {
                        List<Long> tmpList = belongEmpMap.get(key);
                        tmpList.add((Long) sysEmpInfo.get("empid"));
                    } else {
                        List<Long> tmpList = new ArrayList<Long>();
                        tmpList.add((Long) sysEmpInfo.get("empid"));
                        belongEmpMap.put(key, tmpList);
                    }
                }
            }

            if (belongEmpMap.size() > 0) {
                Long userId = getUserInfo().getUserId();
                for (String key : belongEmpMap.keySet()) {
                    String[] keys = key.split("_");
                    genEmpQuotaDetail(keys[0], Integer.parseInt(keys[1]), belongEmpMap.get(key), (short) cal.get(Calendar.YEAR), false,
                            DateUtil.getCurrentTime(true), true, false, userId);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String initExp(String exp) throws Exception {
        return exp.replaceAll("1#(" + BaseConst.FIX_EXT_COLUMN + "\\d+)#.*?=", "(case when (ei.ext_custom_col ->> '$1') = '' then null else ei.ext_custom_col ->> '$1' end)::BIGINT=").replaceAll("1#(" + BaseConst.FIX_EXT_COLUMN + "\\d+)#", "(case when (ei.ext_custom_col ->> '$1') = '' then null else ei.ext_custom_col ->> '$1' end)::BIGINT").replaceAll("9#(" + BaseConst.FIX_EXT_COLUMN + "\\d+)#", "(case when (ep.ext_custom_col ->> '$1') = '' then null else ep.ext_custom_col ->> '$1' end)::BIGINT").replaceAll("orgid='(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
    }

    public String changeExp(String exp) throws Exception {
        if (exp.contains("social_city")) {
            String groupExp = exp.substring(exp.indexOf("social_city"));
            String[] split = groupExp.split("'");
            String[] arr = split[1].split(",");
            String sqlStr = split[0] + arr[1];
            exp = exp.replaceAll(groupExp, sqlStr);
        }
        return exp;
    }

    /**
     * 计算员工假期配额时初始化员工时间轴数据
     *
     * @param belongOrgId
     * @param empidList
     * @param startTime
     * @param endTime
     * @throws Exception
     */
    public void initWaEmpInfoRecord(String belongOrgId, List<Long> empidList, Long startTime, Long endTime, Short year) throws Exception {
        //根据时间查询员工信息时间轴对应的数据
        List<SysEmpInfoRecord> empInfoRecordList = sysEmpInfoRecordMapper.listEmpInfoRecord(belongOrgId, empidList, startTime, endTime);
        if (CollectionUtils.isNotEmpty(empInfoRecordList)) {
            List<WaEmpInfoRecord> waEmpInfoRecordList = new ArrayList<>();
            for (SysEmpInfoRecord emp : empInfoRecordList) {
                WaEmpInfoRecord waEmpInfoRecord = new WaEmpInfoRecord();
                BeanUtils.copyProperties(waEmpInfoRecord, emp);

                //根据入职日期拆分
                if (emp.getHireDate() != null) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTimeInMillis(emp.getHireDate() * 1000);

                    Calendar calendar1 = Calendar.getInstance();
                    calendar1.set(year, calendar.get(Calendar.MONTH), calendar.get(Calendar.DATE), 0, 0, 0);

                    Long hireDate = calendar1.getTimeInMillis() / 1000;
                    Long startDate = emp.getStartDate();
                    Long endDate = emp.getEndDate();
                    if (hireDate > startDate && hireDate < endDate) {
                        waEmpInfoRecord.setEndDate(hireDate);
                        waEmpInfoRecordList.add(waEmpInfoRecord);

                        //第二段
                        WaEmpInfoRecord waEmpInfoRecord2 = new WaEmpInfoRecord();
                        BeanUtils.copyProperties(waEmpInfoRecord2, emp);
                        waEmpInfoRecord2.setStartDate(hireDate);
                        waEmpInfoRecordList.add(waEmpInfoRecord2);
                    } else {
                        waEmpInfoRecordList.add(waEmpInfoRecord);
                    }
                } else {
                    waEmpInfoRecordList.add(waEmpInfoRecord);
                }
            }
            //根据首次参公日期拆分
            List<WaEmpInfoRecord> list = new ArrayList<>();
            for (WaEmpInfoRecord emp : waEmpInfoRecordList) {
                if (emp.getFirstWorkDate() != null && !emp.getFirstWorkDate().equals(emp.getHireDate())) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTimeInMillis(emp.getFirstWorkDate() * 1000);

                    Calendar calendar1 = Calendar.getInstance();
                    calendar1.set(year, calendar.get(Calendar.MONTH), calendar.get(Calendar.DATE), 0, 0, 0);

                    Long firstWorkDate = calendar1.getTimeInMillis() / 1000;
                    Long startDate = emp.getStartDate();
                    Long endDate = emp.getEndDate();

                    if (firstWorkDate > startDate && firstWorkDate < endDate) {
                        WaEmpInfoRecord waEmpInfoRecord = new WaEmpInfoRecord();
                        BeanUtils.copyProperties(waEmpInfoRecord, emp);

                        waEmpInfoRecord.setEndDate(firstWorkDate);
                        list.add(waEmpInfoRecord);

                        //第二段
                        WaEmpInfoRecord waEmpInfoRecord2 = new WaEmpInfoRecord();
                        BeanUtils.copyProperties(waEmpInfoRecord2, emp);
                        waEmpInfoRecord2.setStartDate(firstWorkDate);
                        list.add(waEmpInfoRecord2);
                    }
                }
            }
            waEmpInfoRecordList.addAll(list);
            //删除之前计算的历史数据
            WaEmpInfoRecordExample empInfoRecordExample = new WaEmpInfoRecordExample();
            empInfoRecordExample.createCriteria().andBelongOrgIdEqualTo(belongOrgId).andEmpidIn(empidList);
            int delCount = waEmpInfoRecordMapper.deleteByExample(empInfoRecordExample);
            log.info("计算员工假期配额时初始化员工时间轴数据，员工历史数据删除条数：" + delCount);
            importService.fastInsertList(WaEmpInfoRecord.class, "waEmpRecId", waEmpInfoRecordList);
        }
    }

    /**
     * 根据员工信息时间轴数据折算员工假期配额
     *
     * @param belongId
     * @param empIdList
     * @param groupExp
     * @param leaveQuota
     * @param ruleMap
     * @param startTime
     * @param endTime
     * @throws Exception
     */
    public void getEmpLeaveQuotaByConvertRule(String belongId, List<Long> empIdList, String groupExp,
                                              WaLeaveQuota leaveQuota, Map<Long, List<LeaveQuotaDto>> ruleMap, Long startTime, Long endTime) throws Exception {
        //根据配额规则查询符合此规则的人员数据
        Map<String, Object> params = new HashMap<>();
        params.put("belongId", belongId);
        params.put("groupExp", groupExp);
        params.put("empIdList", empIdList);
        params.put("quotaSettingId", leaveQuota.getQuotaSettingId());
        List<Map> empList = waEmpInfoRecordMapper.listWaEmpInfoRecordByQuotaRule(params);
        if (CollectionUtils.isNotEmpty(empList)) {
            Map<Long, List<Map>> empGroupMap = new HashMap<>();
            empList.forEach(emp -> {
                Long empid = (Long) emp.get("empid");
                if (empGroupMap.containsKey(empid)) {
                    List<Map> personList = empGroupMap.get(empid);
                    personList.add(emp);
                    empGroupMap.put(empid, personList);
                } else {
                    List<Map> personList = new ArrayList<>();
                    personList.add(emp);
                    empGroupMap.put(empid, personList);
                }
            });
            //计算配额有效期总的有效天数
            int maxDay = DateUtilExt.getDifferenceDay(startTime, endTime) + 1;
            for (Map.Entry<Long, List<Map>> entry : empGroupMap.entrySet()) {
                Long empId = entry.getKey();
                List<Map> empInfoList = entry.getValue();
                BigDecimal quotaVa = new BigDecimal(leaveQuota.getQuotaVal());

                empInfoList.sort(Comparator.comparing(o -> Long.valueOf(o.get("startDate").toString())));

                BigDecimal realQuotaVa = new BigDecimal(0);
                for (Map map : empInfoList) {
                    Long startDate = (Long) map.get("startDate");
                    Long endDate = (Long) map.get("endDate");
                    if (startDate <= startTime) {
                        startDate = startTime;
                    }
                    if (endDate > endTime) {
                        endDate = endTime + 86400;
                    }
                    int effectiveDay = DateUtilExt.getDifferenceDay(startDate, endDate);

                    log.info(DateUtil.getDateStrByTimesamp(startDate) + "," + DateUtil.getDateStrByTimesamp(endDate) + "," + effectiveDay);

                    realQuotaVa = realQuotaVa.add(new BigDecimal(effectiveDay * quotaVa.floatValue() / maxDay));
                }

                if (ruleMap.containsKey(empId)) {
                    List<LeaveQuotaDto> list = ruleMap.get(empId);
                    boolean exist = false;
                    for (LeaveQuotaDto row : list) {
                        if (row.getQuotaSettingId().equals(leaveQuota.getQuotaSettingId())) {
                            BigDecimal oldQuotaVal = row.getQuotaVal();
                            oldQuotaVal = oldQuotaVal.add(realQuotaVa);
                            row.setQuotaVal(oldQuotaVal);
                            exist = true;
                            break;
                        }
                    }
                    if (!exist) {
                        LeaveQuotaDto leaveQuotaDto = new LeaveQuotaDto();
                        leaveQuotaDto.setQuotaId(leaveQuota.getQuotaId());
                        leaveQuotaDto.setLeaveType(leaveQuota.getLeaveType());
                        leaveQuotaDto.setQuotaSettingId(leaveQuota.getQuotaSettingId());
                        leaveQuotaDto.setQuotaVal(realQuotaVa);
                        list.add(leaveQuotaDto);
                        ruleMap.put(empId, list);
                    }
                } else {
                    List<LeaveQuotaDto> list = new ArrayList<>();
                    LeaveQuotaDto leaveQuotaDto = new LeaveQuotaDto();
                    leaveQuotaDto.setQuotaId(leaveQuota.getQuotaId());
                    leaveQuotaDto.setLeaveType(leaveQuota.getLeaveType());
                    leaveQuotaDto.setQuotaSettingId(leaveQuota.getQuotaSettingId());
                    leaveQuotaDto.setQuotaVal(realQuotaVa);
                    list.add(leaveQuotaDto);
                    ruleMap.put(empId, list);
                }
            }
        }
    }

    /**
     * 生成假期配额
     *
     * @param belongId
     * @param waGroupId
     * @param empIds
     * @param year
     * @param all
     * @param nowTime
     * @param isCarryForward 是否结转到次年
     * @param isReCalQuota   是否重新扣减请假单
     * @throws Exception
     */
    public void genEmpQuotaDetail(String belongId, Integer waGroupId, List<Long> empIds, Short year, boolean all,
                                  Long nowTime, boolean isCarryForward, boolean isReCalQuota, Long userId) throws Exception {
        List<WaEmpQuota> empQuotaAddList = new ArrayList<>();
        List<WaEmpQuota> empQuotaUpdList = new ArrayList<>();
        List<WaEmpQuota> lastYearEmpQuotaUpdList = new ArrayList<>();//上年配额修改集合
        Map<Long, SysEmpInfo> empInfoMap = new HashMap<>();

        WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waGroupId);
        WaLeaveSettingExample leaveSettingExample = new WaLeaveSettingExample();
        WaLeaveSettingExample.Criteria criteria = leaveSettingExample.createCriteria();
        criteria.andBelongOrgidEqualTo(belongId).andLeaveTypeIdIn(Arrays.asList((Integer[]) waGroup.getLeaveTypeIds()));
        //开启调休配额按照年份去生成维护
        String genQuotaByYear = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongId + RedisKeyDefine.IF_GEN_TXQUOTA_BY_YEAR);
        if (!"1".equals(genQuotaByYear)) {
            //排除调休
            criteria.andLeaveTypeNotEqualTo((short) 4);
        }
        List<WaLeaveSetting> leaveSettingList = waLeaveSettingMapper.selectByExample(leaveSettingExample);
        List<Integer> quotaSettingIdList = new ArrayList<>();
        Map<Integer, WaLeaveSetting> leaveSettingMap = new HashMap<>();
        for (WaLeaveSetting leaveSetting : leaveSettingList) {
            leaveSettingMap.put(leaveSetting.getQuotaSettingId(), leaveSetting);
            quotaSettingIdList.add(leaveSetting.getQuotaSettingId());
        }

        funcCache.clear();

        //年假汇总折算-杰尼亚定制逻辑，目前没想明白怎么拆解出来，暂时冗余在代码里
        //判断是否开启年假汇总折算开关，如果开启了年假汇总折算，则员工年假配额单独做折算处理
        boolean alConversionFlag = false;
        String isOpenALQuotaSumConversion = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongId + RedisKeyDefine.IS_OPEN_AL_QUOTA_SUMCONVERSION);
        if ("1".equals(isOpenALQuotaSumConversion)) {
            alConversionFlag = true;
        }
        //上年留存是否继续结转至次年
        boolean ifCarryforwardRemainQuota = false;
        String remainConfig = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongId + RedisKeyDefine.IF_CARRYFORWARD_REMAIN_QUOTA);
        if ("1".equals(remainConfig)) {
            ifCarryforwardRemainQuota = true;
        }
        Map quotaParams = new HashMap();
        quotaParams.put("belongId", belongId);
        quotaParams.put("quotaSettingList", StringUtils.join(quotaSettingIdList, ","));
        List<WaLeaveQuota> leaveQuotaList = waLeaveQuotaMapper.getGenQuotaList(quotaParams);
        Map<Integer, WaLeaveType> leaveTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(leaveQuotaList)) {
            SysFunc jobAgeFunc = getFunc(belongId, "job_age");
            SysFunc corpAgeFunc = getFunc(belongId, "corp_age");
            String jobAgeSql = getFuncExp(belongId, "job_age");
            String corpAgeSql = getFuncExp(belongId, "corp_age");
            //特殊的按月折算逻辑
            SysFunc convRateFunc = getFunc(belongId, "ConvRate2");
            //首次参公日期取值逻辑
            String firstWorkDateSql = getFuncExp(belongId, "first_work_date");

            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            //计算配额年份对应的开始结束时间
            Calendar yearCalendar = Calendar.getInstance();
            yearCalendar.set(year, 0, 1, 0, 0, 0);
            Long yearStartDate = yearCalendar.getTime().getTime() / 1000;
            Long yearEndDate = DateUtilExt.getYearsEndTime(yearStartDate);

            //开启员工信息字段联动折算（例如：职级联动折算）
            String isOpenConvert = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongId + RedisKeyDefine.OPEN_CONVERT_WHEN_GEN_EMPQUOTA);
            if ("1".equals(isOpenConvert)) {
                //初始化员工历史数据
                initWaEmpInfoRecord(belongId, empIds, yearStartDate, yearEndDate, year);
            }

            Map<Long, List<LeaveQuotaDto>> ruleMap = new HashMap<>();
            Map<String, LeaveQuotaDto> empLeaveQuotaMap = new HashMap<>();
//                String isEmpGroup = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongId + RedisKeyDefine.EMP_GROUP_QUOTA);
            String isEmpGroup = "1";
            for (WaLeaveQuota leaveQuota : leaveQuotaList) {
                if (leaveQuota.getQuotaSettingId() != null) {
                    boolean isCorpAge = false;
                    boolean isJobAge = false;
                    StringBuffer sb = new StringBuffer();
                    if ("1".equals(isEmpGroup)) {
                        String execSql = "";
                        if (leaveQuota.getEmpGroupId() != null) {
                            PayEmpGroup empGroup = empGroupMapper.selectByPrimaryKey(leaveQuota.getEmpGroupId());
                            if (empGroup != null) {
                                execSql = empGroup.getGroupExp();
                            }
                        } else if (leaveQuota.getEmpGroupJsonb() != null) {
                            PGobject pGobject = (PGobject) leaveQuota.getEmpGroupJsonb();
                            if (StringUtils.isNotBlank(pGobject.getValue())) {
                                Map<String, Object> stringObjectMap = JSONUtils.convertPGobjectToMap(pGobject);
                                if (MapUtils.isNotEmpty(stringObjectMap)) {
                                    execSql = (String) stringObjectMap.get("group_exp");
                                }
                            }
                        }
                        if (StringUtils.isBlank(execSql)) {
                            log.info("员工分组表达式为空！");
                            continue;
                        }

                        if (execSql.contains("job_age")) {
                            isJobAge = true;
                        } else if (execSql.contains("corp_age")) {
                            isCorpAge = true;
                        }

                        if (StringUtils.isNotEmpty(jobAgeSql)) {
                            execSql = initExp(execSql.replaceAll("job_age", jobAgeSql));
                        } else {
                            execSql = execSql.replaceAll("job_age", "CASE WHEN first_work_date IS NULL THEN COALESCE (ep.service_years, 0) + EXTRACT ( YEAR FROM age( to_timestamp(#{nowTime}), to_timestamp(hire_date))) + CAST ( EXTRACT ( MONTH FROM age( to_timestamp(#{nowTime}), to_timestamp(hire_date))) / 12 AS DECIMAL (10, 1)) ELSE EXTRACT ( YEAR FROM age( to_timestamp(#{nowTime}), to_timestamp(first_work_date))) + CAST ( EXTRACT ( MONTH FROM age( to_timestamp(#{nowTime}), to_timestamp(first_work_date))) / 12 AS DECIMAL (10, 1)) END");
                        }

                        if (StringUtils.isNotEmpty(corpAgeSql)) {
                            execSql = initExp(execSql.replaceAll("corp_age", corpAgeSql));
                        } else {
                            execSql = execSql.replaceAll("corp_age", "extract(year from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))+cast(extract(month from age(to_timestamp(#{nowTime}),to_timestamp(hire_date)))/12  as  decimal(10, 1))");
                        }

                        execSql = initExp(execSql);
                        execSql = changeExp(execSql);
                        sb.append("and (" + execSql + ")");
                    } else {
                        WaQuotaSegmentRelExample quotaSegmentRelExample = new WaQuotaSegmentRelExample();
                        quotaSegmentRelExample.createCriteria().andQuotaIdEqualTo(leaveQuota.getQuotaId());
                        List<WaQuotaSegmentRel> relList = waQuotaSegmentRelMapper.selectByExample(quotaSegmentRelExample);
                        for (WaQuotaSegmentRel rel : relList) {
                            WaLeaveSegment leaveSegment = waLeaveSegmentMapper.selectByPrimaryKey(rel.getSegmentId());
                            if (leaveSegment != null) {
                                String colName = leaveSegment.getColName();
                                if (leaveSegment.getColNameView().equals("job_age") && StringUtils.isNotEmpty(jobAgeSql)) {
                                    colName = jobAgeSql;
                                    colName = initExp(colName);
                                    isJobAge = true;
                                } else if (leaveSegment.getColNameView().equals("corp_age") && StringUtils.isNotEmpty(corpAgeSql)) {
                                    colName = corpAgeSql;
                                    colName = initExp(colName);
                                    isCorpAge = true;
                                }

                                String operator = rel.getOperator();
                                if ("[]".equals(operator)) {
                                    sb.append(" and " + colName + " between '" + rel.getStartVal() + "' and '" + rel.getEndVal() + "'");
                                } else if ("[)".equals(operator)) {
                                    sb.append(" and " + colName + " >= '" + rel.getStartVal() + "' and " + colName + " < '" + rel.getEndVal() + "'");
                                } else if ("()".equals(operator)) {
                                    sb.append(" and " + colName + " > '" + rel.getStartVal() + "' and " + colName + " < '" + rel.getEndVal() + "'");
                                } else if ("(]".equals(operator)) {
                                    sb.append(" and " + colName + " > '" + rel.getStartVal() + "' and " + colName + " <= '" + rel.getEndVal() + "'");
                                } else if ("=".equals(operator)) {
                                    sb.append(" and " + colName + operator + "'" + rel.getStartVal() + "'");
                                } else {
                                    sb.append(" and " + colName + operator + "'" + rel.getStartVal() + "'");
                                }
                            }
                        }
                    }
                    if (StringUtils.isNotEmpty(sb)) {
                        Map params = new HashMap();
                        params.put("belongId", belongId);
                        WaLeaveSetting leaveSetting = leaveSettingMap.get(leaveQuota.getQuotaSettingId());

                        if (BooleanUtils.isTrue(leaveSetting.getIfConvert()) && "1".equals(isOpenConvert)) {
                            //开启职级联动配额折算逻辑
                            String nowTimeStr = "to_char(to_timestamp(ei.end_date) - INTERVAL '1 d', 'yyyyMMdd'), 'yyyyMMdd'";
                            String groupExp = sb.toString().replaceAll("#\\{nowTime\\}", nowTimeStr).replaceAll("#nowTime#", nowTimeStr).
                                    replaceAll("#\\{nowhireDate\\}", nowTimeStr).replaceAll("#nowhireDate#", nowTimeStr);
                            groupExp = replaceGroupExp(groupExp);
                            getEmpLeaveQuotaByConvertRule(belongId, empIds, groupExp, leaveQuota, ruleMap, yearStartDate, yearEndDate);
                        } else {
                            if (convRateFunc != null && (leaveSetting.getIfConvert() == null || BooleanUtils.isTrue(leaveSetting.getIfConvert()))) {
                                //额度折算
                                String[] logicParams;
                                String midDate = "hire_date";
                                if (isJobAge) {
                                    if ("1".equals(isEmpGroup)) {
                                        midDate = "first_work_date";
                                    } else {
                                        logicParams = jobAgeFunc.getLogicParams().split(",");
                                        midDate = initExp(logicParams[0]);
                                    }
                                    if (StringUtils.isNotBlank(firstWorkDateSql) && midDate.equals("first_work_date")) {
                                        midDate = firstWorkDateSql;
                                    }
                                } else if (isCorpAge) {
                                    if ("1".equals(isEmpGroup)) {
                                        midDate = "hire_date";
                                    } else {
                                        logicParams = corpAgeFunc.getLogicParams().split(",");
                                        midDate = initExp(logicParams[0]);
                                    }
                                }
                                String firstDate = "hire_date";
//                                    String nowTimeStr1 = "to_char(to_timestamp( '" + year + "' || to_char(to_timestamp(#nowTime#), 'MMdd'), 'yyyyMMdd' ) - INTERVAL '1 d', 'yyyyMMdd'), 'yyyyMMdd'";
//                                    String nowTimeStr2 = "'" + year + "' || to_char(to_timestamp(#nowTime#), 'MMdd'), 'yyyyMMdd'";
                                String nowTimeStr1 = "to_char(to_timestamp( datestr_to_ymdtimestamp('" + year + "' || to_char(to_timestamp(#nowTime#), 'MMdd'))) - INTERVAL '1 d', 'yyyyMMdd'), 'yyyyMMdd'";
                                String nowTimeStr2 = "datestr_to_ymdtimestamp('" + year + "' || to_char(to_timestamp(#nowTime#), 'MMdd'))";
                                for (String nowTimeStdr : new String[]{nowTimeStr1, nowTimeStr2}) {
                                    String nowhireDateStdr = nowTimeStdr.replaceAll("#nowTime#", "hire_date");
                                    nowTimeStdr = nowTimeStdr.replaceAll("#nowTime#", midDate);
                                    String groupExp = sb.toString().replaceAll("#\\{nowTime\\}", nowTimeStdr).replaceAll("#nowTime#", nowTimeStdr).
                                            replaceAll("#\\{nowhireDate\\}", nowhireDateStdr).replaceAll("#nowhireDate#", nowhireDateStdr);
                                    params.put("groupExp", replaceGroupExp(groupExp));
                                    params.put("quotaSettingId", leaveQuota.getQuotaSettingId());
                                    params.put("midDate", midDate);
                                    params.put("firstDate", firstDate);
                                    List<Map> quotaRuleEmpList = waConfigMapper.getQuotaRuleEmpList(params);
                                    for (Map empRow : quotaRuleEmpList) {
                                        Long empId = (Long) empRow.get("empid");
                                        if (empIds.contains(empId)) {
                                            if (ruleMap.containsKey(empId)) {
                                                List<LeaveQuotaDto> list = ruleMap.get(empId);
                                                boolean add = true;
                                                for (LeaveQuotaDto row : list) {
                                                    if (row.getQuotaSettingId().equals(leaveQuota.getQuotaSettingId())) {
                                                        add = false;
                                                        if (row.getQuotaVal().intValue() != leaveQuota.getQuotaVal() && empRow.get("firstDate") != null) {
                                                            int min = Math.min(row.getQuotaVal().intValue(), leaveQuota.getQuotaVal());
                                                            int max = Math.max(row.getQuotaVal().intValue(), leaveQuota.getQuotaVal());
                                                            Object midDateValue = empRow.get("midDate");
                                                            Object firstDateValue = empRow.get("firstDate");
                                                            if (midDateValue == null) {
                                                                midDateValue = firstDateValue;
                                                            }
                                                            Map param = new HashMap();
                                                            param.put("midDate", midDateValue);
                                                            param.put("firstDate", firstDateValue);
                                                            param.put("year", year);
                                                            param.put("last", min);
                                                            param.put("next", max);
                                                            //以下参数考虑到存在配额随条件递减的场景，例如：司龄<20，配额为 10，司龄>=20，配额为5
                                                            param.put("lastLeaveQuota", row);
                                                            param.put("nextLeaveQuota", leaveQuota);

                                                            //BigDecimal val = getConvRate3((Long) empRow.get("midDate"), (Long) empRow.get("firstDate"), year, min, max);
                                                            //BigDecimal val = getConvRate((Long) empRow.get("midDate"), (Long) empRow.get("firstDate"), year, min, max);
                                                            BigDecimal val = execFunc(convRateFunc, new BigDecimal(leaveQuota.getQuotaVal()), param);
                                                            if (val != null) {
                                                                row.setQuotaVal(val);
                                                            } else {
                                                                row.setCrossParams(param);
                                                            }
                                                            row.setCross(true);
                                                            break;
                                                        }
                                                    }
                                                }
                                                if (add) {
                                                    LeaveQuotaDto leaveQuotaDto = new LeaveQuotaDto();
                                                    leaveQuotaDto.setQuotaId(leaveQuota.getQuotaId());
                                                    leaveQuotaDto.setLeaveType(leaveQuota.getLeaveType());
                                                    leaveQuotaDto.setQuotaSettingId(leaveQuota.getQuotaSettingId());
                                                    leaveQuotaDto.setQuotaVal(new BigDecimal(leaveQuota.getQuotaVal()));
                                                    list.add(leaveQuotaDto);
                                                }
                                            } else {
                                                List<LeaveQuotaDto> list = new ArrayList<LeaveQuotaDto>();
                                                LeaveQuotaDto leaveQuotaDto = new LeaveQuotaDto();
                                                leaveQuotaDto.setQuotaId(leaveQuota.getQuotaId());
                                                leaveQuotaDto.setLeaveType(leaveQuota.getLeaveType());
                                                leaveQuotaDto.setQuotaSettingId(leaveQuota.getQuotaSettingId());
                                                leaveQuotaDto.setQuotaVal(new BigDecimal(leaveQuota.getQuotaVal()));
                                                list.add(leaveQuotaDto);
                                                ruleMap.put(empId, list);
                                            }
                                        }
                                    }
                                }
                            } else {
                                //额度不折算
                                String nowTimeStr = nowTime.toString();
                                //周期类型1:自然年2:合同年
                                if (leaveSetting != null && leaveSetting.getQuotaPeriodType() != null) {
                                    Calendar cal = Calendar.getInstance();
                                    cal.setTimeInMillis(df.parse(df.format(nowTime * 1000)).getTime());

                                    if (leaveSetting.getQuotaPeriodType() == 1) {
                                        int month = leaveSetting.getStartDate().intValue() / 100;
                                        int day = leaveSetting.getStartDate().intValue() % 100;
                                        cal.set(month == 1 && day == 1 ? year : year + 1, leaveSetting.getEndDate().intValue() / 100 - 1, leaveSetting.getEndDate().intValue() % 100, 23, 59, 59);
                                        nowTimeStr = String.valueOf(cal.getTimeInMillis() / 1000);
                                    } else if (leaveSetting.getQuotaPeriodType() == 2) {
                                        //nowTimeStr = "EXTRACT (YEAR FROM now()) || to_char(to_timestamp(hire_date), 'mmdd'),'yyyymmdd'";
                                    }
                                }

                                String groupExp = sb.toString().replaceAll("#\\{nowTime\\}", nowTimeStr).replaceAll("#nowTime#", nowTimeStr).
                                        replaceAll("#\\{nowhireDate\\}", nowTimeStr).replaceAll("#nowhireDate#", nowTimeStr);
                                ;
                                params.put("groupExp", replaceGroupExp(groupExp));
                                params.put("quotaSettingId", leaveQuota.getQuotaSettingId());
                                params.put("empIdList", empIds);
                                List<Map> quotaRuleEmpList = waConfigMapper.getQuotaRuleEmpList(params);
                                for (Map empRow : quotaRuleEmpList) {
                                    Long empId = (Long) empRow.get("empid");
                                    if (empIds.contains(empId)) {
                                        if (ruleMap.containsKey(empId)) {
                                            List<LeaveQuotaDto> list = ruleMap.get(empId);
                                            boolean exist = false;
                                            for (LeaveQuotaDto row : list) {
                                                if (row.getQuotaSettingId().equals(leaveQuota.getQuotaSettingId())) {
                                                    exist = true;
                                                    break;
                                                }
                                            }
                                            if (!exist) {
                                                LeaveQuotaDto leaveQuotaDto = new LeaveQuotaDto();
                                                leaveQuotaDto.setQuotaId(leaveQuota.getQuotaId());
                                                leaveQuotaDto.setLeaveType(leaveQuota.getLeaveType());
                                                leaveQuotaDto.setQuotaSettingId(leaveQuota.getQuotaSettingId());
                                                leaveQuotaDto.setQuotaVal(new BigDecimal(leaveQuota.getQuotaVal()));
                                                list.add(leaveQuotaDto);
                                                ruleMap.put(empId, list);
                                            }
                                        } else {
                                            List<LeaveQuotaDto> list = new ArrayList<LeaveQuotaDto>();
                                            LeaveQuotaDto leaveQuotaDto = new LeaveQuotaDto();
                                            leaveQuotaDto.setQuotaId(leaveQuota.getQuotaId());
                                            leaveQuotaDto.setLeaveType(leaveQuota.getLeaveType());
                                            leaveQuotaDto.setQuotaSettingId(leaveQuota.getQuotaSettingId());
                                            leaveQuotaDto.setQuotaVal(new BigDecimal(leaveQuota.getQuotaVal()));
                                            list.add(leaveQuotaDto);
                                            ruleMap.put(empId, list);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            //查询已生成的本年&上一年配额
            WaEmpQuotaExample empQuotaExample = new WaEmpQuotaExample();
            empQuotaExample.createCriteria().andPeriodYearEqualTo(year).andEmpidIn(empIds);
            List<WaEmpQuota> curYearQuotaList = waEmpQuotaMapper.selectByExample(empQuotaExample);
            Map<String, WaEmpQuota> empQuotaMap = new HashedMap();
            if (CollectionUtils.isNotEmpty(curYearQuotaList)) {
                for (WaEmpQuota empQuota : curYearQuotaList) {
                    empQuotaMap.put(year + "_" + empQuota.getEmpid() + "_" + empQuota.getQuotaSettingId(), empQuota);
                }
            }

            empQuotaExample = new WaEmpQuotaExample();
            empQuotaExample.createCriteria().andPeriodYearEqualTo((short) (year - 1)).andEmpidIn(empIds);
            List<WaEmpQuota> lastYearQuotaList = waEmpQuotaMapper.selectByExample(empQuotaExample);
            if (CollectionUtils.isNotEmpty(lastYearQuotaList)) {
                for (WaEmpQuota empQuota : lastYearQuotaList) {
                    empQuotaMap.put((year - 1) + "_" + empQuota.getEmpid() + "_" + empQuota.getQuotaSettingId(), empQuota);
                }
            }

            groovyScriptEngine.clearCache();
            Map<String, Float> empQuotaMaxMap = new HashMap<>();
            for (Long empid : empIds) {
                List<LeaveQuotaDto> quotaList = ruleMap.get(empid);
                if (quotaList != null) {
                    for (LeaveQuotaDto lq : quotaList) {
                        WaLeaveSetting leaveSetting = leaveSettingMap.get(lq.getQuotaSettingId());
                        if (leaveSetting != null) {
                            empLeaveQuotaMap.put(empid + "_" + leaveSetting.getQuotaSettingId(), lq);

                            if (lq.getQuotaSettingId() != null && lq.getQuotaSettingId().equals(leaveSetting.getQuotaSettingId())) {
                                WaEmpQuota empQuota = new WaEmpQuota();
                                empQuota.setQuotaDay(0f);
                                empQuota.setOriginalQuotaDay(lq.getQuotaVal().floatValue());
                                empQuota.setQuotaDay(lq.getQuotaVal().floatValue());

                                String key = year + "_" + empid + "_" + leaveSetting.getQuotaSettingId();
                                if (empQuotaMap.containsKey(key)) {
                                    empQuota.setEmpQuotaId(empQuotaMap.get(key).getEmpQuotaId());
                                    empQuota.setRemainDay(empQuotaMap.get(key).getRemainDay());
                                }
                                empQuota.setEmpid(empid);
                                empQuota.setPeriodYear(year);
                                empQuota.setQuotaSettingId(leaveSetting.getQuotaSettingId());
                                if (all || !empQuotaMap.containsKey(key)) {
                                    //周期类型1:自然年2:合同年3:自定义
                                    if (leaveSetting.getQuotaPeriodType() != null) {
                                        Calendar cal = Calendar.getInstance();
                                        cal.setTimeInMillis(df.parse(df.format(cal.getTime())).getTime());
                                        if (leaveSetting.getQuotaPeriodType().equals(1)) {
                                            int month = leaveSetting.getStartDate().intValue() / 100;
                                            int day = leaveSetting.getStartDate().intValue() % 100;
                                            cal.set(year, month - 1, day);
                                            empQuota.setStartDate(cal.getTimeInMillis() / 1000);
                                            cal.set(month == 1 && day == 1 ? year : year + 1, leaveSetting.getEndDate().intValue() / 100 - 1, leaveSetting.getEndDate().intValue() % 100, 23, 59, 59);
                                            empQuota.setLastDate(cal.getTimeInMillis() / 1000);
                                        } else if (leaveSetting.getQuotaPeriodType().equals(2)) {
                                            if (!empInfoMap.containsKey(empid)) {
                                                SysEmpInfo record = sysEmpInfoMapper.selectByPrimaryKey(empid);
                                                empInfoMap.put(empid, record);
                                            }
                                            SysEmpInfo empInfo = empInfoMap.get(empid);

                                            if (empInfo != null && empInfo.getHireDate() != null) {
                                                Calendar hireDate = Calendar.getInstance();
                                                hireDate.setTimeInMillis(empInfo.getHireDate() * 1000);
                                                if (hireDate.get(Calendar.MONTH) * 100 + hireDate.get(Calendar.DATE) > cal.get(Calendar.MONTH) * 100 + cal.get(Calendar.DATE)) {
                                                    empQuota.setPeriodYear((short) (year - 1));
                                                    cal.set(year - 1, hireDate.get(Calendar.MONTH), hireDate.get(Calendar.DATE));
                                                    //@date 2021/07/12 20:18
                                                    //合同年，不在有效期范围内，生成前一年次的配额，若已有配额，则更新
                                                    String preKey = empQuota.getPeriodYear() + "_" + empid + "_" + leaveSetting.getQuotaSettingId();
                                                    if (empQuotaMap.containsKey(preKey)) {
                                                        empQuota.setEmpQuotaId(empQuotaMap.get(preKey).getEmpQuotaId());
                                                        empQuota.setRemainDay(empQuotaMap.get(preKey).getRemainDay());
                                                    }
                                                } else {
                                                    cal.set(year, hireDate.get(Calendar.MONTH), hireDate.get(Calendar.DATE));
                                                }
                                                empQuota.setStartDate(cal.getTimeInMillis() / 1000);
                                                empQuota.setLastDate(DateUtils.addYears(cal.getTime(), 1).getTime() / 1000 - 1);
                                            }
                                        }
                                        if (isCarryForward) {
                                            //自动结转，上年剩余配额全部结转过来，暂时不结转调整的配额（无方案）
                                            //结转方式1：结转2付现3作废
                                            if (leaveSetting.getCarryOverType().equals(1)) {
                                                String key2 = (year - 1) + "_" + empid + "_" + leaveSetting.getQuotaSettingId();
                                                if (empQuotaMap.containsKey(key2)) {
                                                    //查找上一年配额详情
                                                    WaEmpQuota waEmpQuota = empQuotaMap.get(key2);
                                                    //计算留存有效剩余
                                                    BigDecimal remainSurplus = new BigDecimal(0);
                                                    if (ifCarryforwardRemainQuota && waEmpQuota.getRemainValidDate() != null && waEmpQuota.getRemainValidDate() >= DateUtil.getOnlyDate() &&
                                                            waEmpQuota.getRemainValidDate() >= empQuota.getStartDate()) {
                                                        BigDecimal remainDay = covertNull2Zero2(waEmpQuota.getRemainDay());
                                                        BigDecimal remainUsedDay = covertNull2Zero2(waEmpQuota.getRemainUsedDay());
                                                        remainSurplus = remainDay.subtract(remainUsedDay);
                                                    }
                                                    BigDecimal remain_day = covertNull2Zero2(waEmpQuota.getQuotaDay()).add(remainSurplus).subtract(covertNull2Zero2(waEmpQuota.getDeductionDay()))
                                                            .subtract(covertNull2Zero2(waEmpQuota.getUsedDay())).subtract(covertNull2Zero2(waEmpQuota.getFixUsedDay()));

                                                    //留存最大结转额度
                                                    if (leaveSetting.getMaxCarryOverDay() != null && remain_day.intValue() > leaveSetting.getMaxCarryOverDay()) {
                                                        empQuota.setRemainDay(leaveSetting.getMaxCarryOverDay().floatValue());
                                                    } else {
                                                        empQuota.setRemainDay(remain_day.floatValue());
                                                    }

                                                    //更新上年配额结转状态
                                                    WaEmpQuota lastYearQuota = new WaEmpQuota();
                                                    lastYearQuota.setEmpQuotaId(waEmpQuota.getEmpQuotaId());
                                                    lastYearQuota.setUpdtime(DateUtil.getCurrentTime(true));
                                                    lastYearQuota.setUpduser(userId);
                                                    lastYearQuota.setIfCarryForward(true);
                                                    lastYearEmpQuotaUpdList.add(lastYearQuota);
                                                }
                                            }
                                            //设置结转有效期
                                            if (empQuota.getRemainDay() != null && empQuota.getRemainDay() > 0 && leaveSetting.getCarryOverTimeUnit() != null
                                                    && leaveSetting.getCarryOverTimeNum() != null && leaveSetting.getCarryOverTimeNum() > 0) {
                                                Integer carryNum = leaveSetting.getCarryOverTimeNum();
                                                //天
                                                if (leaveSetting.getCarryOverTimeUnit().intValue() == 1) {
                                                    Date date = DateUtils.addDays(new Date(empQuota.getStartDate() * 1000), carryNum - 1);
                                                    empQuota.setRemainValidDate(date.getTime() / 1000);
                                                }
                                                //月
                                                else if (leaveSetting.getCarryOverTimeUnit().intValue() == 2) {
//                                                        Date date = DateUtils.addMonths(new Date(empQuota.getStartDate() * 1000),carryNum);
//                                                        empQuota.setRemainValidDate(date.getTime() / 1000);
                                                    empQuota.setRemainValidDate(DateUtil.addMonthDate(empQuota.getStartDate() * 1000, carryNum, -1));
                                                }
                                                //年
                                                else if (leaveSetting.getCarryOverTimeUnit().intValue() == 3) {
//                                                        Date date = DateUtils.addYears(new Date(empQuota.getStartDate() * 1000),carryNum);
//                                                        empQuota.setRemainValidDate(date.getTime() / 1000);
                                                    empQuota.setRemainValidDate(DateUtilExt.addMonthDate(empQuota.getStartDate() * 1000, carryNum, 0, -1));
                                                }
                                            }
                                        }
                                    }

                                    if (leaveSetting.getLeaveTypeId() != null) {
                                        //折算逻辑
                                        if (!leaveTypeMap.containsKey(leaveSetting.getLeaveTypeId())) {
                                            WaLeaveType record = waLeaveTypeMapper.selectByPrimaryKey(leaveSetting.getLeaveTypeId());
                                            leaveTypeMap.put(record.getLeaveTypeId(), record);
                                        }
                                        WaLeaveType leaveType = leaveTypeMap.get(leaveSetting.getLeaveTypeId());
                                        if (leaveType != null) {

                                            if (leaveType.getQuotaMax() != null && !empQuotaMaxMap.containsKey(empid + "_" + leaveSetting.getLeaveTypeId())) {
                                                empQuotaMaxMap.put(empid + "_" + leaveSetting.getLeaveTypeId(), leaveType.getQuotaMax());
                                            }

                                            if (!empInfoMap.containsKey(empid)) {
                                                SysEmpInfo record = sysEmpInfoMapper.selectByPrimaryKey(empid);
                                                empInfoMap.put(empid, record);
                                            }
                                            SysEmpInfo empInfo = empInfoMap.get(empid);

                                            if (leaveType.getQuotaItType() != null && !leaveType.getQuotaItType().equals(1) && (!Integer.valueOf(1).equals(leaveType.getLeaveType()) || !alConversionFlag)) {//额度折算类型
                                                BigDecimal val = new BigDecimal(empQuota.getQuotaDay());
                                                if (convRateFunc != null && lq.isCross()) {
                                                    if (MapUtils.isNotEmpty(lq.getCrossParams())) {
                                                        BigDecimal val2 = getConvRateQuota((Long) lq.getCrossParams().get("midDate"), (Short) lq.getCrossParams().get("year"), empQuota.getStartDate(), empQuota.getLastDate(), (Integer) lq.getCrossParams().get("last"), (Integer) lq.getCrossParams().get("next"));
                                                        if (val2 != null) {
                                                            val = val2;
                                                        }
                                                    }
                                                    empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), leaveType.getQuotaFour2fiveRule()).floatValue());
                                                } else {
                                                    if (empInfo.getHireDate() != null) {
                                                        Long startDate = null;
                                                        if (leaveSetting.getIsCountInProbation() != null && leaveSetting.getIsCountInProbation() && empInfo.getProdeadLine() != null) {
                                                            startDate = empInfo.getProdeadLine();
                                                        } else {
                                                            startDate = empInfo.getHireDate();
                                                        }
                                                        if (startDate > empQuota.getStartDate()) {
                                                            //[计算开始日期-离配额周期开始日期]/365)*配额
                                                            val = val.multiply(getConvRate(empQuota.getStartDate(), empQuota.getLastDate(), leaveType.getQuotaItType(), startDate, empQuota.getLastDate()));
                                                            empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), leaveType.getQuotaFour2fiveRule()).floatValue());
                                                        } else {
                                                            empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), leaveType.getQuotaFour2fiveRule()).floatValue());
                                                        }
                                                    } else {
                                                        empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), leaveType.getQuotaFour2fiveRule()).floatValue());
                                                    }
                                                }
                                            } else {
                                                if (leaveType.getAcctTimeType() != null && 2 == leaveType.getAcctTimeType()) {
                                                    empQuota.setQuotaDay(empQuota.getQuotaDay() * 60);
                                                }
                                            }

                                            //生成上限
                                            if (leaveType.getQuotaMax() != null && (!Integer.valueOf(1).equals(leaveType.getLeaveType()) || !alConversionFlag)) {
                                                String maxkey = empid + "_" + leaveSetting.getLeaveTypeId();
                                                float diffQuota;
                                                if (leaveType.getAcctTimeType() != null && 2 == leaveType.getAcctTimeType()) {
                                                    diffQuota = empQuotaMaxMap.get(maxkey) - empQuota.getQuotaDay() / 60;
                                                } else {
                                                    diffQuota = empQuotaMaxMap.get(maxkey) - empQuota.getQuotaDay();
                                                }
                                                if (diffQuota < 0) {
                                                    if (empQuotaMaxMap.get(maxkey) > 0) {
                                                        empQuota.setQuotaDay(empQuotaMaxMap.get(maxkey));
                                                    } else {
                                                        empQuota.setQuotaDay(0f);
                                                    }
                                                }
                                                empQuotaMaxMap.put(maxkey, diffQuota);
                                            }

                                            empQuota.setNowQuota(calNowQuota(leaveType, leaveSetting, empInfo, empQuota, belongId, convRateFunc, lq, true));
                                        }
                                    }
                                }
                                if (empQuota.getEmpQuotaId() != null) {
                                    if (all) {
                                        if (isReCalQuota) { //初始化使用配额
                                            empQuota.setRemainUsedDay(0f);
                                            empQuota.setUsedDay(0f);
                                        }
                                        //@date 2020/04/14 14:05
                                        empQuota.setUpduser(userId);
                                        empQuota.setUpdtime(DateUtil.getCurrentTime(true));
                                        empQuotaUpdList.add(empQuota);
                                    }
                                } else {
                                    empQuota.setDeductionDay(0f);
                                    empQuota.setUsedDay(0f);
                                    empQuota.setRemainDay(covertNull2Zero(empQuota.getRemainDay()));
                                    empQuota.setAdjustQuota(0f);
                                    //@date 2020/04/14 14:05
                                    empQuota.setCrttime(DateUtil.getCurrentTime(true));
                                    empQuota.setCrtuser(userId);
                                    empQuotaAddList.add(empQuota);
                                    empQuotaMap.put(key, empQuota);
                                }
                            }
                        }
                    }
                }
            }

            importService.fastInsertList(WaEmpQuota.class, "empQuotaId", empQuotaAddList);
            importService.fastUpdList(WaEmpQuota.class, "empQuotaId", empQuotaUpdList);
            importService.fastUpdList(WaEmpQuota.class, "empQuotaId", lastYearEmpQuotaUpdList);

            //杰尼亚-年假配额汇总折算 CLOUD-8264
            Map params = new HashMap();
            params.put("alConversionFlag", alConversionFlag);
            params.put("empQuotaAddList", empQuotaAddList);
            params.put("empQuotaUpdList", empQuotaUpdList);
            params.put("leaveSettingMap", leaveSettingMap);
            params.put("empInfoMap", empInfoMap);
            params.put("leaveTypeMap", leaveTypeMap);
            params.put("empLeaveQuotaMap", empLeaveQuotaMap);
            params.put("empQuotaMaxMap", empQuotaMaxMap);
            params.put("belongId", belongId);
            params.put("convRateFunc", convRateFunc);
            empALQuotaSumConvert(params);
        }
        //重新扣减请假数据（排除调休）
        if (isReCalQuota) {
            List<WaEmpQuota> allEmpQuotaList = new ArrayList<>();
            allEmpQuotaList.addAll(empQuotaAddList);
            allEmpQuotaList.addAll(empQuotaUpdList);
            waCommonService.reCalLeaveQuota(belongId, userId, year.intValue(), allEmpQuotaList, leaveSettingList, empInfoMap);
        }
        groovyScriptEngine.clearCache();
    }


    private String replaceGroupExp(String groupExp) {
        if (StringUtils.isBlank(groupExp)) {
            return groupExp;
        }
        return groupExp.replaceAll("subOrg='(\\d+)'", "orgid in ( SELECT getsuborgstr('{$1}'))")
                .replaceAll("subOrg<>'(\\d+)'", "orgid not in (" +
                        " SELECT cast(getsuborgstr as integer ) as t FROM getsuborgstr('{$1}')" +
                        ")"
                );
    }

    /**
     * 杰尼亚-年假配额汇总折算
     *
     * @param params
     */
    @Transactional
    public void empALQuotaSumConvert(Map params) {
        Boolean alConversionFlag = (Boolean) params.get("alConversionFlag");
        List<WaEmpQuota> empQuotaAddList = (List<WaEmpQuota>) params.get("empQuotaAddList");
        List<WaEmpQuota> empQuotaUpdList = (List<WaEmpQuota>) params.get("empQuotaUpdList");
        Map<Integer, WaLeaveSetting> leaveSettingMap = (Map<Integer, WaLeaveSetting>) params.get("leaveSettingMap");
        Map<Long, SysEmpInfo> empInfoMap = (Map<Long, SysEmpInfo>) params.get("empInfoMap");
        Map<Integer, WaLeaveType> leaveTypeMap = (Map<Integer, WaLeaveType>) params.get("leaveTypeMap");
        Map<String, LeaveQuotaDto> empLeaveQuotaMap = (Map<String, LeaveQuotaDto>) params.get("empLeaveQuotaMap");
        Map<String, Float> empQuotaMaxMap = (Map<String, Float>) params.get("empQuotaMaxMap");
        String belongId = ConvertHelper.stringConvert(params.get("belongId"));
        SysFunc convRateFunc = (SysFunc) params.get("convRateFunc");

        if (alConversionFlag) {//杰尼亚-年假配额汇总折算 CLOUD-8264
            List<WaEmpQuota> allEmpQuotaList = new ArrayList<>();
            allEmpQuotaList.addAll(empQuotaAddList);
            allEmpQuotaList.addAll(empQuotaUpdList);
            Map<Long, List<WaEmpQuota>> empAlQuotaMap = new HashMap<>();
            allEmpQuotaList.forEach(lq -> {
                WaLeaveSetting leaveSetting = leaveSettingMap.get(lq.getQuotaSettingId());
                if (leaveSetting != null && (leaveSetting.getLeaveType() == 1 || leaveSetting.getLeaveType() == 2)) {
                    List<WaEmpQuota> list = empAlQuotaMap.get(lq.getEmpid());
                    if (list == null) list = new ArrayList<>();
                    list.add(lq);
                    empAlQuotaMap.put(lq.getEmpid(), list);
                }
            });
            if (MapUtils.isNotEmpty(empAlQuotaMap)) {
                List<WaEmpQuota> updList = new ArrayList<>();
                empAlQuotaMap.forEach((empid, alQuotaList) -> {
                    try {
                        //查找法定年假配额数据
                        WaEmpQuota legalQuota = null;//法定
                        WaEmpQuota welfareQuota = null;//福利
                        BigDecimal totalQuota = new BigDecimal(0);//法定+福利

                        for (WaEmpQuota waEmpQuota : alQuotaList) {
                            WaLeaveSetting leaveSetting = leaveSettingMap.get(waEmpQuota.getQuotaSettingId());
                            if (leaveSetting.getLeaveType() == 1) {
                                legalQuota = waEmpQuota;
                            } else if (leaveSetting.getLeaveType() == 2) {
                                welfareQuota = waEmpQuota;
                            }
                            totalQuota = totalQuota.add(new BigDecimal(waEmpQuota.getQuotaDay()));
                        }

                        if (legalQuota != null && welfareQuota != null) {
                            //总配额先=法定+福利，汇总折算，只折算，不参与四舍五入规则[假期设置中的额度四舍五入规则]
                            WaEmpQuota totalEmpQuota = new WaEmpQuota();
                            BeanUtils.copyProperties(totalEmpQuota, legalQuota);
                            totalEmpQuota.setQuotaDay(totalQuota.floatValue());
                            getEmpConversionQuota(empInfoMap, leaveSettingMap, leaveTypeMap, empLeaveQuotaMap, belongId, totalEmpQuota, convRateFunc, empQuotaMaxMap, false);

                            //法定单独折算，折算规则和额度四舍五入规则根据假期设置的来计算
                            getEmpConversionQuota(empInfoMap, leaveSettingMap, leaveTypeMap, empLeaveQuotaMap, belongId, legalQuota, convRateFunc, empQuotaMaxMap, true);

                            //福利=法定+福利一起折算的值 - 法定折算的值
                            BigDecimal welfareQuotaDay = new BigDecimal(totalEmpQuota.getQuotaDay()).subtract(new BigDecimal(legalQuota.getQuotaDay()));
                            //福利配额四舍五入规则--向下取整0.5
                            welfareQuotaDay = PayEngineUtil.handleMantissa(welfareQuotaDay.divide(new BigDecimal(0.5)), (short) 3, (short) 0).multiply(new BigDecimal(0.5));
                            welfareQuota.setQuotaDay(welfareQuotaDay.floatValue());

                            //福利当前配额
                            BigDecimal welfareNowQuotaDay = new BigDecimal(totalEmpQuota.getNowQuota()).subtract(new BigDecimal(legalQuota.getNowQuota()));
                            welfareNowQuotaDay = PayEngineUtil.handleMantissa(welfareNowQuotaDay.divide(new BigDecimal(0.5)), (short) 3, (short) 0).multiply(new BigDecimal(0.5));
                            welfareQuota.setNowQuota(welfareNowQuotaDay.floatValue());

                            updList.add(legalQuota);
                            updList.add(welfareQuota);
                        } else {
                            for (WaEmpQuota waEmpQuota : alQuotaList) {
                                //配额折算
                                getEmpConversionQuota(empInfoMap, leaveSettingMap, leaveTypeMap, empLeaveQuotaMap, belongId, waEmpQuota, convRateFunc, empQuotaMaxMap, true);
                            }
                            updList.addAll(alQuotaList);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                });
                importService.fastUpdList(WaEmpQuota.class, "empQuotaId", updList);
            }
        }
    }

    /**
     * 本年&当前配额折算
     *
     * @param empInfoMap
     * @param leaveSettingMap
     * @param leaveTypeMap
     * @param empLeaveQuotaMap
     * @param belongId
     * @param empQuota
     * @param convRateFunc
     * @param empQuotaMaxMap
     * @param isQuotaFour2fiveRule 折算之后的配额是否参加四舍五入规则P
     */
    private void getEmpConversionQuota(Map<Long, SysEmpInfo> empInfoMap, Map<Integer, WaLeaveSetting> leaveSettingMap, Map<Integer, WaLeaveType> leaveTypeMap, Map<String, LeaveQuotaDto> empLeaveQuotaMap,
                                       String belongId, WaEmpQuota empQuota, SysFunc convRateFunc, Map<String, Float> empQuotaMaxMap, Boolean isQuotaFour2fiveRule) {
        LeaveQuotaDto lq = empLeaveQuotaMap.get(empQuota.getEmpid() + "_" + empQuota.getQuotaSettingId());
        SysEmpInfo empInfo = empInfoMap.get(empQuota.getEmpid());
        WaLeaveSetting leaveSetting = leaveSettingMap.get(empQuota.getQuotaSettingId());
        WaLeaveType leaveType = leaveTypeMap.get(leaveSetting.getLeaveTypeId());

        if (leaveType.getQuotaItType() != null && !leaveType.getQuotaItType().equals(1)) {//额度折算类型
            BigDecimal val = new BigDecimal(empQuota.getQuotaDay());
            if (convRateFunc != null && lq.isCross()) {
                if (MapUtils.isNotEmpty(lq.getCrossParams())) {
                    BigDecimal val2 = getConvRateQuota((Long) lq.getCrossParams().get("midDate"), (Short) lq.getCrossParams().get("year"), empQuota.getStartDate(), empQuota.getLastDate(), (Integer) lq.getCrossParams().get("last"), (Integer) lq.getCrossParams().get("next"));
                    if (val2 != null) {
                        val = val2;
                    }
                }
                if (BooleanUtils.isTrue(isQuotaFour2fiveRule)) {
                    empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), leaveType.getQuotaFour2fiveRule()).floatValue());
                } else {
                    empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), null).floatValue());
                }
            } else {
                if (empInfo.getHireDate() != null) {
                    Long startDate = null;
                    if (leaveSetting.getIsCountInProbation() != null && leaveSetting.getIsCountInProbation() && empInfo.getProdeadLine() != null) {
                        startDate = empInfo.getProdeadLine();
                    } else {
                        startDate = empInfo.getHireDate();
                    }
                    if (startDate > empQuota.getStartDate()) {
                        //[计算开始日期-离配额周期开始日期]/365)*配额
                        val = val.multiply(getConvRate(empQuota.getStartDate(), empQuota.getLastDate(), leaveType.getQuotaItType(), startDate, empQuota.getLastDate()));

                        if (BooleanUtils.isTrue(isQuotaFour2fiveRule)) {
                            empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), leaveType.getQuotaFour2fiveRule()).floatValue());
                        } else {
                            empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), null).floatValue());
                        }
                    } else {
                        if (BooleanUtils.isTrue(isQuotaFour2fiveRule)) {
                            empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), leaveType.getQuotaFour2fiveRule()).floatValue());
                        } else {
                            empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), null).floatValue());
                        }
                    }
                } else {
                    if (BooleanUtils.isTrue(isQuotaFour2fiveRule)) {
                        empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), leaveType.getQuotaFour2fiveRule()).floatValue());
                    } else {
                        empQuota.setQuotaDay(handleMantissa2(belongId, val, leaveType.getAcctTimeType(), null).floatValue());
                    }
                }
            }
        } else {
            if (leaveType.getAcctTimeType() != null && 2 == leaveType.getAcctTimeType()) {
                empQuota.setQuotaDay(empQuota.getQuotaDay() * 60);
            }
        }

        //生成上限
        if (leaveType.getQuotaMax() != null) {
            String maxkey = empInfo.getEmpid() + "_" + leaveSetting.getLeaveTypeId();
            float diffQuota;
            if (leaveType.getAcctTimeType() != null && 2 == leaveType.getAcctTimeType()) {
                diffQuota = empQuotaMaxMap.get(maxkey) - empQuota.getQuotaDay() / 60;
            } else {
                diffQuota = empQuotaMaxMap.get(maxkey) - empQuota.getQuotaDay();
            }
            if (diffQuota < 0) {
                if (empQuotaMaxMap.get(maxkey) > 0) {
                    empQuota.setQuotaDay(empQuotaMaxMap.get(maxkey));
                } else {
                    empQuota.setQuotaDay(0f);
                }
            }
            empQuotaMaxMap.put(maxkey, diffQuota);
        }

        //当前配额折算
        if (BooleanUtils.isTrue(isQuotaFour2fiveRule)) {
            empQuota.setNowQuota(calNowQuota(leaveType, leaveSetting, empInfo, empQuota, belongId, convRateFunc, lq, true));
        } else {
            empQuota.setNowQuota(calNowQuota(leaveType, leaveSetting, empInfo, empQuota, belongId, convRateFunc, lq, false));
        }
    }

    /**
     * 计算当前配额
     *
     * @param leaveType
     * @param leaveSetting
     * @param empInfo
     * @param empQuota
     * @param belongId
     * @param convRateFunc
     * @param lq
     * @param isUseFour2fiveRule 折算之后的配额是否参加四舍五入规则
     * @return
     */
    private Float calNowQuota(WaLeaveType leaveType, WaLeaveSetting leaveSetting, SysEmpInfo empInfo, WaEmpQuota empQuota, String belongId, SysFunc convRateFunc, LeaveQuotaDto lq, Boolean isUseFour2fiveRule) {
        Long now = DateUtil.getCurrentTime(true);
        BigDecimal nowQuota = new BigDecimal(empQuota.getQuotaDay());//小时假，审数据单位为分钟
        Integer useItType = leaveType.getUseItType();
        Integer useFour2fiveRule = leaveType.getUseFour2fiveRule();

        //是否是否四舍五入规则
        if (BooleanUtils.isFalse(isUseFour2fiveRule)) {
            useFour2fiveRule = null;
        }

        BigDecimal oldVal = new BigDecimal(empQuota.getQuotaDay());
        Integer acctTimeType = leaveType.getAcctTimeType();
        if (useItType != null) {
            if (!useItType.equals(1)) {
                if (convRateFunc != null && lq != null && lq.isCross()) {
                    if (acctTimeType != null && 2 == acctTimeType) {
                        nowQuota = nowQuota.divide(new BigDecimal(60));
                    }
                    nowQuota = handleMantissa(belongId, nowQuota, leaveType.getAcctTimeType(), useFour2fiveRule);
                } else {
                    Long hireDate = empInfo.getHireDate();
                    Long terminationDate = empInfo.getTerminationDate();
                    Long startDate = empQuota.getStartDate();
                    if (hireDate != null) {
                        if (leaveSetting.getIsCountInProbation() != null && leaveSetting.getIsCountInProbation() && empInfo.getProdeadLine() != null && empInfo.getProdeadLine() > startDate) {
                            startDate = empInfo.getProdeadLine();
                        } else if (hireDate > startDate) {
                            startDate = hireDate;
                        }
                    }
                    if (terminationDate != null && terminationDate < now) {
                        now = terminationDate;
                    }
                    nowQuota = oldVal.multiply(getConvRate(empQuota.getStartDate(), empQuota.getLastDate(), useItType, startDate, now));
                    if (acctTimeType != null && 2 == acctTimeType) {
                        nowQuota = nowQuota.divide(new BigDecimal(60));
                    }
                    nowQuota = handleMantissa(belongId, nowQuota, leaveType.getAcctTimeType(), useFour2fiveRule);
                }
            }
        }
        return nowQuota.floatValue();
    }

    private Float getTotalDays(Long start, Long end) {
        if (start == null) return 365.0f;
        if (end == null) {
            end = DateUtils.addYears(new Date(start * 1000), 1).getTime() / 1000;
        }
        return (end + 1 - start) / 24 / 3600 * 1.0f;
    }

    private Float covertNull2Zero(Float val) {
        return val == null ? 0 : val;
    }

    private BigDecimal covertNull2Zero2(Float val) {
        return val == null ? new BigDecimal(0) : new BigDecimal(val);
    }

    public List<WaLeaveType> getLeaveTypes(String belongid) {
//        return waLeaveTypeMapper.getLeaveTypes(belongid);
        WaLeaveTypeExample example = new WaLeaveTypeExample();
        example.createCriteria().andBelongOrgidEqualTo(belongid);
        example.setOrderByClause("orders asc");
        return waLeaveTypeMapper.selectByExample(example);
    }

    public List<WaOvertimeType> getOtTypes(String belongid) {
        return waOvertimeTypeMapper.getOtTypes(belongid);
    }

    public Map getWaGroupCycle(Integer sysPeriodMonth, Integer waGroupId) {
        Map map = new HashMap();
        WaGroup group = waGroupMapper.selectByPrimaryKey(waGroupId);
        Integer year = Integer.valueOf(sysPeriodMonth.toString().substring(0, 4));
        Integer month = Integer.valueOf(sysPeriodMonth.toString().substring(4));
        // 根据考勤分组上设置的 规则自动计算出开始和结束日期，如：201803 上月  开始日期1号 则 开始日期= 20180201  结束日期= 20180228
        // 本月 eg: 20180301 20180331
        Long startDate = getWaGroupCycleStartDate(group, year, month);
        Long endDate = getWaGroupCycleEndDate(group, year, month);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        return map;
    }

    private Long getWaGroupCycleStartDate(WaGroup group, Integer year, Integer month) {

        Calendar c = Calendar.getInstance();
        c.clear();

        if (group != null) {
            if (group.getCyleMonth() == 1) { // 1 上月
                month -= 1;
//                c.add(Calendar.MONTH, -1);
//                if(month == 3){ // 如果每月的开始日是29号 例如:选择3月时，上月29到下月末，就是 3.1-3.28
//                    c.add(Calendar.DAY_OF_MONTH,1);
//                }
            } else if (group.getCyleMonth() == 2) { // 1 本月

            }
        }
        // 特殊处理二月份
        group.setCyleStartdate(month.equals(2) && group.getCyleStartdate() > getFebLastDayOfMonth(year) ? getFebLastDayOfMonth(year) : group.getCyleStartdate());
        c.set(year, month - 1, group.getCyleStartdate());
        Long startDate = c.getTimeInMillis() / 1000;
        return startDate;
    }

    private int getFebLastDayOfMonth(Integer year) {
        Calendar febCalendar = Calendar.getInstance();
        febCalendar.clear();
        // 获取3月第一天
        febCalendar.set(year, 2, 1);
        // 获取当前日期的上一天，也就二月的最后一天
        febCalendar.add(Calendar.DATE, -1);
        return febCalendar.get(Calendar.DAY_OF_MONTH);
    }

    private Long getWaGroupCycleEndDate(WaGroup group, Integer year, Integer month) {

        Calendar c = Calendar.getInstance();
        c.clear();
        group.setCyleStartdate(month.equals(2) && group.getCyleStartdate() > getFebLastDayOfMonth(year) ? getFebLastDayOfMonth(year) : group.getCyleStartdate());
        c.set(year, month - 1, group.getCyleStartdate());
        if (group != null) {
            if (group.getCyleMonth() == 1) { // 1 上月
                c.add(Calendar.DAY_OF_MONTH, -1);
            } else if (group.getCyleMonth() == 2) { // 1 本月
                c.add(Calendar.MONTH, 1);
                c.add(Calendar.DAY_OF_MONTH, -1);
            }
        }
        Long endDate = c.getTimeInMillis() / 1000;
        return endDate;
    }

    /**
     * 查询假期类型定义数据
     *
     * @param belongOrgId
     * @return
     */
    public List<Map> listLeaveTypeDef(String belongOrgId, String lang) {
        List<Map> listMap = new ArrayList<>();
        Map params = new HashMap();
        params.put("belongOrgId", belongOrgId);
        List<Map> list = waLeaveTypeDefMapper.listLeaveTypeDef(params);
        if (CollectionUtils.isNotEmpty(list)) {
            list = LangUtil.langlist(list, lang, "leaveTypeDefName", "leaveTypeDefLang");
            for (Map map : list) {
                Map newMap = new HashMap();
                newMap.put("code", map.get("leaveTypeDefCode"));
                newMap.put("text", map.get("leaveTypeDefName"));
                if (null != map.get("i18nLeaveTypeDefName")) {
                    newMap.put("text", LangParseUtil.getI18nLanguage(map.get("i18nLeaveTypeDefName").toString(), map.get("leaveTypeDefName").toString()));
                }
                newMap.put("value", map.get("leaveTypeDefId"));
                listMap.add(newMap);
            }
        }
        return listMap;
    }

    /**
     * 公司考情分组下拉框
     */
    public List<Map> getGroupList() {
        Map parm = new HashMap();
        parm.put("belongOrgId", getUserInfo().getTenantId());
        return empInfoMapper.getGroupList(parm);
    }

    /**
     * 富士通--定制逻辑 调休转加班付现
     *
     * @param belongOrgId
     * @param waGroupId
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    @Transactional
    public List<Map<Integer, Object>> txToCash(String belongOrgId, Integer waGroupId, Long startDate, Long endDate) throws Exception {
        List<Map<Integer, Object>> listData = new ArrayList<>();
        String tenantId = getUserInfo().getTenantId();
        //查询员工的调休配额
        Map params = new HashMap();
        params.put("belongOrgId", tenantId);
        params.put("leaveType", 4);//调休
        params.put("waGroupId", waGroupId);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        List<Map> listEmpQuotaMap = waMapper.listEmpQuotaByWaGroupId(params);
        if (CollectionUtils.isNotEmpty(listEmpQuotaMap)) {
            for (Map map : listEmpQuotaMap) {
                Long empId = ConvertHelper.longConvert(map.get("empid"));
                Integer empQuotaId = (Integer) map.get("empQuotaId");

                //根据考勤开始时间计算前三个月的日期
                Long pre3MonthDate = DateUtilExt.addMonth(startDate, -2);
                String ym = DateUtilExt.getTimeStrByPattern(pre3MonthDate, "yyyyMM");
                //查询pre3MonthDate考勤周期
                Map params2 = new HashMap();
                params2.put("belongOrgId", tenantId);
                params2.put("ym", ym);
                params2.put("empId", empId);
                Map waCycle = waMapper.getEmpWaGroupCycle(params2);
                //考勤周期开始结束日期
                Long cycleStartDate = Long.valueOf(String.valueOf(waCycle.get("startDate")));
                Long cycleEndDate = Long.valueOf(String.valueOf(waCycle.get("endDate")));

                //传考勤周期对应的明细配额
                Map params1 = new HashMap();
                params1.put("belongOrgId", tenantId);
                params1.put("listEmpQuotaId", Arrays.asList(new Integer[]{empQuotaId}));
                params1.put("waStartDate", cycleStartDate);
                params1.put("waEndDate", cycleEndDate);
                List<Map> listEmpQuotaDetail = waMapper.listEmpQuotaDetail(params1);

                if (CollectionUtils.isNotEmpty(listEmpQuotaDetail)) {
                    List<WaEmpQuotaDetail> detailUpdList = new ArrayList<>();
                    List<Integer> listUpdEmpQuotaId = new ArrayList<>();

                    for (Map detail : listEmpQuotaDetail) {
                        Integer empQuotaDetailId = (Integer) detail.get("empQuotaDetailId");
                        BigDecimal surplusQuota = new BigDecimal(String.valueOf(detail.get("surplusQuota")));
                        BigDecimal quotaDay = new BigDecimal(String.valueOf(detail.get("quotaDay")));

                        Long empid = ConvertHelper.longConvert(detail.get("empid"));

                        if (surplusQuota.floatValue() > 0) {
                            //剩余额度转加班付现
                            Map empMap = new HashMap();
                            empMap.put(empid, surplusQuota);
                            listData.add(empMap);

                            WaEmpQuotaDetail empQuotaDetail = new WaEmpQuotaDetail();
                            empQuotaDetail.setEmpQuotaDetailId(empQuotaDetailId);
                            empQuotaDetail.setSurplusQuota((float) 0);
                            empQuotaDetail.setInvalidQuota(surplusQuota.floatValue());
                            empQuotaDetail.setQuotaDay(quotaDay.subtract(surplusQuota).floatValue());
                            detailUpdList.add(empQuotaDetail);

                            if (listUpdEmpQuotaId.indexOf(empQuotaId) < 0) {
                                listUpdEmpQuotaId.add(empQuotaId);
                            }
                        }
                    }

                    //更新配额明细表
                    if (CollectionUtils.isNotEmpty(detailUpdList)) {
                        importService.fastUpdList(WaEmpQuotaDetail.class, "empQuotaDetailId", detailUpdList);
                    }
                    //计算调休配额
                    this.updEmpTxQuota(belongOrgId, listUpdEmpQuotaId);
                }
            }
        }
        return listData;
    }

    /**
     * 计算调休配额
     *
     * @param belongOrgId
     * @param listEmpQuotaId
     */
    @Transactional
    public void updEmpTxQuota(String belongOrgId, List<Integer> listEmpQuotaId) {
        Map params1 = new HashMap();
        params1.put("belongOrgId", belongOrgId);
        params1.put("listEmpQuotaId", listEmpQuotaId);
        List<Map> listEmpQuotaDetail = waMapper.listEmpQuotaDetail(params1);
        if (CollectionUtils.isNotEmpty(listEmpQuotaDetail)) {
            Map<Integer, Object> empQuotaUpd = new HashMap<>();

            for (Map detail : listEmpQuotaDetail) {
                Integer empQuotaId = (Integer) detail.get("empQuotaId");
                BigDecimal totalQuota = new BigDecimal(String.valueOf(detail.get("quotaDay")));//总配额
                BigDecimal invalidQuota = new BigDecimal(String.valueOf(detail.get("invalidQuota")));//失效配额

                BigDecimal totalKyQuota = totalQuota.subtract(invalidQuota);//总的有效配额

                BigDecimal totalKyQuota1 = new BigDecimal(totalKyQuota.floatValue());
                if (empQuotaUpd.containsKey(empQuotaId)) {
                    BigDecimal quota = new BigDecimal(String.valueOf(empQuotaUpd.get(empQuotaId)));
                    totalKyQuota1 = totalKyQuota1.add(quota);
                }
                empQuotaUpd.put(empQuotaId, totalKyQuota1);
            }
            List<WaEmpQuota> updList = new ArrayList<>();
            Iterator<Map.Entry<Integer, Object>> iterator = empQuotaUpd.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Integer, Object> entry = iterator.next();
                WaEmpQuota empQuota = new WaEmpQuota();
                empQuota.setEmpQuotaId(entry.getKey());
                empQuota.setQuotaDay(Float.valueOf(String.valueOf(entry.getValue())));
                empQuota.setNowQuota(Float.valueOf(String.valueOf(entry.getValue())));
                updList.add(empQuota);
            }

            importService.fastUpdList(WaEmpQuota.class, "empQuotaId", updList);
        }
    }

    /**
     * 保存当年调整配额
     *
     * @param corpId
     * @param belongOrgId
     * @param userId
     * @param empQuotaId
     * @param adjustJson
     * @return
     * @throws Exception
     */
    @Transactional
    public Integer saveEmpQuotaAdjust(Long corpId, String belongOrgId, Long userId, Integer empQuotaId, String adjustJson, Integer acctTimeType, String lang) throws Exception {
        int count = 0;
        if (StringUtils.isNotBlank(adjustJson)) {
            List<Map> listAdjust = (List) JacksonJsonUtil.jsonToBean(adjustJson, List.class);
            if (CollectionUtils.isNotEmpty(listAdjust)) {
                WaEmpQuota waEmpQuota = waEmpQuotaMapper.selectByPrimaryKey(empQuotaId);
                if (waEmpQuota != null) {
                    Long minDate = waEmpQuota.getStartDate();
                    Long maxDate = waEmpQuota.getLastDate();
                    /*SysEmpInfo sysEmpInfo = sysEmpInfoMapper.selectByPrimaryKey(waEmpQuota.getEmpid());
                    WaLeaveSetting waLeaveSetting = waLeaveSettingMapper.selectByPrimaryKey(waEmpQuota.getQuotaSettingId());*/
                    List<WaEmpQuotaDetail> list = new ArrayList<>();
                    for (Map map : listAdjust) {
                        Long startDate = Long.valueOf(String.valueOf(map.get("startDate")));
                        Long endDate = Long.valueOf((String.valueOf(map.get("endDate"))));
                        String remarks = (String) map.get("remarks");
                        if (startDate.compareTo(minDate) < 0 || endDate.compareTo(maxDate) > 0) {
                            throw new CDException(messageResource.getMessage("L005987", new Object[]{}, new Locale(lang)));
                        }
                        BigDecimal quotaDay = new BigDecimal(String.valueOf(map.get("quotaDay")));
                        if (acctTimeType == 2) {
                            quotaDay = quotaDay.multiply(new BigDecimal(60));
                        }
                        WaEmpQuotaDetail detail = new WaEmpQuotaDetail();
                        detail.setEmpQuotaId(empQuotaId);
                        detail.setCrtuser(userId);
                        detail.setCrttime(DateUtil.getCurrentTime(true));
                        detail.setCorpId(corpId);
                        detail.setBelongOrgId(belongOrgId);
                        detail.setStatus(1);
                        detail.setQuotaType(2);
                        detail.setStartDate(startDate);
                        detail.setEndDate(endDate);
                        detail.setRemarks(remarks);
                        detail.setQuotaDay(quotaDay.floatValue());
                        detail.setSurplusQuota(quotaDay.floatValue());
                        detail.setUsedDay(0f);
                        detail.setInvalidQuota(0f);
                        detail.setEmpid(waEmpQuota.getEmpid());
                        count = waEmpQuotaDetailMapper.insertSelective(detail);
                        list.add(detail);
                        //是否开启调休配额有效期
                        String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongOrgId + RedisKeyDefine.IS_OPEN_TX_QUOTA_VALIDITY);
                        if (isOpen != null && "1".equals(isOpen)) {
                            //生成配额使用明细
                            List<String> listYm = DateUtilExt.getMonthBetween(startDate, endDate);
                            if (CollectionUtils.isNotEmpty(listYm)) {
                                List<WaEmpQuotaUse> listWaEmpQuotaUse = new ArrayList<>();
                                for (String ym : listYm) {
                                    WaEmpQuotaUse waEmpQuotaUse = new WaEmpQuotaUse();
                                    waEmpQuotaUse.setEmpQuotaDetailId(detail.getEmpQuotaDetailId());
                                    waEmpQuotaUse.setUsedDay(0f);
                                    waEmpQuotaUse.setYm(ym);
                                    listWaEmpQuotaUse.add(waEmpQuotaUse);
                                }
                                importService.fastInsertList(WaEmpQuotaUse.class, "recordId", listWaEmpQuotaUse);
                            }
                        }
                    }
                    //计算并且更新当年调整
                    waCommonService.updateEmpAdjustQuota(waEmpQuota.getEmpid(), new ArrayList(Arrays.asList(waEmpQuota.getEmpQuotaId())));
                }
            }
        }
        return count;
    }

    @Transactional
    public Integer deEmpQuotaAdjust(Long corpId, String belongOrgId, Integer id, String lang) {
        //1、检查调整配额明细是否已使用
        WaEmpQuotaDetailExample detailExample = new WaEmpQuotaDetailExample();
        detailExample.createCriteria().andCorpIdEqualTo(corpId).andBelongOrgIdEqualTo(belongOrgId).andEmpQuotaDetailIdEqualTo(id);
        List<WaEmpQuotaDetail> quotaDetails = waEmpQuotaDetailMapper.selectByExample(detailExample);
        if (CollectionUtils.isNotEmpty(quotaDetails)) {
            WaEmpQuotaDetail quotaDetail = quotaDetails.get(0);
            /*SysEmpInfo sysEmpInfo = sysEmpInfoMapper.selectByPrimaryKey(quotaDetail.getEmpid());*/
            WaEmpQuota waEmpQuota = waEmpQuotaMapper.selectByPrimaryKey(quotaDetail.getEmpQuotaId());
            /*WaLeaveSetting waLeaveSetting = waLeaveSettingMapper.selectByPrimaryKey(waEmpQuota.getQuotaSettingId());
            String start = DateUtil.convertDateTimeToStr(quotaDetail.getStartDate(), "yyyy-MM-dd", true);
            String end = DateUtil.convertDateTimeToStr(quotaDetail.getStartDate(), "yyyy-MM-dd", true);*/
            if (quotaDetail.getUsedDay() != null && quotaDetail.getUsedDay() > 0) {
                throw new CDException(messageResource.getMessage("L005971", new Object[]{}, new Locale(lang)));
            }
            //2、删除配额
            waEmpQuotaDetailMapper.deleteByExample(detailExample);
            //是否开启调休配额有效期
            String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongOrgId + RedisKeyDefine.IS_OPEN_TX_QUOTA_VALIDITY);
            if (isOpen != null && "1".equals(isOpen)) {
                //3、删除调整使用明细
                WaEmpQuotaUseExample useExample = new WaEmpQuotaUseExample();
                useExample.createCriteria().andEmpQuotaDetailIdEqualTo(id);
                waEmpQuotaUseMapper.deleteByExample(useExample);
            }
            if (waEmpQuota != null) {
                //计算并且更新当年调整
                waCommonService.updateEmpAdjustQuota(waEmpQuota.getEmpid(), new ArrayList(Arrays.asList(waEmpQuota.getEmpQuotaId())));
            }
            //记录调整后的 配额信息
            /*WaEmpQuota newWaEmpQuota = waEmpQuotaMapper.selectByPrimaryKey(waEmpQuota.getEmpQuotaId());*/
            return 1;
        }
        return 0;
    }

    /**
     * 加班转调休配额计算
     *
     * @param empId
     * @return
     */
    @Transactional
    public WaEmpQuota calOtToTxQuota(String belongOrgId, Long empId) throws Exception {
        //调休配额类型判断
        Integer settingId = waConfigMapper.getRelaxLeaveSettingId(empId);
        if (settingId == null) {
            Integer leaveTypeId = null;
            //随机取公司的一个调休配额类型
            Map txLeaveType = waMapper.getDefaultTxLeaveType(belongOrgId);
            if (txLeaveType != null) {
                settingId = (Integer) txLeaveType.get("quota_setting_id");
                leaveTypeId = (Integer) txLeaveType.get("leave_type_id");
            }
            if (settingId == null) {
                if (leaveTypeId == null) {
                    leaveTypeId = getOTLeaveTypeId(belongOrgId);
                }
                WaLeaveSetting waLeaveSetting = new WaLeaveSetting();
                waLeaveSetting.setBelongOrgid(belongOrgId);
                waLeaveSetting.setQuotaSettingName("调休");
                waLeaveSetting.setLeaveType((short) 4);
                waLeaveSetting.setStartDate(DateUtil.getOnlyDate());
                waLeaveSetting.setLeaveTypeId(leaveTypeId);
                waLeaveSetting.setEndDate(DateUtil.getTimesampByDateStr("9999.12.31"));
                waLeaveSetting.setCarryOverType(1);
                waLeaveSetting.setCarryOverTimeNum(0);
                waLeaveSetting.setCarryOverTimeUnit(1);
                waLeaveSetting.setQuotaPeriodType(3);
                waLeaveSetting.setCrtuser(1L);
                waLeaveSetting.setCrttime(System.currentTimeMillis() / 1000);
                waLeaveSettingMapper.insertSelective(waLeaveSetting);
                settingId = waLeaveSetting.getQuotaSettingId();
            }
        }
        //员工调休配额判断
        WaEmpQuotaExample quotaExample = new WaEmpQuotaExample();
        WaEmpQuotaExample.Criteria quotaCriteria = quotaExample.createCriteria();
        quotaCriteria.andEmpidEqualTo(empId);
        quotaCriteria.andQuotaSettingIdEqualTo(settingId);
        quotaCriteria.andStartDateLessThanOrEqualTo(DateUtil.getOnlyDate());
        quotaCriteria.andLastDateGreaterThanOrEqualTo(DateUtil.getOnlyDate());
        List<WaEmpQuota> empQuotaList = waEmpQuotaMapper.selectByExample(quotaExample);

        WaEmpQuota empQuota = null;
        if (CollectionUtils.isEmpty(empQuotaList)) {
            empQuota = new WaEmpQuota();
            empQuota.setQuotaSettingId(settingId);
            empQuota.setEmpid(empId);
            empQuota.setStartDate(0l);
            empQuota.setLastDate(DateUtil.getTimesampByDateStr("9999.12.31"));
            empQuota.setRemainDay(0f);
            empQuota.setDeductionDay(0f);
            empQuota.setQuotaDay(0f);
            empQuota.setNowQuota(0f);
            empQuota.setUsedDay(0f);
            empQuota.setPeriodYear((short) 1);
            waEmpQuotaMapper.insertSelective(empQuota);
        } else {
            empQuota = empQuotaList.get(0);
        }
        return empQuota;
    }

    /**
     * 加班转调休配额
     *
     * @throws ParseException
     */
    @Transactional
    public WaEmpQuotaDetail calTxQuotaDetail(Long corpId, String belongOrgId, Integer empQuotaId, Float otNum,
                                             Integer usefulTime, Long cycleStartDate, Long cycleEndDate, Boolean isUpdate) throws Exception {
        //2、调休有效期开始日期=考勤周期开始日期，调休有效期结束日期=开始日期+有效期
        Long validityStartDate = cycleStartDate;
        Long validityEndDate = DateUtilExt.getMonthEndDay(DateUtilExt.addMonth(cycleStartDate, usefulTime - 1));
        //3、生成调休配额明细
        WaEmpQuotaDetail quotaDetail = null;
        WaEmpQuotaDetailExample detailExample = new WaEmpQuotaDetailExample();
        detailExample.createCriteria().andBelongOrgIdEqualTo(belongOrgId).andEmpQuotaIdEqualTo(empQuotaId)
                .andStatusEqualTo(1).andWaStartDateEqualTo(cycleStartDate).andWaEndDateEqualTo(cycleEndDate);
        List<WaEmpQuotaDetail> quotaDetails = waEmpQuotaDetailMapper.selectByExample(detailExample);
        if (CollectionUtils.isEmpty(quotaDetails)) {
            quotaDetail = new WaEmpQuotaDetail();
            quotaDetail.setBelongOrgId(belongOrgId);
            quotaDetail.setCorpId(corpId);
            quotaDetail.setCrttime(DateUtil.getCurrentTime(true));
            quotaDetail.setCrtuser(0L);
            quotaDetail.setEmpQuotaId(empQuotaId);
            quotaDetail.setStartDate(validityStartDate);
            quotaDetail.setEndDate(validityEndDate);
            quotaDetail.setWaStartDate(cycleStartDate);
            quotaDetail.setWaEndDate(cycleEndDate);
            quotaDetail.setInvalidQuota((float) 0);//过期配额
            quotaDetail.setQuotaDay(otNum);//剩余配额
            quotaDetail.setSurplusQuota(otNum);//总配额
            quotaDetail.setStatus(1);
            quotaDetail.setQuotaType(1);
            waEmpQuotaDetailMapper.insertSelective(quotaDetail);

            //生成配额使用明细
            List<String> listYm = DateUtilExt.getMonthBetween(validityStartDate, validityEndDate);
            if (CollectionUtils.isNotEmpty(listYm)) {
                List<WaEmpQuotaUse> listWaEmpQuotaUse = new ArrayList<>();
                for (String ym : listYm) {
                    WaEmpQuotaUse waEmpQuotaUse = new WaEmpQuotaUse();
                    waEmpQuotaUse.setEmpQuotaDetailId(quotaDetail.getEmpQuotaDetailId());
                    waEmpQuotaUse.setUsedDay(0f);
                    waEmpQuotaUse.setYm(ym);
                    listWaEmpQuotaUse.add(waEmpQuotaUse);
                }
                importService.fastInsertList(WaEmpQuotaUse.class, "recordId", listWaEmpQuotaUse);
            }
        } else {
            if (isUpdate) {
                quotaDetail = quotaDetails.get(0);
                quotaDetail.setQuotaDay(otNum);
                quotaDetail.setSurplusQuota(otNum);
                quotaDetail.setUsedDay(0f);
                waEmpQuotaDetailMapper.updateByPrimaryKeySelective(quotaDetail);
            }
        }
        return quotaDetail;
    }

    public Integer getOTLeaveTypeId(String belongOrgId) {
        WaLeaveTypeExample example = new WaLeaveTypeExample();
        WaLeaveTypeExample.Criteria criteria = example.createCriteria();
        criteria.andBelongOrgidEqualTo(belongOrgId);
        criteria.andLeaveTypeEqualTo(3);
        List<WaLeaveType> list = waLeaveTypeMapper.selectByExample(example);
        if (list.isEmpty()) {
            return 0;
        } else {
            return list.get(0).getLeaveTypeId();
        }
    }

    /**
     * 获取加班记录
     *
     * @param belongOrgId
     * @param empGroupEmpids
     * @param startDate
     * @param endDate
     * @param sobEndDate
     * @param parseGroup
     * @param dto
     * @return
     * @throws Exception
     */
    public List<Map> listOtData(String belongOrgId, List<Long> empGroupEmpids, Long startDate, Long endDate, Long sobEndDate, WaParseGroup parseGroup,
                                WaAnalyzDTO dto, Boolean analyzeOtRule) throws Exception {
        List<Map> list = new ArrayList<>();

        //查询考勤周期内的所有加班单据
        Map otParams = new HashMap();
        otParams.put("belongOrgId", belongOrgId);
        otParams.put("empids", empGroupEmpids);
        otParams.put("startDate", startDate);
        otParams.put("endDate", endDate);
        otParams.put("sobEndDate", sobEndDate);
        List<Map> listOt = waMapper.getEmpOtListByParams(otParams);
        if (CollectionUtils.isEmpty(listOt)) return list;

        //如果加班联动签到签退记录，则只查询考勤周期内有签到签退的加班记录
        Boolean isLinkDk = parseGroup.getOtParse() == null ? false : parseGroup.getOtParse();
        if (isLinkDk) {
            Map<String, Object> regParams = new HashMap<>();
            regParams.put("belongid", belongOrgId);
            regParams.put("startDate", startDate);
            regParams.put("endDate", endDate + 86399);
            if (parseGroup != null && parseGroup.getIgnoreLocationExp() != null) {
                regParams.put("ignoreLocationExp", parseGroup.getIgnoreLocationExp());// 是否忽略地点异常的签到签退数据
            }
            regParams.put("anyEmpids2", "'{" + StringUtils.join(empGroupEmpids, ",") + "}'");
            regParams.put("registerType", 1);

            //查询最早的签到记录
            List<Map> singInList = waRegisterRecordMapper.searchRegAnalyseList(regParams);

            //查询最晚的签退纪录
            regParams.put("registerType", 2);
            List<Map> singOffList = waRegisterRecordMapper.searchRegAnalyseList(regParams);

            if (singInList != null && singOffList != null) {
                //生成按时间+empid作为key的Map value 为签到签退数据
                Map<String, WaRegisterRecord> singIns = this.generatRegMap(singInList);
                Map<String, WaRegisterRecord> singOffs = this.generatRegMap(singOffList);

                for (Map map : listOt) {
                    Long empId = (Long) map.get("empid");
                    Long regdate = (Long) map.get("regdate");
                    Long belongDate = (Long) map.get("belongDate");
                    Long start_time = (Long) map.get("start_time");
                    Long end_time = (Long) map.get("end_time");
                    Integer time_duration = (Integer) map.get("time_duration");

                    String key = empId + "_" + regdate;
                    if (singIns.containsKey(key) && singOffs.containsKey(key)) {
                        WaRegisterRecord signInReg = singIns.get(key);
                        WaRegisterRecord signOffReg = singOffs.get(key);

                        Long siginTime = signInReg.getRegDateTime();
                        Long siginOffTime = signOffReg.getRegDateTime();
                        if (siginOffTime == null) {
                            siginOffTime = 0L;
                        } else {
                            // 签退时间精确到分钟
                            siginOffTime = DateUtil.convertStringToDateTime(DateUtil.convertDateTimeToStr(siginOffTime, "yyyy-MM-dd HH:mm", true), "yyyy-MM-dd HH:mm", true);
                        }
                        if (siginTime == null) {
                            siginTime = 0L;
                        } else {
                            // 签到时间精确到分钟
                            siginTime = DateUtil.convertStringToDateTime(DateUtil.convertDateTimeToStr(siginTime, "yyyy-MM-dd HH:mm", true), "yyyy-MM-dd HH:mm", true);
                        }
                        if (start_time <= siginOffTime && end_time >= siginTime) {
                            // 有效工作时长
                            Long validateTime = 0L;
                            // 判断签到时间是否小于等于加班开始时间
                            if (siginTime <= start_time && siginOffTime >= end_time) {
                                validateTime = end_time - start_time; // 有效加班时长
                            } else if (siginTime <= start_time && siginOffTime <= end_time) {
                                validateTime = siginOffTime - start_time; // 有效加班时长
                            } else if (siginTime >= start_time && siginOffTime <= end_time) {
                                validateTime = siginOffTime - siginTime; // 有效加班时长
                            } else if (siginTime >= start_time && siginOffTime >= end_time) {
                                validateTime = end_time - siginTime; // 有效加班时长
                            } else {
                                time_duration = 0;
                            }
                            if (validateTime > 0) {
                                //扣除休息时间
                                EmpShiftInfo empShiftDef = this.getEmpShiftDefByInfo(empId, null, belongDate, dto);
                                if (empShiftDef != null) {
                                    start_time = Math.max(start_time, siginTime);
                                    end_time = Math.min(end_time, siginOffTime);
                                    // 此加班单有可能属于前一天的班次，因此需要扣除掉前一天班次的加班休息时间
                                    Long preDate = empShiftDef.getWorkDate() - 86400L;
                                    EmpShiftInfo preShift = this.getEmpShiftDefByInfo(empId, null, preDate, dto);
                                    if (null != preShift) {
                                        validateTime -= waCommonService.calOtRestTotalTime(belongOrgId, preShift, start_time, end_time);
                                    }
                                    validateTime -= waCommonService.calOtRestTotalTime(belongOrgId, empShiftDef, start_time, end_time);
                                }
                                validateTime /= 60;
                            } else {
                                validateTime = 0l;
                            }
                            // 如果加班小时数大于了 实际的小时数则取实际的小时数，
                            if (time_duration > validateTime) {
                                time_duration = validateTime.intValue();
                            }
                            if (time_duration > 0 && BooleanUtils.isTrue(analyzeOtRule)) {
                                // 根据设置的加班规则进行分析
                                if (map.get("date_type") != null && map.get("compensate_type") != null) {
                                    Integer date_type = Integer.valueOf(map.get("date_type").toString());
                                    Integer compensate_type = Integer.valueOf(map.get("compensate_type").toString());
                                    time_duration = analyzeOtRule(time_duration, parseGroup, date_type, compensate_type, dto.getOtMaps());
                                }
                            }
                        } else {
                            time_duration = 0;
                        }
                        map.put("time_duration", time_duration);
                        list.add(map);
                    }
                }
            }
        } else {
            list.addAll(listOt);
        }
        return list;
    }

    /**
     * 根据员工id，签到时间，班次 查询 （班次信息 日期类型）
     *
     * @param dto
     * @return
     */
    public EmpShiftInfo getEmpShiftDefByInfo(Long empid, Integer shiftDefId, Long workDate, WaAnalyzDTO dto) {
        EmpShiftInfo shiftdef = null;
        //如果对应的班次不存在，则去查询当前所属班次
        if (shiftDefId == null) {
            shiftdef = dto.getEmpShiftByDate(empid, workDate);
            if (shiftdef != null) {
                return shiftdef;
            }

//            WaShiftDef def = this.getEmpShiftInfo(workDate, empid);
//            if (def != null) {
//                shiftdef = new EmpShiftInfo();
//                org.springframework.beans.BeanUtils.copyProperties(def, shiftdef);
//                shiftdef.setEmpid(empid);
//                shiftdef.setWorkDate(workDate);
//
//                String key = empid + "_" + def.getShiftDefId() + "_" + workDate;
//                dto.getEmpShift().put(key, shiftdef);
//            }
        } else {
            shiftdef = dto.getEmpShift(empid, shiftDefId, workDate);
            if (shiftdef == null) {
                shiftdef = dto.getEmpShiftByDate(empid, workDate);
            }
        }
        return shiftdef;
    }

    /**
     * 根据签到时间 empid 查询员工对应的排班，包括非门店，门店排班--废弃
     *
     * @param regTime
     * @param empid
     * @return
     */
    @Deprecated
    public WaShiftDef getEmpShiftInfo(Long regTime, Long empid) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("empid", empid);
        Long belongDate = DateUtil.getDateLong(regTime * 1000, "yyyy-MM-dd", true);
        String date = DateUtil.convertDateTimeToStr(regTime, "yyyy-MM-dd", true);
        Integer ym = Integer.valueOf(date.substring(0, date.lastIndexOf("-")).replaceAll("-", ""));
        Integer day = Integer.valueOf(date.substring(date.lastIndexOf("-") + 1));
        params.put("workDate", belongDate);// 年月日的秒
        params.put("startdate", ym);//年月
        params.put("day", day);//日

        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);

        WaShiftDef shiftdef = null;
        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(empInfo.getBelongOrgId())) {
            shiftdef = remoteSmartWorkTimeService.getEmployeeShiftByDate(empInfo.getBelongOrgId(), empid, belongDate);
            if (shiftdef != null && shiftdef.getDateType() != null && shiftdef.getDateType() == 4) {
                shiftdef.setDateType(1);
            }
        } else {
            List<Map> shiftinfos = waRegisterRecordMapper.getEmpShiftRecord(params);
            if (CollectionUtils.isNotEmpty(shiftinfos) && shiftinfos.size() > 0) {

                Map map = shiftinfos.get(0);
                Integer shift_def_id = (Integer) map.get("shift_def_id");
                if (shift_def_id != null) {
                    shiftdef = new WaShiftDef();
                } else {
                    return shiftdef;
                }
                shiftdef.setShiftDefId(shift_def_id);
                //签到打卡开始时间
                Integer on_duty_start_time = (Integer) map.get("on_duty_start_time");
                shiftdef.setOnDutyStartTime(on_duty_start_time);
                //打卡截止
                Integer on_duty_end_time = (Integer) map.get("on_duty_end_time");
                shiftdef.setOnDutyEndTime(on_duty_end_time);
                //签退打卡开始时间
                Integer off_duty_start_time = (Integer) map.get("off_duty_start_time");
                shiftdef.setOffDutyStartTime(off_duty_start_time);
                Integer off_duty_end_time = (Integer) map.get("off_duty_end_time");
                shiftdef.setOffDutyEndTime(off_duty_end_time);
                //1、工作日，2休息日，3法定假日 4 公司特殊假日
                Integer dateType = (Integer) map.get("date_type");
                //如果是公司特殊假日，则按工作日类型计算
                if (dateType != null && dateType == 4) {
                    dateType = 1;
                }

                shiftdef.setDateType(dateType);
                Integer rest_total_time = (Integer) map.get("rest_total_time");
                shiftdef.setRestTotalTime(rest_total_time);
                Integer work_total_time = (Integer) map.get("work_total_time");
                shiftdef.setWorkTotalTime(work_total_time);
                Integer start_time = (Integer) map.get("start_time");
                shiftdef.setStartTime(start_time);
                Integer end_time = (Integer) map.get("end_time");
                shiftdef.setEndTime(end_time);
                Boolean is_noon_rest = (Boolean) map.get("is_noon_rest");
                shiftdef.setIsNoonRest(is_noon_rest);
                Integer noon_rest_start = (Integer) map.get("noon_rest_start");
                shiftdef.setNoonRestStart(noon_rest_start);
                Integer noon_rest_end = (Integer) map.get("noon_rest_end");
                shiftdef.setNoonRestEnd(noon_rest_end);

                // 是否启用了半天时间定义
                Boolean isHalfdayTime = (Boolean) map.get("isHalfdayTime");
                shiftdef.setIsHalfdayTime(isHalfdayTime);

                Integer halfdayTime = (Integer) map.get("halfdayTime");
                shiftdef.setHalfdayTime(halfdayTime);

                Boolean is_special = (Boolean) map.get("is_special");
                shiftdef.setIsSpecial(is_special);

                Integer special_work_time = (Integer) map.get("special_work_time");
                shiftdef.setSpecialWorkTime(special_work_time);
            }
        }
        return shiftdef;
    }

    private Integer analyzeOtRule(Integer time_duration, WaParseGroup parseGroup, Integer dateType, Integer compensateType, Map<Integer, WaOvertimeType> otMaps) throws JsonProcessingException {
        if (parseGroup != null && parseGroup.getOtPaseJsonb() != null) {
            PGobject pGobject = (PGobject) parseGroup.getOtPaseJsonb();
            List<OtAnalyzeRule> otParseRules = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<OtAnalyzeRule>>() {
            });
            if (CollectionUtils.isNotEmpty(otParseRules)) {
                for1:
                for (int i = 0; i < otParseRules.size(); i++) {
                    OtAnalyzeRule otAnalyzeRule = otParseRules.get(i);
                    if (StringUtils.isNotBlank(otAnalyzeRule.getOtTypeIds())) {

                        String[] otypeids = otAnalyzeRule.getOtTypeIds().split(",");
                        for2:
                        for (int j = 0; j < otypeids.length; j++) {
                            Integer ottypeid = Integer.valueOf(otypeids[j]);

                            if (MapUtils.isNotEmpty(otMaps) && otMaps.containsKey(ottypeid)) {
                                WaOvertimeType overtimeType = otMaps.get(ottypeid);

                                if (overtimeType.getDateType().equals(dateType) && overtimeType.getCompensateType().equals(compensateType)) {
//								规则 1=无, 2=向下取整15分钟 ,3 = 向上取整15分钟 ,4=向下取整 30分钟 ,5=向上取整30 分钟
                                    //如果实际加班小时数小于（最小有效时长）则加班时长归零
                                    if (otAnalyzeRule.getMinValidTime() != null && time_duration < otAnalyzeRule.getMinValidTime()) {
                                        // 如果小于最小时长，则跳出循环
                                        time_duration = 0;
                                        break for1;
                                    }
                                    switch (otAnalyzeRule.getRule()) {
                                        case 1:
                                            break;
                                        case 2:
                                            time_duration = getOtRoundingRule(time_duration.doubleValue(), false, 15);
                                            break;
                                        case 3:
                                            time_duration = getOtRoundingRule(time_duration.doubleValue(), true, 15);
                                            break;
                                        case 4:
                                            time_duration = getOtRoundingRule(time_duration.doubleValue(), false, 30);
                                            break;
                                        case 5:
                                            time_duration = getOtRoundingRule(time_duration.doubleValue(), true, 30);
                                            break;
                                        default:
                                            break;
                                    }
                                    break for1;
                                }
                            }
                        }
                    }
                }
            }
        }
        return time_duration;
    }

    public Integer getOtRoundingRule(Double min, boolean isUp, Integer roundMin) {
        double mod = min % roundMin;
        //向上取整
        if (isUp) {
            min = min + (roundMin - mod);
        } else {
            //向下取整
            min = min - mod;
        }
        return min.intValue();
    }

    // 把list转成Map 把签到时间作为key 时间精确到天
    private Map<String, WaRegisterRecord> generatRegMap(List<Map> list) {
        Map<String, WaRegisterRecord> map = new HashMap<>();

        if (list != null && list.size() > 0) {
            for (Map regMap : list) {
                Integer recordId = (Integer) regMap.get("record_id");
                Long empid = ConvertHelper.longConvert(regMap.get("empid"));
                Integer shift_def_id = (Integer) regMap.get("shift_def_id");
                Integer type = (Integer) regMap.get("type");
                Integer registerType = (Integer) regMap.get("register_type");
                Integer resultType = (Integer) regMap.get("result_type");
                String resultDesc = (String) regMap.get("result_desc");
                String normalAddr = (String) regMap.get("normal_addr");
                String regAddr = (String) regMap.get("reg_addr");
                String reason = (String) regMap.get("reason");
                String normalDate = (String) regMap.get("normal_date");
                Long regDateTime = (Long) regMap.get("reg_date_time");
                Long belongDate = (Long) regMap.get("belong_date");

                WaRegisterRecord record = new WaRegisterRecord();
                record.setShiftDefId(shift_def_id);
                record.setRecordId(recordId);
                record.setEmpid(empid);
                record.setRegisterType(registerType);
                record.setType(type);
                record.setResultType(resultType);
                record.setResultDesc(resultDesc);
                record.setNormalAddr(normalAddr);
                record.setRegAddr(regAddr);
                record.setReason(reason);
                record.setNormalDate(normalDate);
                record.setRegDateTime(regDateTime);
                record.setBelongDate(belongDate);

                map.put(empid + "_" + belongDate, record);
            }
        }

        return map;
    }
}
