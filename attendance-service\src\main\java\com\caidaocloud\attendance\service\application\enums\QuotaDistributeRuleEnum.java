package com.caidaocloud.attendance.service.application.enums;

public enum QuotaDistributeRuleEnum {
    ALL(1, "按全年额度发放"),
    CONVERT(2, "按入职日比例发放"),
    DIST_BY_HIRE_MONTH(3, "按入职月比例发放");

    private Integer index;
    private String name;

    QuotaDistributeRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (QuotaDistributeRuleEnum c : QuotaDistributeRuleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
