package com.caidaocloud.attendance.service.application.enums;

public enum OvertimeCalTypeEnum {
    MIN_APPLY_REGISTER(1, "申请时长与打卡时长取小值"),
    REGISTER_TIME(2, "以打卡时长为准");

    private Integer index;
    private String desc;

    OvertimeCalTypeEnum(Integer index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
