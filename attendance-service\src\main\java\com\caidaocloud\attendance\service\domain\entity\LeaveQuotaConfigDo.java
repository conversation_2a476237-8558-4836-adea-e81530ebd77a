package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.enums.ApplyQuotaEnum;
import com.caidaocloud.attendance.service.domain.repository.ILeaveQuotaConfigRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveQuotaConfigPo;
import com.caidaocloud.util.FastjsonUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Data
@Service
public class LeaveQuotaConfigDo {
    private Long configId;
    private String tenantId;
    private Integer leaveTypeId;
    private Integer distributionCycle;
    private Long disCycleStart;
    private Long disCycleEnd;
    private Integer validityPeriodType;
    private Float validityDuration;
    private Integer validityUnit;
    private Integer validityStartType;
    private Integer quotaDistributeRule;
    private Integer quotaRoundingRule;
    private Integer nowDistributeRule;
    private Integer nowRoundingRule;
    private Integer ifAdvance;
    private Integer expirationRule;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private Long carryOverTo;
    private Integer carryOverStartType;
    private Float carryOverValidityDuration;
    private Integer carryOverValidityUnit;
    private Integer convertRule;
    private Integer childRule;
    private Boolean validityExtension;
    private String invalidDate;
    private Integer invalidType;
    private String description;
    @ApiModelProperty("额度规则名称")
    private String ruleName;
    @ApiModelProperty("额度规则有效期生效日期，时间戳")
    private Long ruleStartDate;
    @ApiModelProperty("额度规则有效期失效日期，时间戳")
    private Long ruleEndDate;
    @ApiModelProperty("备注")
    private String remark;
    private Integer sort;
    @ApiModelProperty("是否包含产前假：0不包含1包含，仅产假有效")
    private Integer containPrenatalLeave;
    @ApiModelProperty("是否允许员工申请调休转付现,0关闭,1开启,默认关闭")
    private Integer applyCompensatoryCash;
    @ApiModelProperty("过期处理,1、作废,2、付现")
    private Integer expireHandle;
    private Set<ApplyQuotaEnum> applyTypes;
    @ApiModelProperty("有效额度申请上限")
    private Float validQuotaLimit;
    /**
     * 假期延期申请开关, true/允许，false/不允许，默认false
     */
    private Boolean leaveExtension;
    /**
     * 延期申请时间单位, 1天、2月
     */
    private Integer extensionUnit;
    /**
     * 延期申请最大申请次数
     */
    private Integer maxExtension;
    /**
     * 延期申请时间, 正整数
     */
    private Integer extensionTime;
    private String i18nRuleName;
    /**
     * 本年额度发放规则按照入职月比例发放时的临界日期
     */
    private Integer dayOfHireMonthDist;
    /**
     * 结转配额显示方式，1、结转单独生成额度 2、结转额度和当年额度合并显示
     */
    private Integer carryToType;
    /**
     * 自定义条件表达式
     */
    private String groupExpCondition;
    /**
     * 结转类型：1结转全部过期额度/2结转部分过期额度,默认1
     */
    private Integer transferType;
    /**
     * 最多结转额度
     */
    private Float maxTransferQuota;

    @Autowired
    private ILeaveQuotaConfigRepository leaveQuotaConfigRepository;

    public int save(LeaveQuotaConfigDo configDo) {
        return leaveQuotaConfigRepository.save(configDo);
    }

    public int update(LeaveQuotaConfigDo configDo) {
        return leaveQuotaConfigRepository.update(configDo);
    }

    public LeaveQuotaConfigDo getById(String tenantId, Integer leaveTypeId) {
        return leaveQuotaConfigRepository.getById(tenantId, leaveTypeId);
    }

    public List<LeaveQuotaConfigDo> getConfigListByIds(String tenantId, List<Integer> leaveTypeIds) {
        return leaveQuotaConfigRepository.getConfigListByIds(tenantId, leaveTypeIds);
    }

    public List<Map> getEmpFixedQuotaConfigList(String tenantId, Long curDate, String datafilter) {
        return leaveQuotaConfigRepository.getEmpFixedQuotaConfigList(tenantId, curDate, datafilter);
    }

    public List<LeaveQuotaConfigDo> getByLeaveTypeId(String tenantId, Integer leaveTypeId) {
        List<LeaveQuotaConfigDo> models = leaveQuotaConfigRepository.getByLeaveTypeId(tenantId, leaveTypeId);
        models.forEach(row -> row.setRuleName(LangParseUtil.getI18nLanguage(row.getI18nRuleName(), row.getRuleName())));
        return models;
    }

    public LeaveQuotaConfigDo getConfigById(String tenantId, Long configId) {
        return leaveQuotaConfigRepository.getConfigById(tenantId, configId);
    }

    public void delete(String tenantId, Long configId) {
        leaveQuotaConfigRepository.delete(tenantId, configId);
    }

    public void updateBatch(List<LeaveQuotaConfigDo> list) {
        leaveQuotaConfigRepository.updateBatch(list);
    }

    public List<LeaveQuotaConfigDo> getLeaveQuotaConfigs(String tenantId, Integer leaveTypeId, Long configId, String ruleName) {
        return leaveQuotaConfigRepository.getLeaveQuotaConfigs(tenantId, leaveTypeId, configId, ruleName);
    }

    public List<LeaveQuotaConfigDo> getLeaveQuotaConfigByIds(String tenantId, List<Long> configIds) {
        return leaveQuotaConfigRepository.getLeaveQuotaConfigByIds(tenantId, configIds);
    }

    public WaLeaveQuotaConfigPo convertToPo() {
        WaLeaveQuotaConfigPo configDo = FastjsonUtil.convertObject(this, WaLeaveQuotaConfigPo.class);
        return configDo;
    }

    public List<Map> loadStatus(List<Long> configIds) {
        return leaveQuotaConfigRepository.loadStatus(configIds);
    }

    public void enableQuotaConfig(Long quotaConfigId) {
        leaveQuotaConfigRepository.enableQuotaConfig(quotaConfigId);
    }

    public void disableQuotaConfig(Long quotaConfigId) {
        leaveQuotaConfigRepository.disableQuotaConfig(quotaConfigId);
    }

    public List<LeaveQuotaConfigDo> listByTenantId(String tenantId) {
        return leaveQuotaConfigRepository.selectListByTenantId(tenantId);
    }
}