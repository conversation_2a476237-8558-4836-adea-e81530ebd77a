package com.caidaocloud.attendance.service.application.dto;

import com.caidaocloud.attendance.service.interfaces.dto.quota.CompensotaryDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompensatoryDataDto {
    @ApiModelProperty("生效配额")
    private Float validQuota;
    @ApiModelProperty("生效配额单位：1天2小时")
    private Integer validQuotaUnit;
    @ApiModelProperty("失效配额")
    private Float inValidQuota;
    @ApiModelProperty("失效配额单位：1天2小时")
    private Integer inValidQuotaUnit;
    @ApiModelProperty("仅允许申请整数：true/false")
    private Boolean onlyInteger;
    @ApiModelProperty("生效配额")
    private List<CompensotaryDto> validQuotaList;
    @ApiModelProperty("失效配额")
    private List<CompensotaryDto> inValidQuotaList;

    public void convertToRounding(Integer roundingRule) {
        if (1 == roundingRule) {
            this.validQuota = BigDecimal.valueOf(validQuota != null ? validQuota : 0).setScale(0, RoundingMode.FLOOR).floatValue();
            this.inValidQuota = BigDecimal.valueOf(inValidQuota != null ? inValidQuota : 0).setScale(0, RoundingMode.FLOOR).floatValue();
        }
    }
}
