package com.caidaocloud.attendance.service.application.filter;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-01-20
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Autowired
    private SessionHolderHandlerInterceptorAdapter sessionHolderHandlerInterceptorAdapter;
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(sessionHolderHandlerInterceptorAdapter);
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 创建 FastJson 消息转换器
        FastJsonHttpMessageConverter converter = new FastJsonHttpMessageConverter();
        // 配置 Fastjson
        FastJsonConfig config = new FastJsonConfig();
        config.setSerializerFeatures(SerializerFeature.WriteBigDecimalAsPlain);
        converter.setFastJsonConfig(config);
        // 添加到转换器列表
        converters.add(0, converter);
    }
}
