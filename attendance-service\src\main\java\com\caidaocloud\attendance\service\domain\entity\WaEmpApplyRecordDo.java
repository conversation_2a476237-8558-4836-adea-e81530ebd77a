package com.caidaocloud.attendance.service.domain.entity;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.attendance.service.domain.repository.IWaEmpApplyRecordRepository;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

@Data
public class WaEmpApplyRecordDo {
    private Long id;
    private String tenantId;
    private String module;
    private Long entityId;
    private Long empId;
    private Long applyTime;
    private String businessKey;
    private Long startTime;
    private Long endTime;
    private Long replaceTime;
    private Integer status;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;

    public static PageResult<WaEmpApplyRecordDo> getPage(QueryPageBean queryPageBean) {
        return SpringUtil.getBean(IWaEmpApplyRecordRepository.class).getPage(queryPageBean);
    }
}