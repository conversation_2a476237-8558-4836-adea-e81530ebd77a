package com.caidaocloud.attendance.service.application.shimz.service;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.wa.dto.EmpQuoDto;
import com.caidao1.wa.dto.UsableCompensatoryQuotaDto;
import com.caidao1.wa.mybatis.mapper.EmpCompensatoryQuotaMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveTypeMapper;
import com.caidao1.wa.mybatis.mapper.WaMapper;
import com.caidao1.wa.mybatis.model.WaLeaveType;
import com.caidao1.wa.service.WaLeaveService;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.LeaveUpperTypeEnum;
import com.caidaocloud.attendance.service.application.enums.QuotaTypeEnum;
import com.caidaocloud.attendance.service.domain.entity.LeaveQuotaConfigDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDefDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDo;
import com.caidaocloud.web.ResponseWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 清水休假定制逻辑
 *
 * <AUTHOR>
 * @Date 2024/7/23
 */
@Slf4j
@Service
public class ShimzLeaveService implements ScriptBindable {
    private static final String SYSTEMANNUALLEAVE_DEF_CODE = "SystemAnnualLeave";
    @Autowired
    private LeaveQuotaConfigDo leaveQuotaConfigDo;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private EmpCompensatoryQuotaMapper waEmpCompensatoryQuotaMapper;
    @Autowired
    private WaLeaveTypeDefDo waLeaveTypeDefDo;
    @Autowired
    private WaLeaveService waLeaveService;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;

    /**
     * 申请休假时进行上级假期校验
     *
     * @param waLeaveType
     * @param empInfo
     * @param totalTimeDuration
     * @param quotaType
     * @param startDate
     * @param endDate
     * @param statutoryALQuotaName 法定年假配额名称
     * @return
     * @throws Exception
     */
    private Map<String, Object> checkUpperLevel(WaLeaveType waLeaveType, SysEmpInfo empInfo, BigDecimal totalTimeDuration,
                                                Integer quotaType, long startDate, long endDate,
                                                String statutoryALQuotaName) throws Exception {
         /*
             CAIDAOM-2894清水定制逻辑：
             1、申请年假时，检查年假是否设置上级假期且上级假期为调休，如果设置了上级假期则检查调休可用配额，
             如果调休可用配额<16H，则不限制休假优先级；如果调休可用配额>=16H，则检查法定年假额度是否有剩余，
             如果法定年假额度>0，则不限制休假优先级；如果法定年假额度=0，则提示请优先使用调休假。
             其他休假场景均走系统标准逻辑
         */
        // 查询假期定义类型
        WaLeaveTypeDefDo waLeaveTypeDef = waLeaveTypeDefDo.getById(waLeaveType.getBelongOrgid(), waLeaveType.getLeaveType());
        if (null == waLeaveTypeDef) {
            return waLeaveService.checkUpperLevel(waLeaveType, empInfo, totalTimeDuration, quotaType, startDate, endDate);
        }

        if (waLeaveTypeDef.getLeaveTypeDefCode().equals(SYSTEMANNUALLEAVE_DEF_CODE)) {
            // 申请的是年假
            // 仅支持上级假期类型设置为额度类型，其他设置均走系统标准逻辑
            if (null == waLeaveType.getUpperLevelQuotaTypeId()) {
                return waLeaveService.checkUpperLevel(waLeaveType, empInfo, totalTimeDuration, quotaType, startDate, endDate);
            }

            // 查询上级假期类型信息
            LeaveUpperTypeEnum upperType = LeaveUpperTypeEnum.QUOTA_TYPE;
            LeaveQuotaConfigDo upperQuotaConfigDo = leaveQuotaConfigDo.getConfigById(waLeaveType.getBelongOrgid(), waLeaveType.getUpperLevelQuotaTypeId());
            if (null == upperQuotaConfigDo) {
                return null;
            }
            WaLeaveType upperLevelLeaveType = waLeaveTypeMapper.selectByPrimaryKey(upperQuotaConfigDo.getLeaveTypeId());
            if (upperLevelLeaveType == null || waLeaveService.checkLeaveTypeIfEmpShow(upperLevelLeaveType) || waLeaveService.checkEmpLeaveAvailable(upperLevelLeaveType, empInfo)) {
                return null;
            }
            Integer upperLevelLeaveQuotaType = upperLevelLeaveType.getQuotaType();
            if (upperLevelLeaveQuotaType == null) {
                upperLevelLeaveQuotaType = upperLevelLeaveType.getLeaveType() == 3 ? QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex()
                        : QuotaTypeEnum.ISSUED_ANNUALLY.getIndex();
            }
            // 仅支持上级假期类型为调休，其他设置均走系统标准逻辑
            if (upperLevelLeaveType.getLeaveType() != 3 || !QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(upperLevelLeaveQuotaType)) {
                return waLeaveService.checkUpperLevel(waLeaveType, empInfo, totalTimeDuration, quotaType, startDate, endDate);
            }

            // 查询调休可用额度
            List<UsableCompensatoryQuotaDto> upperLevelCompensatoryQuotas = waEmpCompensatoryQuotaMapper.selectUsableCompensatoryQuotaList(empInfo.getEmpid(), upperLevelLeaveType.getLeaveTypeId(), startDate, endDate);
//            upperLevelCompensatoryQuotas = upperLevelCompensatoryQuotas.stream()
//                    .filter(o -> o.getQuotaConfigId().equals(upperQuotaConfigDo.getConfigId()))
//                    .collect(Collectors.toList());
            double usableQuota = 0d;
            if (CollectionUtils.isNotEmpty(upperLevelCompensatoryQuotas)) {
                usableQuota = upperLevelCompensatoryQuotas.stream().mapToDouble(UsableCompensatoryQuotaDto::getUsableDay).sum();
            }
            if (!waLeaveTypeDo.doCheckUpperLevelQuota(usableQuota, waLeaveType, upperLevelLeaveType, upperType)) {
                return null;
            }
            // 上级假期余额校验时长
            BigDecimal upperLevelQuotaCheck = BigDecimal.valueOf(waLeaveType.getUpperLevelQuotaCheck());
            if (upperLevelLeaveType.getAcctTimeType() == 2) {//小时
                upperLevelQuotaCheck = upperLevelQuotaCheck.multiply(new BigDecimal(60));
            }

            // 调休额度<16时，则不限制休假优先级
            double upperLevelQuotaLimit = upperLevelQuotaCheck.doubleValue();
            if (usableQuota < upperLevelQuotaLimit) {
                return null;
            }

            // 查询法定年假假期额度
            List<EmpQuoDto> annualLeaveQuotas = waMapper.selectUsableEmpQuotaList(empInfo.getEmpid(),
                    waLeaveType.getLeaveTypeId(), startDate, endDate);
            annualLeaveQuotas = annualLeaveQuotas.stream()
                    .filter(quota -> quota.getUsedDay() == null || quota.getUsedDay() <= 0 || quota.getUsableQuota() > 0)
                    .filter(quota -> statutoryALQuotaName.equals(quota.getQuotaConfigName()))
                    .collect(Collectors.toList());
            double annualLeaveUsableQuota = annualLeaveQuotas.stream().mapToDouble(EmpQuoDto::getUsableQuota).sum();
            if (annualLeaveUsableQuota > 0) {
                return null;
            }

            Map<String, Object> mapRtn = checkUpperLevelLeaveQuota(null, usableQuota, upperLevelLeaveType);
            if ((Integer) mapRtn.get("status") != 0) {
                return mapRtn;
            }
            return null;
        } else {
            return waLeaveService.checkUpperLevel(waLeaveType, empInfo, totalTimeDuration, quotaType, startDate, endDate);
        }
    }

    /**
     * 校验上级假期配额
     *
     * @param year
     * @param usableQuota
     * @param upperLevelLeaveType
     * @return
     */
    public Map<String, Object> checkUpperLevelLeaveQuota(Integer year, double usableQuota, WaLeaveType upperLevelLeaveType) {
        Map<String, Object> mapRtn = new HashMap<>();
        mapRtn.put("status", 0);
        mapRtn.put("message", "success");

        if (usableQuota <= 0) {
            return mapRtn;
        }

        Float upperRoundTimeUnit = upperLevelLeaveType.getRoundTimeUnit();
        upperRoundTimeUnit = upperRoundTimeUnit == null ? 0 : upperRoundTimeUnit;
        if (upperLevelLeaveType.getAcctTimeType() == 1) { //天
            if (usableQuota >= upperRoundTimeUnit) {
                //String msg = String.format("上级假期%s额度还未使用完，剩余%s天，请先使用%s假期", upperLevelLeaveType.getLeaveName(), usableQuota, upperLevelLeaveType.getLeaveName());
                String msg = String.format(ResponseWrap.wrapResult(AttendanceCodes.UPPER_LEAVE_DAY, null).getMsg(), upperLevelLeaveType.getLeaveName(), usableQuota, upperLevelLeaveType.getLeaveName());
                if (null != year && year != 1) {
                    //msg = String.format("%s年%s假期还未使用完，剩余%s天，请先使用%s假期", year, upperLevelLeaveType.getLeaveName(), usableQuota, upperLevelLeaveType.getLeaveName());
                    msg = String.format(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_DAY, null).getMsg(), year, upperLevelLeaveType.getLeaveName(), usableQuota, upperLevelLeaveType.getLeaveName());
                }

                mapRtn.put("status", -1);
                mapRtn.put("message", msg);
                return mapRtn;
            }
        } else { //小时
            if (usableQuota >= (60 * upperRoundTimeUnit)) {
                BigDecimal timeHour = BigDecimal.valueOf(usableQuota).divide(BigDecimal.valueOf(60), 2, RoundingMode.DOWN);
                //String msg = String.format("上级假期%s额度还未使用完，剩余%s小时，请先使用%s假期", upperLevelLeaveType.getLeaveName(), new DecimalFormat("0.00").format(timeHour.floatValue()), upperLevelLeaveType.getLeaveName());
                String msg = String.format(ResponseWrap.wrapResult(AttendanceCodes.UPPER_LEAVE_HOUR, null).getMsg(), upperLevelLeaveType.getLeaveName(), new DecimalFormat("0.00").format(timeHour.floatValue()), upperLevelLeaveType.getLeaveName());
                if (null != year) {
                    //msg = String.format("%s年%s假期还未使用完，剩余%s小时，请先使用%s假期", year, upperLevelLeaveType.getLeaveName(), new DecimalFormat("0.00").format(timeHour.floatValue()), upperLevelLeaveType.getLeaveName());
                    msg = String.format(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_HOUR, null).getMsg(), year, upperLevelLeaveType.getLeaveName(), new DecimalFormat("0.00").format(timeHour.floatValue()), upperLevelLeaveType.getLeaveName());
                }

                mapRtn.put("status", -1);
                mapRtn.put("message", msg);
                return mapRtn;
            }
        }

        mapRtn.put("message", "success");
        return mapRtn;
    }
}
