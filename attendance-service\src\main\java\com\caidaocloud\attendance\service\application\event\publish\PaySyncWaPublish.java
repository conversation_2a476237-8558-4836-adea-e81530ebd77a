package com.caidaocloud.attendance.service.application.event.publish;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.wa.mybatis.mapper.WaAnalyzeMapper;
import com.caidao1.wa.mybatis.model.WaAnalyze;
import com.caidao1.wa.mybatis.model.WaAnalyzeExample;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.service.application.dto.EmpAnalyzeResultDeleteDto;
import com.caidaocloud.attendance.service.application.dto.PaySyncDelWaAnalyzeDto;
import com.caidaocloud.attendance.service.application.dto.PaySyncWaAnalyzeDto;
import com.caidaocloud.attendance.service.application.event.publish.dto.WaAnalyzeInfoDto;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 同步考勤数据到薪资服务
 */
@Slf4j
@Service
public class PaySyncWaPublish {

    private final static String LEAVE_TYPE_DEF_EXCHANGE = "attendance.leaveTypeDef.fac.direct.exchange";
    private final static String LEAVE_TYPE_DEF_ROUTING_KEY = "routingKey.leaveTypeDef";

    private final static String WA_ANALYZE_EXCHANGE = "attendance.waAnalyze.fac.direct.exchange";
    private final static String WA_ANALYZE_ROUTING_KEY = "routingKey.waAnalyze";

    // 删除员工考勤分析数据
    private final static String WA_ANALYZE_DEL_EXCHANGE = "attendance.delWaAnalyze.fac.direct.exchange";
    private final static String WA_ANALYZE_DEL_ROUTING_KEY = "routingKey.delWaAnalyze";

    @Autowired
    private WaCommonService waCommonService;
    @Resource
    private MqMessageProducer producer;
    @Resource
    private WaAnalyzeMapper waAnalyzeMapper;
    @Value("${caidaocloud.data.wasync:false}")
    private boolean waSync;

    public void publishWaLeaveTypeDef(String msg) {
        PaySyncWaMessage message = PaySyncWaMessage.bulidMsg(msg, LEAVE_TYPE_DEF_EXCHANGE, LEAVE_TYPE_DEF_ROUTING_KEY);
        producer.publish(message);
    }

    private void publishWaAnalyze(PaySyncWaAnalyzeDto dto) {
        List<WaAnalyzeInfoDto> data = dto.getAnalyzeData();
        if (null == data || data.isEmpty()) {
            log.warn("publishWaAnalyze getAnalyzeData Empty");
            return;
        }
        log.warn("publishWaAnalyze getAnalyzeData size={}", data.size());
        String msg = JSON.toJSONString(dto);
        PaySyncWaMessage message = PaySyncWaMessage.bulidMsg(msg, WA_ANALYZE_EXCHANGE, WA_ANALYZE_ROUTING_KEY);
        producer.publish(message);
    }

    private void publishDelWaAnalyze(PaySyncDelWaAnalyzeDto dto) {
        List<EmpAnalyzeResultDeleteDto> dataList = dto.getDataList();
        if (null == dataList || dataList.isEmpty()) {
            return;
        }

        String msg = JSON.toJSONString(dto);
        PaySyncWaMessage message = PaySyncWaMessage.bulidMsg(msg, WA_ANALYZE_DEL_EXCHANGE, WA_ANALYZE_DEL_ROUTING_KEY);
        producer.publish(message);
    }

    @Async
    public void syncWaAnalyze(List<Long> empidList, String belongid, long starDate, long endDate) {
        if (!waSync) {
            // 未开启同步
            log.info("Attendance waAnalyze synchronization setting is not enabled");
            return;
        }
        try {
            Thread.sleep(5000);
        } catch (Exception e) {

        }
        int len = 500;
        int size = empidList.size();
        int num = size / len;
        if (size % len != 0) {
            ++num;
        }

        log.info("===total={}, num={}, syncWaAnalyze belongid={}, starDate={}, endDate={}", size, num, belongid, starDate, endDate);

        List empSubList = null;
        int fromIndex = 0;

        PaySyncWaAnalyzeDto synAnalyzeDto = new PaySyncWaAnalyzeDto();
        synAnalyzeDto.setBelongid(belongid);
        synAnalyzeDto.setStarDate(starDate);
        synAnalyzeDto.setEndDate(endDate);
        //查询班次
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongid);
        for (int i = 1; i <= num; ++i) {
            int toIndex = i * len;
            if (toIndex > size) {
                toIndex = size;
            }

            empSubList = empidList.subList(fromIndex, toIndex);
            fromIndex = toIndex;
            if (null == empSubList || empidList.isEmpty()) {
                continue;
            }
            WaAnalyzeExample example = new WaAnalyzeExample();
            WaAnalyzeExample.Criteria criteria = example.createCriteria();
            criteria.andBelongOrgIdEqualTo(belongid)
                    .andBelongDateGreaterThanOrEqualTo(starDate)
                    .andBelongDateLessThanOrEqualTo(endDate)
                    .andEmpidIn(empSubList);
            List<WaAnalyze> data = waAnalyzeMapper.selectByExample(example);
            List<WaAnalyzeInfoDto> list = Lists.newArrayList();
            if (null != data) {
                data = data.stream().map(waAnalyze -> {
                    waAnalyze.setLevelColumnJsonb(jsonb2Str(waAnalyze.getLevelColumnJsonb()));
                    waAnalyze.setOtColumnJsob(jsonb2Str(waAnalyze.getOtColumnJsob()));
                    waAnalyze.setOriginLevelColumnJsonb(jsonb2Str(waAnalyze.getOriginLevelColumnJsonb()));
                    waAnalyze.setOriginOtColumnJsonb(jsonb2Str(waAnalyze.getOriginOtColumnJsonb()));
                    waAnalyze.setExtCustomColJson(jsonb2Str(waAnalyze.getExtCustomColJson()));
                    return waAnalyze;
                }).collect(Collectors.toList());
                list = ObjectConverter.convertList(data, WaAnalyzeInfoDto.class);
                list.forEach(l -> {
                    if (shiftDefMap.containsKey(l.getShiftDefId())) {
                        l.setShiftDefName(shiftDefMap.get(l.getShiftDefId()).getShiftDefName());
                        l.setDateType(shiftDefMap.get(l.getShiftDefId()).getDateType());
                    }
                });
            }
            synAnalyzeDto.setEmpidList(empSubList);
            synAnalyzeDto.setAnalyzeData(list);
            this.publishWaAnalyze(synAnalyzeDto);
            log.info("===Execute to page={} Batch completion", i);
        }
    }

    /**
     * 同步删除员工考勤分析数据
     *
     * @param tenantId
     * @param empAnalyzeResultDelList
     */
    @Async
    public void syncDelWaAnalyze(String tenantId, List<EmpAnalyzeResultDeleteDto> empAnalyzeResultDelList) {
        if (!waSync) {
            // 未开启同步
            log.info("Attendance syncDelWaAnalyze synchronization setting is not enabled");
            return;
        }
        if (CollectionUtils.isEmpty(empAnalyzeResultDelList)) {
            if (log.isDebugEnabled()) {
                log.debug("syncDelWaAnalyze empAnalyzeResultDelList empty");
            }
            return;
        }
        if (log.isDebugEnabled()) {
            log.debug("syncDelWaAnalyze empAnalyzeResultDelList size={}", empAnalyzeResultDelList.size());
        }
        List<List<EmpAnalyzeResultDeleteDto>> dataLists = ListTool.split(empAnalyzeResultDelList, 500);
        PaySyncDelWaAnalyzeDto delWaAnalyzeDto = new PaySyncDelWaAnalyzeDto();
        delWaAnalyzeDto.setBelongOrgId(tenantId);
        for (List<EmpAnalyzeResultDeleteDto> dataList : dataLists) {
            delWaAnalyzeDto.setDataList(dataList);
            this.publishDelWaAnalyze(delWaAnalyzeDto);
        }
    }

    private String jsonb2Str(Object jsonbObj) {
        if (null == jsonbObj) {
            return null;
        }
        if (jsonbObj instanceof PGobject) {
            return ((PGobject) jsonbObj).getValue();
        }
        return jsonbObj.toString();
    }
}
