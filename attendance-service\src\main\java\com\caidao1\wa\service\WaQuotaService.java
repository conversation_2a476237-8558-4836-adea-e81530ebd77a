package com.caidao1.wa.service;

import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.payroll.common.PayEngineUtil;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidao1.wa.dto.EmpLeaveQuotaDto;
import com.caidao1.wa.enums.QuotaRoundingRuleEnum;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class WaQuotaService {

    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;

    /**
     * 处理折算-计算折算比率
     * TODO 此方法和考勤服务 QuotaService.getConvertRate 内容同步
     *
     * @param start
     * @param end
     * @param min
     * @param max
     * @return
     */
    public BigDecimal getConvertRate(Long start, Long end, Long min, Long max) {
        if (start == null || end == null || min == null || max == null) {
            return new BigDecimal(1);
        }

        Long calStart = min;
        Long calEnd = max;

        if (calEnd < calStart || calEnd < start) {
            return new BigDecimal(0);
        }
        if (calStart > end) {
            return new BigDecimal(1);
        }
        if (calStart < start) {
            calStart = start;
        }
        if (calEnd > end) {
            calEnd = end;
        }

        try {
            Integer maxDay = DateUtilExt.getDifferenceDay(start, end) + 1;
            Integer periodDay = DateUtilExt.getDifferenceDay(calStart, calEnd) + 1;

            BigDecimal rate = new BigDecimal(periodDay).divide(new BigDecimal(maxDay), 4, BigDecimal.ROUND_DOWN);

            if (rate.floatValue() > 1) {
                return new BigDecimal(1);
            }
            return rate;
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
            return new BigDecimal(0);
        }
    }

    /**
     * 处理进位
     * TODO 此方法和考勤服务 QuotaService.handleMantissa 内容同步
     *
     * @param val  配额数据 假期单位为天时，值单位为天 例如 2 天，假期单位小时时时，值单位为小时 例如 5 小时
     * @param unit 单位 1 天 2 小时
     * @param rule 折算规则
     * @return 假期单位为天时，返回值单位天，假期单位为小时时，返回值单位为分钟
     */
    public BigDecimal handleMantissa(String belongId, BigDecimal val, Integer unit, Integer rule) {
        //1 四舍五入保留整数、2 向上取整1、3 向下取整1、4 向上取整0.5、5 向下取整0.5
        if (rule != null) {
            if (rule.equals(1)) {
                //四舍五入保留整数
                String func = waConfigService.getFuncExp(belongId, "HandleMantissa" + rule);
                if (StringUtils.isNotEmpty(func)) {
                    Map params = new HashMap();
                    params.put("value", val);
                    val = groovyScriptEngine.executeBigDecimal(func, params);
                } else {
                    val = PayEngineUtil.handleMantissa(val, (short) 1, (short) 0);
                }
            } else if (rule.equals(2)) {
                //向上取整1
                val = PayEngineUtil.handleMantissa(val, (short) 2, (short) 0);
            } else if (rule.equals(4)) {
                //向上取整0.5
                val = PayEngineUtil.handleMantissa(val.divide(new BigDecimal(0.5)), (short) 2, (short) 0).multiply(new BigDecimal(0.5));
            } else if (rule.equals(3)) {
                //向下取整1
                val = PayEngineUtil.handleMantissa(val, (short) 3, (short) 0);
            } else if (rule.equals(5)) {
                //向下取整0.5
                val = PayEngineUtil.handleMantissa(val.divide(new BigDecimal(0.5)), (short) 3, (short) 0).multiply(new BigDecimal(0.5));
            }
        }
        if (unit != null && 2 == unit) {
            // 假期单位为小时时返回分钟数
            val = val.multiply(new BigDecimal(60));
        }
        return val;
    }

    /**
     * 按年发放的假期配额-计算当前配额
     * TODO 此方法和考勤服务 QuotaService.calNowQuota 内容同步
     *
     * @param lq
     * @return
     */
    public Float calNowQuota(EmpLeaveQuotaDto lq) {
        Long now = lq.getCalNowQuotaConvertDate() == null ? DateUtil.getOnlyDate() : lq.getCalNowQuotaConvertDate();
        // CAIDAOM-1374 如果当前日期大于等于发放周期结束日期，当前额度直接等于本年额度
        if (now >= lq.getDisCycleEnd()) {
            return lq.getWaEmpQuotaQuotaDay();
        }
        BigDecimal originalQuotaDay = lq.getOriginalQuotaDay();
        Integer acctTimeType = lq.getAcctTimeType();
        Integer nowRoundingRule = lq.getNowRoundingRule();
        if (nowRoundingRule == null) {
            if (lq.getAcctTimeType() == 1) {
                nowRoundingRule = QuotaRoundingRuleEnum.ROUND_DOW_HALF.getIndex();
            } else {
                nowRoundingRule = QuotaRoundingRuleEnum.ROUND_DOWN_1.getIndex();
            }
        }
        Long hireDate = Optional.ofNullable(lq.getHireDate()).orElse(0L);
        Long startDate = lq.getDisCycleStart();
        if (startDate != null && hireDate > startDate) {
            startDate = hireDate;
        }
        BigDecimal nowQuota;
        if (lq.getNowDistributeRule() != 1) {
            // 当前额度发放规则：1 按天，2 按全年额度发放
            nowQuota = originalQuotaDay;
        } else {
            Long terminationDate = lq.getTerminationDate();
            // 员工配额跨跨界时，根据临界点日期来判断当前配额的计算逻辑
            if (lq.getCrossQuotaDate() != null && lq.getCrossQuotaDate() > 0 && lq.getCrossQuotaDate() <= now && lq.getLastQuota() != null && lq.getNextQuota() != null) {
                Long convertDate = lq.getCrossQuotaDate();
                Long preDayConvertDate = DateUtil.addDate(convertDate * 1000, -1);
                if (terminationDate != null && terminationDate > hireDate) {
                    // 员工离职
                    if (terminationDate >= convertDate) {
                        BigDecimal last = new BigDecimal(lq.getLastQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, preDayConvertDate));
                        //Long endDate = terminationDate > now ? now : terminationDate;
                        BigDecimal next = new BigDecimal(lq.getNextQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), convertDate, terminationDate));
                        nowQuota = last.add(next);
                    } else {
                        nowQuota = originalQuotaDay.multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, terminationDate));
                    }
                } else {
                    BigDecimal last = new BigDecimal(lq.getLastQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, preDayConvertDate));
                    BigDecimal next = new BigDecimal(lq.getNextQuota()).multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), convertDate, now));
                    nowQuota = last.add(next);
                }
            } else {
                if (terminationDate != null && terminationDate > hireDate) {
                    // 员工离职
                    // Long endDate = terminationDate > now ? now : terminationDate;
                    nowQuota = originalQuotaDay.multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, terminationDate));
                } else {
                    nowQuota = originalQuotaDay.multiply(getConvertRate(lq.getDisCycleStart(), lq.getDisCycleEnd(), startDate, now));
                }
            }
        }
        if (acctTimeType != null && 2 == acctTimeType && nowQuota.floatValue() > 0) {
            nowQuota = nowQuota.divide(new BigDecimal(60), 4, BigDecimal.ROUND_DOWN);
        }
        if (nowQuota.floatValue() > 0) {
            nowQuota = handleMantissa(lq.getBelongOrgId(), nowQuota, lq.getAcctTimeType(), nowRoundingRule);
        }
        return nowQuota.floatValue();
    }
}
