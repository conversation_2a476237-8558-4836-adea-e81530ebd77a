package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.report.dto.PageBean;
import com.caidao1.ioc.util.ListsHelper;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class RuleService {

    @Autowired
    private WaAttendanceConfigService waConfigService;

    public List selectParseGroupList() {
        List<Map> list = waConfigService.getParseGroupList(new PageBean(true));
        return ListsHelper.convertBean2List(list, new ListsHelper.LabelValueBeanCreator<Map>() {
            @Override
            public Object getValue(Map t) {
                return t.get("parse_group_id");
            }

            @Override
            public String getLabel(Map t) {
                return (String) t.get("parse_group_name");
            }
        });
    }
}
