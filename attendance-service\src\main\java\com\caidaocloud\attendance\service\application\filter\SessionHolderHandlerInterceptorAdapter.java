package com.caidaocloud.attendance.service.application.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.caidao1.auth.mybatis.mapper.AuthCustRoleMapper;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.service.AuthEmpGroupService;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.StringUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.system.mybatis.mapper.SysCorpBaseMapper;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpBase;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.system.mybatis.model.SysUserInfo;
import com.caidaocloud.attendance.service.application.service.ICacheCommonService;
import com.caidaocloud.attendance.service.infrastructure.util.UserInfoHolder;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.CastUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.RequestHelper;
import com.caidaocloud.web.ResponseWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 适配 SessionHolder
 *
 * <AUTHOR>
 * @date 2020-01-20
 */
@Slf4j
@Component
public class SessionHolderHandlerInterceptorAdapter extends HandlerInterceptorAdapter {
    @Value("${httpSession.MaxInactiveInterval:3600}")
    private Integer httpSessionMaxInactiveInterval;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private AuthEmpGroupService authEmpGroupService;
    @Autowired
    private SysCorpBaseMapper sysCorpBaseMapper;
    @Autowired
    private AuthCustRoleMapper authCustRoleMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Value("${ignoreAuthList:}")
    private List<String> ignoreAuthList;
    @Value("${dataScope.dataScopeAuthList:}")
    private String dataScopeAuthList;
    @Value("${dataScope.open:}")
    private boolean isOpenDataScope;
    @Autowired
    @Lazy
    private ICacheCommonService cacheCommonService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (isSwaggerResourcesUri() || isUrlMatch(request.getRequestURI(), request.getMethod(), ignoreAuthList)) {
            return super.preHandle(request, response, handler);
        }
        boolean isFeignRequest = CastUtil.castBoolean(WebUtil.getRequest().getHeader(CommonConstant.IS_FEIGN_REQUEST), false);
        if (isFeignRequest) {
            // 如果是 fegin 请求，则不用处理权限
            return super.preHandle(request, response, handler);
        }
        UserInfo userInfo = sessionService.getUserInfo();
        if (userInfo == null) {
            SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
            if (null != securityUserInfo) {
                userInfo = new UserInfo();
                userInfo.setUserId(securityUserInfo.getUserId());
                userInfo.setStaffId(securityUserInfo.getEmpId());
                userInfo.setTenantId(securityUserInfo.getTenantId());
            }
        }
        if (null == userInfo || null == userInfo.getUserId()) {
            String errMsg = JSON.toJSONString(ResponseWrap.wrapResult(ErrorCodes.TOKEN_INVALID, "Access-Token illegal!", null), SerializerFeature.WriteMapNullValue);
            response.setStatus(200);
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json; charset=utf-8");
            response.getWriter().write(errMsg);
            return false;
        }
        if (isOpenDataScope) {
            String requestURI = request.getRequestURI();
            /*if (dataScopeAuthList != null && dataScopeAuthList.contains(requestURI)) {
                String dataScope = cacheCommonService.getOrgDataScope(userInfo.getTenantId(), userInfo.getUserId(), requestURI, "", userInfo.getStaffId());
                request.getSession().setAttribute("dataScope", dataScope);
            }*/
            String dataScope = cacheCommonService.getOrgDataScope(userInfo.getTenantId(), userInfo.getUserId(), requestURI, "", userInfo.getStaffId());
            request.getSession().setAttribute("dataScope", dataScope);
        }
        UserInfoHolder.setUserInfo(userInfo);
        Long userId = SessionHolder.getUserId();
        if (null != userId && userId.equals(userInfo.getUserid())) {
            return super.preHandle(request, response, handler);
        }

        doSessionHolder(request, userInfo2SysUserInfo(userInfo));
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        SessionHolder.reSetSessionBean();
        SecurityUserUtil.removeSecurityUserInfo();
        super.afterCompletion(request, response, handler, ex);
    }

    private SysUserInfo userInfo2SysUserInfo(UserInfo userInfo) {
        SysUserInfo sysUserInfo = FastjsonUtil.convertObject(userInfo, SysUserInfo.class);
        sysUserInfo.setUserid(userInfo.getUserId());
        sysUserInfo.setCorpid(Long.valueOf(userInfo.getTenantId()));
        sysUserInfo.setBelongOrgId(userInfo.getTenantId());
        sysUserInfo.setEmpid(userInfo.getStaffId());
        return sysUserInfo;
    }

    private void doSessionHolder(HttpServletRequest req, SysUserInfo userInfo) {
        if (null == userInfo) {
            return;
        }
        HttpSession session = req.getSession();
        session.setMaxInactiveInterval(httpSessionMaxInactiveInterval);
        session.setAttribute("corpid", userInfo.getCorpid());
        session.setAttribute("userid", userInfo.getUserid());
        session.setAttribute("account", userInfo.getAccount());
        session.setAttribute("issuperadmin", userInfo.getIssuperadmin());
        session.setAttribute("belongid", userInfo.getBelongOrgId());

        if (null != userInfo.getIssuperadmin() && userInfo.getIssuperadmin()) {
            session.setAttribute("orgid", userInfo.getBelongOrgId());
        }

        List<Integer> roleIds = null;
        if (userInfo.getEmpid() != null && userInfo.getEmpid().intValue() > 0) {
            session.setAttribute("empid", userInfo.getEmpid());
            SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(userInfo.getEmpid());
            if (null == empInfo) {
                return;
            }
            session.setAttribute("orgid", empInfo.getOrgid());
            session.setAttribute("empname", empInfo.getEmpName());
            session.setAttribute("engname", empInfo.getEngName());
            session.setAttribute("belongid", empInfo.getBelongOrgId());
            session.setAttribute("workno", empInfo.getWorkno());
            roleIds = authEmpGroupService.getUserRole(userInfo.getUserid(), empInfo.getEmpid(), empInfo.getBelongOrgId(), false, 1);
            session.setAttribute("roleIds", StringUtils.join(roleIds, ","));

        } else {
            roleIds = new ArrayList<>();//authEmpGroupService.getUserRole(userInfo.getUserid(), null, userInfo.getBelongOrgId(), false, 1);
            session.setAttribute("roleIds", StringUtils.join(roleIds, ","));
            if (userInfo.getEmpname() != null) {
                session.setAttribute("empname", userInfo.getEmpname());
            }
        }

        //获取用户绑定的角色
        if (CollectionUtils.isNotEmpty(roleIds)) {
            String anyRoleIds = StringUtils.join(roleIds, ",");
            List<String> resUrlList = authCustRoleMapper.getRoleResUrl(anyRoleIds);
            redisTemplate.opsForValue().set(RedisKeyDefine.ROLE_RES_PREFIX_ + userInfo.getUserid(), StringUtils.join(resUrlList, ","), 8, TimeUnit.HOURS);
            // session.setAttribute("resUrlList", resUrlList);

            String roleViewType = authCustRoleMapper.getUserRoleViewTypeByAllRole(anyRoleIds);
            session.setAttribute("roleViewType", roleViewType);

        } else {
            session.setAttribute("roleViewType", "3");
        }


        if (userInfo.getEmail() != null) {
            session.setAttribute("email", userInfo.getEmail());
        }
        if (userInfo.getMobnum() != null) {
            session.setAttribute("mobile", userInfo.getMobnum());
        }

        // 获取公司logo
        SysCorpOrg org = sysCorpOrgMapper.selectByPrimaryKey(ConvertHelper.longConvert(userInfo.getBelongOrgId()));
        if (null != org) {
            String logo = org.getLogo();
            if (StringUtil.isEmptyOrNull(logo)) {
                logo = "assets/caidao/img/logo.png";
            }
            session.setAttribute("logo", logo);
            session.setAttribute("corpname", org.getShortname());
        }

        //获取corp_client_id
        SysCorpBase corpbase = sysCorpBaseMapper.selectByPrimaryKey(userInfo.getCorpid());
        if (null == corpbase) {
            return;
        }
        session.setAttribute("corp_client_id", corpbase.getCorpClientId());
        session.setAttribute("corpcode", corpbase.getCorpcode());
        session.setAttribute("isProbation", corpbase.getIsProbation());
        session.setAttribute("probationDate", corpbase.getProbationDate());

        if (null != corpbase.getIsProbation() && corpbase.getIsProbation()) {
            Long current = DateUtil.getOnlyDate(new Date());
            Long probation = corpbase.getProbationDate();
            if (current > probation) {
                //试用到期
                session.setAttribute("isTrialExpires", true);
            }
        }
    }

    private boolean isUrlMatch(String apiPath, String method, List<String> urlList) {
        if (StringUtils.isEmpty(apiPath) || StringUtils.isEmpty(method)) {
            return true;
        }
        String apiPathLower = apiPath.toLowerCase().trim();
        if (RequestHelper.isStaticFile(apiPathLower)) {
            return true;
        }
        for (String i : urlList) {
            if (apiPathLower.startsWith(i.toLowerCase().trim())) {
                return true;
            }
        }
        return false;
    }

    private boolean isSwaggerResourcesUri() {
        HttpServletRequest httpServletRequest = RequestHelper.getRequest();
        String swaggerUri = httpServletRequest.getRequestURI();
        return null != swaggerUri && swaggerUri.indexOf("swagger") > -1;
    }
}
