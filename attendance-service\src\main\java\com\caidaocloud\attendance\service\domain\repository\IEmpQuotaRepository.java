package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaEmpQuotaDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpQuotaDetail;
import com.caidaocloud.attendance.service.interfaces.dto.FixQuotaDto;
import com.caidaocloud.attendance.service.interfaces.dto.FixQuotaSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.QuotaDto;
import com.caidaocloud.dto.UserInfo;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/4/19
 */
public interface IEmpQuotaRepository {
    int getEmpQuotaCountByYearAndType(QuotaDto dto);

    List<Long> getGroupEmpList(Integer groupId, Short year, Long curDate, String datafilter);

    PageList<WaEmpQuotaDo> getEmpFixQuotaList(FixQuotaSearchDto dto, String belongOrgId);

    void insertFixQuota(FixQuotaDto dto, UserInfo userInfo);

    void updateFixQuota(FixQuotaDto dto, UserInfo userInfo);

    WaEmpQuotaDo getEmpFixQuota(Long empQuotaId);

    List<WaEmpQuotaDo> getEmpFixQuotaByIds(String tenantId, List<Integer> empQuotaIds);

    void deleteFixQuota(Long empQuotaId);

    void deleteFixQuotas(String tenantId, List<Integer> empQuotaIds);

    List<Map> getCurrentlyEffectEmpQuotaList(String belongOrgId, Long curtime, Long empId);

    PageList<EmpQuotaDetail> getEmpQuotaDetailPageList(MyPageBounds pageBounds, String belongOrgId, Long curDate, Short year);

    int getWaEmpQuotaCountByLeaveTypeId(String belongOrgId, Integer leaveTypeId);

    int getWaEmpCompensatoryQuotaCountByLeaveTypeId(String tenantId, Integer leaveTypeId);

    int updateQuotaDayByAnnualQuota(String belongOrgId, Long crossQuotaDate, Long updtime);

    AttendancePageResult<Map> getIssuedAnnuallyQuotaListByEffectiveTime(AttendanceBasePage basePage, String belongOrgId, Long startDate, Long endDate, Long empId);

    List<WaEmpQuotaDo> getEmpQuotaList(String tenantId, List<Long> quotaIds);

    List<EmpQuotaDetail> getNotExpiredEmpQuotaListByYear(String tenantId, Integer year, Long curDate);
}
