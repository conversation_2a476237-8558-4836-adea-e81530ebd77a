package com.caidaocloud.attendance.core.auth.service;

import com.caidao1.system.mybatis.mapper.SysUserInfoMapper;
import com.caidao1.system.mybatis.model.SysUserInfo;
import com.caidao1.system.mybatis.model.SysUserInfoExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class UserService {

    @Autowired
    private SysUserInfoMapper sysUserInfoMapper;

    public SysUserInfo getUserByAccount(String account) {
        SysUserInfoExample example = new SysUserInfoExample();
        SysUserInfoExample.Criteria criteria = example.createCriteria();
        criteria.andAccountEqualTo(account.trim());
        List<SysUserInfo> list = sysUserInfoMapper.selectByExample(example);
        if (list == null || list.size() == 0) {
            return null;
        }
        return list.get(0);
    }
}
