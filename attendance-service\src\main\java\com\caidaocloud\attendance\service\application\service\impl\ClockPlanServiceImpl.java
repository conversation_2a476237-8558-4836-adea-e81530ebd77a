package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.system.mybatis.model.SysCorpOrgExample;
import com.caidao1.system.mybatis.model.SysIbeaconInfo;
import com.caidao1.system.mybatis.model.SysWifiInfo;
import com.caidao1.wa.mybatis.model.WaEmpGroup;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.clock.ClockPlanInfoDto;
import com.caidaocloud.attendance.service.application.dto.clock.VerifyResultDto;
import com.caidaocloud.attendance.service.application.enums.ClockWayEnum;
import com.caidaocloud.attendance.service.application.enums.ModuleTypeEnum;
import com.caidaocloud.attendance.service.application.service.IClockPlanService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.clock.*;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.schedule.application.service.dto.EmpWorkInfo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class ClockPlanServiceImpl implements IClockPlanService {

    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaClockPlan clockPlan;
    @Autowired
    private WaClockSiteDo waClockSiteDo;
    @Autowired
    private WaPlanEmpRel planEmpRel;
    @Autowired
    private BluetoothService bluetoothService;
    @Autowired
    private WifiService wifiService;
    @Autowired
    private SysEmpInfoDo sysEmpInfo;
    @Resource
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Resource
    private SysEmpInfoDo sysEmpInfoDo;
    @Autowired
    private DataBackupDo dataBackupDo;
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    @Override
    @Transactional
    public Result<Boolean> saveClockPlan(ClockPlanDto dto) {
        UserInfo userInfo = this.getUserInfo();
        Long corpId = Long.valueOf(userInfo.getTenantId());
        String belongOrgId = userInfo.getTenantId();
        String name = dto.getPlanName().trim();
        dto.setPlanName(name);
        List<WaClockPlan> oldPlans = clockPlan.getPlanByParams(corpId, belongOrgId, dto.getId(), dto.getPlanName().trim());
        if (CollectionUtils.isNotEmpty(oldPlans)) {
            String msg = String.format(ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_NAME_REPEAT, null).getMsg(), dto.getPlanName());
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, msg, Boolean.FALSE);
        }
        List<Long> empIds = sysEmpInfo.getEmpIdsByGroupExp(corpId, dto.getGroupExp(), null);
        if (CollectionUtils.isEmpty(empIds)) {
            return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_CONDITION_FILTER_EMPTY, Boolean.FALSE);
        }
        /*if (CollectionUtils.isNotEmpty(this.getSelectedEmployees(dto.getId(), empIds))) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "所选适用人员已存在于其他方案", Boolean.FALSE);
        }*/
        Long currentTime = System.currentTimeMillis();
        WaClockPlan plan = ObjectConverter.convert(dto, WaClockPlan.class);
        if (null != dto.getI18nPlanName()) {
            plan.setI18nPlanName(FastjsonUtil.toJson(dto.getI18nPlanName()));
        }
        Long userId = userInfo.getUserId();
        plan.setUpdater(userId);
        plan.setUpdateTime(System.currentTimeMillis());
        LogRecordContext.putVariable("name", plan.getPlanName());
        if (dto.getId() == null) {
            plan.setId(snowflakeUtil.createId());
            plan.setBelongOrgId(belongOrgId);
            plan.setCorpId(corpId);
            plan.setCreator(userId);
            plan.setCreateTime(currentTime);
            LogRecordContext.putVariable("operate", "新增");
            clockPlan.save(plan);
        } else {
            LogRecordContext.putVariable("operate", "编辑");
            clockPlan.update(plan);
        }
        if (CollectionUtils.isNotEmpty(dto.getConflictEmpIds())) {
            planEmpRel.updatePlanIdByParams(plan.getId(), userId, corpId, belongOrgId, dto.getConflictEmpIds());
        }

        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    private void batchInsert(List<WaPlanEmpRel> data, long start, long limit) {
        // 从start开始截取 截取limit条数据
        List<WaPlanEmpRel> items = data.stream().skip(start).limit(limit).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        // 批量插入数据的方法
        planEmpRel.saveBatch(items);
        // 递归每次插入limit条数据
        batchInsert(data, start + limit, limit);
    }

    private List<WaPlanEmpRel> getPlanEmpRel(Long planId, Long corpId, String belongOrgId, Long userId, List<Long> empIds) {
        List<WaPlanEmpRel> items = new ArrayList<>();
        Long currentTime = System.currentTimeMillis();
        List<Long> ids = new ArrayList<>();
        empIds.forEach(empId -> {
            WaPlanEmpRel planEmpRel = new WaPlanEmpRel();
            Long relId = snowflakeUtil.createId();
            if (ids.contains(relId)) {
                log.error("打卡方案，适用人员关系表雪花算法生成id:{}重复", relId);
                relId = snowflakeUtil.createId();
            }
            ids.add(relId);
            planEmpRel.setId(relId);
            planEmpRel.setBelongOrgId(belongOrgId);
            planEmpRel.setCorpId(corpId);
            planEmpRel.setPlanId(planId);
            planEmpRel.setEmpId(empId);
            planEmpRel.setCreator(userId);
            planEmpRel.setCreateTime(currentTime);
            planEmpRel.setUpdater(userId);
            planEmpRel.setUpdateTime(currentTime);
            items.add(planEmpRel);
        });
        return items;
    }

    @Override
    public ClockPlanInfoDto getClockPlan(Long id) {
        WaClockPlan plan = clockPlan.getClockPlanById(id);
        if (null == plan) {
            return null;
        }
        ClockPlanInfoDto dto = ObjectConverter.convert(plan, ClockPlanInfoDto.class);
        if (StringUtils.isNotBlank(plan.getI18nPlanName())) {
            dto.setI18nPlanName(FastjsonUtil.toObject(plan.getI18nPlanName(), Map.class));
        } else if (StringUtils.isNotBlank(plan.getPlanName())) {
            Map<String, String> i18nName = new HashMap<>();
            i18nName.put("default", plan.getPlanName());
            dto.setI18nPlanName(i18nName);
        }
        List<String> clockWays = Stream.of(dto.getClockWay().split(",")).collect(Collectors.toList());
        if (clockWays.contains(ClockWayEnum.GPS.getIndex().toString())) {
            //查询打卡地点（GPS）
            List<Long> siteIds = Arrays.stream(plan.getGps().split(",")).map(Long::valueOf).collect(Collectors.toList());
            List<WaClockSiteDo> sites = waClockSiteDo.getClockSiteListByIds(siteIds);
            sites.forEach(site -> dto.getGps().add(new KeyValue(site.getSiteName(), site.getId())));
        }
        if (clockWays.contains(ClockWayEnum.WIFI.getIndex().toString())) {
            //查询wifi
            List<Integer> wifiIds = Arrays.stream(plan.getWifi().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            List<SysWifiInfo> wifi = wifiService.getWifiListByIds(wifiIds);
            if (CollectionUtils.isNotEmpty(wifi)) {
                wifi.forEach(w -> dto.getWifi().add(new KeyValue(w.getSsid(), w.getWifiId())));
            }
        }
        if (clockWays.contains(ClockWayEnum.BLUETOOTH.getIndex().toString())) {
            // 查询蓝牙
            List<Integer> bluetoothIds = Arrays.stream(plan.getBluetooth().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            List<SysIbeaconInfo> bluetooth = bluetoothService.getBluetoothListByIds(bluetoothIds);
            if (CollectionUtils.isNotEmpty(bluetooth)) {
                bluetooth.forEach(b -> dto.getBluetooth().add(new KeyValue(b.getBluetoothName(), b.getIbeaconId())));
            }
        }
        UserInfo userInfo = sessionService.getUserInfo();
        //查询适配人员
        dto.setEmployees(planEmpRel.getEmployeesByPlanId(userInfo.getTenantId(), plan.getId()));
        return dto;
    }

    @Override
    @Transactional
    public void deleteClockPlan(Long id) {
        WaClockPlan planById = clockPlan.getClockPlanById(id);
        LogRecordContext.putVariable("name", planById.getPlanName());
        int rows = clockPlan.deleteClockPlanById(id);
        if (rows > 0) {
            planEmpRel.deleteByPlanId(id, getUserInfo().getTenantId());
        }

    }

    @Override
    public AttendancePageResult<ClockPlanPageInfoDto> getClockPlanList(ClockPlanPageDto dto) {
        UserInfo userInfo = this.getUserInfo();
        AttendancePageResult<ClockPlanPageInfoDto> result = new AttendancePageResult<>();
        AttendancePageResult<WaClockPlan> pageResult = clockPlan.getPlanPageList(dto, ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), dto.getKeywords());
        List<WaClockPlan> list = pageResult.getItems();
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> allSitIds = new ArrayList<>();
            Map<Long, List<Long>> planSiteMap = new HashMap<>();
            List<Integer> allWifiIds = new ArrayList<>();
            Map<Long, List<Integer>> planWifiMap = new HashMap<>();
            List<Integer> allBluetoothIds = new ArrayList<>();
            Map<Long, List<Integer>> planBluetoothMap = new HashMap<>();
            list.forEach(plan -> {
                // GPS
                if (StringUtils.isNotBlank(plan.getGps())) {
                    List<Long> siteIds = Arrays.stream(plan.getGps().split(",")).map(Long::valueOf).distinct().collect(Collectors.toList());
                    allSitIds.addAll(siteIds);
                    planSiteMap.put(plan.getId(), siteIds);
                }
                // wifi
                if (StringUtils.isNotBlank(plan.getWifi())) {
                    List<Integer> wifiIds = Arrays.stream(plan.getWifi().split(",")).map(Integer::valueOf).distinct().collect(Collectors.toList());
                    allWifiIds.addAll(wifiIds);
                    planWifiMap.put(plan.getId(), wifiIds);
                }
                // 蓝牙
                if (StringUtils.isNotBlank(plan.getBluetooth())) {
                    List<Integer> bluetoothIds = Arrays.stream(plan.getBluetooth().split(",")).map(Integer::valueOf).distinct().collect(Collectors.toList());
                    planBluetoothMap.put(plan.getId(), bluetoothIds);
                    allBluetoothIds.addAll(bluetoothIds);
                }
                plan.setPlanName(LangParseUtil.getI18nLanguage(plan.getI18nPlanName(), plan.getPlanName()));
            });
            // 查询打卡地点（GPS）
            List<WaClockSiteDo> sites = null;
            if (CollectionUtils.isNotEmpty(allSitIds)) {
                sites = waClockSiteDo.getClockSiteListByIds(allSitIds);
            }
            // 查询wifi
            List<SysWifiInfo> wifi = null;
            if (CollectionUtils.isNotEmpty(allWifiIds)) {
                wifi = wifiService.getWifiListByIds(allWifiIds);
            }
            // 查询蓝牙
            List<SysIbeaconInfo> bluetooth = null;
            if (CollectionUtils.isNotEmpty(allBluetoothIds)) {
                bluetooth = bluetoothService.getBluetoothListByIds(allBluetoothIds);
            }
//            // 查询适用人员
//            List<Long> planIds = list.stream().map(WaClockPlan::getId).collect(Collectors.toList());
//            List<EmpInfoKeyValue> planEmployees = planEmpRel.getEmployeesByPlanIds(userInfo.getBelongOrgId(), planIds);
//            Map<Long, List<EmpInfoKeyValue>> planEmpMap = planEmployees.stream().collect(Collectors.groupingBy(EmpInfoKeyValue::getPlanId));
            List<ClockPlanPageInfoDto> items = ObjectConverter.convertList(list, ClockPlanPageInfoDto.class);
            List<WaClockSiteDo> finalSites = sites;
            List<SysWifiInfo> finalWifi = wifi;
            List<SysIbeaconInfo> finalBluetooth = bluetooth;
            items.forEach(item -> {
                // GPS
                if (CollectionUtils.isNotEmpty(finalSites)) {
                    finalSites.stream().filter(s -> planSiteMap.get(item.getId()) != null && planSiteMap.get(item.getId()).contains(s.getId())).forEach(s -> item.getGps().add(new KeyValue(s.getSiteName(), s.getId())));
                }
                // wifi
                if (CollectionUtils.isNotEmpty(finalWifi)) {
                    finalWifi.stream().filter(w -> planWifiMap.get(item.getId()) != null && planWifiMap.get(item.getId()).contains(w.getWifiId())).forEach(w -> item.getWifi().add(new KeyValue(w.getSsid(), w.getWifiId())));
                }
                // 蓝牙
                if (CollectionUtils.isNotEmpty(finalBluetooth)) {
                    finalBluetooth.stream().filter(b -> planBluetoothMap.get(item.getId()) != null && planBluetoothMap.get(item.getId()).contains(b.getIbeaconId())).forEach(b -> item.getBluetooth().add(new KeyValue(b.getBluetoothName(), b.getIbeaconId())));
                }
                // 适用人员
//                if (planEmpMap.containsKey(item.getId()) && CollectionUtils.isNotEmpty(planEmpMap.get(item.getId()))) {
//                    item.setEmployees(planEmpMap.get(item.getId()));
//                }
            });
            result.setItems(items);
            result.setPageNo(pageResult.getPageNo());
            result.setPageSize(pageResult.getPageSize());
            result.setTotal(pageResult.getTotal());
        }
        return result;
    }

    @Override
    public List<VerifyResultDto> verifySelectedEmployees(Long planId, List<Long> empIds) {
        List<VerifyResultDto> items = this.getSelectedEmployees(planId, empIds);
        List<Long> existsEmpIds = items.stream().map(VerifyResultDto::getEmpId).distinct().collect(Collectors.toList());
        empIds.removeAll(existsEmpIds);
        if (CollectionUtils.isNotEmpty(empIds)) {
            empIds.forEach(empId -> items.add(new VerifyResultDto(null, "", empId, "", "")));
        }
        return items;
    }

    @Override
    public List<VerifyResultDto> getSelectedEmployees(Long planId, List<Long> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return new ArrayList<>();
        }
        UserInfo userInfo = this.getUserInfo();
        List<WaClockPlan> list = clockPlan.getPlanRelEmployeesByEmpIds(planId, ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), empIds);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(list, VerifyResultDto.class);
    }

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Override
    public List<ClockPlanDto> getPlanListBySiteId(Long corpId, String belongOrgId, Long id) {
        List<WaClockPlan> list = clockPlan.getPlanListBySiteId(corpId, belongOrgId, id);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(list, ClockPlanDto.class);
    }

    private void doParseFilterList(EmpClockPlanReqDto dto) {
        if (CollectionUtils.isEmpty(dto.getFilterList())) {
            return;
        }
        Iterator<FilterBean> it = dto.getFilterList().iterator();
        while (it.hasNext()) {
            FilterBean filterBean = it.next();
            // 方案状态
            if ("effectiveStatus".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    dto.setEffectiveStatus(filterBean.getMin());
                }
                it.remove();
            }
        }
    }

    @Override
    @CDText(exp = {"empStyle:empStyleName" + TextAspect.DICT_E, "empStatus:empStatusName" + TextAspect.STATUS_ENUM}, classType = EmpClockPlanDto.class)
    public AttendancePageResult<EmpClockPlanDto> getEmpClockPlans(EmpClockPlanReqDto reqDto, UserInfo userInfo) {
        doParseFilterList(reqDto);
        AttendancePageResult<EmpClockPlanDto> dtoPageResult = new AttendancePageResult<>();
        PageBean pageBean = PageUtil.getPageBean(reqDto);
        AttendanceBasePage basePage = ObjectConverter.convert(reqDto, AttendanceBasePage.class);
        String filter = pageBean.getFilter();
        if (StringUtils.isNotBlank(filter)) {
            if (filter.contains("planId")) {
                filter = filter.replaceAll("\"planId\"", "wper.plan_id");
            }
            if (filter.contains("orgid")) {
                filter = filter.replaceAll("\"orgid\"", "ei.orgid");
            }
            if (filter.contains("hire_date")) {
                filter = filter.replaceAll("\"hire_date\"", "ei.hire_date");
            }
            if (filter.contains("termination_date")) {
                filter = filter.replaceAll("\"termination_date\"", "ei.termination_date");
            }
            if (filter.contains("stats")) {
                filter = filter.replaceAll("\"stats\"", "ei.stats");
            }
            if (filter.contains("employ_type")) {
                filter = filter.replaceAll("\"employ_type\"", "ei.employ_type");
            }
        }
        if (StringUtils.isNotBlank(reqDto.getDataScope())) {
            if (StringUtils.isNotBlank(filter)) {
                filter = filter + reqDto.getDataScope();
            } else {
                filter = reqDto.getDataScope();
            }
        }
        AttendancePageResult<WaPlanEmpRel> pageResult = planEmpRel.getEmpClockPlanList(basePage, userInfo.getTenantId(),
                reqDto.getPlanId(), filter, reqDto.getEffectiveStatusFilterValue());
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaPlanEmpRel> doList = pageResult.getItems();
            List<EmpClockPlanDto> dtoList = ObjectConverter.convertList(doList, EmpClockPlanDto.class);
            Long nowDate = DateUtil.getOnlyDate();
            dtoList.forEach(d -> {
                EmpInfoDTO empInfoDTO = ObjectConverter.convert(d, EmpInfoDTO.class);
                empInfoDTO.setName(d.getEmpName());
                d.setEmpInfo(empInfoDTO);
                d.setPlanName(LangParseUtil.getI18nLanguage(d.getI18nPlanName(), d.getPlanName()));
                d.doSetEffectiveStatus(nowDate);
            });
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setTotal(pageResult.getTotal());
            dtoPageResult.setItems(dtoList);
        }
        return dtoPageResult;
    }

    @Override
    public Result<Boolean> saveEmpClockPlan(EmpClockPlanInfoDto dto) {
        // 校验方案是否有效
        WaClockPlan waClockPlan = clockPlan.getClockPlanById(dto.getPlanId());
        if (null == waClockPlan) {
            return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_NOT_EXIST, Boolean.FALSE);
        }
        UserInfo userInfo = getUserInfo();
        WaPlanEmpRel waPlanEmpRel = ObjectConverter.convert(dto, WaPlanEmpRel.class);
        long currentTime = System.currentTimeMillis() / 1000;
        waPlanEmpRel.setCorpId(ConvertHelper.longConvert(userInfo.getTenantId()));
        waPlanEmpRel.setBelongOrgId(userInfo.getTenantId());
        waPlanEmpRel.setUpdateTime(currentTime);
        waPlanEmpRel.setUpdater(userInfo.getUserId());
        waPlanEmpRel.setEmpId(dto.getEmpInfo().getEmpId());
        if (dto.getId() == null) {
            waPlanEmpRel.setId(snowflakeUtil.createId());
            waPlanEmpRel.setCreateTime(currentTime);
            waPlanEmpRel.setCreator(userInfo.getUserId());
            List<WaPlanEmpRel> clockPlanList = planEmpRel.getEmpClockPlanByPeriod(dto.getEmpInfo().getEmpId(), null, userInfo.getTenantId(), null, null);
            if (CollectionUtils.isNotEmpty(clockPlanList)) {
                Optional<WaPlanEmpRel> opt = clockPlanList.stream().max(Comparator.comparing(WaPlanEmpRel::getStartTime));
                if (opt.isPresent()) {
                    WaPlanEmpRel rel = opt.get();
                    if (waPlanEmpRel.getStartTime() > rel.getStartTime() && waPlanEmpRel.getStartTime() <= rel.getEndTime() && waPlanEmpRel.getEndTime() >= rel.getEndTime()) {
                        rel.setEndTime(DateUtil.addDate(waPlanEmpRel.getStartTime() * 1000, -1));
                        rel.setUpdateTime(currentTime);
                        rel.setUpdater(userInfo.getUserId());
                        planEmpRel.update(rel);
                        planEmpRel.save(waPlanEmpRel);
                    } else if (waPlanEmpRel.getStartTime() > rel.getStartTime() && waPlanEmpRel.getEndTime() > rel.getEndTime()) {
                        planEmpRel.save(waPlanEmpRel);
                    } else {
                        log.error("员工该时间内已存在打卡方案，请修改原方案时间！");
                        return ResponseWrap.wrapResult(AttendanceCodes.NEW_CLOCK_PLAN_TIME_OVERLAP, Boolean.FALSE);
                    }
                }
            } else {
                planEmpRel.save(waPlanEmpRel);
            }
        } else {
            // 校验有效期开始时间结束时间是否存在重叠
            List<WaPlanEmpRel> clockPlanList = planEmpRel.getEmpClockPlanByPeriod(dto.getEmpInfo().getEmpId(), dto.getId(), userInfo.getTenantId(), dto.getStartTime(), dto.getEndTime());
            if (CollectionUtils.isNotEmpty(clockPlanList)) {
                log.error("员工该时间内已存在打卡方案，请修改原方案时间！");
                return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_TIME_OVERLAP, Boolean.FALSE);
            }
            planEmpRel.update(waPlanEmpRel);
        }
        return Result.ok(Boolean.TRUE);
    }

    @Override
    @Transactional
    public void deleteEmpClockPlan(Long id) {
        String tenantId = getUserInfo().getTenantId();
        Optional<WaPlanEmpRel> opt = Optional.ofNullable(planEmpRel.getPlanEmpRelById(id, tenantId));
        planEmpRel.deleteByIds(tenantId, Collections.singletonList(id));
        opt.ifPresent(r -> dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_PLAN.name(), ModuleTypeEnum.EMP_PLAN.getTable(), Collections.singletonList(opt.get()), getUserInfo())));
    }

    @Override
    public EmpClockPlanDto getEmpClockPlan(Long id) {
        UserInfo userInfo = sessionService.getUserInfo();
        WaPlanEmpRel waPlanEmpRel = planEmpRel.getPlanEmpRelById(id, userInfo.getTenantId());
        if (null == waPlanEmpRel) {
            return null;
        }
        EmpClockPlanDto dto = ObjectConverter.convert(waPlanEmpRel, EmpClockPlanDto.class);
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(getUserInfo().getTenantId(), waPlanEmpRel.getEmpId());
        EmpInfoDTO empInfoDTO = ObjectConverter.convert(empInfo, EmpInfoDTO.class);
        empInfoDTO.setEmpId(empInfo.getEmpid());
        empInfoDTO.setName(empInfo.getEmpName());
        dto.setEmpInfo(empInfoDTO);
        return dto;
    }

    @Override
    public List<KeyValue> getClockPlanOptions() {
        UserInfo userInfo = getUserInfo();
        List<WaClockPlan> items = clockPlan.getPlanByParams(ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), null, null);
        List<KeyValue> list = new ArrayList<>();
        items.forEach(i -> list.add(new KeyValue(LangParseUtil.getI18nLanguage(i.getI18nPlanName(), i.getPlanName()), i.getId())));
        return list;
    }

    @Override
    public void synchronizeClockPlan(String belongOrgId, Long userId, Long corpId) {
        // 查询所有的打卡方案
        log.info("查询所有的打卡方案开始");
        List<WaClockPlan> plans = clockPlan.getPlanByParams(corpId, belongOrgId, null, null);
        log.info("查询所有的打卡方案结束，总条数：[{}]", plans.size());
        if (CollectionUtils.isNotEmpty(plans)) {
            // 查询已分配员工打卡方案
            log.info("查询已分配员工打卡方案开始");
            List<WaPlanEmpRel> list = planEmpRel.getEmpClockPlanByPeriod(null, null, belongOrgId, null, null);
            log.info("查询已分配员工打卡方案结束，总条数：[{}]", list.size());
            List<Long> assignedEmpIds = list.stream().map(WaPlanEmpRel::getEmpId).distinct().collect(Collectors.toList());
            log.info("更新已分配员工打卡方案开始");
            updateAssignedEmpPlans(corpId, belongOrgId, userId, assignedEmpIds, list, plans);
            log.info("更新已分配员工打卡方案结束");
            List<Long> unAssignedEmpIds = Lists.newArrayList();
            List<WaPlanEmpRel> unAssignedEmpPlans = Lists.newArrayList();
            log.info("筛选未生成过打卡方案的员工开始");
            for (WaClockPlan plan : plans) {
                if (StringUtils.isNotEmpty(plan.getGroupExp())) {
                    // 根据打卡方案匹配规则筛选员工
                    List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, plan.getGroupExp(), null);
                    if (CollectionUtils.isNotEmpty(groupEmpIds)) {
                        groupEmpIds.removeAll(assignedEmpIds);
                        unAssignedEmpIds.addAll(groupEmpIds);
                        for (Long groupEmpId : groupEmpIds) {
                            // 初始化员工打卡方案
                            unAssignedEmpPlans.add(getEmpClockPlan(corpId, belongOrgId, groupEmpId, userId, plan.getId(), null));
                        }
                    }
                }
            }
            log.info("筛选未生成过打卡方案的员工结束");
            // 将未生成过打卡方案的员工自动生成员工打卡方案
            log.info("未生成过打卡方案的员工自动生成员工打卡方案开始");
            if (CollectionUtils.isNotEmpty(unAssignedEmpPlans)) {
                //人员去重
                List<Long> empIdList = unAssignedEmpIds.stream().distinct().collect(Collectors.toList());
                unAssignedEmpPlans = unAssignedEmpPlans.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WaPlanEmpRel::getEmpId))), ArrayList::new));
                // 查询员工信息
                List<SysEmpInfo> unAssignedEmployees = Lists.newArrayList();
                List<List<Long>> lists = ListTool.split(empIdList, 500);
                for (List<Long> empInfos : lists) {
                    if (CollectionUtils.isEmpty(empInfos)) {
                        continue;
                    }
                    unAssignedEmployees.addAll(sysEmpInfoDo.getEmpInfoByIds(belongOrgId, empInfos));
                }
                if (CollectionUtils.isNotEmpty(unAssignedEmployees)) {
                    // 员工信息Map
                    Map<Long, SysEmpInfo> unAssignedEmployeeMap = unAssignedEmployees.stream().collect(Collectors.toMap(SysEmpInfo::getEmpid, Function.identity(), (k1, k2) -> k2));
                    Iterator<WaPlanEmpRel> iterator = unAssignedEmpPlans.iterator();
                    while (iterator.hasNext()) {
                        WaPlanEmpRel waPlanEmpRel = iterator.next();
                        SysEmpInfo unAssignedEmployee = unAssignedEmployeeMap.get(waPlanEmpRel.getEmpId());
                        if (unAssignedEmployee != null) {
                            //生效时间默认1970-1-1
                            waPlanEmpRel.setStartTime(0L);//unAssignedEmployee.getHireDate()
                        } else {
                            iterator.remove();
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(unAssignedEmpPlans)) {
                    // 保存员工打卡方案
                    log.info("保存员工打卡方案");
                    planEmpRel.saveBatch(unAssignedEmpPlans);
                    log.info("保存员工打卡方案结束");
                }
            }
            log.info("未生成过打卡方案的员工自动生成员工打卡方案结束");
        }
    }

    public void updateAssignedEmpPlans(Long corpId, String tenantId, Long userId, List<Long> assignedEmpIds, List<WaPlanEmpRel> assignedEmpPlans, List<WaClockPlan> empPlans) {
        if (CollectionUtils.isEmpty(assignedEmpIds) || CollectionUtils.isEmpty(assignedEmpPlans) || CollectionUtils.isEmpty(empPlans)) {
            return;
        }
        List<EmpWorkInfo> assignedEmployees = sysEmpInfoDo.getEmpInfoList(tenantId, assignedEmpIds.stream().map(String::valueOf).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(assignedEmployees)) {
            return;
        }
        Map<Long, EmpWorkInfo> assignedEmployeeMap = assignedEmployees.stream().collect(Collectors.toMap(emp -> Long.valueOf(emp.getEmpId()), Function.identity(), (k1, k2) -> k2));
        List<WaPlanEmpRel> updateEmpPlans = Lists.newArrayList();
        List<Long> deleteEmpPlans = Lists.newArrayList();
        Map<Long, List<WaPlanEmpRel>> empPlanMap = assignedEmpPlans.stream().collect(Collectors.groupingBy(WaPlanEmpRel::getEmpId));
        List<Long> unAssignedEmpIds = Lists.newArrayList();
        Map<Long, WaClockPlan> empPlansMap = empPlans.stream().collect(Collectors.toMap(WaClockPlan::getId, Function.identity(), (k1, k2) -> k2));
        List<WaPlanEmpRel> addEmpPlans = Lists.newArrayList();
        Map<Long, List<Long>> matchEmpMap = new HashMap<>();
        for (Long empId : assignedEmpIds) {
            if (!assignedEmployeeMap.containsKey(empId)) {
                continue;
            }
            EmpWorkInfo empInfo = assignedEmployeeMap.get(empId);
            Long terminationDate = empInfo.getLeaveDate();
            List<WaPlanEmpRel> empPlanRelList = empPlanMap.get(empId);
            Long hireDate = empInfo.getHireDate();
            if (null == terminationDate) {
                long today = DateUtil.getOnlyDate();
                //生效中的方案
                Optional<WaPlanEmpRel> effectiveOpt = empPlanRelList.stream().filter(g -> today >= g.getStartTime() && today < g.getEndTime()).max(Comparator.comparing(WaPlanEmpRel::getStartTime));
                WaPlanEmpRel newEmpScheme;
                if (effectiveOpt.isPresent()) {//有生效中的方案
                    WaPlanEmpRel effective = effectiveOpt.get();
                    if (!checkEmpGroup(empId, corpId, effective.getPlanId(), empPlansMap)) {//不满足生效中方案适用条件，则重新分配方案
                        //生效中的方案，因条件不匹配，过期掉，失效日期未当前日期（零点）前一秒
                        effective.setEndTime(today - 1);
                        if (effective.getEndTime() < effective.getStartTime()) {
                            effective.setEndTime(effective.getStartTime() + 1);
                            effective.setStartTime(effective.getEndTime());
                        }
                        effective.setUpdater(userId == null ? 0 : userId);
                        effective.setUpdateTime(DateUtil.getCurrentTime(true));
                        updateEmpPlans.add(effective);
                        //查找是否有匹配的方案
                        newEmpScheme = getMatchEmpScheme(tenantId, empId, userId, corpId, today, empPlansMap, empPlanRelList, matchEmpMap);
                        if (null != newEmpScheme) {//匹配到
                            setStartTime(hireDate, newEmpScheme, empPlanRelList, effective);
                            addEmpPlans.add(newEmpScheme);
                        }
                    }
                } else {//无生效中的方案
                    //查找是否有匹配的方案
                    newEmpScheme = getMatchEmpScheme(tenantId, empId, userId, corpId, today, empPlansMap, empPlanRelList, matchEmpMap);
                    if (null != newEmpScheme) {//匹配到
                        setStartTime(hireDate, newEmpScheme, empPlanRelList, null);
                        addEmpPlans.add(newEmpScheme);
                    }
                }
            } else {
                if (null != hireDate && Optional.ofNullable(empInfo.getReentry()).orElse(false)) {
                    //再入职
                    if (assignedEmpPlans.stream().anyMatch(g -> g.getEmpId().equals(empId) && g.getStartTime() >= hireDate)) {
                        continue;
                    }
                    Optional<WaPlanEmpRel> empPlanOpt = empPlanRelList.stream()
                            .filter(g -> terminationDate > g.getStartTime() && terminationDate + 86399 < g.getEndTime())
                            .max(Comparator.comparing(WaPlanEmpRel::getStartTime));
                    if (empPlanOpt.isPresent()) {
                        WaPlanEmpRel empPlan = empPlanOpt.get();
                        if (hireDate > empPlan.getStartTime() && hireDate < empPlan.getEndTime()) {
                            Long planId = empPlan.getId();
                            if (checkEmpGroup(empId, corpId, planId, empPlansMap)) {
                                addEmpPlans.add(getEmpClockPlan(corpId, tenantId, empId, userId, planId, hireDate));
                            } else {
                                unAssignedEmpIds.add(empId);
                            }
                        } else {
                            unAssignedEmpIds.add(empId);
                        }
                        empPlan.setEndTime(DateUtil.addDate(terminationDate * 1000, 1) - 1);
                        empPlan.setUpdateTime(DateUtil.getCurrentTime(true));
                        empPlan.setUpdater(userId == null ? 0 : userId);
                        updateEmpPlans.add(empPlan);
                    } else {
                        unAssignedEmpIds.add(empId);
                    }
                } else if ("1".equals(empInfo.getEmpStatus().getValue())) {
                    //已离职
                    deleteEmpPlans.addAll(empPlanRelList.stream().filter(g -> g.getStartTime() > terminationDate)
                            .map(WaPlanEmpRel::getId).collect(Collectors.toList()));
                    Optional<WaPlanEmpRel> empPlanOpt = empPlanRelList.stream()
                            .filter(g -> terminationDate > g.getStartTime() && terminationDate + 86399 < g.getEndTime())
                            .max(Comparator.comparing(WaPlanEmpRel::getStartTime));
                    if (empPlanOpt.isPresent()) {
                        WaPlanEmpRel empPlan = empPlanOpt.get();
                        empPlan.setEndTime(DateUtil.addDate(terminationDate * 1000, 1) - 1);
                        empPlan.setUpdateTime(DateUtil.getCurrentTime(true));
                        empPlan.setUpdater(userId == null ? 0 : userId);
                        updateEmpPlans.add(empPlan);
                    }
                }
            }
        }
        //重新分配
        if (CollectionUtils.isNotEmpty(unAssignedEmpIds)) {
            List<WaPlanEmpRel> unAssignedEmpPlans = Lists.newArrayList();
            for (WaClockPlan plan : empPlans) {
                if (StringUtils.isEmpty(plan.getGroupExp())) {
                    continue;
                }
                // 根据规则筛选员工
                List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, plan.getGroupExp(), null);
                if (CollectionUtils.isEmpty(groupEmpIds)) {
                    continue;
                }
                groupEmpIds.retainAll(unAssignedEmpIds);
                for (Long groupEmpId : groupEmpIds) {
                    EmpWorkInfo empInfo = assignedEmployeeMap.get(groupEmpId);
                    unAssignedEmpPlans.add(getEmpClockPlan(corpId, tenantId, groupEmpId, userId, plan.getId(), empInfo.getHireDate()));
                }
            }
            if (CollectionUtils.isNotEmpty(unAssignedEmpPlans)) {
                addEmpPlans.addAll(unAssignedEmpPlans);
            }
        }
        if (CollectionUtils.isNotEmpty(addEmpPlans)) {
            addEmpPlans = addEmpPlans.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WaPlanEmpRel::getEmpId))), ArrayList::new));
            //保存员工打卡方案
            planEmpRel.saveBatch(addEmpPlans);
        }
        //离职，在入职，更新
        if (CollectionUtils.isNotEmpty(updateEmpPlans)) {
            for (WaPlanEmpRel row : updateEmpPlans) {
                planEmpRel.update(row);
            }
        }
        //离职，删除
        if (CollectionUtils.isNotEmpty(deleteEmpPlans)) {
            List<List<Long>> deleteLists = ListTool.split(deleteEmpPlans, 50);
            for (List<Long> list : deleteLists) {
                planEmpRel.deleteByIds(tenantId, list);
            }
        }
    }

    private void setStartTime(Long hireDate, WaPlanEmpRel empScheme, List<WaPlanEmpRel> empPlanRelList, WaPlanEmpRel invalid) {
        if (null == empScheme) {
            return;
        }
        Long minDate = empScheme.getStartTime();
        if (null != hireDate) {
            minDate = Math.min(hireDate, minDate);
        }
        if (null != invalid) {
            minDate = DateUtil.getOnlyDate(new Date((invalid.getEndTime() + 86400) * 1000));
        } else if (CollectionUtils.isNotEmpty(empPlanRelList)) {
            Long latestStartTime = empPlanRelList.stream().max(Comparator.comparing(WaPlanEmpRel::getStartTime)).map(WaPlanEmpRel::getEndTime).orElse(null);
            if (null != latestStartTime && minDate < latestStartTime) {
                minDate = DateUtil.getOnlyDate(new Date((latestStartTime + 86400) * 1000));
            }
        }
        empScheme.setStartTime(minDate);
    }

    public boolean checkEmpGroup(Long empId, Long corpId, Long planId, Map<Long, WaClockPlan> empPlansMap) {
        if (!empPlansMap.containsKey(planId)) {
            return false;
        }
        WaClockPlan assignedEmpPlan = empPlansMap.get(planId);
        if (StringUtils.isEmpty(assignedEmpPlan.getGroupExp())) {
            return false;
        }
        // 根据规则筛选员工
        List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, assignedEmpPlan.getGroupExp(), null);
        if (CollectionUtils.isEmpty(groupEmpIds)) {
            return false;
        }
        return groupEmpIds.contains(empId);
    }


    /**
     * 匹配的员工分组
     *
     * @param tenantId      租户
     * @param empId         员工
     * @param userId        用户
     * @param corpId        集团
     * @param today         当前日期（零点）
     * @param empSchemeMap  员工表达式分组
     * @param empSchemeList 员工方案
     * @param matchEmpMap
     * @return
     */
    public WaPlanEmpRel getMatchEmpScheme(String tenantId, Long empId, Long userId, Long corpId, Long today, Map<Long, WaClockPlan> empSchemeMap, List<WaPlanEmpRel> empSchemeList, Map<Long, List<Long>> matchEmpMap) {
        WaPlanEmpRel newEmpScheme = null;
        for (Map.Entry<Long, WaClockPlan> entry : empSchemeMap.entrySet()) {
            if (checkEmpSchemeMatchCondition(empId, corpId, entry.getKey(), entry.getValue().getGroupExp(), matchEmpMap)) {
                //匹配到方案
                newEmpScheme = getEmpClockPlan(corpId, tenantId, empId, userId, entry.getKey(), today);
                break;
            }
        }
        //判断是否有未生效方案，如有，则当前方案的失效日期取未生效方案开始日期（零点）前一秒
        Optional<WaPlanEmpRel> notYetEffectiveEmpSchemeOpt = empSchemeList.stream().filter(g -> g.getStartTime() > today).min(Comparator.comparing(WaPlanEmpRel::getStartTime));
        if (null != newEmpScheme && notYetEffectiveEmpSchemeOpt.isPresent()) {
            long startTime = notYetEffectiveEmpSchemeOpt.get().getStartTime();
            long endTime = notYetEffectiveEmpSchemeOpt.get().getEndTime();
            long startDate = DateUtil.getOnlyDate(new Date(startTime * 1000));
            if (startTime == endTime && (startTime - startDate == 1)) {
                return newEmpScheme;
            }
            newEmpScheme.setEndTime(notYetEffectiveEmpSchemeOpt.get().getStartTime() - 1);
        }
        return newEmpScheme;
    }

    /**
     * 校验方案匹配条件
     *
     * @param empId       员工
     * @param corpId      集团
     * @param planId      条件分组
     * @param exp         表达式
     * @param matchEmpMap 匹配的员工集合
     * @return
     */
    public boolean checkEmpSchemeMatchCondition(Long empId, Long corpId, Long planId, String exp, Map<Long, List<Long>> matchEmpMap) {
        if (com.caidaocloud.util.StringUtil.isBlank(exp)) {
            return false;
        }
        //根据规则筛选员工
        List<Long> empIds;
        if (!matchEmpMap.containsKey(planId)) {
            empIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, exp, null);
            matchEmpMap.put(planId, empIds);
        } else {
            empIds = matchEmpMap.get(planId);
        }
        return empIds.contains(empId);
    }

    @Transactional
    @Override
    public void synchronizeClockPlan() {
        SysCorpOrgExample example = new SysCorpOrgExample();
        example.createCriteria().andOrgtype2EqualTo(1).andStatusEqualTo(1);
        List<SysCorpOrg> items = sysCorpOrgMapper.selectByExample(example);
        for (SysCorpOrg item : items) {
            log.info("自动同步员工打卡方案，参数:[belongOrgId:{}，corpId:{}]", item.getBelongOrgId(), item.getCorpid());
            try {
                synchronizeClockPlan(item.getOrgid().toString(), 0L, item.getCorpid());
            } catch (Exception e) {
                log.error("自动同步员工打卡方案异常：{}", e.getMessage(), e);
            }
            log.info("自动同步员工打卡方案结束");
        }
    }

    private WaPlanEmpRel getEmpClockPlan(Long corpId, String belongOrgId, Long empId, Long userId, Long planId, Long hireDate) {
        long currentTime = System.currentTimeMillis() / 1000;
        WaPlanEmpRel rel = new WaPlanEmpRel();
        rel.setId(snowflakeUtil.createId());
        rel.setPlanId(planId);
        rel.setCorpId(corpId);
        rel.setBelongOrgId(belongOrgId);
        rel.setEmpId(empId);
        // 9999-12-31
        rel.setEndTime(253402271999L);
        if (null != hireDate) {
            rel.setStartTime(hireDate);
        }
        rel.setCreator(userId);
        rel.setCreateTime(currentTime);
        rel.setUpdater(userId);
        rel.setUpdateTime(currentTime);
        return rel;
    }

    @Override
    public ClockPlanInfoDto getEmpPlan() {
        UserInfo userInfo = getUserInfo();
        WaPlanEmpRel waPlanEmpRel = planEmpRel.getPlanEmpRelByEmpIdAndPeriod(userInfo.getTenantId(), userInfo.getStaffId(), System.currentTimeMillis() / 1000);
        if (null == waPlanEmpRel) {
            return null;
        }
        Optional<WaClockPlan> planOpt = Optional.ofNullable(clockPlan.getClockPlanById(waPlanEmpRel.getPlanId()));
        ClockPlanInfoDto clockPlanInfoDto = null;
        if (planOpt.isPresent()) {
            clockPlanInfoDto = ObjectConverter.convert(planOpt.get(), ClockPlanInfoDto.class);
        }
        return clockPlanInfoDto;
    }

    @Override
    @Transactional
    public void deleteEmpClockPlans(List<Long> ids) {
        String tenantId = getUserInfo().getTenantId();
        List<WaPlanEmpRel> list = planEmpRel.getByIds(tenantId, ids);
        planEmpRel.deleteByIds(tenantId, ids);
        if (CollectionUtils.isNotEmpty(list)) {
            dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_PLAN.name(), ModuleTypeEnum.EMP_PLAN.getTable(), list, getUserInfo()));
        }
    }
}