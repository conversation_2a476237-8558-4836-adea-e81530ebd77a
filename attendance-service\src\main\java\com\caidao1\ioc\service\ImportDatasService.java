package com.caidao1.ioc.service;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.utils.*;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.dto.*;
import com.caidao1.ioc.mybatis.mapper.ImportOtherMapper;
import com.caidao1.ioc.mybatis.mapper.IocImportMapper;
import com.caidao1.ioc.mybatis.mapper.SysImportFuncMapper;
import com.caidao1.ioc.mybatis.mapper.SysImportTemplateMapper;
import com.caidao1.ioc.mybatis.model.SysImportFunc;
import com.caidao1.ioc.mybatis.model.SysImportTemplate;
import com.caidao1.ioc.mybatis.model.SysImportTemplateExample;
import com.caidao1.ioc.util.*;
import com.caidao1.mobile.bean.SessionBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.service.config.ioc.config;
import com.caidao1.system.mybatis.model.SysParmDict;
import com.caidao1.xss.test.cache.RedisService;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.imports.service.application.service.ioc.ImportCacheService;
import com.caidaocloud.imports.service.application.service.ioc.ImportConfigService;
import com.caidaocloud.imports.service.application.service.ioc.ImportOrgTemplateService;
import com.caidaocloud.imports.service.application.service.system.ParameterService;
import com.caidaocloud.oss.dto.UploadResult;
import com.caidaocloud.oss.service.OssService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.GsonBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.Jedis;

import java.io.InputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class ImportDatasService {

    private static final String PAY_SOB_EMP_TABLE = "pay_sob_emp";
    private Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private IocImportMapper iocImportMapper;
    @Autowired
    private SysImportFuncMapper sysImportFuncMapper;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private ImportCacheService importCacheService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ParameterService parameterService;
    @Autowired
    private ImportOrgTemplateService importOrgTemplateService;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private ImportOtherMapper importOtherMapper;
    @Autowired
    private ImportConfigService importConfigService;
    @Autowired
    private OssService ossService;
    @Autowired
    private SysImportTemplateMapper sysImportTemplateMapper;

    private static ConcurrentHashMap<String, Object> checkCache = new ConcurrentHashMap<String, Object>();
    private static ConcurrentHashMap<String, Long> redisCache = new ConcurrentHashMap<String, Long>();

    /**
     * 根据table取得字段
     *
     * @return
     */
    public List<Map> selectFuncList(String chnName) {
        return iocImportMapper.selectFuncList(chnName);
    }

    /**
     * 根据功能取得关联模型
     *
     * @return
     */
    public List<Map> selectModelListByFuncId() {
        return iocImportMapper.selectModelListByFuncId(SessionHolder.getBelongOrgId(), null);
    }

    /**
     * 根据模型取得关联表
     *
     * @param modelId
     * @return
     */
    public List<Map> selectTableListByModelId(PageBean pageBean, String belongId, Integer modelId) {
        Map params = new HashMap();
        params.put("filter", pageBean.getFilter());
        params.put("modelId", modelId);
        params.put("belongId", belongId);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount());
        return iocImportMapper.selectTableListByModelId(pageBounds, params);
    }

    /**
     * 根据table取得字段
     *
     * @param tableId
     * @return
     */
    public List<Map> selectFieldListByTableId(String tableId) {
        return iocImportMapper.selectFieldListByTableId(tableId);
    }

    /**
     * 根据model取得字段
     *
     * @param modelId
     * @return
     */
    public List<Map> selectFieldListByModelId(Integer modelId) {
        return iocImportMapper.selectFieldListByModelId(SessionHolder.getBelongOrgId(), modelId);
    }

    /**
     * 根据字段取得校验规则列表
     *
     * @param field
     * @return
     */
    public List<Map> selectCheckRuleListByField(String field) {
        return iocImportMapper.selectCheckRuleListByField(field);
    }


    /**
     * 导入文件
     *
     * @param file
     * @param belongId
     * @return
     * @throws Exception
     */
    public ImportResult importFile(MultipartFile file, String belongId, Integer resId) throws Exception {
        ImportResult ir = new ImportResult();
        InputStream in = null;
        try {
            String filename = file.getOriginalFilename();
            String bucketName = String.format("%s-%s", belongId, belongId);
            UploadResult vo = ossService.upload(bucketName, file);
            if (vo == null || vo.getObjectPath() == null) {
                ir.setMessage("上传服务异常");
                ir.setResult(false);
                return ir;
            }
            log.info("上传返回信息,{}-原文件名{}", vo, filename);
            in = ossService.getInputStream(vo.getObjectPath());

            String fileType = FilenameUtils.getExtension(filename);
            IocFileReader reader = IocReaderFactory.getReader(fileType, in);

            ir.setFileName(String.valueOf(vo.getId()));
            String fix = filename.substring(filename.lastIndexOf(".") + 1);
            if (!BaseConst.IMPORT_FILE_TYPES.contains(fix)) {
                ir.setResult(false);
                ir.setMessage("导入文件格式不正确，请选择Excel或CSV文件重新导入！");
                return ir;
            }
            if (reader != null) {
                List<String> titles = reader.getTitle();
                if (titles != null) {
                    List<String> titlesSet = new ArrayList<>();
                    titlesSet.addAll(titles);
                    if (titlesSet.size() < titles.size()) {

                        Map<String, Integer> map = new HashMap<>();
                        for (String str : titles) {
                            Integer i = 1; //定义一个计数器，用来记录重复数据的个数
                            if (map.get(str) != null) {
                                i = map.get(str) + 1;
                            }
                            map.put(str, i);
                        }
                        log.error("重复数据的个数：{}", map.toString());

                        String titleStr = "";
                        for (String s : map.keySet()) {
                            if (map.get(s) > 1) {
                                titleStr += s + " ";
                            }
                        }

                        log.error("重复的数据为：{}", titleStr);

                        ir.setResult(false);
                        ir.setMessage("文件表头列名不可重复,请检查表头是否有重复列或空白列！");
                    } else {
                        List<Map> temp = iocImportMapper.selectTemplateByResId(resId, belongId);
                        if (CollectionUtils.isNotEmpty(temp)) {

                            Map tempMap = new HashMap();
                            String md5 = IocUtil.getTitleMd5(titles);
                            log.info("md5={},titles={}", md5, JSON.toJSONString(titles));
                            boolean exist = false;
                            for (Map template : temp) {
                                // 2.0 修改为模版持续更新
                                if (null != template.get("templateId")) {
                                    int delTemplates = importOtherMapper.delTemplate((Integer) template.get("templateId"));
                                    log.info("清除已存在模版信息：-{}", delTemplates);
                                }
                                //获取当前表字段导入信息
                                List<Map> templateAll = importConfigService.selectFieldListByFuncId(UserContext.getTenantId(), (Integer) template.get("importFuncId"), null);
                                TemplateConfigDto templateConfigDto = new TemplateConfigDto();
                                templateConfigDto.setFuncId((Integer) template.get("importFuncId"));
                                templateConfigDto.setTemplateId((Integer) template.get("templateId"));
                                List<TemplateFieldDto> templateFieldDtos = new ArrayList<>();
                                for (String s : titlesSet) {
                                    for (Map map2 : templateAll) {
                                        if (s.equals(map2.get("fieldName"))) {
                                            TemplateFieldDto t = new TemplateFieldDto();
                                            t.setColName(s);
                                            t.setFieldRegId((String) map2.get("fieldRegId"));
                                            templateFieldDtos.add(t);
                                        }
                                    }
                                }
                                for (Map map : templateAll) {
                                    //休假处理
                                    if (("wa_emp_leave".equals(map.get("tableName")) && "time_slot".equals(map.get("fieldCode")))) {
                                        TemplateFieldDto t = new TemplateFieldDto();
                                        t.setColName("请假时间段");
                                        t.setFieldRegId((String) map.get("fieldRegId"));
                                        templateFieldDtos.add(t);
                                    }

                                }
                                templateConfigDto.setFieldList(templateFieldDtos);
                                log.info("2.0更新模版信息-{}", JSON.toJSON(templateConfigDto));
                                importConfigService.saveTemplateField(belongId, SessionHolder.getUserId() == null ? -1 : SessionHolder.getUserId(), String.valueOf(vo.getId()), templateConfigDto);
                                log.info("执行更新模版结束");
                                exist = true;
                                tempMap = template;
                                break;
//                                }
                            }
                            if (exist) {
                                ir.setFuncId((Integer) tempMap.get("importFuncId"));
                                ir.setTemplateId((Integer) tempMap.get("templateId"));
                                ir.setTemplateName((String) tempMap.get("templateName"));
                            } else {
                                ir.setFuncId((Integer) temp.get(0).get("importFuncId"));
                            }
                        }
                    }
                }
            } else {
                ir.setResult(false);
                ir.setMessage("导入文件格式不正确，请选择Excel或CSV文件重新导入！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            ir.setResult(false);
            log.error(e.getMessage());
            ir.setMessage("导入文件发生错误！");
        } finally {
            in.close();
        }
        //首次上传处理
        if (ir.getFuncId() != null) {
            SysImportTemplateExample example = new SysImportTemplateExample();
            example.createCriteria().andImportFuncIdEqualTo(ir.getFuncId()).andBelongOrgidEqualTo(SessionHolder.getBelongOrgId());
            List<SysImportTemplate> list = sysImportTemplateMapper.selectByExample(example);
            if (list.size() > 0 && null != list) {
                ir.setTemplateId(list.get(0).getTemplateId());
                ir.setTemplateName(list.get(0).getMd5());
                ir.setFileName(list.get(0).getTemplateName());
            } else {
                ir.setMessage("未知异常->模版无法生成");
            }
        }
        return ir;
    }


    /**
     * 确认数据
     *
     * @param rtnFileName
     * @param templateId
     * @return
     * @throws Exception
     */
    public Map getImportDataList(PageBean pageBean, String rtnFileName, Integer templateId) throws Exception {

        InputStream in = null;
        String fileType = null;
        IocFileReader reader = null;
        try {
            rtnFileName = importOtherMapper.getPathUrlFileName(Long.valueOf(rtnFileName), SessionHolder.getBelongOrgId());
            fileType = rtnFileName.substring(rtnFileName.lastIndexOf(".") + 1, rtnFileName.length());
            in = ossService.getInputStream(rtnFileName);
            reader = IocReaderFactory.getReader(fileType, in);
        } catch (Exception e) {
            log.info("获取文件流异常");
        } finally {
            in.close();
        }
        List<String> titles = reader.getTitle(0, 0);
        List<List<String>> rows = reader.getRows(pageBean.getPosStart(), 0, pageBean.getCount());
        List<Integer> includeList = new ArrayList<Integer>();
        List<Map> fieldList = iocImportMapper.selectFieldListByTemplateId(templateId);
        for (Map map : fieldList) {
            if (titles.contains(map.get("colName"))) {
                includeList.add(titles.indexOf(map.get("colName")));
            }
        }

        return GridUtil.covertFile2Grid(titles, includeList, rows, pageBean.getPosStart(), reader.getTotalRows() - 1);
    }

    /**
     * 取得员工ID
     *
     * @param jedis
     * @param belongList
     * @param cellValue
     * @return
     */
    public Long getEmpIdByWorkNo(Jedis jedis, List<String> belongList, String cellValue) {
        Long empid = null;
        for (String subBelongId : belongList) {
            String empInfo = jedis.get(BaseConst.EMP_ + subBelongId + "_" + cellValue);
            if (empInfo != null) {
                //缓存服务 set 地方 多加 双引号处理
                empid = Long.parseLong(empInfo.split(",")[0].replaceAll("\"", ""));
                break;
            }
        }
        return empid;
    }


    private String formatRowKey(String exp, List<String> row, Map<String, Integer> keyIndxMap, Map<String, String> typeMap) {
        Pattern pattern = Pattern.compile(IocUtil.PARAM_ROW, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(exp);
        while (matcher.find()) {
            Object itemValue = IocUtil.getDateValue(row.get(keyIndxMap.get(matcher.group(1))), typeMap.get(matcher.group(1)));
            if (itemValue == null) {
                itemValue = "";
            } else if (exp.startsWith("SELECT") || exp.startsWith("select")) {
                itemValue = itemValue.toString().replace("'", "''");
            }
            exp = exp.replace(matcher.group(0), itemValue.toString());
        }
//        pattern = Pattern.compile(IocUtil.PARAM_EMP_STRUC, Pattern.CASE_INSENSITIVE);
//        matcher = pattern.matcher(exp);
//        while (matcher.find()) {
//            String workno = matcher.group(1);
//            Long structId = getPayStructId(workno, keyIndxMap, row);
//            if (structId == null) {
//                throw new CDException("[" + workno + "]未匹配到薪资结构");
//            }
//            exp = exp.replace(matcher.group(0), structId.toString());
//        }
        return exp;
    }

    public Integer checkMainSize(String exp) {
        List<String> list = new ArrayList<>();
        Pattern pattern = Pattern.compile("薪资结构ID='(\\d+)'", 1);
        Matcher matcher = pattern.matcher(exp);
        while (matcher.find()) {
            list.add(matcher.group(1));
        }
        log.info("当前人员薪资结构信息{}", JSON.toJSON(list));
        for (int i = 0; i < list.size() - 1; i++) {
            for (int j = list.size() - 1; j > i; j--) {
                if (list.get(j).equals(list.get(i))) {
                    list.remove(j);
                }
            }
        }
        log.info("去重后:当前人员薪资结构信息{}", JSON.toJSON(list));
        return list.size();
    }

    public static List<String> getDiffrent(List<String> list1, List<String> list2) {
        Map<String, Integer> map = new HashMap<String, Integer>(list1.size() + list2.size());
        List<String> diff = new ArrayList<String>();
        List<String> maxList = list1;
        List<String> minList = list2;
        if (list2.size() > list1.size()) {
            maxList = list2;
            minList = list1;
        }

        for (String string : maxList) {
            map.put(string, 1);
        }

        for (String string : minList) {
            Integer cc = map.get(string);
            if (cc != null) {
                map.put(string, ++cc);
                continue;
            }
            map.put(string, 1);
        }

        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            if (entry.getValue() == 1) {
                diff.add(entry.getKey());
            }
        }
        return diff;
    }


    /**
     * 数据检查
     *
     * @param rtnFileName
     * @param templateId
     * @param progress
     * @return
     * @throws Exception
     */
    public boolean checkImportData(String rtnFileName, Integer templateId, String progress, ProgressListener iocProgressListener) throws Exception {
        Jedis jedis = RedisService.getResource();
        List<CheckMessage> errorList = new ArrayList<CheckMessage>();
        InputStream in = null;
        try {

            rtnFileName = importOtherMapper.getPathUrlFileName(Long.valueOf(rtnFileName), SessionHolder.getBelongOrgId());
            if (rtnFileName == null) {
                errorList.add(new CheckMessage(-1, -1, "文件服务获取失败!!!！"));
                iocProgressListener.updatePercent(1);
                saveError2Cache(errorList, progress);
                return false;
            }

            String fileName = rtnFileName.substring(0, rtnFileName.lastIndexOf("."));
            String fileType = rtnFileName.substring(rtnFileName.lastIndexOf(".") + 1, rtnFileName.length());

            in = ossService.getInputStream(rtnFileName);

            IocFileReader reader = IocReaderFactory.getReader(fileType, in);

//            IocFileReader reader = IocReaderFactory.getReader(fileType, new FileInputStream(uploadFilePath + rtnFileName));
            if (reader == null) {
                errorList.add(new CheckMessage(-1, -1, "导入文件格式不正确！"));
                iocProgressListener.updatePercent(1);
                saveError2Cache(errorList, progress);
                return false;
            }

            List<String> titles;
            List<List<String>> rows;
            try {

                /**
                 * 腾讯HR助手导入提示语
                 * “模板不匹配”
                 * 适用场景：
                 * 1、表头字段不在第一行 2、首行表头字段与模板不一致
                 */
                List<String> tableRegId = importOtherMapper.getTableId(templateId);
                log.info("tableRegId->{}", tableRegId);
                if (CollectionUtils.isEmpty(tableRegId)) {
                    errorList.add(new CheckMessage(-1, -1, "模板不符合上传要求，请检查后重试"));
                    iocProgressListener.updatePercent(1);
                    saveError2Cache(errorList, progress);
                    return false;
                }
                titles = reader.getTitle(0, 0);
                log.info("titles->{}", titles);
                if (CollectionUtils.isEmpty(titles)) {
                    errorList.add(new CheckMessage(-1, -1, "导入文件不能没有表头！"));
                    iocProgressListener.updatePercent(1);
                    saveError2Cache(errorList, progress);
                    return false;
                }
                String tableRegIds = tableRegId.stream().map(s -> "\'" + s + "\'").collect(Collectors.joining(", "));
                List<String> modelList = importOtherMapper.getModelList(tableRegIds);
                log.info("modelList->{}", modelList);
                if (CollectionUtils.isEmpty(modelList)) {
                    errorList.add(new CheckMessage(-1, -1, "模版查询为空联系管理人员执行对应操作"));
                    iocProgressListener.updatePercent(1);
                    saveError2Cache(errorList, progress);
                    return false;
                }

                SysImportTemplate sysImportTemplate = sysImportTemplateMapper.selectByPrimaryKey(templateId);
                if (sysImportTemplate != null) {
                    Integer colNum = ImportConst.IMPORT_COL_NUM.get(sysImportTemplate.getImportFuncId());
                    if (colNum != null && titles.size() < colNum) {
                        errorList.add(new CheckMessage(-1, -1, "模板不符合上传要求，请检查后重试"));
                        iocProgressListener.updatePercent(1);
                        saveError2Cache(errorList, progress);
                        return false;
                    }
                }

//                List<String> temResult = modelList;
                List<String> temTitles = new ArrayList<>();
                temTitles.addAll(titles);
                //月度动态拼接
                Integer checkFuncId = importOtherMapper.getFuncIdCheck(templateId);
                if (null != checkFuncId && checkFuncId == 5) {
                    List<String> detailNameList = importOtherMapper.getDetailNameList(SessionHolder.getBelongOrgId());
                    modelList.addAll(detailNameList);
                }

                if (titles.retainAll(modelList) == true) {
                    errorList.add(new CheckMessage(-1, -1, "导入文件与模版不匹配(" + getDiffrent(temTitles, titles).toString().replace("[", "").replace("]", "") + ")"));
                    iocProgressListener.updatePercent(1);
                    saveError2Cache(errorList, progress);
                    return false;
                }

                rows = reader.getRows(0, 0);
                if (CollectionUtils.isEmpty(rows)) {
                    errorList.add(new CheckMessage(-1, -1, "导入文件数据不能为空！"));
                    iocProgressListener.updatePercent(1);
                    saveError2Cache(errorList, progress);
                    return false;
                }
            } catch (Exception e) {
                log.error("checkImportData err,{}", e.getMessage(), e);
                errorList.add(new CheckMessage(-1, -1, "导入文件已损坏,无法读取数据！"));
                iocProgressListener.updatePercent(1);
                saveError2Cache(errorList, progress);
                return false;
            } finally {
                in.close();
            }

            //开始校验
            //取得模板字段定义
            List<Map> templateFieldList = iocImportMapper.selectFieldListByTemplateId(templateId);
            Set<String> fieldSet = templateFieldList.stream().map(
                    r -> {
                        return (String) r.get("tableName") + (String) r.get("fieldCode") + (r.get("subFieldId") != null ? r.get("subFieldId") : "");
                    }).collect(Collectors.toSet());
            for (Map map : templateFieldList) {
                //特殊处理
                if (!"wa_emp_leave".equals(map.get("tableName")) && !"wa_emp_leave_time".equals(map.get("tableName"))) {
                    if (fieldSet.size() < templateFieldList.size()) {
                        errorList.add(new CheckMessage(-1, -1, "表格多列匹配了同一系统字段，请检查配置！"));
                        iocProgressListener.updatePercent(1);
                        saveError2Cache(errorList, progress);
                        return false;
                    }
                }
            }
//            if (fieldSet.size() < templateFieldList.size()) {
//                errorList.add(new CheckMessage(-1, -1, "表格多列匹配了同一系统字段，请检查配置！"));
//                iocProgressListener.updatePercent(1);
//                saveError2Cache(errorList, progress);
//                return false;
//            }

            //清空缓存
            checkCache.clear();
            redisCache.clear();
            ImportCacheService.empStrucList.clear();

            //取得校验定义规则
            List<CheckRuleDto> checkRuleList = iocImportMapper.selectCheckRuleListByTemplateId(templateId);
            Map<String, List<CheckRuleDto>> checkRuleMap = new HashMap<String, List<CheckRuleDto>>();
            for (CheckRuleDto checkRule : checkRuleList) {
                List<CheckRuleDto> crList = new ArrayList<CheckRuleDto>();
                if (checkRuleMap.containsKey(checkRule.getColName())) {
                    crList = checkRuleMap.get(checkRule.getColName());
                }
                crList.add(checkRule);
                checkRuleMap.put(checkRule.getColName(), crList);
            }
            log.info("公司ID:" + SessionHolder.getBelongOrgId());
            Set<String> tables = new TreeSet<String>();
            Map<String, String> fieldMap = new HashMap<>();
            Map<String, Map> fieldConfigMap = new HashMap<>();
            for (Map field : templateFieldList) {
                fieldMap.put((String) field.get("colName"), (String) field.get("fieldCode"));
                fieldConfigMap.put((String) field.get("colName"), field);
                tables.add((String) field.get("tableName"));
                if (field.get("type_code") != null) {
                    CheckRuleDto checkRule = new CheckRuleDto();
                    checkRule.setColName((String) field.get("colName"));
                    checkRule.setFieldCode((String) field.get("fieldCode"));
                    checkRule.setCheckPattern("FK");
                    checkRule.setCheckExp(":DICT(:CORP_ID," + field.get("type_code") + ",:VALUE)");
                    checkRule.setMessage("{0}不存在");
                    List<CheckRuleDto> crList = new ArrayList<CheckRuleDto>();
                    if (checkRuleMap.containsKey(checkRule.getColName())) {
                        crList = checkRuleMap.get(checkRule.getColName());
                    }
                    crList.add(checkRule);
                    checkRuleMap.put(checkRule.getColName(), crList);
                }
            }
            Map<String, Integer> fieldIdxMap = new HashMap<String, Integer>();
            for (int j = 0; j < titles.size(); j++) {
                for (Map field : templateFieldList) {
                    if (field.get("colName").equals(titles.get(j))) {
                        fieldIdxMap.put((String) field.get("fieldCode"), j);
                        break;
                    }
                }
            }
            //取得主键
            boolean checkRepeat = true;
            Set<String> keyListMap = new HashSet<String>();
            Map<String, String> typeMap = new HashMap<String, String>();
            for (String table : tables) {
                List<FieldMetaInfo> fieldMetaInfoList = iocImportMapper.getFieldMetaInfo(UserContext.getTenantId(), table, templateId);
                for (FieldMetaInfo fieldMetaInfo : fieldMetaInfoList) {
                    if (fieldMetaInfo.isPk()) {
                        String idSql = fieldMetaInfo.getDefaultExp();
                        keyListMap.addAll(IocUtil.getKeyListFromSql(idSql, fieldMetaInfo.getField()));
                    }
                    typeMap.put(fieldMetaInfo.getField(), fieldMetaInfo.getJdbcType());
                }
                if (table.startsWith(BaseConst.UI_EMP_DATA_TABLE) || table.startsWith(BaseConst.UI_FORM_DATA_TABLE) || table.startsWith(PAY_SOB_EMP_TABLE)) {
                    checkRepeat = false;
                }
                if (table.equals("wa_emp_leave_time")) {
                    log.info("tables={}",table);
                    keyListMap.add("shalf_day");
                    keyListMap.add("ehalf_day");
                }
            }
            UserInfo userInfo = UserContext.getCurrentUser();
            String belongId = userInfo.getTenantId();
            Long corpId = ConvertHelper.longConvert(belongId);

            List<String> belongList = iocImportMapper.getBelongidList(belongId);
            belongList.add(belongId);
            Map<String, Map<Integer, String>> matchListMap = new HashMap<String, Map<Integer, String>>();
            Map<String, String> matchKeyName = new HashMap<String, String>();
            List<String> rowKeys = new ArrayList<String>(rows.size());

            for (int i = 0; i < rows.size(); i++) {
                List<String> row = rows.get(i);
                List<String> realRow = new ArrayList<String>();
                realRow.addAll(row);

                if (checkRepeat) {
                    //重复项校验
                    String rowKey = "";
                    String titleKeys = " ";
                    for (String key : keyListMap) {
                        if (fieldIdxMap.get(key) != null) {
                            Map config = fieldConfigMap.get(titles.get(fieldIdxMap.get(key)));
                            if (config != null) {
                                String mapType = (String) config.get("mapType");
                                if (mapType == null || !mapType.contains("C2R")) {
                                    rowKey += row.get(fieldIdxMap.get(key)) + "_";
                                    titleKeys += titles.get(fieldIdxMap.get(key)) + "_";
                                }
                            }
                        }
                    }

                    if (rowKeys.contains(rowKey)) {

                        errorList.add(new CheckMessage(i + 1, -1, MessageFormat.format("与{0}行的{1}存在重复数据", rowKeys.indexOf(rowKey) + 2, titleKeys)));
                    }
                    rowKeys.add(rowKey);
                }

                for (int j = 0; j < titles.size(); j++) {
                    String title = titles.get(j);
                    String cellValue = row.get(j);
//                    System.out.println(i + "------------" + j);
                    List<CheckRuleDto> fieldCheckRuleList = checkRuleMap.get(title);
                    if (CollectionUtils.isNotEmpty(fieldCheckRuleList)) {
                        boolean checkError = false;
                        for (CheckRuleDto crDto : fieldCheckRuleList) {
//                            if ("pay_fix_info".equals(crDto.getTableName()) && "empid".equals(crDto.getFieldCode())) {
//                                String exp = importCacheService.getMainList(corpId, belongId, cellValue);
//                                Integer check = checkMainSize(exp);
//                                if (check == 1) {
//                                    break;
//                                } else if (check > 1) {
//                                    errorList.add(new CheckMessage(i + 1, j + 1, exp));
//                                    checkError = true;
//                                } else {
//                                    errorList.add(new CheckMessage(i + 1, j + 1, exp));
//                                    checkError = true;
//                                }
//                            }

                            String message = crDto.getMessage();
                            if (!cellValue.equals("%#")) {

                                switch (IocEnum.valueOf(crDto.getCheckPattern())) {

                                    //非空校验
                                    case Required:
                                        if (StringUtils.isEmpty(cellValue)) {
                                            errorList.add(new CheckMessage(i + 1, j + 1, message));
                                            checkError = true;
                                        }
                                        break;
                                    //格式校验
                                    case Regex:
                                        String exp = crDto.getCheckExp();
                                        if (StringUtils.isNotEmpty(cellValue)) {
                                            if (exp.contains(":")) {
                                                Map config = fieldConfigMap.get(title);
                                                if (config != null) {
                                                    String mapType = (String) config.get("mapType");
                                                    if (mapType != null && mapType.contains("C2R")) {
                                                        String subFieldId = (String) config.get("subFieldId");
                                                        if (subFieldId != null && subFieldId.contains(":")) {
                                                            String dataType = subFieldId.split(":")[0];
                                                            if (!dataType.equals(exp.split(":")[0])) {
                                                                exp = null;
                                                            } else {
                                                                exp = exp.split(":")[1];
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            if (exp != null && !cellValue.matches(exp)) {
                                                errorList.add(new CheckMessage(i + 1, j + 1, MessageFormat.format(message, cellValue)));
                                                checkError = true;
                                            }
                                        }
                                        break;
                                    //枚举
                                    case Enum:
                                        if (StringUtils.isNotEmpty(cellValue)) {
                                            String enumExp = crDto.getCheckExp();
                                            if (StringUtils.isNotEmpty(enumExp)) {
                                                Class<?> clazz = Class.forName("com.caidao1.commons.enums." + enumExp);
                                                Method method = clazz.getMethod("getValue", String.class);
                                                Object res = method.invoke(null, cellValue);
                                                if (res == null) {
                                                    errorList.add(new CheckMessage(i + 1, j + 1, MessageFormat.format(message, cellValue)));
                                                    checkError = true;
                                                }
                                            }
                                        }
                                        break;
                                    //范围校验
                                    case Range:
                                        if (StringUtils.isNotEmpty(cellValue)) {
                                            String range = crDto.getCheckExp();
                                            if (StringUtils.isNotEmpty(range)) {
                                                Pattern pattern = Pattern.compile("^(\\(|\\[)(.*),(.*)(\\)|\\])$", Pattern.CASE_INSENSITIVE);
                                                Matcher matcher = pattern.matcher(range);
                                                message = MessageFormat.format(message, cellValue);
                                                if (matcher.find()) {
                                                    int c1 = new BigDecimal(cellValue).compareTo(new BigDecimal(matcher.group(2)));
                                                    int c2 = new BigDecimal(cellValue).compareTo(new BigDecimal(matcher.group(3)));
                                                    if (c1 < 0) {
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    } else if (c1 == 0 && "(".equals(matcher.group(1))) {
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    }
                                                    if (c2 > 0) {
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    } else if (c2 == 0 && ")".equals(matcher.group(4))) {
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    }
                                                } else {
                                                    if (!range.contains(cellValue)) {
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    //SQL校验
                                    case SQL:
                                        //外键校验
                                        String exp2 = crDto.getCheckExp();
                                        if (StringUtils.isNotEmpty(cellValue) && StringUtils.isNotEmpty(exp2)) {
                                            if (exp2.startsWith("SELECT ") || exp2.startsWith("select ")) {
                                                Object val = IocUtil.getDateValue(cellValue, typeMap.get(crDto.getFieldCode()));
                                                String sqlExp = IocUtil.formatExp(exp2, val != null ? val.toString() : "", fileName);
                                                sqlExp = formatRowKey(sqlExp, row, fieldIdxMap, typeMap);
                                                realRow.set(j, String.valueOf(iocImportMapper.queryObjectBySql(sqlExp)));
                                            }
                                        }
                                        break;
                                    case SQLCHECK:
                                        //sql校验
                                    case FK:
                                        try {
                                            String sql = crDto.getCheckExp();
                                            if (StringUtils.isNotEmpty(cellValue) && StringUtils.isNotEmpty(sql)) {
                                                Object existFk = null;
                                                if (StringUtils.isNotEmpty(cellValue) && crDto.getFieldCode().equals("orgid") && cellValue.contains("/")) {
                                                    existFk = createFKObject(jedis, null, crDto.getFieldCode(), cellValue, false, null, belongId, null);
                                                } else {
                                                    if (sql.startsWith(IocUtil.REDIS_DICT_CACHE)) {
                                                        String sqlExp = IocUtil.formatExp(sql, cellValue, fileName);
                                                        Pattern pattern = Pattern.compile(IocUtil.REDIS_DICT_CACHE + "\\((.*)\\)+?", Pattern.CASE_INSENSITIVE);
                                                        Matcher matcher = pattern.matcher(sqlExp);
                                                        if (matcher.find()) {
                                                            String[] params = matcher.group(1).split(",");
                                                            String dictName = params[1].trim();
                                                            if (dictName.startsWith("EXP")) {
                                                                String itemName = dictName.substring(4, dictName.lastIndexOf("#"));
                                                                Object itemValue = IocUtil.getDateValue(row.get(fieldIdxMap.get(itemName)), typeMap.get(crDto.getFieldCode()));
                                                                Map params2 = new HashedMap();
                                                                params2.put(itemName, itemValue);
                                                                dictName = groovyScriptEngine.executeString(dictName.substring(3).replaceAll("#", ""), params2);
                                                            }
                                                            existFk = getFkByDictCache(jedis, ConvertHelper.longConvert(params[0].trim()), dictName, params[2].trim(), false);
                                                        }
                                                    } else if (sql.equals(IocUtil.REDIS_EMPID_CACHE)) {
                                                        if (cellValue.contains(",")) {
                                                            for (String workno : cellValue.split(",")) {
                                                                existFk = getEmpIdByWorkNo(jedis, belongList, workno);
                                                                if (existFk == null || ConvertHelper.intConvert(existFk) == -1) {
                                                                    break;
                                                                }
                                                            }
                                                        } else {
                                                            existFk = getEmpIdByWorkNo(jedis, belongList, cellValue);
                                                        }
                                                    } else {
                                                        Object val = IocUtil.getDateValue(cellValue, typeMap.get(crDto.getFieldCode()));
                                                        String sqlExp = IocUtil.formatExp(sql, val != null ? val.toString() : "", fileName);
                                                        sqlExp = formatRowKey(sqlExp, row, fieldIdxMap, typeMap);
                                                        existFk = getFkByExpCache(sqlExp);
                                                    }
                                                }
                                                if (existFk == null || ConvertHelper.intConvert(existFk) == -1) {
                                                    message = MessageFormat.format(message, cellValue);
                                                    errorList.add(new CheckMessage(i + 1, j + 1, message, crDto.getCheckOrder()));
                                                    checkError = true;
                                                } else {
                                                    realRow.set(j, String.valueOf(existFk));
                                                    //如果总部导入所有公司
                                                    if (crDto.getFieldCode().matches("belong_org_?id")) {
                                                        belongId = ConvertHelper.stringConvert(existFk);
                                                    }
                                                }
                                            }
                                        } catch (Exception e) {
                                            log.error("导入出现异常，{}", e.getMessage(), e);
                                            String errorMsg = "导入配置存在问题，请联系管理员！";
                                            if (e instanceof CDException) {
                                                errorMsg = e.getMessage();
                                            }
                                            errorList.add(new CheckMessage(i + 1, j + 1, errorMsg));
                                            checkError = true;
                                        }
                                        break;
                                    //非空校验
                                    case UN:
                                        if (StringUtils.isNotEmpty(cellValue)) {
                                            //跟文件中的数据比较
                                            boolean isUn = true;
                                            for (int idx = 0; idx < i; idx++) {
                                                String compareValue = rows.get(idx).get(j);
                                                if (cellValue.equalsIgnoreCase(compareValue)) {
                                                    message = MessageFormat.format(message, cellValue);
                                                    errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                    checkError = true;
                                                    isUn = false;
                                                    break;
                                                }
                                            }
                                            if (isUn) {
                                                String unSql = crDto.getCheckExp();
                                                if (StringUtils.isNotEmpty(unSql)) {
                                                    String sqlExp = IocUtil.formatExp(unSql, cellValue, fileName);
                                                    sqlExp = formatRowKey(sqlExp, row, fieldIdxMap, typeMap);
                                                    Object existFk = iocImportMapper.queryFKBySql(sqlExp);
                                                    if (existFk != null) {
                                                        message = MessageFormat.format(message, cellValue);
                                                        errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                        checkError = true;
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    //匹配校验
                                    case Match:
                                        String keyName = null;
                                        if (!matchListMap.containsKey(title)) {
                                            String matchSql = crDto.getCheckExp();
                                            if (StringUtils.isNotEmpty(matchSql)) {
                                                String sqlExp = IocUtil.formatExp(matchSql, cellValue, fileName);
                                                sqlExp = formatRowKey(sqlExp, row, fieldIdxMap, typeMap);
                                                List<Map<String, Object>> keyList = iocImportMapper.queryKeyListBySql(sqlExp);
                                                if (CollectionUtils.isNotEmpty(keyList)) {
                                                    Map keymap = new HashMap();
                                                    Map<String, Object> firstMap = keyList.get(0);
                                                    for (String key : firstMap.keySet()) {
                                                        if (!crDto.getFieldCode().equals(key)) {
                                                            keyName = key;
                                                            break;
                                                        }
                                                    }
                                                    for (Map keyMap : keyList) {
                                                        keymap.put(keyMap.get(keyName), keyMap.get(crDto.getFieldCode()));
                                                    }
                                                    matchListMap.put(title, keymap);
                                                    matchKeyName.put(title, keyName);
                                                } else {
                                                    matchListMap.put(title, new HashMap());
                                                }
                                            }
                                        } else {
                                            keyName = matchKeyName.get(title);
                                        }
                                        if (keyName != null) {
                                            String matchValue = matchListMap.get(title).get(row.get(fieldIdxMap.get(keyName)));
                                            if (matchValue != null && !matchValue.equals(cellValue)) {
                                                message = MessageFormat.format(message, row.get(fieldIdxMap.get(keyName)), cellValue, matchValue);
                                                errorList.add(new CheckMessage(i + 1, j + 1, message, crDto.getCheckOrder()));
                                                checkError = true;
                                            }
                                        }
                                        break;
                                    case Java:
                                        try {
                                            String javaScript = crDto.getCheckExp();
                                            if (StringUtils.isNotEmpty(javaScript) && StringUtils.isNotEmpty(cellValue)) {
                                                Map<String, Object> binding = new HashMap<String, Object>();
                                                binding.put("titles", titles);
                                                binding.put("row", row);
                                                binding.put("realRow", realRow);
                                                binding.put("rows", rows);
                                                binding.put("rowIdx", i);
                                                binding.put("fieldMap", fieldMap);
                                                binding.put("fieldIdxMap", fieldIdxMap);
                                                binding.put("fieldCode", crDto.getFieldCode());
                                                binding.put("title", title);
                                                binding.put("belongId", belongId);
                                                binding.put("corpId", SessionHolder.getCorpId());
                                                binding.put("fieldConfig", fieldConfigMap.get(title));
                                                String javaExp = IocUtil.formatExp(javaScript, cellValue, fileName);
                                                javaExp = formatRowKey(javaExp, row, fieldIdxMap, typeMap);
                                                boolean result = groovyScriptEngine.executeBoolean(javaExp, binding);
                                                if (!result) {
                                                    message = MessageFormat.format(message, title, cellValue);
                                                    errorList.add(new CheckMessage(i + 1, j + 1, message, crDto.getCheckOrder()));
                                                    checkError = true;
                                                }
                                            }
                                        } catch (Exception e) {
                                            log.error("checkImportData check Java err,{}", e.getMessage(), e);
                                            errorList.add(new CheckMessage(i + 1, j + 1, "导入配置存在问题，请联系管理员！"));
                                            checkError = true;
                                        }
                                        break;
                                    case EXCEL:
                                        if (StringUtils.isNotEmpty(cellValue)) {
                                            for (int idx = 0; idx < i; idx++) {
                                                String compareValue = rows.get(idx).get(j);
                                                if (cellValue.equalsIgnoreCase(compareValue)) {
                                                    errorList.add(new CheckMessage(i + 1, j + 1, message));
                                                    checkError = true;
                                                    break;
                                                }
                                            }
                                        }
                                        break;
                                    case FKB:
                                        try {
                                            String sql = crDto.getCheckExp();
                                            if (StringUtils.isNotEmpty(cellValue) && StringUtils.isNotEmpty(sql)) {
                                                Long existFk = null;
                                                Object val = IocUtil.getDateValue(cellValue, typeMap.get(crDto.getFieldCode()));
                                                String sqlExp = IocUtil.formatExp(sql, val != null ? val.toString() : "", fileName);
                                                Object obj = iocImportMapper.queryObjectBySql(formatRowKey(sqlExp, row, fieldIdxMap, typeMap));
                                                if (null != obj) {
                                                    existFk = (Long) obj;
                                                }
                                                if (existFk == null || existFk == -1) {
                                                    message = MessageFormat.format(message, cellValue);
                                                    errorList.add(new CheckMessage(i + 1, j + 1, message, crDto.getCheckOrder()));
                                                    checkError = true;
                                                } else {
                                                    realRow.set(j, String.valueOf(existFk));
                                                }
                                            }
                                        } catch (Exception e) {
                                            log.error("导入出现异常，{}", e.getMessage(), e);
                                            errorList.add(new CheckMessage(i + 1, j + 1, "导入配置存在问题，请联系管理员！"));
                                            checkError = true;
                                        }
                                        break;
                                }
                            }

                            if (checkError) {
                                break;
                            }
                        }
                    }
                }
                if (i % 100 == 0) {
                    iocProgressListener.updatePercent((i + 1) * 1.0 / rows.size());
                }
            }
            iocProgressListener.updatePercent(1.0);
            saveError2Cache(errorList, progress);
        } catch (Exception e) {
            log.error("checkImportData check err,{}", e.getMessage(), e);
            throw e;
        } finally {
            jedis.close();
        }
        return errorList.size() == 0;
    }

    private void saveError2Cache(List<CheckMessage> errorList, String progress) throws Exception {
        if (errorList.size() > 0) {
            String cacheKey = "IMP_" + progress;
            redisTemplate.opsForValue().set(cacheKey, new ObjectMapper().writeValueAsString(errorList));
            redisTemplate.expire(cacheKey, 1, TimeUnit.HOURS);
        }
    }

    private Object getFkByExpCache(String sqlExp) {
        Object existFk = null;
        if (checkCache.containsKey(sqlExp)) {
            existFk = checkCache.get(sqlExp);
        } else {
            existFk = iocImportMapper.queryFKBySql(sqlExp);
//            checkCache.put(sqlExp, existFk == null ? -1 : existFk);
            if (null == existFk) {
                return existFk;
            }
        }
        return existFk;
    }

    private Long getFkByDictCache(Jedis jedis, Long corpid, String typeCode, String dictName, boolean create) throws Exception {
        Long existFk = null;
        String key = corpid + "_" + typeCode + "_" + dictName;
        if (redisCache.containsKey(key)) {
            existFk = redisCache.get(key);
        } else {
            SysParmDict dic = CDCacheUtil.getDictByName(jedis, corpid, typeCode, dictName);
            if (dic != null) {
                existFk = ConvertHelper.longConvert(dic.getDictId());
            } else if (create) {
                SysParmDict parmDict = new SysParmDict();
                parmDict.setTypeId((Long) redisTemplate.opsForValue().get(RedisKeyDefine.SYS_PARM_TYPE_CODE + "_" + typeCode));
                parmDict.setDictChnName(dictName);
                parmDict.setDictCode(dictName);
                parmDict.setSortNum((short) CDCacheUtil.getDictOptions(typeCode,  corpid).size());
                SysParmDict dict = parameterService.saveParmDict2(parmDict, UserContext.getUserId(), corpid, null);
                existFk = ConvertHelper.longConvert(dict.getDictId());
            }
            redisCache.put(key, Optional.ofNullable(existFk).orElse(-1L));
        }
        return existFk;
    }

    /**
     * 校验数据
     *
     * @param progress
     * @return
     * @throws Exception
     */
    public Map getCheckDataList(PageBean pageBean, String progress) throws Exception {
        List<Map> msgList = new ArrayList<>();
        int total = 0;
        String cacheKey = "IMP_" + progress;
        String listData = (String) redisTemplate.opsForValue().get("IMP_" + progress);
        if (StringUtils.isNotEmpty(listData)) {
            msgList = new ObjectMapper().readValue(listData, List.class);
            total = msgList.size();
            if (pageBean.getPosStart() > total) {
                pageBean.setPosStart(0);
            }
            int end = pageBean.getPosStart() + pageBean.getCount();
            end = end > total ? total : end;
            msgList = msgList.subList(pageBean.getPosStart(), end);
        }
        Map result = GridUtil.covertList2GridJson(msgList, pageBean);
        result.put("total_count", total);
        result.put("pos", pageBean.getPosStart());
        redisTemplate.expire(cacheKey, 2, TimeUnit.MINUTES);
        return result;
    }

    private String getRowKey(String belongId, Map<String, Integer> fieldIdxMap, Jedis jedis, String table, Map<String, List<String>> tableOtherFields, List<String> keyList, Map<String, Map> configMap, List<String> titles, List<String> row, Integer rowCnt, List<Integer> allC2rCols, TreeMap<String, List<Integer>> c2rCols, Map<String, String> fieldMap, Map<String, String> typeMap, Map<String, String> defaultExpMap, String fileName, Map<String, Map<String, Integer>> keymap, Map<String, List<String>> keyListMap, List<String> belongList) throws Exception {
        String key = "#";
        for (String item : keyList) {
            Integer keyIdx = null;
            for (int colCnt = 0; colCnt < titles.size(); colCnt++) {
                String itemCode = fieldMap.get(titles.get(colCnt));
                if (item.equals(itemCode)) {
                    keyIdx = colCnt;
                    break;
                }
            }
            if (keyIdx != null) {
                if (allC2rCols.size() > 0 && allC2rCols.contains(keyIdx)) {
                    List<Integer> c2rList = c2rCols.get(table + "_" + fieldMap.get(titles.get(keyIdx)));
                    Map config = configMap.get(titles.get(c2rList.get(rowCnt % c2rList.size())));
                    String subFieldId = (String) config.get("subFieldId");
                    if (subFieldId != null) {
                        key += "_" + subFieldId;
                    } else {
                        String type = typeMap.get(table + "_" + fieldMap.get(titles.get(keyIdx)));
                        if ("BIGINT".equalsIgnoreCase(type) || "DATE".equalsIgnoreCase(type)) {
                            key += "_" + IocUtil.getDateValue(row.get(keyIdx), type);
                        } else {
                            key += "_" + row.get(keyIdx);
                        }
                    }
                } else {
                    String type = typeMap.get(table + "_" + fieldMap.get(titles.get(keyIdx)));
                    if ("BIGINT".equalsIgnoreCase(type) || "DATE".equalsIgnoreCase(type)) {
                        key += "_" + IocUtil.getDateValue(row.get(keyIdx), type);
                    } else if (defaultExpMap.containsKey(table + "_" + item) && defaultExpMap.get(table + "_" + item) != null) {
                        String defaultExp = defaultExpMap.get(table + "_" + item);
                        Object existFk = null;

                        if (defaultExp.startsWith(IocUtil.REDIS_DICT_CACHE)) {
                            Pattern pattern = Pattern.compile(IocUtil.REDIS_DICT_CACHE + "\\((.*)\\)+?", Pattern.CASE_INSENSITIVE);
                            Matcher matcher = pattern.matcher(defaultExp);
                            if (matcher.find()) {
                                String[] params = matcher.group(1).split(",");
                                String dictName = params[1].trim();
                                if (dictName.startsWith("EXP")) {
                                    String itemName = dictName.substring(4, dictName.lastIndexOf("#"));
                                    Object itemValue = IocUtil.getDateValue(row.get(fieldIdxMap.get(itemName)), type);
                                    Map params2 = new HashedMap();
                                    params2.put(itemName, itemValue);
                                    dictName = groovyScriptEngine.executeString(dictName.substring(3).replaceAll("#", ""), params2);
                                }
                                existFk = getFkByDictCache(jedis, ConvertHelper.longConvert(params[0].trim()), dictName, params[2].trim(), false);
                            }
                        } else if (defaultExp.equals(IocUtil.REDIS_EMPID_CACHE)) {
                            if (row.get(keyIdx).contains(",")) {
                                List<Long> fkList = new ArrayList<>();
                                for (String workno : row.get(keyIdx).split(",")) {
                                    fkList.add(getEmpIdByWorkNo(jedis, belongList, workno));
                                }
                                existFk = StringUtils.join(fkList, ",");
                            } else {
                                existFk = getEmpIdByWorkNo(jedis, belongList, row.get(keyIdx));
                            }
                        } else if (defaultExp.startsWith("SELECT") || defaultExp.startsWith("select")) {
                            existFk = getExistFkVal(row.get(keyIdx) == null ? "" : row.get(keyIdx), belongId, fileName, fieldIdxMap, typeMap, jedis, row, null, fieldMap.get(titles.get(keyIdx)), defaultExp);
                        } else {
                            existFk = IocUtil.formatExp(defaultExp, "", fileName);
                        }
                        key += "_" + (existFk == null ? -1 : existFk);
                    } else {
                        key += "_" + row.get(keyIdx);
                    }
                }
            } else {
                if (!defaultExpMap.containsKey(table + "_" + item)) {
                    //主键缓存
                    key += "_";
                    for (String keyfield : keymap.keySet()) {
                        if (keyfield.split("#")[1].equals(item)) {
                            String idkey = getRowKey(belongId, fieldIdxMap, jedis, table, tableOtherFields, keyListMap.get(item), configMap, titles, row, rowCnt, allC2rCols, c2rCols, fieldMap, typeMap, defaultExpMap, fileName, keymap, keyListMap, belongList);
                            key += keymap.get(keyfield).get(idkey);
                            break;
                        }
                    }

                    if (item.equals(BaseConst.UI_FORM_ID_COL) && (table.startsWith(BaseConst.UI_EMP_DATA_TABLE) || table.startsWith(BaseConst.UI_FORM_DATA_TABLE))) {
                        String alias = importService.getAliasTable(table);
                        key += table.substring(table.indexOf(alias) + alias.length() + 1);
                    }

                    if (tableOtherFields.get(table).contains(item) && key.endsWith("_")) {
                        return "#";
                    }
                } else {
                    String exp = defaultExpMap.get(table + "_" + item);
                    if (exp != null && exp.contains(IocUtil.PARAM_FILE_NAME)) {
                        key += "_" + fileName;
                    } else {
                        return "#";
                    }
                }
            }
        }
        return key;
    }

    private String formatRowMinMaxKey(String exp, String table, List<List<String>> convertRows, Map<String, Integer> fieldIdxMap, Map<String, String> typeMap) {
        Pattern pattern = Pattern.compile(IocUtil.PARAM_ROWMIN, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(exp);
        boolean searchMinMax = false;
        Long min = DateUtil.MAX_DATE;
        Long max = 0L;
        while (matcher.find()) {
            for (int rowCnt = 0; rowCnt < convertRows.size(); rowCnt++) {
                List<String> row = convertRows.get(rowCnt);
                Long itemValue = (Long) IocUtil.getDateValue(row.get(fieldIdxMap.get(matcher.group(1))), typeMap.get(table + "_" + matcher.group(1)));
                min = Math.min(min, itemValue);
                max = Math.max(max, itemValue);
            }
            searchMinMax = true;

            exp = exp.replace(matcher.group(0), min.toString());
        }
        pattern = Pattern.compile(IocUtil.PARAM_ROWMAX, Pattern.CASE_INSENSITIVE);
        matcher = pattern.matcher(exp);
        while (matcher.find()) {
            if (!searchMinMax) {
                for (int rowCnt = 0; rowCnt < convertRows.size(); rowCnt++) {
                    List<String> row = convertRows.get(rowCnt);
                    Long itemValue = (Long) IocUtil.getDateValue(row.get(fieldIdxMap.get(matcher.group(1))), typeMap.get(matcher.group(1)));
                    min = Math.min(min, itemValue);
                    max = Math.max(max, itemValue);
                }
            }
            exp = exp.replace(matcher.group(0), max.toString());
        }
        return exp;
    }

    /**
     * 保存数据
     *
     * @param rtnFileName
     * @param templateId
     * @param isOverFlag
     * @param funcId
     * @param progress
     * @param progressListener @return
     * @throws Exception
     */
    @Transactional
    public ImportResultMessage saveImportData(Long corpId, Long userId, String belongId, String rtnFileName, Integer templateId, Integer isOverFlag, Integer funcId, String progress, ProgressListener progressListener) throws
            Exception {
        return saveImportData(corpId, userId, belongId, rtnFileName, templateId, isOverFlag, funcId, progress, progressListener, 0);
    }


    @Transactional
    public ImportResultMessage saveImportData(Long corpId, Long userId, String belongId, String rtnFileName, Integer templateId, Integer isOverFlag, Integer funcId, String progress, ProgressListener progressListener, Integer isUpdTemplate) throws
            Exception {
        //同一个功能同时只能有一个导入 防止数据重复
        String lockKey = MessageFormat.format("IMPORT_LOCK_{0}_{1}", belongId, funcId);
        if (redisTemplate.hasKey(lockKey)) {
            return new ImportResultMessage(-1, "另一个导入进程正在运行中，请稍候重新尝试!");
        }
        //异步重新成员变量赋值
        if (null == UserContext.getCurrentUser()) {
            SessionBean sessionBean = new SessionBean();
            sessionBean.setBelongid(ConvertHelper.stringConvert(belongId));
            sessionBean.setCorpid(corpId);
            sessionBean.setUserid(ConvertHelper.longConvert(userId));
            CdGloVarThreadLocal.register(sessionBean);
        }
        if (null == SessionHolder.getBelongOrgId()) {
            ImportResultMessage resu = new ImportResultMessage(-1, "error_缓存信息不存在");
            return resu;
        }
        rtnFileName = importOtherMapper.getPathUrlFileName(Long.valueOf(rtnFileName), ConvertHelper.stringConvert(belongId));
        if (rtnFileName == null) {
            ImportResultMessage resu = new ImportResultMessage(-1, "error_获取文件服务器信息失败");
            return resu;
        }

        //防重复导入模式，增加USERID模式
        if (com.caidao1.service.config.ioc.config.GetImportDataModel().equalsIgnoreCase(config.IMPORT_DATA_MODEL_USER)) {
            lockKey = MessageFormat.format("IMPORT_LOCK_{0}_{1}_{2}", belongId, funcId, userId);
        }

        redisTemplate.opsForValue().set(lockKey, 1);
        redisTemplate.expire(lockKey, 5, TimeUnit.MINUTES);

        InputStream in = null;

//        String fileType = FilenameUtils.getExtension(filename);

        try {
            long startTime = new Date().getTime();
            String fileName = rtnFileName.substring(0, rtnFileName.lastIndexOf("."));
            String fileType = rtnFileName.substring(rtnFileName.lastIndexOf(".") + 1, rtnFileName.length());

            in = ossService.getInputStream(rtnFileName);
            IocFileReader reader = IocReaderFactory.getReader(fileType, in);

//            IocFileReader reader = IocReaderFactory.getReader(fileType, new FileInputStream(uploadFilePath + rtnFileName));
            List<String> titles = reader.getTitle(0, 0);
            //表头按表切分
            Map<String, List<Integer>> headerMap = new LinkedHashMap<String, List<Integer>>();
            Map<Integer, String> groupMap = new LinkedHashMap<Integer, String>();
            Map<String, String> fieldMap = new HashMap<String, String>();
            Map<String, Map> configMap = new HashMap<String, Map>();
            List<String> tableList = new ArrayList<String>();
            Map<String, Integer> fieldIdxMap = new HashMap<String, Integer>();

            // 根据模板id 查询  Field
            List<Map> fieldDtosList = iocImportMapper.selectFieldListByTemplateId(templateId);

            for (Map map : fieldDtosList) {
                if ("wa_emp_leave".equals(map.get("tableName"))) {
                    titles.add("请假时间段");
                    break;
                }
                break;
            }

            log.debug("导入的表的字段配置信息:{}", JacksonJsonUtil.beanToJson(fieldDtosList));

            for (Map field : fieldDtosList) {
                fieldMap.put((String) field.get("colName"), (String) field.get("fieldCode"));
                configMap.put((String) field.get("colName"), field);
            }
            boolean isPre = false;
            for (int i = 0; i < titles.size(); i++) {
                for (Map field : fieldDtosList) {
                    String tablename = (String) field.get("tableName");
                    if (titles.get(i).equals(field.get("colName"))) {
                        if (isPre && tablename.startsWith("sys_emp")) {
                            tablename = "pre_entry_emp";
                        }
                        if (tablename.startsWith("pre_")) {
                            isPre = true;
                        }
                        groupMap.put(i, tablename);
                        fieldIdxMap.put((String) field.get("fieldCode"), i);
                        if (!tableList.contains(tablename)) {
                            tableList.add(tablename);
                        }
                        break;
                    } else if (("pay_period_info".equals(tablename) || "pay_fix_info".equals(tablename)) && "薪资结构".equals(titles.get(i))) {
                        fieldIdxMap.put("def_struct_main_id", i);
                    }
                }
            }
            for (int i = 0; i < titles.size(); i++) {
                List<Integer> tableTitles = new ArrayList<Integer>();
                String tableName = groupMap.get(i);
                if (tableName != null) {
                    if (headerMap.containsKey(groupMap.get(i))) {
                        tableTitles = headerMap.get(groupMap.get(i));
                    }
                    for (Map field : fieldDtosList) {
                        if (titles.get(i).equals(field.get("colName"))) {
                            tableTitles.add(i);
                            break;
                        }
                    }
                    headerMap.put(tableName, tableTitles);
                }
            }

            List<List<String>> rows = reader.getRows(0, 0);

            //取得校验定义规则
            List<CheckRuleDto> checkRuleList = iocImportMapper.selectCheckRuleListByTemplateId(templateId);
            Map<String, List<CheckRuleDto>> checkRuleMap = new HashMap<String, List<CheckRuleDto>>();
            for (CheckRuleDto checkRule : checkRuleList) {
                List<CheckRuleDto> crList = new ArrayList<CheckRuleDto>();
                if (checkRuleMap.containsKey(checkRule.getTableName() + "_" + checkRule.getFieldCode())) {
                    crList = checkRuleMap.get(checkRule.getTableName() + "_" + checkRule.getFieldCode());
                }
                crList.add(checkRule);
                checkRuleMap.put(checkRule.getTableName() + "_" + checkRule.getFieldCode(), crList);
            }

            Map<String, Map<String, Integer>> keymap = new HashMap<String, Map<String, Integer>>();
            Map<String, List<UpdRowDto>> addList = new HashMap<String, List<UpdRowDto>>();
            Map<String, List<UpdRowDto>> updList = new HashMap<String, List<UpdRowDto>>();
            Map<String, List<String>> tableFields = new HashMap<String, List<String>>();
            Map<String, List<String>> otherTableFields = new HashMap<String, List<String>>();
            Map<String, List<String>> tableOtherFields = new HashMap<String, List<String>>();
            Map<String, String> typeMap = new HashMap<String, String>();
            Map<String, String> defaultExpMap = new HashMap<String, String>();
            Map<String, Boolean> isExistUpd = new HashMap<String, Boolean>();
            final Map<String, KeyConfigDto> keyConfig = new HashMap<String, KeyConfigDto>();
            final Map<String, List<String>> keyListMap = new HashMap<String, List<String>>();

            UserInfo userInfo = UserContext.getCurrentUser();

            for (String table : tableList) {
                List<FieldMetaInfo> fieldMetaInfoList = iocImportMapper.getFieldMetaInfo(userInfo.getTenantId(), table, templateId);
                List<Integer> colIdxs = headerMap.get(table);
                List<String> items = new ArrayList<String>();
                for (int colCnt : colIdxs) {
                    String itemCode = fieldMap.get(titles.get(colCnt));
                    Map config = configMap.get(titles.get(colCnt));
                    if (config != null) {
                        //列转行的时候
                        if (!items.contains(itemCode)) {
                            String mapType = (String) config.get("mapType");
                            String subFieldId = (String) config.get("subFieldId");
                            if (mapType != null && mapType.contains("C2R")) {
                                items.add(itemCode);
                                if (subFieldId != null && mapType.length() > 3) {
                                    items.add(mapType.substring(mapType.indexOf("_") + 1));
                                }
                            } else if (itemCode.startsWith(BaseConst.FIX_EXT_COLUMN)) {
                                if (!items.contains(BaseConst.EXT_CUSTOM_COLOUMN_NAME)) {
                                    items.add(BaseConst.EXT_CUSTOM_COLOUMN_NAME);
                                }
                            } else if (table.startsWith(BaseConst.UI_EMP_DATA_TABLE)) {
                                if (itemCode.equals("empid")) {
                                    items.add(itemCode);
                                } else {
                                    if (!items.contains(BaseConst.UI_FORM_EXT_COL)) {
                                        items.add(BaseConst.UI_FORM_EXT_COL);
                                    }
                                }
                            } else if (table.startsWith(BaseConst.UI_FORM_DATA_TABLE)) {
                                if (!items.contains(BaseConst.UI_FORM_EXT_COL)) {
                                    items.add(BaseConst.UI_FORM_EXT_COL);
                                }
                            } else {
                                items.add(itemCode);
                            }
                        }
                    }
                }
                tableFields.put(table, items);

                List<String> otherItems = new ArrayList<String>();
                isExistUpd.put(table, false);
                for (FieldMetaInfo fieldMetaInfo : fieldMetaInfoList) {
                    boolean isPk = fieldMetaInfo.isPk();
                    if (isPk) {
                        for (String ktable : keyConfig.keySet()) {
                            if (keyConfig.get(ktable).getIdName().equals(fieldMetaInfo.getField())) {
                                isPk = false;
                                break;
                            }
                        }
                    }

                    if (fieldMetaInfo.isPk()) {
                        KeyConfigDto keyConfigDto = new KeyConfigDto();
                        keyConfigDto.setIdName(fieldMetaInfo.getField());
                        keyConfigDto.setKeyExp(fieldMetaInfo.getDefaultExp());
                        keyConfig.put(table, keyConfigDto);
                    } else {
                        if (StringUtils.isNotEmpty(fieldMetaInfo.getDefaultExp())) {
                            defaultExpMap.put(table + "_" + fieldMetaInfo.getField(), fieldMetaInfo.getDefaultExp());
                        }
                    }
                    typeMap.put(table + "_" + fieldMetaInfo.getField(), fieldMetaInfo.getJdbcType());

                    if (!isPk && (!fieldMetaInfo.isNullable() || fieldMetaInfo.isMust()) && !items.contains(fieldMetaInfo.getField())) {
                        otherItems.add(fieldMetaInfo.getField());
                    } else {
                        if ("upduser".equals(fieldMetaInfo.getField())) {
                            isExistUpd.put(table, true);
                        }
                    }
                }
                if (table.startsWith(BaseConst.UI_EMP_DATA_TABLE) || table.startsWith(BaseConst.UI_FORM_DATA_TABLE)) {
                    otherItems.add("crttime");
                    otherItems.add("crtuser");
                    otherItems.add("updtime");
                    otherItems.add("upduser");
                    isExistUpd.put(table, true);
                }
                otherTableFields.put(table, otherItems);
                tableOtherFields.put(table, otherItems);

                //补齐数据
                if ("wa_emp_leave".equals(table) && titles.size() > rows.get(0).size()) {
                    for (List<String> row : rows) {
                        if (row.size() == 6) {
                            if (StringUtils.isNotBlank(row.get(row.size() - 2)) && StringUtils.isNotBlank(row.get(row.size() - 1))) {
                                row.add(row.get(row.size() - 4) + row.get(row.size() - 2) + " -> " + row.get(row.size() - 3) + row.get(row.size() - 1));
                            } else {
                                row.add(row.get(row.size() - 4) + " -> " + row.get(row.size() - 3));
                            }
                        } else {
                            row.add(row.get((row.size() - (row.size() - 2))) + " -> " + row.get((row.size() - (row.size() - 1))));
                        }
//                        row.add(getWaEmpLeaveTimeSlot(row,fieldIdxMap));
                    }
                }
            }

            log.info("导入提取表相关字段完成，tableFields:{}，otherTableFields:{}，tableOtherFields:{}", JacksonJsonUtil.beanToJson(tableFields)
                    , JacksonJsonUtil.beanToJson(otherTableFields), JacksonJsonUtil.beanToJson(tableOtherFields));

            //列转行的特殊判断
            TreeMap<String, List<Integer>> c2rCols = new TreeMap<String, List<Integer>>();
            List<List<String>> convertRows = new ArrayList<List<String>>();

            for (int colCnt = 0; colCnt < titles.size(); colCnt++) {
                Map config = configMap.get(titles.get(colCnt));
                if (config != null) {
                    String mapType = (String) config.get("mapType");
                    if (mapType != null && mapType.contains("C2R")) {
                        //列转行的时候
                        List<Integer> c2rList = c2rCols.get(config.get("tableName") + "_" + fieldMap.get(titles.get(colCnt)));
                        if (c2rList == null) {
                            c2rList = new ArrayList<Integer>();
                        }
                        c2rList.add(colCnt);
                        c2rCols.put(config.get("tableName") + "_" + fieldMap.get(titles.get(colCnt)), c2rList);
                    }
                }
            }

            List<Integer> allC2rCols = new ArrayList<Integer>();
            for (String header : c2rCols.keySet()) {
                allC2rCols.addAll(c2rCols.get(header));
            }

            if (c2rCols.size() > 0) {
                boolean convert = true;
                String type = typeMap.get(c2rCols.firstKey());
                if ("JSON".equals(type)) {
                    convertRows = rows;
                    convert = false;
                }

                if (convert) {
                    for (int rowCnt = 0; rowCnt < rows.size(); rowCnt++) {
                        List<String> row = rows.get(rowCnt);
                        List<Integer> c2rList = c2rCols.get(c2rCols.firstKey());
                        for (int c2rCnt = 0; c2rCnt < c2rList.size(); c2rCnt++) {
                            int c2rIdx = c2rList.get(c2rCnt);
                            List<String> tempRow = new ArrayList<String>();
                            for (int ict = 0; ict < row.size(); ict++) {
                                if (ict == c2rIdx) {
                                    for (String header : c2rCols.keySet()) {
                                        tempRow.add(row.get(c2rCols.get(header).get(c2rCnt)));
                                    }
                                } else {
                                    String itemCode = fieldMap.get(titles.get(ict));
                                    if (!allC2rCols.contains(ict)) {
                                        if (itemCode != null || ict < c2rList.get(0)) {
                                            tempRow.add(row.get(ict));
                                        }
                                    }
                                }
                            }
                            convertRows.add(tempRow);
                        }
                    }
                }
            } else {
                convertRows = rows;
            }

            //初始化主键取得
//            List<String> idNames = new ArrayList<String>();
            for (String table : tableList) {
                KeyConfigDto kcd = keyConfig.get(table);
                if (null == kcd) {
                    log.error("从 keyConfig 中获取 Key 为 {} 的 KeyConfigDto 对象为空", table);
                    throw new RuntimeException("导入数据失败，请重试或联系管理员！");
                }
                String idName = kcd.getIdName();
//                if (!idNames.contains(table + ":" + idName)) {
//                    tableOtherFields.get(table).remove(idName);
//                }
//                idNames.add(table + ":" + idName);
                String idSql = kcd.getKeyExp();
                if (StringUtils.isNotEmpty(idSql)) {
                    List<String> keyList = IocUtil.getKeyListFromSql(idSql, idName);
                    keyListMap.put(idName, keyList);
                    Map<String, Integer> keyCahceMap = new HashMap<String, Integer>();
//                    if (!table.startsWith(BaseConst.UI_EMP_DATA_TABLE) && !table.startsWith(BaseConst.UI_FORM_DATA_TABLE)) {
                    idSql = IocUtil.formatExp(idSql, "", fileName);
                    idSql = formatRowMinMaxKey(idSql, table, convertRows, fieldIdxMap, typeMap);
                    List<Map<String, Object>> cacheList = iocImportMapper.queryKeyListBySql(IocUtil.formatExp(idSql, "", fileName));

                    if (CollectionUtils.isNotEmpty(cacheList)) {
                        for (Map cache : cacheList) {
                            String key = "#";
                            for (String item : keyList) {
                                key += "_";
                                if (cache.containsKey(item)) {
                                    key += cache.get(item);
                                }
                            }

                            Object cacheValue = cache.get(idName);
                            if (cacheValue != null) {
                                keyCahceMap.put(key, cacheValue instanceof Long ? ((Long) cacheValue).intValue() : (Integer) cacheValue);
                            }
                        }
//                        }
                    }
                    keymap.put(table + "#" + idName, keyCahceMap);
                }
            }

            log.debug("主键信息提取完成，keymap:{}", JacksonJsonUtil.beanToJson(keymap));

            //根据依赖关系排序
            if (tableList.size() > 1) {
                tableList = IocUtil.sortTableByRelation(tableList, keyConfig, keyListMap);
            }
            List<String> belongList = iocImportMapper.getBelongidList(belongId);
            belongList.add(belongId);
            Long orgId = null;

            Jedis jedis = RedisService.getResource();

            Map<String, String> empDetailIdMap = new HashMap<>();
            Map<String, String> empDetailNameMap = new HashMap<>();
//            if (tableList.contains("pay_period_info") && allC2rCols.size() > 0) {
//                List<Map> detailList = payConfigService.getStrucDetailList(belongId, new PageBean(true), null, null);
//                for (Map detail : detailList) {
//                    empDetailIdMap.put(detail.get("struc_main_id") + "_" + detail.get("payslip_name"), detail.get("data_type") + ":" + detail.get("detail_id"));
//                    empDetailNameMap.put(detail.get("data_type") + ":" + detail.get("detail_id"), (String) detail.get("payslip_name"));
//                }
//            }
            SysImportFunc importFunc = sysImportFuncMapper.selectByPrimaryKey(funcId);
            try {
                for (int rowCnt = 0; rowCnt < convertRows.size(); rowCnt++) {
                    List<String> row = convertRows.get(rowCnt);
                    Integer rowId = null;
                    for (String table : tableList) {
                        if (allC2rCols.size() > 0) {
                            String c2rTable = groupMap.get(allC2rCols.get(0));
                            if (!table.equals(c2rTable) && (rowCnt % c2rCols.get(c2rCols.firstKey()).size()) != 0) {
                                continue;
                            }
                        }
                        UpdRowDto updRowDto = new UpdRowDto();
                        updRowDto.setRowCnt(rowCnt);

                        //薪资项目按人找
//                        if (table.equals("pay_period_info") && allC2rCols.size() > 0) {
//                            Long strucId = getPayStructId(row.get(fieldIdxMap.get("empid")), fieldIdxMap, row);
//
//                            for (Integer colCnt : allC2rCols) {
//                                Map config = configMap.get(titles.get(colCnt));
//                                String subFieldId = (String) config.get("subFieldId");
//                                if (subFieldId != null && empDetailIdMap.get(strucId + "_" + empDetailNameMap.get(subFieldId)) != null) {
//                                    config.put("subFieldId", empDetailIdMap.get(strucId + "_" + empDetailNameMap.get(subFieldId)));
//                                } else {
//                                    System.out.println(strucId + "_" + subFieldId);
//                                }
//                            }
//                        }

                        String key = getRowKey(belongId, fieldIdxMap, jedis, table, tableOtherFields, keyListMap.get(keyConfig.get(table).getIdName()), configMap, titles, row, rowCnt, allC2rCols, c2rCols, fieldMap, typeMap, defaultExpMap, fileName, keymap, keyListMap, belongList);
                        rowId = keymap.get(table + "#" + keyConfig.get(table).getIdName()).get(key);
                        updRowDto.setKey(key);
                        updRowDto.setRowData(row);

                        List<Integer> colIdxs = headerMap.get(table);
                        List<SaveItemDto> itemList = new ArrayList<SaveItemDto>();
                        for (int colCnt : colIdxs) {
                            //更新时排除关键字
                            if (allC2rCols.size() > 0 && allC2rCols.contains(colCnt)) {
                                List<Integer> c2rList = c2rCols.get(table + "_" + fieldMap.get(titles.get(colCnt)));
                                String itemCode = fieldMap.get(titles.get(colCnt));
                                if (itemCode != null) {
                                    String itemType = typeMap.get(table + "_" + itemCode);
                                    if ("JSON".equals(itemType)) {
                                        String cellValue = row.get(colCnt);
                                        SaveItemDto itemDto = null;
                                        Map<String, Object> json = new HashMap<String, Object>();
                                        for (SaveItemDto item : itemList) {
                                            if (item.getItemCode().equals(itemCode)) {
                                                itemDto = item;
                                                if (item.getItemValue() != null) {
                                                    json = new ObjectMapper().readValue((String) item.getItemValue(), Map.class);
                                                }
                                            }
                                        }
                                        if (itemDto == null) {
                                            itemDto = new SaveItemDto();
                                            itemDto.setItemCode(itemCode);
                                            itemDto.setItemType("JSONB");
                                            itemList.add(itemDto);
                                        }
                                        if (StringUtils.isNotEmpty(cellValue)) {
                                            String type = typeMap.get(table + "_" + itemCode);
                                            Map config = configMap.get(titles.get(colCnt));
                                            String subFieldId = (String) config.get("subFieldId");
                                            if (subFieldId != null) {
                                                if (subFieldId.contains(":")) {
                                                    subFieldId = subFieldId.split(":")[1];
                                                }
                                                json.put(subFieldId, IocUtil.getItemValue(row.get(colCnt), itemCode, type));
                                            } else {
                                                json.put(itemCode, IocUtil.getItemValue(row.get(colCnt), itemCode, type));
                                            }
                                            itemDto.setItemValue(new ObjectMapper().writeValueAsString(json));
                                        }
                                    } else {
                                        if (colCnt == c2rList.get(rowCnt % c2rList.size())) {
                                            SaveItemDto itemDto = new SaveItemDto();
                                            itemDto.setItemCode(itemCode);
                                            itemDto.setItemType(typeMap.get(table + "_" + itemCode));
                                            Map config = configMap.get(titles.get(colCnt));
                                            String mapType = (String) config.get("mapType");
                                            String subFieldId = (String) config.get("subFieldId");
                                            if (subFieldId != null) {
                                                itemDto.setItemValue(subFieldId);
                                                itemList.add(itemDto);
                                                itemDto = new SaveItemDto();
                                                itemDto.setItemCode(mapType.substring(mapType.indexOf("_") + 1));
                                                itemDto.setItemType(typeMap.get(table + "_" + mapType.substring(mapType.indexOf("_") + 1)));
                                                itemDto.setItemValue(row.get(c2rList.get(0)));
                                                itemList.add(itemDto);
                                            } else {
                                                itemDto.setItemValue(row.get(c2rList.get(0)));
                                                itemList.add(itemDto);
                                            }
                                        }
                                    }
                                }
                            } else {
                                if (rowId == null || (rowId != null && !keyListMap.get(keyConfig.get(table).getIdName()).contains(colCnt))) {
                                    String itemCode = fieldMap.get(titles.get(colCnt));
                                    if (itemCode != null) {
                                        Map config = configMap.get(titles.get(colCnt));
                                        String mapType = (String) config.get("mapType");
                                        //自定义字段
                                        if (itemCode.startsWith(BaseConst.FIX_EXT_COLUMN)) {
                                            String cellValue = row.get(colCnt);
                                            SaveItemDto itemDto = null;
                                            Map<String, Object> json = new HashMap<String, Object>();
                                            for (SaveItemDto item : itemList) {
                                                if (item.getItemCode().equals(BaseConst.EXT_CUSTOM_COLOUMN_NAME)) {
                                                    itemDto = item;
                                                    if (item.getItemValue() != null) {
                                                        json = new ObjectMapper().readValue((String) item.getItemValue(), Map.class);
                                                    }
                                                }
                                            }
                                            if (itemDto == null) {
                                                itemDto = new SaveItemDto();
                                                itemDto.setItemCode(BaseConst.EXT_CUSTOM_COLOUMN_NAME);
                                                itemDto.setItemType("JSONB");
                                                itemList.add(itemDto);
                                            }
                                            if (cellValue != null) {
                                                String type = StringUtils.defaultIfEmpty(typeMap.get(table + "_" + itemCode), "");
                                                if (type.toLowerCase().startsWith("select2")) {
                                                    String type_code = type.substring(7);
                                                    List<Map> parmDictByTypeCode = parameterService.getParmDictByTypeCode(type_code, corpId);
                                                    for (int i = 0; i < parmDictByTypeCode.size(); i++) {
                                                        Map<String, Object> dictMap = parmDictByTypeCode.get(i);
                                                        if ((dictMap.get("dict_code").toString().equals(cellValue))) {
                                                            json.put(itemCode, dictMap.get("dict_id"));
                                                            break;
                                                        }
                                                    }
                                                } else {
                                                    if (type.toLowerCase().startsWith("select")) {
                                                        SysParmDict dic = CDCacheUtil.getDictByName(jedis, corpId, type.substring(6), cellValue);
                                                        if (dic != null) {
                                                            json.put(itemCode, dic.getDictId());
                                                        }
                                                    } else if (type.toLowerCase().startsWith("cascade")) {
                                                        SysParmDict dic = CDCacheUtil.getDictByName(jedis, corpId, type.substring(7), cellValue);
                                                        if (dic != null) {
                                                            json.put(itemCode, dic.getDictId());
                                                        }
                                                    } else {
                                                        if ("calendar".equalsIgnoreCase(type)) {
                                                            type = "BIGINT";
                                                            if ("".equals(cellValue)) {
                                                                json.put(itemCode, "");
                                                            } else {
                                                                json.put(itemCode, IocUtil.getItemValue(row.get(colCnt), itemCode, type));
                                                            }
                                                        } else {

                                                            json.put(itemCode, IocUtil.getItemValue(row.get(colCnt), itemCode, type));
                                                        }
                                                    }
                                                }

                                                itemDto.setItemValue(new ObjectMapper().writeValueAsString(json));
                                            }
                                        } else if ((table.startsWith(BaseConst.UI_EMP_DATA_TABLE) || table.startsWith(BaseConst.UI_FORM_DATA_TABLE)) && !itemCode.equals("empid")) {
                                            String cellValue = row.get(colCnt);
                                            SaveItemDto itemDto = null;
                                            Map<String, Object> json = new HashMap<String, Object>();
                                            for (SaveItemDto item : itemList) {
                                                if (item.getItemCode().equals(BaseConst.UI_FORM_EXT_COL)) {
                                                    itemDto = item;
                                                    if (item.getItemValue() != null) {
                                                        json = new ObjectMapper().readValue((String) item.getItemValue(), Map.class);
                                                    }
                                                }
                                            }
                                            if (itemDto == null) {
                                                itemDto = new SaveItemDto();
                                                itemDto.setItemCode(BaseConst.UI_FORM_EXT_COL);
                                                itemDto.setItemType("JSONB");
                                                itemList.add(itemDto);
                                            }
                                            if (StringUtils.isNotEmpty(cellValue)) {
                                                String type = typeMap.get(table + "_" + itemCode);
                                                json.put(itemCode, IocUtil.getItemValue(getItemValue(jedis, checkRuleMap, table, itemCode, row, rowId, colCnt, fileName, belongList, typeMap, fieldIdxMap), itemCode, type));
                                                itemDto.setItemValue(new ObjectMapper().writeValueAsString(json));
                                            }
                                        } else if ("JSON".equals(mapType)) {
                                            String cellValue = row.get(colCnt);
                                            SaveItemDto itemDto = null;
                                            Map<String, Object> json = new HashMap<String, Object>();
                                            for (SaveItemDto item : itemList) {
                                                if (item.getItemCode().equals(itemCode)) {
                                                    itemDto = item;
                                                    if (item.getItemValue() != null) {
                                                        json = new ObjectMapper().readValue((String) item.getItemValue(), Map.class);
                                                    }
                                                }
                                            }
                                            if (itemDto == null) {
                                                itemDto = new SaveItemDto();
                                                itemDto.setItemCode(itemCode);
                                                itemDto.setItemType("JSONB");
                                                itemList.add(itemDto);
                                            }
                                            if (StringUtils.isNotEmpty(cellValue)) {
                                                String subFieldId = (String) config.get("subFieldId");
                                                if (subFieldId != null) {
                                                    json.put(subFieldId, row.get(colCnt));
                                                }
                                                itemDto.setItemValue(new ObjectMapper().writeValueAsString(json));
                                            }
                                        } else {
                                            SaveItemDto itemDto = new SaveItemDto();
                                            itemDto.setItemCode(itemCode);
                                            itemDto.setItemType(typeMap.get(table + "_" + itemCode));
                                            List<CheckRuleDto> fieldCheckRuleList = checkRuleMap.get(table + "_" + itemCode);
                                            if (CollectionUtils.isNotEmpty(fieldCheckRuleList)) {
                                                for (CheckRuleDto crDto : fieldCheckRuleList) {
                                                    String cellValue1 = row.get(colCnt);
                                                    if (!cellValue1.equals("%#")) {

                                                        switch (IocEnum.valueOf(crDto.getCheckPattern())) {
                                                            case SQL:
                                                                //外键校验
                                                                String exp2 = crDto.getCheckExp();
                                                                if (StringUtils.isNotEmpty(cellValue1) && StringUtils.isNotEmpty(exp2)) {
                                                                    if (exp2.startsWith("SELECT ") || exp2.startsWith("select ")) {
                                                                        Object val = IocUtil.getDateValue(cellValue1, typeMap.get(crDto.getFieldCode()));
                                                                        String sqlExp = IocUtil.formatExp(exp2, val != null ? val.toString() : "", fileName);
                                                                        sqlExp = formatRowKey(sqlExp, row, fieldIdxMap, typeMap);
                                                                        itemDto.setItemValue(String.valueOf(iocImportMapper.queryObjectBySql(sqlExp)));
                                                                    }
                                                                }
                                                                break;
                                                            case FK:
                                                                if (StringUtils.isNotEmpty(cellValue1)) {
                                                                    if (itemCode.equals("orgid") && cellValue1.contains("/")) {
                                                                        Long existFk = createFKObject(jedis, null, itemCode, cellValue1, false, rowId, ConvertHelper.stringConvert(belongId), null);
                                                                        orgId = existFk;
                                                                        itemDto.setItemValue(existFk == null ? -1 : existFk);
                                                                    } else {
                                                                        String fksql = IocUtil.formatExp(crDto.getCheckExp(), cellValue1, fileName);
                                                                        if (fksql.startsWith(IocUtil.REDIS_DICT_CACHE)) {
                                                                            Pattern pattern = Pattern.compile(IocUtil.REDIS_DICT_CACHE + "\\((.*)\\)+?", Pattern.CASE_INSENSITIVE);
                                                                            Matcher matcher = pattern.matcher(fksql);
                                                                            if (matcher.find()) {
                                                                                String[] params = matcher.group(1).split(",");
                                                                                String dictName = params[1].trim();
                                                                                if (dictName.startsWith("EXP")) {
                                                                                    String itemName = dictName.substring(4, dictName.lastIndexOf("#"));
                                                                                    Object itemValue = IocUtil.getDateValue(row.get(fieldIdxMap.get(itemName)), typeMap.get(crDto.getFieldCode()));
                                                                                    Map params2 = new HashedMap();
                                                                                    params2.put(itemName, itemValue);
                                                                                    dictName = groovyScriptEngine.executeString(dictName.substring(3).replaceAll("#", ""), params2);
                                                                                }
                                                                                itemDto.setItemValue(getFkByDictCache(jedis, ConvertHelper.longConvert(params[0].trim()), dictName, params[2].trim(), false));
                                                                            }
                                                                        } else if (fksql.equals(IocUtil.REDIS_EMPID_CACHE)) {
                                                                            if (cellValue1.contains(",")) {
                                                                                List<Long> fkList = new ArrayList<>();
                                                                                for (String workno : cellValue1.split(",")) {
                                                                                    fkList.add(getEmpIdByWorkNo(jedis, belongList, workno));
                                                                                }
                                                                                itemDto.setItemValue(StringUtils.join(fkList, ","));
                                                                            } else {
                                                                                itemDto.setItemValue(getEmpIdByWorkNo(jedis, belongList, cellValue1));
                                                                            }
                                                                        } else {
                                                                            fksql = formatRowKey(fksql, row, fieldIdxMap, typeMap);
                                                                            Object existFk = getFkByExpCache(fksql);
                                                                            itemDto.setItemValue(existFk);
                                                                        }

                                                                    }
                                                                    //如果总部导入所有公司
                                                                    if (itemCode.matches("belong_org_?id") && !"-1".equals(itemDto.getItemValue())) {
                                                                        belongId = ConvertHelper.stringConvert(itemDto.getItemValue());
                                                                    }
                                                                }
                                                                break;
                                                            case FKC:
                                                                String fksql = IocUtil.formatExp(crDto.getCheckExp(), cellValue1, fileName);
                                                                Long existFk = createFKObject(jedis, fksql, itemCode, cellValue1, true, rowId, ConvertHelper.stringConvert(belongId), orgId);
                                                                if (itemCode.equals("orgid")) {
                                                                    orgId = existFk;
                                                                }
                                                                itemDto.setItemValue(existFk == null ? -1 : existFk);
                                                                break;
                                                            case Range:
                                                                String range = crDto.getCheckExp();
                                                                if (range.contains(":")) {
                                                                    String[] ranges = range.split(",");
                                                                    for (String dict : ranges) {
                                                                        String[] dictMap = dict.split(":");
                                                                        if (dictMap[0].equals(row.get(colCnt))) {
                                                                            itemDto.setItemValue(dictMap[1]);
                                                                            break;
                                                                        }
                                                                    }
                                                                } else {
                                                                    itemDto.setItemValue(row.get(colCnt));
                                                                }
                                                                break;
                                                            case Enum:
                                                                String enumExp = crDto.getCheckExp();
                                                                if (StringUtils.isNotEmpty(enumExp)) {
                                                                    Class<?> clazz = Class.forName("com.caidao1.commons.enums." + enumExp);
                                                                    Method method = clazz.getMethod("getValue", String.class);
                                                                    itemDto.setItemValue(method.invoke(null, row.get(colCnt)));
                                                                }
                                                                break;
                                                            case FKB:
                                                                if (StringUtils.isNotEmpty(cellValue1)) {
                                                                    String fkbsql = IocUtil.formatExp(crDto.getCheckExp(), cellValue1, fileName);
                                                                    Object existFkCB = iocImportMapper.queryObjectBySql(formatRowKey(fkbsql, row, fieldIdxMap, typeMap));
                                                                    itemDto.setItemValue(existFkCB);
                                                                }
                                                                break;
                                                        }
                                                    }
                                                }
                                                if (itemDto.getItemValue() == null) {
                                                    if (!row.get(colCnt).equals("%#")) {
                                                        itemDto.setItemValue(row.get(colCnt));
                                                    } else {
                                                        if (itemDto.getItemType().equals("Integer")) {
                                                            itemDto.setItemValue(-999);
                                                        } else {
                                                            itemDto.setItemValue("%#");
                                                        }
                                                    }
                                                }
                                            } else {
                                                itemDto.setItemValue(row.get(colCnt));
                                            }
                                            if (!itemDto.getItemType().equals("Integer") && ("%#").equals(itemDto.getItemValue())) {
                                                // 特殊字符不做更新操作 -8733
                                            } else if (itemDto.getItemType().equals("Integer") && new Integer(-999).equals(itemDto.getItemValue())) {
                                                // 特殊字符不做更新操作 -8733
                                            } else {
                                                itemList.add(itemDto);
                                            }

                                        }
                                    }
                                }
                            }
                        }

                        //插入时带上默认值
                        if (rowId == null) {
                            List<String> otherFields = otherTableFields.get(table);
                            List<String> tabOtherFields = tableOtherFields.get(table);
                            List<String> otherFields2 = new ArrayList<>(tabOtherFields);
                            for (String itemCode : otherFields) {
                                if ((table.startsWith(BaseConst.UI_EMP_DATA_TABLE) || table.startsWith(BaseConst.UI_FORM_DATA_TABLE)) && !Arrays.asList("empid", "belong_org_id", "crtuser", "upduser", "crttime", "updtime").contains(itemCode)) {
                                    String defaultExp = defaultExpMap.get(table + "_" + itemCode);
                                    if (StringUtils.isNotEmpty(defaultExp)) {
                                        SaveItemDto itemDto = null;
                                        Map<String, Object> json = new HashMap<String, Object>();
                                        for (SaveItemDto item : itemList) {
                                            if (item.getItemCode().equals(BaseConst.UI_FORM_EXT_COL)) {
                                                itemDto = item;
                                                if (item.getItemValue() != null) {
                                                    json = new ObjectMapper().readValue((String) item.getItemValue(), Map.class);
                                                }
                                            }
                                        }
                                        if (itemDto == null) {
                                            itemDto = new SaveItemDto();
                                            itemDto.setItemCode(BaseConst.UI_FORM_EXT_COL);
                                            itemDto.setItemType("JSONB");
                                            itemList.add(itemDto);
                                        }
                                        if (json.get(itemCode) == null) {
                                            if (defaultExp.startsWith("SELECT") || defaultExp.startsWith("select")) {
                                                Object existFk = getExistFkVal("", belongId, fileName, fieldIdxMap, typeMap, jedis, row, rowId, itemCode, defaultExp);
                                                json.put(itemCode, existFk == null ? -1 : existFk);
                                            } else {
                                                json.put(itemCode, IocUtil.formatExp(defaultExp, "", fileName));
                                            }
                                        }
                                        itemDto.setItemValue(new ObjectMapper().writeValueAsString(json));
                                    }
                                    otherFields2.remove(itemCode);
                                } else {
                                    SaveItemDto itemDto = new SaveItemDto();
                                    itemDto.setItemCode(itemCode);
                                    itemDto.setItemType(typeMap.get(table + "_" + itemCode));
                                    if (!itemCode.equals(keyConfig.get(table).getIdName())) {
                                        //取缓存
                                        if (keymap.containsKey(table + "#" + itemCode)) {
                                            String idkey = getRowKey(belongId, fieldIdxMap, jedis, table, tableOtherFields, keyListMap.get(itemCode), configMap, titles, row, rowCnt, allC2rCols, c2rCols, fieldMap, typeMap, defaultExpMap, fileName, keymap, keyListMap, belongList);
                                            itemDto.setItemValue(keymap.get(table + "#" + itemCode).get(idkey));
                                        } else {
                                            //主键缓存
                                            for (String keyfield : keymap.keySet()) {
                                                if (keyfield.split("#")[1].equals(itemCode)) {
                                                    String idkey = getRowKey(belongId, fieldIdxMap, jedis, table, tableOtherFields, keyListMap.get(itemCode), configMap, titles, row, rowCnt, allC2rCols, c2rCols, fieldMap, typeMap, defaultExpMap, fileName, keymap, keyListMap, belongList);
                                                    itemDto.setItemValue(keymap.get(keyfield).get(idkey));
                                                    break;
                                                }
                                            }

                                            //外键同项目更新
                                            for (String table2 : tableList) {
                                                if (table2.equals(table)) {
                                                    break;
                                                }
                                                if (keyListMap.get(keyConfig.get(table2).getIdName()).contains(itemCode)) {
                                                    List<SaveItemDto> tmpList = null;
                                                    if (updList.get(table2) != null && updList.get(table2).get(updList.get(table2).size() - 1).getRowCnt().equals(updRowDto.getRowCnt())) {
                                                        tmpList = updList.get(table2).get(updList.get(table2).size() - 1).getRow();
                                                    } else if (addList.get(table2) != null && addList.get(table2).get(addList.get(table2).size() - 1).getRowCnt().equals(updRowDto.getRowCnt())) {
                                                        tmpList = addList.get(table2).get(addList.get(table2).size() - 1).getRow();
                                                    }
                                                    if (tmpList != null) {
                                                        for (SaveItemDto dto : tmpList) {
                                                            if (dto.getItemCode().equals(itemCode)) {
                                                                itemDto.setItemValue(dto.getItemValue());
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                                if (itemDto.getItemValue() != null) {
                                                    break;
                                                }
                                            }

                                            if (itemDto.getItemValue() == null) {
                                                String defaultExp = defaultExpMap.get(table + "_" + itemCode);
                                                if (StringUtils.isNotEmpty(defaultExp)) {
                                                    if (defaultExp.startsWith("SELECT") || defaultExp.startsWith("select")) {
                                                        Object existFk = getExistFkVal("", belongId, fileName, fieldIdxMap, typeMap, jedis, row, rowId, itemCode, defaultExp);
                                                        itemDto.setItemValue(existFk == null ? -1 : existFk);
                                                    } else {
                                                        itemDto.setItemValue(IocUtil.formatExp(defaultExp, "", fileName));
                                                    }
                                                } else {
                                                    if ("crtuser".equals(itemCode) || "upduser".equals(itemCode)) {
                                                        itemDto.setItemType("BIGINT");
                                                        itemDto.setItemValue(UserContext.getUserId());
                                                    } else if ("crttime".equals(itemCode) || "updtime".equals(itemCode)) {
                                                        itemDto.setItemType("BIGINT");
                                                        itemDto.setItemValue(System.currentTimeMillis() / 1000);
                                                    } else {
                                                        itemDto.setItemValue("");
                                                    }
                                                }
                                            }
                                        }
                                        itemList.add(itemDto);
                                    } else {
                                        if (rowId != null) {
                                            itemDto.setItemValue(rowId);
                                        } else {
                                            for (String item : keymap.keySet()) {
                                                if (item.split("#")[1].equals(itemCode)) {
                                                    itemDto.setItemValue(keymap.get(item).get(updRowDto.getKey()));
                                                    break;
                                                }
                                            }
                                        }
                                        itemList.add(itemDto);
                                    }
                                }
                            }
                            tableOtherFields.put(table, otherFields2);
                        } else {
                            if (isExistUpd.get(table)) {
                                SaveItemDto itemDto = new SaveItemDto();
                                itemDto.setItemCode("upduser");
                                itemDto.setItemType("BIGINT");
                                itemDto.setItemValue(SessionHolder.getUserId());
                                itemList.add(itemDto);

                                itemDto = new SaveItemDto();
                                itemDto.setItemCode("updtime");
                                itemDto.setItemType("BIGINT");
                                itemDto.setItemValue(System.currentTimeMillis() / 1000);
                                itemList.add(itemDto);
                            }
                        }

                        updRowDto.setRow(itemList);
                        //判断更新还是插入
                        if (rowId != null) {
                            updRowDto.setId(ConvertHelper.longConvert(rowId));
                            updRowDto.setIdName(keyConfig.get(table).getIdName());
                            List<UpdRowDto> valueUpdList = updList.get(table);
                            if (valueUpdList == null) {
                                valueUpdList = new ArrayList<UpdRowDto>();
                            }
                            valueUpdList.add(updRowDto);
                            updList.put(table, valueUpdList);
                        } else {
                            updRowDto.setIdName(keyConfig.get(table).getIdName());
                            List<UpdRowDto> valueAddList = addList.get(table);
                            if (valueAddList == null) {
                                valueAddList = new ArrayList<UpdRowDto>();
                            }
                            valueAddList.add(updRowDto);
                            addList.put(table, valueAddList);
                        }
                    }

                    if (rowCnt % 100 == 0) {
                        progressListener.updatePercent((rowCnt + 1) * 0.4 / rows.size());
                    }
                }

                for (String table : tableList) {
                    List<UpdRowDto> valueAddList = addList.get(table);
                    if (valueAddList != null) {
                        List<UpdRowDto> valueAddList2 = new ArrayList<>();
                        for (UpdRowDto row : addList.get(table)) {
                            if (row.getKey().equals("#")) {
                                log.debug("过滤掉无效的插入数据:{}", JacksonJsonUtil.beanToJson(row));
                            } else {
                                valueAddList2.add(row);
                            }
                        }

                        if (CollectionUtils.isEmpty(valueAddList2)) {
                            addList.remove(table);
                        } else {
                            addList.put(table, valueAddList2);
                        }
                    }

                    List<UpdRowDto> valueUpdList = updList.get(table);
                    if (valueUpdList != null) {
                        List<UpdRowDto> valueUpdList2 = new ArrayList<>();

                        for (UpdRowDto row : valueUpdList) {
                            if (row.getKey().equals("#")) {
                                log.debug("过滤掉无效的更新数据:{}", JacksonJsonUtil.beanToJson(row));
                            } else {
                                valueUpdList2.add(row);
                            }
                        }

                        if (CollectionUtils.isEmpty(valueUpdList2)) {
                            updList.remove(table);
                        } else {
                            updList.put(table, valueUpdList2);
                        }
                    }
                }

                //开始保存数据
                if (StringUtils.isNotEmpty(importFunc.getBeforeTrigger())) {
                    Map<String, Object> binding = new HashMap<String, Object>();
                    binding.put("addList", addList);
                    binding.put("updList", updList);
                    binding.put("tableList", tableList);
                    binding.put("belongId", userInfo.getTenantId());
                    binding.put("corpId", ConvertHelper.longConvert(userInfo.getTenantId()));
                    groovyScriptEngine.execute(importFunc.getBeforeTrigger(), binding);
                }

                boolean existInsert = false;
                for (int tcnt = 0; tcnt < tableList.size(); tcnt++) {
                    String table = tableList.get(tcnt);
                    List<UpdRowDto> valueAddList = addList.get(table);
                    if (valueAddList != null) {
                        existInsert = true;
                        AddTableDto saveTableDto = new AddTableDto();
                        saveTableDto.setTableName(table);
                        List<String> fields = new ArrayList<String>();
                        fields.addAll(tableFields.get(table));
                        List<String> otherFields = tableOtherFields.get(table);
                        fields.addAll(otherFields);
                        saveTableDto.setFields(fields);
                        saveTableDto.setRows(valueAddList);
                        if (tcnt != 0) {
                            for (String key : keymap.keySet()) {
                                int idCnt = -1;
                                for (int i = 0; i < otherFields.size(); i++) {
                                    if (key.split("#")[1].equals(otherFields.get(i))) {
                                        idCnt = i;
                                        break;
                                    }
                                }
                                if (idCnt >= 0) {
                                    for (UpdRowDto rowDto : valueAddList) {
                                        Object itemValue = rowDto.getRow().get(tableFields.get(table).size() + idCnt).getItemValue();
                                        if (itemValue == null || itemValue.equals(-1)) {
                                            String idkey = getRowKey(belongId, fieldIdxMap, jedis, table, tableOtherFields, keyListMap.get(key.split("#")[1]), configMap, titles, convertRows.get(rowDto.getRowCnt()), rowDto.getRowCnt(), allC2rCols, c2rCols, fieldMap, typeMap, defaultExpMap, fileName, keymap, keyListMap, belongList);
                                            rowDto.getRow().get(tableFields.get(table).size() + idCnt).setItemValue(keymap.get(key).get(idkey));
                                        }
                                    }
                                }
                            }
                        }
                        log.debug("保存 saveTableDto 的数据为：{}", new GsonBuilder().create().toJson(saveTableDto));
                        if ("wa_plan_emp_rel".equals(saveTableDto.getTableName())) {
                            continue;
                        }
                        importService.fastInsertList(saveTableDto, progressListener, 0.4, 0.2 * (tcnt + 1) / tableList.size());
                        if (tcnt != tableList.size() - 1 || StringUtils.isNotEmpty(importFunc.getAfterTrigger())) {
                            //如果多个表，需要刷新缓存给下一个表取ID
                            String sql = IocUtil.formatExp(keyConfig.get(table).getKeyExp(), "", fileName);
                            sql = formatRowMinMaxKey(sql, table, convertRows, fieldIdxMap, typeMap);
                            List<Map<String, Object>> cacheList = iocImportMapper.queryKeyListBySql(sql);
                            if (CollectionUtils.isNotEmpty(cacheList)) {
                                Map<String, Integer> keyCahceMap = new HashMap<String, Integer>();
                                for (Map cache : cacheList) {
                                    String key = "#";
                                    for (String keyIdx : keyListMap.get(keyConfig.get(table).getIdName())) {
                                        key += "_" + cache.get(keyIdx);
                                    }
                                    keyCahceMap.put(key, (Integer) cache.get(keyConfig.get(table).getIdName()));
                                }
                                keymap.put(table + "#" + keyConfig.get(table).getIdName(), keyCahceMap);

                                for (UpdRowDto rowDto : valueAddList) {
                                    String key = getRowKey(belongId, fieldIdxMap, jedis, table, tableOtherFields, keyListMap.get(keyConfig.get(table).getIdName()), configMap, titles, rowDto.getRowData(), rowDto.getRowCnt(), allC2rCols, c2rCols, fieldMap, typeMap, defaultExpMap, fileName, keymap, keyListMap, belongList);
                                    rowDto.setId(ConvertHelper.longConvert(keymap.get(table + "#" + keyConfig.get(table).getIdName()).get(key)));
                                }
                            }
                        }
                    }
                }

                if (isOverFlag != 2) {
                    int tcnt = 1;
                    for (String table : tableList) {
                        UpdTableDto updTableDto = new UpdTableDto();
                        updTableDto.setTableName(table);
                        List<String> fields = tableFields.get(table);
                        for (String keyIdx : keyListMap.get(keyConfig.get(table).getIdName())) {
                            fields.remove(keyIdx);
                        }
                        if (CollectionUtils.isNotEmpty(fields)) {
                            updTableDto.setFields(fields);
                            updTableDto.setRows(updList.get(table));

                            log.debug("修改 updTableDto 的数据为：{}", new GsonBuilder().create().toJson(updTableDto));

                            importService.fastUpdateList(updTableDto, progressListener, (existInsert ? 0.4 : 0) + (tcnt - 1) * 1.0 / tableList.size(), (existInsert ? 0.2 : 0.9) * tcnt / tableList.size());
                        }
                        tcnt++;
                    }
                }

                if (StringUtils.isNotEmpty(importFunc.getAfterTrigger())) {
                    Map<String, Object> binding = new HashMap<String, Object>();
                    binding.put("addList", addList);
                    binding.put("updList", updList);
                    binding.put("belongId", userInfo.getTenantId());
                    binding.put("corpId", ConvertHelper.longConvert(userInfo.getTenantId()));
                    groovyScriptEngine.execute(importFunc.getAfterTrigger(), binding);
                }
                if (!tableList.contains("wa_plan_emp_rel")) {
                    //保存模版文件
                    String finalBelongId = belongId.toString();
                    importOrgTemplateService.saveExcelTemplateFileAfterImport(titles, rtnFileName, corpId, finalBelongId, funcId, ConvertHelper.longConvert(userId), isUpdTemplate);
                }
                progressListener.updatePercent(1);
            } catch (Exception e) {
                log.error("导入数据异常，{}", e.getMessage(), e);
                throw e;
            } finally {
                jedis.close();
            }
            long endTime = new Date().getTime();
            String duration = IocUtil.formatTimeDuration(endTime - startTime);
            return new ImportResultMessage(0, "导入成功!", rows.size(), fieldDtosList.size(), duration);
        } finally {
            redisTemplate.delete(lockKey);
            progressListener.updatePercent(1);
        }
    }

    public String getWaEmpLeaveTimeSlot(List<String> row, Map<String, Integer> fieldIdxMap) {
        if (row.get(fieldIdxMap.get("start_time")) == null || row.get(fieldIdxMap.get("end_time")) == null) {
            return "";
        }
        String startTimeStr = row.get(fieldIdxMap.get("start_time"));
        String endTimeStr = row.get(fieldIdxMap.get("end_time"));
        startTimeStr = startTimeStr.replaceAll("/", "-");
        endTimeStr = endTimeStr.replaceAll("/", "-");
        if (fieldIdxMap.containsKey("shalf_day") && fieldIdxMap.containsKey("ehalf_day")) {
            return startTimeStr + BaseConst.LEAVE_HALF_MAPS.get(row.get(fieldIdxMap.get("shalf_day"))) + " -> " + endTimeStr + BaseConst.LEAVE_HALF_MAPS.get(row.get(fieldIdxMap.get("ehalf_day")));
        } else {
            return startTimeStr + " -> " + endTimeStr;
        }
    }

//    private Long getPayStructId(String workno, Map<String, Integer> fieldIdxMap, List<String> row) {
//        String structName = null;
//        if (fieldIdxMap.get("def_struct_main_id") != null) {
//            structName = row.get(fieldIdxMap.get("def_struct_main_id"));
//        }
//        Long strucId;
//        if (StringUtil.isNullOrEmpty(structName)) {
//            strucId = importCacheService.getPayStrucMainByEmp(UserContext.getCorpId(), UserContext.getTenantId(), workno);
//        } else {
//            strucId = 0L;
//        }
//        return strucId;
//    }

    private Object getExistFkVal(String value, String belongId, String fileName, Map<String, Integer> fieldIdxMap, Map<String, String> typeMap, Jedis jedis, List<String> row, Integer rowId, String itemCode, String defaultExp) throws Exception {
        String sql = IocUtil.formatExp(defaultExp, value, fileName);
        sql = formatRowKey(sql, row, fieldIdxMap, typeMap);
        Object existFk = iocImportMapper.queryObjectBySql(sql);
        if (existFk == null && defaultExp.contains(IocUtil.PARAM_FILE_NAME)) {
            existFk = createFKObject(jedis, null, itemCode, fileName, false, rowId, ConvertHelper.stringConvert(belongId), null);
        }
        return existFk;
    }

    public Object getItemValue(Jedis jedis, Map<String, List<CheckRuleDto>> checkRuleMap, String table, String itemCode, List<String> row, Integer rowId, Integer colCnt, String fileName, List<String> belongList, Map<String, String> typeMap, Map<String, Integer> fieldIdxMap) throws Exception {
        Object val = null;
        List<CheckRuleDto> fieldCheckRuleList = checkRuleMap.get(table + "_" + itemCode);
        if (CollectionUtils.isNotEmpty(fieldCheckRuleList)) {
            for (CheckRuleDto crDto : fieldCheckRuleList) {
                String cellValue1 = row.get(colCnt);
                switch (IocEnum.valueOf(crDto.getCheckPattern())) {
                    case FK:
                        if (StringUtils.isNotEmpty(cellValue1)) {
                            String fksql = IocUtil.formatExp(crDto.getCheckExp(), cellValue1, fileName);
                            if (fksql.startsWith(IocUtil.REDIS_DICT_CACHE)) {
                                Pattern pattern = Pattern.compile(IocUtil.REDIS_DICT_CACHE + "\\((.*)\\)+?", Pattern.CASE_INSENSITIVE);
                                Matcher matcher = pattern.matcher(fksql);
                                if (matcher.find()) {
                                    String[] params = matcher.group(1).split(",");
                                    String dictName = params[1].trim();
                                    SysParmDict dic = CDCacheUtil.getDictByName(jedis, ConvertHelper.longConvert(params[0].trim()), dictName, params[2].trim());
                                    if (dic != null) {
                                        val = dic.getDictId();
                                    }
                                }
                            } else if (fksql.equals(IocUtil.REDIS_EMPID_CACHE)) {
                                if (cellValue1.contains(",")) {
                                    List<Long> fkList = new ArrayList<>();
                                    for (String workno : cellValue1.split(",")) {
                                        fkList.add(getEmpIdByWorkNo(jedis, belongList, workno));
                                    }
                                    val = StringUtils.join(fkList, ",");
                                } else {
                                    val = getEmpIdByWorkNo(jedis, belongList, cellValue1);
                                }
                            } else {
                                fksql = formatRowKey(fksql, row, fieldIdxMap, typeMap);
                                Object existFk = getFkByExpCache(fksql);
                                val = existFk;
                            }
                        }
                        break;
                    case FKC:
                        String fksql = IocUtil.formatExp(crDto.getCheckExp(), cellValue1, fileName);
                        Long existFk = createFKObject(jedis, fksql, itemCode, cellValue1, true, rowId, null, null);
                        val = Optional.ofNullable(existFk).orElse(-1L);
                        break;
                    case Range:
                        String range = crDto.getCheckExp();
                        if (range.contains(":")) {
                            String[] ranges = range.split(",");
                            for (String dict : ranges) {
                                String[] dictMap = dict.split(":");
                                if (dictMap[0].equals(row.get(colCnt))) {
                                    val = dictMap[1];
                                    break;
                                }
                            }
                        } else {
                            val = row.get(colCnt);
                        }
                        break;
                    case Enum:
                        String enumExp = crDto.getCheckExp();
                        if (StringUtils.isNotEmpty(enumExp)) {
                            Class<?> clazz = Class.forName("com.caidao1.commons.enums." + enumExp);
                            Method method = clazz.getMethod("getValue", String.class);
                            val = method.invoke(null, row.get(colCnt));
                        }
                        break;
                    case FKB:
                        if (StringUtils.isNotEmpty(cellValue1)) {
                            String fkbsql = IocUtil.formatExp(crDto.getCheckExp(), cellValue1, fileName);
                            val = iocImportMapper.queryObjectBySql(formatRowKey(fkbsql, row, fieldIdxMap, typeMap));
                        }
                        break;
                    default:
                        if (val == null) {
                            val = row.get(colCnt);
                        }
                }
            }
        } else {
            val = row.get(colCnt);
        }
        return val;
    }

    public Long createFKObject(Jedis jedis, String fksql, String itemCode, String cellValue, boolean create, Integer rowId, String belongId, Long orgId) throws Exception {
        Long existFk = -1L;
        if (StringUtils.isNotEmpty(cellValue)) {
            if (StringUtils.isNotEmpty(fksql) && fksql.startsWith(IocUtil.REDIS_DICT_CACHE)) {
                Pattern pattern = Pattern.compile(IocUtil.REDIS_DICT_CACHE + "\\((.*)\\)+?", Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(fksql);
                if (matcher.find()) {
                    String[] params = matcher.group(1).split(",");
                    String typeCode = params[1].trim();
                    existFk = getFkByDictCache(jedis, ConvertHelper.longConvert(params[0].trim()), typeCode, params[2].trim(), true);
                }
            }
        }
        return existFk;
    }
}