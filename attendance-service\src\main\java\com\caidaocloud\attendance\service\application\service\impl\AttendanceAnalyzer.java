package com.caidaocloud.attendance.service.application.service.impl;


import com.alibaba.nacos.common.utils.CollectionUtils;
import com.caidao1.commons.exception.CDException;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidaocloud.attendance.service.application.dto.AnalyzeResultCalculateDto;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Function;

/**
 * 考勤分析并发执行工具类
 *
 * <AUTHOR>
 * @Date 2025/7/17
 */
@Slf4j
public class AttendanceAnalyzer implements AutoCloseable {
    // 创建固定大小的线程池，核心线程数为5
    private final ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(1);
    private final ExecutorService executorService = Executors.newFixedThreadPool(2);

    // 设置任务超时时间为1小时（3600秒）
    private static final long TASK_TIMEOUT = 3600;
    private static final TimeUnit TIMEOUT_UNIT = TimeUnit.SECONDS;

    // 使用函数式接口接收分析方法
    private final Function<AnalyzeResultCalculateDto, Integer> analysisFunction;

    // 构造函数注入分析方法
    public AttendanceAnalyzer(Function<AnalyzeResultCalculateDto, Integer> analysisFunction) {
        this.analysisFunction = analysisFunction;
    }

    public int analyze(String belongId, Long startDate, Long endDate, Integer tmType, List<Long> empIdList,
                       WaParseGroup parseGroup, WaSob waSob, Long userId, boolean isJob, boolean includeInProgress) throws Exception {
        if (CollectionUtils.isEmpty(empIdList)) {
            return 0;
        }
        int batchSize = 200;
        int totalSize = empIdList.size();
        int batchCount = (totalSize + batchSize - 1) / batchSize;
        log.info("===tmType={} 分{}次并发执行", tmType, batchCount);
        List<Future<Integer>> futures = new ArrayList<>(batchCount);
        // 分割任务并提交到线程池
        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min((i + 1) * batchSize, totalSize);
            List<Long> batchEmpIds = empIdList.subList(fromIndex, toIndex);

            int finalI = i;
            Callable<Integer> task = () -> {
                try {
                    AnalyzeResultCalculateDto calculateDto = new AnalyzeResultCalculateDto();
                    calculateDto.setBelongid(belongId);
                    calculateDto.setStartDate(startDate);
                    calculateDto.setEndDate(endDate);
                    calculateDto.setEmpids(batchEmpIds);
                    calculateDto.setTmType(tmType);
                    calculateDto.setParseGroup(parseGroup);
                    calculateDto.setWaSob(waSob);
                    calculateDto.setUserId(userId);
                    calculateDto.setJob(isJob);
                    calculateDto.setIncludeInProgress(includeInProgress);
                    // 使用注入的函数执行分析
                    int result = analysisFunction.apply(calculateDto);
                    log.info("===完成第{}批，处理{}条员工记录", finalI + 1, batchEmpIds.size());
                    return result;
                } catch (Exception e) {
                    log.error("批次处理异常: {}", e.getMessage(), e);
                    if (e instanceof CDException) {
                        throw new CDException(e.getMessage());
                    }
                    throw e;
                }
            };

            // 提交任务并添加超时监控
            futures.add(submitTaskWithTimeout(task, TASK_TIMEOUT, TIMEOUT_UNIT));
        }
        // 汇总所有批次的结果
        int totalRows = 0;
        for (Future<Integer> future : futures) {
            try {
                totalRows += future.get();
            } catch (ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof TimeoutException) {
                    log.error("任务执行超时（超过{}小时）", TASK_TIMEOUT / 3600.0);
                } else if (cause instanceof CDException) {
                    throw new CDException(cause.getMessage());
                }
                log.error("考勤分析任务失败{}", cause.getMessage(), e);
                throw new RuntimeException("考勤分析任务失败", cause);
            }
        }

        return totalRows;
    }

    // 带超时控制的任务提交方法
    private <T> Future<T> submitTaskWithTimeout(Callable<T> task, long timeout, TimeUnit unit) {
        final FutureTask<T> futureTask = new FutureTask<>(task);

        // 提交主任务
        executorService.execute(futureTask);
        // 安排超时任务
        scheduledExecutor.schedule(() -> {
            if (!futureTask.isDone()) {
                log.warn("任务超时，正在取消...");
                futureTask.cancel(true); // 尝试中断执行中的任务
            }
        }, timeout, unit);
        return futureTask;
    }

    @Override
    public void close() throws Exception {
        shutdown();
    }

    // 资源释放方法
    public void shutdown() {
        // 关闭主执行器
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // 关闭调度器
        scheduledExecutor.shutdown();
        try {
            if (!scheduledExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                scheduledExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduledExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
