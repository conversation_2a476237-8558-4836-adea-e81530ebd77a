package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("考勤方案-工时规则-保存DTO")
public class WorkingHourRuleSaveDto {
    @ApiModelProperty("规则Id，修改时使用")
    private Long ruleId;
    @ApiModelProperty("工时类型：STD 标准工时制、COMP 综合工时制、FLEXIBLE 弹性工时制")
    private String workType;
    @ApiModelProperty("综合工时制-统计周期：1 考勤周期")
    private Integer compStatPeriod;
    @ApiModelProperty("综合工时制-统计区间：1 在职期间")
    private Integer compStatInterval;
    @ApiModelProperty("综合工时制-统计方式：1 从每月的考勤周期起始日开始")
    private Integer compStatMethod;
    @ApiModelProperty("综合工时制-标准工时-统计方式：1 按固定值、2 按工作日历")
    private Integer compStdCalcMethod;
    @ApiModelProperty("综合工时-制标准工时-时长：单位小时")
    private BigDecimal compStdWorkingHours;
    @ApiModelProperty("综合工时制-标准工时-日期类型：1 工作日、2 休息日、3 法定假日、4 特殊休日、5 法定休日, 多个值使用,隔开")
    private String compStdDateType;
    @ApiModelProperty("考勤方案ID")
    private Integer waGroupId;
}