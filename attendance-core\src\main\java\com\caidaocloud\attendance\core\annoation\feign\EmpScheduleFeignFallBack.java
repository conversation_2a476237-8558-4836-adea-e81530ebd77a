package com.caidaocloud.attendance.core.annoation.feign;

import com.caidaocloud.attendance.core.schedule.dto.ListEmpScheduleQueryDto;
import com.caidaocloud.attendance.core.wa.vo.WorkingHourCalendarDateVo;
import com.caidaocloud.attendance.core.wa.vo.EmpMultiShiftInfoVo;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EmpScheduleFeignFallBack implements EmpScheduleFeignClient {
    @Override
    public Result<List<EmpMultiShiftInfoVo>> getEmpScheduleList(ListEmpScheduleQueryDto queryDto) {
        return Result.fail();
    }

    @Override
    public Result<List<WorkingHourCalendarDateVo>> getShiftByMonthByEmpId(Integer searchMonth, Long empId) {
        return Result.fail();
    }
}
