package com.caidaocloud.attendance.sdk.feign;

import com.caidaocloud.attendance.core.wa.dto.TenantCommonConfigDto;
import com.caidaocloud.attendance.sdk.dto.tenant.TenantDto;
import com.caidaocloud.attendance.sdk.feign.fallback.TenantFeignFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import java.util.List;

/**
 * 运维平台-租户管理接口
 */
@FeignClient(
        value = "${feign.rename.caidaocloud-maintenance-service:caidaocloud-maintenance-service}",
        fallback = TenantFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "tenantFeignClient"
)
public interface TenantFeignClient {

    /**
     * 查看租户列表
     *
     * @return
     */
    @ResponseBody
    @GetMapping("/api/maintenance/v1/tenant")
    Result<List<TenantDto>> tenantList();

    @GetMapping("/api/maintenance/v1/common/config")
    Result<TenantCommonConfigDto> tenantCommonConfig();
}
