# 考勤服务消息队列配置说明

## 🎯 概述

本文档介绍考勤服务的消息队列配置系统，通过 Nacos 配置中心统一管理 RabbitMQ 相关配置，支持死信队列、重试机制、失败策略等功能的灵活配置。

## 📋 配置结构

### 核心配置类
- `AttendanceMqProperties`: 统一的配置属性类
- `MultiNodeClockAnalyseRabbitConfig`: RabbitMQ 队列和交换机配置
- `RabbitMQConfig`: 消费者容器配置
- `AttendanceConfigurationPropertiesConfig`: 配置验证和启用类

### 相关消费者
- `MultiNodeAnalyseClockSubscribe`: 主要消息消费者（支持手动 ACK）
- `MultiNodeAnalyseClockDlqSubscribe`: 死信队列监控消费者

## 🔧 配置项详解

### 基础配置

| 配置项 | 默认值 | 说明 | 推荐值 |
|-------|-------|------|--------|
| `attendance.mq.enableDLQ` | `true` | 是否启用死信队列 | 生产：`true` |
| `attendance.mq.maxRetryCount` | `1` | 最大重试次数 | 生产：`3` |
| `attendance.mq.failureStrategy` | `ACK` | 失败处理策略 | 生产：`ACK` |

### 消费者配置

| 配置项 | 默认值 | 说明 | 推荐值 |
|-------|-------|------|--------|
| `consumer.concurrentConsumers` | `1` | 最小并发消费者数 | 生产：`2` |
| `consumer.maxConcurrentConsumers` | `3` | 最大并发消费者数 | 生产：`5` |
| `consumer.prefetchCount` | `1` | 预取消息数量 | 生产：`1` |

### 队列配置

| 配置项 | 默认值 | 说明 | 推荐值 |
|-------|-------|------|--------|
| `queue.messageTtl` | `0` | 消息TTL（毫秒） | 生产：`0` |
| `queue.autoDelete` | `false` | 是否自动删除队列 | 生产：`false` |

## 🚀 使用方式

### 1. 在 Nacos 配置中心添加配置

```yaml
attendance:
  mq:
    enableDLQ: true
    maxRetryCount: 3
    failureStrategy: ACK
    consumer:
      concurrentConsumers: 2
      maxConcurrentConsumers: 5
      prefetchCount: 1
    queue:
      messageTtl: 0
      autoDelete: false
```

### 2. 配置生效

- 配置会在应用启动时自动加载
- 支持 Nacos 配置热更新（需要重启相关消费者）
- 启动时会自动验证配置合理性并输出到日志

### 3. 查看配置状态

启动日志中会显示当前配置：

```
============ 考勤消息队列配置信息 ============
启用死信队列: true
最大重试次数: 3
失败处理策略: ACK
消费者并发数: 2
最大消费者并发数: 5
预取数量: 1
消息TTL: 0
自动删除队列: false
=============================================
```

## 🔄 不同场景的配置策略

### 场景一：生产环境（推荐）

```yaml
attendance:
  mq:
    enableDLQ: true       # 启用死信队列保护
    maxRetryCount: 3      # 合理的重试次数
    failureStrategy: ACK  # 安全的失败策略
    consumer:
      concurrentConsumers: 2
      maxConcurrentConsumers: 5
      prefetchCount: 1
```

**特点**：
- ✅ 高可靠性：死信队列保护失败消息
- ✅ 合理重试：避免临时故障
- ✅ 性能平衡：适度并发处理
- ✅ 故障恢复：支持人工介入处理

### 场景二：测试环境

```yaml
attendance:
  mq:
    enableDLQ: true
    maxRetryCount: 1      # 减少重试，快速暴露问题
    failureStrategy: ACK
    consumer:
      concurrentConsumers: 1
      maxConcurrentConsumers: 2
    queue:
      messageTtl: 300000  # 5分钟过期，避免测试数据积累
```

**特点**：
- 🔍 快速定位问题
- 💾 减少资源占用
- ⏰ 自动清理过期消息

### 场景三：开发环境

```yaml
attendance:
  mq:
    enableDLQ: false      # 简化配置
    maxRetryCount: 1
    failureStrategy: ACK
    consumer:
      concurrentConsumers: 1
      maxConcurrentConsumers: 1
```

**特点**：
- 🎯 简化开发和调试
- 💻 单线程处理，便于断点调试
- 🚫 不依赖死信队列

### 场景四：现有队列兼容

```yaml
attendance:
  mq:
    enableDLQ: false      # 兼容现有队列参数
    maxRetryCount: 1
    failureStrategy: ACK  # 安全策略
```

**特点**：
- 🔄 平滑迁移现有队列
- ⚠️ 临时方案，建议后续升级

## 🛡️ 失败处理策略对比

| 策略 | 死信队列开启 | 死信队列关闭 | 适用场景 |
|------|-------------|-------------|----------|
| `ACK` | 进入死信队列 | 丢弃消息 | 生产环境推荐 |
| `NACK_DISCARD` | 进入死信队列 | 丢弃消息 | 特殊业务场景 |
| `NACK_REQUEUE` | 进入死信队列 | 重新入队（危险） | 不推荐使用 |

## 📊 监控和告警

### 启动时监控

系统启动时会输出详细的配置信息和验证结果，便于运维人员确认配置正确性。

### 死信队列监控

当启用死信队列时，`MultiNodeAnalyseClockDlqSubscribe` 会自动监控失败消息：

- 记录失败详情
- 输出业务关键信息
- 支持自定义告警逻辑

### 配置验证警告

系统会自动检测配置合理性并输出警告：

- 重试次数过高警告
- 消费者配置不合理警告
- 死信队列配置冲突警告

## 🔧 高级配置

### 消息 TTL 配置

设置消息过期时间，适合实时性要求高的业务：

```yaml
queue:
  messageTtl: 300000  # 5分钟过期
```

### 动态消费者扩缩容

根据消息积压情况自动调整消费者数量：

```yaml
consumer:
  concurrentConsumers: 1    # 最少1个消费者
  maxConcurrentConsumers: 10 # 最多10个消费者
```

## 🚨 注意事项

1. **队列参数一致性**：修改 `enableDLQ` 配置可能需要删除现有队列
2. **消息丢失风险**：`enableDLQ: false` 时失败消息可能丢失
3. **性能影响**：过高的重试次数和并发数可能影响系统性能
4. **配置热更新**：大部分配置需要重启应用才能生效

## 📝 故障排查

### 队列参数冲突

**现象**：启动时报 `PRECONDITION_FAILED` 错误

**解决**：
1. 检查现有队列参数
2. 删除冲突队列或修改配置
3. 确保 `enableDLQ` 设置正确

### 消息堆积

**现象**：队列中消息持续增长

**排查**：
1. 检查消费者并发配置
2. 查看业务处理性能
3. 确认是否有大量重试

### 死信队列消息过多

**现象**：死信队列中消息不断增长

**排查**：
1. 查看具体失败原因
2. 检查业务逻辑异常
3. 考虑调整重试策略

## 🔗 相关文件

- 配置类：`AttendanceMqProperties.java`
- 配置示例：`mq-config-example.yml`
- 队列配置：`MultiNodeClockAnalyseRabbitConfig.java`
- 消费者：`MultiNodeAnalyseClockSubscribe.java`
- 死信队列监控：`MultiNodeAnalyseClockDlqSubscribe.java` 