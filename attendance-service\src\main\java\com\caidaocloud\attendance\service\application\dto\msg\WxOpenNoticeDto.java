package com.caidaocloud.attendance.service.application.dto.msg;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class WxOpenNoticeDto implements Serializable {

    /**
     * 消息通知对象，多个消息对象使用"|"进行拼接，最多支持1000个对象
     */
    private String[] empIds;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 跳转链接
     */
    private String linkUrl;

    /**
     * 点击消息卡片后小程序页面地址
     */
    private String pagePath;

    /**
     * 消息通知类型，默认0:卡片消息，1:文本消息, 2:markdown消息
     */
    private int msgType;

    /**
     * 底部文字，默认为详情，不超过四个字
     */
    private String btntxt;

}