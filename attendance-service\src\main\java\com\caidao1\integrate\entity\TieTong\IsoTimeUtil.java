package com.caidao1.integrate.entity.TieTong;


import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Date;

public class IsoTimeUtil {

    public static void main(String[] args)throws Exception {
        String ISODateTime = "2021-10-26T14:53:29+08:00";
        System.out.println("dateStr = " + isoTimeToDefaultTime(ISODateTime));

        String dateTime = "2021-10-26 15:34:32";
        System.out.println("ISOStr = " + timeToIso(dateTime));

        System.out.println(isoTimeStrToTime(ISODateTime));
        System.out.println(isoTimeStrToTimeDayBegin(ISODateTime));
    }

    /**
     * ISO8601时间格式转字符串类型时间(yyyy-MM-dd HH:mm:ss)
     *
     * @param isoTimeDateStr ISO8601时间格式字符串
     * @return
     */
    public static String isoTimeToDefaultTime(String isoTimeDateStr) {
        DateTimeFormatter dtf1 = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ssZZ");
        DateTime dt = dtf1.parseDateTime(isoTimeDateStr);
        DateTimeFormatter dtf2 = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        String res = dt.toString(dtf2);
        return res;
    }

    /**
     * 字符串类型时间(yyyy-MM-dd HH:mm:ss)转ISO8601时间格式
     *
     * @param timestamp 字符串类型时间
     * @return
     */
    public static String timeToIso(String timestamp) {
        java.time.format.DateTimeFormatter dtf1 = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        java.time.LocalDateTime ldt = java.time.LocalDateTime.parse(timestamp, dtf1);
        ZoneOffset offset = ZoneOffset.of("+08:00");
        OffsetDateTime date = OffsetDateTime.of(ldt, offset);
        java.time.format.DateTimeFormatter dtf2 = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
        String res = date.format(dtf2);
        return res;
    }

    public static Long isoTimeStrToTime(String isoTimeStr) throws Exception {
        String timeStr = isoTimeToDefaultTime(isoTimeStr);
        Date date = DateUtils.parseDate(timeStr, "yyyy-MM-dd HH:mm:ss");
        return date.getTime() / 1000 ;
    }
    public static Long isoTimeStrToTimeDayBegin(String isoTimeStr) throws Exception {
        String timeStr = isoTimeToDefaultTime(isoTimeStr);
        Date date = DateUtils.parseDate(timeStr, "yyyy-MM-dd HH:mm:ss");
        long time = date.getTime();
        return (time - (time + 8 * 3600000) % 86400000) / 1000;
    }
}
