package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.cache.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 考勤分析进度更新器
 */
@Slf4j
@Component
public class ClockAnalyseProgressUpd {
    public static final String ASYNC_ANALYSE_PROCESS_TOTAL_COUNT = "ASYNC_ANALYSE_PROCESS_TOTAL_COUNT_";
    public static final String ASYNC_ANALYSE_PROCESS_COMPLETE_COUNT = "ASYNC_ANALYSE_PROCESS_COMPLETE_COUNT_";
    public static final String ASYNC_ANALYSE_PROCESS = "ASYNC_ANALYSE_PROCESS_";
    public static final int CACHE_EXPIRE_SECONDS = 3600;

    /**
     * 初始化任务进度
     *
     * @param progress     任务标识
     * @param total        总任务数
     * @param cacheService 缓存服务
     */
    public static void initProgress(String progress, int total, CacheService cacheService) {
        if (progress == null || progress.trim().isEmpty()) {
            log.error("ERROR: Cannot initialize progress: progress parameter is null or empty");
            return;
        }

        if (total <= 0) {
            log.error("ERROR: Cannot initialize progress: total should be greater than 0, got {}", total);
            return;
        }

        if (cacheService == null) {
            log.error("ERROR: Cannot initialize progress: cacheService is null");
            return;
        }

        try {
            cacheService.cacheValue(ASYNC_ANALYSE_PROCESS_TOTAL_COUNT + progress, String.valueOf(total), CACHE_EXPIRE_SECONDS);
            cacheService.cacheValue(ASYNC_ANALYSE_PROCESS_COMPLETE_COUNT + progress, "0", CACHE_EXPIRE_SECONDS);
            cacheService.cacheValue(ASYNC_ANALYSE_PROCESS + progress, "0.1", CACHE_EXPIRE_SECONDS);
        } catch (Exception e) {
            log.error("ERROR: Failed to initialize progress: progress={}, total={}, error={}",
                    progress, total, e.getMessage(), e);
        }
    }

    /**
     * 更新任务进度
     *
     * @param progress      任务标识
     * @param completedSize 本次完成的任务数量
     * @param cacheService  缓存服务
     */
    public static void doUpdateProgress(String progress, int completedSize, CacheService cacheService) {
        if (progress == null || progress.trim().isEmpty()) {
            log.warn("WARN: Cannot update progress: progress parameter is null or empty");
            return;
        }

        if (completedSize <= 0) {
            log.warn("WARN: Invalid completedSize for progress update: progress={}, size={}", progress, completedSize);
            return;
        }

        if (cacheService == null) {
            log.error("ERROR: Cannot update progress: cacheService is null");
            return;
        }

        try {
            // 获取总任务数
            String totalStr = cacheService.getValue(ASYNC_ANALYSE_PROCESS_TOTAL_COUNT + progress);
            if (totalStr == null || totalStr.trim().isEmpty()) {
                log.warn("WARN: Progress not initialized or expired: progress={}", progress);
                return;
            }

            // 获取当前完成数
            String completedStr = cacheService.getValue(ASYNC_ANALYSE_PROCESS_COMPLETE_COUNT + progress);
            if (completedStr == null || completedStr.trim().isEmpty()) {
                log.warn("WARN: Progress completion count not found: progress={}", progress);
                return;
            }

            int total = Integer.parseInt(totalStr);
            int currentCompleted = Integer.parseInt(completedStr);
            int newCompleted = Math.min(currentCompleted + completedSize, total);

            // 更新完成数
            cacheService.cacheValue(ASYNC_ANALYSE_PROCESS_COMPLETE_COUNT + progress,
                    String.valueOf(newCompleted), CACHE_EXPIRE_SECONDS);

            // 计算并更新进度率
            Double processRate = new BigDecimal(newCompleted)
                    .divide(new BigDecimal(total), 2, RoundingMode.HALF_UP)
                    .doubleValue();
            processRate = processRate > 1.0 ? 1.0 : processRate;
            cacheService.cacheValue(ASYNC_ANALYSE_PROCESS + progress,
                    String.valueOf(processRate), CACHE_EXPIRE_SECONDS);
        } catch (NumberFormatException e) {
            log.error("ERROR: Error parsing progress data: progress={}, error={}", progress, e.getMessage(), e);
        } catch (Exception e) {
            log.error("ERROR: Error updating progress: progress={}, completedSize={}, error={}",
                    progress, completedSize, e.getMessage(), e);
        }
    }

    /**
     * 清除任务进度缓存
     *
     * @param progress     任务标识
     * @param cacheService 缓存服务
     */
    public static void doClearCache(String progress, CacheService cacheService) {
        if (progress == null || progress.trim().isEmpty()) {
            log.warn("WARN: Cannot clear cache: progress parameter is null or empty");
            return;
        }

        if (cacheService == null) {
            log.error("ERROR: Cannot clear cache: cacheService is null");
            return;
        }

        try {
            cacheService.remove(ASYNC_ANALYSE_PROCESS_TOTAL_COUNT + progress);
            cacheService.remove(ASYNC_ANALYSE_PROCESS_COMPLETE_COUNT + progress);
            cacheService.remove(ASYNC_ANALYSE_PROCESS + progress);
        } catch (Exception e) {
            log.error("ERROR: Error clearing progress cache: progress={}, error={}",
                    progress, e.getMessage(), e);
        }
    }
} 