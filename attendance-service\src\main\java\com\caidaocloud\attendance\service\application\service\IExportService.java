package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.infrastructure.feign.wfm.SchedulingAndProcessStatusDto;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.export.ExportEmpClockPlanDto;
import com.caidaocloud.attendance.service.interfaces.dto.export.ExportEmpShiftDto;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.BatchLeaveQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.leaveExtension.LeaveExtensionReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.BatchOvertimeQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OtLeftDurationRequestDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.CompensatoryQuotaSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.QuotaEmpDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.QuotaPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.task.TaskDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.workHour.WorkingHourSettlementPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WorkflowRevokeReqDto;
import com.caidaocloud.attendance.service.schedule.interfaces.dto.performance.PerformanceEmpQueryDto;
import com.caidaocloud.attendance.service.schedule.interfaces.dto.performance.RecordCheckQueryDto;
import com.caidaocloud.attendance.service.wfm.application.dto.ProcessStatisticsPageQueryDto;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.WfmDayAnalysePageDto;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.WfmMonthAnalysePageDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;

import java.util.Locale;

/**
 * @Author: Aaron.Chen
 * @Date: 2021/4/20 16:32
 * @Description:
 **/
public interface IExportService {

    void uploadData(RegisterRecordRequestDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadPerformanceData(PerformanceEmpQueryDto dto, TaskDto task, SecurityUserInfo userInfo);

    /**
     * 考勤机数据是否打卡记录导出
     * @param dto
     * @param task
     * @param userInfo
     */
    void uploadKaoQinJiRecordCheckData(RecordCheckQueryDto dto, TaskDto task, SecurityUserInfo userInfo);

    void uploadSchedulingAndProcessStatus(SchedulingAndProcessStatusDto dto, TaskDto task, SecurityUserInfo userInfo);

    void uploadPerformanceImportTemplateData(PerformanceEmpQueryDto dto, TaskDto task, SecurityUserInfo userInfo);

    void uploadBdkData(RegisterRecordRequestDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadData(ShiftPageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadData(LeaveApplyDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadData(OvertimeApplyDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadData(QuotaPageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadData(DayAnalysePageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    /**
     * 动态列 每日分析导出
     */
    void uploadDataByDynamic(DayAnalysePageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadData(MonthAnalysePageDto requestDto, TaskDto task, Boolean summary, UserInfo userInfo, Locale locale);
    /**
     * 动态列 月度汇总分析导出
     */
    void uploadDataByDynamic(MonthAnalysePageDto requestDto, TaskDto task, Boolean summary, UserInfo userInfo, Locale locale);

    void uploadEmpFixQuotaData(FixQuotaSearchDto dto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadEmpCompensatoryQuotaData(CompensatoryQuotaSearchDto dto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadAnnualQuotaData(AnnualLeaveSearchDto dto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadEmpQuotaData(QuotaEmpDto dto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadEmpShiftChangeData(EmpShiftSearchDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportEmpShiftList(ExportEmpShiftDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadEmpGroupChangeData(AttEmpGroupReqDto dto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportOutworkList(RegisterRecordRequestDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportEmpClockPlanList(ExportEmpClockPlanDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadLeaveCancelData(LeaveCancelReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportEmpTravelList(EmpTravelReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportDayAnalyseAbnormalList(DayAnalyseAbnormalPageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportOvertimeRevokeList(WorkflowRevokeReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportOvertimeAbolishList(WorkflowRevokeReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportTravelRevokeList(WorkflowRevokeReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportTravelAbolishList(WorkflowRevokeReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadBatchLeaveData(BatchLeaveQueryDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadBatchOvertimeData(BatchOvertimeQueryDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void uploadBatchAnalyseAdjustData(BatchAnalyseResultAdjustQueryDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportLeaveExtensionList(LeaveExtensionReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    /**
     * 动态列 考勤分析导出
     */
    void uploadDataAdvanceByDynamic(MonthAnalysePageDto dto, TaskDto task, boolean b, UserInfo userInfo, Locale locale, SecurityUserInfo sUserInfo);

    void exportEmpOtLeftDurationList(OtLeftDurationRequestDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportDayWorkingHourAnalyze(WfmDayAnalysePageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportMonthWorkingHourAnalyze(WfmMonthAnalysePageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportProcessStatistic(ProcessStatisticsPageQueryDto requestDto, TaskDto task, UserInfo userInfo, Locale locale);

    void exportWorkHourSettlement(WorkingHourSettlementPageDto requestDto, TaskDto task, SecurityUserInfo userInfo, Locale locale);

    void uploadDataAdvanceByDynamicMultiGroup(MonthAnalysePageDtoV2 dto, TaskDto task, boolean b, UserInfo userInfo, Locale locale);
}
