//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.caidao1.integrate.util;

import com.caidao1.commons.mybatis.ReflectUtil;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.DateUtils;
import com.caidao1.commons.utils.JacksonJsonUtil;
import com.caidao1.integrate.mybatis.model.SysDataInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class IntegrateUtil {
    private static Pattern patternMap = Pattern.compile(":MAP\\((.*)#(.*)\\)#(.*)", Pattern.CASE_INSENSITIVE);
    private static Pattern patternNvl = Pattern.compile(":NVL\\((.*);(.*)\\)", Pattern.CASE_INSENSITIVE);

    public IntegrateUtil() {
    }

    public static List<Map<String, Object>> parseSourceResultSourceExp(SysDataInput dataInput, List<Map<String, Object>> targetSourceResult, List<Map<String, Object>> sourceResult) {
        if (StringUtils.isNotBlank(dataInput.getSourceExp())) {
            sourceResult.stream().forEach(sourceRow -> {
                Map<String, Object> newRow = new HashMap<String, Object>();
                for (String field : dataInput.getSourceExp().split(",")) {
                    if (field.indexOf(":TIMESTAMP") != -1) {
                        // 文本转时间戳处理
                        hadlerTimeStamp(sourceRow, newRow, field);
                    } else if (field.contains(":MAP")) {
                        // 文本转数字MAP定义处理
                        handlerDataMapMapping(sourceRow, newRow, field);
                    } else if (field.contains(":NVL")) {
                        // 空字符串转NULL处理
                        handlerDataNVLMapping(sourceRow, newRow, field);
                    } else if (field.contains(":DEFAULT")) {
                        handlerDefaultValue(sourceRow, newRow, field);
                    } else {
                        // 原字段转新字段
                        String[] meta = field.split("#");
                        if (meta.length > 1) {
                            newRow.put(meta[1], sourceRow.get(meta[0]));
                        } else {
                            newRow.put(field, sourceRow.get(field));
                        }
                    }
                }
                targetSourceResult.add(newRow);
            });
            return targetSourceResult;
        }
        return sourceResult;
    }

    private static void handlerDataNVLMapping(Map<String, Object> sourceRow, Map<String, Object> newRow, String field) {
        Matcher matcher = patternNvl.matcher(field);
        if (matcher.find()) {
            String code1 = matcher.group(1);
            String code2 = matcher.group(2);
            String[] meta = field.split("#");
            newRow.put(meta[1], nvl(sourceRow.get(code1), sourceRow.get(code2)));
        }
    }

    private static void handlerDataMapMapping(Map<String, Object> sourceRow, Map<String, Object> newRow, String field) {
        Matcher matcher = patternMap.matcher(field);
        if (matcher.find()) {
            String code = matcher.group(1);
            String[] mapList = matcher.group(2).split("_");
            Object key = ReflectUtil.getItemValue(sourceRow, StringUtils.trim(code));
            for (String map : mapList) {
                String[] range = map.split(":");
                if (range[0].equals(String.valueOf(key))) {
                    newRow.put(matcher.group(3), range[1]);
                    break;
                }
            }
        }
    }

    public static void hadlerTimeStamp(Map<String, Object> item, Map<String, Object> row, String field) {
        String key = field.substring(field.lastIndexOf("#") + 1);
        String dataFormat = field.substring(field.indexOf("(") + 1, field.indexOf(")"));
        SimpleDateFormat sf = new SimpleDateFormat(dataFormat.split("#")[1]);
        try {
            Object object = item.get(dataFormat.split("#")[0]);
            if (object != null) {
                Long time = sf.parse((String) object).getTime() / 1000;
                row.put(key, time);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void handlerDefaultValue(Map<String, Object> item, Map<String, Object> row, String field) {
        String key = field.substring(field.indexOf("(") + 1, field.indexOf(")"));
        try {
            String[] valueAndKey = key.split("#");
            row.put(valueAndKey[1], valueAndKey[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public final static <T> T nvl(final T t, final T defaultValue) {
        T temp = null;
        if (t instanceof String) {
            temp = StringUtils.isBlank((String) t) ? null : t;
        }
        return temp == null ? defaultValue : t;
    }

    public static void main(String[] args) {
        System.out.println(getStringTime("今天-1", "yyyy-MM-dd HH:mm:ss", 0L));
        System.out.println(getStringTime("今天+1", "yyyy-MM-dd HH:mm:ss", 86399L));
        System.out.println(getStringTime("今天", "yyyy-MM-dd HH:mm:ss", 0L));
        System.out.println(getStringTime("今天", "yyyy-MM-dd HH:mm:ss", 86399L));
    }

    public static String getStringTime(String exp, String format, Long addTime) {
        exp = exp.replace("今天", "");
        Integer subtraction = null;
        Long onlyDate = DateUtil.getOnlyDate();
        onlyDate = onlyDate + addTime;
        if (exp.startsWith("-")) {
            subtraction = ConvertHelper.intConvert(exp.replace("-", ""));
            onlyDate = onlyDate- (86400 * subtraction);
        }
        if (exp.startsWith("+")) {
            subtraction = ConvertHelper.intConvert(exp.replace("+", ""));
            onlyDate = onlyDate + (86400 * subtraction);
        }
        return DateUtil.parseDateToPattern(new Date(onlyDate * 1000), format);
    }

    /**
     * 格式化日期
     *
     * @param exp
     * @param format
     * @return
     */
    public static String formatExp(String exp, String format) {
        String workCycleStart = "考勤周期开始日";
        String workCycleEnd = "考勤周期结束日";
        Calendar cal = Calendar.getInstance();
        Integer day = cal.get(Calendar.DAY_OF_MONTH);
        Integer month = cal.get(Calendar.MONTH);

        if (exp != null && exp.startsWith("当前周期")){
            Integer year = DateUtils.getYear();
            Integer mm = DateUtils.getMonth();
            Integer subtraction = ConvertHelper.intConvert(exp.replace("当前周期-", ""));
            if(subtraction != null){
                year = (mm == 1) ? year - 1 : year;
                mm = (mm == 1) ? 12 : mm - subtraction;
            }
            return year + "" + (mm < 10 ? ("0" + mm) : mm);
        } else if ("昨天".equals(exp)) {
            cal.add(Calendar.DATE, -1);
            if (StringUtils.isNotEmpty(format)) {
                return DateUtil.parseDateToPattern(cal.getTime(), format);
            } else {
                return DateUtil.getOnlyDate(cal.getTime()).toString();
            }
        } else if (exp != null && exp.startsWith("今天-")) {
            // 今天的时间减去几天
            Integer subtraction = ConvertHelper.intConvert(exp.replace("今天-", ""));
            cal.add(Calendar.DATE, -subtraction);
            if (StringUtils.isNotEmpty(format)) {
                return DateUtil.parseDateToPattern(cal.getTime(), format);
            } else {
                return DateUtil.getOnlyDate(cal.getTime()).toString();
            }
        } else if ("今天".equals(exp)) {
            if (StringUtils.isNotEmpty(format)) {
                return DateUtil.parseDateToPattern(cal.getTime(), format);
            } else {
                return DateUtil.getOnlyDate(cal.getTime()).toString();
            }
        } else if ("明天".equals(exp)) {
            cal.add(Calendar.DATE, 1);
            if (StringUtils.isNotEmpty(format)) {
                return DateUtil.parseDateToPattern(cal.getTime(), format);
            } else {
                return DateUtil.getOnlyDate(cal.getTime()).toString();
            }
        } else if ("月初".equals(exp)) {
            cal.set(Calendar.DATE, 1);
            return DateUtil.parseDateToPattern(cal.getTime(), format);
        } else if ("月末".equals(exp)) {
            cal.set(Calendar.DATE, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            return DateUtil.parseDateToPattern(cal.getTime(), format);
        } else if ("本月".equals(exp)) {
            return DateUtil.parseDateToPattern(cal.getTime(), format);
        } else if (exp.startsWith(workCycleStart)) {
            Integer cyleStart = Integer.valueOf(exp.replaceAll(workCycleStart, ""));
            //考勤周期 本月25号到下月24号
            if (day <= cyleStart) {
                cal.set(Calendar.MONTH, month - 1);
            }
            cal.set(Calendar.DAY_OF_MONTH, cyleStart);
            Long currentTime = DateUtil.getCurrentTime(false);
            // 如果考勤周期截止时间 > 当前时间 则设置成当前时间
            if (cal.getTimeInMillis() > currentTime) {
                cal.setTimeInMillis(currentTime);
            }
            return DateUtil.parseDateToPattern(cal.getTime(), format);
        } else if (exp.startsWith(workCycleEnd)) {
//            Integer end = Integer.valueOf(exp.replaceAll(workCycleEnd,""));
            return DateUtil.parseDateToPattern(cal.getTime(), format);
        }
        return exp;
    }

    /**
     * 格式化日期，返回日期时间戳
     *
     * @param exp
     * @param format
     * @return
     * @throws ParseException
     */
    public static Long getFormatTimeStamp(String exp, String format) throws ParseException {
        String workCycleStart = "考勤周期开始日";
        String workCycleEnd = "考勤周期结束日";

        Calendar cal = Calendar.getInstance();
        SimpleDateFormat df = new SimpleDateFormat(format);

        if ("昨天".equals(exp)) {
            cal.add(Calendar.DATE, -1);
        } else if (exp != null && exp.startsWith("今天-")) {
            // 今天的时间减去几天
            Integer subtraction = ConvertHelper.intConvert(exp.replace("今天-", ""));
            cal.add(Calendar.DATE, -subtraction);
        } else if ("今天".equals(exp)) {
            Date date = df.parse(df.format(cal.getTime()));
            return date.getTime() / 1000;
        } else if ("月初".equals(exp)) {
            cal.add(Calendar.MONTH, 0);
            cal.set(Calendar.DAY_OF_MONTH, 1);
        } else if ("月末".equals(exp)) {
            cal.add(Calendar.MONTH, 1);
            cal.set(Calendar.DAY_OF_MONTH, 0);
        } else if ("本月".equals(exp)) {
            Date date = df.parse(df.format(cal.getTime()));
            return date.getTime() / 1000;
        } else if (exp.startsWith(workCycleStart)) {
            Integer day = cal.get(Calendar.DAY_OF_MONTH);
            Integer month = cal.get(Calendar.MONTH);
            Integer cyleStart = Integer.valueOf(exp.replaceAll(workCycleStart, ""));
            //考勤周期 本月25号到下月24号
            if (day <= cyleStart) {
                cal.set(Calendar.MONTH, month - 1);
            }
            cal.set(Calendar.DAY_OF_MONTH, cyleStart);
            Long currentTime = DateUtil.getCurrentTime(false);
            // 如果考勤周期截止时间 > 当前时间 则设置成当前时间
            if (cal.getTimeInMillis() > currentTime) {
                cal.setTimeInMillis(currentTime);
            }
        } else if (exp.startsWith(workCycleEnd)) {
            Date date = df.parse(df.format(cal.getTime()));
            return date.getTime() / 1000;
        } else if (exp.matches("\\d{4}-\\d{2}-\\d{2}.*?")) {
            return DateUtil.convertStringToDateTime(exp, format, true);
        }
        return df.parse(df.format(cal.getTime())).getTime() / 1000;
    }
    /**
     * 从 authMap中获取参数
     *
     * @param cacheMap
     * @param nowHeaderValue 当前header 值
     * @return
     */
    public static String findLastNodeToken(Map cacheMap, String nowHeaderValue) {
        String tokenFindReg = nowHeaderValue.substring(nowHeaderValue.indexOf("#") + 1);
        String lastNodeToken = null;
        try {
            String[] tokenGetConfig = tokenFindReg.split("\\.");
            if(tokenGetConfig.length == 1){
                return nowHeaderValue.substring(0, nowHeaderValue.indexOf("#")) + cacheMap.get(tokenGetConfig[0]);
            }
            Map tokenChildMap = null;
            for (int i = 1; i <= tokenGetConfig.length; i++) {
                if (i == 1) {
                    tokenChildMap = (Map) JacksonJsonUtil.jsonToBean(JacksonJsonUtil.beanToJson(cacheMap.get(tokenGetConfig[i - 1])),
                            Map.class);
                } else {
                    if (i == tokenGetConfig.length) {
                        lastNodeToken = tokenChildMap.getOrDefault(tokenGetConfig[i - 1], "").toString();
                        break;
                    } else {
                        tokenChildMap = (Map) JacksonJsonUtil.jsonToBean(JacksonJsonUtil.beanToJson(tokenChildMap.get(tokenGetConfig[i - 1])),
                                Map.class);
                    }
                }
            }
        } catch (Exception e) {
            log.error("token 多层取值err:{}", e.getMessage(), e);
        }
        return lastNodeToken == null ? nowHeaderValue :
                nowHeaderValue.substring(0, nowHeaderValue.indexOf("#")) + lastNodeToken;
    }
}
