package com.caidaocloud.attendance.service.application.enums;

/**
 * <AUTHOR>
 */
public enum StatisticsTypeEnum {
    lat(1, "迟到", "lat"),
    early(2, "早退", "early"),
    kg(3, "旷工", "kg"),
    leavetime(4, "请假", "leavetime"),
    overtime(5, "加班", "overtime");

    private Integer index;

    private String name;

    private String code;

    StatisticsTypeEnum(Integer index, String name, String code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getCode(int index) {
        for (StatisticsTypeEnum s : StatisticsTypeEnum.values()) {
            if (s.getIndex() == index) {
                return s.code;
            }
        }
        return null;
    }


    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
