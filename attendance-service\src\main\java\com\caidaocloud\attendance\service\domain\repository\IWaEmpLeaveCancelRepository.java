package com.caidaocloud.attendance.service.domain.repository;


import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveCancelDo;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.dto.PageResult;

import java.util.List;
import java.util.Map;

public interface IWaEmpLeaveCancelRepository {

    void save(WaEmpLeaveCancelDo cancelDo);

    void update(WaEmpLeaveCancelDo cancelDo);

    PageResult<WaEmpLeaveCancelDo> selectPageList(AttendanceBasePage basePage, Map params);

    List<WaEmpLeaveCancelDo> getListByLeaveId(String tenantId, Integer leaveId);

    WaEmpLeaveCancelDo getById(String tenantId, Long leaveCancelId);

    WaEmpLeaveCancelDo getInfoById(String tenantId, Long leaveCancelId);

    List<WaEmpLeaveCancelDo> getListByLeaveIds(String tenantId, List<Integer> leaveIds);
}
