package com.caidaocloud.attendance.service.application.event.publish;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PerformanceEmpSyncPublish {
    @Resource
    private MqMessageProducer<PerformanceEmpSyncMessage> producer;

    private final static String EXCHANGE = "attendance.performance.fac.direct.exchange";

    private final static String ROUTING_KEY = "routingKey.performance";

    public void publish(String msg) {
        PerformanceEmpSyncMessage message = new PerformanceEmpSyncMessage();
        message.setBody(msg);
        message.setExchange(EXCHANGE);
        message.setRoutingKey(ROUTING_KEY);
        producer.publish(message);
    }
}
