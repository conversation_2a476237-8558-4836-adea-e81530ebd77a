package com.caidaocloud.attendance.service.application.feign;

import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = "${feign.rename.caidaocloud-hr-service:caidaocloud-hr-service}",
        fallback = WaEmpFeignClientFeignFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "waEmpFeginClient"
)
public interface WaEmpFeginClient {
    /**
     * 查询员工任职信息详情
     *
     * @param empId
     * @param dataTime
     * @return
     */
    @GetMapping("/api/hr/emp/work/v1/detail")
    Result<EmpWorkInfoVo> getEmpWorkInfo(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime);
}
