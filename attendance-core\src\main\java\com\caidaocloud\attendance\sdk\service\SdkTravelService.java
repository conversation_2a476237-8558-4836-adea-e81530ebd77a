package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.sdk.dto.SdkEmpTravelSaveDTO;
import com.caidaocloud.attendance.sdk.dto.SdkRevokeEmpTravelDTO;
import com.caidaocloud.attendance.sdk.feign.ITravelFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 出差申请
 *
 * <AUTHOR>
 * @Date 2023/6/19
 */
@Slf4j
@Service
public class SdkTravelService {
    @Autowired
    private ITravelFeignClient travelFeignClient;

    /**
     * 获取出差时长
     *
     * @param dto
     * @return
     */
    public Result<?> getTravelTime(SdkEmpTravelSaveDTO dto) {
        return travelFeignClient.getTravelTime(dto);
    }

    /**
     * 保存出差申请单
     *
     * @param dto
     * @return
     */
    public Result<?> saveTravelTime(SdkEmpTravelSaveDTO dto) {
        return travelFeignClient.saveTravelTime(dto);
    }

    /**
     * 撤销出差单
     * @param dto
     * @return
     */
    public Result<?> revokeEmpTravel(SdkRevokeEmpTravelDTO dto) {
        return travelFeignClient.revokeEmpTravel(dto);
    }
}
