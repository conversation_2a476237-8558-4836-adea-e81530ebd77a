package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.domain.repository.IWaAnalyseResultAdjustRepository;
import com.caidaocloud.dto.UserInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Data
@Slf4j
@Service
public class WaAnalyseResultAdjustDo {
    private Long adjustId;

    private Long batchId;

    private Long empid;

    private Long belongDate;

    private Integer originalSigninId;

    private Integer originalSignoffId;

    private Long originalSigninTime;

    private Long originalSignoffTime;

    private Float originalLateTime;

    private Float originalEarlyTime;

    private Integer originalKgWorkTime;

    private Long signinTime;

    private Long signoffTime;

    private String filePath;

    private String fileName;

    private String businessKey;

    private String reason;

    private String extCustomCol;

    private Integer status;

    private Long lastApprovalTime;

    private Long lastEmpid;

    private String revokeReason;

    private Integer revokeStatus;

    private String tenantId;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    @Autowired
    private IWaAnalyseResultAdjustRepository waAnalyseResultAdjustRepository;

    public WaAnalyseResultAdjustDo getById(Long batchId) {
        return waAnalyseResultAdjustRepository.getById(batchId);
    }

    public List<WaAnalyseResultAdjustDo> listByBatchId(Long batchId) {
        return waAnalyseResultAdjustRepository.getListByBatchId(batchId);
    }

    public List<WaAnalyseResultAdjustDo> listByDate(Long empid, Long belongDate, List<Integer> statusList) {
        return waAnalyseResultAdjustRepository.listByDate(empid, belongDate, statusList);
    }

    public List<WaAnalyseResultAdjustDo> listByDateRange(Long empid, Long startDate, Long endDate, List<Integer> statusList) {
        return waAnalyseResultAdjustRepository.listByDateRange(empid, startDate, endDate, statusList);
    }

    public void updateById(WaAnalyseResultAdjustDo updateData) {
        waAnalyseResultAdjustRepository.updateById(updateData);
    }

    public void save(WaAnalyseResultAdjustDo addData) {
        waAnalyseResultAdjustRepository.insert(addData);
    }

    public void deleteById(Long id) {
        waAnalyseResultAdjustRepository.deleteById(id);
    }

    public void deleteByBatchId(Long batchId) {
        waAnalyseResultAdjustRepository.deleteByBatchId(batchId);
    }

    public void doInitCrtField() {
        UserInfo userInfo = UserContext.preCheckUser();
        this.setTenantId(userInfo.getTenantId());
        this.setDeleted(0);
        this.setCreateBy(userInfo.getUserId());
        this.setCreateTime(System.currentTimeMillis());
        this.setUpdateBy(this.getCreateBy());
        this.setUpdateTime(this.getCreateTime());
    }
}