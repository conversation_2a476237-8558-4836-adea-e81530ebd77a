package com.caidaocloud.attendance.service.application.enums;

public enum  LeaveJobTypeEnum {

    ANNUAL(1, "年假"),
    PARENTING(2, "育儿假");

    private Integer index;
    private String name;

    LeaveJobTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (LeaveJobTypeEnum c : LeaveJobTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
