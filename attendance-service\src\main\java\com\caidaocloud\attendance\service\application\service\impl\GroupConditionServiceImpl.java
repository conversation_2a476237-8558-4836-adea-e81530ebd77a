package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.attendance.service.application.service.IGroupConditionService;
import com.caidaocloud.hrpaas.paas.common.feign.IConditionFeign;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class GroupConditionServiceImpl implements IGroupConditionService {

    @Resource
    private IConditionFeign conditionFeign;
    private static final String LEAVE_QUOTA_GEN = "LEAVE_QUOTA_GEN";
    private static final String ATTENDANCE_PLAN = "ATTENDANCE_PLAN";
    private static final String LEAVE_QUOTA_GEN_RULE = "LEAVE_QUOTA_GEN_RULE";
    /**
     * 工时班组匹配条件
     */
    private static final String SHIFT_GROUP = "SHIFT_GROUP";
    /**
     * 排班消息提醒指定人员匹配条件
     */
    private static final String WA_SHIFT_NOTICE = "wa_shift_notice";

    /**
     * 排产消息提醒指定人员匹配条件
     */
    private static final String WA_SHIFT_NOTICE_PRODUCTION = "wa_shift_notice_production";

    /**
     * 员工日历匹配条件
     */
    private static final String ATTENDANCE_EMP_CALENDAR = "ATTENDANCE_EMP_CALENDAR";

    @Override
    public List<ConditionDataVo> quotaCondition() {
        return selectAndConvert(LEAVE_QUOTA_GEN);
    }

    @Override
    public List<ConditionDataVo> empGroupCondition() {
        return selectAndConvert(ATTENDANCE_PLAN);
    }

    @Override
    public List<ConditionDataVo> waShiftNoticeCondition() {
        return selectAndConvert(WA_SHIFT_NOTICE);
    }

    @Override
    public List<ConditionDataVo> waShiftNoticeProductionCondition() {
        return selectAndConvert(WA_SHIFT_NOTICE_PRODUCTION);
    }

    @Override
    public List<ConditionDataVo> empShiftGroupCondition() {
        return selectAndConvert(SHIFT_GROUP);
    }

    @Override
    public List<ConditionDataVo> quotaRuleCondition() {
        return selectAndConvert(LEAVE_QUOTA_GEN_RULE);
    }

    @Override
    public List<Map> getQuotaRuleCondition() {
        List<Map> mapList = Lists.newArrayList();
        List<ConditionDataVo> list = selectAndConvert(LEAVE_QUOTA_GEN_RULE);
        if (CollectionUtils.isEmpty(list)) {
            return mapList;
        }
        for (ConditionDataVo condition : list) {
            if ("shebaodi".equals(condition.getProperty())) {
                mapList.add(new HashMap() {{
                    put("text", "社保地");
                    put("value", "social_security_location");
                }});
            } else if ("siPlace".equals(condition.getProperty())) {
                // 远景：社保缴纳地-字典下拉（字典类型编码：siPlace）
                mapList.add(new HashMap() {{
                    put("text", "社保缴纳地");
                    put("value", "si_place");
                }});
            }
        }
        return mapList;
    }

    @Override
    public List<ConditionDataVo> empWorkCalendarCondition() {
        return selectAndConvert(ATTENDANCE_EMP_CALENDAR);
    }

    private List<ConditionDataVo> selectAndConvert(String code) {
        try {
            List<ConditionDataVo> list = conditionFeign.getConditionDataByCode(code, false).getData();
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newArrayList();
            }
            for (ConditionDataVo p : list) {
                p.setCode(p.getIdentifier() + "#" + p.getProperty());
            }
            return list;
        } catch (Exception e) {
            log.error("Get condition failed:{}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
