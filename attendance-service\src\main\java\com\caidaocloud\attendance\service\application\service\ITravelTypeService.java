package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTypeDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTypeListReqDto;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.web.Result;

import java.util.List;

/**
 * 出差规则相关接口服务
 *
 * <AUTHOR>
 * @Date 2021/9/8
 */
public interface ITravelTypeService {
    Boolean checkBeforeSaveOrUpdate(TravelTypeDto dto);

    int saveOrUpdate(TravelTypeDto dto);

    Result<Boolean> deleteTravelTypeById(Long travelTypeId);

    WaTravelTypeDo selectOneById(Long travelTypeId);

    PageResult<WaTravelTypeDo> getTravelTypePageResult(TravelTypeListReqDto dto);

    List<KeyValue> getTravelTypeList();
}
