package com.caidaocloud.attendance.core.wa.dto;

import java.util.ArrayList;
import java.util.List;

public class MobileEnum {

	public enum RegisterErrorType {
		TIME_ERR,LOCAL_ERR,DEVICE_ERR
		// 时间异常  地点异常  设备号异常
	}
	
	
	public static void main(String[] args) {
		List<MobileEnum.RegisterErrorType> logs = new ArrayList<MobileEnum.RegisterErrorType>();
		logs.add(MobileEnum.RegisterErrorType.DEVICE_ERR);
		logs.add(MobileEnum.RegisterErrorType.DEVICE_ERR);
		logs.add(MobileEnum.RegisterErrorType.DEVICE_ERR);
		System.out.println(logs.toString());
	}
}
