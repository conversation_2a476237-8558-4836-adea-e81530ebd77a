package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaGroupMapper;
import com.caidao1.wa.mybatis.model.WaGroup;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.service.WaLeaveService;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.service.application.dto.EmpOvertimeDto;
import com.caidaocloud.attendance.service.application.dto.EmpRegisterTimeDto;
import com.caidaocloud.attendance.service.application.enums.CompStdCalcMethodEnum;
import com.caidaocloud.attendance.service.application.enums.SalaryWorkHourEnum;
import com.caidaocloud.attendance.service.application.enums.WorkHourEnum;
import com.caidaocloud.attendance.service.application.service.ISobService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkOvertimeMapper;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.workHour.HourlyWorkingHourEmpDto;
import com.caidaocloud.attendance.service.interfaces.dto.workHour.WorkingHourSettlementDto;
import com.caidaocloud.attendance.service.interfaces.dto.workHour.WorkingHourSettlementPageDto;
import com.caidaocloud.attendance.service.interfaces.vo.workHour.EmpWorkingHoursSettlementVo;
import com.caidaocloud.attendance.service.schedule.application.service.dto.EmpWorkInfo;
import com.caidaocloud.attendance.service.schedule.application.service.schedule.IEmpScheduleService;
import com.caidaocloud.attendance.service.wfm.application.dto.EmpMultiShiftInfoDto;
import com.caidaocloud.attendance.service.wfm.application.dto.WfmCalendarDateDto;
import com.caidaocloud.attendance.service.wfm.application.service.WfmHolidayService;
import com.caidaocloud.attendance.service.wfm.domain.entity.WorkingHourAnalyzeDo;
import com.caidaocloud.em.DeleteStatusEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.util.*;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.googlecode.totallylazy.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 考勤统计-工时结算服务
 */
@Slf4j
@Service
public class EmpWorkingHoursSettlementService {

    @Autowired
    private WaEmpWorkingHoursSettlementDo waEmpWorkingHoursSettlementDo;
    @Autowired
    private WaGroupWorkingHourRuleService waGroupWorkingHourRuleService;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WaAnalyzeDo waAnalyzeDo;
    @Autowired
    private WaEmpOvertimeDo waEmpOvertimeDo;

    @Autowired
    private WaLeaveService waLeaveService;
    @Autowired
    private WorkOvertimeMapper workOvertimeMapper;

    private final static String EMP_WORK_INFO_IDENTIFIER = "entity.hr.EmpWorkInfo";
    private static final String STANDARD_WORK_HOUR_CODE = "0";
    private static final String COMPREHENSIVE_WORK_HOUR_CODE = "1";
    private static final String PIECEWORK_CODE = "1";
    private static final String NON_PIECEWORK_CODE = "0";
    // 夜班开始时间（20:00，单位分钟）
    private static final int NIGHT_SHIFT_START = 20 * 60;
    // 夜班结束时间（次日08:00，单位分钟）
    private static final int NIGHT_SHIFT_END = 8 * 60 + 24 * 60; // 次日08:00
    private static final ZoneId DEFAULT_ZONE = ZoneId.systemDefault();
    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    private static final int DEFAULT_SPLIT_PAGE_SIZE = 1000;

    private static <T> T repository(Class<T> clazz) {
        return SpringUtil.getBean(clazz);
    }

    public void workHourSettlement(WorkingHourSettlementDto dto, SecurityUserInfo userInfo) {
        // 1. 参数校验与基础数据准备
        SettlementContext context = prepareSettlementContext(dto, userInfo);
        // 2. 执行工时计算
        List<WaEmpWorkingHoursSettlementDo> settlementResults = calculateWorkingHours(context, userInfo);
        // 3. 保存结算结果（带事务）
        saveSettlementResults(context.getTenantId(), context.getAttendanceMonth(), context.getWaGroupId(), settlementResults);
    }

    /**
     * 准备结算上下文
     */
    private SettlementContext prepareSettlementContext(WorkingHourSettlementDto dto, SecurityUserInfo userInfo) {
        String tenantId = userInfo.getTenantId();
        Integer attendanceMonth = dto.getAttendanceMonth();
        Integer waGroupId = dto.getWaGroupId();
        // 获取工时规则
        WaGroupWorkingHourRuleDo rule = waGroupWorkingHourRuleService.getByWaGroupId(tenantId, waGroupId);
        if (rule == null) {
            log.info("未设置工时规则，waGroupId={}", waGroupId);
            throw new CDException("未设置工时规则");
        }
        // 获取考勤周期
        Pair<Long, Long> timePair = getAttendancePeriod(tenantId, attendanceMonth, waGroupId, rule.getCompStatPeriod());
        if (timePair == null) {
            log.info("未查到考勤周期，tenantId={}, attendanceMonth={}, waGroupId={}", tenantId, attendanceMonth, waGroupId);
            throw new CDException("未查到考勤周期");
        }
        // 获取工作组信息
        WaGroup waGroup = getWaGroup(waGroupId);
        // 获取员工ID列表
        List<Long> empIds = getEmpGroupEmpIds(tenantId, waGroup, timePair);
        if (CollectionUtils.isEmpty(empIds)) {
            log.info("未查到需要计算的员工，tenantId={}, waGroupId={}", tenantId, waGroupId);
            throw new CDException("未查到需要计算的员工");
        }
        // 获取员工详细信息
        List<EmpWorkInfo> empWorkInfos = getTenantEmpInfos(tenantId, empIds);
        if (CollectionUtils.isEmpty(empWorkInfos)) {
            log.info("未查到需要计算的员工详细信息，tenantId={}, empIds={}", tenantId, empIds);
            throw new CDException("未查到需要计算的员工详细信息");
        }
        // 按员工类型分类
        List<HourlyWorkingHourEmpDto> pieceworkEmpWorkInfos = getPieceworkEmpWorkInfos(empWorkInfos);
        List<HourlyWorkingHourEmpDto> nonPieceworkEmpWorkInfos = getNonPieceworkEmpWorkInfos(empWorkInfos);
        // 构建上下文
        return new SettlementContext(tenantId, attendanceMonth, waGroupId, rule, timePair, waGroup, empIds, empWorkInfos, pieceworkEmpWorkInfos, nonPieceworkEmpWorkInfos);
    }

    /**
     * 执行工时计算
     */
    private List<WaEmpWorkingHoursSettlementDo> calculateWorkingHours(SettlementContext context, SecurityUserInfo userInfo) {
        List<WaEmpWorkingHoursSettlementDo> results = new ArrayList<>();
        // 计件员工计算
        if (CollectionUtils.isNotEmpty(context.getPieceworkEmpWorkInfos())) {
            results.addAll(calculatePieceWorkWorkHourStatistics(context.getTenantId(), context.getAttendanceMonth(), context.getTimePair(), context.getRule(), context.getPieceworkEmpWorkInfos(), userInfo));
        }
        // 非计件员工计算（完善实现）
        if (CollectionUtils.isNotEmpty(context.getNonPieceworkEmpWorkInfos())) {
            results.addAll(calculateNonPieceWorkWorkHourStatistics(userInfo, context.getAttendanceMonth(), context.getTimePair(), context.getWaGroup(), context.getRule(), context.getNonPieceworkEmpWorkInfos()));
        }
        return results;
    }

    /**
     * 保存结算结果（带事务）
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSettlementResults(String tenantId, Integer attendanceMonth, Integer waGroupId, List<WaEmpWorkingHoursSettlementDo> results) {
        if (CollectionUtils.isEmpty(results)) {
            log.info("没有需要保存的工时结算结果");
            return;
        }
        // 获取需要处理的员工ID列表
        List<Long> empIdList = results.stream().map(WaEmpWorkingHoursSettlementDo::getEmpId).distinct().collect(Collectors.toList());
        // 删除老数据
        deleteExistedSettlement(tenantId, attendanceMonth, waGroupId, empIdList);
        // 插入新数据
        int saveNum = waEmpWorkingHoursSettlementDo.saveEmpWorkingHourSettlementList(results);
        log.info("共计保存工时结算结果{}条", saveNum);
    }

    /**
     * 非计件员工工时计算（完善实现）
     */
    private List<WaEmpWorkingHoursSettlementDo> calculateNonPieceWorkWorkHourStatistics(SecurityUserInfo userInfo, Integer attendanceMonth, Pair<Long, Long> timePair, WaGroup waGroup, WaGroupWorkingHourRuleDo rule, List<HourlyWorkingHourEmpDto> empWorkInfos) {
        // 实现非计件员工的工时计算逻辑
        val result = calcHourlyWorkingHours(userInfo, String.valueOf(attendanceMonth), timePair.getKey(), timePair.getValue(), waGroup, rule, empWorkInfos);
        return result;
    }

    /**
     * 工时结算上下文，封装结算过程中需要的所有数据
     */
    private static class SettlementContext {
        private final String tenantId;
        private final Integer attendanceMonth;
        private final Integer waGroupId;
        private final WaGroupWorkingHourRuleDo rule;
        private final Pair<Long, Long> timePair;
        private final WaGroup waGroup;
        private final List<Long> empIds;
        private final List<EmpWorkInfo> empWorkInfos;
        private final List<HourlyWorkingHourEmpDto> pieceworkEmpWorkInfos;
        private final List<HourlyWorkingHourEmpDto> nonPieceworkEmpWorkInfos;

        // 全参构造函数
        public SettlementContext(String tenantId, Integer attendanceMonth, Integer waGroupId, WaGroupWorkingHourRuleDo rule, Pair<Long, Long> timePair,
                                 WaGroup waGroup, List<Long> empIds, List<EmpWorkInfo> empWorkInfos, List<HourlyWorkingHourEmpDto> pieceworkEmpWorkInfos,
                                 List<HourlyWorkingHourEmpDto> nonPieceworkEmpWorkInfos) {
            // 必要参数非空检查
            this.tenantId = Objects.requireNonNull(tenantId, "tenantId不能为空");
            this.attendanceMonth = Objects.requireNonNull(attendanceMonth, "attendanceMonth不能为空");
            this.waGroupId = Objects.requireNonNull(waGroupId, "waGroupId不能为空");
            this.rule = Objects.requireNonNull(rule, "rule不能为空");
            this.timePair = Objects.requireNonNull(timePair, "timePair不能为空");
            this.waGroup = Objects.requireNonNull(waGroup, "waGroup不能为空");
            // 集合参数空安全处理
            this.empIds = empIds != null ? Collections.unmodifiableList(empIds) : Collections.emptyList();
            this.empWorkInfos = empWorkInfos != null ? Collections.unmodifiableList(empWorkInfos) : Collections.emptyList();
            this.pieceworkEmpWorkInfos = pieceworkEmpWorkInfos != null ? Collections.unmodifiableList(pieceworkEmpWorkInfos) : Collections.emptyList();
            this.nonPieceworkEmpWorkInfos = nonPieceworkEmpWorkInfos != null ? Collections.unmodifiableList(nonPieceworkEmpWorkInfos) : Collections.emptyList();
        }

        public String getTenantId() {
            return tenantId;
        }

        public Integer getAttendanceMonth() {
            return attendanceMonth;
        }

        public Integer getWaGroupId() {
            return waGroupId;
        }

        public WaGroupWorkingHourRuleDo getRule() {
            return rule;
        }

        public Pair<Long, Long> getTimePair() {
            return timePair;
        }

        public WaGroup getWaGroup() {
            return waGroup;
        }

        public List<Long> getEmpIds() {
            return empIds;
        }

        public List<EmpWorkInfo> getEmpWorkInfos() {
            return empWorkInfos;
        }

        public List<HourlyWorkingHourEmpDto> getPieceworkEmpWorkInfos() {
            return pieceworkEmpWorkInfos;
        }

        public List<HourlyWorkingHourEmpDto> getNonPieceworkEmpWorkInfos() {
            return nonPieceworkEmpWorkInfos;
        }
    }

    /**
     * 删除老数据
     *
     * @param tenantId        租户
     * @param attendanceMonth 周期
     * @param waGroupId       考勤方案
     * @param empIds          员工
     */
    public void deleteExistedSettlement(String tenantId, Integer attendanceMonth, Integer waGroupId, List<Long> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return;
        }
        int batchSize = 0;
        List<List<Long>> empIdList = ListTool.split(empIds, DEFAULT_SPLIT_PAGE_SIZE);
        for (List<Long> rows : empIdList) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            batchSize++;
            int num = waEmpWorkingHoursSettlementDo.deleteEmpWorkingHourSettlementList(tenantId, attendanceMonth, waGroupId, rows);
            log.info("删除总批次：{}，本次第{}批次，删除条数：{}", empIdList.size(), batchSize, num);
        }
    }

    /**
     * 计件员工工时结算
     *
     * @param tenantId        租户
     * @param attendanceMonth 周期
     * @param timePair        周期范围
     * @param rule            工时规则
     * @param empDtoList      员工
     * @return 工时结算结果
     */
    private List<WaEmpWorkingHoursSettlementDo> calculatePieceWorkWorkHourStatistics(String tenantId, Integer attendanceMonth, Pair<Long, Long> timePair, WaGroupWorkingHourRuleDo rule,
                                                                                     List<HourlyWorkingHourEmpDto> empDtoList, SecurityUserInfo userInfo) {
        if (CollectionUtils.isEmpty(empDtoList)) {
            return Collections.emptyList();
        }
        // 提前计算常量值
        long currentTimeSec = System.currentTimeMillis() / 1000;
        Long createUserId = userInfo.getUserId();
        Integer compStdCalcMethod = rule.getCompStdCalcMethod();
        BigDecimal compStdBase = Optional.ofNullable(rule.getCompStdWorkingHours()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(60)); // 预计算固定值乘数
        // 批量获取数据
        List<Long> empIds = empDtoList.stream().map(HourlyWorkingHourEmpDto::getEmpId).map(Long::parseLong).distinct().collect(Collectors.toList());
        Map<Long, HourlyWorkingHourEmpDto> empDtoMap = empDtoList.stream().collect(Collectors.toMap(emp -> Long.parseLong(emp.getEmpId()), Function.identity(), (existing, replacement) -> existing));
        Map<Long, EmployeeShiftStats> shiftStatsMap = getCalculateResult(tenantId, timePair, empIds, rule, empDtoMap);
        Map<Long, CompensatoryPoolDo> compensatoryPoolMap = getCompensatoryPoolResult(tenantId, attendanceMonth, timePair, empIds);
        // 处理每个员工的数据
        return empDtoList.stream().map(empDto -> {
            Long empId = Long.parseLong(empDto.getEmpId());
            WaEmpWorkingHoursSettlementDo settlement = ObjectConverter.convert(empDto, WaEmpWorkingHoursSettlementDo.class);
            // 基础属性设置（通用部分）
            settlement.setSettlementId(snowflakeUtil.createId());
            settlement.setAttendanceMonth(attendanceMonth);
            settlement.setTenantId(tenantId);
            settlement.setPeriodStartDate(timePair.getLeft());
            settlement.setPeriodEndDate(timePair.getRight());
            settlement.setWaGroupId(rule.getWaGroupId());
            settlement.setEmpId(empId);
            settlement.setDeleted(DeleteStatusEnum.UN_DELETED.getIndex());
            settlement.setCreateBy(createUserId);
            settlement.setCreateTime(currentTimeSec);
            settlement.setUpdateBy(createUserId);
            settlement.setUpdateTime(currentTimeSec);
            // 1. 工时统计数据
            EmployeeShiftStats shiftStats = shiftStatsMap.get(empId);
            if (shiftStats != null) {
                settlement.setStdWorkTime(shiftStats.getWorkingHours()); // 初始用计算的标准工时
                settlement.setActualWorkTime(shiftStats.getActualWorkingHours());
                settlement.setMiddleShiftDays(BigDecimal.ZERO);
                settlement.setNightShiftDays(shiftStats.getNightShiftDays());
            }
            // 2. 综合工时制-固定值逻辑（按员工个体的工时类型判断）
            if (HourlyWorkingHourEmpDto.WorkingHourType.COMPREHENSIVE.equals(empDto.getWorkingHourType())
                    && compStdCalcMethod != null && compStdCalcMethod == 1) {
                // 覆盖标准工时为固定值
                settlement.setStdWorkTime(compStdBase);
            }
            // 3. 调休池数据处理
            CompensatoryPoolDo pool = compensatoryPoolMap.get(empId);
            if (pool != null) {
                settlement.setCurMonthSalarySettlement(Optional.ofNullable(pool.getSettlement()).orElse(BigDecimal.ZERO));
                settlement.setCurPeriodCompensatory(Optional.ofNullable(pool.getAmount()).orElse(BigDecimal.ZERO));
                settlement.setLastPeriodCompensatory(Optional.ofNullable(pool.getLastAmount()).orElse(BigDecimal.ZERO));
                settlement.setCurPeriodCompensatorySum(Optional.ofNullable(pool.getCompensatoryTotal()).orElse(BigDecimal.ZERO));
            } else {
                // 无调休数据时显式设为0（避免null）
                settlement.setCurPeriodCompensatorySum(BigDecimal.ZERO);
                settlement.setLastPeriodCompensatory(BigDecimal.ZERO);
                settlement.setCurPeriodCompensatory(BigDecimal.ZERO);
                settlement.setCurMonthSalarySettlement(BigDecimal.ZERO);
            }
            return settlement;
        }).collect(Collectors.toList());
    }

    /**
     * 调休池计算
     *
     * @param tenantId        租户
     * @param attendanceMonth 周期
     * @param timePair        时间范围
     * @param empIds          员工
     * @return 调休池结果Map
     */
    private Map<Long, CompensatoryPoolDo> getCompensatoryPoolResult(String tenantId, Integer attendanceMonth, Pair<Long, Long> timePair, List<Long> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Collections.emptyMap();
        }
        List<CompensatoryPoolDo> compensatoryPoolDoList = CompensatoryPoolDo.generate(tenantId, String.valueOf(attendanceMonth),
                empIds.stream().map(String::valueOf).collect(Collectors.toList()), timePair.getLeft(), timePair.getRight());
        if (CollectionUtils.isEmpty(compensatoryPoolDoList)) {
            return Collections.emptyMap();
        }
        return compensatoryPoolDoList.stream().collect(Collectors.toMap(compensatoryPool -> Long.parseLong(compensatoryPool.getEmpId()), Function.identity(), (existing, replacement) -> existing));
    }

    /**
     * 统计工时，班次数据
     *
     * @param tenantId 租户
     * @param timePair 周期
     * @param empIds   员工
     * @return Map<Long, EmployeeShiftStats>
     */
    private Map<Long, EmployeeShiftStats> getCalculateResult(String tenantId, Pair<Long, Long> timePair, List<Long> empIds, WaGroupWorkingHourRuleDo rule, Map<Long, HourlyWorkingHourEmpDto> empDtoMap) {
        List<EmpMultiShiftInfoDto> shiftList = getEmpShift(tenantId, timePair, empIds);
        // 工时分析数据
        List<WorkingHourAnalyzeDo> hourList = getWorkingHourList(tenantId, timePair, empIds);
        // 计算统计结果
        return calculatePieceWorkEmpShiftStatistics(tenantId, shiftList, hourList, rule, empDtoMap, timePair);
    }

    /**
     * 统计每个员工的工作日小时数和夜班天数(计件)
     *
     * @param shiftList       班次信息列表
     * @param workingHourList 工时分析列表（用于获取实际工时）
     * @return 统计结果映射（员工ID -> 统计结果）
     */
    private Map<Long, EmployeeShiftStats> calculatePieceWorkEmpShiftStatistics(String tenantId, List<EmpMultiShiftInfoDto> shiftList, List<WorkingHourAnalyzeDo> workingHourList,
                                                                               WaGroupWorkingHourRuleDo rule, Map<Long, HourlyWorkingHourEmpDto> empDtoMap,
                                                                               Pair<Long, Long> timePair) {
        Map<Long, Integer> specialDateMap = getSpecialDate(tenantId, timePair);
        // 按员工ID和日期过滤并汇总实际工时
        Map<Long, List<WorkingHourAnalyzeDo>> employeeDailyHours = workingHourList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(WorkingHourAnalyzeDo::getEmpId));
        // 按员工ID分组统计
        return shiftList.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(EmpMultiShiftInfoDto::getEmpId,
                        Collectors.collectingAndThen(Collectors.toList(), shifts -> calculateStatsForPieceworkEmployee(shifts, rule, empDtoMap, timePair, specialDateMap, employeeDailyHours))
                ));
    }

    /**
     * 计算单个员工的统计数据
     */
    private EmployeeShiftStats calculateStatsForPieceworkEmployee(List<EmpMultiShiftInfoDto> empShifts, WaGroupWorkingHourRuleDo rule,
                                                                  Map<Long, HourlyWorkingHourEmpDto> empDtoMap, Pair<Long, Long> timePair,
                                                                  Map<Long, Integer> specialDateMap, Map<Long, List<WorkingHourAnalyzeDo>> employeeDailyHours) {
        EmployeeShiftStats stats = new EmployeeShiftStats();
        Long empId = empShifts.get(0).getEmpId();
        // 筛选符合条件的班次和工时记录（根据统计区间过滤）
        FilterResult filterResult = filterShiftsAndHours(empId, empShifts, empDtoMap, timePair, employeeDailyHours);
        List<EmpMultiShiftInfoDto> shifts = filterResult.getShifts();
        List<WorkingHourAnalyzeDo> empDailyHours = filterResult.getEmpDailyHours();
        // 汇总实际工时（单位：分钟）(不受配置影响，无需根据选择日期类型筛选)
        BigDecimal totalActualWorkTime = sumActualWorkingTime(empDailyHours);
        // 按日期分组工时记录
        Map<String, List<WorkingHourAnalyzeDo>> empDailyHoursMap = groupByEmpIdAndDate(empDailyHours);
        // 统计标准工时和夜班天数
        StatsResult statsResult;
        HourlyWorkingHourEmpDto empDto = empDtoMap.get(empId);
        if (HourlyWorkingHourEmpDto.WorkingHourType.COMPREHENSIVE.equals(empDto.getWorkingHourType())) {
            List<Integer> compStdDateTypes = getCompStdDateTypes(rule.getCompStdDateType());
            // 综合工时
            statsResult = calculateWorkingHoursAndNightShiftDaysComprehensive(shifts, compStdDateTypes, specialDateMap, empDailyHoursMap);
        } else {
            // 标准工时
            statsResult = calculateWorkingHoursAndNightShifts(shifts, specialDateMap, empDailyHoursMap);
        }
        // 设置统计结果
        stats.setWorkingHours(statsResult.getWorkingHours());
        stats.setNightShiftDays(statsResult.getNightShiftCount());
        stats.setActualWorkingHours(totalActualWorkTime);
        return stats;
    }

    /**
     * 排班，工时筛选结果内部类
     */
    private static class FilterResult {
        private final List<EmpMultiShiftInfoDto> shifts;
        private final List<WorkingHourAnalyzeDo> empDailyHours;

        public FilterResult(List<EmpMultiShiftInfoDto> shifts, List<WorkingHourAnalyzeDo> empDailyHours) {
            this.shifts = shifts;
            this.empDailyHours = empDailyHours;
        }

        public List<EmpMultiShiftInfoDto> getShifts() {
            return shifts;
        }

        public List<WorkingHourAnalyzeDo> getEmpDailyHours() {
            return empDailyHours;
        }
    }

    /**
     * 标准工时，实际工时，夜班天数计算结果内部类
     */
    private static class StatsResult {
        private final BigDecimal workingHours;
        private final BigDecimal nightShiftCount;

        public StatsResult(BigDecimal workingHours, BigDecimal nightShiftCount) {
            this.workingHours = workingHours;
            this.nightShiftCount = nightShiftCount;
        }

        public BigDecimal getWorkingHours() {
            return workingHours;
        }

        public BigDecimal getNightShiftCount() {
            return nightShiftCount;
        }
    }

    /**
     * 筛选符合条件的班次和工时记录
     *
     * @param empId              员工
     * @param empShifts          排班
     * @param empDtoMap          员工信息
     * @param timePair           周期范围
     * @param employeeDailyHours 工时
     * @return 映射结果内部类FilterResult
     */
    private FilterResult filterShiftsAndHours(Long empId, List<EmpMultiShiftInfoDto> empShifts, Map<Long, HourlyWorkingHourEmpDto> empDtoMap,
                                              Pair<Long, Long> timePair, Map<Long, List<WorkingHourAnalyzeDo>> employeeDailyHours) {
        List<EmpMultiShiftInfoDto> shifts = new ArrayList<>(empShifts);
        List<WorkingHourAnalyzeDo> empDailyHours = employeeDailyHours.getOrDefault(empId, Collections.emptyList());
        // 获取员工信息
        HourlyWorkingHourEmpDto empDto = empDtoMap.get(empId);
        if (empDto == null) {
            return new FilterResult(Collections.emptyList(), Collections.emptyList());
        }
        // 工时制为非综合工时的不需要根据统计区间过滤
        if (!HourlyWorkingHourEmpDto.WorkingHourType.COMPREHENSIVE.equals(empDto.getWorkingHourType())) {
            return new FilterResult(shifts, empDailyHours);
        }
        // 处理入职日期
        Long hireDate = empDto.getHireDate();
        if (hireDate != null && isHiredOrLeaveDuringPeriod(hireDate, timePair.getLeft(), timePair.getRight() + 86399)) {
            long hireDateSeconds = hireDate / 1000;
            shifts = shifts.stream().filter(shift -> shift.getWorkDate() >= hireDateSeconds).collect(Collectors.toList());
            empDailyHours = empDailyHours.stream().filter(analyze -> analyze.getBelongDate() >= hireDateSeconds).collect(Collectors.toList());
        }
        // 处理离职日期
        Long leaveDate = empDto.getLeaveDate();
        if (leaveDate != null && isHiredOrLeaveDuringPeriod(leaveDate, timePair.getLeft(), timePair.getRight() + 86399)) {
            long leaveDateSeconds = leaveDate / 1000;
            shifts = shifts.stream().filter(shift -> shift.getWorkDate() <= leaveDateSeconds).collect(Collectors.toList());
            empDailyHours = empDailyHours.stream().filter(analyze -> analyze.getBelongDate() <= leaveDateSeconds).collect(Collectors.toList());
        }
        return new FilterResult(shifts, empDailyHours);
    }

    /**
     * 汇总实际工时（工时制：标准工时）
     *
     * @param empDailyHours 工时
     * @return BigDecimal
     */
    private BigDecimal sumActualWorkingTime(List<WorkingHourAnalyzeDo> empDailyHours) {
        return empDailyHours.stream().map(WorkingHourAnalyzeDo::getActualWorkTime).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 汇总实际工时（工时制：综合工时）
     *
     * @param empDailyHours    工时
     * @param compStdDateTypes 日期类型
     * @param specialDateMap   特殊日期
     * @return BigDecimal COMPREHENSIVE
     */
    private BigDecimal sumActualWorkingTimeComprehensive(List<WorkingHourAnalyzeDo> empDailyHours, List<Integer> compStdDateTypes, Map<Long, Integer> specialDateMap) {
        return empDailyHours.stream().filter(row -> checkMatchDateType(row.getBelongDate(), compStdDateTypes, specialDateMap))
                .map(WorkingHourAnalyzeDo::getActualWorkTime).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算标准工时和夜班次数（标准工时）
     *
     * @param shifts           排班
     * @param specialDateMap   特殊日期
     * @param empDailyHoursMap 工时
     * @return 内部类StatsResult
     */
    private StatsResult calculateWorkingHoursAndNightShifts(List<EmpMultiShiftInfoDto> shifts, Map<Long, Integer> specialDateMap, Map<String, List<WorkingHourAnalyzeDo>> empDailyHoursMap) {
        BigDecimal workingHours = BigDecimal.ZERO;
        BigDecimal nightShiftCount = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(shifts)) {
            return new StatsResult(workingHours, nightShiftCount);
        }
        Map<Long, List<EmpMultiShiftInfoDto>> shiftDateMap = shifts.stream().collect(Collectors.groupingBy(EmpMultiShiftInfoDto::getWorkDate));
        for (Map.Entry<Long, List<EmpMultiShiftInfoDto>> entry : shiftDateMap.entrySet()) {
            Long workDate = entry.getKey();
            if (getDateType(workDate, specialDateMap) == 1) {// 标准工时，只统计工作日
                // 统计标准工时
                workingHours = workingHours.add(BigDecimal.valueOf(480));
            }
            List<EmpMultiShiftInfoDto> perDayShifts = entry.getValue();
            for (EmpMultiShiftInfoDto shift : perDayShifts) {
                // 计算夜班天数
                nightShiftCount = nightShiftCount.add(calculateNightShiftFactor(shift, empDailyHoursMap));
            }
        }
        return new StatsResult(workingHours, nightShiftCount);
    }

    /**
     * 计算标准工时和夜班次数（综合工时）
     *
     * @param shifts           排班
     * @param compStdDateTypes 日期类型
     * @param specialDateMap   特殊日期
     * @param empDailyHoursMap 工时
     * @return 内部类StatsResult
     */
    private StatsResult calculateWorkingHoursAndNightShiftDaysComprehensive(List<EmpMultiShiftInfoDto> shifts, List<Integer> compStdDateTypes, Map<Long, Integer> specialDateMap, Map<String, List<WorkingHourAnalyzeDo>> empDailyHoursMap) {
        BigDecimal workingHours = BigDecimal.ZERO;
        BigDecimal nightShiftCount = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(shifts)) {
            return new StatsResult(workingHours, nightShiftCount);
        }
        Map<Long, List<EmpMultiShiftInfoDto>> shiftDateMap = shifts.stream().collect(Collectors.groupingBy(EmpMultiShiftInfoDto::getWorkDate));
        for (Map.Entry<Long, List<EmpMultiShiftInfoDto>> entry : shiftDateMap.entrySet()) {
            for (EmpMultiShiftInfoDto shift : entry.getValue()) {
                if (checkMatchDateType(entry.getKey(), compStdDateTypes, specialDateMap)) {
                    // 统计标准工时
                    workingHours = workingHours.add(BigDecimal.valueOf(Optional.ofNullable(shift.getWorkTotalTime()).orElse(0)));
                }
                // 计算夜班天数
                nightShiftCount = nightShiftCount.add(calculateNightShiftFactor(shift, empDailyHoursMap));
            }
        }
        return new StatsResult(workingHours, nightShiftCount);
    }

    /**
     * 将员工每日工时列表转换为按员工ID和日期分组的映射
     *
     * @param empDailyHours 员工每日工时列表
     * @return 映射（键格式："empId_belongDate_shiftId"）
     */
    public Map<String, List<WorkingHourAnalyzeDo>> groupByEmpIdAndDate(List<WorkingHourAnalyzeDo> empDailyHours) {
        if (empDailyHours == null || empDailyHours.isEmpty()) {
            return Collections.emptyMap();
        }
        return empDailyHours.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(hour -> String.format("%s_%s_%s", hour.getEmpId(), hour.getBelongDate(), hour.getShiftId())));
    }

    /**
     * 校验日期类型是否需要统计工时
     *
     * @param workDate         归属日期
     * @param compStdDateTypes 日期类型
     * @param specialDateMap   特殊日期
     * @return 布尔值
     */
    private boolean checkMatchDateType(Long workDate, List<Integer> compStdDateTypes, Map<Long, Integer> specialDateMap) {
        if (null == workDate || CollectionUtils.isEmpty(compStdDateTypes)) {
            return false;
        }
        return compStdDateTypes.contains(getDateType(workDate, specialDateMap));
    }

    /**
     * 解析工时规则勾选工时结算日期类型
     *
     * @param compStdDateType 日期类型，多个逗号分隔
     * @return Integer类型List数组
     */
    private List<Integer> getCompStdDateTypes(String compStdDateType) {
        List<Integer> compStdDateTypes = Lists.newArrayList();
        if (null != compStdDateType && StringUtil.isNotBlank(compStdDateType.trim())) {
            compStdDateTypes = Arrays.stream(compStdDateType.trim().split(",")).distinct().map(Integer::valueOf).collect(Collectors.toList());
        }
        return compStdDateTypes;
    }

    /**
     * 日期类型
     *
     * @param workDate       归属日期
     * @param specialDateMap 特殊日期
     * @return 返回日期类型：1、工作日 2、休息日 3、法定假日
     */
    private Integer getDateType(Long workDate, Map<Long, Integer> specialDateMap) {
        if (Optional.ofNullable(getSpecialDateType(workDate, specialDateMap)).isPresent()) {
            return getSpecialDateType(workDate, specialDateMap);
        } else if (isWeekend(workDate)) {
            return DateTypeEnum.DATE_TYP_2.getIndex();
        } else {
            return DateTypeEnum.DATE_TYP_1.getIndex();
        }
    }

    /**
     * 特殊日期
     *
     * @param workDate       归属日期
     * @param specialDateMap 特殊日期
     */
    private Integer getSpecialDateType(Long workDate, Map<Long, Integer> specialDateMap) {
        if (null == specialDateMap || specialDateMap.isEmpty()) {
            return null;
        }
        return Optional.ofNullable(specialDateMap.get(workDate)).orElse(null);
    }

    /**
     * 周末判断（休息日）
     *
     * @param timestamp 时间戳（秒级）
     */
    private boolean isWeekend(long timestamp) {
        // 将秒级时间戳转换为 LocalDate
        LocalDate date = Instant.ofEpochSecond(timestamp).atZone(DEFAULT_ZONE).toLocalDate();
        // 获取星期几
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        // 判断是否为周六或周日
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;
    }

    private Map<Long, Integer> getSpecialDate(String tenantId, Pair<Long, Long> timePair) {
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            // 查询特殊日期
            List<WfmCalendarDateDto> specialDate = repository(WfmHolidayService.class).getCalendarDateList(tenantId, timePair.getLeft(), timePair.getRight() + 86400);
            if (CollectionUtils.isEmpty(specialDate)) {
                return Collections.emptyMap();
            }
            return specialDate.stream().collect(Collectors.toMap(WfmCalendarDateDto::getCalendarDate, WfmCalendarDateDto::getDateType));
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    /**
     * 判断入职日期是否在指定周期内
     *
     * @param actualDateMillis 入职日期（毫秒级时间戳）
     * @param startDate        周期开始时间（秒级时间戳）
     * @param endDate          周期结束时间（秒级时间戳）
     * @return 如果入职日期在周期内返回 true，否则返回 false
     */
    public boolean isHiredOrLeaveDuringPeriod(Long actualDateMillis, Long startDate, Long endDate) {
        // 将毫秒级时间戳转换为秒级时间戳
        long actualDateSeconds = actualDateMillis / 1000;

        // 判断是否在周期内（包含边界）
        return actualDateSeconds >= startDate && actualDateSeconds <= endDate;
    }

    /**
     * 计算夜班系数（0/0.5/1）
     */
    private BigDecimal calculateNightShiftFactor(EmpMultiShiftInfoDto shift, Map<String, List<WorkingHourAnalyzeDo>> empDailyHoursMap) {
        // 检查班次时间是否与夜班时间（20:00~08:00）有交集
        boolean isNightShift = checkShiftOverlapsWithNight(shift);
        if (!isNightShift) {
            return BigDecimal.ZERO;
        }
        // 获取班次时长（单位：分钟）
        String groupKey = String.format("%s_%s_%s", shift.getEmpId(), shift.getWorkDate(), shift.getShiftDefId());
        List<WorkingHourAnalyzeDo> workHours = Optional.ofNullable(empDailyHoursMap.get(groupKey)).orElse(Collections.emptyList());
        BigDecimal actualWorkTime = getMaxActualWorkTime(workHours);
        if (actualWorkTime.compareTo(BigDecimal.valueOf(240)) >= 0 && actualWorkTime.compareTo(BigDecimal.valueOf(480)) < 0) {
            return new BigDecimal("0.5");
        } else if (actualWorkTime.compareTo(BigDecimal.valueOf(480)) >= 0) {
            return new BigDecimal("1.0");
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取列表中最大的实际工时值（actualWorkTime）
     *
     * @param workingHours 工时分析列表
     * @return 最大的实际工时值（Optional<BigDecimal>，避免空指针）
     */
    private BigDecimal getMaxActualWorkTime(List<WorkingHourAnalyzeDo> workingHours) {
        if (workingHours == null || workingHours.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return workingHours.stream().map(WorkingHourAnalyzeDo::getActualWorkTime).filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
    }

    /**
     * 检查班次是否与夜班时间（20:00~08:00）有交集
     */
    private boolean checkShiftOverlapsWithNight(EmpMultiShiftInfoDto shift) {
        // 获取班次的开始和结束时间（考虑跨天情况）
        int startTime = shift.getStartTime();
        int endTime = shift.getEndTime();
        // 处理跨天班次
        if (shift.getEndTimeBelong() != null && shift.getEndTimeBelong() == 2) {
            endTime += 24 * 60; // 如果下班时间归属次日，加上24小时
        }
        // 计算与夜班时间的交集
        int overlapStart = Math.max(startTime, NIGHT_SHIFT_START);
        int overlapEnd = Math.min(endTime, NIGHT_SHIFT_END);
        return overlapEnd > overlapStart;
    }

    /**
     * 查询工时统计
     *
     * @param tenantId 租户
     * @param timePair 周期
     * @param empIds   员工
     * @return 工时
     */
    private List<WorkingHourAnalyzeDo> getWorkingHourList(String tenantId, Pair<Long, Long> timePair, List<Long> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Collections.emptyList();
        }
        long startDate = timePair.getLeft();
        long endDate = timePair.getRight();
        List<WorkingHourAnalyzeDo> workingHourList = new ArrayList<>(empIds.size());
        //查询员工排班
        List<List<Long>> empIdList = ListTool.split(empIds, DEFAULT_SPLIT_PAGE_SIZE);
        for (List<Long> rows : empIdList) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            String anyEmpIds = "'{" + StringUtils.join(rows, ",") + "}'";
            workingHourList.addAll(repository(WorkingHourAnalyzeDo.class)
                    .getDayAnalyseListByRange(tenantId, startDate, endDate, anyEmpIds, null, null, null, null));
        }
        return workingHourList;
    }

    /**
     * 查员工排班
     *
     * @param tenantId 租户
     * @param timePair 周期
     * @param empIds   员工
     * @return 排班
     */
    private List<EmpMultiShiftInfoDto> getEmpShift(String tenantId, Pair<Long, Long> timePair, List<Long> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Collections.emptyList();
        }
        long startDate = timePair.getLeft();
        long endDate = timePair.getRight();
        List<EmpMultiShiftInfoDto> allEmpShifts = new ArrayList<>(empIds.size());
        //查询员工排班
        List<List<Long>> empIdList = ListTool.split(empIds, DEFAULT_SPLIT_PAGE_SIZE);
        for (List<Long> rows : empIdList) {
            if (CollectionUtils.isEmpty(rows)) {
                continue;
            }
            allEmpShifts.addAll(repository(IEmpScheduleService.class).getEmpShiftInfos(tenantId, startDate, endDate, rows));
        }
        return allEmpShifts;
    }

    public static class EmployeeShiftStats {
        private BigDecimal workingHours;
        private BigDecimal nightShiftDays;
        private BigDecimal actualWorkingHours; // 实际工时（单位：分钟）

        public EmployeeShiftStats() {
            this.actualWorkingHours = BigDecimal.ZERO;
        }

        public BigDecimal getWorkingHours() {
            return workingHours;
        }

        public void setWorkingHours(BigDecimal workingHours) {
            this.workingHours = workingHours;
        }

        public BigDecimal getNightShiftDays() {
            return nightShiftDays;
        }

        public void setNightShiftDays(BigDecimal nightShiftDays) {
            this.nightShiftDays = nightShiftDays;
        }

        public BigDecimal getActualWorkingHours() {
            return actualWorkingHours;
        }

        public void setActualWorkingHours(BigDecimal actualWorkingHours) {
            this.actualWorkingHours = actualWorkingHours;
        }
    }

    /**
     * 计件员工
     *
     * @param empWorkInfos 员工信息
     * @return List<HourlyWorkingHourEmpDto>
     */
    public List<HourlyWorkingHourEmpDto> getPieceworkEmpWorkInfos(List<EmpWorkInfo> empWorkInfos) {
        return empWorkInfos.stream().filter(this::isPieceworkEmployee).map(this::getHourlyWorkingHourEmpDto).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 非计件员工
     *
     * @param empWorkInfos 员工信息
     * @return List<HourlyWorkingHourEmpDto>
     */
    public List<HourlyWorkingHourEmpDto> getNonPieceworkEmpWorkInfos(List<EmpWorkInfo> empWorkInfos) {
        return empWorkInfos.stream().filter(this::isNonPieceworkEmployee).map(this::getHourlyWorkingHourEmpDto).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 转换员工信息
     *
     * @param emp 员工
     * @return HourlyWorkingHourEmpDto
     */
    private HourlyWorkingHourEmpDto getHourlyWorkingHourEmpDto(EmpWorkInfo emp) {
        HourlyWorkingHourEmpDto empDto = new HourlyWorkingHourEmpDto();
        empDto.setEmpId(emp.getEmpId());
        empDto.setEmpName(emp.getName());
        empDto.setWorkno(emp.getWorkno());
        empDto.setOrganize(null != emp.getOrganize() ? Long.parseLong(emp.getOrganize()) : null);
        empDto.setOrganizeTxt(Optional.ofNullable(emp.getOrganizeTxt()).orElse(null));
        empDto.setSalaryWorkHour(Optional.ofNullable(emp.getSalaryWorkHour().getCode()).orElse(null));
        empDto.setWorkHour(Optional.ofNullable(emp.getWorkHour().getValue()).orElse(null));
        empDto.setHireDate(emp.getHireDate());
        empDto.setLeaveDate(emp.getLeaveDate());
        empDto.setWorkHour(Optional.ofNullable(emp.getWorkHour().getValue()).orElse(null));
        if (isStandardWorkHour(emp)) {
            empDto.setWorkingHourType(HourlyWorkingHourEmpDto.WorkingHourType.STANDARD);
        } else if (isComprehensive(emp)) {
            empDto.setWorkingHourType(HourlyWorkingHourEmpDto.WorkingHourType.COMPREHENSIVE);
        } else {
            return null;
        }
        return empDto;
    }

    /**
     * 判断是否为计件员工
     */
    private boolean isPieceworkEmployee(EmpWorkInfo empWorkInfo) {
        DictSimple salaryWorkHour = empWorkInfo.getSalaryWorkHour();
        return salaryWorkHour != null && StringUtil.isNotBlank(salaryWorkHour.getCode()) && PIECEWORK_CODE.equals(salaryWorkHour.getCode());
    }

    /**
     * 判断是否为非计件员工
     */
    private boolean isNonPieceworkEmployee(EmpWorkInfo empWorkInfo) {
        DictSimple salaryWorkHour = empWorkInfo.getSalaryWorkHour();
        return salaryWorkHour != null && StringUtil.isNotBlank(salaryWorkHour.getCode()) && NON_PIECEWORK_CODE.equals(salaryWorkHour.getCode());
    }

    /**
     * 判断是否为标准工时
     */
    private boolean isStandardWorkHour(EmpWorkInfo empWorkInfo) {
        EnumSimple workHour = empWorkInfo.getWorkHour();
        return workHour != null && StringUtil.isNotBlank(workHour.getValue()) && STANDARD_WORK_HOUR_CODE.equals(workHour.getValue());
    }

    /**
     * 判断是否为综合工时
     */
    private boolean isComprehensive(EmpWorkInfo empWorkInfo) {
        EnumSimple workHour = empWorkInfo.getWorkHour();
        return workHour != null && StringUtil.isNotBlank(workHour.getValue()) && COMPREHENSIVE_WORK_HOUR_CODE.equals(workHour.getValue());
    }

    /**
     * 查询员工数据
     *
     * @param tenantId 租户
     * @param empIds   员工
     * @return List<EmpWorkInfo>
     */
    public List<EmpWorkInfo> getTenantEmpInfos(String tenantId, List<Long> empIds) {
        List<EmpWorkInfo> empInfoList = new ArrayList<>(empIds.size());
        try {
            UserContext.doInitSecurityUserInfo(tenantId, null, null, null, null, null);
            List<List<Long>> empIdList = ListTool.split(empIds, DEFAULT_SPLIT_PAGE_SIZE);
            for (List<Long> rows : empIdList) {
                if (CollectionUtils.isEmpty(rows)) {
                    continue;
                }
                empInfoList.addAll(getEmpInfoList(tenantId, rows.stream().map(String::valueOf).collect(Collectors.toList())));
            }
            return empInfoList;
        } finally {
            UserContext.removeSecurityUserInfo();
        }
    }

    /**
     * 查询员工数据
     *
     * @param tenantId 租户
     * @param empIds   员工
     * @return List<EmpWorkInfo>
     */
    private List<EmpWorkInfo> getEmpInfoList(String tenantId, List<String> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Collections.emptyList();
        }
        DataFilter filter = DataFilter.eq("tenantId", tenantId).andIn("empId", empIds).andNe("deleted", Boolean.TRUE.toString());
        try {
            return DataQuery.identifier(EMP_WORK_INFO_IDENTIFIER).decrypt().dept().specifyLanguage()
                    .queryInvisible().exp().limit(-1, 1).filter(filter, EmpWorkInfo.class, System.currentTimeMillis())
                    .getItems();
        } catch (Exception e) {
            log.error("Query {} exception:{}", EMP_WORK_INFO_IDENTIFIER, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询方案下的员工
     *
     * @param tenantId 租户
     * @param waGroup  方案
     * @param timePair 时间
     * @return List<Long>
     */
    private List<Long> getEmpGroupEmpIds(String tenantId, WaGroup waGroup, Pair<Long, Long> timePair) {
        Boolean isDefault = false;
        if (waGroup.getIsDefault() != null) {
            isDefault = waGroup.getIsDefault();
        }
        Long startDate = timePair.getLeft();
        Long endDate = timePair.getRight();
        return repository(WaSobDo.class).getEmpIdListByGroup(tenantId, endDate, isDefault, waGroup.getWaGroupId(), startDate, endDate, null);
    }

    /**
     * 考勤方案
     *
     * @param waGroupId 主键
     * @return WaGroup
     */
    private WaGroup getWaGroup(Integer waGroupId) {
        return waGroupMapper.selectByPrimaryKey(waGroupId);
    }

    /**
     * 周期范围
     *
     * @param tenantId        租户
     * @param attendanceMonth 周期
     * @param waGroupId       方案主键
     * @param compStatPeriod  统计周期
     * @return Pair<Long, Long>
     */
    private Pair<Long, Long> getAttendancePeriod(String tenantId, Integer attendanceMonth, Integer waGroupId, Integer compStatPeriod) {
        switch (compStatPeriod) {
            case 1:
                return getAttendancePeriod(tenantId, attendanceMonth, waGroupId);
            case 2:
            default:
                break;
        }
        return null;
    }

    /**
     * 周期范围
     *
     * @param tenantId        租户
     * @param attendanceMonth 周期
     * @param waGroupId       方案主键
     * @return Pair<Long, Long>
     */
    private Pair<Long, Long> getAttendancePeriod(String tenantId, Integer attendanceMonth, Integer waGroupId) {
        List<WaSobDo> sobs = repository(ISobService.class).getWaSobIdByDateRangeAndPeriodMonth(tenantId, null, Collections.singletonList(attendanceMonth), waGroupId);
        Long currentStart;
        Long segmentEnd;
        if (CollectionUtils.isNotEmpty(sobs)) {
            WaSobDo sob = sobs.get(0);
            currentStart = sob.getStartDate();
            segmentEnd = DateUtil.getOnlyDate(new Date(sob.getEndDate() * 1000));
        } else {
            Map map = repository(WaAttendanceConfigService.class).getWaGroupCycle(attendanceMonth, waGroupId);
            currentStart = Long.parseLong(map.get("startDate").toString());
            segmentEnd = Long.parseLong(map.get("endDate").toString());
        }
        return Pair.of(currentStart, segmentEnd);
    }

    public AttendancePageResult<EmpWorkingHoursSettlementVo> getPageList(WorkingHourSettlementPageDto dto, SecurityUserInfo userInfo) {
        PageBean pageBean = PageUtil.getNewPageBean(dto);
        String tenantId = userInfo.getTenantId();
        String sortString = pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(sortString));
        PageList<WaEmpWorkingHoursSettlementDo> list = waEmpWorkingHoursSettlementDo.getPageList(tenantId, dto.getAttendanceMonth(), dto.getWaGroupId(), dto.getOrganize(), dto.getWorkHours(), dto.getSalaryWorkHours(), dto.getKeywords(), dto.getDataScope(), pageBounds);
        if (CollectionUtils.isEmpty(list)) {
            return new AttendancePageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0);
        }
        List<EmpWorkingHoursSettlementVo> items = new ArrayList<>(list.size());
        for (WaEmpWorkingHoursSettlementDo row : list) {
            EmpWorkingHoursSettlementVo vo = ObjectConverter.convert(row, EmpWorkingHoursSettlementVo.class);
            vo.setWorkHour(vo.getWorkHour() == null ? "" : WorkHourEnum.getName(Integer.parseInt(vo.getWorkHour())));
            vo.setSalaryWorkHour(vo.getSalaryWorkHour() == null ? "" : SalaryWorkHourEnum.getName(Integer.parseInt(vo.getSalaryWorkHour())));
            vo.convertTime();
            items.add(vo);
        }
        return new AttendancePageResult<>(items, dto.getPageNo(), dto.getPageSize(), list.getPaginator().getTotalCount());
    }

    public List<WaEmpWorkingHoursSettlementDo> calcHourlyWorkingHours(SecurityUserInfo userInfo, String period, long startTime, long endTime, WaGroup waGroup, WaGroupWorkingHourRuleDo rule, List<HourlyWorkingHourEmpDto> emps) {
        val tenantId = userInfo.getTenantId();
        val operator = userInfo.getUserId();
        val empIds = emps.stream().map(HourlyWorkingHourEmpDto::getEmpId).collect(Collectors.toList());
        val groupedEmps = emps.stream()
                .collect(Collectors.groupingBy(HourlyWorkingHourEmpDto::getWorkingHourType));
        Map<String, BigDecimal> workingHoursOfStandard = calcHourlyWorkingHoursOfStandard(tenantId, startTime, endTime,
                groupedEmps.getOrDefault(HourlyWorkingHourEmpDto.WorkingHourType.STANDARD, Lists.newArrayList())
                        .stream().map(HourlyWorkingHourEmpDto::getEmpId).collect(Collectors.toList()));
        Map<String, BigDecimal> workingHoursOfComprehensive = calcHourlyWorkingHoursOfComprehensive(tenantId, startTime, endTime, rule,
                groupedEmps.getOrDefault(HourlyWorkingHourEmpDto.WorkingHourType.COMPREHENSIVE, Lists.newArrayList())
                        .stream().map(HourlyWorkingHourEmpDto::getEmpId).collect(Collectors.toList()));
        Map<String, BigDecimal> workingHour = Maps.map(workingHoursOfStandard);
        workingHour.putAll(workingHoursOfComprehensive);
        //实际工时:考勤分析-月度汇总表中的“实际出勤时长（小时）”字段
        Map<String, BigDecimal> actualWorkHour = waAnalyzeDo.groupRegisterTimeByEmps(tenantId, startTime, endTime, empIds);
        List<EmpRegisterTimeDto> empRegisterList = waAnalyzeDo.listRegisterTimeByEmps(tenantId, startTime, endTime, empIds);
        List<EmpOvertimeDto> empOvertimeList = waEmpOvertimeDo.listOvertimeByEmps(tenantId, startTime, endTime, empIds);
        val empCompensatories = CompensatoryPoolDo.generate(tenantId, period, empIds, startTime, endTime).stream()
                .collect(Collectors.groupingBy(it -> it.getEmpId()));
        Map<String, WaEmpWorkingHoursSettlementDo> settlementMap = Maps.map();
        emps.forEach(emp -> {
            val empId = emp.getEmpId();
            val settlement = new WaEmpWorkingHoursSettlementDo();
            settlement.setSettlementId(SnowUtil.createId());
            settlement.setAttendanceMonth(Integer.valueOf(period));
            settlement.setPeriodStartDate(startTime);
            settlement.setPeriodEndDate(endTime);
            settlement.setEmpId(Long.valueOf(empId));
            settlement.setEmpName(emp.getEmpName());
            settlement.setWorkno(emp.getWorkno());
            settlement.setOrganize(emp.getOrganize());
            settlement.setOrganizeTxt(emp.getOrganizeTxt());
            settlement.setSalaryWorkHour(emp.getSalaryWorkHour());
            settlement.setWorkHour(emp.getWorkHour());
            settlement.setStdWorkTime(workingHour.getOrDefault(empId, new BigDecimal(0)).multiply(new BigDecimal(60)));//标准工时
            settlement.setActualWorkTime(actualWorkHour.getOrDefault(empId, new BigDecimal(0)).multiply(new BigDecimal(60)));//实际工时
            settlement.setNightShiftDays(new BigDecimal(0));//夜班天数
            settlement.setTenantId(tenantId);
            settlement.setDeleted(DeleteStatusEnum.UN_DELETED.getIndex());
            settlement.setCreateBy(operator);
            settlement.setCreateTime(System.currentTimeMillis() / 1000);
            settlement.setUpdateBy(operator);
            settlement.setUpdateTime(settlement.getCreateTime());
            settlement.setWaGroupId(waGroup.getWaGroupId());
            settlement.setWaGroupName(waGroup.getWaGroupName());
            if (empCompensatories.containsKey(empId)) {
                val compensatory = empCompensatories.get(empId).get(0);
                settlement.setCurPeriodCompensatorySum(compensatory.getCompensatoryTotal());
                settlement.setLastPeriodCompensatory(compensatory.getLastAmount());
                settlement.setCurPeriodCompensatory(compensatory.getAmount());
                settlement.setCurMonthSalarySettlement(compensatory.getSettlement());
            }
            settlementMap.put(empId, settlement);
        });
        val leaveList = waLeaveService.listLeaveDetail(startTime, endTime, empIds.stream().map(it->Long.valueOf(it)).collect(Collectors.toList()));
        Map<String, Object> analyseQueryMap = Maps.map();
        analyseQueryMap.put("startDate", startTime);
        analyseQueryMap.put("endDate", endTime);
        analyseQueryMap.put("belongid", tenantId);
        analyseQueryMap.put("columns", "");
        analyseQueryMap.put("wafilter", " 1=1");
        analyseQueryMap.put("empIds", empIds.stream().map(it->Long.valueOf(it))
                .collect(Collectors.toList()));
        if(true){
            return com.googlecode.totallylazy.Lists.list(settlementMap.values());
        }
        List<Map<String, Object>> empDailyWaAnalyzeList = workOvertimeMapper.searchRegMonthList(analyseQueryMap);
        Map<Long, List<Map>> empDailyWaAnalyzeListMap = empDailyWaAnalyzeList.stream()
                .collect(Collectors.groupingBy(map -> (Long) map.get("empid")));

        for(String empId : empIds){
            List<Map> selectedEmpDailyWaAnalyzeList = empDailyWaAnalyzeListMap.get(empId);
            val settlement = settlementMap.get(empId);
            if(settlement != null){
                for(long loop = startTime;loop <= endTime;loop = loop + 24 * 3600){
                    val belongDate = loop;
                    val selectedEmpDailyWaAnalyze = selectedEmpDailyWaAnalyzeList.stream().filter(it->(long)it.get("belong_date") == belongDate)
                            .findFirst().get();
                    boolean isKg = (int) selectedEmpDailyWaAnalyze.getOrDefault("is_kg", 0) == 1;
                    Integer workTime = Optional.ofNullable((Integer) selectedEmpDailyWaAnalyze.get("work_time")).orElse(0);
                    val lateTime = Optional.ofNullable((Number)selectedEmpDailyWaAnalyze.get("late_time")).orElse(0d).doubleValue();
                    val earlyTime = Optional.ofNullable((Number)selectedEmpDailyWaAnalyze.get("early_time")).orElse(0d).doubleValue();
                    val leaveTime = leaveList.stream().filter(it->empId.equals(it.getEmpId()) && ((Long)belongDate).equals(it.getLeaveDate())).mapToDouble(it->{
                        Double duration = it.getTimeDuration() - it.getCancelTimeDuration();
                        if(1 == it.getTimeUnit()){
                            duration = duration * 8 * 60;
                        }
                        return duration;
                    }).sum();

                    double attendanceTime = 0d;
                    // //旷工||迟到早退>30||全天假
                    if (isKg || lateTime+earlyTime>30 || leaveTime>=workTime) {

                    }
                    // 请非全天假
                    else if (leaveTime > 0) {//非全天假
                        attendanceTime = Math.max(workTime - leaveTime, 0);
                    }
                    // 正常出勤
                    else {
                        attendanceTime = workTime;
                    }


                    val empRegister = empRegisterList.stream()
                            .filter(it->it.getEmpId().equals(empId)
                                    && it.getBelongDate() == belongDate).findAny().orElse(null);
                    val overtime = empOvertimeList.stream()
                            .filter(it->it.getEmpId().equals(empId) && Instant.ofEpochSecond(it.getOvertimeStartTime())
                                    .atZone(ZoneId.systemDefault()).withHour(0).withMinute(0)
                                    .withSecond(0).withNano(0).toEpochSecond() == belongDate).findAny().orElse(null);
                    long shiftTimeRange = 0l;
                    if(null != empRegister){
                        long shiftStartTime = empRegister.getShiftStartTime();
                        long shiftEndTime = empRegister.getShiftEndTime();
                        if (shiftStartTime > shiftEndTime) {
                            shiftEndTime = shiftEndTime + 24 * 60;
                        }
                        shiftTimeRange = (shiftEndTime < 1920 ? shiftEndTime : 1920) - (shiftStartTime > 1200 ? shiftStartTime : 1200);
                    }
                    if(null == overtime){
                        if(empRegister != null){
                            double registerTime = attendanceTime;//实际出勤总时长--分钟数量
                            double time1 = 0;
                            if (shiftTimeRange >= 240 && shiftTimeRange < 480) {
                                time1 = 0.5;
                            } else if (shiftTimeRange >= 480) {
                                time1 = 1;
                            }
                            double time2 = 0;
                            if (registerTime >= 240 && registerTime < 480) {
                                time2 = 0.5;
                            } else if (registerTime >= 480) {
                                time2 = 1;
                            }
                            val time = Math.min(time1, time2);
                            settlement.setNightShiftDays(settlement.getNightShiftDays().add(new BigDecimal(time)));
                        }
                    }else{
                        long otStartTime = overtime.getOvertimeStartTime();
                        long otEndTime = overtime.getOvertimeEndTime();
                        double relTime = overtime.getRelTimeDuration();
                        val otCompareEndTime = Instant.ofEpochSecond(otEndTime)
                                .atZone(ZoneId.systemDefault()).withHour(0).withMinute(0).withSecond(0).withNano(0).toEpochSecond() + 480*60;
                        val otCompareStartTime = Instant.ofEpochSecond(otStartTime)
                                .atZone(ZoneId.systemDefault()).withHour(0).withMinute(0).withSecond(0).withNano(0).toEpochSecond() + 1200*60;
                        val otTimeRange = (otEndTime < otCompareEndTime ? otEndTime : otCompareEndTime) - (otStartTime > otCompareStartTime ? otStartTime : otCompareStartTime);
                        if(otTimeRange > 0){
                            if(empRegister == null || shiftTimeRange<=0){
                                double time1 = 0;
                                if (otTimeRange >= 240*60 && otTimeRange < 480*60) {
                                    time1 = 0.5;
                                } else if (otTimeRange >= 480*60) {
                                    time1 = 1;
                                }
                                double time2 = 0;
                                if (relTime >= 240 && relTime < 480) {
                                    time2 = 0.5;
                                } else if (relTime >= 480) {
                                    time2 = 1;
                                }
                                val time = Math.min(time1, time2);
                                settlement.setNightShiftDays(settlement.getNightShiftDays().add(new BigDecimal(time)));
                            }else{
                                val a = otTimeRange - 0l;//加班休息扣减秒数
                                val b = shiftTimeRange - 0l;//班次休息扣减分钟
                                val workdayTime = attendanceTime;//正常工作日工时分钟数
                                if(relTime * 60 > a && workdayTime > b){
                                    if(a + b * 60 >= 480 * 60){
                                        settlement.setNightShiftDays(settlement.getNightShiftDays().add(new BigDecimal(1)));
                                    }else if(a + b * 60 < 480 * 60 && a + b * 60 >= 240 *60){
                                        settlement.setNightShiftDays(settlement.getNightShiftDays().add(new BigDecimal(0.5)));
                                    }
                                }
                            }
                        }else{
                            if(empRegister != null){
                                double registerTime = empRegister.getRegisterTime();//实际出勤总时长--分钟数量
                                double time1 = 0;
                                if (shiftTimeRange >= 240 && shiftTimeRange < 480) {
                                    time1 = 0.5;
                                } else if (shiftTimeRange >= 480) {
                                    time1 = 1;
                                }
                                double time2 = 0;
                                if (registerTime >= 240 && registerTime < 480) {
                                    time2 = 0.5;
                                } else if (registerTime >= 480) {
                                    time2 = 1;
                                }
                                val time = Math.min(time1, time2);
                                settlement.setNightShiftDays(settlement.getNightShiftDays().add(new BigDecimal(time)));
                            }
                        }
                    }
                }
            }
        }
        return com.googlecode.totallylazy.Lists.list(settlementMap.values());
    }


    public Map<String, BigDecimal> calcHourlyWorkingHoursOfStandard(String tenantId, long startTime, long endTime, List<String> empIds) {
        if (empIds.isEmpty()) {
            return Maps.map();
        }
        //标准工时:日历排班天数*8
        Map<Integer, WaShiftDef> corpShiftDefMap = waCommonService.getCorpAllShiftDef(tenantId);
        Map<String, Object> shiftMap = new HashMap<>();
        shiftMap.put("belongid", tenantId);
        shiftMap.put("startDate", startTime);
        shiftMap.put("endDate", endTime);
        shiftMap.put("anyEmpids", "'{" + StringUtils.join(empIds, ",").concat("}'"));
        Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();
        Map<String, EmpShiftInfo> empShift = waCommonService.getEmpShiftInfoListMaps(shiftMap, empShiftInfoByDateMap, empIds.stream().map(Long::valueOf).collect(Collectors.toList()), corpShiftDefMap);
        Map<String, Long> empWorkHours = Maps.map();
        Map<String, BigDecimal> empWorkHoursBigDecimal = Maps.map();
        empShift.forEach((key, shiftInfo) -> {
            if (startTime > shiftInfo.getWorkDate() || endTime < shiftInfo.getWorkDate()) {
                return;
            }
            if (shiftInfo.getDateType() != 1) {
                return;
            }
            String empId = StringUtils.substringBefore(key, "_");
            if (empWorkHours.containsKey(empId)) {
                empWorkHours.put(empId, empWorkHours.get(empId) + 1);
            } else {
                empWorkHours.put(empId, 1L);
            }
        });
        empWorkHours.keySet().forEach(empId -> empWorkHours.put(empId, empWorkHours.get(empId) * 8));
        empIds.forEach(empId -> {
            if (!empWorkHours.containsKey(empId)) {
                empWorkHoursBigDecimal.put(empId, new BigDecimal(0));
            } else {
                empWorkHoursBigDecimal.put(empId, new BigDecimal(empWorkHours.get(empId)));
            }
        });
        return empWorkHoursBigDecimal;
    }

    public Map<String, BigDecimal> calcHourlyWorkingHoursOfComprehensive(String tenantId, long startTime, long endTime, WaGroupWorkingHourRuleDo rule, List<String> empIds) {
        if (empIds.isEmpty()) {
            return Maps.map();
        }
        //标准工时:根据考勤方案 - 工时规则中配置的固定值还是工作日历去显示该周期的标准工时
        if (CompStdCalcMethodEnum.BY_FIX.getIndex().equals(rule.getCompStdCalcMethod())) {
            val fix = rule.getCompStdWorkingHours();
            return Maps.map(empIds.stream().map(it -> com.googlecode.totallylazy.Pair.pair(it, fix)).collect(Collectors.toList()));
        } else {
            val compStdDateType = Arrays.stream(rule.getCompStdDateType().split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            Map<Integer, WaShiftDef> corpShiftDefMap = waCommonService.getCorpAllShiftDef(tenantId);
            Map<String, Object> shiftMap = new HashMap<>();
            shiftMap.put("belongid", tenantId);
            shiftMap.put("startDate", startTime);
            shiftMap.put("endDate", endTime);
            shiftMap.put("anyEmpids", "'{" + StringUtils.join(empIds, ",").concat("}'"));
            Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();
            Map<String, EmpShiftInfo> empShift = waCommonService.getEmpShiftInfoListMaps(shiftMap, empShiftInfoByDateMap, empIds.stream().map(Long::valueOf).collect(Collectors.toList()), corpShiftDefMap);
            Map<String, Long> empWorkHours = Maps.map();
            Map<String, BigDecimal> empWorkHoursBigDecimal = Maps.map();
            empShift.forEach((key, shiftInfo) -> {
                if (startTime > shiftInfo.getWorkDate() || endTime < shiftInfo.getWorkDate()) {
                    return;
                }
                if (compStdDateType.contains(shiftInfo.getDateType())) {
                    String empId = StringUtils.substringBefore(key, "_");
                    if (empWorkHours.containsKey(empId)) {
                        empWorkHours.put(empId, empWorkHours.get(empId) + 1);
                    } else {
                        empWorkHours.put(empId, 1L);
                    }
                }
            });
            empWorkHours.keySet().forEach(empId -> empWorkHours.put(empId, empWorkHours.get(empId) * 8));
            empIds.forEach(empId -> {
                if (!empWorkHours.containsKey(empId)) {
                    empWorkHoursBigDecimal.put(empId, new BigDecimal(0));
                } else {
                    empWorkHoursBigDecimal.put(empId, new BigDecimal(empWorkHours.get(empId)));
                }
            });
            return empWorkHoursBigDecimal;
        }
    }


}
