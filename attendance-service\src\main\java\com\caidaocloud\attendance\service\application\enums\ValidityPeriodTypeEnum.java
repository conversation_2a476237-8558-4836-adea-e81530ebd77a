package com.caidaocloud.attendance.service.application.enums;

/**
 * 配额有效期类型
 *
 * <AUTHOR>
 * @Date 2021/7/27
 */
public enum ValidityPeriodTypeEnum {
    RESTRICTION(1, "限制有效期"),
    NORESTRICTIONS(2, "不限制有效期");

    private Integer index;
    private String name;

    ValidityPeriodTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ValidityPeriodTypeEnum c : ValidityPeriodTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
