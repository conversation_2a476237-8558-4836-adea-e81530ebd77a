package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OvertimeCarryForwardDto {
    @ApiModelProperty("主键")
    private Integer overtimeId;
    @ApiModelProperty("申请日期")
    private Long applyDate;
    @ApiModelProperty("加班类型")
    private String overtimeTypeName;
    @ApiModelProperty("申请加班开始时间")
    private Long startTime;
    @ApiModelProperty("申请加班结束时间")
    private Long endTime;
    @ApiModelProperty("加班单位")
    private String timeUnitName;
    @ApiModelProperty("申请时长")
    private Float duration;
    @ApiModelProperty("有效时长")
    private Float relTimeDuration;
    @ApiModelProperty("本次结转时长")
    private Float carryForwardDuration;
    @ApiModelProperty("加班事由")
    private String reason;
    @ApiModelProperty("审批状态")
    private String statusName;
    @ApiModelProperty("审批日期")
    private Long lastApprovalTime;
    private Integer detailId;
}
