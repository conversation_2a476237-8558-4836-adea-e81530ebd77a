package com.caidaocloud.attendance.service.application.enums;

/**
 * 考勤方案-工时规则：综合工时制-统计区间
 */
public enum CompStatIntervalEnum {
    INTERVAL_1(1, "在职期间");

    private Integer index;

    private String name;

    CompStatIntervalEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (CompStatIntervalEnum c : CompStatIntervalEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
