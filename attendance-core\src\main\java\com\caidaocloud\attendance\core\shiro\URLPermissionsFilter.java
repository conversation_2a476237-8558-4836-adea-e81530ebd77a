package com.caidaocloud.attendance.core.shiro;

import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.ioc.util.SessionHolder;
import com.github.pagehelper.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authz.AuthorizationFilter;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class URLPermissionsFilter extends AuthorizationFilter {
    private static Pattern HTML_PATTERN = Pattern.compile(".*html/(.*?)/.*\\.html");

    @Autowired
    private StringEncryptor stringEncryptor;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${jasypt.supercode:}")
    private String SUPERCODE;

    @Value("${jasypt.open:false}")
    private boolean OPEN;

    @Value("${jasypt.exclud:}")
    public  void setExclud(String exclud) {
        EXCLUDE_URLS.addAll(Arrays.asList(exclud.split(",")));
    }
    private static List<String> EXCLUDE_URLS;

    static {
        EXCLUDE_URLS = new ArrayList<String>() {{
            add("login");
            add("logout");
            add("content.html");
            add("index.html");

            // swagger
            add("swagger-ui.html");
            add("v2/api-docs");
            add("swagger-resources");
            add("webjars/");
        }};
        // 排除日志请求路径
        EXCLUDE_URLS.add("log/vpm");
        // 排除日志请求路径
        EXCLUDE_URLS.add("pageAuth/getBtnByResId");
        EXCLUDE_URLS.add("ui/getSubUiForm");
        // 排除自定义表单路径
        EXCLUDE_URLS.add("ui/getUiForm");
        EXCLUDE_URLS.add("ui/empDataList");
        EXCLUDE_URLS.add("ui/form");
        EXCLUDE_URLS.add("rptConf/viewReport");
        EXCLUDE_URLS.add("html/system/notify");
        EXCLUDE_URLS.add("assets");
        EXCLUDE_URLS.add("upload/attachfile");
        EXCLUDE_URLS.add("open/api/interface");

        //mobile url
        EXCLUDE_URLS.add("mobile/");
        EXCLUDE_URLS.add("mobileLeave/");
        EXCLUDE_URLS.add("mobileLeave2/");
        EXCLUDE_URLS.add("mobilePersonnel/");
        EXCLUDE_URLS.add("mobileAch/");
        EXCLUDE_URLS.add("mobileToken/");
        EXCLUDE_URLS.add("mobileToken/");
        EXCLUDE_URLS.add("mobileV16/");
        EXCLUDE_URLS.add("mobileV18/");
        EXCLUDE_URLS.add("bonus/");
        EXCLUDE_URLS.add("mobileV16Employee/");
        EXCLUDE_URLS.add("mobileWorkflow/");
        EXCLUDE_URLS.add("wechart/");
        EXCLUDE_URLS.add("gdEmployee/getPubEmployeeInfo");
        EXCLUDE_URLS.add("/api/caidaocloud/monitor");
    }

    @Override
    public boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) throws Exception {
        if (!OPEN) {
            return true;
        }

        Subject subject = this.getSubject(request, response);
        boolean allowed = subject.getPrincipal() != null;
        HttpServletRequest req = (HttpServletRequest) request;
        HttpSession  session = req.getSession();
        Integer userid = (Integer) session.getAttribute("userid");

        if (allowed) {
            String url = req.getRequestURI().replace(req.getContextPath() + "/", "");
            if (StringUtils.isBlank(url) || isExclude(url)) {
                return true;
            }
            boolean ajax = isAjax(req);
            boolean html = StringUtils.isNotEmpty(getModule(url));
            if (html || ajax) {
                String kcode = req.getHeader("kcode");
                if (StringUtil.isEmpty(kcode)) {
                    kcode = req.getParameter("kcode");
                }

                String kurl = req.getHeader("kurl");
//                if(isExclude(kurl)){return true;} // 如果kurl包含在被过滤的url中，则直接返回true

                if (StringUtils.isNotEmpty(kcode)) {
                    if(SUPERCODE.equals(kcode)){
                        return true;
                    }
                    String kcodeString = (String) redisTemplate.opsForValue().get(RedisKeyDefine.KCODE_PREFIX_ +userid);
                    //验证kcode 是否有效
                    if(kcodeString!=null && !kcodeString.contains(kcode)){
                        return false;
                    }
                    if (StringUtils.isNotEmpty(kurl)) {
//                        if (html) {
//                            String module = getModule(url);
//                            String module2 = getModule(kurl);
//                            if (module.equals(module2)) {
//                                url = kurl;
//                            }
//                        } else {
                            url = kurl;
//                        }
                    }
                    String resUrl = (String) redisTemplate.opsForValue().get(RedisKeyDefine.ROLE_RES_PREFIX_ +userid);
                    if(resUrl.contains(url)){
                        //  当在资源范围内时才校验html
                        if (isExclude(url) || (SessionHolder.getUserId() + ":" + url).equals(stringEncryptor.decrypt(kcode))) {
                            return true;
                        }
                    }
                } else if (StringUtils.isNotEmpty(kurl) && isExclude(kurl)) {
                    return true;
                }
                return false;
            }else{
                // 不是ajax请求的时候必须指定kcode，kurl，如：导出
                String kurl = req.getParameter("kurl");
                if(StringUtils.isNotBlank(kurl)){
                    String kcodeString = (String) redisTemplate.opsForValue().get(RedisKeyDefine.KCODE_PREFIX_ +userid);
                    //验证kcode 是否有效
                    String kcode = req.getParameter("kcode");
                    if(kcodeString != null && kcodeString.contains(kcode)) {
                        return true;
                    }
                    return false;
                }else{
                    return true;
                }
            }
        }
        return !allowed;
    }

    public boolean isExclude(String url) {
        boolean exclude = false;
        for (String excludeUrl : EXCLUDE_URLS) {
            if (url.endsWith(excludeUrl) || url.startsWith(excludeUrl)) {
                exclude = true;
            }
        }
        return exclude;
    }

    public String getModule(String url) {
        Matcher matcher = HTML_PATTERN.matcher(url);
        boolean html = matcher.find();
        if (html) {
            return matcher.group(1);
        }
        return "";
    }


    public boolean isAjax(HttpServletRequest request) {
        if (request.getHeader("x-requested-with") != null && request.getHeader("x-requested-with").equalsIgnoreCase("XMLHttpRequest")) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
