package com.caidaocloud.attendance.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WaAnalyzeStatisticsReportDto {
    @ApiModelProperty("员工ID")
    private Long empId;
    @ApiModelProperty("排班工时（分钟）")
    private Integer workTime;
    @ApiModelProperty("出勤时长（分钟）")
    private Float actualWorkTime;
    @ApiModelProperty("迟到时长（分钟）")
    private Float lateTime;
    @ApiModelProperty("早退时长（分钟）")
    private Float earlyTime;
    @ApiModelProperty("旷工时长（分钟）")
    private Integer kgWorkTime;
    @ApiModelProperty("补卡次数（次）")
    private Integer bdkCount;
    @ApiModelProperty("加班（分钟）")
    private Integer otTime;
    @ApiModelProperty("休假次数（次）")
    private Integer leaveCount;
    @ApiModelProperty("迟到次数")
    private Integer totalLateCount;
    @ApiModelProperty("早退次数")
    private Integer totalEarlyCount;
    @ApiModelProperty("旷工次数")
    private Integer totalKgCount;
}