package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;

import java.util.List;
import java.util.Map;

public interface IGroupConditionService {

    List<ConditionDataVo> quotaCondition();

    List<ConditionDataVo> empGroupCondition();

    List<ConditionDataVo> waShiftNoticeCondition();

    List<ConditionDataVo> waShiftNoticeProductionCondition();

    List<ConditionDataVo> quotaRuleCondition();

    List<Map> getQuotaRuleCondition();

    List<ConditionDataVo> empShiftGroupCondition();

    List<ConditionDataVo> empWorkCalendarCondition();
}