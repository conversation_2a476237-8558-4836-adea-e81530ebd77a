package com.caidaocloud.attendance.core.wa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 加班申请
 *
 * <AUTHOR>
 * @Date 2021/3/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OvertimeApplyBean {
    private Long corpId;
    private String belongId;
    private Long userid;
    private Long empid;
    private Integer otId;
    private Integer preOtId;
    private long startTime;
    private long endTime;
    private String reason;
    private Integer compensateType;
    private Boolean isValidateTimeControl;
    private String file;
    private String fileName;
    private String myFiles;
    private Integer overtimeTypeId;
    private Integer timeControlType;
    private Float controlTimeDuration;
    private Integer controlTimeUnit;
    /**
     * 批量单据ID，批量申请时使用
     */
    private Long batchId;

    public Boolean getIsValidateTimeControl() {
        return isValidateTimeControl;
    }

    public void setIsValidateTimeControl(Boolean validateTimeControl) {
        isValidateTimeControl = validateTimeControl;
    }
}
