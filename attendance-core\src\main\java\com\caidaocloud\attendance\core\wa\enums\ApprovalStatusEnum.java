package com.caidaocloud.attendance.core.wa.enums;

import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.workflow.enums.WfProcessStatusEnum;

public enum ApprovalStatusEnum {
    DRAFT(0, "暂存",201853),
    IN_APPROVAL(1, "审批中", WfProcessStatusEnum.IN_PROCESS, 201854),
    PASSED(2, "已通过", WfProcessStatusEnum.APPROVE, 201855),
    REJECTED(3, "已拒绝", WfProcessStatusEnum.REFUSE, 201856),
    CANCELLATION(4, "已作废", 201857),
    RETURNED(5, "已退回", 201858),
    REVOKING(8, "撤销中", 201859),
    REVOKED(9, "已撤销", WfProcessStatusEnum.REVOKE, 201860);

    private Integer index;
    private String name;
    /**
     * 2.0 工作流审批状态
     */
    private WfProcessStatusEnum wfProcessStatusEnum;
    private Integer code;

    ApprovalStatusEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    ApprovalStatusEnum(Integer index, String name, WfProcessStatusEnum wfProcessStatusEnum, Integer code) {
        this.name = name;
        this.index = index;
        this.wfProcessStatusEnum = wfProcessStatusEnum;
        this.code = code;
    }

    public static String getName(int index) {
        for (ApprovalStatusEnum c : ApprovalStatusEnum.values()) {
            if (c.getIndex() == index) {
                String msg = "";
                try {
                    msg = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(msg)) {
                    return msg;
                } else {
                    return c.name;
                }
            }
        }
        return null;
    }

    public static Integer getIndexByName(String name) {
        for (ApprovalStatusEnum c : ApprovalStatusEnum.values()) {
            if (c.getName().equals(name)) {
                return c.getIndex();
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public WfProcessStatusEnum getWfProcessStatusEnum() {
        return wfProcessStatusEnum;
    }

    public void setWfProcessStatusEnum(WfProcessStatusEnum wfProcessStatusEnum) {
        this.wfProcessStatusEnum = wfProcessStatusEnum;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
