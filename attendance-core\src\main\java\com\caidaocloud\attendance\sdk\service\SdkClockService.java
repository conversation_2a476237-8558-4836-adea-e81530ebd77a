package com.caidaocloud.attendance.sdk.service;

import com.caidaocloud.attendance.core.wa.dto.clock.ClockAnalyseDto;
import com.caidaocloud.attendance.sdk.feign.IClockFeignClient;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SDK打卡服务
 */
@Slf4j
@Service
public class SdkClockService {
    @Autowired
    private IClockFeignClient clockFeignClient;

    /**
     * 打卡分析
     *
     * @param dto
     * @return
     */
    public boolean analyseRegisterRecord(ClockAnalyseDto dto) {
        Result<Boolean> result = clockFeignClient.analyseRegisterRecord(dto);
        if (null == result || !result.isSuccess()) {
            return false;
        }
        return true;
    }
}
