package com.caidaocloud.attendance.core.integrate.listener;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;

import com.caidao1.commons.utils.EmailTmplateMessage;
import com.caidao1.commons.utils.HtmlMailUtil;

public class EmailListerner implements MessageListener {
	private static final Log log = LogFactory.getLog(EmailListerner.class);
	@Autowired
    private RedisTemplate redisTemplate;
    @Autowired
	private HtmlMailUtil sendMailUtil;
	@Override
	public void onMessage(Message message, byte[] pattern) {
		 byte[] body = message.getBody();

		Object obj = redisTemplate.getValueSerializer().deserialize(body);
		if(obj instanceof  EmailTmplateMessage){
            try {
                sendMailUtil.send2((EmailTmplateMessage)obj);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else if(obj instanceof EmailTmplateMessage[]){
			EmailTmplateMessage[] ems = (EmailTmplateMessage[])obj;
			log.info("======send many email :"+ems!=null?ems.length:"0");
            try {
                sendMailUtil.sendBatchEmail(ems);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
	}

}
