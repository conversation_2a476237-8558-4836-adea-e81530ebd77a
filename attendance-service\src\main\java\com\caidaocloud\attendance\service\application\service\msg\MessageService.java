package com.caidaocloud.attendance.service.application.service.msg;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.mybatis.model.SysEmpInfoExample;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidaocloud.attendance.service.application.dto.PhoneSimple;
import com.caidaocloud.attendance.service.application.dto.QuotaGenDto;
import com.caidaocloud.attendance.service.application.dto.msg.*;
import com.caidaocloud.attendance.service.application.enums.GenQuotaMode;
import com.caidaocloud.attendance.service.application.enums.LeaveJobTypeEnum;
import com.caidaocloud.attendance.service.application.enums.msg.MsgAccountType;
import com.caidaocloud.attendance.service.application.enums.msg.MsgPushType;
import com.caidaocloud.attendance.service.application.event.publish.MessageParams;
import com.caidaocloud.attendance.service.application.event.publish.MessagePublish;
import com.caidaocloud.attendance.service.application.event.subscribe.EntityDataChange;
import com.caidaocloud.attendance.service.application.event.subscribe.TenantDto;
import com.caidaocloud.attendance.service.application.service.IGroupService;
import com.caidaocloud.attendance.service.application.service.IQuotaService;
import com.caidaocloud.attendance.service.application.service.ITenantService;
import com.caidaocloud.attendance.service.application.service.workflow.RegisterMsgVarService;
import com.caidaocloud.attendance.service.interfaces.dto.group.GroupDetailDto;
import com.caidaocloud.attendance.service.schedule.application.service.shiftGroup.IWaEmpShiftGroupService;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.DingtalkTemplateTypeEnum;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.util.Base64Util;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SnowflakeUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MessageService {

    @Autowired
    private MsgNoticeService msgNoticeService;
    @Resource
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private MessagePublish messagePublish;
    @Resource
    private GenerateConsumerService generateConsumerService;
    @Resource
    private RegisterMsgVarService registerMsgVarService;
    @Value("${mq.consumer.empWorkInfo:}")
    private String hrPaasEntityEmpWorkInfoField;
    @Value("${mq.consumer.empWorkOverview:}")
    private String hrPaasEntityEmpWorkOverviewField;
    @Autowired
    private IQuotaService quotaService;
    @Autowired
    private IGroupService groupService;
    @Autowired
    private ITenantService tenantService;
    @Resource
    private IWaEmpShiftGroupService waEmpShiftGroupService;

    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public MsgConfigDto getMsgConfigList(NoticeType nType) {
        List<MsgConfigDto> msgConfigList = msgNoticeService.getMsgConfigList(nType);
        if (CollectionUtils.isEmpty(msgConfigList)) {
            return null;
        }
        return msgConfigList.get(0);
    }

    public void createTenant(TenantDto tenantDto) {
        if (null == tenantDto || StringUtils.isBlank(tenantDto.getTenantId())) {
            log.info("Init tenant tenantId is null");
            return;
        }
        try {
            log.info("Create new tenant, start to init consumer");
            generateConsumerService.initGenerateConsumer(tenantDto.getTenantId());
            log.info("Create new tenant, init consumer end");
            log.info("Create new tenant, start to register workflow msg var");
            registerMsgVarService.registerWorkflowMsgVar(tenantDto.getTenantId());
            log.info("Create new tenant, register workflow msg var end");

            log.info("Create new tenant, start to init table");
            tenantService.initTenantTable(tenantDto.getTenantId());
            log.info("Create new tenant, init table end");
        } catch (Exception e) {
            log.error("Init tenant error:{}", e.getMessage(), e);
        }
    }

    /**
     * 消息处理
     *
     * @param msgInfo 消息体
     */
    public void handleMsg(MessageParams msgInfo) {
        if ("msg".equals(msgInfo.getType())) {
            msgPublish(msgInfo);
        }
    }

    /**
     * 信息发布到消息中心
     *
     * @param msgInfo 消息体
     */
    private void msgPublish(MessageParams msgInfo) {
        if (null == msgInfo || null == msgInfo.getNType()) {
            log.info("msg body is empty");
            return;
        }
        Optional<MsgConfigDto> msgConfigOpt = Optional.ofNullable(getMsgConfigList(msgInfo.getNType()));
        if (!msgConfigOpt.isPresent()) {
            log.info("Get msg config from message service is null");
            return;
        }
        MsgConfigDto msgConfigDto = msgConfigOpt.get();
        if (null == msgConfigDto.getChannel()) {
            log.info("msg channel of msg config is null");
            return;
        }
        log.info("Prepare to publish message, msgInfo[{}]", FastjsonUtil.toJson(msgInfo));
        // 创建消息
        List<NoticeMsgDTO> msgList = Lists.newArrayList();
        //查询员工信息，消息接收对象
        List<SysEmpInfo> employees = this.getEmpInfo(msgInfo.getHandlers());
        if (CollectionUtils.isEmpty(employees)) {
            log.info("msg receiver is null");
            return;
        }
        List<String> channels = Arrays.stream(msgConfigDto.getChannel().split(",")).collect(Collectors.toList());
        for (SysEmpInfo employee : employees) {
            String tenantId = employee.getBelongOrgId();
            NoticeMsgDTO noticeMsgDTO = new NoticeMsgDTO();
            noticeMsgDTO.setMsgConfig(msgConfigDto.getBid());
            noticeMsgDTO.setEventEmpId(Objects.toString(employee.getEmpid()));
            noticeMsgDTO.setChannel(channels);
            noticeMsgDTO.setTenantId(tenantId);
            Map<String, Object> ext = new HashMap<>();
            ext.put("receiversReplacedBy", employee.getEmpid());
            noticeMsgDTO.setExt(ext);
            for (String channel : channels) {
                switch (channel) {
                    //短信
                    case "0":
                        noticeMsgDTO.setMessage(createMsgDTO(employee, msgInfo));
                        break;
                    //邮件通知
                    case "1":
                        noticeMsgDTO.setEmailMsg(createEmailDTO(employee, msgInfo));
                        break;
                    //app 通知
                    case "3":
                        noticeMsgDTO.setAppMsg(createAppDTO(employee, msgInfo));
                        break;
                    case "4":
                        // 钉钉工作通知
                        noticeMsgDTO.setDingTalkMsg(createDingTalkMsgDTO(employee, msgInfo));
                        break;
                    case "5":
                        // 钉钉待办通知
                        noticeMsgDTO.setDingtalkToDoNoticeDto(createDingTalkToDoNoticeMsgDTO(employee, msgInfo));
                        break;
                    case "6":
                        noticeMsgDTO.setWxNoticeMsg(createWxOpenNoticeDto(employee, msgInfo));
                        break;
                    default:
                        break;
                }
            }
            msgList.add(noticeMsgDTO);
        }
        if (CollectionUtils.isNotEmpty(msgList)) {
            for (NoticeMsgDTO noticeMsgDTO : msgList) {
                messagePublish.publishToMessageService(noticeMsgDTO);
            }
        }
        log.info("Finish to publish message, msgInfo[{}]", FastjsonUtil.toJson(msgInfo));
    }

    private void buildMsgContent(MessageParams messageParams, StringBuilder builder, boolean isEmail) {
        var lineBreak = "\n";
        if (isEmail) {
            lineBreak = "<br> ";
        }
        if (StringUtils.isNotBlank(messageParams.getContent())) {
            builder.append(messageParams.getContent());
            if (CollectionUtils.isNotEmpty(messageParams.getCustomForms())) {
                builder.append(lineBreak);
            }
        }
        List<KeyValue> customForms = messageParams.getCustomForms();
        if (CollectionUtils.isNotEmpty(customForms)) {
            for (KeyValue keyValue : customForms) {
                builder.append(keyValue.getText()).append(": ").append(keyValue.getValue()).append(lineBreak);
            }
        }
        if (StringUtils.isNotBlank(messageParams.getProcessId())) {
            if (isEmail) {
                builder.append("<a target= \"_blank\" href=\"").append(messageParams.getProcessId()).append("\">点击查看详情</a>").append(lineBreak);
            } else {
                if (messageParams.getNType() != NoticeType.ATTENDANCE_ABNORMAL_REMINDER_MSG) {
                    builder.append("详情请查看：").append(messageParams.getProcessId());
                }
            }
        }
        if (builder.length() > 0 && !isEmail) {
            builder.deleteCharAt(builder.length() - 1);
        }
    }

    private WxOpenNoticeDto createWxOpenNoticeDto(SysEmpInfo empInfo, MessageParams params) {
        if (empInfo == null || empInfo.getEmpid() == null) {
            log.warn("[wxOpenNotice] create wx open notice body failed as target user is not found");
            return null;
        }
        return new WxOpenNoticeDto()
                .setEmpIds(new String[]{Objects.toString(empInfo.getEmpid())})
                .setTitle(params.getTitle())
                .setLinkUrl(params.getLinkPage())
                .setContent(params.getContent());
    }

    /**
     * 创建钉钉待办dto
     */
    private DingtalkToDoNoticeDto createDingTalkToDoNoticeMsgDTO(SysEmpInfo empInfo, MessageParams msgInfo) {
        if (null == empInfo) {
            log.info("empInfo is null");
            return null;
        }
        if (StringUtils.isBlank(empInfo.getEmail())) {
            log.info("empInfo mobile is null");
            return null;
        }
        StringBuilder builder = new StringBuilder();
        buildMsgContent(msgInfo, builder, false);
        String content = builder.toString();
        var title = msgInfo.getTitle();
        List<String> mobileList = Collections.singletonList(empInfo.getMobile());
        DingtalkToDoNoticeDto messageDTO = new DingtalkToDoNoticeDto();
        //钉钉待办任务与审批任务关联表的主键ID，传给message服务，用来更新关联表
        messageDTO.setDingCaidaoId(String.valueOf(snowflakeUtil.createId()));
        List<PhoneSimple> mobiles = mobileList.stream().map(phone -> {
                    PhoneSimple simple = new PhoneSimple();
                    simple.setValue(phone);
                    return simple;
                }
        ).collect(Collectors.toList());
        messageDTO.setMobiles(mobiles);
        messageDTO.setDescription(content);
        messageDTO.setSubject(title);
        /*messageDTO.setBusinessKey(noticeEvent.getBusinessKey());
        messageDTO.setTaskId(noticeEvent.getTaskId());*/
        return messageDTO;
    }

    /**
     * 创建钉钉dto
     */
    private DingtalkMsgDto createDingTalkMsgDTO(SysEmpInfo empInfo, MessageParams msgInfo) {
        if (null == empInfo) {
            log.info("empInfo is null");
            return null;
        }
        if (StringUtils.isBlank(empInfo.getEmail())) {
            log.info("empInfo mobile is null");
            return null;
        }
        StringBuilder builder = new StringBuilder();
        buildMsgContent(msgInfo, builder, false);
        String content = builder.toString();
        var title = msgInfo.getTitle();
        List<String> mobileList = Collections.singletonList(empInfo.getMobile());
        DingtalkMsgDto messageDTO = new DingtalkMsgDto();
        List<PhoneSimple> mobiles = mobileList.stream().map(phone -> {
                    PhoneSimple simple = new PhoneSimple();
                    simple.setValue(phone);
                    return simple;
                }
        ).collect(Collectors.toList());
        messageDTO.setMobiles(mobiles);
        //钉钉工作通知默认markdown类型
        messageDTO.setTemplateType(DingtalkTemplateTypeEnum.MARKDOWN_MESSAGE.getIndex());
        if (msgInfo.getNType() == NoticeType.ATTENDANCE_ABNORMAL_REMINDER_MSG) {
            messageDTO.setTemplateType(DingtalkTemplateTypeEnum.LINK_MESSAGE.getIndex());
            messageDTO.setUrl(msgInfo.getProcessId());
        }
        messageDTO.setContent(content);
        messageDTO.setSubject(title);
        return messageDTO;
    }

    /**
     * 创建短信dto
     */
    private MessageDTO createMsgDTO(SysEmpInfo empInfo, MessageParams msgInfo) {
        if (null == empInfo) {
            log.info("empInfo is null");
            return null;
        }
        if (StringUtils.isBlank(empInfo.getEmail())) {
            log.info("empInfo mobile is null");
            return null;
        }
        var mobileList = Collections.singletonList(new MessageDTO.MobileDto().setMobile(empInfo.getMobile()));
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMobile(mobileList);
        StringBuilder builder = new StringBuilder();
        buildMsgContent(msgInfo, builder, false);
        String content = builder.toString();
        messageDTO.setContent(content);
        return messageDTO;
    }

    /**
     * 创建发送邮件dto
     */
    private EmailMsgDTO createEmailDTO(SysEmpInfo empInfo, MessageParams msgInfo) {
        if (null == empInfo) {
            log.info("empInfo is null");
            return null;
        }
        if (StringUtils.isBlank(empInfo.getEmail())) {
            log.info("empInfo email is null");
            return null;
        }
        var title = msgInfo.getTitle();
        var emailStr = Joiner.on(";").join(Collections.singletonList(empInfo.getEmail()));
        var emailMsgDTO = new EmailMsgDTO();
        emailMsgDTO.setSubject(title);
        StringBuilder builder = new StringBuilder();
        buildMsgContent(msgInfo, builder, true);
        String content = builder.toString();
        if (StringUtils.isNotBlank(content)) {
            emailMsgDTO.setContent(Base64Util.encode(content));
        } else {
            emailMsgDTO.setContent(content);
        }
        emailMsgDTO.setTo(emailStr);
        if (CollectionUtils.isNotEmpty(msgInfo.getFilePaths())) {
            emailMsgDTO.setAffix(String.join(",", msgInfo.getFilePaths()));
        }
        if (CollectionUtils.isNotEmpty(msgInfo.getFileNames())) {
            emailMsgDTO.setAffixName(String.join(",", msgInfo.getFileNames()));
        }
        return emailMsgDTO;
    }

    /**
     * 创建发送app消息dto
     */
    private List<AppMsgSenderDTO> createAppDTO(SysEmpInfo empInfo, MessageParams msgInfo) {
        if (null == empInfo) {
            return Lists.newArrayList();
        }
        StringBuilder builder = new StringBuilder();
        buildMsgContent(msgInfo, builder, false);
        String content = builder.toString();
        var title = msgInfo.getTitle();
        var currentTimeMillis = System.currentTimeMillis();
        List<AppMsgSenderDTO> appMsgDTOList = Lists.newArrayList();
        var appMsgSenderDTO = new AppMsgSenderDTO();
        appMsgSenderDTO.setUserId(empInfo.getWorkno());
        appMsgSenderDTO.setMsgType(MsgPushType.text);
        appMsgSenderDTO.setTitle(title);
        appMsgSenderDTO.setContent(content);
        appMsgSenderDTO.setCreateTime(currentTimeMillis);
        appMsgSenderDTO.setType(MsgAccountType.workno);
        appMsgDTOList.add(appMsgSenderDTO);
        return appMsgDTOList;
    }

    private List<SysEmpInfo> getEmpInfo(List<Long> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return new ArrayList<>();
        }
        SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
        SysEmpInfoExample.Criteria criteria = empInfoExample.createCriteria();
        criteria.andEmpidIn(empIds);
        return sysEmpInfoMapper.selectByExample(empInfoExample);
    }

    public void handleHrPaasMsg(List<EntityDataChange> data) {
        if (null == data || data.isEmpty()) {
            return;
        }
        Calendar cal = Calendar.getInstance();
        int curYear = cal.get(Calendar.YEAR);
        for (EntityDataChange entityData : data) {
            EntityDataDto after = entityData.getAfter();
            EntityDataDto before = entityData.getBefore();
            if (after != null && !after.isDeleted()) {
                Long empId = Long.valueOf(after.getBid());
                Long dataStartTime = Optional.of(after.getDataStartTime()).orElse(0L);
                Long dataEndTime = Optional.of(after.getDataEndTime()).orElse(0L);
                if (dataStartTime.equals(0L) && dataEndTime.equals(0L)) {
                    continue;
                }
                long nowTime = System.currentTimeMillis();
                if (dataStartTime >= nowTime || dataEndTime >= nowTime) {
                    continue;
                }
                if (after.getProperties() == null || after.getProperties().isEmpty()) {
                    continue;
                }
                // 修改
                String identifier = after.getIdentifier();
                List<String> fields = new ArrayList<>();
                if ("entity.hr.EmpWorkInfo".equals(identifier)) {
                    //标准字段：司龄调整（divisionAgeAdjust），用工类型（empType）字典值， empType.dict.text，福利起始日期（adjustdate）
                    //自定义字段：是否参与总部考勤人员（clock），劳动合同主体与科锐关系（comCareer），员工子类型（empsubtype），是否享有年假（annual）
                    if (StringUtils.isBlank(hrPaasEntityEmpWorkInfoField)) {
                        continue;
                    }
                    fields = Arrays.stream(hrPaasEntityEmpWorkInfoField.split(",")).collect(Collectors.toList());
                }
                if ("entity.hr.EmpWorkOverview".equals(identifier)) {
                    //标准字段：首次工作日期（firstWorkDate），工龄调整（workAgeAdjust）
                    if (StringUtils.isBlank(hrPaasEntityEmpWorkOverviewField)) {
                        continue;
                    }
                    fields = Arrays.stream(hrPaasEntityEmpWorkOverviewField.split(",")).collect(Collectors.toList());
                }
                boolean handleAnnualLeaveQuota = false;
                String dicRegex = "\\.dict\\.code";
                if (null == before) {
                    List<String> finalFields = fields;
                    handleAnnualLeaveQuota = after.getProperties().stream().anyMatch(property -> {
                        String propertyName = property.getProperty();
                        if (property.getDataType() == PropertyDataType.Dict) {
                            propertyName = propertyName.replaceAll(dicRegex, "");
                        }
                        return (finalFields.contains(propertyName) && property.getValue() != null);
                    });
                } else {
                    for (PropertyDataDto property : after.getProperties()) {
                        String propertyName = property.getProperty();
                        if (property.getDataType() == PropertyDataType.Dict) {
                            propertyName = propertyName.replaceAll(dicRegex, "");
                        }
                        if (!fields.contains(propertyName)) {
                            continue;
                        }
                        String finalPropertyName = propertyName;
                        handleAnnualLeaveQuota = before.getProperties().stream().filter(Objects::nonNull).anyMatch(beforeProperty -> {
                            String beforePropertyName = beforeProperty.getProperty();
                            if (beforeProperty.getDataType() == PropertyDataType.Dict) {
                                beforePropertyName = beforePropertyName.replaceAll(dicRegex, "");
                            }
                            String afterValue = Optional.of(property.getValue()).orElse("");
                            String beforeValue = Optional.of(beforeProperty.getValue()).orElse("");
                            return finalPropertyName.equals(beforePropertyName) && !afterValue.equals(beforeValue);
                        });
                        if (handleAnnualLeaveQuota) {
                            break;
                        }
                    }
                }
                if (handleAnnualLeaveQuota) {
                    String tenantId = after.getTenantId();
                    // 核算年假
                    QuotaGenDto genDto = new QuotaGenDto();
                    genDto.setAll(false);
                    genDto.setBelongId(tenantId);
                    genDto.setUserId(0L);
                    GroupDetailDto group = groupService.getEmpGroup(tenantId, empId, DateUtil.getCurrentTime(true));
                    if (null == group) {
                        continue;
                    }
                    genDto.setWaGroupId(group.getWaGroupId());
                    genDto.setYear((short) curYear);
                    genDto.setType(LeaveJobTypeEnum.ANNUAL.getIndex());
                    genDto.setGenQuotaMode(GenQuotaMode.ALL.getIndex());
                    genDto.setCorpId(ConvertHelper.longConvert(tenantId));
                    genDto.setEmpid(empId);
                    log.info("belongOrgId = {} ,waGroupName：{},params：{}", tenantId, null, JSONUtils.ObjectToJson(genDto));
                    try {
                        quotaService.genEmpQuotaForIssuedAnnually(genDto);
                    } catch (Exception e) {
                        log.info("waGroupName:{} handleHrPaasMsg error msg：{}", null, e.getMessage(), e);
                    }
                    try {
                        log.info("EmpDataChangeAutoFlushEmpShiftGroup,tenantId={},Emp={}", tenantId, empId);
                        waEmpShiftGroupService.flushEmpShiftGroupByEmp(tenantId, empId, 0L);
                    } catch (Exception e) {
                        log.info("flushEmpShiftGroup,empId={},handleHrPaasMsg error msg：{}", empId, e.getMessage(), e);
                    }
                }
            }
        }
    }
}
