package com.caidaocloud.attendance.core.annoation.feign;

import com.caidaocloud.attendance.core.wa.dto.shift.ListScheduleQueryDto;
import com.caidaocloud.attendance.core.wa.vo.SdkListEmpShiftForLeaveVo;
import com.caidaocloud.attendance.core.wa.vo.WaShiftDefVo;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ScheduleQueryFeignFallBack implements ScheduleQueryFeignClient {
    @Override
    public Result<List<WaShiftDefVo>> getEmpCalendarShiftList(ListScheduleQueryDto queryDto) {
        return Result.fail();
    }

    @Override
    public Result<List<SdkListEmpShiftForLeaveVo>> getEmpShiftForLeaveCancel(Integer leaveId, Long date, Integer cancelType) {
        return Result.fail();
    }
}
