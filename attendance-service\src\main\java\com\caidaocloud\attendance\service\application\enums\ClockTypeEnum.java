package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

/**
 * 打卡时间类型
 */
public enum ClockTypeEnum {
    SIGN_IN(1, "签到", AttendanceCodes.SIGN_IN),
    SIGN_OUT(2, "签退", AttendanceCodes.SIGN_OUT),
    SIGN_OUT_WORKER(3, "外勤", AttendanceCodes.SIGN_OUT_WORKER),
    SIGN_ONCE(4, "一次卡", AttendanceCodes.SIGN_ONCE);

    private Integer index;
    private Integer code;
    private String name;

    ClockTypeEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(int index) {
        for (ClockTypeEnum c : ClockTypeEnum.values()) {
            if (c.getIndex() == index) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return c.name;
            }
        }
        return "";
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
