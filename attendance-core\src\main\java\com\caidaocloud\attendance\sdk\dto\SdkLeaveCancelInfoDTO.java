package com.caidaocloud.attendance.sdk.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SdkLeaveCancelInfoDTO {
    @ApiModelProperty("开始时间，unix时间戳，精确到秒")
    private Long startTime;
    @ApiModelProperty("结束时间，unix时间戳，精确到秒")
    private Long endTime;
    @ApiModelProperty("半天开始：A 上半天 P 下半天")
    private String shalfDay;
    @ApiModelProperty("半天结束：A 上半天 P 下半天")
    private String ehalfDay;

    @ApiModelProperty("开始日期所选的班次集合（一天排多个班次且假期时间类型为天时使用），时间调整")
    private List<Long> startShifts;
    @ApiModelProperty("结束日期所选的班次集合（一天排多个班次且假期时间类型为天时使用），时间调整")
    private List<Long> endShifts;
}
