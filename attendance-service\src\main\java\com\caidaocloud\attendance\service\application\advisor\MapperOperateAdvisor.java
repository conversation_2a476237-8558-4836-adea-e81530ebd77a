package com.caidaocloud.attendance.service.application.advisor;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.SpringUtils;
import com.caidao1.ioc.dto.SaveItemDto;
import com.caidao1.ioc.dto.UpdRowDto;
import com.caidao1.wa.mybatis.mapper.WaEmpLeaveMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpLeaveTimeMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpOvertimeMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.enums.ApplyModule;
import com.caidaocloud.attendance.service.application.enums.ClockWayEnum;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpApplyRecordMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpTravelMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkCalendarMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftApplyRecord;
import com.caidaocloud.attendance.service.infrastructure.repository.po.*;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Aspect
@Slf4j
public class MapperOperateAdvisor {

    @Autowired
    private ISessionService sessionService;
    /**
     * 这里注入对申请表操作的mapper
     */
    @Resource
    private WaEmpApplyRecordMapper empApplyRecordMapper;
    @Autowired
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private WaEmpLeaveTimeMapper waEmpLeaveTimeMapper;
    @Autowired
    private WaEmpOvertimeMapper waEmpOvertimeMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaEmpTravelMapper waEmpTravelMapper;
    @Resource
    private WorkCalendarMapper workCalendarMapper;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    /**
     * 这里配置要拦截的方法，把不适用的去掉，可以使用模糊匹配
     * 使用||把不同的拦截点拼接即可
     * 新增
     */
    @Pointcut("execution(* com.caidao1.wa.mybatis.mapper.WaEmpLeaveMapper.insertSelective(..)) " +
            "|| execution(* com.caidao1.wa.mybatis.mapper.WaEmpLeaveTimeMapper.insertSelective(..)) " +
            "|| execution(* com.caidao1.wa.mybatis.mapper.WaEmpOvertimeMapper.insertSelective(..)) " +
            "|| execution(* com.caidao1.wa.mybatis.mapper.WaEmpLeaveTimeMapper.insertSelective(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpTravelMapper.insertSelective(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaShiftApplyRecordMapper.insertSelective(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpLeaveCancelMapper.insertSelective(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpLeaveCancelInfoMapper.insertSelective(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaRegisterRecordBdkMapper.insertSelective(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpCompensatoryCaseApplyMapper.insertSelective(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpCompensatoryCaseApplyMapper.insert(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaWorkflowRevokeMapper.insertSelective(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaWorkflowRevokeMapper.insert(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaLeaveExtensionMapper.insert(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaLeaveExtensionMapper.insertSelective(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpShiftMapper.insertSelective(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpShiftMapper.batchSave(..)) "
    )
    public void pointCut() {
    }

    /**
     * 在返回接口中对申请表操作，rvt是返回数据
     * joinpoint.getArgs()是mapper层接口的参数数据
     * 组装后保存到申请表即可
     * 此处事务和service一起成功，一起失败
     *
     * @param joinpoint
     * @param rvt
     */
    @AfterReturning(returning = "rvt", pointcut = "pointCut()")
    public void afterInsertReturnValue(JoinPoint joinpoint, Object rvt) {
        try {
            if (joinpoint.getArgs() != null && joinpoint.getArgs().length > 0) {
                this.getEmpApplyRecordMapper();
                for (Object arg : joinpoint.getArgs()) {
                    if (arg instanceof WaEmpLeave) {
                        this.saveEmpLeaveApplyEntity((WaEmpLeave) arg);
                    }
                    if (arg instanceof WaEmpTravel) {
                        this.saveEmpTravelApplyEntity((WaEmpTravel) arg);
                    }
                    if (arg instanceof WaRegisterRecordBdkPo) {
                        WaRegisterRecordBdkPo record = (WaRegisterRecordBdkPo) arg;
                        if (ClockWayEnum.FILLCLOCK.getIndex().equals(record.getType())) {
                            this.saveEmpRegisterApplyEntity(record);
                        }
                    }
                    if (arg instanceof WaEmpLeaveTime) {
                        this.updateEmpLeaveTimeApplyEntity((WaEmpLeaveTime) arg);
                    }
                    if (arg instanceof WaEmpOvertime) {
                        this.saveEmpOverTimeApplyEntity((WaEmpOvertime) arg);
                    }
                    if (arg instanceof WaShiftApplyRecord) {
                        this.saveEmpShiftApplyEntity((WaShiftApplyRecord) arg);
                    }
                    if (arg instanceof WaEmpLeaveCancel) {
                        this.saveLeaveCancelApplyEntity((WaEmpLeaveCancel) arg);
                    }
                    if (arg instanceof WaEmpLeaveCancelInfo) {
                        this.updateLeaveCancelApplyEntity((WaEmpLeaveCancelInfo) arg);
                    }
                    if (arg instanceof WaEmpCompensatoryCaseApply) {
                        this.saveEmpCompensatoryApplyEntity((WaEmpCompensatoryCaseApply) arg);
                    }
                    if (arg instanceof WaWorkflowRevoke) {
                        this.saveWaWorkflowRevokeEntity((WaWorkflowRevoke) arg);
                    }
                    if (arg instanceof WaLeaveExtension) {
                        this.saveWaLeaveExtensionEntity((WaLeaveExtension) arg);
                    }
                    if (joinpoint.getSignature().getDeclaringTypeName().contains("EmpShiftMapper")) {
                        log.info("updateEmpAttendAnceTypeBySaveStart...");
                        UserInfo userInfo = this.getUserInfo();
                        workCalendarMapper.syncEmpAttendanceType(userInfo.getTenantId(), DateUtil.getOnlyDate(), null);
                        log.info("updateEmpAttendAnceTypeBySaveEnd...");

                    }
                }
            }
        } catch (Exception e) {
            log.error("afterInsertReturnValue error:{}, {}", e.getMessage(), e);
        }
    }

    private UserInfo getUserInfo() {
        UserInfo userInfo = UserContext.getCurrentUser();
        if (null != userInfo) {
            log.debug("MapperOperateAdvisor.getUserInfo userInfo={}", FastjsonUtil.toJsonStr(userInfo));
            return userInfo;
        }
        if (null == sessionService) {
            userInfo = this.getUserInfoBean();
        } else {
            userInfo = sessionService.getUserInfo();
        }
        if (null == userInfo) {
            userInfo = this.getUserInfoBean();
        }
        return userInfo;
    }

    private UserInfo getUserInfoBean() {
        ISessionService iSessionService = SpringUtils.getBean(ISessionService.class);
        return iSessionService.getUserInfo();
    }

    private void getEmpApplyRecordMapper() {
        if (empApplyRecordMapper == null) {
            empApplyRecordMapper = SpringUtils.getBean(WaEmpApplyRecordMapper.class);
        }
    }

    /**
     * 休假
     *
     * @param leave
     */
    private void saveEmpLeaveApplyEntity(WaEmpLeave leave) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(leave.getEmpid());
        WaEmpApplyRecordPo applyRecordPo = new WaEmpApplyRecordPo();
        applyRecordPo.setId(snowflakeUtil.createId());
        applyRecordPo.setTenantId(empInfo.getBelongOrgId());
        applyRecordPo.setModule(ApplyModule.LEAVE.name());
        applyRecordPo.setEntityId(Long.valueOf(leave.getLeaveId()));
        applyRecordPo.setEmpId(leave.getEmpid());
        applyRecordPo.setApplyTime(leave.getCrttime());
        applyRecordPo.setBusinessKey(String.format("%s_%s", leave.getLeaveId(), BusinessCodeEnum.LEAVE.getCode()));
        applyRecordPo.setStatus(Integer.valueOf(leave.getStatus()));
        applyRecordPo.setCreateTime(leave.getCrttime() != null ? leave.getCrttime() : System.currentTimeMillis() / 1000);
        if (null != leave.getCrtuser()) {
            applyRecordPo.setCreateBy(Long.valueOf(leave.getCrtuser()));
        }
        if (null != leave.getUpduser()) {
            applyRecordPo.setUpdateBy(Long.valueOf(leave.getUpduser()));
        }
        applyRecordPo.setUpdateTime(leave.getUpdtime());
        empApplyRecordMapper.insertSelective(applyRecordPo);
        log.info("Save WaEmpLeave end");
    }

    /**
     * 更新休假时间
     *
     * @param leaveTime
     */
    private void updateEmpLeaveTimeApplyEntity(WaEmpLeaveTime leaveTime) {
        UserInfo userInfo = this.getUserInfo();
        if (null != leaveTime.getLeaveId()) {
            Optional<WaEmpApplyRecordPo> opt = Optional.ofNullable(empApplyRecordMapper.queryByModuleAndEntityId(userInfo.getTenantId(), ApplyModule.LEAVE.name(), Long.valueOf(leaveTime.getLeaveId())));
            if (opt.isPresent()) {
                WaEmpApplyRecordPo applyRecordPo = opt.get();
                applyRecordPo.setStartTime(leaveTime.getShiftStartTime());
                applyRecordPo.setEndTime(leaveTime.getShiftEndTime());
                if (null != userInfo.getUserId()) {
                    applyRecordPo.setUpdateBy(userInfo.getUserId());
                    applyRecordPo.setUpdateTime(System.currentTimeMillis() / 1000);
                }
                empApplyRecordMapper.updateByPrimaryKeySelective(applyRecordPo);
                log.info("Update WaEmpLeave end");
            }
        }
    }

    /**
     * 加班
     *
     * @param overtime
     */
    private void saveEmpOverTimeApplyEntity(WaEmpOvertime overtime) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(overtime.getEmpid());
        WaEmpApplyRecordPo applyRecordPo = new WaEmpApplyRecordPo();
        applyRecordPo.setTenantId(empInfo.getBelongOrgId());
        applyRecordPo.setId(snowflakeUtil.createId());
        applyRecordPo.setModule(ApplyModule.OVERTIME.name());
        applyRecordPo.setEntityId(Long.valueOf(overtime.getOtId()));
        applyRecordPo.setEmpId(overtime.getEmpid());
        applyRecordPo.setApplyTime(overtime.getCrttime());
        applyRecordPo.setBusinessKey(String.format("%s_%s", overtime.getOtId(), BusinessCodeEnum.OVERTIME.getCode()));
        applyRecordPo.setStatus(Integer.valueOf(overtime.getStatus()));
        applyRecordPo.setStartTime(overtime.getStartTime());
        applyRecordPo.setEndTime(overtime.getEndTime());
        applyRecordPo.setCreateTime(overtime.getCrttime() == null ? System.currentTimeMillis() / 1000 : overtime.getCrttime());
        applyRecordPo.setUpdateTime(overtime.getUpdtime());
        if (null != overtime.getCrtuser()) {
            applyRecordPo.setCreateBy(Long.valueOf(overtime.getCrtuser()));
        }
        if (null != overtime.getUpduser()) {
            applyRecordPo.setUpdateBy(Long.valueOf(overtime.getUpduser()));
        }
        empApplyRecordMapper.insertSelective(applyRecordPo);
        log.info("Save WaEmpOvertime end");
    }

    /**
     * 出差
     *
     * @param travel
     */
    private void saveEmpTravelApplyEntity(WaEmpTravel travel) {
        UserInfo userInfo = this.getUserInfo();
        WaEmpApplyRecordPo applyRecordPo = new WaEmpApplyRecordPo();
        applyRecordPo.setId(snowflakeUtil.createId());
        applyRecordPo.setTenantId(userInfo.getTenantId());
        applyRecordPo.setModule(ApplyModule.GO_OUT.name());
        applyRecordPo.setEntityId(travel.getTravelId());
        applyRecordPo.setEmpId(travel.getEmpId());
        applyRecordPo.setApplyTime(travel.getCreateTime());
        applyRecordPo.setBusinessKey(String.format("%s_%s", travel.getTravelId(), BusinessCodeEnum.TRAVEL.getCode()));
        applyRecordPo.setStatus(travel.getStatus());
        applyRecordPo.setStartTime(travel.getShiftStartTime());
        applyRecordPo.setEndTime(travel.getShiftEndTime());
        applyRecordPo.setCreateTime(travel.getCreateTime() == null ? System.currentTimeMillis() / 1000 : travel.getCreateTime());
        applyRecordPo.setUpdateTime(travel.getUpdateTime());
        if (null != travel.getCreateBy()) {
            applyRecordPo.setCreateBy(travel.getCreateBy());
        }
        if (null != travel.getUpdateBy()) {
            applyRecordPo.setUpdateBy(travel.getUpdateBy());
        }
        empApplyRecordMapper.insertSelective(applyRecordPo);
        log.info("Save WaEmpTravel end");
    }

    /**
     * 调班
     *
     * @param applyRecord
     */
    private void saveEmpShiftApplyEntity(WaShiftApplyRecord applyRecord) {
        UserInfo userInfo = getUserInfo();
        WaEmpApplyRecordPo applyRecordPo = new WaEmpApplyRecordPo();
        applyRecordPo.setId(snowflakeUtil.createId());
        applyRecordPo.setTenantId(userInfo.getTenantId());
        applyRecordPo.setEntityId(applyRecord.getRecId());
        applyRecordPo.setEmpId(applyRecord.getEmpId());
        applyRecordPo.setApplyTime(applyRecord.getCreateTime());
        applyRecordPo.setModule(ApplyModule.SHIFT_CHANGE.name());
        applyRecordPo.setBusinessKey(String.format("%s_%s", applyRecord.getRecId(), BusinessCodeEnum.SHIFT.getCode()));
        applyRecordPo.setStatus(applyRecord.getStatus());
        this.save(applyRecordPo, userInfo);

    }

    /**
     * 销假
     *
     * @param applyRecord
     */
    private void saveLeaveCancelApplyEntity(WaEmpLeaveCancel applyRecord) {
        UserInfo userInfo = getUserInfo();
        WaEmpApplyRecordPo applyRecordPo = new WaEmpApplyRecordPo();
        applyRecordPo.setId(snowflakeUtil.createId());
        applyRecordPo.setTenantId(userInfo.getTenantId());
        applyRecordPo.setEntityId(applyRecord.getLeaveCancelId());
        applyRecordPo.setEmpId(applyRecord.getEmpid());
        applyRecordPo.setApplyTime(applyRecord.getCreateTime());
        applyRecordPo.setModule(ApplyModule.LEAVE_CANCEL.name());
        applyRecordPo.setBusinessKey(String.format("%s_%s", applyRecord.getLeaveCancelId(), BusinessCodeEnum.VACATION.getCode()));
        applyRecordPo.setStatus(Integer.valueOf(applyRecord.getStatus()));
        this.save(applyRecordPo, userInfo);
    }

    /**
     * 更新销假时间
     *
     * @param cancelInfo
     */
    private void updateLeaveCancelApplyEntity(WaEmpLeaveCancelInfo cancelInfo) {
        UserInfo userInfo = this.getUserInfo();
        if (null != cancelInfo.getLeaveCancelId()) {
            Optional<WaEmpApplyRecordPo> opt = Optional.ofNullable(empApplyRecordMapper.queryByModuleAndEntityId(userInfo.getTenantId(), ApplyModule.LEAVE_CANCEL.name(), Long.valueOf(cancelInfo.getLeaveCancelId())));
            if (opt.isPresent()) {
                WaEmpApplyRecordPo applyRecordPo = opt.get();
                applyRecordPo.setStartTime(cancelInfo.getShiftStartTime());
                applyRecordPo.setEndTime(cancelInfo.getShiftEndTime());
                if (null != userInfo.getUserId()) {
                    applyRecordPo.setUpdateBy(userInfo.getUserId());
                    applyRecordPo.setUpdateTime(System.currentTimeMillis() / 1000);
                }
                empApplyRecordMapper.updateByPrimaryKeySelective(applyRecordPo);
                log.info("Update WaEmpLeaveCancelInfo end");
            }
        }
    }

    /**
     * 补卡
     *
     * @param registerRecord
     */
    private void saveEmpRegisterApplyEntity(WaRegisterRecordBdkPo registerRecord) {
        UserInfo userInfo = this.getUserInfo();
        WaEmpApplyRecordPo applyRecordPo = new WaEmpApplyRecordPo();
        applyRecordPo.setId(snowflakeUtil.createId());
        applyRecordPo.setTenantId(userInfo.getTenantId());
        applyRecordPo.setModule(ApplyModule.PUNCH_IN.name());
        applyRecordPo.setEntityId(registerRecord.getRecordId());
        applyRecordPo.setEmpId(registerRecord.getEmpid());
        applyRecordPo.setApplyTime(registerRecord.getCrttime());
        applyRecordPo.setBusinessKey(String.format("%s_%s", registerRecord.getRecordId(), BusinessCodeEnum.REGISTER.getCode()));
        applyRecordPo.setStatus(registerRecord.getApprovalStatus());
        applyRecordPo.setReplaceTime(registerRecord.getRegDateTime());
        if (null != registerRecord.getCrtuser()) {
            applyRecordPo.setCreateBy(Long.valueOf(registerRecord.getCrtuser()));
        }
        applyRecordPo.setCreateTime(registerRecord.getCrttime() == null ? System.currentTimeMillis() / 1000 : registerRecord.getCrttime());
        applyRecordPo.setUpdateTime(registerRecord.getUpdtime());
        if (null != registerRecord.getUpduser()) {
            applyRecordPo.setUpdateBy(Long.valueOf(registerRecord.getUpduser()));
        }
        empApplyRecordMapper.insertSelective(applyRecordPo);
        log.info("Save WaRegisterRecordBdkPo end");
    }

    /**
     * 补卡
     *
     * @param registerRecord
     */
    private void saveEmpRegisterApplyEntity(WaRegisterRecord registerRecord) {
        UserInfo userInfo = this.getUserInfo();
        WaEmpApplyRecordPo applyRecordPo = new WaEmpApplyRecordPo();
        applyRecordPo.setId(snowflakeUtil.createId());
        applyRecordPo.setTenantId(userInfo.getTenantId());
        applyRecordPo.setModule(ApplyModule.PUNCH_IN.name());
        applyRecordPo.setEntityId(Long.valueOf(registerRecord.getRecordId()));
        applyRecordPo.setEmpId(registerRecord.getEmpid());
        applyRecordPo.setApplyTime(registerRecord.getCrttime());
        applyRecordPo.setBusinessKey(String.format("%s_%s", registerRecord.getRecordId(), BusinessCodeEnum.REGISTER.getCode()));
        applyRecordPo.setStatus(registerRecord.getApprovalStatus());
        applyRecordPo.setReplaceTime(registerRecord.getRegDateTime());
        if (null != registerRecord.getCrtuser()) {
            applyRecordPo.setCreateBy(Long.valueOf(registerRecord.getCrtuser()));
        }
        applyRecordPo.setCreateTime(registerRecord.getCrttime() == null ? System.currentTimeMillis() / 1000 : registerRecord.getCrttime());
        applyRecordPo.setUpdateTime(registerRecord.getUpdtime());
        if (null != registerRecord.getUpduser()) {
            applyRecordPo.setUpdateBy(Long.valueOf(registerRecord.getUpduser()));
        }
        empApplyRecordMapper.insertSelective(applyRecordPo);
        log.info("Save WaRegisterRecord end");
    }

    /**
     * 调休付现
     *
     * @param model
     */
    private void saveEmpCompensatoryApplyEntity(WaEmpCompensatoryCaseApply model) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(model.getEmpId());
        WaEmpApplyRecordPo applyRecordPo = new WaEmpApplyRecordPo();
        applyRecordPo.setId(snowflakeUtil.createId());
        applyRecordPo.setTenantId(empInfo.getBelongOrgId());
        applyRecordPo.setModule(ApplyModule.COMPENSATORY.name());
        applyRecordPo.setEntityId(model.getId());
        applyRecordPo.setEmpId(model.getEmpId());
        applyRecordPo.setApplyTime(model.getCreateTime());
        applyRecordPo.setBusinessKey(String.format("%s_%s", model.getId(), BusinessCodeEnum.COMPENSATORY.getCode()));
        applyRecordPo.setStatus(model.getStatus());
        applyRecordPo.setCreateTime(model.getCreateTime() != null ? model.getCreateTime() : System.currentTimeMillis() / 1000);
        if (null != model.getCreateBy()) {
            applyRecordPo.setCreateBy(model.getCreateBy());
        }
        if (null != model.getUpdateBy()) {
            applyRecordPo.setUpdateBy(model.getUpdateBy());
        }
        applyRecordPo.setUpdateTime(model.getUpdateTime());
        empApplyRecordMapper.insertSelective(applyRecordPo);
        log.info("Save WaEmpCompensatoryCase end");
    }

    /**
     * 流程撤销废止
     *
     * @param model
     */
    private void saveWaWorkflowRevokeEntity(WaWorkflowRevoke model) {
        String moduleName = model.getModuleName();
        BusinessCodeEnum workflowEnum = BusinessCodeEnum.getByCode(BusinessCodeEnum.getCodeByName(moduleName));
        if (null == workflowEnum) {
            return;
        }
        Long entityId = model.getEntityId();
        Long empId = getEmpId(entityId, workflowEnum);
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (null == empId) {
            return;
        }
        WaEmpApplyRecordPo applyRecordPo = new WaEmpApplyRecordPo();
        applyRecordPo.setId(snowflakeUtil.createId());
        applyRecordPo.setTenantId(empInfo.getBelongOrgId());
        applyRecordPo.setModule(moduleName);
        applyRecordPo.setEntityId(model.getId());
        applyRecordPo.setEmpId(empId);
        applyRecordPo.setApplyTime(model.getCreateTime());
        applyRecordPo.setBusinessKey(String.format("%s_%s", model.getId(), workflowEnum.getCode()));
        applyRecordPo.setStatus(model.getStatus());
        applyRecordPo.setCreateTime(model.getCreateTime() != null ? model.getCreateTime() : System.currentTimeMillis() / 1000);
        if (null != model.getCreateBy()) {
            applyRecordPo.setCreateBy(model.getCreateBy());
        }
        if (null != model.getUpdateBy()) {
            applyRecordPo.setUpdateBy(model.getUpdateBy());
        }
        applyRecordPo.setUpdateTime(model.getUpdateTime());
        empApplyRecordMapper.insertSelective(applyRecordPo);
        log.info("Save WaWorkflowRevoke end");
    }

    /**
     * 假期延期
     *
     * @param model
     */
    private void saveWaLeaveExtensionEntity(WaLeaveExtension model) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(model.getEmpId());
        WaEmpApplyRecordPo applyRecordPo = new WaEmpApplyRecordPo();
        applyRecordPo.setId(snowflakeUtil.createId());
        applyRecordPo.setTenantId(empInfo.getBelongOrgId());
        applyRecordPo.setModule(ApplyModule.LEAVE_EXTENSION.name());
        applyRecordPo.setEntityId(model.getId());
        applyRecordPo.setEmpId(model.getEmpId());
        applyRecordPo.setApplyTime(model.getCreateTime());
        applyRecordPo.setBusinessKey(String.format("%s_%s", model.getId(), BusinessCodeEnum.LEAVE_EXTENSION.getCode()));
        applyRecordPo.setStatus(model.getStatus());
        applyRecordPo.setCreateTime(model.getCreateTime() != null ? model.getCreateTime() : System.currentTimeMillis() / 1000);
        if (null != model.getCreateBy()) {
            applyRecordPo.setCreateBy(model.getCreateBy());
        }
        if (null != model.getUpdateBy()) {
            applyRecordPo.setUpdateBy(model.getUpdateBy());
        }
        applyRecordPo.setUpdateTime(model.getUpdateTime());
        empApplyRecordMapper.insertSelective(applyRecordPo);
        log.info("Save saveWaLeaveExtensionEntity end");
    }

    private Long getEmpId(Long entityId, BusinessCodeEnum workflowEnum) {
        switch (workflowEnum) {
            case OVERTIME_REVOKE:
            case OVERTIME_ABOLISH:
                return getEmpIdFromOvertime(entityId);
            case TRAVEL_REVOKE:
            case TRAVEL_ABOLISH:
                return getEmpIdFromTravel(entityId);
        }
        return null;
    }

    private Long getEmpIdFromOvertime(Long entityId) {
        Optional<WaEmpOvertime> opt = Optional.ofNullable(waEmpOvertimeMapper.selectByPrimaryKey(Integer.valueOf(entityId.toString())));
        return opt.map(WaEmpOvertime::getEmpid).orElse(null);
    }

    private Long getEmpIdFromTravel(Long entityId) {
        Optional<WaEmpTravel> opt = Optional.ofNullable(waEmpTravelMapper.selectByPrimaryKey(entityId));
        return opt.map(WaEmpTravel::getEmpId).orElse(null);
    }

    private void save(WaEmpApplyRecordPo applyRecordPo, UserInfo userInfo) {
        applyRecordPo.setCreateBy(userInfo.getUserId());
        applyRecordPo.setCreateTime(System.currentTimeMillis() / 1000);
        applyRecordPo.setUpdateBy(userInfo.getUserId());
        applyRecordPo.setUpdateTime(System.currentTimeMillis() / 1000);
        empApplyRecordMapper.insertSelective(applyRecordPo);
    }

    /**
     * 更新
     */
    @Pointcut("execution(* com.caidao1.wa.mybatis.mapper.WaEmpLeaveMapper.updateByPrimaryKeySelective(..)) " +
            "|| execution(* com.caidao1.wa.mybatis.mapper.WaEmpOvertimeMapper.updateByPrimaryKeySelective(..)) " +
            "|| execution(* com.caidao1.wa.mybatis.mapper.EmpTravelMapper.updateWaEmpTravelStatus(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpTravelMapper.updateByPrimaryKeySelective(..))" +
            "|| execution(* com.caidao1.wa.mybatis.mapper.WaEmpShiftApplyRecordMapper.updateShiftApplyStatus(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaShiftApplyRecordMapper.updateByPrimaryKeySelective(..))" +
            "|| execution(* com.caidao1.wa.mybatis.mapper.WaLeaveCancelMapper.updateByPrimaryKeySelective(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpLeaveCancelMapper.updateByPrimaryKeySelective(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaRegisterRecordBdkMapper.updateByPrimaryKeySelective(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpCompensatoryCaseApplyMapper.updateByPrimaryKeySelective(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpCompensatoryCaseApplyMapper.updateByPrimaryKey(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaWorkflowRevokeMapper.updateByPrimaryKeySelective(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaWorkflowRevokeMapper.updateByPrimaryKey(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaLeaveExtensionMapper.updateByPrimaryKey(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaLeaveExtensionMapper.updateByPrimaryKeySelective(..))" +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpShiftMapper.updateByPrimaryKeySelective(..))" )
    public void updPointCut() {
    }

    /**
     * 更新
     *
     * @param joinpoint
     * @param rvt
     */
    @AfterReturning(returning = "rvt", pointcut = "updPointCut()")
    public void afterUpdateReturnValue(JoinPoint joinpoint, Object rvt) {
        try {
            if (joinpoint.getArgs() != null && joinpoint.getArgs().length > 0) {
                this.getEmpApplyRecordMapper();
                if (joinpoint.getSignature().getName().equals("updateWaEmpTravelStatus")) {
                    Long travelId = (Long) joinpoint.getArgs()[0];
                    Integer status = (Integer) joinpoint.getArgs()[1];
                    Long time = (Long) joinpoint.getArgs()[2];
                    if (null != status) {
                        this.updateEmpApply(status, ApplyModule.GO_OUT.name(), travelId, null, time);
                    }
                } else if (joinpoint.getSignature().getName().equals("updateShiftApplyStatus")) {
                    Long recId = (Long) joinpoint.getArgs()[0];
                    Integer status = (Integer) joinpoint.getArgs()[1];
                    Long time = (Long) joinpoint.getArgs()[2];
                    if (null != status) {
                        updateEmpApply(status, ApplyModule.SHIFT_CHANGE.name(), recId, null, time);
                    }
                } else if (joinpoint.getSignature().getDeclaringTypeName().contains("EmpShiftMapper")) {
                    log.info("updateEmpAttendAnceTypeByUpdateStart...");
                    UserInfo userInfo = this.getUserInfo();
                    workCalendarMapper.syncEmpAttendanceType(userInfo.getTenantId(), DateUtil.getOnlyDate(), null);
                    log.info("updateEmpAttendAnceTypeByUpdateEnd...");

                } else {
                    for (Object arg : joinpoint.getArgs()) {
                        if (arg instanceof WaEmpLeave) {
                            WaEmpLeave empLeave = ((WaEmpLeave) arg);
                            if (null != empLeave.getStatus()) {
                                Long updUser = empLeave.getUpduser() == null ? null : Long.valueOf(empLeave.getUpduser());
                                this.updateEmpApply(Integer.valueOf(empLeave.getStatus()), ApplyModule.LEAVE.name(),
                                        Long.valueOf(empLeave.getLeaveId()), updUser, empLeave.getUpdtime());
                            }
                        }
                        if (arg instanceof WaEmpOvertime) {
                            WaEmpOvertime empOvertime = (WaEmpOvertime) arg;
                            if (null != empOvertime.getStatus()) {
                                Long updUser = empOvertime.getUpduser() == null ? null : Long.valueOf(empOvertime.getUpduser());
                                this.updateEmpApply(Integer.valueOf(empOvertime.getStatus()), ApplyModule.OVERTIME.name(),
                                        Long.valueOf(empOvertime.getOtId()), updUser, empOvertime.getUpdtime());
                            }
                        }
                        if (arg instanceof WaEmpTravel) {
                            WaEmpTravel empTravel = (WaEmpTravel) arg;
                            if (null != empTravel.getStatus()) {
                                this.updateEmpApply(empTravel.getStatus(), ApplyModule.GO_OUT.name(), empTravel.getTravelId(), empTravel.getUpdateBy(), empTravel.getUpdateTime());
                            }
                        }
                        if (arg instanceof WaRegisterRecord) {
                            WaRegisterRecord registerRecord = (WaRegisterRecord) arg;
                            if (null != registerRecord.getApprovalStatus()) {
                                Long updUser = registerRecord.getUpduser() == null ? null : Long.valueOf(registerRecord.getUpduser());
                                this.updateEmpApply(registerRecord.getApprovalStatus(), ApplyModule.PUNCH_IN.name(),
                                        Long.valueOf(registerRecord.getRecordId()), updUser, registerRecord.getUpdtime());
                            }
                        }
                        if (arg instanceof WaRegisterRecordBdkPo) {
                            WaRegisterRecordBdkPo registerRecord = (WaRegisterRecordBdkPo) arg;
                            if (null != registerRecord.getApprovalStatus()) {
                                Long updUser = registerRecord.getUpduser() == null ? null : Long.valueOf(registerRecord.getUpduser());
                                this.updateEmpApply(registerRecord.getApprovalStatus(), ApplyModule.PUNCH_IN.name(), registerRecord.getRecordId(), updUser, registerRecord.getUpdtime());
                            }
                        }
                        if (arg instanceof WaShiftApplyRecord) {
                            WaShiftApplyRecord record = (WaShiftApplyRecord) arg;
                            if (null != record.getStatus()) {
                                updateEmpApply(record.getStatus(), ApplyModule.SHIFT_CHANGE.name(), record.getRecId(), record.getUpdateBy(), record.getUpdateTime());
                            }
                        }
                        if (arg instanceof WaLeaveCancel) {
                            WaLeaveCancel leaveCancel = ((WaLeaveCancel) arg);
                            if (null != leaveCancel.getStatus()) {
                                Long updUser = leaveCancel.getUpdateBy() == null ? null : leaveCancel.getUpdateBy();
                                this.updateEmpApply(Integer.valueOf(leaveCancel.getStatus()), ApplyModule.LEAVE_CANCEL.name(),
                                        leaveCancel.getLeaveCancelId(), updUser, leaveCancel.getUpdateTime());
                            }
                        }
                        if (arg instanceof WaEmpLeaveCancel) {
                            WaEmpLeaveCancel leaveCancel = ((WaEmpLeaveCancel) arg);
                            if (null != leaveCancel.getStatus()) {
                                Long updUser = leaveCancel.getUpdateBy() == null ? null : leaveCancel.getUpdateBy();
                                this.updateEmpApply(Integer.valueOf(leaveCancel.getStatus()), ApplyModule.LEAVE_CANCEL.name(),
                                        leaveCancel.getLeaveCancelId(), updUser, leaveCancel.getUpdateTime());
                            }
                        }
                        if (arg instanceof WaEmpCompensatoryCaseApply) {
                            WaEmpCompensatoryCaseApply model = ((WaEmpCompensatoryCaseApply) arg);
                            if (null != model.getStatus()) {
                                Long updUser = model.getUpdateBy() == null ? null : model.getUpdateBy();
                                this.updateEmpApply(model.getStatus(), ApplyModule.COMPENSATORY.name(), model.getId(), updUser, model.getUpdateTime());
                            }
                        }
                        if (arg instanceof WaWorkflowRevoke) {
                            WaWorkflowRevoke model = ((WaWorkflowRevoke) arg);
                            if (null != model.getStatus()) {
                                Long updUser = model.getUpdateBy() == null ? null : model.getUpdateBy();
                                this.updateEmpApply(model.getStatus(), model.getModuleName(), model.getId(), updUser, model.getUpdateTime());
                            }
                        }
                        if (arg instanceof WaLeaveExtension) {
                            WaLeaveExtension model = ((WaLeaveExtension) arg);
                            if (null != model.getStatus()) {
                                Long updUser = model.getUpdateBy() == null ? null : model.getUpdateBy();
                                this.updateEmpApply(model.getStatus(), ApplyModule.LEAVE_EXTENSION.name(), model.getId(), updUser, model.getUpdateTime());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("afterUpdateReturnValue error:{}, {}", e.getMessage(), e);
        }
    }

    private void updateEmpApply(Integer status, String module, Long entityId, Long updater, Long UpdateTime) {
        UserInfo userInfo = this.getUserInfo();
        String tenantId = userInfo != null ? userInfo.getTenantId() : null;
        Optional<WaEmpApplyRecordPo> opt = Optional.ofNullable(empApplyRecordMapper.queryByModuleAndEntityId(tenantId, module, entityId));
        WaEmpApplyRecordPo empApplyRecordPo = null;
        if (opt.isPresent()) {
            empApplyRecordPo = opt.get();
            empApplyRecordPo.setStatus(status);
            empApplyRecordPo.setUpdateBy(updater);
            empApplyRecordPo.setUpdateTime(UpdateTime);
        }
        if (null != empApplyRecordPo) {
            empApplyRecordMapper.updateByPrimaryKeySelective(empApplyRecordPo);
        }
    }

    /**
     * 删除
     */
    @Pointcut("execution(* com.caidao1.wa.mybatis.mapper.WaEmpLeaveMapper.deleteByExample(..)) " +
            "|| execution(* com.caidao1.wa.mybatis.mapper.WaEmpLeaveMapper.deleteByPrimaryKey(..)) " +
            "|| execution(* com.caidao1.wa.mybatis.mapper.WaEmpOvertimeMapper.deleteByExample(..)) " +
            "|| execution(* com.caidao1.wa.mybatis.mapper.WaEmpOvertimeMapper.deleteByPrimaryKey(..)) " +
            "|| execution(* com.caidao1.wa.mybatis.mapper.WaRegisterRecordMapper.deleteByPrimaryKey(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.impl.WorkflowRevokeRepository.delete(..)) "+
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpShiftMapper.deleteByPrimaryKey(..)) " +
            "|| execution(* com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpShiftMapper.deleteByIds(..)) "
    )
    public void delPointCut() {
    }

    /**
     * 删除
     *
     * @param joinpoint
     * @param rvt
     */
    @AfterReturning(returning = "rvt", pointcut = "delPointCut()")
    public void afterDeleteReturnValue(JoinPoint joinpoint, Object rvt) {
        try {
            if (joinpoint.getArgs() != null && joinpoint.getArgs().length > 0) {
                this.getEmpApplyRecordMapper();
                if (joinpoint.getSignature().getDeclaringTypeName().contains("EmpShiftMapper")) {
                    log.info("updateEmpAttendAnceTypeByDeleteStart...");
                    UserInfo userInfo = this.getUserInfo();
                    workCalendarMapper.syncEmpAttendanceType(userInfo.getTenantId(), DateUtil.getOnlyDate(), null);
                    log.info("updateEmpAttendAnceTypeByDeleteEnd...");
                }
                for (Object arg : joinpoint.getArgs()) {
                    if (arg instanceof WaEmpLeave) {
                        WaEmpLeave empLeave = ((WaEmpLeave) arg);
                        this.deleteEmpApply(ApplyModule.LEAVE.name(), Long.valueOf(empLeave.getLeaveId()));
                    }
                    if (arg instanceof WaEmpOvertime) {
                        WaEmpOvertime empOvertime = (WaEmpOvertime) arg;
                        this.deleteEmpApply(ApplyModule.OVERTIME.name(), Long.valueOf(empOvertime.getOtId()));
                    }
                    if (arg instanceof WaEmpTravel) {
                        WaEmpTravel empTravel = (WaEmpTravel) arg;
                        this.deleteEmpApply(ApplyModule.GO_OUT.name(), empTravel.getTravelId());
                    }
                    if (arg instanceof WaRegisterRecord) {
                        WaRegisterRecord registerRecord = (WaRegisterRecord) arg;
                        this.deleteEmpApply(ApplyModule.PUNCH_IN.name(), Long.valueOf(registerRecord.getRecordId()));
                    }
                    if (arg instanceof Integer) {
                        if (joinpoint.getSignature().getDeclaringTypeName().contains("WaEmpLeaveMapper")) {
                            empApplyRecordMapper.deleteByModuleAndEntityId(null, ApplyModule.LEAVE.name(), Long.valueOf((Integer) arg));
                        }
                        if (joinpoint.getSignature().getDeclaringTypeName().contains("WaEmpOvertimeMapper")) {
                            empApplyRecordMapper.deleteByModuleAndEntityId(null, ApplyModule.OVERTIME.name(), Long.valueOf((Integer) arg));
                        }
                        if (joinpoint.getSignature().getDeclaringTypeName().contains("WaRegisterRecordMapper")) {
                            empApplyRecordMapper.deleteByModuleAndEntityId(null, ApplyModule.PUNCH_IN.name(), Long.valueOf((Integer) arg));
                        }
                    }
                    if (arg instanceof WaWorkflowRevoke) {
                        WaWorkflowRevoke workflowRevoke = (WaWorkflowRevoke) arg;
                        this.deleteEmpApply(workflowRevoke.getModuleName(), workflowRevoke.getId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("afterDeleteReturnValue error:{}, {}", e.getMessage(), e);
        }
    }

    /**
     * 删除员工申请
     *
     * @param module
     * @param entityId
     */
    private void deleteEmpApply(String module, Long entityId) {
        Optional<WaEmpApplyRecordPo> opt = Optional.ofNullable(empApplyRecordMapper.queryByModuleAndEntityId(null, module, entityId));
        if (opt.isPresent()) {
            WaEmpApplyRecordPo empApplyRecordPo = opt.get();
            empApplyRecordMapper.deleteByPrimaryKey(empApplyRecordPo.getId());
        }
    }


    /**
     * 导入新增
     */
    @Pointcut("execution(* com.caidao1.ioc.mybatis.mapper.IocImportMapper.fastInsertObjList(..))" +
            " || execution(* com.caidao1.ioc.mybatis.mapper.IocImportMapper.fastInsertList(..))")
    public void fastInsertPointCut() {
    }

    @AfterReturning(returning = "rvt", pointcut = "fastInsertPointCut()")
    public void afterImportReturnValue(JoinPoint joinpoint, Object rvt) {
        try {
            String method = joinpoint.getSignature().getName();
            if (joinpoint.getArgs() != null && joinpoint.getArgs().length > 0) {
                this.getEmpApplyRecordMapper();
                Object[] args = joinpoint.getArgs();
                String table = String.valueOf(args[0]);
                if (method.equals("fastInsertObjList")) {
                    if ("wa_emp_leave".equals(table)) {
                        List<WaEmpLeave> list = (List<WaEmpLeave>) args[3];
                        if (CollectionUtils.isNotEmpty(list)) {
                            for (WaEmpLeave leave : list) {
                                this.saveEmpLeaveApplyEntity(leave);
                            }
                        }
                    }
                    if ("wa_emp_leave_time".equals(table)) {
                        List<WaEmpLeaveTime> list = (List<WaEmpLeaveTime>) args[3];
                        if (CollectionUtils.isNotEmpty(list)) {
                            for (WaEmpLeaveTime leaveTime : list) {
                                this.updateImportEmpLeaveTimeApplyEntity(leaveTime);
                            }
                        }
                    }
                    if ("wa_emp_overtime".equals(table)) {
                        List<WaEmpOvertime> list = (List<WaEmpOvertime>) args[3];
                        if (CollectionUtils.isNotEmpty(list)) {
                            for (WaEmpOvertime overtime : list) {
                                this.saveEmpOverTimeApplyEntity(overtime);
                            }
                        }
                    }
                    if ("wa_emp_shift".equals(table)) {
                        log.info("updateEmpAttendAnceTypeByImportInsertStart...");
                        UserInfo userInfo = getUserInfo();
                        workCalendarMapper.syncEmpAttendanceType(userInfo.getTenantId(), DateUtil.getOnlyDate(), null);
                        log.info("updateEmpAttendAnceTypeByImportInsertEnd...");
                    }
                } else {
                    if ("wa_emp_shift".equals(table)) {
                        log.info("updateEmpAttendAnceTypeByImportUpdateStart...");
                        UserInfo userInfo = getUserInfo();
                        workCalendarMapper.syncEmpAttendanceType(userInfo.getTenantId(), DateUtil.getOnlyDate(), null);
                        log.info("updateEmpAttendAnceTypeByImportUpdateEnd...");
                    }
                    if ("wa_emp_leave".equals(table)) {
                        List<UpdRowDto> rows = (List<UpdRowDto>) args[2];
                        if (CollectionUtils.isNotEmpty(rows)) {
                            for (UpdRowDto row : rows) {
                                Optional<WaEmpLeave> opt = Optional.ofNullable(waEmpLeaveMapper.selectByPrimaryKey(this.getPrimaryKey(row.getRow(), "leave_id")));
                                if (!opt.isPresent()) {
                                    continue;
                                }
                                this.saveEmpLeaveApplyEntity(opt.get());
                            }
                        }
                    }
                    if ("wa_emp_leave_time".equals(table)) {
                        List<UpdRowDto> rows = (List<UpdRowDto>) args[2];
                        if (CollectionUtils.isNotEmpty(rows)) {
                            for (UpdRowDto row : rows) {
                                Optional<WaEmpLeaveTime> opt = Optional.ofNullable(waEmpLeaveTimeMapper.selectByPrimaryKey(this.getPrimaryKey(row.getRow(), "leave_time_id")));
                                if (!opt.isPresent()) {
                                    continue;
                                }
                                this.updateImportEmpLeaveTimeApplyEntity(opt.get());
                            }
                        }
                    }
                    if ("wa_emp_overtime".equals(table)) {
                        List<UpdRowDto> rows = (List<UpdRowDto>) args[2];
                        if (CollectionUtils.isNotEmpty(rows)) {
                            for (UpdRowDto row : rows) {
                                Optional<WaEmpOvertime> opt = Optional.ofNullable(waEmpOvertimeMapper.selectByPrimaryKey(this.getPrimaryKey(row.getRow(), "ot_id")));
                                if (!opt.isPresent()) {
                                    continue;
                                }
                                this.saveEmpOverTimeApplyEntity(opt.get());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("afterImportReturnValue error:{}, {}", e.getMessage(), e);
        }
    }

    /**
     * 更新休假时间
     *
     * @param leaveTime
     */
    private void updateImportEmpLeaveTimeApplyEntity(WaEmpLeaveTime leaveTime) {
        Optional<WaEmpLeaveTime> opt = Optional.ofNullable(waEmpLeaveTimeMapper.selectByPrimaryKey(leaveTime.getLeaveTimeId()));
        if (opt.isPresent()) {
            leaveTime = opt.get();
        }
        if (null != leaveTime.getLeaveId()) {
            Optional<WaEmpApplyRecordPo> applyOpt = Optional.ofNullable(empApplyRecordMapper.queryByModuleAndEntityId(null, ApplyModule.LEAVE.name(), Long.valueOf(leaveTime.getLeaveId())));
            if (applyOpt.isPresent()) {
                WaEmpApplyRecordPo applyRecordPo = applyOpt.get();
                applyRecordPo.setStartTime(leaveTime.getShiftStartTime() == null ? leaveTime.getStartTime() : leaveTime.getShiftStartTime());
                applyRecordPo.setEndTime(leaveTime.getShiftEndTime() == null ? leaveTime.getEndTime() : leaveTime.getShiftEndTime());
                empApplyRecordMapper.updateByPrimaryKeySelective(applyRecordPo);
            }
        }
        log.info("Update Import WaEmpLeave end");
    }

    public Integer getPrimaryKey(List<SaveItemDto> list, String keyName) {
        Map<String, Object> map = list.stream().filter(i -> i.getItemValue() != null).collect(Collectors.toMap(SaveItemDto::getItemCode, SaveItemDto::getItemValue));
        if (null == map.get(keyName)) {
            return null;
        }
        return Integer.valueOf(map.get(keyName).toString());
    }

    /**
     * 撤销
     */
    /*@Pointcut("execution(* com.caidao1.workflow.mybatis.mapper.WorkflowMapper.execRevokeSql(..))")
    public void fastRevokePointCut() {
    }

    @AfterReturning(returning = "rvt", pointcut = "fastRevokePointCut()")
    public void afterRevokeReturnValue(JoinPoint joinpoint, Object rvt) {
        try {
            if (joinpoint.getArgs() != null && joinpoint.getArgs().length > 0) {
                this.getEmpApplyRecordMapper();
                for (Object arg : joinpoint.getArgs()) {
                    String sql = String.valueOf(arg);
                    String[] cs = Pattern.compile("[^0-9]+").split(sql);
                    if (cs.length < 3) {
                        continue;
                    }
                    Long currentTime = System.currentTimeMillis() / 1000;
                    UserInfo userInfo = getUserInfo();
                    Long userId = userInfo.getUserId();
                    Long entityId = Long.valueOf(cs[2]);
                    if (sql.contains("wa_emp_leave")) {
                        this.updateEmpApply(ApprovalStatusEnum.REVOKED.getIndex(), ApplyModule.LEAVE.name(), entityId, userId, currentTime);
                    }
                    if (sql.contains("wa_register_record_bdk")) {
                        this.updateEmpApply(ApprovalStatusEnum.REVOKED.getIndex(), ApplyModule.PUNCH_IN.name(), entityId, userId, currentTime);
                    }
                    if (sql.contains("wa_emp_overtime")) {
                        this.updateEmpApply(ApprovalStatusEnum.REVOKED.getIndex(), ApplyModule.OVERTIME.name(), entityId, userId, currentTime);
                    }
                    if (sql.contains("wa_emp_travel")) {
                        this.updateEmpApply(ApprovalStatusEnum.REVOKED.getIndex(), ApplyModule.GO_OUT.name(), entityId, userId, currentTime);
                    }
                    if (sql.contains("wa_shift_apply_record")) {
                        updateEmpApply(ApprovalStatusEnum.REVOKED.getIndex(), ApplyModule.SHIFT_CHANGE.name(), entityId, userId, currentTime);
                    }
                    if (sql.contains("wa_emp_leave_cancel")) {
                        this.updateEmpApply(ApprovalStatusEnum.REVOKED.getIndex(), ApplyModule.LEAVE_CANCEL.name(), entityId, userId, currentTime);
                    }
                }
            }
        } catch (Exception e) {
            log.error("afterRevokeReturnValue error:{}, {}", e.getMessage(), e);
        }
    }*/
}