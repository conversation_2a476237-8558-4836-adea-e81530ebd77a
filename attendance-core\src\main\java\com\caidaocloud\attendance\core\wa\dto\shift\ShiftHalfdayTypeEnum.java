package com.caidaocloud.attendance.core.wa.dto.shift;

/**
 * 班次：半天定义类型
 */
public enum ShiftHalfdayTypeEnum {
    CUSTOM_TIME(1, "自定义半天时间点"),
    HALF_OF_SHIFT(2, "班次工作时长的一半");

    private Integer index;
    private String name;

    ShiftHalfdayTypeEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ShiftHalfdayTypeEnum c : ShiftHalfdayTypeEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
