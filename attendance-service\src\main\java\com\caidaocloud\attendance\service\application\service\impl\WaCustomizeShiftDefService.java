package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.shift.MultiWorkTimeBaseDto;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftBelongModuleEnum;
import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.core.wa.vo.MultiWorkTimeInfoSimpleVo;
import com.caidaocloud.attendance.service.domain.entity.WaCustomizeShiftDefDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.interfaces.dto.shift.CustomizeShiftDefQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.multi.CustMultiShiftDefSaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.multi.MultiCustomizeShiftDefDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.multi.MultiShiftSlightSaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.multi.MultiWorkTimeDto;
import com.caidaocloud.attendance.service.interfaces.vo.shift.CustMultiShiftDefForBladeVo;
import com.caidaocloud.attendance.service.interfaces.vo.shift.CustMultiShiftDefVo;
import com.caidaocloud.attendance.service.wfm.application.service.WfmShiftService;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.WebUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自定义班次设置
 *
 * <AUTHOR>
 * @Date 2025/1/20
 */
@Slf4j
@Service
public class WaCustomizeShiftDefService {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1L, 1L);

    /**
     * 自定义班次上班打卡时间范围，单位分钟
     */
    @Value("${caidaocloud.data.custshift.regtimerange.signin:180}")
    private Integer signInRegTimerange;

    /**
     * 自定义班次下班打卡时间范围，单位分钟
     */
    @Value("${caidaocloud.data.custshift.regtimerange.signout:180}")
    private Integer signOutRegTimerange;

    @Autowired
    private WaCustomizeShiftDefDo waCustomizeShiftDefDo;
    @Autowired
    private WfmShiftService wfmShiftService;

    @Transactional(rollbackFor = Exception.class)
    public Long save(CustMultiShiftDefSaveDto dto) {
        dto.preCheck();
        dto.initField();
        dto.checkTime();
        WaCustomizeShiftDefDo shiftDefDo = dto.doConvertToDo(signInRegTimerange, signOutRegTimerange);

        WaCustomizeShiftDefDo oldCustomizeShiftDefDo = null;
        if (null != shiftDefDo.getShiftDefId()) {
            oldCustomizeShiftDefDo = waCustomizeShiftDefDo.getByWaShiftDefId(shiftDefDo.getShiftDefId().intValue());
            if (null == oldCustomizeShiftDefDo) {
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_200602", WebUtil.getRequest()));
            }
            shiftDefDo.setShiftDefCode(oldCustomizeShiftDefDo.getShiftDefCode());
        } else {
            // 班次重叠校验（当班次时间只有一段标准工时时才进行校验）
            Integer waShiftDefId = performShiftOverlapValidation(dto, shiftDefDo);
            if (null != waShiftDefId) {
                if (dto.isIfAdd()) {
                    return Long.valueOf(waShiftDefId);
                }
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_202834", WebUtil.getRequest()));
            }
        }

        // 同步保存至【标准班次wa_shift_def表中】
        MultiShiftSlightSaveDto waShftDefSaveDto = new MultiShiftSlightSaveDto();
        waShftDefSaveDto.setDateType(shiftDefDo.getDateType());
        waShftDefSaveDto.setI18nShiftDefName(dto.getI18nShiftDefName());
        waShftDefSaveDto.setShiftDefCode(shiftDefDo.getShiftDefCode());
        waShftDefSaveDto.setIsNoonRest(shiftDefDo.getRestTotalTime() != null && shiftDefDo.getRestTotalTime() > 0);
        waShftDefSaveDto.setRestPeriods(shiftDefDo.getRestPeriodDtoList());
        waShftDefSaveDto.setMultiWorkTimes(shiftDefDo.getMultiWorkTimeDtoList());
        waShftDefSaveDto.setMultiOvertime(shiftDefDo.getMultiOvertimeDtoList());
        waShftDefSaveDto.setTemporaryShift(Boolean.TRUE);
        waShftDefSaveDto.setFlexibleShiftSwitch(1);
        waShftDefSaveDto.setFlexibleWorkRule(1);
        if (null != oldCustomizeShiftDefDo) {
            waShftDefSaveDto.setShiftDefId(oldCustomizeShiftDefDo.getWaShiftDefId());
        }
        Integer waShiftDefId = wfmShiftService.save(waShftDefSaveDto);

        // 自定义班次表数据保存
        shiftDefDo.setWaShiftDefId(waShiftDefId);
        LogRecordContext.putVariable("name", shiftDefDo.getShiftDefName());
        if (null == shiftDefDo.getShiftDefId()) {
            shiftDefDo.setShiftDefId(snowflakeUtil.createId());
            LogRecordContext.putVariable("operate", "新增");
            waCustomizeShiftDefDo.save(shiftDefDo);
        } else {
            LogRecordContext.putVariable("operate", "编辑");
            shiftDefDo.setShiftDefId(oldCustomizeShiftDefDo.getShiftDefId());
            waCustomizeShiftDefDo.updateById(shiftDefDo);
        }
        return Long.valueOf(waShiftDefId);
    }

    /**
     * 执行班次重叠校验
     * 当班次时间只有一段标准工时时才进行校验
     *
     * @param dto        自定义班次保存DTO
     * @param shiftDefDo 班次定义DO
     */
    private Integer performShiftOverlapValidation(CustMultiShiftDefSaveDto dto, WaCustomizeShiftDefDo shiftDefDo) {
        // 检查是否需要进行重叠校验：只有当dto和shiftDefDo的工作时间都只有一段时才校验
        if (!shouldPerformOverlapValidation(dto, shiftDefDo)) {
            return null;
        }

        MultiWorkTimeDto workTimeDto = shiftDefDo.getMultiWorkTimeDtoList().get(0);
        List<WaShiftDo> conflictingShifts = findConflictingShifts(workTimeDto);

        if (CollectionUtils.isEmpty(conflictingShifts)) {
            return null;
        }

        return validateNoSingleWorkTimeConflicts(conflictingShifts);
    }

    /**
     * 判断是否需要执行重叠校验
     */
    private boolean shouldPerformOverlapValidation(CustMultiShiftDefSaveDto dto, WaCustomizeShiftDefDo shiftDefDo) {
        List<MultiWorkTimeDto> multiWorkTimeDtoList = shiftDefDo.getMultiWorkTimeDtoList();
        return CollectionUtils.isNotEmpty(dto.getMultiWorkTimes())
                && dto.getMultiWorkTimes().size() == 1
                && CollectionUtils.isNotEmpty(multiWorkTimeDtoList)
                && multiWorkTimeDtoList.size() == 1;
    }

    /**
     * 查找时间冲突的班次
     */
    private List<WaShiftDo> findConflictingShifts(MultiWorkTimeDto workTimeDto) {
        if (null == workTimeDto.getStartTime()
                || null == workTimeDto.getEndTime()
                || null == workTimeDto.getStartTimeBelong()
                || null == workTimeDto.getEndTimeBelong()) {
            return new ArrayList<>();
        }
        return wfmShiftService.getListByStTime(UserContext.getTenantId(),
                ShiftBelongModuleEnum.WFM, true, workTimeDto.getStartTime(), workTimeDto.getEndTime(),
                workTimeDto.getStartTimeBelong(), workTimeDto.getEndTimeBelong());
    }

    /**
     * 校验是否存在单一工作时间段的冲突
     */
    private Integer validateNoSingleWorkTimeConflicts(List<WaShiftDo> conflictingShifts) {
        List<Integer> shiftDefIds = conflictingShifts.stream()
                .map(WaShiftDo::getShiftDefId)
                .distinct()
                .collect(Collectors.toList());

        List<WaCustomizeShiftDefDo> customizeShiftDefs = waCustomizeShiftDefDo.getListByRelShiftIds(shiftDefIds);
        if (CollectionUtils.isEmpty(customizeShiftDefs)) {
            return null;
        }

        for (WaCustomizeShiftDefDo customizeShiftDef : customizeShiftDefs) {
            if (isSingleWorkTimeShift(customizeShiftDef)) {
                return customizeShiftDef.getWaShiftDefId();
            }
        }
        return null;
    }

    /**
     * 判断是否为单一工作时间段班次
     */
    private boolean isSingleWorkTimeShift(WaCustomizeShiftDefDo customizeShiftDef) {
        List<MultiCustomizeShiftDefDto> oriWorkTimes = FastjsonUtil.toArrayList(
                customizeShiftDef.getOriMultiWorkTimes(), MultiCustomizeShiftDefDto.class);
        return oriWorkTimes != null && oriWorkTimes.size() == 1;
    }

    private List<MultiWorkTimeInfoSimpleVo> getMultiWorkTimeVoList(List<MultiWorkTimeDto> multiWorkTimeDtoList) {
        return multiWorkTimeDtoList.stream().map(timeIt -> {
            MultiWorkTimeInfoSimpleVo timeInfoVo = ObjectConverter.convert(timeIt, MultiWorkTimeInfoSimpleVo.class);
            timeInfoVo.setRealStartTime(timeIt.doGetRealStartTime());
            timeInfoVo.setRealEndTime(timeIt.doGetRealEndTime());
            return timeInfoVo;
        }).collect(Collectors.toList());
    }

    private void setShiftSummaryList(List<MultiWorkTimeDto> multiWorkTimeDtoList, MultiShiftSimpleVo simpleVo) {
        Long nowDate = DateUtil.getOnlyDate();
        List<String> timeTxtList = multiWorkTimeDtoList.stream().map(timeIt -> {
            String start = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getStartTime() * 60), "HH:mm", true);
            String end = DateUtil.convertDateTimeToStr(nowDate + (timeIt.getEndTime() * 60), "HH:mm", true);
            if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getStartTimeBelong())) {
                start = ShiftTimeBelongTypeEnum.getName(timeIt.getStartTimeBelong()) + start;
            }
            if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(timeIt.getEndTimeBelong())) {
                end = ShiftTimeBelongTypeEnum.getName(timeIt.getEndTimeBelong()) + end;
            }
            return String.format("%s~%s", start, end);
        }).collect(Collectors.toList());
        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(simpleVo.getDateType())) {
            simpleVo.setSummary(String.format("%s（%s）", simpleVo.getShiftDefName(), org.apache.commons.lang3.StringUtils.join(timeTxtList, "、")));
        } else {
            simpleVo.setSummary(simpleVo.getShiftDefName());
        }
    }

    public void calShiftTimeList(List<MultiWorkTimeDto> multiWorkTimeDtoList, MultiShiftSimpleVo simpleVo) {
        if (multiWorkTimeDtoList.size() == 1) {
            MultiWorkTimeDto multiWorkTimeDto = multiWorkTimeDtoList.get(0);
            simpleVo.setStartTime(multiWorkTimeDto.getStartTime());
            simpleVo.setEndTime(multiWorkTimeDto.getEndTime());
            simpleVo.setStartTimeBelong(multiWorkTimeDto.getStartTimeBelong());
            simpleVo.setEndTimeBelong(multiWorkTimeDto.getEndTimeBelong());
            simpleVo.setIsNight(multiWorkTimeDto.getIsNight());
        } else {
            MultiWorkTimeDto minMultiWorkTimeDto = multiWorkTimeDtoList.get(0);
            MultiWorkTimeDto maxMultiWorkTimeDto = multiWorkTimeDtoList.get(multiWorkTimeDtoList.size() - 1);
            simpleVo.setStartTime(minMultiWorkTimeDto.getStartTime());
            simpleVo.setEndTime(maxMultiWorkTimeDto.getEndTime());
            simpleVo.setStartTimeBelong(minMultiWorkTimeDto.getStartTimeBelong());
            simpleVo.setEndTimeBelong(maxMultiWorkTimeDto.getEndTimeBelong());
            long kyCount = multiWorkTimeDtoList.stream().filter(workTimeIt -> workTimeIt.getIsNight() != null && workTimeIt.getIsNight()).count();
            simpleVo.setIsNight(kyCount > 0);
        }
    }

    private List<MultiShiftSimpleVo> convertDoToVo(List<WaCustomizeShiftDefDo> doList) {
        return doList.stream().map(shiftDefDo -> {
            MultiShiftSimpleVo simpleVo = new MultiShiftSimpleVo();
            simpleVo.setShiftDefId(Long.valueOf(shiftDefDo.getWaShiftDefId()));
            simpleVo.setDateType(shiftDefDo.getDateType());
            simpleVo.setShiftDefName(LangParseUtil.getI18nLanguage(shiftDefDo.getI18nShiftDefName(), shiftDefDo.getShiftDefName()));
            simpleVo.setShiftDefCode(shiftDefDo.getShiftDefCode());
            simpleVo.setWorkTotalTime(shiftDefDo.getWorkTotalTime());
            simpleVo.setTemporary(Boolean.TRUE);
            simpleVo.setColorMark(shiftDefDo.getColorMark());

            // 工作时间
            List<MultiWorkTimeDto> multiWorkTimeDtoList = FastjsonUtil.toArrayList(shiftDefDo.getMultiWorkTimes(), MultiWorkTimeDto.class);
            multiWorkTimeDtoList.sort(Comparator.comparing(MultiWorkTimeBaseDto::doGetRealStartTime));
            simpleVo.setMultiWorkTimes(getMultiWorkTimeVoList(multiWorkTimeDtoList));
            setShiftSummaryList(multiWorkTimeDtoList, simpleVo);
            calShiftTimeList(multiWorkTimeDtoList, simpleVo);

            // 班次时间（包含非工作时间）
            List<MultiCustomizeShiftDefDto> oriMultiWorkTimeDtoList = FastjsonUtil.toArrayList(shiftDefDo.getOriMultiWorkTimes(), MultiCustomizeShiftDefDto.class);
            List<MultiWorkTimeInfoSimpleVo> oriMultiWorkTimeVoList = oriMultiWorkTimeDtoList.stream().map(timeIt -> {
                MultiWorkTimeInfoSimpleVo timeInfoVo = ObjectConverter.convert(timeIt, MultiWorkTimeInfoSimpleVo.class);
                timeInfoVo.setRealStartTime(timeIt.doGetRealStartTime());
                timeInfoVo.setRealEndTime(timeIt.doGetRealEndTime());
                return timeInfoVo;
            }).collect(Collectors.toList());
            simpleVo.setOriMultiWorkTimes(oriMultiWorkTimeVoList);
            return simpleVo;
        }).collect(Collectors.toList());
    }

    public List<MultiShiftSimpleVo> getList(CustomizeShiftDefQueryDto queryDto) {
        StopWatch st = new StopWatch();
        st.start("开始查询自定义班次");
        log.info("cust shift query start time={}", System.currentTimeMillis());

        List<WaCustomizeShiftDefDo> doList = waCustomizeShiftDefDo.getList(queryDto);
        if (CollectionUtils.isEmpty(doList)) {
            return Lists.newArrayList();
        }

        st.stop();
        log.info("cust shift query end time={}", System.currentTimeMillis());

        st.start("开始转换自定义班次");
        log.info("cust shift convert start time={}", System.currentTimeMillis());

        List<MultiShiftSimpleVo> voList = convertDoToVo(doList);

        st.stop();
        log.info("cust shift convert end time={}", System.currentTimeMillis());

        log.info("WaCustomizeShiftDefService getList time :{}", st.prettyPrint());
        return voList;
    }

    public List<MultiShiftSimpleVo> getList(String tenantId, String belongModule, Long startDate, Long endDate) {
        List<WaCustomizeShiftDefDo> doList = waCustomizeShiftDefDo.getList(tenantId, belongModule, startDate, endDate);
        if (CollectionUtils.isEmpty(doList)) {
            return Lists.newArrayList();
        }
        return convertDoToVo(doList);
    }

    public List<WaCustomizeShiftDefDo> getDoList(String tenantId, String belongModule, Long startDate, Long endDate) {
        return waCustomizeShiftDefDo.getList(tenantId, belongModule, startDate, endDate);
    }

    public List<MultiShiftSimpleVo> getListByIds(List<Long> shiftDefIds) {
        List<WaCustomizeShiftDefDo> doList = waCustomizeShiftDefDo.getListByIds(shiftDefIds);
        if (CollectionUtils.isEmpty(doList)) {
            return Lists.newArrayList();
        }
        return convertDoToVo(doList);
    }

    public List<MultiShiftSimpleVo> getListByRelShiftIds(List<Integer> waShiftDefIds) {
        List<WaCustomizeShiftDefDo> doList = waCustomizeShiftDefDo.getListByRelShiftIds(waShiftDefIds);
        if (CollectionUtils.isEmpty(doList)) {
            return Lists.newArrayList();
        }
        return convertDoToVo(doList);
    }

    public CustMultiShiftDefVo getByShiftId(Integer waShiftDefId) {
        List<WaCustomizeShiftDefDo> doList = waCustomizeShiftDefDo.getListByRelShiftIds(Lists.newArrayList(waShiftDefId));
        if (CollectionUtils.isEmpty(doList)) {
            return null;
        }
        List<CustMultiShiftDefVo> voList = CustMultiShiftDefVo.convertDoToVo(doList);
        return voList.get(0);
    }

    public CustMultiShiftDefForBladeVo getBladeShiftByShiftId(Integer waShiftDefId) {
        List<WaCustomizeShiftDefDo> doList = waCustomizeShiftDefDo.getListByRelShiftIds(Lists.newArrayList(waShiftDefId));
        if (CollectionUtils.isEmpty(doList)) {
            return null;
        }
        List<CustMultiShiftDefForBladeVo> voList = CustMultiShiftDefForBladeVo.convertDoToVo(doList);
        return voList.get(0);
    }
}
