package com.caidaocloud.attendance.service.application.service.impl;

import com.caidaocloud.attendance.service.infrastructure.repository.mapper.SysCorpOrg2Mapper;
import com.caidaocloud.attendance.service.interfaces.vo.ChildOrgVo;
import com.caidaocloud.attendance.service.interfaces.vo.OrgTreeVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class GetOrgTreeService {
    @Resource
    private SysCorpOrg2Mapper sysCorpOrg2Mapper;

    public List<ChildOrgVo> getSysCorpOrgTreeByTid(Long corpId) {
        OrgTreeVo orgTreeVo = sysCorpOrg2Mapper.getFirstNode(corpId);
        if(orgTreeVo == null){
            return null;
        }
        List<ChildOrgVo> childOrgVoList = getInfo(orgTreeVo.getValue());
        orgTreeVo.setChildren(childOrgVoList);
        return childOrgVoList;
    }

    public List<ChildOrgVo> getInfo(Long orgId) {
        List<ChildOrgVo> lisOrg = sysCorpOrg2Mapper.getOrgTreeDetail(orgId);
        if (lisOrg.size() > 0 && lisOrg.get(0).getType().equals("org")) {
            for (ChildOrgVo childOrgVo : lisOrg) {
                childOrgVo.setChildren(getInfo(childOrgVo.getValue()));
            }
        }
        return lisOrg;
    }
}
